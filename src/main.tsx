import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { AppProviders } from './components/providers/AppProviders.tsx';

// const topProps = {
//   style: { top: baseTheme.space.base * 3 },
// } as HTMLAttributes<HTMLDivElement>;
// const bottomProps = {
//   style: { bottom: baseTheme.space.base * 3 },
// } as HTMLAttributes<HTMLDivElement>;
// const placementProps = {
//   'top-start': topProps,
//   top: topProps,
//   'top-end': topProps,
//   'bottom-start': bottomProps,
//   bottom: bottomProps,
//   'bottom-end': bottomProps,
// };
// export const queryClient = new QueryClient({
//   defaultOptions: {
//     queries: {
//       refetchOnWindowFocus: false,
//     },
//   },
// });

// const cache = new InMemoryCache();

// export const graphqlClient = new ApolloClient({
//   cache: cache,
//   uri: constants.GRAPHQL_URI,
//   // uri: 'http://localhost:3002/graphql',
//   headers: {
//   },
// });

// export const graphqlClientV2 = new ApolloClient({
//   cache: cache,
//   uri: constants.GRAPHQL_URI_V2,
//   // uri: 'http://localhost:3002/graphql',
//   headers: {
//   },
// });
ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <AppProviders>
    <App />
  </AppProviders>,
);
