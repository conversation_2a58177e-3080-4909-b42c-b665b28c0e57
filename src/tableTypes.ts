export interface RowData {
  [key: string]: string | string[] | boolean | number;
}

export interface ColumnData {
  title: string;
  renderType:
    | 'text'
    | 'Tags'
    | 'date'
    | 'Icon'
    | 'IconButton'
    | 'EditAndRemove'
    | 'Link'
    | 'Button'
    | 'None'
    | 'attachments';
  isOptional?: boolean;
  isSkippable?: boolean;
}

export interface ReturnApprovedData {
  [key: string]: ColumnData;
}

export interface Return {
  [key: string]: ColumnData;
}

export interface ReturnDTO {
  [key: string]: ColumnData;
}

export const ReturnDTO: ReturnDTO = {
  Id: {
    title: 'id',
    renderType: 'None',
    isSkippable: true,
  },
  New: {
    title: 'New',
    renderType: 'EditAndRemove',
  },
  AwbNumber: {
    title: 'Awb Number',
    renderType: 'text',
  },
  OrderId: {
    title: 'Order Id',
    renderType: 'text',
  },
  ReturnId: {
    title: 'Return Id',
    renderType: 'text',
  },
  DTOPackageOpenDate: {
    title: 'DTO Package Open Date',
    renderType: 'date',
  },
  Attachments: {
    title: 'Attachments',
    renderType: 'attachments',
  },
  ProductName: {
    title: 'Product Name',
    renderType: 'text',
  },
  QtyReceived: {
    title: 'Qty Received',
    renderType: 'text',
  },
  Status: {
    title: 'Status',
    renderType: 'Tags',
  },
  InspectionRemark: {
    title: 'Inspection Remark',
    renderType: 'text',
  },
  Resolution: {
    title: 'Valid DTO',
    renderType: 'text',
  },
  MovedToNextStage: {
    title: 'Moved to Next Stage',
    renderType: 'date',
  },
  CurrentStatus: {
    title: 'Current Status',
    renderType: 'text',
  },
};

export const Return: Return = {
  Id: {
    title: 'id',
    renderType: 'None',
    isSkippable: true,
  },
  ReturnId: {
    title: 'Return Id',
    renderType: 'Link',
  },
  OrderId: {
    title: 'Order Id',
    renderType: 'Link',
  },
  Status: {
    title: 'Status',
    renderType: 'Tags',
  },
  Source: {
    title: 'Source',
    renderType: 'text',
  },
  CreatedAt: {
    title: 'Created At',
    renderType: 'date',
  },
  Description: {
    title: 'Description',
    renderType: 'text',
  },
  Breached: {
    title: 'Breached',
    renderType: 'Icon',
  },
  AdminAdd: {
    title: 'Admin Add',
    renderType: 'text',
  },
  CreatedBy: {
    title: 'Created By',
    renderType: 'text',
  },
  CustomerEmail: {
    title: 'Customer Email',
    renderType: 'text',
  },
};

export const ReturnApproved: ReturnApprovedData = {
  Id: {
    title: 'id',
    renderType: 'None',
    isSkippable: true,
  },
  OrderId: {
    title: 'Order Id',
    renderType: 'text',
  },
  AwbNumber: {
    title: 'Awb No.',
    renderType: 'Link',
  },
  SalesReturnId: {
    title: 'Sales Return Id',
    renderType: 'Link',
  },
  InvoiceNo: {
    title: 'Source',
    renderType: 'text',
  },
  Location: {
    title: 'Location',
    renderType: 'text',
  },
  VinculumnStatus: {
    title: 'Vinculumn Status',
    renderType: 'text',
  },
  Action: {
    title: 'Action',
    renderType: 'text',
  },
  Transporter: {
    title: 'Transporter',
    renderType: 'text',
  },
  ReturnDate: {
    title: 'Return Date',
    renderType: 'text',
  },
  ReturnNo: {
    title: 'Return No',
    renderType: 'text',
  },
  CreatedAt: {
    title: 'Created At',
    renderType: 'date',
  },
  Cancel: {
    title: 'Cancel',
    renderType: 'Button',
  },
};

export const dataReturn: RowData[] = [
  {
    Id: 1,
    ReturnId: 'R001',
    OrderId: 'O001',
    Status: 'Open',
    Source: 'Website',
    CreatedAt: '23/05/2023',
    Description: 'Not working and it is under warrantY so please return',
    Breached: true,
    AdminAdd: 'Admin 1',
    CreatedBy: '',
    CustomerEmail: '<EMAIL>',
  },
  {
    Id: 2,
    ReturnId: 'R002',
    OrderId: 'O002',
    Status: 'Closed',
    Source: 'Mobile App',
    CreatedAt: '23/05/2023',
    Description: 'Sample description 2',
    Breached: false,
    AdminAdd: 'Admin 2',
    CreatedBy: '',
    CustomerEmail: '<EMAIL>',
  },
];

export const data: RowData[] = [
  {
    Id: 10,
    ReturnId: '123',
    OrderId: '456',
    Status: 'Open',
    Source: 'Web',
    CreatedAt: '2023-05-25',
    Description: 'Some description',
    Breached: true,
  },
  {
    Id: 11,
    ReturnId: '1234',
    OrderId: '456',
    Status: 'Closed',
    Source: 'Web',
    CreatedAt: '2023-05-25',
    Description: 'Some description',
    Breached: true,
  },
  {
    Id: 12,
    ReturnId: '1233',
    OrderId: '456',
    Status: 'Open',
    Source: 'Web',
    CreatedAt: '2023-05-25',
    Description: 'Some description',
    Breached: true,
  },

  // Add more rows as needed
];

export const ReturnApprovedData: RowData[] = [
  {
    Id: 1,
    OrderId: 'ORD001',
    AwbNumber: 'AWB001',
    SalesReturnId: 'SR001',
    InvoiceNo: 'INV001',
    Location: 'Warehouse A',
    VinculumnStatus: 'Shipped',
    Action: 'Process',
    Transporter: 'ABC Transport',
    ReturnDate: '2023-05-29',
    ReturnNo: 'R001',
    CreatedAt: '2023-05-29',
    Cancel: 'Cancel',
  },
  {
    Id: 2,
    OrderId: 'ORD002',
    AwbNumber: 'AWB002',
    SalesReturnId: 'SR002',
    InvoiceNo: 'INV002',
    Location: 'Warehouse B',
    VinculumnStatus: 'Delivered',
    Action: 'Process',
    Transporter: 'XYZ Transport',
    ReturnDate: '2023-05-30',
    ReturnNo: 'R002',
    CreatedAt: '2023-05-30',
    Cancel: 'Cancel',
  },
  // Add more objects as needed
];

export const ReturnDTOData: RowData[] = [
  {
    Id: 1,
    New: 'Edit/Remove',
    AwbNumber: 'AWB001',
    OrderId: 'ORD001',
    ReturnId: 'RET001',
    DTOPackageOpenDate: '2023-05-29',
    Attachments: 'file1.pdf',
    ProductName: 'Product A',
    QtyReceived: '10',
    Status: 'Pending',
    InspectionRemark: 'Requires further inspection',
    Resolution: 'Valid',
    MovedToNextStage: '2023-05-30',
    CurrentStatus: 'In Progress',
  },
  {
    Id: 2,
    New: 'Edit/Remove',
    AwbNumber: 'AWB002',
    OrderId: 'ORD002',
    ReturnId: 'RET002',
    DTOPackageOpenDate: '2023-05-30',
    Attachments: 'file4.pdf',
    ProductName: 'Product B',
    QtyReceived: '5',
    Status: 'Approved',
    InspectionRemark: 'No issues found',
    Resolution: 'Valid',
    MovedToNextStage: '2023-05-31',
    CurrentStatus: 'Completed',
  },
  // Add more objects as needed
];
