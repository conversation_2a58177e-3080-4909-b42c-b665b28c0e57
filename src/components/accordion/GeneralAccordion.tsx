import { Accordion } from '../UI-components/Accordion';
import { IconButton } from '../UI-components/IconButton';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import { MinusIcon, AddIcon, RemoveIcon, EditIconDTO } from '../../utils/icons';
import { ReactNode } from 'react';
import styled from 'styled-components';
import { Row } from '../UI-components/Grid';
import { baseTheme } from '../../themes/theme';
import './investor-relaton/module/css/base.css';

 
const GeneralAccordion = ({
  title,
  expandedSections,
  setExpandedSections,
  children,
  indexing,
  inAction,
  item,
  handleBtnAction,
}: {
  title: string;
  item?: any;
  expandedSections: Array<number>;
  setExpandedSections: React.Dispatch<React.SetStateAction<Array<number>>>;
  children: ReactNode;
  indexing: number;
  inAction?: boolean;
  handleBtnAction?: any;
}) => {
  // console.log(item)
  return (
    <Accordion.Section>
      <Accordion.Header>
        <Accordion.Label>
          <Row alignItems="center">
            {handleBtnAction && inAction && (
              <>
                <IconButton
                  onClick={() => {
                    item ?
                      handleBtnAction('edit', item) :
                      handleBtnAction('edit')
                  }}
                >
                  <EditIconDTO />
                </IconButton>
                <IconButton
                  onClick={() => {
                    item?._id ?
                      handleBtnAction('remove', item._id) :
                      handleBtnAction('remove')
                  }}
                >
                  <RemoveIcon style={{ color: baseTheme.colors.dangerHue }} />
                </IconButton>
              </>
            )}

            {title}
          </Row>
        </Accordion.Label>
        <Tooltip content={expandedSections.includes(indexing) ? 'Contract' : 'Expand'}>
          <IconButton isOrange aria-label="expand row" isPrimary size="small">
            {expandedSections.includes(indexing) ? <MinusIcon /> : <AddIcon />}
          </IconButton>
        </Tooltip>
      </Accordion.Header>
      <Accordion.Panel>{children}</Accordion.Panel>
    </Accordion.Section>
  );
};

export default GeneralAccordion;
