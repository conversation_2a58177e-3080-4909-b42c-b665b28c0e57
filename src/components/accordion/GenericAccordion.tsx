import { Accordion } from '../UI-components/Accordion';
import { IconButton } from '../UI-components/IconButton';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import { MinusIcon, AddIcon } from '../../utils/icons';
import { ReactNode } from 'react';

const CreateFormAccordion = ({
  title,
  expandedSections,
  setExpandedSections,
  children,
  indexing,
}: {
  title: string;
  expandedSections: Array<number>;
  setExpandedSections: React.Dispatch<React.SetStateAction<Array<number>>>;
  children: ReactNode;
  indexing: number;
}) => {
  return (
    <Accordion.Section>
      <Accordion.Header>
        <Accordion.Label>{title}</Accordion.Label>
        <Tooltip content="Expand">
          <IconButton isOrange aria-label="expand row" isPrimary size="small">
            {expandedSections.includes(indexing) ? <MinusIcon /> : <AddIcon />}
          </IconButton>
        </Tooltip>
      </Accordion.Header>
      <Accordion.Panel>{children}</Accordion.Panel>
    </Accordion.Section>
  );
};

export default CreateFormAccordion;
