import React, { useEffect, useState } from 'react';
import GenericAccordion from '../../GenericAccordion';
import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import {
  Field as MultiField,
  Label as MultiLabel,
} from '@zendeskgarden/react-forms';
import { LG } from '../../../UI-components/Typography';
import { IDeliveryLocation } from '../../../../types/new-return-types';
import { Col, Row, SROW } from '../../../UI-components/Grid';
import { baseTheme } from '../../../../themes/theme';
import ReasonTable from '../../../table/new-return-modules/ReasonTable';
import { Tag } from '../../../UI-components/Tags';
import { Span } from '@zendeskgarden/react-typography';
import { TickIcon } from '../../../../utils/icons';
import { ReasonTableColumn } from '../../../table/new-return-modules/columns';
import ActionReimbursement from '../../../table/new-return-modules/ActionReimbursement';
import useAxios from '../../../../hooks/useAxios';
import routes from '../../../../constants/routes';
import useToast from '../../../../hooks/useToast';
import { useGetDelivery } from '../../../../hooks/useReturn';
import returnClient from '../../../../apollo-client/ReturnClient';
import { Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../../constants';
import { useMutation } from '@tanstack/react-query';
import krakendPaths from '../../../../constants/krakendPaths';

const DefaultDelivery = () => {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [selectedItem, setSelectedItem] = useState<IDeliveryLocation>();
  // const [deliveryLocations, setDeliveryLocation] = useState<
  //   IDeliveryLocation[]
  // >([]);
  const [selectedItemId, setSelectedItemId] = useState<
    string | number | undefined
  >(undefined);
  const axios = useAxios();
  const addToast = useToast();

  const { refetch, data: deliveryLocations } = useGetDelivery();

  const { mutate: editLocation, isLoading: loading } = useMutation(
    async (body: any) => {
      const response = await axios.patch(
        `${krakendPaths.RETURN_URL}/admin-api/v1/config/location/${body.id}`,
        {
          ...body,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': constants.RETURN_API_KEY,
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        console.log('Error Adding tier Price', err);
        addToast(
          'error',
          err.message ? err.message : 'Error occured in creating tierPrice',
        );
      },
      onSuccess: (data: any) => {
        refetch();
      },
    },
  );

  useEffect(() => {
    if (deliveryLocations) {
      setSelectedItem(
        deliveryLocations.filter((loc) => loc.enable && loc.default)[0],
      );
    }
  }, [deliveryLocations]);

  return (
    <>
      <Row>
        <Col size={3}>
          <Dropdown
            selectedItem={selectedItem}
            onSelect={(item: IDeliveryLocation) =>
              editLocation({
                id: item.id,
                default: true,
              })
            }
            downshiftProps={{
              itemToString: (item: IDeliveryLocation) => item && item.name,
            }}
          >
            <Field>
              <Label>
                <LG>Default</LG>
              </Label>
              <Select>
                {selectedItem ? selectedItem.name : 'Default Delivery'}
              </Select>
            </Field>
            <Menu>
              {deliveryLocations &&
                deliveryLocations
                  .filter((option) => option.enable)
                  .map((option) => (
                    <Item key={option.id} value={option}>
                      {option.name}
                    </Item>
                  ))}
            </Menu>
          </Dropdown>
        </Col>
        <Col>{loading && <Spinner />}</Col>
      </Row>
      <Row
        style={{ height: `${baseTheme.components.dimension.width.base * 5}px` }}
      ></Row>
      <Row>
        <Col>
          <MultiField>
            <MultiLabel>
              <LG>Enable</LG>
            </MultiLabel>
          </MultiField>
          <SROW>
            {deliveryLocations &&
              deliveryLocations.map(
                (element: IDeliveryLocation, index: number) => {
                  return (
                    <>
                      <Tag
                        key={index}
                        active={element.enable}
                        style={{
                          border: '1px solid #0A5F79',
                          borderRadius: baseTheme.borderRadii.xs,
                          margin: baseTheme.space.xs,
                          padding: baseTheme.space.sm,
                        }}
                        hue={baseTheme.colors.background}
                        onClick={() =>
                          editLocation({
                            id: element.id,
                            enable: !element.enable,
                          })
                        }
                      >
                        {element.enable && (
                          <>
                            <Tag.Avatar>
                              <TickIcon />
                            </Tag.Avatar>
                          </>
                        )}

                        <Span hue={baseTheme.colors.deepBlue}>
                          {element.name}
                        </Span>
                      </Tag>
                    </>
                  );
                },
              )}
          </SROW>
        </Col>
      </Row>
    </>
  );
};

export default DefaultDelivery;
