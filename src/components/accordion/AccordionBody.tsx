import { Accordion } from '../UI-components/Accordion';
import { IconButton } from '../UI-components/IconButton';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import { MinusIcon, AddIcon } from '../../utils/icons';
import { ReactNode } from 'react';

const AccordionBody = ({
  title,
  expandedSections,
  expandedRow,
  children,
  item,
  handleChange,
}: {
  title: string;
  expandedSections: Array<number>;
  expandedRow: number;
  children: ReactNode;
  item?: any;
  handleChange?: any;
}) => {
  return (
    <Accordion.Section onClick={() => handleChange?.(item)}>
      <Accordion.Header>
        <Accordion.Label>{title}</Accordion.Label>
        <Tooltip content="Expand">
          <IconButton isOrange aria-label="expand row" isPrimary size="small">
            {expandedSections.includes(expandedRow) ? (
              <MinusIcon />
            ) : (
              <AddIcon />
            )}
          </IconButton>
        </Tooltip>
      </Accordion.Header>
      <Accordion.Panel>{children} </Accordion.Panel>
    </Accordion.Section>
  );
};

export default AccordionBody;
