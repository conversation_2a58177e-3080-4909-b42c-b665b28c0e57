import React, { useState } from 'react';
import { Col, Row } from '../../../../UI-components/Grid';
import { Field } from '@zendeskgarden/react-forms';
import { MediaInput } from '../../../../UI-components/MediaInput';
import { EditIcon, EditIconDTO, RemoveIcon } from '../../../../../utils/icons';
import { baseTheme } from '../../../../../themes/theme';
import { IInvestorRelation } from '../../../../layouts/investor-relation/InvestorRelationLayout';
import RemoveTextModal from '../../../../modal/investor-relation/main-content/RemoveTextModal';
import EditTextModal from '../../../../modal/investor-relation/main-content/EditTextModal';

const TextIRData = ({ data }: { data: IInvestorRelation }) => {
  const [isVisible, setVisible] = useState<boolean>(false);
  const [isEditable, setIsEditable] = useState<boolean>(false);

  const closeRemoveModal = () => {
    setVisible(false);
  };

  const closeEditModal = () => {
    setIsEditable(false);
  };

  return (
    <>
      <Row>
        <Col>
          <Field>
            <MediaInput
              value={data.text != null ? data.text : ''}
              start={
                <RemoveIcon
                  style={{
                    fill: baseTheme.colors.dangerHue,
                    color: baseTheme.colors.dangerHue,
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    setVisible(true);
                  }}
                />
              }
              end={
                <EditIconDTO
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    setIsEditable(true);
                  }}
                />
              }
              readOnly
            />
          </Field>
        </Col>
      </Row>
      {isVisible && <RemoveTextModal close={closeRemoveModal} data={data} />}
      {isEditable && <EditTextModal close={closeEditModal} data={data} />}
    </>
  );
};

export default TextIRData;
