import React, { useEffect, useState } from 'react';
import { Button } from '../../../../UI-components/Button';
import { baseTheme } from '../../../../../themes/theme';
import { Col, Row } from '../../../../UI-components/Grid';
import {
  ICollapsibleItem,
  IInvestorRelation,
} from '../../../../layouts/investor-relation/InvestorRelationLayout';
import DropdownIR from '../main-content/Dropdown';
import { Accordion } from '../../../../UI-components/Accordion';
import GeneralAccordion from '../../../GeneralAccordion';
import ListIR from '../main-content/List';
import TextIRData from '../main-content/TextIRData';
import { useInvestorRelationContext } from '../../../../../pages/investor-relation/InvestorRelationContext';
import LIstDropdown from './dropdown/LIstDropdown';
import TextDrodown from './dropdown/TextDrodown';
import AddListInDropdownModal from '../../../../modal/investor-relation/main-content/dropdown/AddListInDropdownModal';
import AddTextInDropdown from '../../../../modal/investor-relation/main-content/dropdown/AddTextInDropdown';

const BaseDDAccordion = ({
  data,
  index,
  baseObj,
}: {
  index: number;
  data: ICollapsibleItem;
  baseObj: IInvestorRelation;
}) => {
  const [expandedSections, setExpandedSections] = useState<number[]>([0,1,2,3,4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20]);
  const { results } = useInvestorRelationContext();
  const [showAddListModal, setShowListModal] = useState<boolean>(false);
  const [showAddTextModal, setShowAddTextModal] = useState<boolean>(false);

  const closeListModal = () => {
    setShowListModal(false);
  };

  const closeTextModal = () => {
    setShowAddTextModal(false);
  };

  return (
    <>
      <Row justifyContent="start">
        <Button
          onClick={() => {
            setShowListModal(true);
          }}
          style={{ margin: baseTheme.space.md }}
          isPrimary
        >
          Add List
        </Button>
        <Button
          onClick={() => {
            setShowAddTextModal(true);
          }}
          style={{ margin: baseTheme.space.md }}
          isPrimary
        >
          Add Text
        </Button>
      </Row>
      <Row mt="md">
        <Col>
          <Accordion
            level={4}
            isBare
            isAnimated
            expandedSections={expandedSections}
            onChange={(index) => {
              if (expandedSections.includes(index)) {
                setExpandedSections(
                  expandedSections.filter((n) => n !== index),
                );
              } else {
                setExpandedSections([...expandedSections, index]);
              }
            }}
          >
            {data.list && data.list != null && data.list.length != 0 && (
              <>
                <GeneralAccordion
                  indexing={index}
                  title={'List'}
                  children={
                    <>
                      <LIstDropdown
                        baseObj={baseObj}
                        collapseItem={data}
                        data={data.list}
                      />
                    </>
                  }
                  expandedSections={expandedSections}
                  setExpandedSections={setExpandedSections}
                />
              </>
            )}
            {data.text && data.text != null && (
              <>
                <GeneralAccordion
                  indexing={index}
                  title="Text"
                  children={
                    <>
                      <TextDrodown
                        baseObj={baseObj}
                        collapseItem={data}
                        data={data.text}
                      />
                    </>
                  }
                  expandedSections={expandedSections}
                  setExpandedSections={setExpandedSections}
                />
              </>
            )}
          </Accordion>
        </Col>
      </Row>
      {showAddListModal && (
        <AddListInDropdownModal
          baseObj={baseObj}
          close={closeListModal}
          data={data}
        />
      )}
      {showAddTextModal && (
        <AddTextInDropdown
          baseObj={baseObj}
          close={closeTextModal}
          data={data}
        />
      )}
    </>
  );
};

export default BaseDDAccordion;
