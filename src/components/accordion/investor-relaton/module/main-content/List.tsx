import React, { useState } from 'react';
import {
  Body,
  Cell as _Cell,
  Head,
  HeaderRow,
  Row,
  Table,
  _TableContainer,
  HeaderCell as _HeaderCell,
} from '../../../../UI-components/Table';
import {
  IInvestorRelation,
  IList,
} from '../../../../layouts/investor-relation/InvestorRelationLayout';
import { baseTheme } from '../../../../../themes/theme';
import { Row as GridRow } from '../../../../UI-components/Grid';
import { Grid } from 'lucide-react';
import { IconButton } from '@zendeskgarden/react-buttons';
import { EditIconDTO, RemoveIcon } from '../../../../../utils/icons';
import styled from 'styled-components';
import EditListModal from '../../../../modal/investor-relation/main-content/EditListModal';
import RemoveListModal from '../../../../modal/investor-relation/main-content/RemoveListModal';

const Cell = styled(_Cell)`
  min-width: ${baseTheme.components.dimension.width.base200};
`;

const HeaderCell = styled(_HeaderCell)`
  min-width: ${baseTheme.components.dimension.width.base200};
`;

export const TableContainer = styled(_TableContainer)`
  border-radius: ${baseTheme.borderRadii.xs};
  background-color: white;
`;

const ListIR = ({
  data,
  baseObj,
}: {
  data: IList[];
  baseObj: IInvestorRelation;
}) => {
  const [editvisible, setEditVisible] = useState<boolean>(false);
  const [removeVisible, setRemoveVisible] = useState<boolean>(false);
  const [currentList, setCurrentList] = useState<IList>();

  const closeEditModal = () => {
    setEditVisible(false);
  };

  const closeRemoveModal = () => {
    setRemoveVisible(false);
  };

  return (
    <>
      <TableContainer>
        <Table>
          <Head isSticky>
            <HeaderRow>
              <HeaderCell></HeaderCell>
              <HeaderCell>Title</HeaderCell>
              <HeaderCell>URL</HeaderCell>
            </HeaderRow>
          </Head>
          <Body>
            {data.map((item, index) => (
              <>
                <Row>
                  <Cell>
                    <GridRow justifyContent="center">
                      <IconButton
                        size="medium"
                        onClick={() => {
                          setCurrentList(item);
                          setEditVisible(true);
                        }}
                      >
                        <EditIconDTO />
                      </IconButton>
                      <IconButton
                        onClick={() => {
                          // handleDelete(dtoID);
                          setCurrentList(item);
                          setRemoveVisible(true);
                        }}
                        size="medium"
                      >
                        <RemoveIcon
                          style={{ color: baseTheme.colors.dangerHue }}
                        />
                      </IconButton>
                    </GridRow>
                  </Cell>
                  <Cell>{item.title}</Cell>
                  <Cell>{item.url}</Cell>
                </Row>
              </>
            ))}
          </Body>
        </Table>
      </TableContainer>
      {editvisible && currentList && (
        <EditListModal
          baseObj={baseObj}
          close={closeEditModal}
          listItem={currentList}
        />
      )}
      {removeVisible && currentList && (
        <RemoveListModal
          baseObj={baseObj}
          close={closeRemoveModal}
          listItem={currentList}
        />
      )}
    </>
  );
};

export default ListIR;
