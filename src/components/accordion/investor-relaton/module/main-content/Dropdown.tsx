import React, { useEffect, useState } from 'react';
import { Accordion } from '../../../../UI-components/Accordion';
import {
  ICollapsibleItem,
  IInvestorRelation,
} from '../../../../layouts/investor-relation/InvestorRelationLayout';
import { Col, Row } from '../../../../UI-components/Grid';
import GeneralAccordion from '../../../GeneralAccordion';
import BaseDDAccordion from './BaseDDAccordion';
import EditDropdownModal from '../../../../modal/investor-relation/main-content/EditDropdownModal';
import RemoveDropdownModal from '../../../../modal/investor-relation/main-content/RemoveDropdownModal';

const DropdownIR = ({
  data,
  baseData,
}: {
  data: ICollapsibleItem[];
  baseData: IInvestorRelation;
}) => {
  const [expandedSections, setExpandedSections] = useState<number[]>([0,1,2,3,4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20]);
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [removeVisible, setRemoveVisble] = useState<boolean>(false);
  const [currentItem, setCurrentItem] = useState<ICollapsibleItem>();

  const handleClick = (actionType: string) => {
    if (actionType === 'edit') {
      setEditVisible(true);
    } else if (actionType === 'remove') {
      setRemoveVisble(true);
    }
  };

  const closeEditModal = () => {
    setEditVisible(false);
  };

  const closeRemoveModal = () => {
    setRemoveVisble(false);
  };

  return (
    <>
      <Row>
        <Col>
          <Accordion
            level={4}
            isBare
            isAnimated
            expandedSections={expandedSections}
            onChange={(index) => {
              if (expandedSections.includes(index)) {
                setExpandedSections(
                  expandedSections.filter((n) => n !== index),
                );
              } else {
                setExpandedSections([...expandedSections, index]);
              }
            }}
          >
            {data.map((item, index) => (
              <>
                <GeneralAccordion
                  inAction
                  expandedSections={expandedSections}
                  setExpandedSections={setExpandedSections}
                  indexing={index}
                  title={item.title}
                  handleBtnAction={(str: any) => {
                    handleClick(str);
                    setCurrentItem(item);
                  }}
                  children={
                    <>
                      <BaseDDAccordion
                        baseObj={baseData}
                        index={index}
                        data={item}
                      />
                    </>
                  }
                />
              </>
            ))}
          </Accordion>
        </Col>
      </Row>
      {editVisible && currentItem && (
        <EditDropdownModal
          data={currentItem}
          baseObj={baseData}
          close={closeEditModal}
        />
      )}

      {removeVisible && currentItem && (
        <RemoveDropdownModal
          data={currentItem}
          baseObj={baseData}
          close={closeRemoveModal}
        />
      )}
    </>
  );
};

export default DropdownIR;
