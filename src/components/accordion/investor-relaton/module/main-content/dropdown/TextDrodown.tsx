import React, { useState } from 'react';
import { Row, Col } from '../../../../../UI-components/Grid';
import { Field, MediaInput } from '@zendeskgarden/react-forms';
import { EditIconDTO, RemoveIcon } from '../../../../../../utils/icons';
import { baseTheme } from '../../../../../../themes/theme';
import {
  ICollapsibleItem,
  IInvestorRelation,
} from '../../../../../layouts/investor-relation/InvestorRelationLayout';
import EditTextInDropdown from '../../../../../modal/investor-relation/main-content/dropdown/EditTextInDropdown';
import RemoveTextInDropdown from '../../../../../modal/investor-relation/main-content/dropdown/RemoveTextInDropdown';

const TextDrodown = ({
  data,
  collapseItem,
  baseObj,
}: {
  data: string;
  collapseItem: ICollapsibleItem;
  baseObj: IInvestorRelation;
}) => {
  const [editVisble, setEditVisible] = useState<boolean>(false);
  const [removeVisible, setRemoveVisible] = useState<boolean>(false);

  const closeEditModal = () => {
    setEditVisible(false);
  };

  const closeTextModal = () => {
    setRemoveVisible(false);
  };

  return (
    <>
      <Row>
        <Col>
          <Field>
            <MediaInput
              value={data != null ? data : ''}
              start={
                <RemoveIcon
                  style={{
                    fill: baseTheme.colors.dangerHue,
                    color: baseTheme.colors.dangerHue,
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    setRemoveVisible(true);
                  }}
                />
              }
              end={
                <EditIconDTO
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    setEditVisible(true);
                  }}
                />
              }
              readOnly
            />
          </Field>
        </Col>
      </Row>
      {editVisble && (
        <EditTextInDropdown
          close={closeEditModal}
          baseObj={baseObj}
          data={collapseItem}
        />
      )}
      {removeVisible && (
        <RemoveTextInDropdown
          close={closeTextModal}
          baseObj={baseObj}
          data={collapseItem}
        />
      )}
    </>
  );
};

export default TextDrodown;
