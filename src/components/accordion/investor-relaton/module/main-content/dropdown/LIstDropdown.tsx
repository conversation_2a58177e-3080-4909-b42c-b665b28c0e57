import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>erCell,
  HeaderRow,
  Table,
  _TableContainer,
  Body,
  Row,
  Cell,
} from '../../../../../UI-components/Table';
import { Row as GridRow } from '../../../../../UI-components/Grid';
import { IconButton } from '../../../../../UI-components/IconButton';
import { EditIconDTO, RemoveIcon } from '../../../../../../utils/icons';
import { baseTheme } from '../../../../../../themes/theme';
import {
  ICollapsibleItem,
  IInvestorRelation,
  IList,
} from '../../../../../layouts/investor-relation/InvestorRelationLayout';
import styled from 'styled-components';
import EditListInDropdownModal from '../../../../../modal/investor-relation/main-content/dropdown/EditListInDropdown';
import RemoveListInDropdown from '../../../../../modal/investor-relation/main-content/dropdown/RemoveListInDropdown';

export const TableContainer = styled(_TableContainer)`
  border-radius: ${baseTheme.borderRadii.xs};
  background-color: white;
`;

const LIstDropdown = ({
  baseObj,
  data,
  collapseItem,
}: {
  data: IList[];
  baseObj: IInvestorRelation;
  collapseItem: ICollapsibleItem;
}) => {
  const [editVisble, setEditVisible] = useState<boolean>(false);
  const [removeVisible, setRemoveVisible] = useState<boolean>(false);

  const [currentList, setCurrentList] = useState<IList>();

  const closeEditModal = () => {
    setEditVisible(false);
  };

  const closeRemoveModal = () => {
    setRemoveVisible(false);
  };

  return (
    <>
      <TableContainer>
        <Table>
          <Head isSticky>
            <HeaderRow>
              <HeaderCell></HeaderCell>
              <HeaderCell>Title</HeaderCell>
              <HeaderCell>URL</HeaderCell>
            </HeaderRow>
          </Head>
          <Body>
            {data.map((item, index) => (
              <>
                <Row>
                  <Cell>
                    <GridRow justifyContent="center">
                      <IconButton
                        size="medium"
                        onClick={() => {
                          setEditVisible(true);
                          setCurrentList(item);
                        }}
                      >
                        <EditIconDTO />
                      </IconButton>
                      <IconButton
                        onClick={() => {
                          // handleDelete(dtoID);
                          setRemoveVisible(true);
                          setCurrentList(item);
                        }}
                        size="medium"
                      >
                        <RemoveIcon
                          style={{ color: baseTheme.colors.dangerHue }}
                        />
                      </IconButton>
                    </GridRow>
                  </Cell>
                  <Cell>{item.title}</Cell>
                  <Cell>{item.url}</Cell>
                </Row>
              </>
            ))}
          </Body>
        </Table>
      </TableContainer>
      {editVisble && baseObj.collapsableItems && currentList && (
        <EditListInDropdownModal
          data={collapseItem}
          listItem={currentList}
          baseObj={baseObj}
          close={closeEditModal}
        />
      )}
      {removeVisible && baseObj.collapsableItems && currentList && (
        <RemoveListInDropdown
          data={collapseItem}
          listItem={currentList}
          baseObj={baseObj}
          close={closeRemoveModal}
        />
      )}
    </>
  );
};

export default LIstDropdown;
