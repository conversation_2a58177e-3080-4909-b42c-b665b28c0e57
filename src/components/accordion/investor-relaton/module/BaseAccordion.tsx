import React, { useState } from 'react';
import { Button } from '../../../UI-components/Button';
import { baseTheme } from '../../../../themes/theme';
import { Col, Row } from '../../../UI-components/Grid';
import { IInvestorRelation } from '../../../layouts/investor-relation/InvestorRelationLayout';
import DropdownIR from './main-content/Dropdown';
import { Accordion } from '../../../UI-components/Accordion';
import GeneralAccordion from '../../GeneralAccordion';
import ListIR from './main-content/List';
import TextIRData from './main-content/TextIRData';
import AddDropdownModal from '../../../modal/investor-relation/main-content/AddDropdownModal';
import AddTextModal from '../../../modal/investor-relation/main-content/AddTextModal';
import AddListModal from '../../../modal/investor-relation/main-content/AddListModal';

const BaseAccordion = ({
  data,
  index,
}: {
  data: IInvestorRelation;
  index: number;
}) => {
  const [expandedSections, setExpandedSections] = useState<number[]>([]);
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
  const [textVisible, setTextVisible] = useState<boolean>(false);
  const [listVisible, setListVisible] = useState<boolean>(false);

  const closeDropdownModal = () => {
    setDropdownVisible(false);
  };

  const closeTextModal = () => {
    setTextVisible(false);
  };

  const closeListModal = () => {
    setListVisible(false);
  };
  console.log([...Array(data?.collapsableItems?.length).keys()].map(i => i + 1));

  return (
    <>
      <Row justifyContent="start">
        <Button
          onClick={() => {
            setDropdownVisible(true);
          }}
          style={{ margin: baseTheme.space.md }}
          isPrimary
        >
          Add Dropdown
        </Button>
        <Button
          onClick={() => {
            setListVisible(true);
          }}
          style={{ margin: baseTheme.space.md }}
          isPrimary
        >
          Add List
        </Button>
        <Button
          onClick={() => {
            setTextVisible(true);
          }}
          style={{ margin: baseTheme.space.md }}
          isPrimary
        >
          Add Text
        </Button>
      </Row>
      <Row mt="md">
        <Col>
          <Accordion
            level={4}
            isBare
            isAnimated
            expandedSections={[0,1,2,3,4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20]}
            onChange={(index) => {
              if (expandedSections.includes(index)) {
                setExpandedSections(
                  expandedSections.filter((n) => n !== index),
                );
              } else {
                setExpandedSections([...expandedSections, index]);
              }
            }}
          >
            
            {data.collapsableItems &&
              data.collapsableItems != null &&
              data.collapsableItems.length != 0 && (
                <>
                  <GeneralAccordion
                    indexing={index}
                    title={'Dropdown'}
                    children={
                      <>
                        <DropdownIR
                          baseData={data}
                          data={data.collapsableItems}
                        />
                      </>
                    }
                    expandedSections={[0,1,2,3,4,5,6,7,9,10,11,12,13,14,15,16,17,18,19,20]}
                    setExpandedSections={setExpandedSections}
                  />
                </>
              )}
            {data.list && data.list != null && data.list.length != 0 && (
              <>
                <GeneralAccordion
                  indexing={index}
                  title={'List'}
                  children={
                    <>
                      <ListIR baseObj={data} data={data.list} />
                    </>
                  }
                  expandedSections={expandedSections}
                  setExpandedSections={setExpandedSections}
                />
              </>
            )}
            {data.text && data.text != null && (
              <>
                <GeneralAccordion
                  indexing={index}
                  title="Text"
                  children={
                    <>
                      <TextIRData data={data} />
                    </>
                  }
                  expandedSections={expandedSections}
                  setExpandedSections={setExpandedSections}
                />
              </>
            )}
          </Accordion>
        </Col>
      </Row>
      {dropdownVisible && (
        <AddDropdownModal close={closeDropdownModal} data={data} />
      )}
      {textVisible && <AddTextModal close={closeTextModal} data={data} />}
      {listVisible && <AddListModal close={closeListModal} data={data} />}
    </>
  );
};

export default BaseAccordion;
