import React, { useEffect, useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import {
  Field as _Field,
  Input,
  Label,
  Textarea,
} from '@zendeskgarden/react-forms';
import { IAddress } from '../../../layouts/investor-relation/InvestorBannerLayout';
import { Button } from '../../../UI-components/Button';
import styled from 'styled-components';
import { baseTheme } from '../../../../themes/theme';
import UPDATE_INVESTOR_ADRESS from '../../../../graphql//mutations/editInvestorAddress.gql';
import useToast from '../../../../hooks/useToast';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import krakendPaths from '../../../../constants/krakendPaths';

const Field = styled(_Field)`
  margin-top: ${baseTheme.space.md};
`;

const Address = ({ data }: { data: IAddress }) => {
  const addToast = useToast();
  const [editedAddress, setEditedAddress] = useState<IAddress>({
    address: data.address,
    email: data.email,
    _id: data._id,
    name: data.name,
    telephone: data.telephone,
    title: data.title,
  });

  // const [updateAddress, { loading: mutationLoading }] = useMutation(
  //   UPDATE_INVESTOR_ADRESS,
  //   {
  //     onCompleted: (data) => {
  //       addToast('success', 'Updated successfully');
  //     },
  //     onError: (error) => {
  //       addToast('error', error.message);
  //     },
  //   },
  // );

  const axios = useAxios();

  const { mutate: updateAddress, isLoading: mutationLoading } = useMutation(
    async (obj: any) => {
      const response = await axios.put(
        `${krakendPaths.INVESTORS_URL}/admin-api/v1/investors/address`,
        obj,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err.message}`);
      },
      onSuccess: () => {
        addToast('success', 'Investor address updated successfully');
      },
    },
  );

  const handleInputChange = (prop: string, e: any) => {
    const val = e.target.value;
    setEditedAddress((prev) => ({
      ...prev,
      [prop]: val,
    }));
  };

  const handleSubmit = () => {
    const updatedAddress: IAddress = {
      ...editedAddress,
    };

    const { _id, address, email, name, telephone, title } = updatedAddress;

    if (
      _id.trim() != '' &&
      address.trim() != '' &&
      title.trim() != '' &&
      email.trim() != '' &&
      name.trim() != '' &&
      telephone.trim() != ''
    ) {
      updateAddress({
        ...updatedAddress,
      });
    } else {
      addToast('error', 'Please provide Valid value !!');
    }
  };

  return (
    <>
      <Row>
        <Col>
          <Field>
            <Label>Title</Label>
            <Input
              defaultValue={data.title}
              onChange={(e) => {
                handleInputChange('title', e);
              }}
            />
          </Field>
          <Field>
            <Label>Name</Label>
            <Input
              defaultValue={data.name}
              onChange={(e) => {
                handleInputChange('name', e);
              }}
            />
          </Field>
          <Field>
            <Label>Address</Label>
            <Textarea
              onChange={(e) => {
                handleInputChange('address', e);
              }}
              defaultValue={data.address}
            />
          </Field>
          <Field>
            <Label>Telephone</Label>
            <Input
              defaultValue={data.telephone}
              onChange={(e) => {
                handleInputChange('telephone', e);
              }}
            />
          </Field>
          <Field>
            <Label>Email</Label>
            <Input
              defaultValue={data.email}
              onChange={(e) => {
                handleInputChange('email', e);
              }}
            />
          </Field>
        </Col>
      </Row>
      <Row mt="md" justifyContent="start">
        <Col>
          <Button
            onClick={() => {
              handleSubmit();
            }}
            isPrimary
          >
            Update
          </Button>
        </Col>
      </Row>
    </>
  );
};

export default Address;
