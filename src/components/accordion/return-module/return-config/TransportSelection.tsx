import React, { useEffect, useState } from 'react';
import GenericAccordion from '../../../accordion/GenericAccordion';
import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import {
  Field as MultiField,
  Label as MultiLabel,
} from '@zendeskgarden/react-forms';
import { LG } from '../../../UI-components/Typography';
import {
  IItem,
  ITransporter,
  PaginatedInterface,
} from '../../../../types/types';
import { Col, Row, SROW } from '../../../UI-components/Grid';
import { baseTheme } from '../../../../themes/theme';
import ReasonTable from '../../../table/return-modules/ReasonTable';
import { Tag } from '../../../UI-components/Tags';
import { Span } from '@zendeskgarden/react-typography';
import { TickIcon } from '../../../../utils/icons';
import { ReasonTableColumn } from '../../../table/return-modules/columns';
import ActionReimbursement from '../../../table/return-modules/ActionReimbursement';
import useAxios from '../../../../hooks/useAxios';
import { useMutation, useQuery } from '@tanstack/react-query';
import routes from '../../../../constants/routes';
import useToast from '../../../../hooks/useToast';
import krakendPaths from '../../../../constants/krakendPaths';

const TransporterSelection = () => {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [selectedItem, setSelectedItem] = useState<ITransporter>();
  const [transporters, setTransporters] = useState<ITransporter[]>([]);
  const axios = useAxios();
  const [selectedItemId, setSelectedItemId] = useState<
    string | number | undefined
  >(undefined);
  const addToast = useToast();

  // const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
  //   useQuery({
  //     queryKey: ['return-transporter'],
  //     queryFn: async (): Promise<ITransporter[]> => {
  //       return await axios.get(`${krakendPaths.RETURN_URL}/return-transpoter`, {
  //         headers: {
  //         },
  //       });
  //     },
  //     onError: (err) => {
  //       console.log(err);
  //     },
  //     onSuccess: (data) => {
  //       const transporter: ITransporter[] = data.map(
  //         (transporter: any, index: number) => {
  //           return {
  //             id: transporter.id,
  //             created_at: transporter.created_at,
  //             updated_at: transporter.updated_at,
  //             transpoter: transporter.transpoter,
  //             code: transporter.code,
  //             enable: transporter.enable,
  //             default: transporter.default,
  //             link: transporter.link,
  //             user: transporter.user,
  //           };
  //         },
  //       );

  //       setTransporters(transporter);
  //     },
  //   });

  const { mutate: updateTransporter, isLoading: isTransporterCreating } =
    useMutation(
      async (transport: any) => {
        const response: any = await axios.put(
          // `${routes.FORWARDERS}/return-transpoter/${transport.id}`,
          `${krakendPaths.ORDER_MG_URL}/admin-api/v1/return/transpoter/${transport.id}`,
          { enable: transport.enable },
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            },
          },
        );
        return response;
      },
      {
        onError: (err) => {
          // console.log(err);
          addToast('error', 'Error occured in updating transporter');
        },
        onSuccess: (data) => {
          // console.log(data);
          addToast('success', 'Transporter updated successfully');
        },
      },
    );

  const { mutate: updateDefaultTransporter, isLoading: isDefaultLoading } =
    useMutation(
      async (transport: ITransporter) => {
        const response = await axios.put(
          // `${routes.FORWARDERS}/return-transpoter/${transport.id}`,
          `${krakendPaths.ORDER_MG_URL}/admin-api/v1/return/transpoter/${transport.id}`,
          { default: true },
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            },
          },
        );
        return response;
      },
      {
        onError: (err) => {
          // console.log(err);
          addToast('error', 'Error occured in updating transporter');
        },
        onSuccess: (data) => {
          addToast('success', 'Transporter updated successfully');
        },
      },
    );

  const handleChange = (index: number) => {
    const updatedItems = [...transporters];
    updatedItems[index].enable = !updatedItems[index].enable;
    const updatedSelectedItems = new Set(selectedItems);
    if (updatedSelectedItems.has(updatedItems[index].transpoter)) {
      updatedSelectedItems.delete(updatedItems[index].transpoter);
    } else {
      updatedSelectedItems.add(updatedItems[index].transpoter);
    }
    setSelectedItems(updatedSelectedItems);

    // Set the selected item ID
    setSelectedItemId(updatedItems[index].id);

    const updateTransport = {
      id: updatedItems[index].id,
      enable: updatedItems[index].enable,
    };

    // Call the updateTransporterMutation with the selected item ID
    updateTransporter(updateTransport);
  };

  const onSelectTransporter = (item: ITransporter) => {
    setSelectedItem(item);
    updateDefaultTransporter(item);
  };

  return (
    <>
      <Row>
        <Col size={3}>
          <Dropdown
            selectedItem={selectedItem}
            onSelect={(item: ITransporter) => onSelectTransporter(item)}
            downshiftProps={{
              itemToString: (item: ITransporter) => item && item.transpoter,
            }}
          >
            <Field>
              <Label>
                <LG>Default</LG>
              </Label>
              <Select>
                {selectedItem
                  ? selectedItem.transpoter
                  : 'Select Default Transporter'}
              </Select>
            </Field>
            <Menu>
              {transporters
                .filter((option) => option.enable) // Filter out options with enable = false
                .map((option) => (
                  <Item key={option.id} value={option}>
                    {option.transpoter}
                  </Item>
                ))}
            </Menu>
          </Dropdown>
        </Col>
      </Row>
      <Row
        style={{ height: `${baseTheme.components.dimension.width.base * 5}px` }}
      ></Row>
      <Row>
        <Col>
          <MultiField>
            <MultiLabel>
              <LG>Enable</LG>
            </MultiLabel>
          </MultiField>
          <SROW>
            {transporters.length != 0 &&
              transporters.map((element: ITransporter, index) => {
                return (
                  <>
                    <Tag
                      key={index}
                      active={element.enable}
                      style={{
                        border: '1px solid #0A5F79',
                        borderRadius: baseTheme.borderRadii.xs,
                        margin: baseTheme.space.xs,
                        padding: baseTheme.space.sm,
                      }}
                      hue={baseTheme.colors.background}
                      onClick={() => handleChange(index)}
                    >
                      {element.enable && (
                        <>
                          <Tag.Avatar>
                            <TickIcon />
                          </Tag.Avatar>
                        </>
                      )}

                      <Span hue={baseTheme.colors.deepBlue}>
                        {element.transpoter}
                      </Span>
                    </Tag>
                  </>
                );
              })}
          </SROW>
        </Col>
      </Row>
    </>
  );
};

export default TransporterSelection;
