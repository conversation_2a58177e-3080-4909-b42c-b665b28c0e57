import React, { useEffect, useState } from 'react';
import GenericAccordion from '../../../accordion/GenericAccordion';
import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import {
  Field as MultiField,
  Label as MultiLabel,
} from '@zendeskgarden/react-forms';
import { LG } from '../../../UI-components/Typography';
import {
  IDeliveryLocation,
  IItem,
  ITransporter,
  PaginatedInterface,
} from '../../../../types/types';
import { Col, Row, SROW } from '../../../UI-components/Grid';
import { baseTheme } from '../../../../themes/theme';
import ReasonTable from '../../../table/return-modules/ReasonTable';
import { Tag } from '../../../UI-components/Tags';
import { Span } from '@zendeskgarden/react-typography';
import { TickIcon } from '../../../../utils/icons';
import { ReasonTableColumn } from '../../../table/return-modules/columns';
import ActionReimbursement from '../../../table/return-modules/ActionReimbursement';
import useAxios from '../../../../hooks/useAxios';
import { useMutation, useQuery } from '@tanstack/react-query';
import routes from '../../../../constants/routes';
import useToast from '../../../../hooks/useToast';
import krakendPaths from '../../../../constants/krakendPaths';

const DefaultDelivery = () => {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [selectedItem, setSelectedItem] = useState<IDeliveryLocation>();
  const [deliveryLocations, setDeliveryLocation] = useState<
    IDeliveryLocation[]
  >([]);
  const [selectedItemId, setSelectedItemId] = useState<
    string | number | undefined
  >(undefined);
  const axios = useAxios();
  const addToast = useToast();

  // const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
  //   useQuery({
  //     queryKey: ['delivery-location'],
  //     queryFn: async (): Promise<IDeliveryLocation[]> => {
  //       return await axios.get(`${krakendPaths.RETURN_URL}/location`, {
  //         headers: {
  //         },
  //       });
  //     },
  //     onError: (err) => {
  //       console.log(err);
  //     },
  //     onSuccess: (data) => {
  //       const deliveryLocation: IDeliveryLocation[] = data.map(
  //         (delivery: any, index: number) => {
  //           return {
  //             id: delivery.id,
  //             created_at: delivery.created_at,
  //             updated_at: delivery.updated_at,
  //             name: delivery.name,
  //             code: delivery.code,
  //             enable: delivery.enable,
  //             default: delivery.default,
  //             user: delivery.user,
  //           };
  //         },
  //       );

  //       setDeliveryLocation(deliveryLocation);
  //     },
  //   });

  const { mutate: updateDelivery, isLoading: isTransporterCreating } =
    useMutation(
      async (location: any) => {
        const response = await axios.put(
          // `${routes.FORWARDERS}/return-location/${location.id}`,
          `${krakendPaths.ORDER_MG_URL}/admin-api/v1/return/location/${location.id}`,
          { enable: location.enable },
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            },
          },
        );
        return response;
      },
      {
        onError: (err) => {
          // console.log(err);
          addToast('error', 'Error occured in updating transporter');
        },
        onSuccess: (data) => {
          // console.log(data);
          addToast('success', 'Location updated successfully');
        },
      },
    );

  const { mutate: updateDefaultDelivery, isLoading: isDefaultLoading } =
    useMutation(
      async (location: IDeliveryLocation) => {
        const response = await axios.put(
          // `${routes.FORWARDERS}/return-location/${location.id}`,
          `${krakendPaths.ORDER_MG_URL}/admin-api/v1/return/location/${location.id}`,
          { default: true },
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            },
          },
        );
        return response;
      },
      {
        onError: (err) => {
          // console.log(err);
          addToast('error', 'Error occured in updating transporter');
        },
        onSuccess: (data) => {
          addToast('success', 'Location updated successfully');
        },
      },
    );

  const handleChange = (index: number) => {
    const updatedItems = [...deliveryLocations];
    updatedItems[index].enable = !updatedItems[index].enable;
    const updatedSelectedItems = new Set(selectedItems);
    if (updatedSelectedItems.has(updatedItems[index].name)) {
      updatedSelectedItems.delete(updatedItems[index].name);
    } else {
      updatedSelectedItems.add(updatedItems[index].name);
    }
    setSelectedItems(updatedSelectedItems);
    setSelectedItemId(updatedItems[index].id);

    const updateLocation = {
      id: updatedItems[index].id,
      enable: updatedItems[index].enable,
    };

    updateDelivery(updateLocation);
  };

  const onSelectedLocation = (item: IDeliveryLocation) => {
    setSelectedItem(item);
    updateDefaultDelivery(item);
  };

  return (
    <>
      <Row>
        <Col size={3}>
          <Dropdown
            selectedItem={selectedItem}
            onSelect={(item: IDeliveryLocation) => onSelectedLocation(item)}
            downshiftProps={{
              itemToString: (item: IDeliveryLocation) => item && item.name,
            }}
          >
            <Field>
              <Label>
                <LG>Default</LG>
              </Label>
              <Select>
                {selectedItem ? selectedItem.name : 'Default Delivery'}
              </Select>
            </Field>
            <Menu>
              {deliveryLocations
                .filter((option) => option.enable)
                .map((option) => (
                  <Item key={option.id} value={option}>
                    {option.name}
                  </Item>
                ))}
            </Menu>
          </Dropdown>
        </Col>
      </Row>
      <Row
        style={{ height: `${baseTheme.components.dimension.width.base * 5}px` }}
      ></Row>
      <Row>
        <Col>
          <MultiField>
            <MultiLabel>
              <LG>Enable</LG>
            </MultiLabel>
          </MultiField>
          <SROW>
            {deliveryLocations.length != 0 &&
              deliveryLocations.map((element: IDeliveryLocation, index) => {
                return (
                  <>
                    <Tag
                      key={index}
                      active={element.enable}
                      style={{
                        border: '1px solid #0A5F79',
                        borderRadius: baseTheme.borderRadii.xs,
                        margin: baseTheme.space.xs,
                        padding: baseTheme.space.sm,
                      }}
                      hue={baseTheme.colors.background}
                      onClick={() => handleChange(index)}
                    >
                      {element.enable && (
                        <>
                          <Tag.Avatar>
                            <TickIcon />
                          </Tag.Avatar>
                        </>
                      )}

                      <Span hue={baseTheme.colors.deepBlue}>
                        {element.name}
                      </Span>
                    </Tag>
                  </>
                );
              })}
          </SROW>
        </Col>
      </Row>
    </>
  );
};

export default DefaultDelivery;
