import { Field, Label as _Label, Radio } from '@zendeskgarden/react-forms';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Col, Row } from '../../UI-components/Grid';
import { XMD } from '../../UI-components/Typography';
import { baseTheme } from '../../../themes/theme';
import styled from 'styled-components';
import { Button } from '../../UI-components/Button';
import AddAddressModal from '../../modal/new-order/AddAddress';
import EditAddressModal from '../../modal/new-order/EditAddress';
import {
  CartData,
  useNewOrderContext,
} from '../../../pages/order/NewOrderContext';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../hooks/useCommonAxios';
import routes from '../../../constants/routes';
import { useParams } from 'react-router-dom';
import { useCustomerContext } from '../../../pages/customer/CustomerContext';
import RemoveAddressModal from '../../modal/customer/RemoveAddressModal';
import useToast from '../../../hooks/useToast';
import { useMutation } from '@tanstack/react-query';
import { convertToSentenceCase } from '../../../utils/convertToSentenceCase';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Spacer = styled(Row)`
  height: ${(p) => baseTheme.components.dimension.height.sm};
`;

const SmallSpacer = styled(Row)`
  height: ${(p) => baseTheme.components.dimension.height.xsm};
`;

const Label = styled(_Label)`
  & .cIcMRP:checked ~ .StyledRadioLabel-sc-1aq2e5t-0::before {
    border-color: rgb(0, 0, 255); /* Replace with your deep blue RGB values */
    background-color: rgb(
      0,
      0,
      255
    ); /* Replace with your deep blue RGB values */
  }
`;

const AddressDetailAccordion = () => {
  const [radioValue, setRadioValue] = useState('annual');

  useEffect(() => {
    baseTheme.colors.primaryHue = baseTheme.colors.deepBlue;
  }, []);

  const [showAddAddress, setShowAddAddress] = useState<boolean>(false);
  const [deleteAddress, setDeleteAddress] = useState<boolean>(false);
  const [editShowAddress, setEditAddAddress] = useState<boolean>(false);

  const [addressList, setAddressList] = useState<any[]>([]);

  const { customerId } = useParams();

  const {
    setSelectedAddress,
    selectedAddress,
    cartId,
    setIsOrderHaveAddress,
    refetchProductData,
    shippingData,
    availableCartData,
  } = useNewOrderContext();
  const isMountAdress = useRef(true);
  const cartAdressId =
    availableCartData?.shipping_addresses[0]?.customer_address_id || null;

  const isAddressExist = useMemo(() => {
    return addressList.findIndex((item) => item.id === cartAdressId);
  }, [addressList, cartAdressId]);

  // Set initial radio value when address list is loaded and there's a pre-selected address
  useEffect(() => {
    if (isAddressExist != -1 && isMountAdress.current) {
      setRadioValue(isAddressExist.toString());
      isMountAdress.current = false;
    }
  }, [isAddressExist]);

  const axios = useAxios();

  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 100,
  });

  const { refetch, isLoading: isGettingAddress } = useQuery({
    queryKey: ['get-customer-address', customerId],
    queryFn: async () => {
      const response = await axios.get(
        `${krakendPaths.CUSTOMER_URL}/admin-api/v1/customers/${customerId}/addresses`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': '8tyeXkUYPg2JiP8AXBTj9DuIuTfO3kO9cm',
          },
          params: {
            ...filters,
          },
        },
      );

      return response;
    },
    enabled: false,
    onSuccess: (response: any) => {
      // console.log('response', response);
      setAddressList(response.data);
    },
    onError: (err) => {
      console.log('error', err);
    },
  });

  useEffect(() => {
    if (filters) {
      refetch();
    }
  }, [filters]);

  const addToast = useToast();

  const { mutate: updateAddress, isLoading: isCreating } = useMutation(
    async (shippingData: CartData) => {
      const response = axios.put(
        `${krakendPaths.CART_URL}/admin-api/v1/carts/customers/${customerId}/addresses/shipping`,
        {
          ...shippingData,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        // console.log('Error', `${convertToSentenceCase(err.message)}`);
        addToast('error', `${convertToSentenceCase(err.message)}`);
      },
      onSuccess: (data: any) => {
        addToast('success', 'Customer Address added successfully');
        refetch();
        setIsOrderHaveAddress(true);
        close();
        refetchProductData();
      },
    },
  );

  useEffect(() => {
    const selectedAddressItem = addressList.find(
      (item, index) => index.toString() === radioValue,
    );

    if (selectedAddressItem) {
      const currentAddressCart: CartData = {
        cart_id: cartId,
        shipping_addresses: [
          {
            address: {
              alternate_mobile: selectedAddressItem?.alternate_telephone,
              city: selectedAddressItem?.city,
              company: selectedAddressItem?.company,
              country_code: selectedAddressItem?.country_id,
              firstname: selectedAddressItem?.firstname,
              gst_id: selectedAddressItem?.vat_id,
              lastname: selectedAddressItem?.lastname,
              postcode: selectedAddressItem?.postcode,
              region: selectedAddressItem?.region,
              region_code: selectedAddressItem?.postcode,
              region_id: selectedAddressItem?.region_id,
              save_in_address_book: true,
              street: selectedAddressItem?.street,
              telephone: selectedAddressItem?.telephone,
            },
            customer_address_id: selectedAddressItem.id,
            same_as_shipping: true,
            user_for_shipping: true,
          },
        ],
      };

      updateAddress(currentAddressCart);
    }
  }, [radioValue, addressList]);

  return (
    <>
      <Row>
        <Col size={12}>
          <div role="group" aria-label="Address List">
            {addressList.map((item, index) => (
              <>
                <Row key={item.id}>
                  <Col size={8}>
                    <Field>
                      <Radio
                        name="default example"
                        value={`${index}`}
                        checked={
                          radioValue === `${index}` ||
                          (availableCartData?.shipping_addresses?.length > 0 &&
                            availableCartData?.shipping_addresses[0]
                              ?.customer_address_id === item.id)
                        }
                        onChange={(event) => setRadioValue(event.target.value)}
                      >
                        <Label>
                          {`${item.street} ${item.city} ${item.region} ${item.country_code} ${item.postcode}  `}
                        </Label>
                      </Radio>
                    </Field>
                  </Col>
                  <Col size={4}>
                    <Row>
                      <XMD
                        hue="primary"
                        onClick={() => {
                          setEditAddAddress(true);
                          setSelectedAddress(item);
                        }}
                        style={{ cursor: 'pointer' }}
                      >
                        <u>Edit </u>
                      </XMD>
                      <XMD
                        onClick={() => {
                          setDeleteAddress(true);
                          setSelectedAddress(item);
                        }}
                        style={{ marginLeft: '8px', cursor: 'pointer' }}
                        hue="secondary"
                      >
                        <u>Delete</u>
                      </XMD>
                    </Row>
                  </Col>
                </Row>
                <SmallSpacer />
              </>
            ))}

            {/* <Row>
              <Col size={8}>
                <Field>
                  <Radio
                    name="default example"
                    value="perennial"
                    checked={radioValue === 'perennial'}
                    onChange={(event) => setRadioValue(event.target.value)}
                  >
                    <Label>
                      Aditya Kumar Rai, Sitapur Road, Jankipuram, Lucknow, India
                      - 226578
                    </Label>
                  </Radio>
                </Field>
              </Col>
              <Col size={4}>
                <Row>
                  <XMD
                    style={{ cursor: 'pointer' }}
                    hue="primary"
                    onClick={() => {
                      setEditAddAddress(true);
                    }}
                  >
                    <u>Edit </u>
                  </XMD>
                  <XMD style={{ marginLeft: '8px' }} hue="secondary">
                    <u>Delete</u>
                  </XMD>
                </Row>
              </Col>
            </Row> */}
          </div>
        </Col>

        <Col>
          <Spacer />
          <Button
            onClick={() => {
              setShowAddAddress(true);
            }}
            isPrimary
          >
            Add new address
          </Button>
        </Col>
      </Row>
      {showAddAddress && (
        <AddAddressModal
          refetch={refetch}
          close={() => {
            setShowAddAddress(false);
          }}
        />
      )}

      {editShowAddress && selectedAddress && (
        <EditAddressModal
          close={() => {
            setEditAddAddress(false);
            refetch();
          }}
          setRadioValue={setRadioValue}
        />
      )}

      {deleteAddress && selectedAddress && (
        <>
          <RemoveAddressModal
            addressData={selectedAddress}
            close={() => {
              setDeleteAddress(false);
              refetch();
            }}
          />
        </>
      )}
    </>
  );
};

export default AddressDetailAccordion;
