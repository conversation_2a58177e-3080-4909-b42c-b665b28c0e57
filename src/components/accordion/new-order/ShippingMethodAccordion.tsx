import React, { useEffect, useState } from 'react';
import { baseTheme } from '../../../themes/theme';
import { Col, Row, SmallSpacer, Spacer } from '../../UI-components/Grid';
import { LG, MD, XMD } from '../../UI-components/Typography';
import { Field, Label, Radio } from '@zendeskgarden/react-forms';
import { Span } from '@zendeskgarden/react-typography';
import { useNewOrderContext } from '../../../pages/order/NewOrderContext';

const ShippingMethodAccordion = () => {
  useEffect(() => {
    setTimeout(() => {
      baseTheme.colors.primaryHue = baseTheme.colors.deepBlue;
    }, 500);
  }, []);

  const [radioValue, setRadioValue] = useState('delivery-charge');

  const { paymentInfo } = useNewOrderContext();

  useEffect(() => {
    if (paymentInfo) {
      paymentInfo?.totals?.total_segments?.map((item: any) => {
        if (item?.code === 'shipping') {
          if (item?.value === 0) {
            setRadioValue('free-delivery');
          } else {
            setRadioValue('delivery-charge');
          }
        }
      });
    }
  }, [paymentInfo]);

  return (
    <Row>
      <Col>
        <MD isBold hue="primary">
          Shipping methods
        </MD>
        <SmallSpacer />
        <div role="group" aria-label="Choose a shipping method">
          <Row>
            <Col>
              <Label>Delivery charge</Label>
            </Col>
          </Row>
          <SmallSpacer />

          <Row>
            <Col offset={0.5}>
              <Field>
                <Radio
                  name="delivery-charge"
                  value="delivery-charge"
                  checked={radioValue === 'delivery-charge'}
                  // onChange={(event) => setRadioValue(event.target.value)}
                >
                  <Label>Delivery charge</Label>
                </Radio>
              </Field>
            </Col>
          </Row>
          <SmallSpacer />
          <Row>
            <Col>
              <Label style={{ fontWeight: 'none' }}>
                Free shipping <Span isBold>₹ 0.00</Span>
              </Label>
            </Col>
          </Row>
          <SmallSpacer />
          <Row>
            <Col offset={0.5}>
              <Field>
                <Radio
                  name="free-delivery"
                  value="free-delivery"
                  checked={radioValue === 'free-delivery'}
                  // onChange={(event) => setRadioValue(event.target.value)}
                >
                  <Label style={{ fontWeight: 'none' }}>Free shipping</Label>
                </Radio>
              </Field>
            </Col>
          </Row>
        </div>
      </Col>
    </Row>
  );
};

export default ShippingMethodAccordion;
