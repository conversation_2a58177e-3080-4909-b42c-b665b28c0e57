import React, { useEffect, useState } from 'react';
import { Button } from '../../UI-components/Button';
import {
  Body,
  Cell,
  GroupRow,
  Head,
  HeaderCell,
  HeaderRow,
  Table,
} from '@zendeskgarden/react-tables';
import { baseTheme } from '../../../themes/theme';
import { Spacer } from '../../UI-components/Grid';
import { RemoveRedIcon } from '../../../utils/icons';
import ThumbnailIcon from '../../../utils/icons/thumbnail.svg';
import {
  ProductColumns,
  UpSellsProductTableColumns as columns,
} from '../../table/product/Columns';
import CategorySearchFilter from '../../modal/catalog-service/CategorySearchModal';
import useToast from '../../../hooks/useToast';
import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import styled from 'styled-components';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { TableHolder } from '../../UI-components/Table';
import { DataTable } from '../../table/product/DataTable';
import NothingToshow from '../../UI-components/NothingToShow';
import routes from '../../../constants/routes';
import { Product } from '../../modal/catalog-service/types';
import { Col, Row } from '@zendeskgarden/react-grid';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const TableCard = styled.div`
  margin: 10px 0px;
`;
const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;

const UpSellsProducts = () => {
  const [isSearch, setIsSearch] = useState(false);
  const addToast = useToast();
  const { contextProdData, setContextProdData, setContextUpdateProdData } =
    useProductContext();
  const [selectedProd, setSelectedProd] = useState<Product[]>();
  const [data, setData] = useState<ProductColumns[]>([]);
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const [upsellProd, setUpsellProd] = useState<number[]>([]);
  const upsellData = contextProdData.product_links?.upsell;

  useEffect(() => {
    if (upsellData && Array.isArray(upsellData)) {
      const productIds = upsellData.map((item) => item.product_id);
      setUpsellProd(productIds);
    } else {
      setUpsellProd([]);
    }
  }, [upsellData]);

  const { data: upsells, refetch } = useQuery({
    queryKey: ['get-upsells-prod'],
    queryFn: async (): Promise<any> => {
      const postdata = {
        product_ids: upsellProd,
        filters: {
          // status: 0,
          // type_id: 'simple',
        },
        pagination: {
          page: 1,
        },
      };
      if (upsellProd && upsellProd.length > 0) {
        try {
          const response = await axios.post(
            // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/search`,
            `${constants.CATALOG_URL}/v1/catalog-admin/search-products`,
            postdata,
            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem('access-token')}`,
                'x-api-key': `${constants.CATALOG_KEY}`,
                'Content-Type': 'application/json',
              },
            },
          );
          return response.data;
        } catch (error) {
          throw new Error('Failed to fetch UpSells Product');
        }
      } else {
        // console.log('Related Product Id not present. ')
        return {
          item_count: 0,
          items: [],
          page_no: 1,
          page_size: 1,
          pages_count: null,
        };
      }
    },
    onError: (err) => {
      // console.log('UpSells Product error', err);
      addToast('error', `UpSells Product error: ${err}`);
    },
    onSuccess: (data) => {
      // console.log('related product data: ', data);
      setData(data.items);
    },
    enabled: upsellProd && upsellProd.length > 0,
  });

  const table = useReactTable({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: {
      columnVisibility,
      rowSelection,
    },
  });
  useEffect(() => {
    table.setPageSize(100);
  }, []);

  useEffect(() => {
    refetch();
  }, [upsellProd, contextProdData]);

  return (
    <div
      style={{
        marginBottom: `${baseTheme.paddings.xl}`,
      }}
    >
      <p>
        An up-sell item is offered to the customer as a pricier or
        higher-quality alternative to the product the customer is looking at.
      </p>
      <Button isPrimary isOrange onClick={() => setIsSearch(!isSearch)}>
        Add Up Sells Products
      </Button>
      <Spacer />
      <Container>
        <TableCard>
          <TableHolder>
            {table.getRowModel()?.rows?.length ? (
              <>
                <DataTable
                  table={table}
                  columns={columns}
                  data={upsells?.items}
                />
              </>
            ) : (
              <NothingToshow divHeight="55vh" />
            )}
          </TableHolder>
        </TableCard>
      </Container>
      <div style={{ overflowX: 'auto' }}>
        {isSearch && (
          <CategorySearchFilter
            close={() => {
              setIsSearch(false);
            }}
            setProducts={(products) => {
              const upSellProducts = products.map((product, index) => ({
                product_id: product.id,
                position: 0,
              }));
              // console.log("upSellProducts: ",upSellProducts)

              setContextProdData((prevState) => ({
                ...prevState,
                product_links: {
                  ...prevState.product_links,
                  upsell: prevState.product_links?.upsell
                    ? [...prevState.product_links.upsell, ...upSellProducts]
                    : upSellProducts,
                },
              }));
              setContextUpdateProdData((prevState) => ({
                ...prevState,
                product_links: {
                  ...prevState.product_links,
                  upsell: prevState.product_links?.upsell
                    ? [...prevState.product_links.upsell, ...upSellProducts]
                    : upSellProducts,
                },
              }));
              // updateUpSellProd({id: contextProdData?.id, relId: products[0].id})
            }}
          />
        )}
      </div>
    </div>
  );
};

export default UpSellsProducts;
