import React, { useEffect, useState } from 'react';
import { Button } from '../../UI-components/Button';
import {
  Body,
  Cell,
  GroupRow,
  Head,
  HeaderCell,
  HeaderRow,
  Row,
  Table,
} from '@zendeskgarden/react-tables';
import { baseTheme } from '../../../themes/theme';
import { Spacer } from '../../UI-components/Grid';
import { RemoveRedIcon } from '../../../utils/icons';
import ThumbnailIcon from '../../../utils/icons/thumbnail.svg';
import CategorySearchFilter from '../../modal/catalog-service/CategorySearchModal';
import axios from 'axios';
import { useMutation, useQuery } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import {
  ProductColumns,
  CrossSellProductTableColumns as columns,
} from '../../table/product/Columns';
import styled from 'styled-components';
import { TableHolder } from '../../UI-components/Table';
import NothingToshow from '../../UI-components/NothingToShow';
import { DataTable } from '../../table/product/DataTable';
import routes from '../../../constants/routes';
import { Product } from '../../modal/catalog-service/types';
import { Col } from '@zendeskgarden/react-grid';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const TableCard = styled.div`
  margin: 10px 0px;
`;
const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;

const CrossSellsProducts = () => {
  const [isSearch, setIsSearch] = useState(false);
  const [data, setData] = useState<ProductColumns[]>([]);
  const { contextProdData, setContextProdData, setContextUpdateProdData } =
    useProductContext();
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const addToast = useToast();

  const [relatedProd, setRelatedProd] = useState<number[]>([]);
  const relatedData = contextProdData.product_links?.crosssell;

  useEffect(() => {
    if (relatedData && Array.isArray(relatedData)) {
      const productIds = relatedData.map((item) => item.product_id);
      setRelatedProd(productIds);
    } else {
      setRelatedProd([]);
    }
  }, [relatedData]);

  const { data: crossSells, refetch } = useQuery({
    queryKey: ['get-crosssell-prod'],
    queryFn: async (): Promise<any> => {
      const postdata = {
        product_ids: relatedProd,
        filters: {
          // status: 0,
          // type_id: 'simple',
        },
        pagination: {
          page: 1,
        },
      };
      if (relatedProd && relatedProd.length > 0) {
        try {
          const response = await axios.post(
            // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/search`,
            `${constants.CATALOG_URL}/v1/catalog-admin/search-products`,
            postdata,
            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem('api-token')}`,
                'x-api-key': `${constants.CATALOG_KEY}`,
                // 'Content-Type': 'application/json',
              },
            },
          );
          return response.data;
        } catch (error) {
          throw new Error('Failed to fetch Cross Sell data');
        }
      } else {
        return {
          item_count: 0,
          items: [],
          page_no: 1,
          page_size: 1,
          pages_count: null,
        };
      }
    },
    onError: (err) => {
      addToast('error', `Cross Sell product error: ${err}`);
    },
    onSuccess: (data) => {
      setData(data.items);
    },
    enabled: relatedProd && relatedProd.length > 0,
  });

  const table = useReactTable({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: {
      columnVisibility,
      rowSelection,
    },
  });
  useEffect(() => {
    table.setPageSize(100);
  }, []);

  useEffect(() => {
    refetch();
  }, [relatedProd, contextProdData]);

  return (
    <div
      style={{
        marginBottom: `${baseTheme.paddings.xl}`,
      }}
    >
      <p>
        These "impulse-buy" products appear next to the shopping cart as
        cross-sells to the items already in the shopping cart.
      </p>
      <Button isPrimary isOrange onClick={() => setIsSearch(!isSearch)}>
        Add Cross Sells Products
      </Button>
      <Spacer />
      <Container>
        <TableCard>
          <TableHolder>
            {table.getRowModel()?.rows?.length ? (
              <>
                <DataTable
                  table={table}
                  columns={columns}
                  data={crossSells?.items}
                />
              </>
            ) : (
              <NothingToshow divHeight="55vh" />
            )}
          </TableHolder>
        </TableCard>
      </Container>
      <div style={{ overflowX: 'auto' }}>
        {isSearch && (
          <CategorySearchFilter
            close={() => {
              setIsSearch(false);
            }}
            setProducts={(products) => {
              const crossSellProducts = products.map((product, index) => ({
                product_id: product.id,
                position: 0,
              }));

              setContextProdData((prevState) => ({
                ...prevState,
                product_links: {
                  ...prevState.product_links,
                  crosssell: prevState.product_links?.crosssell
                    ? [
                        ...prevState.product_links.crosssell,
                        ...crossSellProducts,
                      ]
                    : crossSellProducts,
                },
              }));
              setContextUpdateProdData((prevState) => ({
                ...prevState,
                product_links: {
                  ...prevState.product_links,
                  crosssell: prevState.product_links?.crosssell
                    ? [
                        ...prevState.product_links.crosssell,
                        ...crossSellProducts,
                      ]
                    : crossSellProducts,
                },
              }));
            }}
          />
        )}
      </div>
    </div>
  );
};

export default CrossSellsProducts;
