import { ColumnDef } from '@tanstack/react-table';
import { Link as _Link } from 'react-router-dom';
import { Tag } from '../../UI-components/Tags';
import {
  CircleIcon,
  CrossFilledIcon,
  ItemIcon,
  RemoveIcon,
  TableEditIcon,
  TickFilledIcon,
} from '../../../utils/icons';
import { baseTheme } from '../../../themes/theme';
import { Span } from '@zendeskgarden/react-typography';
import { convertToSentenceCase } from '../../../utils/convertToSentenceCase';
import ReactTimeago from 'react-timeago';
import styled from 'styled-components';
import { Checkbox, Field, Label } from '@zendeskgarden/react-forms';
import { useContext, useState } from 'react';
import TableDropdown from '../../dropdown/TableDropdown';
import { IReason, IReturnAction, ITableDropdown } from '../../../types/types';
import { Col, Row } from '../../UI-components/Grid';
import { Toggle } from '../../UI-components/Toggle';
import { IconButton } from '../../UI-components/IconButton';
import { Button as ZButton } from '@zendeskgarden/react-buttons';
import useAxios from '../../../hooks/useAxios';
import routes from '../../../constants/routes';
import { format, parse } from 'date-fns';
import EditReturnDTO from '../../modal/new-return-module/EditReturnDTO';
import { OrderContext } from '../../../hooks/useContext';
import ReturnDTOModal from '../../modal/new-return-module/return-item-request/ReturnDTO';
import useToast from '../../../hooks/useToast';
import { pageRoutes } from '../../navigation/RouteConfig';
import AddNonReturnable from '../../modal/new-return-module/non-returnable/AddNonReturnable';
import RemoveReturnApproved from '../../modal/new-return-module/return-approved/RemoveReturnApproved';
import RemoveNonReturnable from '../../modal/new-return-module/non-returnable/RemoveNonReturnable';
import RemoveDTO from '../../modal/new-return-module/return-item-request/RemoveDTO';
import constants from '../../../constants';
import { formatDateIntoDDMMYYYYAndTime } from '../../../helpers/helper';
import SearchReturnIdModal from '../../modal/new-return-module/SearchReturnId';
import {
  CompensationContextType,
  useCompensation,
} from '../../layouts/new-return-module/CompensationLayout';
import {
  useQueryClient,
  useMutation as useRestMutation,
} from '@tanstack/react-query';
import krakendPaths from '../../../constants/krakendPaths';
import ReadMore from '../../UI-components/ReadMore';
import { Button } from '../../UI-components/Button';

interface HeaderDivProps {
  columnWidth: string;
  isCenter?: boolean;
}

const HeaderDiv = styled.div<HeaderDivProps>`
  width: ${(props) => props.columnWidth};
  ${(props) =>
    props.isCenter ? `justify-content:center; text-align: center;` : ''}
`;

const Link = styled(_Link)`
  text-decoration: none;
  color: ${baseTheme.colors.deepBlue};
  cursor: pointer;
  text-align: center;
`;

export type ReturnTableColumn = {
  id: number;
  return_id: number;
  order_id: string;
  status: string;
  source: string;
  created_at: Date;
  description: string;
  breached: boolean;
  admin_add: boolean;
  customer_email: string;
  updated_at: string;
};

export const columns: ColumnDef<any>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'return_id',
    header: 'Return Id',
    cell: ({ row }) => {
      const returnId = parseInt(row.getValue('id'));
      const linkURL = `${pageRoutes['GO_TO_NEW_RETURN_ITEM_GENERAL']}/${returnId}`;

      const { setOrderId } = useContext(OrderContext);

      return (
        <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
          <Link to={linkURL}>{returnId}</Link>
        </HeaderDiv>
      );
    },
    enablePinning: true,
  },
  {
    accessorKey: 'order_id',
    header: 'Order Id',
    cell: ({ row }) => {
      const returnId: string = row.getValue('order_id');
      const linkUrl = `/${constants.PREFIX}/${routes.ORDERS}/${returnId}`;
      return (
        <>
          <Link to={linkUrl}>{returnId}</Link>
        </>
      );
    },
    enablePinning: true,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status: string = row.getValue('status');
      return (
        <>
          {status === 'open' ? (
            <>
              <Tag isOpen isPill hue={baseTheme.colors.transparentGreen}>
                <Tag.Avatar>
                  <CircleIcon
                    color={baseTheme.colors.solidGreen}
                    style={{
                      width: baseTheme.iconSizes.xs,
                      height: baseTheme.iconSizes.xs,
                    }}
                  />
                </Tag.Avatar>
                <Span>{convertToSentenceCase(status)}</Span>
              </Tag>
            </>
          ) : (
            <>
              <Tag isPill hue={baseTheme.colors.transparentRed}>
                <Tag.Avatar>
                  <CircleIcon
                    color={baseTheme.colors.solidRed}
                    style={{
                      width: baseTheme.iconSizes.xs,
                      height: baseTheme.iconSizes.xs,
                    }}
                  />
                </Tag.Avatar>
                <Span>{convertToSentenceCase(status)}</Span>
              </Tag>
            </>
          )}
        </>
      );
    },
    enablePinning: true,
  },
  {
    accessorKey: 'source',
    header: 'Source',
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated At',
    cell: ({ row }) => {
      const updatedAt = new Date(row.getValue('updated_at'));

      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base150}>
            {/* {updatedAt} */}
            {formatDateIntoDDMMYYYYAndTime(updatedAt)}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: ({ row }) => {
      const createdAt = new Date(row.getValue('created_at'));

      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base150}>
            {formatDateIntoDDMMYYYYAndTime(createdAt)}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const description: string = row.getValue('description');
      if (description && description.length > 0)
        return <ReadMore text={description} limit={50} />;
    },
  },
  {
    accessorKey: 'tat',
    header: 'TAT',
    cell: ({ row, table }) => {
      const created_at: Date = new Date(row.getValue('created_at'));
      created_at.setUTCHours(0, 0, 0, 0);

      const today_date: Date = new Date();
      today_date.setUTCHours(0, 0, 0, 0);

      const closed_date: Date = new Date(row.getValue('updated_at'));
      closed_date.setUTCHours(0, 0, 0, 0);

      let diffInMs = 0;
      if (row.original?.status === 'open') {
        diffInMs = today_date.getTime() - created_at.getTime();
      } else if (row.original?.status === 'closed') {
        diffInMs = closed_date.getTime() - created_at.getTime();
      }

      const diffTime = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
      return <>{diffTime}</>;
      // if (result && result.length > 0) {
      //   let slaHour = result?.[0]?.sla_hours;

      //   if (breachedValue === 'open' || breachedValue === 'partial_open') {
      //     if (
      //       (new Date().getTime() - new Date(created_at).getTime()) /
      //         1000 /
      //         60 /
      //         60 >
      //       slaHour
      //     )
      //       return <CrossFilledIcon />;
      //   } else if (breachedValue === 'closed') {
      //     if (
      //       (new Date(updated_at).getTime() - new Date(created_at).getTime()) /
      //         1000 /
      //         60 /
      //         60 >
      //       slaHour
      //     )
      //       return <CrossFilledIcon />;
      //   }
      // }
      // return <TickFilledIcon />;

      // return <>{breachedValue ? <TickFilledIcon /> : <CrossFilledIcon />}</>;
    },
  },
  {
    accessorKey: 'is_sla_breached',
    header: 'SLA',
    cell: ({ row }) => {
      const isSlaBreached: boolean = row.getValue('is_sla_breached');

      if (isSlaBreached == null) {
        return (
          <Tag isPill hue={baseTheme.colors.transparentYellow}>
            <Tag.Avatar>
              <CircleIcon
                color={baseTheme.colors.yellow}
                style={{
                  width: baseTheme.iconSizes.xs,
                  height: baseTheme.iconSizes.xs,
                }}
              />
            </Tag.Avatar>
            <Span>Pending</Span>
          </Tag>
        );
      } else if (isSlaBreached == false) {
        return (
          <Tag isPill hue={baseTheme.colors.transparentGreen}>
            <Tag.Avatar>
              <CircleIcon
                color={baseTheme.colors.solidGreen}
                style={{
                  width: baseTheme.iconSizes.xs,
                  height: baseTheme.iconSizes.xs,
                }}
              />
            </Tag.Avatar>
            <Span>Not Breached</Span>
          </Tag>
        );
      }
      if (isSlaBreached == true) {
        return (
          <Tag isPill hue={baseTheme.colors.transparentRed}>
            <Tag.Avatar>
              <CircleIcon
                color={baseTheme.colors.solidRed}
                style={{
                  width: baseTheme.iconSizes.xs,
                  height: baseTheme.iconSizes.xs,
                }}
              />
            </Tag.Avatar>
            <Span>Breached</Span>
          </Tag>
        );
      }
    },
  },
  {
    accessorKey: 'total_items',
    header: 'Total Items',
    cell: ({ row }) => {
      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
            {(row.original?.approved_item_count || 0) +
              (row.original?.pending_item_count || 0) +
              (row.original?.rejected_item_count || 0)}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'approved_items',
    header: 'Approved Items',
    cell: ({ row }) => {
      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
            {row.original?.approved_item_count || 0}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'pending_items',
    header: 'Pending Items',
    cell: ({ row }) => {
      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
            {row.original?.pending_item_count || 0}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'awaited_information',
    header: 'Awaited Information',
    cell: ({ row }) => {
      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
            {row.original?.awaited_information_count || 0}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'in_progress',
    header: 'In Progress',
    cell: ({ row }) => {
      return (
        <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
          {row.original?.in_progress_count || 0}
        </HeaderDiv>
      );
    },
  },
  {
    accessorKey: 'rejected_items',
    header: 'Rejected Items',
    cell: ({ row }) => {
      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
            {row.original?.rejected_item_count || 0}
          </HeaderDiv>
        </>
      );
    },
  },
  // {
  //   accessorKey: 'adminAdd',
  //   header: 'Admin Add',
  //   cell: ({ row }) => {
  //     const adminAdd = row.getValue('adminAdd');
  //     return (
  //       <>
  //         <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
  //           {adminAdd ? 'True' : 'False'}
  //         </HeaderDiv>
  //       </>
  //     );
  //   },
  // },
  {
    accessorKey: 'current_assignee',
    header: 'Current Assignee',
    cell: ({ row }) => {
      return (
        <>{row?.original?.current_assignee?.user ?? <Tag>Not Available</Tag>}</>
      );
    },
  },
  {
    accessorKey: 'created_by',
    header: 'Created by',
    cell: ({ row }) => {
      // console.log('row', row.original);
      return <>{row.original.created_by}</>;
    },
  },
  {
    accessorKey: 'customer_email',
    header: 'Customer Email',
  },
  {
    id: 'cancel',
    header: 'Cancel',
    cell: ({ row }) => {
      const axios = useAxios();
      const addToast = useToast();
      const queryClient = useQueryClient();
      const returnId = parseInt(row.getValue('id'));
      const isDisabled: boolean = row.original.status === 'close';
      const handleButtonClick = async () => {
        try {
          const response = await axios.post(
            `${krakendPaths.RETURN_URL}/admin-api/v1/returns/cancel`,
            {
              return_id: returnId,
            },
            {
              headers: {
                'x-api-key': constants.RETURN_API_KEY,
                Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              },
            },
          );
          addToast('success', 'Return Cancelled Successfully !!');
          queryClient.invalidateQueries({
            queryKey: ['returnList'],
            exact: false, // allow partial match since filters is dynamic
          });
        } catch (error: any) {
          console.error(error);
          addToast('error', error?.message || 'Error encountered !!');
        }
      };

      return (
        <HeaderDiv columnWidth={baseTheme.components.dimension.width.base150}>
          <Button
            style={{
              color: isDisabled
                ? baseTheme.colors.black
                : baseTheme.colors.white,
              backgroundColor: isDisabled
                ? baseTheme.colors.grey
                : baseTheme.colors.deepRed,
              width: baseTheme.components.dimension.width.base150,
            }}
            disabled={isDisabled}
            onClick={handleButtonClick}
          >
            Cancel
          </Button>
        </HeaderDiv>
      );
    },
  },
];

export type ReturnApprovedTableColumn = {
  id: number;
  order_id: string;
  awb_number: number;
  sales_return_id: number;
  return_amount: number;
  invoice_no: string;
  location: string;
  vinculum_status: string | null;
  action: string;
  admin_action: string;
  transporter: number;
  return_date: string;
  return_no: string;
  created_at: Date;
};

export const returnApprovedColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'id',
    enableHiding: true,
  },
  {
    accessorKey: 'response',
    enableHiding: true,
  },
  {
    accessorKey: 'order_id',
    header: 'Order Id',
    cell: ({ row }) => {
      const order_id: string = row.getValue('order_id');
      return <>{order_id}</>;
    },
    enablePinning: true,
  },
  {
    accessorKey: 'awb_number',
    header: 'Awb Number',
    enablePinning: true,
    cell: ({ row }) => {
      const awbNum = row.original.awb_number;

      return (
        <>
          <Link
            to={`https://dentalkart.clickpost.ai/reverse?waybill=${awbNum}`}
          >
            {awbNum}
          </Link>
        </>
      );
    },
  },
  {
    accessorKey: 'clickpost_status',
    header: 'Shipment Status',
  },
  {
    accessorKey: 'payment_method',
    header: 'Payment Method',
    cell: ({ row }) => {
      const paymentMethod =
        row.original?.returnItem?.returnOrder?.payment_method;
      return <>{paymentMethod}</>;
    },
  },
  {
    accessorKey: 'refund_status',
    header: 'Refund Status',
  },
  {
    accessorKey: 'return_id',
    header: 'Sales Return Id',
    enablePinning: true,
    cell: ({ row }) => {
      const salesReturnId = parseInt(row.getValue('return_id'));
      const linkURL = `${pageRoutes['GO_TO_NEW_RETURN_ITEM_GENERAL']}/${salesReturnId}`;

      return (
        <>
          <HeaderDiv
            isCenter
            columnWidth={baseTheme.components.dimension.width.base150}
          >
            <Link to={linkURL}>
              {isNaN(salesReturnId) ? <Tag>Not Provided</Tag> : salesReturnId}
            </Link>
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'return_amount',
    header: 'Return Amount',
    cell: ({ row }) => {
      const price = row.original?.returnItem?.price;

      return <>{price}</>;
    },
  },
  {
    accessorKey: 'invoice_no',
    header: 'Invoice No',
    cell: ({ row }) => {
      // console.log({ request: row.original.request });
      const request = JSON.parse(row.original.request);
      // const invoice_no = request?.orderReturn[0]?.invoice_no;
      const invoice =
        request?.request?.orderReturn &&
        request?.request?.orderReturn[0]?.invoice_no;
      const invoice_no = 1234;

      return (
        <>
          {
            <HeaderDiv
              columnWidth={baseTheme.components.dimension.width.base150}
            >
              {invoice ? invoice : <Tag>Not Provided</Tag>}
            </HeaderDiv>
          }
        </>
      );
    },
  },
  {
    accessorKey: 'location',
    header: 'Location',
    cell: ({ row }) => {
      const request = JSON.parse(row.original.request);
      const location =
        request?.request?.orderReturn?.length > 0 &&
        request?.request?.orderReturn[0]?.order_location;
      // const location = 'location';

      return <>{location}</>;
    },
  },
  {
    accessorKey: 'vinculum_status',
    header: 'Vinculum Status',
    cell: ({ row }) => {
      const response = row.getValue('response');
      let viniCulumn: any;
      try {
        const responseJSON = response ? JSON.parse(`${response}`) : null;
        viniCulumn = responseJSON?.response?.requestStatus
          ? responseJSON?.response?.requestStatus?.status
          : null;
      } catch (e) {
        console.log(e);
      }

      return (
        <>
          <HeaderDiv
            isCenter
            columnWidth={baseTheme.components.dimension.width.base150}
          >
            {viniCulumn}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'action',
    header: 'Customer Action',
    cell: ({ row }) => {
      return <>{row.original?.action}</>;
    },
  },
  {
    accessorKey: 'admin_action',
    header: 'Admin Action',
    cell: ({ row }) => {
      const request = JSON.parse(row.original.request);
      const action =
        request?.request?.orderReturn?.length > 0 &&
        request?.request?.orderReturn[0]?.category;
      // const action = 'category';

      return <>{row.original?.returnItem?.admin_action}</>;
    },
  },
  {
    accessorKey: 'transporter',
    header: 'Transporter',
    cell: ({ row }) => {
      const request = JSON.parse(row.original.request);
      const transporter =
        request?.request?.orderReturn &&
        request?.request?.orderReturn[0]?.transporter;

      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base150}>
            {transporter}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'return_date',
    header: 'Return Date',
    cell: ({ row }) => {
      const request = JSON.parse(row.original.request);
      const returnDate =
        request?.request?.orderReturn &&
        request?.request?.orderReturn[0]?.return_date;
      const dateFromAPI = parse(returnDate, 'dd/MM/yyyy HH:mm:ss', new Date());
      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base150}>
            {returnDate ? (
              format(dateFromAPI, 'dd/MM/yyyy HH:mm:ss')
            ) : (
              <Tag>Not Available</Tag>
            )}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'return_no',
    header: 'Return No',
    cell: ({ row }) => {
      const returnNo: string = row.getValue('return_no');

      return <>{returnNo}</>;
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created at',
    cell: ({ row }) => {
      const createdAt = new Date(row.original.created_at);

      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base150}>
            {formatDateIntoDDMMYYYYAndTime(createdAt)}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated at',
    cell: ({ row }) => {
      const updatedAt = new Date();
      // Number(row.getValue('updated_at'))

      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base150}>
            {formatDateIntoDDMMYYYYAndTime(updatedAt)}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'cancel',
    header: 'Cancel',
    cell: ({ row }) => {
      const returnId: number = parseInt(row.getValue('return_id'));
      const returnNo: string = row.getValue('return_no');
      const addToast = useToast();
      const [showCancelModal, setShowCancelModal] = useState<boolean>(false);
      const axios = useAxios();
      const { mutate: cancelReturn, isLoading: loading } = useRestMutation(
        async () => {
          const response = await axios.post(
            `${krakendPaths.RETURN_URL}/admin-api/v1/returns/cancel/${returnNo}`,

            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem('api-token')}`,
                'x-api-key': constants.RETURN_API_KEY,
              },
            },
          );
          return response;
        },
        {
          onError: (error: any) => {
            // console.log(error);
            addToast('error', `Error encountered !! : ${error.message}`);
            setShowCancelModal(false);
            // TODO update this
            // returnClient.refetchQueries({
            //   include: [GET_APPROVED_RETURN_LIST],
            // });
          },
          onSuccess: (data) => {
            // console.log(data);
            // TODO update this
            // returnClient.refetchQueries({
            //   include: [GET_APPROVED_RETURN_LIST],
            // });
            addToast('success', 'Removed successfully');
            setShowCancelModal(false);
          },
        },
      );
      const handleCancelClick = (returnNo: any) => {
        cancelReturn();
      };

      return (
        <>
          <ZButton
            onClick={() => {
              setShowCancelModal(true);
            }}
            style={{ width: baseTheme.components.dimension.width.base150 }}
            isDanger
            isPrimary
            disabled={row.original.status === 'cancelled'}
          >
            Cancel
          </ZButton>
          {showCancelModal && (
            <>
              <RemoveReturnApproved
                setVisible={setShowCancelModal}
                isLoading={loading}
                returnId={returnId}
                currentData={row.original}
              />
            </>
          )}
        </>
      );
    },
  },
];

export type ReturnDTOTableColumn = {
  id: number;
  order_id: string;
  return_id: string;
  awb_number: string;
  dto_pack_date: string;
  attachments: string[];
  product_name: string;
  qty_received: number;
  status: string;
  inspection_remark: string;
  resolution: string;
  moved_to_next_stage: string | null;
  dto_stage: string;
  current_stage: string;
  created_At: string | null;
};

export type FailedPickupRequest = {
  id: number;
  sku: string;
  order_id: string;
  pick_up_attempt: number;
  awb_number: string;
  courier_id: string;
  courier: string;
  image: string;
};

export type CompensationTableColumn = {
  id: number;
  order_id: string;
  return_id: string;
  return_item_id: string;
  sku: string;
  product_name: string;
  status: string;
  image: string;
  qty: string;
  reason: string;
  sub_reason: number;
  compensation_amount: number;
  created_at: Date;
};

export const returnDTOColumn: ColumnDef<ReturnDTOTableColumn>[] = [
  {
    accessorKey: 'id',
    enableHiding: true,
  },
  {
    accessorKey: 'action',
    enableColumnFilter: true,
    enablePinning: true,
    header: () => {
      const [visible, setVisible] = useState(false);
      const [createModalVisible, setCReateModalVisible] =
        useState<boolean>(false);

      const [returnItemInfo, setreturnItemInfo] = useState<any>();

      const [returnItemId, setreturnItemId] = useState<number>();

      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
            <Span
              style={{ cursor: 'pointer' }}
              onClick={() => {
                setVisible(true);
              }}
              hue="white"
            >
              New
            </Span>
          </HeaderDiv>
          {visible && (
            <>
              <SearchReturnIdModal
                setCreateModalVisible={setCReateModalVisible}
                setVisible={setVisible}
                setreturnItemInfo={setreturnItemInfo}
                returnItemId={returnItemId}
                setreturnItemId={setreturnItemId}
              />
            </>
          )}

          {createModalVisible &&
            returnItemInfo != undefined &&
            returnItemId != undefined && (
              <>
                <ReturnDTOModal
                  setVisible={setCReateModalVisible}
                  returnItemId={returnItemId}
                  returnItemInfo={returnItemInfo}
                />
              </>
            )}
        </>
      );
    },
    cell: ({ row }) => {
      const axios = useAxios();

      const { mutate: deleteReturnDto, isLoading: deleteReturnDTOLoading } =
        useRestMutation({
          mutationFn: async (dtoId: number) => {
            const response = await axios.delete(
              `${krakendPaths.RETURN_URL}/admin-api/v1/admin/dto/${dtoId}`,
              {
                headers: {
                  'x-api-key': constants.RETURN_API_KEY,
                  Authorization: `Bearer ${localStorage.getItem('api-token')}`,
                },
              },
            );

            return response;
          },
        });

      const [visible, setVisible] = useState<boolean>(false);
      const [removeVisible, setRemoveVisible] = useState<boolean>(false);

      const addToast = useToast();

      const dtoID: number = Number(row.original.id);
      const queryClient = useQueryClient();
      const handleDelete = (dtoID: number) => {
        deleteReturnDto(dtoID, {
          onError: (error) => {
            // console.log(error);
            addToast('error', 'Error encountered !!');
            queryClient.invalidateQueries(['returnDTO']);
          },
          onSuccess: (data) => {
            // console.log(data);
            queryClient.invalidateQueries(['returnDTO']);

            addToast('success', 'Removed Successfully !!');
          },
        });
      };

      return (
        <>
          <Row>
            {/* <IconButton size="medium" onClick={() => setVisible(!visible)}>
              <EditIconDTO />
            </IconButton> */}
            {row.original?.status !== 'close' && (
              <IconButton
                onClick={() => {
                  // handleDelete(dtoID);
                  setRemoveVisible(true);
                }}
                size="medium"
              >
                <RemoveIcon style={{ color: baseTheme.colors.dangerHue }} />
              </IconButton>
            )}
          </Row>
          {visible && (
            <>
              <EditReturnDTO id={dtoID} setVisible={setVisible} />
            </>
          )}

          {removeVisible && (
            <RemoveDTO
              isLoading={deleteReturnDTOLoading}
              remove={() => handleDelete(dtoID)}
              setVisible={setRemoveVisible}
            />
          )}
        </>
      );
    },
  },
  {
    accessorKey: 'awb_number',
    header: 'Awb Number',
    cell: ({ row }) => {
      const value: any = row.getValue('awb_number');
      const awbNumber = parseInt(value);
      return <>{isNaN(awbNumber) ? value : awbNumber}</>;
    },
    enablePinning: true,
  },
  {
    accessorKey: 'order_id',
    header: 'Order Id',
    cell: ({ row }) => {
      const order_id: string = row.getValue('order_id');
      return <>{order_id}</>;
    },
    enablePinning: true,
  },
  {
    accessorKey: 'return_id',
    header: 'Return Id',
    cell: ({ row }) => {
      const value: any = row.getValue('return_id');
      const salesReturnId = parseInt(value);
      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
            {isNaN(salesReturnId) ? value : salesReturnId}
          </HeaderDiv>
        </>
      );
    },
    enablePinning: true,
  },
  {
    accessorKey: 'dto_pack_date',
    header: 'DTO Package Open Date',
    cell: ({ row }) => {
      const dtoPack = row.getValue('dto_pack_date');

      return (
        <>
          <HeaderDiv
            isCenter
            columnWidth={baseTheme.components.dimension.width.base200}
          >
            {dtoPack ? formatDateIntoDDMMYYYYAndTime(dtoPack) : 'Null'}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'attachments',
    header: 'Attachments',
    cell: ({ row, getValue }) => {
      console.log(row.original);
      return (
        <>
          <Span>{row.original?.attachments?.length}</Span>
        </>
      );
    },
  },
  {
    accessorKey: 'product_name',
    header: 'Product Name',
    cell: ({ row }) => {
      const productName: string = row.getValue('product_name');
      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base250}>
            {productName}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'qty_received',
    header: 'Qty Received',
    cell: ({ row }) => {
      const qty = parseInt(row.getValue('qty_received'));
      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
            {qty}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status: string = row.getValue('status');
      return (
        <>
          {status === 'open' ? (
            <>
              <Tag isOpen isPill hue={baseTheme.colors.transparentGreen}>
                <Tag.Avatar>
                  <CircleIcon
                    color={baseTheme.colors.solidGreen}
                    style={{
                      width: baseTheme.iconSizes.xs,
                      height: baseTheme.iconSizes.xs,
                    }}
                  />
                </Tag.Avatar>
                <Span>{status}</Span>
              </Tag>
            </>
          ) : (
            <>
              <Tag isPill hue={baseTheme.colors.transparentRed}>
                <Tag.Avatar>
                  <CircleIcon
                    color={baseTheme.colors.solidRed}
                    style={{
                      width: baseTheme.iconSizes.xs,
                      height: baseTheme.iconSizes.xs,
                    }}
                  />
                </Tag.Avatar>
                <Span>{status}</Span>
              </Tag>
            </>
          )}
        </>
      );
    },
  },
  {
    accessorKey: 'inspection_remark',
    header: 'Inspection Remark',
    cell: ({ row }) => {
      const inspectionRemark: string = row.getValue('inspection_remark');
      return (
        <>
          <HeaderDiv
            isCenter
            columnWidth={baseTheme.components.dimension.width.base150}
          >
            {inspectionRemark}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'resolution',
    header: 'Resolution',
    cell: ({ row }) => {
      const resolution = row.getValue('resolution');

      return <>{resolution}</>;
    },
  },
  {
    accessorKey: 'moved_to_next_stage',
    header: 'Moved to next Stage on',
    cell: ({ row }) => {
      const moveStage: string = row.getValue('moved_to_next_stage');
      return (
        <>
          <HeaderDiv
            isCenter
            columnWidth={baseTheme.components.dimension.width.base200}
          >
            {moveStage == null ? (
              <Tag>Not Provided</Tag>
            ) : (
              formatDateIntoDDMMYYYYAndTime(moveStage)
            )}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'dto_stage',
    header: 'DTO Stage',
    cell: ({ row }) => {
      const dtoStage: string = row.getValue('dto_stage');
      return <>{dtoStage}</>;
    },
  },
  {
    accessorKey: 'current_stage',
    header: 'Current Status',
    cell: ({ row }) => {
      const currentStage: string = row.getValue('current_stage');
      return (
        <>
          <HeaderDiv
            columnWidth={baseTheme.components.dimension.width.base150}
            isCenter
          >
            {currentStage}
          </HeaderDiv>
        </>
      );
    },
  },
  {
    accessorKey: 'created_At',
    header: 'Created At',
    cell: ({ row }) => {
      // console.log('Row', row.original);
      return (
        <>
          {row.original.created_At || row.original.created_At != null
            ? format(new Date(row.original.created_At), 'MM/dd/yyyy HH:mm:ss')
            : ''}
        </>
      );
    },
  },
];

export const compensationColumn: ColumnDef<CompensationTableColumn>[] = [
  {
    accessorKey: 'Edit',
    enablePinning: true,
    header: '',
    cell: ({ row }) => {
      const { setselectedCompensation } =
        useCompensation() as CompensationContextType;
      return (
        <HeaderDiv
          columnWidth={`${baseTheme.components.dimension.width.base * 5}px`}
        >
          <IconButton
            disabled={row.original?.status === 'close'}
            onClick={() => {
              setselectedCompensation(row.original);
            }}
            size="small"
            // onClick={() => setReimbursementModal(row.original)}
          >
            <TableEditIcon />
          </IconButton>
        </HeaderDiv>
      );
    },
  },

  {
    accessorKey: 'order_id',
    header: 'Order Id',
    cell: ({ row }) => {
      const order_id: string = row.getValue('order_id');
      return <>{order_id}</>;
    },
    enablePinning: true,
  },
  {
    accessorKey: 'return_id',
    header: 'Return Id',
    cell: ({ row }) => {
      const value: any = row.getValue('return_id');
      const salesReturnId = parseInt(value);
      return (
        <>
          <HeaderDiv columnWidth={baseTheme.components.dimension.width.base100}>
            {isNaN(salesReturnId) ? value : salesReturnId}
          </HeaderDiv>
        </>
      );
    },
    enablePinning: true,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status: string = row.getValue('status');
      return (
        <>
          {status === 'open' ? (
            <>
              <Tag isOpen isPill hue={baseTheme.colors.transparentGreen}>
                <Tag.Avatar>
                  <CircleIcon
                    color={baseTheme.colors.solidGreen}
                    style={{
                      width: baseTheme.iconSizes.xs,
                      height: baseTheme.iconSizes.xs,
                    }}
                  />
                </Tag.Avatar>
                <Span>{convertToSentenceCase(status)}</Span>
              </Tag>
            </>
          ) : (
            <>
              <Tag isPill hue={baseTheme.colors.transparentRed}>
                <Tag.Avatar>
                  <CircleIcon
                    color={baseTheme.colors.solidRed}
                    style={{
                      width: baseTheme.iconSizes.xs,
                      height: baseTheme.iconSizes.xs,
                    }}
                  />
                </Tag.Avatar>
                <Span>{convertToSentenceCase(status)}</Span>
              </Tag>
            </>
          )}
        </>
      );
    },
    enablePinning: true,
  },
  {
    accessorKey: 'qty',
    header: 'Qty',
    cell: ({ row }) => {
      const qty: string = row.getValue('qty');
      return <>{qty}</>;
    },
    enablePinning: true,
  },
  {
    accessorKey: 'product_name',
    header: 'Product Name',
    cell: ({ row }) => {
      const product_name: string = row.getValue('product_name');
      return <>{product_name}</>;
    },
    enablePinning: true,
  },
  {
    accessorKey: 'sku',
    header: 'SKU',
    cell: ({ row }) => {
      const sku: string = row.getValue('sku');
      return <>{sku}</>;
    },
    enablePinning: true,
  },
  {
    accessorKey: 'reason',
    header: 'Reason',
    cell: ({ row }) => {
      const reason: string = row.getValue('reason');
      return <>{reason}</>;
    },
    enablePinning: true,
  },
  {
    accessorKey: 'sub_reason',
    header: 'Sub Reason',
    cell: ({ row }) => {
      const sub_reason: string = row.getValue('sub_reason');
      return <>{sub_reason}</>;
    },
    enablePinning: true,
  },
  {
    accessorKey: 'created_at',
    header: 'Compensated At',
    cell: ({ row }) => {
      // console.log('Row', row.original);
      return (
        <>
          {row.original.created_at || row.original.created_at != null
            ? format(new Date(row.original.created_at), 'MM/dd/yyyy HH:mm:ss')
            : ''}
        </>
      );
    },
  },
];

export type ReturnCreateTableColumn = {
  id: number;
  item: string;
  order_qty: string;
  return_qty: number;
  reason: IReason[] | undefined;
  action: IReturnAction[] | undefined;
};

export const quantityMenu: ITableDropdown[] = [
  {
    label: 1,
    value: 1,
  },
  {
    label: 2,
    value: 2,
  },
  {
    label: 3,
    value: 3,
  },
  {
    label: 4,
    value: 4,
  },
  {
    label: 5,
    value: 5,
  },
  {
    label: 6,
    value: 6,
  },
  {
    label: 7,
    value: 7,
  },
  {
    label: 8,
    value: 8,
  },
  {
    label: 9,
    value: 9,
  },
  {
    label: 10,
    value: 10,
  },
];

const reasonMenu: ITableDropdown[] = [
  {
    label: 'I dont want this',
    value: 1,
  },
  {
    label: 'Never bought it',
    value: 2,
  },
  {
    label: 'Not interested',
    value: 3,
  },
  {
    label: 'Already have it',
    value: 4,
  },
  {
    label: 'Not my style',
    value: 5,
  },
  {
    label: 'Too expensive',
    value: 6,
  },
  {
    label: 'Not available',
    value: 7,
  },
  {
    label: 'Received as a gift',
    value: 8,
  },
  {
    label: 'No longer needed',
    value: 9,
  },
];

export const returnCreateTableColumn: ColumnDef<ReturnCreateTableColumn>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    id: 'select',
    header: 'Select',
    cell: ({ row }) => {
      const [isChecked, setIsChecked] = useState(row.getIsSelected());
      const handleCheckboxChange = (event: any) => {
        const { checked } = event.target;
        setIsChecked(checked);
        row.toggleSelected(checked);
        console.log('HEllo', row._getAllCellsByColumnId());
      };

      return (
        <>
          <Row justifyContent="center" alignItems="center">
            <Col size={4}>
              <Field>
                <Checkbox
                  checked={isChecked}
                  onChange={handleCheckboxChange}
                  aria-label="Select row"
                >
                  <Label hidden>Accessibly hidden label one</Label>
                </Checkbox>
              </Field>
            </Col>
          </Row>
        </>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'item',
    header: 'Item',
    cell: ({ row }) => {
      const item: string = row.getValue('item');
      return (
        <>
          <Span style={{ fontSize: baseTheme.fontSizes.lg }}>
            <Span.Icon>
              <ItemIcon />
            </Span.Icon>
            {item}
          </Span>
        </>
      );
    },
  },
  {
    accessorKey: 'order_qty',
    header: 'Order Qty',
    cell: ({ row }) => {
      const qty = parseInt(row.getValue('order_qty'));
      const [selectedItem, setSelectedItem] = useState<ITableDropdown>(
        quantityMenu[1],
      );
      return (
        <>
          <Row justifyContent="start" alignItems="center">
            <Col size={3}>
              <TableDropdown
                data={quantityMenu}
                selectedItem={selectedItem}
                setSelectedItem={setSelectedItem}
                isDisabled
              />
            </Col>
          </Row>
        </>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'return_qty',
    header: 'Return Qty',
    cell: ({ row }) => {
      const returnQty = parseInt(row.getValue('return_qty'));
      const [selectedItem, setSelectedItem] = useState<ITableDropdown>(
        quantityMenu[1],
      );
      return (
        <>
          <Row justifyContent="start" alignItems="center">
            <Col size={3}>
              <TableDropdown
                data={quantityMenu}
                selectedItem={selectedItem}
                setSelectedItem={setSelectedItem}
              />
            </Col>
          </Row>
        </>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'reason',
    header: 'Reason',
    cell: ({ row }) => {
      const reason = parseInt(row.getValue('reason'));
      const [selectedItem, setSelectedItem] = useState<ITableDropdown>(
        reasonMenu[1],
      );
      return (
        <>
          <Row justifyContent="start" alignItems="center">
            <Col size={3}>
              <TableDropdown
                data={reasonMenu}
                selectedItem={selectedItem}
                setSelectedItem={setSelectedItem}
                isDisabled
              />
            </Col>
          </Row>
        </>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'sub_reason',
    header: 'Sub Reason',
    cell: ({ row }) => {
      const subReason = parseInt(row.getValue('sub_reason'));
      const [selectedItem, setSelectedItem] = useState<ITableDropdown>(
        reasonMenu[1],
      );
      return (
        <>
          <Row justifyContent="start" alignItems="center">
            <Col size={3}>
              <TableDropdown
                data={reasonMenu}
                selectedItem={selectedItem}
                setSelectedItem={setSelectedItem}
                isDisabled
              />
            </Col>
          </Row>
        </>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'action',
    header: 'Action',
    cell: ({ row }) => {
      const subReason = parseInt(row.getValue('action'));
      const [selectedItem, setSelectedItem] = useState<ITableDropdown>(
        reasonMenu[1],
      );
      return (
        <>
          <Row justifyContent="start" alignItems="center">
            <Col size={3}>
              <TableDropdown
                data={reasonMenu}
                selectedItem={selectedItem}
                setSelectedItem={setSelectedItem}
                isDisabled
              />
            </Col>
          </Row>
        </>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'attachments',
    header: 'Attachments',
    cell: ({ row }) => {
      const subReason = parseInt(row.getValue('action'));
      const [selectedItem, setSelectedItem] = useState<ITableDropdown>(
        reasonMenu[1],
      );
      return (
        <>
          <Row justifyContent="start" alignItems="center">
            <Col size={3}>
              <TableDropdown
                data={reasonMenu}
                selectedItem={selectedItem}
                setSelectedItem={setSelectedItem}
                isDisabled
              />
            </Col>
          </Row>
        </>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
];

export type NonReturnableTableColumn = {
  updatedAt: string;
  sku: string;
  productName: string;
  createdAt: string;
};

export const nonReturnableTableColumn: ColumnDef<NonReturnableTableColumn>[] = [
  {
    accessorKey: 'remove',
    header: ({ header }) => {
      const [visible, setVisible] = useState<boolean>(false);

      const handleNewClick = () => {
        setVisible(true);
      };

      return (
        <>
          <Button
            size="small"
            isPrimary
            isOrange
            onClick={() => {
              handleNewClick();
            }}
            style={{
              minWidth: '80px',
            }}
          >
            New
          </Button>
          {visible && <AddNonReturnable setVisible={setVisible} />}
        </>
      );
    },
    enableColumnFilter: true,
    cell: ({ row }) => {
      const axios = useAxios();

      const { mutate: deleteNonReturnable, isLoading: deleteMutationLoading } =
        useRestMutation({
          mutationFn: async (sku: string) => {
            const response = await axios.delete(
              `${krakendPaths.RETURN_URL}/admin-api/v1/config/non-returnable/${sku}`,
              {
                headers: {
                  'x-api-key': constants.RETURN_API_KEY,
                  Authorization: `Bearer ${localStorage.getItem('api-token')}`,
                },
              },
            );
            return response;
          },
        });

      const [visible, setVisible] = useState<boolean>(false);

      const addToast = useToast();
      const queryClient = useQueryClient();

      const sku: string = row.getValue('sku');

      const handleDelete = (sku: string) => {
        deleteNonReturnable(sku, {
          onError: (error) => {
            // console.log(error);
            addToast('error', 'Error encountered !!');
          },
          onSuccess: (data) => {
            // console.log(data);

            queryClient.invalidateQueries(['nonReturnable']);

            addToast('success', 'Removed successfully');
            setVisible(false);
          },
        });
      };
      return (
        <>
          <IconButton
            onClick={() => {
              // handleDelete(productId);
              setVisible(true);
            }}
          >
            <RemoveIcon style={{ color: baseTheme.colors.dangerHue }} />
          </IconButton>

          {visible && (
            <RemoveNonReturnable
              setVisible={setVisible}
              isLoading={deleteMutationLoading}
              remove={() => handleDelete(sku)}
            />
          )}
        </>
      );
    },
  },
  {
    accessorKey: 'productName',
    header: 'Product Name',
    cell: ({ row }) => {
      const productName: string = row.getValue('productName');
      return <>{productName}</>;
    },
  },
  {
    accessorKey: 'sku',
    header: 'SKU',
    cell: ({ row }) => {
      const sku: string = row.getValue('sku');
      return <>{sku}</>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Created At',
    cell: ({ row }) => {
      const createdAt: string = row.getValue('createdAt');
      return (
        <>
          {createdAt != null
            ? format(Date.parse(createdAt), 'MM/dd/yyyy HH:mm:ss')
            : ''}
        </>
      );
    },
  },
];

export type ReasonTableColumn = {
  id: number;
  reason: string;
  code: string | null;
  createdAt: string;
  updatedAt: string;
  enable: boolean;
  attachmentRequired: boolean;
};

export const reasonTableColumn: ColumnDef<ReasonTableColumn>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'reason',
    header: 'Reason',
    cell: ({ row }) => {
      const reason: string = row.getValue('reason');
      return <>{reason}</>;
    },
  },
  {
    accessorKey: 'code',
    header: 'Code',
    cell: ({ row }) => {
      const code: string = row.getValue('code');
      return <>{code}</>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Created At',
    cell: ({ row }) => {
      const createdAt = Date.parse(row.getValue('createdAt'));

      return (
        <>
          <ReactTimeago date={createdAt} />
        </>
      );
    },
  },
  {
    accessorKey: 'enable',
    header: 'Enable',
    cell: ({ row }) => {
      const enable: boolean = row.getValue('enable');
      return (
        <>
          <Field>
            <Toggle checked={enable}>
              <Label hidden>Enable</Label>
            </Toggle>
          </Field>
        </>
      );
    },
  },
  {
    accessorKey: 'attachmentRequired',
    header: 'Attachment Required',
    cell: ({ row }) => {
      const attachmentRequired: boolean = row.getValue('attachmentRequired');
      return (
        <>
          <Field>
            <Toggle checked={attachmentRequired}>
              <Label hidden>Attachment Required</Label>
            </Toggle>
          </Field>
        </>
      );
    },
  },
];

export type DTOStatusColumn = {
  id: number;
  dtoStatus: string;
  createdAt: Date;
  enable: boolean;
};

export const dtoStatusColumn: ColumnDef<DTOStatusColumn>[] = [
  {
    id: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'dtoStatus',
    header: 'DTO Status',
    cell: ({ row }) => {
      const dtoStatus: string = row.getValue('dtoStatus');
      return <>{dtoStatus}</>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Created A',
    cell: ({ row }) => {
      const createdAt = Date.parse(row.getValue('createdAt'));
      return (
        <>
          <ReactTimeago date={createdAt} />
        </>
      );
    },
  },
  {
    accessorKey: 'enable',
    header: 'Enable',
    cell: ({ row }) => {
      return (
        <>
          <Field>
            <Toggle>
              <Label hidden>Enable</Label>
            </Toggle>
          </Field>
        </>
      );
    },
  },
];

export type ActionReimbursementColumn = {
  id: number;
  reason: string;
  createdAt: string;
  enable: boolean;
  updatedAt: string;
};

export const actionReimbursementColumn: ColumnDef<ActionReimbursementColumn>[] =
  [
    {
      id: 'id',
      header: 'Id',
      enableHiding: true,
    },
    {
      accessorKey: 'reason',
      header: 'Reason',
      cell: ({ row }) => {
        const dtoStatus: string = row.getValue('reason');
        return <>{dtoStatus}</>;
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Created A',
      cell: ({ row }) => {
        const createdAt = Date.parse(row.getValue('createdAt'));
        return (
          <>
            <ReactTimeago date={createdAt} />
          </>
        );
      },
    },
    {
      accessorKey: 'enable',
      header: 'Enable',
      cell: ({ row }) => {
        const enable: boolean = row.getValue('enable');
        const id = row.getValue('id');
        const axios = useAxios();

        return (
          <>
            <Field>
              <Toggle checked={enable}>
                <Label hidden>Enable</Label>
              </Toggle>
            </Field>
          </>
        );
      },
    },
  ];

export type StatusTableColumn = {
  id: number;
  code: string;
  enable: boolean;
  status: string;
  created_at: string;
  user: string;
};

export const statusTableColumn: ColumnDef<StatusTableColumn>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status: string = row.getValue('status');
      return <>{status}</>;
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created at',
    cell: ({ row }) => {
      const createdAt = new Date(Number(row.getValue('created_at')));
      return (
        <>
          <ReactTimeago date={createdAt} />
        </>
      );
    },
  },
];

export type TagTableColumn = {
  id: number;
  tag: string;
  created_at: string;
  enable: boolean;
  code: string;
};

export const tagTableColumn: ColumnDef<TagTableColumn>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'tag',
    header: 'Tag',
    cell: ({ row }) => {
      const reason: string = row.getValue('tag');
      return <>{reason}</>;
    },
  },
  {
    accessorKey: 'code',
    header: 'Code',
    cell: ({ row }) => {
      const code: string = row.getValue('code');
      return <>{code}</>;
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: ({ row }) => {
      const createdAt = Date.parse(row.getValue('created_at'));

      return (
        <>
          <ReactTimeago date={createdAt} />
        </>
      );
    },
  },
  {
    accessorKey: 'enable',
    header: 'Enable',
    cell: ({ row }) => {
      const enable: boolean = row.getValue('enable');
      return (
        <>
          <Field>
            <Toggle checked={enable}>
              <Label hidden>Enable</Label>
            </Toggle>
          </Field>
        </>
      );
    },
  },
];

export type SLATableColumn = {
  id: number;
  sla_hour: number;
  created_at: string;
  updated_at: string;
};

export const slaTableColumn: ColumnDef<SLATableColumn>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'sla_hour',
    header: 'SLA hours',
    cell: ({ row }) => {
      const slaHour: number = parseInt(row.getValue('sla_hour'));
      return <>{slaHour}</>;
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: ({ row }) => {
      const createdAt = Date.parse(row.getValue('created_at'));
      return (
        <>
          <ReactTimeago date={createdAt} />
        </>
      );
    },
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated at',
    cell: ({ row }) => {
      const updatedAt = Date.parse(row.getValue('created_at'));
      return (
        <>
          <ReactTimeago date={updatedAt} />
        </>
      );
    },
  },
];

export type InspectionRemarkTableColumn = {
  id: string;
  inspection_remark: string;
  enable: boolean;
};

export const inspectionRemarkColumn: ColumnDef<InspectionRemarkTableColumn>[] =
  [
    {
      accessorKey: 'id',
      header: 'Id',
      enableHiding: true,
    },
    {
      accessorKey: 'inspection_remark',
      header: 'Inspection Remark',
      cell: ({ row }) => {
        const inspectionRemark: string = row.getValue('inspection_remark');
        return <>{inspectionRemark}</>;
      },
    },
    {
      accessorKey: 'enable',
      header: 'Enable',
      cell: ({ row }) => {
        const enable: boolean = row.getValue('enable');
        return (
          <>
            <Field>
              <Toggle checked={enable}>
                <Label hidden>Enable</Label>
              </Toggle>
            </Field>
          </>
        );
      },
    },
  ];

export type ResolutionTableColumn = {
  id: string;
  resolution: string;
  enable: boolean;
};

export const resolutionTableColumn: ColumnDef<ResolutionTableColumn>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'resolution',
    header: 'Inspection Remark',
    cell: ({ row }) => {
      const resolutionName: string = row.getValue('resolution');
      return <>{resolutionName}</>;
    },
  },
  {
    accessorKey: 'enable',
    header: 'Enable',
    cell: ({ row }) => {
      const enable: boolean = row.getValue('enable');
      return (
        <>
          <Field>
            <Toggle checked={enable}>
              <Label hidden>Enable</Label>
            </Toggle>
          </Field>
        </>
      );
    },
  },
];

export type ReturnDTOStatusTableColumn = {
  id: string;
  dtoStatus: string;
  enable: boolean;
};

export const returnDTOStatusTableColumn: ColumnDef<ReturnDTOStatusTableColumn>[] =
  [
    {
      accessorKey: 'id',
      header: 'Id',
      enableHiding: true,
    },
    {
      accessorKey: 'dtoStatus',
      header: 'DTO Status',
      cell: ({ row }) => {
        const dtoStatus: string = row.getValue('dtoStatus');
        return <>{dtoStatus}</>;
      },
    },
    {
      accessorKey: 'enable',
      header: 'Enable',
      cell: ({ row }) => {
        const enable: boolean = row.getValue('enable');
        return (
          <>
            <Field>
              <Toggle checked={enable}>
                <Label hidden>Enable</Label>
              </Toggle>
            </Field>
          </>
        );
      },
    },
  ];

export type ReturnAssigneeTableColumn = {
  enable: boolean;
  id: number;
  created_at: string;
  updated_at: string;
  assignee_name: string;
};

export const returnAssigneeColumn: ColumnDef<ReturnAssigneeTableColumn>[] = [
  {
    accessorKey: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'assignee_name',
    header: 'Assignee Name',
    cell: ({ row }) => {
      const assigneeName: string = row.getValue('assignee_name');
      return <>{assigneeName}</>;
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: ({ row }) => {
      const createdAt = Date.parse(row.getValue('created_at'));
      return (
        <>
          <ReactTimeago date={createdAt} />
        </>
      );
    },
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated at',
    cell: ({ row }) => {
      const updatedAt = Date.parse(row.getValue('created_at'));
      return (
        <>
          <ReactTimeago date={updatedAt} />
        </>
      );
    },
  },
  {
    accessorKey: 'enable',
    header: 'Enable',
    cell: ({ row }) => {
      const enable: boolean = row.getValue('enable');
      return (
        <>
          <Field>
            <Toggle checked={enable}>
              <Label hidden>Enable</Label>
            </Toggle>
          </Field>
        </>
      );
    },
  },
];

export type CustomerRemarkColumn = {
  id: string;
  customerRemark: string;
  enable: boolean;
};

export const customerRemarkColumn: ColumnDef<CustomerRemarkColumn>[] = [
  {
    id: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'customerRemark',
    header: 'Customer Remark',
    cell: ({ row }) => {
      const cRem: string = row.getValue('customerRemark');
      return <>{cRem}</>;
    },
  },
  {
    accessorKey: 'enable',
    header: 'Enable',
    cell: ({ row }) => {
      const enable: boolean = row.getValue('enable');
      return (
        <>
          <Field>
            <Toggle checked={enable}>
              <Label hidden>Enable</Label>
            </Toggle>
          </Field>
        </>
      );
    },
  },
];

export type AdminRemarkColumn = {
  id: string;
  adminRemark: string;
  enable: boolean;
};

export const adminRemarkColumn: ColumnDef<AdminRemarkColumn>[] = [
  {
    id: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'adminRemark',
    header: 'Admin Remark',
    cell: ({ row }) => {
      const adminRemark: string = row.getValue('adminRemark');
      return <>{adminRemark}</>;
    },
  },
  {
    accessorKey: 'enable',
    header: 'Enable',
    cell: ({ row }) => {
      const enable: boolean = row.getValue('enable');
      return (
        <>
          <Field>
            <Toggle checked={enable}>
              <Label hidden>Enable</Label>
            </Toggle>
          </Field>
        </>
      );
    },
  },
];

export type ReturnDTOStageColumn = {
  id: string;
  dtoStage: string;
  enable: boolean;
};

export const returnDtoStageColumn: ColumnDef<ReturnDTOStageColumn>[] = [
  {
    id: 'id',
    header: 'Id',
    enableHiding: true,
  },
  {
    accessorKey: 'dtoStage',
    header: 'DTO Stage',
    cell: ({ row }) => {
      const dtoStage: string = row.getValue('dtoStage');
      return <>{dtoStage}</>;
    },
  },
  {
    accessorKey: 'enable',
    header: 'Enable',
    cell: ({ row }) => {
      const enable: boolean = row.getValue('enable');
      return (
        <>
          <Field>
            <Toggle checked={enable}>
              <Label hidden>Enable</Label>
            </Toggle>
          </Field>
        </>
      );
    },
  },
];
