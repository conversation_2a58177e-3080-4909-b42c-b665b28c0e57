import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../themes/theme';
import {
  Body,
  Cell,
  Head,
  HeaderCell,
  HeaderRow,
  Table,
  Row as TableRow,
  TableContainer,
  TableHolder,
} from '../../UI-components/Table';
import { Span } from '@zendeskgarden/react-typography';
import { Dropdown, Item, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import { Link as _Link } from 'react-router-dom';
import { DownIcon, LeftArrowIcon, RightArrowIcon } from '../../../utils/icons';
import { Col, Row } from '../../UI-components/Grid';
import { Button } from '../../UI-components/Button';
import { Pagination } from '../../UI-components/Pagination';
import {
  ReturnApprovedTableColumn,
  returnApprovedColumns as columns,
} from './columns';
import { DataTable } from './DataTable';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
// import TopBar from '../../topbar/TopBar';
import { CommonFilterProps, IPagination } from '../../../types/types';
import { mediaQuery } from '@zendeskgarden/react-theming';
import NothingToshow from '../../UI-components/NothingToShow';
import TopBar from './TopBar';
import LazyLoading from '../../UI-components/LazyLoading';
import ApprovedReturnFilter from '../../drawer/new-return-module/ApprovedReturnFilter';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import { RETURN_BASE } from '../../../../env';
import constants from '../../../constants';

// const TableContainer = styled(_TableContainer)`
//   border-radius: ${(p) => baseTheme.borderRadii.xs};
//   background-color: white;
//   height: 78dvh;
//   overflow-x: unset;
// `;

// const TableHolder = styled.div`
//   height: 90%;
//   border-top-left-radius: ${baseTheme.borderRadii.md};
//   border-top-right-radius: ${baseTheme.borderRadii.md};
//   border-bottom: 2px solid #2053754d;
//   overflow-x: auto;

//   @media screen and (max-width: 1366px) {
//     height: 80%;
//   }
// `;

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;

const ReturnApprovedTable = ({
  data,
  page,
  setPage,
  totalPage,
  searchContent,
  setSearchContent,
  refetch,
  filters,
  setFilters,
  count,
  // pagination,
  isFetching,
  isRefetching,
}: {
  data: any[];
  page: number;
  count: number;
  setPage: React.Dispatch<React.SetStateAction<number>>;
  totalPage: number;
  // pagination?: IPagination;
  searchContent: string | undefined;
  setSearchContent: React.Dispatch<React.SetStateAction<string | undefined>>;
  refetch?: () => void;
  filters: CommonFilterProps;
  setFilters: React.Dispatch<React.SetStateAction<CommonFilterProps>>;
  isFetching: boolean;
  isRefetching: boolean;
}) => {
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rotated, setRotated] = useState<boolean | undefined>();

  const [rowSelection, setRowSelection] = React.useState({});
  const [searchType, setsearchType] = useState<'order_id' | 'return_id'>(
    'order_id',
  );

  const [searchOrder, setSearchOrder] = useState<'order_id'>('order_id');

  const [isOpen, setIsOpen] = useState<boolean>(false);

  const options = {
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  };

  const dataTable = useReactTable(options);

  useEffect(() => {
    dataTable.setPageSize(filters.rowsPerPage || 50);
  }, []);

  const handleRowPerPage: (item: string) => void = (item: string) => {
    setFilters((prev: any) => ({
      ...prev,
      rowsPerPage: Number(item),
      page: 1,
    }));
    dataTable.setPageSize(Number(item));
  };

  const reset = () => {
    setFilters({
      page: 1,
      rowsPerPage: 50,
    });
  };

  const disabledColumns = ['Order Id', 'Awb Number', 'Sales Return Id'];
  const [alreadyEnabledColumn, setAlreadyEnabledColumn] = useState<string[]>([
    'Order Id',
    'Awb Number',
    'Payment Method',
    'Shipment Status',
    'Refund Status',
    'Sales Return Id',
    'Return Amount',
    'Invoice No',
    'Location',
    // 'Updated At',
    'Vinculum Status',
    'Customer Action',
    'Admin Action',
    'Transporter',
    'Return Date',
    'Return No',
    'Created at',
    'Cancel',
  ]);
  const axios = useAxios();
  const addToast = useToast();
  const downloadCsv = async () => {
    try {
      const response = await axios.get(
        `${RETURN_BASE}/api/v1/admin/returns/approved-returns/download-csv`,
        {
          params: {},
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      const csvData: any = response;
      const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });

      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `return-approved-data-${Date.now()}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      addToast('error', 'Failed to download CSV');
      console.error('Error downloading CSV:', error);
    }
  };

  return (
    <>
      <TopBar
        table={dataTable}
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        haveSearch={true}
        searchPlaceholder={'Search by Order Id'}
        refetch={refetch}
        setIsOpen={setIsOpen}
        handleSearch={() => {
          if (searchType === 'order_id') {
            setFilters({
              ...filters,
              order_id: searchContent,
              return_id: undefined,
            });
          }
        }}
        searchType={searchType}
        setSearchType={setsearchType}
        searchOrderId={searchOrder}
        setSearchOrderId={setSearchOrder}
        // haveFilter={true}
        alreadyEnabledColumn={alreadyEnabledColumn}
        setAlreadyEnabledColumn={setAlreadyEnabledColumn}
        disabledColumn={disabledColumns}
        reset={reset}
        exportFunction={downloadCsv}
        // setFilters={setFilters}
      />
      {isFetching || isRefetching ? (
        <LazyLoading />
      ) : (
        <Container>
          <TableContainer>
            {dataTable.getRowModel().rows?.length && (
              <>
                <TableHolder>
                  <DataTable
                    table={dataTable}
                    columns={columns}
                    data={data}
                    // onCancelClick={(id) => handleCancelClick(id)}
                    alreadyEnabledColumn={alreadyEnabledColumn}
                  />
                </TableHolder>
                <div style={{ overflowX: 'clip' }}>
                  <Row
                    style={{
                      height: `${
                        baseTheme.components.dimension.width.base * 5
                      }px`,
                      marginTop: baseTheme.space.sm,
                      backgroundColor: colors.white,
                      paddingLeft: baseTheme.space.lg,
                      paddingRight: baseTheme.space.lg,
                    }}
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="start" alignItems="center">
                        <Button
                          style={{
                            maxWidth:
                              baseTheme.components.dimension.width.base100,
                          }}
                          size="medium"
                          isAction
                          disabled={filters.page - 1 <= 0 ? true : false}
                          onClick={() => {
                            setFilters((prev) => ({
                              ...prev,
                              page: prev.page - 1 <= 0 ? 0 : prev.page - 1,
                            }));
                          }}
                        >
                          <Button.StartIcon>
                            <LeftArrowIcon />
                          </Button.StartIcon>
                          Previous
                        </Button>
                      </Row>
                    </Col>
                    <Col textAlign="center" lg={2} md={2}>
                      <Dropdown
                        onSelect={(item) => handleRowPerPage(item)}
                        onStateChange={(options) =>
                          Object.hasOwn(options, 'isOpen') &&
                          setRotated(options.isOpen)
                        }
                      >
                        <Trigger>
                          <Button size="medium" isAction disabled={count === 0}>
                            Row Per Page:
                            <Span style={{ paddingLeft: baseTheme.space.sm }}>
                              {dataTable.getState().pagination.pageSize}
                            </Span>
                            <Button.EndIcon
                              isRotated={rotated}
                              style={{ marginLeft: 0 }}
                            >
                              <DownIcon />
                            </Button.EndIcon>
                          </Button>
                        </Trigger>
                        <Menu>
                          <Item value={10}>10</Item>
                          <Item value={20}>20</Item>
                          <Item value={50}>50</Item>
                          <Item value={100}>100</Item>
                        </Menu>
                      </Dropdown>
                    </Col>
                    <Col lg={5} md={5}>
                      <Row justifyContent="center" alignItems="center">
                        <Pagination
                          color={baseTheme.colors.deepBlue}
                          totalPages={totalPage}
                          pagePadding={2}
                          currentPage={filters.page}
                          onChange={(e) =>
                            setFilters((prev) => ({ ...prev, page: e }))
                          }
                        />
                      </Row>
                    </Col>
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="start" alignItems="end">
                        <Button size="medium" isAction>
                          {(filters.page - 1) * (filters?.rowsPerPage || 1) + 1}
                          -
                          {count < filters.page * (filters?.rowsPerPage || 1)
                            ? count
                            : filters.page * (filters?.rowsPerPage || 1)}{' '}
                          of {count}
                        </Button>
                      </Row>
                    </Col>
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="end" alignItems="end">
                        <Button
                          style={{
                            maxWidth:
                              baseTheme.components.dimension.width.base100,
                          }}
                          size="medium"
                          isAction
                          disabled={
                            totalPage
                              ? filters.page + 1 >
                                Math.ceil(
                                  count / (filters.rowsPerPage as number),
                                )
                              : true
                          }
                          onClick={() => {
                            setFilters((prev) => ({
                              ...prev,
                              page:
                                prev.page + 1 >=
                                Math.ceil(
                                  count /
                                    ((filters.rowsPerPage as number) ?? 1),
                                )
                                  ? Math.ceil(
                                      count /
                                        ((filters.rowsPerPage as number) ?? 1),
                                    )
                                  : prev.page + 1,
                            }));
                          }}
                        >
                          Next
                          <Button.EndIcon>
                            <RightArrowIcon />
                          </Button.EndIcon>
                        </Button>
                      </Row>
                    </Col>
                  </Row>
                </div>
              </>
            )}
            {!isFetching &&
              !isRefetching &&
              dataTable.getRowModel().rows?.length == 0 && (
                <>
                  <NothingToshow divHeight="70vh" />
                </>
              )}
          </TableContainer>
        </Container>
      )}
      {isOpen && (
        <ApprovedReturnFilter
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          filters={filters}
          setFilters={setFilters}
          reset={reset}
        />
      )}
    </>
  );
};

export default ReturnApprovedTable;
