'use client';

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  VisibilityState,
  getPaginationRowModel,
  Table as TTable,
} from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';
import {
  Body,
  Cell as _Cell,
  Head,
  HeaderCell as _HeaderCell,
  HeaderRow as _HeaderRow,
  Row as _Row,
  Table,
} from '../../UI-components/Table';

import styled from 'styled-components';
import { baseTheme, colors } from '../../../themes/theme';
import { UpIcon, DownIcon } from '../../../utils/icons';

const HeaderRow = styled(_HeaderRow)<{ sticky?: boolean }>``;

const HeaderCell = styled(_HeaderCell)<{ sticky?: boolean }>`
  ${(p) => (p.sticky ? 'position: sticky; left : 0;' : '')}

  background-color: ${(p) => p.theme.colors.deepBlue};

  /* Add appropriate left margin and cell width for each sticky column */
  &:nth-child(1) {
    ${(p) => (p.sticky ? 'left: 0; z-index: 499;' : '')}
  }
  &:nth-child(2) {
    ${(p) => (p.sticky ? 'left: 250px;  z-index: 499;' : '')}
  }
  &:nth-child(3) {
    ${(p) => (p.sticky ? 'left: 500px;  z-index: 499;' : '')}
  }
`;

const Cell = styled(_Cell)<{ sticky?: boolean }>`
  /* Add more nth-child rules for additional sticky columns */
  ${(p) => (p.sticky ? 'position: sticky; left: 0;' : '')}

  background-color: inherit;

  /* Add appropriate left margin and cell width for each sticky column */
  &:nth-child(1) {
    ${(p) => (p.sticky ? 'left: 0;' : '')}
  }
  &:nth-child(2) {
    ${(p) => (p.sticky ? 'left: 250px;' : '')}
  }
  &:nth-child(3) {
    ${(p) => (p.sticky ? 'left: 500px;' : '')}
  }
`;

const Row = styled(_Row)`
  &:nth-child(odd) {
    background-color: ${colors.white};
  }
  &:nth-child(even) {
    background-color: ${colors.lightGrey};
  }
`;

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<any, any>[];
  data: TData[];
  table: any;
  tableSize?: '"small" | "medium" | "large" | undefined';
  onCancelClick?: (id: number) => void;
  alreadyEnabledColumn: string[];
}

export function CatalogDataTable<TData, TValue>({
  columns,
  data,
  table,
  tableSize,
  onCancelClick,
  alreadyEnabledColumn,
}: DataTableProps<TData, TValue>) {
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [rotated, setRotated] = useState<boolean | undefined>();
  const [isChecked, setIsChecked] = useState(false);

  const handleCheckboxChange = (event: any, column: any) => {
    const { checked } = event.target;
    setIsChecked(checked);
    console.log(column);
    column.toggleVisibility(checked);
  };

  return (
    <>
      <Table size={'medium'}>
        <Head isSticky>
          {table.getHeaderGroups().map((headerGroup:any) => (
              <HeaderRow key={headerGroup.id}>
              {headerGroup.headers.map((header:any, index:number) => {
                return (
                  !header.column.columnDef.enableHiding && (header.column.columnDef.accessorKey === 'Checkbox' || alreadyEnabledColumn.includes(
                    `${header.column.columnDef.accessorKey}`,
                  ) ) && (
                    <HeaderCell
                      style={{
                        minWidth:
                          index === 0
                            ? baseTheme.components.dimension.width.base100
                            : baseTheme.components.dimension.width.base150,
                        cursor: `${header.column.getCanSort() && 'pointer'}`
                      }}
                      key={header.id}
                      sticky={header.column.columnDef.enablePinning}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                      {{asc: <UpIcon style={{marginLeft: "5px"}} />, desc: <DownIcon style={{marginLeft: "5px"}} />}[header.column.getIsSorted() as string]}
                    </HeaderCell>
                  )
                );
              })}
            </HeaderRow>
          ))}
        </Head>
        <Body>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row:any) => (
              <Row key={row.id} data-state={row.getIsSelected() && 'selected'}>
                {row.getVisibleCells().map(
                  (cell:any, index:number) =>
                    !cell.column.columnDef.enableHiding && (cell.column.columnDef.accessorKey === 'Checkbox' || alreadyEnabledColumn.includes(
                        `${cell.column.columnDef.accessorKey}`,
                      ) ) && (
                      <Cell
                        style={{
                          minWidth:
                            index === 0
                              ? baseTheme.components.dimension.width.base100
                              : baseTheme.components.dimension.width.base150,
                        }}
                        sticky={cell.column.columnDef.enablePinning}
                        key={cell.id}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </Cell>
                    ),
                )}
              </Row>
            ))
          ) : (
            <Row>
              <Cell colSpan={columns.length} className="h-24 text-center">
                No results.
              </Cell>
            </Row>
          )}
        </Body>
      </Table>
    </>
  );
}
