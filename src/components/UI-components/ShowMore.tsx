import React, { useEffect, useState } from 'react';
import { baseTheme } from '../../themes/theme';

interface ShowMoreProps {
  text: string | object;
  limit: number;
}

const ShowMore: React.FC<ShowMoreProps> = ({ text, limit }) => {
  const [expanded, setExpanded] = useState(false);
  const [displayText, setDisplayText] = useState('');

  useEffect(() => {
    setExpanded(false);
    const textToDisplay =
      typeof text === 'object' ? JSON.stringify(text, null, 2) : text;
    setDisplayText(textToDisplay);
  }, [text, limit]);

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  const renderContent = () => {
    if (expanded) {
      return (
        <>
          {displayText}{' '}
          <span
            onClick={toggleExpanded}
            style={{
              color: baseTheme.colors.primaryHue,
              cursor: 'pointer',
              fontWeight: baseTheme.customFontWeights.medium,
            }}
          >
            Read Less
          </span>
        </>
      );
    }

    if (!displayText) {
      return null;
    }

    const displayedCharacters = displayText.slice(0, limit);
    const hiddenCharacters = displayText.slice(limit);

    return (
      <>
        {displayedCharacters}{' '}
        {hiddenCharacters && (
          <span
            onClick={toggleExpanded}
            style={{
              color: baseTheme.colors.primaryHue,
              cursor: 'pointer',
              fontWeight: baseTheme.customFontWeights.medium,
            }}
          >
            ... Read More
          </span>
        )}
      </>
    );
  };

  return <div className="content">{renderContent()}</div>;
};

export default ShowMore;
