import { Input as _Input } from '@zendeskgarden/react-forms';
import styled from 'styled-components';

export const SInput = styled(_Input)`
  padding: 20px;
`;
import { baseTheme } from '../../themes/theme';

interface InputProps {
  BackgroundColor?: string;
  fontSize?: string;
}

const Input = styled(_Input) <InputProps>`
  ${(props) =>
    props.BackgroundColor
      ? `background-color: ${props.BackgroundColor};`
      : `background-color: ${props.theme.colors.white};`}
  font-weight: ${(p) => p.theme.fontWeights.regular};
  ${(props) =>
    props.fontSize
      ? `font-size: ${props.fontSize};`
      : `font-size: ${props.theme.fontSizes.sm};`}
  color: ${(p) => p.theme.colors.black};

  &:active {
    background-color: ${(p) => p.theme.colors.white};
    color: ${(p) => p.theme.colors.black};
    border: ${(p) => p.theme.borders.sm};
      ${(p) => p.theme.components.TextInput.borderColor};
    outline: ${baseTheme.borders.sm};
      ${baseTheme.components.TextInput.borderColor};
    box-shadow: none;
  }
  &:focus {
    background-color: ${baseTheme.colors.white};
    color: ${baseTheme.colors.black};
    outline: ${baseTheme.borders.sm};
      ${baseTheme.components.TextInput.borderColor};
    box-shadow: none;
  }

  &:hover {
    border: ${baseTheme.borders.sm};
      ${baseTheme.components.TextInput.borderColor};
    box-shadow: none;
  }
  ::placeholder {
    color: grey;
  }
`;

export default Input;
