import { Grid, Row as _Row, Col as _Col } from '@zendeskgarden/react-grid';
import styled from 'styled-components';
import { colors } from '../../themes/theme';

Grid.defaultProps = {
  gutters: false,
};

type propTypes = {
  mg?: 'xxs' | 'xs' | 'sm' | 'md' | 'lg'; //margin
  mt?: 'xxs' | 'xs' | 'sm' | 'md' | 'lg'; //margin-top
  mb?: 'xxs' | 'xs' | 'sm' | 'md' | 'lg'; //margin-bottom
  ml?: 'xxs' | 'xs' | 'sm' | 'md' | 'lg'; //margin-left
  mr?: 'xxs' | 'xs' | 'sm' | 'md' | 'lg'; //margin-right
};

type animtedProp = {
  showDatePicker?: boolean;
};

export const Row = styled(_Row)<propTypes>`
  ${(p) => p.mg && `margin:${p.theme.space[p.mg]}`}
  ${(p) => p.mt && `margin-top:${p.theme.space[p.mt]}`}
  ${(p) => p.mb && `margin-bottom:${p.theme.space[p.mb]}`}
  ${(p) => p.ml && `margin-left:${p.theme.space[p.ml]}`}
  ${(p) => p.mr && `margin-right:${p.theme.space[p.mr]}`}
`;
export const Col = styled(_Col)<propTypes>`
  ${(p) => p.mg && `margin:${p.theme.space[p.mg]}`}
  ${(p) => p.mt && `margin-top:${p.theme.space[p.mt]}`}
  ${(p) => p.mb && `margin-bottom:${p.theme.space[p.mb]}`}
  ${(p) => p.ml && `margin-left:${p.theme.space[p.ml]}`}
  ${(p) => p.mr && `margin-right:${p.theme.space[p.mr]}`}
`;

export const AnimatedRow = styled(Row)<animtedProp>`
  transition: opacity 0.5s, transform 0.5s;
`;

export const SROW = styled(Row)`
  background-color: ${colors.transparentLightGrey};
  padding: ${(p) => p.theme.space.md};
  border: 1px solid #dadada;
`;

export const Spacer = styled(Row)`
  height: ${(p) => p.theme.components.dimension.height.sm};
`;

export const SmallSpacer = styled(Row)`
  height: ${(p) => p.theme.components.dimension.height.xsm};
`;

export default Grid;
