import { Col, Row } from '@zendeskgarden/react-grid';
import { FrameIcon } from '../../utils/icons';
import { XXL } from './Typography';
import { baseTheme } from '../../themes/theme';

const NothingToshow = ({ divHeight }: { divHeight?: string }) => {
  return (
    <Row
      justifyContent="center"
      alignItems="center"
      style={{
        height: divHeight ? divHeight : '87vh',
        backgroundColor: 'white',
        margin: baseTheme.space.md,
        padding: '0px',
        borderRadius: baseTheme.borderRadii.xs,
      }}
    >
      <Col>
        <Row justifyContent="center" alignItems="center">
          <XXL>Nothing To Show</XXL>
        </Row>
        <Row justifyContent="center" alignItems="center">
          <FrameIcon />
        </Row>
      </Col>
    </Row>
  );
};
export default NothingToshow;
