import { baseTheme } from '../../themes/theme';
import styled from 'styled-components';
import { Field as _Field } from '@zendeskgarden/react-forms';
export const Field = styled(_Field)`
  div {
    padding-top: ${(p) => p.theme.space.sm};
    padding-bottom: ${(p) => p.theme.space.sm};
    background-color: ${(p) => p.theme.colors.grey};
    border: ${baseTheme.borders.sm} ${(p) => p.theme.colors.grey};
    border-radius: ${(p) => p.theme.borderRadii.md};
  }
`;
