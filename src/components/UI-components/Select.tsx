import { Select as _Select } from '@zendeskgarden/react-dropdowns';
import styled from 'styled-components';
import { baseTheme } from '../../themes/theme';

export const SSelect = styled(_Select)`
  padding: 20px;
`;

interface SelectProps {
    BackgroundColor?: string;
    fontSize?: string;
}

const Select = styled(_Select) <SelectProps>`
  ${(props) =>
        props.BackgroundColor
            ? `background-color: ${props.BackgroundColor};`
            : `background-color: ${props.theme.colors.white};`}
  font-weight: ${(p) => p.theme.fontWeights.regular};
  ${(props) =>
        props.fontSize
            ? `font-size: ${props.fontSize};`
            : `font-size: ${props.theme.fontSizes.sm};`}
  color: ${(p) => p.theme.colors.black};

  &:active {
    background-color: ${(p) => p.theme.colors.white};
    color: ${(p) => p.theme.colors.black};
    border: ${(p) => p.theme.borders.sm}
      ${(p) => p.theme.components.TextInput.borderColor};
    outline: ${baseTheme.borders.sm}
      ${baseTheme.components.TextInput.borderColor};
    box-shadow: none;
  }
  &:focus {
    background-color: ${baseTheme.colors.white};
    color: ${baseTheme.colors.black};
    outline: ${baseTheme.borders.sm}
      ${baseTheme.components.TextInput.borderColor};
    box-shadow: none;
  }

  &:hover {
    border: ${baseTheme.borders.sm}
      ${baseTheme.components.TextInput.borderColor};
    box-shadow: none;
  }
  ::placeholder {
    color: grey;
  }
`;

export default Select;
