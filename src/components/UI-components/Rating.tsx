import React from 'react';
import { BlankStarIcon, FullStarIcon, HalfStarIcon } from '../../utils/icons';

interface StarRatingProps {
  rating: number;
}

const StarRating: React.FC<StarRatingProps> = ({ rating }) => {
  const getStarIcon = (index: number): JSX.Element => {
    if (rating >= index + 1) {
      return <FullStarIcon />;
    } else if (rating > index) {
      return <HalfStarIcon />;
    } else {
      return <BlankStarIcon />;
    }
  };

  return (
    <div>
      {Array.from({ length: 5 }, (_, index) => (
        <span key={index}>{getStarIcon(index)}</span>
      ))}
    </div>
  );
};

export default StarRating;

// import React, { useState } from 'react';
// import styled from 'styled-components';
// import {
//   BlankStarIcon,
//   FullStarIcon,
//   HalfStarIcon,
//   HalfStarIcon as LeftHalfStar,
// } from '../../utils/icons'; // Replace with your star icon components

// const colors = {
//   muted: { default: '#ccc' },
//   danger: { default: 'red' },
//   warning: { default: 'orange' },
//   success: { default: 'green' },
// };

// const RatingStar = styled(FullStarIcon)`
//   color: ${({ rating, index }) => {
//     if (index > rating) return colors.muted.default;
//     if (rating < 1) return colors.danger.default;
//     if (rating >= 1 && rating < 2) return colors.warning.default;
//     else return colors.success.default;
//   }};
// `;

// const RatingStarHalf = styled(HalfStarIcon)`
//   color: ${({ rating, index }) => {
//     if (rating < 1) return colors.danger.default;
//     if (rating >= 1 && rating < 2) return colors.warning.default;
//     else return colors.success.default;
//   }};
// `;

// const Rating = ({ rating }) => {
//   const [hoverRating, setHoverRating] = useState(0);

//   const handleMouseEnter = (index) => {
//     setHoverRating(index);
//   };

//   const handleMouseLeave = () => {
//     setHoverRating(0);
//   };

//   return (
//     <div className="d-flex align-items-center">
//       <span className="font-weight-bold mr-2">{rating.toFixed(1)}</span>
//       {[0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5].map((index) => {
//         const isHovered = index <= hoverRating;
//         const isActive = index <= rating;

//         return (
//           <React.Fragment key={index}>
//             {isActive && !isHovered ? (
//               <RatingStar rating={rating} index={index} size={25} />
//             ) : (
//               <RatingStarHalf rating={rating} index={index} size={25} />
//             )}
//             <RatingStar
//               rating={hoverRating}
//               index={index}
//               size={25}
//               onMouseEnter={() => handleMouseEnter(index)}
//               onMouseLeave={handleMouseLeave}
//             />
//           </React.Fragment>
//         );
//       })}
//     </div>
//   );
// };

// export default Rating;
