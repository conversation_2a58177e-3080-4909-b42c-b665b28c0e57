import { baseTheme } from '../../themes/theme';
import styled from 'styled-components';
import { Modal as _Modal } from '@zendeskgarden/react-modals';

interface ModalProps {
  haveFooter?: boolean;
  isLarger?: boolean;
}

export const Modal = styled(_Modal)<ModalProps>`
  & > [data-garden-id='modals.header'] {
    background-color: ${baseTheme.colors.primaryHue};
  }
  & > [data-garden-id='modals.body'] {
    margin-bottom: ${(p) => (p.haveFooter ? '0px' : baseTheme.fontSizes.xl)};
  }
  & > [data-garden-id='modals.footer'] {
    padding-top: ${(p) => p.haveFooter && '0px'};
  }
`;

export const SModal = styled(_Modal)<{ isLarger?: boolean }>`
  border-radius: ${(p) => p.theme.space.base * 4}px;
  transition: width 0.3s ease-in-out;
  ${(p) => p.isLarger && 'width:80vw;'}
`;
