import { Col, Row } from '@zendeskgarden/react-grid';
import styled from 'styled-components';
import { Avatar } from '@zendeskgarden/react-avatars';
import { PALETTE } from '@zendeskgarden/react-theming';
import { Label } from '@zendeskgarden/react-forms';
import { SM } from '@zendeskgarden/react-typography';
import WomenAv from '../../utils/icons/womenPhoto.png';
import { baseTheme } from '../../themes/theme';
const MainDiv = styled.div`
  max-width: ${(p) => p.theme.components.dimension.width.base200};
  padding: ${(p) => p.theme.space.md};
  background-color: ${(p) => p.theme.colors.white};
`;
const UserDetails = () => {
  return (
    <>
      <MainDiv>
        <Row>
          <Col size={4}>
            <Avatar backgroundColor={PALETTE.grey[600]} status="available">
              <img alt="image avatar" src={WomenAv} />
            </Avatar>
          </Col>
          <Col size={7}>
            <Label style={{ color: baseTheme.colors.primaryHue }}>
              <PERSON>
            </Label>
            <SM style={{ color: baseTheme.colors.veryDarkGray }}>Admin</SM>
          </Col>
        </Row>
      </MainDiv>
    </>
  );
};
export default UserDetails;
