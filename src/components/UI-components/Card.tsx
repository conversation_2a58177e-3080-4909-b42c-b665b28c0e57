import styled from 'styled-components';
import { baseTheme, colors } from '../../themes/theme';

interface CardProps {
  bg?:
    | 'white'
    | 'black'
    | 'primary'
    | 'secondary'
    | 'neutral'
    | 'green'
    | 'grey';
  minHeight?: boolean;
  shadow?: boolean;
}

export const Card = styled.div<CardProps>`
  box-sizing: border-box;
  border: ${baseTheme.borders.sm} ${colors.darkBoredGrey};
  border-radius: ${baseTheme.borderRadii.md};
  ${(_) => (_.shadow ? 'box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);' : '')}
  background-color: ${(props) => {
    switch (props.bg) {
      case 'white':
        return colors.white;
      case 'black':
        return colors.black;
      case 'primary':
        return baseTheme.colors.deepBlue;
      case 'secondary':
        return baseTheme.colors.deepOrange;
      case 'neutral':
        return colors.darkGrey;
      case 'green':
        return baseTheme.colors.solidGreen;
      case 'grey':
        return colors.zikaGrey;
      default:
        return colors.lightGrey;
    }
  }};
`;
