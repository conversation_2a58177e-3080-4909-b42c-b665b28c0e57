import styled from 'styled-components';
import { Pagination as _Pagination } from '@zendeskgarden/react-pagination';
import { baseTheme, colors } from '../../themes/theme';

export const Pagination = styled(_Pagination)`
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)};

  & > [aria-current='true'] {
    background-color: ${baseTheme.colors.deepBlue};
    color: ${colors.white};
  }

  & > [aria-current='true']:hover {
    background-color: ${colors.transparentGrey};
    color: ${baseTheme.colors.deepBlue};
  }

  & > [data-garden-id="pagination.navigation"] {
    display:none;
  }
`;
