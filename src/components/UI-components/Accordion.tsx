import { Accordion as _Accordion } from '@zendeskgarden/react-accordions';
import styled from 'styled-components';

interface AccordionProps {}

export const Accordion = styled(_Accordion)<AccordionProps>`
  margin-top: 20px;
  & > [data-garden-id='accordions.section'] {
    border: 1px solid rgba(0, 0, 0, 0.2);
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    border-radius: 6px;
    margin-bottom: 5px;
    word-break: break-word;
    & > [data-garden-id='accordions.header'] {
      display: flex;
      justify_content: center;
      & > [data-garden-id='accordions.rotate_icon'] {
        display: none;
      }
      & > [data-garden-container-id='containers.tooltip'] {
        margin: 20px;
      }
    }
  }
`;
