import {
  MD as _MD,
  SM as _SM,
  LG as _LG,
  XL as _XL,
  XXL as _XXL,
  XXXL as _XXXL,
  SM as _XS,
} from '@zendeskgarden/react-typography';
import styled from 'styled-components';
import { baseTheme, colors } from '../../themes/theme';

interface Properties {
  color?: string;
  hue?:
    | 'white'
    | 'black'
    | 'primary'
    | 'secondary'
    | 'neutral'
    | 'success'
    | 'danger'
    | 'green'
    | 'baseGreen';

  isCenter?: boolean;
}

const getColor = (props: any) => {
  switch (props.hue) {
    case 'white':
      return colors.white;
    case 'black':
      return colors.black;
    case 'primary':
      return baseTheme.colors.deepBlue;
    case 'secondary':
      return baseTheme.colors.deepOrange;
    case 'neutral':
      return baseTheme.colors.neutralHue;
    case 'success':
      return baseTheme.colors.successHue;
    case 'danger':
      return baseTheme.colors.dangerHue;
    case 'green':
      return baseTheme.colors.solidGreen;
    case 'baseGreen':
      return baseTheme.colors.transparentGreen;
    default:
      return baseTheme.colors.deepBlue;
  }
};

export const XS = styled(_XS)<Properties>`
  //font-size: 12px;
  font-size: 13px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;

export const SM = styled(_SM)<Properties>`
  //font-size: 14px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;

export const XSM = styled(_SM)<Properties>`
  font-size: 14px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;

export const XMD = styled(_MD)<Properties>`
  font-size: 15px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;

export const MD = styled(_MD)<Properties>`
  font-size: 16px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;

export const SmallHeading = styled(_MD)<Properties>`
  font-size: 18px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;

export const LG = styled(_LG)<Properties>`
  font-size: 20px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;

export const Heading = styled(_MD)<Properties>`
  font-size: 22px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;

export const XL = styled(_XL)<Properties>`
  //font-size: 24px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;

export const XXL = styled(_XXL)<Properties>`
  //font-size: 36px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;

export const XXXL = styled(_XXXL)<Properties>`
  //font-size: 48px;
  color: ${(props) => getColor(props)};
  ${(p) => (p.isCenter ? 'text-align: center;' : '')}
`;
