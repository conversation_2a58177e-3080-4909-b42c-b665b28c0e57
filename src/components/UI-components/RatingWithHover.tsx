import React, { useEffect, useState } from 'react';

interface StarProps {
  ownValue: number;
  rating: number;
  setRating: React.Dispatch<React.SetStateAction<number>>;
}

const Star: React.FC<StarProps> = ({ ownValue, rating, setRating }) => {
  const [hovered, setHovered] = useState(false); 
  return (
    <svg
      onClick={() => {
        setRating(ownValue);
        setHovered(true);
      }}
      onMouseLeave={() => {
        setHovered(false);
      }}
      style={{
        width: '40px',
        height: '40px',
        cursor: 'pointer',
        fill: hovered || rating >= ownValue || rating == 5 ? '#FCC21A' : 'none',
        stroke: '#000',
        strokeWidth:
          hovered || rating >= ownValue || rating == 5 ? '0.0px' : '0.5px',  
      }}
      viewBox="0 0 24 24"
    >
      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
    </svg>
  );
};

export const RatingWithHover = ({
  rating,
  setRating,
}: {
  rating: number;
  setRating: React.Dispatch<React.SetStateAction<number>>;
}) => {
  return (
    <div>
      {[1, 2, 3, 4, 5].map((v) => (
        <Star key={v} ownValue={v} rating={rating}  
        setRating={(newRating) => {
          setRating(newRating);
        }}/>
      ))}
    </div>
  );
};
