import { baseTheme, colors } from '../../themes/theme';
import styled from 'styled-components';

interface ContainerProps {
  haveBackground?: boolean;
  height?: string;
}
export const Container = styled.div<ContainerProps>`
   
  ${(props) => props.haveBackground && `min-height: ${props.height ? props.height : '83vh'};`}
  ${(props) => props.haveBackground && `margin: ${baseTheme.space.md};`}
  ${(props) => props.haveBackground && `background-color: ${colors.white};`}
  ${(props) => props.haveBackground && `box-shadow: -2px -2px 66px rgba(0, 0, 0, 0.08)`};
  border-radius: 8px;
  padding: 20px;
`;
