import { FC } from 'react';
import styled, { keyframes } from 'styled-components';
import { Row } from './Grid';

const CenteredContent = styled.div`
  position: absolute;
  background-color: ${(props) => props.theme.colors.bodyBackground};
  min-height: 100vh;
  width: 100vw;
  left: 0;
  top: 0;
  z-index: 100;
`;

// const Page = styled.div`
//   height: 100vh;
//   width: 100vw;
//   display: flex;
//   flex-direction: column;
//   justify-content: center;
//   align-items: center;
//   background-color: #eceff1;
// `;

const pulse = keyframes`
    0% {
        opacity:0.2;
    }
    50% {
        opacity:0.8
    }
    100% {
        opacity:0.2;
    }
`;

const IconImage = styled.img`
  margin-top: 20%;
  height: 4rem;
  border-radius: 0.4rem;
  animation: ${pulse} 1.2s infinite;
  filter: grayscale(100%);
`;

const LazyLoading: FC = () => {
  return (
    //<CenteredContent>
    <Row
      style={{ marginLeft: '0px', marginRight: '0px' }}
      justifyContent="center"
      alignItems="center"
    >
      <IconImage
        src="https://dentalkart-media.s3.ap-south-1.amazonaws.com/updatedLogo/Logo.png"
        alt="Dentalkart Logo"
      />
    </Row>
    //</CenteredContent>
  );
};

export default LazyLoading;
