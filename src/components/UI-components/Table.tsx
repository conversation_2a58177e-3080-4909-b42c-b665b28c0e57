/* eslint-disable react-refresh/only-export-components */
import {
  Body as _Body,
  Cell as _Cell,
  Head as _Head,
  HeaderCell as _HeaderCell,
  HeaderRow as _HeaderRow,
  Row as _Row,
  Table as _Table,
} from '@zendeskgarden/react-tables';
import styled from 'styled-components';
import { baseTheme, colors } from '../../themes/theme';

interface CellProps {
  isBold?: boolean;
  isPrimary?: boolean;
  width?: string;
}

export const Head = styled(_Head)`
  background-color: ${(p) => p.theme.colors.deepBlue};
`;

export const HeaderCell = styled(_HeaderCell)<CellProps>`
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: ${(p) => p.theme.fontSizes.md};
  ${(p) =>
    p.isPrimary ? `color:${p.theme.colors.deepBlue}` : `color:${colors.white}`};
  ${(p) => p.isBold && `font-weight:${p.theme.fontWeights.semibold}`};
  ${(p) => p.width && `width : ${p.width};`}
`;

export const Table = styled(_Table)`
  min-width: 500;
  table-layout: auto;
  border-collapse: unset;
`;

export const _TableContainer = styled.div`
  overflow-x: auto; 
`;

export const HeaderRow = styled(_HeaderRow)``;

export const Body = styled(_Body)``;

export const Cell = styled(_Cell)<CellProps>`
  font-size: ${(p) => p.theme.fontSizes.md};
  ${(p) => p.isPrimary && `color:${p.theme.colors.deepBlue}`};
  ${(p) => p.isBold && `font-weight:${p.theme.fontWeights.semibold}`};
`;
export const Row = styled(_Row)`
  background-color: ${colors.white};
  box-shadow: ${baseTheme.components.properties.boxShadow};
  vertical-align: middle;
  margin-top: ${baseTheme.space.sm};
  margin-bottom: ${baseTheme.space.xs};
`;

export const TableContainer = styled(_TableContainer)`
  border-radius: ${baseTheme.borderRadii.xs};
  background-color: white;
  height: 78vh;
  @media (max-height: ${baseTheme.breakpoints.md}) {
    height: 74vh;
  }
`;

export const TableHolder = styled.div`
  height: 90%;
  border-radius: ${baseTheme.borderRadii.xs} ${baseTheme.borderRadii.xs} 0 0;
  border-bottom: 2px solid #2053754d;
  overflow-x: auto;
  @media (max-height: ${baseTheme.breakpoints.md}) {
    height: 86%;
  }
  &::-webkit-scrollbar {
    width: 0px; /* Set the width of the scrollbar */
  }

  /* Customize the scrollbar track for WebKit browsers */
  &::-webkit-scrollbar-track {
    background: #f1f1f1; /* Set the background color of the track */
  }
`;
