import React, { useEffect, useState } from 'react';

import { Button } from './Button';
import { baseTheme } from '../../themes/theme';

export interface Tab {
  title: string;
  content: React.ReactNode;
}

export const Tabs: React.FC<{
  tabs: Tab[];
  setSelectedTab?: React.Dispatch<React.SetStateAction<number>>;
  activeTab: number;
  setActiveTab: React.Dispatch<React.SetStateAction<number>>;
}> = ({ tabs, setSelectedTab, activeTab, setActiveTab }) => {
  const handleTabClick = (index: number) => {
    setActiveTab(index);
    if (setSelectedTab) {
      setSelectedTab(index);
    }
    sessionStorage.setItem('activeTabIndex', index.toString());
  };
  useEffect(() => {}, [activeTab]); 
  useEffect(() => { 
    const storedIndex = sessionStorage.getItem('activeTabIndex');
    if (storedIndex !== null) {
      const index = parseInt(storedIndex, 10);
      setActiveTab(index);
      if (setSelectedTab) {
        setSelectedTab(index);
      }
    } 
    const storeActiveTabIndex = () => {
      sessionStorage.setItem('activeTabIndex', activeTab.toString());
    };
  
    window.addEventListener('beforeunload', storeActiveTabIndex);
  
    return () => { 
      window.removeEventListener('beforeunload', storeActiveTabIndex);
      sessionStorage.removeItem('activeTabIndex');
    };
  }, [activeTab]); 
  
  

  return (
    <div className="tabs">
      <div className="tab-buttons">
        {tabs.map((tab, index) => (
          <Button
            key={index}
            className={index === activeTab ? 'active' : ''}
            onClick={() => handleTabClick(index)}
            style={{
              padding: '15px 20px',
              color: baseTheme.colors.black,
              border: baseTheme.customBorder.none,
              fontSize: baseTheme.customFontSizes.md + 2,
              fontWeight: 600,
              margin: `${baseTheme.components.dimension.width.base - 10}px ${
                baseTheme.components.dimension.width.base - 10
              }px`,
              borderTop:
                index === activeTab
                  ? baseTheme.customBorder.styleSix
                  : baseTheme.customBorder.styleFive,
              cursor: baseTheme.components.properties.pointer,
              borderRadius: 0,
              backgroundColor:
                index === activeTab ? baseTheme.colors.white : '#F6F6F6',
              minHeight: '50px',
            }}
          >
            {tab.title}
          </Button>
        ))}
      </div>
      <div>{tabs[activeTab].content}</div>
    </div>
  );
};
