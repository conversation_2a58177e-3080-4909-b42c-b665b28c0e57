import styled from 'styled-components';
import { Tag as _Tag } from '@zendeskgarden/react-tags';
import { baseTheme } from '../../themes/theme';

interface TagProps {
  isOpen?: boolean;
  active?: boolean;
}

export const Tag = styled(_Tag) <TagProps>`
  padding: ${(p) => p.theme.space.md};
  color: ${(p) =>
    p.isOpen
      ? p.theme.colors.solidGreen
      : p.active
        ? p.theme.colors.primaryHue
        : p.theme.colors.veryDarkGray};
  border: ${(p) =>
    p.active ? p.theme.customBorder.styleTwo : p.theme.customBorder.none};
`;
