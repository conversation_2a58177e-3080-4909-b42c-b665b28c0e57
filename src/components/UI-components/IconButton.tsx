import { IconButton as _IconButton } from '@zendeskgarden/react-buttons';
import styled, { css } from 'styled-components';
import { baseTheme } from '../../themes/theme';

interface ButtonProps {
  isOrange?: boolean;
}

export const IconButton = styled(_IconButton)<ButtonProps>`
  ${(props) =>
    props.isOrange
      ? css`
          background-color: ${baseTheme.colors.deepOrange};
        `
      : css`
          background-color: none;
        `};
`;
