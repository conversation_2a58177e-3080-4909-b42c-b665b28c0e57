import {
  MediaInput as _MediaInput,
  Field as _Field,
} from '@zendeskgarden/react-forms';
import styled from 'styled-components';
interface Props {
  greyBgcolor?: boolean;
}
// const MediaInput = styled(_MediaInput)<Props>`

//   ${(props) =>
//     props.greyBgcolor
//       ? `background-color:${baseTheme.colors.grey};;`
//       : `background-color: ${baseTheme.colors.white};`}
//   //background-color: ${baseTheme.colors.white};
//   font-weight: ${baseTheme.fontWeights.regular};
//   font-size: ${baseTheme.fontSizes.xs};
//   color: ${baseTheme.colors.black};
//   ::placeholder {
//     color: #bcbcbc;
//   }
// `;
export const MediaInput = styled(_MediaInput)<Props>`
  color: ${(p) => p.theme.colors.black};

  input::placeholder {
    color: ${(p) => p.theme.colors.blue};
  }
`;
// export default MediaInput;
