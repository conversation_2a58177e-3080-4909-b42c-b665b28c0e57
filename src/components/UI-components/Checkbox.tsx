import React, { ReactNode } from 'react';

type CustomCheckboxProps = {
  checked: boolean;
  onChange: (e: any) => void;
  children: ReactNode;
};

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  checked,
  onChange,
  children,
}) => {
  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div
        style={{
          width: '20px',
          height: '20px',
          border: '1px solid #000',
          borderRadius: '4px',
          backgroundColor: checked ? '#000' : 'transparent',
          marginRight: '8px',
          cursor: 'pointer',
        }}
        onClick={onChange}
      />
      <label style={{ fontSize: '14px' }}>{children}</label>
    </div>
  );
};

export default CustomCheckbox;
