import {
  Button as _Button,
  Anchor as _Anchor,
} from '@zendeskgarden/react-buttons';
import styled from 'styled-components';
import { baseTheme } from '../../themes/theme';

interface ButtonProps {
  isOrange?: boolean;
  isAction?: boolean;
}

export const Button = styled(_Button)<ButtonProps>`  
  ${(props) =>
    props.isPrimary
      ? props.isOrange
        ? `background-color: ${baseTheme.colors.deepOrange};`
        : `background-color: ${baseTheme.colors.deepBlue};`
      : props.isDanger
      ? `background-color: ${baseTheme.colors.dangerHue};`
      : `background-color: none;`};

  min-width: ${(p) =>
    p.isAction
      ? p.theme.components.dimension.width.base * 2
      : p.theme.components.dimension.width.base * 12}px;

  svg {
    width: ${(p) => p.theme.iconSizes.lg} !important;
    height: ${(p) => p.theme.iconSizes.lg} !important;
  }

  span {
    margin-left: ${(p) => (p.isAction ? `${p.theme.xs}` : ``)};
  }

  ${(p) =>
    p.isAction && !p.isPrimary ? `background: rgba(32, 83, 117, 0.05);` : ``}

  ${(props) =>
    props.isAction
      ? `
        border: ${props.theme.borders.sm} rgba(32, 83, 117, 0.5);
        border-radius: ${props.theme.borderRadii.md};
        font-size: ${props.theme.fontSizes.md};
        `
      : ``}
`;

export const IButton = styled(_Button)<ButtonProps>`
  ${(props) =>
    props.isPrimary
      ? props.isOrange
        ? `background-color: ${baseTheme.colors.deepOrange};`
        : `background-color: ${baseTheme.colors.deepBlue};`
      : props.isDanger
      ? `background-color: ${baseTheme.colors.dangerHue};`
      : `background-color: none;`};

  min-width: ${(p) =>
    p.isAction
      ? p.theme.components.dimension.width.base * 2
      : p.theme.components.dimension.width.base * 12}px;

  svg {
    width: ${(p) => p.theme.iconSizes.sm} !important;
    height: ${(p) => p.theme.iconSizes.sm} !important;
  }

  span {
    margin-left: ${(p) => (p.isAction ? `${p.theme.xs}` : ``)};
  }

  ${(p) =>
    p.isAction && !p.isPrimary ? `background: rgba(32, 83, 117, 0.05);` : ``}

  ${(props) =>
    props.isAction
      ? `
        border: ${props.theme.borders.sm} rgba(32, 83, 117, 0.5);
        border-radius: ${props.theme.borderRadii.md};
        font-size: ${props.theme.fontSizes.md};
        `
      : ``}
`;

export const Buttons = styled(_Button)<ButtonProps>`
  ${(props) =>
    props.isPrimary
      ? props.isOrange
        ? `background-color: ${baseTheme.colors.deepOrange};`
        : `background-color: ${baseTheme.colors.deepBlue};`
      : props.isDanger
      ? `background-color: ${baseTheme.colors.dangerHue};`
      : `background-color: none;`};
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}

  min-width: ${(p) =>
    p.isAction
      ? p.theme.components.dimension.width.base * 2
      : p.theme.components.dimension.width.base * 12}px;

  svg {
    width: ${(p) => p.theme.iconSizes.md} !important;
    height: ${(p) => p.theme.iconSizes.md} !important;
  }

  span {
    margin-left: ${(p) => (p.isAction ? `${p.theme.xs}` : ``)};
  }

  ${(p) =>
    p.isAction && !p.isPrimary ? `background: rgba(32, 83, 117, 0.05);` : ``}

  ${(props) =>
    props.isAction
      ? `
        border: ${props.theme.borders.sm} rgba(32, 83, 117, 0.5);
        border-radius: ${props.theme.borderRadii.md};
        font-size: ${props.theme.fontSizes.md};
        `
      : ``}
`;

export const Anchor = styled(_Anchor)`
  font-size: ${(p) => p.theme.fontSizes.xl};
  font-weight: ${baseTheme.fontWeights.semibold};
`;
