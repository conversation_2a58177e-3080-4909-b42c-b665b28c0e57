import {
  Notification,
  Title,
  Close,
  ToastProvider,
} from '@zendeskgarden/react-notifications';
import { <PERSON><PERSON>ventHandler } from 'react';
import { toastType } from '../../hooks/useToast';
interface PropsType {
  type: toastType;
  message: string;
  close: Mouse<PERSON>ventHandler<HTMLButtonElement>;
}
const returnTitle = (type: 'info' | 'error' | 'success' | 'warning') => {
  return type.charAt(0).toUpperCase() + type.slice(1);
};
const Toast = ({ type, message, close }: PropsType) => {
  return (
    <Notification
      type={type}
      style={{ maxWidth: 450, zIndex: 100, borderRadius: '1rem' }}
    >
      <Title>{returnTitle(type)}</Title>
      {message}
      <Close aria-label="Close" onClick={close} />
    </Notification>
  );
};

export default Toast;
