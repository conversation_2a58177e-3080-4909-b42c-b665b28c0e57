import React, { useState } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import Input from '../../UI-components/Input';
import { Field as _Field } from '../../UI-components/Field';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { MediaInput } from '../../UI-components/MediaInput';
import { CalendarIcon } from '../../../utils/icons';

interface filterObject {
    year?: string,
    month?: string,
    day?: string,
    author?: string,
    publisher?: string,
    rowsPerPage: number;
    pageNumber: number;
}

const MagazineFilters = ({
    isOpen,
    setIsOpen,
    filters,
    setFilters,
}: {
    isOpen: boolean;
    setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
    filters: filterObject;
    setFilters: React.Dispatch<React.SetStateAction<filterObject>>;
}) => {
    const close = () => setIsOpen(false);
    let date = (filters.year && filters.month && filters.day) ? new Date(`${filters.year}/${filters.month}/${filters.day}`) : null;

    const [startDate, setStartDate] = useState<any>(date?date:null);
    const [author, setAuthor] = useState(filters?.author || '');
    const [publisher, setPublisher] = useState(filters?.publisher || '');
    const handleResetFilter = () => {
        setFilters((prev: filterObject) => ({
            ...prev,
            year: '',
            month: '',
            day: '',
            author: '',
            publisher: '',
        }));
    }

    const applyFilters = () => {
        setFilters((prev: filterObject) => ({
            ...prev,
            year: `${startDate?.getFullYear() ? startDate?.getFullYear() : ''}`,
            month: `${startDate?.getMonth() ? startDate?.getMonth() + 1 : ''}`,
            day: `${startDate?.getDate() ? startDate?.getDate() : ''}`,
            author: author,
            publisher: publisher,
            pageNumber: 1
        }));
    };

    return (
        <Row>
            <Col textAlign="center">
                <DrawerModal isOpen={isOpen} onClose={close}>
                    <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
                    <DrawerModal.Body>
                        <Row>
                            <Col size={12} mt={'sm'}>
                                <Label>Author</Label>
                                <Input placeholder='Author Name' value={author} onChange={(e) => setAuthor(e.target.value)} />
                            </Col>
                        </Row>
                        <Row>
                            <Col size={12} mt={'sm'}>
                                <Label>Publisher</Label>
                                <Input placeholder='Publisher Name' value={publisher} onChange={(e) => setPublisher(e.target.value)} />
                            </Col>
                        </Row>
                        <Row>
                            <Col size={12} mt={'sm'}>
                                <Label>Publish Date</Label>
                                <Datepicker value={startDate} onChange={setStartDate}>
                                    {/* <Input /> */}
                                    <MediaInput 
                                        end={<CalendarIcon />}
                                        />
                                </Datepicker>
                            </Col>
                        </Row>
                    </DrawerModal.Body>
                    <DrawerModal.Footer>
                        <DrawerModal.FooterItem>
                            <Button isDanger isPrimary onClick={handleResetFilter}>
                                Reset Filters
                            </Button>
                        </DrawerModal.FooterItem>
                        <DrawerModal.FooterItem>
                            <Button isPrimary onClick={applyFilters}>
                                Apply Filters
                            </Button>
                        </DrawerModal.FooterItem>
                    </DrawerModal.Footer>
                    <DrawerModal.Close />
                </DrawerModal>
            </Col>
        </Row>
    );
};

export default MagazineFilters;
