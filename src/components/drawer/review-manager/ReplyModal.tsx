import {
    <PERSON>er,
    Body,
    Close,
    Footer,
    FooterItem,
} from '@zendeskgarden/react-modals';
import { LG } from '../../UI-components/Typography';
import { Label } from '../../UI-components/Label';
import { Row, Col } from '../../UI-components/Grid';
import { Modal } from '../../UI-components/Modal';
import { Button } from '../../UI-components/Button';
import { useRef, useState } from 'react';
import useToast from '../../../hooks/useToast';
import { Message, Textarea } from '@zendeskgarden/react-forms';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import krakendPaths, { KrakendTokens } from '../../../constants/krakendPaths';

interface ReplyInterface {
    created_at: string;
    id: number;
    is_visible: boolean;
    reply_text: string;
    updated_at: string;
    notify_customer?: boolean;
}

interface ReplyModalProps {
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    id: number;
    review: string;
    reply: string;
    isVisible?: boolean;
    replies: ReplyInterface[];
    refetch: () => void;
}

const ReplyModal = (props: ReplyModalProps) => {
    const addToast = useToast();
    const queryClient = useQueryClient();
    const axios = useAxios();
    const modalRef = useRef(null);
    const replies = Array.isArray(props.replies) ? props.replies : [];
    const replyId = replies[replies.length - 1]?.id;
    const replyMessage = replies[replies.length - 1]?.reply_text;
    const { review, isVisible, setVisible, visible, refetch } = props

    const [editReview, setEditReview] = useState({
        review: review || '',
        reply: replyMessage || '',
        isVisible: isVisible || false,
        notify: replies[replies.length - 1]?.notify_customer || false,
        is_replay_visible: replies[replies.length - 1]?.is_visible,
    });

    const [errors, setErrors] = useState<{
        review: string;
        reply: string;
        [key: string]: string;
    }>({
        review: '',
        reply: '',
    });

    const handleCloseModal = () => {
        setVisible(false);
    };

    const handleModalClick = (event: any) => {
        event.stopPropagation();
    };

    const validateForm = () => {
        const newErrors = {
            review: '',
            reply: '',
        };

        if (!editReview.review.trim()) {
            newErrors.review = 'Review is required.';
        }

        if (!editReview.reply.trim()) {
            newErrors.reply = 'reply is required.';
        }

        setErrors(newErrors);

        return Object.values(newErrors).every((x) => x === '');
    };

    const handleColumnValueChange = (event: any, field: string) => {
        if (errors[field] && event.target.value.trim() !== '') {
            setErrors((prev) => ({ ...prev, [field]: '' }));
        }
        setEditReview((prev) => ({ ...prev, [field]: event.target.value }));
    };

    const handleCheckboxChange = (
        event: React.ChangeEvent<HTMLInputElement>,
        field: string,
    ) => {
        setEditReview((prev) => ({
            ...prev,
            [field]: event.target.checked,
        }));
    };

    const { mutate: addReply, isLoading: isAddReplyLoading } = useMutation(
        async (payload: {
            reply_text: string;
            is_visible: boolean;
            notify_customer: boolean;
        }) => {
            const { reply_text, is_visible, notify_customer } = payload;
            if (replyId) {
                const response = await axios.put(
                    `${krakendPaths.REVIEW_URL}/admin-api/v1/replies/${replyId}/`,
                    {
                        reply_text,
                        is_visible,
                        notify_customer,
                    },
                    {
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    },
                );
                return response.data;
            }

            const response = await axios.post(
                `${krakendPaths.REVIEW_URL}/admin-api/v1/reviews/${props.id}/reply`,
                {
                    reply_text,
                    is_visible,
                    notify_customer,
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                },
            );
            return response.data;
        },
        {
            onSuccess: () => {
                addToast('success', 'Reply added successfully');
                queryClient.invalidateQueries(['reviews']);
                setVisible(false);
                refetch();
            },
            onError: (error: any) => {
                addToast('error', error.message || 'Failed to add reply');
            },
        },
    );

    const handleSendClick = () => {
        if (validateForm()) {
            addReply({
                reply_text: editReview.reply,
                is_visible: editReview.is_replay_visible,
                notify_customer: editReview?.notify || false,
            });
        } else {
            addToast('warning', 'Please fill in all required fields.');
        }
    };

    return (
        <>
            {visible && (
                <Modal
                    onClose={handleCloseModal}
                    onClick={handleModalClick}
                    ref={modalRef}
                >
                    <Header>
                        <Row justifyContent="start" alignItems="center">
                            <LG hue="white">{replyId ? 'Edit Reply' : 'Reply'}</LG>
                        </Row>
                    </Header>
                    <Body>
                        <Row mt="md">
                            <Col size={12}>
                                <Label>Review</Label>
                                <Textarea
                                    placeholder="Enter Title"
                                    value={editReview.review}
                                    onChange={(event) => handleColumnValueChange(event, 'review')}
                                    validation={errors.review ? 'error' : undefined}
                                    minRows={4}
                                />
                                {errors.review && (
                                    <Message validation="error">{errors.review}</Message>
                                )}
                            </Col>
                        </Row>
                        <Row mt="md">
                            <Col size={12}>
                                <Label>Reply</Label>
                                <Textarea
                                    placeholder="Enter Reply"
                                    value={editReview.reply}
                                    onChange={(event) => handleColumnValueChange(event, 'reply')}
                                    validation={errors.reply ? 'error' : undefined}
                                    minRows={4}
                                />
                                {errors.reply && (
                                    <Message validation="error">{errors.reply}</Message>
                                )}
                            </Col>
                        </Row>
                        <Row mt="md">
                            <Col size={6}>
                                <Label>
                                    <input
                                        type="checkbox"
                                        checked={editReview?.notify}
                                        onChange={(event) => handleCheckboxChange(event, 'notify')}
                                    />
                                    <span> Notify Customer</span>
                                </Label>
                            </Col>
                            <Col size={6}>
                                <Label>
                                    <input
                                        type="checkbox"
                                        checked={editReview?.is_replay_visible}
                                        onChange={(event) =>
                                            handleCheckboxChange(event, 'is_replay_visible')
                                        }
                                    />
                                    <span> Visibility</span>
                                </Label>
                            </Col>
                        </Row>
                    </Body>
                    <Footer>
                        <FooterItem>
                            <Button
                                isPrimary
                                onClick={handleSendClick}
                                disabled={isAddReplyLoading}
                            >
                                {isAddReplyLoading ? 'Sending...' : 'Send'}
                            </Button>
                        </FooterItem>
                    </Footer>
                    <Close aria-label="Close modal" style={{ color: 'white' }} />
                </Modal>
            )}
        </>
    );
};

export default ReplyModal;
