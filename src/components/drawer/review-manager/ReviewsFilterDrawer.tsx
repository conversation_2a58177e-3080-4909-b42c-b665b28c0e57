import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Field as _Field } from '../../UI-components/Field';
import { Dropdown } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { ProductFilterTypes } from '../../../types/types';
import { Row, Col } from '../../UI-components/Grid';
import { Label } from '@zendeskgarden/react-forms';
import Input from '../../UI-components/Input';
import styled from 'styled-components';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { FiltersType } from '../../../pages/review-manager/Reviews';
import { baseTheme } from '../../../themes/theme';
import { format } from 'date-fns';
import { convertToSentenceCase } from '../../../utils/convertToSentenceCase';

export interface DropDownTypes {
  label: string;
  value: number;
}

const StyledInput = styled.input`
  width: 95%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #d4d4d4;
    box-shadow: 0 0 5px #d4d4d4;
  }
`;
export const DropDownItem: DropDownTypes[] = [
  { label: '1', value: 1 },
  { label: '2', value: 2 },
  { label: '3', value: 3 },
  { label: '4', value: 4 },
  { label: '5', value: 5 },
];
export const ReviewsFilterDrawer = ({
  isOpen,
  setIsOpen,
  setFilters,
  filters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setFilters: React.Dispatch<React.SetStateAction<FiltersType>>;
  filters: FiltersType;
  reset: any;
}) => {
  const close = () => setIsOpen(false);
  const [avgRatingFrom, setAvgRatingFrom] = useState<
    DropDownTypes | undefined
  >();
  const [avgRatingTo, setAvgRatingTo] = useState<DropDownTypes>();
  const [reviewCountTo, setReviewCountTo] = useState<number>();
  const [reviewCountFrom, setReviewCountFrom] = useState<number>();
  const [product_name, setProductName] = useState('');
  const [reviewer, setReviewer] = useState('');

  const [startDate, setStartDate] = useState<Date | undefined>(
    filters.from_admin_created_at
      ? new Date(filters.from_admin_created_at)
      : undefined,
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    filters.to_admin_created_at
      ? new Date(filters.to_admin_created_at)
      : undefined,
  );

  const [selectedStatus, setSelectedStatus] = useState<string | undefined>(
    undefined,
  );
  const [selectedType, setSelectedType] = useState<string | undefined>(
    undefined,
  );

  const statuses = ['reviewed', 'approved', 'disapproved', 'replied'];
  const types = ['Admin', 'Customer', 'Guest'];

  const dateFormatter = new Intl.DateTimeFormat('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  const applyFilters = () => {
    setFilters((prev: FiltersType) => {
      const updatedFilters: FiltersType = {
        ...prev,
      };

      if (startDate || endDate) {
        if (startDate) {
          updatedFilters.from_admin_created_at = format(
            startDate,
            'yyyy-MM-dd',
          );
        } else {
          updatedFilters.from_admin_created_at = '';
        }

        if (endDate) {
          updatedFilters.to_admin_created_at = format(endDate, 'yyyy-MM-dd');
        } else {
          updatedFilters.to_admin_created_at = format(new Date(), 'yyyy-MM-dd');
        }
      }

      if (selectedStatus) {
        updatedFilters.status = selectedStatus;
      }
      if (selectedType) {
        updatedFilters.created_by = selectedType;
      }
      if (product_name) {
        updatedFilters.product_name = product_name;
      }
      if (reviewer) {
        updatedFilters.search_by = reviewer;
      }

      updatedFilters.page = 1;

      console.log(updatedFilters);
      return updatedFilters;
    });

    setIsOpen(false);
  };

  const resetFilters = () => {
    setFilters({
      page: 1,
      created_by: '',
      product_name: '',
      search_by: '',
      page_size: 20,
    });
    setProductName('');
    setReviewer('');
    setStartDate(undefined);
    setEndDate(undefined);
    setSelectedStatus(undefined);
    setSelectedType(undefined);
  };
  useEffect(() => {
    resetFilters();
  }, [reset])

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mb='md'>
                <_Field>
                  <Label>Product Name</Label>
                  <Input
                    value={product_name}
                    onChange={(e) => setProductName(e.target.value)}
                  />
                </_Field>
              </Col>
              <Col size={12} mb='md'>
                <_Field>
                  <Label>Reviewer</Label>
                  <Input
                    value={reviewer}
                    placeholder='Customer name and Email'
                    onChange={(e) => setReviewer(e.target.value)}
                  />
                </_Field>
              </Col>
              <Col size={12} mb="md">
                <Label>Created By</Label>
                <div style={{ marginTop: '10px' }}>{ }</div>
                <Dropdown
                  selectedItem={selectedType}
                  onSelect={(item: any) => setSelectedType(item)}
                  downshiftProps={{
                    itemToString: (item: string) => item,
                  }}
                >
                  <DropDownField>
                    <Select>
                      {selectedType
                        ? convertToSentenceCase(selectedType)
                        : 'Select Type'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {types.map((option) => (
                      <Item key={option} value={option}>
                        {convertToSentenceCase(option)}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mb='md'>
                <Label>Status</Label>
                <div style={{ marginTop: '10px' }}>{ }</div>
                <Dropdown
                  selectedItem={selectedStatus}
                  onSelect={(item: any) => setSelectedStatus(item)}
                  downshiftProps={{
                    itemToString: (item: string) => item,
                  }}
                >
                  <DropDownField>
                    <Select>
                      {selectedStatus
                        ? convertToSentenceCase(selectedStatus)
                        : 'Select Status'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {statuses.map((option) => (
                      <Item key={option} value={option}>
                        {convertToSentenceCase(option)}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mb='md'>
                <Label>Reviewed At From</Label>
                <Datepicker
                  value={startDate}
                  onChange={setStartDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Reviewed At Date'}
                    fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
              <Col size={12} >
                <Label>Reviewed At To</Label>
                <Datepicker
                  value={endDate}
                  onChange={setEndDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Reviewed At Date'}
                    fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={resetFilters}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
