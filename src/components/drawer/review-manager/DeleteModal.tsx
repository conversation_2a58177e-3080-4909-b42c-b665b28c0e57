import {
    Header,
    Body,
    Close,
    Footer,
    FooterItem,
} from '@zendeskgarden/react-modals';
import { LG } from '../../UI-components/Typography';
import { Row } from '../../UI-components/Grid';
import { Modal } from '../../UI-components/Modal';
import { Button } from '../../UI-components/Button';
import { useRef } from 'react';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import krakendPaths from '../../../constants/krakendPaths';
import { ButtonWrapper, CloseWrapper } from './style';

interface DeleteReplyModalProps {
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    reviewId: number;
    replyId: number;
    refetch: () => void;
    onDeleteSuccess?: () => void;
}

const DeleteReplyModal = (props: DeleteReplyModalProps) => {
    const { refetch, reviewId, onDeleteSuccess, setVisible, visible } = props;
    const addToast = useToast();
    const axios = useAxios();
    const modalRef = useRef(null);

    const { mutate: deleteReply, isLoading: isDeleteReplyLoading } = useMutation(
        async () => {
            const response = await axios.delete(
                `${krakendPaths.REVIEW_URL}/admin-api/v1/reviews/${reviewId}/reply/`,
                {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
            return response.data;
        },
        {
            onSuccess: () => {
                addToast('success', 'Reply deleted successfully');
                onDeleteSuccess?.();
                setVisible(false);
                refetch();
            },
            onError: (error: Error) => {
                addToast('error', error.message || 'Failed to delete reply');
            },

        }
    );

    const handleCloseModal = () => {
        setVisible(false);
    };
    const handleDeleteConfirm = () => {
        deleteReply();
    };

    return (
        <>
            {visible && (
                <Modal
                    onClose={handleCloseModal}
                    ref={modalRef}
                >
                    <Header>
                        <Row justifyContent="start" alignItems="center">
                            <LG hue="white">Delete Reply</LG>
                        </Row>
                    </Header>
                    <Body>
                        <Row>
                            <LG>Are you sure you want to delete this reply?</LG>
                        </Row>
                    </Body>
                    <Footer>
                        <FooterItem>
                            <ButtonWrapper>
                                <Button
                                    onClick={handleDeleteConfirm}
                                    isPrimary
                                    disabled={isDeleteReplyLoading}

                                >
                                    {isDeleteReplyLoading ? 'Deleting...' : 'Yes'}
                                </Button>
                                <Button
                                    onClick={handleCloseModal}
                                    disabled={isDeleteReplyLoading}
                                >
                                    No
                                </Button>
                            </ButtonWrapper>
                        </FooterItem>
                    </Footer>
                    <CloseWrapper>
                        <Close aria-label="Close modal" />
                    </CloseWrapper>
                </Modal>
            )}
        </>
    );
};

export default DeleteReplyModal;