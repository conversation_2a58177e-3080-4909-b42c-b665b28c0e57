import React, { useEffect, useState } from 'react';

import { Col, Row } from '@zendeskgarden/react-grid';
import { DrawerModal, Footer } from '@zendeskgarden/react-modals';
import { XMD as _XMD } from '../../UI-components/Typography';
import styled from 'styled-components';
import { baseTheme } from '../../../themes/theme';
import { RefetchOptions, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import { Title, Well, Paragraph } from '@zendeskgarden/react-notifications';
import { Spinner } from '@zendeskgarden/react-loaders';
import NothingToshow from '../../UI-components/NothingToShow';
import { Tag as _Tag } from '../../UI-components/Tags';
import { convertToSentenceCase } from '../../../utils/convertToSentenceCase';
import { format } from 'date-fns';
import { Button } from '../../UI-components/Button';
import { Span } from '@zendeskgarden/react-typography';
import krakendPaths, { KrakendTokens } from '../../../constants/krakendPaths';

interface ReviewProductProps {
  product_name: string;
  product_id: string;
}
const XMD = styled(_XMD)`
  color: ${baseTheme.colors.fontBlack};
`;

const TitleHead = styled.div`
  justify-content: space-between;
  display: flex;
  align-items: center;
`;
const FooterButton = styled(Button)`
  background-color: ${baseTheme.colors.primaryHue};
  border: none;
  padding: 8px 16px;
  border-radius: ${baseTheme.borderRadii.xs};
  cursor: pointer;
`;

const StarButton = styled.button`
  background-color: ${baseTheme.colors.lightGreen};
  align-items: center;
  border: none;
  padding: 2px 8px;
  display: flex;
`;

const RatingSpan = styled(Span)`
  padding-right: ${baseTheme.paddings.sm};
  padding-left: ${baseTheme.paddings.base};
  color: white;
`;

const RatingContainer = styled.div`
  display: flex;
  align-items: center;
`;
const Nickname = styled.span`
  margin-left: 5px;
`;
const Status = styled.span<{ status: string }>`
  padding: 5px 10px;
  border-radius: 5px;
  color: ${(props) => {
    switch (props.status) {
      case 'approved':
        return baseTheme.colors.deepGreen;
      case 'reviewed':
        return baseTheme.colors.tagBlue;
      case 'disapproved':
        return baseTheme.colors.deepRed;
      default:
        return '';
    }
  }};
`;

const Tag = styled(_Tag)`
  padding: ${(p) => p.theme.space.sm};
`;
export interface FiltersType {
  page: number;
  page_size: number;
  status?: string;
  product_name?: string;
  created_by: string;
  search_by?: string;
  from_admin_created_at?: string;
  to_admin_created_at?: string;
}

export interface Review {
  id: number;
  customer_name: string;
  nickname: string;
  customer_email: string;
  item_name: string;
  rating: number;
  description: string;
  reviewed_at: string;
  status: string;
}
const ReviewsProductDrawer: React.FC<ReviewProductProps> = ({
  product_name,
  product_id,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [allReviews, setAllReviews] = useState<Review[]>([]);
  const [page, setPage] = useState(1);
  const open = () => {
    setIsOpen(true);
    // Reset state when opening for a new product id
    setAllReviews([]);
    setPage(1);
  };
  const close = () => {
    setIsOpen(false);
    setAllReviews([]);
    setPage(1);
  };
  const axios = useAxios();
  const addToast = useToast();

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['review-list-reviewed'],
      queryFn: async (): Promise<any> => {
        return await axios.get(`${krakendPaths.REVIEW_URL}/list-review`, {
          params: { product_id: product_id, page: page },
          headers: {
            Authorization: KrakendTokens.REVIEW,
          },
        });
      },
      onError: (err) => {
        console.log(err);
        addToast('error', 'Error Occured');
      },
      onSuccess: (data) => {
        // console.log("All Review data: ", allReviews)
        setAllReviews((prevReviews) => prevReviews.concat(data.results));
        // setAllReviews(data.results);
      },
      enabled: isOpen,
    });

  const fetchNextPage = async () => {
    await setPage((prevPage) => prevPage + 1);
    await refetch();
  };

  useEffect(() => {
    return () => {
      setAllReviews([]);
    };
  }, [product_id]);

  const Review = ({
    rating,
    iconColor,
  }: {
    rating: number;
    iconColor: string;
  }) => {
    return (
      <span>
        <StarButton>
          <RatingSpan>{rating}</RatingSpan>
          <svg
            style={{
              width: `${baseTheme.components.dimension.width.xsm}`,
              height: `${baseTheme.components.dimension.height.xsm}`,
            }}
            viewBox="0 0 24 24"
          >
            <path
              d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"
              fill={iconColor}
            />
          </svg>
        </StarButton>
      </span>
    );
  };

  return (
    <Row>
      <XMD onClick={open}>{product_name}</XMD>
      <DrawerModal isOpen={isOpen} onClose={close} style={{ width: '40%' }}>
        <DrawerModal.Header>All Reviews</DrawerModal.Header>
        {isLoading || isFetching ? (
          <Row alignItems="center" justifyContent="center">
            <Col textAlign="center">
              <Spinner size="64" />
            </Col>
          </Row>
        ) : (
          allReviews.length === 0 && (
            <Well style={{ height: '100vh' }}>
              <NothingToshow />
            </Well>
          )
        )}
        {isError && <div>Error fetching reviews</div>}
        {!isLoading && !isError && allReviews.length > 0 && (
          <>
            {allReviews.map((review) => (
              <Well
                key={review.id}
                style={{ borderBottom: '1px solid #e1e1e1' }}
              >
                <Title>
                  <TitleHead>
                    <RatingContainer>
                      <Review rating={review.rating} iconColor="#ffffff" />
                      <Nickname>{review.nickname}</Nickname>
                    </RatingContainer>
                    <Tag>
                      <Status status={review.status}>
                        {convertToSentenceCase(review.status)}
                      </Status>
                    </Tag>
                  </TitleHead>
                </Title>
                <Paragraph style={{ margin: ' 10px 0' }}>
                  {review.description}
                </Paragraph>
                <Paragraph style={{ justifyContent: 'left', display: 'flex' }}>
                  <DrawerModal.FooterItem
                    style={{ color: `${baseTheme.colors.veryDarkGray}` }}
                  >
                    {review.customer_name}
                  </DrawerModal.FooterItem>
                  <DrawerModal.FooterItem
                    style={{ color: `${baseTheme.colors.textColorGrey}` }}
                  >
                    {review.reviewed_at}
                  </DrawerModal.FooterItem>
                </Paragraph>
              </Well>
            ))}
          </>
        )}
        <DrawerModal.Footer
          style={{
            borderTop: `1px solid ${baseTheme.colors.lightGrey}`,
            justifyContent: 'center',
          }}
        >
          {allReviews.length > 0 && data && data.next ? (
            <>
              <DrawerModal.FooterItem>
                <FooterButton isPrimary onClick={fetchNextPage}>
                  {isFetching || isRefetching ? 'Fetching...' : 'Load More'}
                </FooterButton>
              </DrawerModal.FooterItem>
            </>
          ) : (
            <DrawerModal.FooterItem>{''}</DrawerModal.FooterItem>
          )}
        </DrawerModal.Footer>
        <DrawerModal.Close />
      </DrawerModal>
    </Row>
  );
};

export default ReviewsProductDrawer;
