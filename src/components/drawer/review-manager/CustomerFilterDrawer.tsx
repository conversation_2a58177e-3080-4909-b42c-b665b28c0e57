import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Field as _Field } from '../../UI-components/Field';
import { Dropdown } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';

import { convertToSentenceCase } from '../../../utils/convertToSentenceCase';

import { Row, Col } from '../../UI-components/Grid';
import { Label } from '@zendeskgarden/react-forms';
import Input from '../../UI-components/Input';
import styled from 'styled-components';
import { CustomerFilterTypes } from '../../../pages/review-manager/CustomerFilterContext';
export interface DropDownTypes {
  label: string;
  value: number;
}
const StyledInput = styled.input`
  width: 95%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #d4d4d4;
    box-shadow: 0 0 5px #d4d4d4;
  }
`;
export const DropDownItem: DropDownTypes[] = [
  { label: '1', value: 1 },
  { label: '2', value: 2 },
  { label: '3', value: 3 },
  { label: '4', value: 4 },
  { label: '5', value: 5 },
];
export const CustomerFilterDrawer = ({
  isOpen,
  setIsOpen,
  setFilters,
  filters,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setFilters: React.Dispatch<React.SetStateAction<CustomerFilterTypes>>;
  filters: CustomerFilterTypes;
}) => {
  const close = () => setIsOpen(false);

  // const [search_by, setSearchBy] = useState('');
  const [customer_id, setCustomerId] = useState('');
  const [avgRatingFrom, setAvgRatingFrom] = useState<
    DropDownTypes | undefined
  >();
  const [avgRatingTo, setAvgRatingTo] = useState<DropDownTypes>();
  // const [reviewCountTo, setReviewCountTo] = useState<DropDownTypes>();
  const [reviewCountTo, setReviewCountTo] = useState<number>();

  // const [reviewCountFrom, setReviewCountFrom] = useState<DropDownTypes>();
  const [reviewCountFrom, setReviewCountFrom] = useState<number>();

  const [selectedType, setSelectedType] = useState<string | undefined>(
    undefined,
  );
  const types = ['admin', 'guest', 'customer'];

  useEffect(() => {
    // if(search_by){
    //   filters.search_by= search_by;
    // }
    if (customer_id) {
      filters.customer_id = customer_id;
    }
    if (selectedType) {
      filters.created_by = selectedType;
    }
    if (filters.from_avg_rating) {
      const selected = DropDownItem.find(
        (item) => item.value == filters.from_avg_rating,
      );
      setAvgRatingFrom(selected);
    }
    if (filters.to_avg_rating) {
      const selected = DropDownItem.find(
        (item) => item.value == filters.to_avg_rating,
      );
      setAvgRatingTo(selected);
    }

    if (filters.from_review_count) {
      setReviewCountFrom(filters.from_review_count);
    }

    if (filters.to_review_count) {
      setReviewCountTo(filters.to_review_count);
    }
  }, [filters]);

  const applyFilters = () => {
    setFilters((prev: any) => ({
      ...prev,

      from_avg_rating: avgRatingFrom?.value,
      to_avg_rating: avgRatingTo?.value,
      // from_review_count: reviewCountFrom?.value,
      from_review_count: reviewCountFrom,
      to_review_count: reviewCountTo,
      page: 1,

      // to_review_count: reviewCountTo?.value,
    }));
    setIsOpen(false);
  };
  useEffect(() => {
    if (isOpen) {
      // setSearchBy(filters.search_by || '');
      setCustomerId(filters.customer_id || '');
      setSelectedType(filters.created_by || undefined);

      const avgRatingFromSelected = DropDownItem.find(
        (item) => item.value === filters.from_avg_rating,
      );
      setAvgRatingFrom(avgRatingFromSelected || undefined);

      const avgRatingToSelected = DropDownItem.find(
        (item) => item.value === filters.to_avg_rating,
      );
      setAvgRatingTo(avgRatingToSelected || undefined);

      setReviewCountFrom(filters.from_review_count || undefined);
      setReviewCountTo(filters.to_review_count || undefined);
    }
  }, [isOpen, filters]);

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mb="md">
                <_Field>
                  <Label>Customer Id</Label>
                  <Input
                    value={customer_id}
                    onChange={(e) => setCustomerId(e.target.value)}
                  />
                </_Field>
              </Col>
              <Col size={12}>
                <Dropdown
                  selectedItem={avgRatingFrom}
                  onSelect={(items) => {
                    const Selected: DropDownTypes | undefined =
                      DropDownItem.find((elem) => elem.value === items);

                    setAvgRatingFrom(Selected);
                  }}
                  downshiftProps={{
                    itemToString: (item: DropDownTypes) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Average Rating From </Label>
                    <Select>
                      {avgRatingFrom
                        ? avgRatingFrom.label
                        : 'Select Average Rating From'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {DropDownItem.map((option, index) => (
                      <Item key={index} value={option.value}>
                        {option.label}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="md">
                <Dropdown
                  selectedItem={avgRatingTo}
                  onSelect={(items) => {
                    const temp: DropDownTypes | undefined = DropDownItem.find(
                      (elem) => elem.value === items,
                    );

                    setAvgRatingTo(temp);
                  }}
                  downshiftProps={{
                    itemToString: (item: DropDownTypes) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Average Rating To </Label>
                    <Select>
                      {avgRatingTo
                        ? avgRatingTo.label
                        : 'Select Average Rating To'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {DropDownItem.map((option, index) => (
                      <>
                        <Item key={index} value={option.value}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="md">
                <Label>Review Count From</Label>
                <StyledInput
                  min={0}
                  type="number"
                  value={reviewCountFrom}
                  onChange={(e) => setReviewCountFrom(+e.target.value)}
                />
              </Col>
              <Col size={12} mt="md">
                <Label>Review Count To</Label>
                <StyledInput
                  min={0}
                  type="number"
                  value={reviewCountTo}
                  onChange={(e) => setReviewCountTo(+e.target.value)}
                />
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  setFilters({
                    page: 1,
                    page_size: 20,
                    created_by: '',
                  });
                  setCustomerId('');
                  setAvgRatingFrom(undefined);
                  setAvgRatingTo(undefined);
                  setReviewCountFrom(undefined);
                  setReviewCountTo(undefined);
                  setIsOpen(false);
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
