import React, { useEffect, useState } from 'react';

import { Col, Row } from '@zendeskgarden/react-grid';
import { DrawerModal, Footer } from '@zendeskgarden/react-modals';
import { XMD as _XMD } from '../../UI-components/Typography';
import styled from 'styled-components';
import { baseTheme } from '../../../themes/theme';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import { Title, Well, Paragraph } from '@zendeskgarden/react-notifications';
import { Spinner } from '@zendeskgarden/react-loaders';
import NothingToshow from '../../UI-components/NothingToShow';
import { Tag as _Tag } from '../../UI-components/Tags';
import { convertToSentenceCase } from '../../../utils/convertToSentenceCase';
import { Span } from '@zendeskgarden/react-typography';
import { Button } from '../../UI-components/Button';
import krakendPaths, { KrakendTokens } from '../../../constants/krakendPaths';

interface ReviewCustomerProps {
  customer_email: string;
  customer_id: number;
}
const XMD = styled(_XMD)`
  color: ${baseTheme.colors.fontBlack};
`;
interface ImageContainerProps {
  imageUrl: string;
}
const ImageContainer = styled.div<{ imageUrl: string }>`
  width: 80%;
  height: 0;
  padding-bottom: 60%; /* Adjust the padding as needed */
  background-image: ${(props) => `url(${props.imageUrl})`};
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
`;

const FooterButton = styled(Button)`
  background-color: ${baseTheme.colors.primaryHue};
  border: none;
  padding: 8px 16px;
  border-radius: ${baseTheme.borderRadii.xs};
  cursor: pointer;
`;

const StarButton = styled.button`
  background-color: ${baseTheme.colors.lightGreen};
  align-items: center;
  border: none;
  padding: 2px 8px;
  display: flex;
`;

const RatingSpan = styled(Span)`
  padding-right: ${baseTheme.paddings.sm};
  padding-left: ${baseTheme.paddings.base};
  color: white;
`;

const TitleHead = styled.div`
  justify-content: space-between;
  display: flex;
  align-items: center;
`;

const RatingContainer = styled.div`
  display: flex;
  align-items: center;
`;
const Nickname = styled(Span)`
  margin-left: ${baseTheme.paddings.lg};
`;
const Status = styled(Span)<{ status: string }>`
  padding: 5px 10px;
  border-radius: ${baseTheme.borderRadii.sm};
  color: ${(props) => {
    switch (props.status) {
      case 'approved':
        return baseTheme.colors.deepGreen;
      case 'reviewed':
        return baseTheme.colors.tagBlue;
      case 'disapproved':
        return baseTheme.colors.deepRed;
      default:
        return '';
    }
  }};
`;

const ItemSpan = styled(Span)`
  font-size: small;
  margin: ${baseTheme.paddings.md} 0;
`;
const DescripSpan = styled(Span)`
  margin: ${baseTheme.paddings.md} 0;
`;
const BottomPara = styled(Paragraph)`
  justify-content: left;
  display: flex;
  margin-top: ${baseTheme.paddings.lg};
`;
const Tag = styled(_Tag)`
  padding: ${(p) => p.theme.space.sm};
`;
export interface FiltersType {
  page: number;
  page_size: number;
  status?: string;
  product_name?: string;
  created_by?: string;
  search_by?: string;
  from_admin_created_at?: string;
  to_admin_created_at?: string;
}

export interface Review {
  item_image: any;
  review: any;
  id: number;
  customer_name: string;
  nickname: string;
  customer_email: string;
  item_name: string;
  rating: number;
  description: string;
  reviewed_at: string;
  status: string;
}
const ReviewsCustomerDrawer: React.FC<ReviewCustomerProps> = ({
  customer_email,
  customer_id,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [allReviews, setAllReviews] = useState<Review[]>([]);
  const [page, setPage] = useState(1);
  const axios = useAxios();
  const addToast = useToast();

  const open = () => setIsOpen(true);
  const close = () => {
    setIsOpen(false);
    setAllReviews([]);
    setPage(1);
  };

  const { isLoading, isError, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['review-list-reviewed', customer_id],
      queryFn: async (): Promise<any> => {
        return await axios.get(`${krakendPaths.REVIEW_URL}/list-review`, {
          params: { customer_id: customer_id, page: page },
          headers: {
            Authorization: KrakendTokens.REVIEW,
          },
        });
      },
      onError: (err) => {
        console.log(err);
        addToast('error', 'Error Occurred');
      },
      onSuccess: (data) => {
        setAllReviews((prevReviews) => prevReviews.concat(data.results));
      },
      enabled: isOpen,
    });

  const fetchNextPage = async () => {
    await setPage((prevPage) => prevPage + 1);
    await refetch();
  };

  useEffect(() => {
    return () => {
      setAllReviews([]);
    };
  }, [customer_id]);

  const Review = ({
    rating,
    iconColor,
  }: {
    rating: number;
    iconColor: string;
  }) => {
    return (
      <>
        <StarButton>
          <RatingSpan>{rating}</RatingSpan>

          <svg
            style={{
              width: `${baseTheme.components.dimension.width.xsm}`,
              height: `${baseTheme.components.dimension.height.xsm}`,
            }}
            viewBox="0 0 24 24"
          >
            <path
              d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"
              fill={iconColor}
            />
          </svg>
        </StarButton>
      </>
    );
  };

  return (
    <Row>
      <XMD onClick={open}>{customer_email}</XMD>
      <DrawerModal isOpen={isOpen} onClose={close} style={{ width: '40%' }}>
        <DrawerModal.Header>All Reviews</DrawerModal.Header>
        {isLoading ? (
          <Row>
            <Col textAlign="center">
              <Spinner size="64" />
            </Col>
          </Row>
        ) : (
          allReviews.length === 0 && (
            <Well style={{ height: '100vh' }}>
              <NothingToshow />
            </Well>
          )
        )}
        {isError && <div>Error fetching reviews</div>}

        {!isLoading && !isError && allReviews.length > 0 && (
          <>
            {allReviews.map((review) => (
              <Well
                key={review.id}
                style={{ borderBottom: '1px solid #e1e1e1' }}
              >
                <Title
                  style={{ marginBottom: `calc(${baseTheme.paddings.sm}*5)` }}
                >
                  <TitleHead>
                    <RatingContainer>
                      <Review rating={review.rating} iconColor="#ffffff" />
                      <Nickname>{review.nickname}</Nickname>
                    </RatingContainer>
                    <Tag>
                      <Status isBold status={review.status}>
                        {convertToSentenceCase(review.status)}
                      </Status>
                    </Tag>
                  </TitleHead>
                </Title>
                <Row>
                  <Col size={4}>
                    <ImageContainer imageUrl={review.item_image} />
                    <ItemSpan>{review.item_name}</ItemSpan>
                  </Col>
                  <Col>
                    <DescripSpan>{review.description}</DescripSpan>
                  </Col>
                </Row>
                <BottomPara>
                  <DrawerModal.FooterItem
                    style={{ color: `${baseTheme.colors.veryDarkGray}` }}
                  >
                    {review.customer_name}
                  </DrawerModal.FooterItem>
                  <DrawerModal.FooterItem
                    style={{ color: `${baseTheme.colors.textColorGrey}` }}
                  >
                    {review.reviewed_at}
                  </DrawerModal.FooterItem>
                </BottomPara>
              </Well>
            ))}
          </>
        )}
        <DrawerModal.Footer
          style={{
            borderTop: `1px solid ${baseTheme.colors.lightGrey}`,
            justifyContent: 'center',
          }}
        >
          {allReviews.length > 0 && data && data.next ? (
            <>
              <DrawerModal.FooterItem>
                <FooterButton isPrimary onClick={fetchNextPage}>
                  {isFetching || isRefetching ? 'Fetching...' : 'Load More'}
                </FooterButton>
              </DrawerModal.FooterItem>
            </>
          ) : (
            <DrawerModal.FooterItem>{''}</DrawerModal.FooterItem>
          )}
        </DrawerModal.Footer>

        <DrawerModal.Close />
      </DrawerModal>
    </Row>
  );
};

export default ReviewsCustomerDrawer;
