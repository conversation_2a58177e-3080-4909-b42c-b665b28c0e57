import React, { useState } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { Dropdown } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { Field as DTField, Input as DTInput } from '@zendeskgarden/react-forms';
import { Datepicker } from '@zendeskgarden/react-datepickers';

export interface IItem {
  label: string;
  value: string;
}

function formatDate(date: any) {
  const originalDate = new Date(date);
  const year = originalDate.getFullYear();
  const month = (originalDate.getMonth() + 1).toString().padStart(2, '0');
  const day = originalDate.getDate().toString().padStart(2, '0');
  const formattedDate = `${year}-${month}-${day}`;
  return formattedDate;
}

export const promotionTypeList: IItem[] = [
  { label: 'Amount Promotion', value: 'amount_promotion' },
  { label: 'Item Promotion', value: 'item_promotion' },
];

export const statusList: IItem[] = [
  { label: 'Active', value: 'active' },
  { label: 'Expired', value: 'expired' },
];

const CartPromotionFilters = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  searchQuery,
  setSearchQuery,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: any;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  searchQuery: string | undefined;
  setSearchQuery: React.Dispatch<React.SetStateAction<string | undefined>>;
}) => {
  const close = () => setIsOpen(false);
  const [promotionType, setPromotionType] = useState<IItem | undefined>(() => {
    return promotionTypeList.find(
      (type) => type.value === filters.promotion_type,
    );
  });
  const [status, setStatus] = useState<IItem | undefined>(() => {
    return statusList.find((stat) => stat.value === String(filters.status));
  });
  const [startDate, setStartDate] = useState<Date | undefined>(
    filters?.start_date ? new Date(filters?.start_date) : undefined,
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    filters?.end_date ? new Date(filters?.end_date) : undefined,
  );

  const applyFilters = () => {
    setFilters((prev: any) => ({
      ...prev,
      promotion_type: promotionType?.value,
      status: status?.value,
      start_date: startDate ? formatDate(startDate) : '',
      end_date: endDate ? formatDate(endDate) : '',
    }));
    setIsOpen(false);
  };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filter</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12}>
                <Dropdown
                  selectedItem={promotionType}
                  onSelect={(items) => setPromotionType(items)}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Promotion Type</Label>
                    <Select>
                      {promotionType?.label
                        ? promotionType?.label
                        : 'Select Promotion Type'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {promotionTypeList.map((option, index) => (
                      <Item key={index} value={option}>
                        {option.label}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt={'md'}>
                <Dropdown
                  selectedItem={status}
                  onSelect={(items) => setStatus(items)}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Status</Label>
                    <Select>
                      {status?.label ? status?.label : 'Select Status'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {statusList.map((option, index) => (
                      <Item key={index} value={option}>
                        {option.label}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt={'md'}>
                <DTField>
                  <Label>Start Date</Label>
                  <Datepicker
                    placement="bottom"
                    value={startDate}
                    onChange={setStartDate}
                  >
                    <DTInput placeholder="2024-02-26" />
                  </Datepicker>
                </DTField>
              </Col>
              <Col size={12} mt={'md'}>
                <DTField>
                  <Label>End Date</Label>
                  <Datepicker value={endDate} onChange={setEndDate}>
                    <DTInput placeholder="2024-02-26" />
                  </Datepicker>
                </DTField>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  setPromotionType(undefined);
                  setStatus(undefined);
                  setStartDate(undefined);
                  setEndDate(undefined);
                  setSearchQuery(undefined);
                  setFilters({
                    page: 0,
                    size: 20,
                  });
                  setIsOpen(false);
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default CartPromotionFilters;
