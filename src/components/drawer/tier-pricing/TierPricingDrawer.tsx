import React, { useState, useRef, useEffect } from 'react';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { Span as _Span, Span as ZSpan } from '@zendeskgarden/react-typography';
import {
  Item,
  Menu,
  Label,
  Field,
  Dropdown,
  Autocomplete,
  Select,
} from '@zendeskgarden/react-dropdowns';
import { Body, Cell, Row as TRow, Table } from '@zendeskgarden/react-tables';
import {
  Head,
  HeaderRow,
  HeaderCell as _HeaderCell,
} from '../../UI-components/Table';
import styled from 'styled-components';
import {
  CrossIcon,
  DeleteIcon,
  EditPenIcon,
  SaveIcon,
} from '../../../utils/icons';
import { PlusIcon } from 'lucide-react';
import { IconButton } from '../../UI-components/IconButton';
import Input from '../../UI-components/Input';
import { Button } from '../../UI-components/Button';
import { SM, Span } from '../../layouts/tier-pricing/TierPricingLayout';
import { useTierPricingContext } from '../../../pages/tier-pricing/TierPricingContext';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxiosTierPrice';
import useToast from '../../../hooks/useToast';
// import { selectedTempTierResultData as selectedResults } from '../../../pages/tier-pricing/selectedTierPriceData';
import routes from '../../../constants/routes';
import { baseTheme } from '../../../themes/theme';

const HeaderCell = styled(_HeaderCell)`
  border: none;
  font-size: ${baseTheme.space.one * 14}px;
  font-weight: 400;
`;
const options = [
  'NOT LOGGED IN',
  'General',
  'In House Team',
  'Group1',
  'Gold',
  'All groups',
];

interface ITierPrice {
  price?: number;
  price_type?: string;
  website_id?: number;
  sku?: string;
  customer_group?: string;
  quantity?: number;
}

interface IIUpdateTier {
  customeGroup: string;
  qty: number;
  tierRule: string;
  price: number;
}

const option = ['Fixed', 'Discount'];
export const TierPricingDrawer = ({
  isOpen,
  setIsOpen,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const close = () => setIsOpen(false);

  const axios = useAxios();
  const addToast = useToast();

  //For Add
  const [selectedGroup, setSelectedGroup] = useState<string[]>([]);
  const [tierRule, setTierRule] = useState<string[]>([]);
  const [quantity, setQuantity] = useState<number[]>([]);
  const [prices, setPrices] = useState<number[]>([]);
  // const [priceValue, setPriceValue] = useState<number[]>([]);
  //

  //For Update
  const [updateGroup, setUpdateGroup] = useState<string[]>([]);
  const [tierUpdateRule, setTierUpdateRule] = useState<string[]>([]);
  const [updateQty, setUpdateQty] = useState<number[]>([]);
  const [updatedPrice, setUpdatedPrice] = useState<number[]>([]);

  const { selectetedTierPrice, setSelectedTierPrice, selectedResults } =
    useTierPricingContext();

  const [editModeValues, setEditModeValues] = useState<string[]>([]);

  const [priceGroup, setPriceGroup] = useState<number>();

  const [matchingOptions, setMatchingOptions] = useState(options);
  const [matchingOption, setMatchingOption] = useState(option);

  const [tierPriceUpdateRequest, setTierPriceUpdateRequest] = useState<any[]>(
    [],
  );

  const [changeSelectedPrice, setChangeSelectedPrice] = useState<any[]>([]);

  const [addedTierPrice, setAddedTierPrice] = useState<IIUpdateTier[]>([]);

  const customerGroups: {
    [key: string]: string;
  } = {
    '3': 'NOT LOGGED IN',
    '1': 'General',
    '4': 'In House Team',
    '5': 'Group1',
    '6': 'Gold',
    '32000': 'All groups',
    '0': 'All groups',
  };

  useEffect(() => {
    if (selectedResults) {
      const item = selectedResults[0];
      const amountValue = item.price.minimalPrice
        ? item.price.minimalPrice.amount.value
        : item.price.regularPrice.amount.value;
      setPriceGroup(amountValue);
    }
  }, [selectedResults]);

  const findKeyByValue = (obj: any, value: string) => {
    return Object.keys(obj).find((key) => obj[key] === value);
  };

  const [editRowIndex, setEditRowIndex] = useState<number | null>(null);

  const toggleEditMode = (rowIndex: number, item?: any) => {
    if (editRowIndex === null) {
      setEditRowIndex(rowIndex);
      setUpdateGroup([
        ...updateGroup.slice(0, rowIndex),
        customerGroups[item.customer_group_id],
        ...updateGroup.slice(rowIndex + 1),
      ]);
      setUpdatedPrice([
        ...updatedPrice.slice(0, rowIndex),
        item.value,
        ...updatedPrice.slice(rowIndex + 1),
      ]);
      setUpdateQty([
        ...updateQty.slice(0, rowIndex),
        item.qty,
        ...updateQty.slice(rowIndex + 1),
      ]);
      setTierUpdateRule([
        ...tierUpdateRule.slice(0, rowIndex),
        item.percentage_value === null ? 'fixed' : 'discount',
        ...tierUpdateRule.slice(rowIndex + 1),
      ]);
    } else {
      setEditRowIndex(null);
      setUpdateGroup(Array(selectetedTierPrice.length).fill(''));
      setUpdateQty(Array(selectetedTierPrice.length).fill(0));
      setTierUpdateRule(Array(selectetedTierPrice.length).fill(''));
      setUpdatedPrice(Array(selectetedTierPrice.length).fill(0));
    }
  };

  const [rows, setRows] = useState<any>([]);
  const addRow = () => {
    setRows([...rows, {}]);
    setPrices([...prices, 0]);
    setTierRule([...tierRule, '']);
    setSelectedGroup([...selectedGroup, '']);
    setQuantity([...quantity, 0]);
    // setPriceValue([...priceValue, 0]);
  };

  const removeRow = (indexToRemove: any) => {
    setPrices(prices.filter((_, index: any) => index !== indexToRemove));
    setTierRule(tierRule.filter((_, index: any) => index !== indexToRemove));
    setSelectedGroup(
      selectedGroup.filter((_, index: any) => index !== indexToRemove),
    );
    setQuantity(quantity.filter((_, index) => index !== indexToRemove));
    setRows(rows.filter((_: any, index: any) => index !== indexToRemove));
  };

  const createTierRequestArray = (
    productArray: any[],
    tierPriceArray: any[],
  ) => {
    const resultArray: ITierPrice[] = productArray.reduce(
      (acc: any, product: any) => {
        const tierPriceObjects = tierPriceArray.map((tierPrice: any) => ({
          price: tierPrice.value,
          price_type:
            tierPrice.percentage_value === null
              ? 'FIXED'.toLowerCase()
              : 'DISCOUNT'.toLowerCase(),
          website_id: 0,
          sku: product.sku,
          customer_group: customerGroups[tierPrice.customer_group_id],
          quantity: tierPrice.qty,
        }));

        return [...acc, ...tierPriceObjects];
      },
      [],
    );
    return resultArray;
  };

  useEffect(() => {
    if (selectetedTierPrice && selectedResults) {
      const resultArray: ITierPrice[] = createTierRequestArray(
        selectedResults,
        selectetedTierPrice,
      );
      setTierPriceUpdateRequest(resultArray);
      setUpdateGroup(Array(selectetedTierPrice.length).fill(''));
      setUpdateQty(Array(selectetedTierPrice.length).fill(0));
      setEditModeValues(Array(selectetedTierPrice.length).fill(''));
      setTierUpdateRule(Array(selectetedTierPrice.length).fill(''));
      setUpdatedPrice(Array(selectetedTierPrice.length).fill(0));
      setChangeSelectedPrice(selectetedTierPrice);
    }
  }, [selectetedTierPrice, selectedResults]);

  const { mutate: addTierPrice, isLoading } = useMutation(
    async (tierRequest: any) => {
      const response = await axios.post(
        `${routes.FORWARDERS}/add-group-tier`,
        {
          ...tierRequest,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        console.log('Error Adding tier Price', err);
        addToast(
          'error',
          err.message ? err.message : 'Error occured in creating tierPrice',
        );
      },
      onSuccess: (data) => {
        if (Array.isArray(data) && data.length === 0) {
          addToast('success', 'Rule Added successfully');
          selectetedTierPrice &&
            setSelectedTierPrice([...selectetedTierPrice, ...addedTierPrice]);
          setPrices([]);
          setTierRule([]);
          setSelectedGroup([]);
          setQuantity([]);
        } else {
          addToast('error', 'Error occured in creating tierPrice');
        }
        setRows([]);
      },
    },
  );

  const { mutate: updateTierPrice, isLoading: isUpdating } = useMutation(
    async (tierRequest: any) => {
      const response = await axios.put(
        `${routes.FORWARDERS}/update-group-tier`,
        {
          ...tierRequest,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        addToast('error', `Error occured in creating tierPrice`);
      },
      onSuccess: (data) => {
        // console.log('Data', data);
        if (Array.isArray(data) && data.length == 0) {
          addToast('success', 'Rule Updated successfully');
          setSelectedTierPrice(changeSelectedPrice);
        } else {
          addToast('info', 'No Changes occured in tierPrice');
        }
        editRowIndex && handleCancelEdit(editRowIndex);
      },
    },
  );

  const { mutate: deleteTierPrice, isLoading: isDeleting } = useMutation(
    async (tierRequest: any) => {
      const response = await axios.post(
        `${routes.FORWARDERS}/delete-group-tier`,
        {
          ...tierRequest,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    {
      onError: (err) => {
        addToast('error', 'Error occured in removing tierPrice');
      },
      onSuccess: (data) => {
        // console.log(data);
        if (Array.isArray(data) && data.length === 0) {
          addToast('success', 'Rule Removed successfully');
        } else {
          addToast('error', 'Error occured in removing tierPrice');
        }
      },
    },
  );
  useEffect(() => {
    setEditRowIndex(null);
  }, [isOpen]);

  // useEffect(() => {
  //   prices && console.log('Price array', prices);
  //   tierRule && console.log('Tier Rule', tierRule);
  //   selectedGroup && console.log('Selcted group', selectedGroup);
  //   quantity && console.log('Quantity', quantity);
  // }, [prices, tierRule, selectedGroup, quantity]);

  // useEffect(() => {
  //   updatedPrice && console.log('Update Price array', updatedPrice);
  //   tierUpdateRule && console.log('Update Tier Rule', tierUpdateRule);
  //   updateGroup && console.log('Update Selcted group', updateGroup);
  //   updateQty && console.log('Update Quantity', updateQty);
  // }, [updatedPrice, tierUpdateRule, updateGroup, updateQty]);

  const handleAdd = () => {
    if (prices.some((val) => val <= 0 || isNaN(val))) {
      addToast('error', 'Invalid Price value');
      throw new Error('Price cannot be zero or string.');
    }

    if (quantity.some((val) => val <= 0 || isNaN(val))) {
      addToast('error', 'Invalid quantity value');
      throw new Error('Quality cannot be zero or string.');
    }

    if (selectedGroup.some((val) => val === '')) {
      addToast('error', 'Customer group is not selected');
      throw new Error('Selected group cannot be an empty string.');
    }

    if (tierRule.some((val) => val === '')) {
      addToast('error', 'Tier rule is not selected');
      throw new Error('Tier rule cannot be an empty string.');
    }

    tierRule.map((item: string, index) => {
      if (item.toLowerCase() == 'discount') {
        if (prices[index] > 100) {
          addToast(
            'error',
            'Invalid Price value, must be lesser than 100 if tier rule is Discount',
          );
          throw new Error('Price must be lesser than 100.');
        }
      }
    });

    if (
      !prices ||
      !quantity ||
      !selectedGroup ||
      !tierRule ||
      prices.length !== quantity.length ||
      prices.length !== selectedGroup.length ||
      prices.length !== tierRule.length
    ) {
      addToast('error', 'Invalid input');
      throw new Error('Invalid input data.');
    }

    const toBeAddedArray: IIUpdateTier[] = rows.map(
      (_: any, index: number) => ({
        qty: quantity[index],
        customer_group_id: findKeyByValue(customerGroups, selectedGroup[index]),
        percentage_value:
          tierRule[index].toLowerCase() === 'discount' ? prices[index] : null,
        value: prices[index],
      }),
    );

    // console.log('To be added array', toBeAddedArray);

    setAddedTierPrice(toBeAddedArray);

    const tierPriceArray: ITierPrice[] = selectedResults?.reduce(
      (acc: any, product: any) => {
        const tierPriceObject = rows.map((_: any, index: number) => ({
          price: prices[index],
          price_type: tierRule[index].toLowerCase(),
          website_id: 0, //change
          sku: product.sku,
          customer_group: selectedGroup[index],
          quantity: quantity[index],
        }));

        return [...acc, ...tierPriceObject];
      },
      [],
    );

    const tierPriceMainRequest = {
      prices: [...tierPriceArray],
    };

    const mainRequest = {
      base_price: priceGroup,
      data: tierPriceMainRequest,
    };

    addTierPrice(mainRequest);
  };

  const handleChange = (
    prop: string,
    value: any,
    index: number,
    price?: number,
  ) => {
    const updatedTierPrices: any[] = changeSelectedPrice.map(
      (item: any, i: number) => {
        if (i === index) {
          const clonedItem = { ...item };

          if (prop === 'percentage_value') {
            clonedItem[prop] =
              value.toLowerCase() === 'fixed' ? null : updatedPrice[index];
          } else if (prop === 'customer_group_id') {
            clonedItem[prop] = findKeyByValue(customerGroups, value);
          } else {
            clonedItem[prop] = value;
          }

          return clonedItem;
        }
        return item;
      },
    );
    setChangeSelectedPrice(updatedTierPrices);

    setTierPriceUpdateRequest(updatedTierPrices);
  };

  const handleCancelEdit = (index: number) => {
    toggleEditMode(index);
  };

  const handleUpdate = (index: number) => {
    // console.log('Tier updated request', tierPriceUpdateRequest);

    if (updatedPrice[index] === 0 || isNaN(updatedPrice[index])) {
      addToast('error', 'Invalid Price value');
      throw new Error('Price cannot be zero.');
    }

    if (updateQty[index] === 0 || isNaN(updateQty[index])) {
      addToast('error', 'Invalid Quantity value');
      throw new Error('Quality cannot be zero.');
    }

    if (updateGroup[index] === '') {
      addToast('error', 'Customer group is not selected');
      throw new Error('Selected group cannot be an empty string.');
    }

    if (tierUpdateRule[index] === '') {
      addToast('error', 'Tier rule is not selected');
      throw new Error('Tier rule cannot be an empty string.');
    }

    if (tierUpdateRule[index].toLowerCase() === 'discount') {
      if (updatedPrice[index] > 100) {
        addToast(
          'error',
          'Price percentage cannot be greater than 100 if tier rule is discount',
        );
        throw new Error('Price percentage cannot be greater than 100.');
      }
    }

    if (
      !updatedPrice ||
      !updateQty ||
      !updateGroup ||
      !tierUpdateRule ||
      updatedPrice.length !== updateQty.length ||
      updatedPrice.length !== updateGroup.length ||
      updatedPrice.length !== tierUpdateRule.length
    ) {
      addToast('error', 'Invalid input');
      throw new Error('Invalid input data.');
    }

    if (selectedResults) {
      const resultArray: ITierPrice[] = createTierRequestArray(
        selectedResults,
        tierPriceUpdateRequest,
      );

      const tierPriceUpdateMainRequest = {
        prices: [...resultArray],
      };

      const mainRequest = {
        base_price: priceGroup,
        data: tierPriceUpdateMainRequest,
      };

      updateTierPrice(mainRequest);
    }
  };

  const handleDelete = (index: number) => {
    const tierPriceDeleteArray = [selectetedTierPrice[index]];

    if (selectedResults) {
      const resultArray: ITierPrice[] = createTierRequestArray(
        selectedResults,
        tierPriceDeleteArray,
      );

      const tierPriceUpdateMainRequest = {
        prices: [...resultArray],
      };
      deleteTierPrice(tierPriceUpdateMainRequest);
    }
  };

  //Second Flow

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal
          style={{ minWidth: '600px' }}
          isOpen={isOpen}
          onClose={close}
        >
          <DrawerModal.Header>
            <Row>
              <Span>Price Group - {priceGroup}</Span>
            </Row>
            <Row>
              <Span>Number of Child Products - {selectedResults?.length}</Span>
            </Row>
          </DrawerModal.Header>
          <DrawerModal.Body style={{ padding: '0px' }}>
            <div style={{ overflowX: 'auto' }}>
              <Table>
                <Head>
                  <HeaderRow
                    style={{
                      background: '#205375',
                      boxShadow: '0px 4px 20px 0px rgba(0, 0, 0, 0.30)',
                    }}
                  >
                    <HeaderCell width="32%">Customer Group</HeaderCell>
                    <HeaderCell>Quantity</HeaderCell>
                    <HeaderCell width="25%">Tier Rule</HeaderCell>
                    <HeaderCell>Price</HeaderCell>
                    <HeaderCell></HeaderCell>
                  </HeaderRow>
                </Head>
                <Body>
                  {selectetedTierPrice &&
                    selectetedTierPrice.map((item: any, index: number) => {
                      return (
                        <>
                          {editRowIndex === index ? (
                            <>
                              <TRow>
                                <Cell>
                                  <Row
                                    justifyContent="center"
                                    alignItems="center"
                                  >
                                    <Col size={2}>
                                      <SaveIcon
                                        style={{
                                          marginTop: `${
                                            baseTheme.space.one * 2
                                          }px`,
                                          cursor: 'pointer',
                                          height: baseTheme.iconSizes.lg,
                                          width: 'auto',
                                        }}
                                        onClick={() => handleUpdate(index)}
                                      />
                                    </Col>
                                    <Col size={10}>
                                      <Dropdown
                                        selectedItem={updateGroup[index]}
                                        onSelect={(item) => {
                                          setUpdateGroup([
                                            ...updateGroup.slice(0, index),
                                            item,
                                            ...updateGroup.slice(index + 1),
                                          ]);
                                          handleChange(
                                            'customer_group_id',
                                            item,
                                            index,
                                          );
                                        }}
                                        downshiftProps={{
                                          defaultHighlightedIndex: 0,
                                        }}
                                      >
                                        <Field>
                                          <Label hidden></Label>
                                          <Select isCompact>
                                            {updateGroup[index]}
                                          </Select>
                                        </Field>
                                        <Menu>
                                          {matchingOptions.length ? (
                                            matchingOptions.map((option) => (
                                              <Item
                                                style={{
                                                  fontSize: `${
                                                    baseTheme.space.one * 10
                                                  }px`,
                                                  fontWeight: '400',
                                                  color:
                                                    baseTheme.colors.customOne,
                                                }}
                                                key={option}
                                                value={option}
                                                disabled={
                                                  option != 'All groups'
                                                }
                                              >
                                                <span>{option}</span>
                                              </Item>
                                            ))
                                          ) : (
                                            <Item disabled>
                                              No matches found
                                            </Item>
                                          )}
                                        </Menu>
                                      </Dropdown>
                                    </Col>
                                  </Row>
                                </Cell>
                                <Cell>
                                  <Input
                                    defaultValue={item.qty}
                                    onChange={(e) => {
                                      setUpdateQty([
                                        ...updateQty.slice(0, index),
                                        parseInt(e.target.value),
                                        ...updateQty.slice(index + 1),
                                      ]);
                                      handleChange(
                                        'qty',
                                        parseInt(e.target.value),
                                        index,
                                      );
                                    }}
                                    isCompact
                                  />
                                </Cell>
                                <Cell>
                                  <Dropdown
                                    selectedItem={tierUpdateRule[index]}
                                    onSelect={(item) => {
                                      setTierUpdateRule([
                                        ...tierUpdateRule.slice(0, index),
                                        item,
                                        ...tierUpdateRule.slice(index + 1),
                                      ]);
                                      handleChange(
                                        'percentage_value',
                                        item,
                                        index,
                                      );
                                    }}
                                    downshiftProps={{
                                      defaultHighlightedIndex: 0,
                                    }}
                                  >
                                    <Field>
                                      <Label hidden></Label>
                                      <Select isCompact>
                                        {tierUpdateRule[index]}
                                      </Select>
                                    </Field>
                                    <Menu>
                                      {matchingOption.length ? (
                                        matchingOption.map((option) => (
                                          <Item
                                            style={{
                                              fontSize: `${
                                                baseTheme.space.one * 10
                                              }px`,
                                              fontWeight: '400',
                                              color: baseTheme.colors.customOne,
                                            }}
                                            key={option}
                                            value={option}
                                          >
                                            <span>{option}</span>
                                          </Item>
                                        ))
                                      ) : (
                                        <Item disabled>No matches found</Item>
                                      )}
                                    </Menu>
                                  </Dropdown>
                                </Cell>
                                <Cell>
                                  <Input
                                    onChange={(e) => {
                                      setUpdatedPrice([
                                        ...updatedPrice.slice(0, index),
                                        parseInt(e.target.value),
                                        ...updatedPrice.slice(index + 1),
                                      ]);
                                      handleChange(
                                        'value',
                                        parseInt(e.target.value),
                                        index,
                                      );
                                    }}
                                    defaultValue={item.value}
                                    isCompact
                                  />
                                </Cell>
                                <Cell>
                                  <CrossIcon
                                    style={{
                                      marginTop: `${baseTheme.space.one * 5}px`,
                                      cursor: 'pointer',
                                    }}
                                    onClick={() => {
                                      handleCancelEdit(index);
                                    }}
                                  />
                                </Cell>
                              </TRow>
                            </>
                          ) : (
                            <>
                              <TRow>
                                <Cell isTruncated>
                                  <ZSpan
                                    style={{
                                      fontSize: `${
                                        baseTheme.customFontSizes.one * 15
                                      }px`,
                                    }}
                                  >
                                    <ZSpan.Icon>
                                      <EditPenIcon
                                        style={{
                                          marginRight: `${
                                            baseTheme.space.one * 10
                                          }px`,
                                        }}
                                        onClick={() =>
                                          toggleEditMode(index, item)
                                        }
                                      />
                                    </ZSpan.Icon>
                                    {
                                      customerGroups[
                                        selectetedTierPrice[index]
                                          .customer_group_id
                                      ]
                                    }
                                  </ZSpan>
                                </Cell>
                                <Cell isTruncated>
                                  <SM>{item.qty}</SM>
                                </Cell>
                                <Cell>
                                  <SM>
                                    {item.percentage_value != null
                                      ? 'DISCOUNT'
                                      : 'FIXED'}
                                  </SM>
                                </Cell>
                                <Cell isTruncated>
                                  <SM>{item.value}</SM>
                                </Cell>
                                <Cell isTruncated>
                                  <DeleteIcon
                                    style={{ cursor: 'pointer' }}
                                    onClick={() => {
                                      handleDelete(index);
                                    }}
                                  />
                                </Cell>
                              </TRow>
                            </>
                          )}
                        </>
                      );
                    })}
                </Body>
              </Table>
            </div>
            <Row mt="md" justifyContent="center">
              <Col size={2}>
                <IconButton
                  style={{
                    border: `1px solid ${baseTheme.colors.customOne}`,
                  }}
                  onClick={addRow}
                >
                  <PlusIcon />
                </IconButton>
              </Col>
            </Row>
            {rows.map((row: any, index: number) => (
              <>
                <Row mt="sm" justifyContent="center" alignItems="center">
                  <Col md={3.5} size={3.5}>
                    <Dropdown
                      selectedItem={selectedGroup[index]}
                      onSelect={(item: string) =>
                        setSelectedGroup([
                          ...selectedGroup.slice(0, index),
                          item,
                          ...selectedGroup.slice(index + 1),
                        ])
                      }
                      downshiftProps={{ defaultHighlightedIndex: 0 }}
                    >
                      <Field>
                        <Label hidden></Label>
                        <Autocomplete isCompact>
                          {selectedGroup[index]}
                        </Autocomplete>
                      </Field>
                      <Menu>
                        {matchingOptions.length ? (
                          matchingOptions.map((option) => (
                            <Item
                              style={{
                                fontSize: `${baseTheme.space.one * 10}px`,
                                fontWeight: '400',
                                color: baseTheme.colors.customOne,
                              }}
                              key={option}
                              value={option}
                              disabled={option != 'All groups'}
                            >
                              <span>{option}</span>
                            </Item>
                          ))
                        ) : (
                          <Item disabled>No matches found</Item>
                        )}
                      </Menu>
                    </Dropdown>
                  </Col>

                  <Col md={1.8} size={1.8}>
                    <Input
                      onChange={(e) => {
                        setQuantity([
                          ...quantity.slice(0, index),
                          parseInt(e.target.value),
                          ...quantity.slice(index + 1),
                        ]);
                      }}
                      defaultValue={String(quantity[index])}
                      isCompact
                    />
                  </Col>
                  <Col md={3.5} size={3.5}>
                    <Dropdown
                      selectedItem={tierRule[index]}
                      onSelect={(item) =>
                        setTierRule([
                          ...tierRule.slice(0, index),
                          item,
                          ...tierRule.slice(index + 1),
                        ])
                      }
                      downshiftProps={{ defaultHighlightedIndex: 0 }}
                    >
                      <Field>
                        <Label hidden></Label>
                        <Autocomplete isCompact>{tierRule[index]}</Autocomplete>
                      </Field>
                      <Menu>
                        {matchingOption.length ? (
                          matchingOption.map((option) => (
                            <Item
                              style={{
                                fontSize: `${baseTheme.space.one * 10}px`,
                                fontWeight: '400',
                                color: baseTheme.colors.customOne,
                              }}
                              key={option}
                              value={option}
                            >
                              <span>{option}</span>
                            </Item>
                          ))
                        ) : (
                          <Item disabled>No matches found</Item>
                        )}
                      </Menu>
                    </Dropdown>
                  </Col>
                  <Col md={1.8} size={1.8}>
                    <Input
                      onChange={(e) => {
                        setPrices([
                          ...prices.slice(0, index),
                          parseInt(e.target.value),
                          ...prices.slice(index + 1),
                        ]);
                      }}
                      defaultValue={String(prices[index])}
                      isCompact
                    />
                  </Col>

                  <Col size={1}>
                    <CrossIcon onClick={() => removeRow(index)} />
                  </Col>
                </Row>
              </>
            ))}
            {rows.length != 0 && (
              <>
                <Row mt="md" justifyContent="center">
                  <Button
                    size="small"
                    style={{
                      fontSize: `${baseTheme.customFontSizes.one * 15}px`,
                      fontWeight: '600',
                    }}
                    onClick={() => {
                      handleAdd();
                    }}
                  >
                    Submit
                  </Button>
                </Row>
              </>
            )}
          </DrawerModal.Body>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
