import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import Input from '../../UI-components/Input';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { MediaInput } from '../../UI-components/MediaInput';
import { CalendarIcon } from '../../../utils/icons';
import {
  Dropdown,
  Item,
  Menu,
  Field,
  Select,
} from '@zendeskgarden/react-dropdowns';

interface IItem {
  label: string;
  value: string;
}

const TierPricingProductListDrawer = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: any;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {
  const close = () => setIsOpen(false);

  const handleResetFilter = () => {
    setFilters({
      page: 1,
    });
    setSelectedItem(undefined);

    close();
  };

  const applyFilters = () => {
    setFilters((prev: any) => ({
      ...prev,
      type: selectedItem?.value,
    }));

    close();
  };

  const items = [
    { label: 'Simple', value: 'simple' },
    { label: 'Group', value: 'grouped' },
    ,
  ];

  const [selectedItem, setSelectedItem] = useState<IItem>();

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row>
              <Col>
                <Dropdown
                  selectedItem={selectedItem}
                  onSelect={setSelectedItem}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <Field>
                    <Label>Select type</Label>
                    <Select>
                      {selectedItem?.label ? selectedItem.label : 'Select type'}
                    </Select>
                  </Field>
                  <Menu>
                    {items.map((option) => (
                      <Item key={option?.value} value={option}>
                        {option?.label}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button isDanger isPrimary onClick={handleResetFilter}>
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default TierPricingProductListDrawer;
