import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { Dropdown } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { Field as DTField, Input as DTInput } from '@zendeskgarden/react-forms';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { CouponFilters as Filters } from '../../../pages/coupon/Coupon';
import { format } from 'date-fns';

export interface IItem {
  label: string;
  value: string;
}
export const couponTypes: IItem[] = [
  { label: 'Product Promotion', value: 'product_based' },
  { label: 'Cart Promotion', value: 'cart_based' },
];
const methodItems: IItem[] = [
  { label: 'Discount Coupon', value: 'discount_code' },
  { label: 'Automatic Coupon', value: 'automatic_code' },
  { label: 'Bulk Coupon', value: 'bulk_code' },
];
export const CouponFilters = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: Filters;
  setFilters: React.Dispatch<React.SetStateAction<Filters>>;
}) => {
  const dateFormatter = new Intl.DateTimeFormat('en-CA', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  const close = () => setIsOpen(false);
  const [couponType, setCouponType] = useState<IItem | undefined>(() => {
    return couponTypes.find((type) => type.value === filters.type);
  });
  const [method, setMethod] = useState<IItem | undefined>(() => {
    return methodItems.find((type) => type.value === filters.method);
  });
  const [couponFrom, setCouponFrom] = useState<Date | undefined>(() => {
    if (filters['start-date']) return new Date(filters['start-date']);
    else return undefined;
  });
  const [couponTo, setCouponTo] = useState<Date | undefined>(() => {
    if (filters['end-date']) return new Date(filters['end-date']);
    else return undefined;
  });

  const applyFilters = () => {
    const newFilters: any = {};
    if (couponType) {
      newFilters['type'] = couponType.value;
    }
    if (method) {
      newFilters['method'] = method.value;
    }
    if (couponTo) {
      newFilters['end-date'] = format(couponTo, 'yyyy-MM-dd');
    }
    if (couponFrom) {
      newFilters['start-date'] = format(couponFrom, 'yyyy-MM-dd');
    }
    setFilters((prev: Filters) => ({
      ...prev,
      ...newFilters,
      page: 1,
      size: 20,
    }));

    setIsOpen(false);
  };
  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filter</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12}>
                <Dropdown
                  selectedItem={couponType}
                  onSelect={(items) => setCouponType(items)}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Coupon Type</Label>
                    <Select>
                      {couponType?.label
                        ? couponType?.label
                        : 'Select Coupon Type'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {couponTypes.map((option, index) => (
                      <>
                        <Item key={index} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="md">
                <Dropdown
                  selectedItem={method}
                  onSelect={(items) => setMethod(items)}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Methods</Label>
                    <Select>
                      {method?.label ? method?.label : 'Select Method'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {methodItems.map((option, index) => (
                      <>
                        <Item key={index} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt={'md'}>
                <DTField>
                  <Label>Coupon Created From</Label>
                  <Datepicker
                    placement="bottom"
                    value={couponFrom}
                    onChange={setCouponFrom}
                    formatDate={(date) => dateFormatter.format(date)}
                  >
                    <DTInput placeholder="From date" />
                  </Datepicker>
                </DTField>
              </Col>
              <Col size={12} mt={'md'}>
                <DTField>
                  <Label>Coupon Created To</Label>
                  <Datepicker
                    placeholder="To Date"
                    value={couponTo}
                    onChange={setCouponTo}
                    formatDate={(date) => dateFormatter.format(date)}
                  >
                    <DTInput placeholder="To date" />
                  </Datepicker>
                </DTField>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  setCouponType(undefined);
                  setMethod(undefined);
                  setFilters({
                    page: 1,
                    size: 20,
                  });
                  setIsOpen(false);
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
