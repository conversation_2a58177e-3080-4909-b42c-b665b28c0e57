import React from 'react';
import { Col, Row } from '@zendeskgarden/react-grid';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { baseTheme } from '../../../themes/theme';
import styled from 'styled-components';
import { format } from 'date-fns';
import { getImageUrl } from '../../../helpers/helper';

interface Item {
  can_cancel: boolean;
  image: string;
  name: string;
  price: string;
  qty_canceled: number | null;
  qty_cancellable: boolean;
  qty_ordered: string;
  qty_refunded: number | null;
  qty_shipped: number | null;
  sku: string;
  status: string;
  url_key: string;
}

interface ItemDrawerProps {
  data: Item[];
  date: string;
  status: string;
  close: () => void;
}

const NameCell = styled.div`
  maring-bottom: 10px;
  color: #373737;
  font-weight: 400;
`;

const ItemDrawer: React.FC<ItemDrawerProps> = ({ data, date, status, close }) => {
  const drawerContent = React.useMemo(() => {
    if (!data || data.length === 0) return null;

    return data.map((item, index) => (
      <div
        key={item.sku}
        style={{
          margin: '20px',
          padding: '20px',
          border: '0.758px solid #DDE0E3',
          borderRadius: '7px',
          height: 'auto',
        }}
      >
        <Row style={{ position: 'relative' }}>
          <Col>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                border: '0.758px solid #DDE0E3',
                borderRadius: '7px',
              }}
            >
              <img
                style={{
                  maxWidth: `${baseTheme.components.dimension.width.base * 15}px`,
                  width: 'auto',
                  objectFit: 'contain',
                  height: 'auto',
                }}
                src={getImageUrl(item?.image)}
                alt={item.name} 
              />
            </div>
          </Col>
          <Col style={{alignItems: 'center', fontSize: '14px'}}>
            <div style={{color: '#205375', marginBottom: '10px'}}>{item.name}</div>
            <div>
            <NameCell>Status - {item.status}</NameCell>
            <NameCell>Price - {item.price}</NameCell>
            <NameCell>
              {status === "Cancelled" ? (
                <>{date ? `Cancelled On ${format(new Date(date), 'EEEE, dd MMM yyyy')}` : ""}</>
              ):(
                status === "Returned" ? (
                  <>{date ? `Returned On ${format(new Date(date), 'EEEE, dd MMM yyyy')}` : ''}</>
                ):(
                  <>{date ? `Expected Delivery Date On ${format(new Date(date), 'EEEE, dd MMM yyyy')}`: '' }</>
                )
              )}
              </NameCell>
            </div>
          </Col>
        </Row>
      </div>
    ));
  }, [data, date]);

  return (
    <DrawerModal
      
      isOpen={data.length > 0}
      onClose={close}
      style={{ width: '30%' }}
    >
      <DrawerModal.Header>All Items</DrawerModal.Header>
      {drawerContent}
      <DrawerModal.Close />
    </DrawerModal>
  );
};

export default ItemDrawer;