import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import Input from '../../UI-components/Input';
import {
  Field as DTField,
  Label,
  Input as DTInput,
} from '@zendeskgarden/react-forms';

import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import {
  Dropdown,
  Field as DField,
  Multiselect,
  Trigger,
  Label as DLabel,
  Select as DSelect,
} from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { Combobox, Field } from '@zendeskgarden/react-dropdowns';
import { ApprovedByOutput } from '../../../gql/graphql';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { Tag } from '@zendeskgarden/react-tags';
import { format } from 'date-fns';
import { Select } from '@zendeskgarden/react-forms';
import { IItem } from '../ViewCancelRequestDrawer';

const CountryCodes: IItem[] = [
  { label: 'In', value: 'IN' },
  { label: 'Not In', value: 'NOT IN' },
];
export interface OrderfilterObject {
  order_id?: string | undefined;
  payment_method_code?: string[] | undefined;
  source?: string[] | undefined;
  status?: string | undefined;
  mapped_order_status?: string[] | undefined;
  erp_status?: string[] | undefined;
  entity_id?: string | undefined;
  customer_email?: string | undefined;
  country_id?: string | undefined;
  dateFrom?: string | undefined;
  dateTo?: string | undefined;
  dateFilter?: any;
  rowsPerPage: number;
  pageNumber: number;
}

export const OrderFilters = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  customer_email,
  setCustomer_email,
  payment_method_code,
  setPayment_method_code,
  source,
  setSource,
  erpStatus,
  setErpStatus,
  country_id,
  setCountry_id,
  order_status,
  setOrder_status,
  entity_id,
  setEntity_id,
  endDate,
  setEndDate,
  startDate,
  setStartDate,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: OrderfilterObject;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  customer_email: string | undefined;
  setCustomer_email: React.Dispatch<React.SetStateAction<string | undefined>>;
  payment_method_code: string[];
  setPayment_method_code: React.Dispatch<React.SetStateAction<string[]>>;
  source: string[];
  setSource: React.Dispatch<React.SetStateAction<string[]>>;
  erpStatus: string[];
  setErpStatus: React.Dispatch<React.SetStateAction<string[]>>;
  country_id: IItem | undefined;
  setCountry_id: React.Dispatch<React.SetStateAction<IItem | undefined>>;
  order_status: string[];
  setOrder_status: React.Dispatch<React.SetStateAction<string[]>>;
  entity_id: string | undefined;
  setEntity_id: React.Dispatch<React.SetStateAction<string | undefined>>;
  endDate: Date | undefined;
  setEndDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  startDate: Date | undefined;
  setStartDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
}) => {
  const close = () => setIsOpen(false);

  const dateFormatter = new Intl.DateTimeFormat('en-CA', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  const getNull = (items: any) => {
    const index = items.indexOf('null');
    index === -1 ? items : (items[index] = null);
    return items;
  };
  const applyFilters = () => {
    const countryId = country_id?.value;
    // let resultDate: Date;
    // if (!startDate) {
    //   const currentDate: Date = new Date();
    //   resultDate = new Date(
    //     currentDate.getFullYear(),
    //     currentDate.getMonth() - 3,
    //     currentDate.getDate(),
    //   );
    //   if (currentDate.getMonth() < 2) {
    //     resultDate.setFullYear(resultDate.getFullYear() - 1);
    //   }

    //   // setStartDate(resultDate);
    // } else {
    //   resultDate = startDate;
    // }
    setFilters((prev: any) => ({
      ...prev,
      pageNumber: 1,
      payment_method_code:
        payment_method_code.length > 0 ? payment_method_code : undefined,
      source: source.length > 0 ? source : undefined,
      erp_status: erpStatus.length > 0 ? getNull(erpStatus) : undefined,
      order_status: order_status.length > 0 ? getNull(order_status) : undefined,
      entity_id: entity_id ? entity_id.trim() : undefined,
      customer_email: customer_email ? customer_email.trim() : undefined,
      country_id: countryId ? countryId : undefined,
      dateFilter:
        startDate || endDate
          ? {
              dateFrom: startDate?.toLocaleDateString('en-CA'),
              dateTo: endDate?.toLocaleDateString('en-CA'),
            }
          : undefined,
    }));
    close();
  };

  useEffect(() => {
    if (erpStatus.some((item) => item === null)) {
      setErpStatus((prev) =>
        prev.map((item) => (item === null ? 'null' : item)),
      );
    }
  }, [erpStatus]);

  useEffect(() => {
    if (order_status.some((item) => item === null)) {
      setOrder_status((prev) =>
        prev.map((item) => (item === null ? 'null' : item)),
      );
    }
  }, [order_status]);

  // const setErpStatusNull = (items: any[]) => {
  //   const processedItems = items.map((item) => (item === null ? 'null' : item));
  //   setErpStatus(processedItems);
  // };

  // const setOrderStatusNull = (items: any[]) => {
  //   const processedItems = items.map((item) => (item === null ? 'null' : item));
  //   setOrder_status(processedItems);
  // };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12}>
                <Dropdown
                  selectedItems={source}
                  onSelect={(items) => setSource(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>Source</Label>
                    <Multiselect
                      renderItem={({ value, removeValue }: any) => (
                        <Tag>
                          <span>{value}</span>
                          <Tag.Close onClick={() => removeValue()} />
                        </Tag>
                      )}
                    />
                  </DropDownField>
                  <Menu>
                    <Item key={1} value={'magento'}>
                      Magento
                    </Item>
                    <Item key={2} value={'order-service'}>
                      Order Service
                    </Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItems={
                    order_status ? order_status : filters.mapped_order_status
                  }
                  onSelect={(items) => setOrder_status(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>Order Status</Label>
                    <Multiselect
                      renderItem={({ value, removeValue }: any) => (
                        <Tag>
                          <span>{value}</span>
                          <Tag.Close onClick={() => removeValue()} />
                        </Tag>
                      )}
                    />
                  </DropDownField>
                  <Menu>
                    <Item key={1} value={'Order Confirmed'}>
                      Order Confirmed
                    </Item>
                    <Item key={2} value={'Packed'}>
                      Packed
                    </Item>
                    <Item key={3} value={'Shipped'}>
                      Shipped
                    </Item>
                    <Item key={4} value={'Delivered'}>
                      Delivered
                    </Item>
                    <Item key={5} value={'Cancelled'}>
                      Cancelled
                    </Item>
                    <Item key={6} value={'Returned'}>
                      Returned
                    </Item>
                    <Item key={7} value={'Payment Pending'}>
                      Payment Pending
                    </Item>
                    <Item key={8} value={'Processing'}>
                      Processing
                    </Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItems={erpStatus}
                  onSelect={(items) => {
                    setErpStatus(items);
                  }}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>ERP Status</Label>
                    <Multiselect
                      renderItem={({ value, removeValue }: any) => (
                        <Tag>
                          <span>{value}</span>
                          <Tag.Close onClick={() => removeValue()} />
                        </Tag>
                      )}
                    />
                  </DropDownField>
                  <Menu>
                    <Item key={1} value={'null'}>
                      Null
                    </Item>
                    <Item key={2} value={'Packed'}>
                      Packed
                    </Item>
                    <Item key={1} value={'Part Allocated'}>
                      Part Allocated
                    </Item>
                    <Item key={2} value={'Allocated'}>
                      Allocated
                    </Item>
                    <Item key={1} value={'Cancelled'}>
                      Cancelled
                    </Item>
                    <Item key={2} value={'Closed'}>
                      Closed
                    </Item>
                    <Item key={1} value={'Confirmed'}>
                      Confirmed
                    </Item>
                    <Item key={2} value={'Delivered'}>
                      Delivered
                    </Item>
                    <Item key={2} value={'Part Picked'}>
                      Part Picked
                    </Item>
                    <Item key={1} value={'Partially Shipped'}>
                      Partially Shipped
                    </Item>
                    <Item key={2} value={'Pending'}>
                      Pending
                    </Item>
                    <Item key={1} value={'Pick complete'}>
                      Pick complete
                    </Item>
                    <Item key={1} value={'Shipped & Returned'}>
                      Shipped & Returned
                    </Item>
                    <Item key={2} value={'Shipped complete'}>
                      Shipped complete
                    </Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItems={payment_method_code}
                  onSelect={(items) => setPayment_method_code(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>Payment Method</Label>
                    <Multiselect
                      renderItem={({ value, removeValue }: any) => (
                        <Tag>
                          <span>{value}</span>
                          <Tag.Close onClick={() => removeValue()} />
                        </Tag>
                      )}
                    />
                  </DropDownField>
                  <Menu>
                    <Item key={1} value={'cashondelivery'}>
                      Cash on Delivery
                    </Item>
                    <Item key={2} value={'razorpay'}>
                      RazorPay
                    </Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt={'sm'}>
                <DTField>
                  <Label>Order From</Label>
                  <Datepicker
                    placement="bottom"
                    value={startDate}
                    onChange={setStartDate}
                    formatDate={(date) => dateFormatter.format(date)}
                  >
                    <DTInput />
                  </Datepicker>
                </DTField>
              </Col>
              <Col size={12} mt={'sm'}>
                <DTField>
                  <Label>Order To</Label>
                  <Datepicker
                    value={endDate}
                    onChange={setEndDate}
                    formatDate={(date) => dateFormatter.format(date)}
                  >
                    <DTInput />
                  </Datepicker>
                </DTField>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Entity Id</Label>
                <Input
                  value={entity_id}
                  onChange={(e) => {
                    setEntity_id(e.target.value);
                  }}
                />
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Customer Email</Label>
                <Input
                  value={customer_email}
                  onChange={(e) => {
                    setCustomer_email(e.target.value);
                  }}
                />
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItem={country_id}
                  onSelect={setCountry_id}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DField>
                    <DLabel>Select Country</DLabel>
                    <DSelect>
                      {country_id ? country_id.label : 'Select Country'}
                    </DSelect>
                  </DField>
                  <Menu>
                    {CountryCodes.map((option, index) => (
                      <>
                        <Item key={option.value} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  setCustomer_email(undefined);
                  setPayment_method_code([]);
                  setSource([]);
                  setErpStatus([]);
                  setCountry_id(undefined);
                  setOrder_status([]);
                  setEntity_id(undefined);
                  setEndDate(undefined);
                  setStartDate(undefined);
                  setFilters({
                    rowsPerPage: 20,
                    pageNumber: 1,
                  });
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
