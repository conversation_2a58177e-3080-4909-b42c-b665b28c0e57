import { Datepicker } from '@zendeskgarden/react-datepickers';
import {
  Dropdown,
  Field as DField,
  Label as DLabel,
  Multiselect,
  Menu,
  Item,
  Field,
  Select,
} from '@zendeskgarden/react-dropdowns';
import { Input, Label } from '@zendeskgarden/react-forms';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Tag } from '@zendeskgarden/react-tags';
import React, { useEffect, useState } from 'react';
import { Button } from '../../UI-components/Button';
import { baseTheme } from '../../../themes/theme';
import { format, parseISO } from 'date-fns';
import { Col, Row } from '../../UI-components/Grid';
import { ILabelProps } from '@zendeskgarden/react-dropdowns.next';
import { IFeedsCategory } from '../../../pages/feeds/FeedsCategory';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import { useGetFeedsTags } from '../../../hooks/useQuery';
import TagsMultiDropdown from '../../dropdown/feeds/TagsMultiDropdown';
import { filter } from 'lodash';

interface IItem {
  label: string;
  value: string;
}

const items = [
  { label: 'Oldest', value: 'asc' },
  { label: 'Latest', value: 'desc' },
];

const itemSort = [
  { label: 'Duration', value: 'duration' },
  { label: 'Watch Time', value: 'watch_time' },
  { label: 'Popularity', value: 'popularity' },
];

const FeedVideoFilter = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: any;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  reset: () => void;
}) => {
  const close = () => setIsOpen(false);

  const [selectedOrder, setSelectedOrder] = useState<IItem>(items[1]);
  const [selectedSort, setSelectedSort] = useState<IItem | undefined>();
  const [selectedTags, setSelectedTags] = useState<IFeedsCategory[]>([]);

  const axios = useAxios();
  const addToast = useToast();

  const { data: tags, isLoading: isTagsLoading } = useGetFeedsTags({
    unique: '2',
  });

  const [tagsData, setTagsData] = useState<IFeedsCategory[]>();

  useEffect(() => {
    if (tags) {
      setTagsData(tags);
    }
  }, [tags]);

  function getObjectByValue(value: any, array: any[]) {
    return array.find((item) => item.value === value);
  }

  useEffect(() => {
    if (selectedTags.length != 0) {
      console.log('Selected tags', selectedTags);
    }
  }, [selectedTags]);

  useEffect(() => {
    if (filters?.order) {
      const object = getObjectByValue(filters.order, items);
      setSelectedOrder(object);
    } else {
      setSelectedOrder(items[1]);
    }

    if (filters?.sort) {
      const object = getObjectByValue(filters.sort, itemSort);
      setSelectedSort(object);
    } else {
      setSelectedSort(undefined);
    }

    if (filters?.tags && tagsData) {
      const selectedTagsArray = filters.tags.map((tagId: any) => {
        return tagsData.find((tag) => tag.id === Number(tagId));
      });
      setSelectedTags(selectedTagsArray);
    } else {
      setSelectedTags([]);
    }
  }, [filters, tagsData]);

  const applyFilters = () => {
    const newFilters: any = {};

    if (selectedOrder) {
      newFilters.order = selectedOrder.value;
    }

    if (selectedSort) {
      newFilters.sort = selectedSort.value;
    }

    if (selectedTags.length !== 0) {
      const tagIds = selectedTags.map((tag) => `${tag.id}`);
      newFilters.tags = tagIds;
    }

    setFilters((prev: any) => ({
      ...prev,
      ...newFilters,
      page: 1,
    }));

    close();
  };

  const onSelectedOrder = (item: IItem) => {
    setSelectedOrder(item);
  };

  const onSelectedSort = (item: IItem) => {
    setSelectedSort(item);
  };

  return (
    <>
      <Row>
        <Col textAlign="center">
          <DrawerModal isOpen={isOpen} onClose={close}>
            <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
            <DrawerModal.Body>
              <Row justifyContent="center" alignItems="center">
                <Col mt="sm" size={12}>
                  <Dropdown
                    selectedItem={selectedOrder}
                    onSelect={(item) => {
                      onSelectedOrder(item);
                    }}
                    downshiftProps={{
                      itemToString: (item: IItem) => item && item.label,
                    }}
                  >
                    <Field>
                      <Label>Order by</Label>
                      <Select>{selectedOrder.label}</Select>
                    </Field>
                    <Menu>
                      {items.map((option) => (
                        <Item key={option.value} value={option}>
                          {option.label}
                        </Item>
                      ))}
                    </Menu>
                  </Dropdown>
                </Col>
                <Col mt="sm" size={12}>
                  <Dropdown
                    selectedItem={selectedSort}
                    onSelect={(item) => {
                      onSelectedSort(item);
                    }}
                    downshiftProps={{
                      itemToString: (item: IItem) => item && item.label,
                    }}
                  >
                    <Field>
                      <Label>Sort by</Label>
                      <Select>
                        {selectedSort?.label
                          ? selectedSort.label
                          : 'Select Sort by'}
                      </Select>
                    </Field>
                    <Menu>
                      {itemSort.map((option) => (
                        <Item key={option.value} value={option}>
                          {option.label}
                        </Item>
                      ))}
                    </Menu>
                  </Dropdown>
                </Col>

                <Col mt="sm" size={12}>
                  {!isTagsLoading && tagsData && selectedTags ? (
                    <>
                      <TagsMultiDropdown
                        selectedTags={selectedTags}
                        setSelectedTags={setSelectedTags}
                        tagsData={tagsData}
                      />
                    </>
                  ) : (
                    <></>
                  )}
                </Col>
              </Row>
            </DrawerModal.Body>
            <DrawerModal.Footer>
              <DrawerModal.FooterItem>
                <Button
                  isPrimary
                  isDanger
                  onClick={() => {
                    reset && reset();
                    close();
                  }}
                >
                  Reset Filters
                </Button>
              </DrawerModal.FooterItem>
              <DrawerModal.FooterItem>
                <Button isPrimary onClick={applyFilters}>
                  Apply Filters
                </Button>
              </DrawerModal.FooterItem>
            </DrawerModal.Footer>
            <DrawerModal.Close />
          </DrawerModal>
        </Col>
      </Row>
    </>
  );
};

export default FeedVideoFilter;
