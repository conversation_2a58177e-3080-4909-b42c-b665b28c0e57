import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import Input from '../../UI-components/Input';
import { Field as _Field } from '../../UI-components/Field';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { Dropdown, Multiselect } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { Tag } from '@zendeskgarden/react-tags';
import { baseTheme } from '../../../themes/theme';
import { format } from 'date-fns';
import { DTORecievedFilterProps } from '../../../types/types';

const DTORecievedFilter = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: DTORecievedFilterProps;
  setFilters: React.Dispatch<React.SetStateAction<DTORecievedFilterProps>>;
  reset: () => void;
}) => {
  const close = () => setIsOpen(false);
  const [status, setStatus] = useState<string[]>(filters.status || []);
  const [startDate, setStartDate] = useState<Date | undefined>(
    filters.dateFilter.dateFrom
      ? new Date(filters.dateFilter.dateFrom)
      : undefined,
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    filters.dateFilter.dateTo ? new Date(filters.dateFilter.dateTo) : undefined,
  );
  const dateFormatter = new Intl.DateTimeFormat('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  const applyFilters = () => {
    setFilters((prev: DTORecievedFilterProps) => {
      const updatedFilters: DTORecievedFilterProps = {
        ...prev,
      };

      if (startDate) {
        updatedFilters.dateFilter.dateFrom = format(startDate, 'yyyy-MM-dd');
      } else {
        updatedFilters.dateFilter.dateFrom = '';
      }

      if (updatedFilters.dateFilter && endDate) {
        updatedFilters.dateFilter.dateTo = format(endDate, 'yyyy-MM-dd');
      } else {
        updatedFilters.dateFilter.dateTo = format(new Date(), 'yyyy-MM-dd');
      }

      if (status && status.length > 0) {
        updatedFilters.status = status;
      }

      updatedFilters.pageNumber = 1;

      return updatedFilters;
    });
    close();
  };

  useEffect(() => {
    setStartDate(
      filters.dateFilter?.dateFrom
        ? new Date(filters.dateFilter.dateFrom)
        : undefined,
    );
    setEndDate(
      filters.dateFilter?.dateTo
        ? new Date(filters.dateFilter.dateTo)
        : undefined,
    );
    setStatus(filters.status || []);
  }, [filters.dateFilter.dateFrom, filters.dateFilter.dateTo]);

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItems={status}
                  onSelect={(items) => setStatus(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>Status</Label>
                    <Multiselect
                      placeholder={'Choose Status'}
                      renderItem={({ value, removeValue }: any) => (
                        <Tag>
                          <span>{value}</span>
                          <Tag.Close onClick={() => removeValue()} />
                        </Tag>
                      )}
                    />
                  </DropDownField>
                  <Menu>
                    <Item key={1} value={'Close'}>
                      Close
                    </Item>
                    <Item key={2} value={'Open'}>
                      Open
                    </Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Created At From</Label>
                <Datepicker
                  value={startDate}
                  onChange={setStartDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Created At Date'}
                    fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Created At To</Label>
                <Datepicker
                  value={endDate}
                  onChange={setEndDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Created At Date'}
                    fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  reset && reset();

                  close();
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default DTORecievedFilter;
