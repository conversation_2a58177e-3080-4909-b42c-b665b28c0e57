import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import Input from '../../UI-components/Input';
import { Field as _Field } from '../../UI-components/Field';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Field } from '../../UI-components/Field';
import { Col } from '../../UI-components/Grid';
import { Dropdown, Multiselect } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { ApprovedByOutput } from '../../../gql/graphql';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { Tag } from '@zendeskgarden/react-tags';
import { baseTheme } from '../../../themes/theme';
import { format, parseISO } from 'date-fns';
import { useQuery } from '@tanstack/react-query';
import { IReturnAssignee } from '../../../types/types';
import useAxios from '../../../hooks/useAxios';
import krakendPaths from '../../../constants/krakendPaths';

interface filterObject {
  created_at_from?: string | undefined;
  created_at_to?: string | undefined;
  status?: string | undefined;
  refund_status?: string | undefined;
  current_assignee?: string | undefined;
  customer_email?: string | undefined;
  rowsPerPage?: number;
  order_by?: string;
  page: number;
}

interface IItem {
  label: string;
  value: string;
}

const refundItem = [
  { label: 'Full', value: 'full' },
  { label: 'Partial', value: 'partial' },
];

const orderByItem = [
  { label: 'Latest First', value: '-created_at' },
  { label: 'Oldest First', value: 'created_at' },
];

const statuses = [
  { label: 'Closed', value: 'closed' },
  { label: 'Open', value: 'open' },
  { label: 'Partial Open', value: 'partial_open' },
];

const assignedToItem: IItem[] = [];

const ReturnFilter = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: filterObject;
  setFilters: React.Dispatch<React.SetStateAction<filterObject>>;
  reset: () => void;
}) => {
  const close = () => setIsOpen(false);

  const [orderBy, setorderBy] = useState<IItem>();
  const [assignedTo, setAssignedTo] = useState<IItem>();
  const [status, setStatus] = useState<string[]>([]);
  const [refundStatus, setRefundStatus] = useState<IItem>();
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [orderToDate, setOrderToDate] = useState();
  const [email, setEmail] = useState<string>();
  const [assigners, setAssigners] = useState<IItem[]>([]);
  // console.log('shiva', orderBy);
  const axios = useAxios();

  const dateFormatter = new Intl.DateTimeFormat('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  const {
    isLoading,
    isError,
    error,
    data: assigneeData,
    isRefetching,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ['return-assigneee'],
    queryFn: async (): Promise<any> => {
      return await axios.get(`/return/assignee`);
      // return await axios.get(`${krakendPaths.RETURN_URL}/assignee`);
    },
    onError: (err) => {
      console.log(err);
    },
    onSuccess: (data) => {
      console.log(data);

      const assigners: IItem[] = data?.results.map(
        (assigner: any, index: number) => {
          return {
            label: assigner.user,
            value: `${assigner.id}`,
          };
        },
      );

      setAssigners(assigners);
    },
  });

  useEffect(() => {
    // console.log('Filters', filters);
    if (filters.status) {
      setStatus(status);
    } else {
      setStatus([]);
    }

    if (filters.refund_status) {
      const selectedRefundItem = refundItem.find(
        (item) => item.value === filters.refund_status,
      );
      setRefundStatus(selectedRefundItem);
    } else {
      setRefundStatus(undefined);
    }

    //ymd -> mdy

    if (filters.created_at_from) {
      const selectedDate = new Date(
        format(parseISO(filters.created_at_from), 'MM/dd/yyyy'),
      );
      setStartDate(selectedDate);
    } else {
      setStartDate(undefined);
    }

    if (filters.created_at_to) {
      const selectedDate = new Date(
        format(parseISO(filters.created_at_to), 'MM/dd/yyyy'),
      );
      setEndDate(selectedDate);
    } else {
      setEndDate(undefined);
    }

    if (filters.current_assignee) {
      const selectedAssignee = assigners.find(
        (item) => item.value === filters.current_assignee,
      );
      setAssignedTo(selectedAssignee);
    } else {
      setAssignedTo(undefined);
    }

    if (filters.status && filters.status.length != 0) {
      const statusArray = filters.status.split(',');
      setStatus(statusArray);
    } else {
      setStatus([]);
    }

    if (filters.order_by) {
      const selectedOrder = orderByItem.find(
        (item) => item.value === filters.order_by,
      );
      setorderBy(selectedOrder);
    } else {
      setorderBy(undefined);
    }

    if (filters.customer_email) {
      setEmail(filters.customer_email);
    } else {
      setEmail(undefined);
    }
  }, [filters]);

  const applyFilters = () => {
    setFilters((prev: filterObject) => {
      const updatedFilters: filterObject = {
        ...prev,
      };

      if (email) {
        updatedFilters.customer_email = email;
      }

      if (refundStatus) {
        updatedFilters.refund_status = refundStatus.value;
      }

      if (assignedTo) {
        updatedFilters.current_assignee = assignedTo.value;
      }

      if (startDate) {
        updatedFilters.created_at_from = format(startDate, 'yyyy-MM-dd');
      }

      if (endDate) {
        updatedFilters.created_at_to = format(endDate, 'yyyy-MM-dd');
      }

      if (status.length > 0 && status) {
        updatedFilters.status = status.join(',');
      }

      if (orderBy) {
        updatedFilters.order_by = orderBy.value;
      }
      localStorage.setItem('return-filters', JSON.stringify(updatedFilters));
      return updatedFilters;
    });
    // setTimeout(() => {
    //   close();
    // }, 500);
    close();
  };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItems={status}
                  onSelect={(items) => setStatus(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>Status</Label>
                    <Multiselect
                      placeholder={'Choose Status'}
                      renderItem={({ value, removeValue }: any) => (
                        <Tag>
                          <span>{value}</span>
                          <Tag.Close onClick={() => removeValue()} />
                        </Tag>
                      )}
                    />
                  </DropDownField>
                  <Menu>
                    {statuses.map((option, index) => (
                      <>
                        <Item key={1} value={option.value}>
                          {option.value}
                        </Item>
                      </>
                    ))}

                    {/* <Item key={2} value={'open'}>
                      Open
                    </Item>
                    <Item key={3} value={'partial_open'}>
                      Partial Open
                    </Item> */}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItem={refundStatus}
                  onSelect={(items) => setRefundStatus(items)}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Refund Status</Label>
                    <Select>
                      {refundStatus ? refundStatus.label : 'Select Status'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {refundItem.map((option, index) => (
                      <>
                        <Item key={index} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItem={assignedTo}
                  onSelect={(items) => setAssignedTo(items)}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Assigned To</Label>
                    <Select>
                      {assignedTo ? assignedTo.label : 'Assigned to'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {assigners.map((option, index) => (
                      <>
                        <Item key={index} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItem={orderBy}
                  onSelect={setorderBy}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Order by</Label>
                    <Select>{orderBy ? orderBy.label : 'Order by'}</Select>
                  </DropDownField>
                  <Menu>
                    {orderByItem.map((option, index) => (
                      <>
                        <Item key={index} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Order From</Label>
                <Datepicker
                  value={startDate}
                  onChange={setStartDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Order Date'}
                    fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Order To</Label>
                <Datepicker
                  value={endDate}
                  onChange={setEndDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Order Date'}
                    fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
              <Col size={12} mt="sm">
                <Field>
                  <Label>Customer Email</Label>
                  <Input
                    onChange={(e) => {
                      setEmail(e.target.value);
                    }}
                    fontSize={baseTheme.fontSizes.md}
                    placeholder="Customer Email"
                    defaultValue={filters?.customer_email}
                  />
                </Field>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  reset && reset();
                  close();
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default ReturnFilter;
