import { Datepicker } from '@zendeskgarden/react-datepickers';
import { Field, Input, Label } from '@zendeskgarden/react-forms';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Tag } from '@zendeskgarden/react-tags';
import React, { useEffect, useState } from 'react';
import { Button } from '../../UI-components/Button';
import { baseTheme } from '../../../themes/theme';
import { format, parseISO } from 'date-fns';
import { Col, Row } from '../../UI-components/Grid';
import { filterObject } from '../../table/return-modules/ReturnDTO';
import { ILabelProps } from '@zendeskgarden/react-dropdowns.next';
import { IFeedsCategory } from '../../../pages/feeds/FeedsCategory';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import { useGetFeedsTags } from '../../../hooks/useQuery';
import TagsMultiDropdown from '../../dropdown/feeds/TagsMultiDropdown';
import { filter } from 'lodash';
import StateDropdown from '../../dropdown/customer/StateDropdown';

interface IItem {
  label: string;
  value: string;
}

const items = [
  { label: 'Oldest', value: 'asc' },
  { label: 'Latest', value: 'desc' },
];

const itemSort = [
  { label: 'Duration', value: 'duration' },
  { label: 'Watch Time', value: 'watch_time' },
  { label: 'Popularity', value: 'popularity' },
];

const CustomerFilter = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: any;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  reset: () => void;
}) => {
  const close = () => setIsOpen(false);

  const [localFilter, setLocalFilter] = useState<any>();

  const handleInputChange = (e: any, prop: string) => {
    const value = e.target.value;
    if (prop === 'customerId') {
      setLocalFilter((prev: any) => ({
        ...prev,
        customer_id: value,
      }));
    } else if (prop === 'customerName') {
      setLocalFilter((prev: any) => ({
        ...prev,
        customer_name: value,
      }));
    } else if (prop === 'phone') {
      setLocalFilter((prev: any) => ({
        ...prev,
        mobile: value,
      }));
    }
    //  else if (prop === 'email') {
    //   setLocalFilter((prev: any) => ({
    //     ...prev,
    //     customer_id: value,
    //   }));
    // }
    else if (prop === 'country') {
      setLocalFilter((prev: any) => ({
        ...prev,
        country_id: value,
      }));
    } else if (prop === 'state') {
      setLocalFilter((prev: any) => ({
        ...prev,
        region_id: value,
      }));
    }
  };

  const applyFilter = () => {
    setFilters({
      ...filters,
      ...localFilter,
      page: 0,
    });
    close();
  };

  const [currentCountry, setCurrentCountry] = useState<any>();
  const [currentState, setcurrentState] = useState<any>();

  useEffect(() => {
    if (currentCountry) {
      setLocalFilter({
        ...localFilter,
        country_id: currentCountry.country_id,
      });
    }
  }, [currentCountry]);

  useEffect(() => {
    if (currentState) {
      setLocalFilter({
        ...localFilter,
        region_id: currentState.region_id,
      });
    }
  }, [currentState]);

  useEffect(() => {
    console.log('Filters', filters);
  }, [filters]);

  return (
    <>
      <Row>
        <Col textAlign="center">
          <DrawerModal isOpen={isOpen} onClose={close}>
            <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
            <DrawerModal.Body>
              <Row justifyContent="center" alignItems="center">
                <Col mt="sm" size={12}>
                  <Field>
                    <Label>Customer Id</Label>
                    <Input
                      defaultValue={filters?.customer_id}
                      onChange={(e) => {
                        handleInputChange(e, 'customerId');
                      }}
                    />
                  </Field>
                </Col>
                <Col mt="sm" size={12}>
                  <Field>
                    <Label>Customer Name</Label>
                    <Input
                      defaultValue={filters?.customer_name}
                      onChange={(e) => {
                        handleInputChange(e, 'customerName');
                      }}
                    />
                  </Field>
                </Col>
                <Col mt="sm" size={12}>
                  <Field>
                    <Label>Phone No</Label>
                    <Input
                      defaultValue={filters?.mobile}
                      onChange={(e) => {
                        handleInputChange(e, 'phone');
                      }}
                    />
                  </Field>
                </Col>
                <Col mt="sm" size={12}>
                  <StateDropdown
                    currentCountry={currentCountry}
                    currentState={currentState}
                    setcurrentCountry={setCurrentCountry}
                    setcurrentState={setcurrentState}
                    isVertical
                    labelHidden={false}
                    inFilter
                  />
                </Col>
              </Row>
            </DrawerModal.Body>
            <DrawerModal.Footer>
              <DrawerModal.FooterItem>
                <Button
                  isPrimary
                  isDanger
                  onClick={() => {
                    reset && reset();
                    setCurrentCountry(undefined);
                    setcurrentState(undefined);
                    close();
                  }}
                >
                  Reset Filters
                </Button>
              </DrawerModal.FooterItem>
              <DrawerModal.FooterItem>
                <Button
                  isPrimary
                  onClick={() => {
                    applyFilter();
                  }}
                >
                  Apply Filters
                </Button>
              </DrawerModal.FooterItem>
            </DrawerModal.Footer>
            <DrawerModal.Close />
          </DrawerModal>
        </Col>
      </Row>
    </>
  );
};

export default CustomerFilter;
