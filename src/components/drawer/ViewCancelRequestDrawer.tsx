import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Field as _Field } from '../UI-components/Field';
import { Label } from '../UI-components/Label';
import { Row } from '../UI-components/Grid';
import { Col } from '../UI-components/Grid';
import { Dropdown } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';

export interface ViewCancelRequestFilter {
  page: number;
  order_id?: string | undefined;
  status?: string | undefined;
  payment_method?: string | undefined;
}
export interface IItem {
  label: string;
  value: string;
}
export const statusItem: IItem[] = [
  { label: 'Requested', value: 'requested' },
  { label: 'In Process', value: 'in_process' },
  { label: 'Closed', value: 'closed' },
];
const paymentItem: IItem[] = [
  { label: 'Cash on Delivery', value: 'cashondelivery' },
  { label: 'Razorpay', value: 'razorpay' },
];
export const ViewCancelRequestFilters = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  status,
  setStatus,
  payment_method,
  setPayment_method,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: ViewCancelRequestFilter;
  setFilters: React.Dispatch<React.SetStateAction<ViewCancelRequestFilter>>;
  status: IItem | undefined;
  setStatus: React.Dispatch<React.SetStateAction<IItem | undefined>>;
  payment_method: IItem | undefined;
  setPayment_method: React.Dispatch<React.SetStateAction<IItem | undefined>>;
}) => {
  const close = () => setIsOpen(false);

  const applyFilters = () => {
    setFilters((prev: any) => ({
      ...prev,
      page: 1,
      status: status?.value,
      payment_method: payment_method?.value,
    }));
    setIsOpen(false);
  };
  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12}>
                <Dropdown
                  selectedItem={status}
                  onSelect={(items) => setStatus(items)}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Status</Label>
                    <Select>{status ? status.label : 'Select Status'}</Select>
                  </DropDownField>
                  <Menu>
                    {statusItem.map((option, index) => (
                      <>
                        <Item key={index} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItem={payment_method}
                  onSelect={(items) => setPayment_method(items)}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Payment Method</Label>
                    <Select>
                      {payment_method
                        ? payment_method.label
                        : 'Select Payment Method'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {paymentItem.map((option, index) => (
                      <>
                        <Item key={index} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  setStatus(undefined);
                  setPayment_method(undefined);
                  setFilters({
                    page: 1,
                  });
                  setIsOpen(false);
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
