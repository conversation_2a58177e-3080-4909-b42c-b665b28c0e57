import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import Input from '../../UI-components/Input';
import {
  Field as DTField,
  Label,
  Input as DTInput,
} from '@zendeskgarden/react-forms';

import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { filterTypes } from '../../../pages/delivery/ProductWarehousesQuantity';

export interface ProductWarehousesQtyfilterObject {
  product_id?: number | undefined;
  warehouse?: string | undefined;
  page: number;
}

export const ProductWarehousesQtyFilters = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: ProductWarehousesQtyfilterObject;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  reset: () => void;
}) => {
  const close = () => setIsOpen(false);
  const [warehouse, setWarehouse] = useState<string | undefined>(
    filters.warehouse || '',
  );
  const [productId, setProductId] = useState<number | undefined>(
    filters.product_id || undefined,
  );

  const applyFilters = () => {
    const upDatedFilter: filterTypes = {
      ...filters,
      page: 1,
    };
    if (warehouse && warehouse.length > 0) upDatedFilter.warehouse = warehouse;
    if (productId) upDatedFilter.product_id = productId;
    setFilters(upDatedFilter);
    close();
  };

  const handleWarehouseChange = (e: any) => {
    if (/^[a-zA-Z]*$/.test(e.target.value)) {
      setWarehouse(e.target.value);
    }
  };

  let invalidChars = ['e', 'E', '+', '-', '.'];
  const handlekeyDown = (e: any) => {
    if (invalidChars.includes(e.key)) {
      e.preventDefault();
    }
  };

  const handleProductIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    if (/^[0-9]*$/.test(input)) {
      // Use parseInt to convert the input string to a number
      setProductId(parseInt(input, 10));
    }
  };
  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Label>Product Id</Label>
                <Input
                  value={productId}
                  type="number"
                  onChange={(e) => {
                    handleProductIdChange(e);
                  }}
                  onKeyDown={handlekeyDown}
                />
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'lg'}>
                <Label>Warehouse</Label>
                <Input
                  value={warehouse}
                  onChange={(e) => {
                    handleWarehouseChange(e);
                  }}
                />
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  setWarehouse(undefined);
                  setFilters({
                    page: 1,
                  });
                  reset();
                  close();
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
