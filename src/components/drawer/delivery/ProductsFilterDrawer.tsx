import React, { useState } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import Input from '../../UI-components/Input';
import { Label } from '@zendeskgarden/react-forms';
import { Row, Col } from '../../UI-components/Grid';
import {
  Item,
  Dropdown,
  Field,
  Label as DLabel,
  Select as DSelect,
  Menu,
} from '@zendeskgarden/react-dropdowns';
import { IItem } from '../../../types/types';
import { ProductsFilterTypes } from '../../../pages/delivery/Products';

const statusItems: IItem[] = [
  { label: 'Enable', value: 'enable' },
  { label: 'Disable', value: 'disable' },
];

export const ProductsFilterDrawer = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  resetSearch,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: ProductsFilterTypes;
  setFilters: React.Dispatch<React.SetStateAction<ProductsFilterTypes>>;
  resetSearch: () => void;
}) => {
  const [skuInput, setSkuInput] = useState<string>(
    filters.sku ? filters.sku : '',
  );
  const [statusInput, setStatusInput] = useState<IItem | undefined>(() => {
    if (filters.status === 1) return { label: 'Enable', value: 'enable' };
    else if (filters.status === 0)
      return { label: 'Disable', value: 'disable' };
    return undefined;
  });
  const close = () => setIsOpen(false);

  const applyFilters = () => {
    const updatedFilters: ProductsFilterTypes = {
      ...filters,
      page: 1,
    };
    if (skuInput !== undefined && skuInput.length != 0) {
      updatedFilters.sku = skuInput;
    }
    if (statusInput !== undefined && statusInput.value !== undefined) {
      updatedFilters.status = statusInput.value === 'disable' ? 0 : 1;
    }
    setFilters(updatedFilters);
    close();
  };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Label>Enter SKU</Label>
                <Input
                  value={skuInput}
                  onChange={(e) => {
                    setSkuInput(e.target.value);
                  }}
                />
              </Col>
              <Col size={12} mt={'lg'}>
                <Dropdown
                  selectedItem={statusInput}
                  onSelect={setStatusInput}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <Field>
                    <DLabel>Status</DLabel>
                    <DSelect>
                      {statusInput ? statusInput.label : 'Select Status'}
                    </DSelect>
                  </Field>
                  <Menu>
                    {statusItems.map((option, index) => (
                      <>
                        <Item key={option.value} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  // setStatus(undefined);
                  // setSku('');
                  // setStatusInput(undefined);
                  // setSkuInput('');
                  setSkuInput('');
                  setStatusInput(undefined);
                  setFilters({
                    page: 1,
                  });
                  resetSearch();
                  close();
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
