import React, { useState } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import Input from '../../UI-components/Input';
import { Label } from '@zendeskgarden/react-forms';
import { Row, Col } from '../../UI-components/Grid';
import {
  Item,
  Dropdown,
  Field,
  Label as DLabel,
  Select as DSelect,
  Menu,
} from '@zendeskgarden/react-dropdowns';
import { IItem } from '../../../types/types';
import { WarehouseFilterTypes } from '../../../pages/delivery/Warehouses';

const statusItems: IItem[] = [
  { label: 'Enable', value: 'enable' },
  { label: 'Disable', value: 'disable' },
];

export const WarehousesFilterDrawer = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  resetSearch,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: WarehouseFilterTypes;
  setFilters: React.Dispatch<React.SetStateAction<WarehouseFilterTypes>>;
  resetSearch: () => void;
}) => {
  const [codeInput, setcodeInput] = useState<string>(filters.code || '');
  const [statusInput, setStatusInput] = useState<IItem | undefined>(() => {
    if (filters.enable === 1) return { label: 'Enable', value: 'enable' };
    else if (filters.enable === 0)
      return { label: 'Disable', value: 'disable' };
    else return undefined;
  });
  const close = () => setIsOpen(false);

  const applyFilters = () => {
    const updatedFilters: WarehouseFilterTypes = {
      ...filters,
      page: 1,
    };
    if (codeInput || codeInput.length != 0) updatedFilters.code = codeInput;

    if (statusInput?.value === 'disable') updatedFilters.enable = 0;
    else if (statusInput?.value === 'enable') updatedFilters.enable = 1;
    setFilters(updatedFilters);
    close();
  };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Label>Enter code</Label>
                <Input
                  value={codeInput}
                  onChange={(e) => {
                    setcodeInput(e.target.value);
                  }}
                />
              </Col>
              <Col size={12} mt={'lg'}>
                <Dropdown
                  selectedItem={statusInput}
                  onSelect={setStatusInput}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <Field>
                    <DLabel>Status</DLabel>
                    <DSelect>
                      {statusInput ? statusInput.label : 'Select Status'}
                    </DSelect>
                  </Field>
                  <Menu>
                    {statusItems.map((option) => (
                      <>
                        <Item key={option.value} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  setStatusInput(undefined);
                  setcodeInput('');
                  setFilters({
                    page: 1,
                  });
                  resetSearch();
                  close();
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
