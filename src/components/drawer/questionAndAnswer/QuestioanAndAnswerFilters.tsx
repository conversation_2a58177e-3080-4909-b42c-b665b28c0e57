import React, { useState } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { Field as _Field } from '../../UI-components/Field';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { Dropdown } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { DrawerModal } from '@zendeskgarden/react-modals';

interface filterObject {
  enable?: string | undefined;
  status?: string | boolean | undefined;
  search?: string | undefined;
  rowsPerPage: number;
  pageNumber: number;
}

const statusFilters = [
  { label: 'Closed', value: 'closed' },
  { label: 'Open', value: 'open' },
];

const enableFilters = [
  { label: 'Enabled', value: 'Enabled' },
  { label: 'Disabled', value: 'Disabled' },
];

const QuestionAndAnswerFilters = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
  isVersionTwo,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: filterObject;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  reset: any;
  isVersionTwo: boolean;
}) => {
  const close = () => setIsOpen(false);

  const [enable, setEnable] = useState(filters.enable || '');
  const [status, setStatus] = useState(filters.status || '');

  const applyFilters = () => {
    if (isVersionTwo) {
      setFilters((prev: any) => ({
        ...prev,
        status: enable === 'Enabled' ? 'true' : 'false',
      }));
      setIsOpen(false);
      return;
    }
    setFilters((prev: any) => ({
      ...prev,
      enable: enable,
      status: status,
      pageNumber: 1,
    }));
    localStorage.setItem(
      'questions-filters',
      JSON.stringify({
        ...filters,
        enable: enable,
        status: status,
        pageNumber: 1,
      }),
    );
    setIsOpen(false);
  };
  const handleReset = () => {
    reset();
    close();
  };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12}>
                <Dropdown
                  selectedItem={enable}
                  onSelect={(items) => setEnable(items)}
                  downshiftProps={{
                    itemToString: (item: any) => item,
                  }}
                >
                  <DropDownField>
                    <Label>Question Enabled / Disabled</Label>
                    <Select>
                      {enable
                        ? enableFilters.find(
                            (item: any) => item.value === enable,
                          )?.label
                        : 'Select Enabled/Disabled'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {enableFilters.map((option, index) => (
                      <>
                        <Item key={index} value={option.value}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              {!isVersionTwo && (
                <Col size={12} mt={'sm'}>
                  <Dropdown
                    selectedItem={status}
                    onSelect={(items) => setStatus(items)}
                    downshiftProps={{
                      itemToString: (item: any) => item,
                    }}
                  >
                    <DropDownField>
                      <Label>Question Status</Label>
                      <Select>
                        {status
                          ? statusFilters.find(
                              (item: any) => item.value === status,
                            )?.label
                          : 'Select Status'}
                      </Select>
                    </DropDownField>
                    <Menu>
                      {statusFilters.map((option, index) => (
                        <>
                          <Item key={index} value={option.value}>
                            {option.label}
                          </Item>
                        </>
                      ))}
                    </Menu>
                  </Dropdown>
                </Col>
              )}
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button isDanger isPrimary onClick={handleReset}>
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default QuestionAndAnswerFilters;
