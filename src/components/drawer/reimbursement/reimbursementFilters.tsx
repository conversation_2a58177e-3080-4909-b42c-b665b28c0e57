import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import Input from '../../UI-components/Input';
import { Field as _Field } from '../../UI-components/Field';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Field } from '../../UI-components/Field';
import { Col } from '../../UI-components/Grid';
import { useQuery } from '@tanstack/react-query';
import { Dropdown, Multiselect } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { ApprovedByNewOutput, ApprovedByOutput } from '../../../gql/graphql';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { Tag } from '@zendeskgarden/react-tags';
import { DrawerModal } from '@zendeskgarden/react-modals';
import constants from '../../../constants';
import axios from 'axios';
import useToast from '../../../hooks/useToast';
import { useLocation } from 'react-router-dom';
import krakendPaths from '../../../constants/krakendPaths';
import { slaBreachedMapping } from '../new-return-module/ReturnFilter';

interface dateFilter {
  dateFrom?: string | undefined;
  dateTo?: string | undefined;
}

interface filterObject {
  dateFilter?: dateFilter;
  status?: [string] | undefined;
  assignedBy?: [string] | undefined;
  approvedBy?: [string] | undefined;
  assignedTo?: [string] | undefined;
  source?: [string] | undefined;
  payment_method_code?: [string] | undefined;
  reimbursement_type?: [string] | undefined;
  rowsPerPage: number;
  pageNumber: number;
  is_sla_breached?: string;
}

const ReimbursementFilters = ({
  isOpen,
  setIsOpen,
  isreset,
  setIsReset,
  filters,
  setFilters,
  setSearchContent,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isreset: boolean;
  setIsReset: React.Dispatch<React.SetStateAction<boolean>>;
  filters: filterObject;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  setSearchContent: React.Dispatch<React.SetStateAction<any>>;
}) => {
  const close = () => setIsOpen(false);
  const location = useLocation();
  const [assignedBy, setAssignedBy] = useState(filters.assignedBy || []);
  const [approvedBy, setApprovedBy] = useState(filters.approvedBy || []);
  const [assignedTo, setAssignedTo] = useState(filters.assignedTo || []);
  const [status, setStatus] = useState(filters.status || []);
  const [slaBreached, setSlaBreached] = useState<string | undefined>(
    filters.is_sla_breached,
  );

  const [source, setSource] = useState(filters.source || []);
  const [paymentMethod, setPaymentMethod] = useState(
    filters.payment_method_code || [],
  );
  const [reimbursementType, setReimbursementType] = useState(
    filters.reimbursement_type || [],
  );

  const [startDate, setStartDate] = useState<any>(
    filters.dateFilter?.dateFrom
      ? new Date(filters.dateFilter?.dateFrom)
      : null,
  );
  const addToast = useToast();
  const { data, error, refetch, isLoading } = useQuery({
    queryKey: ['reimbursment-filter-config'],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(
        `${krakendPaths.REIMBURSEMENT_URL}/api/v1/admin/reimbursement/configs`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response.data;
    },
    onError: (error) => {
      console.log('config error: ', error);
      addToast('error', `${error}`);
    },
    onSuccess: (data) => {},
  });
  const [endDate, setEndDate] = useState<any>(
    filters.dateFilter?.dateTo ? new Date(filters.dateFilter?.dateTo) : null,
  );

  const resetFilters = () => {
    setAssignedBy([]);
    setStatus([]);
    setSlaBreached(undefined);
    setApprovedBy([]);
    setAssignedTo([]);
    setSource([]);
    setPaymentMethod([]);
    setStartDate(null);
    setEndDate(null);
    setSearchContent('');
    setFilters({
      rows_per_page: 20,
      page_number: 1,
    });
    close();
  };

  const applyFilters = () => {
    setFilters((prev: any) => ({
      ...prev,
      assignedBy: assignedBy.length > 0 ? assignedBy : undefined,
      approvedBy: approvedBy.length > 0 ? approvedBy : undefined,
      assignedTo: assignedTo.length > 0 ? assignedTo : undefined,
      status: status.length > 0 ? status : undefined,
      is_sla_breached: slaBreached,
      source: source.length > 0 ? source : undefined,
      payment_method_code: paymentMethod.length > 0 ? paymentMethod : undefined,
      reimbursement_type:
        reimbursementType.length > 0 ? reimbursementType : undefined,
      dateFilter:
        startDate || endDate
          ? {
              dateFrom: startDate
                ? startDate.toLocaleDateString('en-CA')
                : undefined,
              dateTo: endDate
                ? endDate.toLocaleDateString('en-CA')
                : startDate
                ? new Date().toLocaleDateString('en-CA')
                : undefined,
            }
          : undefined,
      page_number: 1,
    }));
    close();
  };

  useEffect(() => {
    if (isreset) {
      resetFilters();
      setIsReset(false);
    }
  }, [isreset]);

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12}>
                {data?.assigned_by && (
                  <Dropdown
                    selectedItems={assignedBy}
                    onSelect={(items) => setAssignedBy(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DropDownField>
                      <Label>Assigned By</Label>
                      <Multiselect
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DropDownField>
                    <Menu>
                      {data.assigned_by.map(
                        (option: ApprovedByNewOutput) =>
                          option.enable && (
                            <Item key={option.id} value={option.name}>
                              {option.name}
                            </Item>
                          ),
                      )}
                    </Menu>
                  </Dropdown>
                )}
              </Col>
              <Col size={12}>
                {data?.approved_by && (
                  <Dropdown
                    selectedItems={approvedBy}
                    onSelect={(items) => setApprovedBy(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DropDownField>
                      <Label>Approved By</Label>
                      <Multiselect
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DropDownField>
                    <Menu>
                      {data.approved_by.map(
                        (option: ApprovedByNewOutput) =>
                          option.enable && (
                            <Item key={option.id} value={option.name}>
                              {option.name}
                            </Item>
                          ),
                      )}
                    </Menu>
                  </Dropdown>
                )}
              </Col>
              <Col size={12} mt={'sm'}>
                {data?.status && (
                  <Dropdown
                    selectedItems={status}
                    onSelect={(items) => setStatus(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DropDownField>
                      <Label>Status</Label>
                      <Multiselect
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DropDownField>
                    <Menu>
                      {data.status.map(
                        (option: ApprovedByNewOutput) =>
                          option.enable && (
                            <Item key={option.id} value={option.name}>
                              {option.name}
                            </Item>
                          ),
                      )}
                    </Menu>
                  </Dropdown>
                )}
              </Col>
              <Col size={12} mt={'sm'}>
                <Dropdown
                  // selectedItems={status}
                  onSelect={(items) => setSlaBreached(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>SLA Breached</Label>
                    <Select>
                      {slaBreached ? slaBreachedMapping[slaBreached] : ''}
                    </Select>
                  </DropDownField>
                  <Menu>
                    <Item key={2} value={'true'}>
                      Breached
                    </Item>
                    <Item key={3} value={'false'}>
                      Not Breached
                    </Item>
                    <Item key={3} value={'null'}>
                      Pending
                    </Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                {data?.assigned_to && (
                  <Dropdown
                    selectedItems={assignedTo}
                    onSelect={(items) => setAssignedTo(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DropDownField>
                      <Label>Assigned To</Label>
                      <Multiselect
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DropDownField>
                    <Menu>
                      {data.assigned_to.map(
                        (option: ApprovedByNewOutput) =>
                          option.enable && (
                            <Item key={option.id} value={option.name}>
                              {option.name}
                            </Item>
                          ),
                      )}
                    </Menu>
                  </Dropdown>
                )}
              </Col>
              <Col size={12} mt="sm">
                {data?.source && (
                  <Dropdown
                    selectedItems={source}
                    onSelect={(items) => setSource(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DropDownField>
                      <Label>Source</Label>
                      <Multiselect
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DropDownField>
                    <Menu>
                      {data.source.map(
                        (option: ApprovedByNewOutput) =>
                          option.enable && (
                            <Item key={option.id} value={option.name}>
                              {option.name}
                            </Item>
                          ),
                      )}
                    </Menu>
                  </Dropdown>
                )}
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItems={paymentMethod}
                  onSelect={(items) => setPaymentMethod(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>Payment Method</Label>
                    <Multiselect
                      renderItem={({ value, removeValue }: any) => (
                        <Tag>
                          <span>{value}</span>
                          <Tag.Close onClick={() => removeValue()} />
                        </Tag>
                      )}
                    />
                  </DropDownField>
                  <Menu>
                    {['cashondelivery', 'razorpay'].map((option: any) => (
                      <Item key={option} value={option}>
                        {option}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                {data?.reimbursement_type && (
                  <Dropdown
                    selectedItems={reimbursementType}
                    onSelect={(items) => setReimbursementType(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DropDownField>
                      <Label>Reimbursement Type</Label>
                      <Multiselect
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DropDownField>
                    <Menu>
                      {data.reimbursement_type?.map(
                        (option: {
                          name: string;
                          enable: boolean;
                          id: string;
                        }) =>
                          option.enable && (
                            <Item key={option.id} value={option.name}>
                              {option.name}
                            </Item>
                          ),
                      )}
                    </Menu>
                  </Dropdown>
                )}
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Date From</Label>
                <Datepicker value={startDate} onChange={setStartDate}>
                  <Input />
                </Datepicker>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Date To</Label>
                <Datepicker value={endDate} onChange={setEndDate}>
                  <Input />
                </Datepicker>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button isDanger isPrimary onClick={() => setIsReset(true)}>
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default ReimbursementFilters;
