import React, { useEffect, useState } from 'react';
import { Col, Row as _Row } from '@zendeskgarden/react-grid';
import { Paragraph, Span } from '@zendeskgarden/react-typography';
import { DrawerModal } from '@zendeskgarden/react-modals';
import {
  Checkbox,
  Field,
  Input,
  Label,
  MediaInput,
  Textarea,
} from '@zendeskgarden/react-forms';
import {
  CalendarIcon,
  CalenderIcon,
  CrossIcon,
  RemoveBlueIcon,
} from '../../../utils/icons';
import styled from 'styled-components';
import { baseTheme } from '../../../themes/theme';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { Button, Buttons } from '../../UI-components/Button';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { StockAlertFiltersType } from '../../../pages/catalog-service/stock alert/StockAlert';
import useToast from '../../../hooks/useToast';

const Row = styled(_Row)`
  margin: 15px 0px;
`;

const ColSection = styled(Col)`
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
  margin: 10px 10px;
`;

const StockAlertFilterDrawer = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  refetch
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: StockAlertFiltersType;
  setFilters: React.Dispatch<React.SetStateAction<StockAlertFiltersType>>;
  refetch: ()=> void;
}) => {
  const addToast = useToast();
  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const [dateAdded, setDateAdded] = useState(new Date());
  const [createdfrom, setCreatedFrom] = useState<any>();
  const [createdTo, setCreatedTo] = useState<any>();
  const [prodSku, setProdSku] = useState('');
  const [prodName, setProdName] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');

  const dateFormatter = new Intl.DateTimeFormat('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  const applyFilters = () => {
      setFilters((prev: any) => ({
        ...prev,
        sku: prodSku ?? undefined,
        product_name: prodName ?? undefined,
        customer_email: customerEmail ?? undefined,
        customer_name: customerName ?? undefined,
        filters_created_at_from: createdfrom ?? undefined,
        filters_created_at_to: createdTo ?? undefined,
      }));
      setIsOpen(false);
      close();
  };

  const reset =()=>{
    setFilters({
      page: 1,
      size: 20
    });
    refetch();
    close();
  }
  useEffect(() => {
    if (filters.filters_created_at_from) {
      setCreatedFrom(filters.filters_created_at_from);
    }
    if (filters.filters_created_at_to) {
      setCreatedTo(filters.filters_created_at_to);
    }
    if (filters.sku) {
      setProdSku(filters.sku);
    }
    if (filters.product_name) {
      setProdName(filters.product_name);
    }
    if (filters.customer_email) {
      setCustomerEmail(filters.customer_email);
    }
    if (filters.customer_name) {
      setCustomerName(filters.customer_name);
    }
  }, [filters]);
  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal style={{ width: '35%' }} isOpen={isOpen} onClose={close}>
          <DrawerModal.Header
            tag="h2"
            style={{
              display: 'flex',
              backgroundColor: `${baseTheme.colors.deepBlue}`,
              color: 'white',
              justifyContent: 'center',
            }}
          >
            Filters
          </DrawerModal.Header>
          <DrawerModal.Body>
          <Row>
              <Col>
                <ColSection>
                  <Col size={1}>
                    <Label></Label>
                  </Col>
                  <Col>
                    <Label>Updated At</Label>
                  </Col>
                </ColSection>
                <ColSection>
                  <Col size={1}>
                    <Label>From</Label>
                  </Col>
                  <Col>
                    <Datepicker value={createdfrom} onChange={setCreatedFrom}>
                      <MediaInput
                        end={
                          <div
                            style={{
                              alignItems: 'center',
                              display: 'flex',
                              justifyContent: 'center',
                            }}
                          >
                            {createdfrom ? (
                              <CrossIcon
                                style={{ cursor: 'pointer' }}
                                onClick={() => {
                                  setCreatedFrom(undefined);
                                }}
                              />
                            ) : (
                              <CalenderIcon style={{ cursor: 'pointer' }} />
                            )}
                          </div>
                        }
                      />
                    </Datepicker>
                  </Col>
                </ColSection>
                <ColSection>
                  <Col size={1}>
                    <Label>To</Label>
                  </Col>
                  <Col>
                    <Datepicker value={createdTo} onChange={setCreatedTo}>
                      <MediaInput
                        end={
                          <div
                            style={{
                              alignItems: 'center',
                              display: 'flex',
                              justifyContent: 'center',
                            }}
                          >
                            {createdTo ? (
                              <CrossIcon
                                style={{ cursor: 'pointer' }}
                                onClick={() => setCreatedTo(undefined)}
                              />
                            ) : (
                              <CalenderIcon style={{ cursor: 'pointer' }} />
                            )}
                          </div>
                        }
                      />
                    </Datepicker>
                  </Col>
                </ColSection>
              </Col>
            </Row>
            <Row>
              <Col>
                <ColSection>
                  <Col size={1}>
                    <Label></Label>
                  </Col>
                  <Col>
                    <Label>Product SKU</Label>
                  </Col>
                </ColSection>
                <ColSection>
                  <Col size={1}>
                    <Label></Label>
                  </Col>
                  <Col>
                    <Input
                      value={prodSku}
                      onChange={(e) => {
                        setProdSku(e.target.value);
                      }}
                    />
                  </Col>
                </ColSection>
              </Col>
            </Row>

            <Row>
              <Col>
                <ColSection>
                  <Col size={1}>
                    <Label></Label>
                  </Col>
                  <Col>
                    <Label>Product Name</Label>
                  </Col>
                </ColSection>
                <ColSection>
                  <Col size={1}>
                    <Label></Label>
                  </Col>
                  <Col>
                    <Input
                      value={prodName}
                      onChange={(e) => {
                        setProdName(e.target.value);
                      }}
                    />
                  </Col>
                </ColSection>
              </Col>
            </Row>
            <Row>
              <Col>
                <ColSection>
                  <Col size={1}>
                    <Label></Label>
                  </Col>
                  <Col>
                    <Label>Customer Name</Label>
                  </Col>
                </ColSection>
                <ColSection>
                  <Col size={1}>
                    <Label></Label>
                  </Col>
                  <Col>
                    <Input
                      value={customerName}
                      onChange={(e) => {
                        setCustomerName(e.target.value);
                      }}
                    />
                  </Col>
                </ColSection>
              </Col>
            </Row>
            <Row>
              <Col>
                <ColSection>
                  <Col size={1}>
                    <Label></Label>
                  </Col>
                  <Col>
                    <Label>Customer Email</Label>
                  </Col>
                </ColSection>
                <ColSection>
                  <Col size={1}>
                    <Label></Label>
                  </Col>
                  <Col>
                    <Input
                      value={customerEmail}
                      onChange={(e) => {
                        setCustomerEmail(e.target.value);
                      }}
                    />
                  </Col>
                </ColSection>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button onClick={reset}>Reset Filter</Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary isOrange disabled={!(prodName || prodSku || customerEmail || customerName || createdfrom || createdTo)} onClick={applyFilters}>
                Apply
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close style={{ color: 'white' }} />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default StockAlertFilterDrawer;
