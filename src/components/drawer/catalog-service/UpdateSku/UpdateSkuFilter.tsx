import { Col, Row } from '@zendeskgarden/react-grid';
import { DrawerModal } from '@zendeskgarden/react-modals';
import React, { useEffect, useState } from 'react'
import { baseTheme } from '../../../../themes/theme';
import styled from 'styled-components';
import { Input, Label, MediaInput } from '@zendeskgarden/react-forms';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { CalenderIcon, CrossIcon } from '../../../../utils/icons';
import { Button } from '../../../UI-components/Button';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import { FiltersType } from '../../../../pages/catalog-service/UpdateSKUGrid/UpdateSku';
import useToast from '../../../../hooks/useToast';

const ColSection = styled(Col)`
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
  margin: 10px 10px;
`;


const UpdateSkuFilter = ({
  isOpen,
  setIsOpen,
  setFilters,
  filters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setFilters: React.Dispatch<React.SetStateAction<FiltersType>>;
  filters: FiltersType;
  reset: any;
}) => {
  const resetFilters = ()=>{
    reset()
    close();
  }
  const addToast = useToast();

  const close = () => setIsOpen(false);
  const [createdfrom, setCreatedFrom] = useState<any>();
  const [createdTo, setCreatedTo] = useState<any>();
  const [updatedFrom, setUpdatedFrom] = useState<any>();
  const [updatedTo, setUpdatedTo] =  useState<any>();
  const [oldsku, setOldSku] =  useState<any>();
  const [newsku, setNewSku] = useState<any>();
  const [errorSku, setErrorSku] =  useState<any>();

  // status
  const [skuStatus, setSkuStatus] =  useState<any>();
  const [inputSkuStatus, setInputSkuStatus] = useState();
  const handleSelectedStatus = (item: string)=>{
    setSkuStatus(item)
  }
  const handleInputSkuStatusChange = ()=>{

  }

  const applyFilters = () =>{
      setFilters((prev: any)=>({
        ...prev,
        filters_created_at_from: createdfrom,
        filters_created_at_to: createdTo,
        filters_updated_at_from: updatedFrom,
        filters_updated_at_to: updatedTo,
        filters_status: skuStatus ? skuStatus === 'Complete' ? 1 : 0 : undefined,
        old_sku: oldsku,
        new_sku: newsku,
      }))
      close()
    
  }
  useEffect(()=>{
    if(filters.filters_created_at_from){
      setCreatedFrom(filters.filters_created_at_from)
    }
    if(filters.filters_created_at_to){
      setCreatedTo(filters.filters_created_at_to)
    }
    if(filters.filters_status === 1){
      setSkuStatus('Complete');
    }
    if(filters.filters_status === 0){
      setSkuStatus('Fail');
    }
    if(filters.filters_updated_at_from){
      setUpdatedFrom(filters.filters_updated_at_from)
    }
    if(filters.filters_updated_at_to){
      setUpdatedTo(filters.filters_updated_at_to)
    }
    if(filters.old_sku){
      setOldSku(filters.old_sku)
    }
    if(filters.new_sku){
      setNewSku(filters.new_sku)
    }
    // close();
  },[filters])
  return (
    <div>
      <Row>
      <Col textAlign="center">
        <DrawerModal style={{ width: '30%' }} isOpen={isOpen} onClose={close}>
          <DrawerModal.Header
            tag="h2"
            style={{
              display: 'flex',
              backgroundColor: `${baseTheme.colors.deepBlue}`,
              color: 'white',
              justifyContent: 'center',
              fontSize: `${baseTheme.fontSizes.lg}`,
            }}
          >
            Filters
          </DrawerModal.Header>
          <DrawerModal.Body>
            <div>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Created At</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>From</Label>
                    </Col>
                    <Col>
                      <Datepicker
                        value={createdfrom}
                        onChange={setCreatedFrom}
                      >
                        <MediaInput
                          end={
                            <div style={{alignItems:'center', display:'flex', justifyContent: 'center'}}>
                              {createdfrom ? (
                                <CrossIcon
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => {
                                    setCreatedFrom(undefined)
                                  }}
                                />
                              ) : (
                                <CalenderIcon style={{ cursor: 'pointer' }} />
                              )}
                            </div>
                          }
                        />
                      </Datepicker>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>To</Label>
                    </Col>
                    <Col>
                      <Datepicker
                        value={createdTo}
                        onChange={setCreatedTo}
                      >
                        <MediaInput
                          end={
                            <div style={{alignItems:'center', display:'flex', justifyContent: 'center'}}>
                              {createdTo ? (
                                <CrossIcon
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => setCreatedTo(undefined)}
                                />
                              ) : (
                                <CalenderIcon style={{ cursor: 'pointer' }} />
                              )}
                            </div>
                          }
                        />
                      </Datepicker>
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Updated At</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>From</Label>
                    </Col>
                    <Col>
                      <Datepicker
                        value={updatedFrom}
                        onChange={setUpdatedFrom}
                      >
                        <MediaInput
                          end={
                            <div style={{alignItems:'center', display:'flex', justifyContent: 'center'}}>
                              {updatedFrom ? (
                                <CrossIcon
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => {
                                    setUpdatedFrom(undefined)
                                  }}
                                />
                              ) : (
                                <CalenderIcon style={{ cursor: 'pointer' }} />
                              )}
                            </div>
                          }
                        />
                      </Datepicker>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>To</Label>
                    </Col>
                    <Col>
                      <Datepicker
                        value={updatedTo}
                        onChange={setUpdatedTo}
                      >
                        <MediaInput
                          end={
                            <div style={{alignItems:'center', display:'flex', justifyContent: 'center'}}>
                              {updatedTo ? (
                                <CrossIcon
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => setUpdatedTo(undefined)}
                                />
                              ) : (
                                <CalenderIcon style={{ cursor: 'pointer' }} />
                              )}
                            </div>
                          }
                        />
                      </Datepicker>
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Old SKU</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Input
                        value={oldsku}
                        onChange={(e) => {
                          setOldSku(e.target.value);
                        }}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Status</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <ProductDropdown
                        selectedItem={skuStatus}
                        onSelect={handleSelectedStatus}
                        inputValue={inputSkuStatus}
                        onInputValueChange={handleInputSkuStatusChange}
                        options={[ 'Complete', 'Fail']}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              
               
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>New SKU</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Input
                        value={newsku}
                        onChange={(e) => {
                          setNewSku(e.target.value);
                        }}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
             
            </div>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button onClick={resetFilters}>Reset Filter</Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary isOrange
              disabled={!(createdTo || createdfrom || updatedFrom || updatedTo || skuStatus || oldsku || newsku)}
              onClick={applyFilters}>
                Apply Filter
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close style={{ color: 'white' }} />
        </DrawerModal>
      </Col>
    </Row>
    </div>
  )
}

export default UpdateSkuFilter