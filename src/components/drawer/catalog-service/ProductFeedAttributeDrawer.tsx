import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import Input from '../../UI-components/Input';
import {
  Field as DTField,
  Label,
  Input as DTInput,
} from '@zendeskgarden/react-forms';
import {
  Dropdown,
  Field as DropDownField,
  // Field,
  Menu,
  Trigger,
  Item,
  Label as DLabel,
  Select as DSelect,
  Select,
} from '@zendeskgarden/react-dropdowns';
import { Field } from '../../UI-components/Field';
import { Field as _Field } from '../../UI-components/Field';
import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { baseTheme } from '../../../themes/theme';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { format } from 'date-fns';
import useAxios from '../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import krakendPaths from '../../../constants/krakendPaths';
import useToast from '../../../hooks/useToast';

export interface ProductFeedAttributefilterObject {
  page: number;
  rowsPerPage: number;
  generate_feed?: boolean | undefined;
  // product_type?: string | undefined;
  categories?: string | undefined;
  type_id?: string | undefined;
  stock_status?: boolean | undefined;
  magento_stock_status?: boolean | undefined;
  product_visibility?: number | undefined;
  product_status?: number | undefined;
  internationally_enabled?: number | undefined;
  shopping_ads_excul_country?: string | undefined;
  dateFilter: { dateFrom?: string; dateTo?: string };
}

const feedFilters = [
  { label: 'RESET', value: '' },
  { label: 'True', value: 'true' },
  { label: 'False', value: 'false' },
];

const typeIdFilters = [
  { label: 'RESET', value: '' },
  { label: 'Simple', value: 'simple' },
  { label: 'Grouped', value: 'grouped' },
];

const stockStatusFilters = [
  { label: 'RESET', value: '' },
  { label: 'True', value: 'true' },
  { label: 'False', value: 'false' },
];

const magentoStockStatusFilters = [
  { label: 'RESET', value: '' },
  { label: 'True', value: 'true' },
  { label: 'False', value: 'false' },
];

const productVisibilityFilters = [
  { label: 'RESET', value: '' },
  { label: 'Not Visible Individually', value: '1' },
  { label: 'Catalog', value: '2' },
  { label: 'Search', value: '3' },
  { label: 'Catalog, Search', value: '4' },
];

const productStatusFilters = [
  { label: 'RESET', value: '' },
  { label: 'Enabled', value: '1' },
  { label: 'Disabled', value: '2' },
];

const internatinallyEnabledFilters = [
  { label: 'RESET', value: '' },
  { label: 'True', value: '1' },
  { label: 'False', value: '0' },
];

export const ProductFeedAttributeFilters = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: ProductFeedAttributefilterObject;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  reset: () => void;
}) => {
  const close = () => setIsOpen(false);

  const [feed, setFeed] = useState<string | undefined>(
    filters.generate_feed === undefined
      ? undefined
      : filters.generate_feed
      ? 'true'
      : 'false',
  );
  const [productType, setProductType] = useState<string | undefined>(
    filters.categories || undefined,
  );
  const [typeId, setTypeId] = useState<string | undefined>(
    filters.type_id || undefined,
  );
  const [stockStatus, setStockStatus] = useState<string | undefined>(
    filters.stock_status === undefined
      ? undefined
      : filters.stock_status
      ? 'true'
      : 'false',
  );
  const [magentoStockStatus, setMagentoStockStatus] = useState<
    string | undefined
  >(
    filters.magento_stock_status === undefined
      ? undefined
      : filters.magento_stock_status
      ? 'true'
      : 'false',
  );
  const [productVisibility, setProductVisibility] = useState<
    number | undefined
  >(filters.product_visibility || undefined);
  const [productStatus, setProductStatus] = useState<number | undefined>(
    filters.product_status || undefined,
  );
  const [internationalyEnabled, setInternationalyEnabled] = useState<
    number | undefined
  >(filters.internationally_enabled || undefined);
  const [shoppingAds, setShoppingAds] = useState<string | undefined>(
    filters.shopping_ads_excul_country,
  );
  const [prodTypeArr, setProdTypeArr] = useState([]);

  const [startDate, setStartDate] = useState<Date | undefined>(
    filters?.dateFilter?.dateFrom
      ? new Date(filters?.dateFilter?.dateFrom)
      : undefined,
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    filters?.dateFilter?.dateTo
      ? new Date(filters?.dateFilter?.dateTo)
      : undefined,
  );

  const dateFormatter = new Intl.DateTimeFormat('en-GB', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  // const {
  //   error: prodTypeQueryError,
  //   loading: prodTypeQueryLoading,
  //   data: prodTypeQueryData,
  //   refetch: refetchProdTypeData,
  //   // networkStatus,
  // } = useQuery(GET_PRODUCT_TYPE, {
  //   fetchPolicy: 'network-only',
  //   onCompleted: (data) => {
  //     // console.log("data: ", data)
  //     if(data.getProductType){
  //       setProdTypeArr(data.getProductType.filter((el:any) => el.enable));
  //     }else{
  //       setProdTypeArr([]);
  //     }
  //   }
  // });
  const addToast = useToast();
  const axios = useAxios();
  const { data: prodTypeQueryData, isLoading: prodTypeQueryLoading } = useQuery(
    {
      queryKey: ['get-product-feed-config'],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          // 'http://localhost:3002/magazines',
          `${krakendPaths.PRODUCT_FEED_ATTRIBUTE}/admin-api/v1/product-attributes/config/list`,
          {
            // params: {
            //   page: filters.pageNumber,
            //   pageSize: filters?.rowsPerPage,
            // },
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            },
          },
        );
        return response;
      },
      onError: (err: any) => {
        addToast('error', err?.message);
      },
      onSuccess: (data) => {
        if (data) {
          setProdTypeArr(
            data?.product_configurations?.filter((el: any) => el.enable),
          );
        } else {
          setProdTypeArr([]);
        }
      },
    },
  );

  const applyFilters = () => {
    const upDatedFilter: ProductFeedAttributefilterObject = {
      ...filters,
      generate_feed: feed ? (feed === 'true' ? true : false) : undefined,
      // product_type: productType,
      categories: productType,
      type_id: typeId,
      stock_status: stockStatus
        ? stockStatus === 'true'
          ? true
          : false
        : undefined,
      magento_stock_status: magentoStockStatus
        ? magentoStockStatus === 'true'
          ? true
          : false
        : undefined,
      product_visibility: Number(productVisibility) || undefined,
      product_status: Number(productStatus) || undefined,
      internationally_enabled:
        Number(internationalyEnabled) === 1 ||
        Number(internationalyEnabled) === 0
          ? Number(internationalyEnabled)
          : undefined,
      shopping_ads_excul_country: shoppingAds,
      dateFilter: {
        dateFrom: filters?.dateFilter?.dateFrom || undefined,
        dateTo: filters?.dateFilter?.dateTo || undefined,
      },
      rowsPerPage: 20,
      page: 1,
    };

    if (startDate || endDate) {
      if (startDate) {
        upDatedFilter.dateFilter.dateFrom = format(startDate, 'yyyy-MM-dd');
      } else {
        upDatedFilter.dateFilter.dateFrom = '';
      }

      if (upDatedFilter.dateFilter && endDate) {
        upDatedFilter.dateFilter.dateTo = format(endDate, 'yyyy-MM-dd');
      } else {
        upDatedFilter.dateFilter.dateTo = format(new Date(), 'yyyy-MM-dd');
      }
    }

    setFilters(upDatedFilter);
    close();
  };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItem={feed}
                  onSelect={(items) => setFeed(items)}
                  downshiftProps={{
                    itemToString: (item: any) => item,
                  }}
                >
                  <DropDownField>
                    <Label>Generate Feed</Label>
                    <Select>
                      {feed
                        ? feedFilters.find((item: any) => item.value === feed)
                            ?.label
                        : 'Select Generated Feed'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {feedFilters.map((option, index) => (
                      <>
                        <Item key={index} value={option.value}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItem={productType}
                  onSelect={(items) => setProductType(items)}
                  downshiftProps={{
                    itemToString: (item: any) => item,
                  }}
                >
                  <DropDownField>
                    <Label>Product Type</Label>
                    <Select>
                      {productType ? productType : 'Select Product Type'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {prodTypeArr &&
                      prodTypeArr.map((prodType) => {
                        return (
                          <Item value={prodType['name']}>
                            {prodType['name']}
                          </Item>
                        );
                      })}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItem={typeId}
                  onSelect={(items) => setTypeId(items)}
                  downshiftProps={{
                    itemToString: (item: any) => item,
                  }}
                >
                  <DropDownField>
                    <Label>Type Id</Label>
                    <Select>
                      {typeId
                        ? typeIdFilters.find(
                            (item: any) => item.value === typeId,
                          )?.label
                        : 'Select Type Id'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {typeIdFilters.map((option, index) => (
                      <>
                        <Item key={index} value={option.value}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItem={stockStatus}
                  onSelect={(items) => setStockStatus(items)}
                  downshiftProps={{
                    itemToString: (item: any) => item,
                  }}
                >
                  <DropDownField>
                    <Label>Stock Status</Label>
                    <Select>
                      {stockStatus
                        ? stockStatusFilters.find(
                            (item: any) => item.value === stockStatus,
                          )?.label
                        : 'Select Stock Status'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {stockStatusFilters.map((option, index) => (
                      <>
                        <Item key={index} value={option.value}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItem={magentoStockStatus}
                  onSelect={(items) => setMagentoStockStatus(items)}
                  downshiftProps={{
                    itemToString: (item: any) => item,
                  }}
                >
                  <DropDownField>
                    <Label>Magento Stock Status</Label>
                    <Select>
                      {magentoStockStatus
                        ? magentoStockStatusFilters.find(
                            (item: any) => item.value === magentoStockStatus,
                          )?.label
                        : 'Select Magento Stock Status'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {magentoStockStatusFilters.map((option, index) => (
                      <>
                        <Item key={index} value={option.value}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItem={productVisibility}
                  onSelect={(items) => setProductVisibility(Number(items))}
                  downshiftProps={{
                    itemToString: (item: any) => item,
                  }}
                >
                  <DropDownField>
                    <Label>Product Visibility</Label>
                    <Select>
                      {productVisibility
                        ? productVisibilityFilters.find(
                            (item: any) =>
                              Number(item.value) === productVisibility,
                          )?.label
                        : 'Select Product Visibility'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {productVisibilityFilters.map((option, index) => (
                      <>
                        <Item key={index} value={option.value}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItem={productStatus}
                  onSelect={(items) => setProductStatus(Number(items))}
                  downshiftProps={{
                    itemToString: (item: any) => item,
                  }}
                >
                  <DropDownField>
                    <Label>Product Status</Label>
                    <Select>
                      {productStatus
                        ? productStatusFilters.find(
                            (item: any) => Number(item.value) === productStatus,
                          )?.label
                        : 'Select Product Status'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {productStatusFilters.map((option, index) => (
                      <>
                        <Item key={index} value={option.value}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItem={internationalyEnabled}
                  onSelect={(items) => setInternationalyEnabled(Number(items))}
                  downshiftProps={{
                    itemToString: (item: any) => item,
                  }}
                >
                  <DropDownField>
                    <Label>Internationally Enabled</Label>
                    <Select>
                      {internationalyEnabled === 1 ||
                      internationalyEnabled === 0
                        ? internatinallyEnabledFilters.find(
                            (item: any) =>
                              Number(item.value) === internationalyEnabled,
                          )?.label
                        : 'Select Internationally Enabled'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {internatinallyEnabledFilters.map((option, index) => (
                      <>
                        <Item key={index} value={option.value}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Field>
                  <Label>Shopping Ads Excluding Country</Label>
                  <Input
                    onChange={(e) => {
                      setShoppingAds(e.target.value);
                    }}
                    fontSize={baseTheme.fontSizes.md}
                    placeholder="Shopping Ads Excluding Country"
                    defaultValue={filters?.shopping_ads_excul_country}
                  />
                </Field>
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Label>Updated At From</Label>
                <Datepicker
                  value={startDate}
                  onChange={setStartDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Updated At Date'}
                    fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
            </Row>

            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Label>Updated At To</Label>
                <Datepicker
                  value={endDate}
                  onChange={setEndDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Updated At Date'}
                    fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  setFeed(undefined);
                  setProductType(undefined);
                  setTypeId(undefined);
                  setStockStatus(undefined);
                  setMagentoStockStatus(undefined);
                  setProductVisibility(undefined);
                  setProductStatus(undefined);
                  setInternationalyEnabled(undefined);
                  setShoppingAds(undefined);
                  setStartDate(undefined);
                  setEndDate(undefined);
                  setFilters({
                    page: 1,
                    rowsPerPage: 20,
                  });
                  // reset();
                  close();
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
