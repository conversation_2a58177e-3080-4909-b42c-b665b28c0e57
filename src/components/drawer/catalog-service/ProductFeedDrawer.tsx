import React, { useState } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Field as _Field } from '../../UI-components/Field';
import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { GET_Product_Data_Attribute } from '../../../graphql/queries/getProductDataAttribute';
import { Spinner } from '@zendeskgarden/react-loaders';
import { CSVDownload } from 'react-csv';
import useToast from '../../../hooks/useToast';
import useAxios from '../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import krakendPaths from '../../../constants/krakendPaths';

export interface ProductFeedAttributefilterObject {
  page: number;
  rowsPerPage: number;
  generate_feed?: boolean | undefined;
  // product_type?: string | undefined;
  categories?: string | undefined;
  type_id?: string | undefined;
  stock_status?: boolean | undefined;
  magento_stock_status?: boolean | undefined;
  product_visibility?: number | undefined;
  product_status?: number | undefined;
  internationally_enabled?: number | undefined;
  shopping_ads_excul_country?: string | undefined;
  dateFilter: { dateFrom?: string; dateTo?: string };
}

export const ProductFeedDrawer = ({
  isOpen,
  setIsOpen,
  filters,
  haveUpload,
  setUploadModal,
  haveExport,
  haveGenerate,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: ProductFeedAttributefilterObject;
  haveUpload?: boolean;
  setUploadModal?: React.Dispatch<React.SetStateAction<any>>;
  haveExport?: boolean;
  haveGenerate?: boolean;
}) => {
  const addToast = useToast();
  const [allData, setAllData] = useState();
  const [loadingGenerate, setLoadingGenerate] = useState(false);

  const close = () => {
    setAllData(undefined);
    setIsOpen(false);
  };

  const sanitizeString = (obj: any) => {
    for (let key in obj) {
      if (key === 'product_name' || key === 'sku') {
        if (typeof obj[key] === 'string') {
          obj[key] = obj[key].replace(/\"/g, '');
        }
      }
    }

    return obj;
  };

  const sanitizeData = (data: any) => {
    let newData: any = [...data];

    let updatedData = newData.map((obj: any) => {
      return { ...sanitizeString({ ...obj }) };
    });

    return updatedData;
  };

  // const getAllData = async () => {
  //   setLoading(true);

  //   const { data } = await graphqlClient.query({
  //     query: GET_Product_Data_Attribute,
  //     variables: {
  //       ...filters,
  //       page: null,
  //       rowsPerPage: null
  //     },
  //     fetchPolicy: 'cache-first',
  //   });
  //   data && setAllData(sanitizeData(data?.getProductDataAttribute?.result));

  //   setLoading(false);
  // };
  const axios = useAxios();
  const { refetch, isLoading: loading } = useQuery({
    queryKey: ['get-product-feed-attributes', filters],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(
        `${krakendPaths.PRODUCT_FEED_ATTRIBUTE}/admin-api/v1/product-attributes/list`,
        {
          params: {
            ...filters,
          },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    onError: (err: any) => {
      addToast('error', err?.message);
    },
    onSuccess: (data) => {
      data && setAllData(sanitizeData(data?.result));
    },
  });

  const handleDownload = () => {
    setLoadingGenerate(true);
    addToast(
      'info',
      'Feed will be generated within some seconds. Please check your downloads for downloaded csv file.',
    );

    setTimeout(() => {
      setLoadingGenerate(false);
    }, 1000 * 12);
  };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Options</DrawerModal.Header>
          <DrawerModal.Body>
            {haveGenerate && (
              <Row justifyContent="center" alignItems="center">
                <Col size={12} mt={'lg'}>
                  <a href="https://dental-admin.dentalkart.com/generateFeed">
                    <Button onClick={() => handleDownload()}>
                      {loadingGenerate ? <Spinner /> : 'Generate Feed'}
                    </Button>
                  </a>
                </Col>
              </Row>
            )}

            {/* {haveUpload && setUploadModal && (
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'lg'}>
                <Button onClick={() => setUploadModal(true)}>
                  Upload CSV
                </Button>
              </Col>
            </Row>
            )} */}

            {haveExport && (
              <Row justifyContent="center" alignItems="center">
                <Col size={12} mt={'lg'}>
                  <Button onClick={() => refetch()}>
                    {loading ? <Spinner /> : 'Export to CSV'}
                  </Button>
                  {allData && <CSVDownload data={allData} target="_blank" />}
                </Col>
              </Row>
            )}
          </DrawerModal.Body>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
