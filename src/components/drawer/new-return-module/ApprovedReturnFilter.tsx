import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import Input from '../../UI-components/Input';
import { Field as _Field } from '../../UI-components/Field';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Field } from '../../UI-components/Field';
import { Col } from '../../UI-components/Grid';
import { useQuery } from '@tanstack/react-query';
import { Dropdown, Multiselect, Trigger } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { DrawerModal } from '@zendeskgarden/react-modals';
import useToast from '../../../hooks/useToast';
import { IItem } from '../coupon/CouponFilter';

interface dateFilter {
  dateFrom?: string | undefined;
  dateTo?: string | undefined;
}

interface filterObject {
  dateFilter?: dateFilter;
  status?: [string] | undefined;
  assignedBy?: [string] | undefined;
  approvedBy?: [string] | undefined;
  assignedTo?: [string] | undefined;
  source?: [string] | undefined;
  payment_method_code?: [string] | undefined;
  rowsPerPage: number;
  pageNumber: number;
}

const shipmentStatusList: IItem[] = [
  { label: 'PickupPending', value: 'PickupPending' },
  { label: 'PickupFailed', value: 'PickupFailed' },
  { label: 'InTransit', value: 'InTransit' },
  { label: 'Delivered', value: 'Delivered' },
  { label: 'ShipmentDelayed', value: 'ShipmentDelayed' },
  { label: 'OrderPlaced', value: 'OrderPlaced' },
  { label: 'PickedUp', value: 'PickedUp' },
  { label: 'ContactCustomerCare', value: 'ContactCustomerCare' },
  { label: 'Cancelled', value: 'Cancelled' },
  { label: 'ShipmentHeld', value: 'ShipmentHeld' },
];

const refundStatusList: IItem[] = [
  { label: 'Pending', value: 'pending' },
  {
    label: 'Closed~With Pending Bank Details',
    value: 'Closed~With Pending Bank Details',
  },
  { label: 'Pending', value: 'Pending' },
  { label: 'Closed', value: 'Closed' },
  { label: 'Closed~Refunded', value: 'Closed~Refunded' },
  { label: 'Open', value: 'Open' },
];

const ApprovedReturnFilter = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: any;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  reset: () => void;
}) => {
  const close = () => setIsOpen(false);

  const [refundStatus, setRefundStatus] = useState<any>(
    refundStatusList.find(
      (stat) => stat.value == String(filters.refund_status),
    ),
  );
  const [shipmentStatus, setShipmentStatus] = useState<any>(
    shipmentStatusList.find(
      (stat) => stat.value == String(filters.clickpost_status),
    ),
  );

  const applyFilters = () => {
    const newfilter: any = {
      ...filters,
      page: 1,
      size: 20,
    };

    if (shipmentStatus) {
      newfilter.clickpost_status = shipmentStatus.value;
    }

    if (refundStatus) {
      newfilter.refund_status = refundStatus.value;
    }

    setFilters((prev: any) => ({
      ...prev,
      ...newfilter,
    }));

    close();
  };
  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItem={shipmentStatus}
                  onSelect={(items) => setShipmentStatus(items)}
                  downshiftProps={{
                    itemToString: (item: any) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Shipment Status</Label>
                    <Select>
                      {shipmentStatus
                        ? shipmentStatus.label
                        : 'Select Shipment Status'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {shipmentStatusList.map((option, index) => (
                      <Item key={index} value={option}>
                        {option.label}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>

              <Col size={12} mt="md">
                <Dropdown
                  selectedItem={refundStatus}
                  onSelect={(items) => setRefundStatus(items)}
                  downshiftProps={{
                    itemToString: (item: any) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Refund Status</Label>
                    <Select>
                      {refundStatus
                        ? refundStatus.label
                        : 'Select Refund Status'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {refundStatusList.map((option, index) => (
                      <Item key={index} value={option}>
                        {option.label}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button isDanger isPrimary onClick={reset}>
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default ApprovedReturnFilter;
