import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import Input from '../../UI-components/Input';
import { Field as _Field } from '../../UI-components/Field';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Field } from '../../UI-components/Field';
import { Col } from '../../UI-components/Grid';
import { Dropdown, Multiselect } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { ApprovedByOutput } from '../../../gql/graphql';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { Tag } from '@zendeskgarden/react-tags';
import { baseTheme } from '../../../themes/theme';
import { format, parseISO } from 'date-fns';
import { useQuery } from '@tanstack/react-query';
import { IReturnAssignee } from '../../../types/types';
import useAxios from '../../../hooks/useAxios';
import { FilterProp } from '../../layouts/new-return-module/ReturnLayout';
import AssigneeDropdown from '../../dropdown/new-return-module/AssigneeDropdown';
import { useGetReturnAssignees } from '../../../hooks/useReturn';

interface IItem {
  label: string;
  value: string;
}

export const slaBreachedMapping: Record<string, string> = {
  true: 'Breached',
  false: 'Not Breached',
  null: 'Pending',
};

const refundItem = [
  { label: 'Full', value: 'full' },
  { label: 'Partial', value: 'partial' },
];

const orderByItem = [
  { label: 'Latest First', value: '-created_at' },
  { label: 'Oldest First', value: 'created_at' },
];

const statuses = [
  { label: 'Close', value: 'close' },
  { label: 'Open', value: 'open' },
  { label: 'Partial Open', value: 'partial_open' },
];

const ReturnFilter = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: FilterProp;
  setFilters: React.Dispatch<React.SetStateAction<FilterProp>>;
  reset: () => void;
}) => {
  const close = () => setIsOpen(false);

  const [orderBy, setorderBy] = useState<IItem>();
  const [assignedTo, setAssignedTo] = useState<IReturnAssignee>();
  const [status, setStatus] = useState<string>();
  const [slaBreached, setSlaBreached] = useState<string | undefined>();
  const [refundStatus, setRefundStatus] = useState<IItem>();
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [email, setEmail] = useState<string>();

  const dateFormatter = new Intl.DateTimeFormat('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  const {
    data: assignees,
    isLoading: isAssigneeLoading,
    refetch: reloadReturnAssignees,
  } = useGetReturnAssignees();

  useEffect(() => {
    if (filters.status) {
      setStatus(status);
    } else {
      setStatus(undefined);
    }

    if (filters.refund_status) {
      const selectedRefundItem = refundItem.find(
        (item) => item.value === filters.refund_status,
      );
      setRefundStatus(selectedRefundItem);
    } else {
      setRefundStatus(undefined);
    }

    //ymd -> mdy

    if (filters.created_at_from) {
      const selectedDate = new Date(
        format(parseISO(filters.created_at_from), 'MM/dd/yyyy'),
      );
      setStartDate(selectedDate);
    } else {
      setStartDate(undefined);
    }

    if (filters.created_at_to) {
      const selectedDate = new Date(
        format(parseISO(filters.created_at_to), 'MM/dd/yyyy'),
      );
      setEndDate(selectedDate);
    } else {
      setEndDate(undefined);
    }

    // if (filters.current_assignee) {
    //   const selectedAssignee = assignees.find(
    //     (item) => item.value === filters.current_assignee,
    //   );
    //   setAssignedTo(selectedAssignee);
    // } else {
    //   setAssignedTo(undefined);
    // }

    if (filters.status && filters.status.length != 0) {
      setStatus(filters.status);
    } else {
      setStatus(undefined);
    }

    if (filters.is_sla_breached) {
      setSlaBreached(filters.is_sla_breached);
    } else {
      setSlaBreached(undefined);
    }

    if (filters.order_by) {
      const selectedOrder = orderByItem.find(
        (item) => item.value === filters.order_by,
      );
      setorderBy(selectedOrder);
    } else {
      setorderBy(undefined);
    }

    if (filters.customer_email) {
      setEmail(filters.customer_email);
    } else {
      setEmail(undefined);
    }
  }, [filters]);

  const applyFilters = () => {
    setFilters((prev: FilterProp) => {
      const updatedFilters: FilterProp = {
        ...prev,
      };

      if (email) {
        updatedFilters.customer_email = email;
      }

      if (refundStatus) {
        updatedFilters.refund_status = refundStatus.value;
      }

      if (assignedTo) {
        updatedFilters.current_assignee = assignedTo.id;
      }

      if (startDate) {
        updatedFilters.created_at_from = format(startDate, 'yyyy-MM-dd');
      }

      if (endDate) {
        updatedFilters.created_at_to = format(endDate, 'yyyy-MM-dd');
      }

      if (status) {
        updatedFilters.status = status;
      }

      if (orderBy) {
        updatedFilters.order_by = orderBy.value;
      }
      if (slaBreached) {
        updatedFilters.is_sla_breached = slaBreached;
      }
      return updatedFilters;
    });
    // setTimeout(() => {
    //   close();
    // }, 500);
    close();
  };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt={'sm'}>
                <Dropdown
                  // selectedItems={status}
                  onSelect={(items) => setStatus(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>Status</Label>
                    <Select>{status ? status : 'Choose Status'}</Select>
                    {/* <Multiselect
                      placeholder={'Choose Status'}
                      renderItem={({ value, removeValue }: any) => (
                        <Tag>
                          <span>{value}</span>
                          <Tag.Close onClick={() => removeValue()} />
                        </Tag>
                      )}
                    /> */}
                  </DropDownField>
                  <Menu>
                    {statuses.map((option, index) => (
                      <>
                        <Item key={1} value={option.value}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                    {/* 
                    <Item key={2} value={'open'}>
                      Open
                    </Item>
                    <Item key={3} value={'partial_open'}>
                      Partial Open
                    </Item> */}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt={'sm'}>
                <Dropdown
                  // selectedItems={status}
                  onSelect={(items) => setSlaBreached(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>SLA Breached</Label>
                    <Select>
                      {slaBreached ? slaBreachedMapping[slaBreached] : 'Select'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    <Item key={2} value={'true'}>
                      Breached
                    </Item>
                    <Item key={3} value={'false'}>
                      Not Breached
                    </Item>
                    <Item key={3} value={'null'}>
                      Pending
                    </Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItem={refundStatus}
                  onSelect={(items) => setRefundStatus(items)}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Refund Status</Label>
                    <Select>
                      {refundStatus ? refundStatus.label : 'Select Status'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {refundItem.map((option, index) => (
                      <>
                        <Item key={index} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt="sm">
                <Label>Assigned To</Label>

                <AssigneeDropdown
                  data={assignees}
                  selectedItem={assignedTo}
                  setSelectedItem={setAssignedTo}
                />
              </Col>
              <Col size={12} mt="sm">
                <Dropdown
                  selectedItem={orderBy}
                  onSelect={setorderBy}
                  downshiftProps={{
                    itemToString: (item: IItem) => item && item.label,
                  }}
                >
                  <DropDownField>
                    <Label>Order by</Label>
                    <Select>{orderBy ? orderBy.label : 'Order by'}</Select>
                  </DropDownField>
                  <Menu>
                    {orderByItem.map((option, index) => (
                      <>
                        <Item key={index} value={option}>
                          {option.label}
                        </Item>
                      </>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Order From</Label>
                <Datepicker
                  value={startDate}
                  onChange={setStartDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Order Date'}
                    fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Order To</Label>
                <Datepicker
                  value={endDate}
                  onChange={setEndDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Order Date'}
                    fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
              <Col size={12} mt="sm">
                <Field>
                  <Label>Customer Email</Label>
                  <Input
                    onChange={(e) => {
                      setEmail(e.target.value);
                    }}
                    fontSize={baseTheme.fontSizes.md}
                    placeholder="Customer Email"
                    defaultValue={filters?.customer_email}
                  />
                </Field>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  reset && reset();
                  close();
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default ReturnFilter;
