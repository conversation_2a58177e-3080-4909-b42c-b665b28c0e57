import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import Input from '../../UI-components/Input';
import { Field as _Field } from '../../UI-components/Field';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Field } from '../../UI-components/Field';
import { Col } from '../../UI-components/Grid';
import { useQuery } from '@tanstack/react-query';
import { Dropdown, Multiselect } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { ApprovedByNewOutput, ApprovedByOutput } from '../../../gql/graphql';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { Tag } from '@zendeskgarden/react-tags';
import { DrawerModal } from '@zendeskgarden/react-modals';
import constants from '../../../constants';
import axios from 'axios';
import useToast from '../../../hooks/useToast';
import { useLocation } from 'react-router-dom';
import krakendPaths from '../../../constants/krakendPaths';
import { ExtraMaterialReceivedFilters } from '../../layouts/new-return-module/ExtraMterialReceivedLayout';
import { format } from 'date-fns';

interface dateFilter {
  dateFrom?: string | undefined;
  dateTo?: string | undefined;
}

interface filterObject {
  dateFilter?: dateFilter;
  status?: [string] | undefined;
  assignedBy?: [string] | undefined;
  approvedBy?: [string] | undefined;
  assignedTo?: [string] | undefined;
  source?: [string] | undefined;
  payment_method_code?: [string] | undefined;
  rowsPerPage: number;
  pageNumber: number;
}

const ExtraMaterialReceivedFilter = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: ExtraMaterialReceivedFilters;
  setFilters: React.Dispatch<
    React.SetStateAction<ExtraMaterialReceivedFilters>
  >;
}) => {
  const close = () => setIsOpen(false);
  const addToast = useToast();

  const [status, setStatus] = useState(filters.status || []);
  const [currentStatus, setCurrentStatus] = useState(
    filters.current_status || [],
  );
  const [startDate, setStartDate] = useState<any>(
    filters?.date_from ? new Date(filters?.date_from) : null,
  );
  const [endDate, setEndDate] = useState<any>(
    filters?.date_to ? new Date(filters?.date_to) : null,
  );

  const applyFilters = () => {
    const filter: ExtraMaterialReceivedFilters = {
      page: 1,
      size: 20,
    };
    if (startDate && endDate && startDate > endDate) {
      addToast('warning', 'Date to must be greater than or equal to From Date');
      return;
    }
    if (status.length > 0) {
      filter.status = status;
    }

    if (currentStatus.length > 0) {
      filter.current_status = currentStatus;
    }

    if (startDate) {
      filter.date_from = format(startDate, 'yyyy-MM-dd');
    }
    if (endDate) {
      filter.date_to = format(endDate, 'yyyy-MM-dd');
    }
    setFilters((prev: any) => ({
      ...prev,
      ...filter,
    }));

    close();
  };
  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12}>
                <Dropdown
                  selectedItems={status}
                  onSelect={(items) => setStatus(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>Status</Label>
                    <Multiselect
                      renderItem={({ value, removeValue }: any) => (
                        <Tag>
                          <span>{value}</span>
                          <Tag.Close onClick={() => removeValue()} />
                        </Tag>
                      )}
                    />
                  </DropDownField>
                  <Menu>
                    <Item key={crypto.randomUUID()} value="open">
                      Open
                    </Item>
                    <Item key={crypto.randomUUID()} value="closed">
                      Closed
                    </Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mt={'sm'}>
                <Dropdown
                  selectedItems={currentStatus}
                  onSelect={(items) => setCurrentStatus(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>Current Status</Label>
                    <Multiselect
                      renderItem={({ value, removeValue }: any) => (
                        <Tag>
                          <span>{value}</span>
                          <Tag.Close onClick={() => removeValue()} />
                        </Tag>
                      )}
                    />
                  </DropDownField>
                  <Menu>
                    <Item key={crypto.randomUUID()} value="open">
                      Open
                    </Item>
                    <Item key={crypto.randomUUID()} value="closed">
                      Closed
                    </Item>
                  </Menu>
                </Dropdown>
              </Col>

              <Col size={12} mt={'sm'}>
                <Label>Date From</Label>
                <Datepicker value={startDate} onChange={setStartDate}>
                  <Input />
                </Datepicker>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Date To</Label>
                <Datepicker value={endDate} onChange={setEndDate}>
                  <Input />
                </Datepicker>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() =>
                  setFilters({
                    page: 1,
                    size: 20,
                  })
                }
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default ExtraMaterialReceivedFilter;
