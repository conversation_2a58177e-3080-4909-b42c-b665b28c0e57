import { Datepicker } from '@zendeskgarden/react-datepickers';
import {
  Dropdown,
  Field as D<PERSON>ield,
  Label as DLabe<PERSON>,
  Multiselect,
  Menu,
  Item,
} from '@zendeskgarden/react-dropdowns';
import { Input, Label } from '@zendeskgarden/react-forms';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Tag } from '@zendeskgarden/react-tags';
import React, { useEffect, useState } from 'react';
import { Button } from '../../UI-components/Button';
import { baseTheme } from '../../../themes/theme';
import { format, parseISO } from 'date-fns';
import { Col, Row } from '../../UI-components/Grid';
import { filterObject } from '../../table/new-return-modules/ReturnDTO';

// interface filterObject {
//   curr_status?: string[] | undefined;
//   rowsPerPage: number;
//   pageNumber: number;
//   dto_open_date?: string | null;
//   status?: string[] | undefined;
// }

const ReturnDTOFilter = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
  refetch,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: filterObject;
  setFilters: React.Dispatch<React.SetStateAction<filterObject>>;
  reset: () => void;
  refetch: () => void;
}) => {
  const close = () => setIsOpen(false);

  const [openDate, setOpenDate] = useState<Date | undefined>(undefined);
  const [currentStatus, setCurrentStatus] = useState<string[]>([]);
  const [status, setStatus] = useState<string[]>([]);

  const dateFormatter = new Intl.DateTimeFormat('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  useEffect(() => {
    console.log('Filters', filters);
    if (filters.curr_status) {
      setCurrentStatus(filters.curr_status);
    } else {
      setCurrentStatus([]);
    }

    if (filters.status) {
      setStatus(filters.status);
    } else {
      setStatus([]);
    }

    //ymd -> mdy
    if (filters.dto_open_date) {
      const selectedDate = new Date(
        format(parseISO(filters.dto_open_date), 'MM/dd/yyyy'),
      );
      setOpenDate(selectedDate);
    } else {
      setOpenDate(undefined);
    }
  }, [filters]);

  const applyFilters = () => {
    setFilters((prev: filterObject) => {
      const updatedFilters: filterObject = {
        ...prev,
      };

      if (currentStatus) {
        updatedFilters.curr_status = [...currentStatus];
        updatedFilters.page = 1;
      }

      if (openDate) {
        updatedFilters.dto_open_date = format(openDate, 'yyyy-MM-dd');
        updatedFilters.page = 1;
      } else {
        updatedFilters.dto_open_date = null;
      }

      if (status.length > 0 && status) {
        updatedFilters.status = [...status];
        updatedFilters.page = 1;
      }

      return updatedFilters;
    });

    setTimeout(() => {
      close();
    }, 500);
  };

  return (
    <>
      <Row>
        <Col textAlign="center">
          <DrawerModal isOpen={isOpen} onClose={close}>
            <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
            <DrawerModal.Body>
              <Row justifyContent="center" alignItems="center">
                <Col mt="sm" size={12}>
                  <Dropdown
                    selectedItems={status}
                    onSelect={(items) => setStatus(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DField>
                      <DLabel>Status</DLabel>
                      <Multiselect
                        placeholder={'Choose Status'}
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DField>
                    <Menu>
                      <Item key={1} value={'close'}>
                        Closed
                      </Item>
                      <Item key={2} value={'open'}>
                        Open
                      </Item>
                    </Menu>
                  </Dropdown>
                </Col>
                <Col mt="sm" size={12}>
                  <Dropdown
                    selectedItems={currentStatus}
                    onSelect={(items) => setCurrentStatus(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DField>
                      <DLabel>Current Status</DLabel>
                      <Multiselect
                        placeholder={'Choose Current Status'}
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DField>
                    <Menu>
                      <Item key={1} value={'redispatch'}>
                        Redispatch
                      </Item>
                      <Item key={2} value={'refund'}>
                        Refund
                      </Item>
                      <Item key={2} value={'in progress'}>
                        In Progress
                      </Item>
                    </Menu>
                  </Dropdown>
                </Col>

                <Col mt="sm" size={12}>
                  <Label>DTO Package Open Date</Label>
                  <Datepicker
                    value={openDate}
                    onChange={setOpenDate}
                    formatDate={(date) => dateFormatter.format(date)}
                  >
                    <Input placeholder={'Choose Package Open Date'} />
                  </Datepicker>
                </Col>
              </Row>
            </DrawerModal.Body>
            <DrawerModal.Footer>
              <DrawerModal.FooterItem>
                <Button
                  isDanger
                  isPrimary
                  onClick={() => {
                    reset && reset();
                    close();
                  }}
                >
                  Reset Filters
                </Button>
              </DrawerModal.FooterItem>
              <DrawerModal.FooterItem>
                <Button isPrimary onClick={applyFilters}>
                  Apply Filters
                </Button>
              </DrawerModal.FooterItem>
            </DrawerModal.Footer>
            <DrawerModal.Close />
          </DrawerModal>
        </Col>
      </Row>
    </>
  );
};

export default ReturnDTOFilter;
