import { Datepicker } from '@zendeskgarden/react-datepickers';
import {
  Dropdown,
  Field as DField,
  Label as DLabe<PERSON>,
  Multiselect,
  Menu,
  Item,
} from '@zendeskgarden/react-dropdowns';
import { Input, Label } from '@zendeskgarden/react-forms';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Tag } from '@zendeskgarden/react-tags';
import React, { useEffect, useState } from 'react';
import { Button } from '../../UI-components/Button';
import { baseTheme } from '../../../themes/theme';
import { format, parseISO } from 'date-fns';
import { Col, Row } from '../../UI-components/Grid';
import { filterObject } from '../../table/vin-order/VinOrderAllData';

const VinOrderFilter = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: filterObject;
  setFilters: React.Dispatch<React.SetStateAction<filterObject>>;
  reset: () => void;
}) => {
  const close = () => setIsOpen(false);

  const [orderStatus, setOrderStatus] = useState<string[]>([]);
  const [startDate, setStartDate] = useState<Date | undefined>(
    filters.dateFilter.dateFrom
      ? new Date(filters.dateFilter.dateFrom)
      : undefined,
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    filters.dateFilter.dateTo ? new Date(filters.dateFilter.dateTo) : undefined,
  );

  const dateFormatter = new Intl.DateTimeFormat('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  useEffect(() => {
    console.log('Filters', filters);
    if (filters.order_status) {
      setOrderStatus(filters.order_status);
    } else {
      setOrderStatus([]);
    }

    if (filters.dateFilter.dateFrom) {
      setStartDate(new Date(filters.dateFilter.dateFrom));
    } else {
      setStartDate(undefined);
    }

    if (filters.dateFilter.dateTo) {
      setEndDate(new Date(filters.dateFilter.dateTo));
    } else {
      setEndDate(undefined);
    }

  }, [filters]);

  const applyFilters = () => {
    setFilters((prev: filterObject) => {
      const updatedFilters: filterObject = {
        ...prev,
        pageNumber: 1,
      };

      if (orderStatus) {
        updatedFilters.order_status = [...orderStatus];
      }

      if (startDate) {
        updatedFilters.dateFilter.dateFrom = format(startDate, 'yyyy-MM-dd');
      } else {
        updatedFilters.dateFilter.dateFrom = '';
      }

      if (updatedFilters.dateFilter && endDate) {
        updatedFilters.dateFilter.dateTo = format(endDate, 'yyyy-MM-dd');
      } else {
        updatedFilters.dateFilter.dateTo = format(new Date(), 'yyyy-MM-dd');
      }

      return updatedFilters;
    });

    setTimeout(() => {
      close();
    }, 500);
  };

  return (
    <>
      <Row>
        <Col textAlign="center">
          <DrawerModal isOpen={isOpen} onClose={close}>
            <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
            <DrawerModal.Body>
              <Row justifyContent="center" alignItems="center">
                <Col mt="sm" size={12}>
                  <Dropdown
                    selectedItems={orderStatus}
                    onSelect={(items) => setOrderStatus(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DField>
                      <DLabel>Order Status</DLabel>
                      <Multiselect
                        placeholder={'Choose Order Status'}
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DField>
                    <Menu>
                      <Item key={1} value={'new_order'}>
                        New Order
                      </Item>
                      <Item key={2} value={'payment_received'}>
                        Payment Recieved
                      </Item>
                      <Item key={3} value={'pending'}>
                        Pending
                      </Item>
                    </Menu>
                  </Dropdown>
                </Col>
                <Col size={12} mt={'sm'}>
                <Label>Created At From</Label>
                <Datepicker
                  value={startDate}
                  onChange={setStartDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Created At Date'}
                    // fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Created At To</Label>
                <Datepicker
                  value={endDate}
                  onChange={setEndDate}
                  formatDate={(date) => dateFormatter.format(date)}
                >
                  <Input
                    placeholder={'Choose Created At Date'}
                    // fontSize={baseTheme.fontSizes.md}
                  />
                </Datepicker>
              </Col>

                {/* <Col mt="sm" size={12}>
                  <Label>DTO Package Open Date</Label>
                  <Datepicker
                    value={openDate}
                    onChange={setOpenDate}
                    formatDate={(date) => dateFormatter.format(date)}
                  >
                    <Input placeholder={'Choos Package Open Date'} />
                  </Datepicker>
                </Col> */}
              </Row>
            </DrawerModal.Body>
            <DrawerModal.Footer>
              <DrawerModal.FooterItem>
                <Button
                  isDanger
                  isPrimary
                  onClick={() => {
                    reset && reset();
                    close();
                  }}
                >
                  Reset Filters
                </Button>
              </DrawerModal.FooterItem>
              <DrawerModal.FooterItem>
                <Button isPrimary onClick={applyFilters}>
                  Apply Filters
                </Button>
              </DrawerModal.FooterItem>
            </DrawerModal.Footer>
            <DrawerModal.Close />
          </DrawerModal>
        </Col>
      </Row>
    </>
  );
};

export default VinOrderFilter;
