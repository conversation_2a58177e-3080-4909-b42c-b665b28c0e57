import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import { Field as _Field } from '../../UI-components/Field';
import { Dropdown } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Select } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { ProductFilterTypes } from '../../../types/types';
import { Row, Col } from '../../UI-components/Grid';
import { Label } from '@zendeskgarden/react-forms';
import Input from '../../UI-components/Input';
import styled from 'styled-components';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { baseTheme } from '../../../themes/theme';
import { format } from 'date-fns';
import { convertToSentenceCase } from '../../../utils/convertToSentenceCase';
import { BrandFilters } from '../../../pages/catalog-service/brand/BrandContext';
export interface DropDownTypes {
  label: string;
  value: number;
}

export const DropDownItem: DropDownTypes[] = [
  { label: '1', value: 1 },
  { label: '2', value: 2 },
  { label: '3', value: 3 },
  { label: '4', value: 4 },
  { label: '5', value: 5 },
];
const BrandFilterDrawer = ({
  isOpen,
  setIsOpen,
  setFilters,
  filters,
  reset,
  setReset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setFilters: React.Dispatch<React.SetStateAction<BrandFilters>>;
  filters: BrandFilters;
  reset: boolean;
  setReset: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const close = () => setIsOpen(false);

  const [selectedIsActive, setSelectedIsActive] = useState<string | undefined>(
    undefined,
  );
  const [selectedIsFeatured, setSelectedIsFeatured] = useState<
    string | undefined
  >(undefined);
  const [selectedIsInternational, setSelectedIsInternational] = useState<
    string | undefined
  >(undefined);
  const [selectedIsDKSuggested, setSelectedIsDKSuggested] = useState<
    string | undefined
  >(undefined);
  const [selectedIsTop, setSelectedIsTop] = useState<string | undefined>(
    undefined,
  );
  const [selectedIsNewlyAdded, setSelectedIsNewlyAdded] = useState<
    string | undefined
  >(undefined);

  const statuses = ['Select', 'True', 'False'];
  const applyFilters = () => {
    setFilters((prev: BrandFilters) => {
      const updatedFilters: BrandFilters = {
        ...prev,
        is_active:
          selectedIsActive === 'True'
            ? true
            : selectedIsActive == 'False'
            ? false
            : undefined,
        is_featured:
          selectedIsFeatured === 'True'
            ? true
            : selectedIsFeatured == 'False'
            ? false
            : undefined,
        is_international:
          selectedIsInternational === 'True'
            ? true
            : selectedIsInternational == 'False'
            ? false
            : undefined,
        is_dk_suggest:
          selectedIsDKSuggested === 'True'
            ? true
            : selectedIsDKSuggested == 'False'
            ? false
            : undefined,
        is_newly_added:
          selectedIsNewlyAdded === 'True'
            ? true
            : selectedIsNewlyAdded == 'False'
            ? false
            : undefined,
        is_top:
          selectedIsTop === 'True'
            ? true
            : selectedIsTop == 'False'
            ? false
            : undefined,
      };

      return updatedFilters;
    });

    setIsOpen(false);
  };

  const resetFilters = () => {
    setFilters({});
    setSelectedIsActive(undefined);
    setSelectedIsDKSuggested(undefined);
    setSelectedIsFeatured(undefined);
    setSelectedIsInternational(undefined);
    setSelectedIsNewlyAdded(undefined);
    setSelectedIsTop(undefined);
  };
  useEffect(() => {
    if (reset) {
      resetFilters();
      setReset(false);
    }
  }, [reset]);

  useEffect(() => {
    const setBooleanFilter = (
      filterValue: boolean | undefined,
      setState: (value: string | undefined) => void,
    ) => {
      setState(
        filterValue === true
          ? 'True'
          : filterValue === false
          ? 'False'
          : undefined,
      );
    };

    setBooleanFilter(filters.is_active, setSelectedIsActive);
    setBooleanFilter(filters.is_dk_suggest, setSelectedIsDKSuggested);
    setBooleanFilter(filters.is_international, setSelectedIsInternational);
    setBooleanFilter(filters.is_newly_added, setSelectedIsNewlyAdded);
    setBooleanFilter(filters.is_featured, setSelectedIsFeatured);
    setBooleanFilter(filters.is_top, setSelectedIsTop);
  }, []);

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mb="md">
                <Label>Is Active</Label>
                <div
                  style={{
                    marginTop: `${baseTheme.components.dimension.width.base}px`,
                  }}
                >
                  {}
                </div>
                <Dropdown
                  selectedItem={selectedIsActive}
                  onSelect={(item: any) => {
                    if (item === 'Select') {
                      setSelectedIsActive(undefined);
                    } else {
                      setSelectedIsActive(item);
                    }
                  }}
                  downshiftProps={{
                    itemToString: (item: string) => item,
                  }}
                >
                  <DropDownField>
                    <Select>
                      {selectedIsActive
                        ? convertToSentenceCase(selectedIsActive)
                        : 'Select Is Active'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {statuses.map((option) => (
                      <Item key={option} value={option}>
                        {convertToSentenceCase(option)}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mb="md">
                <Label>Is Featured</Label>
                <div
                  style={{
                    marginTop: `${baseTheme.components.dimension.width.base}px`,
                  }}
                >
                  {}
                </div>
                <Dropdown
                  selectedItem={selectedIsFeatured}
                  onSelect={(item: any) => {
                    if (item === 'Select') {
                      setSelectedIsFeatured(undefined);
                    } else {
                      setSelectedIsFeatured(item);
                    }
                  }}
                  downshiftProps={{
                    itemToString: (item: string) => item,
                  }}
                >
                  <DropDownField>
                    <Select>
                      {selectedIsFeatured
                        ? convertToSentenceCase(selectedIsFeatured)
                        : 'Select Fetaured'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {statuses.map((option) => (
                      <Item key={option} value={option}>
                        {convertToSentenceCase(option)}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mb="md">
                <Label>Is Dentalkart Suggested</Label>
                <div
                  style={{
                    marginTop: `${baseTheme.components.dimension.width.base}px`,
                  }}
                >
                  {}
                </div>
                <Dropdown
                  selectedItem={selectedIsDKSuggested}
                  onSelect={(item: any) => {
                    if (item === 'Select') {
                      setSelectedIsDKSuggested(undefined);
                    } else {
                      setSelectedIsDKSuggested(item);
                    }
                  }}
                  downshiftProps={{
                    itemToString: (item: string) => item,
                  }}
                >
                  <DropDownField>
                    <Select>
                      {selectedIsDKSuggested
                        ? convertToSentenceCase(selectedIsDKSuggested)
                        : 'Select Is Dentalkart Suggest'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {statuses.map((option) => (
                      <Item key={option} value={option}>
                        {convertToSentenceCase(option)}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mb="md">
                <Label>Is Top</Label>
                <div
                  style={{
                    marginTop: `${baseTheme.components.dimension.width.base}px`,
                  }}
                >
                  {}
                </div>
                <Dropdown
                  selectedItem={selectedIsTop}
                  onSelect={(item: any) => {
                    if (item === 'Select') {
                      setSelectedIsTop(undefined);
                    } else {
                      setSelectedIsTop(item);
                    }
                  }}
                  downshiftProps={{
                    itemToString: (item: string) => item,
                  }}
                >
                  <DropDownField>
                    <Select>
                      {selectedIsTop
                        ? convertToSentenceCase(selectedIsTop)
                        : 'Select Is Top'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {statuses.map((option) => (
                      <Item key={option} value={option}>
                        {convertToSentenceCase(option)}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mb="md">
                <Label>Is Newly Added</Label>
                <div
                  style={{
                    marginTop: `${baseTheme.components.dimension.width.base}px`,
                  }}
                >
                  {}
                </div>
                <Dropdown
                  selectedItem={selectedIsNewlyAdded}
                  onSelect={(item: any) => {
                    if (item === 'Select') {
                      setSelectedIsNewlyAdded(undefined);
                    } else {
                      setSelectedIsNewlyAdded(item);
                    }
                  }}
                  downshiftProps={{
                    itemToString: (item: string) => item,
                  }}
                >
                  <DropDownField>
                    <Select>
                      {selectedIsNewlyAdded
                        ? convertToSentenceCase(selectedIsNewlyAdded)
                        : 'Select Is Newly Added'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {statuses.map((option) => (
                      <Item key={option} value={option}>
                        {convertToSentenceCase(option)}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
              <Col size={12} mb="md">
                <Label>Is International</Label>
                <div
                  style={{
                    marginTop: `${baseTheme.components.dimension.width.base}px`,
                  }}
                >
                  {}
                </div>
                <Dropdown
                  selectedItem={selectedIsInternational}
                  onSelect={(item: any) => {
                    if (item === 'Select') {
                      setSelectedIsInternational(undefined);
                    } else {
                      setSelectedIsInternational(item);
                    }
                  }}
                  downshiftProps={{
                    itemToString: (item: string) => item,
                  }}
                >
                  <DropDownField>
                    <Select>
                      {selectedIsInternational
                        ? convertToSentenceCase(selectedIsInternational)
                        : 'Select Is International'}
                    </Select>
                  </DropDownField>
                  <Menu>
                    {statuses.map((option) => (
                      <Item key={option} value={option}>
                        {convertToSentenceCase(option)}
                      </Item>
                    ))}
                  </Menu>
                </Dropdown>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button isDanger isPrimary onClick={resetFilters}>
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button
                isPrimary
                onClick={applyFilters}
                disabled={
                  !(
                    selectedIsActive ||
                    selectedIsDKSuggested ||
                    selectedIsFeatured ||
                    selectedIsNewlyAdded ||
                    selectedIsTop ||
                    selectedIsInternational
                  )
                }
              >
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default BrandFilterDrawer;
