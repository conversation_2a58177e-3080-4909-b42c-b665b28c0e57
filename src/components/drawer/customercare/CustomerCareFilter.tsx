import React, { useState } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import Input from '../../UI-components/Input';
import { Field as _Field } from '../../UI-components/Field';
import { Label } from '../../UI-components/Label';
import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import { useQuery, gql } from '@apollo/client';
import { Dropdown, Multiselect } from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { ApprovedByOutput } from '../../../gql/graphql';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { Tag } from '@zendeskgarden/react-tags';
import { DrawerModal } from '@zendeskgarden/react-modals';
import constants from '../../../constants';

const GET_STATUS = gql`
  {
    getStatus {
      _id
      name
      enable
    }
  }
`;
const GET_SOURCE = gql`
  {
    getSource {
      _id
      name
      enable
    }
  }
`;

const GET_ASSIGNED_TO = gql`
  {
    getAssignedTo {
      _id
      name
      enable
    }
  }
`;
const GET_ASSIGNED_BY = gql`
  {
    getAssignedBy {
      _id
      name
      enable
    }
  }
`;

interface dateFilter {
  dateFrom?: string | undefined;
  dateTo?: string | undefined;
}

interface filterObject {
  dateFilter?: dateFilter;
  status?: [string] | undefined;
  assignedBy?: [string] | undefined;
  assignedTo?: [string] | undefined;
  source?: [string] | undefined;
  rowsPerPage: number;
  pageNumber: number;
}

const CustomerCareFilter = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: filterObject;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {
  const close = () => setIsOpen(false);

  const [assignedBy, setAssignedBy] = useState(filters.assignedBy || []);
  const [assignedTo, setAssignedTo] = useState(filters.assignedTo || []);
  const [status, setStatus] = useState(filters.status || []);
  const [source, setSource] = useState(filters.source || []);
  const [startDate, setStartDate] = useState<any>(
    filters.dateFilter?.dateFrom
      ? new Date(filters.dateFilter?.dateFrom)
      : null,
  );
  const [endDate, setEndDate] = useState<any>(
    filters.dateFilter?.dateTo ? new Date(filters.dateFilter?.dateTo) : null,
  );

  const { data: assignedByData } = useQuery(GET_ASSIGNED_BY, {
    context: {
      headers: {
        'x-api-key': constants.API_KEY,
      },
    },
    fetchPolicy: 'network-only',
  });

  const { data: assignedToData } = useQuery(GET_ASSIGNED_TO, {
    context: {
      headers: {
        'x-api-key': constants.API_KEY,
      },
    },
    fetchPolicy: 'network-only',
  });

  const { data: statusData } = useQuery(GET_STATUS, {
    context: {
      headers: {
        'x-api-key': constants.API_KEY,
      },
    },
    fetchPolicy: 'network-only',
  });

  const { data: sourceData } = useQuery(GET_SOURCE, {
    context: {
      headers: {
        'x-api-key': constants.API_KEY,
      },
    },
    fetchPolicy: 'network-only',
  });
  const applyFilters = () => {
    setFilters((prev: any) => ({
      ...prev,
      assignedBy: assignedBy.length > 0 ? assignedBy : undefined,
      assignedTo: assignedTo.length > 0 ? assignedTo : undefined,
      status: status.length > 0 ? status : undefined,
      source: source.length > 0 ? source : undefined,
      dateFilter:
        startDate || endDate
          ? {
              dateFrom: startDate
                ? startDate.toLocaleDateString('en-CA')
                : undefined,
              dateTo: endDate
                ? endDate.toLocaleDateString('en-CA')
                : startDate
                ? new Date().toLocaleDateString('en-CA')
                : undefined,
            }
          : undefined,
      pageNumber: 1,
    }));
  };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12}>
                {assignedByData && assignedByData.getAssignedBy && (
                  <Dropdown
                    selectedItems={assignedBy}
                    onSelect={(items) => setAssignedBy(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DropDownField>
                      <Label>Assigned By</Label>
                      <Multiselect
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DropDownField>
                    <Menu>
                      {assignedByData.getAssignedBy.map(
                        (option: ApprovedByOutput) =>
                          option.enable && (
                            <Item key={option._id} value={option.name}>
                              {option.name}
                            </Item>
                          ),
                      )}
                    </Menu>
                  </Dropdown>
                )}
              </Col>
              <Col size={12} mt={'sm'}>
                {statusData && statusData.getStatus && (
                  <Dropdown
                    selectedItems={status}
                    onSelect={(items) => setStatus(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DropDownField>
                      <Label>Status</Label>
                      <Multiselect
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DropDownField>
                    <Menu>
                      {statusData.getStatus.map(
                        (option: ApprovedByOutput) =>
                          option.enable && (
                            <Item key={option._id} value={option.name}>
                              {option.name}
                            </Item>
                          ),
                      )}
                    </Menu>
                  </Dropdown>
                )}
              </Col>
              <Col size={12} mt="sm">
                {assignedToData && assignedToData.getAssignedTo && (
                  <Dropdown
                    selectedItems={assignedTo}
                    onSelect={(items) => setAssignedTo(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DropDownField>
                      <Label>Assigned To</Label>
                      <Multiselect
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DropDownField>
                    <Menu>
                      {assignedToData.getAssignedTo.map(
                        (option: ApprovedByOutput) =>
                          option.enable && (
                            <Item key={option._id} value={option.name}>
                              {option.name}
                            </Item>
                          ),
                      )}
                    </Menu>
                  </Dropdown>
                )}
              </Col>
              <Col size={12} mt="sm">
                {sourceData && sourceData.getSource && (
                  <Dropdown
                    selectedItems={source}
                    onSelect={(items) => setSource(items)}
                    downshiftProps={{ defaultHighlightedIndex: 0 }}
                  >
                    <DropDownField>
                      <Label>Source</Label>
                      <Multiselect
                        renderItem={({ value, removeValue }: any) => (
                          <Tag>
                            <span>{value}</span>
                            <Tag.Close onClick={() => removeValue()} />
                          </Tag>
                        )}
                      />
                    </DropDownField>
                    <Menu>
                      {sourceData.getSource.map(
                        (option: ApprovedByOutput) =>
                          option.enable && (
                            <Item key={option._id} value={option.name}>
                              {option.name}
                            </Item>
                          ),
                      )}
                    </Menu>
                  </Dropdown>
                )}
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Date From</Label>
                <Datepicker value={startDate} onChange={setStartDate}>
                  <Input />
                </Datepicker>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Date To</Label>
                <Datepicker value={endDate} onChange={setEndDate}>
                  <Input />
                </Datepicker>
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button isDanger isPrimary onClick={close}>
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default CustomerCareFilter;
