import { useState } from 'react';
import FormTable from '../../table/generic-form/FormTable';

const FormLayout = ({
    columns,
    rows,
    count,
    // refetch,
    filters,
    setFilters,
}: {
    columns: [];
    rows: [];
    count: number;
    // refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>(
        undefined,
    );
    // Add more objects as needed

    return (
        <>
            <FormTable
                data={rows}
                columns={columns}
                count={count}
                // refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default FormLayout;
