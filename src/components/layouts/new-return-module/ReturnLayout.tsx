import { useEffect, useState } from 'react';
import { IPagination } from '../../../types/types';
import ReturnTable from '../../table/new-return-modules/ReturnTable';
import useAxios from '../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import { baseTheme } from '../../../themes/theme';
import LazyLoading from '../../UI-components/LazyLoading';
import useToast from '../../../hooks/useToast';
import constants from '../../../constants';
import buildQueryParams from '../../../utils/helper/buildQueryParams';
import krakendPaths from '../../../constants/krakendPaths';

export interface FilterProp {
  created_at_from?: string | undefined;
  created_at_to?: string | undefined;
  status?: string | undefined;
  refund_status?: string | undefined;
  current_assignee?: number | undefined;
  customer_email?: string | undefined;
  order_by?: string;
  rowsPerPage?: number;
  pageNumber: number;
  order_id?: string;
  return_id?: number;
  is_sla_breached?: string;
}

const ReturnLayout = () => {
  const addToast = useToast();
  const [page, setPage] = useState<number>(1);
  // const [totalPages, setTotalPages] = useState(0);
  // const [returnData, setReturnData] = useState<any[]>([]);
  const [paginatedResult, setPaginatedResult] = useState<IPagination>();

  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );
  const [searchType, setsearchType] = useState<'order_id' | 'return_id'>(
    'order_id',
  );
  const regex = /^[\d-]+$/;
  const [breachVal, setBreachVal] = useState<any[]>([]);

  const [filters, setFilters] = useState<FilterProp>({
    pageNumber: 1,
    rowsPerPage: 50,
  });

  useEffect(() => {
    const savedFilters = localStorage.getItem('newReturnModuleReturnFilter');
    if (savedFilters) {
      setFilters(JSON.parse(savedFilters));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem(
      'newReturnModuleReturnFilter',
      JSON.stringify(filters),
    );
  }, [filters]);

  const axios = useAxios();
  const {
    data: returnData,
    isLoading: loading,
    isRefetching,
    refetch,
    error,
    isFetching,
  } = useQuery({
    queryKey: ['returnList', filters],
    queryFn: async () => {
      const params = buildQueryParams({
        ...filters,
        page: filters.pageNumber,
        size: filters.rowsPerPage,
      } as any);
      const response = await axios.get(
        `${krakendPaths.RETURN_URL}/admin-api/v1/returns?${params}`,
        {
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response;
    },
    onSuccess: (data) => {
      const returnHolders = data;
      const paginatedRes = {
        count: (returnHolders as any).total,
        next: null,
        previous: null,
      };
      // setTotalPages((returnHolders as any).total);
      setPaginatedResult(paginatedRes);
    },
    onError: (error: any) => {
      console.log(error);
      addToast('error', error?.message || 'Error occured !!');
    },
  });

  useEffect(() => {
    if (
      searchType === 'return_id' &&
      searchContent != null &&
      searchContent != undefined &&
      searchContent != ''
    ) {
      if (searchContent && regex.test(searchContent)) {
        // refetchReturnSearch();
      } else {
        addToast('info', 'Please provide valid Return ID');
      }
    } else if (
      searchType === 'order_id' &&
      searchContent != null &&
      searchContent != undefined &&
      searchContent != ''
    ) {
      if (searchContent && regex.test(searchContent)) {
        // refetchOrderSearch();
      } else {
        addToast('info', 'Please provide valid Order ID');
      }
    } else {
      refetch();
    }
  }, [filters]);

  useEffect(() => {
    baseTheme.colors.primaryHue = baseTheme.colors.primaryHue;
  }, []);

  return (
    <>
      {loading || isRefetching || isFetching ? (
        <LazyLoading />
      ) : (
        <>
          {returnData ? (
            <ReturnTable
              data={returnData.data as any}
              page={page}
              setPage={setPage}
              count={(returnData as any).total as any}
              searchContent={searchContent}
              setSearchContent={setSearchContent}
              pagination={paginatedResult}
              refetch={refetch}
              filters={filters}
              setFilters={setFilters}
              searchType={searchType}
              setsearchType={setsearchType}
              isFetching={loading}
              isRefetching={loading}
              breachedValue={breachVal}
            />
          ) : (
            <LazyLoading />
          )}
        </>
      )}
    </>
  );
};

export default ReturnLayout;
