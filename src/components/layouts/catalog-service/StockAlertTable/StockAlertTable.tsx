import React, { useEffect, useState } from 'react';
import { Button, Buttons } from '../../../UI-components/Button';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import { useNavigate } from 'react-router-dom';
import { DownIcon, FilterIcon, LeftArrowIcon, RightArrowIcon } from '../../../../utils/icons';
import { pageRoutes } from '../../../navigation/RouteConfig';
import styled from 'styled-components'; 
import { StockAlertTableColumns as columns, StockAlertColumns, UpdateSKUColumns } from '../../../table/product/Columns'; 
import { baseTheme, colors } from '../../../../themes/theme';
import { Span } from '@zendeskgarden/react-typography';
import NothingToshow from '../../../UI-components/NothingToShow';
import { TableContainer, TableHolder } from '../../../UI-components/Table';
import { DataTable } from '../../../table/product/DataTable';
import {
  Dropdown,
  Item,
  Menu,
  Trigger,
} from '@zendeskgarden/react-dropdowns';
import { Pagination } from '../../../UI-components/Pagination';
import StockAlertFilterDrawer from '../../../drawer/catalog-service/StockAlertFilterDrawer';
import { StockAlertFiltersType } from '../../../../pages/catalog-service/stock alert/StockAlert';


const Row = styled(_Row)`
  margin: 20px 0px;
`;

const StockAlertTable = (
    {
        data,
        filters,
        setFilters,
        count,
        page,
        setPage,
        table
      }: {
        data: StockAlertColumns[];
        filters: StockAlertFiltersType;
        setFilters: React.Dispatch<React.SetStateAction<StockAlertFiltersType>>;
        count: number;
        page: number;
        setPage: React.Dispatch<React.SetStateAction<number>>;
        table: any
      }
) => {
    const navigate = useNavigate();
  const [rotated, setRotated] = useState<boolean | undefined>();
  const [isOpen, setIsOpen] = useState(false);
  const [isFilterOpen , setIsFilterOpen] = useState(false);
  
  const handleRowPerPage = (item: string) => {
    const pageSize = Number(item);
    setFilters((prev: any) => ({
      ...prev,
      limit: pageSize,
      page: 1,
    }));
    table.setPageSize(pageSize);
  };
    useEffect(() => {
      table.setPageSize(Number(filters.size));
    }, []);
  return (
    <>
        <TableContainer>
        <TableHolder>
          {table.getRowModel()?.rows?.length ? (
            <DataTable table={table} columns={columns} data={data} />
          ) : (
            <NothingToshow divHeight="55vh" />
          )}
        </TableHolder>
        {table.getRowModel()?.rows?.length && count > 0 ? (
                <div style={{ overflowX: 'clip' }}>
                  <Row
                    style={{
                      height: `${
                        baseTheme.components.dimension.width.base * 5
                      }px`,
                      marginTop: baseTheme.space.sm,
                      backgroundColor: baseTheme.colors.white,
                      paddingLeft: baseTheme.space.lg,
                      paddingRight: baseTheme.space.lg,
                    }}
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="start" alignItems="center">
                        <Button
                          style={{
                            maxWidth:
                              baseTheme.components.dimension.width.base100,
                          }}
                          size="medium"
                          isAction
                          disabled={page <= 1 ? true : false}
                          onClick={() => {
                            setFilters((prev: any) => ({
                              ...prev,
                              page: prev.page - 1,
                            }));
                          }}
                        >
                          <Button.StartIcon>
                            <LeftArrowIcon />
                          </Button.StartIcon>
                          Previous
                        </Button>
                      </Row>
                    </Col>
                    <Col textAlign="center" lg={2} md={2}>
                      <Dropdown
                        onSelect={(item) => handleRowPerPage(item)}
                        onStateChange={(options) =>
                          Object.hasOwn(options, 'isOpen') &&
                          setRotated(options.isOpen)
                        }
                      >
                        <Trigger>
                          <Button size="medium" isAction>
                            Row Per Page:
                            <Span style={{ paddingLeft: baseTheme.space.sm }}>
                              {table.getState().pagination.pageSize}
                            </Span>
                            <Button.EndIcon
                              isRotated={rotated}
                              style={{ marginLeft: 0 }}
                            >
                              <DownIcon />
                            </Button.EndIcon>
                          </Button>
                        </Trigger>
                        <Menu>
                          <Item value={20}>20</Item>
                          <Item value={50}>50</Item>
                          <Item value={100}>100</Item>
                        </Menu>
                      </Dropdown>
                    </Col>
                    <Col lg={5} md={5}>
                      <Row justifyContent="center" alignItems="center">
                        <Pagination
                          color={baseTheme.colors.deepBlue}
                          totalPages={Math.ceil(
                            count / table.getState().pagination.pageSize,
                          )}
                          pagePadding={2}
                          currentPage={filters.page}
                          onChange={(e) => {
                            setFilters((prev: any) => ({
                              ...prev,
                              page: prev.page - 1,
                            }));
                          }}
                        />
                      </Row>
                    </Col>
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="start" alignItems="end">
                      <Button size="medium" isAction style={{ cursor: 'default' }}>
                        {(filters.page - 1) * table.getState().pagination.pageSize +
                          1}
                        -
                        {count < filters.page * table.getState().pagination.pageSize
                          ? count
                          : filters.page * table.getState().pagination.pageSize}{' '}
                        of {count}
                      </Button>
                      </Row>
                    </Col>
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="end" alignItems="end">
                        <Button
                          style={{
                            maxWidth: baseTheme.components.dimension.width.base100,
                          }}
                          size="medium"
                          isAction
                          disabled={
                            filters.page >=
                            Math.ceil(count / table.getState().pagination.pageSize)
                              ? true
                              : false
                          }
                          onClick={() =>
                            setFilters((prev: any) => ({
                              ...prev,
                              page: prev.page + 1,
                            }))
                          }
                        >
                          Next
                          <Button.EndIcon>
                            <RightArrowIcon />
                          </Button.EndIcon>
                        </Button> 
                      </Row>
                    </Col>
                  </Row>
                </div>
              ) : (
                <>{''}</>
              )}
        </TableContainer>
    </>
  )
}

export default StockAlertTable