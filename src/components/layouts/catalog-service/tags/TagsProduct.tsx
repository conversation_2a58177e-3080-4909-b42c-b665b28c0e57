import React, { useEffect, useState } from 'react';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import {
  AttachmentProductsColumns,
  TagProductTableColumns as columns,
  TagColumns,
  TagProductsColumns,
} from '../../../table/product/Columns';
import { TableHolder } from '../../../UI-components/Table';
import { baseTheme, colors } from '../../../../themes/theme';
import NothingToshow from '../../../UI-components/NothingToShow';
import { DataTable } from '../../../table/product/DataTable';
import { Row, Col } from '../../../UI-components/Grid';
import { Dropdown, Item, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import { Pagination } from '../../../UI-components/Pagination';
import { Button } from '../../../UI-components/Button';
import {
  DownIcon,
  FilterIcon,
  LeftArrowIcon,
  RightArrowIcon,
  SearchIcon,
} from '../../../../utils/icons';
import { Span } from '@zendeskgarden/react-typography';
import styled from 'styled-components';
import SearchInput from '../../../search-input/SearchInput';
import TagsFilterDrawer from './TagsFilterDrawer';
import CategorySearchModal from '../../../modal/catalog-service/CategorySearchModal';
import { Product } from '../../../modal/catalog-service/types';
import useAxios from '../../../../hooks/useAxios';
import useToast from '../../../../hooks/useToast';
import { useMutation, useQuery } from '@tanstack/react-query';
import ProductSearchModal from '../../../modal/catalog-service/Attachment/ProductSearchModal';
import { IconButton } from '../../../UI-components/IconButton';
import { MediaInput } from '../../../UI-components/MediaInput';
import routes from '../../../../constants/routes';
import { useTagContext } from '../../../../pages/catalog-service/Tags/TagsContext';
import constants from '../../../../constants';
import TagProductFilterDrawer from './TagProductFilterDrawer';
import krakendPaths from '../../../../constants/krakendPaths';

const TopBarDiv = styled.div`
  background-color: ${colors.white};
  padding-bottom: ${(p) => p.theme.space.md};
  padding-left: ${(p) => p.theme.space.md};
  padding-right: ${(p) => p.theme.space.md};
`;

export const TableContainer = styled.div`
  overflow-x: auto;
  border-radius: ${baseTheme.borderRadii.xs};
  background-color: white;
  height: 65vh;
  @media (max-height: ${baseTheme.breakpoints.md}) {
    height: 74vh;
  }
`;
interface TagFilters {
  id?: number;
  status?: boolean;
  sku?: string;
  name?: string;
  visibility?: number;
  page: number;
  limit: number;
}

const TagsProduct = ({ prodId }: { prodId: number | undefined }) => {
  const queryParams = new URLSearchParams(location.search);
  const [filters, setFilters] = useState<TagFilters>({
    page:
      Number(queryParams.get('page')) > 0 ? Number(queryParams.get('page')) : 1,
    limit: 20,
  });
  const [rotated, setRotated] = useState<boolean | undefined>();
  const [isSearch, setIsSearch] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const { refetchTagProduct, setRefetchTagProduct } = useTagContext();

  const axios = useAxios();
  const addToast = useToast();

  const [tagProd, setTagProd] = useState<TagProductsColumns[]>([]);
  const [count, setCount] = useState(0);
  const [search, setSearch] = useState('');
  const { data, refetch: refetchTagProd } = useQuery({
    queryKey: ['get-tag-products'],
    queryFn: async (): Promise<any> => {
      if (prodId) {
        return await axios.get(
          `${krakendPaths.CATALOG_URL}/admin-api/v1/tags/${prodId}/products`,
          {
            params: { ...filters },
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
            },
          },
        );
      }
      return {};
    },
    enabled: !!prodId,
    onError: (err: any) => {
      addToast('error', `${err?.response?.data?.message}`);
    },
    onSuccess: (data) => {
      setTagProd(data?.products);
      setCount(data?.count);
    },
  });

  const table = useReactTable({
    data: tagProd,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  });

  const { mutate, isLoading: isAdding } = useMutation(
    async (products: AttachmentProductsColumns[]) => {
      const response = await axios.post(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/tags/${prodId}/products`,
        {
          addProductIds: products.map(
            (product: AttachmentProductsColumns) => product.id,
          ),
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );

      return response;
    },
    {
      onSuccess(data) {
        addToast('success', 'product added successfully');
        refetchTagProd();
        setIsSearch(false);
      },
      onError(error) {
        addToast('error', (error as any)?.message);
        setIsSearch(false);
      },
    },
  );
  const { mutate: removeMutation, isLoading: isDeleting } = useMutation(
    async (products: TagProductsColumns[]) => {
      const response = await axios.patch(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/tags/${prodId}/products`,
        {
          removeProductIds: products.map(
            (product: TagProductsColumns) => product.id,
          ),
          thumbnail: '',
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );

      return response;
    },
    {
      onSuccess(data) {
        addToast('success', 'product removed successfully');
        // refetch();
        setIsSearch(false);
      },
      onError(error) {
        addToast('error', (error as any)?.message);
        setIsSearch(false);
      },
    },
  );
  const handleRowPerPage = (item: string) => {
    const pageSize = Number(item);
    setFilters((prev: any) => ({
      ...prev,
      limit: pageSize,
      page: 1,
    }));
    table.setPageSize(pageSize);
  };

  useEffect(() => {
    refetchTagProd();
  }, [refetchTagProduct, filters]);

  const reset = () => {
    setFilters({
      limit: 20,
      page: 1,
    });
  };
  const handleSearch = () => {
    setIsSearch(true);
  };

  useEffect(() => {
    table.setPageSize(Number(filters.limit));
  }, []);

  return (
    <div>
      <TopBarDiv>
        <Row>
          <Col size={3}>
            <MediaInput
              placeholder="Search Product to Add"
              onClick={() => {
                setIsSearch(true);
              }}
              value={search}
              onChange={() => {
                setIsSearch(true);
              }}
              end={
                <IconButton>
                  <SearchIcon />
                </IconButton>
              }
            />
          </Col>
          <Col
            style={{
              display: 'flex',
              justifyContent: 'end',
              alignItems: 'center',
              gap: '10px',
            }}
          >
            <Button
              size="medium"
              isAction
              onClick={() => {
                setIsOpen(true);
              }}
            >
              <Button.StartIcon>
                <FilterIcon />
              </Button.StartIcon>
              <Span>Filter</Span>
            </Button>
          </Col>
        </Row>
      </TopBarDiv>
      <>
        <TableContainer>
          <TableHolder>
            {count > 0 ? (
              <DataTable table={table} columns={columns} data={tagProd} />
            ) : (
              <NothingToshow divHeight="55vh" />
            )}
          </TableHolder>
          {count > 0 ? (
            <div style={{ overflowX: 'clip' }}>
              <Row
                style={{
                  height: `${baseTheme.components.dimension.width.base * 5}px`,
                  marginTop: baseTheme.space.sm,
                  backgroundColor: baseTheme.colors.white,
                  paddingLeft: baseTheme.space.lg,
                  paddingRight: baseTheme.space.lg,
                }}
                justifyContent="center"
                alignItems="center"
              >
                <Col lg={1.5} md={1.5}>
                  <Row justifyContent="start" alignItems="center">
                    <Button
                      style={{
                        maxWidth: baseTheme.components.dimension.width.base100,
                      }}
                      size="medium"
                      isAction
                      disabled={filters.page <= 1 ? true : false}
                      onClick={() => {
                        setFilters((prev: any) => ({
                          ...prev,
                          page: prev.page - 1,
                        }));
                      }}
                    >
                      <Button.StartIcon>
                        <LeftArrowIcon />
                      </Button.StartIcon>
                      Previous
                    </Button>
                  </Row>
                </Col>
                <Col textAlign="center" lg={2} md={2}>
                  <Dropdown
                    onSelect={(item) => handleRowPerPage(item)}
                    onStateChange={(options) =>
                      Object.hasOwn(options, 'isOpen') &&
                      setRotated(options.isOpen)
                    }
                  >
                    <Trigger>
                      <Button size="medium" isAction>
                        Row Per Page:
                        <Span style={{ paddingLeft: baseTheme.space.sm }}>
                          {table.getState().pagination.pageSize}
                        </Span>
                        <Button.EndIcon
                          isRotated={rotated}
                          style={{ marginLeft: 0 }}
                        >
                          <DownIcon />
                        </Button.EndIcon>
                      </Button>
                    </Trigger>
                    <Menu>
                      <Item value={20}>20</Item>
                      <Item value={50}>50</Item>
                      <Item value={100}>100</Item>
                    </Menu>
                  </Dropdown>
                </Col>
                <Col lg={5} md={5}>
                  <Row justifyContent="center" alignItems="center">
                    <Pagination
                      color={baseTheme.colors.deepBlue}
                      totalPages={Math.ceil(
                        count / table.getState().pagination.pageSize,
                      )}
                      pagePadding={2}
                      currentPage={page}
                      onChange={(e) => {
                        setFilters((prev: any) => ({ ...prev, page: e }));
                      }}
                    />
                  </Row>
                </Col>
                <Col lg={1.5} md={1.5}>
                  <Row justifyContent="start" alignItems="end">
                    <Button
                      size="medium"
                      isAction
                      style={{ cursor: 'default' }}
                    >
                      {(filters.page - 1) *
                        table.getState().pagination.pageSize +
                        1}
                      -
                      {count <
                      filters.page * table.getState().pagination.pageSize
                        ? count
                        : filters.page *
                          table.getState().pagination.pageSize}{' '}
                      of {count}
                    </Button>
                  </Row>
                </Col>
                <Col lg={1.5} md={1.5}>
                  <Row justifyContent="end" alignItems="end">
                    <Button
                      style={{
                        maxWidth: baseTheme.components.dimension.width.base100,
                      }}
                      size="medium"
                      isAction
                      disabled={
                        filters.page >=
                        Math.ceil(count / table.getState().pagination.pageSize)
                          ? true
                          : false
                      }
                      onClick={() =>
                        setFilters((prev: any) => ({
                          ...prev,
                          page: prev.page + 1,
                        }))
                      }
                    >
                      Next
                      <Button.EndIcon>
                        <RightArrowIcon />
                      </Button.EndIcon>
                    </Button>
                  </Row>
                </Col>
              </Row>
            </div>
          ) : (
            <>{''}</>
          )}
        </TableContainer>
      </>
      {isOpen && (
        <TagProductFilterDrawer
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          setFilters={setFilters}
          filters={filters}
          reset={reset}
        />
      )}
      {isSearch && (
        <ProductSearchModal
          close={() => {
            setIsSearch(false);
          }}
          loading={isAdding}
          setProducts={(products) => {
            if (products && products.length > 0 && prodId) mutate(products);
          }}
        />
      )}
    </div>
  );
};

export default TagsProduct;
