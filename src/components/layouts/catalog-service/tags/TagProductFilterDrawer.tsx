import React, { useEffect, useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import { Input, Label, MediaInput } from '@zendeskgarden/react-forms';
import { DrawerModal } from '@zendeskgarden/react-modals';
import styled from 'styled-components';
import { Button } from '../../../UI-components/Button';
import { baseTheme } from '../../../../themes/theme';
import { AttachmentFilters } from '../../../../pages/catalog-service/attachment/Attachment';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { CalenderIcon, CrossIcon } from '../../../../utils/icons';
import { useTagContext } from '../../../../pages/catalog-service/Tags/TagsContext';


const ColSection = styled(Col)`
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
  margin: 10px 10px;
`;

interface TagProductFilters {
  id?: number;
  status?: boolean;
  sku?: string;
  name?: string;
  visibility?: number;
  page: number;
  limit: number;
}

const TagProductFilterDrawer =({
  isOpen,
  setIsOpen,
  setFilters,
  filters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setFilters: React.Dispatch<React.SetStateAction<TagProductFilters>>;
  filters: TagProductFilters;
  reset: any;
}) => {
  const [id, setId] = useState<any>();
  const {visibilityData, visibilityList} = useTagContext();
  // const [idFrom, setIdFrom] = useState<any>();
  // const [idTo, setIdTo] = useState<any>();
  const [sku, setSku] = useState<any>();
  const [tagName, setTagName] = useState<any>();
  const [prodId, setProdId] = useState<any>();
  const [status, setStatus] = useState<any>();
  const [typeId, setTypeId] = useState<any>();

  // visibility
  const [visibility, setVisibility] = useState<any>();
   const [inputVisibility, setInputVisibility] = useState('');
   const handleSelectVisibility = (item: string) => {
     if (item === 'Select visibility') {
       setVisibility(undefined);
     } else {
       setVisibility(item);
     }
   };
   const handleInputVisibilityChange = (value: string) => {
     setInputVisibility(value);
   };
 
   useEffect(()=>{
     const selectedVisibility = visibilityData?.find(
       (val:any) => val.id === filters.visibility,
     );
     const visibleValue = selectedVisibility?.value;
     setVisibility(visibleValue);
   },[visibilityData])

  const resetFilters = () => {
    reset();
    setIsOpen(false);
  };

  const applyFilters = () => {
    setIsOpen(false);
    const selectedVisibility = visibilityData?.find(
      (val:any) => val.value === visibility,
    );
    const visibleId = selectedVisibility?.id; 
    setFilters({
      ...filters,
      id: id ? Number(id) : undefined,
      sku: sku,
      name: tagName,
      visibility: visibleId ?? undefined,
      status: status ? status === 'Enabled' ? true : false : undefined,
    });
  };
  const close = () => setIsOpen(false);

  useEffect(() => {
    if(filters.id){
      setId(filters.id)
    }
    if(filters.name){
      setTagName(filters.name);
    }
    if (filters.status!=undefined) {
      setStatus(filters.status === true ? "Enabled" : 'Disabled');
    }
    if(filters.sku){
      setSku(filters.sku);
    }
  }, []);
  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal style={{ width: '30%' }} isOpen={isOpen} onClose={close}>
          <DrawerModal.Header
            tag="h2"
            style={{
              display: 'flex',
              backgroundColor: `${baseTheme.colors.deepBlue}`,
              color: 'white',
              justifyContent: 'center',
              fontSize: `${baseTheme.fontSizes.lg}`,
            }}
          >
            Filters
          </DrawerModal.Header>
          <DrawerModal.Body>
            <div>
            <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Id</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Input type='number' value={id} onChange={(e)=>{setId(e.target.value)}}/>
                    </Col>
                  </ColSection>
                </Col>
              </Row>
            <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Status</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <ProductDropdown
                        options={['Enabled', 'Disabled']}
                        selectedItem={status}
                        onSelect={(item: string) => {
                            setStatus(item);
                        }}
                        inputValue={undefined}
                        onInputValueChange={function (value: any): void {
                          throw new Error('Function not implemented.');
                        }}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Name</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Input
                        value={tagName}
                        onChange={(e) => {
                          setTagName(e.target.value);
                        }}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                  <Label>Visibility</Label>
                  </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                   <ProductDropdown
                        selectedItem={visibility}
                        onSelect={handleSelectVisibility}
                        inputValue={inputVisibility}
                        onInputValueChange={handleInputVisibilityChange}
                        options={visibilityList}
                      />
                   </Col>
                   </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>SKU</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Input
                        value={sku}
                        onChange={(e) => {
                          setSku(e.target.value);
                        }}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
            </div>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button onClick={resetFilters}>Reset Filter</Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary isOrange
              disabled={!(id || visibility || tagName || sku || status)}
               onClick={applyFilters}>
                Apply Filter
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close style={{ color: 'white' }} />
        </DrawerModal>
      </Col>
    </Row>
  );
}

export default TagProductFilterDrawer