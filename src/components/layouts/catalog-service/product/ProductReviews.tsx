import React, { useEffect, useState } from 'react';
import {
  VisibilityState,
} from '@tanstack/react-table';
import { TableContainer, TableHolder } from '../../../UI-components/Table';
import NothingToshow from '../../../UI-components/NothingToShow';
import styled from 'styled-components';
import { useParams } from 'react-router-dom';

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;
interface ReviewData {
  id: number;
  customer_name: string;
  nickname: string;
  customer_email: string | null;
  item_name: string;
  item_image: string;
  product_id: number;
  customer_id: number | null;
  rating: number;
  title: string;
  description: string;
  status: string;
  source: string | null;
  admin_user: string | null;
  created_at: string;
  updated_at: string;
  admin_created_at: string | null;
  reviewed_at: string;
  created_by: string;
}

export interface ReviewResponse {
  count: number;
  next: string;
  previous: string;
  results: ReviewData[];
}
const ProductReviews = (
) => {
  const [prodId, setProdId] = useState<number | undefined>(undefined)
  const {id} = useParams();
  
  useEffect(() => {
    if (id ) {
      const newid = Number(id.split('=')[1]);
      setProdId(newid);
    }
  }, [id, prodId]);
  
  return (
    <div>
      <Container>
        <TableContainer>
          <TableHolder>
              <NothingToshow divHeight="55vh" />
          </TableHolder>
        </TableContainer>
      </Container>
    </div>
  );
};

export default ProductReviews;
