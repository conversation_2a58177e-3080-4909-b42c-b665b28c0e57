import React, { useState } from 'react';
import GroupedProducts from '../../../accordion/catalog-service/GroupedProducts';
import { Accordion } from '../../../UI-components/Accordion';
import styled from 'styled-components';
import GeneralAccordion from '../../../accordion/GeneralAccordion';

const Main = styled.div`
  margin: 10px 20px;
`;

const ProductGrouping = () => {
  return (
    <div style={{padding: '10px 20px'}}>
      <GroupedProducts />
    </div>
  );
};

export default ProductGrouping;
