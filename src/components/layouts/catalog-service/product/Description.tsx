import { Field, Input, Label, Textarea } from '@zendeskgarden/react-forms';
import { Col, Row as _Row } from '@zendeskgarden/react-grid';
import { useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import { useProductContext } from '../../../../pages/catalog-service/ProductFilterContext';
import { useParams } from 'react-router-dom';
import { baseTheme } from '../../../../themes/theme';
import 'react-quill/dist/quill.snow.css';
import useToast from '../../../../hooks/useToast';
import JoditEditor, { Jodit } from 'jodit-react';
import { fieldNameFromStoreName } from '@apollo/client/cache';

const Main = styled.div`
  height: 100%;
  background-color: #fff;
  margin-bottom: 100px;
`;
const Row = styled(_Row)`
  margin: 10px 20px;
  padding: 10px 10px;
`;

const BlueLabel = styled(Label)`
  color: ${baseTheme.colors.deepBlue};
`;

export const JoditEditorWrapper = styled.div`
  .jodit-wysiwyg ul,
  .jodit-wysiwyg ol {
    list-style-position: inside;
    list-style-type: disc;
    margin-left: 5px;
    padding-left: 5px;
  }

  .jodit-wysiwyg ol {
    list-style-type: decimal;
  }

  .jodit-wysiwyg li {
    margin-left: 20px;
    padding-left: 0;
    display: list-item;
  }
`;

interface ResultObject {
  _id: string;
  question: string;
  answer: {
    value: string;
    updated_by: string;
    updated_at: string;
  };
  product_id: number;
  product_name: string;
  product_image: string;
  status: string;
  enable: boolean;
  like: number;
  dislike: number;
  created_at: Date;
  customer_token: string;
  user: string;
  customer_email: string;
  customer_name: string;
}

interface GetQuestionsProps {
  getQuestions: {
    count: number;
    result: ResultObject[];
  };
}

interface AddQuestionsProps {
  addQuestionsAnswer: ResultObject;
}

interface EditQuestionsProps {
  editQuestionsAnswer: ResultObject;
}

const Description = () => {
  // Create a ref with any type to avoid TypeScript errors
  const descriptionEditorRef = useRef<any>(null);
  const featuresEditorRef = useRef<any>(null);
  const keySpecEditorRef = useRef<any>(null);
  const packagingEditorRef = useRef<any>(null);
  const directionsEditorRef = useRef<any>(null);
  const warrantyEditorRef = useRef<any>(null);
  const additionalInfoEditorRef = useRef<any>(null);

  const {
    contextProdData,
    setContextProdData,
    contextUpdateProdData,
    setContextUpdateProdData,
  } = useProductContext();
  const id = useParams();
  const [prodId, setProdId] = useState<number | undefined>(undefined);

  const [expandedSections, setExpandedSections] = useState<number[]>([]);

  const [shortDescription, setShortDescription] = useState<
    string | undefined
  >();
  const [productDescription, setProductDescription] = useState<
    string | undefined
  >();
  const [features, setFeatures] = useState<string | undefined>();
  const [keySpecification, setKeySpecification] = useState<
    string | undefined
  >();
  const [packaging, setPackaging] = useState<string | undefined>();
  const [direction, setDirection] = useState<string | undefined>();
  const [warranty, setWarranty] = useState<string | undefined>();
  const [additionlInfo, setAdditionlInfo] = useState<string | undefined>();

  const updateContextData = (attribute: string, value: any) => {
    setContextProdData((prevState) => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        [attribute]: value,
      },
    }));
    setContextUpdateProdData((prevState) => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        [attribute]: value,
      },
    }));
  };

  // Leaving this for reference but using direct upload in the handler

  // Loading state for image uploads
  const [isUploading, setIsUploading] = useState(false);

  const config = useMemo<any>(
    () => ({
      defaultActionOnPaste: 'insert_as_html',
      askBeforePaste: false,
      // enter: 'p',
      iframe: false,
      readonly: false,
      toolbarAdaptive: false,
      // height: 200,
      placeholder: '',
      buttons: [
        'undo',
        'redo',
        'paragraph',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        '|',
        'ul',
        'ol',
        'outdent',
        'indent',
        '|',
        'font',
        'fontsize',
        '|',
        'link',
        'unlink',
        'image',
        'table',
        '|',
        'align',
        'hr',
        '|',
        'symbols',
      ],
      events: {
        beforePaste: (e: any, html: any) => html,
        afterInit: (editor: any) => {
          const statusbar = document.createElement('div');
          statusbar.style.backgroundColor = '#f8f8f8';
          statusbar.style.color = 'red';
          statusbar.style.fontSize = '11px';
          statusbar.style.padding = '1px 4px';

          function calcStat() {
            const text = editor.editor.innerText.trim();
            const wordCount = text
              .split(/[\s\n\r\t]+/)
              .filter((value: any) => value).length;
            const charCount = text.replace(/[\s\n\r\t]+/, '').length;

            statusbar.innerText = `Words: ${wordCount} Chars: ${charCount}`;
          }

          editor.events
            .on('change afterInit', editor.async.debounce(calcStat, 100))
            .on('afterInit', () => {
              editor.container.appendChild(statusbar);
            });
        },
      },
    }),
    [],
  );

  useEffect(() => {
    // console.log("id: ",id)
    if (id && id.id) {
      const newid = Number(id.id.split('=')[1]);
      setProdId(newid);
    }
    // console.log("prodId: ", prodId)
  }, [id, prodId]);

  useEffect(() => {
    const updateAttribute = (
      attr: string,
      updateData: any,
      prodData: any,
      setState: (value: string | undefined) => void,
    ) => {
      if (updateData?.attributes_list?.[attr]) {
        setState(updateData.attributes_list[attr]);
      } else if (prodData?.attributes_list?.[attr]) {
        setState(prodData.attributes_list[attr]);
      }
    };

    updateAttribute(
      'short_description',
      contextUpdateProdData,
      contextProdData,
      setShortDescription,
    );
    updateAttribute(
      'description',
      contextUpdateProdData,
      contextProdData,
      setProductDescription,
    );
    updateAttribute(
      'features',
      contextUpdateProdData,
      contextProdData,
      setFeatures,
    );
    updateAttribute(
      'key_specifications',
      contextUpdateProdData,
      contextProdData,
      setKeySpecification,
    );
    updateAttribute(
      'packaging',
      contextUpdateProdData,
      contextProdData,
      setPackaging,
    );
    updateAttribute(
      'htext',
      contextUpdateProdData,
      contextProdData,
      setDirection,
    );
    updateAttribute(
      'warranty',
      contextUpdateProdData,
      contextProdData,
      setWarranty,
    );
    updateAttribute(
      'other_info',
      contextUpdateProdData,
      contextProdData,
      setAdditionlInfo,
    );
  }, [contextProdData]);

  const editorConfig = useMemo(() => {
    return {
      ...config,
      uploader: {
        insertImageAsBase64URI: false,
        insertImageAsBlob: false,
        imagesExtensions: ['jpg', 'png', 'jpeg', 'gif'],
        withCredentials: false,
        format: 'json',
        method: 'POST',
        url: 'https://catalogservice-dev.dentalkart.com/catalog-product/upload/image',
        // Remove Content-Type header to let browser set it with boundary
        headers: {},
        prepareData: function (formData: FormData) {
          // Log to debug what's in formData
          console.log(
            'Original FormData entries:',
            Array.from(formData.entries()),
          );

          // Create a completely new FormData with only the files field
          const newFormData = new FormData();

          // Find the file in the original formData
          let fileFound = false;
          for (let [key, value] of formData.entries()) {
            console.log('Checking FormData entry:', key, value);
            if (value instanceof File) {
              // Only append the file with 'files' key - no other fields
              newFormData.append('files', value);
              console.log(
                'Added file to new FormData:',
                value.name,
                value.size,
              );
              fileFound = true;
              break; // Only take the first file
            }
          }

          if (!fileFound) {
            console.error('No file found in FormData');
            return formData;
          }

          // Log the final FormData to verify it only contains 'files'
          console.log(
            'Final FormData entries:',
            Array.from(newFormData.entries()),
          );
          return newFormData;
        },
        isSuccess: function (resp: any) {
          return !resp.message;
        },
        getMsg: function (this: any, resp: any) {
          return resp?.message?.join !== undefined
            ? resp.message.join(' ')
            : resp.message;
        },

        process: function (this: any, resp: any) {
          return {
            files: [resp.uploaded_url],
            path: '',
            baseurl: '',
            error: resp.error ? 1 : 0,
            msg: resp.message,
          };
        },
        defaultHandlerSuccess: function (this: any, data: any) {
          console.log('Upload success data:', data);
          const files = data.files || [];
          if (files.length) {
            console.log('Inserting image:', files[0]);
            this.selection.insertImage(files[0], null, 250);
          } else {
            console.error('No files in success response');
          }
        },
        defaultHandlerError: function (this: any, resp: any) {
          console.error('Upload error:', resp);
          this.events.fire(
            'errorPopap',
            this.i18n(resp.message || 'Upload failed'),
          );
        },
      },
      events: {
        ...config.events,
        // Add custom file upload handler as backup
        beforeImageUpload: function (this: any, files: FileList) {
          console.log('beforeImageUpload triggered with files:', files);
          if (files && files.length > 0) {
            const file = files[0];
            console.log('File to upload:', file.name, file.size, file.type);

            // Custom upload logic - only send 'files' field
            const formData = new FormData();
            formData.append('files', file);

            // Log what we're sending
            console.log(
              'Sending FormData entries:',
              Array.from(formData.entries()),
            );

            setIsUploading(true);

            fetch(
              'https://catalogservice-dev.dentalkart.com/catalog-product/upload/image',
              {
                method: 'POST',
                body: formData,
              },
            )
              .then((response) => {
                console.log('Response status:', response.status);
                return response.json();
              })
              .then((data) => {
                console.log('Custom upload success:', data);
                if (data.uploaded_url) {
                  this.selection.insertImage(data.uploaded_url, null, 250);
                } else {
                  console.error('No uploaded_url in response:', data);
                }
              })
              .catch((error) => {
                console.error('Custom upload error:', error);
              })
              .finally(() => {
                setIsUploading(false);
              });

            return false; // Prevent default upload
          }
          return true;
        },
      },
    };
  }, [config]);

  return (
    <Main>
      <div>
        <Row>
          <Col size={12}>
            <Field>
              <BlueLabel>Short Description</BlueLabel>
              <Textarea
                value={shortDescription}
                onChange={(e) => {
                  setShortDescription(e.target.value);
                  updateContextData('short_description', e.target.value);
                }}
              />
            </Field>
          </Col>
        </Row>
        <Row>
          <Col style={{ position: 'relative' }} size={12}>
            <BlueLabel>Product Description</BlueLabel>
            {isUploading && (
              <div
                style={{
                  position: 'absolute',
                  top: '30px',
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(255, 255, 255, 0.7)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 10,
                }}
              >
                Uploading image...
              </div>
            )}
            <JoditEditorWrapper>
              <JoditEditor
                ref={descriptionEditorRef}
                value={productDescription || ''}
                onBlur={(newContent) => {
                  setProductDescription(newContent);
                  updateContextData('description', newContent);
                }}
                config={editorConfig}
              />
            </JoditEditorWrapper>
          </Col>
        </Row>
        <Row>
          <Col style={{ position: 'relative' }} size={12}>
            <BlueLabel>Features</BlueLabel>
            <JoditEditorWrapper>
              <JoditEditor
                ref={featuresEditorRef}
                value={features || ''}
                onBlur={(newContent) => {
                  setFeatures(newContent);
                  updateContextData('features', newContent);
                }}
                config={editorConfig}
              />
            </JoditEditorWrapper>
          </Col>
        </Row>
        <Row>
          <Col style={{ position: 'relative' }} size={12}>
            <BlueLabel>Key Specifications</BlueLabel>
            <JoditEditorWrapper>
              <JoditEditor
                ref={keySpecEditorRef}
                value={keySpecification || ''}
                onBlur={(newContent) => {
                  setKeySpecification(newContent);
                  updateContextData('key_specifications', newContent);
                }}
                config={editorConfig}
              />
            </JoditEditorWrapper>
          </Col>
        </Row>
        <Row>
          <Col style={{ position: 'relative' }} size={12}>
            <BlueLabel>Packaging</BlueLabel>
            <JoditEditorWrapper>
              <JoditEditor
                ref={packagingEditorRef}
                value={packaging || ''}
                onBlur={(newContent) => {
                  setPackaging(newContent);
                  updateContextData('packaging', newContent);
                }}
                config={editorConfig}
              />
            </JoditEditorWrapper>
          </Col>
        </Row>
        <Row>
          <Col style={{ position: 'relative' }} size={12}>
            <BlueLabel>Direction to use</BlueLabel>
            <JoditEditorWrapper>
              <JoditEditor
                ref={directionsEditorRef}
                value={direction || ''}
                onBlur={(newContent) => {
                  setDirection(newContent);
                  updateContextData('htext', newContent);
                }}
                config={editorConfig}
              />
            </JoditEditorWrapper>
          </Col>
        </Row>
        <Row>
          <Col style={{ position: 'relative' }} size={12}>
            <BlueLabel>Warranty</BlueLabel>
            <JoditEditorWrapper>
              <JoditEditor
                ref={warrantyEditorRef}
                value={warranty || ''}
                onBlur={(newContent) => {
                  setWarranty(newContent);
                  updateContextData('warranty', newContent);
                }}
                config={editorConfig}
              />
            </JoditEditorWrapper>
          </Col>
        </Row>
        <Row>
          <Col style={{ position: 'relative', marginBottom: '80px' }} size={12}>
            <BlueLabel>Additional Info</BlueLabel>
            <JoditEditorWrapper>
              <JoditEditor
                ref={additionalInfoEditorRef}
                value={additionlInfo || ''}
                onBlur={(newContent) => {
                  setAdditionlInfo(newContent);
                  updateContextData('other_info', newContent);
                }}
                config={editorConfig}
              />
            </JoditEditorWrapper>
          </Col>
        </Row>
      </div>
    </Main>
  );
};

export default Description;
