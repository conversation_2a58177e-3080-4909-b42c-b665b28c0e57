import React, { useState } from 'react'
import GeneralAccordion from '../../../accordion/GeneralAccordion'
import { Accordion as _Accordion } from '../../../UI-components/Accordion';
import styled from 'styled-components';
import RelatedProducts from '../../../accordion/catalog-service/RelatedProducts';
import UpSellsProducts from '../../../accordion/catalog-service/UpSellsProducts';
import CrossSellsProducts from '../../../accordion/catalog-service/CrossSellsProducts';
import { ProductColumns } from '../../../table/product/Columns';

const Main = styled.div`
  margin: 10px 10px;
`

const Accordion = styled(_Accordion)`
  margin-top: 0;
`;

const ProductAccordion = () => {
  const [expandedSections, setExpandedSections] = useState<number[]>([]);
  // console.log("expandedSection: ", expandedSections)
  return (
    <Main>
      <Accordion
                level={4}
                isBare
                isAnimated
                expandedSections={expandedSections}
                onChange={(index: any) => {
                  if (expandedSections.includes(index)) {
                    setExpandedSections([]);
                  } else {
                    setExpandedSections([index]);
                  }
                }}
              >
      <GeneralAccordion
      inAction={true}
      indexing={0}
      title={'Related Products'}
      children={
        <>
          <RelatedProducts/>
        </>
      }
      expandedSections={expandedSections}
      setExpandedSections={setExpandedSections}/>
      <GeneralAccordion
      inAction={true}
      indexing={1}
      title={'Up Sells'}
      children={
        <>
          <UpSellsProducts/>
        </>
      }
      expandedSections={expandedSections}
      setExpandedSections={setExpandedSections}/>
      <GeneralAccordion
      inAction={true}
      indexing={2}
      title={'Cross Sells'}
      children={
        <>
          <CrossSellsProducts/>
        </>
      }
      expandedSections={expandedSections}
      setExpandedSections={setExpandedSections}/>
              </Accordion>
    </Main>
  )
}

export default ProductAccordion