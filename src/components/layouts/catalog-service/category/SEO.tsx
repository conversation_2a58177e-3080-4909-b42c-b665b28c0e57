import React, { useEffect, useState } from 'react';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import Input from '../../../UI-components/Input';
import styled from 'styled-components';
import { Checkbox, Field, Label, Textarea } from '@zendeskgarden/react-forms';
import { CategoryObj } from '../../../../pages/catalog-service/category/Category';
import { baseTheme } from '../../../../themes/theme';
import { useCategoryContext } from '../../../../pages/catalog-service/category/CategoryContext';

const Row = styled(_Row)`
  margin: 20px auto;
`;

const URLLabel = styled(Label)`
  color: ${baseTheme.colors.textColorGrey};
`;

const SEO = ({
  categoryObj,
  setCategoryObj,
  setChangeValue,
  changeValue,
  urlKey,
  setUrlKey
}: {
  categoryObj: CategoryObj;
  setCategoryObj: (cat: CategoryObj) => void;
  setChangeValue: (cat: any) => void;
  changeValue: any;
  urlKey: string;
  setUrlKey: (value: string) => void;
}) => {
  const [checkUrl, setCheckUrl] = useState(false);
  const [urlKeyvalue, setUrlKeyValue] = useState('');
  const {originalUrlKey, setOriginalUrlKey} = useCategoryContext();
  useEffect(()=>{
    if(categoryObj.url_key){
      if (originalUrlKey === '') {
        setOriginalUrlKey(categoryObj.url_key);
      }
    }
  },[categoryObj])

  useEffect(()=>{
    if(originalUrlKey != ''){
      setUrlKeyValue(originalUrlKey);
    }
  },[originalUrlKey])

  useEffect(() => {
    if (urlKeyvalue.trim() === originalUrlKey.trim()) {
      setUrlKey('');
    }
  }, [urlKeyvalue, originalUrlKey]);


  return (
    <div>
      <Row>
        <Col size={3}>
          <Label>URL Key</Label>
        </Col>
        <Col size={5}>
          <Input
            value={urlKeyvalue}
            onChange={(e) => {
              const value = e.target.value;
              if(value.trim() !== originalUrlKey.trim()){
                setUrlKey(value);
              }else{
                setUrlKey('');
              }
              setUrlKeyValue(e.target.value);
            }} 
          />
          <Field>
            <Checkbox
              checked={checkUrl}
              onChange={() => {
                const val = !checkUrl;
                setCheckUrl(val);
                setCategoryObj({
                  ...categoryObj,
                  enableRedirect: val,
                })
                setChangeValue({
                  ...changeValue,
                  enableRedirect: val,
                })
              }}
            >
              <URLLabel>Create Permanent Redirect for old URL</URLLabel>
            </Checkbox>
          </Field>
        </Col>
      </Row>
      <Row>
        <Col size={3}>
          <Label>Meta Title</Label>
        </Col>
        <Col size={5}>
          <Input
            value={categoryObj.meta_title}
            onChange={(e) => {
              setCategoryObj({
                ...categoryObj,
                meta_title: e.target.value,
              });
              setChangeValue({
                ...changeValue,
                meta_title: e.target.value,
              });
            }}
          />
        </Col>
      </Row>
      <Row>
        <Col size={3}>
          <Label>Meta Keywords</Label>
        </Col>
        <Col size={5}>
          <Input
            value={categoryObj.meta_keyword}
            onChange={(e) => {
              setCategoryObj({
                ...categoryObj,
                meta_keyword: e.target.value,
              });
              setChangeValue({
                ...changeValue,
                meta_keyword: e.target.value,
              });
            }}
          />
        </Col>
      </Row>
      <Row>
        <Col size={3}>
          <Label>Meta Description</Label>
        </Col>
        <Col size={5}>
          <Textarea
            value={categoryObj.meta_description}
            onChange={(e) => {
              setCategoryObj({
                ...categoryObj,
                meta_description: e.target.value,
              });
              setChangeValue({
                ...changeValue,
                meta_description: e.target.value,
              });
            }}
            rows={8}
          />
        </Col>
      </Row>
    </div>
  );
};

export default SEO;
