import React, { useState } from 'react';
import styled from 'styled-components';
import { DownIcon, UpIcon } from '../../../../utils/icons';

const CategoryTreeContainer = styled.ul`
  list-style-type: none;
  padding-left: 0;
  margin-left: 10px;
  position: relative;
`;

const CategoryItem = styled.div<{ level: number }>`
  position: relative;
  padding-left: ${({ level }) => (level === 0 ? 0 : 20)}px; 
  margin-bottom: 10px;
  line-height: 2;

  &:before {
    content: '';
    position: absolute;
    left: ${({ level }) => (level === 0 ? '-20px' : '0')};
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #ccc;
  }

  & > ul {
    padding-left: 20px; 
  }
`;

const CategoryContainer = styled.div`
  display: flex;
  align-items: center;
  position: relative;
  background-color: #f9f9f9;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #e6f7ff;
  }

  &:before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    width: 20px;
    height: 2px;
    background-color: #ccc;
  }
`;

const CategoryName = styled.span`
  margin-left: 8px;
  font-size: 14px;
`;

const ExpandButton = styled.button`
  margin-right: 8px;
  cursor: pointer;
  background: none;
  border: none;
  font-size: 20px;
  line-height: 1;
  padding: 0;
  color: #007bff;
  
  &:hover {
    color: #0056b3;
  }
`;

const Checkbox = styled.input`
  cursor: pointer;
  margin-right: 8px;
`;

interface MenuItem {
  name: string;
  id: number;
  url_path: string;
  position: number;
  path: string;
  children: MenuItem[];
}

interface CategoryTreeProps {
  categories: MenuItem[] | undefined;
  onCategorySelect: (category: { id: number; name: string }) => void;
  selectItems: string[];
  setSelectItems: React.Dispatch<React.SetStateAction<string[]>>;
}

const CategoryTree: React.FC<CategoryTreeProps> = ({ categories, onCategorySelect, selectItems, setSelectItems }) => {
  const [expandedIds, setExpandedIds] = useState<number[]>([]);

  const toggleExpand = (id: number) => {
    setExpandedIds(prev =>
      prev.includes(id) ? prev.filter(expandedId => expandedId !== id) : [...prev, id]
    );
  };

  const handleCheckboxChange = (category: MenuItem, isChecked: boolean) => {
    const formattedCategory = `${category.name} (ID: ${category.id})`;
    if (isChecked) {
      setSelectItems(prev => [...prev, formattedCategory]);
      onCategorySelect({ id: category.id, name: category.name });
    } else {
      setSelectItems(prev => prev.filter(item => item !== formattedCategory));
    }
  };

  const renderCategory = (category: MenuItem, level: number = 0) => {
    const isChecked = selectItems.includes(`${category.name} (ID: ${category.id})`);

    return (
      <CategoryItem key={category.id} level={level}>
        <CategoryContainer>
          <Checkbox
            type="checkbox"
            checked={isChecked}
            onChange={(e) => handleCheckboxChange(category, e.target.checked)}
          />
          <CategoryName>{`${category.name} (ID: ${category.id})`}</CategoryName>
          {category.children.length > 0 && (
            <ExpandButton onClick={() => toggleExpand(category.id)}>
              {expandedIds.includes(category.id) ? <UpIcon /> : <DownIcon />}
            </ExpandButton>
          )}
        </CategoryContainer>
        {expandedIds.includes(category.id) && category.children.length > 0 && (
          <ul>
            {category.children.map(child => renderCategory(child, level + 1))}
          </ul>
        )}
      </CategoryItem>
    );
  };

  return (
    <CategoryTreeContainer>
      {categories && categories.map(category => renderCategory(category))}
    </CategoryTreeContainer>
  );
};

export default CategoryTree;
