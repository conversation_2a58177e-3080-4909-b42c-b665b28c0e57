import React, { useEffect, useRef, useState } from 'react';
import {
  Checkbox,
  Field,
  Input,
  Label,
  Select,
  Textarea,
  Toggle,
} from '@zendeskgarden/react-forms';
import {
  Autocomplete,
  Field as DropField,
  Dropdown,
  Item,
  Menu,
} from '@zendeskgarden/react-dropdowns';
import { debounce } from 'lodash';
import { Button } from '../../../UI-components/Button';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import styled from 'styled-components';
import { CategoryObj } from '../../../../pages/catalog-service/category/Category';
import { SM } from '@zendeskgarden/react-typography';
import { ImageContainer } from '../../../../pages/HomeSections/SiteMetrics';
import AddSingleCategoryModal from '../../../modal/home/<USER>';
const Container = styled.div`
  padding: 2rem;
`;
const Row = styled(_Row)`
  margin: 20px auto;
`;
const options = [
  'Asparagus',
  'Brussel sprouts',
  'Cauliflower',
  'Garlic',
  'Jerusalem artichoke',
  'Kale',
  'Lettuce',
  'Onion',
  'Mushroom',
  'Potato',
  'Radish',
  'Spinach',
  'Tomato',
  'Yam',
  'Zucchini',
];
const DisplaySettings = ({
  categoryObj,
  setCategoryObj,
  setChangeValue,
  changeValue,
}: {
  categoryObj: CategoryObj;
  setCategoryObj: (cat: CategoryObj) => void;
  setChangeValue: (cat: any) => void;
  changeValue: any;
}) => {
  return (
    <div>
      <Container>
        <Row mb="md">
          <Col lg={3}>
            <Label>Title</Label>
            <Input placeholder="Title" />
          </Col>
          <Col lg={3}>
            <Label>Mobile Title</Label>
            <Input placeholder="Mobile Title" />
          </Col>
        </Row>
        <Row mb="md">
          <Col lg={3}>
            <Dropdown selectedItem={''} onSelect={(s) => {}}>
              <Field>
                <Label>Landing Page Type</Label>
              </Field>
              <Menu>
              </Menu>
            </Dropdown>
          </Col>
          <Col lg={3}>
            <Label
            >
              Landing Page Url
            </Label>
            <Input placeholder="Landing Page Url" />
          </Col>
        </Row>

        <Row mb="md">
          <Col lg={6}>
            <Label>Product/Category</Label>
            <Row>
              <Col lg={9}>
                <Input />
              </Col>
              <Col>
                <Button>Browse</Button>
              </Col>
            </Row>
          </Col>
        </Row>
        <Row>
          <Col>
            <Label>Image</Label>
          </Col>
        </Row>
        <Row>
          <Col lg={7}>
            <Row justifyContent="center">
              <Col>
                <ImageContainer
                >
                </ImageContainer>
                Web
                <Col style={{ paddingLeft: '0px' }}>
                  <SM>
                  </SM>
                  <SM>
                    Max Size - <span style={{ color: 'red' }}>200KB</span>
                  </SM>
                  <SM>Format - JPEG/PNG/JPG</SM>
                </Col>
              </Col>
              <Col>
                <ImageContainer>
                </ImageContainer>
                Mobile
                
              </Col>
              <Col>
                <ImageContainer
                >
                </ImageContainer>
                I Pad
              </Col>
              <Col>
                <ImageContainer
                >
                </ImageContainer>
                Small Image
                
              </Col>
            </Row>
          </Col>
        </Row>

        <Row mt="lg">
          <Col>
            <Field>
              <Checkbox
              // checked={sectionElementObj?.relative}
              // onChange={() =>
              //   setsectionElementObj({
              //     ...sectionElementObj,
              //     relative: !sectionElementObj?.relative,
              //   } as SectionElement)
              // }
              >
                <Label>Relative</Label>
              </Checkbox>
            </Field>
          </Col>
        </Row>
        <Row>
          <Col></Col>
        </Row>
      </Container>
    </div>
  );
};

export default DisplaySettings;
