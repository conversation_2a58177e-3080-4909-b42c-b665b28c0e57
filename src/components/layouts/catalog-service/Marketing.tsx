import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Col, Row as _Row } from '../../UI-components/Grid';
import { Toggle } from '../../UI-components/Toggle';
import Input from '../../UI-components/Input'; 
import {
  Checkbox,
  Label as _Label,
  Field,
  Textarea,
} from '@zendeskgarden/react-forms';
import styled from 'styled-components'; 
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { baseTheme } from '../../../themes/theme';
import { debounce } from 'lodash';
import useToast from '../../../hooks/useToast';

const Main = styled.div`
  height: 100vh;
  padding: 10px 20px;
`;

const Row = styled(_Row)`
  margin: 10px 0px;
`;

const Label = styled(_Label)`
  color: ${baseTheme.colors.deepBlue};
`;

const URLLabel = styled(_Label)`
  color: ${baseTheme.colors.textColorGrey};
`;

const Marketing = ({ id }: { id: any }) => {
  const addToast = useToast();
  const { contextProdData, contextUpdateProdData, setContextProdData, setContextUpdateProdData, setRedirect, productErrors, setProductErrors } = useProductContext();
  const [checkView, setCheckView] = useState(contextProdData?.attributes_list?.international_active ?? false);
  const [checkUrl, setCheckUrl] = useState<boolean | null>();
  const [metaTitle, setMetaTitle] = useState<string | undefined>(contextProdData?.attributes_list?.meta_title ?? '');
  const [metaKeywords, setMetaKeywords] = useState<string | undefined>(contextProdData?.attributes_list?.meta_keyword ?? '');
  const [metaDescription, setMetdescription] = useState<string | undefined>(contextProdData?.attributes_list?.meta_description ?? '');
  const [gtin, setGtin] = useState<string | undefined>(contextProdData?.attributes_list?.gtin ?? '');
  const [urlkey, setUrlkey] = useState<string>(contextProdData?.attributes_list?.url_key ?? '');

  const [originalUrl, setOriginalUrl] = useState<string>('');

  const handleChangeUrlKey =(e:any)=>{
    const value = e.target.value;
    const urlKeyRegex = /^[a-zA-Z0-9-_]+$/;

    if(value === ''){
      setProductErrors({
        ...productErrors,
        url_key: 'Url key should not be empty!'
      })
      setUrlkey('');
    }
    if (urlKeyRegex.test(value) || value === '') {
      if(value.length > 0 ){
        setProductErrors({
          ...productErrors,
          url_key: ''
        })
      }
      setUrlkey(value);
      const updatedData = (prevState:any) => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          url_key: value,
        },
      });
      
      if(value.trim() !== originalUrl.trim()){
        setContextUpdateProdData(updatedData);
      }else{
        setContextUpdateProdData((prevState: any) => {
          const { url_key, ...restAttributes } = prevState.attributes_list;
          return {
            ...prevState,
            attributes_list: restAttributes,
          };
        });
      }
    } else {
      addToast('error','Invalid URL key! Only alphanumeric characters, hyphens, and underscores are allowed.');
    }
  }


  useEffect(()=>{
    if(originalUrl != ''){
      setUrlkey(originalUrl);
    }
  },[originalUrl])


  useEffect(() => {
    setCheckView(contextProdData?.attributes_list?.international_active ?? false);
    if (contextProdData?.attributes_list?.meta_title) {
      setMetaTitle(contextProdData.attributes_list.meta_title);
    }
    if (contextProdData?.attributes_list?.meta_keyword) {
      setMetaKeywords(contextProdData.attributes_list.meta_keyword);
    }
    if (contextProdData?.attributes_list?.meta_description) {
      setMetdescription(contextProdData.attributes_list.meta_description);
    }
    if (contextProdData?.attributes_list?.gtin) {
      setGtin(contextProdData.attributes_list.gtin);
    }
    if (contextProdData?.attributes_list?.url_key) {
      if (originalUrl === '') {
        setOriginalUrl(contextProdData.attributes_list.url_key);
      }
    }
    
  }, [contextProdData]);

  const handleViewToggleChange = () => {
    const newCheckView = !checkView;
    setCheckView(newCheckView);
    setContextProdData((prevState) => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        international_active: newCheckView,
      },
    }));
    setContextUpdateProdData((prevState) => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        international_active: newCheckView,
      },
    }));
  };

  const debouncedSetContextProdData = useCallback(
    debounce((key: string, value: string | undefined) => {
      setContextProdData((prevState) => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          [key]: value,
        },
      }));
      setContextUpdateProdData((prevState) => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          [key]: value,
        },
      }));
    }, 300),
    []
  );

  const handleInputChange = (key: string) => (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { value } = e.target;
    const newValue = value.trim() === '' ? undefined : value; 
    switch (key) {
      case 'meta_title':
        setMetaTitle(newValue);
        break;
      case 'meta_keyword':
        setMetaKeywords(newValue);
        break;
      case 'meta_description':
        setMetdescription(newValue);
        break;
      case 'gtin':
        setGtin(newValue);
        break;
      default:
        break;
    }
    debouncedSetContextProdData(key, newValue);
  };
  const regex = /^[a-zA-Z0-9_-]+$/;

  function isValidUrlKey(key:string) {
    return regex.test(key);
  }

  return (
    <Main>
      <Row>
        <Col>
          <Field>
            <Label style={{ marginRight: '10px' }}>International View</Label>
            <Toggle checked={checkView} onChange={handleViewToggleChange}>
              <Label hidden>International View</Label>
            </Toggle>
          </Field>
        </Col>
      </Row>
      <Row>
        <Col>
          <Label>URL key</Label>
          <Field>
            <Input
              value={urlkey}
              onChange={handleChangeUrlKey}
            />
          </Field>
          {id && (
            <Field>
              <Checkbox
                checked={checkUrl ?? false}
                onChange={() => {
                  const val = !checkUrl;
                  setCheckUrl(val);
                  setRedirect(val);
                }}
                disabled={!contextUpdateProdData?.attributes_list?.url_key}
              >
                <URLLabel>Create Permanent Redirect for old URL</URLLabel>
              </Checkbox>
            </Field>
          )}
        </Col>
        <Col>
          <Label>Meta Title</Label>
          <Field>
            <Input
              value={metaTitle}
              onChange={handleInputChange('meta_title')}
            />
          </Field>
        </Col>
      </Row>
      <Row>
        <Col>
          <Label>Meta Keywords</Label>
          <Field>
            <Textarea
              value={metaKeywords}
              onChange={handleInputChange('meta_keyword')}
              rows={4}
            />
          </Field>
        </Col>
        <Col>
          <Label>Meta Description</Label>
          <Field>
            <Textarea
              value={metaDescription}
              onChange={handleInputChange('meta_description')}
              rows={4}
            />
          </Field>
        </Col>
      </Row>
      <Row>
        <Col size={6}>
          <Label>Gtin</Label>
          <Field>
            <Input
              value={gtin}
              onChange={handleInputChange('gtin')}
            />
          </Field>
        </Col>
      </Row>
    </Main>
  );
};

export default Marketing;
