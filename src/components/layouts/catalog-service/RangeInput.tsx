import { Input, Label } from '@zendeskgarden/react-forms';
import { ColSection } from '../../drawer/catalog-service/ProductsFilterDrawer';
import { useEffect } from 'react';

type RangeInputProps = {
  label: string;
  fromValue?: number;
  toValue?: number;
  onFromChange: (value?: number) => void;
  onToChange: (value?: number) => void;
  // isDate?: boolean;
  error?: string;
  error_value: any;
  setError: (value: any) => void;
};

export const RangeInput = ({
  label,
  fromValue,
  toValue,
  onFromChange,
  onToChange,
  // isDate = false,
  error,
  error_value,
  setError
}: RangeInputProps) => {

  const handleFromChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFrom = e.target.value !== '' ? Number(e.target.value) : undefined;
    onFromChange(newFrom);
    
    if (newFrom !== undefined && toValue !== undefined && newFrom > toValue) {
      setError((prev:any) => ({ ...prev, [label]: 'From value must be less than To value' }));
    } else {
      setError((prev:any) => {
        const updated = { ...prev };
        delete updated[label];
        return updated;
      });
    }
  };

  const handleToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTo = e.target.value !== '' ? Number(e.target.value) : undefined;
    onToChange(newTo);
  
    if (fromValue !== undefined && newTo !== undefined && fromValue > newTo) {
      setError((prev:any) => ({ ...prev, [label]: 'From value must be less than To value' }));
    } else {
      setError((prev:any) => {
        const updated = { ...prev };
        delete updated[label];
        return updated;
      });
    }
  };
  
  return (
    <>
      <ColSection>
        <Label>{label}</Label>
      </ColSection>
      <ColSection>
        <div style={{width: '48%'}}>
          <Input
            value={fromValue?.toString() ?? ''}
            onChange={handleFromChange}
            type='number'
            placeholder="From"
          />
        </div>
        <div style={{width: '48%'}}>
          <Input
            value={toValue?.toString() ?? ''}
            onChange={handleToChange}
            type='number'
            placeholder="To"
          />
        </div>
      </ColSection>
      {error && <ColSection style={{color: 'red'}}>{error}</ColSection>}
    </>
  );
};
