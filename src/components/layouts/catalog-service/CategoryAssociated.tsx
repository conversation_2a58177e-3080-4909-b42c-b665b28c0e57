import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import useAxios from '../../../hooks/useAxios';
import constants from '../../../constants';
import { Row, Col } from '@zendeskgarden/react-grid';
import CategoryTree from './category/CategoryTree';
import styled from 'styled-components';
import { Input, MediaInput } from '@zendeskgarden/react-forms';
import { CategoryData, useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import useToast from '../../../hooks/useToast';
import { SearchIcon } from '../../../utils/icons';
import krakendPaths from '../../../constants/krakendPaths';

const Main = styled.div`
  margin: 20px 50px;
  padding: 10px 10px;
`;

const MainTree = styled.div`
  margin: 20px 50px;
  padding: 10px 10px;
  width: ${({ theme }) =>
    parseFloat(theme.components.dimension.width.base100) * 10}px;
  height: ${({ theme }) =>
    parseFloat(theme.components.dimension.width.base100) * 10}px;
`;
const MainSearch = styled.div`
  margin: 20px 10px;
  padding: 10px 10px;
  width: ${({ theme }) =>
    parseFloat(theme.components.dimension.width.base100) * 10}px;
`;

const OptionContainer = styled.div<{ isChecked: boolean }>`
  padding: 10px;
  display: flex;
  align-items: center;
  background-color: ${({ isChecked }) => (isChecked ? '#f0f8ff' : '#fff')};
  border-radius: 5px;
  margin-bottom: 8px;
  border: 1px solid #ddd;
`;

interface MenuItem {
  name: string;
  id: number;
  url_path: string;
  position: number;
  path: string;
  children: MenuItem[];
}

interface CategoryProps {
  selected: string[];
  setSelected: React.Dispatch<React.SetStateAction<string[]>>;
  categoryData: CategoryData[];
  setCategoryData: React.Dispatch<React.SetStateAction<CategoryData[]>>;
  categoryIds: number[];
}

const CategoryAssociated: React.FC<CategoryProps> = ({
  selected,
  setSelected,
  categoryData,
  setCategoryData,
  categoryIds,
}) => {
  const [selectedCategories, setSelectedCategories] = useState<MenuItem[]>([]);
  const [categName, setCategName] = useState<any>();
  const axios = useAxios();
  const addToast = useToast();

  const fetchCatagoryList: any = async ({ pageParam = 1, search = '' }) => {
    try {
      const response = await axios.get(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/categories?page_no=${pageParam}&category_name=${search}`,
        // `${constants.CATALOG_URL}/v1/catalog-admin/category/?page_no=${pageParam}&category_name=${search}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response;
    } catch (error) {
      addToast('error', 'Error in Fetching category List.');
      throw error;
    }
  };

  const { data: catNameData, status: categoryStatus } = useInfiniteQuery({
    queryKey: ['get-catalog-category-list-new', categName],
    queryFn: ({ pageParam = 1 }) =>
      fetchCatagoryList({ pageParam, search: categName }),
    getNextPageParam: (lastPage: any) => {
      const currentPage = lastPage.page_no;
      const totalPages = Math.ceil(lastPage.item_count / lastPage.page_size);
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
  });

  const { data: treeData, refetch: refetchCategoryData } = useQuery({
    queryKey: ['category-tree'],
    queryFn: async () => {
      const res = await axios.get(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/tree`,
        // `${constants.CATALOG_URL}/v1/catalog-admin/category/category-tree/`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );
      return res?.data?.categoryList as MenuItem[];
    },
  });

  const handleCategorySelect = (category: { id: number; name: string }) => {
    setSelectedCategories((prev: any) => {
      const isCategorySelected = prev.some(
        (c: { id: number }) => c.id === category.id,
      );
      if (isCategorySelected) {
        return prev.filter((c: { id: number }) => c.id !== category.id);
      } else {
        return [...prev, category];
      }
    });

    setSelected((prevSelected: any) => {
      const isCategorySelected = prevSelected.some(
        (c: string) => c === `${category.name} (ID: ${category.id})`,
      );
      if (isCategorySelected) {
        return prevSelected.filter(
          (c: string) => c !== `${category.name} (ID: ${category.id})`,
        );
      } else {
        return [...prevSelected, `${category.name} (ID: ${category.id})`];
      }
    });
  };

  const findCategoryPath = (
    categories: any,
    categoryId: number,
    currentPath = '',
  ) => {
    for (const category of categories) {
      const newPath = currentPath
        ? `${currentPath}/${category.name}`
        : category.name;

      if (category.id === categoryId) {
        return newPath as string;
      }

      if (category.children) {
        const foundPath: string = findCategoryPath(
          category.children,
          categoryId,
          newPath,
        );
        if (foundPath) {
          return foundPath as string;
        }
      }
    }
    return '';
  };

  const renderOptions = () => {
    return categoryData.map((option) => {
      const isChecked = selected.some(
        (c) => c === `${option.name} (ID: ${option.id})`,
      );
      const categoryPath = findCategoryPath(treeData, option.id);
      return (
        <OptionContainer isChecked={isChecked} key={option.id}>
          <label
            style={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              cursor: 'pointer',
            }}
          >
            <input
              style={{ marginRight: '10px' }}
              type="checkbox"
              checked={isChecked}
              onChange={() =>
                handleCategorySelect({ id: option.id, name: option.name })
              }
            />
            <span
              style={{
                fontSize: '14px',
                fontWeight: isChecked ? 'bold' : 'normal',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                gap: '5px',
              }}
            >
              {`${option.name} (ID: ${option.id})`}{' '}
              <span
                style={{ fontSize: '12px', color: '#666'}}
              >{categoryPath.length > 70 ? categoryPath.slice(0, 70) + '...' : categoryPath}</span>
            </span>
          </label>
        </OptionContainer>
      );
    });
  };

  useEffect(() => {
    if (selectedCategories.length > 0) {
      const itemsName = selectedCategories.map(
        (item) => `${item.name} (ID: ${item.id})`,
      );
      const itemsIds = selectedCategories.map((item) => item.id);
      setSelected((prevSelected: any) => {
        const updatedNames = Array.from(
          new Set([...prevSelected, ...itemsName]),
        );
        return updatedNames;
      });
    }
  }, [selectedCategories]);

  useEffect(() => {
    if (catNameData) {
      const allItems = catNameData.pages.flatMap((page) => page?.items);
      setCategoryData(allItems);
    }
  }, [catNameData, setCategoryData]);

  return (
    <Main>
      <Row style={{ marginBottom: '20px' }}>
        <Col>
          <MediaInput
            end={<SearchIcon />}
            value={categName}
            onChange={(e) => {
              const value = e.target.value;
              setCategName(value);
            }}
          />
        </Col>
      </Row>
      {categName ? (
        <MainSearch>{renderOptions()}</MainSearch>
      ) : (
        <MainTree>
          <Row>
            <Col style={{ textAlign: 'center' }}>
              <CategoryTree
                categories={treeData}
                onCategorySelect={handleCategorySelect}
                selectItems={selected}
                setSelectItems={setSelected}
              />  
            </Col>
          </Row>
        </MainTree>
      )}
    </Main>
  );
};

export default CategoryAssociated;
