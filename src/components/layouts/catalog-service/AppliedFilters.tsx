import React from 'react';
import { CrossIcon } from '../../../utils/icons';

interface Filter {
  name: string;
  value: string;
}

interface Props {
  appliedFilters: Filter[];
  removeFilter: (name: string) => void;
}

const AppliedFilters: React.FC<Props> = ({ appliedFilters, removeFilter }) => {
  return (
    <div>
      {appliedFilters.map((filter, index) => (
        <div
          key={index}
          style={{
            alignItems: 'center',
            textAlign: 'center',
            display: 'inline-flex',
            borderRadius: '10px',
            background: '#e5e9eb',
            padding: '5px 10px',
            marginRight: '10px',
          }}
        >
          <span>{filter.name}</span>
          <CrossIcon
            style={{ cursor: 'pointer', marginLeft: '5px' }}
            onClick={() => removeFilter(filter.name)}
          />
        </div>
      ))}
    </div>
  );
};

export default AppliedFilters;
