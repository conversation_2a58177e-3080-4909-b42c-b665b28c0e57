import {
  Field,
  Label as _Label,
  Input,
  Toggle,
  MediaInput,
} from '@zendeskgarden/react-forms';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import React, { useEffect, useState } from 'react';
import { useProductContext } from '../../../../pages/catalog-service/ProductFilterContext';
import { baseTheme } from '../../../../themes/theme';
import { useParams } from 'react-router-dom';
import useToast from '../../../../hooks/useToast';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import ProductMultiDropdown from '../../../dropdown/catalog-service/ProductMultiDropdown';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { CalenderIcon, CrossIcon } from '../../../../utils/icons';
import styled from 'styled-components';

const Row = styled(_Row)`
  margin: 20px 0px;
`;

const Label = styled(_Label)`
  color: ${baseTheme.colors.deepBlue};
`;

const Shipping = ({id}:{id : any}) => {
  const {
    contextProdData,
    contextUpdateProdData,
    setContextProdData,
    setContextUpdateProdData,
    dispatchDaysData,
    dispatchDaysList,
  } = useProductContext();
  
  const [cod, setCod] = useState<any>(
    contextProdData.attributes_list?.is_cod != null
      ? contextProdData.attributes_list.is_cod ? 'YES' : 'NO'
      : ''
  );
  const [returnPeriod, setReturnPeriod] = useState<any>(contextProdData?.attributes_list?.return_period ?? '');
  const [dispatch, setDispatch] = useState<any>(contextProdData.attributes_list?.dispatch_days);
  const [demoAvailable, setDemoAvailable] = useState<any>(
    contextProdData.attributes_list?.demo_available != null
      ? contextProdData.attributes_list.demo_available ? 'YES' : 'NO'
      : ''
  );

  const updateContextData = (attribute: string, value: any) => {
    setContextProdData(prevState => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        [attribute]: value,
      },
    }));
    setContextUpdateProdData(prevState => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        [attribute]: value,
      },
    }));
  };

  // Cash on Delivery
  const [inputCod, setInputCod] = useState('');
  const handleSelectCod = (item: any) => {
    setCod(item);
    updateContextData('is_cod', item === 'YES');
  };

  // Dispatch Day 
  const [inputDispatchday, setInputDispatchday] = useState('');
  const handleSelectDispatchday = (item: any) => {
    setDispatch(item);
    const selectedDispatch = dispatchDaysData?.find(val => val.value === item);
    const dispatchId = selectedDispatch?.id;
    
    setContextProdData(prevState => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        dispatch_days: item,
      },
    }));
    setContextUpdateProdData(prevState => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        dispatch_days: dispatchId,
      },
    }));
  };

  // Demo Available
  const [inputDemo, setInputDemo] = useState('');
  const handleSelectDemo = (item: any) => {
    setDemoAvailable(item);
    updateContextData('demo_available', item === 'YES');
  };
 
  useEffect(() => {
    if (contextUpdateProdData?.attributes_list?.is_cod != null) {
      setCod(contextUpdateProdData.attributes_list.is_cod ? "YES" : 'NO');
    } else if (contextProdData?.attributes_list?.is_cod != null) {
      setCod(contextProdData.attributes_list.is_cod ? "YES" : 'NO');
    }
    
    if (contextUpdateProdData?.attributes_list?.return_period != null) {
      setReturnPeriod(contextUpdateProdData.attributes_list.return_period);
    } else if (contextProdData?.attributes_list?.return_period != null) {
      setReturnPeriod(contextProdData.attributes_list.return_period);
    }
    
    if (contextUpdateProdData?.attributes_list?.dispatch_days != null) {
      const selectedDispatch = dispatchDaysData?.find(
        val => val.id === contextUpdateProdData?.attributes_list?.dispatch_days
      );
      setDispatch(selectedDispatch?.value ?? '');
    } else if (contextProdData?.attributes_list?.dispatch_days != null) {
      setDispatch(contextProdData.attributes_list.dispatch_days);
    }
    
    if (contextUpdateProdData?.attributes_list?.demo_available != null) {
      setDemoAvailable(contextUpdateProdData.attributes_list.demo_available ? 'YES' : 'NO');
    } else if (contextProdData?.attributes_list?.demo_available != null) {
      setDemoAvailable(contextProdData.attributes_list.demo_available ? 'YES' : 'NO');
    }
  }, [contextProdData, contextUpdateProdData, dispatchDaysData]);

  useEffect(() => {
    if (!id) {
      setCod('YES');
      updateContextData('is_cod', true);
      setDemoAvailable('NO');
      updateContextData('demo_available', false);
      setDispatch('2-3');
      
      const dispatchdays = dispatchDaysData?.find(val => val.value === '2-3');
      
      setContextProdData(prevState => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          dispatch_days: '2-3',
        },
      }));
      
      setContextUpdateProdData(prevState => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          dispatch_days: dispatchdays?.id,
        },
      }));
    }
  }, [id, dispatchDaysData]);

  return (
    <div>
      <Row>
        <Col size={3}>
          <ProductDropdown
            label="Cash on Delivery"
            options={['YES', 'NO']}
            selectedItem={cod}
            inputValue={inputCod}
            onSelect={handleSelectCod}
            onInputValueChange={setInputCod}
          />
        </Col>
      </Row>
      <Row>
        <Col size={3}>
          <Field>
            <Label>Return Period</Label>
            <Input
              type="number"
              value={returnPeriod}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '') {
                  setReturnPeriod('');
                  updateContextData('return_period', null);
                } else {
                  const parsedValue = parseFloat(value);
                  if (parsedValue > 0) {
                    setReturnPeriod(parsedValue);
                    updateContextData('return_period', parsedValue);
                  }
                } 
              }}
            />
          </Field>
        </Col>
      </Row>
      <Row>
        <Col size={3}>
          {contextProdData.type_id === 'grouped' ? (
            <Field>
              <Label>Dispatch Days</Label>
              <Input value={''} disabled />
            </Field>
          ) : (
            <ProductDropdown
              label="Dispatch Days"
              options={dispatchDaysList.length > 0 ? [...dispatchDaysList] : ['Loading....']}
              selectedItem={dispatch}
              inputValue={inputDispatchday}
              onSelect={handleSelectDispatchday}
              onInputValueChange={setInputDispatchday}
            />
          )}
        </Col>
      </Row>
      <Row>
        <Col size={3}>
          <Label>Demo Available</Label>
          <ProductDropdown
            options={['YES', 'NO']}
            selectedItem={demoAvailable}
            inputValue={inputDemo}
            onSelect={handleSelectDemo}
            onInputValueChange={setInputDemo}
          />
        </Col>
      </Row>
    </div>
  );
};

export default Shipping;