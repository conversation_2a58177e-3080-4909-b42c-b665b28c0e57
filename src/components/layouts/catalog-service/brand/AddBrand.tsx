import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../../themes/theme';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import Input from '../../../UI-components/Input';
import { Label as FLabel } from '../../../UI-components/Label';
import { Button, Buttons } from '../../../UI-components/Button';
import {
  Label as _Label,
  Field as _Field,
  Dropdown,
  Autocomplete,
} from '@zendeskgarden/react-dropdowns';
import { useLocation, useNavigate } from 'react-router-dom';
import { pageRoutes } from '../../../navigation/RouteConfig';
import { DeleteIcon, LeftArrowIcon } from '../../../../utils/icons';
import { useMutation, useQuery } from '@tanstack/react-query';
import useToast from '../../../../hooks/useToast';
import useAxios from '../../../../hooks/useAxios';
import constants from '../../../../constants';
import { Field, Toggle } from '@zendeskgarden/react-forms';
import { Spinner } from '@zendeskgarden/react-loaders';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import { IconButton } from '../../../UI-components/IconButton';
import krakendPaths from '../../../../constants/krakendPaths';
import { useBrandContext } from '../../../../pages/catalog-service/brand/BrandContext';
import { useAuth } from '../../../providers/AuthProvider';

const Main = styled.div`
  background-color: ${baseTheme.colors.white};
  min-height: 150vh;
  width: 100%;
`;

const Row = styled(_Row)`
  margin: ${baseTheme.components.dimension.width.base}px
    ${baseTheme.components.dimension.width.base}px;
`;
const Label = styled(FLabel)`
  color: ${baseTheme.colors.primaryHue};
  font-weight: bold;
`;
const BrandImage = styled.img`
  height: ${baseTheme.components.dimension.height.base100};
`;

const TopBarDiv = styled.div`
  background-color: ${colors.white};
  padding-bottom: ${(p) => p.theme.space.md};
  padding-left: ${(p) => p.theme.space.md};
  padding-right: ${(p) => p.theme.space.md};
`;

interface Brand {
  brand_id?: number;
  brand_name?: string;
  category_id?: number;
  created_at?: string;
  id?: number;
  is_active?: boolean;
  is_dk_suggest?: boolean;
  is_featured?: boolean;
  is_international?: boolean;
  is_newly_added?: boolean;
  is_top?: boolean;
  logo?: string;
  sort_order?: number;
  updated_at?: string;
  url?: string;
}

const AddBrand = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const { setHeaderInformation } = useAuth();
  const fileInputRef = useRef(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const [brand, setBrand] = useState<Brand>({
    is_active: true,
  });
  const { brandId, setBrandId } = useBrandContext();
  const [errorMessage, setError] = useState<string | undefined>();

  const axios = useAxios();
  const addToast = useToast();

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['get-brand-detail'],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          `${krakendPaths.CATALOG_URL}/admin-api/v1/brands/${brandId}`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            },
          },
        );
        return response;
      },
      enabled: !!brandId,
      onError: (err: {
        message: string;
        error: string;
        statusCode: number;
      }) => {
        addToast('error', `${err.message}`);
        navigate(`${pageRoutes['GO_TO_BRAND']}`);
      },
      onSuccess: (data: any) => {
        setBrand(data);
      },
    });

  const { mutate: addMutation, isLoading: addLoading } = useMutation(
    async () => {
      const payload = {
        attribute_id: 81,
        attribute_value: brand?.brand_name,
        logo: brand?.logo,
        sort_order: brand?.sort_order,
        is_active: brand?.is_active,
        is_international: brand?.is_international,
        is_featured: brand?.is_featured,
        is_top: brand?.is_top,
        is_newly_added: brand?.is_newly_added,
        is_dk_suggest: brand?.is_dk_suggest,
      };
      const response = await axios.post(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/brands`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response as Brand;
    },
    {
      onError: (err: {
        message: string;
        error: string;
        statusCode: number;
      }) => {
        addToast('error', `${err.message}`);
      },
      onSuccess: (data: Brand) => {
        setBrandId(data.id);
        addToast('success', 'Successfully created.');
      },
    },
  );

  const { mutate: updateMutation, isLoading: isUpdating } = useMutation(
    async () => {
      const payload = {
        logo: brand?.logo,
        sort_order: brand?.sort_order,
        is_active: brand?.is_active,
        is_international: brand?.is_international,
        is_featured: brand?.is_featured,
        is_top: brand?.is_top,
        is_newly_added: brand?.is_newly_added,
        is_dk_suggest: brand?.is_dk_suggest,
      };
      const response = await axios.patch(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/brands/${brandId}`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    {
      onError: (err: {
        message: string;
        error: string;
        statusCode: number;
      }) => {
        addToast('error', `${err.message}`);
      },
      onSuccess: () => {
        addToast('success', 'Successfully updated.');
      },
    },
  );

  const { mutate: uploadfileUrlMutation, isLoading: uploadurlLoading } =
    useMutation(
      async (selectedFile: string) => {
        const formData = new FormData();
        formData.append('file', selectedFile);
        const response = await axios.post(
          `${krakendPaths.CATALOG_URL}/admin-api/v1/file/upload`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'Content-Type': 'multipart/form-data',
            },
          },
        );

        return response;
      },
      {
        onError: () => {
          addToast('error', 'Error in Logo upload.');
        },
        onSuccess: (data: any) => {
          addToast('success', 'logo uploaded.');
          setBrand((prev) => ({
            ...prev,
            logo: data.file_url,
          }));
        },
      },
    );

  const handleSave = () => {
    if (brandId) {
      updateMutation();
    } else {
      if (brand.brand_name) {
        addMutation();
      } else {
        setError('Require Name of Brand!');
      }
    }
  };

  const handleFileChange = (e: any) => {
    setSelectedFile(e.target.files[0]);
  };

  useEffect(() => {
    if (selectedFile) {
      uploadfileUrlMutation(selectedFile);
    }
  }, [selectedFile]);

  const handleButtonClick = () => {
    (fileInputRef?.current as any)?.click();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBrand((prev) => ({
      ...prev,
      [name]: value,
    }));
    setError('');
  };

  const handleToggleChange = (field: keyof Brand) => {
    setBrand((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  useEffect(() => {
    if (brandId) {
      navigate(`${pageRoutes['GO_TO_BRAND_DETAIL']}`);
      queryParams.set('id', brandId.toString());
      navigate({ search: queryParams.toString() });
    } else {
      navigate(`${pageRoutes['GO_TO_ADD_BRAND']}`);
    }
  }, [brandId]);

  useEffect(() => {
    setHeaderInformation({
      title: brandId ? 'Edit Brand' : 'Add Brand',
      breadcrumbParent: 'Brand',
    });
  }, [setHeaderInformation]);

  return (
    <Main>
      <TopBarDiv>
        <div style={{ display: 'flex' }}>
          <Col style={{ display: 'flex', justifyContent: 'end' }}>
            <Buttons
              style={{
                margin: `0px ${baseTheme.components.dimension.width.base}px`,
              }}
              onClick={() => {
                navigate(`${pageRoutes['GO_TO_BRAND']}`);
                setBrandId(undefined);
              }}
              isAction
            >
              <Buttons.StartIcon>
                <LeftArrowIcon />
              </Buttons.StartIcon>
              Back
            </Buttons>
            <Button isPrimary onClick={handleSave}>
              {isUpdating || addLoading ? <Spinner /> : 'Save'}
            </Button>
          </Col>
        </div>
      </TopBarDiv>
      <div>
        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>Active</Label>
              </Col>
              <Col size={5}>
                <Field>
                  <Toggle
                    checked={brand.is_active}
                    onChange={() => {
                      const value = !brand.is_active;
                      setBrand({
                        ...brand,
                        is_active: value,
                      });
                    }}
                  >
                    <Label hidden>Enable in Category</Label>
                  </Toggle>
                </Field>
              </Col>
            </Row>
          </Col>
        </Row>
        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>
                  Name{' '}
                  <span style={{ color: `${baseTheme.colors.deepRed}` }}>
                    *
                  </span>
                </Label>
              </Col>
              <Col size={8}>
                <Input
                  name="brand_name"
                  value={brand.brand_name}
                  onChange={handleInputChange}
                  type="text"
                  disabled={!!brandId}
                />
                {errorMessage && (
                  <div style={{ color: `${baseTheme.colors.deepRed}` }}>
                    {errorMessage}
                  </div>
                )}
              </Col>
            </Row>
          </Col>
          <Col size={3}>
            <Dropdown>
              <_Field>
                <_Label>Parent Category</_Label>
                <Autocomplete disabled>Dental Brand</Autocomplete>
              </_Field>
            </Dropdown>
          </Col>
        </Row>

        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>Brand Id</Label>
              </Col>
              <Col size={8}>
                <Input
                  disabled
                  name="brand_id"
                  value={brand.brand_id}
                  onChange={(e) => {
                    const value = e.target.value;
                    setBrand((prev) => ({
                      ...prev,
                      brand_id: Number(value),
                    }));
                  }}
                  type="number"
                />
              </Col>
            </Row>
          </Col>
          <Col size={6} style={{ display: 'flex', alignItems: 'center' }}>
            <Col
              size={2}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'start',
              }}
            >
              <Label>Att. Id</Label>
            </Col>
            <Col size={4}>
              <Input disabled value={81} />
            </Col>
          </Col>
        </Row>
        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>Category Id</Label>
              </Col>
              <Col size={8}>
                <Input
                  disabled
                  name="category_id"
                  value={brand.category_id}
                  onChange={(e) => {
                    const value = e.target.value;
                    setBrand((prev) => ({
                      ...prev,
                      category_id: Number(value),
                    }));
                  }}
                  type="number"
                />
              </Col>
            </Row>
          </Col>
        </Row>

        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>URL</Label>
              </Col>
              <Col size={8}>
                <Input
                  disabled
                  name="url"
                  value={brand.url}
                  onChange={handleInputChange}
                  type="text"
                />
              </Col>
            </Row>
          </Col>
        </Row>
        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>Logo</Label>
              </Col>
              <Col size={8}>
                <input
                  type="file"
                  ref={fileInputRef}
                  style={{ display: 'none' }}
                  onChange={handleFileChange}
                />
                {selectedFile ? (
                  <BrandImage
                    src={URL.createObjectURL(selectedFile)}
                    alt="Brand Img"
                  />
                ) : brand.logo ? (
                  <BrandImage src={brand.logo} alt="Brand Img" />
                ) : (
                  <></>
                )}
                <div>
                  {brand.logo || selectedFile ? (
                    <Tooltip content="Delete Brand Image">
                      <IconButton
                        onClick={() => {
                          if (brand.logo) {
                            setBrand((prev) => ({
                              ...prev,
                              logo: undefined,
                            }));
                            setSelectedFile(null);
                          } else {
                            setSelectedFile(null);
                          }
                        }}
                        style={{ marginRight: `${baseTheme.paddings.md}` }}
                        isDanger
                      >
                        {<DeleteIcon />}
                      </IconButton>
                    </Tooltip>
                  ) : (
                    <></>
                  )}
                  <Button onClick={handleButtonClick}>
                    {selectedFile || brand.logo ? 'Change Image' : 'Add Image'}
                  </Button>
                </div>
              </Col>
            </Row>
          </Col>
        </Row>
        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>Featured</Label>
              </Col>
              <Col size={8}>
                <Field>
                  <Toggle
                    checked={brand.is_featured}
                    onChange={() => {
                      handleToggleChange('is_featured');
                    }}
                  >
                    <Label hidden>Featured</Label>
                  </Toggle>
                </Field>
              </Col>
            </Row>
          </Col>
        </Row>
        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>Dentalkart Suggests</Label>
              </Col>
              <Col size={8}>
                <Field>
                  <Toggle
                    checked={brand.is_dk_suggest}
                    onChange={() => handleToggleChange('is_dk_suggest')}
                  >
                    <Label hidden>Is Newly Added</Label>
                  </Toggle>
                </Field>
              </Col>
            </Row>
          </Col>
        </Row>
        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>Top Brands</Label>
              </Col>
              <Col size={8}>
                <Field>
                  <Toggle
                    checked={brand.is_top}
                    onChange={() => handleToggleChange('is_top')}
                  >
                    <Label hidden>Is Newly Added</Label>
                  </Toggle>
                </Field>
              </Col>
            </Row>
          </Col>
        </Row>
        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>Newly Added</Label>
              </Col>
              <Col size={4}>
                <Field>
                  <Toggle
                    checked={brand.is_newly_added}
                    onChange={() => handleToggleChange('is_newly_added')}
                  >
                    <Label hidden>Is Newly Added</Label>
                  </Toggle>
                </Field>
              </Col>
            </Row>
          </Col>
        </Row>
        <Row>
          <Col size={6}>
            <Row>
              <Col size={3}>
                <Label>International</Label>
              </Col>
              <Col size={4}>
                <Field>
                  <Toggle
                    checked={brand.is_international}
                    onChange={() => handleToggleChange('is_international')}
                  >
                    <Label hidden>Is Newly Added</Label>
                  </Toggle>
                </Field>
              </Col>
            </Row>
          </Col>
        </Row>
      </div>
    </Main>
  );
};

export default AddBrand;
