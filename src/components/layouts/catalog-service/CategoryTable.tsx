import React, { useState } from 'react';
import TopBar from '../../topbar/TopBar';
import { ProductColumns, ProductTableColumns as columns } from '../../table/product/Columns'; 
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import styled from 'styled-components';
import {
  TableContainer,
  TableHolder,
  TableContainer as _TableContainer,
} from '../../UI-components/Table';
import { DataTable } from '../../table/product/DataTable';
import NothingToshow from '../../UI-components/NothingToShow';
import { baseTheme } from '../../../themes/theme';
import { Col, Row } from '../../UI-components/Grid';
import { Button } from '../../UI-components/Button';
import { Dropdown, Item, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import { DownIcon, LeftArrowIcon, RightArrowIcon } from '../../../utils/icons';
import { Pagination } from '../../UI-components/Pagination';
import { Span } from '@zendeskgarden/react-typography';
import { useNavigate } from 'react-router-dom';
import { pageRoutes } from '../../navigation/RouteConfig'; 
const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;
const CategoryTable = ({data,
    searchContent,
    setSearchContent,
    handleSearch,
    filters,
    setFilters,
    count,
    refetch,
    currentSearch,
    setCurrentSearch,
  }: {
    data: ProductColumns[];
    searchContent: string | undefined;
    setSearchContent: React.Dispatch<React.SetStateAction<string | undefined>>;
    handleSearch: () => void;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
    count: number;
    refetch: any;
    currentSearch?: string;
    setCurrentSearch?: React.Dispatch<React.SetStateAction<string | undefined>>;
  }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [rowSelection, setRowSelection] = React.useState({});
    const [columnVisibility, setColumnVisibility] =
      React.useState<VisibilityState>({});
    const [rotated, setRotated] = useState<boolean | undefined>();
    const navigate = useNavigate();
    const table = useReactTable({
      data,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      onColumnVisibilityChange: setColumnVisibility,
      onRowSelectionChange: setRowSelection,
      state: {
        columnVisibility,
        rowSelection,
      },
    });
  
    const reset = () => {
      setFilters({
        page: 1,
        page_size: 20,
        created_by: '',
        search_by: '',
      });
      setSearchContent('');
      setCurrentSearch && setCurrentSearch('');
    };
    const addProduct = () => {
      navigate(`${pageRoutes['GO_TO_ADD_PRODUCT']}/`);
    };
    const handleRowPerPage = (item: string) => {
      const pageSize = Number(item);
      setFilters((prev:any) => ({
        ...prev,
        page: 1,
        page_size: pageSize,
      }));
      table.setPageSize(pageSize);
    };
    return (
      <div>
        
        <Container>
          <TableContainer>
            <TableHolder>
              {table.getRowModel().rows?.length ? (
                <DataTable table={table} columns={columns} data={data} />
              ) : (
                <NothingToshow divHeight="55vh" />
              )}
            </TableHolder>
            {table.getRowModel().rows?.length && count > 0 ? (
              <div style={{ overflowX: 'clip' }}>
                <Row
                  style={{
                    height: `${baseTheme.components.dimension.width.base * 5}px`,
                    marginTop: baseTheme.space.sm,
                    backgroundColor: baseTheme.colors.white,
                    paddingLeft: baseTheme.space.lg,
                    paddingRight: baseTheme.space.lg,
                  }}
                  justifyContent="center"
                  alignItems="center"
                >
                  <Col lg={1.5} md={1.5}>
                    <Row justifyContent="start" alignItems="center">
                      <Button
                        style={{
                          maxWidth: baseTheme.components.dimension.width.base100,
                        }}
                        size="medium"
                        isAction
                        disabled={filters.page <= 1 ? true : false}
                        onClick={() => {
                          setFilters((prev: any) => ({
                            ...prev,
                            page: prev.page - 1,
                          }));
                        }}
                      >
                        <Button.StartIcon>
                          <LeftArrowIcon />
                        </Button.StartIcon>
                        Previous
                      </Button>
                    </Row>
                  </Col>
                  <Col textAlign="center" lg={2} md={2}>
                    <Dropdown
                      onSelect={(item) => handleRowPerPage(item)}
                      onStateChange={(options) =>
                        Object.hasOwn(options, 'isOpen') &&
                        setRotated(options.isOpen)
                      }
                    >
                      <Trigger>
                        <Button size="medium" isAction>
                          Row Per Page:
                          <Span style={{ paddingLeft: baseTheme.space.sm }}>
                            {table.getState().pagination.pageSize}
                          </Span>
                          <Button.EndIcon
                            isRotated={rotated}
                            style={{ marginLeft: 0 }}
                          >
                            <DownIcon />
                          </Button.EndIcon>
                        </Button>
                      </Trigger>
                      <Menu>
                        <Item value={20}>20</Item>
                        <Item value={50}>50</Item>
                        <Item value={100}>100</Item>
                      </Menu>
                    </Dropdown>
                  </Col>
                  <Col lg={5} md={5}>
                    <Row justifyContent="center" alignItems="center">
                      <Pagination
                        color={baseTheme.colors.deepBlue}
                        totalPages={Math.ceil(
                          count / table.getState().pagination.pageSize,
                        )}
                        pagePadding={2}
                        currentPage={filters.page}
                        onChange={(e) =>
                          setFilters((prev: any) => ({ ...prev, page: e }))
                        }
                      />
                    </Row>
                  </Col>
                  <Col lg={1.5} md={1.5}>
                    <Row justifyContent="start" alignItems="end">
                      <Button
                        size="medium"
                        isAction
                        style={{ cursor: 'default' }}
                      >
                        {(filters.page - 1) *
                          table.getState().pagination.pageSize +
                          1}
                        -
                        {count <
                        filters.page * table.getState().pagination.pageSize
                          ? count
                          : filters.page *
                            table.getState().pagination.pageSize}{' '}
                        of {count}
                      </Button>
                    </Row>
                  </Col>
                  <Col lg={1.5} md={1.5}>
                    <Row justifyContent="end" alignItems="end">
                      <Button
                        style={{
                          maxWidth: baseTheme.components.dimension.width.base100,
                        }}
                        size="medium"
                        isAction
                        disabled={
                          filters.page >=
                          Math.ceil(count / table.getState().pagination.pageSize)
                            ? true
                            : false
                        }
                        onClick={() =>
                          setFilters((prev: any) => ({
                            ...prev,
                            page: prev.page + 1,
                          }))
                        }
                      >
                        Next
                        <Button.EndIcon>
                          <RightArrowIcon />
                        </Button.EndIcon>
                      </Button>
                    </Row>
                  </Col>
                </Row>
              </div>
            ) : (
              <>{''}</>
            )}
          </TableContainer>
        </Container>
        {/* <ProductsFilterDrawer
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          setFilters={setFilters}
          filters={filters}
          reset={reset}
        /> */}
      </div>
  )
}

export default CategoryTable