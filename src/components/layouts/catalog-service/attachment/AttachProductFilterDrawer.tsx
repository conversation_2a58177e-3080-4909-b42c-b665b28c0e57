import React, { useEffect, useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import { Input, Label, MediaInput } from '@zendeskgarden/react-forms';
import { DrawerModal } from '@zendeskgarden/react-modals';
import styled from 'styled-components';
import { Button } from '../../../UI-components/Button';
import { baseTheme } from '../../../../themes/theme';
import { AttachmentFilters } from '../../../../pages/catalog-service/attachment/Attachment';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { CalenderIcon, CrossIcon } from '../../../../utils/icons';
import { useAttachmentContext } from '../../../../pages/catalog-service/attachment/AttachmentContext';

interface AttachmentProductFilters {
  id_from?: number;
  id_to?: number;
  price_from?: number;
  price_to?: number;
  product_id?: number;
  type_id?: string;
  sku?: string;
  name?: string;
  status?: boolean;
  visibility?: number;
  sort_by?: string;
  sort_order?: string;
  page: number;
  limit: number;
}

const ColSection = styled(Col)`
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
  margin: 10px 10px;
`;

const AttachProductFilterDrawer = ({
  isOpen,
  setIsOpen,
  setFilters,
  filters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setFilters: React.Dispatch<React.SetStateAction<AttachmentProductFilters>>;
  filters: AttachmentProductFilters;
  reset: any;
}) => {

  const {visibilityData, visibilityList} = useAttachmentContext();
  // const [id, setId] = useState<any>();
  const [idFrom, setIdFrom] = useState<any>();
  const [idTo, setIdTo] = useState<any>();
  const [sku, setSku] = useState<any>();
  const [prodName, setProdName] = useState<any>();
  const [typeId, setTypeId] = useState<any>();
  const [priceFrom, setPriceFrom] = useState<any>();
  const [priceTo, setPriceTo] = useState<any>();
  const [description, setDescription] = useState<any>();
  const [prodId, setProdId] = useState<any>();
  const [status, setStatus] = useState<any>();

   //visibility
   const [visibility, setVisibility] = useState<any>();
   const [inputVisibility, setInputVisibility] = useState('');
   const handleSelectVisibility = (item: string) => {
     if (item === 'Select visibility') {
       setVisibility(undefined);
     } else {
       setVisibility(item);
     }
   };
   const handleInputVisibilityChange = (value: string) => {
     setInputVisibility(value);
   };
 
   useEffect(()=>{
     const selectedVisibility = visibilityData?.find(
       (val:any) => val.id === filters.visibility,
     );
     const visibleValue = selectedVisibility?.value;
     setVisibility(visibleValue);
   },[visibilityData])


  const resetFilters = () => {
    reset();
    setIsOpen(false);
  };
  const applyFilters = () => {
    setIsOpen(false);
    const selectedVisibility = visibilityData?.find(
      (val:any) => val.value === visibility,
    );
    const visibleId = selectedVisibility?.id; 
    let typeIdValue;
    switch (typeId) {
      case 'Simple Product':
        typeIdValue = 'simple';
        break;
      case 'Grouped Product':
        typeIdValue = 'grouped';
        break;
      case 'Virtual Product':
        typeIdValue = 'virtual';
        break;
      default:
        typeIdValue = undefined;
    }
  
    setFilters({
      ...filters,
      id_from: idFrom,
      id_to: idTo,
      price_from: priceFrom,
      price_to: priceTo,
      type_id: typeIdValue,
      sku: sku,
      name: prodName,
      visibility: visibleId ?? undefined,
      status: status ? (status === 'Enabled' ? true : false) : undefined,
    });
  };
  const close = () => setIsOpen(false);

  useEffect(() => {
    if (filters.id_from) {
      setIdFrom(filters.id_from);
    }
    if (filters.id_to) {
      setIdTo(filters.id_to);
    }
    if (filters.price_from) {
      setPriceFrom(filters.price_from);
    }
    if (filters.price_to) {
      setPriceTo(filters.price_to);
    }
    if (filters.status != undefined) {
      setStatus(filters.status === true ? 'Enabled' : 'Disabled');
    }
    if (filters.sku) {
      setSku(filters.sku);
    }
    if (filters.name) {
      setProdName(filters.name);
    }
    if (filters.type_id) {
      let productType;
      switch (filters.type_id) {
        case 'simple':
          productType = 'Simple Product';
          break;
        case 'grouped':
          productType = 'Grouped Product';
          break;
        case 'virtual':
          productType = 'Virtual Product';
          break;
        default:
          productType = undefined;
      }
      setTypeId(productType);
    }
  }, []);
  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal style={{ width: '30%' }} isOpen={isOpen} onClose={close}>
          <DrawerModal.Header
            tag="h2"
            style={{
              display: 'flex',
              backgroundColor: `${baseTheme.colors.deepBlue}`,
              color: 'white',
              justifyContent: 'center',
              fontSize: `${baseTheme.fontSizes.lg}`,
            }}
          >
            Filters
          </DrawerModal.Header>
          <DrawerModal.Body>
            <div>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>ID</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>From</Label>
                    </Col>
                    <Col>
                      <Input
                      type='number'
                        value={idFrom !== undefined ? idFrom.toString() : ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          setIdFrom(value !== '' ? Number(value) : undefined);
                        }}
                      />
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>to</Label>
                    </Col>
                    <Col>
                      <Input
                      type='number'
                        value={idTo !== undefined ? idTo.toString() : ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          setIdTo(value !== '' ? Number(value) : undefined);
                        }}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Name</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                  <Input
                    value={prodName}
                    onChange={(e) => {
                      setProdName(e.target.value);
                    }}
                  />
                  
                  </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                  <Label>SKU</Label>
                  </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                  <Input
                    value={sku}
                    onChange={(e) => {
                      setSku(e.target.value);
                    }}
                  />
                   </Col>
                   </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Price</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>From</Label>
                    </Col>
                    <Col>
                      <Input
                      type='number'
                        value={priceFrom !== undefined ? priceFrom.toString() : ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          setPriceFrom(
                            value !== '' ? Number(value) : undefined,
                          );
                        }}
                      />
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>to</Label>
                    </Col>
                    <Col>
                      <Input
                      type='number'
                        value={priceTo !== undefined ? priceTo.toString() : ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          setPriceTo(value !== '' ? Number(value) : undefined);
                        }}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                  <Label>Visibility</Label>
                  </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                   <ProductDropdown
                        selectedItem={visibility}
                        onSelect={handleSelectVisibility}
                        inputValue={inputVisibility}
                        onInputValueChange={handleInputVisibilityChange}
                        options={visibilityList}
                      />
                   </Col>
                   </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                  <Label>Status</Label>
                  </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                  <ProductDropdown
                    options={['Enabled', 'Disabled']}
                    selectedItem={status}
                    onSelect={(item: string) => {
                      setStatus(item);
                    }}
                    inputValue={undefined}
                    onInputValueChange={function (value: any): void {
                      throw new Error('Function not implemented.');
                    }}
                  />
                   </Col>
                   </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                  <Label>Type</Label>
                  </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                  <ProductDropdown
                    options={['Simple Product', 'Grouped Product', 'Virtual Product']}
                    selectedItem={typeId}
                    onSelect={(item: string) => {
                      setTypeId(item);
                    }}
                    inputValue={undefined}
                    onInputValueChange={function (value: any): void {
                      throw new Error('Function not implemented.');
                    }}
                  />
                   </Col>
                   </ColSection>
                </Col>
              </Row>
            </div>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button onClick={resetFilters}>Reset Filter</Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary isOrange disabled={!(idFrom || idTo || status || priceFrom || priceTo || prodName || sku || visibility || typeId)} onClick={applyFilters}>
                Apply Filter
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close style={{ color: 'white' }} />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default AttachProductFilterDrawer;
