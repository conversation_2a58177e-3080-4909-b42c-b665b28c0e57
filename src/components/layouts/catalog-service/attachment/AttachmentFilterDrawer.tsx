import React, { useEffect, useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import { Input, Label, MediaInput } from '@zendeskgarden/react-forms';
import { DrawerModal } from '@zendeskgarden/react-modals';
import styled from 'styled-components';
import { Button } from '../../../UI-components/Button';
import { baseTheme } from '../../../../themes/theme';
import { AttachmentFilters } from '../../../../pages/catalog-service/attachment/Attachment';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { CalenderIcon, CrossIcon } from '../../../../utils/icons';

const ColSection = styled(Col)`
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
  margin: 10px 10px;
`;

const AttachmentFilterDrawer = ({
  isOpen,
  setIsOpen,
  setFilters,
  filters,
  reset,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setFilters: React.Dispatch<React.SetStateAction<AttachmentFilters>>;
  filters: AttachmentFilters;
  reset: any;
}) => {
  const [id, setId] = useState<any>();
  // const [idFrom, setIdFrom] = useState<any>();
  // const [idTo, setIdTo] = useState<any>();
  const [createdAtFrom, setCreatedAtFrom] = useState<any>();
  const [createdAtTo, setCreatedAtTo] = useState<any>();
  const [updatedAtFrom, setUpdatedAtFrom] = useState<any>();
  const [updatedAtTo, setUpdatedAtTo] = useState<any>();
  const [description, setDescription] = useState<any>();
  const [prodId, setProdId] = useState<any>();
  const [status, setStatus] = useState<any>();
  const resetFilters = () => {
    reset();
    setIsOpen(false);
  };

  const applyFilters = () => {
    setIsOpen(false);
    setFilters({
      ...filters,
      id: Number(id),
      from_created_at: createdAtFrom,
      to_created_at: createdAtTo,
      from_updated_at: updatedAtFrom,
      to_updated_at: updatedAtTo,
      status: status ? status === 'Enabled' ? true : false : undefined,
      description: description,
    });
  };
  const close = () => setIsOpen(false);

  useEffect(() => {
    if(filters.id){
      setId(filters.id);
    }
    if (filters.from_created_at) {
      setCreatedAtFrom(filters.from_created_at);
    }
    if (filters.to_created_at) {
      setCreatedAtTo(filters.to_created_at);
    }
    if (filters.from_updated_at) {
      setUpdatedAtFrom(filters.from_updated_at);
    }
    if (filters.to_updated_at) {
      setUpdatedAtTo(filters.to_updated_at);
    }
    if (filters.status != undefined) {
      setStatus(filters.status === true ? "Enabled" : 'Disabled');
    }
    if (filters.description) {
      setDescription(filters.description);
    }
  }, []);
  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal style={{ width: '30%' }} isOpen={isOpen} onClose={close}>
          <DrawerModal.Header
            tag="h2"
            style={{
              display: 'flex',
              backgroundColor: `${baseTheme.colors.deepBlue}`,
              color: 'white',
              justifyContent: 'center',
              fontSize: `${baseTheme.fontSizes.lg}`,
            }}
          >
            Filters
          </DrawerModal.Header>
          <DrawerModal.Body>
            <div>
              
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>ID</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Input
                        value={id}
                        type='number'
                        onChange={(e) => {
                          setId(e.target.value);
                        }}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Created At</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>From</Label>
                    </Col>
                    <Col>
                    <Datepicker
                        value={createdAtFrom}
                        maxValue={createdAtTo ? createdAtTo : undefined}
                        onChange={setCreatedAtFrom}
                      >
                        <MediaInput
                          end={
                            <div style={{alignItems:'center', display:'flex', justifyContent: 'center'}}>
                              {createdAtFrom ? (
                                <CrossIcon
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => {
                                    setCreatedAtFrom(undefined)
                                  }}
                                />
                              ) : (
                                <CalenderIcon style={{ cursor: 'pointer' }} />
                              )}
                            </div>
                          }
                        />
                      </Datepicker>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>to</Label>
                    </Col>
                    <Col>
                    <Datepicker
                        value={createdAtTo}
                        minValue={createdAtFrom ? createdAtFrom : undefined}
                        onChange={setCreatedAtTo}
                      >
                        <MediaInput
                          end={
                            <div style={{alignItems:'center', display:'flex', justifyContent: 'center'}}>
                              {createdAtTo ? (
                                <CrossIcon
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => {
                                    setCreatedAtTo(undefined)
                                  }}
                                />
                              ) : (
                                <CalenderIcon style={{ cursor: 'pointer' }} />
                              )}
                            </div>
                          }
                        />
                      </Datepicker>
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Upated At</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>From</Label>
                    </Col>
                    <Col>
                    <Datepicker
                        value={updatedAtFrom}
                        maxValue={updatedAtTo ? updatedAtTo : undefined}
                        onChange={setUpdatedAtFrom}
                      >
                        <MediaInput
                          end={
                            <div style={{alignItems:'center', display:'flex', justifyContent: 'center'}}>
                              {updatedAtFrom ? (
                                <CrossIcon
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => {
                                    setUpdatedAtFrom(undefined)
                                  }}
                                />
                              ) : (
                                <CalenderIcon style={{ cursor: 'pointer' }} />
                              )}
                            </div>
                          }
                        />
                      </Datepicker>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label>to</Label>
                    </Col>
                    <Col>
                    <Datepicker
                        value={updatedAtTo}
                        minValue={updatedAtFrom ? updatedAtFrom : undefined}
                        onChange={setUpdatedAtTo}
                      >
                        <MediaInput
                          end={
                            <div style={{alignItems:'center', display:'flex', justifyContent: 'center'}}>
                              {updatedAtTo ? (
                                <CrossIcon
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => {
                                    setUpdatedAtTo(undefined)
                                  }}
                                />
                              ) : (
                                <CalenderIcon style={{ cursor: 'pointer' }} />
                              )}
                            </div>
                          }
                        />
                      </Datepicker>
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Description</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Input
                        value={description}
                        onChange={(e) => {
                          setDescription(e.target.value);
                        }}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
              <Row>
                <Col>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <Label>Status</Label>
                    </Col>
                  </ColSection>
                  <ColSection>
                    <Col size={1}>
                      <Label></Label>
                    </Col>
                    <Col>
                      <ProductDropdown
                        options={['Enabled', 'Disabled']}
                        selectedItem={status}
                        onSelect={(item: string) => {
                          // if (item === 'Select') {
                          //   setStatus('');
                          // } else {
                            setStatus(item);
                          // }
                        }}
                        inputValue={undefined}
                        onInputValueChange={function (value: any): void {
                          throw new Error('Function not implemented.');
                        }}
                      />
                    </Col>
                  </ColSection>
                </Col>
              </Row>
            </div>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button onClick={resetFilters}>Reset Filters</Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary isOrange onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close style={{ color: 'white' }} />
        </DrawerModal>
      </Col>
    </Row>
  );
};

export default AttachmentFilterDrawer;
