import { useState } from 'react';
import { LowerPriceOutputInfo } from '../../../gql/graphql';
import LowerPriceTable from '../../table/feedback/LowerPriceTable';

const LowerPriceLayout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
}: {
    rows: LowerPriceOutputInfo[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>(
        undefined,
    );
    // Add more objects as needed
    return (
        <>
            <LowerPriceTable
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default LowerPriceLayout;
