import { useState } from 'react';
import { ProductSuggestionOutputInfo } from '../../../gql/graphql';
import ProductSuggestionTable from '../../table/feedback/ProductSuggestionTable';

const ProductSuggestionLayout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
}: {
    rows: ProductSuggestionOutputInfo[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>(
        undefined,
    );
    // Add more objects as needed

    return (
        <>
            <ProductSuggestionTable
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default ProductSuggestionLayout;
