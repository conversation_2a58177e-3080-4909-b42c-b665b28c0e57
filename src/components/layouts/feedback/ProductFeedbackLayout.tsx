import { useState } from 'react';
import { ProductFeedbackOutputInfo } from '../../../gql/graphql';
import ProductFeedbackTable from '../../table/feedback/ProductFeedbackTable';

const ProductFeedbackLayout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
}: {
    rows: ProductFeedbackOutputInfo[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>(
        undefined,
    );
    // Add more objects as needed

    return (
        <>
            <ProductFeedbackTable
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default ProductFeedbackLayout;
