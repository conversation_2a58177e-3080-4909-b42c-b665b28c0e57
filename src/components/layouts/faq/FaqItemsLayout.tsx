import { useState } from 'react';
import { FaqItemsOutputItem } from '../../../gql/graphql';
import FaqItemsTable from '../../table/faq/FaqItemsTable';

const FaqItemsLayout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
}: {
    rows: FaqItemsOutputItem[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>(
        undefined,
    );
    // Add more objects as needed
    return (
        <>
            <FaqItemsTable
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default FaqItemsLayout;