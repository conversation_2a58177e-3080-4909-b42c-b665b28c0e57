import { useState } from 'react';
import {  } from '../../../gql/graphql';
import FaqPopularTable from '../../table/faq/FaqPopularTable';
import { FaqPopularOutputItem } from '../../../gql/graphql';

const FaqPopularLayout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
}: {
    rows: FaqPopularOutputItem[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>(
        undefined,
    );
    // Add more objects as needed
    return (
        <>
            <FaqPopularTable
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default FaqPopularLayout;