import { useState } from 'react';
import { FaqCategoryOutputItem } from '../../../gql/graphql';
import FaqCategoryTable from '../../table/faq/FaqCategoryTable';

const FaqCategoryLayout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
}: {
    rows: FaqCategoryOutputItem[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>(
        undefined,
    );
    // Add more objects as needed
    return (
        <>
            <FaqCategoryTable
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default FaqCategoryLayout;
