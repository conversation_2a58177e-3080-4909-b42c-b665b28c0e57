import React, { ReactNode, useState } from 'react';
import GenericCustomTopBar from '../../topbar/GenericCustomTopBar';
import {
  DownIcon,
  FilterIcon,
  LeftArrowIcon,
  PlusCircleIcon,
  RefetchIcon,
  ResetIcon,
} from '../../../utils/icons';
import { Span } from '@zendeskgarden/react-typography';
import { Col, Row } from '../../UI-components/Grid';
import {
  Dropdown,
  Field as _Field,
  Menu,
  Trigger,
} from '@zendeskgarden/react-dropdowns';
import {
  Checkbox,
  Fieldset,
  Select,
  Label as _Label,
} from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';
import { Table } from '@tanstack/react-table';
import styled from 'styled-components';
import {
  Button as _Button,
  Buttons as _Buttons,
} from '../../UI-components/Button';
import {
  useScreenDefaultSize,
  useScreenDefaultWidth,
} from '../../../hooks/useScreenSize';
import { useNavigate } from 'react-router-dom';
import { pageRoutes } from '../../navigation/RouteConfig';
import routes from '../../../constants/routes';

const Button = styled(_Button)`
  margin-right: ${baseTheme.space.md};
`;

const Buttons = styled(_Buttons)`
  margin-right: ${baseTheme.space.md};
`;

const Field = styled(_Field)`
  padding: ${baseTheme.space.sm};
`;

const Label = styled(_Label)`
  font-size: ${baseTheme.fontSizes.md};
`;

const searchOptions = [
  {
    label: 'Customer ID',
    value: 'customer_id',
  },
  {
    label: 'First name',
    value: 'name',
  },
  {
    label: 'Mobile No',
    value: 'mobile_no',
  },
  {
    label: 'Email',
    value: 'email',
  },
];

const TopBar = <TData, TValue>({
  searchContent,
  setSearchContent,
  setIsOpen,
  refetch,
  reset,
  table,
  disabledColumn,
  alreadyEnabledColumn,
  setAlreadyEnabledColumn,
  haveSearchDropdown,
  searchType,
  setSearchType,
  handleSearch,
  backFlag,
  backAction,
  haveRefetch,
}: {
  searchContent: string | undefined;
  setSearchContent: React.Dispatch<React.SetStateAction<string | undefined>>;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  refetch: () => void;
  reset: () => void;
  table?: Table<TData>;
  disabledColumn?: string[];
  alreadyEnabledColumn?: string[];
  setAlreadyEnabledColumn?: React.Dispatch<React.SetStateAction<string[]>>;
  haveSearchDropdown?: boolean;
  searchType?: any;
  setSearchType?: React.Dispatch<React.SetStateAction<any>>;
  handleSearch: () => void;
  backFlag?: boolean;
  backAction?: () => void;
  haveRefetch?: boolean;
}) => {
  const [rotated, setRotated] = useState<boolean | undefined>();
  const [isChecked, setIsChecked] = useState(false);

  const handleCheckboxChange = (event: any, column: any, header: string) => {
    const { checked } = event.target;
    setIsChecked(checked);
    if (checked === true) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) => [...prev, header]);

      column.toggleVisibility(true);
    } else if (checked === false) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) =>
          prev.filter((item) => item !== header),
        );

      column.toggleVisibility(false);
    }
  };

  const isDefaultScreen = useScreenDefaultSize();

  const navigate = useNavigate();

  const isSmallScreen = useScreenDefaultWidth();

  return (
    <>
      <GenericCustomTopBar
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        handleSearch={() => {
          handleSearch();
        }}
        searchPlaceholder="Search by Customer Id, Name & Mobile Number"
        haveSearchDropdown={haveSearchDropdown}
        searchType={searchType}
        setSearchType={setSearchType}
        searchOptions={searchOptions}
        width="100%"
      >
        <Col size={7}>
          <Row justifyContent="end" alignItems="center">
            {backFlag && backAction && (
              <Buttons
                size="medium"
                isAction
                onClick={() => {
                  backAction();
                }}
              >
                <Buttons.StartIcon>
                  <LeftArrowIcon />
                </Buttons.StartIcon>
                Back
              </Buttons>
            )}
            <Button
              size="medium"
              isAction
              onClick={() => {
                setIsOpen(true);
              }}
            >
              <Button.StartIcon>
                <FilterIcon />
              </Button.StartIcon>
              <Span>Filter</Span>
            </Button>
            {haveRefetch && (
              <Button size="medium" isAction onClick={() => refetch()}>
                <Button.StartIcon>
                  <RefetchIcon />
                </Button.StartIcon>
                <Span>Refetch</Span>
              </Button>
            )}

            <Button
              size="medium"
              isAction
              onClick={() => {
                reset();
              }}
            >
              <Button.StartIcon>
                <ResetIcon />
              </Button.StartIcon>
              <Span>{'Reset '}</Span>
            </Button>
            {!backFlag && (
              <Button
                size="medium"
                isAction
                isPrimary
                onClick={() => {
                  navigate(`${pageRoutes['GO_TO_CUSTOMER_INFO']}/add`);
                }}
              >
                <Span>Add new customer</Span>
                <Button.EndIcon>
                  <PlusCircleIcon />
                </Button.EndIcon>
              </Button>
            )}

            <Dropdown
              onSelect={(item) => alert(`You planted a ${item}`)}
              onStateChange={(options) =>
                Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
              }
            >
              <Trigger>
                <Button isPrimary>
                  Select Item
                  <Button.EndIcon isRotated={rotated}>
                    <DownIcon
                      style={{
                        height: baseTheme.iconSizes.md,
                        width: baseTheme.iconSizes.md,
                      }}
                    />
                  </Button.EndIcon>
                </Button>
              </Trigger>
              <Menu
                style={{
                  width: baseTheme.components.dimension.width.base200,
                  transform: 'translateX(4px)',
                  borderRadius: baseTheme.borderRadii.lg,
                }}
              >
                <Fieldset>
                  {table &&
                    table
                      .getAllColumns()
                      .filter((column) => column.getCanHide())
                      .map((column) => {
                        return (
                          <>
                            {!column.columnDef.enableHiding &&
                              !column.columnDef.enableColumnFilter && (
                                <Field>
                                  <Checkbox
                                    disabled={disabledColumn?.includes(
                                      column.columnDef.header as string,
                                    )}
                                    key={column.id}
                                    checked={alreadyEnabledColumn?.includes(
                                      column.columnDef.header as string,
                                    )}
                                    onChange={(e) =>
                                      handleCheckboxChange(
                                        e,
                                        column,
                                        column.columnDef.header as string,
                                      )
                                    }
                                  >
                                    <Label>
                                      <>
                                        {column.columnDef.header as ReactNode}
                                      </>
                                    </Label>
                                  </Checkbox>
                                </Field>
                              )}
                          </>
                        );
                      })}
                </Fieldset>
              </Menu>
            </Dropdown>
          </Row>
        </Col>
      </GenericCustomTopBar>
    </>
  );
};

export default TopBar;
