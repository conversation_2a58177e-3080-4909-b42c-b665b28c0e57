import React, { useEffect, useState } from 'react';
import GenericCustomTopBar from '../../topbar/GenericCustomTopBar';
import TopBar from './TopBar';
import CustomersTable from '../../table/customer/CustomersTable';
import useAxios from '../../../hooks/useCommonAxios';
import { useQuery } from '@tanstack/react-query';
import CustomerFilter from '../../drawer/customer/CustomerFilters';
import LazyLoading from '../../UI-components/LazyLoading';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

export interface ICustomerList {
  data: any[];
  totalCount: number;
  totalPages: number;
}

const CustomerDetailsLayout = () => {
  const axios = useAxios();
  const [customerList, setCustomerList] = useState<ICustomerList>();

  const [filters, setFilters] = useState<any>({ page: 0, size: 20 });

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['get-customer-lists'],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          `${krakendPaths.CUSTOMER_URL}/admin-api/v1/customers`,
          {
            params: {
              ...filters,
            },
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': constants.CUSTOMER_API_KEY,
            },
          },
        );

        return response;
      },
      onError: (err) => {
        // console.log('Error', err);
      },
      onSuccess: (response: ICustomerList) => {
        // console.log('Success', data);
        setCustomerList(response);
      },
    });

  const [isOpen, setIsOpen] = useState(false);

  const reset = () => {
    setFilters({ size: 20, page: 0 });
  };

  useEffect(() => {
    if (filters) {
      refetch();
    }
  }, [filters]);

  return (
    <>
      {isLoading || isFetching ? (
        <>
          <LazyLoading />
        </>
      ) : (
        <>
          {customerList && (
            <>
              <CustomersTable
                filters={filters}
                setFilters={setFilters}
                customerList={customerList}
                setIsOpen={setIsOpen}
                refetch={refetch}
              />
              <CustomerFilter
                filters={filters}
                isOpen={isOpen}
                reset={reset}
                setFilters={setFilters}
                setIsOpen={setIsOpen}
              />
            </>
          )}
        </>
      )}
    </>
  );
};

export default CustomerDetailsLayout;
