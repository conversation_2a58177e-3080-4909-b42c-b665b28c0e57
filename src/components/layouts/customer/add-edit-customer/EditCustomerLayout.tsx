import React, { useCallback, useEffect } from 'react';
import { Card } from '../../../UI-components/Card';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import { Button, Buttons } from '../../../UI-components/Button';
import { DownIcon, LeftArrowIcon } from '../../../../utils/icons';
import { useScreenResSize } from '../../../../hooks/useScreenSize';
import { XSM as _XSM } from '../../../UI-components/Typography';
import {
  Field,
  InputGroup,
  Label,
  Message,
  Input as _Input,
} from '@zendeskgarden/react-forms';
import styled from 'styled-components';
import {
  ISelectedItem,
  Item,
  ItemGroup,
  Menu,
} from '@zendeskgarden/react-dropdowns.next';
import { useState } from 'react';
import CustomSelect from '../../../dropdown/customer/CustomerDropdown';
import { baseTheme } from '../../../../themes/theme';
import { Toggle as _Toggle } from '../../../UI-components/Toggle';
import { useCustomerContext } from '../../../../pages/customer/CustomerContext';
import { Address } from './AddCustomerLayout';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import routes from '../../../../constants/routes';
import useToast from '../../../../hooks/useToast';
import StateDropdown from '../../../dropdown/customer/StateDropdown';
import { useNavigate } from 'react-router-dom';
import { pageRoutes } from '../../../navigation/RouteConfig';
import { Spinner } from '@zendeskgarden/react-loaders';
import { convertToSentenceCase } from '../../../../utils/convertToSentenceCase';
import krakendPaths from '../../../../constants/krakendPaths';

const StyledInputGroup = styled(InputGroup)`
  /* stylelint-disable-next-line */
  & > input[aria-autocomplete='list'] {
    position: absolute;
  }
`;

const adrressType = [
  { label: 'Home', value: 'item-1' },
  { label: 'Office', value: 'item-2' },
];

const Row = styled(_Row)`
  margin-top: ${baseTheme.space.one * 25}px;
`;

const XSM = styled(_XSM)`
  text-align: end;
`;

const Toggle = styled(_Toggle)``;

const Input = styled(_Input)`
  background: rgba(32, 83, 117, 0.05);
  color: ${baseTheme.colors.deepBlue};
  border: 1px solid #20537580;
`;

interface ICustomerUpdate {
  address: Address;
  customer_id: number;
  customer_address_id: number;
}

const EditCustomerLayout = () => {
  const isSmallScreen = useScreenResSize();

  const { selectedAddress, setSelectedAddress } = useCustomerContext();

  const [selectedDropdownItem, setSelectedDropdownItem] = useState(
    adrressType[0],
  );

  const [addressData, setAddressData] = useState<Address>({
    firstname: '',
    lastname: '',
    company: '',
    telephone: '',
    street: ['', '', ''],
    city: '',
    region: {
      region_code: '',
      region: '',
      region_id: -1,
    },
    postcode: '',
    country_id: '',
    country_code: '',
    default_shipping: false,
    default_billing: false,
    prefix: '',
    vat_id: '',
    custom_attributes: [],
  });

  useEffect(() => {
    if (selectedAddress) {
      setAddressData({
        firstname: selectedAddress.firstname,
        lastname: selectedAddress.lastname,
        company: selectedAddress.company,
        telephone: selectedAddress.telephone,
        street: [selectedAddress.street, '', ''],
        city: selectedAddress.city,
        region: {
          region_code: 'UP',
          region: selectedAddress.region,
          region_id: selectedAddress.region_id,
        },
        postcode: selectedAddress.postcode,
        country_id: selectedAddress.country_id,
        country_code: selectedAddress.country_code,
        default_billing: selectedAddress.default_billing,
        default_shipping: selectedAddress.default_shipping,
        prefix: selectedAddress?.prefix,
        vat_id: selectedAddress.vat_id,
        custom_attributes: [
          {
            attribute_code: 'alternate_telephone',
            value: selectedAddress.alternate_telephone
              ? selectedAddress.alternate_telephone
              : null,
          },
        ],
      });
    }
  }, [selectedAddress]);

  const [selectedItem, setSelectedItem] = useState<ISelectedItem>({
    value: '+91',
    name: 'call-code',
    type: 'radio',
  });

  const [selectedMobItem, setSelectedMobItem] = useState<ISelectedItem>({
    value: '+91',
    name: 'call-code',
    type: 'radio',
  });

  const [rotated, setRotated] = useState(false);
  const [rotatedMob, setMobRotated] = useState(false);

  const handleChange = useCallback(
    ({
      isExpanded,
      selectedItems,
    }: {
      isExpanded?: boolean;
      selectedItems?: ISelectedItem[];
    }) => {
      if (isExpanded !== undefined) {
        setRotated(isExpanded);
      }
      if (selectedItems !== undefined && selectedItems[0]) {
        setSelectedItem(selectedItems[0]);
      }
    },
    [],
  );

  const handleMobChange = useCallback(
    ({
      isExpanded,
      selectedItems,
    }: {
      isExpanded?: boolean;
      selectedItems?: ISelectedItem[];
    }) => {
      if (isExpanded !== undefined) {
        setMobRotated(isExpanded);
      }
      if (selectedItems !== undefined && selectedItems[0]) {
        setSelectedMobItem(selectedItems[0]);
      }
    },
    [],
  );

  const handleInputChange = (e: any, prop: string) => {
    const value = e.target.value;

    if (prop === 'alternativenumber') {
      setAddressData({
        ...addressData,
        custom_attributes: [
          {
            attribute_code: 'alternate_telephone',
            value: value,
          },
        ],
      });
    } else if (prop === 'firstname') {
      setAddressData({
        ...addressData,
        firstname: value,
      });
    } else if (prop === 'lastname') {
      setAddressData({
        ...addressData,
        lastname: value,
      });
    } else if (prop === 'street1') {
      setAddressData({
        ...addressData,
        street: [value, addressData.street[1], addressData.street[2]],
      });
    } else if (prop === 'street2') {
      setAddressData({
        ...addressData,
        street: [addressData.street[0], value, addressData.street[2]],
      });
    } else if (prop === 'street3') {
      setAddressData({
        ...addressData,
        street: [addressData.street[0], addressData.street[1], value],
      });
    } else if (prop === 'city') {
      setAddressData({
        ...addressData,
        city: value,
      });
    } else if (prop === 'mobile') {
      setAddressData({
        ...addressData,
        telephone: value,
      });
    } else if (prop === 'taxvat') {
      setAddressData({
        ...addressData,
        vat_id: value,
      });
    } else if (prop === 'postalcode') {
      setAddressData({
        ...addressData,
        postcode: value,
      });
    } else if (prop === 'company') {
      setAddressData({
        ...addressData,
        company: value,
      });
    }
  };

  const axios = useAxios();

  const addToast = useToast();

  const { mutate: updateCustomerAddress, isLoading: isUpdating } = useMutation(
    async (customerAddressUpdate: ICustomerUpdate) => {
      const response = await axios.put(
        `${krakendPaths.CUSTOMER_URL}/admin-api/v1/customers/addresses`,
        {
          ...customerAddressUpdate,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': '8tyeXkUYPg2JiP8AXBTj9DuIuTfO3kO9cm',
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        console.log('Error', err);
        addToast('error', `${convertToSentenceCase(err.message)}`);
      },
      onSuccess: (data) => {
        addToast('success', 'Address updated successfully');
      },
    },
  );

  const handleUpdate = () => {
    if (selectedAddress) {
      const customerUpdate: ICustomerUpdate = {
        address: addressData,
        customer_address_id: selectedAddress.id,
        customer_id: selectedAddress.customer_id,
      };
      if (!isValid()) {
        console.log('Error');
      } else {
        updateCustomerAddress(customerUpdate);
      }
    }
  };
  const [currentCountry, setCurrentCountry] = useState<any>();
  const [currentState, setcurrentState] = useState<any>();

  useEffect(() => {
    if (currentCountry) {
      setAddressData({
        ...addressData,
        country_code: currentCountry.value,
        country_id: currentCountry.country_id,
      });
    }
    if (currentState) {
      setAddressData({
        ...addressData,
        region: {
          region: currentState.label,
          region_code: currentState.value,
          region_id: currentState.region_id,
        },
      });
    }
  }, [currentCountry, currentState]);

  const [error, setError] = useState({
    firstname: '',
    lastname: '',
    company: '',
    telephone: '',
    street: '',
    city: '',
    region: '',
    postcode: '',
    country_id: '',
    country_code: '',
    prefix: '',
    vat_id: '',
  });

  const isValid = () => {
    if (addressData.firstname == '') {
      error.firstname = 'Firstname is required !!';
    } else error.firstname = '';
    if (addressData.lastname == '') {
      error.lastname = 'Lastname is required !!';
    } else error.lastname = '';
    if (addressData.company == '') {
      error.company = 'Company is required !!';
    } else error.company = '';
    if (addressData.telephone == '') {
      error.telephone = 'Mobile is required !!';
    } else error.telephone = '';
    if (addressData.street[0] == '') {
      error.street = 'Street is required !!';
    } else error.street = '';
    if (addressData.city == '') {
      error.city = 'City is required !!';
    } else error.city = '';
    if (addressData.postcode == '') {
      error.postcode = 'Postcode is required !!';
    } else error.postcode = '';
    if (addressData.country_id == '') {
      error.country_id = 'Country Id is required !!';
    } else error.country_id = '';
    if (addressData.country_code == '') {
      error.country_code = 'Country is required !!';
    } else error.country_code = '';

    const newError = {
      ...error,
    };

    setError(newError);

    return Object.values(newError).every((val) => val == '');
  };

  const navigate = useNavigate();

  const [validations, setValidationRules] = useState({
    alternate_telephone_required: false,
    emailsignup: false,
    mobilesignup: false,
    postcode_format: false,
    postcode_required: false,
    service_availability_enabled: false,
    state_dropdown_required: false,
    state_required: false,
    tax_format: false,
    tax_label: false,
    tax_required: false,
    telephone_code: '',
    telephone_format: false,
    whatsappsignup: false,
  });

  return (
    <>
      <Card bg="white" style={{ minHeight: '100%' }}>
        <Row
          style={{ position: 'sticky', top: 0 }}
          mt="lg"
          justifyContent="end"
        >
          <Col
            offset={isSmallScreen ? 7.5 : 9.5}
            size={isSmallScreen ? 1.5 : 1}
          >
            <Buttons
              isAction
              onClick={() => {
                if (selectedAddress) {
                  navigate(
                    `${pageRoutes['GO_TO_CUSTOMER_INFO']}/${selectedAddress.customer_id}`,
                  );
                }
              }}
            >
              <Buttons.StartIcon>
                <LeftArrowIcon />
              </Buttons.StartIcon>
              Back
            </Buttons>
          </Col>
        </Row>
        <Row justifyContent="center">
          <Col size={10}>
            <div style={{ height: '100%' }}>
              <Row justifyContent="center">
                <Col offset={-1} size={8}>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Alternative Number</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'alternativenumber');
                          }}
                          value={
                            addressData?.custom_attributes[0]
                              ?.attribute_code === 'alternate_telephone' &&
                            addressData?.custom_attributes[0]?.value != null
                              ? addressData.custom_attributes[0]?.value
                              : ''
                          }
                        />
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Default Billing Address</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Toggle
                          onClick={() => {
                            setAddressData({
                              ...addressData,
                              default_billing: !addressData.default_billing,
                            });
                          }}
                          checked={addressData.default_billing}
                        >
                          <Label hidden>Yes</Label>
                        </Toggle>
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Default Shipping Address</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Toggle
                          onClick={() => {
                            setAddressData({
                              ...addressData,
                              default_shipping: !addressData.default_shipping,
                            });
                          }}
                          checked={addressData.default_shipping}
                        >
                          <Label hidden>Yes</Label>
                        </Toggle>
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>First Name</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'firstname');
                          }}
                          value={addressData.firstname}
                          validation={
                            error.firstname != '' ? 'error' : undefined
                          }
                        />
                        {error.firstname != '' && (
                          <Message validation="error">
                            {error.firstname}
                          </Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Last Name</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'lastname');
                          }}
                          value={addressData.lastname}
                          validation={
                            error.lastname != '' ? 'error' : undefined
                          }
                        />
                        {error.lastname != '' && (
                          <Message validation="error">{error.lastname}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Street Address</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'street1');
                          }}
                          value={addressData.street[0]}
                          validation={error.street != '' ? 'error' : undefined}
                        />
                        {error.street != '' && (
                          <Message validation="error">{error.street}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row>
                    <Col></Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'street2');
                          }}
                        />
                      </Field>
                    </Col>
                  </Row>
                  <Row>
                    <Col></Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'street3');
                          }}
                        />
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>City</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'city');
                          }}
                          value={addressData.city}
                          validation={error.city != '' ? 'error' : undefined}
                        />
                        {error.city != '' && (
                          <Message validation="error">{error.city}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Company</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'company');
                          }}
                          value={addressData.company}
                          validation={error.company != '' ? 'error' : undefined}
                        />
                        {error.company != '' && (
                          <Message validation="error">{error.company}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Country & State</XSM>
                    </Col>
                    <Col>
                      <StateDropdown
                        labelHidden={true}
                        setcurrentCountry={setCurrentCountry}
                        setcurrentState={setcurrentState}
                        setValidationRules={setValidationRules}
                      />
                      {error.country_code != '' && (
                        <Message validation="error">
                          {error.country_code}
                        </Message>
                      )}
                    </Col>
                  </Row>

                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Zip/Postal Code</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'postalcode');
                          }}
                          value={addressData.postcode}
                          validation={
                            error.postcode != '' ? 'error' : undefined
                          }
                        />
                        {error.postcode != '' && (
                          <Message validation="error">{error.postcode}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Mobile Number</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'mobile');
                          }}
                          value={addressData.telephone}
                        />

                        {error.telephone != '' && (
                          <Message validation="error">
                            {error.telephone}
                          </Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Tax/VAT Number</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'taxvat');
                          }}
                          value={addressData.vat_id}
                          validation={error.vat_id != '' ? 'error' : undefined}
                        />
                        {error.vat_id != '' && (
                          <Message validation="error">{error.vat_id}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
        <Row
          style={{ height: baseTheme.components.dimension.height.base150 }}
          justifyContent="end"
        >
          <Col size={2.5}>
            <Button
              onClick={() => {
                handleUpdate();
              }}
              isPrimary
            >
              {isUpdating ? <Spinner /> : 'Save'}
            </Button>
          </Col>
        </Row>
      </Card>
    </>
  );
};

export default EditCustomerLayout;
