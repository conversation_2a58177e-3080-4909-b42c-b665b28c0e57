import React, { useCallback, useEffect } from 'react';
import { Card } from '../../../UI-components/Card';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import { Button, Buttons } from '../../../UI-components/Button';
import { DownIcon, LeftArrowIcon } from '../../../../utils/icons';
import { useScreenResSize } from '../../../../hooks/useScreenSize';
import { XSM as _XSM } from '../../../UI-components/Typography';
import {
  Field,
  InputGroup,
  Label,
  Message,
  Input as _Input,
} from '@zendeskgarden/react-forms';
import styled from 'styled-components';
import {
  ISelectedItem,
  Item,
  ItemGroup,
  Menu,
} from '@zendeskgarden/react-dropdowns.next';
import { useState } from 'react';
import CustomSelect from '../../../dropdown/customer/CustomerDropdown';
import { baseTheme } from '../../../../themes/theme';
import { Toggle as _Toggle } from '../../../UI-components/Toggle';
import { useNavigate, useParams } from 'react-router-dom';
import { pageRoutes } from '../../../navigation/RouteConfig';
import useAxios from '../../../../hooks/useCommonAxios';
import { useMutation } from '@tanstack/react-query';
import routes from '../../../../constants/routes';
import useToast from '../../../../hooks/useToast';
import StateDropdown from '../../../dropdown/customer/StateDropdown';
import { validate } from 'graphql';
import { Spinner } from '@zendeskgarden/react-loaders';
import { convertToSentenceCase } from '../../../../utils/convertToSentenceCase';
import krakendPaths from '../../../../constants/krakendPaths';

const StyledInputGroup = styled(InputGroup)`
  /* stylelint-disable-next-line */
  & > input[aria-autocomplete='list'] {
    position: absolute;
  }
`;

export const temporaryCountryOptions: any[] = [
  {
    label: 'India',
    value: 'India',
  },
  {
    label: 'United States',
    value: 'United States',
  },
  {
    label: 'Canada',
    value: 'Canada',
  },
  {
    label: 'United Kingdom',
    value: 'United Kingdom',
  },
  {
    label: 'Australia',
    value: 'Australia',
  },
  // Add more country options as needed
];

export const indiaStates: string[] = [
  'Andaman and Nicobar Islands',
  'Andhra Pradesh',
  'Arunachal Pradesh',
  'Assam',
  'Bihar',
  'Chandigarh',
  'Chhattisgarh',
  'Dadra and Nagar Haveli and Daman and Diu',
  'Delhi',
  'Goa',
  'Gujarat',
  'Haryana',
  'Himachal Pradesh',
  'Jharkhand',
  'Karnataka',
  'Kerala',
  'Ladakh',
  'Lakshadweep',
  'Madhya Pradesh',
  'Maharashtra',
  'Manipur',
  'Meghalaya',
  'Mizoram',
  'Nagaland',
  'Odisha',
  'Puducherry',
  'Punjab',
  'Rajasthan',
  'Sikkim',
  'Tamil Nadu',
  'Telangana',
  'Tripura',
  'Uttar Pradesh',
  'Uttarakhand',
  'West Bengal',
];

export const indiaStatesOptions = indiaStates.map((state) => ({
  label: state,
  value: state,
}));

export interface Address {
  firstname: string;
  lastname: string;
  company: string;
  telephone: string;
  street: string[];
  city: string;
  region: {
    region_code: string;
    region: string;
    region_id: number;
  };
  postcode: string;
  country_id: string;
  country_code: string;
  default_shipping: boolean;
  default_billing: boolean;
  prefix: string;
  vat_id: string;
  custom_attributes: {
    attribute_code: string;
    value: string | null;
  }[];
}

interface CustomerAddressData {
  address: Address;
  customer_id: number;
}

const Row = styled(_Row)`
  margin-top: ${baseTheme.space.one * 25}px;
`;

const XSM = styled(_XSM)`
  text-align: end;
`;

const Toggle = styled(_Toggle)``;

const Input = styled(_Input)`
  background: rgba(32, 83, 117, 0.05);
  color: ${baseTheme.colors.deepBlue};
  border: 1px solid #20537580;
`;

const adrressType = [
  { label: 'Home', value: 'item-1' },
  { label: 'Office', value: 'item-2' },
];

const AddCustomerLayout = () => {
  const isSmallScreen = useScreenResSize();

  const { customerId } = useParams();

  const [customerAddressData, setCustomerAddressData] =
    useState<CustomerAddressData>();

  const [addressData, setAddressData] = useState<Address>({
    firstname: '',
    lastname: '',
    company: '',
    telephone: '',
    street: ['', '', ''],
    city: '',
    region: {
      region_code: '',
      region: '',
      region_id: -1,
    },
    postcode: '',
    country_id: '',
    country_code: '',
    default_shipping: false,
    default_billing: false,
    prefix: '',
    vat_id: '',
    custom_attributes: [
      {
        attribute_code: 'alternate_telephone',
        value: null,
      },
    ],
  });

  const [selectedItem, setSelectedItem] = useState<ISelectedItem>({
    value: '+91',
    name: 'call-code',
    type: 'radio',
  });

  const [selectedMobItem, setSelectedMobItem] = useState<ISelectedItem>({
    value: '+91',
    name: 'call-code',
    type: 'radio',
  });

  const [rotated, setRotated] = useState(false);
  const [rotatedMob, setMobRotated] = useState(false);

  const handleChange = useCallback(
    ({
      isExpanded,
      selectedItems,
    }: {
      isExpanded?: boolean;
      selectedItems?: ISelectedItem[];
    }) => {
      if (isExpanded !== undefined) {
        setRotated(isExpanded);
      }
      if (selectedItems !== undefined && selectedItems[0]) {
        setSelectedItem(selectedItems[0]);
      }
    },
    [],
  );

  const handleMobChange = useCallback(
    ({
      isExpanded,
      selectedItems,
    }: {
      isExpanded?: boolean;
      selectedItems?: ISelectedItem[];
    }) => {
      if (isExpanded !== undefined) {
        setMobRotated(isExpanded);
      }
      if (selectedItems !== undefined && selectedItems[0]) {
        setSelectedMobItem(selectedItems[0]);
      }
    },
    [],
  );

  const [selectedDropdownItem, setSelectedDropdownItem] = useState(
    adrressType[0],
  );

  const navigate = useNavigate();

  const handleInputChange = (e: any, prop: string) => {
    const value = e.target.value;

    if (prop === 'alternativenumber') {
      const inputValue = value.replace(/[^0-9]/g, '');
      setAddressData({
        ...addressData,
        custom_attributes: [
          {
            attribute_code: 'alternate_telephone',
            value: inputValue,
          },
        ],
      });
    } else if (prop === 'firstname') {
      setAddressData({
        ...addressData,
        firstname: value,
      });
    } else if (prop === 'lastname') {
      setAddressData({
        ...addressData,
        lastname: value,
      });
    } else if (prop === 'street1') {
      setAddressData({
        ...addressData,
        street: [value, addressData.street[1], addressData.street[2]],
      });
    } else if (prop === 'street2') {
      setAddressData({
        ...addressData,
        street: [addressData.street[0], value, addressData.street[2]],
      });
    } else if (prop === 'street3') {
      setAddressData({
        ...addressData,
        street: [addressData.street[0], addressData.street[1], value],
      });
    } else if (prop === 'city') {
      setAddressData({
        ...addressData,
        city: value,
      });
    } else if (prop === 'mobile') {
      const inputValue = value.replace(/[^0-9]/g, '');
      setAddressData({
        ...addressData,
        telephone: inputValue,
      });
    } else if (prop === 'taxvat') {
      setAddressData({
        ...addressData,
        vat_id: value,
      });
    } else if (prop === 'postalcode') {
      setAddressData({
        ...addressData,
        postcode: value,
      });
    } else if (prop === 'company') {
      setAddressData({
        ...addressData,
        company: value,
      });
    }
  };

  const axios = useAxios();

  const { mutate: createCustomerAddress, isLoading: isCreating } = useMutation(
    async (customerAddress: CustomerAddressData) => {
      const response = axios.post(
        `${krakendPaths.CUSTOMER_URL}/admin-api/v1/customers/addresses`,
        {
          ...customerAddress,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': '8tyeXkUYPg2JiP8AXBTj9DuIuTfO3kO9cm',
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        // console.log('Error', `${convertToSentenceCase(err.message)}`);
        addToast('error', `${convertToSentenceCase(err.message)}`);
      },
      onSuccess: (data) => {
        addToast('success', 'Customer Address added successfully');
        setAddressData({
          firstname: '',
          lastname: '',
          company: '',
          telephone: '',
          street: ['', '', ''],
          city: '',
          region: {
            region_code: '',
            region: '',
            region_id: -1,
          },
          postcode: '',
          country_id: '',
          country_code: '',
          default_shipping: false,
          default_billing: false,
          prefix: '',
          vat_id: '',
          custom_attributes: [
            {
              attribute_code: 'alternate_telephone',
              value: null,
            },
          ],
        });
      },
    },
  );

  const addToast = useToast();

  const handleSaveClick = () => {
    if (customerId) {
      const customerAddressCreate: CustomerAddressData = {
        address: addressData,
        customer_id: parseInt(customerId),
      };
      if (!isValid()) {
        console.log('Error');
      } else {
        // console.log('Success', error);
        createCustomerAddress(customerAddressCreate);
      }
    } else {
      addToast('error', 'Invalid Customer Id');
    }
  };

  const [currentCountry, setCurrentCountry] = useState<any>();
  const [currentState, setcurrentState] = useState<any>();

  useEffect(() => {
    if (currentCountry) {
      setAddressData({
        ...addressData,
        country_code: currentCountry.value,
        country_id: currentCountry.country_id,
      });
    }
    if (currentState) {
      setAddressData({
        ...addressData,
        region: {
          region: currentState.label,
          region_code: currentState.value,
          region_id: currentState.region_id,
        },
      });
    }
  }, [currentCountry, currentState]);

  const [error, setError] = useState({
    firstname: '',
    lastname: '',
    company: '',
    telephone: '',
    street: '',
    city: '',
    region: '',
    postcode: '',
    country_id: '',
    country_code: '',
    prefix: '',
    vat_id: '',
    alternate_telephone: '',
  });

  const [validations, setValidationRules] = useState({
    alternate_telephone_required: false,
    emailsignup: false,
    mobilesignup: false,
    postcode_format: false,
    postcode_required: false,
    service_availability_enabled: false,
    state_dropdown_required: false,
    state_required: false,
    tax_format: false,
    tax_label: false,
    tax_required: false,
    telephone_code: '',
    telephone_format: false,
    whatsappsignup: false,
  });

  const isValid = () => {
    if (addressData.firstname == '') {
      error.firstname = 'Firstname is required !!';
    } else error.firstname = '';
    if (addressData.lastname == '') {
      error.lastname = 'Lastname is required !!';
    } else error.lastname = '';
    if (addressData.company == '') {
      error.company = 'Company is required !!';
    } else error.company = '';
    if (addressData.telephone == '') {
      error.telephone = 'Mobile is required !!';
    } else error.telephone = '';
    if (addressData.street[0] == '') {
      error.street = 'Street is required !!';
    } else error.street = '';
    if (addressData.city == '') {
      error.city = 'City is required !!';
    } else error.city = '';
    if (validations.state_required && addressData.region.region_code == '') {
      error.region = 'Region Code is required !!';
    } else error.region = '';
    if (validations.state_required && addressData.region.region == '') {
      error.region = 'Region is required !!';
    } else error.region = '';
    if (validations.state_required && addressData.region.region_id == -1) {
      error.region = 'Region Id is required !!';
    } else error.region = '';
    if (validations.postcode_required && addressData.postcode == '') {
      error.postcode = 'Postcode is required !!';
    } else error.postcode = '';
    if (addressData.country_id == '') {
      error.country_id = 'Country Id is required !!';
    } else error.country_id = '';
    if (addressData.country_code == '') {
      error.country_code = 'Country is required !!';
    } else error.country_code = '';

    if (validations.alternate_telephone_required) {
      addressData.custom_attributes.forEach((item) => {
        if (item.attribute_code === 'alternate_telephone') {
          if (item.value == null || item.value === '') {
            error.alternate_telephone = 'Alternate Telephone is required !!';
          } else {
            error.alternate_telephone = '';
          }
        }
      });
    } else {
      error.alternate_telephone = '';
    }

    const newError = {
      ...error,
    };

    setError(newError);

    return Object.values(newError).every((val) => val == '');
  };

  return (
    <>
      <Card bg="white" style={{ minHeight: '100%' }}>
        <Row
          style={{ position: 'sticky', top: 0 }}
          mt="lg"
          justifyContent="end"
        >
          <Col
            offset={isSmallScreen ? 7.5 : 9.5}
            size={isSmallScreen ? 1.5 : 1}
          >
            <Buttons
              isAction
              onClick={() => {
                navigate(`${pageRoutes['GO_TO_CUSTOMER_INFO']}/${customerId}`);
              }}
            >
              <Buttons.StartIcon>
                <LeftArrowIcon />
              </Buttons.StartIcon>
              Back
            </Buttons>
          </Col>
        </Row>
        <Row justifyContent="center">
          <Col size={10}>
            <div style={{ height: '100%' }}>
              <Row justifyContent="center">
                <Col offset={-1} size={8}>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Alternative Number</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'alternativenumber');
                          }}
                          value={
                            addressData.custom_attributes.find(
                              (attr) =>
                                attr.attribute_code === 'alternate_telephone',
                            )?.value || ''
                          }
                          validation={
                            error.alternate_telephone != ''
                              ? 'error'
                              : undefined
                          }
                        />
                        {error.alternate_telephone != '' && (
                          <Message validation="error">
                            {error.alternate_telephone}
                          </Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Default Billing Address</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Toggle
                          onClick={() => {
                            setAddressData({
                              ...addressData,
                              default_billing: !addressData.default_billing,
                            });
                          }}
                          checked={addressData.default_billing}
                        >
                          <Label hidden>Yes</Label>
                        </Toggle>
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Default Shipping Address</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Toggle
                          onClick={() => {
                            setAddressData({
                              ...addressData,
                              default_shipping: !addressData.default_shipping,
                            });
                          }}
                          checked={addressData.default_shipping}
                        >
                          <Label hidden>Yes</Label>
                        </Toggle>
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>First Name</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'firstname');
                          }}
                          validation={
                            error.firstname != '' ? 'error' : undefined
                          }
                          value={addressData.firstname}
                        />
                        {error.firstname != '' && (
                          <Message validation="error">
                            {error.firstname}
                          </Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Last Name</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'lastname');
                          }}
                          value={addressData.lastname}
                        />
                        {error.lastname != '' ? (
                          <Message validation="error">{error.lastname}</Message>
                        ) : (
                          <></>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Street Address</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'street1');
                          }}
                          validation={error.street != '' ? 'error' : undefined}
                        />
                        {error.street != '' && (
                          <Message validation="error">{error.street}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row>
                    <Col></Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'street2');
                          }}
                        />
                      </Field>
                    </Col>
                  </Row>
                  <Row>
                    <Col></Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'street3');
                          }}
                        />
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>City</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'city');
                          }}
                          validation={error.city != '' ? 'error' : undefined}
                        />
                        {error.city != '' && (
                          <Message validation="error">{error.city}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Company</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'company');
                          }}
                          validation={error.company != '' ? 'error' : undefined}
                        />
                        {error.company != '' && (
                          <Message validation="error">{error.company}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Country & State</XSM>
                    </Col>
                    <Col>
                      <StateDropdown
                        labelHidden={true}
                        setcurrentCountry={setCurrentCountry}
                        setcurrentState={setcurrentState}
                        setValidationRules={setValidationRules}
                      />
                      {error.country_code != '' && (
                        <Message validation="error">
                          {error.country_code}
                        </Message>
                      )}
                    </Col>
                  </Row>

                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Zip/Postal Code</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'postalcode');
                          }}
                          validation={
                            error.postcode != '' ? 'error' : undefined
                          }
                        />
                        {error.postcode != '' && (
                          <Message validation="error">{error.postcode}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Mobile Number</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'mobile');
                          }}
                          value={addressData.telephone}
                        />
                        {error.telephone != '' && (
                          <Message validation="error">
                            {error.telephone}
                          </Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                  <Row alignItems="center">
                    <Col>
                      <XSM isBold>Tax/VAT Number</XSM>
                    </Col>
                    <Col>
                      <Field>
                        <Input
                          onChange={(e) => {
                            handleInputChange(e, 'taxvat');
                          }}
                          validation={error.vat_id != '' ? 'error' : undefined}
                        />
                        {error.vat_id != '' && (
                          <Message validation="error">{error.vat_id}</Message>
                        )}
                      </Field>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
        <Row
          style={{ height: baseTheme.components.dimension.height.base150 }}
          justifyContent="end"
        >
          <Col size={2.5}>
            <Button
              onClick={() => {
                handleSaveClick();
              }}
              isPrimary
            >
              {isCreating ? <Spinner /> : 'Save'}
            </Button>
          </Col>
        </Row>
      </Card>
    </>
  );
};

export default AddCustomerLayout;
