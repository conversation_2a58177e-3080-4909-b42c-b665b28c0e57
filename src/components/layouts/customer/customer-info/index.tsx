import React, { useEffect, useState } from 'react';
import { Card } from '../../../UI-components/Card';
import { Col, Row } from '../../../UI-components/Grid';
import { Button, Buttons } from '../../../UI-components/Button';
import { AddIcon, LeftArrowIcon, ResetIcon } from '../../../../utils/icons';
import { baseTheme } from '../../../../themes/theme';
import styled from 'styled-components';
import { MD } from '../../../UI-components/Typography';
import { useColor } from '../../../../hooks/useColorContext';
import CustomerView from './CustomerView';
import AccountInfo from './AccountInfo';
import Address from './Address';
import {
  useScreenResSize,
  useScreenSize,
} from '../../../../hooks/useScreenSize';
import { PlusCircleIcon } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import { pageRoutes } from '../../../navigation/RouteConfig';
import { useMutation, useQuery } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useCommonAxios';
import routes from '../../../../constants/routes';
import useToast from '../../../../hooks/useToast';
import CustomerContextProvider, {
  useCustomerContext,
} from '../../../../pages/customer/CustomerContext';
import { IAddress } from '../../investor-relation/InvestorBannerLayout';
import EditAccountInfo, { IAdminCustomerUpdate } from './EditAccountInfo';
import { isValid } from 'date-fns';
import { convertToSentenceCase } from '../../../../utils/convertToSentenceCase';
import { IItem } from '../../../../types/types';
import { Spinner } from '@zendeskgarden/react-loaders';
import AddAddressModal from '../../../modal/customer/AddAddress';
import krakendPaths from '../../../../constants/krakendPaths';

// const StyledCard = styled.div`
//   border-top-left-radius: 40px;
//   border: 2px solid black;
// `;

interface ContainerProps {
  align?: string;
  selected?: boolean;
}

const StyledCard = styled.div``;

const Container = styled.div`
  border-top-left-radius: 40px;
  background-color: ${baseTheme.colors.deepBlue};
  width: 100%;
  padding: 0px 12px 0px 12px;
  height: ${baseTheme.components.dimension.height.base * 6}px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const SubContainer = styled.div<ContainerProps>`
  width: 100%;
  padding: 0px 12px 0px 12px;
  height: ${baseTheme.components.dimension.height.base * 6}px;
  display: flex;
  align-items: center;
  justify-content: ${(p) => (p.align ? p.align : 'left')};
  border-bottom: 2px solid #20537533;
  ${(p) =>
    p.selected ? `border-left: 2px solid ${baseTheme.colors.deepBlue};` : ''}

  cursor: pointer;
`;

export enum registration_type_enum {
  EMAIL = 'email',
  MOBILE = 'mobile',
  API = 'api',
  GOOGLE = 'google',
  WHATSAPP = 'whatsapp',
  NONE = 'none',
}

export interface ICustomerCreate {
  registration_platform: string;
  registration_type: registration_type_enum;
  customer_group_id: number;
  firstname: string;
  lastname: string;
  email?: string;
  password: string;
  mobile?: string;
  taxvat?: string;
}

export interface ICustomerGroups {
  id: number;
  group_code: string;
  created_at: string;
  updated_at: string;
  deleted_at: null | string;
}

const CustomerInformationLayout = ({ add }: { add: boolean }) => {
  const { selectedColor } = useColor();
  const [section, setSection] = useState<
    'customerView' | 'accountInfo' | 'address'
  >('customerView');

  const [customerCreate, setCustomerCreate] = useState<ICustomerCreate>();
  const [customerUpdate, setCustomerUpdate] = useState<IAdminCustomerUpdate>();
  const [customerGroups, setCustomerGroups] = useState<IItem[]>();
  const [customerViewData, setCustomerViewData] = useState<any>();
  const { refetchSummary, customerSummary, isSummaryLoading } =
    useCustomerContext();

  const handleContainerClick = (selectedSection: any) => {
    setSection(selectedSection);
  };

  const isSmallScreen = useScreenResSize();

  const navigate = useNavigate();

  const { customerId } = useParams();

  useEffect(() => {
    if (customerId && !add) {
      setSection('customerView');
    }
    if (add) {
      setSection('accountInfo');
    }
    refetchSummary();
  }, []);

  useEffect(() => {
    if (customerSummary) {
      setCustomerViewData(customerSummary);
    }
  }, [customerSummary]);

  const axios = useAxios();

  const addToast = useToast();

  const {} = useQuery({
    queryKey: ['get-all-customer-groups'],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(
        `${krakendPaths.CUSTOMER_URL}/admin-api/v1/customer-group`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': '8tyeXkUYPg2JiP8AXBTj9DuIuTfO3kO9cm',
          },
        },
      );

      return response;
    },
    onError: (err) => {
      // console.log('Error', err);
    },
    onSuccess: (response) => {
      const transformedCustomerGroups = response?.customer_groups?.map(
        (group: ICustomerGroups) => ({
          value: group.id,
          label: group.group_code,
        }),
      );
      setCustomerGroups(transformedCustomerGroups);
    },
  });

  const [error, setError] = useState({
    registration_platform: '',
    registration_type: '',
    customer_group_id: '',
    firstname: '',
    lastname: '',
    password: '',
    email: '',
  });

  const [updateError, setUpdateError] = useState({
    registration_platform: '',
    registration_type: '',
    customer_group_id: '',
    firstname: '',
    lastname: '',
  });

  const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;

  const isValid = () => {
    if (customerCreate) {
      if (customerCreate.firstname == '') {
        error.firstname = 'Firstname is required !!';
      } else error.firstname = '';
      if (customerCreate.lastname == '') {
        error.lastname = 'Lastname is required !!';
      } else error.lastname = '';
      if (customerCreate.customer_group_id == -1) {
        error.customer_group_id = 'Customer group Id required !!';
      } else error.customer_group_id = '';
      if (customerCreate.registration_platform == '') {
        error.registration_platform = 'Register platform required !!';
      } else error.registration_platform = '';
      if (customerCreate.registration_type == registration_type_enum.NONE) {
        error.registration_type = 'Registration type required !!';
      } else error.registration_type = '';
      if (customerCreate.password == '') {
        error.password = 'Password is Required !!';
      } else error.password = '';
      if (customerCreate.email) {
        if (customerCreate.email.match(emailPattern)) {
          error.email = '';
        } else error.email = 'Valid email is required !!';
      }
      const newError = {
        ...error,
      };

      setError(newError);

      return Object.values(newError).every((val) => val == '');
    }
  };

  const isUpdateValid = () => {
    if (customerUpdate) {
      if (customerUpdate.firstname == '') {
        error.firstname = 'Firstname is required !!';
      } else error.firstname = '';
      if (customerUpdate.lastname == '') {
        error.lastname = 'Lastname is required !!';
      } else error.lastname = '';
      if (customerUpdate.group_id == -1) {
        error.customer_group_id = 'Customer group Id required !!';
      } else error.customer_group_id = '';
      if (customerUpdate.registration_platform == '') {
        error.registration_platform = 'Register platform required !!';
      } else error.registration_platform = '';
      if (customerUpdate.registration_type == registration_type_enum.NONE) {
        error.registration_type = 'Registration type required !!';
      } else error.registration_type = '';
      const newError = {
        ...error,
      };

      setUpdateError(newError);

      // console.log('Error', newError);

      return Object.values(newError).every((val) => val == '');
    }
  };

  const handleCreate = () => {
    if (isValid()) {
      createCustomer(customerCreate);
    }
  };

  const handleUpdate = () => {
    if (isUpdateValid()) {
      updateCustomer(customerUpdate);
      // console.log('Customer update', customerUpdate);
    } else {
      console.log('Error');
    }
  };

  const { mutate: createCustomer, isLoading: isCustomerCreating } = useMutation(
    async (customerdata: any) => {
      const response = await axios.post(
        `${krakendPaths.CUSTOMER_URL}/admin-api/v1/customers`,
        { ...customerdata },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': '8tyeXkUYPg2JiP8AXBTj9DuIuTfO3kO9cm',
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        // console.log(err);
        addToast('error', `${convertToSentenceCase(err.message)}`);
      },
      onSuccess: (data: any) => {
        // console.log(data);
        addToast('success', 'Customer created successfully');
        setTimeout(() => {
          // navigate(`${pageRoutes.GO_TO_CUSTOMER_INFO}/${data?.id}`);
          navigate(`${pageRoutes.GO_TO_CUSTOMER_DETAILS}`);
        }, 1000);
      },
    },
  );

  const { mutate: updateCustomer, isLoading: isUpdatingCustomer } = useMutation(
    async (customerdata: any) => {
      const response = await axios.put(
        `${krakendPaths.CUSTOMER_URL}/admin-api/v1/customers`,
        { ...customerdata },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': '8tyeXkUYPg2JiP8AXBTj9DuIuTfO3kO9cm',
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        // console.log(err);
        addToast('error', `${convertToSentenceCase(err.message)}`);
      },
      onSuccess: (data) => {
        // console.log(data);
        addToast('success', 'Customer updated successfully');
      },
    },
  );

  const [addSddressShow, setAddAddressShow] = useState<boolean>(false);

  return (
    <>
      <Card bg="white" style={{ minHeight: '100%' }}>
        <Row
          style={{
            position: 'sticky',
            top: 0,
            minHeight: baseTheme.components.dimension.height.md,
            zIndex: 9000,
          }}
          mt="lg"
          justifyContent="end"
        >
          {section === 'accountInfo' ? (
            <>
              <Col
                offset={isSmallScreen ? 4.5 : 6}
                size={isSmallScreen ? 1.2 : 0.9}
              >
                <Buttons
                  onClick={() => {
                    navigate(`${pageRoutes['GO_TO_CUSTOMER_DETAILS']}`);
                  }}
                  isAction
                >
                  <Buttons.StartIcon>
                    <LeftArrowIcon />
                  </Buttons.StartIcon>
                  Back
                </Buttons>
              </Col>
              <Col size={isSmallScreen ? 2 : 2}>
                <Button
                  onClick={() => {
                    if (add == true) {
                      handleCreate();
                    } else if (add == false) {
                      handleUpdate();
                    }
                  }}
                  isPrimary
                >
                  {isUpdatingCustomer || isCustomerCreating ? (
                    <Spinner />
                  ) : (
                    'Save Customer'
                  )}
                </Button>
              </Col>
            </>
          ) : (
            <>
              <Col
                offset={isSmallScreen ? 7.5 : 9.5}
                size={isSmallScreen ? 1.5 : 1}
              >
                <Buttons
                  onClick={() => {
                    navigate(`${pageRoutes['GO_TO_CUSTOMER_DETAILS']}`);
                  }}
                >
                  <Buttons.StartIcon>
                    <LeftArrowIcon />
                  </Buttons.StartIcon>
                  Back
                </Buttons>
              </Col>
              <Col size={isSmallScreen ? 1 : 0.5}></Col>
            </>
          )}
        </Row>
        <Row
          id="baseContainer"
          justifyContent="center"
          style={{
            marginTop: `${baseTheme.space.one * 36}px`,
          }}
        >
          <Col size={11}>
            <StyledCard>
              <Row alignItems="center">
                <Col style={{ paddingLeft: 0 }} size={3}>
                  <Container>
                    <MD isCenter hue="white">
                      CUSTOMER INFORMATION
                    </MD>
                  </Container>
                </Col>
                <Col size={9}>
                  {section === 'customerView' && (
                    <>
                      <SubContainer>
                        <MD
                          hue="primary"
                          style={{ marginLeft: baseTheme.space.md }}
                        >
                          Personal Information
                        </MD>
                      </SubContainer>
                    </>
                  )}
                  {section === 'accountInfo' && (
                    <>
                      <SubContainer>
                        <MD
                          hue="primary"
                          style={{ marginLeft: baseTheme.space.md }}
                        >
                          Account Information
                        </MD>
                      </SubContainer>
                    </>
                  )}
                  {section === 'address' && (
                    <>
                      <SubContainer align="space-between">
                        <MD
                          hue="primary"
                          style={{ marginLeft: baseTheme.space.md }}
                        >
                          Address
                        </MD>
                        <Button
                          isPrimary
                          onClick={() => {
                            // navigate(
                            //   `${pageRoutes['GO_TO_ADD_CUSTOMER']}/${customerId}`,
                            // );
                            setAddAddressShow(true);
                          }}
                        >
                          Add New Address
                          <Button.EndIcon>
                            <PlusCircleIcon />
                          </Button.EndIcon>
                        </Button>
                      </SubContainer>
                    </>
                  )}
                </Col>
              </Row>

              <Row>
                <Col size={3}>
                  {add == true ? (
                    <Row>
                      <SubContainer
                        onClick={() => {
                          handleContainerClick('accountInfo');
                        }}
                        selected={section === 'accountInfo'}
                      >
                        <MD
                          style={{ marginLeft: baseTheme.space.lg }}
                          hue="primary"
                        >
                          Account Information
                        </MD>
                      </SubContainer>
                    </Row>
                  ) : (
                    <>
                      <Row>
                        <SubContainer
                          onClick={() => {
                            handleContainerClick('customerView');
                          }}
                          selected={section === 'customerView'}
                        >
                          <MD
                            style={{ marginLeft: baseTheme.space.lg }}
                            hue="primary"
                          >
                            Customer View
                          </MD>
                        </SubContainer>
                      </Row>
                      <Row>
                        <SubContainer
                          onClick={() => {
                            handleContainerClick('accountInfo');
                          }}
                          selected={section === 'accountInfo'}
                        >
                          <MD
                            style={{ marginLeft: baseTheme.space.lg }}
                            hue="primary"
                          >
                            Account Information
                          </MD>
                        </SubContainer>
                      </Row>
                      <Row>
                        <SubContainer
                          onClick={() => {
                            handleContainerClick('address');
                          }}
                          selected={section === 'address'}
                        >
                          <MD
                            style={{ marginLeft: baseTheme.space.lg }}
                            hue="primary"
                          >
                            Address
                          </MD>
                        </SubContainer>
                      </Row>
                    </>
                  )}
                </Col>
                <Col size={9}>
                  {section === 'customerView' && (
                    <CustomerView
                      isLoading={isSummaryLoading}
                      customerViewData={customerViewData}
                    />
                  )}
                  {section === 'accountInfo' &&
                    add == true &&
                    customerGroups && (
                      <AccountInfo
                        error={error}
                        setCustomerCreate={setCustomerCreate}
                        allCustomerGroups={customerGroups}
                      />
                    )}
                  {section === 'accountInfo' &&
                    add == false &&
                    customerViewData &&
                    customerGroups && (
                      <EditAccountInfo
                        customerViewData={customerViewData}
                        setCustomerUpdate={setCustomerUpdate}
                        error={updateError}
                        isUpdating={isUpdatingCustomer}
                        allCustomerGroups={customerGroups}
                      />
                    )}
                  {section === 'address' && <Address />}
                </Col>
              </Row>
            </StyledCard>
          </Col>
        </Row>
      </Card>
      {addSddressShow && (
        <>
          <AddAddressModal
            close={() => {
              setAddAddressShow(false);
            }}
          />
        </>
      )}
    </>
  );
};

export default CustomerInformationLayout;
