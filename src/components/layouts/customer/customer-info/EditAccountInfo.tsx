import React, { useCallback, useEffect, useState } from 'react';
import { Col, Row as _Row, Row as SRow } from '../../../UI-components/Grid';
import {
  Field,
  ISelectedItem,
  Item,
  ItemGroup,
  Label,
  Menu,
} from '@zendeskgarden/react-dropdowns.next';
import { XSM } from '../../../UI-components/Typography';
import {
  MediaInput as _MediaInput,
  Input as _Input,
  InputGroup,
  Checkbox,
  Message,
} from '@zendeskgarden/react-forms';
import styled from 'styled-components';
import { baseTheme } from '../../../../themes/theme';
import { CalendarsIcon, DownIcon } from '../../../../utils/icons';
import { Button } from '@zendeskgarden/react-buttons';
import CustomCheckbox from '../../../UI-components/Checkbox';
import CustomSelect from '../../../dropdown/customer/CustomerDropdown';
import { ICustomerCreate, ICustomerGroups, registration_type_enum } from '.';
import { GroupIcon } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';

export interface IAdminCustomerUpdate {
  customer_id: number;
  group_id?: number;
  firstname?: string;
  lastname?: string;
  email?: string;
  password?: string;
  mobile?: string | null;
  taxvat?: string;
  registration_platform?: string;
  registration_type?: registration_type_enum;
  last_order?: any;
}

interface IItem {
  label: string;
  value: string;
}
const registerPlatforms = [
  { label: 'Website', value: 'web' },
  { label: 'Android', value: 'android' },
  { label: 'IOS', value: 'ios' },
];

const registerType = [
  { label: 'EMAIL', value: 'email' },
  { label: 'MOBILE', value: 'mobile' },
  { label: 'API', value: 'api' },
  { label: 'GOOGLE', value: 'google' },
  { label: 'WHATSAPP', value: 'whatsapp' },
];

const gender = [
  { label: 'Male', value: 'item-1' },
  { label: 'Female', value: 'item-2' },
];

const Input = styled(_Input)`
  background: rgba(32, 83, 117, 0.05);
  color: ${baseTheme.colors.deepBlue};
  border: 1px solid #********;
`;

const StyledInputGroup = styled(InputGroup)`
  /* stylelint-disable-next-line */
  & > input[aria-autocomplete='list'] {
    position: absolute;
  }
`;

const Row = styled(_Row)`
  margin-top: ${baseTheme.space.one * 25}px;
`;

const EditAccountInfo = ({
  setCustomerUpdate,
  customerViewData,
  error,
  allCustomerGroups,
  isUpdating,
}: {
  setCustomerUpdate: React.Dispatch<
    React.SetStateAction<IAdminCustomerUpdate | undefined>
  >;
  customerViewData: any;
  error: any;
  allCustomerGroups: IItem[];
  isUpdating: boolean;
}) => {
  const [localCustomerUpdate, setlocalCustomerUpdate] =
    useState<IAdminCustomerUpdate>({
      group_id: -1,
      firstname: '',
      lastname: '',
      customer_id: -1,
    });

  const [selectedGroup, setSelectedGroup] = useState<any>();

  const [selectedGender, setSelectedGender] = useState<any>(gender[0]);

  const [def, setDef] = useState(true);

  const [selectedItem, setSelectedItem] = useState<ISelectedItem>({
    value: '+91',
    name: 'call-code',
    type: 'radio',
  });

  const [rotated, setRotated] = useState(false);
  const handleChange = useCallback(
    ({
      isExpanded,
      selectedItems,
    }: {
      isExpanded?: boolean;
      selectedItems?: ISelectedItem[];
    }) => {
      if (isExpanded !== undefined) {
        setRotated(isExpanded);
      }
      if (selectedItems !== undefined && selectedItems[0]) {
        setSelectedItem(selectedItems[0]);
      }
    },
    [],
  );

  const [billingAddress, setBillingAddress] = useState<string | null>(null);

  useEffect(() => {
    if (customerViewData) {
      setlocalCustomerUpdate({
        firstname: customerViewData.firstname,
        lastname: customerViewData.lastname,
        customer_id: parseInt(customerViewData.customer_id),
        // email: customerViewData.email,
        // mobile: customerViewData?.mobile,
        group_id: customerViewData.group_id,
        registration_platform: customerViewData.registration_platform,
        registration_type: customerViewData.registration_type,
      });

      if (customerViewData.last_order != null) {
        const billingAddr = customerViewData.last_order.billing_address;
        const addr = `${billingAddr.street} ${billingAddr.city} ${billingAddr.region} ${billingAddr.country_id}`;
        setBillingAddress(addr);
      }

      setSelectedPlatform(
        registerPlatforms.find(
          (item) => item.value == customerViewData.registration_platform,
        ),
      );
      const registrationTypeValue: registration_type_enum =
        customerViewData.registration_type as registration_type_enum;
      setSelectedRegisterType(registrationTypeValue);

      setSelectedGroup(
        allCustomerGroups.find(
          (item) => item.value == customerViewData.group_id,
        ),
      );
    }
  }, [customerViewData]);

  const [selectedPlatforms, setSelectedPlatform] = useState<any>();

  const [selectedRegisterType, setSelectedRegisterType] = useState<any>();

  useEffect(() => {
    setCustomerUpdate(localCustomerUpdate);
  }, [localCustomerUpdate]);

  const handleChangeInput = (e: any, prop: string) => {
    const val = e.target.value;
    if (prop === 'firstname') {
      setlocalCustomerUpdate({
        ...localCustomerUpdate,
        firstname: val,
      });
    } else if (prop === 'lastname') {
      setlocalCustomerUpdate({
        ...localCustomerUpdate,
        lastname: val,
      });
    } else if (prop === 'email') {
      setlocalCustomerUpdate({
        ...localCustomerUpdate,
        email: val,
      });
    } else if (prop === 'dob') {
    } else if (prop === 'mobile') {
      setlocalCustomerUpdate({
        ...localCustomerUpdate,
        mobile: val,
      });
    } else if (prop === 'lastdeliveryaddr') {
    } else if (prop === 'tax') {
      setlocalCustomerUpdate({
        ...localCustomerUpdate,
        taxvat: val,
      });
    } else if (prop === 'password') {
      setlocalCustomerUpdate({
        ...localCustomerUpdate,
        password: val,
      });
    }
  };

  useEffect(() => {
    let localCustomerData = {
      ...localCustomerUpdate,
    };

    if (selectedPlatforms) {
      localCustomerData = {
        ...localCustomerData,
        registration_platform: selectedPlatforms.value,
      };
    }

    if (selectedRegisterType) {
      localCustomerData = {
        ...localCustomerData,
        registration_type: selectedRegisterType,
      };
    }

    if (selectedGroup) {
      localCustomerData = {
        ...localCustomerData,
        group_id: selectedGroup.value,
      };
    }

    // setlocalCustomerUpdate(localCustomerData);
  }, [selectedPlatforms, selectedRegisterType, selectedGroup]);

  return (
    <>
      <div style={{ height: '100%' }}>
        <Row justifyContent="center">
          <Col size={10}>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Register Platform *</XSM>
              </Col>
              <Col>
                <Field>
                  <CustomSelect
                    items={registerPlatforms}
                    selectedItem={selectedPlatforms}
                    setSelectedItem={setSelectedPlatform}
                  />
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Register Type *</XSM>
              </Col>
              <Col>
                <Field>
                  <CustomSelect
                    items={registerType}
                    selectedItem={selectedRegisterType}
                    setSelectedItem={setSelectedRegisterType}
                  />
                  {error.registration_type != '' && (
                    <Message validation="error">
                      {error.registration_type}
                    </Message>
                  )}
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Group *</XSM>
              </Col>
              <Col>
                <Field>
                  <CustomSelect
                    items={allCustomerGroups}
                    selectedItem={selectedGroup}
                    setSelectedItem={setSelectedGroup}
                  />
                  {error.customer_group_id != '' && (
                    <Message validation="error">
                      {error.customer_group_id}
                    </Message>
                  )}
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>First Name *</XSM>
              </Col>
              <Col>
                <Field>
                  <Input
                    onChange={(e) => handleChangeInput(e, 'firstname')}
                    value={localCustomerUpdate.firstname}
                  />
                  {error.firstname != '' && (
                    <Message validation="error">{error.firstname}</Message>
                  )}
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Last Name *</XSM>
              </Col>
              <Col>
                <Field>
                  <Input
                    onChange={(e) => handleChangeInput(e, 'lastname')}
                    value={localCustomerUpdate.lastname}
                  />
                  {error.lastname != '' && (
                    <Message validation="error">{error.lastname}</Message>
                  )}
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Email</XSM>
              </Col>
              <Col>
                <Field>
                  <Input
                    onChange={(e) => handleChangeInput(e, 'email')}
                    value={customerViewData?.email}
                  />
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Password</XSM>
              </Col>
              <Col>
                <Field>
                  <Input
                    type="password"
                    onChange={(e) => handleChangeInput(e, 'password')}
                  />
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Mobile Number</XSM>
              </Col>
              <Col>
                <Field>
                  <Input
                    onChange={(e) => handleChangeInput(e, 'mobile')}
                    value={customerViewData?.mobile}
                  />
                </Field>
              </Col>
            </Row>
            {/* <Row alignItems="center">
              <Col>
                <XSM isBold>Last Billing Address</XSM>
              </Col>
              <Col>
                <Field>
                  <Input
                    // onChange={(e) => handleChangeInput(e, 'lastdeliveryaddr')}
                    value={billingAddress != null ? billingAddress : ''}
                  />
                </Field>
              </Col>
            </Row> */}
            <Row alignItems="center">
              <Col>
                <XSM isBold>Tax/VAT Number</XSM>
              </Col>
              <Col>
                <Field>
                  <Input
                    onChange={(e) => handleChangeInput(e, 'tax')}
                    value={localCustomerUpdate.taxvat}
                  />
                </Field>
              </Col>
            </Row>
            {/* <Row alignItems="center">
              <Col>
                <XSM isBold>Send welcome email from</XSM>
              </Col>
              <Col>
                <Field>
                  <Checkbox checked={def} onChange={() => setDef(!def)}>
                    <Label isRegular>Default</Label>
                  </Checkbox>
                </Field>
              </Col>
            </Row> */}
            <Row
              style={{ height: baseTheme.components.dimension.height.md }}
            ></Row>
          </Col>
        </Row>
      </div>
    </>
  );
};

export default EditAccountInfo;
