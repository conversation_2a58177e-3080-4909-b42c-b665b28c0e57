import React, { useEffect, useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import styled from 'styled-components';
import { MD, XSM } from '../../../UI-components/Typography';
import { baseTheme } from '../../../../themes/theme';
import { Tag } from '@zendeskgarden/react-tags';
import { format, parseISO } from 'date-fns';
import { Skeleton } from '@zendeskgarden/react-loaders';
import { Field, Label, Toggle } from '@zendeskgarden/react-forms';
import { Span } from '@zendeskgarden/react-typography';

interface TextProps {
  background?: string;
  marginTop?: string;
}

interface ContainerProps {
  align?: string;
  haveBorder?: boolean;
}

const TextContainer = styled.div<TextProps>`
  background-color: ${(p) => (p.background ? p.background : '#20537530')};
  padding: ${baseTheme.space.one * 6}px;
  margin-left: ${baseTheme.space.fs};
  margin-top: ${(p) => (p.marginTop ? p.marginTop : baseTheme.space.fs)};
`;

const SubContainer = styled.div<ContainerProps>`
  width: 100%;
  padding: 0px ${baseTheme.space.fs} 0px ${baseTheme.space.fs};
  height: ${baseTheme.components.dimension.height.base * 6}px;
  display: flex;
  align-items: center;
  justify-content: ${(p) => (p.align ? p.align : 'left')};
  ${(p) => (p.haveBorder ? 'border-bottom: 2px solid #20537533;' : '')}
`;

const SM = styled(XSM)`
  line-height: ${baseTheme.components.dimension.height.base * 2}px;
`;

const CustomerView = ({
  isLoading,
  customerViewData,
}: {
  customerViewData: any;
  isLoading: boolean;
}) => {
  const [checked, setIsChecked] = useState<boolean>(true);

  const [isMemeberShipActive, setIsMembershipActive] = useState<boolean>(false);

  const [monetoryValue, setMonetoryValue] = useState<
    number | undefined | null | string
  >(null);

  useEffect(() => {
    setIsChecked(isMemeberShipActive);
  }, [isMemeberShipActive]);

  useEffect(() => {
    if (customerViewData) {
      if (
        customerViewData?.membership_summary &&
        customerViewData?.membership_summary.length != 0
      ) {
        let isActive: boolean = false;
        if (customerViewData.membership_summary.length == 0) {
          setIsMembershipActive(isActive);
        } else {
          isActive =
            customerViewData.membership_summary.length > 0 &&
            customerViewData.membership_summary.some(
              (item: any) => item.is_active === true,
            );

          setIsMembershipActive(isActive);
        }

        if (isActive) {
          if (customerViewData?.rewards_summary != null) {
            const points = customerViewData?.rewards_summary?.available_point;
            setMonetoryValue(points.toFixed(2));
          } else {
            setMonetoryValue(0);
          }
        } else {
          if (customerViewData?.rewards_summary != null) {
            const points =
              customerViewData?.rewards_summary?.available_point / 2;
            setMonetoryValue(points.toFixed(2));
          } else {
            setMonetoryValue(0);
          }
        }
      }
    }
  }, [customerViewData]);

  return (
    <>
      <Row>
        <Col size={6}>
          {isLoading ? (
            <>
              <Skeleton
                style={{
                  width: '100%',
                  height: baseTheme.components.dimension.height.base200,
                  margin: baseTheme.space.sm,
                }}
              />
            </>
          ) : (
            <>
              <TextContainer>
                <Row alignItems="center">
                  <Col offset={0.5}>
                    <SM isBold>Last logged in:</SM>
                  </Col>
                  <Col>
                    <SM isBold>Never (offline)</SM>
                  </Col>
                </Row>
              </TextContainer>
              <TextContainer>
                <Row alignItems="center">
                  <Col offset={0.5}>
                    <SM isBold>Account Locked:</SM>
                  </Col>
                  <Col>
                    <SM isBold>
                      {customerViewData?.account_locked
                        ? 'Blocked'
                        : 'Unblocked'}
                    </SM>
                  </Col>
                </Row>
              </TextContainer>
              <TextContainer>
                <Row alignItems="center">
                  <Col offset={0.5}>
                    <SM isBold>Confirmed Email:</SM>
                  </Col>
                  <Col>
                    <SM isBold>Confirmed</SM>
                  </Col>
                </Row>
              </TextContainer>
              <TextContainer>
                <Row alignItems="center">
                  <Col offset={0.5}>
                    <SM isBold>Account Created In:</SM>
                  </Col>
                  <Col>
                    <SM isBold>{customerViewData?.created_in}</SM>
                  </Col>
                </Row>
              </TextContainer>
              <TextContainer>
                <Row alignItems="center">
                  <Col offset={0.5}>
                    <SM isBold>Customer Group:</SM>
                  </Col>
                  <Col>
                    <SM isBold>{customerViewData?.customer_group}</SM>
                  </Col>
                </Row>
              </TextContainer>
            </>
          )}
        </Col>
        <Col size={5.5}>
          <TextContainer background="#fff">
            <Row>
              <Col>
                <SM isBold>Default Shipping Address</SM>
              </Col>
            </Row>
          </TextContainer>
          {isLoading ? (
            <>
              <Skeleton
                style={{
                  width: '100%',
                  height: baseTheme.components.dimension.height.base200,
                  margin: baseTheme.space.sm,
                }}
              />
            </>
          ) : (
            <>
              <TextContainer background="#fff">
                <Row>
                  <Col>
                    <SM hue="neutral">
                      {/* Dr Sankhadeep AcharyaSmileZone Dental Clinic, Saha Para Road,
                  Bankumari Bazar, Jogendranagar, Near UCO Bank Agartala,
                  Tripura, 799004India T: ********** */}
                      {customerViewData?.default_shipping_address != null ? (
                        <>
                          {`${customerViewData?.default_shipping_address.firstname}
                      ${customerViewData?.default_shipping_address.lastname}
                      ${customerViewData?.default_shipping_address.street}
                      ${customerViewData?.default_shipping_address.city}
                      ${customerViewData?.default_shipping_address.region}
                      ${customerViewData?.default_shipping_address.postcode}
                      ${customerViewData?.default_shipping_address.country}
                      ${customerViewData?.default_shipping_address.telephone}
                      ${customerViewData?.default_shipping_address.alternate_telephone}`}
                        </>
                      ) : (
                        <Tag>Not Provided</Tag>
                      )}
                    </SM>
                  </Col>
                </Row>
              </TextContainer>
            </>
          )}
        </Col>
      </Row>
      {isLoading ? (
        <></>
      ) : (
        <>
          <Row
            style={{ height: baseTheme.components.dimension.height.base100 }}
          ></Row>
        </>
      )}

      <Row style={{ borderBottom: '2px solid #20537533' }}>
        <Col>
          <SubContainer>
            <MD isCenter hue="primary">
              Customer Delight
            </MD>
          </SubContainer>
        </Col>
        <Col>
          <SubContainer>
            <MD isCenter hue="primary">
              Membership
            </MD>
          </SubContainer>
        </Col>
      </Row>
      <Row>
        <Col size={6}>
          <TextContainer>
            <Row alignItems="center">
              <Col offset={0.5}>
                <SM isBold>Rewards Balance</SM>
              </Col>
              <Col>
                <SM isBold>
                  {isLoading ? (
                    <>
                      <Skeleton
                        style={{
                          width: `${
                            baseTheme.components.dimension.width.base * 4
                          }px`,
                        }}
                      />
                    </>
                  ) : (
                    <>
                      {customerViewData?.rewards_summary != null ? (
                        <>
                          {customerViewData?.rewards_summary?.available_point}{' '}
                          points
                        </>
                      ) : (
                        <>
                          <Tag>Not Provided</Tag>
                        </>
                      )}
                    </>
                  )}
                </SM>
              </Col>
            </Row>
          </TextContainer>
          <TextContainer marginTop={`${baseTheme.space.one * 6}px`}>
            <Row alignItems="center">
              <Col offset={0.5}>
                <SM isBold>Monetory Value</SM>
              </Col>
              <Col>
                <SM isBold>
                  {isLoading ? (
                    <>
                      <Skeleton
                        style={{
                          width: `${
                            baseTheme.components.dimension.width.base * 4
                          }px`,
                        }}
                      />
                    </>
                  ) : (
                    <>
                      {monetoryValue && monetoryValue != null ? (
                        <>Rs. {monetoryValue}</>
                      ) : (
                        <>
                          <Tag>Not provided</Tag>
                        </>
                      )}
                    </>
                  )}
                </SM>
              </Col>
            </Row>
          </TextContainer>
        </Col>
        <Col size={5.5}>
          <TextContainer background="#fff">
            <Row alignItems="center">
              <Col offset={0.5}>
                <SM isBold>Membership Active </SM>
              </Col>
              <Col>
                <SM isBold>
                  {isLoading ? (
                    <>
                      <Skeleton
                        style={{
                          width: `${
                            baseTheme.components.dimension.width.base * 6
                          }px`,
                        }}
                      />
                    </>
                  ) : (
                    <>
                      <Field>
                        <Toggle
                          checked={checked}
                          // onChange={() => {
                          //   setIsChecked(!checked);
                          // }}
                        >
                          <Label style={{ color: baseTheme.colors.primaryHue }}>
                            <Span isBold>
                              {isMemeberShipActive ? 'Yes' : 'No'}
                            </Span>
                          </Label>
                        </Toggle>
                      </Field>
                    </>
                  )}
                </SM>
              </Col>
            </Row>
          </TextContainer>
          <TextContainer marginTop={`${baseTheme.space.one * 6}px`}>
            <Row alignItems="center">
              <Col offset={0.5}>
                <SM isBold>Expiry Date:</SM>
              </Col>
              <Col>
                <SM isBold>
                  {isLoading ? (
                    <>
                      <Skeleton
                        style={{
                          width: `${
                            baseTheme.components.dimension.width.base * 6
                          }px`,
                        }}
                      />
                    </>
                  ) : (
                    <>
                      {customerViewData?.membership_summary &&
                        customerViewData.membership_summary[0]?.expiry_date &&
                        format(
                          new Date(
                            customerViewData?.membership_summary[0]?.expiry_date,
                          ),
                          'dd/MM/yyyy HH:mm:ss',
                        )}
                    </>
                  )}
                </SM>
              </Col>
            </Row>
          </TextContainer>
        </Col>
      </Row>
    </>
  );
};

export default CustomerView;
