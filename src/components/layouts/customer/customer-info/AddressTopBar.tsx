import React, { ReactNode, useState } from 'react';
import GenericCustomTopBar from '../../../topbar/GenericCustomTopBar';
import {
  DownIcon,
  FilterIcon,
  PlusCircleIcon,
  RefetchIcon,
  ResetIcon,
} from '../../../../utils/icons';
import { Span } from '@zendeskgarden/react-typography';
import { Col, Row } from '../../../UI-components/Grid';
import {
  Dropdown,
  Field as _Field,
  Menu,
  Trigger,
} from '@zendeskgarden/react-dropdowns';
import {
  Checkbox,
  Fieldset,
  Select,
  Label as _Label,
} from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../../themes/theme';
import { Table } from '@tanstack/react-table';
import styled from 'styled-components';
import { Button as _Button } from '../../../UI-components/Button';
import {
  useScreenDefaultSize,
  useScreenDefaultWidth,
  useScreenResSize,
} from '../../../../hooks/useScreenSize';

const Button = styled(_Button)`
  margin-right: ${baseTheme.space.md};
`;

const Field = styled(_Field)`
  padding: ${baseTheme.space.sm};
`;

const Label = styled(_Label)`
  font-size: ${baseTheme.fontSizes.md};
`;

const searchOptions = [
  {
    label: 'Mobile no',
    value: 'mobile',
  },
  {
    label: 'First name',
    value: 'firstname',
  },
  {
    label: 'Last name',
    value: 'lastname',
  },
];

const AddressTopBar = <TData, TValue>({
  searchContent,
  setSearchContent,
  table,
  disabledColumn,
  alreadyEnabledColumn,
  setAlreadyEnabledColumn,
  handleSearch,
  searchType,
  setSearchType,
}: {
  searchContent: string | undefined;
  setSearchContent: React.Dispatch<React.SetStateAction<string | undefined>>;
  table?: Table<TData>;
  disabledColumn?: string[];
  alreadyEnabledColumn?: string[];
  setAlreadyEnabledColumn?: React.Dispatch<React.SetStateAction<string[]>>;
  handleSearch: () => void;
  searchType: any;
  setSearchType: React.Dispatch<React.SetStateAction<any>>;
}) => {
  const [rotated, setRotated] = useState<boolean | undefined>();
  const [isChecked, setIsChecked] = useState(false);

  const handleCheckboxChange = (event: any, column: any, header: string) => {
    const { checked } = event.target;
    setIsChecked(checked);
    if (checked === true) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) => [...prev, header]);

      column.toggleVisibility(true);
    } else if (checked === false) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) =>
          prev.filter((item) => item !== header),
        );

      column.toggleVisibility(false);
    }
  };

  const isSmallScreen = useScreenDefaultWidth();

  return (
    <>
      <GenericCustomTopBar
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        handleSearch={() => {
          handleSearch();
        }}
        searchPlaceholder="Search by Name & Mobile Number"
        width="100%"
        searchOptions={searchOptions}
        haveSearchDropdown={true}
        searchType={searchType}
        setSearchType={setSearchType}
        isPlacedInSmallArea
      >
        <Col size={4}>
          <Row justifyContent="end" alignItems="center">
            <Dropdown
              onSelect={(item) => alert(`You planted a ${item}`)}
              onStateChange={(options) =>
                Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
              }
            >
              <Trigger>
                <Button isPrimary>
                  Select Item
                  <Button.EndIcon isRotated={rotated}>
                    <DownIcon
                      style={{
                        height: baseTheme.iconSizes.md,
                        width: baseTheme.iconSizes.md,
                      }}
                    />
                  </Button.EndIcon>
                </Button>
              </Trigger>
              <Menu
                style={{
                  width: baseTheme.components.dimension.width.base200,
                  transform: 'translateX(4px)',
                  borderRadius: baseTheme.borderRadii.lg,
                }}
              >
                <Fieldset>
                  {table &&
                    table
                      .getAllColumns()
                      .filter((column) => column.getCanHide())
                      .map((column) => {
                        return (
                          <>
                            {!column.columnDef.enableHiding &&
                              !column.columnDef.enableColumnFilter && (
                                <Field>
                                  <Checkbox
                                    disabled={disabledColumn?.includes(
                                      column.columnDef.header as string,
                                    )}
                                    key={column.id}
                                    checked={alreadyEnabledColumn?.includes(
                                      column.columnDef.header as string,
                                    )}
                                    onChange={(e) =>
                                      handleCheckboxChange(
                                        e,
                                        column,
                                        column.columnDef.header as string,
                                      )
                                    }
                                  >
                                    <Label>
                                      <>
                                        {column.columnDef.header as ReactNode}
                                      </>
                                    </Label>
                                  </Checkbox>
                                </Field>
                              )}
                          </>
                        );
                      })}
                </Fieldset>
              </Menu>
            </Dropdown>
          </Row>
        </Col>
      </GenericCustomTopBar>
    </>
  );
};

export default AddressTopBar;
