import React, { useCallback, useEffect, useState } from 'react';
import { Col, Row as _Row, Row as SRow } from '../../../UI-components/Grid';
import {
  Field,
  ISelectedItem,
  Item,
  ItemGroup,
  Label,
  Menu,
} from '@zendeskgarden/react-dropdowns.next';
import { XSM } from '../../../UI-components/Typography';
import {
  MediaInput as _MediaInput,
  Input as _Input,
  InputGroup,
  Checkbox,
  Message,
} from '@zendeskgarden/react-forms';
import styled from 'styled-components';
import { baseTheme } from '../../../../themes/theme';
import { CalendarsIcon, DownIcon } from '../../../../utils/icons';
import { Button } from '@zendeskgarden/react-buttons';
import CustomCheckbox from '../../../UI-components/Checkbox';
import CustomSelect from '../../../dropdown/customer/CustomerDropdown';
import { ICustomerCreate, ICustomerGroups, registration_type_enum } from '.';
import { GroupIcon } from 'lucide-react';

interface IItem {
  label: string;
  value: string;
}
const registerPlatforms = [
  { label: 'Website', value: 'web' },
  { label: 'Android', value: 'android' },
  { label: 'IOS', value: 'ios' },
];

const registerType = [
  { label: 'EMAIL', value: 'email' },
  { label: 'MOBILE', value: 'mobile' },
  { label: 'API', value: 'api' },
  { label: 'GOOGLE', value: 'google' },
  { label: 'WHATSAPP', value: 'whatsapp' },
];

const gender = [
  { label: 'Male', value: 'item-1' },
  { label: 'Female', value: 'item-2' },
];

const Input = styled(_Input)`
  background: rgba(32, 83, 117, 0.05);
  color: ${baseTheme.colors.deepBlue};
  border: 1px solid #********;
`;

const StyledInputGroup = styled(InputGroup)`
  /* stylelint-disable-next-line */
  & > input[aria-autocomplete='list'] {
    position: absolute;
  }
`;

const Row = styled(_Row)`
  margin-top: ${baseTheme.space.one * 25}px;
`;

const AccountInfo = ({
  setCustomerCreate,
  error,
  allCustomerGroups,
}: {
  setCustomerCreate: React.Dispatch<
    React.SetStateAction<ICustomerCreate | undefined>
  >;
  error: any;
  allCustomerGroups: IItem[];
}) => {
  const [localCustomerCreate, setLocalCustomerCreate] =
    useState<ICustomerCreate>({
      customer_group_id: -1,
      firstname: '',
      lastname: '',
      registration_platform: '',
      registration_type: registration_type_enum.NONE,
      password: '',
    });

  const [selectedPlatforms, setSelectedPlatform] = useState<any>(
    registerPlatforms[0],
  );

  const [selectedRegisterType, setSelectedRegisterType] = useState<any>(
    registerType[0],
  );

  const [selectedGroup, setSelectedGroup] = useState<any>(allCustomerGroups[0]);

  const [selectedGender, setSelectedGender] = useState<any>(gender[0]);

  const [selectedItem, setSelectedItem] = useState<ISelectedItem>({
    value: '+91',
    name: 'call-code',
    type: 'radio',
  });

  const [rotated, setRotated] = useState(false);
  const handleChange = useCallback(
    ({
      isExpanded,
      selectedItems,
    }: {
      isExpanded?: boolean;
      selectedItems?: ISelectedItem[];
    }) => {
      if (isExpanded !== undefined) {
        setRotated(isExpanded);
      }
      if (selectedItems !== undefined && selectedItems[0]) {
        setSelectedItem(selectedItems[0]);
      }
    },
    [],
  );

  useEffect(() => {
    setCustomerCreate(localCustomerCreate);
  }, [localCustomerCreate]);

  useEffect(() => {
    let localCustomerData = {
      ...localCustomerCreate,
    };
    if (selectedPlatforms) {
      localCustomerData = {
        ...localCustomerData,
        registration_platform: selectedPlatforms.value,
      };
    }
    if (selectedRegisterType) {
      localCustomerData = {
        ...localCustomerData,
        registration_type: selectedRegisterType.value,
      };
    }
    if (selectedGroup) {
      localCustomerData = {
        ...localCustomerData,
        customer_group_id: 1,
      };
    }

    setLocalCustomerCreate(localCustomerData);
  }, [selectedPlatforms, selectedRegisterType, selectedGroup, selectedGender]);

  const handleChangeInput = (e: any, prop: string) => {
    const val = e.target.value;
    if (prop === 'firstname') {
      setLocalCustomerCreate({
        ...localCustomerCreate,
        firstname: val,
      });
    } else if (prop === 'lastname') {
      setLocalCustomerCreate({
        ...localCustomerCreate,
        lastname: val,
      });
    } else if (prop === 'email') {
      setLocalCustomerCreate({
        ...localCustomerCreate,
        email: val,
      });
    } else if (prop === 'dob') {
    } else if (prop === 'mobile') {
      setLocalCustomerCreate({
        ...localCustomerCreate,
        mobile: val,
      });
    } else if (prop === 'lastdeliveryaddr') {
    } else if (prop === 'tax') {
      setLocalCustomerCreate({
        ...localCustomerCreate,
        taxvat: val,
      });
    } else if (prop === 'password') {
      setLocalCustomerCreate({
        ...localCustomerCreate,
        password: val,
      });
    }
  };

  return (
    <>
      <div style={{ height: '100%' }}>
        <Row justifyContent="center">
          <Col size={10}>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Register Platform *</XSM>
              </Col>
              <Col>
                <Field>
                  <CustomSelect
                    items={registerPlatforms}
                    selectedItem={selectedPlatforms}
                    setSelectedItem={setSelectedPlatform}
                  />
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Register Type *</XSM>
              </Col>
              <Col>
                <Field>
                  <CustomSelect
                    items={registerType}
                    selectedItem={selectedRegisterType}
                    setSelectedItem={setSelectedRegisterType}
                  />
                  {error.registration_type != '' && (
                    <Message validation="error">
                      {error.registration_type}
                    </Message>
                  )}
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Group *</XSM>
              </Col>
              <Col>
                <Field>
                  <CustomSelect
                    items={allCustomerGroups}
                    selectedItem={selectedGroup}
                    setSelectedItem={setSelectedGroup}
                  />
                  {error.customer_group_id != '' && (
                    <Message validation="error">
                      {error.customer_group_id}
                    </Message>
                  )}
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>First Name *</XSM>
              </Col>
              <Col>
                <Field>
                  <Input onChange={(e) => handleChangeInput(e, 'firstname')} />
                  {error.firstname != '' && (
                    <Message validation="error">{error.firstname}</Message>
                  )}
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Last Name *</XSM>
              </Col>
              <Col>
                <Field>
                  <Input onChange={(e) => handleChangeInput(e, 'lastname')} />
                  {error.lastname != '' && (
                    <Message validation="error">{error.lastname}</Message>
                  )}
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Email</XSM>
              </Col>
              <Col>
                <Field>
                  <Input onChange={(e) => handleChangeInput(e, 'email')} />
                  {error.email != '' && (
                    <Message validation="error">{error.email}</Message>
                  )}
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Password</XSM>
              </Col>
              <Col>
                <Field>
                  <Input
                    type="password"
                    onChange={(e) => handleChangeInput(e, 'password')}
                  />
                  {error.password != '' && (
                    <Message validation="error">{error.password}</Message>
                  )}
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Mobile Number</XSM>
              </Col>
              <Col>
                <Field>
                  <Input onChange={(e) => handleChangeInput(e, 'mobile')} />
                </Field>
              </Col>
            </Row>
            <Row alignItems="center">
              <Col>
                <XSM isBold>Tax/VAT Number</XSM>
              </Col>
              <Col>
                <Field>
                  <Input onChange={(e) => handleChangeInput(e, 'tax')} />
                </Field>
              </Col>
            </Row>
            <Row
              style={{ height: baseTheme.components.dimension.height.md }}
            ></Row>
          </Col>
        </Row>
      </div>
    </>
  );
};

export default AccountInfo;
