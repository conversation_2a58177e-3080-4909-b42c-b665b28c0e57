import React, { useEffect, useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import styled from 'styled-components';
import { baseTheme } from '../../../../themes/theme';
import { XSM } from '../../../UI-components/Typography';
import { But<PERSON> as _Button } from '@zendeskgarden/react-buttons';
import SearchInput from '../../../search-input/SearchInput';
import AddressTable from '../../../table/customer/AddressTable';
import { useNavigate, useParams } from 'react-router-dom';
import { pageRoutes } from '../../../navigation/RouteConfig';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useCommonAxios';
import routes from '../../../../constants/routes';
import CustomerContextProvider, {
  useCustomerContext,
} from '../../../../pages/customer/CustomerContext';
import { Skeleton } from '@zendeskgarden/react-loaders';
import EditAddressModal from '../../../modal/customer/EditAddress';
import { Tag } from '@zendeskgarden/react-tags';

interface TextProps {
  background?: string;
  marginTop?: string;
}

const TextContainer = styled.div<TextProps>`
  background-color: ${(p) => (p.background ? p.background : '#20537530')};
  padding: ${baseTheme.space.one * 6}px;
  margin-left: ${baseTheme.space.fs};
  margin-top: ${(p) => (p.marginTop ? p.marginTop : baseTheme.space.fs)};
`;

const Button = styled(_Button)`
  width: ${baseTheme.components.dimension.width.base * 6}px;
  height: ${baseTheme.components.dimension.height.base * 2.5}px;
`;

const BaseContainer = styled.div`
  margin: ${baseTheme.space.md};
`;

const SM = styled(XSM)`
  line-height: ${baseTheme.components.dimension.height.base * 2}px;
`;

const Address = () => {
  const navigate = useNavigate();
  const [customerAddress, setCustomerAddress] = useState<any>();

  const axios = useAxios();

  const { customerId } = useParams();

  const {
    setSelectedAddress,
    selectedAddress,
    addressData,
    isGettingAddress,
    refetchAddress,
    addressFilters,
    setAddressFilters,
    results,
    defaultBillingAddress,
    defaultShippingAddress,
    isFetchingAddress,
    isRefetchingAddres,
  } = useCustomerContext();

  // useEffect(() => {
  //   if (results != undefined) {
  //     const shippingAddressObj = results.find(
  //       (address) => address.default_shipping === true,
  //     );

  //     if (shippingAddressObj) {
  //       setShippingAddress(shippingAddressObj);
  //     } else {
  //       setBillingAddress(undefined);
  //     }

  //     const billingAddressObj = results.find(
  //       (address) =>
  //         address.default_billing == true || address.default_billing == null,
  //     );

  //     if (billingAddressObj) {
  //       setBillingAddress(billingAddressObj);
  //     } else {
  //       setBillingAddress(undefined);
  //     }

  //     console.log('Results', results);
  //   }
  // }, [results]);

  useEffect(() => {
    if (addressData) {
      setCustomerAddress(addressData);
    }
  }, [addressData]);

  useEffect(() => {
    if (addressFilters) {
      setTimeout(() => {
        refetchAddress();
      }, 500);
    }
  }, [addressFilters]);

  const [billingAddres, setBillingAddress] = useState<any>();
  const [shippingAddress, setShippingAddress] = useState<any>();
  const [editAddressShowBilling, setEditAddressShowBilling] =
    useState<boolean>(false);
  const [editAddressShowShipping, setEditAddressShowShipping] =
    useState<boolean>(false);

  const handleShippingAddress = () => {
    if (defaultShippingAddress != null) {
      setTimeout(() => {
        setEditAddressShowShipping(true);
      }, 200);
    }
  };

  const handleBillingAddress = () => {
    if (defaultBillingAddress != null) {
      setTimeout(() => {
        setEditAddressShowBilling(true);
      }, 200);
    }
  };

  // useEffect(() => {
  //   console.log('Billing Address', billingAddres);
  //   console.log('Shipping Address', shippingAddress);
  // }, [billingAddres, shippingAddress]);

  return (
    <>
      <Row alignItems="center">
        <Col size={6}>
          <TextContainer
            style={{ marginRight: baseTheme.space.md }}
            background="#fff"
          >
            <Row alignItems="center" justifyContent="between">
              <Col size={7}>
                <SM isBold>Default Billing Address</SM>
              </Col>
              <Col size={2}>
                <Button
                  onClick={() => {
                    // navigate(pageRoutes['GO_TO_EDIT_CUSTOMER']);
                    handleBillingAddress();
                  }}
                >
                  Edit
                </Button>
              </Col>
            </Row>
          </TextContainer>
          <TextContainer background="#fff">
            <Row>
              <Col>
                <SM hue="neutral">
                  {defaultBillingAddress?.firstname ||
                  defaultBillingAddress?.lastname ||
                  defaultBillingAddress?.street ||
                  defaultBillingAddress?.city ||
                  defaultBillingAddress?.region ||
                  defaultBillingAddress?.postcode ||
                  defaultBillingAddress?.country ||
                  defaultBillingAddress?.telephone ? (
                    `${defaultBillingAddress?.firstname} ${defaultBillingAddress?.lastname} ${defaultBillingAddress?.street} ${defaultBillingAddress?.city} ${defaultBillingAddress?.region} ${defaultBillingAddress?.postcode} ${defaultBillingAddress?.country} ${defaultBillingAddress?.telephone}`
                  ) : (
                    <Tag>Not provided</Tag>
                  )}
                </SM>
              </Col>
            </Row>
          </TextContainer>
        </Col>
        <Col size={6}>
          <TextContainer
            style={{ marginRight: baseTheme.space.md }}
            background="#fff"
          >
            <Row alignItems="center" justifyContent="between">
              <Col size={7}>
                <SM isBold>Default Shipping Address</SM>
              </Col>
              <Col size={2}>
                <Button
                  onClick={() => {
                    // navigate(pageRoutes['GO_TO_EDIT_CUSTOMER']);
                    handleShippingAddress();
                  }}
                >
                  Edit
                </Button>
              </Col>
            </Row>
          </TextContainer>
          <TextContainer background="#fff">
            <Row>
              <Col>
                <SM hue="neutral">
                  {/* Dr Sankhadeep AcharyaSmileZone Dental Clinic, Saha Para Road,
                  Bankumari Bazar, Jogendranagar, Near UCO Bank Agartala,
                  Tripura, 799004India T: ********** */}
                  {/* {`${defaultShippingAddress?.firstname} ${defaultShippingAddress?.lastname} ${defaultShippingAddress?.street}  ${defaultShippingAddress?.city} ${defaultShippingAddress?.region} ${defaultShippingAddress?.postcode} ${defaultShippingAddress?.country} ${defaultShippingAddress?.telephone}`} */}
                  {defaultShippingAddress?.firstname ||
                  defaultShippingAddress?.lastname ||
                  defaultShippingAddress?.street ||
                  defaultShippingAddress?.city ||
                  defaultShippingAddress?.region ||
                  defaultShippingAddress?.postcode ||
                  defaultShippingAddress?.country ||
                  defaultShippingAddress?.telephone ? (
                    `${defaultShippingAddress?.firstname} ${defaultShippingAddress?.lastname} ${defaultShippingAddress?.street} ${defaultShippingAddress?.city} ${defaultShippingAddress?.region} ${defaultShippingAddress?.postcode} ${defaultShippingAddress?.country} ${defaultShippingAddress?.telephone}`
                  ) : (
                    <Tag>Not provided</Tag>
                  )}
                </SM>
              </Col>
            </Row>
          </TextContainer>
        </Col>
      </Row>
      <Row>
        <Col>
          {!isGettingAddress && customerAddress ? (
            <AddressTable
              filters={addressFilters}
              setFilters={setAddressFilters}
              address={customerAddress}
            />
          ) : (
            <>
              <Skeleton
                style={{
                  width: '100%',
                  height: baseTheme.components.dimension.height.base300,
                  marginTop: baseTheme.space.md,
                }}
              />
            </>
          )}
        </Col>
      </Row>
      {editAddressShowBilling && defaultBillingAddress && (
        <>
          <EditAddressModal
            addressId={defaultBillingAddress?.address_id}
            close={() => {
              setEditAddressShowBilling(false);
            }}
            addressTitle="Edit Billing Address"
          />
        </>
      )}
      {editAddressShowShipping && defaultShippingAddress && (
        <>
          <EditAddressModal
            close={() => {
              setEditAddressShowShipping(false);
            }}
            addressId={defaultShippingAddress?.address_id}
            addressTitle="Edit Shipping Address"
          />
        </>
      )}
    </>
  );
};

export default Address;
