import { useEffect, useState } from 'react';
import { Card } from '../../UI-components/Card';
import { baseTheme, colors } from '../../../themes/theme';
import styled from 'styled-components';
import { Col, Row } from '../../UI-components/Grid';
import { LG, MD, SM, XL, MD as _MD } from '../../UI-components/Typography';
import { Tiles } from '@zendeskgarden/react-forms';
import { BarChartIcon, WatchIcon } from 'lucide-react';
import { XXL } from '../../UI-components/Typography';
import Engagement from './FeedsAnalyticsLayout/Engagement';
import View from './FeedsAnalyticsLayout/View';
import WatchTime from './FeedsAnalyticsLayout/WatchTime';
import useAxios from '../../../hooks/useAxiosFeeds';
import { useQuery } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';
import { IFeeds } from '../../../pages/feeds/FeedsVideos';
import { Field as _Field } from '@zendeskgarden/react-forms';
import { Blockquote, Ellipsis, Span } from '@zendeskgarden/react-typography';
import { Button } from '../../UI-components/Button';
import { RightArrowIcon } from '../../../utils/icons';
import { useNavigate } from 'react-router-dom';
import { pageRoutes } from '../../navigation/RouteConfig';
import {
  useEngagementPerDayHistory,
  useFeedsWatchCountFullHistory,
  useFeedsWatchCountPerDayHistory,
  useFeedsWatchTimeFullHistory,
} from '../../../hooks/useQuery';
import { Skeleton } from '@zendeskgarden/react-loaders';
import { calculateDecimalHours } from '../../../helpers/helper';
import { IconButton } from '@zendeskgarden/react-buttons';
import { Tooltip } from '@zendeskgarden/react-tooltips';
import DatePickerModal, {
  ITimeLog,
} from '../../modal/feeds/videos/TooltipModal/DatePickerModal';
import { format, subDays } from 'date-fns';
import krakendPaths from '../../../constants/krakendPaths';

const Container = styled.div`
  margin: ${baseTheme.space.md};
  padding: ${baseTheme.space.md};
`;

const StyledCard = styled(Card)`
  border-radius: 12px;
  box-shadow: ${baseTheme.components.properties.boxShadow};
  min-height: 150px;
`;

const Video = styled.video`
  width: 100%;
  height: auto;
`;

const Field = styled(_Field)`
  margin-bottom: ${baseTheme.space.md};
`;

const Image = styled.img`
  width: 90%;
  height: auto;
`;

const StyledMD = styled(_MD)`
  margin-right: ${baseTheme.space.md};
`;

const Overlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.7),
    transparent
  ); /* Fading black background from bottom to top */
  display: flex;
  justify-content: left;
  align-items: end;
`;

const FeedsAnalyticsLayout = () => {
  const [menu, setMenu] = useState<'view' | 'watchTime' | 'engagement'>('view');
  const axios = useAxios();
  const addToast = useToast();
  const [results, setResults] = useState<IFeeds[]>([]);
  const [latestVideo, setLatestVideo] = useState<IFeeds>();
  const navigate = useNavigate();
  const endDate = new Date();

  const startDate = subDays(endDate, 28);

  const formattedStartDate = format(startDate, 'yyyy-MM-dd');
  const formattedEndDate = format(endDate, 'yyyy-MM-dd');

  const [timelog, setTimelog] = useState<ITimeLog | undefined>({
    range: 28,
    startDate: formattedStartDate,
    endDate: formattedEndDate,
  });

  const [watchTime, setWatchTime] = useState<any>();
  const [engagement, setEngagementTime] = useState<any>();

  const [filters, setFilters] = useState<any>({
    page: 1,
    sort: 'popularity',
  });

  const [watchCountFilters, setWatchCountFilters] = useState<any>({
    days: 28,
  });

  const [watchTimeFilters, setWatchTimeFilters] = useState<any>({
    days: 28,
  });

  const [filter, setFilter] = useState<any>({
    days: 28,
  });

  useEffect(() => {
    if (timelog) {
      setFilter({
        days: timelog.range,
        startDate: timelog.startDate,
        endDate: timelog.endDate,
      });
      setWatchTimeFilters({
        days: timelog.range,
        startDate: timelog.startDate,
        endDate: timelog.endDate,
      });
      setWatchCountFilters({
        days: timelog.range,
        startDate: timelog.startDate,
        endDate: timelog.endDate,
      });
    }
  }, [timelog]);

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['get-feeds-popularity'],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds`,
          {
            params: {
              ...filters,
            },
          },
        );

        return response;
      },
      onError: (err) => {
        addToast('error', 'Error encountered !!');
      },
      onSuccess: (data) => {
        setResults(data.data.rows);
        // setLatestVideo(data.data.rows[0]);
      },
    });

  const { isLoading: isLatestVideoLoading } = useQuery({
    queryKey: ['get-feeds-latest'],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(
        `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds`,
        {
          params: {
            page: 1,
          },
        },
      );

      return response;
    },
    onError: (err) => {
      addToast('error', 'Error encountered !!');
    },
    onSuccess: (data) => {
      setLatestVideo(data.data.rows[0]);
    },
  });

  const {
    data: engagementData,
    isLoading: isGenerating,
    refetch: refetchEngagementPerDay,
  } = useEngagementPerDayHistory({ filters: filter });

  const {
    data: feedsWatchCountHistoryData,
    isLoading: isGetting,
    refetch: refetchWatchCountFullHistory,
  } = useFeedsWatchCountFullHistory({ watchCountFilters: watchCountFilters });

  const {
    data: feedsWatchTimeHistoryData,
    isLoading: isAppearing,
    refetch: refetchWatchTimeFullHistory,
  } = useFeedsWatchTimeFullHistory({ watchCountFilters: watchTimeFilters });

  useEffect(() => {
    if (filter && watchCountFilters && watchTimeFilters) {
      refetchEngagementPerDay();
      refetchWatchCountFullHistory();
      refetchWatchTimeFullHistory();
    }
  }, [filter, watchCountFilters, watchTimeFilters]);

  const {
    data: engagement2daysData,
    isLoading: isComing,
    refetch: refetchEngagement2DayHistory,
  } = useEngagementPerDayHistory({
    filters: {
      days: 2,
    },
    enabled: true,
  });

  const {
    data: feedsWatchCount48Data,
    refetch: refetchWatchCount48hours,
    isLoading: isGetting48WC,
  } = useFeedsWatchCountFullHistory({
    watchCountFilters: {
      days: 2,
    },
    enabled: true,
  });

  const {
    data: feedsWatchTime48Data,
    refetch: refetchWatchTime48hours,
    isLoading: isGetting48WT,
  } = useFeedsWatchTimeFullHistory({
    watchCountFilters: {
      days: 2,
    },
    enabled: true,
  });

  const [engageTime, setEngageTime] = useState<any>();

  useEffect(() => {
    if (engagementData) {
      setEngageTime(engagementData?.data[0]?.visitCount);
    }
  }, [engagementData]);

  const [engage2daysTime, setEngage2dayTime] = useState<any>();

  useEffect(() => {
    if (engagement2daysData) {
      setEngage2dayTime(engagement2daysData?.data[0]?.visitCount);
    }
  }, [engagement2daysData]);

  return (
    <>
      <Container>
        <Row>
          <Col size={8.5}>
            <StyledCard bg="white">
              <Row alignItems="center" mt="lg">
                <Col size={10.5}>
                  <Row justifyContent="center">
                    {isGetting && (
                      <>
                        <XXL style={{ width: '100%' }} isCenter isBold>
                          <Skeleton />
                        </XXL>
                      </>
                    )}{' '}
                    {!isGetting && feedsWatchCountHistoryData && (
                      <>
                        <XXL isCenter isBold>
                          All feeds got{' '}
                          {feedsWatchCountHistoryData?.data?.totalWatchCount}{' '}
                          views in the last {filter.days} days
                        </XXL>
                      </>
                    )}
                  </Row>
                </Col>
                <Col size={1.5}>
                  <DatePickerModal setTimeLog={setTimelog} />
                </Col>
              </Row>
              <Row mt="md">
                <Col>
                  <div style={{ padding: baseTheme.space.md }}>
                    <Tiles name="summary-tiles" value={menu}>
                      <Row mt="md">
                        <Col>
                          <Tiles.Tile
                            value="view"
                            onClick={() => {
                              setMenu('view');
                            }}
                          >
                            <Tiles.Icon>
                              <WatchIcon />
                            </Tiles.Icon>
                            <Tiles.Label>Views</Tiles.Label>
                            <Tiles.Description>
                              {isGetting ? (
                                <>
                                  <XL style={{ width: '100%' }} isCenter isBold>
                                    <Skeleton />
                                  </XL>
                                </>
                              ) : (
                                <>
                                  <XL
                                    style={{
                                      color:
                                        menu === 'view'
                                          ? colors.white
                                          : baseTheme.colors.deepBlue,
                                    }}
                                    isBold
                                  >
                                    {feedsWatchCountHistoryData.data
                                      .totalWatchCount != null
                                      ? feedsWatchCountHistoryData.data
                                          .totalWatchCount
                                      : '0'}
                                  </XL>
                                </>
                              )}
                            </Tiles.Description>
                          </Tiles.Tile>
                        </Col>
                        <Col>
                          <Tiles.Tile
                            value="watchTime"
                            onClick={() => {
                              setMenu('watchTime');
                            }}
                          >
                            <Tiles.Icon>
                              <WatchIcon />
                            </Tiles.Icon>
                            <Tiles.Label>Watch time (hours)</Tiles.Label>
                            <Tiles.Description>
                              {isAppearing ? (
                                <>
                                  <XL style={{ width: '100%' }} isCenter isBold>
                                    <Skeleton />
                                  </XL>
                                </>
                              ) : (
                                <>
                                  <XL
                                    isBold
                                    style={{
                                      color:
                                        menu === 'watchTime'
                                          ? colors.white
                                          : baseTheme.colors.deepBlue,
                                    }}
                                  >
                                    {calculateDecimalHours(
                                      feedsWatchTimeHistoryData.data
                                        .totalWatchTime,
                                      2,
                                    )}
                                  </XL>
                                </>
                              )}
                            </Tiles.Description>
                          </Tiles.Tile>
                        </Col>
                        <Col>
                          <Tiles.Tile
                            value="engagement"
                            onClick={() => {
                              setMenu('engagement');
                            }}
                          >
                            <Tiles.Icon>
                              <WatchIcon />
                            </Tiles.Icon>
                            <Tiles.Label>Engagement (visit count)</Tiles.Label>
                            <Tiles.Description>
                              <XL
                                isBold
                                style={{
                                  color:
                                    menu === 'engagement'
                                      ? colors.white
                                      : baseTheme.colors.deepBlue,
                                }}
                              >
                                {engageTime}
                              </XL>
                            </Tiles.Description>
                          </Tiles.Tile>
                        </Col>
                      </Row>
                    </Tiles>
                  </div>
                </Col>
              </Row>
              <Row mt="md">
                <Col>
                  {menu === 'engagement' && (
                    <>
                      <Engagement
                        data={engagementData}
                        isLoading={isGenerating}
                      />
                    </>
                  )}
                  {menu === 'view' && (
                    <>
                      <View isLoading={isGetting} timelog={timelog} />
                    </>
                  )}
                  {menu === 'watchTime' && (
                    <>
                      <WatchTime isLoading={isAppearing} timelog={timelog} />
                    </>
                  )}
                </Col>
              </Row>
            </StyledCard>
            <Row mt="md" mb="md">
              <Col>
                <StyledCard
                  style={{
                    marginTop: baseTheme.space.md,
                    marginBottom: baseTheme.space.md,
                  }}
                  bg="white"
                >
                  <Row mt="md">
                    <Col offset={0.5}>
                      <XXL isBold>
                        Sort by popularity (Top{' '}
                        {results.filter((item) => item.status === 'SUCCESS')
                          .length >= 5
                          ? `5`
                          : 'content'}
                        )
                      </XXL>
                    </Col>
                  </Row>
                  <Row>
                    <Col offset={0.5}>
                      {isLoading && (
                        <>
                          {[0, 0, 0, 0, 0].map((item) => (
                            <>
                              <Row alignItems="center" mt="md">
                                <Col size={2.5}>
                                  <div
                                    style={{
                                      marginLeft: baseTheme.space.sf,
                                      marginRight: baseTheme.space.sf,
                                      marginBottom: baseTheme.space.sf,
                                    }}
                                  >
                                    <Skeleton
                                      style={{
                                        width: '100%',
                                        height: `${
                                          baseTheme.components.dimension.height
                                            .base * 12
                                        }px`,
                                        objectFit: 'contain',
                                        backgroundColor: 'black',
                                        borderRadius: '6%',
                                      }}
                                    />
                                  </div>
                                </Col>
                                <Col size={8}>
                                  <Row>
                                    <MD style={{ width: '90%' }}>
                                      <Skeleton style={{ width: '90%' }} />
                                    </MD>
                                  </Row>
                                  <Row>
                                    <StyledMD>
                                      Views :{' '}
                                      <Span isBold>
                                        <Skeleton />
                                      </Span>
                                    </StyledMD>
                                    <StyledMD>
                                      Likes :{' '}
                                      <Span isBold>
                                        <Skeleton />
                                      </Span>
                                    </StyledMD>
                                    <StyledMD>
                                      Comments :{' '}
                                      <Span isBold>
                                        <Skeleton />
                                      </Span>
                                    </StyledMD>
                                  </Row>
                                </Col>
                                <Col size={1.5}>
                                  <Skeleton
                                    style={{
                                      width: baseTheme.iconSizes.xl,
                                      height: baseTheme.iconSizes.xl,
                                    }}
                                  />
                                </Col>
                              </Row>
                            </>
                          ))}
                        </>
                      )}
                      {!isLoading &&
                        results
                          .filter((item) => item.status === 'SUCCESS')
                          .slice(0, 5)
                          .map((item, index) => (
                            <>
                              <Row alignItems="center" mt="md">
                                <Col size={2.5}>
                                  <div
                                    style={{
                                      marginLeft: baseTheme.space.sf,
                                      marginRight: baseTheme.space.sf,
                                      marginBottom: baseTheme.space.sf,
                                    }}
                                  >
                                    <Image
                                      style={{
                                        width: '100%',
                                        height: `${
                                          baseTheme.components.dimension.height
                                            .base * 12
                                        }px`,
                                        objectFit: 'contain',
                                        backgroundColor: 'black',
                                        borderRadius: '6%',
                                      }}
                                      src={item?.thumbnail_url}
                                    />
                                  </div>
                                </Col>
                                <Col size={8}>
                                  <Row>
                                    <MD style={{ width: '90%' }}>
                                      <Ellipsis style={{ width: '90%' }}>
                                        {item.title}
                                      </Ellipsis>
                                    </MD>
                                  </Row>
                                  <Row>
                                    <StyledMD>
                                      Views :{' '}
                                      <Span isBold>
                                        {item?.totalWatchCount}
                                      </Span>
                                    </StyledMD>
                                    <StyledMD>
                                      Likes :{' '}
                                      <Span isBold>{item?.like_count}</Span>
                                    </StyledMD>
                                    <StyledMD>
                                      Comments :{' '}
                                      <Span isBold>{item?.comment_count}</Span>
                                    </StyledMD>
                                  </Row>
                                </Col>
                                <Col size={1.5}>
                                  <Tooltip
                                    type="dark"
                                    style={{
                                      backgroundColor:
                                        baseTheme.colors.deepBlue,
                                    }}
                                    content="Go to the video analytics"
                                  >
                                    <IconButton
                                      isPrimary
                                      onClick={() => {
                                        navigate(
                                          `${pageRoutes['GO_TO_FEEDS_VIDEO_ANALYTICS']}/${item?.id}`,
                                        );
                                      }}
                                    >
                                      <RightArrowIcon />
                                    </IconButton>
                                  </Tooltip>
                                </Col>
                              </Row>
                            </>
                          ))}
                    </Col>
                  </Row>
                </StyledCard>
              </Col>
            </Row>
          </Col>
          <Col size={3.5}>
            <Row>
              <Col>
                <StyledCard bg="white">
                  <Row mt="lg">
                    <Col>
                      <XL isBold isCenter>
                        Watch Summary
                      </XL>
                    </Col>
                  </Row>

                  <Row mt="md" mb="lg">
                    <Col offset={0.5}>
                      {!isGetting48WC && !isGetting48WT && !isComing && (
                        <>
                          <Row justifyContent="between" mt="md">
                            <Col>
                              <MD style={{ marginBottom: '2px' }} isBold>
                                Watch Count
                              </MD>
                              <SM hue="neutral">
                                <Blockquote>Last 48 hours</Blockquote>
                              </SM>
                            </Col>
                            <Col>
                              <MD hue="primary">
                                {feedsWatchCount48Data?.data?.totalWatchCount !=
                                null
                                  ? `${feedsWatchCount48Data.data.totalWatchCount} views`
                                  : 'Not Present at this moment'}
                              </MD>
                            </Col>
                          </Row>
                          <Row justifyContent="between" mt="md">
                            <Col>
                              <MD style={{ marginBottom: '2px' }} isBold>
                                Watch Time
                              </MD>
                              <SM hue="neutral">
                                <Blockquote>Last 48 hours</Blockquote>
                              </SM>
                            </Col>
                            <Col>
                              <MD hue="primary">
                                {feedsWatchTime48Data?.data?.totalWatchTime !=
                                null
                                  ? `${calculateDecimalHours(
                                      feedsWatchTime48Data.data.totalWatchTime,
                                      2,
                                    )} hours`
                                  : 'Not Present at this moment'}{' '}
                              </MD>
                            </Col>
                          </Row>
                          <Row
                            style={{ marginBottom: baseTheme.space.mf }}
                            justifyContent="between"
                            mt="md"
                          >
                            <Col>
                              <MD style={{ marginBottom: '2px' }} isBold>
                                Engagement Time
                              </MD>
                              <SM hue="neutral">
                                <Blockquote>Last 48 hours</Blockquote>
                              </SM>
                            </Col>
                            <Col>
                              <MD hue="primary">
                                {engage2daysTime} visit count
                              </MD>
                            </Col>
                          </Row>
                        </>
                      )}
                    </Col>
                  </Row>
                </StyledCard>
              </Col>
            </Row>
            <Row mt="md">
              <Col>
                <StyledCard bg="white">
                  <Row mt="lg">
                    <Col>
                      <XL isBold isCenter>
                        Latest Content
                      </XL>
                    </Col>
                  </Row>

                  <Row mt="md" justifyContent="center">
                    <Col>
                      {isLatestVideoLoading && (
                        <>
                          <div
                            style={{
                              margin: baseTheme.space.md,
                            }}
                          >
                            <Field>
                              <Row justifyContent="center">
                                <Skeleton
                                  style={{
                                    width: '90%',
                                    height:
                                      baseTheme.components.dimension.width
                                        .base350,
                                  }}
                                />
                                <Overlay>
                                  <LG
                                    style={{
                                      marginLeft: baseTheme.space.sm,
                                      marginBottom: baseTheme.space.sm,
                                      width: '100%',
                                    }}
                                    color="white"
                                    hue="white"
                                  >
                                    <Ellipsis style={{ width: '90%' }}>
                                      <Skeleton style={{ width: '90%' }} />
                                    </Ellipsis>
                                  </LG>
                                </Overlay>
                              </Row>
                            </Field>
                          </div>
                        </>
                      )}
                      {!isLatestVideoLoading && latestVideo && (
                        <div
                          style={{
                            margin: baseTheme.space.md,
                          }}
                        >
                          <Field>
                            <Row justifyContent="center">
                              <Image src={latestVideo.thumbnail_url} />
                              <Overlay>
                                <LG
                                  style={{
                                    marginLeft: baseTheme.space.sm,
                                    marginBottom: baseTheme.space.sm,
                                    width: '100%',
                                  }}
                                  color="white"
                                  hue="white"
                                >
                                  <Ellipsis style={{ width: '90%' }}>
                                    {latestVideo.title}
                                  </Ellipsis>
                                </LG>
                              </Overlay>
                            </Row>
                          </Field>
                        </div>
                      )}
                    </Col>
                  </Row>
                  <Row mt="md" mb="lg">
                    <Col offset={0.5}>
                      {isLatestVideoLoading && (
                        <>
                          <Row justifyContent="between" mt="md">
                            <Col>
                              <MD isBold>Duration</MD>
                            </Col>
                            <Col>
                              <MD hue="primary">
                                <Skeleton style={{ width: '80%' }} />
                              </MD>
                            </Col>
                          </Row>
                          <Row justifyContent="between" mt="md">
                            <Col>
                              <MD isBold>Watch Count</MD>
                            </Col>
                            <Col>
                              <MD hue="primary">
                                <Skeleton style={{ width: '80%' }} />
                              </MD>
                            </Col>
                          </Row>
                          <Row
                            justifyContent="between"
                            mt="md"
                            style={{ marginBottom: baseTheme.space.mf }}
                          >
                            <Col>
                              <MD isBold>Watch Time</MD>
                            </Col>
                            <Col>
                              <MD hue="primary">
                                <Skeleton style={{ width: '80%' }} />
                              </MD>
                            </Col>
                          </Row>
                        </>
                      )}
                      {!isLatestVideoLoading && latestVideo && (
                        <>
                          <Row justifyContent="between" mt="md">
                            <Col>
                              <MD isBold>Duration</MD>
                            </Col>
                            <Col>
                              <MD hue="primary">
                                {latestVideo.duration
                                  ? latestVideo.duration
                                  : 'Data not available'}
                              </MD>
                            </Col>
                          </Row>
                          <Row justifyContent="between" mt="md">
                            <Col>
                              <MD isBold>Watch Count</MD>
                            </Col>
                            <Col>
                              <MD hue="primary">
                                {latestVideo.totalWatchCount
                                  ? latestVideo.totalWatchCount
                                  : 'Data not available'}
                              </MD>
                            </Col>
                          </Row>
                          <Row
                            justifyContent="between"
                            mt="md"
                            style={{ marginBottom: baseTheme.space.mf }}
                          >
                            <Col>
                              <MD isBold>Watch Time</MD>
                            </Col>
                            <Col>
                              <MD hue="primary">
                                {latestVideo.totalWatchTime
                                  ? calculateDecimalHours(
                                      latestVideo.totalWatchTime,
                                      2,
                                    )
                                  : 'Data not available'}
                              </MD>
                            </Col>
                          </Row>
                        </>
                      )}
                    </Col>
                  </Row>
                  <Row
                    style={{ marginBottom: baseTheme.space.lg }}
                    justifyContent="start"
                    alignItems="center"
                  >
                    <Col offset={0.5}>
                      <Button
                        onClick={() => {
                          navigate(
                            `${pageRoutes['GO_TO_FEEDS_VIDEO_ANALYTICS']}/${latestVideo?.id}`,
                          );
                        }}
                        isPrimary
                      >
                        See Video Analytics
                        <Button.EndIcon>
                          <RightArrowIcon />
                        </Button.EndIcon>
                      </Button>
                    </Col>
                  </Row>
                </StyledCard>
              </Col>
            </Row>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default FeedsAnalyticsLayout;
