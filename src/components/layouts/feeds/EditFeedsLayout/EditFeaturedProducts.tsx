import React, { useEffect, useState } from 'react';
import { Card as _Card } from '../../../UI-components/Card';
import { baseTheme } from '../../../../themes/theme';
import styled from 'styled-components';
import { Col, Row } from '../../../UI-components/Grid';
import {
  DownIcon,
  RemoveIcon,
  ResetIcon,
  SearchIcon,
} from '../../../../utils/icons';
import { LG, MD, SM } from '../../../UI-components/Typography';
import { MediaInput } from '../../../UI-components/MediaInput';
import { IconButton as _IconButton } from '../../../UI-components/IconButton';
import { Ellipsis, Paragraph, Span } from '@zendeskgarden/react-typography';
import { Dropdown, Item, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import { Button } from '../../../UI-components/Button';
import { useMutation, useQuery } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import useAxiosFeeds from '../../../../hooks/useAxiosFeeds';
import { IFeeds } from '../../../../pages/feeds/FeedsVideos';
import useToast from '../../../../hooks/useToast';
import DraggableItems from './DraggableItem';
import { Skeleton } from '@zendeskgarden/react-loaders';
import { ReactComponent as EmptyIcon } from '../../../../assets/item-icon.svg';
import emptyImage from '../../../../../src/assets/item.png';
import krakendPaths from '../../../../constants/krakendPaths';
import { KRAKEND_API_TOKENS } from '../../../../../env';
// import { ReactComponent as EmptyIcon } from '../../../../assets/item-icon.svg';

const DraggableProduct = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  border-radius: ${baseTheme.components.dimension.width.base}px;
  border: ${baseTheme.borders.sm} ${baseTheme.colors.greyBorderColor};
  background-color: ${baseTheme.colors.white};
  padding: ${baseTheme.space.md};
  margin: ${baseTheme.space.one * 10}px 0px;
  height: ${baseTheme.components.dimension.width.base250};
`;

const Card = styled(_Card)`
  margin: ${baseTheme.space.md};
  padding: ${baseTheme.space.md};
`;

const IconButton = styled(_IconButton)`
  transform: translateY(${baseTheme.space.one * 2}px);
  width: ${baseTheme.iconSizes.md};
  height: ${baseTheme.iconSizes.md};
`;

const EditFeaturedProducts = ({ videoData }: { videoData: IFeeds }) => {
  const [rotated, setRotated] = useState<boolean | undefined>();
  const [searchType, setSearchType] = useState<'id' | 'name' | 'sku'>('name');
  const [productArr, setProductArr] = useState<any[]>([]);
  const [addedArr, setAddedArr] = useState<any[]>([]);

  const [addArr, setAddArr] = useState<any[]>([]);

  useEffect(() => {
    if (videoData) {
      if (videoData?.featured_product) {
        const featuredProductIds: any[] = JSON.parse(
          videoData?.featured_product,
        );
        const featureProductArray = featuredProductIds.filter(
          (item) => item != null,
        );
        setAddArr(featureProductArray);
      }
    }
  }, [videoData]);

  const resetSearch = () => {
    setSearchContent('');
    setFilters({
      page: 1,
      status: 'enabled',
    });
  };

  const handleKeyPress = (event: any) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleSearch = () => {
    if (searchContent && searchContent !== '') {
      setFilters((prev: any) => ({
        // ...prev,
        sku: searchType === 'sku' ? searchContent : undefined,
        id:
          searchType === 'id'
            ? searchContent && parseInt(searchContent)
            : undefined,
        name: searchType === 'name' ? searchContent : undefined,
        page: 1,
        pageSize: 50,
      }));
    } else {
      addToast('info', `Please provide ${searchType.toUpperCase()} to search`);
    }
  };

  const handleDragStart = (e: any, index: number) => {
    e.dataTransfer.setData('index', index);
  };

  const handleDragOver = (e: any) => {
    e.preventDefault();
  };

  const handleDrop = (e: any) => {
    const index = e.dataTransfer.getData('index');
    if (addArr.length < 7) {
      const productId = productArr[index].id;
      const newProductArr = [...productArr];
      newProductArr.splice(index, 1);
      setProductArr(newProductArr);
      setAddArr([...addArr, productId]);
      const id = productId;
      const idArray = addArr;
      const updatedArray = idArray.concat(id);
      const updatedVal = {
        featured_product: JSON.stringify(updatedArray),
      };
      updateVideo(updatedVal);
    } else {
      addToast('error', 'Maximum featured product reached');
    }
  };

  const handleRemove = (index: number) => {
    const removedItem = addArr[index];
    const removedProduct = addedArr[index];
    const addUpdateArr = addArr.filter((item, i) => i !== index);
    setAddedArr((prevArr) => prevArr.filter((item, i) => i !== index));
    setAddArr((prevAddArr) => prevAddArr.filter((item, i) => i !== index));
    setProductArr((prevProductArr) => [...prevProductArr, removedProduct]);
    const idArray = addUpdateArr.map((item) => item);
    const updatedVal = {
      featured_product: JSON.stringify(idArray),
      // featured_product: idArray.length != 0 ? JSON.stringify(idArray) : null,

      // idArray.length != 0 ? JSON.stringify(idArray) : undefined,
    };
    updateVideo(updatedVal);
  };

  const axios = useAxios();
  const axiosFeeds = useAxiosFeeds();

  const [filters, setFilters] = useState<any>({
    page: 1,
    status: 'enabled',
  });

  const [searchContent, setSearchContent] = useState<string>();
  const [imageErrors, setImageErrors] = useState<boolean[]>([]);

  const [data, setData] = useState<any[]>();

  const {
    isLoading,
    isError,
    error,
    data: productDataList,
    isRefetching,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ['product-add-list'],
    queryFn: async (): Promise<any> => {
      return await axios.post(
        `${krakendPaths.NODE_SVLSS_URL}/admin-api/v1/products`,
        {
          ...filters,
          sku: filters?.sku ? [filters.sku] : undefined,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': KRAKEND_API_TOKENS.SVLSS,
          },
        },
      );
    },
    enabled: false,
    onError: (err) => {
      console.log(err);
    },
    onSuccess(data) {
      setData(data);
      setProductArr(data.products);
    },
  });

  useEffect(() => {
    if (filters) {
      refetch();
    }
  }, [filters]);

  const handleImageError = (index: number) => {
    const updatedErrors = [...imageErrors];
    updatedErrors[index] = true;
    setImageErrors(updatedErrors);
  };

  const addToast = useToast();

  const { mutate: updateVideo, isLoading: isCreating } = useMutation(
    async (featureProducts: any) => {
      const response = await axiosFeeds.put(
        `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds/${videoData.id}`,
        featureProducts,
        {
          headers: {
            Authorization: localStorage.getItem('api-token'),
            'client-type': 'web_admin',
          },
        },
      );
      return response;
    },
    {
      onError: (err) => {
        // close();
        addToast('error', 'Error occured in updating this video');
      },
      onSuccess: (data) => {
        // close();
        setTimeout(() => {
          addToast('success', 'Product updated successfully');
        }, 50);
      },
    },
  );

  const emptyArray = new Array(8).fill(undefined);

  // custom_attr me check kro -> parent_img_url me check -> media _ gallery _entries -> Pir default image

  const IMAGE_URL = 'https://images1.dentalkart.com/media/catalog/product';

  const getProductImage = (product: any): string => {
    // Step 1: Check media_gallery_entries
    if (product.media_gallery_entries.length > 0) {
      for (const entry of product.media_gallery_entries) {
        if (entry.media_type === 'image' && !entry.disabled) {
          return entry.file;
        }
      }
    }

    // Step 2: Check custom_attributes
    const imageAttribute = product.custom_attributes.find(
      (attr: any) => attr.attribute_code === 'image',
    );

    if (imageAttribute && imageAttribute.value !== '') {
      return imageAttribute.value;
    }

    // Step 3: Check parent_image_url
    if (product.parent_image_url !== '') {
      return product.parent_image_url;
    }

    // Step 4: Return empty image
    return emptyImage;
  };

  return (
    <Card bg="white">
      <Row style={{ flex: 1 }}>
        <Col size={7.5}>
          <Row>
            <Col size={2.9}>
              <Dropdown
                onSelect={(item) => setSearchType(item)}
                onStateChange={(options) =>
                  Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
                }
              >
                <Trigger>
                  <Button>
                    {searchType === 'name'
                      ? 'Search by Name'
                      : searchType === 'id'
                      ? 'Search by Id'
                      : 'Search by SKU'}
                    <Button.EndIcon isRotated={rotated}>
                      <DownIcon />
                    </Button.EndIcon>
                  </Button>
                </Trigger>
                <Menu>
                  <Item value="name">Search by Name</Item>
                  <Item value="id">Search by Id</Item>
                  <Item value="sku">Search by SKU</Item>
                </Menu>
              </Dropdown>
            </Col>
            <Col offset={0.1} size={6.5}>
              <div style={{ marginBottom: baseTheme.space.sm }}>
                <MediaInput
                  style={{
                    padding: 0,
                    zIndex: 400,
                  }}
                  placeholder="Search Product"
                  value={searchContent}
                  end={
                    <>
                      <IconButton onClick={resetSearch}>
                        <ResetIcon
                          style={{
                            width: baseTheme.iconSizes.md,
                            height: baseTheme.iconSizes.md,
                          }}
                          onClick={resetSearch}
                        />
                      </IconButton>
                    </>
                  }
                  onChange={(e) => {
                    const val = e.target.value;
                    setSearchContent(val);
                  }}
                  onKeyPress={handleKeyPress}
                />
              </div>
            </Col>
            <Col size={2}>
              <Button
                isPrimary
                style={{
                  transform: `translateY(${baseTheme.space.one * 2}px)`,
                }}
                onClick={handleSearch}
              >
                <Button.StartIcon>
                  <SearchIcon
                    style={{
                      width: baseTheme.iconSizes.md,
                      height: baseTheme.iconSizes.md,
                    }}
                  />
                </Button.StartIcon>
                Search
              </Button>
            </Col>
          </Row>
          <div
            id="prodDiv"
            style={{ height: '60vh', overflowY: 'scroll', overflowX: 'clip' }}
          >
            <Row>
              {isLoading &&
                emptyArray.map((item) => {
                  return (
                    <>
                      <Col size={2.9}>
                        <DraggableProduct
                          draggable
                          style={{ position: 'relative' }}
                        >
                          <div
                            style={{
                              height: `${baseTheme.components.dimension.width.base150}`,
                              width: `${baseTheme.components.dimension.width.base150}`,
                              objectFit: 'cover',
                              borderRadius: baseTheme.borderRadii.xmd,
                            }}
                          >
                            <Skeleton
                              style={{
                                width: '100%',
                                height: '100%',
                                color: baseTheme.colors.veryDarkGray,
                              }}
                            />
                          </div>
                          <div
                            style={{
                              width: '100%',
                              position: 'relative',
                            }}
                          >
                            <Paragraph>
                              <SM isCenter isBold>
                                <Skeleton
                                  style={{
                                    width: '100%',
                                    height:
                                      baseTheme.components.dimension.height.sm,
                                    color: baseTheme.colors.veryDarkGray,
                                  }}
                                />
                              </SM>
                            </Paragraph>
                          </div>
                          <p
                            style={{
                              fontWeight: '500',
                            }}
                          >
                            <Skeleton
                              style={{
                                width: '100%',
                                height:
                                  baseTheme.components.dimension.height.sm,
                                color: baseTheme.colors.veryDarkGray,
                              }}
                            />
                          </p>
                        </DraggableProduct>
                      </Col>
                    </>
                  );
                })}
              {!isLoading &&
                productArr.map((item, index) => (
                  <>
                    <Col size={2.9}>
                      <>
                        <DraggableProduct
                          onDragStart={(e) => handleDragStart(e, index)}
                          draggable
                          style={{ position: 'relative' }}
                          onClick={(e) => handleDragStart(e, index)}
                        >
                          <div>
                            <img
                              src={
                                getProductImage(item) === emptyImage
                                  ? emptyImage
                                  : `${IMAGE_URL}${getProductImage(item)}`
                              }
                              height={
                                baseTheme.components.dimension.width.base150
                              }
                              width={
                                baseTheme.components.dimension.width.base150
                              }
                              onError={() => handleImageError(index)}
                            />
                          </div>
                          <div
                            style={{
                              width: '100%',
                              position: 'relative',
                            }}
                          >
                            <Paragraph>
                              <SM isCenter isBold>
                                {item.name}
                              </SM>
                            </Paragraph>
                          </div>
                          <div
                            style={{
                              position: 'absolute',
                              top: baseTheme.space.md,
                              left: baseTheme.space.md,
                              border: `${baseTheme.borders.sm} ${baseTheme.colors.lightBlack}`,
                              borderRadius: baseTheme.borderRadii.xxxl,
                              width: baseTheme.space.md,
                              height: baseTheme.space.md,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              cursor: 'pointer',
                              zIndex: 200,
                            }}
                            onDragStart={(e) => handleDragStart(e, index)}
                            onClick={(e) => handleDragStart(e, index)}
                          >
                            {index + 1}
                          </div>
                          <p
                            style={{
                              color: item.status === 1 ? 'green' : 'red',
                              fontWeight: '500',
                            }}
                          >
                            {item.status === 1 ? 'Enabled' : 'Disabled'}
                          </p>
                        </DraggableProduct>
                      </>
                    </Col>
                  </>
                ))}
            </Row>
          </div>
        </Col>
        <Col size={4.5}>
          <div style={{ height: '60vh', overflowY: 'auto', overflowX: 'clip' }}>
            <MD isBold isCenter>
              Added Products {`(${addArr.length})`}
            </MD>
            <Row
              style={{
                borderLeft: `${baseTheme.borders.sm} ${baseTheme.colors.primaryHue}`,
              }}
            >
              <Col>
                <div
                  id="productDiv"
                  style={{
                    minHeight: `${
                      baseTheme.components.dimension.height.base * 55
                    }px`,
                    maxHeight: `${
                      baseTheme.components.dimension.height.base * 60
                    }px`,
                    overflow: 'auto',
                  }}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                >
                  {addArr.map((item, index) => (
                    <>
                      <DraggableItems
                        // handleDragOver={handleDragOver}
                        // handleDrop={handleDrop}
                        handleRemove={() => handleRemove(index)}
                        itemId={item}
                        index={index}
                        key={index}
                        setAddedArr={setAddedArr}
                      />
                    </>
                  ))}
                  {addArr.length === 0 && (
                    <>
                      <div
                        id="emptyProduct"
                        style={{
                          padding: baseTheme.space.xl,
                          borderRadius: baseTheme.borderRadii.xmd,
                          border: `${baseTheme.borders.md} ${baseTheme.colors.grey}`,
                          margin: baseTheme.space.lg,
                          height: `${
                            baseTheme.components.dimension.height.base * 29
                          }px`,
                        }}
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                      >
                        <Row style={{ height: '100%' }} alignItems="center">
                          <Col>
                            <LG isCenter>
                              <Span hue={baseTheme.colors.grey} isBold>
                                Drag Products here to add them in this video
                              </Span>
                            </LG>
                          </Col>
                        </Row>
                      </div>
                    </>
                  )}
                </div>
              </Col>
            </Row>
          </div>
        </Col>
      </Row>
    </Card>
  );
};

export default EditFeaturedProducts;
