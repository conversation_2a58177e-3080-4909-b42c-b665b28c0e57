import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { baseTheme } from '../../../../themes/theme';
import { SM } from '../../../UI-components/Typography';
import { Ellipsis } from '@zendeskgarden/react-typography';
import { RemoveIcon } from '../../../../utils/icons';
import useAxios from '../../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import { Skeleton } from '@zendeskgarden/react-loaders';
import emptyImage from '../../../../../src/assets/item.png';
import krakendPaths from '../../../../constants/krakendPaths';
import { KRAKEND_API_TOKENS } from '../../../../../env';

const DraggableItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${baseTheme.borderRadii.sm};
  border: ${baseTheme.borders.sm} ${baseTheme.colors.greyBorderColor};
  background-color: ${baseTheme.colors.white};
  padding: ${baseTheme.space.one * 10}px;
  margin: ${baseTheme.space.one * 10}px ${baseTheme.space.one * 30}px;
`;

const DraggableItemInner = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 80%;
  margin: ${baseTheme.space.one * 10}px;
`;

const DraggableItems = ({
  // handleDragOver,
  // handleDrop,
  itemId,
  handleRemove,
  index,
  setAddedArr,
}: {
  // handleDragOver: (e: any) => void;
  // handleDrop: (e: any) => void;
  itemId: number;
  handleRemove: (index: number) => void;
  index: number;
  setAddedArr: React.Dispatch<React.SetStateAction<any[]>>;
}) => {
  const [imageAddErrors, setAddImageErrors] = useState<boolean>(false);
  const [item, setItem] = useState<any>();
  const axios = useAxios();

  const { refetch: refetchProduct, isLoading } = useQuery({
    queryKey: ['product-get-specific-list', itemId],
    queryFn: async (): Promise<any> => {
      return await axios.post(
        `${krakendPaths.NODE_SVLSS_URL}/admin-api/v1/products`,
        {
          page: 1,
          id: itemId,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': KRAKEND_API_TOKENS.SVLSS,
          },
        },
      );
    },
    enabled: false,
    onError: (err: any) => {
      console.log(err);
    },
    onSuccess(data: any) {
      setItem(data.products[0]);
    },
  });

  useEffect(() => {
    item && setAddedArr((prevArr) => [...prevArr, item]);
  }, [item]);

  useEffect(() => {
    itemId && refetchProduct();
  }, [itemId]);

  const handleAddImgErr = () => {
    setAddImageErrors(true);
  };

  const IMAGE_URL = 'https://images1.dentalkart.com/media/catalog/product';

  const getProductImage = (product: any): string => {
    // Step 1: Check media_gallery_entries
    if (product.media_gallery_entries.length > 0) {
      for (const entry of product.media_gallery_entries) {
        if (entry.media_type === 'image' && !entry.disabled) {
          return entry.file;
        }
      }
    }

    // Step 2: Check custom_attributes
    const imageAttribute = product.custom_attributes.find(
      (attr: any) => attr.attribute_code === 'image',
    );

    if (imageAttribute && imageAttribute.value !== '') {
      return imageAttribute.value;
    }

    // Step 3: Check parent_image_url
    if (product.parent_image_url !== '') {
      return product.parent_image_url;
    }

    // Step 4: Return empty image
    return emptyImage;
  };

  return (
    <>
      {item && item !== null && (
        <>
          {/* <DraggableItem onDragOver={handleDragOver} onDrop={handleDrop}> */}

          <DraggableItem>
            <DraggableItemInner>
              <img
                src={
                  getProductImage(item) === emptyImage
                    ? emptyImage
                    : `${IMAGE_URL}${getProductImage(item)}`
                }
                alt={'product'}
                width={`${baseTheme.components.dimension.height.md}`}
                height={`${baseTheme.components.dimension.height.md}`}
                onError={() => handleAddImgErr()}
              ></img>

              <SM hue="black">
                <Ellipsis style={{ width: 200 }}>{item.name}</Ellipsis>
              </SM>
              <RemoveIcon
                onClick={() => {
                  handleRemove(index);
                }}
                style={{ cursor: 'pointer' }}
              />
            </DraggableItemInner>
          </DraggableItem>
        </>
      )}
      {isLoading && (
        <>
          <DraggableItem>
            <Skeleton
              style={{
                width: '100%',
                height: baseTheme.components.dimension.height.sm,
              }}
            />
          </DraggableItem>
        </>
      )}
    </>
  );
};

export default DraggableItems;
