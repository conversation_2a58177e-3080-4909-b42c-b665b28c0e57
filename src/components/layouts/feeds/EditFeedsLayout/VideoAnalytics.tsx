import React, { useEffect, useState } from 'react';
import { baseTheme } from '../../../../themes/theme';
import { Card } from '../../../UI-components/Card';
import { Col, Row } from '../../../UI-components/Grid';
import { MD, XXL } from '../../../UI-components/Typography';
import { Ellip<PERSON>, Span } from '@zendeskgarden/react-typography';
import styled from 'styled-components';
import {
  CalendarEditIcon,
  CommentIcon,
  LikeIcon,
} from '../../../../utils/icons';
import { IFeeds } from '../../../../pages/feeds/FeedsVideos';
import { WatchIcon } from 'lucide-react';
import { Accordion } from '../../../UI-components/Accordion';
import GeneralAccordion from '../../../accordion/GeneralAccordion';
import CommentTable from '../../../table/feeds/CommentTable';
import WatchTimeLineChart from '../../../charts/Feeds/Videos/WatchTimeChart';
import WatchCountLineChart from '../../../charts/Feeds/Videos/WatchCountChart';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxiosFeeds';
import useToast from '../../../../hooks/useToast';
import FEEDS_URL from '../../../../constants/index';
import NothingToshow from '../../../UI-components/NothingToShow';
import { IconButton } from '../../../UI-components/IconButton';
import DatePickerModal, {
  ITimeLog,
} from '../../../modal/feeds/videos/TooltipModal/DatePickerModal';
import { filter } from 'lodash';
import { Skeleton } from '@zendeskgarden/react-loaders';
import krakendPaths from '../../../../constants/krakendPaths';

const StyledCard = styled(Card)`
  border-radius: 12px;
  box-shadow: ${baseTheme.components.properties.boxShadow};
  min-height: 150px;
`;

export interface IComments {
  id: number;
  customer_id: number;
  video_id: number;
  comment: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface IWatchTime {
  date: string;
  totalWatchTime: string;
}

export interface IWatchCount {
  date: string;
  totalWatchCount: string;
}

const VideoAnalytics = ({ videoData }: { videoData: IFeeds }) => {
  const [expandedSections, setExpandedSections] = useState<number[]>([]);
  const [comments, setComments] = useState<IComments[]>();
  const [commentCount, setCommentCount] = useState<number>();
  const [watchCount, setWatchCount] = useState<IWatchCount[]>([]);
  const [watchTime, setWatchTime] = useState<IWatchTime[]>([]);
  const [watchtimelog, setWatchTimelog] = useState<ITimeLog | undefined>(
    undefined,
  );
  const [watchcountlog, setWatchcountlog] = useState<ITimeLog | undefined>(
    undefined,
  );

  const [commentFilter, setCommentFilter] = useState<any>({
    page: 1,
  });

  const axios = useAxios();
  const addToast = useToast();

  const { refetch: refetchWatchCount, isLoading: isWatchCountLoading } =
    useQuery({
      queryKey: ['get-video-watch-count'],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds/summary/watch-count`,
          {
            params: {
              video_id: videoData.id,
              days: watchcountlog?.range ? watchcountlog?.range : 28,
              startDate: watchcountlog?.startDate,
              endDate: watchcountlog?.endDate,
            },
          },
        );

        return response;
      },
      onError: (err) => {
        addToast('error', 'Error encountered in getting watch count !!');
      },
      onSuccess: (data) => {
        setWatchCount(data.data);
      },
    });

  const { refetch: refetchWatchTime, isLoading: isWatchTimeLoading } = useQuery(
    {
      queryKey: ['get-video-watch-time'],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds/summary/watch-time`,
          {
            params: {
              video_id: videoData.id,
              days: watchtimelog?.range ? watchtimelog?.range : 28,
              startDate: watchtimelog?.startDate,
              endDate: watchtimelog?.endDate,
            },
          },
        );

        return response;
      },
      onError: (err) => {
        addToast('error', 'Error encountered in getting watch time !!');
      },
      onSuccess: (data) => {
        setWatchTime(data.data);
      },
    },
  );

  const { refetch: refetchVideoComment } = useQuery({
    queryKey: ['get-video-comments'],
    enabled: false,
    queryFn: async (): Promise<any> => {
      const response = await axios.get(
        `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds-action/comments`,
        {
          params: {
            video_id: videoData.id,
            ...commentFilter,
          },
        },
      );

      return response;
    },
    onError: (err) => {
      addToast('error', 'Error encountered in getting comments !!');
    },
    onSuccess: (data) => {
      setComments(data.data.rows);
      setCommentCount(data.data.count);
    },
  });

  useEffect(() => {
    if (watchtimelog) {
      refetchWatchTime();
    }
  }, [watchtimelog]);

  useEffect(() => {
    if (watchcountlog) {
      refetchWatchCount();
    }
  }, [watchcountlog]);

  useEffect(() => {
    refetchVideoComment();
  }, [commentFilter]);

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading for 1 second
    const loadingTimeout = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    // Clear the timeout when the component unmounts
    return () => {
      clearTimeout(loadingTimeout);
    };
  }, []);

  return (
    <Card
      bg="white"
      style={{ margin: baseTheme.space.md, padding: baseTheme.space.md }}
    >
      <Row mb="lg">
        <Col>
          <XXL isBold>
            <Ellipsis style={{ width: '90%' }}>{'Video Analytics'}</Ellipsis>
          </XXL>
        </Col>
      </Row>
      <Row mb="lg">
        <Col size={6}>
          <StyledCard bg="white">
            <Row alignItems="center" mt="md">
              <Col size={4} offset={0.5}>
                <MD isBold>Watch Time History</MD>
              </Col>
              <Col offset={5} size={2}>
                <DatePickerModal setTimeLog={setWatchTimelog} />
              </Col>
            </Row>
            <div style={{ padding: baseTheme.space.md }}>
              {isLoading || isWatchTimeLoading ? (
                <>
                  <Skeleton
                    style={{
                      width: '100%',
                      height: baseTheme.components.dimension.width.base300,
                    }}
                  />
                </>
              ) : (
                <>
                  {watchTime && watchTime.length != 0 ? (
                    <WatchTimeLineChart
                      key={videoData.id}
                      watchTimeData={watchTime}
                    />
                  ) : (
                    <>
                      <Row justifyContent="center">
                        <NothingToshow divHeight={'40vh'} />
                      </Row>
                    </>
                  )}
                </>
              )}
            </div>
          </StyledCard>
        </Col>
        <Col size={6}>
          <StyledCard bg="white">
            <Row alignItems="center" mt="md">
              <Col size={4} offset={0.5}>
                <MD isBold>Watch Count History</MD>
              </Col>
              <Col offset={5} size={2}>
                <DatePickerModal
                  translateX={`-${baseTheme.components.dimension.width.base250}`}
                  setTimeLog={setWatchcountlog}
                />
              </Col>
            </Row>
            <div style={{ padding: baseTheme.space.md }}>
              {isLoading || isWatchCountLoading ? (
                <>
                  <Skeleton
                    style={{
                      width: '100%',
                      height: baseTheme.components.dimension.width.base300,
                    }}
                  />
                </>
              ) : (
                <>
                  {watchCount && watchCount.length != 0 ? (
                    <WatchCountLineChart
                      key={videoData.id}
                      watchCountData={watchCount}
                    />
                  ) : (
                    <>
                      <Row justifyContent="center">
                        <NothingToshow divHeight={'40vh'} />
                      </Row>
                    </>
                  )}
                </>
              )}
            </div>
          </StyledCard>
        </Col>
      </Row>
      <Row>
        <Col size={4}>
          <StyledCard bg="white">
            <Row mt="md" justifyContent="between">
              <Col offset={0.5}>
                <Span
                  style={{ fontSize: '28px' }}
                  hue={baseTheme.colors.deepBlue}
                  isBold
                >
                  <Span.StartIcon>
                    <LikeIcon style={{ width: '36px', height: '36px' }} />
                  </Span.StartIcon>
                  Likes
                </Span>
              </Col>
              <Col></Col>
            </Row>
            <Row mt="md">
              <Col offset={0.5}>
                <Span
                  style={{ fontSize: '28px' }}
                  hue={baseTheme.colors.deepBlue}
                  isBold
                >
                  {videoData?.like_count}
                </Span>
              </Col>
            </Row>
          </StyledCard>
        </Col>
        <Col size={4}>
          <StyledCard bg="white">
            <Row mt="md" justifyContent="between">
              <Col offset={0.5} size={7}>
                <Span
                  style={{ fontSize: '28px' }}
                  hue={baseTheme.colors.deepBlue}
                  isBold
                >
                  <Span.StartIcon>
                    <CommentIcon style={{ width: '36px', height: '36px' }} />
                  </Span.StartIcon>
                  Comments
                </Span>
              </Col>
              <Col size={5}></Col>
            </Row>
            <Row mt="md">
              <Col offset={0.5}>
                <Span
                  style={{ fontSize: '28px' }}
                  hue={baseTheme.colors.deepBlue}
                  isBold
                >
                  {commentCount}
                </Span>
              </Col>
            </Row>
          </StyledCard>
        </Col>
        <Col size={4}>
          <StyledCard bg="white">
            <Row mt="md" justifyContent="between">
              <Col offset={0.5} size={7}>
                <Span
                  style={{ fontSize: '28px' }}
                  hue={baseTheme.colors.deepBlue}
                  isBold
                >
                  <Span.StartIcon>
                    <WatchIcon style={{ width: '36px', height: '36px' }} />
                  </Span.StartIcon>
                  Watch Count
                </Span>
              </Col>
              <Col size={5}></Col>
            </Row>
            <Row mt="md">
              <Col offset={0.5}>
                <Span
                  style={{ fontSize: '28px' }}
                  hue={baseTheme.colors.deepBlue}
                  isBold
                >
                  {videoData.totalWatchCount}
                </Span>
              </Col>
            </Row>
          </StyledCard>
        </Col>
      </Row>
      {comments && commentCount ? (
        <Row>
          <Col>
            <Accordion
              level={4}
              isBare
              isAnimated
              expandedSections={expandedSections}
              onChange={(index) => {
                if (expandedSections.includes(index)) {
                  setExpandedSections(
                    expandedSections.filter((n) => n !== index),
                  );
                } else {
                  setExpandedSections([...expandedSections, index]);
                }
              }}
            >
              <GeneralAccordion
                indexing={1}
                title={'All Comments'}
                children={
                  <CommentTable
                    refetch={refetchVideoComment}
                    filter={commentFilter}
                    setFilter={setCommentFilter}
                    count={commentCount}
                    data={comments}
                  />
                }
                expandedSections={expandedSections}
                setExpandedSections={setExpandedSections}
              />
            </Accordion>
          </Col>
        </Row>
      ) : (
        <></>
      )}
    </Card>
  );
};

export default VideoAnalytics;
