import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Card } from '../../../UI-components/Card';
import { Col, Row } from '../../../UI-components/Grid';
import { useFeedVideoContext } from '../../../../pages/feeds/FeedVideoContext';
import { IFeeds } from '../../../../pages/feeds/FeedsVideos';
import styled from 'styled-components';
import { baseTheme } from '../../../../themes/theme';
import {
  Field as _Field,
  FileList,
  FileUpload,
  Input,
  Label,
  MediaInput,
  Message,
  Textarea as _TextArea,
} from '@zendeskgarden/react-forms';
import { LinkIcon, SaveIcon } from '../../../../utils/icons';
import useToast from '../../../../hooks/useToast';
import { MD, XMD, XXL, XXXL } from '../../../UI-components/Typography';
import { useDropzone } from 'react-dropzone';
import { FileItem } from '../../../uploads/feeds/FeedImageUpload';
import useAxios from '../../../../hooks/useAxiosFeeds';
import { Ellipsis } from '@zendeskgarden/react-typography';
import {
  Dropdown,
  Field as DField,
  Select,
  Label as Dlabel,
  Menu,
  Item,
} from '@zendeskgarden/react-dropdowns';
import { IFeedsCategory } from '../../../../pages/feeds/FeedsCategory';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '../../../UI-components/Button';
import { useGetFeedsTags } from '../../../../hooks/useQuery';
import TagsMultiDropdown from '../../../dropdown/feeds/TagsMultiDropdown';
import { filter } from 'lodash';
import krakendPaths from '../../../../constants/krakendPaths';

const Image = styled.img`
  width: 90%;
  height: auto;
`;

const Video = styled.video`
  width: 90%;
  height: 300px;
  display: block;
  margin: auto;
  margin-top: ${baseTheme.space.md};
`;

const StyledFileUpload = styled(FileUpload)`
  min-height: ${(p) => p.theme.space.base * 20}px;
`;

const Field = styled(_Field)`
  margin-bottom: ${baseTheme.space.md};
`;

interface IPostFeeds {
  thumbnail?: File | null;
  video?: File | null;
  caption?: string;
  title?: string;
  tags?: string[];
  aspect_ratio?: string;
}

const Textarea = styled(_TextArea)`
  font-size: ${baseTheme.customFontSizes.xmd};
`;

const EditFeeds = ({ videoData }: { videoData: IFeeds }) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const addToast = useToast();
  const [files, setFiles] = useState<string[]>([]);
  const [disableThumbnail, setDisableThumbnail] = useState<boolean>(false);
  const [selectedTags, setSelectedTags] = useState<IFeedsCategory[]>([]);
  const [preSelectedTags, setPreSelectedTags] = useState<IFeedsCategory[]>();
  const [videoUrl, setVideoUrl] = useState<any>();

  const [feeddata, setFeedData] = useState<IPostFeeds>({});

  const queryClient = useQueryClient();

  // const [selectedCategory, setSelectedCategory] = useState<
  //   IFeedsCategory | undefined
  // >();

  // const [categories, setCategories] = useState<IFeedsCategory[]>([]);

  // useEffect(() => {
  //   if (categories && categories.length != 0) {
  //     const object = categories.find((item) => item.id);
  //     setSelectedCategory(object);
  //   }
  // }, [categories]);

  const { data: tags, isLoading: isTagsLoading } = useGetFeedsTags({
    unique: '3',
  });

  const [tagsData, setTagsData] = useState<IFeedsCategory[]>();

  useEffect(() => {
    if (tags) {
      setTagsData(tags);
    }
  }, [tags]);

  useEffect(() => {
    if (videoData) {
      // console.log('Video Data', videoData);
      const videoTags = videoData.categories.map((tag) => String(tag.id));
      const filteredTags = tagsData?.filter((tag) =>
        videoTags.includes(String(tag.id)),
      );

      filteredTags && setPreSelectedTags(filteredTags);
      setFeedData((prev) => ({
        ...prev,
        title: videoData.title,
        caption: videoData.caption,
      }));

      setVideoUrl(videoData.video_url);
    }
  }, [videoData, tagsData]);

  useEffect(() => {
    if (selectedTags.length !== 0) {
      const tagIdsAsString = selectedTags.map((tag) => String(tag.id));
      setFeedData((prev) => ({
        ...prev,
        tags: tagIdsAsString,
      }));
    }
  }, [selectedTags]);

  const [errors, setErrors] = useState({
    thumbnail: '',
    video: '',
    caption: '',
    title: '',
    category_id: '',
    aspect_ratio: '',
  });

  const axios = useAxios();

  const validate = () => {
    let errorsCode: any = {};
    errorsCode.thumbnail =
      feeddata?.thumbnail != null ? '' : 'Thumbnail is required !';
    errorsCode.video = feeddata?.video != null ? '' : 'Video is required !';
    errorsCode.caption =
      feeddata?.caption != '' ? '' : 'Description is required !';
    errorsCode.title = feeddata?.title != '' ? '' : 'Title is required !';
    errorsCode.category_id =
      feeddata?.tags?.length != 0 ? '' : 'Tags are required !';
    errorsCode.aspect_ratio =
      feeddata?.aspect_ratio != '' ? '' : 'Aspect Ratio is required !';
    setErrors({ ...errorsCode });
    return Object.values(errorsCode).every((x) => x === '');
  };

  const copyToClipboard = () => {
    if (inputRef.current) {
      inputRef.current.select();
      document.execCommand('copy');
      addToast('success', 'Copied to clipboard');
    }
  };

  // function cleanVideoFilename(videoUrl: string) {
  //   const filenameWithPercentEncoding = videoUrl?.split('/').pop();
  //   if (filenameWithPercentEncoding) {
  //     const decodedFilename = decodeURIComponent(filenameWithPercentEncoding);
  //     const cleanedFilename = decodedFilename
  //       .replace(/%20/g, ' ')
  //       .replace(/%28/g, '(')
  //       .replace(/%29/g, ')');
  //     return cleanedFilename;
  //   } else {
  //     return filenameWithPercentEncoding;
  //   }
  // }

  function cleanVideoFilename(videoUrl: string) {
    const filenameWithPercentEncoding = videoUrl?.split('/').pop();
    if (filenameWithPercentEncoding) {
      try {
        const decodedFilename = decodeURIComponent(filenameWithPercentEncoding);
        const cleanedFilename = decodedFilename
          .replace(/%20/g, ' ')
          .replace(/%28/g, '(')
          .replace(/%29/g, ')');
        return cleanedFilename;
      } catch (error) {
        console.error('Error decoding filename:', error);
        return filenameWithPercentEncoding;
      }
    } else {
      return filenameWithPercentEncoding;
    }
  }

  const removeFile = useCallback(
    (fileIndex: any) => {
      setFiles(files.filter((_, index) => index !== fileIndex));
      setDisableThumbnail(false);
      setFeedData((prev) => ({
        ...prev,
        thumbnail: null,
      }));
    },
    [files],
  );

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        setFeedData((prev) => ({
          ...prev,
          thumbnail: acceptedFiles[0],
        }));
        setDisableThumbnail(true);
        const newFiles = acceptedFiles.map((acceptedFile) => acceptedFile.name);

        setFiles([...files, ...newFiles]);
      }
    },
    [files],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: { 'image/*': ['.jpeg', '.png', '.jpg'] },
    onDrop,
  });

  const handleInputChange = (prop: string, e: any) => {
    const val = e.target.value;
    setFeedData((prev) => ({
      ...prev,
      [prop]: val,
    }));
  };

  const handleSelectChange = (prop: string, val: number) => {
    setFeedData((prev) => ({
      ...prev,
      [prop]: val,
    }));
  };

  const handleSubmit = () => {
    if (feeddata) {
      const filteredData = Object.fromEntries(
        Object.entries(feeddata).filter(([key, value]) => value !== null),
      );
      const formData = new FormData();

      for (const [key, value] of Object.entries(filteredData)) {
        if (key === 'tags' && Array.isArray(value)) {
          value.forEach((tagId) => {
            formData.append('tags', tagId);
          });
        } else {
          formData.append(key, value);
        }
      }

      updateVideo(formData);
    }
  };

  const { mutate: updateVideo, isLoading: isCreating } = useMutation(
    async (uploadData: any) => {
      const response = await axios.put(
        `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds/${videoData.id}`,
        uploadData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: localStorage.getItem('api-token'),
            'client-type': 'web_admin',
          },
        },
      );
      return response;
    },
    {
      onError: (err) => {
        close();
        addToast('error', 'Error occured in updating a video');
      },
      onSuccess: (data) => {
        close();
        queryClient.refetchQueries(['get-specific-feeds']);
        setTimeout(() => {
          addToast('success', 'Video updated successfully');
        }, 50);
      },
    },
  );

  useEffect(() => {
    videoUrl && console.log('Video URL', videoUrl);
  }, [videoUrl]);

  return (
    <>
      <Card
        bg="white"
        style={{ margin: baseTheme.space.md, padding: baseTheme.space.md }}
      >
        <Row mb="lg">
          <Col>
            <XXL isBold>
              <Ellipsis style={{ width: '90%' }}>{'Video Details'}</Ellipsis>
            </XXL>
          </Col>
        </Row>
        <Row>
          <Col size={2.5}>
            <Row>
              <Col>
                <Field>
                  <Image
                    src={
                      feeddata?.thumbnail
                        ? URL.createObjectURL(feeddata.thumbnail)
                        : videoData.thumbnail_url
                    }
                  />
                </Field>
              </Col>
            </Row>
            <Row>
              <Col>
                <Field>
                  <Label>Edit Thumbnail</Label>
                  <StyledFileUpload
                    {...getRootProps()}
                    isDragging={isDragActive}
                    disabled={disableThumbnail}
                  >
                    {isDragActive ? (
                      <span>Drop files here</span>
                    ) : (
                      <span>Choose a file or drag and drop here</span>
                    )}
                    <Input {...getInputProps()} />
                  </StyledFileUpload>
                  {files.length === 0 ? (
                    <Message>Acceptable formats are JPG, PNG, and JPEG</Message>
                  ) : (
                    <FileList>
                      {files.map((file, index) => (
                        <FileItem
                          key={file}
                          name={file}
                          onRemove={() => removeFile(index)}
                        />
                      ))}
                    </FileList>
                  )}
                </Field>
                {errors.thumbnail != '' && (
                  <Message validation="error">{errors.thumbnail}</Message>
                )}
              </Col>
            </Row>
          </Col>
          <Col size={6}>
            <Field>
              <Label>Title *</Label>
              <Textarea
                rows={3}
                style={{ fontSize: baseTheme.fontSizes.md }}
                value={feeddata.title}
                onChange={(e) => {
                  handleInputChange('title', e);
                }}
              />
            </Field>
            <Field>
              <Label>Description *</Label>
              <Textarea
                rows={3}
                value={feeddata.caption}
                onChange={(e) => {
                  handleInputChange('caption', e);
                }}
              />
            </Field>
            {!isTagsLoading && preSelectedTags && tagsData ? (
              <>
                <TagsMultiDropdown
                  selectedTags={preSelectedTags}
                  setSelectedTags={setSelectedTags}
                  tagsData={tagsData}
                />
              </>
            ) : (
              <></>
            )}
          </Col>
          <Col size={3.5}>
            <Card bg="grey">
              <Row>
                <Col style={{ justifyContent: 'center' }}>
                  <Field style={{ width: '100%', justifyContent: 'center' }}>
                    {videoUrl && (
                      <>
                        <Video
                          key={videoUrl}
                          controls
                          poster={videoData.thumbnail_url}
                        >
                          <source src={videoUrl} type="video/mp4" />
                          Your browser does not support the video tag.
                        </Video>
                      </>
                    )}
                  </Field>
                </Col>
              </Row>
              <div style={{ margin: baseTheme.space.sm }}>
                <Row justifyContent="center">
                  <Col>
                    <Field>
                      <Label>Video link</Label>
                      <MediaInput
                        value={videoData.video_url}
                        ref={inputRef}
                        readOnly
                        end={
                          <LinkIcon
                            style={{ cursor: 'pointer' }}
                            onClick={copyToClipboard}
                          />
                        }
                      />
                    </Field>
                  </Col>
                </Row>
                <Row justifyContent="center">
                  <Col>
                    <Field>
                      <Label>Filename</Label>
                      <XMD isBold>
                        <Ellipsis style={{ width: '95%' }}>
                          {cleanVideoFilename(videoData.video_url)}
                        </Ellipsis>
                      </XMD>
                    </Field>
                  </Col>
                </Row>
              </div>
            </Card>
          </Col>
        </Row>
        <Row mt="md" justifyContent="end">
          <Col offset={10.5}>
            <Button
              onClick={() => {
                handleSubmit();
              }}
              isStretched
              isPrimary
            >
              <Button.StartIcon>
                <SaveIcon />
              </Button.StartIcon>
              Save
            </Button>
          </Col>
        </Row>
      </Card>
    </>
  );
};

export default EditFeeds;
