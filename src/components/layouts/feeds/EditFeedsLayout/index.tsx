import { Button } from '@zendeskgarden/react-buttons';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../../themes/theme';
import { mediaQuery } from '@zendeskgarden/react-theming';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Row } from '../../../UI-components/Grid';
import { Col } from '../../../UI-components/Grid';
import { pageRoutes } from '../../../navigation/RouteConfig';
import EditFeeds from './EditFeeds';
import VideoAnalytics from './VideoAnalytics';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxiosFeeds';
import useToast from '../../../../hooks/useToast';
import EditFeaturedProducts from './EditFeaturedProducts';
import krakendPaths from '../../../../constants/krakendPaths';
import { useAuth } from '../../../providers/AuthProvider';

const Tab = styled(Button)<{ isTab: boolean }>`
  border-top: 4px solid ${(p) => (p.isTab ? p.theme.colors.primaryHue : 'none')};
  padding: ${baseTheme.space.md};
  background-color: ${(p) => (p.isTab ? 'white' : colors.tabGrey)};
  ${(p) => mediaQuery('down', 'md', p.theme)} {
    padding: ${baseTheme.space.sm};
    font-size: ${baseTheme.fontSizes.xs};
  }
  color: ${(p) => (p.isTab ? baseTheme.colors.deepBlue : colors.heavyGrey)};
  border-radius: 0;
  font-size: 15px;
  font-weight: 600;
  height: 50px;
  padding: 15px 20px;
`;

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
  background-color: white;
  margin: 16px;
`;

export default ({ menu }: { menu: 'edit' | 'analytics' | 'featured' }) => {
  const { setHeaderInformation } = useAuth();
  const axios = useAxios();
  const addToast = useToast();
  useEffect(() => {
    setHeaderInformation({
      title: 'Edit Feeds',
      breadcrumbParent: '',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { feedId } = useParams();

  const navigate = useNavigate();

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['get-specific-feeds', feedId],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds/${feedId}`,
        );

        return response;
      },
    });

  return (
    <>
      <Container>
        <Row>
          <Col>
            <Row>
              <Tab
                onClick={() => {
                  navigate(`${pageRoutes['GO_TO_EDIT_FEED']}/${feedId}`);
                }}
                isTab={menu === 'edit'}
                isNeutral={menu !== 'edit'}
                isBasic
              >
                Edit Video
              </Tab>
              <Tab
                onClick={() => {
                  navigate(
                    `${pageRoutes['GO_TO_FEEDS_VIDEO_ANALYTICS']}/${feedId}`,
                  );
                }}
                isTab={menu === 'analytics'}
                isNeutral={menu !== 'analytics'}
                isBasic
              >
                Video Analytics
              </Tab>
              <Tab
                onClick={() => {
                  navigate(
                    `${pageRoutes['GO_TO_FEEDS_FEATURED_PRODUCTS']}/${feedId}`,
                  );
                }}
                isTab={menu === 'featured'}
                isNeutral={menu !== 'featured'}
                isBasic
              >
                Featured Products
              </Tab>
            </Row>
          </Col>
        </Row>
        {menu === 'edit' && !isLoading && data?.data && (
          <EditFeeds videoData={data?.data} />
        )}
        {menu === 'analytics' && !isLoading && data?.data && (
          <VideoAnalytics key={data?.data} videoData={data?.data} />
        )}
        {menu === 'featured' && !isLoading && data?.data && (
          <EditFeaturedProducts videoData={data?.data} />
        )}
      </Container>
    </>
  );
};
