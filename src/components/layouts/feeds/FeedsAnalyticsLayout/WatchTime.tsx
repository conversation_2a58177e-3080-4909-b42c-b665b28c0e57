import React, { useEffect, useState } from 'react';
import { Row, Col } from '../../../UI-components/Grid';
import DatePickerModal, {
  ITimeLog,
} from '../../../modal/feeds/videos/TooltipModal/DatePickerModal';
import { MD } from '../../../UI-components/Typography';
import { baseTheme } from '../../../../themes/theme';
import { IWatchTime } from '../EditFeedsLayout/VideoAnalytics';
import WatchTimeLineChart from '../../../charts/Feeds/Analytics/WatchTimeChart';
import NothingToshow from '../../../UI-components/NothingToShow';
import {
  useFeedsWatchTimePerDayHistory,
  useFeedsWatchTimesHistory,
} from '../../../../hooks/useQuery';
import { Skeleton } from '@zendeskgarden/react-loaders';
import { format, subDays } from 'date-fns';

const WatchTime = ({
  isLoading,
  timelog,
}: {
  isLoading: boolean;
  timelog: ITimeLog | undefined;
}) => {
  const [watchTimePerDayFilters, setWatchTimePerDayFilters] = useState<any>();

  const [daysValue, setDaysValue] = useState<number | undefined>(28);

  useEffect(() => {
    if (timelog) {
      setWatchTimePerDayFilters({
        days: timelog.range,
        startDate: timelog.startDate,
        endDate: timelog.endDate,
      });
      setDaysValue(undefined);
      setDaysValue(timelog.range);
    }
  }, [timelog]);

  const { data: feedsWatchTimePerDayHistoryData, refetch } =
    useFeedsWatchTimesHistory({
      watchCountFilters: watchTimePerDayFilters,
    });

  useEffect(() => {
    if (watchTimePerDayFilters) {
      refetch();
    }
  }, [watchTimePerDayFilters]);

  return (
    <>
      <Row>
        <Col>
          <Row alignItems="center" mt="md">
            <Col size={4} offset={0.5}>
              <MD isBold>Watch Time History</MD>
            </Col>
            <Col offset={5} size={2}>
              {/* <DatePickerModal
                preSelectedTimeLog={timelog}
                setTimeLog={setWatchTimelog}
              /> */}
            </Col>
          </Row>
          <div style={{ padding: baseTheme.space.md }}>
            {isLoading ? (
              <>
                <Skeleton
                  style={{
                    width: '100%',
                    height: baseTheme.components.dimension.width.base350,
                  }}
                />
              </>
            ) : (
              <>
                {feedsWatchTimePerDayHistoryData?.data &&
                feedsWatchTimePerDayHistoryData?.data.length != 0 &&
                daysValue &&
                watchTimePerDayFilters ? (
                  <WatchTimeLineChart
                    days={daysValue}
                    watchTimeData={feedsWatchTimePerDayHistoryData.data}
                  />
                ) : (
                  <>
                    <Row justifyContent="center">
                      <NothingToshow divHeight={'40vh'} />
                    </Row>
                  </>
                )}
              </>
            )}
          </div>
        </Col>
      </Row>
    </>
  );
};

export default WatchTime;
