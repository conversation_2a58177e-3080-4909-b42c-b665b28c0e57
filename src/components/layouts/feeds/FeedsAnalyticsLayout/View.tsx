import React, { useEffect, useState } from 'react';
import { Row, Col } from '../../../UI-components/Grid';
import { MD, XL } from '../../../UI-components/Typography';
import DatePickerModal, {
  ITimeLog,
} from '../../../modal/feeds/videos/TooltipModal/DatePickerModal';
import { baseTheme } from '../../../../themes/theme';
import WatchCountLineChart from '../../../charts/Feeds/Analytics/WatchCountChart';
import NothingToshow from '../../../UI-components/NothingToShow';
import { IWatchCount } from '../EditFeedsLayout/VideoAnalytics';
import {
  useFeedsWatchCountPerDayHistory,
  useFeedsWatchCountsHistory,
} from '../../../../hooks/useQuery';
import { Skeleton } from '@zendeskgarden/react-loaders';
import { format, subDays } from 'date-fns';

const View = ({
  timelog,
  isLoading,
}: {
  timelog: ITimeLog | undefined;
  isLoading: boolean;
}) => {
  const [watchCountPerDayFilters, setWatchCountPerDayFilters] = useState<any>();

  useEffect(() => {
    if (timelog) {
      setWatchCountPerDayFilters({
        days: timelog.range,
        startDate: timelog.startDate,
        endDate: timelog.endDate,
      });
    }
  }, [timelog]);

  const { data: feedsWatchCountPerDayHistoryData, refetch } =
    useFeedsWatchCountsHistory({
      watchCountFilters: watchCountPerDayFilters,
    });

  useEffect(() => {
    if (watchCountPerDayFilters) {
      refetch();
    }
  }, [watchCountPerDayFilters]);

  return (
    <>
      <Row>
        <Col>
          <Row alignItems="center" mt="md">
            <Col size={4} offset={0.5}>
              <XL isBold>Watch Count History</XL>
            </Col>
            <Col offset={5} size={2}>
              {/* <DatePickerModal
                preSelectedTimeLog={timelog}
                setTimeLog={setWatchcountlog}
              /> */}
            </Col>
          </Row>
          <Row>
            <Col>
              <div style={{ padding: baseTheme.space.md }}>
                {isLoading ? (
                  <>
                    <Skeleton
                      style={{
                        width: '100%',
                        height: baseTheme.components.dimension.width.base350,
                      }}
                    />
                  </>
                ) : (
                  <>
                    {feedsWatchCountPerDayHistoryData?.data &&
                    feedsWatchCountPerDayHistoryData?.data.length != 0 &&
                    watchCountPerDayFilters ? (
                      <WatchCountLineChart
                        days={watchCountPerDayFilters.days}
                        watchCountData={feedsWatchCountPerDayHistoryData?.data}
                      />
                    ) : (
                      <>
                        <Row justifyContent="center">
                          <NothingToshow divHeight={'40vh'} />
                        </Row>
                      </>
                    )}
                  </>
                )}
              </div>
            </Col>
          </Row>
        </Col>
      </Row>
    </>
  );
};

export default View;
