import React, { useState } from 'react';
import { Col, Row } from '../../../UI-components/Grid';
import EngagementChart from '../../../charts/Feeds/Analytics/EngagementChart';
import DatePickerModal, {
  ITimeLog,
} from '../../../modal/feeds/videos/TooltipModal/DatePickerModal';
import { XL } from '../../../UI-components/Typography';
import { IStats } from '../../../../types/types';
import {
  useEngagementPerDayHistory,
  useFeedsWatchCountPerDayHistory,
} from '../../../../hooks/useQuery';
import NothingToshow from '../../../UI-components/NothingToShow';
import { Skeleton } from '@zendeskgarden/react-loaders';
import { baseTheme } from '../../../../themes/theme';

const Engagement = ({ data, isLoading }: { data: any; isLoading: boolean }) => {
  //   const demoData: I = {
  //     stats: {
  //       cheapest: {
  //         count: 150,
  //         percentage: 15,
  //       },
  //       average: {
  //         count: 300,
  //         percentage: 30,
  //       },
  //       costlier: {
  //         count: 200,
  //         percentage: 20,
  //       },
  //       cheaper: {
  //         count: 100,
  //         percentage: 10,
  //       },
  //       costliest: {
  //         count: 250,
  //         percentage: 25,
  //       },
  //     },
  //     totalCount: 1000,
  //   };

  return (
    <Row>
      <Col>
        <Row alignItems="center" mt="md">
          <Col size={4} offset={0.5}>
            <XL isBold>Engagement</XL>
          </Col>
          {/* <Col offset={5} size={2}>
            <DatePickerModal setTimeLog={setWatchcountlog} />
          </Col> */}
        </Row>
        <Row>
          <Col>
            {isLoading ? (
              <>
                <Row justifyContent="center">
                  <Col size={10}>
                    <Skeleton
                      style={{
                        marginTop: baseTheme.space.lg,
                        width: '100%',
                        height: baseTheme.components.dimension.width.base350,
                        marginBottom: baseTheme.space.lg,
                      }}
                    />
                  </Col>
                </Row>
              </>
            ) : (
              <>
                {data && data?.data && data?.data.length != 0 ? (
                  <>
                    <EngagementChart
                      data={data?.data}
                      isLoading={isLoading}
                      inReports
                    />
                  </>
                ) : (
                  <>
                    <Row justifyContent="center">
                      <NothingToshow divHeight={'40vh'} />
                    </Row>
                  </>
                )}
              </>
            )}
          </Col>
        </Row>
      </Col>
    </Row>
  );
};

export default Engagement;
