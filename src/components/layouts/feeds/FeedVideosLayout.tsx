import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import useAxiosFeed from '../../../hooks/useAxiosFeeds';
import FEEDS_URL from '../../../constants/index';
import FeedVideosTable from '../../table/feeds/FeedVideosTable';
import { useFeedVideoContext } from '../../../pages/feeds/FeedVideoContext';
import useToast from '../../../hooks/useToast';
import FeedVideoFilter from '../../drawer/feeds/FeedVideoFilter';
import constants from '../../../constants/index';
import routes from '../../../constants/routes';
import { useNavigate } from 'react-router-dom';
import krakendPaths from '../../../constants/krakendPaths';

const LOCAL_STORAGE_KEY = 'feedVideoFilters';

const FeedVideosLayout = () => {
  const axios = useAxiosFeed();
  const { results, setResults, setFilters, filters } = useFeedVideoContext();
  const [count, setCount] = useState<number>(0);

  const [searchContent, setSearchContent] = useState<string>();
  const addToast = useToast();
  // const [filters, setFilters] = useState<any>({
  //   page: 1,
  // });

  const saveFiltersToLocalStorage = (filtersToSave: any) => {
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(filtersToSave));
  };

  const loadFiltersFromLocalStorage = () => {
    const savedFilters = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedFilters) {
      return JSON.parse(savedFilters);
    }
    return {
      page: 1,
      limit: 10,
    };
  };

  // const [filters, setFilters] = useState<any>(loadFiltersFromLocalStorage());

  const [filterActive, setFilterActive] = useState<boolean>(false);

  const [isOpen, setIsOpen] = useState(false);

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['get-feeds'],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds`,
          {
            params: {
              ...filters,
            },
          },
        );

        return response;
      },
      onError: (err) => {
        addToast('error', 'Error encountered !!');
      },
      onSuccess: (data) => {
        setResults(data.data.rows);
        setCount(data.data.count);
      },
    });

  const { data: categories } = useQuery({
    queryKey: ['get-feeds-categories'],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(
        `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds/categs`,
        {
          params: {
            ...filters,
          },
        },
      );

      return response;
    },
  });

  useEffect(() => {
    saveFiltersToLocalStorage(filters);
    refetch();
    if (filters.tags || filters.sort) {
      setFilterActive(true);
    }
  }, [filters]);

  const reset = () => {
    setFilters({
      page: 1,
      limit: 10,
    });
  };

  //Back Functionality
  const navigate = useNavigate();

  useEffect(() => {
    const handlePopState = (event: any) => {
      const currentFilter = loadFiltersFromLocalStorage();
      if (currentFilter.tags || currentFilter.sort) {
        const filterLeast = {
          page: 1,
        };
        saveFiltersToLocalStorage(filterLeast);
        setFilters(filterLeast);
        setFilterActive(false);

        event.preventDefault();

        navigate(`/${constants.PREFIX}/${routes.FEEDS}/videos`);
      }
    };

    window.addEventListener('popstate', handlePopState);

    const currentFilter = loadFiltersFromLocalStorage();

    if (!(currentFilter.tags || currentFilter.sort)) {
      return () => {
        window.removeEventListener('popstate', handlePopState);
      };
    }
  }, [filterActive]);

  //

  return (
    <>
      <FeedVideosTable
        filters={filters}
        setFilters={setFilters}
        setSearchContent={setSearchContent}
        searchContent={searchContent}
        count={count}
        setIsOpen={setIsOpen}
        refetch={refetch}
        categories={categories}
      />
      <FeedVideoFilter
        filters={filters}
        isOpen={isOpen}
        reset={reset}
        setFilters={setFilters}
        setIsOpen={setIsOpen}
      />
    </>
  );
};

export default FeedVideosLayout;
