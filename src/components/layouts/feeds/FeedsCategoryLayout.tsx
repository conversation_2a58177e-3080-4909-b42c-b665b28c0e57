import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxiosFeeds';
import React, { useEffect, useState } from 'react';
import FEEDS_URL from '../../../constants/index';
import FeedVideosTable from '../../table/feeds/FeedVideosTable';
import { useFeedVideoContext } from '../../../pages/feeds/FeedVideoContext';
import useToast from '../../../hooks/useToast';
import FeedVideoFilter from '../../drawer/feeds/FeedVideoFilter';
import FeedsCategoryTable from '../../table/feeds/FeedsCategoryTable';
import { useFeedCategoryContext } from '../../../pages/feeds/FeedCategoryContext';
import { useNavigate, useLocation } from 'react-router-dom';
import krakendPaths from '../../../constants/krakendPaths';

const FeedsCategoryLayout = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const axios = useAxios();
  const { results, setResults } = useFeedCategoryContext();
  const [searchContent, setSearchContent] = useState<string>();
  const [count, setCount] = useState<number>(0);
  const addToast = useToast();
  const [filters, setFilters] = useState<any>({
    page:
      Number(queryParams.get('page')) > 0 ? Number(queryParams.get('page')) : 1,
  });

  const [isOpen, setIsOpen] = useState(false);

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['get-feeds-categories'],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          `${krakendPaths.FEEDS_URL}/admin-api/v1/feeds/categs`,
          {
            params: {
              ...filters,
            },
          },
        );

        return response;
      },
      onError: (err) => {
        addToast('error', 'Error encountered !!');
      },
      onSuccess: (data) => {
        setResults(data.data.rows.reverse());
        setCount(data.data.count);
      },
    });

  useEffect(() => {
    queryParams.set('page', filters.page > 0 ? filters.page.toString() : '1');
    navigate({ search: queryParams.toString() });
  }, []);

  useEffect(() => {
    if (Number(queryParams.get('page')) !== filters.page) {
      queryParams.set('page', filters.page.toString());
      navigate({ search: queryParams.toString() });
    }

    refetch();
  }, [filters]);

  useEffect(() => {
    if (
      Number(queryParams.get('page')) !== filters.page &&
      Number(queryParams.get('page')) !== 0
    ) {
      setFilters((prev: any) => ({
        ...prev,
        page: Number(queryParams.get('page')),
      }));
    } else if (Number(queryParams.get('page')) === 0) {
      setFilters((prev: any) => ({ ...prev, page: 1 }));
    }
  }, [queryParams.get('page')]);

  const reset = () => {
    setFilters({
      page: 1,
    });
  };

  return (
    <>
      <FeedsCategoryTable
        filters={filters}
        setFilters={setFilters}
        setSearchContent={setSearchContent}
        searchContent={searchContent}
        count={count}
      />
    </>
  );
};

export default FeedsCategoryLayout;
