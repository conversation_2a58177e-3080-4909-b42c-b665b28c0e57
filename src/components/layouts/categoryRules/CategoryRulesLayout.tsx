import { RulesOutput } from '../../../gql/graphql';
import CategoryRulesTable from '../../table/categoryRules/CategoryRulesTable';
import { useState } from 'react';

const CategoryRulesLayout = ({
  rows,
  count,
  refetch,
  filters,
  setFilters,
}: {
  rows: RulesOutput[];
  count: number;
  refetch: any;
  filters: any;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {
  console.log(rows);
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );

  return (
    <>
      <CategoryRulesTable
        data={rows}
        count={count}
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        refetch={refetch}
        filters={filters}
        setFilters={setFilters}
      />
    </>
  );
};

export default CategoryRulesLayout;
