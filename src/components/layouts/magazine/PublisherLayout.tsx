import { useState } from 'react';
import {
  Body,
  Cell,
  Head,
  HeaderCell,
  HeaderRow,
  Row,
  Table,
  TableContainer,
} from '../../UI-components/Table';
import { Toggle } from '@zendeskgarden/react-forms';
import { Label } from '../../UI-components/Label';
import styled from 'styled-components';
import { Col, Row as MRow } from '../../UI-components/Grid';
import { Field } from '../../UI-components/Field';
import Input from '../..//UI-components/Input';
import { Button } from '../../UI-components/Button';
import { SaveIcon } from '../../../utils/icons';
import { Message } from '@zendeskgarden/react-forms';
import useAxios from '../../../hooks/useAxios';
import krakendPaths from '../../../constants/krakendPaths';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';

export interface PublisherOutput {
  _id: string;
  publisher: string;
  enable: boolean;
}

const TableCellDiv = styled.div`
  height: 50px;
  display: flex;
  align-items: center;
`;

const PublisherLayout = () => {
  const [validateionError, setValidationError] = useState('');
  const axios = useAxios();
  const addToast = useToast();
  const [newPublisher, setNewPublisher] = useState({
    publisher: '',
    enable: true,
  });

  // const { data } = useQuery(GET_PUBLISHERS, {
  //     fetchPolicy: "network-only",
  // });

  const { data } = useQuery({
    queryKey: ['get-publishers-data'],
    queryFn: async () => {
      const response: any = await axios.get(
        `${krakendPaths.MAGAZINE_URL}/admin-api/v1/publishers`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response?.publishers;
    },
    onError: (err: any) => {
      addToast('error', `${err.message}`);
    },
  });

  // const [createPublisher] = useMutation(ADD_NEW_PUBLISHER, {
  //   refetchQueries: [
  //     {
  //       query: GET_PUBLISHERS,
  //     },
  //   ],
  //   onCompleted: () => {
  //     setNewPublisher({ publisher: '', enable: true });
  //     setValidationError('');
  //   },
  // });
  const queryClient = useQueryClient();
  const { mutate: createPublisher, isLoading: mutationLoading } = useMutation(
    async (obj: any) => {
      const response = await axios.post(
        `${krakendPaths.MAGAZINE_URL}/admin-api/v1/publishers`,
        obj,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err.message}`);
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['get-publishers-data'] });
        setNewPublisher({ ...newPublisher, publisher: '' });
        // addToast('success', 'Magazine updated successfully.');
        // setVisible(false);
      },
    },
  );

  // const [editPublisher] = useMutation(UPDATE_PUBLISHER, {
  //   refetchQueries: [
  //     {
  //       query: GET_PUBLISHERS,
  //     },
  //   ],
  // });

  const { mutate: editPublisher, isLoading: editPublisherLoading } =
    useMutation(
      async (obj: any) => {
        const response = await axios.put(
          `${krakendPaths.MAGAZINE_URL}/admin-api/v1/publishers`,
          obj,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            },
          },
        );

        return response.data;
      },
      {
        onError: (err: any) => {
          addToast('error', `${err.message}`);
        },
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['get-publishers-data'] });
          // addToast('success', 'Magazine updated successfully.');
          // setVisible(false);
        },
      },
    );

  const handleChangeSwitch = async (event: any, id: string) => {
    editPublisher({ _id: id, enable: event.target.checked });
  };

  const handleChange = (event: any) => {
    setNewPublisher((prev) => ({ ...prev, publisher: event.target.value }));
    setValidationError('');
  };

  const validate = () => {
    if (!newPublisher.publisher) {
      setValidationError('Enter Value to save');
      return false;
    }
    return true;
  };

  const saveAction = async () => {
    if (validate()) {
      createPublisher({ ...newPublisher });
    }
  };

  if (data) {
    return (
      <>
        <MRow>
          <Col size={2}>
            <Field>
              <Input
                placeholder="Enter Publisher"
                value={newPublisher.publisher}
                onChange={handleChange}
              />
            </Field>
            {!!validateionError && (
              <Message validation="warning">{validateionError}</Message>
            )}
          </Col>
          <Col size={1}>
            <Button isOrange isPrimary onClick={saveAction}>
              <Button.StartIcon>
                <SaveIcon />
              </Button.StartIcon>
              Save
            </Button>
          </Col>
        </MRow>
        <MRow mt={'md'}>
          <Col size={12}>
            <TableContainer>
              <Table>
                <Head>
                  <HeaderRow>
                    <HeaderCell>
                      <TableCellDiv>Publisher</TableCellDiv>
                    </HeaderCell>
                    <HeaderCell>
                      <TableCellDiv>Enable</TableCellDiv>
                    </HeaderCell>
                  </HeaderRow>
                </Head>
                <Body>
                  {data?.map((item: PublisherOutput) => (
                    <Row>
                      <Cell>
                        <TableCellDiv>{item.publisher}</TableCellDiv>
                      </Cell>
                      <Cell>
                        <TableCellDiv>
                          <Field>
                            <Toggle
                              checked={item?.enable}
                              onChange={(e) => handleChangeSwitch(e, item._id)}
                            >
                              <Label hidden>Show Toggle</Label>
                            </Toggle>
                          </Field>
                        </TableCellDiv>
                      </Cell>
                    </Row>
                  ))}
                </Body>
              </Table>
            </TableContainer>
          </Col>
        </MRow>
      </>
    );
  }
  return <div>loading</div>;
};

export default PublisherLayout;
