import { useState } from 'react';
import MagazineTable from '../../table/magazine/MagazineTable';
import { MagazineOutput } from '../../../gql/graphql';

const MagazineLayout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
    searchContent,
    setSearchContent,
}: {
    rows: MagazineOutput[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
    searchContent: string | undefined;
    setSearchContent: React.Dispatch<React.SetStateAction<string | undefined>>;
}) => {

    
    // Add more objects as needed

    return (
        <>
            <MagazineTable
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default MagazineLayout;
