import { useState } from 'react';
import CustomerCareTable from '../../table/customer-care/CustomerCareTable';

const CustomerCareLayout = ({
  rows,
  count,
  
  refetch,
  filters,
  setFilters,
  
  
}: {
  rows: any;
  count: number;
  refetch: any;
  filters: any;
  
  setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );

  return (
    <>
      <CustomerCareTable
        data={rows}
        count={count}
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        refetch={refetch}
        filters={filters}
        setFilters={setFilters} 
        handleSearch={function (): void {
          throw new Error('Function not implemented.');
        } }      
      />
    </>
  );
};

export default CustomerCareLayout;
