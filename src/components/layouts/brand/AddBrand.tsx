import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../themes/theme';
import { Col, Row as _Row } from '../../UI-components/Grid';
import Input from '../../UI-components/Input';
import { Label } from '../../UI-components/Label';
import { Button, Buttons } from '../../UI-components/Button';
import { useLocation, useNavigate } from 'react-router-dom';
import { pageRoutes } from '../../navigation/RouteConfig';
import { Toggle } from '../../UI-components/Toggle';
import { LeftArrowIcon } from '../../../utils/icons';
import { useAuth } from '../../providers/AuthProvider';

const Main = styled.div`
  background-color: ${baseTheme.colors.white};
  height: 100vh;
`;

const Row = styled(_Row)`
    margin: ${baseTheme.components.dimension.width.base*2}px ${baseTheme.components.dimension.width.base*2}px;
`


const TopBarDiv = styled.div`
  background-color: ${colors.white};
  padding-bottom: ${(p) => p.theme.space.md};
  padding-left: ${(p) => p.theme.space.md};
  padding-right: ${(p) => p.theme.space.md};
`;

interface Brand {
  brand_id: number;
  brand_name: string;
  category_id: number;
  created_at: string;
  id: number;
  is_active: boolean;
  is_dk_suggest: boolean;
  is_featured: boolean;
  is_international: boolean;
  is_newly_added: boolean;
  is_top: boolean;
  logo: string;
  sort_order: number;
  updated_at: string;
  url: string;
}

const AddBrand = () => {
    const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const { setHeaderInformation } = useAuth();

  const [prodId, setProdId] = useState<number | undefined>(undefined);

  const [brand, setBrand] = useState<Brand>({
    brand_id: 0,
    brand_name: '',
    category_id: 0,
    created_at: '',
    id: 0,
    is_active: false,
    is_dk_suggest: false,
    is_featured: false,
    is_international: false,
    is_newly_added: false,
    is_top: false,
    logo: '',
    sort_order: 0,
    updated_at: '',
    url: ''
  });

  useEffect(() => {
    setHeaderInformation({
      title: 'Add Brand',
      breadcrumbParent: 'Brand',
    });
  }, [setHeaderInformation]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBrand((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleToggleChange = (field: keyof Brand) => {
    setBrand((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  useEffect(()=>{
    if(queryParams.get('id')){
      const val = queryParams.get('id');
      setProdId(Number(val));
    }
  },[queryParams.get('id')])

  useEffect(() => {
    if (prodId !== undefined) {
      navigate(`${pageRoutes['GO_TO_BRAND_DETAIL']}id=${prodId}`);
    } else {
      navigate(`${pageRoutes['GO_TO_ADD_BRAND']}`);
    }
  }, [prodId, navigate]);

  useEffect(()=>{
    if(prodId){
        queryParams.set('id', prodId.toString());
    }
  },[])




  return (
    <Main>
        <TopBarDiv>
            <div style={{display: 'flex'}}>
                <Col style={{display:'flex', justifyContent:'end'}}>
                <Buttons
                  style={{ margin: '0px 10px' }}
                  onClick={() => {
                    navigate(`${pageRoutes['GO_TO_BRAND']}`);
                    setProdId(undefined);
                  }}
                  isAction
                >
                  <Buttons.StartIcon>
                    <LeftArrowIcon />
                  </Buttons.StartIcon>
                  Back
                </Buttons>
                    <Button  isPrimary>
                        Save
                    </Button>
                </Col>
            </div>
        </TopBarDiv>
      <div>
      <Row>
          <Col size={3}>Is Active</Col>
          <Col size={4}>
            <Toggle 
              checked={brand.is_active}
              onChange={() => {
                const val: boolean = !brand.is_active;
                setBrand((prev)=>({
                    ...prev,
                    is_active: val
                }))
              }}
              
            >
              <Label hidden>Is Active</Label>
            </Toggle>
          </Col>
        </Row>
        <Row>
          <Col size={3}>Brand Id</Col>
          <Col size={4}>
            <Input
              name="brand_id"
              value={brand.brand_id}
              onChange={handleInputChange}
              type="number"
            />
          </Col>
        </Row>
        <Row>
          <Col size={3}>Category Id</Col>
          <Col size={4}>
            <Input
              name="category_id"
              value={brand.category_id}
              onChange={handleInputChange}
              type="number"
            />
          </Col>
        </Row>
        <Row>
          <Col size={3}>Name</Col>
          <Col size={4}>
            <Input
              name="brand_name"
              value={brand.brand_name}
              onChange={handleInputChange}
              type="text"
            />
          </Col>
        </Row>
        
        <Row>
          <Col size={3}>URL</Col>
          <Col size={4}>
            <Input
              name="url"
              value={brand.url}
              onChange={handleInputChange}
              type="text"
            />
          </Col>
        </Row>
        <Row>
          <Col size={3}>Logo</Col>
          <Col size={4}>
            <Input
              name="logo"
              value={brand.logo}
              onChange={handleInputChange}
              type="text"
            />
          </Col>
        </Row>
        <Row>
          <Col size={3}>Featured</Col>
          <Col size={4}>
            <Toggle
              checked={brand.is_featured}
              onChange={() => handleToggleChange('is_featured')}
            >
              <Label hidden>Featured</Label>
            </Toggle>
          </Col>
        </Row>
        <Row>
          <Col size={3}>Is International</Col>
          <Col size={4}>
            <Toggle
              checked={brand.is_international}
              onChange={() => handleToggleChange('is_international')}
            >
              <Label hidden>Is International</Label>
            </Toggle>
          </Col>
        </Row>
        <Row>
          <Col size={3}>Is Newly Added</Col>
          <Col size={4}>
            <Toggle
              checked={brand.is_newly_added}
              onChange={() => handleToggleChange('is_newly_added')}
            >
              <Label hidden>Is Newly Added</Label>
            </Toggle>
          </Col>
        </Row>
      </div>
    </Main>
  );
};

export default AddBrand;
