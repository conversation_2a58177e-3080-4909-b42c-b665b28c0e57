import React, { useState } from 'react';
import { Col, Row } from '../../UI-components/Grid';
import { Label } from '../../../pages/HomeSections/SiteMetrics';
import Input from '../../UI-components/Input';
import { Button } from '../../UI-components/Button';
import { MD } from '@zendeskgarden/react-typography';
import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import useToast from '../../../hooks/useToast';
import krakendPaths from '../../../constants/krakendPaths';
import { SectionElement } from '../../../types/types';

export enum GridBanner {
  FIVE_BANNER = 'five_banner_grid_layout',
  THREE_BANNER = 'three_banner_grid_layout',
}

const MediaDimensionForm: React.FC<{
  sectionElement: SectionElement | undefined;
}> = ({ sectionElement }) => {
  const layoutTypeId = sectionElement?.section?.layout_type?.id;
  const layoutTypeCode = sectionElement?.section?.layout_type?.code;
  const elementPosition = sectionElement?.sort_order;
  const existingDimension =
    sectionElement?.section?.layout_type?.media_dimensions;
  const initialDimension = {
    web: { width: '', height: '' },
    tab: { width: '', height: '' },
    mobile: { width: '', height: '' },
    small: { width: '', height: '' },
  };
  console.log(sectionElement, 'SECTION ELEMENT');
  const [dimension, setDimension] = useState<any>(initialDimension);
  const addToast = useToast();

  const { mutate } = useMutation(
    async (body: any) => {
      return await axios.patch(
        `${krakendPaths.INTERFACE_URL}/admin-api/v1/layout-type/${layoutTypeId}/dimensions`,
        body,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
    },
    {
      onError: (err: any) => {
        console.log(err);
        addToast('error', err?.response?.data?.message);
      },
      onSuccess: () => {
        addToast('success', 'Dimensions saved successfully.');
      },
    },
  );

  const handleChange = (type: string, key: string, value: string) => {
    setDimension((prev: any) => ({
      ...prev,
      [type]: {
        ...prev[type],
        [key]: +value,
      },
    }));
  };

  const handleSubmit = () => {
    console.log(
      layoutTypeCode === GridBanner.FIVE_BANNER,
      layoutTypeCode === GridBanner.THREE_BANNER,
    );
    if (
      layoutTypeCode === GridBanner.FIVE_BANNER ||
      layoutTypeCode === GridBanner.THREE_BANNER
    ) {
      const obj = {
        [`${elementPosition}`]: dimension,
      };
      mutate(obj);
    } else {
      mutate(dimension);
    }
  };

  if (
    (layoutTypeCode === GridBanner.FIVE_BANNER ||
      layoutTypeCode === GridBanner.THREE_BANNER) &&
    existingDimension
  ) {
    if (existingDimension[`${elementPosition}`]) {
      return <></>;
    }
  } else if (existingDimension) {
    return <></>;
  }

  return (
    <>
      <Row mt="md" mb="md">
        <Col mt="md" lg={6}>
          <Label>Media Dimensions</Label>
          {['web', 'tab', 'mobile', 'small'].map((type) => (
            <Row alignItems="center" mt="md" key={type}>
              <Col lg={1}>
                <MD>{type.charAt(0).toUpperCase() + type.slice(1)}</MD>
              </Col>
              <Col lg={4}>
                <Input
                  type="number"
                  placeholder={`${
                    type.charAt(0).toUpperCase() + type.slice(1)
                  } Height (in px)`}
                  value={dimension[type]?.height || ''}
                  onChange={(e) => handleChange(type, 'height', e.target.value)}
                />
              </Col>
              <Col lg={4}>
                <Input
                  type="number"
                  placeholder={`${
                    type.charAt(0).toUpperCase() + type.slice(1)
                  } Width (in px)`}
                  value={dimension[type]?.width || ''}
                  onChange={(e) => handleChange(type, 'width', e.target.value)}
                />
              </Col>
            </Row>
          ))}
          <Col style={{ paddingLeft: '0px' }} mt="md">
            <Button onClick={handleSubmit} isPrimary>
              Save Dimension
            </Button>
          </Col>
        </Col>
      </Row>
    </>
  );
};

export default MediaDimensionForm;
