import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Mo<PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import useToast from '../../hooks/useToast';
import { SModal } from '../UI-components/Modal';
import { baseTheme } from '../../themes/theme';
import {
  GetMagentoOrdersDocument,
  SalesOrderStatusHistory,
} from '../../gql/graphql';
import LazyLoading from '../UI-components/LazyLoading';
import { OrderDetailsTableColumn } from '../table/orders/Columns';
import { OrderDetailsTable } from '../table/orders/OrderDetailsTable';
import { Col, Row } from '../UI-components/Grid';
import { XL, XXL, SM, LG as _LG } from '../UI-components/Typography';
import Textarea from '../UI-components/TextArea';
import { Label } from '../UI-components/Label';
import { Button } from '../UI-components/Button';
import styled from 'styled-components';
import { Message } from '@zendeskgarden/react-dropdowns';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useQuery as useApolloQuery } from '@apollo/client';
import useAxios from '../../hooks/useAxios';
import krakendPaths from '../../constants/krakendPaths';
import constants from '../../constants';
import { JoditEditorWrapper } from '../layouts/catalog-service/product/Description';
import JoditEditor, { Jodit } from 'jodit-react';

export const CloseButton = styled(Close)`
  color: white;
  background-color: rgb(37, 69, 97);

  &:hover {
    color: white;
    background-color: rgb(37, 75, 98);
  }
`;
const LG = styled(_LG)`
  font-size: 16px;
`;
const Div = styled.div`
  margin: 12px 0px;
`;
interface OrderSummary {
  subtotal: number;
  grandtotal: number;
  shippingCharge: number;
  discount: number;
  overweightDeliveryCharges: number;
  rewardDiscount: number;
}
// SalesOrderStatusHistory;
export const OrderModal = ({
  id,
  setVisible,
}: {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  id: string;
}) => {
  const [err, setErr] = useState<string>('');
  const editor = useRef<any>(null);
  const [comments, setComments] = useState<string>('');
  const [respons, setResponse] = useState<OrderDetailsTableColumn[]>();
  const [orderTotal, setOrderTotal] = useState<any>();
  const [notify, setNotify] = useState<boolean>(false);
  const [orderId, setOrderId] = useState<string>('');
  const [commentResponse, setCommentResponse] = useState<
    SalesOrderStatusHistory[]
  >([]);
  // console.log(commentResponse, 'commentResponse');
  const addToast = useToast();
  const config = useMemo(
    () => ({
      readonly: false,
      toolbarAdaptive: false,
      height: 300,
      uploader: {
        insertImageAsBase64URI: true,
      },
      placeholder: '',
      clipboard: {
        allowPasteHTML: true,
      },
      buttons: [
        'undo',
        'redo',
        'paragraph',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        '|',
        'ul',
        'ol',
        'outdent',
        'indent',
        '|',
        'font',
        'fontsize',
        '|',
        'link',
        'unlink',
        'image',
        'table',
        '|',
        'align',
        'hr',
        '|',
        'symbols',
      ],
      events: {
        afterInit: (editor: any) => {
          const statusbar = document.createElement('div');
          statusbar.style.backgroundColor = '#f8f8f8';
          statusbar.style.color = 'red';
          statusbar.style.fontSize = '11px';
          statusbar.style.padding = '1px 4px';

          function calcStat() {
            const text = editor.editor.innerText.trim();
            const wordCount = text
              .split(/[\s\n\r\t]+/)
              .filter((value:any) => value).length;
            const charCount = text.replace(/[\s\n\r\t]+/, '').length;

            statusbar.innerText = `Words: ${wordCount} Chars: ${charCount}`;
          }

          editor.events
            .on('change afterInit', editor.async.debounce(calcStat, 100))
            .on('afterInit', () => {
              editor.container.appendChild(statusbar);
            });
        },
      },
    }),
    [],
  );
  // const { loading: queryLoading, data: queryData } = useApolloQuery(
  //   GetMagentoOrdersDocument,
  //   {
  //     fetchPolicy: 'network-only',
  //     context: {
  //       headers: {
  //         'x-api-key': constants.API_KEY,
  //       },
  //     },
  //     variables: {
  //       order_id: id + 'BJABHA',
  //     },
  //   },
  // );

  const { data: queryData, isLoading: queryLoading } = useQuery({
    queryKey: ['get-magento-orders-by-id', id],
    queryFn: async () => {
      const response: any = await axios.get(
        `${krakendPaths.MAGENTO_URL}/admin-api/v1/orders`,
        {
          params: { order_id: id },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    onError: (err: any) => {
      addToast('error', `${err.message}`);
    },
  });

  // const [addOrderComment] = useMutation(ADD_ORDER_COMMENT, {
  //   onCompleted: (data) => {
  //     addToast('success', 'Done');
  //   },
  //   onError(error, clientOptions) {
  //     addToast('error', 'Error Occured');
  //   },
  // });

  const axios = useAxios();

  const { mutate: addOrderComment } = useMutation(
    async (obj: any) => {
      const response = await axios.post(
        `${krakendPaths.ORDER_COMMENTS_URL}/admin-api/v1/order-comments`,
        obj,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err.message}`);
      },
      onSuccess: () => {
        addToast('success', 'Comment added successfully');
      },
    },
  );

  // const {
  //   error: commentsError,
  //   loading: commentsLoading,
  //   data: commentsData,
  //   refetch: commentsReftch,
  // } = useQuery(Get_Comments_By_Id, {
  //   variables: { id: orderId },
  //   fetchPolicy: 'network-only',
  //   onCompleted: (data) => {
  //     setCommentResponse(data?.getOrderCommentsById);
  //     // addToast('success', 'Success');
  //   },
  //   onError: (error) => {
  //     addToast('error', 'Error Occured');
  //   },
  // });

  const { refetch: commentsReftch } = useQuery({
    queryKey: ['get-comment-by-order-id', orderId],
    queryFn: async (): Promise<any> => {
      const response: any = await axios.get(
        `${krakendPaths.ORDER_COMMENTS_URL}/admin-api/v1/order-comments/${orderId}`,
        {
          // params: { id: orderId },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response?.collection;
    },
    enabled: !!orderId,
    onError: (err: any) => {
      addToast('error', err?.message);
    },
    onSuccess: (data) => {
      setCommentResponse(data);
    },
  });

  const handleSubmit = () => {
    console.log('Rubber');
    if (comments.length === 0) {
      // addToast('warning', 'Please Enter a Comment');
      setErr('Please Enter a Comment');
    } else {
      setErr('');
      addOrderComment({
        order_id: orderId,
        comment: comments,
        is_customer_notified: notify,
        createdBy: localStorage.getItem('username'),
      });
      setComments('');
      setVisible(false);
      commentsReftch();
    }
  };
  useEffect(() => {
    if (queryData?.result) {
      if (queryData?.result[0]?.items) {
        // console.log('modal', queryData.getMagentoOrders.result);
        setOrderId(queryData?.result[0].order_id);
        const responseData: OrderDetailsTableColumn[] =
          queryData?.result[0].items?.map((element: any, index: number) => {
            const product = {
              name: element.name ?? '',
              sku: element.sku ?? '',
              product_id: element.product_id,
              type: element.type,
              weight: element.weight,
            };
            const tax = {
              tax_percent: element.tax_percent,
              tax_amount: element.tax_amount,
            };
            const obj = {
              id: element.product_id ?? '',
              product: product ?? '',
              qty_ordered: element.qty_ordered,
              price_incl_tax: element.price_incl_tax,
              tax: tax,
              discount_amount: element.discount_amount,
              status: element.status,
            };
            return obj;
          });
        setResponse(responseData);
      }
      if (queryData?.result[0]?.order_summary) {
        const subtotal = queryData?.result[0]?.order_summary?.filter(
          (element: any) => element.code === 'subtotal',
        )[0]?.value;
        const grandtotal = queryData?.result[0]?.order_summary?.filter(
          (element: any) => element.code === 'grand_total',
        )[0]?.value;

        const shipping = queryData?.result[0]?.order_summary?.filter(
          (element: any) => element.code === 'shipping',
        )[0]?.value;
        const discount = queryData?.result[0]?.order_summary?.filter(
          (element: any) => element.code === 'discount',
        )[0]?.value;

        const handling_fee = queryData?.result[0]?.order_summary?.filter(
          (element: any) => element.code === 'handling_fee',
        )[0]?.value;

        const reward_earned = queryData?.result[0]?.dk_rewards?.reward_discount;

        const orderSummary = {
          subtotal: subtotal,
          grandtotal: grandtotal,
          shippingCharge: shipping,
          discount: discount,
          overweightDeliveryCharges: handling_fee,
          rewardDiscount: reward_earned,
        };
        setOrderTotal(orderSummary);
        // console.log('orderTotal: ', orderSummary);
      }
    }
  }, [queryData]);

  return (
    <>
      <SModal
        isLarger
        isLarge
        style={{ overflowX: 'hidden' }}
        onClose={() => setVisible(false)}
        isAnimated
      >
        <Header style={{ backgroundColor: baseTheme.colors.primaryHue }}>
          <Row justifyContent="center" alignItems="center">
            <LG hue="white">Items</LG>
          </Row>
        </Header>
        <Body>
          {queryLoading ? (
            <LazyLoading />
          ) : (
            <>
              {respons && <OrderDetailsTable data={respons} />}
              {orderTotal && (
                <>
                  <Row style={{ minHeight: '250px', margin: '12px' }}>
                    <Col size={6}>
                      <Div>
                        <XL>Notes for this Order</XL>
                      </Div>
                      <Div>
                        <SM style={{ color: 'black' }}>Comments</SM>
                      </Div>
                      <Div>
                      <JoditEditorWrapper>
                        <JoditEditor
                          ref={editor}
                          value={comments || ''}
                          onBlur={(newContent) => {
                            setComments(newContent);
                          }}
                          config={config}
                        />
                      </JoditEditorWrapper>
                        {/* <Textarea
                          minRows={6}
                          maxRows={8}
                          value={comments}
                          isResizable
                          onChange={(e) => {
                            setComments(e.target.value);
                          }}
                          style={{
                            fontSize: '14px',
                          }}
                        /> */}
                      </Div>
                      <Div>
                        <Label>
                          <input
                            type="checkbox"
                            checked={notify}
                            onChange={() => setNotify((prev) => !prev)}
                          />
                          <span> Notify Customer by Email</span>
                        </Label>
                      </Div>
                      <div>
                        {!!err && <Message validation="warning">{err}</Message>}
                      </div>
                      <Div>
                        <Button isPrimary isOrange onClick={handleSubmit}>
                          Submit Comment
                        </Button>
                      </Div>
                      <Row>
                        <Col size={6}>
                          {commentResponse?.map((element, index) => {
                            // const inputDate = '2023-06-22T14:45:14.827Z';
                            const dateObject = new Date(element.createdAt);

                            const formattedDate = dateObject.toLocaleDateString(
                              'en-US',
                              {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                              },
                            );

                            const hours = dateObject
                              .getUTCHours()
                              .toString()
                              .padStart(2, '0');
                            const minutes = dateObject
                              .getUTCMinutes()
                              .toString()
                              .padStart(2, '0');
                            const seconds = dateObject
                              .getUTCSeconds()
                              .toString()
                              .padStart(2, '0');

                            const convertedTime = `${hours}:${minutes}:${seconds}`;
                            const convertedDate = `${formattedDate} | ${convertedTime}`;
                            // console.log(convertedDate); // Output: 06/22/2023 | 14:45:14

                            return (
                              <div key={index}>
                                <p>
                                  <span style={{ width: 'auto' }}>
                                    {convertedDate} | {element.status} |{' '}
                                    {element.is_customer_notified
                                      ? 'Customer Notified'
                                      : 'Customer Not Notified'}
                                  </span>
                                </p>
                                <p>
                                  {/* <strong> */}
                                  {element.comment} {' - '}
                                  {element.createdBy}
                                  {/* </strong> */}
                                </p>
                              </div>
                            );
                          })}
                        </Col>
                      </Row>
                    </Col>
                    <Col size={6}>
                      <Div>
                        <XL>Order Totals</XL>
                      </Div>
                      <div
                        style={{
                          backgroundColor: '#F9F9F9',
                          height: '70%',
                          padding: '8px',
                        }}
                      >
                        <Row>
                          <Col size={6}>
                            <LG>Subtotal</LG>
                          </Col>
                          <Col size={6}>{orderTotal.subtotal}</Col>
                        </Row>
                        <Row style={{ marginTop: '15px' }}>
                          <Col size={6}>
                            <LG>Grand Total</LG>
                          </Col>
                          <Col size={6}>{orderTotal.grandtotal}</Col>
                        </Row>
                        <Row style={{ marginTop: '15px' }}>
                          <Col size={6}>
                            <LG>Shipping Charge</LG>
                          </Col>
                          <Col size={6}>{orderTotal.shippingCharge}</Col>
                        </Row>
                        <Row style={{ marginTop: '15px' }}>
                          <Col size={6}>
                            <LG>Discount</LG>
                          </Col>
                          <Col size={6}>{orderTotal.discount}</Col>
                        </Row>
                        <Row style={{ marginTop: '15px' }}>
                          <Col size={6}>
                            <LG>Overweight delivery charges</LG>
                          </Col>
                          <Col size={6}>
                            {orderTotal.overweightDeliveryCharges}
                          </Col>
                        </Row>
                        <Row style={{ marginTop: '15px' }}>
                          <Col size={6}>
                            <LG>Rewards Discount </LG>
                          </Col>
                          <Col size={6}>{orderTotal.rewardDiscount}</Col>
                        </Row>
                      </div>
                    </Col>
                  </Row>
                </>
              )}
            </>
          )}
        </Body>
        <Footer>
          <FooterItem></FooterItem>
          <FooterItem></FooterItem>
        </Footer>
        <CloseButton aria-label="Close modal" />
      </SModal>
    </>
  );
};
