import styled from 'styled-components';
import { Col } from '@zendeskgarden/react-grid';
import { mediaQuery } from '@zendeskgarden/react-theming';

type props = {
  last?: boolean;
};
export default styled(Col)<props>`
  position: relative;
  padding: ${(p) => p.theme.space.md};
  // border-right: ${(p) => p.theme.space.xxs} solid
  //   ${(p) => (p.last ? p.theme.colors.invisible : p.theme.colors.invisible)};
  min-height: 400px;
  overflow: auto;

  ${(props) => mediaQuery('down', 'md', props.theme)} {
    border-right: none;
    border-bottom: ${(p) => p.theme.space.xxs} solid
      ${(p) =>
        p.last ? p.theme.colors.invisible : p.theme.colors.lightSeaGreen};
    padding: ${(p) => p.theme.space.md} 0;
  }
`;
