import { Row as _Row, Col as _Col } from '@zendeskgarden/react-grid';
import styled from 'styled-components';
import { MD, XL } from '@zendeskgarden/react-typography';
import graphColors from '../../../../constants/graphColors';
import Wrapper from './Wrapper';
// import { useStatsData } from '../../hooks/useQuery';
import { IEngagementTimePerSection, IStats, IStatsCP } from '../../../../types/types';
import { useState, useEffect } from 'react';
import { Skeleton } from '@zendeskgarden/react-loaders';
import { mediaQuery } from '@zendeskgarden/react-theming';
import { DiagramIcon } from '../../../../utils/icons';
import { baseTheme } from '../../../../themes/theme';
import { Col, Row as SRow } from '../../../UI-components/Grid';
import { calculateDecimalHours } from '../../../../helpers/helper';

type labelProp = { bg: string; width: number };

// const Col = styled(_Col)`
//   ${(props) => mediaQuery('down', 'md', props.theme)} {
//     flex-wrap: no-wrap;
//   }
// `;

const Bar = styled.div<labelProp>`
  border-radius: ${(p) => p.theme.borderRadii.sm};
  background: ${(p) => p.bg};
  width: ${(p) => p.width}%;
  height: ${(p) => p.theme.space.md};
`;

const Row = styled(_Row)`
  margin-bottom: ${(p) => p.theme.space.sm};
  width: 100%;
  align-items: center;
  ${(props) => mediaQuery('down', 'md', props.theme)} {
    flex-wrap: nowrap;
  }
`;

const Col1 = styled(_Col)`
  ${(props) => mediaQuery('down', 'md', props.theme)} {
    width: 22%;
  }
`;
const Col2 = styled(_Col)`
  ${(props) => mediaQuery('down', 'md', props.theme)} {
    width: 83%;
  }
`;
const Col3 = styled(_Col)`
  ${(props) => mediaQuery('down', 'md', props.theme)} {
    width: 5%;
  }
`;

export default ({
  inReports,
  data,
  isLoading,
}: {
  inReports?: boolean;
  data: IEngagementTimePerSection[];
  isLoading: boolean;
}) => {
  // const { isLoading, isError, error, statistics, isRefetching, refetch } =
  //   useStatsData();

  const [engagementPersection, setEngagementPerSection] = useState<
    IEngagementTimePerSection[]
  >([]);

  const totalSum = data.reduce(
    (sum, section) => sum + parseInt(section.visitCount),
    0,
  );

  console.log('Totdal', totalSum);

  const sectionsWithPercentage = data.map((section) => ({
    section_key: section.section_key,
    visitCount: section.visitCount,
    percentage: (parseInt(section.visitCount) / totalSum) * 100,
  }));

  console.log('Section With Percentage', sectionsWithPercentage);

  return (
    <Wrapper last={inReports ? true : false} lg={inReports ? 12 : 4} md={12}>
      {!isLoading && sectionsWithPercentage && (
        <>
          <SRow alignItems="center" justifyContent="center">
            <Col offset={0.3}>
              {sectionsWithPercentage.map((section, index) => (
                <>
                  <Row style={{ marginTop: '2%' }}>
                    <Col1 lg={2.5} md={1.5}>
                      <MD>{section.section_key}</MD>
                    </Col1>
                    <Col2
                      style={{
                        background: baseTheme.colors.lightGrey,
                        borderRadius: baseTheme.borderRadii.sm,
                      }}
                      lg={6.5}
                      md={7.5}
                    >
                      <Bar
                        bg={graphColors[index]}
                        width={section.percentage || 0}
                      />
                    </Col2>
                    <Col3 lg={2} md={2} offset={0.5}>
                      {section.visitCount} view count
                    </Col3>
                  </Row>
                </>
              ))}
              {/* <Row style={{ marginTop: '2%' }}>
                <Col1 lg={2.5} md={1.5}>
                  <MD>Cheapest</MD>
                </Col1>
                <Col2
                  style={{
                    background: baseTheme.colors.lightGrey,
                    borderRadius: baseTheme.borderRadii.sm,
                  }}
                  lg={7.5}
                  md={8.5}
                >
                  <Bar
                    bg={graphColors[0]}
                    width={marketPostion.stats.cheapest?.percentage || 0}
                  />
                </Col2>
                <Col3 lg={1} md={1} offset={0.5}>
                  {marketPostion.stats.cheapest?.count || 0}
                </Col3>
              </Row>
              <Row style={{ marginTop: '2%' }}>
                <Col1 lg={2.5} md={1.5}>
                  <MD>Cheaper</MD>
                </Col1>
                <Col2
                  style={{
                    background: baseTheme.colors.lightGrey,
                    borderRadius: baseTheme.borderRadii.sm,
                  }}
                  lg={7.5}
                  md={8.5}
                >
                  <Bar
                    bg={graphColors[1]}
                    width={marketPostion.stats.cheaper?.percentage || 0}
                  />
                </Col2>
                <Col3 lg={1} md={1} offset={0.5}>
                  {marketPostion.stats.cheaper?.count || 0}
                </Col3>
              </Row>
              <Row style={{ marginTop: '2%' }}>
                <Col1 lg={2.5} md={1.5}>
                  <MD>Average</MD>
                </Col1>
                <Col2
                  style={{
                    background: baseTheme.colors.lightGrey,
                    borderRadius: baseTheme.borderRadii.sm,
                  }}
                  lg={7.5}
                  md={8.5}
                >
                  <Bar
                    bg={graphColors[2]}
                    width={marketPostion.stats.average?.percentage || 0}
                  />
                </Col2>
                <Col3 lg={1} md={1} offset={0.5}>
                  {marketPostion.stats.average?.count || 0}
                </Col3>
              </Row>
              <Row style={{ marginTop: '2%' }}>
                <Col1 lg={2.5} md={1.5}>
                  <MD>Costlier</MD>
                </Col1>
                <Col2
                  style={{
                    background: baseTheme.colors.lightGrey,
                    borderRadius: baseTheme.borderRadii.sm,
                  }}
                  lg={7.5}
                  md={8.5}
                >
                  <Bar
                    bg={graphColors[3]}
                    width={marketPostion.stats.costlier?.percentage || 0}
                  />
                </Col2>
                <Col3 lg={1} md={1} offset={0.5}>
                  {marketPostion.stats.costlier?.count || 0}
                </Col3>
              </Row>
              <Row style={{ marginTop: '2%' }}>
                <Col1 lg={2.5} md={1.5}>
                  <MD>Costliest</MD>
                </Col1>
                <Col2
                  style={{
                    background: baseTheme.colors.lightGrey,
                    borderRadius: baseTheme.borderRadii.sm,
                  }}
                  lg={7.5}
                  md={8.5}
                >
                  <Bar
                    bg={graphColors[4]}
                    width={marketPostion.stats.costliest?.percentage || 0}
                  />
                </Col2>
                <Col3 lg={1} md={1} offset={0.5}>
                  {marketPostion.stats.costliest?.count || 0}
                </Col3>
              </Row> */}
            </Col>
          </SRow>
        </>
      )}

      {isLoading && (
        <>
          <Row>
            <Col1>
              <MD style={{ height: '30px' }}>
                <Skeleton />
              </MD>
            </Col1>
          </Row>
          <Row>
            <Col2>
              <MD style={{ height: '30px' }}>
                <Skeleton />
              </MD>
            </Col2>
          </Row>
          <Row>
            <Col3>
              <MD style={{ height: '30px' }}>
                <Skeleton />
              </MD>
            </Col3>
          </Row>
          <Row>
            <Col1>
              <MD style={{ height: '30px' }}>
                <Skeleton />
              </MD>
            </Col1>
          </Row>
          <Row>
            <Col2>
              <MD style={{ height: '30px' }}>
                <Skeleton />
              </MD>
            </Col2>
          </Row>
        </>
      )}
    </Wrapper>
  );
};
