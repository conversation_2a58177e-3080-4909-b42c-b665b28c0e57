import React, { useEffect, useRef, useState } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ScaleOptions,
} from 'chart.js';
import { baseTheme } from '../../../../themes/theme';
import { useFeedsWatchCountPerDayHistory } from '../../../../hooks/useQuery';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
);

export interface DataPoint {
  date: string;
  totalWatchCount: string;
}

interface WatchCountLineChartProps {
  watchCountData: DataPoint[];
  days: number;
}

const WatchCountLineChart: React.FC<WatchCountLineChartProps> = ({
  watchCountData,
  days,
}) => {
  const [chartData, setChartData] = useState<any>(null);

  useEffect(() => {
    if (watchCountData.length > 0) {
      const labels = watchCountData.map((entry) => entry.date);
      const dataPoints = watchCountData.map((entry) =>
        parseFloat(entry.totalWatchCount),
      );

      const chartDataset = {
        labels,
        datasets: [
          {
            label: `Total Watch Count (${days} days)`,
            data: dataPoints,
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderWidth: 2,
            fill: true,
            cubicInterpolationMode: 'monotone',
            tension: 1,
          },
        ],
      };

      setChartData(chartDataset);
    }
  }, [watchCountData, days]);

  const options = {
    height: 100,
    scales: {
      y: {
        ticks: {
          fontColor: baseTheme.colors.deepBlue,
          fontFamily: baseTheme.fonts.lexend,
          min: 70,
          max: 120,
          fontStyle: 'bold',
          stepSize: 60,
        },

        grid: { display: false },
      },

      x: {
        ticks: {
          fontColor: baseTheme.colors.deepBlue,
          fontFamily: baseTheme.fonts.lexend,
          fontStyle: 'bold',
        },
        grid: { display: false },
      },
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          font: {
            size: baseTheme.fontSizes.sm,
            family: baseTheme.fonts.lexend,
          },
          usePointStyle: true,
        },
      },
    },
  };

  return (
    <div>
      {chartData ? (
        <Line data={chartData as any} options={options as any} />
      ) : (
        <p>No watch time data available.</p>
      )}
    </div>
  );
};

export default WatchCountLineChart;
