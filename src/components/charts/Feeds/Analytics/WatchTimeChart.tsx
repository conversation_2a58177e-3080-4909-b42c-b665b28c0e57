import React, { useEffect, useRef, useState } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ScaleOptions,
} from 'chart.js';
import { baseTheme } from '../../../../themes/theme';
import { calculateDecimalHours } from '../../../../helpers/helper';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
);

export interface DataPoint {
  date: string;
  totalWatchTime: string;
}

export interface WatchTimeLineChartProps {
  watchTimeData: DataPoint[];
  days: number;
}

const WatchTimeLineChart: React.FC<WatchTimeLineChartProps> = ({
  watchTimeData,
  days,
}) => {
  const [chartData, setChartData] = useState<any>(null);

  const DATA_COUNT = 7;
  const NUMBER_CFG = { count: DATA_COUNT, min: -100, max: 100 };

  useEffect(() => {
    if (watchTimeData.length > 0) {
      const labels = watchTimeData.map((entry) => entry.date);
      const dataPoints = watchTimeData.map((entry) =>
        calculateDecimalHours(parseFloat(entry.totalWatchTime), 2),
      );

      const chartDataset = {
        labels,
        datasets: [
          {
            label: `Total Watch Time (${days} days)`,
            data: dataPoints,
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderWidth: 2,
            fill: true,
            cubicInterpolationMode: 'monotone',
            tension: 1,
          },
        ],
      };

      setChartData(chartDataset);
    }
  }, [watchTimeData, days]);

  const options = {
    height: 100,
    scales: {
      y: {
        ticks: {
          fontColor: baseTheme.colors.deepBlue,
          fontFamily: baseTheme.fonts.lexend,
          min: 70,
          max: 120,
          fontStyle: 'bold',
          stepSize: 60,
        },

        grid: { display: false },
      },

      x: {
        ticks: {
          fontColor: baseTheme.colors.deepBlue,
          fontFamily: baseTheme.fonts.lexend,
          fontStyle: 'bold',
        },
        grid: { display: false },
      },
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          font: {
            size: baseTheme.fontSizes.sm,
            family: baseTheme.fonts.lexend,
          },
          usePointStyle: true,
        },
      },
    },
  };

  return (
    <div>
      {chartData ? (
        <Line data={chartData as any} options={options as any} />
      ) : (
        <p>No watch time data available.</p>
      )}
    </div>
  );
};

export default WatchTimeLineChart;
