import React from 'react';
import { Dropdown, Field, Menu, Item, Select, Label } from '@zendeskgarden/react-dropdowns';
import { Row, Col } from '@zendeskgarden/react-grid';

interface IItem {
  label: string;
  value: string;
}

interface DropdownProps {
  items: IItem[];
  selectedItem: IItem;
  onSelect: (item: IItem) => void;
}

const ProductSelectDropdown: React.FC<DropdownProps> = ({ items, selectedItem, onSelect }) => {
  return (
    <Row justifyContent="center">
      <Col sm={5}>
        <Dropdown
          selectedItem={selectedItem}
          onSelect={onSelect}
          downshiftProps={{ itemToString: (item: IItem) => item && item.label }}
        >
          <Field>
            <Label>Houseplant</Label>
            <Select>{selectedItem.label}</Select>
          </Field>
          <Menu>
            {items.map(option => (
              <Item key={option.value} value={option} >
                {option.label}
              </Item>
            ))}
          </Menu>
        </Dropdown>
      </Col>
    </Row>
  );
};

export default ProductSelectDropdown;
