import React, { useRef, useState, useEffect } from 'react'; 
import { Item, Menu, Label as _Label, Field, Dropdown, Autocomplete } from '@zendeskgarden/react-dropdowns';
import { Row, Col } from '@zendeskgarden/react-grid';
import { debounce } from 'lodash'; 
import { baseTheme } from '../../../themes/theme';
import styled from 'styled-components';

const Label = styled(_Label)`
  color: ${baseTheme.colors.deepBlue};
`;

interface AutocompleteDropdownProps {
  options: any[];
  selectedItem: any;
  inputValue: any;
  onSelect: (item: any) => void;
  onInputValueChange: (value: any) => void;
  label?: string;
  width?: string;
}

const ProductDropdown: React.FC<AutocompleteDropdownProps> = ({
  options,
  selectedItem,
  inputValue,
  onSelect,
  onInputValueChange,
  label,
  width
}) => {
  const [matchingOptions, setMatchingOptions] = useState(options);

  const filterMatchingOptionsRef = useRef(
    debounce((value: any) => {
      if (value && typeof value === 'string') {
        const matchedOptions = options.filter(option => {
          if (typeof option === 'string' && option.trim) {
            return option.trim().toLowerCase().indexOf(value.trim().toLowerCase()) !== -1;
          }
          return false;
        });
    
        setMatchingOptions(matchedOptions);
      } else {
        setMatchingOptions(options);
      }
    }, 300)
  );

  useEffect(() => {
    filterMatchingOptionsRef.current(inputValue);
  }, [inputValue]); 

  useEffect(() => {
    if (Array.isArray(options) && options.length > 0) {
      setMatchingOptions(options);
    }
  }, [JSON.stringify(options)]);

  return (
    <Row justifyContent="center" style={{width: `${width ? width : "auto"}`}}>
      <Col>
        <Dropdown
          inputValue={inputValue}
          selectedItem={selectedItem}
          onSelect={item => onSelect(item)}
          onInputValueChange={value => onInputValueChange(value)}
          downshiftProps={{ defaultHighlightedIndex: 0 }}
        >
          <Field> 
          <Label>{label}</Label>
            <Autocomplete >{selectedItem }</Autocomplete>
            
          </Field>
          <Menu>
            {matchingOptions.length > 0 ? (
              matchingOptions.map((option, index) => (
                <Item key={index} value={option ?? index}>
                  <span>{option}</span>
                </Item>
              ))
            ) : (
              <Item disabled>No matches found</Item>
            )}
          </Menu>
        </Dropdown>
      </Col>
    </Row>
  );
};

export default ProductDropdown;
