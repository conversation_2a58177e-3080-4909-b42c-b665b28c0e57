import React, { useState, useEffect, useRef } from 'react';
import {
  Dropdown as GardenDropdown,
  Multiselect as GardenMultiselect,
  Field,
  Menu,
  Item,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { Tag } from '@zendeskgarden/react-tags';
import { debounce } from 'lodash';

interface ReusableDropdownProps {
  options: string[];
  selectedItems: string[];
  onSelect: (items: string[]) => void;
  label: string;
}

const ProductMultiDropdown: React.FC<ReusableDropdownProps> = ({
  options,
  selectedItems,
  onSelect,
  label,
}) => {
  // console.log('Options of Category dropdown: ', options)
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [matchingOptions, setMatchingOptions] = useState<string[]>(options);
  // console.log('matchingOptions: ',matchingOptions)
  const filterMatchingOptionsRef = useRef(
    debounce((value) => {
      const matchedOptions = options.filter(
        (option) =>
          option.trim().toLowerCase().indexOf(value.trim().toLowerCase()) !==
          -1,
      );

      setMatchingOptions(matchedOptions);
      setIsLoading(false);
    }, 300),
  );

  useEffect(() => {
    setIsLoading(true);
    filterMatchingOptionsRef.current(inputValue);
  }, [inputValue]);

  const renderOptions = () => {
    if (isLoading) {
      return <Item disabled>Loading items...</Item>;
    }

    if (matchingOptions.length === 0) {
      return <Item disabled>No options found</Item>;
    }

    return matchingOptions.map((option, index) => (
      <Item key={index} value={option}>
        <span>{option}</span>
      </Item>
    ));
  };

  return (
    <GardenDropdown
      inputValue={inputValue}
      selectedItems={selectedItems}
      onSelect={onSelect}
      downshiftProps={{ defaultHighlightedIndex: 0 }}
      onInputValueChange={(value) => setInputValue(value)}
    >
      <Field>
        <Label hidden>{label}</Label>
        <GardenMultiselect
          renderItem={({ value, removeValue }) => (
            <Tag>
              <span>{value}</span>
              <Tag.Close onClick={() => removeValue()} />
            </Tag>
          )}
        />
      </Field>
      <Menu>{renderOptions()}</Menu>
    </GardenDropdown>
  );
};

export default ProductMultiDropdown;
