import { useState } from 'react';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { IItem } from '../../types/types';

const CommonTableDropdown = ({ data }: { data: any[] }) => {
  const [selectedItem, setSelectedItem] = useState(data[0]);

  return (
    <>
      <Dropdown
        selectedItem={selectedItem}
        onSelect={setSelectedItem}
        downshiftProps={{ itemToString: (item: IItem) => item && item.label }}
      >
        <Field>
          <Label hidden>Open</Label> <Select>{selectedItem.label}</Select>
        </Field>
        <Menu>
          {data.map((option) => (
            <Item key={option.value} value={option}>
              {option.label}
            </Item>
          ))}
        </Menu>
      </Dropdown>
    </>
  );
};

export default CommonTableDropdown;
