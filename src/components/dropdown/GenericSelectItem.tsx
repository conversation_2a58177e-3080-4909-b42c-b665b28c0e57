import React, { useState } from 'react';
import { Col, Row } from '../UI-components/Grid';
import { Dropdown, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import { DownIcon } from '../../utils/icons';
import {
  Checkbox,
  Field as _Field,
  Fieldset,
  Label as _Label,
} from '@zendeskgarden/react-forms';
import { baseTheme } from '../../themes/theme';
import styled from 'styled-components';
import { Button } from '../UI-components/Button';
import { LG } from '@zendeskgarden/react-typography';
import { IDropdownItem } from '../../types/types';

const Field = styled(_Field)`
  padding: ${baseTheme.space.sm};
`;

const Label = styled(_Label)`
  font-size: ${baseTheme.fontSizes.md};
`;

type SelectItemDropdownProps = {
  label?: string;
  width?: string;
  items: IDropdownItem[];
  setSelectedItems: React.Dispatch<React.SetStateAction<IDropdownItem[]>>;
};

const GenericSelectItemDropdown = ({
  label,
  width,
  items,
  setSelectedItems,
}: SelectItemDropdownProps) => {
  const [rotated, setRotated] = useState<boolean | undefined>();

  const handleSelect = (item: IDropdownItem) => {
    const updatedItems = items.map((i) => ({
      ...i,
      selected: i.value === item.value ? !item.selected : i.selected,
    }));
    setSelectedItems(updatedItems);
  };

  return (
    <>
      <Row>
        <Col>
          <Dropdown
            onSelect={(item) => handleSelect(item)}
            onStateChange={(options) =>
              Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
            }
          >
            <Trigger
              style={{
                width: width
                  ? width
                  : baseTheme.components.dimension.width.base200,
              }}
            >
              <Button size="medium" isPrimary>
                <Row alignItems="center">
                  <LG>{label ? label : 'Select Items'}</LG>
                  <Button.EndIcon isRotated={rotated}>
                    <DownIcon
                      style={{
                        height: baseTheme.iconSizes.md,
                        width: baseTheme.iconSizes.md,
                      }}
                    />
                  </Button.EndIcon>
                </Row>
              </Button>
            </Trigger>
            <Menu
              style={{
                width: width
                  ? width
                  : baseTheme.components.dimension.width.base200,
                transform: 'translateX(4px)',
                borderRadius: baseTheme.borderRadii.lg,
              }}
            >
              <Fieldset>
                {items.map((item) => (
                  <Field key={item.value}>
                    <Checkbox
                      checked={item.selected}
                      onChange={() => handleSelect(item)}
                      disabled={item.disabled}
                    >
                      <Label>{item.label}</Label>
                    </Checkbox>
                  </Field>
                ))}
              </Fieldset>
            </Menu>
          </Dropdown>
        </Col>
      </Row>
    </>
  );
};

export default GenericSelectItemDropdown;
