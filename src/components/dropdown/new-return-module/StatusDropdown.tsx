import React, { useEffect, useState } from 'react';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { Row, Col } from '@zendeskgarden/react-grid';
import useAxios from '../../../hooks/useAxios';
import { IReturnDTOStatus } from '../../../types/new-return-types';
import routes from '../../../constants/routes';
import { GET_RETURN_DTO_STATUS } from '../../../graphql/';
import { baseTheme } from '../../../themes/theme';
import { Message } from '@zendeskgarden/react-forms';
import { Field as FField } from '@zendeskgarden/react-forms';
import { useQuery } from '@tanstack/react-query';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const StatusDropdown = ({
  setActiveStatus,
  activeStatus,
  error,
}: {
  setActiveStatus: React.Dispatch<
    React.SetStateAction<IReturnDTOStatus | undefined>
  >;
  activeStatus?: string;
  error?: any;
}) => {
  const [status, setStatus] = useState<IReturnDTOStatus[]>([]);

  const [filters, setFilters] = useState({
    rowsPerPage: 20,
    pageNumber: 1,
  });
  const axios = useAxios();

  const {
    data,
    isLoading: queryLoading,
    refetch,
  } = useQuery({
    queryKey: ['configList', 'dto_status'],
    queryFn: async () => {
      const response = await axios.get(
        `${krakendPaths.RETURN_URL}/admin-api/v1/config`,
        {
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
          params: { type: 'dto_status' }, // Adding query parameters
        },
      );
      return response;
    },
    staleTime: 0,
    cacheTime: 0,
    onSuccess: (data: any) => {
      const result: IReturnDTOStatus[] = data?.getConfigList;
      setStatus(result);
    },
    onError: (error: any) => {
      console.error(error);
    },
  });

  const [selectedStatus, setSelectedStatus] = useState<
    IReturnDTOStatus | undefined
  >(status.filter((s) => s.enable)[0]);

  useEffect(() => {
    // setInspectionRemark(testData);
    if (selectedStatus?.name) {
      selectedStatus && setActiveStatus(selectedStatus);
    } else {
      setActiveStatus(undefined);
    }
  }, [selectedStatus]);

  useEffect(() => {
    if (activeStatus) {
      const selectedObject = status.find(
        (item) =>
          item.name.trim().toLowerCase() === activeStatus.trim().toLowerCase(),
      );
      if (selectedObject) {
        setSelectedStatus(selectedObject);
      }
    }
  }, [status]);

  return (
    <>
      <FField>
        <Dropdown
          selectedItem={selectedStatus}
          onSelect={(item) => {
            setSelectedStatus(item);
          }}
          downshiftProps={{
            itemToString: (item: IReturnDTOStatus) => item && item.name,
          }}
        >
          <Field>
            <Label
              style={{
                color: baseTheme.colors.veryDarkGray,
                fontWeight: baseTheme.fontWeights.regular,
              }}
            >
              Status *
            </Label>
            <Select
              validation={error?.status ? 'error' : undefined}
              disabled={selectedStatus?.name === 'Closed'}
            >
              {selectedStatus?.name || 'Select'}
            </Select>
          </Field>
          <Menu>
            {status
              .filter((option) => option.enable)
              .map((option) => (
                <Item key={option.id} value={option}>
                  {option.name}
                </Item>
              ))}
            <Item key={'reset'} value={''}>
              {'RESET'}
            </Item>
          </Menu>
        </Dropdown>
        {error && error.status && (
          <Message validation="error">Status is required</Message>
        )}
      </FField>
    </>
  );
};

export default StatusDropdown;
