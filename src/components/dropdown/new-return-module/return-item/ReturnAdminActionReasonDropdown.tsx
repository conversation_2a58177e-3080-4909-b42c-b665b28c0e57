import { useEffect, useState } from 'react';
import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
interface AdminActionReasonDropdownProps {
  setActiveReason: React.Dispatch<React.SetStateAction<string | undefined>>;
  activeReason: string | undefined;
  value?: string;
  disable: boolean;
  reasons: string[] | undefined;
  onSelect: (item: string) => void;
}
const AdminActionReasonDropdown = ({
  setActiveReason,
  activeReason,
  value,
  disable,
  reasons,
  onSelect,
}: AdminActionReasonDropdownProps) => {
  const [reason, setReason] = useState<string | undefined>(value);

  useEffect(() => {
    if (activeReason !== undefined) {
      setReason(activeReason);
    }
  }, [activeReason]);

  const handleSelect = (selectedItem: string) => {
    setReason(selectedItem);
    setActiveReason(selectedItem);
    onSelect(selectedItem); // Pass only the string, not an object
  };

  return (
    <Dropdown
      selectedItem={reason} // Use local state `reason`
      onSelect={handleSelect} // Ensure the selected item is handled correctly
      downshiftProps={{
        itemToString: (item: string | null) => item || '',
      }}
    >
      <Field>
        <Label hidden>Admin Action</Label>
        <Select disabled={disable}>
          {reason || 'Select Admin Action Reason'}
        </Select>
      </Field>
      <Menu>
        {reasons?.map((option: any) => (
          <Item key={option} value={option?.reason}>
            {option.reason}
          </Item>
        ))}
      </Menu>
    </Dropdown>
  );
};
export default AdminActionReasonDropdown;
