import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import React, { useEffect, useState } from 'react';
import { IStatusRes } from '../../../../types/new-return-types';

const ReturnStatusDropdown = ({
  setActiveStatus,
  activeStatus,
  value,
  disable,
  returnStatus,
  onSelect,
}: {
  setActiveStatus: React.Dispatch<React.SetStateAction<IStatusRes | undefined>>;
  value?: string;
  disable: boolean;
  activeStatus: IStatusRes | undefined;
  returnStatus: IStatusRes[] | undefined;
  onSelect: (item: IStatusRes) => void;
}) => {
  const [status, setStatus] = useState<IStatusRes[]>();

  useEffect(() => {
    returnStatus && setStatus(returnStatus);
  }, [returnStatus]);

  useEffect(() => {
    if (value) {
      const selectedOption =
        status &&
        status.find(
          (option) => option.status.toLowerCase() == value.toLowerCase(),
        );
      setActiveStatus(selectedOption);
    }
  }, [status]);

  return (
    <Dropdown
      selectedItem={activeStatus}
      onSelect={onSelect}
      downshiftProps={{
        itemToString: (item: IStatusRes) => item && item.status,
      }}
    >
      <Field>
        <Label hidden>Status</Label>
        <Select disabled={disable}>{activeStatus?.status}</Select>
      </Field>
      <Menu>
        {status &&
          status
            .filter((option) => option.enable)
            .map((option) => (
              <Item key={option.id} value={option}>
                {option.status}
              </Item>
            ))}
      </Menu>
    </Dropdown>
  );
};

export default ReturnStatusDropdown;
