import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import React, { useEffect, useState } from 'react';
import { IReturnAction } from '../../../../types/types';
import { useReturnAction, useReturnStatus } from '../../../../hooks/useReturn';
import { baseTheme } from '../../../../themes/theme';

const ReturnActionDropdown = ({
  setActiveAction,
  activeAction,
  value,
  disable,
  reasonResponse,
  onSelect,
}: {
  setActiveAction: React.Dispatch<
    React.SetStateAction<IReturnAction | undefined>
  >;
  activeAction: IReturnAction | undefined;
  value?: string;
  disable: boolean;
  reasonResponse: IReturnAction[] | undefined;
  onSelect: (item: IReturnAction) => void;
}) => {
  const [status, setStatus] = useState<IReturnAction[]>();

  useEffect(() => {
    reasonResponse && setStatus(reasonResponse);
  }, [reasonResponse]);

  useEffect(() => {
    if (value) {
      const selectedOption =
        reasonResponse &&
        reasonResponse.filter(
          (option) => option.action.toLowerCase() == value.toLowerCase(),
        )[0];

      setActiveAction(selectedOption);
    }
  }, [status]);

  return (
    <Dropdown
      selectedItem={activeAction}
      onSelect={onSelect}
      downshiftProps={{
        itemToString: (item: IReturnAction) => item && item.action,
      }}
    >
      <Field>
        <Label hidden>Status</Label>
        <Select disabled={disable}>{activeAction?.action}</Select>
      </Field>
      <Menu>
        {status &&
          status
            .filter((option) => option.enable)
            .map((option) => (
              <Item key={option.id} value={option}>
                {option.action}
              </Item>
            ))}
      </Menu>
    </Dropdown>
  );
};

export default ReturnActionDropdown;
