import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import React, { useEffect, useState } from 'react';

interface AdminAction {
  id: number;
  code: string;
  name: string;
}

interface AdminActionDropdownProps {
  setActiveAction: React.Dispatch<
    React.SetStateAction<AdminAction | undefined>
  >;
  activeAction: AdminAction | undefined;
  value?: string;
  disable: boolean;
  adminActions: AdminAction[] | undefined;
  onSelect: (item: AdminAction) => void;
}

const AdminActionDropdown = ({
  setActiveAction,
  activeAction,
  value,
  disable,
  adminActions,
  onSelect,
}: AdminActionDropdownProps) => {
  const [actions, setActions] = useState<AdminAction[]>();

  useEffect(() => {
    adminActions && setActions(adminActions);
  }, [adminActions]);

  useEffect(() => {
    if (value) {
      const selectedOption =
        adminActions &&
        (adminActions.filter(
          (option) => option.code.toLowerCase() === value.toLowerCase(),
        )[0] ||
          adminActions.filter(
            (option) => option.name.toLowerCase() === value.toLowerCase(),
          )[0]);
      setActiveAction(selectedOption);
    }
  }, [actions]);

  return (
    <Dropdown
      selectedItem={activeAction}
      onSelect={onSelect}
      downshiftProps={{
        itemToString: (item: AdminAction) => item && item.name,
      }}
    >
      <Field>
        <Label hidden>Admin Action</Label>
        <Select disabled={disable}>
          {activeAction?.name || 'Select Admin Action'}
        </Select>
      </Field>
      <Menu>
        {actions &&
          actions.map((option) => (
            <Item key={option.id} value={option}>
              {option.name}
            </Item>
          ))}
      </Menu>
    </Dropdown>
  );
};

export default AdminActionDropdown;
