import React, { useState } from 'react';
import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import { IDeliveryLocation } from '../../../types/new-return-types';
import { LG } from '../../UI-components/Typography';
import { baseTheme } from '../../../themes/theme';

const LocationDropdown = ({
  locations,
  selectedItem,
  setSelectedItem,
  isDisabled,
}: {
  locations: IDeliveryLocation[];
  isDisabled?: boolean;
  selectedItem: IDeliveryLocation | undefined;
  setSelectedItem: React.Dispatch<
    React.SetStateAction<IDeliveryLocation | undefined>
  >;
}) => {
  return (
    <Dropdown
      selectedItem={selectedItem}
      onSelect={setSelectedItem}
      downshiftProps={{
        itemToString: (item: IDeliveryLocation) => item && item.name,
      }}
    >
      <Field style={{ borderRadius: baseTheme.borderRadii.md }}>
        <Label>
          <LG hue="primary">Location</LG>
        </Label>
        <Select>
          {selectedItem != undefined ? selectedItem.name : 'Select Location'}
        </Select>
      </Field>
      <Menu>
        {locations
          .filter((option) => option.enable) // Filter out options with enable = false
          .map((option) => (
            <Item key={option.id} value={option}>
              {option.name}
            </Item>
          ))}
      </Menu>
    </Dropdown>
  );
};

export default LocationDropdown;
