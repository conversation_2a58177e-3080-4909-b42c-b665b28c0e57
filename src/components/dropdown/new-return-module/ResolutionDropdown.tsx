import React, { useEffect, useState } from 'react';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import useAxios from '../../../hooks/useAxios';
import { IResolution } from '../../../types/new-return-types';
import { Field as FField } from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';
import { useQuery } from '@tanstack/react-query';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const ResolutionDropdown = ({
  setActiveResolution,
  activeResolution,
  stage,
  next_stage,
  pageType,
}: {
  setActiveResolution: React.Dispatch<
    React.SetStateAction<IResolution | undefined>
  >;
  stage: string;
  activeResolution?: string;
  next_stage?: string;
  pageType?: 'create' | 'inventory';
}) => {
  const [selectedResolution, setSelectedResolution] = useState<IResolution>();
  const [resolution, setResolution] = useState<IResolution[]>([]);

  useEffect(() => {
    // setInspectionRemark(testData);
    if (selectedResolution?.key) {
      setActiveResolution(selectedResolution);
    } else {
      setActiveResolution(undefined);
    }
  }, [selectedResolution]);

  const [filters, setFilters] = useState({
    rowsPerPage: 20,
    pageNumber: 1,
  });
  const axios = useAxios();

  const {
    data,
    error,
    isLoading: queryLoading,
    refetch,
  } = useQuery({
    queryKey: ['resolutionConfig', stage, next_stage],
    queryFn: async () => {
      const response = await axios.get(
        `${krakendPaths.RETURN_URL}/admin-api/v1/resolution-config`,
        {
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
          params: { stage, next_stage },
        },
      );
      return response;
    },
    staleTime: 0,
    cacheTime: 0,
    onSuccess: (data: any) => {
      let result: IResolution[] = data?.getResolutionConfigList;
      // Example: Uncomment the below line to filter out "refund" resolutions
      // setResolution(result.filter((res) => res.next_stage !== 'refund'));

      if (pageType === 'create' && stage === 'redispatch') {
        result = result.filter(
          (r) => r.key === 'Send Back as it is (to Redispatch)',
        );
      }

      if (stage === 'inventory') {
        result = result.filter(
          (r) =>
            !['Send back as it iscode', 'Repair by Vendor code'].includes(
              r.key,
            ),
        );
      }

      setResolution(result);
    },
    onError: (error: any) => {
      console.error(error);
    },
  });

  useEffect(() => {
    if (activeResolution) {
      const selectedObject = resolution.find(
        (item) =>
          item.key.trim().toLowerCase() ===
          activeResolution.trim().toLowerCase(),
      );
      if (selectedObject) {
        setSelectedResolution(selectedObject);
      }
    }
  }, [resolution]);

  return (
    <>
      <FField>
        <Dropdown
          selectedItem={selectedResolution}
          onSelect={(item) => {
            setSelectedResolution(item);
          }}
          downshiftProps={{
            itemToString: (item: IResolution) => item && item.key,
          }}
        >
          <Field>
            <Label
              style={{
                color: baseTheme.colors.veryDarkGray,
                fontWeight: baseTheme.fontWeights.regular,
              }}
            >
              Resolution
            </Label>
            <Select>{selectedResolution?.key || 'Select'}</Select>
          </Field>
          <Menu>
            {resolution
              // .filter((option) => option.enable === true)
              .map((option) => (
                <Item key={option.id} value={option}>
                  {option.key}
                </Item>
              ))}
            <Item key={'reset'} value={''}>
              {'RESET'}
            </Item>
          </Menu>
        </Dropdown>
      </FField>
    </>
  );
};

export default ResolutionDropdown;
