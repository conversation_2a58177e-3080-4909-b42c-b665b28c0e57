import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import React, { useState } from 'react';
import { IReturnAssignee, ITag } from '../../../types/types';

const AssigneeDropdown = ({
  data,
  selectedItem,
  setSelectedItem,
  isDisabled,
}: {
  data: IReturnAssignee[];
  isDisabled?: boolean;
  selectedItem: IReturnAssignee | undefined;
  setSelectedItem: React.Dispatch<
    React.SetStateAction<IReturnAssignee | undefined>
  >;
}) => {
  return (
    <>
      <Dropdown
        selectedItem={selectedItem}
        onSelect={setSelectedItem}
        downshiftProps={{
          itemToString: (item: IReturnAssignee) => item && item.user,
        }}
      >
        <Field>
          <Label hidden>Assignee Dropdown</Label>{' '}
          <Select disabled={isDisabled}>
            {selectedItem ? selectedItem.user : 'Assign to'}
          </Select>
        </Field>
        <Menu>
          {data
            .filter((option) => option.active)
            .map((option) => (
              <Item key={option.id} value={option}>
                {option.user}
              </Item>
            ))}
        </Menu>
      </Dropdown>
    </>
  );
};

export default AssigneeDropdown;
