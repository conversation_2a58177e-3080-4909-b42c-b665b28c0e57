import React, { useState } from 'react';
import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import { ITransporter } from '../../../types/new-return-types';
import { LG } from '../../UI-components/Typography';

const TransportDropdown = ({
  transporters,
  selectedItem,
  setSelectedItem,
  isDisabled,
}: {
  transporters: ITransporter[];
  isDisabled?: boolean;
  selectedItem: ITransporter | undefined;
  setSelectedItem: React.Dispatch<
    React.SetStateAction<ITransporter | undefined>
  >;
}) => {
  return (
    <Dropdown
      selectedItem={selectedItem}
      onSelect={setSelectedItem}
      downshiftProps={{
        itemToString: (item: ITransporter) => item && item.transporter,
      }}
    >
      <Field>
        <Label>
          <LG hue="primary">Transporter</LG>
        </Label>
        <Select>
          {selectedItem != undefined
            ? selectedItem.transporter
            : 'Default transporters'}
        </Select>
      </Field>
      <Menu>
        {transporters
          .filter((option) => option.enable) // Filter out options with enable = false
          .map((option) => (
            <Item key={option.id} value={option}>
              {option.transporter}
            </Item>
          ))}
      </Menu>
    </Dropdown>
  );
};

export default TransportDropdown;
