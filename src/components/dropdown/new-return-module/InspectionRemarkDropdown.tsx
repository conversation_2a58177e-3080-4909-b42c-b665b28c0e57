import React, { useEffect, useState } from 'react';
import { GET_INSPECTION_REMARK } from '../../../graphql/';
import useAxios from '../../../hooks/useAxios';
import { InspectionRemarkTableColumn } from '../../table/new-return-modules/columns';
import { IInspectionRemark } from '../../../types/new-return-types';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { baseTheme } from '../../../themes/theme';
import constants from '../../../constants';
import { useQuery } from '@tanstack/react-query';
import krakendPaths from '../../../constants/krakendPaths';

const InspectionRemarkDropdown = ({
  setActiveIR,
  activeIR,
}: {
  setActiveIR: React.Dispatch<
    React.SetStateAction<IInspectionRemark | undefined>
  >;
  activeIR: string;
}) => {
  const [selectedIR, setSelectedIR] = useState<IInspectionRemark>();
  const [inspectionRemarkList, setIRList] = useState<IInspectionRemark[]>([]);

  const axios = useAxios();

  useEffect(() => {
    if (selectedIR?.name) {
      setActiveIR(selectedIR);
    } else {
      setActiveIR(undefined);
    }
  }, [selectedIR]);

  const [filters, setFilters] = useState({
    rowsPerPage: 20,
    pageNumber: 1,
  });

  const {
    data,
    isLoading: queryLoading,
    refetch,
  } = useQuery({
    queryKey: ['configList', 'inspection_remark'],
    queryFn: async () => {
      const response = await axios.get(
        `${krakendPaths.RETURN_URL}/admin-api/v1/config`,
        {
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
          params: { type: 'inspection_remark' }, // Adding query parameters
        },
      );
      return response;
    },
    staleTime: 0,
    cacheTime: 0,
    onSuccess: (data: any) => {
      const result: IInspectionRemark[] = data?.getConfigList;
      setIRList(result);
    },
    onError: (error: any) => {
      console.error(error);
    },
  });

  useEffect(() => {
    if (activeIR) {
      const selectedObject = inspectionRemarkList.find(
        (item) =>
          item.name.trim().toLowerCase() === activeIR.trim().toLowerCase(),
      );
      if (selectedObject) {
        setSelectedIR(selectedObject);
      }
    }
  }, [inspectionRemarkList]);

  return (
    <Dropdown
      selectedItem={selectedIR}
      onSelect={(item) => {
        setSelectedIR(item);
      }}
      downshiftProps={{
        itemToString: (item: IInspectionRemark) => item && item.name,
      }}
    >
      <Field>
        <Label
          style={{
            color: baseTheme.colors.veryDarkGray,
            fontWeight: baseTheme.fontWeights.regular,
          }}
        >
          Inspection remark
        </Label>
        <Select>{selectedIR?.name || 'Select'}</Select>
      </Field>
      <Menu>
        {inspectionRemarkList
          .filter((option) => option.enable === true)
          .map((option) => (
            <Item key={option.id + option.name} value={option}>
              {option.name}
            </Item>
          ))}
        <Item key={'reset'} value={''}>
          {'RESET'}
        </Item>
      </Menu>
    </Dropdown>
  );
};

export default InspectionRemarkDropdown;
