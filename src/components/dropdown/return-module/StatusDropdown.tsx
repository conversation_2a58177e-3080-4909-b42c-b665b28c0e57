import React, { useEffect, useState } from 'react';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { IReturnDTOStatus } from '../../../types/types';
import { baseTheme } from '../../../themes/theme';
import { Message } from '@zendeskgarden/react-forms';
import { Field as FField } from '@zendeskgarden/react-forms';

const StatusDropdown = ({
  setActiveStatus,
  activeStatus,
  error,
}: {
  setActiveStatus: React.Dispatch<
    React.SetStateAction<IReturnDTOStatus | undefined>
  >;
  activeStatus?: string;
  error?: any;
}) => {
  const [status, setStatus] = useState<IReturnDTOStatus[]>([]);

  // const [filters, setFilters] = useState({
  //   rowsPerPage: 20,
  //   pageNumber: 1,
  // });
  // const {
  //   error: queryError,
  //   loading: queryLoading,
  //   data: queryData,
  //   refetch,
  // } = useQuery(GET_RETURN_DTO_STATUS, {
  //   fetchPolicy: 'network-only',
  //   variables: {
  //     ...filters,
  //   },
  //   onCompleted: (data: any) => {
  //     const result: IReturnDTOStatus[] = data.getReturnDtoStatus;
  //     setStatus(result);
  //   },
  //   onError: (error) => {
  //     console.log(error);
  //   },
  // });
  const [selectedStatus, setSelectedStatus] = useState<
    IReturnDTOStatus | undefined
  >(status.filter((s) => s.enable)[0]);

  useEffect(() => {
    // setInspectionRemark(testData);
    if (selectedStatus?.name) {
      selectedStatus && setActiveStatus(selectedStatus);
    } else {
      setActiveStatus(undefined);
    }
  }, [selectedStatus]);

  useEffect(() => {
    if (activeStatus) {
      const selectedObject = status.find(
        (item) =>
          item.name.trim().toLowerCase() === activeStatus.trim().toLowerCase(),
      );
      if (selectedObject) {
        setSelectedStatus(selectedObject);
      }
    }
  }, [status]);

  return (
    <>
      <FField>
        <Dropdown
          selectedItem={selectedStatus}
          onSelect={(item) => {
            setSelectedStatus(item);
          }}
          downshiftProps={{
            itemToString: (item: IReturnDTOStatus) => item && item.name,
          }}
        >
          <Field>
            <Label
              style={{
                color: baseTheme.colors.veryDarkGray,
                fontWeight: baseTheme.fontWeights.regular,
              }}
            >
              Status *
            </Label>
            <Select
              validation={error?.status ? 'error' : undefined}
              disabled={selectedStatus?.name === 'Closed'}
            >
              {selectedStatus?.name}
            </Select>
          </Field>
          <Menu>
            {status
              .filter((option) => option.enable)
              .map((option) => (
                <Item key={option._id} value={option}>
                  {option.name}
                </Item>
              ))}
            <Item key={'reset'} value={''}>
              {'RESET'}
            </Item>
          </Menu>
        </Dropdown>
        {error && error.status && (
          <Message validation="error">Status is required</Message>
        )}
      </FField>
    </>
  );
};

export default StatusDropdown;
