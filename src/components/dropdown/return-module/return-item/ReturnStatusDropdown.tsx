import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import React, { useEffect, useState } from 'react';
import { IReturnAction, IStatusRes } from '../../../../types/types';
import { useReturnAction, useReturnStatus } from '../../../../hooks/useQuery';
import { baseTheme } from '../../../../themes/theme';

const ReturnStatusDropdown = ({
  setActiveStatus,
  activeStatus,
  value,
  disable,
  returnStatus,
  onSelect,
}: {
  setActiveStatus: React.Dispatch<React.SetStateAction<IStatusRes | undefined>>;
  value?: string;
  disable: boolean;
  activeStatus: IStatusRes | undefined;
  returnStatus: IStatusRes[] | undefined;
  onSelect: (item: IStatusRes) => void;
}) => {
  const [status, setStatus] = useState<IStatusRes[]>();

  useEffect(() => {
    returnStatus && setStatus(returnStatus);
  }, [returnStatus]);

  useEffect(() => {
    if (value) {
      const selectedOption =
        status &&
        status.find(
          (option) => option.code == value,
        );
      setActiveStatus(selectedOption);
    }
  }, [status]);
  console.log(activeStatus);

  return (
    <Dropdown
      selectedItem={activeStatus}
      onSelect={onSelect}
      downshiftProps={{
        itemToString: (item: IStatusRes) => item && item.status,
      }}
    >
      <Field>
        <Label hidden>Status</Label>
        <Select disabled={disable}>{activeStatus?.status}</Select>
      </Field>
      <Menu>
        {status &&
          status
            .filter((option) => option.enable)
            .map((option) => (
              <Item key={option.id} value={option}>
                {option.status}
              </Item>
            ))}
      </Menu>
    </Dropdown>
  );
};

export default ReturnStatusDropdown;
