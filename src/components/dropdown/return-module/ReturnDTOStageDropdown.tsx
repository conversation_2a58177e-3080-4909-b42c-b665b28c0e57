import React, { useEffect, useState } from 'react';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { IGQL } from '../../../types/types';
import { baseTheme } from '../../../themes/theme';

const ReturnDTOStageDropdown = ({
  activeDTOStage,
  setActiveDTOStage,
  disable,
}: {
  setActiveDTOStage: React.Dispatch<React.SetStateAction<IGQL | undefined>>;
  disable?: boolean;
  activeDTOStage?: string;
}) => {
  const [selectedDTO, setSelectedDTO] = useState<IGQL>();
  const [dtoStages, setDTOStages] = useState<IGQL[]>([]);

  useEffect(() => {
    // setInspectionRemark(testData);
    if (selectedDTO?.name) {
      setActiveDTOStage(selectedDTO);
    } else {
      setActiveDTOStage(undefined);
    }
  }, [selectedDTO]);

  useEffect(() => {
    if (activeDTOStage) {
      const selectedObject = dtoStages.find(
        (item) =>
          item.name.trim().toLowerCase() ===
          activeDTOStage.trim().toLowerCase(),
      );
      if (selectedObject) {
        setSelectedDTO(selectedObject);
      }
    }
  }, [dtoStages]);

  const [filters, setFilters] = useState({
    rowsPerPage: 20,
    pageNumber: 1,
  });
  // const {
  //   error: queryError,
  //   loading: queryLoading,
  //   data: queryData,
  //   refetch,
  // } = useQuery(GET_RETURN_DTO_STAGE, {
  //   fetchPolicy: 'network-only',
  //   variables: {
  //     ...filters,
  //   },
  //   onCompleted: (data: any) => {
  //     const result: IGQL[] = data.getReturnDtoStage;
  //     // console.log('Stages', result);
  //     setDTOStages(result);
  //   },
  // });

  return (
    <>
      <Dropdown
        selectedItem={selectedDTO}
        onSelect={(item) => {
          setSelectedDTO(item);
        }}
        downshiftProps={{
          itemToString: (item: IGQL) => item && item.name,
        }}
      >
        <Field>
          <Label
            style={{
              color: baseTheme.colors.veryDarkGray,
              fontWeight: baseTheme.fontWeights.regular,
            }}
          >
            DTO Stage
          </Label>
          <Select disabled={disable}>{selectedDTO?.name}</Select>
        </Field>
        <Menu>
          {dtoStages
            .filter((option) => option.enable === true)
            .map((option) => (
              <Item key={option._id} value={option}>
                {option.name}
              </Item>
            ))}
          <Item key={'reset'} value={''}>
            {'RESET'}
          </Item>
        </Menu>
      </Dropdown>
    </>
  );
};

export default ReturnDTOStageDropdown;
