import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { ReasonTableColumn } from '../../table/return-modules/columns';
import useAxios from '../../../hooks/useAxios';
import { IReason, ISubReason } from '../../../types/types';
import routes from '../../../constants/routes';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { baseTheme } from '../../../themes/theme';

const SubReasonDropdown = ({
  activeReason,
  setActiveSubReason,
  isDisabled,
}: {
  activeReason: IReason | undefined;
  setActiveSubReason: React.Dispatch<
    React.SetStateAction<ISubReason | undefined>
  >;
  isDisabled?: boolean;
}) => {
  const [subReasonData, setSubReasonData] = useState<ISubReason[]>([]);
  const [selectedSubReason, setSelectedSubReason] = useState<any>();
  const axios = useAxios();

  useEffect(() => {
    activeReason && setSubReasonData(activeReason.children);
  }, [activeReason]);

  useEffect(() => {
    // setInspectionRemark(testData);
    if (selectedSubReason) {
      setActiveSubReason(selectedSubReason);
    }
  }, [selectedSubReason]);

  return (
    <Dropdown
      selectedItem={selectedSubReason}
      onSelect={setSelectedSubReason}
      downshiftProps={{
        itemToString: (item: any) => item && item?.reason,
      }}
    >
      <Field>
        <Label
          hidden
          style={{
            color: baseTheme.colors.veryDarkGray,
            fontWeight: baseTheme.fontWeights.regular,
          }}
        >
          Reason
        </Label>
        <Select disabled={isDisabled}>{selectedSubReason?.reason}</Select>
      </Field>
      <Menu>
        {subReasonData &&
          subReasonData
            .filter((s) => s.enable)
            .map((option) => (
              <Item key={option.id} value={option}>
                {option.reason}
              </Item>
            ))}
      </Menu>
    </Dropdown>
  );
};

export default SubReasonDropdown;
