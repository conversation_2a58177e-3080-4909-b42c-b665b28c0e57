import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import React, { useState } from 'react';
import { ITag } from '../../../types/types';

const TagDropdown = ({
  data,
  selectedItem,
  setSelectedItem,
  isDisabled,
}: {
  data: ITag[];
  isDisabled?: boolean;
  selectedItem: ITag | undefined;
  setSelectedItem: React.Dispatch<React.SetStateAction<ITag | undefined>>;
}) => {
  return (
    <>
      <Dropdown
        selectedItem={selectedItem}
        onSelect={setSelectedItem}
        downshiftProps={{
          itemToString: (item: ITag) => item && item.label,
        }}
      >
        <Field>
          <Label hidden>Tag Dropdown</Label>{' '}
          <Select disabled={isDisabled}>
            {/* {selectedItem ? selectedItem.label : 'Select Tag'} */}
            Select Tag
          </Select>
        </Field>
        <Menu>
          {data
            .filter((option) => option.enable)
            .map((option) => (
              <Item key={option.id} value={option}>
                {option.label}
              </Item>
            ))}
        </Menu>
      </Dropdown>
    </>
  );
};

export default TagDropdown;
