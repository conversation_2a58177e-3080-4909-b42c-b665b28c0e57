import React, { useEffect, useState } from 'react';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import useAxios from '../../../hooks/useAxios';
import { IResolution } from '../../../types/types';
import { Field as FField } from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';

const ResolutionDropdown = ({
  setActiveResolution,
  activeResolution,
}: {
  setActiveResolution: React.Dispatch<
    React.SetStateAction<IResolution | undefined>
  >;
  activeResolution?: string;
}) => {
  const axios = useAxios();
  const [selectedResolution, setSelectedResolution] = useState<IResolution>();
  const [resolution, setResolution] = useState<IResolution[]>([]);

  useEffect(() => {
    // setInspectionRemark(testData);
    if (selectedResolution?.name) {
      setActiveResolution(selectedResolution);
    } else {
      setActiveResolution(undefined);
    }
  }, [selectedResolution]);

  // const [filters, setFilters] = useState({
  //   rowsPerPage: 20,
  //   pageNumber: 1,
  // });
  // const {
  //   error: queryError,
  //   loading: queryLoading,
  //   data: queryData,
  //   refetch,
  // } = useQuery(GET_RESOLUTION, {
  //   fetchPolicy: 'network-only',
  //   variables: {
  //     ...filters,
  //   },
  //   onCompleted: (data: any) => {
  //     const result: IResolution[] = data.getResolution;
  //     setResolution(result);
  //   },
  // });

  useEffect(() => {
    if (activeResolution) {
      const selectedObject = resolution.find(
        (item) =>
          item.name.trim().toLowerCase() ===
          activeResolution.trim().toLowerCase(),
      );
      if (selectedObject) {
        setSelectedResolution(selectedObject);
      }
    }
  }, [resolution]);

  return (
    <>
      <FField>
        <Dropdown
          selectedItem={selectedResolution}
          onSelect={(item) => {
            setSelectedResolution(item);
          }}
          downshiftProps={{
            itemToString: (item: IResolution) => item && item.name,
          }}
        >
          <Field>
            <Label
              style={{
                color: baseTheme.colors.veryDarkGray,
                fontWeight: baseTheme.fontWeights.regular,
              }}
            >
              Resolution
            </Label>
            <Select>{selectedResolution?.name}</Select>
          </Field>
          <Menu>
            {resolution
              .filter((option) => option.enable === true)
              .map((option) => (
                <Item key={option._id} value={option}>
                  {option.name}
                </Item>
              ))}
            <Item key={'reset'} value={''}>
              {'RESET'}
            </Item>
          </Menu>
        </Dropdown>
      </FField>
    </>
  );
};

export default ResolutionDropdown;
