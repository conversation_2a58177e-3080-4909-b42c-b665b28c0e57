import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { ReasonTableColumn } from '../../table/return-modules/columns';
import useAxios from '../../../hooks/useAxios';
import { IReason } from '../../../types/types';
import routes from '../../../constants/routes';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { baseTheme } from '../../../themes/theme';

const ReasonDropdown = ({
  data,
  setActiveReason,
  isDisabled,
  currentReason,
}: {
  data: IReason[] | undefined;
  setActiveReason: React.Dispatch<React.SetStateAction<IReason | undefined>>;
  isDisabled?: boolean;
  currentReason: IReason | undefined;
}) => {
  const [reasonData, setReasonData] = useState<IReason[]>([]);
  const [selectedReason, setSelectedReason] = useState<IReason>();
  const axios = useAxios();

  useEffect(() => {
    data && setReasonData(data);
  }, [data]);

  useEffect(() => {
    if (currentReason != undefined) {
      setSelectedReason(currentReason);
    }
  }, [currentReason]);

  useEffect(() => {
    // setInspectionRemark(testData);
    if (selectedReason) {
      setActiveReason(selectedReason);
    }
  }, [selectedReason]);

  return (
    <Dropdown
      selectedItem={selectedReason}
      onSelect={setSelectedReason}
      downshiftProps={{
        itemToString: (item: IReason) => item && item.reason,
      }}
    >
      <Field>
        <Label
          hidden
          style={{
            color: baseTheme.colors.veryDarkGray,
            fontWeight: baseTheme.fontWeights.regular,
          }}
        >
          Reason
        </Label>
        <Select disabled={isDisabled}>{selectedReason?.reason}</Select>
      </Field>
      <Menu>
        {reasonData &&
          reasonData
            .filter((r) => r.enable)
            .map((option) => (
              <Item key={option.id} value={option}>
                {option.reason}
              </Item>
            ))}
      </Menu>
    </Dropdown>
  );
};

export default ReasonDropdown;
