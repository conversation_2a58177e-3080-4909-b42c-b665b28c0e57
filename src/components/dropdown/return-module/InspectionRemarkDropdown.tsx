import React, { useEffect, useState } from 'react';
import useAxios from '../../../hooks/useAxios';
import { IInspectionRemark } from '../../../types/types';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { baseTheme } from '../../../themes/theme';

const InspectionRemarkDropdown = ({
  setActiveIR,
  activeIR,
}: {
  setActiveIR: React.Dispatch<
    React.SetStateAction<IInspectionRemark | undefined>
  >;
  activeIR: string;
}) => {
  const [selectedIR, setSelectedIR] = useState<IInspectionRemark>();
  const [inspectionRemarkList, setIRList] = useState<IInspectionRemark[]>([]);

  const axios = useAxios();

  useEffect(() => {
    if (selectedIR?.name) {
      setActiveIR(selectedIR);
    } else {
      setActiveIR(undefined);
    }
  }, [selectedIR]);

  // const [filters, setFilters] = useState({
  //   rowsPerPage: 20,
  //   pageNumber: 1,
  // });
  // const {
  //   error: queryError,
  //   loading: queryLoading,
  //   data: queryData,
  //   refetch,
  // } = useQuery(GET_INSPECTION_REMARK, {
  //   fetchPolicy: 'network-only',
  //   variables: {
  //     ...filters,
  //   },
  //   onCompleted: (data: any) => {
  //     const result: IInspectionRemark[] = data.getInspectionRemark;
  //     setIRList(result);
  //   },
  //   onError: (error) => {
  //     console.log(error);
  //   },
  // });

  useEffect(() => {
    if (activeIR) {
      const selectedObject = inspectionRemarkList.find(
        (item) =>
          item.name.trim().toLowerCase() === activeIR.trim().toLowerCase(),
      );
      if (selectedObject) {
        setSelectedIR(selectedObject);
      }
    }
  }, [inspectionRemarkList]);

  return (
    <Dropdown
      selectedItem={selectedIR}
      onSelect={(item) => {
        setSelectedIR(item);
      }}
      downshiftProps={{
        itemToString: (item: IInspectionRemark) => item && item.name,
      }}
    >
      <Field>
        <Label
          style={{
            color: baseTheme.colors.veryDarkGray,
            fontWeight: baseTheme.fontWeights.regular,
          }}
        >
          Inspection remark
        </Label>
        <Select>{selectedIR?.name}</Select>
      </Field>
      <Menu>
        {inspectionRemarkList
          .filter((option) => option.enable === true)
          .map((option) => (
            <Item key={option._id} value={option}>
              {option.name}
            </Item>
          ))}
        <Item key={'reset'} value={''}>
          {'RESET'}
        </Item>
      </Menu>
    </Dropdown>
  );
};

export default InspectionRemarkDropdown;
