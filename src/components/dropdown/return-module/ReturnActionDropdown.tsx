import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { ReasonTableColumn } from '../../table/return-modules/columns';
import useAxios from '../../../hooks/useAxios';
import { IReason, IReturnAction } from '../../../types/types';
import routes from '../../../constants/routes';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { baseTheme } from '../../../themes/theme';

const ReturnActionDropdown = ({
  data,
  setActiveReturnAction,
  isDisabled,
}: {
  data: IReturnAction[] | undefined;
  setActiveReturnAction: React.Dispatch<
    React.SetStateAction<IReturnAction | undefined>
  >;
  isDisabled?: boolean;
}) => {
  const [returnActionData, setReturnActionData] = useState<IReturnAction[]>([]);
  const [selectedAction, setSelectedAction] = useState<IReturnAction>();

  useEffect(() => {
    data && setReturnActionData(data);
  }, [data]);

  useEffect(() => {
    // setInspectionRemark(testData);
    if (selectedAction) {
      setActiveReturnAction(selectedAction);
    }
  }, [selectedAction]);

  return (
    <Dropdown
      selectedItem={selectedAction}
      onSelect={setSelectedAction}
      downshiftProps={{
        itemToString: (item: IReturnAction) => item && item.action,
      }}
    >
      <Field>
        <Label
          style={{
            color: baseTheme.colors.veryDarkGray,
            fontWeight: baseTheme.fontWeights.regular,
          }}
          hidden
        >
          Return Action
        </Label>
        <Select disabled={isDisabled}>{selectedAction?.action}</Select>
      </Field>
      <Menu>
        {returnActionData &&
          returnActionData
            .filter((r) => r.enable)
            .map((option) => (
              <Item key={option.id} value={option}>
                {option.action}
              </Item>
            ))}
      </Menu>
    </Dropdown>
  );
};

export default ReturnActionDropdown;
