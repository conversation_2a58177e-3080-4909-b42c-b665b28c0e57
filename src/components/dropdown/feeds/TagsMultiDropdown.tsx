import React, { useEffect, useRef, useState } from 'react';
import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Multiselect,
} from '@zendeskgarden/react-dropdowns';
import { Tag } from '@zendeskgarden/react-tags';
import { debounce } from 'lodash';
import { IFeedsCategory } from '../../../pages/feeds/FeedsCategory';
import { IFeeds } from '../../../pages/feeds/FeedsVideos';
// import { IFeedsCategory } from '../../modal/feeds/videos/AddVideoModal';

const TagsMultiDropdown = ({
  selectedTags,
  tagsData,
  setSelectedTags,
}: {
  selectedTags?: IFeedsCategory[];
  tagsData: IFeedsCategory[];
  setSelectedTags: React.Dispatch<React.SetStateAction<IFeedsCategory[]>>;
}) => {
  //   const [selectedItems, setSelectedItems] = useState<IFeedsCategory[]>([]);
  const [currentTags, setCurrentTags] = useState<IFeedsCategory[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [matchingOptions, setMatchingOptions] =
    useState<IFeedsCategory[]>(tagsData);

  useEffect(() => {
    if (selectedTags && selectedTags.length != 0) {
      setCurrentTags(selectedTags);
    }
  }, [selectedTags]);

  const filterMatchingOptionsRef = useRef(
    debounce((value: string) => {
      const matchedOptions = tagsData.filter((option) => {
        return (
          option.category_key
            .trim()
            .toLowerCase()
            .indexOf(value.trim().toLowerCase()) !== -1
        );
      });

      setMatchingOptions(matchedOptions);
      setIsLoading(false);
    }, 300),
  );

  useEffect(() => {
    if (currentTags) {
      setSelectedTags(currentTags);
    }
  }, [currentTags]);

  useEffect(() => {
    setIsLoading(true);
    filterMatchingOptionsRef.current(inputValue);
  }, [inputValue]);

  const renderOptions = () => {
    if (isLoading) {
      return <Item disabled>Loading</Item>;
    }

    if (matchingOptions.length === 0) {
      return <Item disabled>No Items found</Item>;
    }

    return matchingOptions.map((option) => (
      <Item key={option.id} value={option}>
        <span>{option.category_key}</span>
      </Item>
    ));
  };

  const handleSelect = (items: IFeedsCategory[]) => {
    setCurrentTags(items);
  };

  return (
    <>
      <Dropdown
        inputValue={inputValue}
        selectedItems={currentTags}
        onSelect={handleSelect}
        downshiftProps={{
          itemToString: (tag: IFeedsCategory) => tag && tag.category_key,
          defaultHighlightedIndex: 0,
        }}
        onInputValueChange={(value) => setInputValue(value)}
      >
        <Field>
          <Label>Select Tags *</Label>
          <Multiselect
            renderItem={({ value, removeValue }: any) => (
              <Tag>
                <span>{value.category_key}</span>
                <Tag.Close onClick={() => removeValue()} />
              </Tag>
            )}
          />
        </Field>
        <Menu>{renderOptions()}</Menu>
      </Dropdown>
    </>
  );
};

export default TagsMultiDropdown;
