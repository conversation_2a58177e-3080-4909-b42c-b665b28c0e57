import { useQuery } from '@apollo/client';
import React, { useEffect, useState } from 'react';
import meteorClient from '../../../apollo-client/ApolloClient';
import { GET_COUNTRY_STATE } from '../../../graphql';
import useToast from '../../../hooks/useToast';
import { Col, Row } from '../../UI-components/Grid';
import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select as _Select,
} from '@zendeskgarden/react-dropdowns';
import { baseTheme } from '../../../themes/theme';
import styled from 'styled-components';
import constants from '../../../constants';

interface IItem {
  label: string;
  value: string;
  country_id: string;
}

interface IItemState {
  label: string;
  value: string;
  region_id: number;
}
const items = [{ label: 'Select Country', value: '' }];
const itemsState = [{ label: 'Select State', value: '' }];

const Select = styled(_Select)<{ inFilter?: boolean }>`
  ${(p) =>
    p.inFilter
      ? ``
      : `background: rgba(32, 83, 117, 0.05); color: ${baseTheme.colors.deepBlue};
  border: 1px solid #20537580;`}
`;

const StateDropdown = ({
  setcurrentCountry,
  setcurrentState,
  labelHidden,
  isVertical,
  inFilter,
  filters,
  setValidationRules,
  currentCountry,
  currentState,
}: {
  currentCountry?: any;
  currentState?: any;
  setcurrentCountry: React.Dispatch<React.SetStateAction<any>>;
  setcurrentState: React.Dispatch<React.SetStateAction<any>>;
  labelHidden: boolean;
  isVertical?: boolean;
  inFilter?: boolean;
  filters?: any;
  setValidationRules?: any;
}) => {
  const addToast = useToast();
  const [selectedCountry, setselectedCountry] = useState(items[0]);
  const [selectedState, setselectedState] = useState(itemsState[0]);

  useEffect(() => {
    if (currentCountry) {
      setselectedCountry(currentCountry);
    }

    if (currentState) {
      setselectedState(currentState);
    }
  }, [currentCountry, currentState]);

  const { data: categoryList } = useQuery(GET_COUNTRY_STATE, {
    context: {
      headers: {
        'x-api-key': constants.API_KEY,
      },
    },
    fetchPolicy: 'network-only',
    client: meteorClient,
    onCompleted: (data) => {
      console.log('Data', data.countries);
    },
    onError: ({
      graphQLErrors,
      networkError,
    }: {
      graphQLErrors: any;
      networkError: any;
    }) => {
      if (networkError) {
        addToast('error', networkError);
      }
      if (graphQLErrors) {
        addToast('error', graphQLErrors[0].message);
        if (graphQLErrors[0].message === 'Unauthorized') {
          addToast('error', 'Session Time Out');
        }
      }
    },
  });

  const [currentCountyID, setCurrentCountryID] = useState<any>(undefined);
  const [filtersValidation, setFilterValidation] = useState<any>({
    country_id: currentCountyID,
  });

  // const { data: validationRules, refetch: refetchValidationRules } = useQuery(
  //   GET_VALIDATION_RULES,
  //   {
  //     fetchPolicy: 'network-only',
  //     client: meteorClient,
  //     variables: {
  //       ...filtersValidation,
  //     },
  //     skip: currentCountyID == undefined,
  //     onCompleted: (data) => {
  //       setValidationRules(data.getValidationRules);
  //     },
  //     onError: (err) => {
  //       console.log('Error', err);
  //     },
  //   },
  // );

  useEffect(() => {
    if (currentCountyID) {
      setFilterValidation({
        country_id: currentCountyID,
      });
    }
  }, [currentCountyID]);

  const countryItems: IItem[] = categoryList
    ? categoryList.countries
        .map((country: any) => ({
          label: country.full_name_english,
          value: country.three_letter_abbreviation,
          country_id: country.id,
        }))
        .sort((a: any, b: any) => a.label.localeCompare(b.label))
    : [];

  const stateItems: IItemState[] =
    selectedCountry &&
    categoryList &&
    categoryList.countries?.find(
      (country: any) =>
        country.three_letter_abbreviation === selectedCountry.value,
    )
      ? categoryList.countries
          ?.find(
            (country: any) =>
              country.three_letter_abbreviation === selectedCountry.value,
          )
          ?.available_regions?.map((region: any) => ({
            label: region.name,
            value: region.code,
            region_id: region.id,
          })) || []
      : [];

  const handleSelectedCountry = (item: IItem) => {
    setselectedCountry(item);
    setcurrentCountry(item);
    if (setValidationRules) {
      setCurrentCountryID(item.country_id);
    }
  };
  const handleSelectedState = (item: IItemState) => {
    setselectedState(item);
    setcurrentState(item);
  };

  return (
    <>
      <Row>
        <Col size={isVertical ? 12 : 6}>
          <Dropdown
            selectedItem={selectedCountry}
            onSelect={(item) => {
              handleSelectedCountry(item);
            }}
            downshiftProps={{
              itemToString: (item: IItem) => item && item.label,
            }}
          >
            <Field>
              <Label hidden={labelHidden}>Country</Label>
              <Select inFilter={inFilter}>{selectedCountry.label}</Select>
            </Field>
            <Menu>
              {countryItems.map((option) => (
                <Item key={option.value} value={option}>
                  {option.label}
                </Item>
              ))}
            </Menu>
          </Dropdown>
        </Col>
        <Col
          style={{ marginTop: inFilter ? baseTheme.space.sm : 0 }}
          size={isVertical ? 12 : 6}
        >
          <Dropdown
            selectedItem={selectedState}
            onSelect={(item) => {
              handleSelectedState(item);
            }}
            downshiftProps={{
              itemToString: (item: IItem) => item && item.label,
            }}
          >
            <Field>
              <Label hidden={labelHidden}>State</Label>
              <Select inFilter={inFilter}>{selectedState.label}</Select>
            </Field>
            <Menu>
              {stateItems.map((option) => (
                <Item key={option.value} value={option}>
                  {option.label}
                </Item>
              ))}
            </Menu>
          </Dropdown>
        </Col>
      </Row>
    </>
  );
};

export default StateDropdown;
