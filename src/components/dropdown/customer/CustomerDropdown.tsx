import React, { useState } from 'react';
import styled from 'styled-components';
import { DropdownDownIcon, DropdownUpIcon } from '../../../utils/icons';
import { baseTheme } from '../../../themes/theme';

interface SelectIconProps {
  expanded: boolean;
}

const CustomSelectContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const Select = styled.select`
  appearance: none;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  cursor: pointer;
  min-width: ${baseTheme.components.dimension.width.base100};
  max-width: ${baseTheme.components.dimension.width.base500};
  background-color: rgba(32, 83, 117, 0.05);
  color: ${baseTheme.colors.deepBlue};
  transition: background 0.2s ease;

  &:focus {
    background: white;
  }
`;

const Option = styled.option`
  background-color: ${baseTheme.colors.deepBlue};
  color: ${baseTheme.colors.textColorGrey};
  transition: background 0.2s ease, color 0.2s ease;
  &:hover {
    background-color: ${baseTheme.colors.deepBlue};
    color: white;
  }
  &:focus {
    background-color: ${baseTheme.colors.deepBlue};
    color: white;
  }
`;

const SelectIcon = styled.div<SelectIconProps>`
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;
`;

const CustomSelect = ({
  selectedItem,
  setSelectedItem,
  items,
}: {
  selectedItem: any;
  setSelectedItem: React.Dispatch<React.SetStateAction<any>>;
  items: any[];
}) => {
  const [expanded, setExpanded] = useState(false);

  const toggleSelect = () => {
    setExpanded(!expanded);
  };

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = event.target.value;
    const selectedItem = items.find((item) => item.value === selectedValue);
    setSelectedItem(selectedItem);
  };

  return (
    <CustomSelectContainer>
      <Select
        value={selectedItem?.value}
        onChange={handleSelectChange}
        onClick={toggleSelect}
      >
        {items.map((item) => (
          <>
            <Option value={item.value}>{item.label}</Option>
          </>
        ))}
      </Select>
      <SelectIcon expanded={expanded}>
        {expanded ? <DropdownUpIcon /> : <DropdownDownIcon />}
      </SelectIcon>
    </CustomSelectContainer>
  );
};

export default CustomSelect;
