import React from 'react';
import {
  Dropdown,
  Select as _Select,
  Item,
  Menu,
  Field as _DField,
} from '@zendeskgarden/react-dropdowns';
import styled from 'styled-components';
import { baseTheme } from '../../../themes/theme';

const Select = styled(_Select)`
  background: rgba(32, 83, 117, 0.05);
  color: ${baseTheme.colors.deepBlue};
`;

const DField = styled(_DField)``;

const CustomerBaseDropdown = ({
  selectedItem,
  setSelectedItem,
  items,
}: {
  selectedItem: any;
  setSelectedItem: React.Dispatch<React.SetStateAction<any>>;
  items: any[];
}) => {
  return (
    <>
      <Dropdown
        selectedItem={selectedItem}
        onSelect={setSelectedItem}
        downshiftProps={{
          itemToString: (item: any) => item && item.label,
        }}
      >
        <DField>
          <Select>{selectedItem.label} |</Select>
        </DField>
        <Menu>
          {items.map((option) => (
            <Item key={option.value} value={option}>
              {option.label}
            </Item>
          ))}
        </Menu>
      </Dropdown>
    </>
  );
};

export default CustomerBaseDropdown;
