import React, { useState } from 'react';
import styled from 'styled-components';

interface CustomDropdownProps {
  items: string[];
  onItemClick: (item: string) => void;
  dropdownSize?: string;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  items,
  onItemClick,
  dropdownSize,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleItemClick = (item: string) => {
    onItemClick(item);
    setIsOpen(false); // Close the dropdown after item selection
  };

  return (
    <DropdownContainer>
      <DropdownToggle onClick={toggleDropdown}>
        Click here to open dropdown
      </DropdownToggle>
      {isOpen && (
        <DropdownMenu size={dropdownSize}>
          {items.map((item, index) => (
            <DropdownItem key={index} onClick={() => handleItemClick(item)}>
              {item}
            </DropdownItem>
          ))}
        </DropdownMenu>
      )}
    </DropdownContainer>
  );
};

const DropdownContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const DropdownToggle = styled.div`
  cursor: pointer;
`;

const DropdownMenu = styled.ul<{ size?: string }>`
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 999; /* Ensure dropdown appears above other elements */
  width: ${({ size }) => size || '200px'}; /* Set custom width for the dropdown */
  padding: 0;
  margin: 0;
  background-color: #fff;
  border: 1px solid #ccc;
  list-style-type: none;
`;

const DropdownItem = styled.li`
  padding: 8px 12px;
  cursor: pointer;

  &:hover {
    background-color: #f2f2f2;
  }
`;

export default CustomDropdown;
