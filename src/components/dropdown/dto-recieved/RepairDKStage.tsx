import React from 'react';
import GET_REPAIR_DK_STAGE from '../../../graphql/queries/getRepairDkStage.gql';
import { ConfigOutput } from '../../../gql/graphql';
import { DocumentNode } from 'graphql';
import Dropdown from './Dropdown';

const RepairDKStage = ({
  stage,
  setStage,
}: {
  stage: ConfigOutput | undefined;
  setStage: React.Dispatch<React.SetStateAction<ConfigOutput | undefined>>;
}) => {
  return (
    <Dropdown
      state={stage}
      setState={setStage}
      query={GET_REPAIR_DK_STAGE}
      resultKey="getRepairDkStage"
      label="Post Repair Solution"
    />
  );
};

export default RepairDKStage;
