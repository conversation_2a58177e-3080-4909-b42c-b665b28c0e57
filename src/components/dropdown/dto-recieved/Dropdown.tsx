import React, { useEffect, useState } from 'react';
import {
  Dropdown as _Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { useQuery } from '@apollo/client';
import { baseTheme } from '../../../themes/theme';
import { ConfigOutput } from '../../../gql/graphql';
import { convertToSentenceCase } from '../../../utils/convertToSentenceCase';
import { Field as FField } from '@zendeskgarden/react-forms';
import constants from '../../../constants';

const Dropdown = ({
  state,
  setState,
  query,
  resultKey,
  label,
}: {
  state: ConfigOutput | undefined;
  setState: React.Dispatch<React.SetStateAction<ConfigOutput | undefined>>;
  query: any;
  resultKey: string;
  label: string;
}) => {
  const [list, setlist] = useState<ConfigOutput[]>([]);
  const [selectedItem, setSelectedItem] = useState<ConfigOutput>();

  const { data } = useQuery(query, {
    fetchPolicy: 'network-only',
    context: {
      headers: {
        'x-api-key': constants.API_KEY,
      },
    },
    variables: {},
    onCompleted: (data: any) => {
      const result: ConfigOutput[] = data[resultKey];
      setlist(result);
    },
    onError: (error) => {
      console.log(error);
    },
  });

  useEffect(() => {
    if (data) {
      const result: ConfigOutput[] = data[resultKey];
      setlist(result);
    }
  }, [data]);

  return (
    <>
      <FField>
        <_Dropdown
          selectedItem={selectedItem}
          onSelect={setState}
          downshiftProps={{
            itemToString: (item: ConfigOutput) => item && item.name,
          }}
        >
          <Field>
            <Label
              style={{
                color: baseTheme.colors.veryDarkGray,
                fontWeight: baseTheme.fontWeights.regular,
              }}
            >
              {label}
            </Label>
            <Select>{state?.name ? state.name : ''}</Select>
          </Field>
          <Menu>
            {list &&
              list.length != 0 &&
              list
                .filter((option) => option.enable)
                .map((option) => (
                  <Item key={option.name} value={option}>
                    {convertToSentenceCase(option.name)}
                  </Item>
                ))}
            <Item key={'reset'} value={''}>
              {'RESET'}
            </Item>
          </Menu>
        </_Dropdown>
      </FField>
    </>
  );
};

export default Dropdown;
