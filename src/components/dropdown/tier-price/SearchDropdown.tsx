import { Dropdown, Item, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import React, { useState } from 'react';
import { Button } from '../../UI-components/Button';
import { DownIcon } from '../../../utils/icons';
import { baseTheme } from '../../../themes/theme';

const SearchTierDropdown = ({
  searchType,
  setSearchType,
}: {
  searchType: 'name' | 'id' | 'sku';
  setSearchType: React.Dispatch<React.SetStateAction<'name' | 'id' | 'sku'>>;
}) => {
  const [rotatedType, setRotatedType] = useState<boolean>();

  return (
    <>
      <Dropdown
        onSelect={(item) => setSearchType(item)}
        onStateChange={(options) =>
          Object.hasOwn(options, 'isOpen') && setRotatedType(options.isOpen)
        }
      >
        <Trigger>
          <Button isPrimary>
            {searchType
              ? searchType == 'name'
                ? 'Name'
                : searchType === 'id'
                ? 'ID'
                : searchType === 'sku'
                ? 'SKU'
                : 'Select'
              : 'Select'}
            <Button.EndIcon isRotated={rotatedType}>
              <DownIcon
                style={{
                  height: baseTheme.iconSizes.md,
                  width: baseTheme.iconSizes.md,
                }}
              />
            </Button.EndIcon>
          </Button>
        </Trigger>
        <Menu
          style={{
            width: baseTheme.components.dimension.width.base200,
            transform: 'translateX(4px)',
            borderRadius: baseTheme.borderRadii.lg,
          }}
        >
          <>
            <Item value="name">By Name</Item>
            <Item value="id">By Id</Item>
            <Item value="sku">By SKU</Item>
          </>
        </Menu>
      </Dropdown>
    </>
  );
};

export default SearchTierDropdown;
