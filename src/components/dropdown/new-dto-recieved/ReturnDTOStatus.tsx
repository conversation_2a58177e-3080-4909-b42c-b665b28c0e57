import React, { useEffect, useState } from 'react';
import { ConfigOutput } from '../../../gql/graphql';
import Dropdown from './Dropdown';
import GET_INVENTORY_TEAM_STAGE from '../../../graphql/queries/getInventoryTeamStage.gql';

const ReturnDTOStatus = ({
  status,
  setStatus,
}: {
  status: ConfigOutput | undefined;
  setStatus: React.Dispatch<React.SetStateAction<ConfigOutput | undefined>>;
}) => {
  return (
    <Dropdown
      state={status}
      setState={setStatus}
      query={GET_INVENTORY_TEAM_STAGE}
      resultKey="getInventoryTeamStage"
      label="Next Stage"
    />
  );
};

export default ReturnDTOStatus;
