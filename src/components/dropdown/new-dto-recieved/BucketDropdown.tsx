import GET_DTO_RECIEVED_BUCKET from '../../../graphql/queries/getDtoRecievedBucket';

import React from 'react';
import { ConfigOutput } from '../../../gql/graphql';
import Dropdown from './Dropdown';

const BucketDropdown = ({
  bucket,
  setBucket,
}: {
  bucket: ConfigOutput | undefined;
  setBucket: React.Dispatch<React.SetStateAction<ConfigOutput | undefined>>;
}) => {
  return (
    <Dropdown
      state={bucket}
      setState={setBucket}
      query={GET_DTO_RECIEVED_BUCKET}
      resultKey="getDtoRecievedBucket"
      label="Bucket"
    />
  );
};

export default BucketDropdown;
