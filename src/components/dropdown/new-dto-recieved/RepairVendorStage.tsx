import React from 'react';
import { ConfigOutput } from '../../../gql/graphql';
import { DocumentNode } from 'graphql';
import Dropdown from './Dropdown';
import GET_REPAIR_VENDOR_STAGE from '../../../graphql/queries/getRepairVendorStage.gql';

const RepairVendorStage = ({
  stage,
  setStage,
}: {
  stage: ConfigOutput | undefined;
  setStage: React.Dispatch<React.SetStateAction<ConfigOutput | undefined>>;
}) => {
  return (
    <Dropdown
      state={stage}
      setState={setStage}
      query={GET_REPAIR_VENDOR_STAGE}
      resultKey="getRepairVendorStage"
      label="Post Repair Resolution"
    />
  );
};

export default RepairVendorStage;
