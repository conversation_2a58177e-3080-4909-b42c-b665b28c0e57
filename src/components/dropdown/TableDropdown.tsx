import {
  Dropdown,
  Field,
  Item,
  Label,
  Menu,
  Select,
} from '@zendeskgarden/react-dropdowns';
import React, { useState } from 'react';
import { ITableDropdown } from '../../types/types';

const TableDropdown = ({
  data,
  selectedItem,
  setSelectedItem,
  isDisabled,
}: {
  data: ITableDropdown[];
  isDisabled?: boolean;
  selectedItem: ITableDropdown;
  setSelectedItem: React.Dispatch<React.SetStateAction<ITableDropdown>>;
}) => {
  //const [selectedItem, setSelectedItem] = useState(data[0]);

  return (
    <>
      <Dropdown
        selectedItem={selectedItem}
        onSelect={setSelectedItem}
        downshiftProps={{
          itemToString: (item: ITableDropdown) => item && item.label,
        }}
      >
        <Field>
          <Label hidden>Table Dropdown</Label>{' '}
          <Select disabled={isDisabled}>{selectedItem.label}</Select>
        </Field>
        <Menu>
          {data.map((option) => (
            <Item key={option.value} value={option}>
              {option.label}
            </Item>
          ))}
        </Menu>
      </Dropdown>
    </>
  );
};

export default TableDropdown;
