/**
 * Copyright Zendesk, Inc.
 *
 * Use of this source code is governed under the Apache License, Version 2.0
 * found at http://www.apache.org/licenses/LICENSE-2.0.
 */

import React, { useState } from 'react';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { Row, Col } from '@zendeskgarden/react-grid';

const items = ['8870FF'];

const TextColorDropdown = ({
  value,
  onChange,
}: {
  value?: string;
  onChange: React.Dispatch<React.SetStateAction<string | undefined>>;
}) => {
  return (
    <Dropdown
      selectedItem={value}
      onSelect={(v) => {
        onChange(v);
      }}
      downshiftProps={{ itemToString: (item: string) => item }}
    >
      <Field>
        <Label>Text Color</Label>
        <Select disabled placeholder="Select Color">
          {value}
        </Select>
      </Field>
      <Menu>
        {items.map((option) => (
          <Item key={option} value={option}>
            <Row alignItems="center">
              <span
                style={{
                  backgroundColor: `#${option}`,
                  height: '30px',
                  width: '30px',
                }}
              ></span>
              <span>{option}</span>
            </Row>
          </Item>
        ))}
      </Menu>
    </Dropdown>
  );
};

export default TextColorDropdown;
