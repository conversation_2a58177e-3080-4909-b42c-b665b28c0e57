import React, { useState, useEffect } from 'react';
import {
  Dropdown,
  Multiselect,
  Field,
  Menu,
  Item,
  Label,
} from '@zendeskgarden/react-dropdowns';
import { Tag } from '@zendeskgarden/react-tags';
import { baseTheme } from '../../../themes/theme';

const DEVICES = ['web', 'android', 'ios', 'tab'];
const DEVICES_MAP = DEVICES.reduce(
  (acc, item) => ({ ...acc, [item]: true }),
  {},
);
const ALL_DEVICES = DEVICES.join(',');

const DeviceTypeDropdown = ({
  value,
  onChange,
}: {
  value?: string;
  onChange: React.Dispatch<React.SetStateAction<string | undefined>>;
}) => {
  const [selectedDevices, setSelectedDevices] = useState<string[]>([]);

  useEffect(() => {
    if (value) {
      setSelectedDevices(value.split(','));
    }
  }, [value]);
  const handleSelectionChange = (devices: string[]) => {
    if (devices.includes('All')) {
      onChange(ALL_DEVICES);
      setSelectedDevices(DEVICES);
    } else {
      const newValue = devices.join(',');
      onChange(newValue);
      setSelectedDevices(devices);
    }
  };

  return (
    <>
      <Dropdown
        selectedItems={selectedDevices}
        onSelect={(items) => handleSelectionChange(items)}
        downshiftProps={{ defaultHighlightedIndex: 0 }}
      >
        <Field>
          <Label style={{ color: baseTheme.colors.primaryHue }}>
            Active Devices{' '}
            <span style={{ color: baseTheme.colors.deepRed }}> *</span>
          </Label>
          <Multiselect
            renderItem={({ value, removeValue }: any) => (
              <Tag>
                <span>{value}</span>
                <Tag.Close onClick={() => removeValue()} />
              </Tag>
            )}
          />
        </Field>
        <Menu>
          <Item key="All" value="All">
            All
          </Item>
          {DEVICES.map((device) => (
            <Item key={device} value={device}>
              {device}
            </Item>
          ))}
        </Menu>
      </Dropdown>
    </>
  );
};

export default DeviceTypeDropdown;
