import {
  <PERSON>,
  Close,
  Footer,
  <PERSON>er<PERSON><PERSON>,
  Header,
} from '@zendeskgarden/react-modals';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Spinner } from '@zendeskgarden/react-loaders';
import useToast from '../../../hooks/useToast';
import useAxios from '../../../hooks/useAxios';
import { SModal } from '../../UI-components/Modal';
import { baseTheme } from '../../../themes/theme';
import { Button } from '../../UI-components/Button';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const DeleteSectionElementModal = ({
  close,
  elementId,
  refetch,
}: {
  close: () => void;
  elementId: number;
  refetch: () => void;
}) => {
  const addToast = useToast();
  const queryClient = useQueryClient();
  const axios = useAxios();

  const { mutate: deleteSectionElement, isLoading: isDeleting } = useMutation(
    async (id: number) => {
      const response = await axios.delete(
        `${krakendPaths.INTERFACE_URL}/admin-api/v1/section-element/${id}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    {
      onError: (err: any) => {
        addToast('error', err.message);
      },
      onSuccess: () => {
        refetch(); // Refresh the data
        addToast('success', 'Brand Deleted Successfully');
        close(); // Close the modal after deletion
      },
    },
  );

  const handleDelete = () => {
    deleteSectionElement(elementId);
  };

  return (
    <>
      <SModal
        isLarge
        style={{ overflowX: 'hidden' }}
        onClose={() => close()}
        isAnimated
      >
        <Header
          style={{
            backgroundColor: baseTheme.colors.deepBlue,
            color: baseTheme.colors.white,
          }}
          tag="h2"
        >
          Remove Section Element
        </Header>
        <Body>Are you sure you want to remove this section element?</Body>
        <Footer>
          <FooterItem>
            <Button onClick={() => close()} type="submit" isPrimary>
              Cancel
            </Button>
          </FooterItem>
          <FooterItem>
            <Button
              disabled={isDeleting}
              onClick={handleDelete}
              type="submit"
              isPrimary
            >
              {isDeleting ? <Spinner /> : 'Yes'}
            </Button>
          </FooterItem>
        </Footer>
        <Close style={{ color: 'white' }} aria-label="Close modal" />
      </SModal>
    </>
  );
};

export default DeleteSectionElementModal;
