import { ApolloClient, InMemoryCache } from '@apollo/client';
import { RETURN_BASE } from '../../env';

const returnClient = new ApolloClient({
  headers: {
    // authorization: `Bearer ${constants.TOKEN}`,
    'x-api-key': 'da2-b7vqajjrfbgbvjf4fbesbavuhq',
  },
  cache: new InMemoryCache({
    addTypename: false,
  }),
  uri: `${RETURN_BASE}/graphql`,
  // uri: `http://localhost:3000/graphql`,
});

export default returnClient;
