import { ApolloClient, InMemoryCache } from '@apollo/client';

// import { BrowserPersistence} from '@react-projects/shared';

// const storage = new BrowserPersistence();

const meteorClient = new ApolloClient({
  headers: {
    // authorization: `Bearer ${constants.TOKEN}`,
    'x-api-key': 'da2-b7vqajjrfbgbvjf4fbesbavuhq',
  },
  cache: new InMemoryCache({
    addTypename: false,
  }),
  uri: 'https://api-apollo.dentalkart.com/graphql',
  // uri:'/graphql',
  // uri:'http://zxcvbnm4378456.dentalkart.com/graphql'
  // uri:'http://13.233.109.211:3000/graphql'
});

export default meteorClient;
