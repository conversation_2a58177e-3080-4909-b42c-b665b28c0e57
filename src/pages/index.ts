import { lazy } from 'react';

export const Home = lazy(() => import('./Home'));
export const Brand = lazy(() => import('./catalog-service/brand/Brand'));
export const AddBrand = lazy(
  () => import('../components/layouts/catalog-service/brand/AddBrand'),
);
export const CustomerCare = lazy(() => import('./customerCare/CustomerCare'));
export const NetPromotorScore = lazy(
  () => import('./netPromoterScore/NetPromoterScore'),
);
export const CallCustomer = lazy(() => import('./call/CallCustomer'));
export const CallConfig = lazy(() => import('./call/Config'));
export const TierPricing = lazy(() => import('./tier-pricing/TierPricing'));

export const CategoryCatalog = lazy(
  () => import('./categoryCatalog/CategoryCatalog'),
);

// OLD RETURN SECTION
export const Returns = lazy(() => import('./return-module/Returns'));
export const ReturnCreate = lazy(() => import('./return-module/ReturnCreate'));
export const ReturnDTO = lazy(() => import('./return-module/ReturnDTO'));
export const ReturnApproved = lazy(
  () => import('./return-module/ReturnApproved'),
);
export const ReturnConfig = lazy(() => import('./return-module/ReturnConfig'));
export const NonReturnable = lazy(
  () => import('./return-module/NonReturnable'),
);

export const ReturnItem = lazy(
  () => import('../components/layouts/return-module/ReturnItemRequest/index'),
);

export const InventoryTeam = lazy(() => import('./dto-recieved/InventoryTeam'));
export const Redispatch = lazy(() => import('./dto-recieved/Redispatch'));
export const Refund = lazy(() => import('./dto-recieved/Refund'));
export const RepairDK = lazy(() => import('./dto-recieved/RepairDK'));
export const RepairReplacementVendor = lazy(
  () => import('./dto-recieved/RepairReplacementVendor'),
);
export const DTORecievedConfig = lazy(
  () => import('./dto-recieved/DTORecievedConfig'),
);

// NEW RETURN SECTION
export const NewReturns = lazy(() => import('./new-return-module/Returns'));
export const NewReturnCreate = lazy(
  () => import('./new-return-module/ReturnCreate'),
);
export const NewReturnDTO = lazy(() => import('./new-return-module/ReturnDTO'));
export const ExtraMaterialReceived = lazy(
  () => import('./new-return-module/ExtraMaterialReceived'),
);
export const NewReturnApproved = lazy(
  () => import('./new-return-module/ReturnApproved'),
);
export const NewReturnConfig = lazy(
  () => import('./new-return-module/ReturnConfig'),
);
export const NewNonReturnable = lazy(
  () => import('./new-return-module/NonReturnable'),
);

export const NewFailedPickup = lazy(
  () => import('./new-return-module/FailedPickup'),
);
export const NewReturnItem = lazy(
  () =>
    import('../components/layouts/new-return-module/ReturnItemRequest/index'),
);

export const NewInventoryTeam = lazy(
  () => import('./new-dto-recieved/InventoryTeam'),
);
export const NewRedispatch = lazy(
  () => import('./new-dto-recieved/Redispatch'),
);
export const NewRefund = lazy(() => import('./new-dto-recieved/Refund'));
export const Compensation = lazy(
  () => import('./new-return-module/Compensation'),
);
export const NewRepairDK = lazy(() => import('./new-dto-recieved/RepairDK'));
export const NewRepairReplacementVendor = lazy(
  () => import('./new-dto-recieved/RepairReplacementVendor'),
);
export const NewDTORecievedConfig = lazy(
  () => import('./new-dto-recieved/DTORecievedConfig'),
);

export const LowerPrice = lazy(() => import('./feedback/LowerPrice'));
export const ProductFeedback = lazy(() => import('./feedback/PorductFeedback'));
export const ProductSuggestion = lazy(
  () => import('./feedback/ProductSuggestion'),
);
export const CreateForm = lazy(() => import('./generic-form/CreateForm'));
export const ViewForm = lazy(() => import('./generic-form/ViewForm'));
export const ViewFormData = lazy(() => import('./generic-form/ViewFormData'));
export const ViewResponse = lazy(() => import('./generic-form/ViewResponse'));
export const Login = lazy(() => import('./login/Login'));
export const Magazine = lazy(() => import('./magazine/Magazine'));
export const CreateMagazine = lazy(() => import('./magazine/CreateMagazine'));
export const MagazineConfig = lazy(() => import('./magazine/MagazineConfig'));
export const CancelOrder = lazy(() => import('./order-cancel/CancelOrder'));
export const CancelConfig = lazy(() => import('./order-cancel/CancelConfig'));
export const CancelRequestItems = lazy(
  () => import('./order-cancel/CancelRequestItems'),
);
export const ViewCancelRequest = lazy(
  () => import('./order-cancel/ViewCancelRequest'),
);
export const Orders = lazy(() => import('./orders/orders'));
export const NewOrders = lazy(() => import('./new-orders/NewOrderList'));
export const ProductFeedAttributes = lazy(
  () => import('./catalog-service/ProductFeedAttributes'),
);
export const ProductFeedUpdate = lazy(
  () => import('./catalog-service/ProductFeedUpdate'),
);
export const ProductFeedConfig = lazy(
  () => import('./catalog-service/ProductFeedConfig'),
);
export const QuestionAndAnswer = lazy(
  () => import('./questions-and-answers/QuestionAndAnswer'),
);
export const QuestionAndAnswerItem = lazy(
  () => import('./questions-and-answers/QuestionAndAnswerItem'),
);
export const QuestionAndAnswerCreate = lazy(
  () => import('./questions-and-answers/QuestionAndAnswerCreate'),
);
export const CreateRefund = lazy(() => import('./refund/CreateRefund'));
export const RazorPayRefunds = lazy(() => import('./refund/RazorPayRefunds'));
export const RefundPayment = lazy(() => import('./refund/RefundPayments'));
export const RefundPaymentsItems = lazy(
  () => import('./refund/RefundPaymentsItems'),
);
export const RefundPending = lazy(() => import('./refund/RefundPending'));
export const RazorPayRefundsItems = lazy(
  () => import('./refund/RazorPayRefundsItems'),
);
export const Reimbursement = lazy(
  () => import('./reimbursement/Reimbursement'),
);
export const DeletedReimbursement = lazy(
  () => import('./reimbursement/DeletedReimbursement'),
);
export const ReimbursementConfig = lazy(
  () => import('./reimbursement/ReimbursementConfig'),
);

export const Customer = lazy(() => import('./review-manager/Customer'));
export const Products = lazy(() => import('./review-manager/Products'));
export const Queue = lazy(() => import('./review-manager/Queue'));
export const ReviewForm = lazy(() => import('./review-manager/ReviewForm'));
export const Review = lazy(() => import('./review-manager/Reviews'));
export const SaleTemplate = lazy(() => import('./sale-templates/SaleTemplate'));
export const SaleTemplateItem = lazy(
  () => import('./sale-templates/SaleTemplateItem'),
);
export const SearchProduct = lazy(
  () => import('./search-product/SearchProduct'),
);
export const UserList = lazy(() => import('./user-management/UserList'));
export const UserSignup = lazy(() => import('./user-management/UserSignup'));
export const SyncVinOrders = lazy(() => import('./vin-order/SyncVinOrder'));
export const BulkSyncVinOrders = lazy(
  () => import('./vin-order/BulkSyncVinOrder'),
);
export const VinOrderAllData = lazy(
  () => import('./vin-order/VinOrderAllData'),
);
export const Whatsapp = lazy(() => import('./whatsapp/Whatsapp'));
export const ChromeNavigation = lazy(
  () => import('../components/navigation/ChromeNavigation'),
);
export const CategoryRules = lazy(
  () => import('./categoryRules/CategoryRules'),
);
export const NewRules = lazy(() => import('./categoryRules/NewRule'));
export const TierPricingProductList = lazy(
  () => import('./tier-pricing-single/TierPricingProductList'),
);
export const TierPricingSingle = lazy(
  () => import('./tier-pricing-single/TierPricingSingle'),
);
export const DeliveryDays = lazy(() => import('./delivery/DeliveryDays'));
export const UnserviceablePincode = lazy(
  () => import('./delivery/UnserviceablePincode'),
);
export const ProductDetails = lazy(() => import('./delivery/Products'));
export const Warehouses = lazy(() => import('./delivery/Warehouses'));
export const ProductWarehouseQuantity = lazy(
  () => import('./delivery/ProductWarehousesQuantity'),
);
export const FaqCategory = lazy(() => import('./faq/FaqCategory'));
export const FaqItems = lazy(() => import('./faq/FaqItems'));
export const FaqPopular = lazy(() => import('./faq/FaqPopular'));

export const SyncProduct = lazy(() => import('./vin-order/SyncProduct'));
export const Coupon = lazy(() => import('./coupon/Coupon'));
export const CouponCreate = lazy(() => import('./coupon/CouponCreate'));
export const MutationLogs = lazy(() => import('./coupon/MutationLogs'));

export const FreeProduct = lazy(() => import('./promotions/FreeProduct'));
export const Product404 = lazy(() => import('./product404/Product404'));

export const Product = lazy(() => import('./catalog-service/product/Product'));
export const AddProduct = lazy(() => import('./catalog-service/product/AddProduct'));
export const AddProductToGroup = lazy(
  () => import('./catalog-service/product/AddProductToGroup'),
);
export const Category = lazy(
  () => import('./catalog-service/category/Category'),
);
export const Report = lazy(() => import('./catalog-service/report/Report'));
export const UpdateSku = lazy(
  () => import('./catalog-service/UpdateSKUGrid/UpdateSku'),
);
export const Attachment = lazy(
  () => import('./catalog-service/attachment/Attachment'),
);
export const AddAttachment = lazy(
  () => import('../components/layouts/catalog-service/attachment/AddAttachment'),
);
export const StockAlert = lazy(
  () => import('./catalog-service/stock alert/StockAlert'),
);
export const Tags = lazy(() => import('./catalog-service/Tags/Tags'));
export const AddTags = lazy(
  () => import('../components/layouts/catalog-service/tags/AddTags'),
);
export const Feedbacks = lazy(
  () => import('./catalog-service/feedbacks/Feedbacks'),
);
export const Suggestions = lazy(
  () => import('./catalog-service/suggestions/Suggestions'),
);
export const QuestionAndAnswerV2 = lazy(
  () => import('./product-question-answer-v2/QuestionAndAnswer'),
);

export const QuestionAndAnswerItemV2 = lazy(
  () => import('./product-question-answer-v2/QuestionAndAnswerItem'),
);

export const QuestionAndAnswerCreateV2 = lazy(
  () => import('./product-question-answer-v2/QuestionAndAnswerCreate'),
);
