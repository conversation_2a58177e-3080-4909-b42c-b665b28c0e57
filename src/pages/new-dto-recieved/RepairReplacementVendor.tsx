import { createContext, useContext, useEffect, useState } from 'react';
import NothingToshow from '../../components/UI-components/NothingToShow';
import LazyLoading from '../../components/UI-components/LazyLoading';
import { EditHistoryOutput, RepairVendoDtoOutput } from '../../gql/graphql';
import EditHistoryModal from '../../components/modal/new-dto-recieved/EditHistoryModal';
import { DTORecievedFilterProps } from '../../types/types';
import RepairReplacementVendorTable from '../../components/table/new-dto-recieved/RepairReplacementVendorTable';
import EditRepairReplacementVendorModal from '../../components/modal/new-dto-recieved/EditRepairReplacementVendorModal';
import useToast from '../../hooks/useToast';
import returnClient from '../../apollo-client/ReturnClient';
import { IDTORecievedFilterProps } from '../../types/new-return-types';
import { useAuth } from '../../components/providers/AuthProvider';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../hooks/useAxios';
import constants from '../../constants';
import krakendPaths from '../../constants/krakendPaths';

export interface IRepairReplacementVendorProps extends IDTORecievedFilterProps {
  selectedRepairReplacementVendor: RepairVendoDtoOutput | undefined;
  setselectedRepairReplacementVendor: React.Dispatch<
    React.SetStateAction<RepairVendoDtoOutput | undefined>
  >;
}

const RepairReplacementVendorContext =
  createContext<IRepairReplacementVendorProps | null>(null);

const RepairReplacementVendor = () => {
  //const totalPages = 20;
  const addToast = useToast();
  const { setHeaderInformation, logout } = useAuth();
  useEffect(() => {
    setHeaderInformation({
      title: 'Vendor Repair & Replacements',
      breadcrumbParent: 'Returns Processing',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const [selectedRepairReplacementVendor, setselectedRepairReplacementVendor] =
    useState<RepairVendoDtoOutput>();
  const [entityId, setentityId] = useState<null | number>(null);
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );

  const [filters, setFilters] = useState<DTORecievedFilterProps>({
    rowsPerPage: 50,
    pageNumber: 1,
    dateFilter: {
      dateFrom: undefined,
      dateTo: undefined,
    },
  });

  const axios = useAxios();

  const {
    data: queryData,
    error: queryError,
    isLoading: queryLoading,
    refetch,
  } = useQuery({
    queryKey: ['getRepairVendorDto', filters],
    queryFn: async () => {
      const response = await axios.get(
        `${krakendPaths.RETURN_URL}/admin-api/v1/admin/dto/repair-vendor`,
        {
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
          params: {
            ...filters,

            date_from: filters.dateFilter.dateFrom || '',
            date_to: filters.dateFilter.dateTo || '',

            page: filters.pageNumber,
            size: filters.rowsPerPage,
          },
        },
      );
      return response as any;
    },
    onError: (error: any) => {},
  });
  if (queryLoading) return <LazyLoading />;

  return (
    <RepairReplacementVendorContext.Provider
      value={{
        selectedRepairReplacementVendor,
        setselectedRepairReplacementVendor,
        entityId,
        setentityId,
      }}
    >
      {queryData && queryData.result && queryData.count ? (
        <RepairReplacementVendorTable
          data={queryData.result}
          count={queryData.count}
          searchContent={searchContent}
          setSearchContent={setSearchContent}
          refetch={refetch}
          filters={filters}
          setFilters={setFilters}
        />
      ) : (
        <NothingToshow />
      )}
      {selectedRepairReplacementVendor && (
        <EditRepairReplacementVendorModal
          close={() => {
            setselectedRepairReplacementVendor(undefined);
          }}
        />
      )}
      {entityId && (
        <EditHistoryModal
          id={
            queryData?.result?.filter((e: any) => e.id === entityId)[0]?.item_id
          }
          history_type="vendor"
          close={() => {
            setentityId(null);
          }}
        />
      )}
    </RepairReplacementVendorContext.Provider>
  );
};

export const useRepairReplacementVendor = () =>
  useContext(RepairReplacementVendorContext);

export default RepairReplacementVendor;
