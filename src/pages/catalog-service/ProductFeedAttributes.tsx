import { useEffect, useState } from 'react';
import { ProductFeedAttributesTable } from '../../components/table/product/ProductFeedAttributeTable';
import LazyLoading from '../../components/UI-components/LazyLoading';
import useToast from '../../hooks/useToast';
import { ProductFeedAttributeModal } from '../../components/modal/ProductFeedAttributeModal';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../components/providers/AuthProvider';
import useAxios from '../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import krakendPaths from '../../constants/krakendPaths';

const ProductFeedAttributes = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const addToast = useToast();
  const { setHeaderInformation, logout } = useAuth();
  const [visible, setVisible] = useState<boolean>(false);

  const totalPages = 20;
  const [filters, setFilters] = useState<any>({
    page:
      Number(queryParams.get('page')) > 0 ? Number(queryParams.get('page')) : 1,
    rowsPerPage: 20,
  });

  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );
  const [searchType, setSearchType] = useState<string | undefined>();
  const [productType, setProductType] = useState<string | undefined>();

  // const {
  //   error: queryError,
  //   loading: queryLoading,
  //   data: queryData,
  //   refetch: refetchdata,
  //   networkStatus,
  // } = useQuery(GET_Product_Data_Attribute, {
  //   fetchPolicy: 'network-only',
  //   variables: {
  //     ...filters,
  //     notifyOnNetworkStatusChange: true,
  //   },
  //   onError: ({
  //     graphQLErrors,
  //     networkError,
  //   }: {
  //     graphQLErrors: any;
  //     networkError: any;
  //   }) => {
  //     if (networkError) {
  //       addToast('error', networkError);
  //     }
  //     if (graphQLErrors) {
  //       addToast('error', graphQLErrors[0].message);
  //       if (graphQLErrors[0].message === 'Unauthorized') {
  //         addToast('error', 'Session Time Out');
  //         logout();
  //       }
  //     }
  //   },
  // });

  const axios = useAxios();
  const {
    refetch,
    isLoading: loading,
    data: queryData,
  } = useQuery({
    queryKey: ['get-product-feed-attributes', filters],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(
        `${krakendPaths.PRODUCT_FEED_ATTRIBUTE}/admin-api/v1/product-attributes/list`,
        {
          params: {
            ...filters,
          },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    onError: (err: any) => {
      addToast('error', err?.message);
    },
  });

  // function refetch() {
  //   setFilters({
  //     page: 1,
  //     rowsPerPage: 20,
  //   });
  //   refetchdata();
  // }

  useEffect(() => {
    // console.log('dfkn', queryData?.getProductDataAttribute?.result);
    if (queryData?.result) {
      if (queryData?.result.length === 1) {
        if (queryData?.result[0].isExist === false) {
          setVisible(true);
        }
      }
    }
  }, [queryData]);

  useEffect(() => {
    setHeaderInformation({
      title: 'Product Feed Attributes',
      breadcrumbParent: 'Product',
    });
    queryParams.set('page', filters.page > 0 ? filters.page.toString() : '1');
    navigate({ search: queryParams.toString() });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (Number(queryParams.get('page')) !== filters.page) {
      queryParams.set('page', filters.page.toString());
      navigate({ search: queryParams.toString() });
    }
  }, [filters]);

  useEffect(() => {
    if (
      Number(queryParams.get('page')) !== filters.page &&
      Number(queryParams.get('page')) !== 0
    ) {
      setFilters((prev: any) => ({
        ...prev,
        page: Number(queryParams.get('page')),
      }));
    } else if (Number(queryParams.get('page')) === 0) {
      setFilters((prev: any) => ({ ...prev, page: 1 }));
    }
  }, [queryParams.get('page')]);

  const handleSearch = () => {
    if (searchContent) {
      if (searchType == undefined || searchType == null) {
        addToast('error', 'Please Select Product Name or Product Id or Sku');
      } else if (searchType === 'Product Name') {
        setFilters((prevState: any) => ({
          ...prevState,
          product_name: searchContent,
          product_id: null,
          sku: null,
        }));
      } else if (searchType === 'Product Id') {
        setFilters((prevState: any) => ({
          ...prevState,
          product_name: null,
          product_id: Number(searchContent),
          sku: null,
        }));
      } else {
        setFilters((prevState: any) => ({
          ...prevState,
          sku: searchContent,
          product_name: null,
          product_id: null,
        }));
      }
    }
  };
  if (loading) {
    return <LazyLoading />;
  } else if (queryData?.result) {
    return (
      <>
        <ProductFeedAttributesTable
          data={queryData?.result}
          totalPage={totalPages}
          searchContent={searchContent}
          setSearchContent={setSearchContent}
          count={queryData?.count}
          filters={filters}
          setFilters={setFilters}
          handleSearch={handleSearch}
          searchType={searchType}
          setSearchType={setSearchType}
          refetch={refetch}
        />
        {visible && (
          <ProductFeedAttributeModal
            data={queryData?.result[0]}
            setVisible={setVisible}
          />
        )}
      </>
    );
  } else {
    return <LazyLoading />;
  }
};
export default ProductFeedAttributes;
