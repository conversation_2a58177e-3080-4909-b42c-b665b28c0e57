import { InputGroup } from '@zendeskgarden/react-forms';
import { Col, Row } from '../../components/UI-components/Grid';
import { Label } from '../../components/UI-components/Label';
import styled from 'styled-components';
import { MediaInput } from '../../components/UI-components/MediaInput';
import { IconStarting } from '../../utils/icons';
import { Button } from '../../components/UI-components/Button';
import { useState, useEffect } from 'react';
import {
  Dropdown,
  Menu,
  Item,
  Select as _Select,
} from '@zendeskgarden/react-dropdowns';
import { Field as _DropDownField } from '@zendeskgarden/react-dropdowns';
import { baseTheme } from '../../themes/theme';
import UPDATE_BULK_PRODUCT_ATTRIBUTES from '../../graphql/mutations/updateBulkProductAttributes';
import { useMutation } from '@apollo/client';
import useToast from '../../hooks/useToast';
import { useAuth } from '../../components/providers/AuthProvider';
import constants from '../../constants';

const Container = styled.div`
  background-color: white;
  width: auto;
  height: 82vh;
  padding: 10px;
  margin: 20px;
  border-radius: 12px;
`;
interface IItem {
  label: string;
  value: boolean;
}
const Select = styled(_Select)`
svg{
  color:white,
  fill:white,
}
`;
const DropDownField = styled(_DropDownField)`
svg{
  color:white,
  fill:white,
}
`;

const ProductFeedUpdate = () => {
  const addToast = useToast();
  const { setHeaderInformation, logout } = useAuth();
  function parseInput(input: string): string[] {
    // Remove any whitespace and split the input string by commas
    const elements = input.replace(/\s/g, '').split(',');

    return elements;
  }
  // sku_ids: $sku_ids, feed: $feed
  function handleSubmit() {
    const sku_ids: string[] = parseInput(input);
    // console.log('fsk', sku_ids, feed?.value);
    if (feed) {
      updateBulkProductAttributes({
        variables: {
          sku_ids: sku_ids,
          feed: feed.value,
        },
      });
    } else {
      addToast('error', 'Please Choose Feed');
    }
  }

  const feedItems: IItem[] = [
    { label: 'True', value: true },
    { label: 'False', value: false },
  ];
  const [input, setInput] = useState<string>('');
  const [feed, setFeed] = useState<IItem | undefined>(undefined);
  const [updateBulkProductAttributes] = useMutation(
    UPDATE_BULK_PRODUCT_ATTRIBUTES,
    {
      context: {
        headers: {
          'x-api-key': constants.API_KEY,
        },
      },
      onCompleted: (data) => {
        addToast('success', 'Updated Successfully');
      },
      onError: ({
        graphQLErrors,
        networkError,
      }: {
        graphQLErrors: any;
        networkError: any;
      }) => {
        if (networkError) {
          addToast('error', networkError);
        }
        if (graphQLErrors) {
          addToast('error', graphQLErrors[0].message);
          if (graphQLErrors[0].message === 'Unauthorized') {
            addToast('error', 'Session Time Out');
            logout();
          }
        }
      },
    },
  );

  useEffect(() => {
    setHeaderInformation({
      title: 'Product Feed Update',
      breadcrumbParent: 'Prouduct',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <Container>
      <Row style={{ margin: '20px' }} alignItems="center">
        <Col size={6}>
          <Label style={{ color: '#0A5F79', fontWeight: 600 }}>Sku Ids:-</Label>
          <InputGroup>
            <MediaInput
              start={<IconStarting />}
              style={{ minHeight: '1em' }}
              placeholder="Enter sku Ids"
              value={input}
              onChange={(e) => setInput(e.target.value)}
            />
            <Button
              style={{
                height: 'auto',
                backgroundColor: '#E56346',
                color: 'white',
                borderColor: '#E56346',
                minWidth: '80px',
                borderRadius: '4px',
                marginLeft: '2px',
              }}
              onClick={handleSubmit}
            >
              Create
            </Button>
          </InputGroup>
        </Col>
        <Col size={1} alignSelf="end">
          <Dropdown
            selectedItem={feed}
            onSelect={(items) => setFeed(items)}
            downshiftProps={{
              itemToString: (item: IItem) => item && item.label,
            }}
          >
            <DropDownField>
              <Label
                hidden
                style={{ color: '#0A5F79', fontWeight: 600 }}
              ></Label>

              <Select
                style={{
                  backgroundColor: baseTheme.colors.deepBlue,
                  color: baseTheme.colors.white,
                }}
              >
                {feed ? (
                  <span style={{ color: 'white' }}>{feed.label}</span>
                ) : (
                  <span style={{ color: 'white' }}>Select</span>
                )}
              </Select>
            </DropDownField>

            <Menu>
              {feedItems.map((option, index) => (
                <>
                  <Item key={index} value={option}>
                    {option.label}
                  </Item>
                </>
              ))}
            </Menu>
          </Dropdown>
        </Col>
      </Row>
    </Container>
  );
};
export default ProductFeedUpdate;
