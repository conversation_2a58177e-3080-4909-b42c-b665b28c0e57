// ProductFilterContext.tsx

import React, {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
} from 'react';
import useAxios from '../../../hooks/useAxios';
import constants from '../../../constants';
import { useQuery } from '@tanstack/react-query';
import { DropOption } from '../ProductFilterContext';
import useToast from '../../../hooks/useToast';
import krakendPaths from '../../../constants/krakendPaths';

export interface TagFilterTypes {
  id?: number;
  description?: string;
  tag_type?: string;
  from_created_at?: string;
  to_created_at?: string;
  from_updated_at?: string;
  to_updated_at?: string;
  status?: boolean;
  page: number;
  limit: number;
}

export interface TagValue {
  id?: number;
  status?: boolean;
  name?: string;
  unique_code?: string;
  value?: string;
  tag_type?: string;
  position?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
}
interface TagContextProps {
  filters: TagFilterTypes;
  setFilters: React.Dispatch<React.SetStateAction<TagFilterTypes>>;
  tagValue: TagValue;
  setTagValue: React.Dispatch<React.SetStateAction<TagValue>>;
  newTagValue: TagValue;
  setNewTagValue: React.Dispatch<React.SetStateAction<TagValue>>;
  refetchTag: boolean;
  setRefetchTag: React.Dispatch<React.SetStateAction<boolean>>;
  refetchTagProduct: boolean;
  setRefetchTagProduct: React.Dispatch<React.SetStateAction<boolean>>;
  sortBy: string;
  setSortBy: React.Dispatch<React.SetStateAction<string>>;
  sortDirection: string;
  setSortDirection: React.Dispatch<React.SetStateAction<string>>;
  visibilityList: string[];
  visibilityData: DropOption[] | undefined;
}

const TagContext = createContext<TagContextProps | undefined>(undefined);

export const useTagContext = () => {
  const context = useContext(TagContext);

  if (!context) {
    throw new Error(
      'useAttachmentContext must be used within a AttachmentFilterProvider',
    );
  }
  return context;
};

export const TagContextProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const queryParams = new URLSearchParams(location.search);
  const [filters, setFilters] = useState<TagFilterTypes>({
    page: 1,
    limit: 20,
  });
  const [tagValue, setTagValue] = useState<TagValue>({} as TagValue);
  const [newTagValue, setNewTagValue] = useState<TagValue>({} as TagValue);
  const [refetchTag, setRefetchTag] = useState(false);
  const [refetchTagProduct, setRefetchTagProduct] = useState(false);
  const [page, setPage] = useState(1);
  const [isId, setIsId] = useState(false);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [visibilityList, setVisibilityList] = useState<string[]>([]);
  const axios = useAxios();
  const addToast = useToast();

  const {
    isLoading,
    isError,
    error,
    data: visibilityData,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ['get-visibility-dropdown-v1-list'],
    queryFn: async () => {
      const response: any = await axios.get(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/products/attributes-options?attributeId=102&sortOrder=ASC`,
        // `${constants.CATALOG_URL}/v1/catalog-admin/attributes-options?attributeId=102&sortOrder=ASC`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response?.attributes_options as unknown as DropOption[];
    },
    onError: (err) => {
      addToast('error', `visibility dropdown list error`);
    },
  });

  useEffect(() => {
    if (visibilityData) {
      const visibilityName = visibilityData?.map(
        (item: DropOption) => item.value,
      );
      setVisibilityList(visibilityName);
    }
  }, [visibilityData]);

  return (
    <TagContext.Provider
      value={{
        filters,
        setFilters,
        tagValue,
        setTagValue,
        newTagValue,
        setNewTagValue,
        refetchTag,
        setRefetchTag,
        refetchTagProduct,
        setRefetchTagProduct,
        sortBy,
        setSortBy,
        sortDirection,
        setSortDirection,
        visibilityData,
        visibilityList,
      }}
    >
      {children}
    </TagContext.Provider>
  );
};
