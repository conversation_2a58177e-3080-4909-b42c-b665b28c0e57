import { useEffect, useState } from 'react';
import ProductsTable from '../../../components/table/product/ProductsTable';
import { useLocation, useNavigate } from 'react-router-dom';
import { ProductFilterTypes, useProductContext } from '../ProductFilterContext';
import useAxios from '../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import routes from '../../../constants/routes';
import LazyLoading from '../../../components/UI-components/LazyLoading';
import {
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  Visibility,
  VisibilityState,
} from '@tanstack/react-table';
import { baseTheme, colors } from '../../../themes/theme';
import styled from 'styled-components';
import {
  Checkbox,
  Field as CheckField,
  Fieldset,
  Label,
} from '@zendeskgarden/react-forms';
import { Button as _Button } from '../../../components/UI-components/Button';
import {
  Dropdown,
  Field as _Field,
  Menu,
  Trigger,
  Item,
} from '@zendeskgarden/react-dropdowns';
import {
  TableContainer,
  TableHolder,
  TableContainer as _TableContainer,
} from '../../../components/UI-components/Table';
import {
  ProductColumns,
  ProductTableColumns as columns,
} from '../../../components/table/product/Columns';
import { Col, Row } from '../../../components/UI-components/Grid';
import SearchInput from '../../../components/search-input/SearchInput';
import {
  AddIconWhiteBG,
  DownIcon,
  FilterIcon,
  LeftArrowIcon,
  RefetchIcon,
  ResetIcon,
  RightArrowIcon,
} from '../../../utils/icons';
import Import from '../../../components/modal/catalog-service/Import';
import ExportDropdown from '../../../components/dropdown/catalog-service/ExportDropdown';
import { SM, Span } from '@zendeskgarden/react-typography';
import { useScreenDefaultWidth } from '../../../hooks/useScreenSize';
import { pageRoutes } from '../../../components/navigation/RouteConfig';
import NothingToshow from '../../../components/UI-components/NothingToShow';
import { Pagination } from '../../../components/UI-components/Pagination';
import ProductsFilterDrawer from '../../../components/drawer/catalog-service/ProductsFilterDrawer';
import constants from '../../../constants';
import useToast from '../../../hooks/useToast';
import { CatalogDataTable } from '../../../components/table/product/CatalogDataTable';
import krakendPaths from '../../../constants/krakendPaths';
import { useAuth } from '../../../components/providers/AuthProvider';
import { Tooltip } from '@zendeskgarden/react-tooltips';

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;

const TopBarDiv = styled.div`
  background-color: ${colors.white};
  padding-bottom: ${(p) => p.theme.space.md};
  padding-left: ${(p) => p.theme.space.md};
  padding-right: ${(p) => p.theme.space.md};
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;

const DropdownItem = styled.div`
  max-height: 300px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(2, 2fr);
  gap: 10px;
  padding: 20px 15px;
  cursor: pointer;
`;

const ActionButtonsContainer = styled.div`
  display: flex;
  gap: 10px;
`;

const Field = styled(CheckField)`
  padding: ${baseTheme.space.sm};
`;

const Button = styled(_Button)`
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;

const StyledDropdown = styled(Dropdown)`
  margin-right: 10px;
`;
type SearchType = 'name' | 'id' | 'sku' | 'url_key' | '';

const Product = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const [pageSize, setPageSize] = useState(20);
  const [count, setCount] = useState(0);
  const [currentSearch, setCurrentSearch] = useState<string>();
  const [searchType, setSearchType] = useState<SearchType>('name');
  const [searchName, setSearchName] = useState<string>('');
  const [searchId, setSearchId] = useState<any>();
  const [searchIdError, setSearchIdError] = useState<string>('');
  const [searchValue, setSearchValue] = useState<any>();
  const [isReset, setIsReset] = useState<boolean>(false);
  const [isFilterDisabled, setIsFilterDisabled] = useState<{
    count: number;
    isdisabled: boolean;
  }>({ count: 0, isdisabled: false });
  const addToast = useToast();
  const [initialFetch, setInitialFetch] = useState(true);
  const {
    filters,
    setFilters,
    setContextProdData,
    setContextUpdateProdData,
    page,
    setPage,
    columnList,
    field,
    setField,
    order,
    setOrder,
    isMediaRemove,
    setIsMediaRemove,
    detectBrowser,
    setGroupAssociate,
  } = useProductContext();

  const { setHeaderInformation } = useAuth();
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['get-all-products'],
      queryFn: async (): Promise<any> => {
        const response = await axios.get(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/products`,
          `${constants.CATALOG_URL}/v1/catalog-admin/list-products`,
          {
            params: {
              id_from: filters?.id?.from,
              id_to: filters?.id?.to,
              status: filters?.status,
              visibility: filters?.visibility,
              manufacturer: filters?.manufacturer,
              page: page,
              size: pageSize,
              search_by_keyword_field:
                searchType === 'id' ||
                searchContent === undefined ||
                searchContent === ''
                  ? undefined
                  : searchType,
              search_by_keyword_value:
                searchType === 'id' || searchType === '' || searchContent === ''
                  ? undefined
                  : searchContent,
              name: filters?.name,
              product_id: searchType === 'id' ? searchContent : undefined,
              sku: filters?.sku,
              price_from: filters?.price?.from,
              price_to: filters?.price?.to,
              msrp_from: filters?.msrp?.from,
              msrp_to: filters?.msrp?.to,
              product_expiry_from: filters?.product_expiry?.from,
              product_expiry_to: filters?.product_expiry?.to,
              backorders: filters?.backorders,
              is_in_stock: filters?.is_in_stock,
              demo_available: filters?.demo_available,
              min_qty: filters?.quantity?.min,
              max_qty: filters?.quantity?.max,
              quantity:
                filters?.quantity?.min || filters?.quantity?.max
                  ? 1
                  : undefined,
              gstin: filters?.gtin,
              type_id: filters?.type_id,
              sort_by_order: order ? order : undefined,
              sort_by_field: field ? field : undefined,
            },
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              admin_identifier: `${localStorage.getItem('username')}`,
            },
          },
        );
        return response.data;
      },
      enabled: false,
      onError: (error: any) => {
        addToast('error', `${error?.response?.data?.message[0]}`);
        setCurrentSearch('');
      },
      onSuccess: (data) => {
        setCount(data.item_count);
      },
      retry: false,
    });

  const [filtersReset, setFiltersReset] = useState(false);

  const resetFilter = () => {
    reset();
    setSearchId(undefined);
    setSearchName('');
    setFiltersReset(true);
  };
  const getActiveFilterLabels = () => {
    const activeFilters = [];

    if (filters?.id?.from) activeFilters.push(`ID From: ${filters.id.from}`);
    if (filters?.id?.to) activeFilters.push(`ID To: ${filters.id.to}`);
    if (filters?.name) activeFilters.push(`Name: ${filters.name}`);
    if (filters?.sku) activeFilters.push(`SKU: ${filters.sku}`);
    if (filters?.price?.from)
      activeFilters.push(`Price From: ${filters.price.from}`);
    if (filters?.price?.to) activeFilters.push(`Price To: ${filters.price.to}`);
    if (filters?.msrp?.from)
      activeFilters.push(`MSRP From: ${filters.msrp.from}`);
    if (filters?.msrp?.to) activeFilters.push(`MSRP To: ${filters.msrp.to}`);
    if (filters?.product_expiry?.from)
      activeFilters.push(`Expiry From: ${filters.product_expiry.from}`);
    if (filters?.product_expiry?.to)
      activeFilters.push(`Expiry To: ${filters.product_expiry.to}`);
    if (filters?.visibility)
      activeFilters.push(`Visibility: ${filters.visibility}`);
    if (filters?.manufacturer)
      activeFilters.push(`Manufacturer: ${filters.manufacturer}`);
    if (filters?.backorders)
      activeFilters.push(
        `Backorders: ${
          filters?.backorders === true
            ? 'Allow Qty Below 0'
            : 'Do Not Allow Qty Below 0'
        }`,
      );
    if (filters?.is_in_stock)
      activeFilters.push(
        `Stock Status: ${filters.is_in_stock ? 'In Stock' : 'Out of Stock'}`,
      );
    if (filters?.demo_available)
      activeFilters.push(
        `Demo Available: ${filters.demo_available ? 'Enabled' : 'Disabled'}`,
      );
    if (filters?.quantity?.min)
      activeFilters.push(`Min Quantity: ${filters.quantity.min}`);
    if (filters?.quantity?.max)
      activeFilters.push(`Max Quantity: ${filters.quantity.max}`);
    if (filters?.gtin) activeFilters.push(`GTIN: ${filters.gtin}`);
    if (filters?.type_id)
      activeFilters.push(`Product Type: ${filters.type_id}`);
    if (filters?.status)
      activeFilters.push(`Status: ${filters.status ? 'Enabled' : 'Disabled'}`);
    console.log('activeFilters', activeFilters);
    return activeFilters;
  };

  const [isOpen, setIsOpen] = useState(false);
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rotated, setRotated] = useState<boolean | undefined>();

  const from = 'products';
  const [alreadyEnabledColumn, setAlreadyEnabledColumn] = useState<string[]>(
    localStorage.getItem('products')?.split(',') || [
      'Id',
      'Thumbnail',
      'Product Name',
      'Product Type',
      'Quantity',
      'SKU',
      'Backorder',
      'Visibility',
      'Brand',
      'Reward Coin',
      'Product Expiry Date',
      'Status',
      'Tax Class',
      'Weight',
      'International View',
      'HSN Code',
      'Selling Price',
      'COD',
      'Price',
      'Gtin',
      'Demo Available',
      'Stock Status',
      'Categories',
      'Completion Percentage',
    ],
  );

  const disabledColumn = [
    'Action',
    'Id',
    'SKU',
    'Product Name',
    'Product Type',
    'Visibility',
    'Status',
  ];
  const table = useReactTable({
    data: data?.items || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  });
  const [isBulkOpen, setIsBulkOpen] = useState(false);
  const [isImport, setIsImport] = useState(false);
  const [isExport, setIsExport] = useState(false);
  const handleToggleDropdown = () => {
    setIsBulkOpen(!isBulkOpen);
  };
  const [rotatedType, setRotatedType] = useState<boolean>();

  const isSmallScreen = useScreenDefaultWidth();

  const handleImportClick = () => {
    setIsImport(true);
    setIsExport(false);
    setIsBulkOpen(false);
  };

  const handleExportClick = () => {
    setIsExport(true);
    setIsImport(false);
    setIsBulkOpen(false);
  };

  const handleCheckboxChange = (event: any, column: any, header: string) => {
    const { checked } = event.target;
    if (checked === true) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) => [...prev, header]);
      const newColumns = alreadyEnabledColumn && [
        ...alreadyEnabledColumn,
        header,
      ];
      localStorage.setItem(`${from}`, `${newColumns}`);
      column.toggleVisibility(true);
    } else if (checked === false) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) =>
          prev.filter((item) => item !== header),
        );
      const newColumns =
        alreadyEnabledColumn &&
        alreadyEnabledColumn.filter((item) => item !== header);
      localStorage.setItem(`${from}`, `${newColumns}`);
      column.toggleVisibility(false);
    }
  };

  const handleRefetch = () => {
    table.toggleAllPageRowsSelected(false);
    resetFilter();
  };

  const reset = () => {
    setPage(1);
    queryParams.set('page', '1');
    navigate(
      { search: queryParams.toString() },
      {
        replace: true,
      },
    );
    setSearchContent('');
    setCurrentSearch && setCurrentSearch('');
  };

  const addProduct = () => {
    navigate(`${pageRoutes['GO_TO_ADD_PRODUCT']}/`);
  };

  const handleRowPerPage = (item: string) => {
    const pageSize = Number(item);
    setPageSize(pageSize);
    table.setPageSize(pageSize);
  };

  useEffect(() => {
    refetch();
  }, [order, field, pageSize]);

  useEffect(() => {
    if (filtersReset) {
      refetch();
      setFiltersReset(false);
    }
  }, [filtersReset]);

  useEffect(() => {
    if (isMediaRemove) {
      refetch();
      setIsMediaRemove(false);
    }
  }, [isMediaRemove]);

  useEffect(() => {
    console.log(filters, 'filters');
    if (!initialFetch) {
      refetch();
    }
    table.toggleAllPageRowsSelected(false);
  }, [filters]);

  useEffect(() => {
    if (
      Number(queryParams.get('page')) !== page &&
      Number(queryParams.get('page')) !== 0
    ) {
      setPage(Number(queryParams.get('page')));
    } else if (Number(queryParams.get('page')) === 0) {
      setPage(1);
    }
    table.toggleAllPageRowsSelected(false);
  }, [queryParams.get('page')]);

  const handleSearch = () => {
    if (searchContent) {
      queryParams.set('page', '1');
      setCurrentSearch(searchContent.trim());
    }
    refetch();
  };

  useEffect(() => {
    setHeaderInformation({
      title: 'Products',
      breadcrumbParent: '',
    });
    table.setPageSize(Number(pageSize));
    setSearchIdError('');
    setInitialFetch(false);
    setContextProdData({} as ProductColumns);
    setContextUpdateProdData({} as ProductColumns);
    setGroupAssociate([]);
  }, []);
  const searchOptions = [
    {
      label: 'Name',
      value: 'name',
    },
    {
      label: 'ID',
      value: 'id',
    },
    {
      label: 'SKU',
      value: 'sku',
    },
    {
      label: 'URL KEY',
      value: 'url_key',
    },
  ];
  return isLoading || isRefetching || isFetching ? (
    <LazyLoading />
  ) : (
    <div>
      <>
        <div>
          <TopBarDiv>
            <Row>
              <Col size={6}>
                <Row justifyContent="between" alignItems="center">
                  <Col>
                    <Row alignItems="center">
                      <Col size={6.5}>
                        <Row>
                          <SearchInput
                            isLabelHidden
                            searchContent={searchContent}
                            setSearchContent={setSearchContent}
                            placeholder="Search by keyword"
                            handleSearch={handleSearch}
                            label=""
                            width="100%"
                          />
                        </Row>
                        <Row>
                          {searchIdError !== '' && (
                            <div style={{ color: 'red' }}>
                              {' '}
                              Wrong Keyword for id: {searchIdError}
                            </div>
                          )}
                        </Row>
                      </Col>
                      {searchOptions && (
                        <Col offset={0.5} size={2}>
                          <>
                            <Dropdown
                              onSelect={(item) => setSearchType(item)}
                              onStateChange={(options) =>
                                Object.hasOwn(options, 'isOpen') &&
                                setRotatedType(options.isOpen)
                              }
                            >
                              <Trigger>
                                <Button
                                  style={{
                                    transform: isSmallScreen
                                      ? 'translateX(-30px)'
                                      : 'translateX(0)',
                                  }}
                                  isPrimary
                                >
                                  {searchOptions.map((option: any) => {
                                    return option.value === searchType
                                      ? option.label
                                      : null;
                                  })}
                                  <Button.EndIcon isRotated={rotatedType}>
                                    <DownIcon
                                      style={{
                                        height: baseTheme.iconSizes.md,
                                        width: baseTheme.iconSizes.md,
                                      }}
                                    />
                                  </Button.EndIcon>
                                </Button>
                              </Trigger>
                              <Menu
                                style={{
                                  width:
                                    baseTheme.components.dimension.width
                                      .base200,
                                  transform: 'translateX(4px)',
                                  borderRadius: baseTheme.borderRadii.lg,
                                }}
                              >
                                {searchOptions.map((option: any) => (
                                  <Item key={option.value} value={option.value}>
                                    {option.label}
                                  </Item>
                                ))}
                              </Menu>
                            </Dropdown>
                          </>
                        </Col>
                      )}
                    </Row>
                  </Col>
                </Row>
              </Col>
              <Col
                style={{
                  display: 'flex',
                  gap: '5px',
                  justifyContent: 'end',
                  alignItems: 'center',
                }}
              >
                <ActionButtonsContainer>
                  <StyledDropdown
                    onSelect={(item) => alert(`You planted a ${item}`)}
                    onStateChange={(options) =>
                      Object.hasOwn(options, 'isOpen') &&
                      setRotated(options.isOpen)
                    }
                  >
                    <Trigger>
                      <Button
                        size="medium"
                        isAction
                        onClick={handleToggleDropdown}
                      >
                        Bulk Action
                      </Button>
                    </Trigger>
                    <Menu
                      style={{
                        width: baseTheme.components.dimension.width.base150,
                        transform: 'translateX(4px)',
                        borderRadius: baseTheme.borderRadii.lg,
                      }}
                    >
                      <DropdownItem onClick={handleImportClick}>
                        Import
                      </DropdownItem>
                      <DropdownItem onClick={handleExportClick}>
                        Export
                      </DropdownItem>
                    </Menu>
                  </StyledDropdown>
                  {isImport && (
                    <Import visible={isImport} setVisible={setIsImport} />
                  )}
                  {isExport && (
                    <ExportDropdown
                      isExport={isExport}
                      setIsExport={setIsExport}
                    />
                  )}
                </ActionButtonsContainer>
                <>
                  <Tooltip
                    style={{
                      backgroundColor: 'white',
                      boxShadow: '0 2px 4px hsla(0, 0.00%, 0.00%, 0.10)',
                      border: '1px solid #ccc',
                    }}
                    type="dark"
                    size="large"
                    placement="bottom-start"
                    zIndex={9999}
                    content={
                      <div
                        style={{
                          maxWidth: '300px',
                          maxHeight: '200px',
                          overflow: 'auto',
                          color: 'black',
                        }}
                      >
                        {isFilterDisabled.count > 0 ? (
                          <ul style={{ margin: '5px', padding: '5px 20px' }}>
                            {getActiveFilterLabels().map((filter, index) => (
                              <li key={index} style={{ textAlign: 'left' }}>
                                {filter}
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <span>No active filters</span>
                        )}
                      </div>
                    }
                  >
                    <Button
                      size="medium"
                      isAction
                      onClick={() => {
                        setIsReset(true);
                      }}
                      disabled={isFilterDisabled.isdisabled}
                    >
                      <Button.StartIcon>
                        <ResetIcon />
                      </Button.StartIcon>
                      <Span>
                        Reset Filter
                        {isFilterDisabled.count > 0 && (
                          <Span
                            style={{
                              backgroundColor: `${baseTheme.colors.primaryHue}`,
                              color: 'white',
                              borderRadius: '9999px',
                              padding: '2px 6px',
                              fontSize: '12px',
                              marginLeft: '4px',
                            }}
                          >
                            {isFilterDisabled.count}
                          </Span>
                        )}
                      </Span>
                    </Button>
                  </Tooltip>
                </>
                <>
                  <Button
                    size="medium"
                    isAction
                    isPrimary
                    onClick={() => {
                      setIsOpen(true);
                    }}
                  >
                    <Button.StartIcon>
                      <FilterIcon />
                    </Button.StartIcon>
                    <Span>Filter</Span>
                  </Button>
                </>
                <>
                  <Button size="medium" isAction onClick={handleRefetch}>
                    <Button.StartIcon>
                      <RefetchIcon />
                    </Button.StartIcon>
                    <Span>Refetch</Span>
                  </Button>
                </>

                <StyledDropdown>
                  <Dropdown
                    onSelect={(item) => alert(`You planted a ${item}`)}
                    onStateChange={(options) =>
                      Object.hasOwn(options, 'isOpen') &&
                      setRotated(options.isOpen)
                    }
                  >
                    <Trigger>
                      <Button isPrimary>
                        Select Item
                        <Button.EndIcon isRotated={rotated}>
                          <DownIcon
                            style={{
                              height: baseTheme.iconSizes.md,
                              width: baseTheme.iconSizes.md,
                            }}
                          />
                        </Button.EndIcon>
                      </Button>
                    </Trigger>
                    <Menu
                      style={{
                        width: baseTheme.components.dimension.width.base200,
                        transform: 'translateX(4px)',
                        borderRadius: baseTheme.borderRadii.lg,
                        padding: `${baseTheme.paddings.md} ${baseTheme.paddings.md}`,
                      }}
                    >
                      <Fieldset>
                        {table
                          .getAllColumns()
                          .filter(
                            (column) =>
                              column.getCanHide() && column.columnDef.header,
                          )
                          .map((column: any, index) => {
                            if (index === 0) {
                              return null;
                            }
                            const columnHeader =
                              typeof column.columnDef.header === 'function'
                                ? column.columnDef?.accessorKey
                                : (column.columnDef.header as string);
                            return (
                              <>
                                {!column.columnDef.enableHiding &&
                                  !column.columnDef.enableColumnFilter && (
                                    <Field key={column.id}>
                                      <Checkbox
                                        disabled={disabledColumn?.includes(
                                          columnHeader,
                                        )}
                                        key={column.id}
                                        checked={alreadyEnabledColumn?.includes(
                                          columnHeader,
                                        )}
                                        onChange={(e) =>
                                          handleCheckboxChange(
                                            e,
                                            column,
                                            columnHeader,
                                          )
                                        }
                                      >
                                        <Label>{columnHeader}</Label>
                                      </Checkbox>
                                    </Field>
                                  )}
                              </>
                            );
                          })}
                      </Fieldset>
                    </Menu>
                  </Dropdown>
                </StyledDropdown>
                <>
                  <Button
                    isPrimary
                    onClick={() => {
                      addProduct();
                    }}
                  >
                    <Button.StartIcon>
                      <AddIconWhiteBG />
                    </Button.StartIcon>
                    Add New Product
                  </Button>
                </>
              </Col>
            </Row>
            <Row justifyContent="end">
              {currentSearch && currentSearch != '' && (
                <SM
                  style={{
                    marginLeft: baseTheme.space.md,
                    marginRight: baseTheme.space.md,
                    marginTop: baseTheme.space.xs,
                  }}
                >
                  You have searched for "{currentSearch}"
                </SM>
              )}
            </Row>
          </TopBarDiv>

          <Container>
            <TableContainer>
              <TableHolder>
                {table.getRowModel().rows?.length ? (
                  <CatalogDataTable
                    table={table}
                    columns={columns}
                    data={data}
                    alreadyEnabledColumn={alreadyEnabledColumn}
                  />
                ) : (
                  <NothingToshow divHeight="55vh" />
                )}
              </TableHolder>
              {table.getRowModel().rows?.length && count > 0 ? (
                <div style={{ overflowX: 'clip' }}>
                  <Row
                    style={{
                      height: `${
                        baseTheme.components.dimension.width.base * 5
                      }px`,
                      marginTop: baseTheme.space.sm,
                      backgroundColor: baseTheme.colors.white,
                      paddingLeft: baseTheme.space.lg,
                      paddingRight: baseTheme.space.lg,
                    }}
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="start" alignItems="center">
                        <Button
                          style={{
                            maxWidth:
                              baseTheme.components.dimension.width.base100,
                          }}
                          size="medium"
                          isAction
                          disabled={page <= 1 ? true : false}
                          onClick={() => {
                            setFilters((prev) => ({ ...prev, page: page - 1 }));
                            setPage(page - 1);
                            queryParams.set('page', (page - 1).toString());
                            navigate(
                              { search: queryParams.toString() },
                              {
                                replace: true,
                              },
                            );
                          }}
                        >
                          <Button.StartIcon>
                            <LeftArrowIcon />
                          </Button.StartIcon>
                          Previous
                        </Button>
                      </Row>
                    </Col>
                    <Col textAlign="center" lg={2} md={2}>
                      <Dropdown
                        onSelect={(item) => handleRowPerPage(item)}
                        onStateChange={(options) =>
                          Object.hasOwn(options, 'isOpen') &&
                          setRotated(options.isOpen)
                        }
                      >
                        <Trigger>
                          <Button size="medium" isAction>
                            Row Per Page:
                            <Span style={{ paddingLeft: baseTheme.space.sm }}>
                              {table.getState().pagination.pageSize}
                            </Span>
                            <Button.EndIcon
                              isRotated={rotated}
                              style={{ marginLeft: 0 }}
                            >
                              <DownIcon />
                            </Button.EndIcon>
                          </Button>
                        </Trigger>
                        <Menu>
                          <Item value={20}>20</Item>
                          <Item value={50}>50</Item>
                          <Item value={100}>100</Item>
                        </Menu>
                      </Dropdown>
                    </Col>
                    <Col lg={5} md={5}>
                      <Row justifyContent="center" alignItems="center">
                        <Pagination
                          color={baseTheme.colors.deepBlue}
                          totalPages={Math.ceil(
                            count / table.getState().pagination.pageSize,
                          )}
                          pagePadding={2}
                          currentPage={page}
                          onChange={(e) => {
                            setFilters((prev) => ({ ...prev, page: e }));
                            setPage(e);
                            queryParams.set('page', e.toString());
                            navigate(
                              { search: queryParams.toString() },
                              {
                                replace: true,
                              },
                            );
                          }}
                        />
                      </Row>
                    </Col>
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="start" alignItems="end">
                        <Button
                          size="medium"
                          isAction
                          style={{ cursor: 'default' }}
                        >
                          {(page - 1) * table.getState().pagination.pageSize +
                            1}
                          -
                          {count < page * table.getState().pagination.pageSize
                            ? count
                            : page * table.getState().pagination.pageSize}{' '}
                          of {count}
                        </Button>
                      </Row>
                    </Col>
                    <Col lg={1.5} md={1.5}>
                      <Row justifyContent="end" alignItems="end">
                        <Button
                          style={{
                            maxWidth:
                              baseTheme.components.dimension.width.base100,
                          }}
                          size="medium"
                          isAction
                          disabled={
                            page >=
                            Math.ceil(
                              count / table.getState().pagination.pageSize,
                            )
                              ? true
                              : false
                          }
                          onClick={() => {
                            setFilters((prev) => ({ ...prev, page: page + 1 }));
                            setPage(page + 1);
                            queryParams.set('page', (page + 1).toString());
                            navigate(
                              { search: queryParams.toString() },
                              {
                                replace: true,
                              },
                            );
                          }}
                        >
                          Next
                          <Button.EndIcon>
                            <RightArrowIcon />
                          </Button.EndIcon>
                        </Button>
                      </Row>
                    </Col>
                  </Row>
                </div>
              ) : (
                <>{''}</>
              )}
            </TableContainer>
          </Container>
          <ProductsFilterDrawer
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            setFilters={setFilters}
            filters={filters}
            reset={reset}
            isreset={isReset}
            setIsReset={setIsReset}
            setIsFilterDisabled={setIsFilterDisabled}
          />
        </div>
      </>
    </div>
  );
};

export default Product;
