import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import ProductInformation from '../../../components/layouts/catalog-service/product/ProductInformation';
import Description from '../../../components/layouts/catalog-service/product/Description';
import Media from '../../../components/layouts/catalog-service/product/Media';
import ProductReviews from '../../../components/layouts/catalog-service/product/ProductReviews';
import ProductCarousal from '../../../components/layouts/catalog-service/product/ProductAccordion';
import Marketing from '../../../components/layouts/catalog-service/Marketing';
import ProductGrouping from '../../../components/layouts/catalog-service/product/ProductGrouping';
import { Col, Row } from '../../../components/UI-components/Grid';
import { LeftArrowIcon, RightArrowIcon } from '../../../utils/icons';
import ThumbnailIcon from '../../../utils/icons/thumbnail.svg';
import { MD, Span } from '@zendeskgarden/react-typography';
import { pageRoutes } from '../../../components/navigation/RouteConfig';
import { useNavigate } from 'react-router';
import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import useToast from '../../../hooks/useToast';
import { ProductColumns } from '../../../components/table/product/Columns';
import { useLocation, useParams } from 'react-router-dom';
import { DropOption, useProductContext } from '../ProductFilterContext';
import routes from '../../../constants/routes';
import { baseTheme } from '../../../themes/theme';
import { Button, Buttons } from '../../../components/UI-components/Button';
import Log from '../../../components/layouts/catalog-service/product/Log';
import { Skeleton, Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';
import { useAuth } from '../../../components/providers/AuthProvider';

const Main = styled.div`
  border-radius: 5px;
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;

const TopBar = styled(Row)`
  padding: 10px 10px;
  background-color: #fff;
  justify-content: space-between;
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;

const TabContainer = styled.div`
  display: flex;
  margin-left: 10px;
`;

const TabButton = styled.button<{ active: boolean }>`
  padding: ${baseTheme.paddings.md} ${baseTheme.paddings.lg};
  border-top: ${baseTheme.paddings.sm} solid
    ${(props) =>
      props.active
        ? `${baseTheme.colors.primaryHue}`
        : `${baseTheme.colors.textColorGrey}`};
  background-color: ${(props) =>
    props.active
      ? `${baseTheme.colors.white}`
      : `${baseTheme.colors.veryLightGrey}`};
  color: ${(props) =>
    props.active
      ? `${baseTheme.colors.primaryHue}`
      : `${baseTheme.colors.black}`};
  border-right: none;
  border-left: none;
  border-bottom: none;
  font-weight: 600;
  cursor: pointer;
`;
const ContentContainer = styled.div`
  background-color: #fff;
  height: 100%;
`;

interface TabContentProps {
  active: boolean;
}

interface Tab {
  id: string;
  label: string;
}

const TabContent = styled.div<TabContentProps>`
  display: ${(props) => (props.active ? 'block' : 'none')};
  height: 100%;
`;

const tabs = [
  { id: 'tab1', label: 'Product Information' },
  { id: 'tab2', label: 'Product Content' },
  { id: 'tab3', label: 'Media' },
  // { id: 'tab4', label: 'Product Reviews' },
  // { id: 'tab5', label: 'Product Carousal' },
  { id: 'tab6', label: 'Marketing' },
  { id: 'tab7', label: 'Product Grouping' },
  { id: 'tab8', label: 'Log' },
];

const AddProduct: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('tab1');
  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const addToast = useToast();

  const {
    contextProdData,
    setContextProdData,
    contextUpdateProdData,
    setContextUpdateProdData,
    detectBrowser,
    categoryIds,
    redirect,
    refetchLog,
    productErrors,
    setProductErrors,
    isUpdate,
    setIsUpdate,
  } = useProductContext();

  const [filteredTabs, setFilteredTabs] = useState<any>([]);

  const { setHeaderInformation } = useAuth();
  const [prodData, setProdData] = useState<ProductColumns>();
  const [image, setImage] = useState<string | null>(null);

  const { isLoading, isFetching, refetch } = useQuery({
    queryKey: ['get-product-byIds', prodId],
    queryFn: async (): Promise<any> => {
      if (prodId) {
        const response = await axios.post(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/search`,
          `${constants.CATALOG_URL}/v1/catalog-admin/search-products`,
          {
            product_ids: [prodId],
            filters: { showCategoryNames: false, showOptionsValues: true },
            pagination: {
              page: 1,
            },
          },
          {
            headers: {
              'x-api-key': `${constants.CATALOG_KEY}`,
              'Content-Type': 'application/json',
            },
          },
        );
        return response.data;
      } else {
        return [];
      }
    },
    onError: (err: any) => {
      addToast('error', `${err?.message}`);
      navigate(`${pageRoutes['GO_TO_PRODUCT_LIST']}/`);
    },
    onSuccess: (data) => {
      if (data?.items) {
        setProdData(data.items && data.items[0]);
      } else {
      }
    },
  });

  const { mutate: addMutation, isLoading: addLoading } = useMutation(
    async (dataObject: ProductColumns) => {
      const {
        attributes_list,
        inventory_details,
        tier_prices,
        product_links,
        type_id,
        status,
        category_associated,
      } = dataObject;

      let enhancedAttributes = { ...attributes_list };

      if (
        attributes_list?.name &&
        (!attributes_list.meta_title || attributes_list.meta_title === '')
      ) {
        enhancedAttributes.meta_title = attributes_list.name;
      }

      if (
        attributes_list?.description &&
        (!attributes_list.meta_description ||
          attributes_list.meta_description === '')
      ) {
        enhancedAttributes.meta_description = attributes_list.description;
      }

      const filteredAttributes = enhancedAttributes
        ? Object.entries(enhancedAttributes)
            .filter(([key, value]) => value !== undefined)
            .map(([attribute_code, value]) => {
              if (attribute_code === 'url_key') {
                return { attribute_code, value, enableRedirect: redirect };
              }
              return { attribute_code, value };
            })
        : [];

      const payload = {
        status: status ? status : false,
        type_id,
        category_associated: category_associated ? categoryIds : undefined,
        product_links: product_links
          ? {
              associated: product_links?.associated
                ? contextUpdateProdData?.product_links?.associated
                : undefined,
              crosssell: product_links?.crosssell
                ? contextProdData?.product_links?.crosssell
                : undefined,
              upsell: product_links?.upsell
                ? contextProdData?.product_links?.upsell
                : undefined,
              related: product_links?.related
                ? contextProdData?.product_links?.related
                : undefined,
            }
          : undefined,
        attributes_list: filteredAttributes,
        inventory_details: {
          qty: contextUpdateProdData?.inventory_details?.qty,
          is_in_stock: contextUpdateProdData?.inventory_details?.is_in_stock
            ? contextUpdateProdData.inventory_details.is_in_stock
            : true,
          backorders: contextUpdateProdData?.inventory_details?.backorders
            ? contextUpdateProdData?.inventory_details.backorders
            : false,
          ...(contextUpdateProdData?.inventory_details?.min_sale_qty !=
            null && {
            min_sale_qty: contextUpdateProdData.inventory_details.min_sale_qty,
          }),
          ...(contextUpdateProdData?.inventory_details?.max_sale_qty !=
            null && {
            max_sale_qty: contextUpdateProdData.inventory_details.max_sale_qty,
          }),
        },
        // inventory_details: inventory_details ? inventory_details : undefined,
        tier_prices: tier_prices ? tier_prices : undefined,
      };

      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/products`,
        `${constants.CATALOG_URL}/v1/catalog-admin/product`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            admin_identifier: `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err?.response?.data?.message}`);
      },
      onSuccess: (data) => {
        addToast('success', `Product Created Successfully.`);
        console.log('data: ', data);
        if (data.id) {
          navigate(`${pageRoutes['GO_TO_PRODUCT_DETAILS']}?id=${data.id}`);
        }
        setContextUpdateProdData({} as ProductColumns);
        refetch();
      },
    },
  );

  const { mutate: updateMutation, isLoading: isUpdateLoading } = useMutation(
    async (dataObject: ProductColumns) => {
      const {
        attributes_list,
        inventory_details,
        tier_prices,
        product_links,
        category_associated,
        type_id,
        status,
      } = dataObject;

      const filteredAttributes = attributes_list
        ? Object.entries(attributes_list)
            .filter(([key, value]) => value !== undefined)
            .map(([attribute_code, value]) => {
              if (attribute_code === 'url_key') {
                return { attribute_code, value, enableRedirect: redirect };
              }
              return { attribute_code, value };
            })
        : [];

      const response = await axios.patch(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/${prodId}`,
        `${constants.CATALOG_URL}/v1/catalog-admin/product/${prodId}`,
        {
          status: status,
          type_id: type_id,
          category_associated: category_associated ? categoryIds : undefined,
          product_links: product_links
            ? {
                associated: product_links?.associated
                  ? product_links.associated
                  : undefined,
                crosssell: contextProdData?.product_links?.crosssell
                  ? contextProdData?.product_links?.crosssell
                  : undefined,
                upsell: contextProdData?.product_links?.upsell
                  ? contextProdData?.product_links?.upsell
                  : undefined,
                related: contextProdData?.product_links?.related
                  ? contextProdData?.product_links?.related
                  : undefined,
              }
            : undefined,
          attributes_list: filteredAttributes ? filteredAttributes : undefined,
          inventory_details: inventory_details ? inventory_details : undefined,
          tier_prices: tier_prices ? tier_prices : undefined,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
            admin_identifier: `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      return response;
    },
    {
      onError: (error: any) => {
        addToast('error', `${error?.response?.data?.message}`);
      },
      onSuccess(data, variables, context) {
        addToast('success', 'Product is Updated Successfully.');
        setContextUpdateProdData({} as ProductColumns);
        setIsUpdate(!isUpdate);
      },
      onSettled(data, error, variables, context) {
        refetch();
      },
    },
  );

  const handleSave = () => {
    const { type_id, attributes_list } = contextProdData;
    const { attributes_list: updateAttributes_list } = contextUpdateProdData;

    const hasNoChanges =
      (!contextUpdateProdData ||
        Object.keys(contextUpdateProdData).length === 0 ||
        (Object.keys(contextUpdateProdData).length === 1 &&
          updateAttributes_list &&
          Object.keys(updateAttributes_list).length === 0)) &&
      redirect === null;
    const isGrouped = contextProdData.type_id === 'grouped';
    const requiredFields = [
      contextProdData.attributes_list?.visibility,
      type_id,
      contextProdData.attributes_list?.name,
      !isGrouped && attributes_list?.price,
      !isGrouped && attributes_list?.special_price,
      !isGrouped && attributes_list?.manufacturer,
    ];
    const areRequiredFieldsFilled = requiredFields.every(
      (field) => field !== undefined && field !== '',
    );
    const isNameValid =
      attributes_list?.name && attributes_list?.name.trim() !== '';

    if (prodId) {
      if (hasNoChanges) {
        return addToast('info', 'There is no changes.');
      } else {
        if (!isNameValid) {
          addToast('error', 'Product Name should not be empty');
        } else if (productErrors.selling_price?.length > 0) {
          addToast('error', `${productErrors.selling_price}`);
        } else if (productErrors.price?.length > 0) {
          addToast('error', `${productErrors.price}`);
        } else if (productErrors.url_key?.length > 0) {
          addToast('error', `${productErrors.url_key}`);
        } else {
          updateMutation(contextUpdateProdData);
        }
      }
    } else {
      if (!isNameValid) {
        addToast('error', 'Product Name should not be empty');
      } else if (productErrors.selling_price?.length > 0) {
        addToast('error', `${productErrors.selling_price}`);
      } else if (productErrors.url_key?.length > 0) {
        addToast('error', `${productErrors.url_key}`);
      } else if (productErrors.selling_price?.length > 0) {
        addToast('error', `${productErrors.selling_price}`);
      } else if (productErrors.url_key?.length > 0) {
        addToast('error', `${productErrors.url_key}`);
      } else if (areRequiredFieldsFilled) {
        addMutation(contextUpdateProdData);
      } else {
        addToast('warning', 'Please fill the required fields');
      }
    }
  };

  const handleTabClick = (id: string) => {
    setActiveTab(id);
    // localStorage.setItem('activeTab', id);
  };

  useEffect(() => {
    console.log('contextProdData: ', contextProdData);
    console.log('contextUpdateProdData: ', contextUpdateProdData);
    const filterTabs = () => {
      if (prodId) {
        if (contextProdData.type_id !== 'grouped') {
          setFilteredTabs(tabs.filter((tab) => tab.id != 'tab7'));
        } else {
          setFilteredTabs(tabs);
        }
      } else {
        if (contextProdData.type_id !== 'grouped') {
          setFilteredTabs(
            tabs.filter(
              (tab) => tab.id != 'tab8' && tab.id != 'tab7' && tab.id != 'tab3',
            ),
          );
        } else {
          setFilteredTabs(
            tabs.filter((tab) => tab.id != 'tab8' && tab.id != 'tab3'),
          );
        }
      }
    };

    filterTabs();
  }, [contextProdData, contextUpdateProdData]);

  useEffect(() => {
    if (queryParams.get('id')) {
      const val = queryParams.get('id');
      setProdId(Number(val));
    }
    refetch();
  }, [queryParams.get('id'), prodId, refetchLog]);

  useEffect(() => {
    if (prodData) {
      setContextProdData(prodData);
    }
    if (prodData?.media_gallery_entries) {
      const gallery = prodData.media_gallery_entries.filter((item) => {
        return item?.image_tags
          ?.split(',')
          .map((tag) => tag.trim())
          .includes('thumbnail');
      });

      setImage(gallery[0]?.value ?? null);
    }
  }, [prodData]);

  useEffect(() => {
    const activeTabLabel: string =
      tabs.find((tab) => tab.id === activeTab)?.label || 'Product Information';
    setHeaderInformation({
      title: activeTabLabel,
      breadcrumbParent: id ? 'Products' : 'Add Product',
    });
  }, [activeTab]);

  useEffect(() => {
    setHeaderInformation({
      title: 'Product Information',
      breadcrumbParent: id ? 'Products' : 'Add Product',
    });
    localStorage.removeItem('groupedId');
    const metaData = detectBrowser();
    setUserData(metaData);
    setProductErrors({
      selling_price: '',
      price: '',
      compare_at_price: '',
      url_key: '',
    });
  }, []);

  return (
    <div style={{ background: '#fff', height: '100%' }}>
      <TopBar>
        <Col style={{ display: 'flex', gap: '10px' }} size={5}>
          {prodId ? (
            <>
              <img
                src={image ? image : ThumbnailIcon}
                style={{
                  width: `${baseTheme.components.dimension.width.md}`,
                  height: `${baseTheme.components.dimension.height.md}`,
                }}
                alt="thumbnail"
              />
              <div>
                <div
                  style={{ display: 'flex', alignItems: 'center', gap: '5px' }}
                >
                  ID -{' '}
                  {isLoading || isFetching ? (
                    <Skeleton
                      width={`${baseTheme.components.dimension.width.base100}`}
                      height={`${baseTheme.components.dimension.height.base}`}
                    />
                  ) : (
                    contextProdData?.id
                  )}
                </div>
                <div
                  style={{ display: 'flex', alignItems: 'center', gap: '5px' }}
                >
                  SKU -{' '}
                  {isLoading || isFetching ? (
                    <Skeleton
                      width={`${baseTheme.components.dimension.width.base100}`}
                      height={`${baseTheme.components.dimension.height.base}`}
                    />
                  ) : (
                    contextProdData?.sku
                  )}
                </div>
              </div>
            </>
          ) : null}
        </Col>
        <Col style={{ display: 'flex', justifyContent: 'end' }}>
          <Buttons
            style={{ margin: '0px 10px' }}
            onClick={() => {
              navigate(`${pageRoutes['GO_TO_PRODUCT_LIST']}/`);
            }}
          >
            <Buttons.StartIcon>
              <LeftArrowIcon />
            </Buttons.StartIcon>
            Back
          </Buttons>
          <Button onClick={handleSave} isPrimary>
            {addLoading || isUpdateLoading ? <Spinner /> : 'Save'}
          </Button>
        </Col>
      </TopBar>
      <Main>
        <TabContainer>
          {filteredTabs &&
            filteredTabs?.map((tab: any) => (
              <TabButton
                key={tab.id}
                id={tab.id}
                active={activeTab === tab.id}
                onClick={() => handleTabClick(tab.id)}
              >
                {tab.label}
              </TabButton>
            ))}
        </TabContainer>
        <ContentContainer>
          {filteredTabs.map((tab: any) => (
            <TabContent key={tab.id} active={activeTab === tab.id}>
              {tab.id === 'tab1' && <ProductInformation id={prodId} />}
              {tab.id === 'tab2' && <Description />}
              {tab.id === 'tab3' && <Media refetch={refetch} />}
              {tab.id === 'tab6' && <Marketing id={prodId} />}
              {tab.id === 'tab7' && <ProductGrouping />}
              {tab.id === 'tab8' && <Log fetchdata={refetch} id={prodId} />}
            </TabContent>
          ))}
        </ContentContainer>
      </Main>
    </div>
  );
};

export default AddProduct;
