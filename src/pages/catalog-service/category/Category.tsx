import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Col, Row } from '../../../components/UI-components/Grid';
import {
  AddIconWhiteBG,
  LeftArrowIcon,
  SearchIcon,
} from '../../../utils/icons';
import ThumbnailIcon from '../../../utils/icons/thumbnail.svg';
import { Button, Buttons } from '../../../components/UI-components/Button';
import { pageRoutes } from '../../../components/navigation/RouteConfig';
import AddCategory from '../../../components/modal/catalog-service/AddCategory';
import MultiLevelDropdown, { MenuItem } from '../CategoryDropDown';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Skeleton, Spinner } from '@zendeskgarden/react-loaders';
import { CatalogCategory } from '../../../types/types';
import CategoryTabs from '../../../components/layouts/catalog-service/category/CategoryTabs';
import useToast from '../../../hooks/useToast';
import constants from '../../../constants';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { useCategoryContext } from './CategoryContext';
import { useAuth } from '../../../components/providers/AuthProvider';
import {
  Dropdown,
  Autocomplete,
  Item,
  Menu,
  Label as DropLabel,
  Field as DropField,
} from '@zendeskgarden/react-dropdowns';
import axios from 'axios';

const TopBar = styled(Row)`
  padding: 10px 10px;
  justify-content: space-between;
`;

const StyledImage = styled.img`
  // width: 100%;
  // height: 100%;
  object-fit: cover;
  border-radius: 8px;
`;
export interface CategoryObj extends CatalogCategory {
  parent: CatalogCategory;
  uploadedFile?: any;
}

const Category = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const [isCategory, setIsCategory] = useState(false);
  const [urlKey, setUrlKey] = useState('');
  const { setHeaderInformation } = useAuth();
  const { titleError, setOriginalUrlKey } = useCategoryContext();
  const { id } = useParams();
  const [selectedCategory, setselectedCategory] = useState<MenuItem>();
  const [categoryObj, setcategoryObj] = useState<CategoryObj>(
    {} as CategoryObj,
  );
  const [categName, setCategName] = useState('');
  const [prodId, setProdId] = useState<number | undefined>(undefined);
  const [toggleCat, setToggleCat] = useState<boolean>(false);
  const [catObjectValue, setCatObjectValue] = useState<any>({});
  // const handleTabClick: React.MouseEventHandler<HTMLButtonElement> = (
  //   menu:menuTypes
  // ) => {
  //   setActiveTab(menu);
  // };

  // const axios = useAxios();

  const { data: categoryNameData, isLoading: categoryNameLoading } = useQuery({
    queryKey: ['category-name-list', categName],
    queryFn: async () => {
      const response = await axios.get(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories?page_no=${pageParam}&category_name=${search}`,
        `${constants.CATALOG_URL}/v1/catalog-admin/category?page_no=1&category_name=${categName}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
          },
        },
      );
      // console.log('API response:', response);
      return response?.data.items;
    },
  });

  const {
    data,
    isLoading,
    refetch: refetchCategoryData,
  } = useQuery({
    queryKey: ['category-list'],
    queryFn: async () => {
      const res = await axios.get(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/tree`,
        `${constants.CATALOG_URL}/v1/catalog-admin/category/category-tree/`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );

      return res?.data?.data?.categoryList as MenuItem[];
    },
  });

  useEffect(() => {
    setHeaderInformation({
      title: 'Categories',
      breadcrumbParent: 'Products',
    });
  }, []);

  const addToast = useToast();

  const { mutate: updateCategory, isLoading: isUpdating } = useMutation(
    async (categoryObj: CategoryObj) => {
      const formData = new FormData();
      const updateObj: { [key: string]: any } = {
        name: categoryObj.name,
        status: categoryObj.status,
        include_in_menu: categoryObj.include_in_menu,
        meta_title: categoryObj.meta_title,
        meta_description: categoryObj.meta_description,
        banner_web: categoryObj.banner_web,
        banner_app: categoryObj.banner_app,
        description: categoryObj.description,
        enableRedirect: categoryObj.enableRedirect,
        meta_keyword: categoryObj.meta_keyword,
        parent_id: categoryObj.parent,
        web_link: categoryObj.web_link,
        banner_title: categoryObj.banner_title,
      };

      if (urlKey) {
        updateObj.url_key = urlKey;
      }

      for (let key in updateObj) {
        if (updateObj[key] !== undefined) {
          formData.append(key, updateObj[key]);
        }
      }
      if (categoryObj.uploadedFile) {
        formData.append('files', categoryObj.uploadedFile);
      }

      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/${selectedCategory?.id}`,
        `${constants.CATALOG_URL}/v1/catalog-admin/category/update/${selectedCategory?.id}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('access-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'multipart/form-data;',
          },
        },
      );

      return response;
    },
    {
      onSuccess() {
        addToast('success', 'category updated successfully');
        refetchCategoryData();
        setCatObjectValue({});
        setToggleCat(!toggleCat);
        setUrlKey('');
        setOriginalUrlKey('');
      },
      onError(err) {
        addToast('error', (err as any)?.message);
      },
    },
  );

  useEffect(() => {
    if (queryParams.get('id')) {
      const val = queryParams.get('id');
      setProdId(Number(val));
    }
  }, [queryParams.get('id')]);

  useEffect(() => {
    const idParam = selectedCategory?.id ?? prodId;
    if (idParam !== undefined) {
      navigate(`${pageRoutes['GO_TO_PRODUCT_CATEGORY_DETAIL']}id=${idParam}`);
    } else {
      navigate(`${pageRoutes['GO_TO_PRODUCT_CATEGORY_DETAIL']}`);
    }
  }, [selectedCategory, prodId, navigate]);

  useEffect(() => {
    if (prodId) {
      queryParams.set('id', prodId.toString());
    }
  }, []);

  return (
    <div
      style={{
        background: '#fff',
        minHeight: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <TopBar>
        {selectedCategory ? (
          <>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '100%',
                padding: '0 15px',
              }}
            >
              <div
                style={{ display: 'flex', alignItems: 'center', gap: '15px' }}
              >
                <div style={{ height: '100px', width: 'auto', flexShrink: 0 }}>
                  <img
                    src={
                      categoryObj.image
                        ? categoryObj.image.startsWith('https://')
                          ? categoryObj.image
                          : `https://images1.dentalkart.com/${categoryObj.image}`
                        : ThumbnailIcon
                    }
                    style={{
                      height: '100%',
                      width: '100%',
                      objectFit: 'cover',
                      borderRadius: '4px',
                    }}
                  />
                </div>
                <div style={{ color: 'rgb(32, 83, 117)' }}>
                  <div>
                    <strong>Category ID</strong> - {selectedCategory?.id}
                  </div>
                  <div>
                    <strong>Category Name</strong> - {selectedCategory?.name}
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '10px' }}>
                <Buttons
                  onClick={() => {
                    // navigate(`${pageRoutes['GO_TO_PRODUCT_CATEGORY_DETAIL']}`);
                    setselectedCategory(undefined);
                    setProdId(undefined);
                  }}
                  isAction
                >
                  <Buttons.StartIcon>
                    <LeftArrowIcon />
                  </Buttons.StartIcon>
                  Back
                </Buttons>
                <Button
                  onClick={() => {
                    if (titleError.length > 0) {
                      addToast('error', `${titleError}`);
                    } else if (
                      (catObjectValue &&
                        Object.keys(catObjectValue).length > 0) ||
                      catObjectValue?.uploadedFile != undefined ||
                      urlKey.length > 0
                    ) {
                      updateCategory(catObjectValue);
                    } else {
                      addToast('info', 'No changes made to Save!');
                    }
                  }}
                >
                  {isUpdating ? <Spinner /> : 'Save'}
                </Button>
              </div>
            </div>
          </>
        ) : (
          <></>
        )}
      </TopBar>
      <div>
        <Row ml="lg">
          <Col
            lg={4}
            style={{
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <Button isPrimary onClick={() => setIsCategory(!isCategory)}>
              <Button.StartIcon>
                <AddIconWhiteBG />
              </Button.StartIcon>
              Add Category
            </Button>
            {isLoading && (
              <>
                {new Array(8).fill(0).map((_, ind) => (
                  <Skeleton
                    key={ind}
                    style={{
                      height: '20px',
                      width: '100%',
                      marginBottom: '0.5rem',
                    }}
                  ></Skeleton>
                ))}
              </>
            )}
            {!isLoading && data && (
              <MultiLevelDropdown
                refetch={refetchCategoryData}
                selectedCategory={selectedCategory}
                setselectedCategory={setselectedCategory}
                menuItems={data}
                paramId={prodId}
                setProdId={setProdId}
              />
            )}
          </Col>
          <Col lg={8}>
            {selectedCategory?.id !== undefined || prodId ? (
              <CategoryTabs
                categoryObj={categoryObj}
                setCategoryObj={(cat) => setcategoryObj(cat)}
                categoryValue={catObjectValue}
                setCategoryValue={setCatObjectValue}
                selectedCategory={selectedCategory}
                setSelectedCategory={setselectedCategory}
                parentCategories={
                  data ? data?.map((d) => ({ name: d.name, id: d.id })) : []
                }
                urlKey={urlKey}
                setUrlKey={setUrlKey}
                refetchTree={refetchCategoryData}
                setToggleCat={setToggleCat}
                toggleCat={toggleCat}
                prodId={prodId}
              />
            ) : (
              <div style={{ padding: '0px 20px' }}>
                <Dropdown
                  inputValue={categName}
                  selectedItem={categName}
                  onSelect={(i) => {
                    setProdId(i?.id);
                    setCategName('');
                  }}
                  onInputValueChange={(value) => setCategName(value)}
                  downshiftProps={{
                    itemToString: (item: { name: string; id: number }) => {
                      return item;
                    },
                  }}
                >
                  <DropField>
                    <DropLabel>Select Category to View Details</DropLabel>
                    <Autocomplete start={<SearchIcon />}>
                      {categName}
                    </Autocomplete>
                  </DropField>
                  <Menu style={{ maxHeight: '300px' }}>
                    {categoryNameData?.length ? (
                      categoryNameData.map(
                        (option: any, index: React.Key | null | undefined) => {
                          // const categoryPath = findCategoryPath(treeData, option.id);
                          return (
                            <Item key={index} value={option}>
                              <>{`${option.name} (ID: ${option.id})`}</>
                            </Item>
                          );
                        },
                      )
                    ) : (
                      <Item disabled>No matches found</Item>
                    )}
                    {categoryNameLoading && (
                      <Row justifyContent="center">
                        <Spinner />
                      </Row>
                    )}
                  </Menu>
                </Dropdown>
              </div>
            )}
          </Col>
        </Row>
        {isCategory && (
          <AddCategory
            close={() => {
              setIsCategory(false);
            }}
            parentCategories={
              data ? data?.map((d) => ({ name: d.name, id: d.id })) : []
            }
            refetch={refetchCategoryData}
          />
        )}
      </div>
    </div>
  );
};

export default Category;
