import { useEffect, useState } from 'react';
import { Container } from '../../components/UI-components/Container';
import AccordionBody from '../../components/accordion/AccordionBody';
import { Accordion } from '../../components/UI-components/Accordion';
// import ProductTypeConfig from '../../components/table/product/config/ProductTypeConfig';
import { useAuth } from '../../components/providers/AuthProvider';
import ProductTypeConfig from '../../components/table/product/config/ProductTypeConfig';

const ProductFeedConfig = () => {
  const [expandedSections, setExpandedSections] = useState<number[]>([]);
  const { setHeaderInformation } = useAuth();
  useEffect(() => {
    setHeaderInformation({
      title: 'Product Feed Config',
      breadcrumbParent: 'Product Feeds',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // console.log(<ProductTypeConfig />, 'TETSBSHJS');
  return (
    <>
      <Container>
        <Accordion
          level={4}
          isBare
          expandedSections={expandedSections}
          onChange={(index) => {
            if (expandedSections.includes(index)) {
              setExpandedSections(expandedSections.filter((n) => n !== index));
            } else {
              setExpandedSections([...expandedSections, index]);
            }
          }}
        >
          <AccordionBody
            title={'Product Type'}
            expandedSections={expandedSections}
            expandedRow={0}
            children={<ProductTypeConfig />}
          />
        </Accordion>
      </Container>
    </>
  );
};

export default ProductFeedConfig;
