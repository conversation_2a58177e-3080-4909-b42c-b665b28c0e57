import { useState, useEffect, SetStateAction } from 'react';
import LazyLoading from '../../components/UI-components/LazyLoading';
import NothingToshow from '../../components/UI-components/NothingToShow';
import QuestionAndAnswerLayout from '../../components/layouts/questions-and-answers/QuestionAndAnswerLayout';
import useToast from '../../hooks/useToast';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../components/providers/AuthProvider';
import useAxios from '../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import krakendPaths from '../../constants/krakendPaths';

const QuestionAndAnswer = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const axios = useAxios();
  const queryParams = new URLSearchParams(location.search);

  const addToast = useToast();
  const { setHeaderInformation, logout } = useAuth();
  const [filters, setFilters] = useState<any>({
    rowsPerPage: 20,
    enable: queryParams.get('enable') || '',
    status: queryParams.get('status') || '',
    search: queryParams.get('search') || '',
    product_id: queryParams.get('product_id') ? Number(queryParams.get('product_id')) : '',
    product_name: queryParams.get('product_name') || '',
    // ...JSON.parse(localStorage.getItem('questions-filters') as string),
    pageNumber:
      Number(queryParams.get('page')) > 0 ? Number(queryParams.get('page')) : 1,
  });


  const {
    data: queryData,
    refetch,
    isLoading: queryLoading,
    error: queryError,
  } = useQuery({
    queryKey: ['get-qna', filters],
    queryFn: async (): Promise<any> => {
      const params: Record<string, any> = {};
      if (filters.pageNumber !== undefined) params.page = filters.pageNumber;
      if (filters.rowsPerPage !== undefined)
        params.pageSize = filters.rowsPerPage;
      if (
        !isNaN(filters.product_id) &&
        filters?.product_id !== '' &&
        filters?.product_id !== 0
      )
        params.product_id = filters.product_id;
      if (filters.search) params.search = filters.search;
      if (filters.status !== undefined) params.status = filters.status;
      if (filters.enable !== undefined) params.enable = filters.enable;
      if (filters.product_name !== undefined)
        params.product_name = filters.product_name;
      const response = await axios.get(
        `${krakendPaths.QNA_URL}/admin-api/v1/qna`,
        {
          params,
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    onError: (err: any) => {
      addToast('error', err?.message);
    },
  });

  useEffect(() => {
    // Create a new URLSearchParams object
    const newQueryParams = new URLSearchParams();
    
    // Add all filter parameters to URL
    if (filters.pageNumber) newQueryParams.set('page', filters.pageNumber.toString());
    if (filters.status) newQueryParams.set('status', filters.status);
    if (filters.enable) newQueryParams.set('enable', filters.enable);
    if (filters.search) newQueryParams.set('search', filters.search);
    if (filters.product_id) newQueryParams.set('product_id', filters.product_id.toString());
    if (filters.product_name) newQueryParams.set('product_name', filters.product_name);
    
    // Navigate with the updated query params
    navigate({ search: newQueryParams.toString() }, { replace: true });
  }, [filters, navigate]);

  useEffect(() => {
    setHeaderInformation({
      title: 'Questions and Answers ',
      breadcrumbParent: '',
    });
    // We don't need to set URL parameters here as the filter-watching useEffect will handle that
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // This useEffect watches for changes in the URL and updates the filter state
  useEffect(() => {
    const page = Number(queryParams.get('page'));
    const status = queryParams.get('status') || '';
    const enable = queryParams.get('enable') || '';
    const search = queryParams.get('search') || '';
    const product_id = queryParams.get('product_id') ? Number(queryParams.get('product_id')) : '';
    const product_name = queryParams.get('product_name') || '';
    
    // Check if any URL parameters have changed from our current filter state
    const pageChanged = page !== 0 && page !== filters.pageNumber;
    const statusChanged = status !== filters.status;
    const enableChanged = enable !== filters.enable;
    const searchChanged = search !== filters.search;
    const productIdChanged = product_id !== '' && product_id !== filters.product_id;
    const productNameChanged = product_name !== filters.product_name;
    
    // If any parameter has changed, update the filters state
    if (pageChanged || statusChanged || enableChanged || searchChanged || productIdChanged || productNameChanged) {
      setFilters((prev: any) => ({
        ...prev,
        pageNumber: page !== 0 ? page : 1,
        status: status !== filters.status ? status : filters.status,
        enable: enable !== filters.enable ? enable : filters.enable,
        search: search !== filters.search ? search : filters.search,
        product_id: product_id !== filters.product_id ? product_id : filters.product_id,
        product_name: product_name !== filters.product_name ? product_name : filters.product_name,
      }));
    }
  }, [location.search]); // Watch for any changes in the URL query string

  if (
    queryData &&
    (queryData.result || queryData?.result?.length === 0) &&
    (queryData?.count || queryData?.count === 0) &&
    refetch
  )


    return (
      <QuestionAndAnswerLayout
        rows={queryData?.result}
        count={queryData?.count}
        refetch={refetch}
        filters={filters}
        setFilters={setFilters}
        setIsQueryEnabled={function (value: SetStateAction<boolean>): void {
          throw new Error('Function not implemented.');
        }}
      />
    );
  else if (queryLoading) return <LazyLoading />;
  else if (queryError) return <NothingToshow />;
  else return <NothingToshow />;
};

export default QuestionAndAnswer;
