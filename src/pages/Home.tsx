import { ReactComponent as HomePageArt } from '../assets/home.svg';
import { Col, Row } from '../components/UI-components/Grid';
import { LogoIcon } from '../utils/icons';
import { XXL, XXXL } from '../components/UI-components/Typography';
import { baseTheme } from '../themes/theme';
import { Span } from '@zendeskgarden/react-typography';
import { mediaQuery } from '@zendeskgarden/react-theming';
import styled from 'styled-components';
import { Container } from '../components/UI-components/Container';
import { useEffect } from 'react';
import { useAuth } from '../components/providers/AuthProvider';

const HomePageImg = styled(HomePageArt)`
  ${(p) => mediaQuery('down', 'md', p.theme)} {
    width: 400px;
    height: 50px;
  }
`;

const Home = () => {
  const { setHeaderInformation } = useAuth();
  useEffect(() => {
    setHeaderInformation({
      title: 'Home',
      breadcrumbParent: '',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <Container haveBackground>
      <Row
        justifyContent="center"
        alignItems="center"
        style={{ padding: baseTheme.space.md }}
      >
        <Col>
          <XXXL isCenter hue="primary">
            <Span>
              <Span.Icon>
                <LogoIcon
                  style={{
                    width: `${baseTheme.components.dimension.width.base * 5}px`,
                    height: `${
                      baseTheme.components.dimension.width.base * 5
                    }px`,
                    marginRight: '4%',
                  }}
                />
              </Span.Icon>
              Welcome To Admin Portal
              <Span.Icon>
                <LogoIcon
                  style={{
                    width: `${baseTheme.components.dimension.width.base * 5}px`,
                    height: `${
                      baseTheme.components.dimension.width.base * 5
                    }px`,
                    marginLeft: '4%',
                  }}
                />
              </Span.Icon>
            </Span>
          </XXXL>
        </Col>
      </Row>
      <Row justifyContent="center" alignItems="center">
        <HomePageImg />
      </Row>
    </Container>
  );
};

export default Home;
