{"version": 3, "sources": ["../../node_modules/@zendeskgarden/container-utilities/dist/index.esm.js", "../../node_modules/@zendeskgarden/container-utilities/node_modules/@reach/auto-id/dist/reach-auto-id.mjs", "../../node_modules/@zendeskgarden/container-utilities/node_modules/@reach/auto-id/node_modules/@reach/utils/dist/reach-utils.mjs"], "sourcesContent": ["/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport { useRef, useEffect } from 'react';\nexport { useId } from '@reach/auto-id';\n\nfunction composeEventHandlers() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n  return function (event) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return fns.some(fn => {\n      fn && fn(event, ...args);\n      return event && event.defaultPrevented;\n    });\n  };\n}\n\nlet idCounter = 0;\nfunction generateId(prefix) {\n  if (prefix === void 0) {\n    prefix = 'garden';\n  }\n  return `${prefix}-${idCounter++}`;\n}\nfunction setIdCounter(num) {\n  idCounter = num;\n}\n\nfunction getControlledValue() {\n  for (var _len = arguments.length, values = new Array(_len), _key = 0; _key < _len; _key++) {\n    values[_key] = arguments[_key];\n  }\n  for (const value of values) {\n    if (value !== undefined) {\n      return value;\n    }\n  }\n  return undefined;\n}\n\nfunction useCombinedRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  const targetRef = useRef(null);\n  useEffect(() => {\n    console.warn('\"useCombinedRefs()\" has been deprecated and will be removed in the next major version release of \"@zendeskgarden/container-utilities\"');\n    refs.forEach(ref => {\n      if (!ref) return;\n      if (typeof ref === 'function') {\n        ref(targetRef.current);\n      } else {\n        ref.current = targetRef.current;\n      }\n    });\n  }, []);\n  return targetRef;\n}\n\nconst convertToMatrix = (array, columnCount) => {\n  return array.reduce((acc, curr) => {\n    if (acc.length === 0) return [[curr]];\n    if (acc[acc.length - 1].length < columnCount) {\n      acc[acc.length - 1].push(curr);\n    } else {\n      acc.push([curr]);\n    }\n    return acc;\n  }, []);\n};\n\nconst KEY_CODES = {\n  ALT: 18,\n  ASTERISK: 170,\n  BACKSPACE: 8,\n  COMMA: 188,\n  DELETE: 46,\n  DOWN: 40,\n  END: 35,\n  ENTER: 13,\n  ESCAPE: 27,\n  HOME: 36,\n  LEFT: 37,\n  NUMPAD_ADD: 107,\n  NUMPAD_DECIMAL: 110,\n  NUMPAD_DIVIDE: 111,\n  NUMPAD_ENTER: 108,\n  NUMPAD_MULTIPLY: 106,\n  NUMPAD_SUBTRACT: 109,\n  PAGE_DOWN: 34,\n  PAGE_UP: 33,\n  PERIOD: 190,\n  RIGHT: 39,\n  SHIFT: 16,\n  SPACE: 32,\n  TAB: 9,\n  UP: 38\n};\nconst KEYS = {\n  ALT: 'Alt',\n  ASTERISK: '*',\n  BACKSPACE: 'Backspace',\n  COMMA: ',',\n  DELETE: 'Delete',\n  DOWN: 'ArrowDown',\n  END: 'End',\n  ENTER: 'Enter',\n  ESCAPE: 'Escape',\n  HOME: 'Home',\n  LEFT: 'ArrowLeft',\n  NUMPAD_ADD: 'Add',\n  NUMPAD_DECIMAL: 'Decimal',\n  NUMPAD_DIVIDE: 'Divide',\n  NUMPAD_ENTER: 'Enter',\n  NUMPAD_MULTIPLY: 'Multiply',\n  NUMPAD_SUBTRACT: 'Subtract',\n  PAGE_DOWN: 'PageDown',\n  PAGE_UP: 'PageUp',\n  PERIOD: '.',\n  RIGHT: 'ArrowRight',\n  SHIFT: 'Shift',\n  SPACE: ' ',\n  TAB: 'Tab',\n  UNIDENTIFIED: 'Unidentified',\n  UP: 'ArrowUp'\n};\n\nvar DocumentPosition;\n(function (DocumentPosition) {\n  DocumentPosition[DocumentPosition[\"DISCONNECTED\"] = 1] = \"DISCONNECTED\";\n  DocumentPosition[DocumentPosition[\"PRECEDING\"] = 2] = \"PRECEDING\";\n  DocumentPosition[DocumentPosition[\"FOLLOWING\"] = 4] = \"FOLLOWING\";\n  DocumentPosition[DocumentPosition[\"CONTAINS\"] = 8] = \"CONTAINS\";\n  DocumentPosition[DocumentPosition[\"CONTAINED_BY\"] = 16] = \"CONTAINED_BY\";\n  DocumentPosition[DocumentPosition[\"IMPLEMENTATION_SPECIFIC\"] = 32] = \"IMPLEMENTATION_SPECIFIC\";\n})(DocumentPosition || (DocumentPosition = {}));\n\nexport { DocumentPosition, KEYS, KEY_CODES, composeEventHandlers, convertToMatrix, generateId, getControlledValue, setIdCounter, useCombinedRefs };\n", "\"use strict\";\n/**\n  * @reach/auto-id v0.18.0\n  *\n  * Copyright (c) 2018-2022, React Training LLC\n  *\n  * This source code is licensed under the MIT license found in the\n  * LICENSE.md file in the root directory of this source tree.\n  *\n  * @license MIT\n  */\n\n\n// src/reach-auto-id.ts\nimport * as React from \"react\";\nimport { useIsomorphicLayoutEffect as useLayoutEffect } from \"@reach/utils\";\nvar serverHandoffComplete = false;\nvar id = 0;\nfunction genId() {\n  return ++id;\n}\nvar maybeReactUseId = React[\"useId\".toString()];\nfunction useId(providedId) {\n  if (maybeReactUseId !== void 0) {\n    let generatedId = maybeReactUseId();\n    return providedId ?? generatedId;\n  }\n  let initialId = providedId ?? (serverHandoffComplete ? genId() : null);\n  let [id2, setId] = React.useState(initialId);\n  useLayoutEffect(() => {\n    if (id2 === null) {\n      setId(genId());\n    }\n  }, []);\n  React.useEffect(() => {\n    if (serverHandoffComplete === false) {\n      serverHandoffComplete = true;\n    }\n  }, []);\n  return providedId ?? id2 ?? void 0;\n}\nexport {\n  useId\n};\n", "\"use strict\";\n/**\n  * @reach/utils v0.18.0\n  *\n  * Copyright (c) 2018-2022, React Training LLC\n  *\n  * This source code is licensed under the MIT license found in the\n  * LICENSE.md file in the root directory of this source tree.\n  *\n  * @license MIT\n  */\n\n\n// src/can-use-dom.ts\nfunction canUseDOM() {\n  return !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\n}\n\n// src/clone-valid-element.ts\nimport { cloneElement, isValidElement } from \"react\";\nfunction cloneValidElement(element, props, ...children) {\n  return isValidElement(element) ? cloneElement(element, props, ...children) : element;\n}\n\n// src/compose-event-handlers.ts\nfunction composeEventHandlers(theirHand<PERSON>, ourHandler) {\n  return (event) => {\n    theirHandler && theirHandler(event);\n    if (!event.defaultPrevented) {\n      return ourHandler(event);\n    }\n  };\n}\n\n// src/compose-refs.ts\nimport { useCallback } from \"react\";\n\n// src/type-check.ts\nfunction isBoolean(value) {\n  return typeof value === \"boolean\";\n}\nfunction isFunction(value) {\n  return !!(value && {}.toString.call(value) == \"[object Function]\");\n}\nfunction isNumber(value) {\n  return typeof value === \"number\" && !isNaN(value);\n}\nfunction isString(value) {\n  return typeof value === \"string\";\n}\n\n// src/compose-refs.ts\nfunction assignRef(ref, value) {\n  if (ref == null)\n    return;\n  if (isFunction(ref)) {\n    ref(value);\n  } else {\n    try {\n      ref.current = value;\n    } catch (error) {\n      throw new Error(`Cannot assign value \"${value}\" to ref \"${ref}\"`);\n    }\n  }\n}\nfunction useComposedRefs(...refs) {\n  return useCallback((node) => {\n    for (let ref of refs) {\n      assignRef(ref, node);\n    }\n  }, refs);\n}\n\n// src/owner-document.ts\nfunction getOwnerDocument(element) {\n  return canUseDOM() ? element ? element.ownerDocument : document : null;\n}\nfunction getOwnerWindow(element) {\n  let ownerDocument = getOwnerDocument(element);\n  return ownerDocument ? ownerDocument.defaultView || window : null;\n}\n\n// src/computed-styles.ts\nfunction getComputedStyles(element) {\n  let ownerWindow = getOwnerWindow(element);\n  if (ownerWindow) {\n    return ownerWindow.getComputedStyle(element, null);\n  }\n  return null;\n}\nfunction getComputedStyle(element, styleProp) {\n  return getComputedStyles(element)?.getPropertyValue(styleProp) || null;\n}\n\n// src/context.tsx\nimport * as React from \"react\";\nfunction createNamedContext(name, defaultValue) {\n  let Ctx = React.createContext(defaultValue);\n  Ctx.displayName = name;\n  return Ctx;\n}\nfunction createContext2(rootComponentName, defaultContext) {\n  let Ctx = React.createContext(defaultContext);\n  function Provider(props) {\n    let { children, ...context } = props;\n    let value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ React.createElement(Ctx.Provider, {\n      value\n    }, children);\n  }\n  function useContext2(callerComponentName) {\n    let context = React.useContext(Ctx);\n    if (context) {\n      return context;\n    }\n    if (defaultContext) {\n      return defaultContext;\n    }\n    throw Error(`${callerComponentName} must be rendered inside of a ${rootComponentName} component.`);\n  }\n  Ctx.displayName = `${rootComponentName}Context`;\n  Provider.displayName = `${rootComponentName}Provider`;\n  return [Provider, useContext2];\n}\n\n// src/dev-utils.ts\nimport { useRef, useEffect } from \"react\";\nfunction useControlledSwitchWarning(controlledValue, controlledPropName, componentName) {\n  if (true) {\n    let controlledRef = useRef(controlledValue != null);\n    let nameCache = useRef({ componentName, controlledPropName });\n    useEffect(() => {\n      nameCache.current = { componentName, controlledPropName };\n    }, [componentName, controlledPropName]);\n    useEffect(() => {\n      let { current: wasControlled } = controlledRef;\n      let { componentName: componentName2, controlledPropName: controlledPropName2 } = nameCache.current;\n      let isControlled = controlledValue != null;\n      if (wasControlled !== isControlled) {\n        console.error(`A component is changing an ${wasControlled ? \"\" : \"un\"}controlled \\`${controlledPropName2}\\` state of ${componentName2} to be ${wasControlled ? \"un\" : \"\"}controlled. This is likely caused by the value changing from undefined to a defined value, which should not happen. Decide between using a controlled or uncontrolled ${componentName2} element for the lifetime of the component.\n      More info: https://fb.me/react-controlled-components`);\n      }\n    }, [controlledValue]);\n  }\n}\n\n// src/get-document-dimensions.ts\nfunction getDocumentDimensions(element) {\n  let ownerDocument = getOwnerDocument(element);\n  let ownerWindow = ownerDocument.defaultView || window;\n  if (!ownerDocument) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n  return {\n    width: ownerDocument.documentElement.clientWidth ?? ownerWindow.innerWidth,\n    height: ownerDocument.documentElement.clientHeight ?? ownerWindow.innerHeight\n  };\n}\n\n// src/get-scroll-position.ts\nfunction getScrollPosition(element) {\n  let ownerWindow = getOwnerWindow(element);\n  if (!ownerWindow) {\n    return {\n      scrollX: 0,\n      scrollY: 0\n    };\n  }\n  return {\n    scrollX: ownerWindow.scrollX,\n    scrollY: ownerWindow.scrollY\n  };\n}\n\n// src/get-scrollbar-offset.ts\nfunction getScrollbarOffset() {\n  try {\n    if (window.innerWidth > document.documentElement.clientWidth) {\n      return window.innerWidth - document.documentElement.clientWidth;\n    }\n  } catch (err) {\n  }\n  return 0;\n}\n\n// src/is-right-click.ts\nfunction isRightClick(nativeEvent) {\n  return \"which\" in nativeEvent ? nativeEvent.which === 3 : \"button\" in nativeEvent ? nativeEvent.button === 2 : false;\n}\n\n// src/make-id.ts\nfunction makeId(...args) {\n  return args.filter((val) => val != null).join(\"--\");\n}\n\n// src/noop.ts\nfunction noop() {\n}\n\n// src/use-constant.ts\nimport { useRef as useRef2 } from \"react\";\nfunction useConstant(fn) {\n  const ref = useRef2();\n  if (!ref.current) {\n    ref.current = { v: fn() };\n  }\n  return ref.current.v;\n}\n\n// src/use-controlled-state.ts\nimport { useState, useRef as useRef3, useCallback as useCallback2 } from \"react\";\nfunction useControlledState({\n  controlledValue,\n  defaultValue,\n  calledFrom = \"A component\"\n}) {\n  let wasControlled = controlledValue !== void 0;\n  let isControlledRef = useRef3(wasControlled);\n  if (true) {\n    if (!isControlledRef.current && wasControlled) {\n      console.warn(`${calledFrom} is changing from controlled to uncontrolled. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n    }\n    if (isControlledRef.current && !wasControlled) {\n      console.warn(`${calledFrom} is changing from uncontrolled to controlled. Components should not switch from uncontrolled to controlled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n    }\n  }\n  let [valueState, setValue] = useState(isControlledRef.current ? controlledValue : defaultValue);\n  let set = useCallback2((n) => {\n    if (!isControlledRef.current) {\n      setValue(n);\n    }\n  }, []);\n  return [isControlledRef.current ? controlledValue : valueState, set];\n}\n\n// src/use-event-listener.ts\nimport { useRef as useRef4, useEffect as useEffect2 } from \"react\";\nfunction useEventListener(eventName, listener, element = window) {\n  const savedHandler = useRef4(listener);\n  useEffect2(() => {\n    savedHandler.current = listener;\n  }, [listener]);\n  useEffect2(() => {\n    const isSupported = element && element.addEventListener;\n    if (!isSupported) {\n      if (true) {\n        console.warn(\"Event listener not supported on the element provided\");\n      }\n      return;\n    }\n    function eventListener(event) {\n      savedHandler.current(event);\n    }\n    element.addEventListener(eventName, eventListener);\n    return () => {\n      element.removeEventListener(eventName, eventListener);\n    };\n  }, [eventName, element]);\n}\n\n// src/use-focus-change.ts\nimport { useRef as useRef5, useEffect as useEffect3 } from \"react\";\nfunction useFocusChange(handleChange = console.log, when = \"focus\", ownerDocument = document) {\n  let lastActiveElement = useRef5(ownerDocument.activeElement);\n  useEffect3(() => {\n    lastActiveElement.current = ownerDocument.activeElement;\n    function onChange(event) {\n      if (lastActiveElement.current !== ownerDocument.activeElement) {\n        handleChange(ownerDocument.activeElement, lastActiveElement.current, event);\n        lastActiveElement.current = ownerDocument.activeElement;\n      }\n    }\n    ownerDocument.addEventListener(when, onChange, true);\n    return () => {\n      ownerDocument.removeEventListener(when, onChange);\n    };\n  }, [when, handleChange, ownerDocument]);\n}\n\n// src/use-force-update.ts\nimport { useState as useState2, useCallback as useCallback3 } from \"react\";\nfunction useForceUpdate() {\n  let [, dispatch] = useState2(/* @__PURE__ */ Object.create(null));\n  return useCallback3(() => {\n    dispatch(/* @__PURE__ */ Object.create(null));\n  }, []);\n}\n\n// src/use-isomorphic-layout-effect.ts\nimport { useEffect as useEffect4, useLayoutEffect } from \"react\";\nvar useIsomorphicLayoutEffect = canUseDOM() ? useLayoutEffect : useEffect4;\n\n// src/use-lazy-ref.ts\nimport { useRef as useRef6 } from \"react\";\nfunction useLazyRef(fn) {\n  let isSet = useRef6(false);\n  let ref = useRef6();\n  if (!isSet.current) {\n    isSet.current = true;\n    ref.current = fn();\n  }\n  return ref;\n}\n\n// src/use-previous.ts\nimport { useRef as useRef7, useEffect as useEffect5 } from \"react\";\nfunction usePrevious(value) {\n  const ref = useRef7(null);\n  useEffect5(() => {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n}\n\n// src/use-stable-callback.ts\nimport { useRef as useRef8, useEffect as useEffect6, useCallback as useCallback4 } from \"react\";\nfunction createStableCallbackHook(useEffectHook, callback) {\n  let callbackRef = useRef8(callback);\n  useEffectHook(() => {\n    callbackRef.current = callback;\n  });\n  return useCallback4((...args) => {\n    callbackRef.current && callbackRef.current(...args);\n  }, []);\n}\nfunction useStableCallback(callback) {\n  return createStableCallbackHook(useEffect6, callback);\n}\nfunction useStableLayoutCallback(callback) {\n  return createStableCallbackHook(useIsomorphicLayoutEffect, callback);\n}\n\n// src/use-stateful-ref-value.ts\nimport { useState as useState3, useCallback as useCallback5 } from \"react\";\nfunction useStatefulRefValue(ref, initialState) {\n  let [state, setState] = useState3(initialState);\n  let callbackRef = useCallback5((refValue) => {\n    ref.current = refValue;\n    setState(refValue);\n  }, []);\n  return [state, callbackRef];\n}\n\n// src/use-update-effect.ts\nimport { useRef as useRef9, useEffect as useEffect7 } from \"react\";\nfunction useUpdateEffect(effect, deps) {\n  const mounted = useRef9(false);\n  useEffect7(() => {\n    if (mounted.current) {\n      effect();\n    } else {\n      mounted.current = true;\n    }\n  }, deps);\n}\nexport {\n  assignRef,\n  canUseDOM,\n  cloneValidElement,\n  composeEventHandlers,\n  createContext2 as createContext,\n  createNamedContext,\n  getComputedStyle,\n  getComputedStyles,\n  getDocumentDimensions,\n  getOwnerDocument,\n  getOwnerWindow,\n  getScrollPosition,\n  getScrollbarOffset,\n  isBoolean,\n  isFunction,\n  isNumber,\n  isRightClick,\n  isString,\n  makeId,\n  noop,\n  useComposedRefs,\n  useConstant,\n  useControlledState,\n  useControlledSwitchWarning,\n  useEventListener,\n  useFocusChange,\n  useForceUpdate,\n  useIsomorphicLayoutEffect,\n  useLazyRef,\n  usePrevious,\n  useStableCallback,\n  useStableLayoutCallback,\n  useStatefulRefValue,\n  useUpdateEffect\n};\n"], "mappings": ";;;;;;;;AAOA,IAAAA,iBAAkC;;;ACOlC,IAAAC,SAAuB;;;ACKvB,mBAA6C;AAgB7C,IAAAC,gBAA4B;AA4D5B,YAAuB;AA+BvB,IAAAA,gBAAkC;AA6ElC,IAAAA,gBAAkC;AAUlC,IAAAA,gBAAyE;AA0BzE,IAAAA,gBAA2D;AAyB3D,IAAAA,gBAA2D;AAmB3D,IAAAA,gBAAmE;AASnE,IAAAA,gBAAyD;AAIzD,IAAAA,iBAAkC;AAYlC,IAAAA,iBAA2D;AAU3D,IAAAA,iBAAwF;AAkBxF,IAAAA,iBAAmE;AAWnE,IAAAA,iBAA2D;AA7U3D,SAAS,YAAY;AACnB,SAAO,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS;AAChF;AAqRA,IAAI,4BAA4B,UAAU,IAAI,gCAAkB,cAAAC;;;ADrRhE,IAAI,wBAAwB;AAC5B,IAAI,KAAK;AACT,SAAS,QAAQ;AACf,SAAO,EAAE;AACX;AACA,IAAI,kBAAkBC,OAAM,QAAQ,SAAS,CAAC;AAC9C,SAAS,MAAM,YAAY;AACzB,MAAI,oBAAoB,QAAQ;AAC9B,QAAI,cAAc,gBAAgB;AAClC,WAAO,cAAc;AAAA,EACvB;AACA,MAAI,YAAY,eAAe,wBAAwB,MAAM,IAAI;AACjE,MAAI,CAAC,KAAK,KAAK,IAAU,gBAAS,SAAS;AAC3C,4BAAgB,MAAM;AACpB,QAAI,QAAQ,MAAM;AAChB,YAAM,MAAM,CAAC;AAAA,IACf;AAAA,EACF,GAAG,CAAC,CAAC;AACL,EAAM,iBAAU,MAAM;AACpB,QAAI,0BAA0B,OAAO;AACnC,8BAAwB;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO,cAAc,OAAO;AAC9B;;;AD9BA,SAAS,uBAAuB;AAC9B,WAAS,OAAO,UAAU,QAAQ,MAAM,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACtF,QAAI,IAAI,IAAI,UAAU,IAAI;AAAA,EAC5B;AACA,SAAO,SAAU,OAAO;AACtB,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,WAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,IACnC;AACA,WAAO,IAAI,KAAK,QAAM;AACpB,YAAM,GAAG,OAAO,GAAG,IAAI;AACvB,aAAO,SAAS,MAAM;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAEA,IAAI,YAAY;AAChB,SAAS,WAAW,QAAQ;AAC1B,MAAI,WAAW,QAAQ;AACrB,aAAS;AAAA,EACX;AACA,SAAO,GAAG,UAAU;AACtB;AACA,SAAS,aAAa,KAAK;AACzB,cAAY;AACd;AAEA,SAAS,qBAAqB;AAC5B,WAAS,OAAO,UAAU,QAAQ,SAAS,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACzF,WAAO,IAAI,IAAI,UAAU,IAAI;AAAA,EAC/B;AACA,aAAW,SAAS,QAAQ;AAC1B,QAAI,UAAU,QAAW;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB;AACzB,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,SAAK,IAAI,IAAI,UAAU,IAAI;AAAA,EAC7B;AACA,QAAM,gBAAY,uBAAO,IAAI;AAC7B,gCAAU,MAAM;AACd,YAAQ,KAAK,uIAAuI;AACpJ,SAAK,QAAQ,SAAO;AAClB,UAAI,CAAC;AAAK;AACV,UAAI,OAAO,QAAQ,YAAY;AAC7B,YAAI,UAAU,OAAO;AAAA,MACvB,OAAO;AACL,YAAI,UAAU,UAAU;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AAEA,IAAM,kBAAkB,CAAC,OAAO,gBAAgB;AAC9C,SAAO,MAAM,OAAO,CAAC,KAAK,SAAS;AACjC,QAAI,IAAI,WAAW;AAAG,aAAO,CAAC,CAAC,IAAI,CAAC;AACpC,QAAI,IAAI,IAAI,SAAS,CAAC,EAAE,SAAS,aAAa;AAC5C,UAAI,IAAI,SAAS,CAAC,EAAE,KAAK,IAAI;AAAA,IAC/B,OAAO;AACL,UAAI,KAAK,CAAC,IAAI,CAAC;AAAA,IACjB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,IAAM,YAAY;AAAA,EAChB,KAAK;AAAA,EACL,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,OAAO;AAAA,EACX,KAAK;AAAA,EACL,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,KAAK;AAAA,EACL,cAAc;AAAA,EACd,IAAI;AACN;AAEA,IAAI;AAAA,CACH,SAAUC,mBAAkB;AAC3B,EAAAA,kBAAiBA,kBAAiB,cAAc,IAAI,CAAC,IAAI;AACzD,EAAAA,kBAAiBA,kBAAiB,WAAW,IAAI,CAAC,IAAI;AACtD,EAAAA,kBAAiBA,kBAAiB,WAAW,IAAI,CAAC,IAAI;AACtD,EAAAA,kBAAiBA,kBAAiB,UAAU,IAAI,CAAC,IAAI;AACrD,EAAAA,kBAAiBA,kBAAiB,cAAc,IAAI,EAAE,IAAI;AAC1D,EAAAA,kBAAiBA,kBAAiB,yBAAyB,IAAI,EAAE,IAAI;AACvE,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;", "names": ["import_react", "React", "import_react", "useEffect4", "React", "DocumentPosition"]}