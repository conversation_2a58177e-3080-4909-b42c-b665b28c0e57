import {
  DEFAULT_THEME,
  SELECTOR_FOCUS_VISIBLE,
  focusStyles,
  getColor,
  getLineHeight,
  math,
  readableColor,
  retrieveComponentStyles,
  useText
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  Ae,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-tags/dist/index.esm.js
var React = __toESM(require_react());
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
function _extends$1() {
  _extends$1 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$1.apply(this, arguments);
}
var SIZE = ["small", "medium", "large"];
var COMPONENT_ID$2 = "tags.avatar";
var StyledAvatar = styled_components_browser_esm_default((_ref) => {
  let {
    children,
    ...props
  } = _ref;
  return import_react.default.cloneElement(import_react.Children.only(children), props);
}).attrs({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledAvatar",
  componentId: "sc-3kdmgt-0"
})(["flex-shrink:0;font-size:0;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledAvatar.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "tags.close";
var StyledClose = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledClose",
  componentId: "sc-d6lrpn-0"
})(["display:flex;flex-shrink:0;align-items:center;justify-content:center;transition:opacity 0.25s ease-in-out;opacity:0.8;border:0;background:transparent;cursor:pointer;padding:0;color:inherit;font-size:0;appearance:none;&:hover{opacity:0.9;}&:focus{outline:none;}", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledClose.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "tags.tag_view";
var colorStyles = (props) => {
  let backgroundColor;
  let foregroundColor;
  let closeColor;
  if (props.hue) {
    const shade = props.hue === "yellow" ? 400 : 600;
    backgroundColor = getColor(props.hue, shade, props.theme);
    if (props.hue === "yellow" || props.hue === "lemon") {
      foregroundColor = getColor("yellow", 800, props.theme);
    } else {
      foregroundColor = readableColor(backgroundColor, props.theme.palette.grey[800], props.theme.palette.white);
    }
  } else {
    backgroundColor = getColor("neutralHue", 200, props.theme);
    foregroundColor = getColor("neutralHue", 700, props.theme);
    closeColor = getColor("neutralHue", 600, props.theme);
  }
  return Ae(["background-color:", ";color:", ";&:hover{color:", ";}", " & ", "{color:", ";}"], backgroundColor, foregroundColor, foregroundColor, focusStyles({
    theme: props.theme,
    shadowWidth: "sm",
    selector: "&:focus"
  }), StyledClose, closeColor);
};
var sizeStyles = (props) => {
  let borderRadius;
  let padding;
  let height;
  let fontSize;
  let minWidth;
  let avatarSize;
  if (props.size === "small") {
    borderRadius = props.theme.borderRadii.sm;
    padding = props.theme.space.base;
    height = props.theme.space.base * 4;
    fontSize = props.theme.fontSizes.xs;
    avatarSize = 0;
  } else if (props.size === "large") {
    borderRadius = props.theme.borderRadii.md;
    padding = props.theme.space.base * 3;
    height = props.theme.space.base * 8;
    fontSize = props.theme.fontSizes.sm;
    avatarSize = props.theme.space.base * 6;
  } else {
    borderRadius = props.theme.borderRadii.sm;
    padding = props.theme.space.base * 2;
    height = props.theme.space.base * 5;
    fontSize = props.theme.fontSizes.sm;
    avatarSize = props.theme.space.base * 4;
  }
  let avatarBorderRadius = props.size === "large" ? math(`${borderRadius} - 1`) : borderRadius;
  const avatarMargin = (height - avatarSize) / 2;
  const avatarTextMargin = props.isRound ? avatarMargin : avatarMargin * 2;
  if (props.isRound) {
    borderRadius = "50%";
    padding = 0;
    minWidth = height;
    avatarBorderRadius = "50%";
  } else if (props.isPill) {
    borderRadius = "100px";
    avatarBorderRadius = "50%";
    if (props.size === "small") {
      padding = props.theme.space.base * 1.5;
      minWidth = props.theme.space.base * 6;
    } else if (props.size === "large") {
      minWidth = props.theme.space.base * 12;
    } else {
      minWidth = props.theme.space.base * 7.5;
    }
  }
  return Ae(["border-radius:", ";padding:0 ", "px;min-width:", ";height:", "px;line-height:", ";font-size:", ";& > *{width:100%;min-width:", ";}& ", "{margin-", ":-", "px;margin-", ":", "px;border-radius:", ";width:", "px;min-width:", "px;height:", "px;}& ", "{margin-", ":-", "px;border-radius:", ";width:", "px;height:", "px;}"], borderRadius, padding, minWidth ? `${minWidth}px` : `calc(${padding * 2}px + 1ch)`, height, getLineHeight(height, fontSize), fontSize, minWidth ? `${minWidth - padding * 2}px` : "1ch", StyledAvatar, props.theme.rtl ? "right" : "left", padding - avatarMargin, props.theme.rtl ? "left" : "right", avatarTextMargin, avatarBorderRadius, avatarSize, avatarSize, avatarSize, StyledClose, props.theme.rtl ? "left" : "right", padding, borderRadius, height, height);
};
var StyledTag = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledTag",
  componentId: "sc-1jvbe03-0"
})(["display:inline-flex;flex-wrap:nowrap;align-items:center;justify-content:", ";transition:box-shadow 0.1s ease-in-out;box-sizing:border-box;border:0;max-width:100%;overflow:hidden;vertical-align:middle;text-decoration:none;white-space:nowrap;font-weight:", ";direction:", ";", ";&:hover{cursor:default;text-decoration:none;}&:link:hover,&:visited:hover{cursor:pointer;}&:any-link:hover{cursor:pointer;}", "{text-decoration:none;}", ";& > *{overflow:hidden;text-align:center;text-overflow:ellipsis;white-space:nowrap;}& b{font-weight:", ";}& ", "{display:", ";}& ", "{display:", ";}", ";"], (props) => props.isRound && "center", (props) => !props.isRegular && props.theme.fontWeights.semibold, (props) => props.theme.rtl ? "rtl" : "ltr", (props) => sizeStyles(props), SELECTOR_FOCUS_VISIBLE, (props) => colorStyles(props), (props) => props.theme.fontWeights.semibold, StyledAvatar, (props) => (props.isRound || props.size === "small") && "none", StyledClose, (props) => props.isRound && "none", (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledTag.defaultProps = {
  size: "medium",
  theme: DEFAULT_THEME
};
var _path;
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var SvgXStroke = function SvgXStroke2(props) {
  return React.createElement("svg", _extends({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path || (_path = React.createElement("path", {
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M3 9l6-6m0 6L3 3"
  })));
};
var CloseComponent = (0, import_react.forwardRef)((props, ref) => {
  const ariaLabel = useText(CloseComponent, props, "aria-label", "Remove");
  return import_react.default.createElement(StyledClose, _extends$1({
    ref,
    "aria-label": ariaLabel
  }, props, {
    type: "button",
    tabIndex: -1
  }), import_react.default.createElement(SvgXStroke, null));
});
CloseComponent.displayName = "Tag.Close";
var Close = CloseComponent;
var AvatarComponent = (props) => import_react.default.createElement(StyledAvatar, props);
AvatarComponent.displayName = "Tag.Avatar";
var Avatar = AvatarComponent;
var TagComponent = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    size,
    hue,
    ...otherProps
  } = _ref;
  return import_react.default.createElement(StyledTag, _extends$1({
    ref,
    size,
    hue
  }, otherProps));
});
TagComponent.displayName = "Tag";
TagComponent.propTypes = {
  size: import_prop_types.default.oneOf(SIZE),
  hue: import_prop_types.default.string,
  isPill: import_prop_types.default.bool,
  isRound: import_prop_types.default.bool,
  isRegular: import_prop_types.default.bool
};
TagComponent.defaultProps = {
  size: "medium"
};
var Tag = TagComponent;
Tag.Avatar = Avatar;
Tag.Close = Close;
export {
  Tag
};
//# sourceMappingURL=@zendeskgarden_react-tags.js.map
