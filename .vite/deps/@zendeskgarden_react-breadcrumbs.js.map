{"version": 3, "sources": ["../../node_modules/@zendeskgarden/react-breadcrumbs/dist/index.esm.js", "../../node_modules/@zendeskgarden/container-breadcrumb/dist/index.esm.js"], "sourcesContent": ["/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport * as React from 'react';\nimport React__default, { forwardRef, Children, cloneElement } from 'react';\nimport { useBreadcrumb } from '@zendeskgarden/container-breadcrumb';\nimport { retrieveComponentStyles, DEFAULT_THEME, getColor, getLineHeight, useText } from '@zendeskgarden/react-theming';\nimport styled, { css } from 'styled-components';\nimport { em } from 'polished';\n\nconst COMPONENT_ID$1 = 'breadcrumbs.list';\nconst StyledBreadcrumb = styled.ol.attrs({\n  'data-garden-id': COMPONENT_ID$1,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledBreadcrumb\",\n  componentId: \"sc-11jrinn-0\"\n})([\"display:flex;margin:0;padding:0;list-style:none;font-size:\", \";direction:\", \";\", \";\"], props => props.theme.fontSizes.md, props => props.theme.rtl && 'rtl', props => retrieveComponentStyles(COMPONENT_ID$1, props));\nStyledBreadcrumb.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nvar _path;\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nvar SvgChevronRightStroke = function SvgChevronRightStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 12,\n    height: 12,\n    focusable: \"false\",\n    viewBox: \"0 0 12 12\",\n    \"aria-hidden\": \"true\"\n  }, props), _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"currentColor\",\n    d: \"M3.646 10.354a.5.5 0 01-.057-.638l.057-.07L7.293 6 3.646 2.354a.5.5 0 01-.057-.638l.057-.07a.5.5 0 01.638-.057l.07.057 4 4a.5.5 0 01.057.638l-.057.07-4 4a.5.5 0 01-.708 0z\"\n  })));\n};\n\nconst ValidChevronIcon = props => {\n  const {\n    theme,\n    ...validProps\n  } = props;\n  return React__default.createElement(SvgChevronRightStroke, validProps);\n};\nconst StyledChevronIcon = styled(ValidChevronIcon).attrs({\n  role: 'presentation',\n  'aria-hidden': 'true'\n}).withConfig({\n  displayName: \"StyledChevronIcon\",\n  componentId: \"sc-9r9qrm-0\"\n})([\"transform:\", \";margin:0 \", \";color:\", \";\"], props => props.theme.rtl && `rotate(180deg);`, props => em(props.theme.space.base, props.theme.fontSizes.md), props => getColor('neutralHue', 600, props.theme));\nStyledChevronIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID = 'breadcrumbs.item';\nconst linkStyles = _ref => {\n  let {\n    isCurrent\n  } = _ref;\n  return css([\"& > :link,& > :visited{white-space:inherit;}\", \"\"], isCurrent && `\n      & > :link,\n      & > :visited,\n      & > :link:hover,\n      & > :visited:hover,\n      & > :link:focus,\n      & > :visited:focus {\n        color: inherit;\n      }\n    `);\n};\nconst StyledBreadcrumbItem = styled.li.attrs({\n  'data-garden-id': COMPONENT_ID,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledBreadcrumbItem\",\n  componentId: \"sc-r0suq7-0\"\n})([\"line-height:\", \";white-space:nowrap;color:\", \";font-size:inherit;\", \";\", \";\"], props => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), props => props.isCurrent ? getColor(props.theme.colors.neutralHue, 600) : 'inherit', linkStyles, props => retrieveComponentStyles(COMPONENT_ID, props));\nStyledBreadcrumbItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst StyledCenteredBreadcrumbItem = styled(StyledBreadcrumbItem).attrs({\n  'aria-hidden': true\n}).withConfig({\n  displayName: \"StyledCenteredBreadcrumbItem\",\n  componentId: \"sc-1ces9y2-0\"\n})([\"display:flex;align-items:center;\"]);\nStyledCenteredBreadcrumbItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst Breadcrumb = forwardRef((props, ref) => {\n  const {\n    getContainerProps,\n    getCurrentPageProps\n  } = useBreadcrumb();\n  const totalChildren = Children.count(props.children);\n  const mappedChildren = Children.map(props.children, (child, index) => {\n    const isLastItem = index === totalChildren - 1;\n    if (isLastItem) {\n      return React__default.createElement(StyledBreadcrumbItem, {\n        isCurrent: true\n      }, cloneElement(child, getCurrentPageProps()));\n    }\n    return React__default.createElement(React__default.Fragment, null, React__default.createElement(StyledBreadcrumbItem, null, child), React__default.createElement(StyledCenteredBreadcrumbItem, null, React__default.createElement(StyledChevronIcon, null)));\n  });\n  const ariaLabel = useText(Breadcrumb, props, 'aria-label', 'Breadcrumbs');\n  return React__default.createElement(\"nav\", getContainerProps({\n    ...props,\n    ref,\n    role: null,\n    'aria-label': ariaLabel\n  }), React__default.createElement(StyledBreadcrumb, null, mappedChildren));\n});\nBreadcrumb.displayName = 'Breadcrumb';\n\nexport { Breadcrumb };\n", "/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React from 'react';\nimport PropTypes from 'prop-types';\n\nconst useBreadcrumb = () => ({\n  getContainerProps: _ref => {\n    let {\n      role = 'navigation',\n      ...other\n    } = _ref;\n    return {\n      role: role === null ? undefined : role,\n      'data-garden-container-id': 'containers.breadcrumb',\n      'data-garden-container-version': '1.0.3',\n      ...other\n    };\n  },\n  getCurrentPageProps: props => ({\n    'aria-current': 'page',\n    ...props\n  })\n});\n\nconst BreadcrumbContainer = _ref => {\n  let {\n    children,\n    render = children\n  } = _ref;\n  return React.createElement(React.Fragment, null, render(useBreadcrumb()));\n};\nBreadcrumbContainer.propTypes = {\n  render: PropTypes.func,\n  children: PropTypes.func\n};\n\nexport { BreadcrumbContainer, useBreadcrumb };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAAA,SAAuB;AACvB,IAAAC,gBAAmE;;;ACDnE,mBAAkB;AAClB,wBAAsB;AAEtB,IAAM,gBAAgB,OAAO;AAAA,EAC3B,mBAAmB,UAAQ;AACzB,QAAI;AAAA,MACF,OAAO;AAAA,MACP,GAAG;AAAA,IACL,IAAI;AACJ,WAAO;AAAA,MACL,MAAM,SAAS,OAAO,SAAY;AAAA,MAClC,4BAA4B;AAAA,MAC5B,iCAAiC;AAAA,MACjC,GAAG;AAAA,IACL;AAAA,EACF;AAAA,EACA,qBAAqB,YAAU;AAAA,IAC7B,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL;AACF;AAEA,IAAM,sBAAsB,UAAQ;AAClC,MAAI;AAAA,IACF;AAAA,IACA,SAAS;AAAA,EACX,IAAI;AACJ,SAAO,aAAAC,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,OAAO,cAAc,CAAC,CAAC;AAC1E;AACA,oBAAoB,YAAY;AAAA,EAC9B,QAAQ,kBAAAC,QAAU;AAAA,EAClB,UAAU,kBAAAA,QAAU;AACtB;;;ADzBA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB,sCAAO,GAAG,MAAM;AAAA,EACvC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,8DAA8D,eAAe,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,MAAM,OAAO,OAAO,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACzN,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAI;AACJ,SAAS,WAAW;AAAE,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,SAAS,MAAM,MAAM,SAAS;AAAG;AAClV,IAAI,wBAAwB,SAASC,uBAAsB,OAAO;AAChE,SAA0B,qBAAc,OAAO,SAAS;AAAA,IACtD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,UAAU,QAA2B,qBAAc,QAAQ;AAAA,IACpE,MAAM;AAAA,IACN,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAM,mBAAmB,WAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAC,QAAe,cAAc,uBAAuB,UAAU;AACvE;AACA,IAAM,oBAAoB,sCAAO,gBAAgB,EAAE,MAAM;AAAA,EACvD,MAAM;AAAA,EACN,eAAe;AACjB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,cAAc,cAAc,WAAW,GAAG,GAAG,WAAS,MAAM,MAAM,OAAO,mBAAmB,WAAS,KAAG,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,UAAU,EAAE,GAAG,WAAS,SAAS,cAAc,KAAK,MAAM,KAAK,CAAC;AAChN,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,eAAe;AACrB,IAAM,aAAa,UAAQ;AACzB,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,SAAO,GAAI,CAAC,gDAAgD,EAAE,GAAG,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAS3E;AACL;AACA,IAAM,uBAAuB,sCAAO,GAAG,MAAM;AAAA,EAC3C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,gBAAgB,8BAA8B,uBAAuB,KAAK,GAAG,GAAG,WAAS,cAAc,MAAM,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,UAAU,EAAE,GAAG,WAAS,MAAM,YAAY,SAAS,MAAM,MAAM,OAAO,YAAY,GAAG,IAAI,WAAW,YAAY,WAAS,wBAAwB,cAAc,KAAK,CAAC;AACxT,qBAAqB,eAAe;AAAA,EAClC,OAAO;AACT;AAEA,IAAM,+BAA+B,sCAAO,oBAAoB,EAAE,MAAM;AAAA,EACtE,eAAe;AACjB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,kCAAkC,CAAC;AACvC,6BAA6B,eAAe;AAAA,EAC1C,OAAO;AACT;AAEA,IAAM,iBAAa,0BAAW,CAAC,OAAO,QAAQ;AAC5C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc;AAClB,QAAM,gBAAgB,uBAAS,MAAM,MAAM,QAAQ;AACnD,QAAM,iBAAiB,uBAAS,IAAI,MAAM,UAAU,CAAC,OAAO,UAAU;AACpE,UAAM,aAAa,UAAU,gBAAgB;AAC7C,QAAI,YAAY;AACd,aAAO,cAAAA,QAAe,cAAc,sBAAsB;AAAA,QACxD,WAAW;AAAA,MACb,OAAG,4BAAa,OAAO,oBAAoB,CAAC,CAAC;AAAA,IAC/C;AACA,WAAO,cAAAA,QAAe,cAAc,cAAAA,QAAe,UAAU,MAAM,cAAAA,QAAe,cAAc,sBAAsB,MAAM,KAAK,GAAG,cAAAA,QAAe,cAAc,8BAA8B,MAAM,cAAAA,QAAe,cAAc,mBAAmB,IAAI,CAAC,CAAC;AAAA,EAC7P,CAAC;AACD,QAAM,YAAY,QAAQ,YAAY,OAAO,cAAc,aAAa;AACxE,SAAO,cAAAA,QAAe,cAAc,OAAO,kBAAkB;AAAA,IAC3D,GAAG;AAAA,IACH;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,EAChB,CAAC,GAAG,cAAAA,QAAe,cAAc,kBAAkB,MAAM,cAAc,CAAC;AAC1E,CAAC;AACD,WAAW,cAAc;", "names": ["React", "import_react", "React", "PropTypes", "SvgChevronRightStroke", "React__default"]}