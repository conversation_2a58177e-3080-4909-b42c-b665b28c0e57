import {
  _assertThisInitialized,
  _getPrototypeOf,
  _isNativeReflectConstruct,
  _setPrototypeOf
} from "./chunk-LN6LZUGQ.js";
import {
  getControlledValue
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  We
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __commonJS,
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/lodash.memoize/index.js
var require_lodash = __commonJS({
  "node_modules/lodash.memoize/index.js"(exports, module) {
    var FUNC_ERROR_TEXT = "Expected a function";
    var HASH_UNDEFINED = "__lodash_hash_undefined__";
    var funcTag = "[object Function]";
    var genTag = "[object GeneratorFunction]";
    var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;
    var reIsHostCtor = /^\[object .+?Constructor\]$/;
    var freeGlobal = typeof global == "object" && global && global.Object === Object && global;
    var freeSelf = typeof self == "object" && self && self.Object === Object && self;
    var root = freeGlobal || freeSelf || Function("return this")();
    function getValue(object, key) {
      return object == null ? void 0 : object[key];
    }
    function isHostObject(value) {
      var result = false;
      if (value != null && typeof value.toString != "function") {
        try {
          result = !!(value + "");
        } catch (e) {
        }
      }
      return result;
    }
    var arrayProto = Array.prototype;
    var funcProto = Function.prototype;
    var objectProto = Object.prototype;
    var coreJsData = root["__core-js_shared__"];
    var maskSrcKey = function() {
      var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || "");
      return uid ? "Symbol(src)_1." + uid : "";
    }();
    var funcToString = funcProto.toString;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var objectToString = objectProto.toString;
    var reIsNative = RegExp(
      "^" + funcToString.call(hasOwnProperty).replace(reRegExpChar, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"
    );
    var splice = arrayProto.splice;
    var Map2 = getNative(root, "Map");
    var nativeCreate = getNative(Object, "create");
    function Hash(entries) {
      var index = -1, length = entries ? entries.length : 0;
      this.clear();
      while (++index < length) {
        var entry = entries[index];
        this.set(entry[0], entry[1]);
      }
    }
    function hashClear() {
      this.__data__ = nativeCreate ? nativeCreate(null) : {};
    }
    function hashDelete(key) {
      return this.has(key) && delete this.__data__[key];
    }
    function hashGet(key) {
      var data = this.__data__;
      if (nativeCreate) {
        var result = data[key];
        return result === HASH_UNDEFINED ? void 0 : result;
      }
      return hasOwnProperty.call(data, key) ? data[key] : void 0;
    }
    function hashHas(key) {
      var data = this.__data__;
      return nativeCreate ? data[key] !== void 0 : hasOwnProperty.call(data, key);
    }
    function hashSet(key, value) {
      var data = this.__data__;
      data[key] = nativeCreate && value === void 0 ? HASH_UNDEFINED : value;
      return this;
    }
    Hash.prototype.clear = hashClear;
    Hash.prototype["delete"] = hashDelete;
    Hash.prototype.get = hashGet;
    Hash.prototype.has = hashHas;
    Hash.prototype.set = hashSet;
    function ListCache(entries) {
      var index = -1, length = entries ? entries.length : 0;
      this.clear();
      while (++index < length) {
        var entry = entries[index];
        this.set(entry[0], entry[1]);
      }
    }
    function listCacheClear() {
      this.__data__ = [];
    }
    function listCacheDelete(key) {
      var data = this.__data__, index = assocIndexOf(data, key);
      if (index < 0) {
        return false;
      }
      var lastIndex = data.length - 1;
      if (index == lastIndex) {
        data.pop();
      } else {
        splice.call(data, index, 1);
      }
      return true;
    }
    function listCacheGet(key) {
      var data = this.__data__, index = assocIndexOf(data, key);
      return index < 0 ? void 0 : data[index][1];
    }
    function listCacheHas(key) {
      return assocIndexOf(this.__data__, key) > -1;
    }
    function listCacheSet(key, value) {
      var data = this.__data__, index = assocIndexOf(data, key);
      if (index < 0) {
        data.push([key, value]);
      } else {
        data[index][1] = value;
      }
      return this;
    }
    ListCache.prototype.clear = listCacheClear;
    ListCache.prototype["delete"] = listCacheDelete;
    ListCache.prototype.get = listCacheGet;
    ListCache.prototype.has = listCacheHas;
    ListCache.prototype.set = listCacheSet;
    function MapCache(entries) {
      var index = -1, length = entries ? entries.length : 0;
      this.clear();
      while (++index < length) {
        var entry = entries[index];
        this.set(entry[0], entry[1]);
      }
    }
    function mapCacheClear() {
      this.__data__ = {
        "hash": new Hash(),
        "map": new (Map2 || ListCache)(),
        "string": new Hash()
      };
    }
    function mapCacheDelete(key) {
      return getMapData(this, key)["delete"](key);
    }
    function mapCacheGet(key) {
      return getMapData(this, key).get(key);
    }
    function mapCacheHas(key) {
      return getMapData(this, key).has(key);
    }
    function mapCacheSet(key, value) {
      getMapData(this, key).set(key, value);
      return this;
    }
    MapCache.prototype.clear = mapCacheClear;
    MapCache.prototype["delete"] = mapCacheDelete;
    MapCache.prototype.get = mapCacheGet;
    MapCache.prototype.has = mapCacheHas;
    MapCache.prototype.set = mapCacheSet;
    function assocIndexOf(array, key) {
      var length = array.length;
      while (length--) {
        if (eq(array[length][0], key)) {
          return length;
        }
      }
      return -1;
    }
    function baseIsNative(value) {
      if (!isObject(value) || isMasked(value)) {
        return false;
      }
      var pattern = isFunction(value) || isHostObject(value) ? reIsNative : reIsHostCtor;
      return pattern.test(toSource(value));
    }
    function getMapData(map, key) {
      var data = map.__data__;
      return isKeyable(key) ? data[typeof key == "string" ? "string" : "hash"] : data.map;
    }
    function getNative(object, key) {
      var value = getValue(object, key);
      return baseIsNative(value) ? value : void 0;
    }
    function isKeyable(value) {
      var type = typeof value;
      return type == "string" || type == "number" || type == "symbol" || type == "boolean" ? value !== "__proto__" : value === null;
    }
    function isMasked(func) {
      return !!maskSrcKey && maskSrcKey in func;
    }
    function toSource(func) {
      if (func != null) {
        try {
          return funcToString.call(func);
        } catch (e) {
        }
        try {
          return func + "";
        } catch (e) {
        }
      }
      return "";
    }
    function memoize2(func, resolver) {
      if (typeof func != "function" || resolver && typeof resolver != "function") {
        throw new TypeError(FUNC_ERROR_TEXT);
      }
      var memoized = function() {
        var args = arguments, key = resolver ? resolver.apply(this, args) : args[0], cache = memoized.cache;
        if (cache.has(key)) {
          return cache.get(key);
        }
        var result = func.apply(this, args);
        memoized.cache = cache.set(key, result);
        return result;
      };
      memoized.cache = new (memoize2.Cache || MapCache)();
      return memoized;
    }
    memoize2.Cache = MapCache;
    function eq(value, other) {
      return value === other || value !== value && other !== other;
    }
    function isFunction(value) {
      var tag = isObject(value) ? objectToString.call(value) : "";
      return tag == funcTag || tag == genTag;
    }
    function isObject(value) {
      var type = typeof value;
      return !!value && (type == "object" || type == "function");
    }
    module.exports = memoize2;
  }
});

// node_modules/@zendeskgarden/react-theming/dist/index.esm.js
var import_react2 = __toESM(require_react());

// node_modules/@zendeskgarden/container-focusvisible/dist/index.esm.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var INPUT_TYPES_WHITE_LIST = {
  text: true,
  search: true,
  url: true,
  tel: true,
  email: true,
  password: true,
  number: true,
  date: true,
  month: true,
  week: true,
  time: true,
  datetime: true,
  "datetime-local": true
};
function useFocusVisible(_temp) {
  let {
    scope,
    relativeDocument,
    className = "garden-focus-visible",
    dataAttribute = "data-garden-focus-visible"
  } = _temp === void 0 ? {} : _temp;
  if (!scope) {
    throw new Error('Error: the useFocusVisible() hook requires a "scope" property');
  }
  const hadKeyboardEvent = (0, import_react.useRef)(false);
  const hadFocusVisibleRecently = (0, import_react.useRef)(false);
  const hadFocusVisibleRecentlyTimeout = (0, import_react.useRef)();
  (0, import_react.useEffect)(() => {
    let environment = relativeDocument;
    if (!environment) {
      environment = document;
    }
    const isValidFocusTarget = (el) => {
      if (el && el !== scope.current && el.nodeName !== "HTML" && el.nodeName !== "BODY" && "classList" in el && "contains" in el.classList) {
        return true;
      }
      return false;
    };
    const focusTriggersKeyboardModality = (el) => {
      const type = el.type;
      const tagName = el.tagName;
      if (tagName === "INPUT" && INPUT_TYPES_WHITE_LIST[type] && !el.readOnly) {
        return true;
      }
      if (tagName === "TEXTAREA" && !el.readOnly) {
        return true;
      }
      if (el.isContentEditable) {
        return true;
      }
      return false;
    };
    const isFocused = (el) => {
      if (el && (el.classList.contains(className) || el.hasAttribute(dataAttribute))) {
        return true;
      }
      return false;
    };
    const addFocusVisibleClass = (el) => {
      if (isFocused(el)) {
        return;
      }
      el && el.classList.add(className);
      el && el.setAttribute(dataAttribute, "true");
    };
    const removeFocusVisibleClass = (el) => {
      el.classList.remove(className);
      el.removeAttribute(dataAttribute);
    };
    const onKeyDown = (e) => {
      if (e.metaKey || e.altKey || e.ctrlKey) {
        return;
      }
      if (isValidFocusTarget(environment.activeElement)) {
        addFocusVisibleClass(environment.activeElement);
      }
      hadKeyboardEvent.current = true;
    };
    const onPointerDown = () => {
      hadKeyboardEvent.current = false;
    };
    const onFocus = (e) => {
      if (!isValidFocusTarget(e.target)) {
        return;
      }
      if (hadKeyboardEvent.current || focusTriggersKeyboardModality(e.target)) {
        addFocusVisibleClass(e.target);
      }
    };
    const onBlur = (e) => {
      if (!isValidFocusTarget(e.target)) {
        return;
      }
      if (isFocused(e.target)) {
        hadFocusVisibleRecently.current = true;
        clearTimeout(hadFocusVisibleRecentlyTimeout.current);
        const timeoutId = setTimeout(() => {
          hadFocusVisibleRecently.current = false;
          clearTimeout(hadFocusVisibleRecentlyTimeout.current);
        }, 100);
        hadFocusVisibleRecentlyTimeout.current = Number(timeoutId);
        removeFocusVisibleClass(e.target);
      }
    };
    const onInitialPointerMove = (e) => {
      const nodeName = e.target.nodeName;
      if (nodeName && nodeName.toLowerCase() === "html") {
        return;
      }
      hadKeyboardEvent.current = false;
      removeInitialPointerMoveListeners();
    };
    const addInitialPointerMoveListeners = () => {
      environment.addEventListener("mousemove", onInitialPointerMove);
      environment.addEventListener("mousedown", onInitialPointerMove);
      environment.addEventListener("mouseup", onInitialPointerMove);
      environment.addEventListener("pointermove", onInitialPointerMove);
      environment.addEventListener("pointerdown", onInitialPointerMove);
      environment.addEventListener("pointerup", onInitialPointerMove);
      environment.addEventListener("touchmove", onInitialPointerMove);
      environment.addEventListener("touchstart", onInitialPointerMove);
      environment.addEventListener("touchend", onInitialPointerMove);
    };
    const removeInitialPointerMoveListeners = () => {
      environment.removeEventListener("mousemove", onInitialPointerMove);
      environment.removeEventListener("mousedown", onInitialPointerMove);
      environment.removeEventListener("mouseup", onInitialPointerMove);
      environment.removeEventListener("pointermove", onInitialPointerMove);
      environment.removeEventListener("pointerdown", onInitialPointerMove);
      environment.removeEventListener("pointerup", onInitialPointerMove);
      environment.removeEventListener("touchmove", onInitialPointerMove);
      environment.removeEventListener("touchstart", onInitialPointerMove);
      environment.removeEventListener("touchend", onInitialPointerMove);
    };
    const onVisibilityChange = () => {
      if (environment.visibilityState === "hidden") {
        if (hadFocusVisibleRecently.current) {
          hadKeyboardEvent.current = true;
        }
      }
    };
    const currentScope = scope.current;
    if (!environment || !currentScope) {
      return;
    }
    environment.addEventListener("keydown", onKeyDown, true);
    environment.addEventListener("mousedown", onPointerDown, true);
    environment.addEventListener("pointerdown", onPointerDown, true);
    environment.addEventListener("touchstart", onPointerDown, true);
    environment.addEventListener("visibilitychange", onVisibilityChange, true);
    addInitialPointerMoveListeners();
    currentScope && currentScope.addEventListener("focus", onFocus, true);
    currentScope && currentScope.addEventListener("blur", onBlur, true);
    return () => {
      environment.removeEventListener("keydown", onKeyDown);
      environment.removeEventListener("mousedown", onPointerDown);
      environment.removeEventListener("pointerdown", onPointerDown);
      environment.removeEventListener("touchstart", onPointerDown);
      environment.removeEventListener("visibilityChange", onVisibilityChange);
      removeInitialPointerMoveListeners();
      currentScope && currentScope.removeEventListener("focus", onFocus);
      currentScope && currentScope.removeEventListener("blur", onBlur);
      clearTimeout(hadFocusVisibleRecentlyTimeout.current);
    };
  }, [relativeDocument, scope, className, dataAttribute]);
}
var FocusVisibleContainer = (_ref) => {
  let {
    children,
    render = children,
    ...options
  } = _ref;
  const scopeRef = (0, import_react.useRef)(null);
  useFocusVisible({
    scope: scopeRef,
    ...options
  });
  return import_react.default.createElement(import_react.default.Fragment, null, render({
    ref: scopeRef
  }));
};
FocusVisibleContainer.defaultProps = {
  className: "garden-focus-visible",
  dataAttribute: "data-garden-focus-visible"
};
FocusVisibleContainer.propTypes = {
  children: import_prop_types.default.func,
  render: import_prop_types.default.func,
  relativeDocument: import_prop_types.default.object,
  className: import_prop_types.default.string,
  dataAttribute: import_prop_types.default.string
};

// node_modules/@babel/runtime/helpers/esm/extends.js
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}

// node_modules/@babel/runtime/helpers/esm/inheritsLoose.js
function _inheritsLoose(subClass, superClass) {
  subClass.prototype = Object.create(superClass.prototype);
  subClass.prototype.constructor = subClass;
  _setPrototypeOf(subClass, superClass);
}

// node_modules/@babel/runtime/helpers/esm/isNativeFunction.js
function _isNativeFunction(fn) {
  return Function.toString.call(fn).indexOf("[native code]") !== -1;
}

// node_modules/@babel/runtime/helpers/esm/construct.js
function _construct(Parent, args, Class) {
  if (_isNativeReflectConstruct()) {
    _construct = Reflect.construct.bind();
  } else {
    _construct = function _construct2(Parent2, args2, Class2) {
      var a = [null];
      a.push.apply(a, args2);
      var Constructor = Function.bind.apply(Parent2, a);
      var instance = new Constructor();
      if (Class2)
        _setPrototypeOf(instance, Class2.prototype);
      return instance;
    };
  }
  return _construct.apply(null, arguments);
}

// node_modules/@babel/runtime/helpers/esm/wrapNativeSuper.js
function _wrapNativeSuper(Class) {
  var _cache = typeof Map === "function" ? /* @__PURE__ */ new Map() : void 0;
  _wrapNativeSuper = function _wrapNativeSuper2(Class2) {
    if (Class2 === null || !_isNativeFunction(Class2))
      return Class2;
    if (typeof Class2 !== "function") {
      throw new TypeError("Super expression must either be null or a function");
    }
    if (typeof _cache !== "undefined") {
      if (_cache.has(Class2))
        return _cache.get(Class2);
      _cache.set(Class2, Wrapper);
    }
    function Wrapper() {
      return _construct(Class2, arguments, _getPrototypeOf(this).constructor);
    }
    Wrapper.prototype = Object.create(Class2.prototype, {
      constructor: {
        value: Wrapper,
        enumerable: false,
        writable: true,
        configurable: true
      }
    });
    return _setPrototypeOf(Wrapper, Class2);
  };
  return _wrapNativeSuper(Class);
}

// node_modules/polished/dist/polished.esm.js
function last() {
  var _ref;
  return _ref = arguments.length - 1, _ref < 0 || arguments.length <= _ref ? void 0 : arguments[_ref];
}
function negation(a) {
  return -a;
}
function addition(a, b) {
  return a + b;
}
function subtraction(a, b) {
  return a - b;
}
function multiplication(a, b) {
  return a * b;
}
function division(a, b) {
  return a / b;
}
function max() {
  return Math.max.apply(Math, arguments);
}
function min() {
  return Math.min.apply(Math, arguments);
}
function comma() {
  return Array.of.apply(Array, arguments);
}
var defaultSymbols = {
  symbols: {
    "*": {
      infix: {
        symbol: "*",
        f: multiplication,
        notation: "infix",
        precedence: 4,
        rightToLeft: 0,
        argCount: 2
      },
      symbol: "*",
      regSymbol: "\\*"
    },
    "/": {
      infix: {
        symbol: "/",
        f: division,
        notation: "infix",
        precedence: 4,
        rightToLeft: 0,
        argCount: 2
      },
      symbol: "/",
      regSymbol: "/"
    },
    "+": {
      infix: {
        symbol: "+",
        f: addition,
        notation: "infix",
        precedence: 2,
        rightToLeft: 0,
        argCount: 2
      },
      prefix: {
        symbol: "+",
        f: last,
        notation: "prefix",
        precedence: 3,
        rightToLeft: 0,
        argCount: 1
      },
      symbol: "+",
      regSymbol: "\\+"
    },
    "-": {
      infix: {
        symbol: "-",
        f: subtraction,
        notation: "infix",
        precedence: 2,
        rightToLeft: 0,
        argCount: 2
      },
      prefix: {
        symbol: "-",
        f: negation,
        notation: "prefix",
        precedence: 3,
        rightToLeft: 0,
        argCount: 1
      },
      symbol: "-",
      regSymbol: "-"
    },
    ",": {
      infix: {
        symbol: ",",
        f: comma,
        notation: "infix",
        precedence: 1,
        rightToLeft: 0,
        argCount: 2
      },
      symbol: ",",
      regSymbol: ","
    },
    "(": {
      prefix: {
        symbol: "(",
        f: last,
        notation: "prefix",
        precedence: 0,
        rightToLeft: 0,
        argCount: 1
      },
      symbol: "(",
      regSymbol: "\\("
    },
    ")": {
      postfix: {
        symbol: ")",
        f: void 0,
        notation: "postfix",
        precedence: 0,
        rightToLeft: 0,
        argCount: 1
      },
      symbol: ")",
      regSymbol: "\\)"
    },
    min: {
      func: {
        symbol: "min",
        f: min,
        notation: "func",
        precedence: 0,
        rightToLeft: 0,
        argCount: 1
      },
      symbol: "min",
      regSymbol: "min\\b"
    },
    max: {
      func: {
        symbol: "max",
        f: max,
        notation: "func",
        precedence: 0,
        rightToLeft: 0,
        argCount: 1
      },
      symbol: "max",
      regSymbol: "max\\b"
    }
  }
};
var defaultSymbolMap = defaultSymbols;
var ERRORS = {
  "1": "Passed invalid arguments to hsl, please pass multiple numbers e.g. hsl(360, 0.75, 0.4) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75 }).\n\n",
  "2": "Passed invalid arguments to hsla, please pass multiple numbers e.g. hsla(360, 0.75, 0.4, 0.7) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75, alpha: 0.7 }).\n\n",
  "3": "Passed an incorrect argument to a color function, please pass a string representation of a color.\n\n",
  "4": "Couldn't generate valid rgb string from %s, it returned %s.\n\n",
  "5": "Couldn't parse the color string. Please provide the color as a string in hex, rgb, rgba, hsl or hsla notation.\n\n",
  "6": "Passed invalid arguments to rgb, please pass multiple numbers e.g. rgb(255, 205, 100) or an object e.g. rgb({ red: 255, green: 205, blue: 100 }).\n\n",
  "7": "Passed invalid arguments to rgba, please pass multiple numbers e.g. rgb(255, 205, 100, 0.75) or an object e.g. rgb({ red: 255, green: 205, blue: 100, alpha: 0.75 }).\n\n",
  "8": "Passed invalid argument to toColorString, please pass a RgbColor, RgbaColor, HslColor or HslaColor object.\n\n",
  "9": "Please provide a number of steps to the modularScale helper.\n\n",
  "10": "Please pass a number or one of the predefined scales to the modularScale helper as the ratio.\n\n",
  "11": 'Invalid value passed as base to modularScale, expected number or em string but got "%s"\n\n',
  "12": 'Expected a string ending in "px" or a number passed as the first argument to %s(), got "%s" instead.\n\n',
  "13": 'Expected a string ending in "px" or a number passed as the second argument to %s(), got "%s" instead.\n\n',
  "14": 'Passed invalid pixel value ("%s") to %s(), please pass a value like "12px" or 12.\n\n',
  "15": 'Passed invalid base value ("%s") to %s(), please pass a value like "12px" or 12.\n\n',
  "16": "You must provide a template to this method.\n\n",
  "17": "You passed an unsupported selector state to this method.\n\n",
  "18": "minScreen and maxScreen must be provided as stringified numbers with the same units.\n\n",
  "19": "fromSize and toSize must be provided as stringified numbers with the same units.\n\n",
  "20": "expects either an array of objects or a single object with the properties prop, fromSize, and toSize.\n\n",
  "21": "expects the objects in the first argument array to have the properties `prop`, `fromSize`, and `toSize`.\n\n",
  "22": "expects the first argument object to have the properties `prop`, `fromSize`, and `toSize`.\n\n",
  "23": "fontFace expects a name of a font-family.\n\n",
  "24": "fontFace expects either the path to the font file(s) or a name of a local copy.\n\n",
  "25": "fontFace expects localFonts to be an array.\n\n",
  "26": "fontFace expects fileFormats to be an array.\n\n",
  "27": "radialGradient requries at least 2 color-stops to properly render.\n\n",
  "28": "Please supply a filename to retinaImage() as the first argument.\n\n",
  "29": "Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.\n\n",
  "30": "Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",
  "31": "The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation\n\n",
  "32": "To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s')\n\n",
  "33": "The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation\n\n",
  "34": "borderRadius expects a radius value as a string or number as the second argument.\n\n",
  "35": 'borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.\n\n',
  "36": "Property must be a string value.\n\n",
  "37": "Syntax Error at %s.\n\n",
  "38": "Formula contains a function that needs parentheses at %s.\n\n",
  "39": "Formula is missing closing parenthesis at %s.\n\n",
  "40": "Formula has too many closing parentheses at %s.\n\n",
  "41": "All values in a formula must have the same unit or be unitless.\n\n",
  "42": "Please provide a number of steps to the modularScale helper.\n\n",
  "43": "Please pass a number or one of the predefined scales to the modularScale helper as the ratio.\n\n",
  "44": "Invalid value passed as base to modularScale, expected number or em/rem string but got %s.\n\n",
  "45": "Passed invalid argument to hslToColorString, please pass a HslColor or HslaColor object.\n\n",
  "46": "Passed invalid argument to rgbToColorString, please pass a RgbColor or RgbaColor object.\n\n",
  "47": "minScreen and maxScreen must be provided as stringified numbers with the same units.\n\n",
  "48": "fromSize and toSize must be provided as stringified numbers with the same units.\n\n",
  "49": "Expects either an array of objects or a single object with the properties prop, fromSize, and toSize.\n\n",
  "50": "Expects the objects in the first argument array to have the properties prop, fromSize, and toSize.\n\n",
  "51": "Expects the first argument object to have the properties prop, fromSize, and toSize.\n\n",
  "52": "fontFace expects either the path to the font file(s) or a name of a local copy.\n\n",
  "53": "fontFace expects localFonts to be an array.\n\n",
  "54": "fontFace expects fileFormats to be an array.\n\n",
  "55": "fontFace expects a name of a font-family.\n\n",
  "56": "linearGradient requries at least 2 color-stops to properly render.\n\n",
  "57": "radialGradient requries at least 2 color-stops to properly render.\n\n",
  "58": "Please supply a filename to retinaImage() as the first argument.\n\n",
  "59": "Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.\n\n",
  "60": "Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",
  "61": "Property must be a string value.\n\n",
  "62": "borderRadius expects a radius value as a string or number as the second argument.\n\n",
  "63": 'borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.\n\n',
  "64": "The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation.\n\n",
  "65": "To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s').\n\n",
  "66": "The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation.\n\n",
  "67": "You must provide a template to this method.\n\n",
  "68": "You passed an unsupported selector state to this method.\n\n",
  "69": 'Expected a string ending in "px" or a number passed as the first argument to %s(), got %s instead.\n\n',
  "70": 'Expected a string ending in "px" or a number passed as the second argument to %s(), got %s instead.\n\n',
  "71": 'Passed invalid pixel value %s to %s(), please pass a value like "12px" or 12.\n\n',
  "72": 'Passed invalid base value %s to %s(), please pass a value like "12px" or 12.\n\n',
  "73": "Please provide a valid CSS variable.\n\n",
  "74": "CSS variable not found and no default was provided.\n\n",
  "75": "important requires a valid style object, got a %s instead.\n\n",
  "76": "fromSize and toSize must be provided as stringified numbers with the same units as minScreen and maxScreen.\n\n",
  "77": 'remToPx expects a value in "rem" but you provided it in "%s".\n\n',
  "78": 'base must be set in "px" or "%" but you set it in "%s".\n'
};
function format() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  var a = args[0];
  var b = [];
  var c;
  for (c = 1; c < args.length; c += 1) {
    b.push(args[c]);
  }
  b.forEach(function(d) {
    a = a.replace(/%[a-z]/, d);
  });
  return a;
}
var PolishedError = function(_Error) {
  _inheritsLoose(PolishedError2, _Error);
  function PolishedError2(code) {
    var _this;
    if (false) {
      _this = _Error.call(this, "An error occurred. See https://github.com/styled-components/polished/blob/main/src/internalHelpers/errors.md#" + code + " for more information.") || this;
    } else {
      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
        args[_key2 - 1] = arguments[_key2];
      }
      _this = _Error.call(this, format.apply(void 0, [ERRORS[code]].concat(args))) || this;
    }
    return _assertThisInitialized(_this);
  }
  return PolishedError2;
}(_wrapNativeSuper(Error));
var unitRegExp = /((?!\w)a|na|hc|mc|dg|me[r]?|xe|ni(?![a-zA-Z])|mm|cp|tp|xp|q(?!s)|hv|xamv|nimv|wv|sm|s(?!\D|$)|ged|darg?|nrut)/g;
function mergeSymbolMaps(additionalSymbols) {
  var symbolMap = {};
  symbolMap.symbols = additionalSymbols ? _extends({}, defaultSymbolMap.symbols, additionalSymbols.symbols) : _extends({}, defaultSymbolMap.symbols);
  return symbolMap;
}
function exec(operators, values) {
  var _ref;
  var op = operators.pop();
  values.push(op.f.apply(op, (_ref = []).concat.apply(_ref, values.splice(-op.argCount))));
  return op.precedence;
}
function calculate(expression, additionalSymbols) {
  var symbolMap = mergeSymbolMaps(additionalSymbols);
  var match;
  var operators = [symbolMap.symbols["("].prefix];
  var values = [];
  var pattern = new RegExp(
    // Pattern for numbers
    "\\d+(?:\\.\\d+)?|" + // ...and patterns for individual operators/function names
    Object.keys(symbolMap.symbols).map(function(key) {
      return symbolMap.symbols[key];
    }).sort(function(a, b) {
      return b.symbol.length - a.symbol.length;
    }).map(function(val) {
      return val.regSymbol;
    }).join("|") + "|(\\S)",
    "g"
  );
  pattern.lastIndex = 0;
  var afterValue = false;
  do {
    match = pattern.exec(expression);
    var _ref2 = match || [")", void 0], token = _ref2[0], bad = _ref2[1];
    var notNumber = symbolMap.symbols[token];
    var notNewValue = notNumber && !notNumber.prefix && !notNumber.func;
    var notAfterValue = !notNumber || !notNumber.postfix && !notNumber.infix;
    if (bad || (afterValue ? notAfterValue : notNewValue)) {
      throw new PolishedError(37, match ? match.index : expression.length, expression);
    }
    if (afterValue) {
      var curr = notNumber.postfix || notNumber.infix;
      do {
        var prev = operators[operators.length - 1];
        if ((curr.precedence - prev.precedence || prev.rightToLeft) > 0)
          break;
      } while (exec(operators, values));
      afterValue = curr.notation === "postfix";
      if (curr.symbol !== ")") {
        operators.push(curr);
        if (afterValue)
          exec(operators, values);
      }
    } else if (notNumber) {
      operators.push(notNumber.prefix || notNumber.func);
      if (notNumber.func) {
        match = pattern.exec(expression);
        if (!match || match[0] !== "(") {
          throw new PolishedError(38, match ? match.index : expression.length, expression);
        }
      }
    } else {
      values.push(+token);
      afterValue = true;
    }
  } while (match && operators.length);
  if (operators.length) {
    throw new PolishedError(39, match ? match.index : expression.length, expression);
  } else if (match) {
    throw new PolishedError(40, match ? match.index : expression.length, expression);
  } else {
    return values.pop();
  }
}
function reverseString(str) {
  return str.split("").reverse().join("");
}
function math(formula, additionalSymbols) {
  var reversedFormula = reverseString(formula);
  var formulaMatch = reversedFormula.match(unitRegExp);
  if (formulaMatch && !formulaMatch.every(function(unit) {
    return unit === formulaMatch[0];
  })) {
    throw new PolishedError(41);
  }
  var cleanFormula = reverseString(reversedFormula.replace(unitRegExp, ""));
  return "" + calculate(cleanFormula, additionalSymbols) + (formulaMatch ? reverseString(formulaMatch[0]) : "");
}
function endsWith(string, suffix) {
  return string.substr(-suffix.length) === suffix;
}
var cssRegex$1 = /^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;
function stripUnit(value) {
  if (typeof value !== "string")
    return value;
  var matchedValue = value.match(cssRegex$1);
  return matchedValue ? parseFloat(value) : value;
}
var pxtoFactory = function pxtoFactory2(to) {
  return function(pxval, base) {
    if (base === void 0) {
      base = "16px";
    }
    var newPxval = pxval;
    var newBase = base;
    if (typeof pxval === "string") {
      if (!endsWith(pxval, "px")) {
        throw new PolishedError(69, to, pxval);
      }
      newPxval = stripUnit(pxval);
    }
    if (typeof base === "string") {
      if (!endsWith(base, "px")) {
        throw new PolishedError(70, to, base);
      }
      newBase = stripUnit(base);
    }
    if (typeof newPxval === "string") {
      throw new PolishedError(71, pxval, to);
    }
    if (typeof newBase === "string") {
      throw new PolishedError(72, base, to);
    }
    return "" + newPxval / newBase + to;
  };
};
var pixelsto = pxtoFactory;
var em = pixelsto("em");
var em$1 = em;
var cssRegex = /^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;
function getValueAndUnit(value) {
  if (typeof value !== "string")
    return [value, ""];
  var matchedValue = value.match(cssRegex);
  if (matchedValue)
    return [parseFloat(value), matchedValue[2]];
  return [value, void 0];
}
var rem = pixelsto("rem");
function hideVisually() {
  return {
    border: "0",
    clip: "rect(0 0 0 0)",
    height: "1px",
    margin: "-1px",
    overflow: "hidden",
    padding: "0",
    position: "absolute",
    whiteSpace: "nowrap",
    width: "1px"
  };
}
function colorToInt(color) {
  return Math.round(color * 255);
}
function convertToInt(red, green, blue) {
  return colorToInt(red) + "," + colorToInt(green) + "," + colorToInt(blue);
}
function hslToRgb(hue, saturation, lightness, convert) {
  if (convert === void 0) {
    convert = convertToInt;
  }
  if (saturation === 0) {
    return convert(lightness, lightness, lightness);
  }
  var huePrime = (hue % 360 + 360) % 360 / 60;
  var chroma = (1 - Math.abs(2 * lightness - 1)) * saturation;
  var secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));
  var red = 0;
  var green = 0;
  var blue = 0;
  if (huePrime >= 0 && huePrime < 1) {
    red = chroma;
    green = secondComponent;
  } else if (huePrime >= 1 && huePrime < 2) {
    red = secondComponent;
    green = chroma;
  } else if (huePrime >= 2 && huePrime < 3) {
    green = chroma;
    blue = secondComponent;
  } else if (huePrime >= 3 && huePrime < 4) {
    green = secondComponent;
    blue = chroma;
  } else if (huePrime >= 4 && huePrime < 5) {
    red = secondComponent;
    blue = chroma;
  } else if (huePrime >= 5 && huePrime < 6) {
    red = chroma;
    blue = secondComponent;
  }
  var lightnessModification = lightness - chroma / 2;
  var finalRed = red + lightnessModification;
  var finalGreen = green + lightnessModification;
  var finalBlue = blue + lightnessModification;
  return convert(finalRed, finalGreen, finalBlue);
}
var namedColorMap = {
  aliceblue: "f0f8ff",
  antiquewhite: "faebd7",
  aqua: "00ffff",
  aquamarine: "7fffd4",
  azure: "f0ffff",
  beige: "f5f5dc",
  bisque: "ffe4c4",
  black: "000",
  blanchedalmond: "ffebcd",
  blue: "0000ff",
  blueviolet: "8a2be2",
  brown: "a52a2a",
  burlywood: "deb887",
  cadetblue: "5f9ea0",
  chartreuse: "7fff00",
  chocolate: "d2691e",
  coral: "ff7f50",
  cornflowerblue: "6495ed",
  cornsilk: "fff8dc",
  crimson: "dc143c",
  cyan: "00ffff",
  darkblue: "00008b",
  darkcyan: "008b8b",
  darkgoldenrod: "b8860b",
  darkgray: "a9a9a9",
  darkgreen: "006400",
  darkgrey: "a9a9a9",
  darkkhaki: "bdb76b",
  darkmagenta: "8b008b",
  darkolivegreen: "556b2f",
  darkorange: "ff8c00",
  darkorchid: "9932cc",
  darkred: "8b0000",
  darksalmon: "e9967a",
  darkseagreen: "8fbc8f",
  darkslateblue: "483d8b",
  darkslategray: "2f4f4f",
  darkslategrey: "2f4f4f",
  darkturquoise: "00ced1",
  darkviolet: "9400d3",
  deeppink: "ff1493",
  deepskyblue: "00bfff",
  dimgray: "696969",
  dimgrey: "696969",
  dodgerblue: "1e90ff",
  firebrick: "b22222",
  floralwhite: "fffaf0",
  forestgreen: "228b22",
  fuchsia: "ff00ff",
  gainsboro: "dcdcdc",
  ghostwhite: "f8f8ff",
  gold: "ffd700",
  goldenrod: "daa520",
  gray: "808080",
  green: "008000",
  greenyellow: "adff2f",
  grey: "808080",
  honeydew: "f0fff0",
  hotpink: "ff69b4",
  indianred: "cd5c5c",
  indigo: "4b0082",
  ivory: "fffff0",
  khaki: "f0e68c",
  lavender: "e6e6fa",
  lavenderblush: "fff0f5",
  lawngreen: "7cfc00",
  lemonchiffon: "fffacd",
  lightblue: "add8e6",
  lightcoral: "f08080",
  lightcyan: "e0ffff",
  lightgoldenrodyellow: "fafad2",
  lightgray: "d3d3d3",
  lightgreen: "90ee90",
  lightgrey: "d3d3d3",
  lightpink: "ffb6c1",
  lightsalmon: "ffa07a",
  lightseagreen: "20b2aa",
  lightskyblue: "87cefa",
  lightslategray: "789",
  lightslategrey: "789",
  lightsteelblue: "b0c4de",
  lightyellow: "ffffe0",
  lime: "0f0",
  limegreen: "32cd32",
  linen: "faf0e6",
  magenta: "f0f",
  maroon: "800000",
  mediumaquamarine: "66cdaa",
  mediumblue: "0000cd",
  mediumorchid: "ba55d3",
  mediumpurple: "9370db",
  mediumseagreen: "3cb371",
  mediumslateblue: "7b68ee",
  mediumspringgreen: "00fa9a",
  mediumturquoise: "48d1cc",
  mediumvioletred: "c71585",
  midnightblue: "191970",
  mintcream: "f5fffa",
  mistyrose: "ffe4e1",
  moccasin: "ffe4b5",
  navajowhite: "ffdead",
  navy: "000080",
  oldlace: "fdf5e6",
  olive: "808000",
  olivedrab: "6b8e23",
  orange: "ffa500",
  orangered: "ff4500",
  orchid: "da70d6",
  palegoldenrod: "eee8aa",
  palegreen: "98fb98",
  paleturquoise: "afeeee",
  palevioletred: "db7093",
  papayawhip: "ffefd5",
  peachpuff: "ffdab9",
  peru: "cd853f",
  pink: "ffc0cb",
  plum: "dda0dd",
  powderblue: "b0e0e6",
  purple: "800080",
  rebeccapurple: "639",
  red: "f00",
  rosybrown: "bc8f8f",
  royalblue: "4169e1",
  saddlebrown: "8b4513",
  salmon: "fa8072",
  sandybrown: "f4a460",
  seagreen: "2e8b57",
  seashell: "fff5ee",
  sienna: "a0522d",
  silver: "c0c0c0",
  skyblue: "87ceeb",
  slateblue: "6a5acd",
  slategray: "708090",
  slategrey: "708090",
  snow: "fffafa",
  springgreen: "00ff7f",
  steelblue: "4682b4",
  tan: "d2b48c",
  teal: "008080",
  thistle: "d8bfd8",
  tomato: "ff6347",
  turquoise: "40e0d0",
  violet: "ee82ee",
  wheat: "f5deb3",
  white: "fff",
  whitesmoke: "f5f5f5",
  yellow: "ff0",
  yellowgreen: "9acd32"
};
function nameToHex(color) {
  if (typeof color !== "string")
    return color;
  var normalizedColorName = color.toLowerCase();
  return namedColorMap[normalizedColorName] ? "#" + namedColorMap[normalizedColorName] : color;
}
var hexRegex = /^#[a-fA-F0-9]{6}$/;
var hexRgbaRegex = /^#[a-fA-F0-9]{8}$/;
var reducedHexRegex = /^#[a-fA-F0-9]{3}$/;
var reducedRgbaHexRegex = /^#[a-fA-F0-9]{4}$/;
var rgbRegex = /^rgb\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*\)$/i;
var rgbaRegex = /^rgb(?:a)?\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;
var hslRegex = /^hsl\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*\)$/i;
var hslaRegex = /^hsl(?:a)?\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;
function parseToRgb(color) {
  if (typeof color !== "string") {
    throw new PolishedError(3);
  }
  var normalizedColor = nameToHex(color);
  if (normalizedColor.match(hexRegex)) {
    return {
      red: parseInt("" + normalizedColor[1] + normalizedColor[2], 16),
      green: parseInt("" + normalizedColor[3] + normalizedColor[4], 16),
      blue: parseInt("" + normalizedColor[5] + normalizedColor[6], 16)
    };
  }
  if (normalizedColor.match(hexRgbaRegex)) {
    var alpha = parseFloat((parseInt("" + normalizedColor[7] + normalizedColor[8], 16) / 255).toFixed(2));
    return {
      red: parseInt("" + normalizedColor[1] + normalizedColor[2], 16),
      green: parseInt("" + normalizedColor[3] + normalizedColor[4], 16),
      blue: parseInt("" + normalizedColor[5] + normalizedColor[6], 16),
      alpha
    };
  }
  if (normalizedColor.match(reducedHexRegex)) {
    return {
      red: parseInt("" + normalizedColor[1] + normalizedColor[1], 16),
      green: parseInt("" + normalizedColor[2] + normalizedColor[2], 16),
      blue: parseInt("" + normalizedColor[3] + normalizedColor[3], 16)
    };
  }
  if (normalizedColor.match(reducedRgbaHexRegex)) {
    var _alpha = parseFloat((parseInt("" + normalizedColor[4] + normalizedColor[4], 16) / 255).toFixed(2));
    return {
      red: parseInt("" + normalizedColor[1] + normalizedColor[1], 16),
      green: parseInt("" + normalizedColor[2] + normalizedColor[2], 16),
      blue: parseInt("" + normalizedColor[3] + normalizedColor[3], 16),
      alpha: _alpha
    };
  }
  var rgbMatched = rgbRegex.exec(normalizedColor);
  if (rgbMatched) {
    return {
      red: parseInt("" + rgbMatched[1], 10),
      green: parseInt("" + rgbMatched[2], 10),
      blue: parseInt("" + rgbMatched[3], 10)
    };
  }
  var rgbaMatched = rgbaRegex.exec(normalizedColor.substring(0, 50));
  if (rgbaMatched) {
    return {
      red: parseInt("" + rgbaMatched[1], 10),
      green: parseInt("" + rgbaMatched[2], 10),
      blue: parseInt("" + rgbaMatched[3], 10),
      alpha: parseFloat("" + rgbaMatched[4]) > 1 ? parseFloat("" + rgbaMatched[4]) / 100 : parseFloat("" + rgbaMatched[4])
    };
  }
  var hslMatched = hslRegex.exec(normalizedColor);
  if (hslMatched) {
    var hue = parseInt("" + hslMatched[1], 10);
    var saturation = parseInt("" + hslMatched[2], 10) / 100;
    var lightness = parseInt("" + hslMatched[3], 10) / 100;
    var rgbColorString = "rgb(" + hslToRgb(hue, saturation, lightness) + ")";
    var hslRgbMatched = rgbRegex.exec(rgbColorString);
    if (!hslRgbMatched) {
      throw new PolishedError(4, normalizedColor, rgbColorString);
    }
    return {
      red: parseInt("" + hslRgbMatched[1], 10),
      green: parseInt("" + hslRgbMatched[2], 10),
      blue: parseInt("" + hslRgbMatched[3], 10)
    };
  }
  var hslaMatched = hslaRegex.exec(normalizedColor.substring(0, 50));
  if (hslaMatched) {
    var _hue = parseInt("" + hslaMatched[1], 10);
    var _saturation = parseInt("" + hslaMatched[2], 10) / 100;
    var _lightness = parseInt("" + hslaMatched[3], 10) / 100;
    var _rgbColorString = "rgb(" + hslToRgb(_hue, _saturation, _lightness) + ")";
    var _hslRgbMatched = rgbRegex.exec(_rgbColorString);
    if (!_hslRgbMatched) {
      throw new PolishedError(4, normalizedColor, _rgbColorString);
    }
    return {
      red: parseInt("" + _hslRgbMatched[1], 10),
      green: parseInt("" + _hslRgbMatched[2], 10),
      blue: parseInt("" + _hslRgbMatched[3], 10),
      alpha: parseFloat("" + hslaMatched[4]) > 1 ? parseFloat("" + hslaMatched[4]) / 100 : parseFloat("" + hslaMatched[4])
    };
  }
  throw new PolishedError(5);
}
function rgbToHsl(color) {
  var red = color.red / 255;
  var green = color.green / 255;
  var blue = color.blue / 255;
  var max2 = Math.max(red, green, blue);
  var min2 = Math.min(red, green, blue);
  var lightness = (max2 + min2) / 2;
  if (max2 === min2) {
    if (color.alpha !== void 0) {
      return {
        hue: 0,
        saturation: 0,
        lightness,
        alpha: color.alpha
      };
    } else {
      return {
        hue: 0,
        saturation: 0,
        lightness
      };
    }
  }
  var hue;
  var delta = max2 - min2;
  var saturation = lightness > 0.5 ? delta / (2 - max2 - min2) : delta / (max2 + min2);
  switch (max2) {
    case red:
      hue = (green - blue) / delta + (green < blue ? 6 : 0);
      break;
    case green:
      hue = (blue - red) / delta + 2;
      break;
    default:
      hue = (red - green) / delta + 4;
      break;
  }
  hue *= 60;
  if (color.alpha !== void 0) {
    return {
      hue,
      saturation,
      lightness,
      alpha: color.alpha
    };
  }
  return {
    hue,
    saturation,
    lightness
  };
}
function parseToHsl(color) {
  return rgbToHsl(parseToRgb(color));
}
var reduceHexValue = function reduceHexValue2(value) {
  if (value.length === 7 && value[1] === value[2] && value[3] === value[4] && value[5] === value[6]) {
    return "#" + value[1] + value[3] + value[5];
  }
  return value;
};
var reduceHexValue$1 = reduceHexValue;
function numberToHex(value) {
  var hex = value.toString(16);
  return hex.length === 1 ? "0" + hex : hex;
}
function colorToHex(color) {
  return numberToHex(Math.round(color * 255));
}
function convertToHex(red, green, blue) {
  return reduceHexValue$1("#" + colorToHex(red) + colorToHex(green) + colorToHex(blue));
}
function hslToHex(hue, saturation, lightness) {
  return hslToRgb(hue, saturation, lightness, convertToHex);
}
function hsl(value, saturation, lightness) {
  if (typeof value === "number" && typeof saturation === "number" && typeof lightness === "number") {
    return hslToHex(value, saturation, lightness);
  } else if (typeof value === "object" && saturation === void 0 && lightness === void 0) {
    return hslToHex(value.hue, value.saturation, value.lightness);
  }
  throw new PolishedError(1);
}
function hsla(value, saturation, lightness, alpha) {
  if (typeof value === "number" && typeof saturation === "number" && typeof lightness === "number" && typeof alpha === "number") {
    return alpha >= 1 ? hslToHex(value, saturation, lightness) : "rgba(" + hslToRgb(value, saturation, lightness) + "," + alpha + ")";
  } else if (typeof value === "object" && saturation === void 0 && lightness === void 0 && alpha === void 0) {
    return value.alpha >= 1 ? hslToHex(value.hue, value.saturation, value.lightness) : "rgba(" + hslToRgb(value.hue, value.saturation, value.lightness) + "," + value.alpha + ")";
  }
  throw new PolishedError(2);
}
function rgb(value, green, blue) {
  if (typeof value === "number" && typeof green === "number" && typeof blue === "number") {
    return reduceHexValue$1("#" + numberToHex(value) + numberToHex(green) + numberToHex(blue));
  } else if (typeof value === "object" && green === void 0 && blue === void 0) {
    return reduceHexValue$1("#" + numberToHex(value.red) + numberToHex(value.green) + numberToHex(value.blue));
  }
  throw new PolishedError(6);
}
function rgba(firstValue, secondValue, thirdValue, fourthValue) {
  if (typeof firstValue === "string" && typeof secondValue === "number") {
    var rgbValue = parseToRgb(firstValue);
    return "rgba(" + rgbValue.red + "," + rgbValue.green + "," + rgbValue.blue + "," + secondValue + ")";
  } else if (typeof firstValue === "number" && typeof secondValue === "number" && typeof thirdValue === "number" && typeof fourthValue === "number") {
    return fourthValue >= 1 ? rgb(firstValue, secondValue, thirdValue) : "rgba(" + firstValue + "," + secondValue + "," + thirdValue + "," + fourthValue + ")";
  } else if (typeof firstValue === "object" && secondValue === void 0 && thirdValue === void 0 && fourthValue === void 0) {
    return firstValue.alpha >= 1 ? rgb(firstValue.red, firstValue.green, firstValue.blue) : "rgba(" + firstValue.red + "," + firstValue.green + "," + firstValue.blue + "," + firstValue.alpha + ")";
  }
  throw new PolishedError(7);
}
var isRgb = function isRgb2(color) {
  return typeof color.red === "number" && typeof color.green === "number" && typeof color.blue === "number" && (typeof color.alpha !== "number" || typeof color.alpha === "undefined");
};
var isRgba = function isRgba2(color) {
  return typeof color.red === "number" && typeof color.green === "number" && typeof color.blue === "number" && typeof color.alpha === "number";
};
var isHsl = function isHsl2(color) {
  return typeof color.hue === "number" && typeof color.saturation === "number" && typeof color.lightness === "number" && (typeof color.alpha !== "number" || typeof color.alpha === "undefined");
};
var isHsla = function isHsla2(color) {
  return typeof color.hue === "number" && typeof color.saturation === "number" && typeof color.lightness === "number" && typeof color.alpha === "number";
};
function toColorString(color) {
  if (typeof color !== "object")
    throw new PolishedError(8);
  if (isRgba(color))
    return rgba(color);
  if (isRgb(color))
    return rgb(color);
  if (isHsla(color))
    return hsla(color);
  if (isHsl(color))
    return hsl(color);
  throw new PolishedError(8);
}
function curried(f, length, acc) {
  return function fn() {
    var combined = acc.concat(Array.prototype.slice.call(arguments));
    return combined.length >= length ? f.apply(this, combined) : curried(f, length, combined);
  };
}
function curry(f) {
  return curried(f, f.length, []);
}
function adjustHue(degree, color) {
  if (color === "transparent")
    return color;
  var hslColor = parseToHsl(color);
  return toColorString(_extends({}, hslColor, {
    hue: hslColor.hue + parseFloat(degree)
  }));
}
var curriedAdjustHue = curry(adjustHue);
function guard(lowerBoundary, upperBoundary, value) {
  return Math.max(lowerBoundary, Math.min(upperBoundary, value));
}
function darken(amount, color) {
  if (color === "transparent")
    return color;
  var hslColor = parseToHsl(color);
  return toColorString(_extends({}, hslColor, {
    lightness: guard(0, 1, hslColor.lightness - parseFloat(amount))
  }));
}
var curriedDarken = curry(darken);
var curriedDarken$1 = curriedDarken;
function desaturate(amount, color) {
  if (color === "transparent")
    return color;
  var hslColor = parseToHsl(color);
  return toColorString(_extends({}, hslColor, {
    saturation: guard(0, 1, hslColor.saturation - parseFloat(amount))
  }));
}
var curriedDesaturate = curry(desaturate);
function getLuminance(color) {
  if (color === "transparent")
    return 0;
  var rgbColor = parseToRgb(color);
  var _Object$keys$map = Object.keys(rgbColor).map(function(key) {
    var channel = rgbColor[key] / 255;
    return channel <= 0.03928 ? channel / 12.92 : Math.pow((channel + 0.055) / 1.055, 2.4);
  }), r = _Object$keys$map[0], g = _Object$keys$map[1], b = _Object$keys$map[2];
  return parseFloat((0.2126 * r + 0.7152 * g + 0.0722 * b).toFixed(3));
}
function getContrast(color1, color2) {
  var luminance1 = getLuminance(color1);
  var luminance2 = getLuminance(color2);
  return parseFloat((luminance1 > luminance2 ? (luminance1 + 0.05) / (luminance2 + 0.05) : (luminance2 + 0.05) / (luminance1 + 0.05)).toFixed(2));
}
function lighten(amount, color) {
  if (color === "transparent")
    return color;
  var hslColor = parseToHsl(color);
  return toColorString(_extends({}, hslColor, {
    lightness: guard(0, 1, hslColor.lightness + parseFloat(amount))
  }));
}
var curriedLighten = curry(lighten);
var curriedLighten$1 = curriedLighten;
function mix(weight, color, otherColor) {
  if (color === "transparent")
    return otherColor;
  if (otherColor === "transparent")
    return color;
  if (weight === 0)
    return otherColor;
  var parsedColor1 = parseToRgb(color);
  var color1 = _extends({}, parsedColor1, {
    alpha: typeof parsedColor1.alpha === "number" ? parsedColor1.alpha : 1
  });
  var parsedColor2 = parseToRgb(otherColor);
  var color2 = _extends({}, parsedColor2, {
    alpha: typeof parsedColor2.alpha === "number" ? parsedColor2.alpha : 1
  });
  var alphaDelta = color1.alpha - color2.alpha;
  var x = parseFloat(weight) * 2 - 1;
  var y = x * alphaDelta === -1 ? x : x + alphaDelta;
  var z = 1 + x * alphaDelta;
  var weight1 = (y / z + 1) / 2;
  var weight2 = 1 - weight1;
  var mixedColor = {
    red: Math.floor(color1.red * weight1 + color2.red * weight2),
    green: Math.floor(color1.green * weight1 + color2.green * weight2),
    blue: Math.floor(color1.blue * weight1 + color2.blue * weight2),
    alpha: color1.alpha * parseFloat(weight) + color2.alpha * (1 - parseFloat(weight))
  };
  return rgba(mixedColor);
}
var curriedMix = curry(mix);
var mix$1 = curriedMix;
function opacify(amount, color) {
  if (color === "transparent")
    return color;
  var parsedColor = parseToRgb(color);
  var alpha = typeof parsedColor.alpha === "number" ? parsedColor.alpha : 1;
  var colorWithAlpha = _extends({}, parsedColor, {
    alpha: guard(0, 1, (alpha * 100 + parseFloat(amount) * 100) / 100)
  });
  return rgba(colorWithAlpha);
}
var curriedOpacify = curry(opacify);
var defaultReturnIfLightColor = "#000";
var defaultReturnIfDarkColor = "#fff";
function readableColor(color, returnIfLightColor, returnIfDarkColor, strict) {
  if (returnIfLightColor === void 0) {
    returnIfLightColor = defaultReturnIfLightColor;
  }
  if (returnIfDarkColor === void 0) {
    returnIfDarkColor = defaultReturnIfDarkColor;
  }
  if (strict === void 0) {
    strict = true;
  }
  var isColorLight = getLuminance(color) > 0.179;
  var preferredReturnColor = isColorLight ? returnIfLightColor : returnIfDarkColor;
  if (!strict || getContrast(color, preferredReturnColor) >= 4.5) {
    return preferredReturnColor;
  }
  return isColorLight ? defaultReturnIfLightColor : defaultReturnIfDarkColor;
}
function saturate(amount, color) {
  if (color === "transparent")
    return color;
  var hslColor = parseToHsl(color);
  return toColorString(_extends({}, hslColor, {
    saturation: guard(0, 1, hslColor.saturation + parseFloat(amount))
  }));
}
var curriedSaturate = curry(saturate);
function setHue(hue, color) {
  if (color === "transparent")
    return color;
  return toColorString(_extends({}, parseToHsl(color), {
    hue: parseFloat(hue)
  }));
}
var curriedSetHue = curry(setHue);
function setLightness(lightness, color) {
  if (color === "transparent")
    return color;
  return toColorString(_extends({}, parseToHsl(color), {
    lightness: parseFloat(lightness)
  }));
}
var curriedSetLightness = curry(setLightness);
function setSaturation(saturation, color) {
  if (color === "transparent")
    return color;
  return toColorString(_extends({}, parseToHsl(color), {
    saturation: parseFloat(saturation)
  }));
}
var curriedSetSaturation = curry(setSaturation);
function shade(percentage, color) {
  if (color === "transparent")
    return color;
  return mix$1(parseFloat(percentage), "rgb(0, 0, 0)", color);
}
var curriedShade = curry(shade);
function tint(percentage, color) {
  if (color === "transparent")
    return color;
  return mix$1(parseFloat(percentage), "rgb(255, 255, 255)", color);
}
var curriedTint = curry(tint);
function transparentize(amount, color) {
  if (color === "transparent")
    return color;
  var parsedColor = parseToRgb(color);
  var alpha = typeof parsedColor.alpha === "number" ? parsedColor.alpha : 1;
  var colorWithAlpha = _extends({}, parsedColor, {
    alpha: guard(0, 1, +(alpha * 100 - parseFloat(amount) * 100).toFixed(2) / 100)
  });
  return rgba(colorWithAlpha);
}
var curriedTransparentize = curry(transparentize);

// node_modules/@zendeskgarden/react-theming/dist/index.esm.js
var import_lodash = __toESM(require_lodash());
function _extends2() {
  _extends2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends2.apply(this, arguments);
}
var PALETTE = {
  black: "#000",
  white: "#fff",
  product: {
    support: "#00a656",
    message: "#37b8af",
    explore: "#30aabc",
    gather: "#f6c8be",
    guide: "#ff6224",
    connect: "#ff6224",
    chat: "#f79a3e",
    talk: "#efc93d",
    sell: "#c38f00"
  },
  grey: {
    100: "#f8f9f9",
    200: "#e9ebed",
    300: "#d8dcde",
    400: "#c2c8cc",
    500: "#87929d",
    600: "#68737d",
    700: "#49545c",
    800: "#2f3941"
  },
  blue: {
    100: "#edf7ff",
    200: "#cee2f2",
    300: "#adcce4",
    400: "#5293c7",
    500: "#337fbd",
    600: "#1f73b7",
    700: "#144a75",
    800: "#0f3554"
  },
  red: {
    100: "#fff0f1",
    200: "#f5d5d8",
    300: "#f5b5ba",
    400: "#e35b66",
    500: "#d93f4c",
    600: "#cc3340",
    700: "#8c232c",
    800: "#681219"
  },
  yellow: {
    100: "#fff7ed",
    200: "#ffeedb",
    300: "#fed6a8",
    400: "#ffb057",
    500: "#f79a3e",
    600: "#ed8f1c",
    700: "#ad5918",
    800: "#703815"
  },
  green: {
    100: "#edf8f4",
    200: "#d1e8df",
    300: "#aecfc2",
    400: "#5eae91",
    500: "#228f67",
    600: "#038153",
    700: "#186146",
    800: "#0b3b29"
  },
  kale: {
    100: "#f5fcfc",
    200: "#daeded",
    300: "#bdd9d7",
    400: "#90bbbb",
    500: "#467b7c",
    600: "#17494d",
    700: "#03363d",
    800: "#012b30"
  },
  fuschia: {
    400: "#d653c2",
    600: "#a81897",
    M400: "#cf62a8",
    M600: "#a8458c"
  },
  pink: {
    400: "#ec4d63",
    600: "#d42054",
    M400: "#d57287",
    M600: "#b23a5d"
  },
  crimson: {
    400: "#e34f32",
    600: "#c72a1c",
    M400: "#cc6c5b",
    M600: "#b24a3c"
  },
  orange: {
    400: "#de701d",
    600: "#bf5000",
    M400: "#d4772c",
    M600: "#b35827"
  },
  lemon: {
    400: "#ffd424",
    600: "#ffbb10",
    M400: "#e7a500",
    M600: "#c38f00"
  },
  lime: {
    400: "#43b324",
    600: "#2e8200",
    M400: "#519e2d",
    M600: "#47782c"
  },
  mint: {
    400: "#00a656",
    600: "#058541",
    M400: "#299c66",
    M600: "#2e8057"
  },
  teal: {
    400: "#02a191",
    600: "#028079",
    M400: "#2d9e8f",
    M600: "#3c7873"
  },
  azure: {
    400: "#3091ec",
    600: "#1371d6",
    M400: "#5f8dcf",
    M600: "#3a70b2"
  },
  royal: {
    400: "#5d7df5",
    600: "#3353e2",
    M400: "#7986d8",
    M600: "#4b61c3"
  },
  purple: {
    400: "#b552e2",
    600: "#6a27b8",
    M400: "#b072cc",
    M600: "#9358b0"
  }
};
var BASE = 4;
var borderRadii = {
  sm: `${BASE / 2}px`,
  md: `${BASE}px`
};
var borderStyles = {
  solid: "solid"
};
var borderWidths = {
  sm: "1px",
  md: "3px"
};
var borders = {
  sm: `${borderWidths.sm} ${borderStyles.solid}`,
  md: `${borderWidths.md} ${borderStyles.solid}`
};
var breakpoints = {
  xs: "0px",
  sm: `${BASE * 144}px`,
  md: `${BASE * 192}px`,
  lg: `${BASE * 248}px`,
  xl: `${BASE * 300}px`
};
var colors = {
  background: PALETTE.white,
  foreground: PALETTE.grey[800],
  primaryHue: "blue",
  dangerHue: "red",
  warningHue: "yellow",
  successHue: "green",
  neutralHue: "grey",
  chromeHue: "kale"
};
var fonts = {
  mono: ["SFMono-Regular", "Consolas", '"Liberation Mono"', "Menlo", "Courier", "monospace"].join(","),
  system: ["system-ui", "-apple-system", "BlinkMacSystemFont", '"Segoe UI"', "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", '"Helvetica Neue"', "Arial", "sans-serif"].join(",")
};
var fontSizes = {
  xs: "10px",
  sm: "12px",
  md: "14px",
  lg: "18px",
  xl: "22px",
  xxl: "26px",
  xxxl: "36px"
};
var fontWeights = {
  thin: 100,
  extralight: 200,
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
  extrabold: 800,
  black: 900
};
var iconSizes = {
  sm: "12px",
  md: "16px",
  lg: "26px"
};
var lineHeights = {
  sm: `${BASE * 4}px`,
  md: `${BASE * 5}px`,
  lg: `${BASE * 6}px`,
  xl: `${BASE * 7}px`,
  xxl: `${BASE * 8}px`,
  xxxl: `${BASE * 11}px`
};
var palette = {
  ...PALETTE
};
delete palette.product;
var shadowWidths = {
  xs: "1px",
  sm: "2px",
  md: "3px"
};
var shadows = {
  xs: (color) => `0 0 0 ${shadowWidths.xs} ${color}`,
  sm: (color) => `0 0 0 ${shadowWidths.sm} ${color}`,
  md: (color) => `0 0 0 ${shadowWidths.md} ${color}`,
  lg: (offsetY, blurRadius, color) => `0 ${offsetY} ${blurRadius} 0 ${color}`
};
var space = {
  base: BASE,
  xxs: `${BASE}px`,
  xs: `${BASE * 2}px`,
  sm: `${BASE * 3}px`,
  md: `${BASE * 5}px`,
  lg: `${BASE * 8}px`,
  xl: `${BASE * 10}px`,
  xxl: `${BASE * 12}px`
};
var DEFAULT_THEME = {
  borders,
  borderRadii,
  borderStyles,
  borderWidths,
  breakpoints,
  colors: {
    base: "light",
    ...colors
  },
  components: {},
  fonts,
  fontSizes,
  fontWeights,
  iconSizes,
  lineHeights,
  palette,
  rtl: false,
  shadowWidths,
  shadows,
  space
};
var useDocument = (theme) => {
  const [controlledDocument, setControlledDocument] = (0, import_react2.useState)();
  (0, import_react2.useEffect)(() => {
    if (theme && theme.document) {
      setControlledDocument(theme.document);
    } else {
      setControlledDocument(document);
    }
  }, [theme]);
  return controlledDocument;
};
var ThemeProvider = (_ref) => {
  let {
    theme,
    focusVisibleRef,
    children,
    ...other
  } = _ref;
  const scopeRef = (0, import_react2.useRef)(null);
  const relativeDocument = useDocument(theme);
  const controlledScopeRef = focusVisibleRef === null ? import_react2.default.createRef() : getControlledValue(focusVisibleRef, scopeRef);
  useFocusVisible({
    scope: controlledScopeRef,
    relativeDocument
  });
  return import_react2.default.createElement(Le, _extends2({
    theme
  }, other), focusVisibleRef === void 0 ? import_react2.default.createElement("div", {
    ref: scopeRef
  }, children) : children);
};
ThemeProvider.defaultProps = {
  theme: DEFAULT_THEME
};
function isRtl() {
  let {
    theme
  } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
  return Boolean(theme && theme.rtl);
}
function retrieveComponentStyles(componentId, props) {
  const components = props.theme && props.theme.components;
  if (!components) {
    return void 0;
  }
  const componentStyles = components[componentId];
  if (typeof componentStyles === "function") {
    return componentStyles(props);
  }
  return componentStyles;
}
function withTheme(WrappedComponent) {
  return Je(WrappedComponent);
}
function getDocument() {
  let {
    theme
  } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
  return theme && theme.document || document;
}
var DEFAULT_SHADE = 600;
var adjust = (color, expected, actual) => {
  if (expected !== actual) {
    const amount = Math.abs(expected - actual) / 100 * 0.05;
    return expected > actual ? curriedDarken$1(amount, color) : curriedLighten$1(amount, color);
  }
  return color;
};
var getColor = (0, import_lodash.default)(function(hue) {
  let shade2 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : DEFAULT_SHADE;
  let theme = arguments.length > 2 ? arguments[2] : void 0;
  let transparency = arguments.length > 3 ? arguments[3] : void 0;
  let retVal;
  if (isNaN(shade2)) {
    return void 0;
  }
  const palette2 = theme && theme.palette ? theme.palette : DEFAULT_THEME.palette;
  const colors2 = theme && theme.colors ? theme.colors : DEFAULT_THEME.colors;
  let _hue;
  if (typeof hue === "string") {
    _hue = colors2[hue] || hue;
  } else {
    _hue = hue;
  }
  if (Object.prototype.hasOwnProperty.call(palette2, _hue)) {
    _hue = palette2[_hue];
  }
  if (typeof _hue === "object") {
    retVal = _hue[shade2];
    if (!retVal) {
      const _shade = Object.keys(_hue).map((hueKey) => parseInt(hueKey, 10)).reduce((previous, current) => {
        return Math.abs(current - shade2) < Math.abs(previous - shade2) ? current : previous;
      });
      retVal = adjust(_hue[_shade], shade2, _shade);
    }
  } else {
    retVal = adjust(_hue, shade2, DEFAULT_SHADE);
  }
  if (transparency) {
    retVal = rgba(retVal, transparency);
  }
  return retVal;
}, (hue, shade2, theme, transparency) => JSON.stringify({
  hue,
  shade: shade2,
  palette: theme == null ? void 0 : theme.palette,
  colors: theme == null ? void 0 : theme.colors,
  transparency
}));
var getFocusBoxShadow = (_ref) => {
  let {
    boxShadow,
    inset = false,
    hue = "primaryHue",
    shade: shade2 = DEFAULT_SHADE,
    shadowWidth = "md",
    spacerHue = "background",
    spacerShade = DEFAULT_SHADE,
    spacerWidth = "xs",
    theme = DEFAULT_THEME
  } = _ref;
  const color = getColor(hue, shade2, theme);
  const shadow = theme.shadows[shadowWidth](color);
  if (spacerWidth === null) {
    return `${inset ? "inset" : ""} ${shadow}`;
  }
  const spacerColor = getColor(spacerHue, spacerShade, theme);
  const retVal = `
    ${inset ? "inset" : ""} ${theme.shadows[spacerWidth](spacerColor)},
    ${inset ? "inset" : ""} ${shadow}`;
  return boxShadow ? `${retVal}, ${boxShadow}` : retVal;
};
function getLineHeight(height, fontSize) {
  const [heightValue, heightUnit] = getValueAndUnit(height.toString());
  const [fontSizeValue, fontSizeUnit] = getValueAndUnit(fontSize.toString());
  const PIXELS = "px";
  if (heightUnit && heightUnit !== PIXELS) {
    throw new Error(`Unexpected \`height\` with '${heightUnit}' units.`);
  }
  if (fontSizeUnit && fontSizeUnit !== PIXELS) {
    throw new Error(`Unexpected \`fontSize\` with '${fontSizeUnit}' units.`);
  }
  return heightValue / fontSizeValue;
}
var maxWidth = (breakpoints2, breakpoint) => {
  const keys = Object.keys(breakpoints2);
  const index = keys.indexOf(breakpoint) + 1;
  if (keys[index]) {
    const dimension = getValueAndUnit(breakpoints2[keys[index]]);
    const value = dimension[0] - 0.02;
    const unit = dimension[1];
    return `${value}${unit}`;
  }
  return void 0;
};
function mediaQuery(query, breakpoint, theme) {
  let retVal;
  let min2;
  let max2;
  const breakpoints2 = theme && theme.breakpoints ? theme.breakpoints : DEFAULT_THEME.breakpoints;
  if (typeof breakpoint === "string") {
    if (query === "up") {
      min2 = breakpoints2[breakpoint];
    } else if (query === "down") {
      if (breakpoint === "xl") {
        min2 = DEFAULT_THEME.breakpoints.xs;
      } else {
        max2 = maxWidth(breakpoints2, breakpoint);
      }
    } else if (query === "only") {
      min2 = breakpoints2[breakpoint];
      max2 = maxWidth(breakpoints2, breakpoint);
    }
  } else if (query === "between") {
    min2 = breakpoints2[breakpoint[0]];
    max2 = maxWidth(breakpoints2, breakpoint[1]);
  }
  if (min2) {
    retVal = `@media (min-width: ${min2})`;
    if (max2) {
      retVal = `${retVal} and (max-width: ${max2})`;
    }
  } else if (max2) {
    retVal = `@media (max-width: ${max2})`;
  } else {
    throw new Error(`Unexpected query and breakpoint combination: '${query}', '${breakpoint}'.`);
  }
  return retVal;
}
var exponentialSymbols = {
  symbols: {
    sqrt: {
      func: {
        symbol: "sqrt",
        f: (a) => Math.sqrt(a),
        notation: "func",
        precedence: 0,
        rightToLeft: 0,
        argCount: 1
      },
      symbol: "sqrt",
      regSymbol: "sqrt\\b"
    }
  }
};
var animationStyles$1 = (position, modifier) => {
  const property = position.split("-")[0];
  const animationName = We(["0%,66%{", ":2px;border:transparent;}"], property);
  return Ae(["&", "::before,&", "::after{animation:0.3s ease-in-out ", ";}"], modifier, modifier, animationName);
};
var positionStyles = (position, size, inset) => {
  const margin = math(`${size} / -2`);
  const placement = math(`${margin} + ${inset}`);
  let clipPath;
  let positionCss;
  let propertyRadius;
  if (position.startsWith("top")) {
    propertyRadius = "border-bottom-right-radius";
    clipPath = "polygon(100% 0, 100% 1px, 1px 100%, 0 100%, 0 0)";
    positionCss = Ae(["top:", ";right:", ";left:", ";margin-left:", ";"], placement, position === "top-right" && size, position === "top" ? "50%" : position === "top-left" && size, position === "top" && margin);
  } else if (position.startsWith("right")) {
    propertyRadius = "border-bottom-left-radius";
    clipPath = "polygon(100% 0, 100% 100%, calc(100% - 1px) 100%, 0 1px, 0 0)";
    positionCss = Ae(["top:", ";right:", ";bottom:", ";margin-top:", ";"], position === "right" ? "50%" : position === "right-top" && size, placement, position === "right-bottom" && size, position === "right" && margin);
  } else if (position.startsWith("bottom")) {
    propertyRadius = "border-top-left-radius";
    clipPath = "polygon(100% 0, calc(100% - 1px) 0, 0 calc(100% - 1px), 0 100%, 100% 100%)";
    positionCss = Ae(["right:", ";bottom:", ";left:", ";margin-left:", ";"], position === "bottom-right" && size, placement, position === "bottom" ? "50%" : position === "bottom-left" && size, position === "bottom" && margin);
  } else if (position.startsWith("left")) {
    propertyRadius = "border-top-right-radius";
    clipPath = "polygon(0 100%, 100% 100%, 100% calc(100% - 1px), 1px 0, 0 0)";
    positionCss = Ae(["top:", ";bottom:", ";left:", ";margin-top:", ";"], position === "left" ? "50%" : position === "left-top" && size, size, placement, position === "left" && margin);
  }
  return Ae(["&::before{", ":100%;clip-path:", ";}&::before,&::after{", "}"], propertyRadius, clipPath, positionCss);
};
function arrowStyles(position) {
  let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  const size = options.size || "6px";
  const inset = options.inset || "0";
  const squareSize = math(`${size} * 2 / sqrt(2)`, exponentialSymbols);
  return Ae(["position:relative;&::before{border-width:inherit;border-style:inherit;border-color:transparent;background-clip:content-box;}&::after{z-index:-1;border:inherit;box-shadow:inherit;}&::before,&::after{position:absolute;transform:rotate(45deg);background-color:inherit;box-sizing:inherit;width:", ";height:", ";content:'';}", ";", ";"], squareSize, squareSize, positionStyles(position, squareSize, inset), options.animationModifier && animationStyles$1(position, options.animationModifier));
}
var useWindow = (theme) => {
  const [controlledWindow, setControlledWindow] = (0, import_react2.useState)();
  (0, import_react2.useEffect)(() => {
    if (theme && theme.window) {
      setControlledWindow(theme.window);
    } else {
      setControlledWindow(window);
    }
  }, [theme]);
  return controlledWindow;
};
var useText = function(component, props, name, text) {
  let condition = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : true;
  const value = condition ? props[name] : void 0;
  return (0, import_react2.useMemo)(() => {
    if (condition) {
      if (name === "children") {
        throw new Error("Error: `children` is not a valid `useText` prop.");
      } else if (value === null || value === "") {
        throw new Error(component.displayName ? `Error: you must provide a valid \`${name}\` text value for <${component.displayName}>.` : `Error: you must provide a valid \`${name}\` text value.`);
      } else if (value === void 0) {
        if (true) {
          console.warn(component.displayName ? `Warning: you did not provide a customized/translated \`${name}\` text value for <${component.displayName}>. Zendesk Garden is rendering <${component.displayName} ${name}="${text}"> by default.` : `Warning: you did not provide a customized/translated \`${name}\` text value. Zendesk Garden is rendering ${name}="${text}" by default.`);
        }
        return text;
      }
    }
    return value;
  }, [component.displayName, value, name, text, condition]);
};
var animationStyles = (position, options) => {
  const theme = options.theme || DEFAULT_THEME;
  let translateValue = `${theme.space.base * 5}px`;
  let transformFunction;
  if (position === "top") {
    transformFunction = "translateY";
  } else if (position === "right") {
    transformFunction = "translateX";
    translateValue = `-${translateValue}`;
  } else if (position === "bottom") {
    transformFunction = "translateY";
    translateValue = `-${translateValue}`;
  } else {
    transformFunction = "translateX";
  }
  const animationName = We(["0%{transform:", "(", ");}"], transformFunction, translateValue);
  return Ae(["&", " ", "{animation:0.2s cubic-bezier(0.15,0.85,0.35,1.2) ", ";}"], options.animationModifier, options.childSelector || "> *", animationName);
};
var hiddenStyles = (options) => {
  const transition = "opacity 0.2s ease-in-out, 0.2s visibility 0s linear";
  return Ae(["transition:", ";visibility:hidden;opacity:0;"], options.animationModifier && transition);
};
function menuStyles(position) {
  let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  const theme = options.theme || DEFAULT_THEME;
  let marginProperty;
  if (position === "top") {
    marginProperty = "margin-bottom";
  } else if (position === "right") {
    marginProperty = "margin-left";
  } else if (position === "bottom") {
    marginProperty = "margin-top";
  } else {
    marginProperty = "margin-right";
  }
  return Ae(["position:absolute;z-index:", ";", ":", ";line-height:0;font-size:0.01px;& ", "{display:inline-block;position:relative;margin:0;box-sizing:border-box;border:", " ", ";border-radius:", ";box-shadow:", ";background-color:", ";cursor:default;padding:0;text-align:", ";white-space:normal;font-size:", ";font-weight:", ";direction:", ";:focus{outline:none;}}", ";", ";"], options.zIndex || 0, marginProperty, options.margin, options.childSelector || "> *", theme.borders.sm, getColor("neutralHue", 300, theme), theme.borderRadii.md, theme.shadows.lg(`${theme.space.base * 5}px`, `${theme.space.base * 7.5}px`, getColor("chromeHue", 600, theme, 0.15)), theme.colors.background, theme.rtl ? "right" : "left", theme.fontSizes.md, theme.fontWeights.regular, theme.rtl && "rtl", options.animationModifier && animationStyles(position, options), options.hidden && hiddenStyles(options));
}
var SELECTOR_FOCUS_VISIBLE = '&:focus-visible, &[data-garden-focus-visible="true"]';
var focusStyles = (_ref) => {
  let {
    condition = true,
    selector = SELECTOR_FOCUS_VISIBLE,
    shadowWidth = "md",
    spacerWidth = "xs",
    styles: {
      boxShadow,
      ...styles
    } = {},
    theme,
    ...options
  } = _ref;
  const _boxShadow = condition ? getFocusBoxShadow({
    boxShadow,
    shadowWidth,
    spacerWidth,
    theme,
    ...options
  }) : boxShadow;
  let outline;
  let outlineOffset;
  if (spacerWidth === null) {
    outline = theme.shadowWidths[shadowWidth];
  } else {
    outline = `${math(`${theme.shadowWidths[shadowWidth]} - ${theme.shadowWidths[spacerWidth]}`)} solid transparent`;
    outlineOffset = theme.shadowWidths[spacerWidth];
  }
  return Ae(["&:focus{outline:none;}", "{outline:", ";outline-offset:", ";box-shadow:", ";", "}"], selector, outline, outlineOffset, _boxShadow, styles);
};
var ARROW_POSITION = ["top", "top-left", "top-right", "right", "right-top", "right-bottom", "bottom", "bottom-left", "bottom-right", "left", "left-top", "left-bottom"];
var MENU_POSITION = ["top", "right", "bottom", "left"];

export {
  useFocusVisible,
  _extends,
  _inheritsLoose,
  math,
  stripUnit,
  em$1,
  hideVisually,
  rgba,
  readableColor,
  PALETTE,
  DEFAULT_THEME,
  useDocument,
  ThemeProvider,
  isRtl,
  retrieveComponentStyles,
  withTheme,
  getDocument,
  getColor,
  getFocusBoxShadow,
  getLineHeight,
  mediaQuery,
  arrowStyles,
  useWindow,
  useText,
  menuStyles,
  SELECTOR_FOCUS_VISIBLE,
  focusStyles,
  ARROW_POSITION,
  MENU_POSITION
};
//# sourceMappingURL=chunk-KGUWDO6Q.js.map
