{"version": 3, "sources": ["../../node_modules/@zendeskgarden/react-typography/dist/index.esm.js", "../../node_modules/prism-react-renderer/prism/index.js", "../../node_modules/prism-react-renderer/dist/index.js", "../../node_modules/@zendeskgarden/container-scrollregion/dist/index.esm.js"], "sourcesContent": ["/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { Children, forwardRef, useRef, useMemo, createContext, useContext } from 'react';\nimport PropTypes from 'prop-types';\nimport styled, { css } from 'styled-components';\nimport { retrieveComponentStyles, DEFAULT_THEME, getColor, getLineHeight } from '@zendeskgarden/react-theming';\nimport { math } from 'polished';\nimport Highlight, { Prism } from 'prism-react-renderer';\nimport { useScrollRegion } from '@zendeskgarden/container-scrollregion';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nconst HUE = ['grey', 'red', 'green', 'yellow'];\nconst SIZE = ['small', 'medium', 'large'];\nconst INHERIT_SIZE = ['inherit', ...SIZE];\nconst TYPE_ORDERED_LIST = ['decimal', 'decimal-leading-zero', 'lower-alpha', 'lower-roman', 'upper-alpha', 'upper-roman'];\nconst TYPE_UNORDERED_LIST = ['circle', 'disc', 'square'];\nconst LANGUAGES = ['markup', 'bash', 'clike', 'c', 'cpp', 'css', 'javascript', 'jsx', 'coffeescript', 'actionscript', 'css-extr', 'diff', 'git', 'go', 'graphql', 'handlebars', 'json', 'less', 'makefile', 'markdown', 'objectivec', 'ocaml', 'python', 'reason', 'sass', 'scss', 'sql', 'stylus', 'tsx', 'typescript', 'wasm', 'yaml'];\n\nconst COMPONENT_ID$9 = 'typography.font';\n[...SIZE, 'extralarge', '2xlarge', '3xlarge'];\nconst THEME_SIZES = {\n  small: 'sm',\n  medium: 'md',\n  large: 'lg',\n  extralarge: 'xl',\n  '2xlarge': 'xxl',\n  '3xlarge': 'xxxl'\n};\nconst fontStyles = props => {\n  const monospace = props.isMonospace && ['inherit', 'small', 'medium', 'large'].indexOf(props.size) !== -1;\n  const fontFamily = monospace && props.theme.fonts.mono;\n  const direction = props.theme.rtl ? 'rtl' : 'ltr';\n  let fontSize;\n  let fontWeight;\n  let lineHeight;\n  let color;\n  if (monospace) {\n    if (props.size === 'inherit') {\n      fontSize = 'calc(1em - 1px)';\n      lineHeight = 'normal';\n    } else {\n      const themeSize = THEME_SIZES[props.size];\n      fontSize = math(`${props.theme.fontSizes[themeSize]} - 1px`);\n      lineHeight = math(`${props.theme.lineHeights[themeSize]} - 1px`);\n    }\n  } else if (props.size !== 'inherit') {\n    const themeSize = THEME_SIZES[props.size];\n    fontSize = props.theme.fontSizes[themeSize];\n    lineHeight = props.theme.lineHeights[themeSize];\n  }\n  if (props.isBold === true) {\n    fontWeight = props.theme.fontWeights.semibold;\n  } else if (props.isBold === false || props.size !== 'inherit') {\n    fontWeight = props.theme.fontWeights.regular;\n  }\n  if (props.hue) {\n    const shade = props.hue === 'yellow' ? 700 : 600;\n    color = getColor(props.hue, shade, props.theme);\n  }\n  return css([\"line-height:\", \";color:\", \";font-family:\", \";font-size:\", \";font-weight:\", \";direction:\", \";\"], lineHeight, color, fontFamily, fontSize, fontWeight, direction);\n};\nconst StyledFont = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$9,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledFont\",\n  componentId: \"sc-1iildbo-0\"\n})([\"\", \";\", \";\"], props => fontStyles(props), props => retrieveComponentStyles(COMPONENT_ID$9, props));\nStyledFont.defaultProps = {\n  theme: DEFAULT_THEME,\n  size: 'inherit'\n};\n\nconst COMPONENT_ID$8 = 'typography.blockquote';\nconst StyledBlockquote = styled.blockquote.attrs({\n  'data-garden-id': COMPONENT_ID$8,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledBlockquote\",\n  componentId: \"sc-1tt3ye0-0\"\n})([\"margin:0;border-\", \":\", \" solid;border-color:\", \";padding:0;padding-\", \":\", \"px;direction:\", \";p + &,& + &{margin-top:\", \";}\", \";\"], props => props.theme.rtl ? 'right' : 'left', props => props.theme.shadowWidths.sm, props => getColor('neutralHue', 400, props.theme), props => props.theme.rtl ? 'right' : 'left', props => props.theme.space.base * 4, props => props.theme.rtl ? 'rtl' : 'ltr', props => props.theme.lineHeights[THEME_SIZES[props.size]], props => retrieveComponentStyles(COMPONENT_ID$8, props));\nStyledBlockquote.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$7 = 'typography.code';\nconst colorStyles$3 = props => {\n  const hue = props.hue || 'neutralHue';\n  const backgroundColor = getColor(hue, 200, props.theme);\n  const shade = hue === 'yellow' ? 800 : 700;\n  const foregroundColor = getColor(hue, shade, props.theme);\n  return css([\"background-color:\", \";color:\", \";a &{color:inherit;}\"], backgroundColor, foregroundColor);\n};\nconst StyledCode = styled(StyledFont).attrs({\n  'data-garden-id': COMPONENT_ID$7,\n  'data-garden-version': '8.67.0',\n  as: 'code'\n}).withConfig({\n  displayName: \"StyledCode\",\n  componentId: \"sc-l8yvmf-0\"\n})([\"border-radius:\", \";padding:1.5px;\", \";\", \";\"], props => props.theme.borderRadii.sm, props => colorStyles$3(props), props => retrieveComponentStyles(COMPONENT_ID$7, props));\nStyledCode.defaultProps = {\n  theme: DEFAULT_THEME,\n  isMonospace: true,\n  hue: 'neutralHue',\n  size: 'inherit'\n};\n\nconst COMPONENT_ID$6 = 'typography.codeblock';\nconst colorStyles$2 = props => {\n  const backgroundColor = getColor('neutralHue', props.isLight ? 100 : 1000, props.theme);\n  const foregroundColor = props.isLight ? props.theme.colors.foreground : getColor('neutralHue', 300, props.theme);\n  return css([\"background-color:\", \";color:\", \";\"], backgroundColor, foregroundColor);\n};\nconst StyledCodeBlock = styled.pre.attrs({\n  'data-garden-id': COMPONENT_ID$6,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledCodeBlock\",\n  componentId: \"sc-5wky57-0\"\n})([\"display:table;margin:0;padding:\", \"px;box-sizing:border-box;width:100%;direction:ltr;white-space:pre;counter-reset:linenumber;\", \";\", \";\"], props => props.theme.space.base * 3, props => colorStyles$2(props), props => retrieveComponentStyles(COMPONENT_ID$6, props));\nStyledCodeBlock.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$5 = 'typography.codeblock_container';\nconst StyledCodeBlockContainer = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$5,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledCodeBlockContainer\",\n  componentId: \"sc-14zgbfw-0\"\n})([\"overflow:auto;&:focus{outline:none;}&[data-garden-focus-visible]{box-shadow:\", \";}\", \";\"], props => props.theme.shadows.md(getColor('primaryHue', 600, props.theme, 0.35)), props => retrieveComponentStyles(COMPONENT_ID$5, props));\nStyledCodeBlockContainer.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$4 = 'typography.codeblock_code';\nconst colorStyles$1 = props => {\n  let backgroundColor;\n  if (props.diff) {\n    let hue;\n    switch (props.diff) {\n      case 'hunk':\n        hue = 'royal';\n        break;\n      case 'add':\n        hue = 'lime';\n        break;\n      case 'delete':\n        hue = 'crimson';\n        break;\n      case 'change':\n        hue = 'lemon';\n        break;\n    }\n    backgroundColor = getColor(hue, 400, props.theme, 0.2);\n  } else if (props.isHighlighted) {\n    const hue = props.isLight ? props.theme.palette.black : props.theme.palette.white;\n    backgroundColor = getColor(hue, 600, props.theme, 0.1);\n  }\n  return css([\"background-color:\", \";\"], backgroundColor);\n};\nconst lineNumberStyles = props => {\n  const color = getColor('neutralHue', props.isLight ? 600 : 500, props.theme);\n  let padding;\n  if (props.language && props.language === 'diff') {\n    padding = 0;\n  } else if (props.size === 'small') {\n    padding = props.theme.space.base * 4;\n  } else if (props.size === 'large') {\n    padding = props.theme.space.base * 7;\n  } else {\n    padding = props.theme.space.base * 6;\n  }\n  return `\n    &::before {\n      display: table-cell;\n      padding-right: ${padding}px;\n      width: 1px;\n      text-align: right;\n      color: ${color};\n      content: counter(linenumber);\n      counter-increment: linenumber;\n    }\n  `;\n};\nconst StyledCodeBlockLine = styled(StyledFont).attrs({\n  'data-garden-id': COMPONENT_ID$4,\n  'data-garden-version': '8.67.0',\n  as: 'code',\n  isMonospace: true\n}).withConfig({\n  displayName: \"StyledCodeBlockLine\",\n  componentId: \"sc-1goay17-0\"\n})([\"display:table-row;height:\", \";direction:ltr;\", \";\", \";&::after{display:inline-block;width:\", \"px;content:'';}\", \";\"], props => props.theme.lineHeights[THEME_SIZES[props.size]], props => colorStyles$1(props), props => props.isNumbered && lineNumberStyles(props), props => props.theme.space.base * 3, props => retrieveComponentStyles(COMPONENT_ID$4, props));\nStyledCodeBlockLine.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$3 = 'typography.codeblock_token';\nconst colorStyles = props => {\n  const palette = props.theme.palette;\n  const colors = {\n    boolean: props.isLight ? palette.royal[600] : palette.azure[400],\n    builtin: palette.teal[400],\n    comment: props.isLight ? palette.lime[600] : palette.mint[400],\n    constant: props.isLight ? palette.azure[400] : palette.blue[500],\n    coord: props.isLight ? palette.purple[600] : palette.blue[200],\n    deleted: props.isLight ? palette.red[700] : palette.red[200],\n    diff: props.isLight ? palette.yellow[800] : palette.yellow[200],\n    function: props.isLight ? palette.orange['M600'] : palette.yellow[300],\n    inserted: props.isLight ? palette.green[700] : palette.green[200],\n    keyword: palette.fuschia['M400'],\n    name: props.isLight ? palette.crimson[400] : palette.blue[300],\n    number: props.isLight ? palette.green[600] : palette.green[300],\n    punctuation: props.isLight ? palette.red[800] : palette.grey[600],\n    regex: palette.red[400],\n    value: props.isLight ? palette.red[700] : palette.crimson['M400']\n  };\n  return css([\"&.builtin,&.class-name,&.tag:not(.punctuation):not(.attr-name):not(.attr-value):not(.script){color:\", \";}&.doctype,&.prolog,&.tag.punctuation:not(.attr-value):not(.script):not(.spread){color:\", \";}&.attribute.value,&.attr-value,&.atrule,&.cdata,&.string,&.url.content{color:\", \";}&.constant,&.interpolation-punctuation{color:\", \";}&.attr-name,&.attr-value.spread,&.environment,&.interpolation,&.parameter,&.property,&.property-access,&.variable{color:\", \";}&.parameter.punctuation,&.attr-name + .attr-value.punctuation{color:inherit;}&.regex{color:\", \";}&.boolean,&.bold:not(.diff),&.entity,&.important,&.tag:not(.punctuation):not(.attr-name):not(.attr-value):not(.script):not(.class-name){color:\", \";}&.number,&.unit{color:\", \";}&.assign-left,&.function,&.selector:not(.attribute){color:\", \";}&.atrule.rule,&.keyword{color:\", \";}&.blockquote,&.comment,&.shebang{color:\", \";}\", \".language-css &&.plain{color:\", \";}\", \".language-diff &&.coord{color:\", \";}\", \".language-diff &&.deleted{color:\", \";}\", \".language-diff &&.diff{color:\", \";}\", \".language-diff &&.inserted{color:\", \";}\"], colors.builtin, colors.punctuation, colors.value, colors.constant, colors.name, colors.regex, colors.boolean, colors.number, colors.function, colors.keyword, colors.comment, StyledCodeBlock, colors.value, StyledCodeBlock, colors.coord, StyledCodeBlock, colors.deleted, StyledCodeBlock, colors.diff, StyledCodeBlock, colors.inserted);\n};\nconst StyledCodeBlockToken = styled.span.attrs({\n  'data-garden-id': COMPONENT_ID$3,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledCodeBlockToken\",\n  componentId: \"sc-1hkshdq-0\"\n})([\"display:inline-block;&.bold:not(.diff){font-weight:\", \";}&.coord{padding-left:0.75em;}&.italic{font-style:italic;}&.prefix{width:2em;text-align:center;}\", \";\", \";\"], props => props.theme.fontWeights.semibold, props => colorStyles(props), props => retrieveComponentStyles(COMPONENT_ID$3, props));\nStyledCodeBlockToken.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$2 = 'typography.ellipsis';\nconst StyledEllipsis = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$2,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledEllipsis\",\n  componentId: \"sc-1u4uqmy-0\"\n})([\"overflow:hidden;text-overflow:ellipsis;white-space:nowrap;direction:\", \";\", \";\"], props => props.theme.rtl ? 'rtl' : 'ltr', props => retrieveComponentStyles(COMPONENT_ID$2, props));\nStyledEllipsis.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$1 = 'typography.icon';\nconst sizeStyles = props => {\n  const margin = props.isStart && `${props.theme.space.base * 2}px`;\n  const size = props.theme.iconSizes.md;\n  return css([\"margin-\", \":\", \";width:\", \";height:\", \";\"], props.theme.rtl ? 'left' : 'right', margin, size, size);\n};\nconst StyledIcon = styled(_ref => {\n  let {\n    children,\n    isStart,\n    ...props\n  } = _ref;\n  return React.cloneElement(Children.only(children), props);\n}).attrs({\n  'data-garden-id': COMPONENT_ID$1,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledIcon\",\n  componentId: \"sc-10rfb5b-0\"\n})([\"position:relative;top:-1px;vertical-align:middle;\", \";\", \";\"], props => sizeStyles(props), props => retrieveComponentStyles(COMPONENT_ID$1, props));\nStyledIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst listStyles = props => {\n  const rtl = props.theme.rtl;\n  return css([\"direction:\", \";margin:0;margin-\", \":24px;padding:0;list-style-position:outside;list-style-type:\", \";\"], rtl ? 'rtl' : 'ltr', rtl ? 'right' : 'left', props.listType);\n};\nconst ORDERED_ID$1 = 'typography.ordered_list';\nconst StyledOrderedList = styled.ol.attrs({\n  'data-garden-id': ORDERED_ID$1,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledList__StyledOrderedList\",\n  componentId: \"sc-jdbsfi-0\"\n})([\"\", \";\", \";\"], props => listStyles(props), props => retrieveComponentStyles(ORDERED_ID$1, props));\nStyledOrderedList.defaultProps = {\n  theme: DEFAULT_THEME\n};\nconst UNORDERED_ID$1 = 'typography.unordered_list';\nconst StyledUnorderedList = styled.ul.attrs({\n  'data-garden-id': UNORDERED_ID$1,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledList__StyledUnorderedList\",\n  componentId: \"sc-jdbsfi-1\"\n})([\"\", \";\", \";\"], props => listStyles(props), props => retrieveComponentStyles(UNORDERED_ID$1, props));\nStyledUnorderedList.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst listItemPaddingStyles = props => {\n  const base = props.theme.space.base;\n  const paddingTop = props.space === 'large' ? `${base * 2}px` : `${base}px`;\n  return css([\"padding-top:\", \";\", \" > &:first-child,\", \" > &:first-child{padding-top:0;}\", \" \", \" > &:first-child,\", \" \", \" > &:first-child,\", \" \", \" > &:first-child,\", \" \", \" > &:first-child{padding-top:\", \";}\"], paddingTop, StyledOrderedList, StyledUnorderedList, StyledOrderedList, StyledOrderedList, StyledOrderedList, StyledUnorderedList, StyledUnorderedList, StyledUnorderedList, StyledUnorderedList, StyledUnorderedList, paddingTop);\n};\nconst listItemStyles = props => {\n  return css([\"line-height:\", \";\", \";\"], getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md), props.space !== 'small' && listItemPaddingStyles(props));\n};\nconst ORDERED_ID = 'typography.ordered_list_item';\nconst StyledOrderedListItem = styled(StyledFont).attrs({\n  'data-garden-id': ORDERED_ID,\n  'data-garden-version': '8.67.0',\n  as: 'li'\n}).withConfig({\n  displayName: \"StyledListItem__StyledOrderedListItem\",\n  componentId: \"sc-9rsipg-0\"\n})([\"margin-\", \":\", \";padding-\", \":\", \";\", \";\", \";\"], props => props.theme.rtl ? 'right' : 'left', props => math(`${props.theme.space.base} * -1px`), props => props.theme.rtl ? 'right' : 'left', props => math(`${props.theme.space.base} * 1px`), props => listItemStyles(props), props => retrieveComponentStyles(ORDERED_ID, props));\nStyledOrderedListItem.defaultProps = {\n  space: 'medium',\n  theme: DEFAULT_THEME\n};\nconst UNORDERED_ID = 'typography.unordered_list_item';\nconst StyledUnorderedListItem = styled(StyledFont).attrs({\n  'data-garden-id': UNORDERED_ID,\n  'data-garden-version': '8.67.0',\n  as: 'li'\n}).withConfig({\n  displayName: \"StyledListItem__StyledUnorderedListItem\",\n  componentId: \"sc-9rsipg-1\"\n})([\"\", \";\", \";\"], props => listItemStyles(props), props => retrieveComponentStyles(UNORDERED_ID, props));\nStyledUnorderedListItem.defaultProps = {\n  space: 'medium',\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID = 'typography.paragraph';\nconst StyledParagraph = styled.p.attrs({\n  'data-garden-id': COMPONENT_ID,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledParagraph\",\n  componentId: \"sc-zkuftz-0\"\n})([\"margin:0;padding:0;direction:\", \";blockquote + &,& + &{margin-top:\", \";}\", \";\"], props => props.theme.rtl ? 'rtl' : 'ltr', props => props.theme.lineHeights[THEME_SIZES[props.size]], props => retrieveComponentStyles(COMPONENT_ID, props));\nStyledParagraph.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst SM = forwardRef((_ref, ref) => {\n  let {\n    tag,\n    ...other\n  } = _ref;\n  return React.createElement(StyledFont, _extends({\n    as: tag,\n    ref: ref,\n    size: \"small\"\n  }, other));\n});\nSM.displayName = 'SM';\nSM.propTypes = {\n  tag: PropTypes.any,\n  isBold: PropTypes.bool,\n  isMonospace: PropTypes.bool\n};\nSM.defaultProps = {\n  tag: 'div'\n};\n\nconst MD = forwardRef((_ref, ref) => {\n  let {\n    tag,\n    ...other\n  } = _ref;\n  return React.createElement(StyledFont, _extends({\n    as: tag,\n    ref: ref,\n    size: \"medium\"\n  }, other));\n});\nMD.displayName = 'MD';\nMD.propTypes = {\n  tag: PropTypes.any,\n  isBold: PropTypes.bool,\n  isMonospace: PropTypes.bool\n};\nMD.defaultProps = {\n  tag: 'div'\n};\n\nconst LG = forwardRef((_ref, ref) => {\n  let {\n    tag,\n    ...other\n  } = _ref;\n  return React.createElement(StyledFont, _extends({\n    as: tag,\n    ref: ref,\n    size: \"large\"\n  }, other));\n});\nLG.displayName = 'LG';\nLG.propTypes = {\n  tag: PropTypes.any,\n  isBold: PropTypes.bool,\n  isMonospace: PropTypes.bool\n};\nLG.defaultProps = {\n  tag: 'div'\n};\n\nconst XL = forwardRef((_ref, ref) => {\n  let {\n    tag,\n    ...other\n  } = _ref;\n  return React.createElement(StyledFont, _extends({\n    as: tag,\n    ref: ref,\n    size: \"extralarge\"\n  }, other));\n});\nXL.displayName = 'XL';\nXL.propTypes = {\n  tag: PropTypes.any,\n  isBold: PropTypes.bool\n};\nXL.defaultProps = {\n  tag: 'div'\n};\n\nconst XXL = forwardRef((_ref, ref) => {\n  let {\n    tag,\n    ...other\n  } = _ref;\n  return React.createElement(StyledFont, _extends({\n    as: tag,\n    ref: ref,\n    size: \"2xlarge\"\n  }, other));\n});\nXXL.displayName = 'XXL';\nXXL.propTypes = {\n  tag: PropTypes.any,\n  isBold: PropTypes.bool\n};\nXXL.defaultProps = {\n  tag: 'div'\n};\n\nconst XXXL = forwardRef((_ref, ref) => {\n  let {\n    tag,\n    ...other\n  } = _ref;\n  return React.createElement(StyledFont, _extends({\n    as: tag,\n    ref: ref,\n    size: \"3xlarge\"\n  }, other));\n});\nXXXL.displayName = 'XXXL';\nXXXL.propTypes = {\n  tag: PropTypes.any,\n  isBold: PropTypes.bool\n};\nXXXL.defaultProps = {\n  tag: 'div'\n};\n\nconst Blockquote = React.forwardRef((props, ref) => React.createElement(StyledBlockquote, _extends({\n  ref: ref\n}, props)));\nBlockquote.displayName = 'Blockquote';\nBlockquote.propTypes = {\n  size: PropTypes.oneOf(SIZE)\n};\nBlockquote.defaultProps = {\n  size: 'medium'\n};\n\nconst Code = forwardRef((_ref, ref) => {\n  let {\n    hue,\n    ...other\n  } = _ref;\n  return React.createElement(StyledCode, _extends({\n    ref: ref,\n    hue: hue\n  }, other));\n});\nCode.displayName = 'Code';\nCode.propTypes = {\n  hue: PropTypes.oneOf(HUE),\n  size: PropTypes.oneOf(INHERIT_SIZE)\n};\nCode.defaultProps = {\n  hue: 'grey',\n  size: 'inherit'\n};\n\nconst CodeBlock = React.forwardRef((_ref, ref) => {\n  let {\n    children,\n    containerProps,\n    highlightLines,\n    isLight,\n    isNumbered,\n    language,\n    size,\n    ...other\n  } = _ref;\n  const containerRef = useRef(null);\n  const code = Array.isArray(children) ? children[0] : children;\n  const dependency = useMemo(() => [size, children], [size, children]);\n  const containerTabIndex = useScrollRegion({\n    containerRef,\n    dependency\n  });\n  const getDiff = line => {\n    let retVal;\n    if (language === 'diff') {\n      const token = line.find(value => !(value.empty || value.content === ''));\n      if (token) {\n        if (token.types.includes('deleted')) {\n          retVal = 'delete';\n        } else if (token.types.includes('inserted')) {\n          retVal = 'add';\n        } else if (token.types.includes('coord')) {\n          retVal = 'hunk';\n        } else if (token.types.includes('diff')) {\n          retVal = 'change';\n        }\n      }\n    }\n    return retVal;\n  };\n  return React.createElement(StyledCodeBlockContainer, _extends({}, containerProps, {\n    ref: containerRef,\n    tabIndex: containerTabIndex\n  }), React.createElement(Highlight, {\n    Prism: Prism,\n    code: code ? code.trim() : '',\n    language: LANGUAGES.includes(language) ? language : 'tsx'\n  }, _ref2 => {\n    let {\n      className,\n      tokens,\n      getLineProps,\n      getTokenProps\n    } = _ref2;\n    return React.createElement(StyledCodeBlock, _extends({\n      className: className,\n      ref: ref,\n      isLight: isLight\n    }, other), tokens.map((line, index) => React.createElement(StyledCodeBlockLine, _extends({}, getLineProps({\n      line\n    }), {\n      key: index,\n      language: language,\n      isHighlighted: highlightLines && highlightLines.includes(index + 1),\n      isLight: isLight,\n      isNumbered: isNumbered,\n      diff: getDiff(line),\n      size: size\n    }), line.map((token, tokenKey) => React.createElement(StyledCodeBlockToken, _extends({}, getTokenProps({\n      token\n    }), {\n      key: tokenKey,\n      isLight: isLight\n    }), token.empty ? '\\n' : token.content)))));\n  }));\n});\nCodeBlock.displayName = 'CodeBlock';\nCodeBlock.defaultProps = {\n  language: 'tsx',\n  size: 'medium'\n};\n\nconst Ellipsis = forwardRef((_ref, ref) => {\n  let {\n    children,\n    title,\n    tag,\n    ...other\n  } = _ref;\n  let textContent = undefined;\n  if (title !== undefined) {\n    textContent = title;\n  } else if (typeof children === 'string') {\n    textContent = children;\n  }\n  return React.createElement(StyledEllipsis, _extends({\n    as: tag,\n    ref: ref,\n    title: textContent\n  }, other), children);\n});\nEllipsis.displayName = 'Ellipsis';\nEllipsis.propTypes = {\n  title: PropTypes.string,\n  tag: PropTypes.any\n};\nEllipsis.defaultProps = {\n  tag: 'div'\n};\n\nconst Paragraph = forwardRef((props, ref) => React.createElement(StyledParagraph, _extends({\n  ref: ref\n}, props)));\nParagraph.displayName = 'Paragraph';\nParagraph.propTypes = {\n  size: PropTypes.oneOf(SIZE)\n};\nParagraph.defaultProps = {\n  size: 'medium'\n};\n\nconst OrderedListContext = createContext(undefined);\nconst useOrderedListContext = () => {\n  const listContext = useContext(OrderedListContext);\n  if (!listContext) {\n    throw new Error('This component must be rendered within an `OrderedList` component.');\n  }\n  return listContext;\n};\n\nconst OrderedListItem = forwardRef((props, ref) => {\n  const {\n    size\n  } = useOrderedListContext();\n  return React.createElement(StyledOrderedListItem, _extends({\n    ref: ref,\n    space: size\n  }, props));\n});\nOrderedListItem.displayName = 'OrderedList.Item';\nconst Item$1 = OrderedListItem;\n\nconst OrderedListComponent = React.forwardRef((_ref, ref) => {\n  let {\n    size,\n    type,\n    ...other\n  } = _ref;\n  const value = useMemo(() => ({\n    size: size\n  }), [size]);\n  return React.createElement(OrderedListContext.Provider, {\n    value: value\n  }, React.createElement(StyledOrderedList, _extends({\n    ref: ref,\n    listType: type\n  }, other)));\n});\nOrderedListComponent.displayName = 'OrderedList';\nOrderedListComponent.propTypes = {\n  size: PropTypes.oneOf(SIZE),\n  type: PropTypes.oneOf(TYPE_ORDERED_LIST)\n};\nOrderedListComponent.defaultProps = {\n  size: 'medium',\n  type: 'decimal'\n};\nconst OrderedList = OrderedListComponent;\nOrderedList.Item = Item$1;\n\nconst UnorderedListContext = createContext(undefined);\nconst useUnorderedListContext = () => {\n  const listContext = useContext(UnorderedListContext);\n  if (!listContext) {\n    throw new Error('This component must be rendered within an `UnorderedList` component.');\n  }\n  return listContext;\n};\n\nconst UnorderedListItem = forwardRef((props, ref) => {\n  const {\n    size\n  } = useUnorderedListContext();\n  return React.createElement(StyledUnorderedListItem, _extends({\n    ref: ref,\n    space: size\n  }, props));\n});\nUnorderedListItem.displayName = 'UnorderedList.Item';\nconst Item = UnorderedListItem;\n\nconst UnorderedListComponent = forwardRef((_ref, ref) => {\n  let {\n    size,\n    type,\n    ...other\n  } = _ref;\n  const value = useMemo(() => ({\n    size: size\n  }), [size]);\n  return React.createElement(UnorderedListContext.Provider, {\n    value: value\n  }, React.createElement(StyledUnorderedList, _extends({\n    ref: ref,\n    listType: type\n  }, other)));\n});\nUnorderedListComponent.displayName = 'UnorderedList';\nUnorderedListComponent.propTypes = {\n  size: PropTypes.oneOf(SIZE),\n  type: PropTypes.oneOf(TYPE_UNORDERED_LIST)\n};\nUnorderedListComponent.defaultProps = {\n  size: 'medium',\n  type: 'disc'\n};\nconst UnorderedList = UnorderedListComponent;\nUnorderedList.Item = Item;\n\nconst StartIconComponent = props => React.createElement(StyledIcon, _extends({\n  isStart: true\n}, props));\nStartIconComponent.displayName = 'Span.StartIcon';\nconst StartIcon = StartIconComponent;\n\nconst IconComponent = props => React.createElement(StyledIcon, props);\nIconComponent.displayName = 'Span.Icon';\nconst Icon = IconComponent;\n\nconst SpanComponent = forwardRef((_ref, ref) => {\n  let {\n    tag,\n    ...other\n  } = _ref;\n  return React.createElement(StyledFont, _extends({\n    as: tag,\n    ref: ref,\n    size: \"inherit\"\n  }, other));\n});\nSpanComponent.displayName = 'Span';\nSpanComponent.propTypes = {\n  tag: PropTypes.any,\n  isBold: PropTypes.bool,\n  isMonospace: PropTypes.bool,\n  hue: PropTypes.string\n};\nSpanComponent.defaultProps = {\n  tag: 'span'\n};\nconst Span = SpanComponent;\nSpan.Icon = Icon;\nSpan.StartIcon = StartIcon;\n\nexport { Blockquote, Code, CodeBlock, Ellipsis, LG, MD, OrderedList, Paragraph, SM, Span, UnorderedList, XL, XXL, XXXL };\n", "/**\n * Prism: Lightweight, robust, elegant syntax highlighting\n *\n * @license MIT <https://opensource.org/licenses/MIT>\n * <AUTHOR> <https://lea.verou.me>\n * @namespace\n * @public\n */\n/**\n * prism-react-renderer:\n * This file has been modified to remove:\n * - globals and window dependency\n * - worker support\n * - highlightAll and other element dependent methods\n * - _.hooks helpers\n * - UMD/node-specific hacks\n * It has also been run through prettier\n */\n\n var Prism = (function () {\n\n\t// Private helper vars\n\tvar lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n\tvar uniqueId = 0;\n\n\t// The grammar object for plaintext\n\tvar plainTextGrammar = {};\n\n\n\tvar _ = {\n\t\t/**\n\t\t * A namespace for utility methods.\n\t\t *\n\t\t * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n\t\t * change or disappear at any time.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t */\n\t\tutil: {\n\t\t\tencode: function encode(tokens) {\n\t\t\t\tif (tokens instanceof Token) {\n\t\t\t\t\treturn new Token(tokens.type, encode(tokens.content), tokens.alias);\n\t\t\t\t} else if (Array.isArray(tokens)) {\n\t\t\t\t\treturn tokens.map(encode);\n\t\t\t\t} else {\n\t\t\t\t\treturn tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the name of the type of the given value.\n\t\t\t *\n\t\t\t * @param {any} o\n\t\t\t * @returns {string}\n\t\t\t * @example\n\t\t\t * type(null)      === 'Null'\n\t\t\t * type(undefined) === 'Undefined'\n\t\t\t * type(123)       === 'Number'\n\t\t\t * type('foo')     === 'String'\n\t\t\t * type(true)      === 'Boolean'\n\t\t\t * type([1, 2])    === 'Array'\n\t\t\t * type({})        === 'Object'\n\t\t\t * type(String)    === 'Function'\n\t\t\t * type(/abc+/)    === 'RegExp'\n\t\t\t */\n\t\t\ttype: function (o) {\n\t\t\t\treturn Object.prototype.toString.call(o).slice(8, -1);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns a unique number for the given object. Later calls will still return the same number.\n\t\t\t *\n\t\t\t * @param {Object} obj\n\t\t\t * @returns {number}\n\t\t\t */\n\t\t\tobjId: function (obj) {\n\t\t\t\tif (!obj['__id']) {\n\t\t\t\t\tObject.defineProperty(obj, '__id', { value: ++uniqueId });\n\t\t\t\t}\n\t\t\t\treturn obj['__id'];\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Creates a deep clone of the given object.\n\t\t\t *\n\t\t\t * The main intended use of this function is to clone language definitions.\n\t\t\t *\n\t\t\t * @param {T} o\n\t\t\t * @param {Record<number, any>} [visited]\n\t\t\t * @returns {T}\n\t\t\t * @template T\n\t\t\t */\n\t\t\tclone: function deepClone(o, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar clone; var id;\n\t\t\t\tswitch (_.util.type(o)) {\n\t\t\t\t\tcase 'Object':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = /** @type {Record<string, any>} */ ({});\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\tfor (var key in o) {\n\t\t\t\t\t\t\tif (o.hasOwnProperty(key)) {\n\t\t\t\t\t\t\t\tclone[key] = deepClone(o[key], visited);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tcase 'Array':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = [];\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\t(/** @type {Array} */(/** @type {any} */(o))).forEach(function (v, i) {\n\t\t\t\t\t\t\tclone[i] = deepClone(v, visited);\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn o;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n\t\t\t *\n\t\t\t * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @returns {string}\n\t\t\t */\n\t\t\tgetLanguage: function (element) {\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar m = lang.exec(element.className);\n\t\t\t\t\tif (m) {\n\t\t\t\t\t\treturn m[1].toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn 'none';\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Sets the Prism `language-xxxx` class of the given element.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} language\n\t\t\t * @returns {void}\n\t\t\t */\n\t\t\tsetLanguage: function (element, language) {\n\t\t\t\t// remove all `language-xxxx` classes\n\t\t\t\t// (this might leave behind a leading space)\n\t\t\t\telement.className = element.className.replace(RegExp(lang, 'gi'), '');\n\n\t\t\t\t// add the new `language-xxxx` class\n\t\t\t\t// (using `classList` will automatically clean up spaces for us)\n\t\t\t\telement.classList.add('language-' + language);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns whether a given class is active for `element`.\n\t\t\t *\n\t\t\t * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n\t\t\t * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n\t\t\t * given class is just the given class with a `no-` prefix.\n\t\t\t *\n\t\t\t * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n\t\t\t * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n\t\t\t * ancestors have the given class or the negated version of it, then the default activation will be returned.\n\t\t\t *\n\t\t\t * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n\t\t\t * version of it, the class is considered active.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} className\n\t\t\t * @param {boolean} [defaultActivation=false]\n\t\t\t * @returns {boolean}\n\t\t\t */\n\t\t\tisActive: function (element, className, defaultActivation) {\n\t\t\t\tvar no = 'no-' + className;\n\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar classList = element.classList;\n\t\t\t\t\tif (classList.contains(className)) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tif (classList.contains(no)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn !!defaultActivation;\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tlanguages: {\n\t\t\t/**\n\t\t\t * The grammar for plain, unformatted text.\n\t\t\t */\n\t\t\tplain: plainTextGrammar,\n\t\t\tplaintext: plainTextGrammar,\n\t\t\ttext: plainTextGrammar,\n\t\t\ttxt: plainTextGrammar,\n\n\t\t\t/**\n\t\t\t * Creates a deep copy of the language with the given id and appends the given tokens.\n\t\t\t *\n\t\t\t * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n\t\t\t * will be overwritten at its original position.\n\t\t\t *\n\t\t\t * ## Best practices\n\t\t\t *\n\t\t\t * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n\t\t\t * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n\t\t\t * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n\t\t\t *\n\t\t\t * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n\t\t\t * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n\t\t\t *\n\t\t\t * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n\t\t\t * @param {Grammar} redef The new tokens to append.\n\t\t\t * @returns {Grammar} The new language created.\n\t\t\t * @public\n\t\t\t * @example\n\t\t\t * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n\t\t\t *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n\t\t\t *     // at its original position\n\t\t\t *     'comment': { ... },\n\t\t\t *     // CSS doesn't have a 'color' token, so this token will be appended\n\t\t\t *     'color': /\\b(?:red|green|blue)\\b/\n\t\t\t * });\n\t\t\t */\n\t\t\textend: function (id, redef) {\n\t\t\t\tvar lang = _.util.clone(_.languages[id]);\n\n\t\t\t\tfor (var key in redef) {\n\t\t\t\t\tlang[key] = redef[key];\n\t\t\t\t}\n\n\t\t\t\treturn lang;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Inserts tokens _before_ another token in a language definition or any other grammar.\n\t\t\t *\n\t\t\t * ## Usage\n\t\t\t *\n\t\t\t * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n\t\t\t * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n\t\t\t * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n\t\t\t * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n\t\t\t * this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.markup.style = {\n\t\t\t *     // token\n\t\t\t * };\n\t\t\t * ```\n\t\t\t *\n\t\t\t * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n\t\t\t * before existing tokens. For the CSS example above, you would use it like this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'cdata', {\n\t\t\t *     'style': {\n\t\t\t *         // token\n\t\t\t *     }\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Special cases\n\t\t\t *\n\t\t\t * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n\t\t\t * will be ignored.\n\t\t\t *\n\t\t\t * This behavior can be used to insert tokens after `before`:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'comment', {\n\t\t\t *     'comment': Prism.languages.markup.comment,\n\t\t\t *     // tokens after 'comment'\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Limitations\n\t\t\t *\n\t\t\t * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n\t\t\t * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n\t\t\t * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n\t\t\t * deleting properties which is necessary to insert at arbitrary positions.\n\t\t\t *\n\t\t\t * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n\t\t\t * Instead, it will create a new object and replace all references to the target object with the new one. This\n\t\t\t * can be done without temporarily deleting properties, so the iteration order is well-defined.\n\t\t\t *\n\t\t\t * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n\t\t\t * you hold the target object in a variable, then the value of the variable will not change.\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * var oldMarkup = Prism.languages.markup;\n\t\t\t * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n\t\t\t *\n\t\t\t * assert(oldMarkup !== Prism.languages.markup);\n\t\t\t * assert(newMarkup === Prism.languages.markup);\n\t\t\t * ```\n\t\t\t *\n\t\t\t * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n\t\t\t * object to be modified.\n\t\t\t * @param {string} before The key to insert before.\n\t\t\t * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n\t\t\t * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n\t\t\t * object to be modified.\n\t\t\t *\n\t\t\t * Defaults to `Prism.languages`.\n\t\t\t * @returns {Grammar} The new grammar object.\n\t\t\t * @public\n\t\t\t */\n\t\t\tinsertBefore: function (inside, before, insert, root) {\n\t\t\t\troot = root || /** @type {any} */ (_.languages);\n\t\t\t\tvar grammar = root[inside];\n\t\t\t\t/** @type {Grammar} */\n\t\t\t\tvar ret = {};\n\n\t\t\t\tfor (var token in grammar) {\n\t\t\t\t\tif (grammar.hasOwnProperty(token)) {\n\n\t\t\t\t\t\tif (token == before) {\n\t\t\t\t\t\t\tfor (var newToken in insert) {\n\t\t\t\t\t\t\t\tif (insert.hasOwnProperty(newToken)) {\n\t\t\t\t\t\t\t\t\tret[newToken] = insert[newToken];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Do not insert token which also occur in insert. See #1525\n\t\t\t\t\t\tif (!insert.hasOwnProperty(token)) {\n\t\t\t\t\t\t\tret[token] = grammar[token];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar old = root[inside];\n\t\t\t\troot[inside] = ret;\n\n\t\t\t\t// Update references in other language definitions\n\t\t\t\t_.languages.DFS(_.languages, function (key, value) {\n\t\t\t\t\tif (value === old && key != inside) {\n\t\t\t\t\t\tthis[key] = ret;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn ret;\n\t\t\t},\n\n\t\t\t// Traverse a language definition with Depth First Search\n\t\t\tDFS: function DFS(o, callback, type, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar objId = _.util.objId;\n\n\t\t\t\tfor (var i in o) {\n\t\t\t\t\tif (o.hasOwnProperty(i)) {\n\t\t\t\t\t\tcallback.call(o, i, o[i], type || i);\n\n\t\t\t\t\t\tvar property = o[i];\n\t\t\t\t\t\tvar propertyType = _.util.type(property);\n\n\t\t\t\t\t\tif (propertyType === 'Object' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, null, visited);\n\t\t\t\t\t\t} else if (propertyType === 'Array' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, i, visited);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tplugins: {},\n\n\n\t\t/**\n\t\t * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns a string with the HTML produced.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-tokenize`\n\t\t * 2. `after-tokenize`\n\t\t * 3. `wrap`: On each {@link Token}.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @param {string} language The name of the language definition passed to `grammar`.\n\t\t * @returns {string} The highlighted HTML.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n\t\t */\n\t\thighlight: function (text, grammar, language) {\n\t\t\tvar env = {\n\t\t\t\tcode: text,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tlanguage: language\n\t\t\t};\n\t\t\t_.hooks.run('before-tokenize', env);\n\t\t\tenv.tokens = _.tokenize(env.code, env.grammar);\n\t\t\t_.hooks.run('after-tokenize', env);\n\t\t\treturn Token.stringify(_.util.encode(env.tokens), env.language);\n\t\t},\n\n\t\t/**\n\t\t * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns an array with the tokenized code.\n\t\t *\n\t\t * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n\t\t *\n\t\t * This method could be useful in other contexts as well, as a very crude parser.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @returns {TokenStream} An array of strings and tokens, a token stream.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * let code = `var foo = 0;`;\n\t\t * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n\t\t * tokens.forEach(token => {\n\t\t *     if (token instanceof Prism.Token && token.type === 'number') {\n\t\t *         console.log(`Found numeric literal: ${token.content}`);\n\t\t *     }\n\t\t * });\n\t\t */\n\t\ttokenize: function (text, grammar) {\n\t\t\tvar rest = grammar.rest;\n\t\t\tif (rest) {\n\t\t\t\tfor (var token in rest) {\n\t\t\t\t\tgrammar[token] = rest[token];\n\t\t\t\t}\n\n\t\t\t\tdelete grammar.rest;\n\t\t\t}\n\n\t\t\tvar tokenList = new LinkedList();\n\t\t\taddAfter(tokenList, tokenList.head, text);\n\n\t\t\tmatchGrammar(text, tokenList, grammar, tokenList.head, 0);\n\n\t\t\treturn toArray(tokenList);\n\t\t},\n\n\t\t/**\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thooks: {\n\t\t\tall: {},\n\n\t\t\t/**\n\t\t\t * Adds the given callback to the list of callbacks for the given hook.\n\t\t\t *\n\t\t\t * The callback will be invoked when the hook it is registered for is run.\n\t\t\t * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n\t\t\t *\n\t\t\t * One callback function can be registered to multiple hooks and the same hook multiple times.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {HookCallback} callback The callback function which is given environment variables.\n\t\t\t * @public\n\t\t\t */\n\t\t\tadd: function (name, callback) {\n\t\t\t\tvar hooks = _.hooks.all;\n\n\t\t\t\thooks[name] = hooks[name] || [];\n\n\t\t\t\thooks[name].push(callback);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Runs a hook invoking all registered callbacks with the given environment variables.\n\t\t\t *\n\t\t\t * Callbacks will be invoked synchronously and in the order in which they were registered.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n\t\t\t * @public\n\t\t\t */\n\t\t\trun: function (name, env) {\n\t\t\t\tvar callbacks = _.hooks.all[name];\n\n\t\t\t\tif (!callbacks || !callbacks.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tfor (var i = 0, callback; (callback = callbacks[i++]);) {\n\t\t\t\t\tcallback(env);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tToken: Token\n\t};\n\n\n\t// Typescript note:\n\t// The following can be used to import the Token type in JSDoc:\n\t//\n\t//   @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n\n\t/**\n\t * Creates a new token.\n\t *\n\t * @param {string} type See {@link Token#type type}\n\t * @param {string | TokenStream} content See {@link Token#content content}\n\t * @param {string|string[]} [alias] The alias(es) of the token.\n\t * @param {string} [matchedStr=\"\"] A copy of the full string this token was created from.\n\t * @class\n\t * @global\n\t * @public\n\t */\n\tfunction Token(type, content, alias, matchedStr) {\n\t\t/**\n\t\t * The type of the token.\n\t\t *\n\t\t * This is usually the key of a pattern in a {@link Grammar}.\n\t\t *\n\t\t * @type {string}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.type = type;\n\t\t/**\n\t\t * The strings or tokens contained by this token.\n\t\t *\n\t\t * This will be a token stream if the pattern matched also defined an `inside` grammar.\n\t\t *\n\t\t * @type {string | TokenStream}\n\t\t * @public\n\t\t */\n\t\tthis.content = content;\n\t\t/**\n\t\t * The alias(es) of the token.\n\t\t *\n\t\t * @type {string|string[]}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.alias = alias;\n\t\t// Copy of the full string this token was created from\n\t\tthis.length = (matchedStr || '').length | 0;\n\t}\n\n\t/**\n\t * A token stream is an array of strings and {@link Token Token} objects.\n\t *\n\t * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process\n\t * them.\n\t *\n\t * 1. No adjacent strings.\n\t * 2. No empty strings.\n\t *\n\t *    The only exception here is the token stream that only contains the empty string and nothing else.\n\t *\n\t * @typedef {Array<string | Token>} TokenStream\n\t * @global\n\t * @public\n\t */\n\n\t/**\n\t * Converts the given token or token stream to an HTML representation.\n\t *\n\t * The following hooks will be run:\n\t * 1. `wrap`: On each {@link Token}.\n\t *\n\t * @param {string | Token | TokenStream} o The token or token stream to be converted.\n\t * @param {string} language The name of current language.\n\t * @returns {string} The HTML representation of the token or token stream.\n\t * @memberof Token\n\t * @static\n\t */\n\tToken.stringify = function stringify(o, language) {\n\t\tif (typeof o == 'string') {\n\t\t\treturn o;\n\t\t}\n\t\tif (Array.isArray(o)) {\n\t\t\tvar s = '';\n\t\t\to.forEach(function (e) {\n\t\t\t\ts += stringify(e, language);\n\t\t\t});\n\t\t\treturn s;\n\t\t}\n\n\t\tvar env = {\n\t\t\ttype: o.type,\n\t\t\tcontent: stringify(o.content, language),\n\t\t\ttag: 'span',\n\t\t\tclasses: ['token', o.type],\n\t\t\tattributes: {},\n\t\t\tlanguage: language\n\t\t};\n\n\t\tvar aliases = o.alias;\n\t\tif (aliases) {\n\t\t\tif (Array.isArray(aliases)) {\n\t\t\t\tArray.prototype.push.apply(env.classes, aliases);\n\t\t\t} else {\n\t\t\t\tenv.classes.push(aliases);\n\t\t\t}\n\t\t}\n\n\t\t_.hooks.run('wrap', env);\n\n\t\tvar attributes = '';\n\t\tfor (var name in env.attributes) {\n\t\t\tattributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n\t\t}\n\n\t\treturn '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n\t};\n\n\t/**\n\t * @param {RegExp} pattern\n\t * @param {number} pos\n\t * @param {string} text\n\t * @param {boolean} lookbehind\n\t * @returns {RegExpExecArray | null}\n\t */\n\tfunction matchPattern(pattern, pos, text, lookbehind) {\n\t\tpattern.lastIndex = pos;\n\t\tvar match = pattern.exec(text);\n\t\tif (match && lookbehind && match[1]) {\n\t\t\t// change the match to remove the text matched by the Prism lookbehind group\n\t\t\tvar lookbehindLength = match[1].length;\n\t\t\tmatch.index += lookbehindLength;\n\t\t\tmatch[0] = match[0].slice(lookbehindLength);\n\t\t}\n\t\treturn match;\n\t}\n\n\t/**\n\t * @param {string} text\n\t * @param {LinkedList<string | Token>} tokenList\n\t * @param {any} grammar\n\t * @param {LinkedListNode<string | Token>} startNode\n\t * @param {number} startPos\n\t * @param {RematchOptions} [rematch]\n\t * @returns {void}\n\t * @private\n\t *\n\t * @typedef RematchOptions\n\t * @property {string} cause\n\t * @property {number} reach\n\t */\n\tfunction matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n\t\tfor (var token in grammar) {\n\t\t\tif (!grammar.hasOwnProperty(token) || !grammar[token]) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tvar patterns = grammar[token];\n\t\t\tpatterns = Array.isArray(patterns) ? patterns : [patterns];\n\n\t\t\tfor (var j = 0; j < patterns.length; ++j) {\n\t\t\t\tif (rematch && rematch.cause == token + ',' + j) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar patternObj = patterns[j];\n\t\t\t\tvar inside = patternObj.inside;\n\t\t\t\tvar lookbehind = !!patternObj.lookbehind;\n\t\t\t\tvar greedy = !!patternObj.greedy;\n\t\t\t\tvar alias = patternObj.alias;\n\n\t\t\t\tif (greedy && !patternObj.pattern.global) {\n\t\t\t\t\t// Without the global flag, lastIndex won't work\n\t\t\t\t\tvar flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n\t\t\t\t\tpatternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n\t\t\t\t}\n\n\t\t\t\t/** @type {RegExp} */\n\t\t\t\tvar pattern = patternObj.pattern || patternObj;\n\n\t\t\t\tfor ( // iterate the token list and keep track of the current token/string position\n\t\t\t\t\tvar currentNode = startNode.next, pos = startPos;\n\t\t\t\t\tcurrentNode !== tokenList.tail;\n\t\t\t\t\tpos += currentNode.value.length, currentNode = currentNode.next\n\t\t\t\t) {\n\n\t\t\t\t\tif (rematch && pos >= rematch.reach) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar str = currentNode.value;\n\n\t\t\t\t\tif (tokenList.length > text.length) {\n\t\t\t\t\t\t// Something went terribly wrong, ABORT, ABORT!\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (str instanceof Token) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeCount = 1; // this is the to parameter of removeBetween\n\t\t\t\t\tvar match;\n\n\t\t\t\t\tif (greedy) {\n\t\t\t\t\t\tmatch = matchPattern(pattern, pos, text, lookbehind);\n\t\t\t\t\t\tif (!match || match.index >= text.length) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar from = match.index;\n\t\t\t\t\t\tvar to = match.index + match[0].length;\n\t\t\t\t\t\tvar p = pos;\n\n\t\t\t\t\t\t// find the node that contains the match\n\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\twhile (from >= p) {\n\t\t\t\t\t\t\tcurrentNode = currentNode.next;\n\t\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// adjust pos (and p)\n\t\t\t\t\t\tp -= currentNode.value.length;\n\t\t\t\t\t\tpos = p;\n\n\t\t\t\t\t\t// the current node is a Token, then the match starts inside another Token, which is invalid\n\t\t\t\t\t\tif (currentNode.value instanceof Token) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// find the last node which is affected by this match\n\t\t\t\t\t\tfor (\n\t\t\t\t\t\t\tvar k = currentNode;\n\t\t\t\t\t\t\tk !== tokenList.tail && (p < to || typeof k.value === 'string');\n\t\t\t\t\t\t\tk = k.next\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tremoveCount++;\n\t\t\t\t\t\t\tp += k.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tremoveCount--;\n\n\t\t\t\t\t\t// replace with the new match\n\t\t\t\t\t\tstr = text.slice(pos, p);\n\t\t\t\t\t\tmatch.index -= pos;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmatch = matchPattern(pattern, 0, str, lookbehind);\n\t\t\t\t\t\tif (!match) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// eslint-disable-next-line no-redeclare\n\t\t\t\t\tvar from = match.index;\n\t\t\t\t\tvar matchStr = match[0];\n\t\t\t\t\tvar before = str.slice(0, from);\n\t\t\t\t\tvar after = str.slice(from + matchStr.length);\n\n\t\t\t\t\tvar reach = pos + str.length;\n\t\t\t\t\tif (rematch && reach > rematch.reach) {\n\t\t\t\t\t\trematch.reach = reach;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeFrom = currentNode.prev;\n\n\t\t\t\t\tif (before) {\n\t\t\t\t\t\tremoveFrom = addAfter(tokenList, removeFrom, before);\n\t\t\t\t\t\tpos += before.length;\n\t\t\t\t\t}\n\n\t\t\t\t\tremoveRange(tokenList, removeFrom, removeCount);\n\n\t\t\t\t\tvar wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n\t\t\t\t\tcurrentNode = addAfter(tokenList, removeFrom, wrapped);\n\n\t\t\t\t\tif (after) {\n\t\t\t\t\t\taddAfter(tokenList, currentNode, after);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (removeCount > 1) {\n\t\t\t\t\t\t// at least one Token object was removed, so we have to do some rematching\n\t\t\t\t\t\t// this can only happen if the current pattern is greedy\n\n\t\t\t\t\t\t/** @type {RematchOptions} */\n\t\t\t\t\t\tvar nestedRematch = {\n\t\t\t\t\t\t\tcause: token + ',' + j,\n\t\t\t\t\t\t\treach: reach\n\t\t\t\t\t\t};\n\t\t\t\t\t\tmatchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n\n\t\t\t\t\t\t// the reach might have been extended because of the rematching\n\t\t\t\t\t\tif (rematch && nestedRematch.reach > rematch.reach) {\n\t\t\t\t\t\t\trematch.reach = nestedRematch.reach;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @typedef LinkedListNode\n\t * @property {T} value\n\t * @property {LinkedListNode<T> | null} prev The previous node.\n\t * @property {LinkedListNode<T> | null} next The next node.\n\t * @template T\n\t * @private\n\t */\n\n\t/**\n\t * @template T\n\t * @private\n\t */\n\tfunction LinkedList() {\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar head = { value: null, prev: null, next: null };\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar tail = { value: null, prev: head, next: null };\n\t\thead.next = tail;\n\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.head = head;\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.tail = tail;\n\t\tthis.length = 0;\n\t}\n\n\t/**\n\t * Adds a new node with the given value to the list.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {T} value\n\t * @returns {LinkedListNode<T>} The added node.\n\t * @template T\n\t */\n\tfunction addAfter(list, node, value) {\n\t\t// assumes that node != list.tail && values.length >= 0\n\t\tvar next = node.next;\n\n\t\tvar newNode = { value: value, prev: node, next: next };\n\t\tnode.next = newNode;\n\t\tnext.prev = newNode;\n\t\tlist.length++;\n\n\t\treturn newNode;\n\t}\n\t/**\n\t * Removes `count` nodes after the given node. The given node will not be removed.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {number} count\n\t * @template T\n\t */\n\tfunction removeRange(list, node, count) {\n\t\tvar next = node.next;\n\t\tfor (var i = 0; i < count && next !== list.tail; i++) {\n\t\t\tnext = next.next;\n\t\t}\n\t\tnode.next = next;\n\t\tnext.prev = node;\n\t\tlist.length -= i;\n\t}\n\t/**\n\t * @param {LinkedList<T>} list\n\t * @returns {T[]}\n\t * @template T\n\t */\n\tfunction toArray(list) {\n\t\tvar array = [];\n\t\tvar node = list.head.next;\n\t\twhile (node !== list.tail) {\n\t\t\tarray.push(node.value);\n\t\t\tnode = node.next;\n\t\t}\n\t\treturn array;\n\t}\n\n\treturn _;\n\n}());\n\nvar prism = Prism;\nPrism.default = Prism;\n\n/* This content is auto-generated to include some prismjs language components: */\n\n/* \"prismjs/components/prism-markup\" */\n\nprism.languages.markup = {\n  'comment': {\n    pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n    greedy: true\n  },\n  'prolog': {\n    pattern: /<\\?[\\s\\S]+?\\?>/,\n    greedy: true\n  },\n  'doctype': {\n    // https://www.w3.org/TR/xml/#NT-doctypedecl\n    pattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n    greedy: true,\n    inside: {\n      'internal-subset': {\n        pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n        lookbehind: true,\n        greedy: true,\n        inside: null // see below\n\n      },\n      'string': {\n        pattern: /\"[^\"]*\"|'[^']*'/,\n        greedy: true\n      },\n      'punctuation': /^<!|>$|[[\\]]/,\n      'doctype-tag': /^DOCTYPE/i,\n      'name': /[^\\s<>'\"]+/\n    }\n  },\n  'cdata': {\n    pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n    greedy: true\n  },\n  'tag': {\n    pattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n    greedy: true,\n    inside: {\n      'tag': {\n        pattern: /^<\\/?[^\\s>\\/]+/,\n        inside: {\n          'punctuation': /^<\\/?/,\n          'namespace': /^[^\\s>\\/:]+:/\n        }\n      },\n      'special-attr': [],\n      'attr-value': {\n        pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n        inside: {\n          'punctuation': [{\n            pattern: /^=/,\n            alias: 'attr-equals'\n          }, /\"|'/]\n        }\n      },\n      'punctuation': /\\/?>/,\n      'attr-name': {\n        pattern: /[^\\s>\\/]+/,\n        inside: {\n          'namespace': /^[^\\s>\\/:]+:/\n        }\n      }\n    }\n  },\n  'entity': [{\n    pattern: /&[\\da-z]{1,8};/i,\n    alias: 'named-entity'\n  }, /&#x?[\\da-f]{1,8};/i]\n};\nprism.languages.markup['tag'].inside['attr-value'].inside['entity'] = prism.languages.markup['entity'];\nprism.languages.markup['doctype'].inside['internal-subset'].inside = prism.languages.markup; // Plugin to make entity title show the real entity, idea by Roman Komarov\n\nprism.hooks.add('wrap', function (env) {\n  if (env.type === 'entity') {\n    env.attributes['title'] = env.content.replace(/&amp;/, '&');\n  }\n});\nObject.defineProperty(prism.languages.markup.tag, 'addInlined', {\n  /**\n   * Adds an inlined language to markup.\n   *\n   * An example of an inlined language is CSS with `<style>` tags.\n   *\n   * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n   * case insensitive.\n   * @param {string} lang The language key.\n   * @example\n   * addInlined('style', 'css');\n   */\n  value: function addInlined(tagName, lang) {\n    var includedCdataInside = {};\n    includedCdataInside['language-' + lang] = {\n      pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n      lookbehind: true,\n      inside: prism.languages[lang]\n    };\n    includedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n    var inside = {\n      'included-cdata': {\n        pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n        inside: includedCdataInside\n      }\n    };\n    inside['language-' + lang] = {\n      pattern: /[\\s\\S]+/,\n      inside: prism.languages[lang]\n    };\n    var def = {};\n    def[tagName] = {\n      pattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () {\n        return tagName;\n      }), 'i'),\n      lookbehind: true,\n      greedy: true,\n      inside: inside\n    };\n    prism.languages.insertBefore('markup', 'cdata', def);\n  }\n});\nObject.defineProperty(prism.languages.markup.tag, 'addAttribute', {\n  /**\n   * Adds an pattern to highlight languages embedded in HTML attributes.\n   *\n   * An example of an inlined language is CSS with `style` attributes.\n   *\n   * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n   * case insensitive.\n   * @param {string} lang The language key.\n   * @example\n   * addAttribute('style', 'css');\n   */\n  value: function (attrName, lang) {\n    prism.languages.markup.tag.inside['special-attr'].push({\n      pattern: RegExp(/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source, 'i'),\n      lookbehind: true,\n      inside: {\n        'attr-name': /^[^\\s=]+/,\n        'attr-value': {\n          pattern: /=[\\s\\S]+/,\n          inside: {\n            'value': {\n              pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n              lookbehind: true,\n              alias: [lang, 'language-' + lang],\n              inside: prism.languages[lang]\n            },\n            'punctuation': [{\n              pattern: /^=/,\n              alias: 'attr-equals'\n            }, /\"|'/]\n          }\n        }\n      }\n    });\n  }\n});\nprism.languages.html = prism.languages.markup;\nprism.languages.mathml = prism.languages.markup;\nprism.languages.svg = prism.languages.markup;\nprism.languages.xml = prism.languages.extend('markup', {});\nprism.languages.ssml = prism.languages.xml;\nprism.languages.atom = prism.languages.xml;\nprism.languages.rss = prism.languages.xml;\n/* \"prismjs/components/prism-bash\" */\n\n(function (Prism) {\n  // $ set | grep '^[A-Z][^[:space:]]*=' | cut -d= -f1 | tr '\\n' '|'\n  // + LC_ALL, RANDOM, REPLY, SECONDS.\n  // + make sure PS1..4 are here as they are not always set,\n  // - some useless things.\n  var envVars = '\\\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\\\b';\n  var commandAfterHeredoc = {\n    pattern: /(^([\"']?)\\w+\\2)[ \\t]+\\S.*/,\n    lookbehind: true,\n    alias: 'punctuation',\n    // this looks reasonably well in all themes\n    inside: null // see below\n\n  };\n  var insideString = {\n    'bash': commandAfterHeredoc,\n    'environment': {\n      pattern: RegExp('\\\\$' + envVars),\n      alias: 'constant'\n    },\n    'variable': [// [0]: Arithmetic Environment\n    {\n      pattern: /\\$?\\(\\([\\s\\S]+?\\)\\)/,\n      greedy: true,\n      inside: {\n        // If there is a $ sign at the beginning highlight $(( and )) as variable\n        'variable': [{\n          pattern: /(^\\$\\(\\([\\s\\S]+)\\)\\)/,\n          lookbehind: true\n        }, /^\\$\\(\\(/],\n        'number': /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee]-?\\d+)?/,\n        // Operators according to https://www.gnu.org/software/bash/manual/bashref.html#Shell-Arithmetic\n        'operator': /--|\\+\\+|\\*\\*=?|<<=?|>>=?|&&|\\|\\||[=!+\\-*/%<>^&|]=?|[?~:]/,\n        // If there is no $ sign at the beginning highlight (( and )) as punctuation\n        'punctuation': /\\(\\(?|\\)\\)?|,|;/\n      }\n    }, // [1]: Command Substitution\n    {\n      pattern: /\\$\\((?:\\([^)]+\\)|[^()])+\\)|`[^`]+`/,\n      greedy: true,\n      inside: {\n        'variable': /^\\$\\(|^`|\\)$|`$/\n      }\n    }, // [2]: Brace expansion\n    {\n      pattern: /\\$\\{[^}]+\\}/,\n      greedy: true,\n      inside: {\n        'operator': /:[-=?+]?|[!\\/]|##?|%%?|\\^\\^?|,,?/,\n        'punctuation': /[\\[\\]]/,\n        'environment': {\n          pattern: RegExp('(\\\\{)' + envVars),\n          lookbehind: true,\n          alias: 'constant'\n        }\n      }\n    }, /\\$(?:\\w+|[#?*!@$])/],\n    // Escape sequences from echo and printf's manuals, and escaped quotes.\n    'entity': /\\\\(?:[abceEfnrtv\\\\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/\n  };\n  Prism.languages.bash = {\n    'shebang': {\n      pattern: /^#!\\s*\\/.*/,\n      alias: 'important'\n    },\n    'comment': {\n      pattern: /(^|[^\"{\\\\$])#.*/,\n      lookbehind: true\n    },\n    'function-name': [// a) function foo {\n    // b) foo() {\n    // c) function foo() {\n    // but not “foo {”\n    {\n      // a) and c)\n      pattern: /(\\bfunction\\s+)[\\w-]+(?=(?:\\s*\\(?:\\s*\\))?\\s*\\{)/,\n      lookbehind: true,\n      alias: 'function'\n    }, {\n      // b)\n      pattern: /\\b[\\w-]+(?=\\s*\\(\\s*\\)\\s*\\{)/,\n      alias: 'function'\n    }],\n    // Highlight variable names as variables in for and select beginnings.\n    'for-or-select': {\n      pattern: /(\\b(?:for|select)\\s+)\\w+(?=\\s+in\\s)/,\n      alias: 'variable',\n      lookbehind: true\n    },\n    // Highlight variable names as variables in the left-hand part\n    // of assignments (“=” and “+=”).\n    'assign-left': {\n      pattern: /(^|[\\s;|&]|[<>]\\()\\w+(?=\\+?=)/,\n      inside: {\n        'environment': {\n          pattern: RegExp('(^|[\\\\s;|&]|[<>]\\\\()' + envVars),\n          lookbehind: true,\n          alias: 'constant'\n        }\n      },\n      alias: 'variable',\n      lookbehind: true\n    },\n    'string': [// Support for Here-documents https://en.wikipedia.org/wiki/Here_document\n    {\n      pattern: /((?:^|[^<])<<-?\\s*)(\\w+)\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\2/,\n      lookbehind: true,\n      greedy: true,\n      inside: insideString\n    }, // Here-document with quotes around the tag\n    // → No expansion (so no “inside”).\n    {\n      pattern: /((?:^|[^<])<<-?\\s*)([\"'])(\\w+)\\2\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\3/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'bash': commandAfterHeredoc\n      }\n    }, // “Normal” string\n    {\n      // https://www.gnu.org/software/bash/manual/html_node/Double-Quotes.html\n      pattern: /(^|[^\\\\](?:\\\\\\\\)*)\"(?:\\\\[\\s\\S]|\\$\\([^)]+\\)|\\$(?!\\()|`[^`]+`|[^\"\\\\`$])*\"/,\n      lookbehind: true,\n      greedy: true,\n      inside: insideString\n    }, {\n      // https://www.gnu.org/software/bash/manual/html_node/Single-Quotes.html\n      pattern: /(^|[^$\\\\])'[^']*'/,\n      lookbehind: true,\n      greedy: true\n    }, {\n      // https://www.gnu.org/software/bash/manual/html_node/ANSI_002dC-Quoting.html\n      pattern: /\\$'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      greedy: true,\n      inside: {\n        'entity': insideString.entity\n      }\n    }],\n    'environment': {\n      pattern: RegExp('\\\\$?' + envVars),\n      alias: 'constant'\n    },\n    'variable': insideString.variable,\n    'function': {\n      pattern: /(^|[\\s;|&]|[<>]\\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\\s;|&])/,\n      lookbehind: true\n    },\n    'keyword': {\n      pattern: /(^|[\\s;|&]|[<>]\\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\\s;|&])/,\n      lookbehind: true\n    },\n    // https://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n    'builtin': {\n      pattern: /(^|[\\s;|&]|[<>]\\()(?:\\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\\s;|&])/,\n      lookbehind: true,\n      // Alias added to make those easier to distinguish from strings.\n      alias: 'class-name'\n    },\n    'boolean': {\n      pattern: /(^|[\\s;|&]|[<>]\\()(?:false|true)(?=$|[)\\s;|&])/,\n      lookbehind: true\n    },\n    'file-descriptor': {\n      pattern: /\\B&\\d\\b/,\n      alias: 'important'\n    },\n    'operator': {\n      // Lots of redirections here, but not just that.\n      pattern: /\\d?<>|>\\||\\+=|=[=~]?|!=?|<<[<-]?|[&\\d]?>>|\\d[<>]&?|[<>][&=]?|&[>&]?|\\|[&|]?/,\n      inside: {\n        'file-descriptor': {\n          pattern: /^\\d/,\n          alias: 'important'\n        }\n      }\n    },\n    'punctuation': /\\$?\\(\\(?|\\)\\)?|\\.\\.|[{}[\\];\\\\]/,\n    'number': {\n      pattern: /(^|\\s)(?:[1-9]\\d*|0)(?:[.,]\\d+)?\\b/,\n      lookbehind: true\n    }\n  };\n  commandAfterHeredoc.inside = Prism.languages.bash;\n  /* Patterns in command substitution. */\n\n  var toBeCopied = ['comment', 'function-name', 'for-or-select', 'assign-left', 'string', 'environment', 'function', 'keyword', 'builtin', 'boolean', 'file-descriptor', 'operator', 'punctuation', 'number'];\n  var inside = insideString.variable[1].inside;\n\n  for (var i = 0; i < toBeCopied.length; i++) {\n    inside[toBeCopied[i]] = Prism.languages.bash[toBeCopied[i]];\n  }\n\n  Prism.languages.shell = Prism.languages.bash;\n})(prism);\n/* \"prismjs/components/prism-clike\" */\n\n\nprism.languages.clike = {\n  'comment': [{\n    pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    lookbehind: true,\n    greedy: true\n  }, {\n    pattern: /(^|[^\\\\:])\\/\\/.*/,\n    lookbehind: true,\n    greedy: true\n  }],\n  'string': {\n    pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    greedy: true\n  },\n  'class-name': {\n    pattern: /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n    lookbehind: true,\n    inside: {\n      'punctuation': /[.\\\\]/\n    }\n  },\n  'keyword': /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,\n  'boolean': /\\b(?:false|true)\\b/,\n  'function': /\\b\\w+(?=\\()/,\n  'number': /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n  'operator': /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n  'punctuation': /[{}[\\];(),.:]/\n};\n/* \"prismjs/components/prism-c\" */\n\nprism.languages.c = prism.languages.extend('clike', {\n  'comment': {\n    pattern: /\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    greedy: true\n  },\n  'string': {\n    // https://en.cppreference.com/w/c/language/string_literal\n    pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n    greedy: true\n  },\n  'class-name': {\n    pattern: /(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/,\n    lookbehind: true\n  },\n  'keyword': /\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/,\n  'function': /\\b[a-z_]\\w*(?=\\s*\\()/i,\n  'number': /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i,\n  'operator': />>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/\n});\nprism.languages.insertBefore('c', 'string', {\n  'char': {\n    // https://en.cppreference.com/w/c/language/character_constant\n    pattern: /'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/,\n    greedy: true\n  }\n});\nprism.languages.insertBefore('c', 'string', {\n  'macro': {\n    // allow for multiline macro definitions\n    // spaces after the # character compile fine with gcc\n    pattern: /(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im,\n    lookbehind: true,\n    greedy: true,\n    alias: 'property',\n    inside: {\n      'string': [{\n        // highlight the path of the include statement as a string\n        pattern: /^(#\\s*include\\s*)<[^>]+>/,\n        lookbehind: true\n      }, prism.languages.c['string']],\n      'char': prism.languages.c['char'],\n      'comment': prism.languages.c['comment'],\n      'macro-name': [{\n        pattern: /(^#\\s*define\\s+)\\w+\\b(?!\\()/i,\n        lookbehind: true\n      }, {\n        pattern: /(^#\\s*define\\s+)\\w+\\b(?=\\()/i,\n        lookbehind: true,\n        alias: 'function'\n      }],\n      // highlight macro directives as keywords\n      'directive': {\n        pattern: /^(#\\s*)[a-z]+/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'directive-hash': /^#/,\n      'punctuation': /##|\\\\(?=[\\r\\n])/,\n      'expression': {\n        pattern: /\\S[\\s\\S]*/,\n        inside: prism.languages.c\n      }\n    }\n  }\n});\nprism.languages.insertBefore('c', 'function', {\n  // highlight predefined macros as constants\n  'constant': /\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/\n});\ndelete prism.languages.c['boolean'];\n/* \"prismjs/components/prism-cpp\" */\n\n(function (Prism) {\n  var keyword = /\\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\\b/;\n  var modName = /\\b(?!<keyword>)\\w+(?:\\s*\\.\\s*\\w+)*\\b/.source.replace(/<keyword>/g, function () {\n    return keyword.source;\n  });\n  Prism.languages.cpp = Prism.languages.extend('c', {\n    'class-name': [{\n      pattern: RegExp(/(\\b(?:class|concept|enum|struct|typename)\\s+)(?!<keyword>)\\w+/.source.replace(/<keyword>/g, function () {\n        return keyword.source;\n      })),\n      lookbehind: true\n    }, // This is intended to capture the class name of method implementations like:\n    //   void foo::bar() const {}\n    // However! The `foo` in the above example could also be a namespace, so we only capture the class name if\n    // it starts with an uppercase letter. This approximation should give decent results.\n    /\\b[A-Z]\\w*(?=\\s*::\\s*\\w+\\s*\\()/, // This will capture the class name before destructors like:\n    //   Foo::~Foo() {}\n    /\\b[A-Z_]\\w*(?=\\s*::\\s*~\\w+\\s*\\()/i, // This also intends to capture the class name of method implementations but here the class has template\n    // parameters, so it can't be a namespace (until C++ adds generic namespaces).\n    /\\b\\w+(?=\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\\s*::\\s*\\w+\\s*\\()/],\n    'keyword': keyword,\n    'number': {\n      pattern: /(?:\\b0b[01']+|\\b0x(?:[\\da-f']+(?:\\.[\\da-f']*)?|\\.[\\da-f']+)(?:p[+-]?[\\d']+)?|(?:\\b[\\d']+(?:\\.[\\d']*)?|\\B\\.[\\d']+)(?:e[+-]?[\\d']+)?)[ful]{0,4}/i,\n      greedy: true\n    },\n    'operator': />>=?|<<=?|->|--|\\+\\+|&&|\\|\\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\\b/,\n    'boolean': /\\b(?:false|true)\\b/\n  });\n  Prism.languages.insertBefore('cpp', 'string', {\n    'module': {\n      // https://en.cppreference.com/w/cpp/language/modules\n      pattern: RegExp(/(\\b(?:import|module)\\s+)/.source + '(?:' + // header-name\n      /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|<[^<>\\r\\n]*>/.source + '|' + // module name or partition or both\n      /<mod-name>(?:\\s*:\\s*<mod-name>)?|:\\s*<mod-name>/.source.replace(/<mod-name>/g, function () {\n        return modName;\n      }) + ')'),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'string': /^[<\"][\\s\\S]+/,\n        'operator': /:/,\n        'punctuation': /\\./\n      }\n    },\n    'raw-string': {\n      pattern: /R\"([^()\\\\ ]{0,16})\\([\\s\\S]*?\\)\\1\"/,\n      alias: 'string',\n      greedy: true\n    }\n  });\n  Prism.languages.insertBefore('cpp', 'keyword', {\n    'generic-function': {\n      pattern: /\\b(?!operator\\b)[a-z_]\\w*\\s*<(?:[^<>]|<[^<>]*>)*>(?=\\s*\\()/i,\n      inside: {\n        'function': /^\\w+/,\n        'generic': {\n          pattern: /<[\\s\\S]+/,\n          alias: 'class-name',\n          inside: Prism.languages.cpp\n        }\n      }\n    }\n  });\n  Prism.languages.insertBefore('cpp', 'operator', {\n    'double-colon': {\n      pattern: /::/,\n      alias: 'punctuation'\n    }\n  });\n  Prism.languages.insertBefore('cpp', 'class-name', {\n    // the base clause is an optional list of parent classes\n    // https://en.cppreference.com/w/cpp/language/class\n    'base-clause': {\n      pattern: /(\\b(?:class|struct)\\s+\\w+\\s*:\\s*)[^;{}\"'\\s]+(?:\\s+[^;{}\"'\\s]+)*(?=\\s*[;{])/,\n      lookbehind: true,\n      greedy: true,\n      inside: Prism.languages.extend('cpp', {})\n    }\n  });\n  Prism.languages.insertBefore('inside', 'double-colon', {\n    // All untokenized words that are not namespaces should be class names\n    'class-name': /\\b[a-z_]\\w*\\b(?!\\s*::)/i\n  }, Prism.languages.cpp['base-clause']);\n})(prism);\n/* \"prismjs/components/prism-css\" */\n\n\n(function (Prism) {\n  var string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n  Prism.languages.css = {\n    'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n    'atrule': {\n      pattern: /@[\\w-](?:[^;{\\s]|\\s+(?![\\s{]))*(?:;|(?=\\s*\\{))/,\n      inside: {\n        'rule': /^@[\\w-]+/,\n        'selector-function-argument': {\n          pattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n          lookbehind: true,\n          alias: 'selector'\n        },\n        'keyword': {\n          pattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n          lookbehind: true\n        } // See rest below\n\n      }\n    },\n    'url': {\n      // https://drafts.csswg.org/css-values-3/#urls\n      pattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n      greedy: true,\n      inside: {\n        'function': /^url/i,\n        'punctuation': /^\\(|\\)$/,\n        'string': {\n          pattern: RegExp('^' + string.source + '$'),\n          alias: 'url'\n        }\n      }\n    },\n    'selector': {\n      pattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n      lookbehind: true\n    },\n    'string': {\n      pattern: string,\n      greedy: true\n    },\n    'property': {\n      pattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n      lookbehind: true\n    },\n    'important': /!important\\b/i,\n    'function': {\n      pattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n      lookbehind: true\n    },\n    'punctuation': /[(){};:,]/\n  };\n  Prism.languages.css['atrule'].inside.rest = Prism.languages.css;\n  var markup = Prism.languages.markup;\n\n  if (markup) {\n    markup.tag.addInlined('style', 'css');\n    markup.tag.addAttribute('style', 'css');\n  }\n})(prism);\n/* \"prismjs/components/prism-css-extras\" */\n\n\n(function (Prism) {\n  var string = /(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/;\n  var selectorInside;\n  Prism.languages.css.selector = {\n    pattern: Prism.languages.css.selector.pattern,\n    lookbehind: true,\n    inside: selectorInside = {\n      'pseudo-element': /:(?:after|before|first-letter|first-line|selection)|::[-\\w]+/,\n      'pseudo-class': /:[-\\w]+/,\n      'class': /\\.[-\\w]+/,\n      'id': /#[-\\w]+/,\n      'attribute': {\n        pattern: RegExp('\\\\[(?:[^[\\\\]\"\\']|' + string.source + ')*\\\\]'),\n        greedy: true,\n        inside: {\n          'punctuation': /^\\[|\\]$/,\n          'case-sensitivity': {\n            pattern: /(\\s)[si]$/i,\n            lookbehind: true,\n            alias: 'keyword'\n          },\n          'namespace': {\n            pattern: /^(\\s*)(?:(?!\\s)[-*\\w\\xA0-\\uFFFF])*\\|(?!=)/,\n            lookbehind: true,\n            inside: {\n              'punctuation': /\\|$/\n            }\n          },\n          'attr-name': {\n            pattern: /^(\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+/,\n            lookbehind: true\n          },\n          'attr-value': [string, {\n            pattern: /(=\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+(?=\\s*$)/,\n            lookbehind: true\n          }],\n          'operator': /[|~*^$]?=/\n        }\n      },\n      'n-th': [{\n        pattern: /(\\(\\s*)[+-]?\\d*[\\dn](?:\\s*[+-]\\s*\\d+)?(?=\\s*\\))/,\n        lookbehind: true,\n        inside: {\n          'number': /[\\dn]+/,\n          'operator': /[+-]/\n        }\n      }, {\n        pattern: /(\\(\\s*)(?:even|odd)(?=\\s*\\))/i,\n        lookbehind: true\n      }],\n      'combinator': />|\\+|~|\\|\\|/,\n      // the `tag` token has been existed and removed.\n      // because we can't find a perfect tokenize to match it.\n      // if you want to add it, please read https://github.com/PrismJS/prism/pull/2373 first.\n      'punctuation': /[(),]/\n    }\n  };\n  Prism.languages.css['atrule'].inside['selector-function-argument'].inside = selectorInside;\n  Prism.languages.insertBefore('css', 'property', {\n    'variable': {\n      pattern: /(^|[^-\\w\\xA0-\\uFFFF])--(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*/i,\n      lookbehind: true\n    }\n  });\n  var unit = {\n    pattern: /(\\b\\d+)(?:%|[a-z]+(?![\\w-]))/,\n    lookbehind: true\n  }; // 123 -123 .123 -.123 12.3 -12.3\n\n  var number = {\n    pattern: /(^|[^\\w.-])-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/,\n    lookbehind: true\n  };\n  Prism.languages.insertBefore('css', 'function', {\n    'operator': {\n      pattern: /(\\s)[+\\-*\\/](?=\\s)/,\n      lookbehind: true\n    },\n    // CAREFUL!\n    // Previewers and Inline color use hexcode and color.\n    'hexcode': {\n      pattern: /\\B#[\\da-f]{3,8}\\b/i,\n      alias: 'color'\n    },\n    'color': [{\n      pattern: /(^|[^\\w-])(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)(?![\\w-])/i,\n      lookbehind: true\n    }, {\n      pattern: /\\b(?:hsl|rgb)\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*\\)\\B|\\b(?:hsl|rgb)a\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*,\\s*(?:0|0?\\.\\d+|1)\\s*\\)\\B/i,\n      inside: {\n        'unit': unit,\n        'number': number,\n        'function': /[\\w-]+(?=\\()/,\n        'punctuation': /[(),]/\n      }\n    }],\n    // it's important that there is no boundary assertion after the hex digits\n    'entity': /\\\\[\\da-f]{1,8}/i,\n    'unit': unit,\n    'number': number\n  });\n})(prism);\n/* \"prismjs/components/prism-javascript\" */\n\n\nprism.languages.javascript = prism.languages.extend('clike', {\n  'class-name': [prism.languages.clike['class-name'], {\n    pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n    lookbehind: true\n  }],\n  'keyword': [{\n    pattern: /((?:^|\\})\\s*)catch\\b/,\n    lookbehind: true\n  }, {\n    pattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n    lookbehind: true\n  }],\n  // Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n  'function': /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n  'number': {\n    pattern: RegExp(/(^|[^\\w$])/.source + '(?:' + ( // constant\n    /NaN|Infinity/.source + '|' + // binary integer\n    /0[bB][01]+(?:_[01]+)*n?/.source + '|' + // octal integer\n    /0[oO][0-7]+(?:_[0-7]+)*n?/.source + '|' + // hexadecimal integer\n    /0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source + '|' + // decimal bigint\n    /\\d+(?:_\\d+)*n/.source + '|' + // decimal number (integer or float) but no bigint\n    /(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source) + ')' + /(?![\\w$])/.source),\n    lookbehind: true\n  },\n  'operator': /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n});\nprism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/;\nprism.languages.insertBefore('javascript', 'keyword', {\n  'regex': {\n    // eslint-disable-next-line regexp/no-dupe-characters-character-class\n    pattern: /((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/,\n    lookbehind: true,\n    greedy: true,\n    inside: {\n      'regex-source': {\n        pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n        lookbehind: true,\n        alias: 'language-regex',\n        inside: prism.languages.regex\n      },\n      'regex-delimiter': /^\\/|\\/$/,\n      'regex-flags': /^[a-z]+$/\n    }\n  },\n  // This must be declared before keyword because we use \"function\" inside the look-forward\n  'function-variable': {\n    pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n    alias: 'function'\n  },\n  'parameter': [{\n    pattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n    lookbehind: true,\n    inside: prism.languages.javascript\n  }, {\n    pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n    lookbehind: true,\n    inside: prism.languages.javascript\n  }, {\n    pattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n    lookbehind: true,\n    inside: prism.languages.javascript\n  }, {\n    pattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n    lookbehind: true,\n    inside: prism.languages.javascript\n  }],\n  'constant': /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n});\nprism.languages.insertBefore('javascript', 'string', {\n  'hashbang': {\n    pattern: /^#!.*/,\n    greedy: true,\n    alias: 'comment'\n  },\n  'template-string': {\n    pattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n    greedy: true,\n    inside: {\n      'template-punctuation': {\n        pattern: /^`|`$/,\n        alias: 'string'\n      },\n      'interpolation': {\n        pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n        lookbehind: true,\n        inside: {\n          'interpolation-punctuation': {\n            pattern: /^\\$\\{|\\}$/,\n            alias: 'punctuation'\n          },\n          rest: prism.languages.javascript\n        }\n      },\n      'string': /[\\s\\S]+/\n    }\n  },\n  'string-property': {\n    pattern: /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n    lookbehind: true,\n    greedy: true,\n    alias: 'property'\n  }\n});\nprism.languages.insertBefore('javascript', 'operator', {\n  'literal-property': {\n    pattern: /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n    lookbehind: true,\n    alias: 'property'\n  }\n});\n\nif (prism.languages.markup) {\n  prism.languages.markup.tag.addInlined('script', 'javascript'); // add attribute support for all DOM events.\n  // https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n\n  prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source, 'javascript');\n}\n\nprism.languages.js = prism.languages.javascript;\n/* \"prismjs/components/prism-coffeescript\" */\n\n(function (Prism) {\n  // Ignore comments starting with { to privilege string interpolation highlighting\n  var comment = /#(?!\\{).+/;\n  var interpolation = {\n    pattern: /#\\{[^}]+\\}/,\n    alias: 'variable'\n  };\n  Prism.languages.coffeescript = Prism.languages.extend('javascript', {\n    'comment': comment,\n    'string': [// Strings are multiline\n    {\n      pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n      greedy: true\n    }, {\n      // Strings are multiline\n      pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n      greedy: true,\n      inside: {\n        'interpolation': interpolation\n      }\n    }],\n    'keyword': /\\b(?:and|break|by|catch|class|continue|debugger|delete|do|each|else|extend|extends|false|finally|for|if|in|instanceof|is|isnt|let|loop|namespace|new|no|not|null|of|off|on|or|own|return|super|switch|then|this|throw|true|try|typeof|undefined|unless|until|when|while|window|with|yes|yield)\\b/,\n    'class-member': {\n      pattern: /@(?!\\d)\\w+/,\n      alias: 'variable'\n    }\n  });\n  Prism.languages.insertBefore('coffeescript', 'comment', {\n    'multiline-comment': {\n      pattern: /###[\\s\\S]+?###/,\n      alias: 'comment'\n    },\n    // Block regexp can contain comments and interpolation\n    'block-regex': {\n      pattern: /\\/{3}[\\s\\S]*?\\/{3}/,\n      alias: 'regex',\n      inside: {\n        'comment': comment,\n        'interpolation': interpolation\n      }\n    }\n  });\n  Prism.languages.insertBefore('coffeescript', 'string', {\n    'inline-javascript': {\n      pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n      inside: {\n        'delimiter': {\n          pattern: /^`|`$/,\n          alias: 'punctuation'\n        },\n        'script': {\n          pattern: /[\\s\\S]+/,\n          alias: 'language-javascript',\n          inside: Prism.languages.javascript\n        }\n      }\n    },\n    // Block strings\n    'multiline-string': [{\n      pattern: /'''[\\s\\S]*?'''/,\n      greedy: true,\n      alias: 'string'\n    }, {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        interpolation: interpolation\n      }\n    }]\n  });\n  Prism.languages.insertBefore('coffeescript', 'keyword', {\n    // Object property\n    'property': /(?!\\d)\\w+(?=\\s*:(?!:))/\n  });\n  delete Prism.languages.coffeescript['template-string'];\n  Prism.languages.coffee = Prism.languages.coffeescript;\n})(prism);\n/* \"prismjs/components/prism-yaml\" */\n\n\n(function (Prism) {\n  // https://yaml.org/spec/1.2/spec.html#c-ns-anchor-property\n  // https://yaml.org/spec/1.2/spec.html#c-ns-alias-node\n  var anchorOrAlias = /[*&][^\\s[\\]{},]+/; // https://yaml.org/spec/1.2/spec.html#c-ns-tag-property\n\n  var tag = /!(?:<[\\w\\-%#;/?:@&=+$,.!~*'()[\\]]+>|(?:[a-zA-Z\\d-]*!)?[\\w\\-%#;/?:@&=+$.~*'()]+)?/; // https://yaml.org/spec/1.2/spec.html#c-ns-properties(n,c)\n\n  var properties = '(?:' + tag.source + '(?:[ \\t]+' + anchorOrAlias.source + ')?|' + anchorOrAlias.source + '(?:[ \\t]+' + tag.source + ')?)'; // https://yaml.org/spec/1.2/spec.html#ns-plain(n,c)\n  // This is a simplified version that doesn't support \"#\" and multiline keys\n  // All these long scarry character classes are simplified versions of YAML's characters\n\n  var plainKey = /(?:[^\\s\\x00-\\x08\\x0e-\\x1f!\"#%&'*,\\-:>?@[\\]`{|}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]|[?:-]<PLAIN>)(?:[ \\t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g, function () {\n    return /[^\\s\\x00-\\x08\\x0e-\\x1f,[\\]{}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]/.source;\n  });\n  var string = /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/.source;\n  /**\n   *\n   * @param {string} value\n   * @param {string} [flags]\n   * @returns {RegExp}\n   */\n\n  function createValuePattern(value, flags) {\n    flags = (flags || '').replace(/m/g, '') + 'm'; // add m flag\n\n    var pattern = /([:\\-,[{]\\s*(?:\\s<<prop>>[ \\t]+)?)(?:<<value>>)(?=[ \\t]*(?:$|,|\\]|\\}|(?:[\\r\\n]\\s*)?#))/.source.replace(/<<prop>>/g, function () {\n      return properties;\n    }).replace(/<<value>>/g, function () {\n      return value;\n    });\n    return RegExp(pattern, flags);\n  }\n\n  Prism.languages.yaml = {\n    'scalar': {\n      pattern: RegExp(/([\\-:]\\s*(?:\\s<<prop>>[ \\t]+)?[|>])[ \\t]*(?:((?:\\r?\\n|\\r)[ \\t]+)\\S[^\\r\\n]*(?:\\2[^\\r\\n]+)*)/.source.replace(/<<prop>>/g, function () {\n        return properties;\n      })),\n      lookbehind: true,\n      alias: 'string'\n    },\n    'comment': /#.*/,\n    'key': {\n      pattern: RegExp(/((?:^|[:\\-,[{\\r\\n?])[ \\t]*(?:<<prop>>[ \\t]+)?)<<key>>(?=\\s*:\\s)/.source.replace(/<<prop>>/g, function () {\n        return properties;\n      }).replace(/<<key>>/g, function () {\n        return '(?:' + plainKey + '|' + string + ')';\n      })),\n      lookbehind: true,\n      greedy: true,\n      alias: 'atrule'\n    },\n    'directive': {\n      pattern: /(^[ \\t]*)%.+/m,\n      lookbehind: true,\n      alias: 'important'\n    },\n    'datetime': {\n      pattern: createValuePattern(/\\d{4}-\\d\\d?-\\d\\d?(?:[tT]|[ \\t]+)\\d\\d?:\\d{2}:\\d{2}(?:\\.\\d*)?(?:[ \\t]*(?:Z|[-+]\\d\\d?(?::\\d{2})?))?|\\d{4}-\\d{2}-\\d{2}|\\d\\d?:\\d{2}(?::\\d{2}(?:\\.\\d*)?)?/.source),\n      lookbehind: true,\n      alias: 'number'\n    },\n    'boolean': {\n      pattern: createValuePattern(/false|true/.source, 'i'),\n      lookbehind: true,\n      alias: 'important'\n    },\n    'null': {\n      pattern: createValuePattern(/null|~/.source, 'i'),\n      lookbehind: true,\n      alias: 'important'\n    },\n    'string': {\n      pattern: createValuePattern(string),\n      lookbehind: true,\n      greedy: true\n    },\n    'number': {\n      pattern: createValuePattern(/[+-]?(?:0x[\\da-f]+|0o[0-7]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|\\.inf|\\.nan)/.source, 'i'),\n      lookbehind: true\n    },\n    'tag': tag,\n    'important': anchorOrAlias,\n    'punctuation': /---|[:[\\]{}\\-,|>?]|\\.\\.\\./\n  };\n  Prism.languages.yml = Prism.languages.yaml;\n})(prism);\n/* \"prismjs/components/prism-markdown\" */\n\n\n(function (Prism) {\n  // Allow only one line break\n  var inner = /(?:\\\\.|[^\\\\\\n\\r]|(?:\\n|\\r\\n?)(?![\\r\\n]))/.source;\n  /**\n   * This function is intended for the creation of the bold or italic pattern.\n   *\n   * This also adds a lookbehind group to the given pattern to ensure that the pattern is not backslash-escaped.\n   *\n   * _Note:_ Keep in mind that this adds a capturing group.\n   *\n   * @param {string} pattern\n   * @returns {RegExp}\n   */\n\n  function createInline(pattern) {\n    pattern = pattern.replace(/<inner>/g, function () {\n      return inner;\n    });\n    return RegExp(/((?:^|[^\\\\])(?:\\\\{2})*)/.source + '(?:' + pattern + ')');\n  }\n\n  var tableCell = /(?:\\\\.|``(?:[^`\\r\\n]|`(?!`))+``|`[^`\\r\\n]+`|[^\\\\|\\r\\n`])+/.source;\n  var tableRow = /\\|?__(?:\\|__)+\\|?(?:(?:\\n|\\r\\n?)|(?![\\s\\S]))/.source.replace(/__/g, function () {\n    return tableCell;\n  });\n  var tableLine = /\\|?[ \\t]*:?-{3,}:?[ \\t]*(?:\\|[ \\t]*:?-{3,}:?[ \\t]*)+\\|?(?:\\n|\\r\\n?)/.source;\n  Prism.languages.markdown = Prism.languages.extend('markup', {});\n  Prism.languages.insertBefore('markdown', 'prolog', {\n    'front-matter-block': {\n      pattern: /(^(?:\\s*[\\r\\n])?)---(?!.)[\\s\\S]*?[\\r\\n]---(?!.)/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'punctuation': /^---|---$/,\n        'front-matter': {\n          pattern: /\\S+(?:\\s+\\S+)*/,\n          alias: ['yaml', 'language-yaml'],\n          inside: Prism.languages.yaml\n        }\n      }\n    },\n    'blockquote': {\n      // > ...\n      pattern: /^>(?:[\\t ]*>)*/m,\n      alias: 'punctuation'\n    },\n    'table': {\n      pattern: RegExp('^' + tableRow + tableLine + '(?:' + tableRow + ')*', 'm'),\n      inside: {\n        'table-data-rows': {\n          pattern: RegExp('^(' + tableRow + tableLine + ')(?:' + tableRow + ')*$'),\n          lookbehind: true,\n          inside: {\n            'table-data': {\n              pattern: RegExp(tableCell),\n              inside: Prism.languages.markdown\n            },\n            'punctuation': /\\|/\n          }\n        },\n        'table-line': {\n          pattern: RegExp('^(' + tableRow + ')' + tableLine + '$'),\n          lookbehind: true,\n          inside: {\n            'punctuation': /\\||:?-{3,}:?/\n          }\n        },\n        'table-header-row': {\n          pattern: RegExp('^' + tableRow + '$'),\n          inside: {\n            'table-header': {\n              pattern: RegExp(tableCell),\n              alias: 'important',\n              inside: Prism.languages.markdown\n            },\n            'punctuation': /\\|/\n          }\n        }\n      }\n    },\n    'code': [{\n      // Prefixed by 4 spaces or 1 tab and preceded by an empty line\n      pattern: /((?:^|\\n)[ \\t]*\\n|(?:^|\\r\\n?)[ \\t]*\\r\\n?)(?: {4}|\\t).+(?:(?:\\n|\\r\\n?)(?: {4}|\\t).+)*/,\n      lookbehind: true,\n      alias: 'keyword'\n    }, {\n      // ```optional language\n      // code block\n      // ```\n      pattern: /^```[\\s\\S]*?^```$/m,\n      greedy: true,\n      inside: {\n        'code-block': {\n          pattern: /^(```.*(?:\\n|\\r\\n?))[\\s\\S]+?(?=(?:\\n|\\r\\n?)^```$)/m,\n          lookbehind: true\n        },\n        'code-language': {\n          pattern: /^(```).+/,\n          lookbehind: true\n        },\n        'punctuation': /```/\n      }\n    }],\n    'title': [{\n      // title 1\n      // =======\n      // title 2\n      // -------\n      pattern: /\\S.*(?:\\n|\\r\\n?)(?:==+|--+)(?=[ \\t]*$)/m,\n      alias: 'important',\n      inside: {\n        punctuation: /==+$|--+$/\n      }\n    }, {\n      // # title 1\n      // ###### title 6\n      pattern: /(^\\s*)#.+/m,\n      lookbehind: true,\n      alias: 'important',\n      inside: {\n        punctuation: /^#+|#+$/\n      }\n    }],\n    'hr': {\n      // ***\n      // ---\n      // * * *\n      // -----------\n      pattern: /(^\\s*)([*-])(?:[\\t ]*\\2){2,}(?=\\s*$)/m,\n      lookbehind: true,\n      alias: 'punctuation'\n    },\n    'list': {\n      // * item\n      // + item\n      // - item\n      // 1. item\n      pattern: /(^\\s*)(?:[*+-]|\\d+\\.)(?=[\\t ].)/m,\n      lookbehind: true,\n      alias: 'punctuation'\n    },\n    'url-reference': {\n      // [id]: http://example.com \"Optional title\"\n      // [id]: http://example.com 'Optional title'\n      // [id]: http://example.com (Optional title)\n      // [id]: <http://example.com> \"Optional title\"\n      pattern: /!?\\[[^\\]]+\\]:[\\t ]+(?:\\S+|<(?:\\\\.|[^>\\\\])+>)(?:[\\t ]+(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\)))?/,\n      inside: {\n        'variable': {\n          pattern: /^(!?\\[)[^\\]]+/,\n          lookbehind: true\n        },\n        'string': /(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\))$/,\n        'punctuation': /^[\\[\\]!:]|[<>]/\n      },\n      alias: 'url'\n    },\n    'bold': {\n      // **strong**\n      // __strong__\n      // allow one nested instance of italic text using the same delimiter\n      pattern: createInline(/\\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\\b|\\*\\*(?:(?!\\*)<inner>|\\*(?:(?!\\*)<inner>)+\\*)+\\*\\*/.source),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'content': {\n          pattern: /(^..)[\\s\\S]+(?=..$)/,\n          lookbehind: true,\n          inside: {} // see below\n\n        },\n        'punctuation': /\\*\\*|__/\n      }\n    },\n    'italic': {\n      // *em*\n      // _em_\n      // allow one nested instance of bold text using the same delimiter\n      pattern: createInline(/\\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\\b|\\*(?:(?!\\*)<inner>|\\*\\*(?:(?!\\*)<inner>)+\\*\\*)+\\*/.source),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'content': {\n          pattern: /(^.)[\\s\\S]+(?=.$)/,\n          lookbehind: true,\n          inside: {} // see below\n\n        },\n        'punctuation': /[*_]/\n      }\n    },\n    'strike': {\n      // ~~strike through~~\n      // ~strike~\n      // eslint-disable-next-line regexp/strict\n      pattern: createInline(/(~~?)(?:(?!~)<inner>)+\\2/.source),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'content': {\n          pattern: /(^~~?)[\\s\\S]+(?=\\1$)/,\n          lookbehind: true,\n          inside: {} // see below\n\n        },\n        'punctuation': /~~?/\n      }\n    },\n    'code-snippet': {\n      // `code`\n      // ``code``\n      pattern: /(^|[^\\\\`])(?:``[^`\\r\\n]+(?:`[^`\\r\\n]+)*``(?!`)|`[^`\\r\\n]+`(?!`))/,\n      lookbehind: true,\n      greedy: true,\n      alias: ['code', 'keyword']\n    },\n    'url': {\n      // [example](http://example.com \"Optional title\")\n      // [example][id]\n      // [example] [id]\n      pattern: createInline(/!?\\[(?:(?!\\])<inner>)+\\](?:\\([^\\s)]+(?:[\\t ]+\"(?:\\\\.|[^\"\\\\])*\")?\\)|[ \\t]?\\[(?:(?!\\])<inner>)+\\])/.source),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'operator': /^!/,\n        'content': {\n          pattern: /(^\\[)[^\\]]+(?=\\])/,\n          lookbehind: true,\n          inside: {} // see below\n\n        },\n        'variable': {\n          pattern: /(^\\][ \\t]?\\[)[^\\]]+(?=\\]$)/,\n          lookbehind: true\n        },\n        'url': {\n          pattern: /(^\\]\\()[^\\s)]+/,\n          lookbehind: true\n        },\n        'string': {\n          pattern: /(^[ \\t]+)\"(?:\\\\.|[^\"\\\\])*\"(?=\\)$)/,\n          lookbehind: true\n        }\n      }\n    }\n  });\n  ['url', 'bold', 'italic', 'strike'].forEach(function (token) {\n    ['url', 'bold', 'italic', 'strike', 'code-snippet'].forEach(function (inside) {\n      if (token !== inside) {\n        Prism.languages.markdown[token].inside.content.inside[inside] = Prism.languages.markdown[inside];\n      }\n    });\n  });\n  Prism.hooks.add('after-tokenize', function (env) {\n    if (env.language !== 'markdown' && env.language !== 'md') {\n      return;\n    }\n\n    function walkTokens(tokens) {\n      if (!tokens || typeof tokens === 'string') {\n        return;\n      }\n\n      for (var i = 0, l = tokens.length; i < l; i++) {\n        var token = tokens[i];\n\n        if (token.type !== 'code') {\n          walkTokens(token.content);\n          continue;\n        }\n        /*\n         * Add the correct `language-xxxx` class to this code block. Keep in mind that the `code-language` token\n         * is optional. But the grammar is defined so that there is only one case we have to handle:\n         *\n         * token.content = [\n         *     <span class=\"punctuation\">```</span>,\n         *     <span class=\"code-language\">xxxx</span>,\n         *     '\\n', // exactly one new lines (\\r or \\n or \\r\\n)\n         *     <span class=\"code-block\">...</span>,\n         *     '\\n', // exactly one new lines again\n         *     <span class=\"punctuation\">```</span>\n         * ];\n         */\n\n\n        var codeLang = token.content[1];\n        var codeBlock = token.content[3];\n\n        if (codeLang && codeBlock && codeLang.type === 'code-language' && codeBlock.type === 'code-block' && typeof codeLang.content === 'string') {\n          // this might be a language that Prism does not support\n          // do some replacements to support C++, C#, and F#\n          var lang = codeLang.content.replace(/\\b#/g, 'sharp').replace(/\\b\\+\\+/g, 'pp'); // only use the first word\n\n          lang = (/[a-z][\\w-]*/i.exec(lang) || [''])[0].toLowerCase();\n          var alias = 'language-' + lang; // add alias\n\n          if (!codeBlock.alias) {\n            codeBlock.alias = [alias];\n          } else if (typeof codeBlock.alias === 'string') {\n            codeBlock.alias = [codeBlock.alias, alias];\n          } else {\n            codeBlock.alias.push(alias);\n          }\n        }\n      }\n    }\n\n    walkTokens(env.tokens);\n  });\n  Prism.hooks.add('wrap', function (env) {\n    if (env.type !== 'code-block') {\n      return;\n    }\n\n    var codeLang = '';\n\n    for (var i = 0, l = env.classes.length; i < l; i++) {\n      var cls = env.classes[i];\n      var match = /language-(.+)/.exec(cls);\n\n      if (match) {\n        codeLang = match[1];\n        break;\n      }\n    }\n\n    var grammar = Prism.languages[codeLang];\n\n    if (!grammar) {\n      if (codeLang && codeLang !== 'none' && Prism.plugins.autoloader) {\n        var id = 'md-' + new Date().valueOf() + '-' + Math.floor(Math.random() * 1e16);\n        env.attributes['id'] = id;\n        Prism.plugins.autoloader.loadLanguages(codeLang, function () {\n          var ele = document.getElementById(id);\n\n          if (ele) {\n            ele.innerHTML = Prism.highlight(ele.textContent, Prism.languages[codeLang], codeLang);\n          }\n        });\n      }\n    } else {\n      env.content = Prism.highlight(textContent(env.content), grammar, codeLang);\n    }\n  });\n  var tagPattern = RegExp(Prism.languages.markup.tag.pattern.source, 'gi');\n  /**\n   * A list of known entity names.\n   *\n   * This will always be incomplete to save space. The current list is the one used by lowdash's unescape function.\n   *\n   * @see {@link https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/unescape.js#L2}\n   */\n\n  var KNOWN_ENTITY_NAMES = {\n    'amp': '&',\n    'lt': '<',\n    'gt': '>',\n    'quot': '\"'\n  }; // IE 11 doesn't support `String.fromCodePoint`\n\n  var fromCodePoint = String.fromCodePoint || String.fromCharCode;\n  /**\n   * Returns the text content of a given HTML source code string.\n   *\n   * @param {string} html\n   * @returns {string}\n   */\n\n  function textContent(html) {\n    // remove all tags\n    var text = html.replace(tagPattern, ''); // decode known entities\n\n    text = text.replace(/&(\\w{1,8}|#x?[\\da-f]{1,8});/gi, function (m, code) {\n      code = code.toLowerCase();\n\n      if (code[0] === '#') {\n        var value;\n\n        if (code[1] === 'x') {\n          value = parseInt(code.slice(2), 16);\n        } else {\n          value = Number(code.slice(1));\n        }\n\n        return fromCodePoint(value);\n      } else {\n        var known = KNOWN_ENTITY_NAMES[code];\n\n        if (known) {\n          return known;\n        } // unable to decode\n\n\n        return m;\n      }\n    });\n    return text;\n  }\n\n  Prism.languages.md = Prism.languages.markdown;\n})(prism);\n/* \"prismjs/components/prism-graphql\" */\n\n\nprism.languages.graphql = {\n  'comment': /#.*/,\n  'description': {\n    pattern: /(?:\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")(?=\\s*[a-z_])/i,\n    greedy: true,\n    alias: 'string',\n    inside: {\n      'language-markdown': {\n        pattern: /(^\"(?:\"\")?)(?!\\1)[\\s\\S]+(?=\\1$)/,\n        lookbehind: true,\n        inside: prism.languages.markdown\n      }\n    }\n  },\n  'string': {\n    pattern: /\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n    greedy: true\n  },\n  'number': /(?:\\B-|\\b)\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n  'boolean': /\\b(?:false|true)\\b/,\n  'variable': /\\$[a-z_]\\w*/i,\n  'directive': {\n    pattern: /@[a-z_]\\w*/i,\n    alias: 'function'\n  },\n  'attr-name': {\n    pattern: /\\b[a-z_]\\w*(?=\\s*(?:\\((?:[^()\"]|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")*\\))?:)/i,\n    greedy: true\n  },\n  'atom-input': {\n    pattern: /\\b[A-Z]\\w*Input\\b/,\n    alias: 'class-name'\n  },\n  'scalar': /\\b(?:Boolean|Float|ID|Int|String)\\b/,\n  'constant': /\\b[A-Z][A-Z_\\d]*\\b/,\n  'class-name': {\n    pattern: /(\\b(?:enum|implements|interface|on|scalar|type|union)\\s+|&\\s*|:\\s*|\\[)[A-Z_]\\w*/,\n    lookbehind: true\n  },\n  'fragment': {\n    pattern: /(\\bfragment\\s+|\\.{3}\\s*(?!on\\b))[a-zA-Z_]\\w*/,\n    lookbehind: true,\n    alias: 'function'\n  },\n  'definition-mutation': {\n    pattern: /(\\bmutation\\s+)[a-zA-Z_]\\w*/,\n    lookbehind: true,\n    alias: 'function'\n  },\n  'definition-query': {\n    pattern: /(\\bquery\\s+)[a-zA-Z_]\\w*/,\n    lookbehind: true,\n    alias: 'function'\n  },\n  'keyword': /\\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\\b/,\n  'operator': /[!=|&]|\\.{3}/,\n  'property-query': /\\w+(?=\\s*\\()/,\n  'object': /\\w+(?=\\s*\\{)/,\n  'punctuation': /[!(){}\\[\\]:=,]/,\n  'property': /\\w+/\n};\nprism.hooks.add('after-tokenize', function afterTokenizeGraphql(env) {\n  if (env.language !== 'graphql') {\n    return;\n  }\n  /**\n   * get the graphql token stream that we want to customize\n   *\n   * @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n   * @type {Token[]}\n   */\n\n\n  var validTokens = env.tokens.filter(function (token) {\n    return typeof token !== 'string' && token.type !== 'comment' && token.type !== 'scalar';\n  });\n  var currentIndex = 0;\n  /**\n   * Returns whether the token relative to the current index has the given type.\n   *\n   * @param {number} offset\n   * @returns {Token | undefined}\n   */\n\n  function getToken(offset) {\n    return validTokens[currentIndex + offset];\n  }\n  /**\n   * Returns whether the token relative to the current index has the given type.\n   *\n   * @param {readonly string[]} types\n   * @param {number} [offset=0]\n   * @returns {boolean}\n   */\n\n\n  function isTokenType(types, offset) {\n    offset = offset || 0;\n\n    for (var i = 0; i < types.length; i++) {\n      var token = getToken(i + offset);\n\n      if (!token || token.type !== types[i]) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n  /**\n   * Returns the index of the closing bracket to an opening bracket.\n   *\n   * It is assumed that `token[currentIndex - 1]` is an opening bracket.\n   *\n   * If no closing bracket could be found, `-1` will be returned.\n   *\n   * @param {RegExp} open\n   * @param {RegExp} close\n   * @returns {number}\n   */\n\n\n  function findClosingBracket(open, close) {\n    var stackHeight = 1;\n\n    for (var i = currentIndex; i < validTokens.length; i++) {\n      var token = validTokens[i];\n      var content = token.content;\n\n      if (token.type === 'punctuation' && typeof content === 'string') {\n        if (open.test(content)) {\n          stackHeight++;\n        } else if (close.test(content)) {\n          stackHeight--;\n\n          if (stackHeight === 0) {\n            return i;\n          }\n        }\n      }\n    }\n\n    return -1;\n  }\n  /**\n   * Adds an alias to the given token.\n   *\n   * @param {Token} token\n   * @param {string} alias\n   * @returns {void}\n   */\n\n\n  function addAlias(token, alias) {\n    var aliases = token.alias;\n\n    if (!aliases) {\n      token.alias = aliases = [];\n    } else if (!Array.isArray(aliases)) {\n      token.alias = aliases = [aliases];\n    }\n\n    aliases.push(alias);\n  }\n\n  for (; currentIndex < validTokens.length;) {\n    var startToken = validTokens[currentIndex++]; // add special aliases for mutation tokens\n\n    if (startToken.type === 'keyword' && startToken.content === 'mutation') {\n      // any array of the names of all input variables (if any)\n      var inputVariables = [];\n\n      if (isTokenType(['definition-mutation', 'punctuation']) && getToken(1).content === '(') {\n        // definition\n        currentIndex += 2; // skip 'definition-mutation' and 'punctuation'\n\n        var definitionEnd = findClosingBracket(/^\\($/, /^\\)$/);\n\n        if (definitionEnd === -1) {\n          continue;\n        } // find all input variables\n\n\n        for (; currentIndex < definitionEnd; currentIndex++) {\n          var t = getToken(0);\n\n          if (t.type === 'variable') {\n            addAlias(t, 'variable-input');\n            inputVariables.push(t.content);\n          }\n        }\n\n        currentIndex = definitionEnd + 1;\n      }\n\n      if (isTokenType(['punctuation', 'property-query']) && getToken(0).content === '{') {\n        currentIndex++; // skip opening bracket\n\n        addAlias(getToken(0), 'property-mutation');\n\n        if (inputVariables.length > 0) {\n          var mutationEnd = findClosingBracket(/^\\{$/, /^\\}$/);\n\n          if (mutationEnd === -1) {\n            continue;\n          } // give references to input variables a special alias\n\n\n          for (var i = currentIndex; i < mutationEnd; i++) {\n            var varToken = validTokens[i];\n\n            if (varToken.type === 'variable' && inputVariables.indexOf(varToken.content) >= 0) {\n              addAlias(varToken, 'variable-input');\n            }\n          }\n        }\n      }\n    }\n  }\n});\n/* \"prismjs/components/prism-sql\" */\n\nprism.languages.sql = {\n  'comment': {\n    pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:--|\\/\\/|#).*)/,\n    lookbehind: true\n  },\n  'variable': [{\n    pattern: /@([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1/,\n    greedy: true\n  }, /@[\\w.$]+/],\n  'string': {\n    pattern: /(^|[^@\\\\])(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\]|\\2\\2)*\\2/,\n    greedy: true,\n    lookbehind: true\n  },\n  'identifier': {\n    pattern: /(^|[^@\\\\])`(?:\\\\[\\s\\S]|[^`\\\\]|``)*`/,\n    greedy: true,\n    lookbehind: true,\n    inside: {\n      'punctuation': /^`|`$/\n    }\n  },\n  'function': /\\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\\s*\\()/i,\n  // Should we highlight user defined functions too?\n  'keyword': /\\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\\b/i,\n  'boolean': /\\b(?:FALSE|NULL|TRUE)\\b/i,\n  'number': /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+\\b/i,\n  'operator': /[-+*\\/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\\b/i,\n  'punctuation': /[;[\\]()`,.]/\n};\n/* \"prismjs/components/prism-js-templates\" */\n\n(function (Prism) {\n  var templateString = Prism.languages.javascript['template-string']; // see the pattern in prism-javascript.js\n\n  var templateLiteralPattern = templateString.pattern.source;\n  var interpolationObject = templateString.inside['interpolation'];\n  var interpolationPunctuationObject = interpolationObject.inside['interpolation-punctuation'];\n  var interpolationPattern = interpolationObject.pattern.source;\n  /**\n   * Creates a new pattern to match a template string with a special tag.\n   *\n   * This will return `undefined` if there is no grammar with the given language id.\n   *\n   * @param {string} language The language id of the embedded language. E.g. `markdown`.\n   * @param {string} tag The regex pattern to match the tag.\n   * @returns {object | undefined}\n   * @example\n   * createTemplate('css', /\\bcss/.source);\n   */\n\n  function createTemplate(language, tag) {\n    if (!Prism.languages[language]) {\n      return undefined;\n    }\n\n    return {\n      pattern: RegExp('((?:' + tag + ')\\\\s*)' + templateLiteralPattern),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'template-punctuation': {\n          pattern: /^`|`$/,\n          alias: 'string'\n        },\n        'embedded-code': {\n          pattern: /[\\s\\S]+/,\n          alias: language\n        }\n      }\n    };\n  }\n\n  Prism.languages.javascript['template-string'] = [// styled-jsx:\n  //   css`a { color: #25F; }`\n  // styled-components:\n  //   styled.h1`color: red;`\n  createTemplate('css', /\\b(?:styled(?:\\([^)]*\\))?(?:\\s*\\.\\s*\\w+(?:\\([^)]*\\))*)*|css(?:\\s*\\.\\s*(?:global|resolve))?|createGlobalStyle|keyframes)/.source), // html`<p></p>`\n  // div.innerHTML = `<p></p>`\n  createTemplate('html', /\\bhtml|\\.\\s*(?:inner|outer)HTML\\s*\\+?=/.source), // svg`<path fill=\"#fff\" d=\"M55.37 ...\"/>`\n  createTemplate('svg', /\\bsvg/.source), // md`# h1`, markdown`## h2`\n  createTemplate('markdown', /\\b(?:markdown|md)/.source), // gql`...`, graphql`...`, graphql.experimental`...`\n  createTemplate('graphql', /\\b(?:gql|graphql(?:\\s*\\.\\s*experimental)?)/.source), // sql`...`\n  createTemplate('sql', /\\bsql/.source), // vanilla template string\n  templateString].filter(Boolean);\n  /**\n   * Returns a specific placeholder literal for the given language.\n   *\n   * @param {number} counter\n   * @param {string} language\n   * @returns {string}\n   */\n\n  function getPlaceholder(counter, language) {\n    return '___' + language.toUpperCase() + '_' + counter + '___';\n  }\n  /**\n   * Returns the tokens of `Prism.tokenize` but also runs the `before-tokenize` and `after-tokenize` hooks.\n   *\n   * @param {string} code\n   * @param {any} grammar\n   * @param {string} language\n   * @returns {(string|Token)[]}\n   */\n\n\n  function tokenizeWithHooks(code, grammar, language) {\n    var env = {\n      code: code,\n      grammar: grammar,\n      language: language\n    };\n    Prism.hooks.run('before-tokenize', env);\n    env.tokens = Prism.tokenize(env.code, env.grammar);\n    Prism.hooks.run('after-tokenize', env);\n    return env.tokens;\n  }\n  /**\n   * Returns the token of the given JavaScript interpolation expression.\n   *\n   * @param {string} expression The code of the expression. E.g. `\"${42}\"`\n   * @returns {Token}\n   */\n\n\n  function tokenizeInterpolationExpression(expression) {\n    var tempGrammar = {};\n    tempGrammar['interpolation-punctuation'] = interpolationPunctuationObject;\n    /** @type {Array} */\n\n    var tokens = Prism.tokenize(expression, tempGrammar);\n\n    if (tokens.length === 3) {\n      /**\n       * The token array will look like this\n       * [\n       *     [\"interpolation-punctuation\", \"${\"]\n       *     \"...\" // JavaScript expression of the interpolation\n       *     [\"interpolation-punctuation\", \"}\"]\n       * ]\n       */\n      var args = [1, 1];\n      args.push.apply(args, tokenizeWithHooks(tokens[1], Prism.languages.javascript, 'javascript'));\n      tokens.splice.apply(tokens, args);\n    }\n\n    return new Prism.Token('interpolation', tokens, interpolationObject.alias, expression);\n  }\n  /**\n   * Tokenizes the given code with support for JavaScript interpolation expressions mixed in.\n   *\n   * This function has 3 phases:\n   *\n   * 1. Replace all JavaScript interpolation expression with a placeholder.\n   *    The placeholder will have the syntax of a identify of the target language.\n   * 2. Tokenize the code with placeholders.\n   * 3. Tokenize the interpolation expressions and re-insert them into the tokenize code.\n   *    The insertion only works if a placeholder hasn't been \"ripped apart\" meaning that the placeholder has been\n   *    tokenized as two tokens by the grammar of the embedded language.\n   *\n   * @param {string} code\n   * @param {object} grammar\n   * @param {string} language\n   * @returns {Token}\n   */\n\n\n  function tokenizeEmbedded(code, grammar, language) {\n    // 1. First filter out all interpolations\n    // because they might be escaped, we need a lookbehind, so we use Prism\n\n    /** @type {(Token|string)[]} */\n    var _tokens = Prism.tokenize(code, {\n      'interpolation': {\n        pattern: RegExp(interpolationPattern),\n        lookbehind: true\n      }\n    }); // replace all interpolations with a placeholder which is not in the code already\n\n\n    var placeholderCounter = 0;\n    /** @type {Object<string, string>} */\n\n    var placeholderMap = {};\n\n    var embeddedCode = _tokens.map(function (token) {\n      if (typeof token === 'string') {\n        return token;\n      } else {\n        var interpolationExpression = token.content;\n        var placeholder;\n\n        while (code.indexOf(placeholder = getPlaceholder(placeholderCounter++, language)) !== -1) {\n          /* noop */\n        }\n\n        placeholderMap[placeholder] = interpolationExpression;\n        return placeholder;\n      }\n    }).join(''); // 2. Tokenize the embedded code\n\n\n    var embeddedTokens = tokenizeWithHooks(embeddedCode, grammar, language); // 3. Re-insert the interpolation\n\n    var placeholders = Object.keys(placeholderMap);\n    placeholderCounter = 0;\n    /**\n     *\n     * @param {(Token|string)[]} tokens\n     * @returns {void}\n     */\n\n    function walkTokens(tokens) {\n      for (var i = 0; i < tokens.length; i++) {\n        if (placeholderCounter >= placeholders.length) {\n          return;\n        }\n\n        var token = tokens[i];\n\n        if (typeof token === 'string' || typeof token.content === 'string') {\n          var placeholder = placeholders[placeholderCounter];\n          var s = typeof token === 'string' ? token :\n          /** @type {string} */\n          token.content;\n          var index = s.indexOf(placeholder);\n\n          if (index !== -1) {\n            ++placeholderCounter;\n            var before = s.substring(0, index);\n            var middle = tokenizeInterpolationExpression(placeholderMap[placeholder]);\n            var after = s.substring(index + placeholder.length);\n            var replacement = [];\n\n            if (before) {\n              replacement.push(before);\n            }\n\n            replacement.push(middle);\n\n            if (after) {\n              var afterTokens = [after];\n              walkTokens(afterTokens);\n              replacement.push.apply(replacement, afterTokens);\n            }\n\n            if (typeof token === 'string') {\n              tokens.splice.apply(tokens, [i, 1].concat(replacement));\n              i += replacement.length - 1;\n            } else {\n              token.content = replacement;\n            }\n          }\n        } else {\n          var content = token.content;\n\n          if (Array.isArray(content)) {\n            walkTokens(content);\n          } else {\n            walkTokens([content]);\n          }\n        }\n      }\n    }\n\n    walkTokens(embeddedTokens);\n    return new Prism.Token(language, embeddedTokens, 'language-' + language, code);\n  }\n  /**\n   * The languages for which JS templating will handle tagged template literals.\n   *\n   * JS templating isn't active for only JavaScript but also related languages like TypeScript, JSX, and TSX.\n   */\n\n\n  var supportedLanguages = {\n    'javascript': true,\n    'js': true,\n    'typescript': true,\n    'ts': true,\n    'jsx': true,\n    'tsx': true\n  };\n  Prism.hooks.add('after-tokenize', function (env) {\n    if (!(env.language in supportedLanguages)) {\n      return;\n    }\n    /**\n     * Finds and tokenizes all template strings with an embedded languages.\n     *\n     * @param {(Token | string)[]} tokens\n     * @returns {void}\n     */\n\n\n    function findTemplateStrings(tokens) {\n      for (var i = 0, l = tokens.length; i < l; i++) {\n        var token = tokens[i];\n\n        if (typeof token === 'string') {\n          continue;\n        }\n\n        var content = token.content;\n\n        if (!Array.isArray(content)) {\n          if (typeof content !== 'string') {\n            findTemplateStrings([content]);\n          }\n\n          continue;\n        }\n\n        if (token.type === 'template-string') {\n          /**\n           * A JavaScript template-string token will look like this:\n           *\n           * [\"template-string\", [\n           *     [\"template-punctuation\", \"`\"],\n           *     (\n           *         An array of \"string\" and \"interpolation\" tokens. This is the simple string case.\n           *         or\n           *         [\"embedded-code\", \"...\"] This is the token containing the embedded code.\n           *                                  It also has an alias which is the language of the embedded code.\n           *     ),\n           *     [\"template-punctuation\", \"`\"]\n           * ]]\n           */\n          var embedded = content[1];\n\n          if (content.length === 3 && typeof embedded !== 'string' && embedded.type === 'embedded-code') {\n            // get string content\n            var code = stringContent(embedded);\n            var alias = embedded.alias;\n            var language = Array.isArray(alias) ? alias[0] : alias;\n            var grammar = Prism.languages[language];\n\n            if (!grammar) {\n              // the embedded language isn't registered.\n              continue;\n            }\n\n            content[1] = tokenizeEmbedded(code, grammar, language);\n          }\n        } else {\n          findTemplateStrings(content);\n        }\n      }\n    }\n\n    findTemplateStrings(env.tokens);\n  });\n  /**\n   * Returns the string content of a token or token stream.\n   *\n   * @param {string | Token | (string | Token)[]} value\n   * @returns {string}\n   */\n\n  function stringContent(value) {\n    if (typeof value === 'string') {\n      return value;\n    } else if (Array.isArray(value)) {\n      return value.map(stringContent).join('');\n    } else {\n      return stringContent(value.content);\n    }\n  }\n})(prism);\n/* \"prismjs/components/prism-typescript\" */\n\n\n(function (Prism) {\n  Prism.languages.typescript = Prism.languages.extend('javascript', {\n    'class-name': {\n      pattern: /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,\n      lookbehind: true,\n      greedy: true,\n      inside: null // see below\n\n    },\n    'builtin': /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/\n  }); // The keywords TypeScript adds to JavaScript\n\n  Prism.languages.typescript.keyword.push(/\\b(?:abstract|declare|is|keyof|readonly|require)\\b/, // keywords that have to be followed by an identifier\n  /\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/, // This is for `import type *, {}`\n  /\\btype\\b(?=\\s*(?:[\\{*]|$))/); // doesn't work with TS because TS is too complex\n\n  delete Prism.languages.typescript['parameter'];\n  delete Prism.languages.typescript['literal-property']; // a version of typescript specifically for highlighting types\n\n  var typeInside = Prism.languages.extend('typescript', {});\n  delete typeInside['class-name'];\n  Prism.languages.typescript['class-name'].inside = typeInside;\n  Prism.languages.insertBefore('typescript', 'function', {\n    'decorator': {\n      pattern: /@[$\\w\\xA0-\\uFFFF]+/,\n      inside: {\n        'at': {\n          pattern: /^@/,\n          alias: 'operator'\n        },\n        'function': /^[\\s\\S]+/\n      }\n    },\n    'generic-function': {\n      // e.g. foo<T extends \"bar\" | \"baz\">( ...\n      pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,\n      greedy: true,\n      inside: {\n        'function': /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,\n        'generic': {\n          pattern: /<[\\s\\S]+/,\n          // everything after the first <\n          alias: 'class-name',\n          inside: typeInside\n        }\n      }\n    }\n  });\n  Prism.languages.ts = Prism.languages.typescript;\n})(prism);\n/* \"prismjs/components/prism-js-extras\" */\n\n\n(function (Prism) {\n  Prism.languages.insertBefore('javascript', 'function-variable', {\n    'method-variable': {\n      pattern: RegExp('(\\\\.\\\\s*)' + Prism.languages.javascript['function-variable'].pattern.source),\n      lookbehind: true,\n      alias: ['function-variable', 'method', 'function', 'property-access']\n    }\n  });\n  Prism.languages.insertBefore('javascript', 'function', {\n    'method': {\n      pattern: RegExp('(\\\\.\\\\s*)' + Prism.languages.javascript['function'].source),\n      lookbehind: true,\n      alias: ['function', 'property-access']\n    }\n  });\n  Prism.languages.insertBefore('javascript', 'constant', {\n    'known-class-name': [{\n      // standard built-ins\n      // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\n      pattern: /\\b(?:(?:Float(?:32|64)|(?:Int|Uint)(?:8|16|32)|Uint8Clamped)?Array|ArrayBuffer|BigInt|Boolean|DataView|Date|Error|Function|Intl|JSON|(?:Weak)?(?:Map|Set)|Math|Number|Object|Promise|Proxy|Reflect|RegExp|String|Symbol|WebAssembly)\\b/,\n      alias: 'class-name'\n    }, {\n      // errors\n      pattern: /\\b(?:[A-Z]\\w*)Error\\b/,\n      alias: 'class-name'\n    }]\n  });\n  /**\n   * Replaces the `<ID>` placeholder in the given pattern with a pattern for general JS identifiers.\n   *\n   * @param {string} source\n   * @param {string} [flags]\n   * @returns {RegExp}\n   */\n\n  function withId(source, flags) {\n    return RegExp(source.replace(/<ID>/g, function () {\n      return /(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/.source;\n    }), flags);\n  }\n\n  Prism.languages.insertBefore('javascript', 'keyword', {\n    'imports': {\n      // https://tc39.es/ecma262/#sec-imports\n      pattern: withId(/(\\bimport\\b\\s*)(?:<ID>(?:\\s*,\\s*(?:\\*\\s*as\\s+<ID>|\\{[^{}]*\\}))?|\\*\\s*as\\s+<ID>|\\{[^{}]*\\})(?=\\s*\\bfrom\\b)/.source),\n      lookbehind: true,\n      inside: Prism.languages.javascript\n    },\n    'exports': {\n      // https://tc39.es/ecma262/#sec-exports\n      pattern: withId(/(\\bexport\\b\\s*)(?:\\*(?:\\s*as\\s+<ID>)?(?=\\s*\\bfrom\\b)|\\{[^{}]*\\})/.source),\n      lookbehind: true,\n      inside: Prism.languages.javascript\n    }\n  });\n  Prism.languages.javascript['keyword'].unshift({\n    pattern: /\\b(?:as|default|export|from|import)\\b/,\n    alias: 'module'\n  }, {\n    pattern: /\\b(?:await|break|catch|continue|do|else|finally|for|if|return|switch|throw|try|while|yield)\\b/,\n    alias: 'control-flow'\n  }, {\n    pattern: /\\bnull\\b/,\n    alias: ['null', 'nil']\n  }, {\n    pattern: /\\bundefined\\b/,\n    alias: 'nil'\n  });\n  Prism.languages.insertBefore('javascript', 'operator', {\n    'spread': {\n      pattern: /\\.{3}/,\n      alias: 'operator'\n    },\n    'arrow': {\n      pattern: /=>/,\n      alias: 'operator'\n    }\n  });\n  Prism.languages.insertBefore('javascript', 'punctuation', {\n    'property-access': {\n      pattern: withId(/(\\.\\s*)#?<ID>/.source),\n      lookbehind: true\n    },\n    'maybe-class-name': {\n      pattern: /(^|[^$\\w\\xA0-\\uFFFF])[A-Z][$\\w\\xA0-\\uFFFF]+/,\n      lookbehind: true\n    },\n    'dom': {\n      // this contains only a few commonly used DOM variables\n      pattern: /\\b(?:document|(?:local|session)Storage|location|navigator|performance|window)\\b/,\n      alias: 'variable'\n    },\n    'console': {\n      pattern: /\\bconsole(?=\\s*\\.)/,\n      alias: 'class-name'\n    }\n  }); // add 'maybe-class-name' to tokens which might be a class name\n\n  var maybeClassNameTokens = ['function', 'function-variable', 'method', 'method-variable', 'property-access'];\n\n  for (var i = 0; i < maybeClassNameTokens.length; i++) {\n    var token = maybeClassNameTokens[i];\n    var value = Prism.languages.javascript[token]; // convert regex to object\n\n    if (Prism.util.type(value) === 'RegExp') {\n      value = Prism.languages.javascript[token] = {\n        pattern: value\n      };\n    } // keep in mind that we don't support arrays\n\n\n    var inside = value.inside || {};\n    value.inside = inside;\n    inside['maybe-class-name'] = /^[A-Z][\\s\\S]*/;\n  }\n})(prism);\n/* \"prismjs/components/prism-jsx\" */\n\n\n(function (Prism) {\n  var javascript = Prism.util.clone(Prism.languages.javascript);\n  var space = /(?:\\s|\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))\\*\\/)/.source;\n  var braces = /(?:\\{(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])*\\})/.source;\n  var spread = /(?:\\{<S>*\\.{3}(?:[^{}]|<BRACES>)*\\})/.source;\n  /**\n   * @param {string} source\n   * @param {string} [flags]\n   */\n\n  function re(source, flags) {\n    source = source.replace(/<S>/g, function () {\n      return space;\n    }).replace(/<BRACES>/g, function () {\n      return braces;\n    }).replace(/<SPREAD>/g, function () {\n      return spread;\n    });\n    return RegExp(source, flags);\n  }\n\n  spread = re(spread).source;\n  Prism.languages.jsx = Prism.languages.extend('markup', javascript);\n  Prism.languages.jsx.tag.pattern = re(/<\\/?(?:[\\w.:-]+(?:<S>+(?:[\\w.:$-]+(?:=(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s{'\"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\\/?)?>/.source);\n  Prism.languages.jsx.tag.inside['tag'].pattern = /^<\\/?[^\\s>\\/]*/;\n  Prism.languages.jsx.tag.inside['attr-value'].pattern = /=(?!\\{)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s'\">]+)/;\n  Prism.languages.jsx.tag.inside['tag'].inside['class-name'] = /^[A-Z]\\w*(?:\\.[A-Z]\\w*)*$/;\n  Prism.languages.jsx.tag.inside['comment'] = javascript['comment'];\n  Prism.languages.insertBefore('inside', 'attr-name', {\n    'spread': {\n      pattern: re(/<SPREAD>/.source),\n      inside: Prism.languages.jsx\n    }\n  }, Prism.languages.jsx.tag);\n  Prism.languages.insertBefore('inside', 'special-attr', {\n    'script': {\n      // Allow for two levels of nesting\n      pattern: re(/=<BRACES>/.source),\n      alias: 'language-javascript',\n      inside: {\n        'script-punctuation': {\n          pattern: /^=(?=\\{)/,\n          alias: 'punctuation'\n        },\n        rest: Prism.languages.jsx\n      }\n    }\n  }, Prism.languages.jsx.tag); // The following will handle plain text inside tags\n\n  var stringifyToken = function (token) {\n    if (!token) {\n      return '';\n    }\n\n    if (typeof token === 'string') {\n      return token;\n    }\n\n    if (typeof token.content === 'string') {\n      return token.content;\n    }\n\n    return token.content.map(stringifyToken).join('');\n  };\n\n  var walkTokens = function (tokens) {\n    var openedTags = [];\n\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n      var notTagNorBrace = false;\n\n      if (typeof token !== 'string') {\n        if (token.type === 'tag' && token.content[0] && token.content[0].type === 'tag') {\n          // We found a tag, now find its kind\n          if (token.content[0].content[0].content === '</') {\n            // Closing tag\n            if (openedTags.length > 0 && openedTags[openedTags.length - 1].tagName === stringifyToken(token.content[0].content[1])) {\n              // Pop matching opening tag\n              openedTags.pop();\n            }\n          } else {\n            if (token.content[token.content.length - 1].content === '/>') ; else {\n              // Opening tag\n              openedTags.push({\n                tagName: stringifyToken(token.content[0].content[1]),\n                openedBraces: 0\n              });\n            }\n          }\n        } else if (openedTags.length > 0 && token.type === 'punctuation' && token.content === '{') {\n          // Here we might have entered a JSX context inside a tag\n          openedTags[openedTags.length - 1].openedBraces++;\n        } else if (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces > 0 && token.type === 'punctuation' && token.content === '}') {\n          // Here we might have left a JSX context inside a tag\n          openedTags[openedTags.length - 1].openedBraces--;\n        } else {\n          notTagNorBrace = true;\n        }\n      }\n\n      if (notTagNorBrace || typeof token === 'string') {\n        if (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces === 0) {\n          // Here we are inside a tag, and not inside a JSX context.\n          // That's plain text: drop any tokens matched.\n          var plainText = stringifyToken(token); // And merge text with adjacent text\n\n          if (i < tokens.length - 1 && (typeof tokens[i + 1] === 'string' || tokens[i + 1].type === 'plain-text')) {\n            plainText += stringifyToken(tokens[i + 1]);\n            tokens.splice(i + 1, 1);\n          }\n\n          if (i > 0 && (typeof tokens[i - 1] === 'string' || tokens[i - 1].type === 'plain-text')) {\n            plainText = stringifyToken(tokens[i - 1]) + plainText;\n            tokens.splice(i - 1, 1);\n            i--;\n          }\n\n          tokens[i] = new Prism.Token('plain-text', plainText, null, plainText);\n        }\n      }\n\n      if (token.content && typeof token.content !== 'string') {\n        walkTokens(token.content);\n      }\n    }\n  };\n\n  Prism.hooks.add('after-tokenize', function (env) {\n    if (env.language !== 'jsx' && env.language !== 'tsx') {\n      return;\n    }\n\n    walkTokens(env.tokens);\n  });\n})(prism);\n/* \"prismjs/components/prism-diff\" */\n\n\n(function (Prism) {\n  Prism.languages.diff = {\n    'coord': [// Match all kinds of coord lines (prefixed by \"+++\", \"---\" or \"***\").\n    /^(?:\\*{3}|-{3}|\\+{3}).*$/m, // Match \"@@ ... @@\" coord lines in unified diff.\n    /^@@.*@@$/m, // Match coord lines in normal diff (starts with a number).\n    /^\\d.*$/m] // deleted, inserted, unchanged, diff\n\n  };\n  /**\n   * A map from the name of a block to its line prefix.\n   *\n   * @type {Object<string, string>}\n   */\n\n  var PREFIXES = {\n    'deleted-sign': '-',\n    'deleted-arrow': '<',\n    'inserted-sign': '+',\n    'inserted-arrow': '>',\n    'unchanged': ' ',\n    'diff': '!'\n  }; // add a token for each prefix\n\n  Object.keys(PREFIXES).forEach(function (name) {\n    var prefix = PREFIXES[name];\n    var alias = [];\n\n    if (!/^\\w+$/.test(name)) {\n      // \"deleted-sign\" -> \"deleted\"\n      alias.push(/\\w+/.exec(name)[0]);\n    }\n\n    if (name === 'diff') {\n      alias.push('bold');\n    }\n\n    Prism.languages.diff[name] = {\n      pattern: RegExp('^(?:[' + prefix + '].*(?:\\r\\n?|\\n|(?![\\\\s\\\\S])))+', 'm'),\n      alias: alias,\n      inside: {\n        'line': {\n          pattern: /(.)(?=[\\s\\S]).*(?:\\r\\n?|\\n)?/,\n          lookbehind: true\n        },\n        'prefix': {\n          pattern: /[\\s\\S]/,\n          alias: /\\w+/.exec(name)[0]\n        }\n      }\n    };\n  }); // make prefixes available to Diff plugin\n\n  Object.defineProperty(Prism.languages.diff, 'PREFIXES', {\n    value: PREFIXES\n  });\n})(prism);\n/* \"prismjs/components/prism-git\" */\n\n\nprism.languages.git = {\n  /*\n   * A simple one line comment like in a git status command\n   * For instance:\n   * $ git status\n   * # On branch infinite-scroll\n   * # Your branch and 'origin/sharedBranches/frontendTeam/infinite-scroll' have diverged,\n   * # and have 1 and 2 different commits each, respectively.\n   * nothing to commit (working directory clean)\n   */\n  'comment': /^#.*/m,\n\n  /*\n   * Regexp to match the changed lines in a git diff output. Check the example below.\n   */\n  'deleted': /^[-–].*/m,\n  'inserted': /^\\+.*/m,\n\n  /*\n   * a string (double and simple quote)\n   */\n  'string': /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n\n  /*\n   * a git command. It starts with a random prompt finishing by a $, then \"git\" then some other parameters\n   * For instance:\n   * $ git add file.txt\n   */\n  'command': {\n    pattern: /^.*\\$ git .*$/m,\n    inside: {\n      /*\n       * A git command can contain a parameter starting by a single or a double dash followed by a string\n       * For instance:\n       * $ git diff --cached\n       * $ git log -p\n       */\n      'parameter': /\\s--?\\w+/\n    }\n  },\n\n  /*\n   * Coordinates displayed in a git diff command\n   * For instance:\n   * $ git diff\n   * diff --git file.txt file.txt\n   * index 6214953..1d54a52 100644\n   * --- file.txt\n   * +++ file.txt\n   * @@ -1 +1,2 @@\n   * -Here's my tetx file\n   * +Here's my text file\n   * +And this is the second line\n   */\n  'coord': /^@@.*@@$/m,\n\n  /*\n   * Match a \"commit [SHA1]\" line in a git log output.\n   * For instance:\n   * $ git log\n   * commit a11a14ef7e26f2ca62d4b35eac455ce636d0dc09\n   * Author: lgiraudel\n   * Date:   Mon Feb 17 11:18:34 2014 +0100\n   *\n   *     Add of a new line\n   */\n  'commit-sha1': /^commit \\w{40}$/m\n};\n/* \"prismjs/components/prism-go\" */\n\nprism.languages.go = prism.languages.extend('clike', {\n  'string': {\n    pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`[^`]*`/,\n    lookbehind: true,\n    greedy: true\n  },\n  'keyword': /\\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\\b/,\n  'boolean': /\\b(?:_|false|iota|nil|true)\\b/,\n  'number': [// binary and octal integers\n  /\\b0(?:b[01_]+|o[0-7_]+)i?\\b/i, // hexadecimal integers and floats\n  /\\b0x(?:[a-f\\d_]+(?:\\.[a-f\\d_]*)?|\\.[a-f\\d_]+)(?:p[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i, // decimal integers and floats\n  /(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?[\\d_]+)?i?(?!\\w)/i],\n  'operator': /[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\./,\n  'builtin': /\\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\\b/\n});\nprism.languages.insertBefore('go', 'string', {\n  'char': {\n    pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){0,10}'/,\n    greedy: true\n  }\n});\ndelete prism.languages.go['class-name'];\n/* \"prismjs/components/prism-markup-templating\" */\n\n(function (Prism) {\n  /**\n   * Returns the placeholder for the given language id and index.\n   *\n   * @param {string} language\n   * @param {string|number} index\n   * @returns {string}\n   */\n  function getPlaceholder(language, index) {\n    return '___' + language.toUpperCase() + index + '___';\n  }\n\n  Object.defineProperties(Prism.languages['markup-templating'] = {}, {\n    buildPlaceholders: {\n      /**\n       * Tokenize all inline templating expressions matching `placeholderPattern`.\n       *\n       * If `replaceFilter` is provided, only matches of `placeholderPattern` for which `replaceFilter` returns\n       * `true` will be replaced.\n       *\n       * @param {object} env The environment of the `before-tokenize` hook.\n       * @param {string} language The language id.\n       * @param {RegExp} placeholderPattern The matches of this pattern will be replaced by placeholders.\n       * @param {(match: string) => boolean} [replaceFilter]\n       */\n      value: function (env, language, placeholderPattern, replaceFilter) {\n        if (env.language !== language) {\n          return;\n        }\n\n        var tokenStack = env.tokenStack = [];\n        env.code = env.code.replace(placeholderPattern, function (match) {\n          if (typeof replaceFilter === 'function' && !replaceFilter(match)) {\n            return match;\n          }\n\n          var i = tokenStack.length;\n          var placeholder; // Check for existing strings\n\n          while (env.code.indexOf(placeholder = getPlaceholder(language, i)) !== -1) {\n            ++i;\n          } // Create a sparse array\n\n\n          tokenStack[i] = match;\n          return placeholder;\n        }); // Switch the grammar to markup\n\n        env.grammar = Prism.languages.markup;\n      }\n    },\n    tokenizePlaceholders: {\n      /**\n       * Replace placeholders with proper tokens after tokenizing.\n       *\n       * @param {object} env The environment of the `after-tokenize` hook.\n       * @param {string} language The language id.\n       */\n      value: function (env, language) {\n        if (env.language !== language || !env.tokenStack) {\n          return;\n        } // Switch the grammar back\n\n\n        env.grammar = Prism.languages[language];\n        var j = 0;\n        var keys = Object.keys(env.tokenStack);\n\n        function walkTokens(tokens) {\n          for (var i = 0; i < tokens.length; i++) {\n            // all placeholders are replaced already\n            if (j >= keys.length) {\n              break;\n            }\n\n            var token = tokens[i];\n\n            if (typeof token === 'string' || token.content && typeof token.content === 'string') {\n              var k = keys[j];\n              var t = env.tokenStack[k];\n              var s = typeof token === 'string' ? token : token.content;\n              var placeholder = getPlaceholder(language, k);\n              var index = s.indexOf(placeholder);\n\n              if (index > -1) {\n                ++j;\n                var before = s.substring(0, index);\n                var middle = new Prism.Token(language, Prism.tokenize(t, env.grammar), 'language-' + language, t);\n                var after = s.substring(index + placeholder.length);\n                var replacement = [];\n\n                if (before) {\n                  replacement.push.apply(replacement, walkTokens([before]));\n                }\n\n                replacement.push(middle);\n\n                if (after) {\n                  replacement.push.apply(replacement, walkTokens([after]));\n                }\n\n                if (typeof token === 'string') {\n                  tokens.splice.apply(tokens, [i, 1].concat(replacement));\n                } else {\n                  token.content = replacement;\n                }\n              }\n            } else if (token.content\n            /* && typeof token.content !== 'string' */\n            ) {\n                walkTokens(token.content);\n              }\n          }\n\n          return tokens;\n        }\n\n        walkTokens(env.tokens);\n      }\n    }\n  });\n})(prism);\n/* \"prismjs/components/prism-handlebars\" */\n\n\n(function (Prism) {\n  Prism.languages.handlebars = {\n    'comment': /\\{\\{![\\s\\S]*?\\}\\}/,\n    'delimiter': {\n      pattern: /^\\{\\{\\{?|\\}\\}\\}?$/,\n      alias: 'punctuation'\n    },\n    'string': /([\"'])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    'number': /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][+-]?\\d+)?/,\n    'boolean': /\\b(?:false|true)\\b/,\n    'block': {\n      pattern: /^(\\s*(?:~\\s*)?)[#\\/]\\S+?(?=\\s*(?:~\\s*)?$|\\s)/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    'brackets': {\n      pattern: /\\[[^\\]]+\\]/,\n      inside: {\n        punctuation: /\\[|\\]/,\n        variable: /[\\s\\S]+/\n      }\n    },\n    'punctuation': /[!\"#%&':()*+,.\\/;<=>@\\[\\\\\\]^`{|}~]/,\n    'variable': /[^!\"#%&'()*+,\\/;<=>@\\[\\\\\\]^`{|}~\\s]+/\n  };\n  Prism.hooks.add('before-tokenize', function (env) {\n    var handlebarsPattern = /\\{\\{\\{[\\s\\S]+?\\}\\}\\}|\\{\\{[\\s\\S]+?\\}\\}/g;\n    Prism.languages['markup-templating'].buildPlaceholders(env, 'handlebars', handlebarsPattern);\n  });\n  Prism.hooks.add('after-tokenize', function (env) {\n    Prism.languages['markup-templating'].tokenizePlaceholders(env, 'handlebars');\n  });\n  Prism.languages.hbs = Prism.languages.handlebars;\n})(prism);\n/* \"prismjs/components/prism-json\" */\n// https://www.json.org/json-en.html\n\n\nprism.languages.json = {\n  'property': {\n    pattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n    lookbehind: true,\n    greedy: true\n  },\n  'string': {\n    pattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?!\\s*:)/,\n    lookbehind: true,\n    greedy: true\n  },\n  'comment': {\n    pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    greedy: true\n  },\n  'number': /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n  'punctuation': /[{}[\\],]/,\n  'operator': /:/,\n  'boolean': /\\b(?:false|true)\\b/,\n  'null': {\n    pattern: /\\bnull\\b/,\n    alias: 'keyword'\n  }\n};\nprism.languages.webmanifest = prism.languages.json;\n/* \"prismjs/components/prism-less\" */\n\n/* FIXME :\n :extend() is not handled specifically : its highlighting is buggy.\n Mixin usage must be inside a ruleset to be highlighted.\n At-rules (e.g. import) containing interpolations are buggy.\n Detached rulesets are highlighted as at-rules.\n A comment before a mixin usage prevents the latter to be properly highlighted.\n */\n\nprism.languages.less = prism.languages.extend('css', {\n  'comment': [/\\/\\*[\\s\\S]*?\\*\\//, {\n    pattern: /(^|[^\\\\])\\/\\/.*/,\n    lookbehind: true\n  }],\n  'atrule': {\n    pattern: /@[\\w-](?:\\((?:[^(){}]|\\([^(){}]*\\))*\\)|[^(){};\\s]|\\s+(?!\\s))*?(?=\\s*\\{)/,\n    inside: {\n      'punctuation': /[:()]/\n    }\n  },\n  // selectors and mixins are considered the same\n  'selector': {\n    pattern: /(?:@\\{[\\w-]+\\}|[^{};\\s@])(?:@\\{[\\w-]+\\}|\\((?:[^(){}]|\\([^(){}]*\\))*\\)|[^(){};@\\s]|\\s+(?!\\s))*?(?=\\s*\\{)/,\n    inside: {\n      // mixin parameters\n      'variable': /@+[\\w-]+/\n    }\n  },\n  'property': /(?:@\\{[\\w-]+\\}|[\\w-])+(?:\\+_?)?(?=\\s*:)/,\n  'operator': /[+\\-*\\/]/\n});\nprism.languages.insertBefore('less', 'property', {\n  'variable': [// Variable declaration (the colon must be consumed!)\n  {\n    pattern: /@[\\w-]+\\s*:/,\n    inside: {\n      'punctuation': /:/\n    }\n  }, // Variable usage\n  /@@?[\\w-]+/],\n  'mixin-usage': {\n    pattern: /([{;]\\s*)[.#](?!\\d)[\\w-].*?(?=[(;])/,\n    lookbehind: true,\n    alias: 'function'\n  }\n});\n/* \"prismjs/components/prism-makefile\" */\n\nprism.languages.makefile = {\n  'comment': {\n    pattern: /(^|[^\\\\])#(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n])*/,\n    lookbehind: true\n  },\n  'string': {\n    pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    greedy: true\n  },\n  'builtin-target': {\n    pattern: /\\.[A-Z][^:#=\\s]+(?=\\s*:(?!=))/,\n    alias: 'builtin'\n  },\n  'target': {\n    pattern: /^(?:[^:=\\s]|[ \\t]+(?![\\s:]))+(?=\\s*:(?!=))/m,\n    alias: 'symbol',\n    inside: {\n      'variable': /\\$+(?:(?!\\$)[^(){}:#=\\s]+|(?=[({]))/\n    }\n  },\n  'variable': /\\$+(?:(?!\\$)[^(){}:#=\\s]+|\\([@*%<^+?][DF]\\)|(?=[({]))/,\n  // Directives\n  'keyword': /-include\\b|\\b(?:define|else|endef|endif|export|ifn?def|ifn?eq|include|override|private|sinclude|undefine|unexport|vpath)\\b/,\n  'function': {\n    pattern: /(\\()(?:abspath|addsuffix|and|basename|call|dir|error|eval|file|filter(?:-out)?|findstring|firstword|flavor|foreach|guile|if|info|join|lastword|load|notdir|or|origin|patsubst|realpath|shell|sort|strip|subst|suffix|value|warning|wildcard|word(?:list|s)?)(?=[ \\t])/,\n    lookbehind: true\n  },\n  'operator': /(?:::|[?:+!])?=|[|@]/,\n  'punctuation': /[:;(){}]/\n};\n/* \"prismjs/components/prism-objectivec\" */\n\nprism.languages.objectivec = prism.languages.extend('c', {\n  'string': {\n    pattern: /@?\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n    greedy: true\n  },\n  'keyword': /\\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\\b/,\n  'operator': /-[->]?|\\+\\+?|!=?|<<?=?|>>?=?|==?|&&?|\\|\\|?|[~^%?*\\/@]/\n});\ndelete prism.languages.objectivec['class-name'];\nprism.languages.objc = prism.languages.objectivec;\n/* \"prismjs/components/prism-ocaml\" */\n// https://ocaml.org/manual/lex.html\n\nprism.languages.ocaml = {\n  'comment': {\n    pattern: /\\(\\*[\\s\\S]*?\\*\\)/,\n    greedy: true\n  },\n  'char': {\n    pattern: /'(?:[^\\\\\\r\\n']|\\\\(?:.|[ox]?[0-9a-f]{1,3}))'/i,\n    greedy: true\n  },\n  'string': [{\n    pattern: /\"(?:\\\\(?:[\\s\\S]|\\r\\n)|[^\\\\\\r\\n\"])*\"/,\n    greedy: true\n  }, {\n    pattern: /\\{([a-z_]*)\\|[\\s\\S]*?\\|\\1\\}/,\n    greedy: true\n  }],\n  'number': [// binary and octal\n  /\\b(?:0b[01][01_]*|0o[0-7][0-7_]*)\\b/i, // hexadecimal\n  /\\b0x[a-f0-9][a-f0-9_]*(?:\\.[a-f0-9_]*)?(?:p[+-]?\\d[\\d_]*)?(?!\\w)/i, // decimal\n  /\\b\\d[\\d_]*(?:\\.[\\d_]*)?(?:e[+-]?\\d[\\d_]*)?(?!\\w)/i],\n  'directive': {\n    pattern: /\\B#\\w+/,\n    alias: 'property'\n  },\n  'label': {\n    pattern: /\\B~\\w+/,\n    alias: 'property'\n  },\n  'type-variable': {\n    pattern: /\\B'\\w+/,\n    alias: 'function'\n  },\n  'variant': {\n    pattern: /`\\w+/,\n    alias: 'symbol'\n  },\n  // For the list of keywords and operators,\n  // see: http://caml.inria.fr/pub/docs/manual-ocaml/lex.html#sec84\n  'keyword': /\\b(?:as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|match|method|module|mutable|new|nonrec|object|of|open|private|rec|sig|struct|then|to|try|type|val|value|virtual|when|where|while|with)\\b/,\n  'boolean': /\\b(?:false|true)\\b/,\n  'operator-like-punctuation': {\n    pattern: /\\[[<>|]|[>|]\\]|\\{<|>\\}/,\n    alias: 'punctuation'\n  },\n  // Custom operators are allowed\n  'operator': /\\.[.~]|:[=>]|[=<>@^|&+\\-*\\/$%!?~][!$%&*+\\-.\\/:<=>?@^|~]*|\\b(?:and|asr|land|lor|lsl|lsr|lxor|mod|or)\\b/,\n  'punctuation': /;;|::|[(){}\\[\\].,:;#]|\\b_\\b/\n};\n/* \"prismjs/components/prism-python\" */\n\nprism.languages.python = {\n  'comment': {\n    pattern: /(^|[^\\\\])#.*/,\n    lookbehind: true,\n    greedy: true\n  },\n  'string-interpolation': {\n    pattern: /(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i,\n    greedy: true,\n    inside: {\n      'interpolation': {\n        // \"{\" <expression> <optional \"!s\", \"!r\", or \"!a\"> <optional \":\" format specifier> \"}\"\n        pattern: /((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/,\n        lookbehind: true,\n        inside: {\n          'format-spec': {\n            pattern: /(:)[^:(){}]+(?=\\}$)/,\n            lookbehind: true\n          },\n          'conversion-option': {\n            pattern: /![sra](?=[:}]$)/,\n            alias: 'punctuation'\n          },\n          rest: null\n        }\n      },\n      'string': /[\\s\\S]+/\n    }\n  },\n  'triple-quoted-string': {\n    pattern: /(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i,\n    greedy: true,\n    alias: 'string'\n  },\n  'string': {\n    pattern: /(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i,\n    greedy: true\n  },\n  'function': {\n    pattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g,\n    lookbehind: true\n  },\n  'class-name': {\n    pattern: /(\\bclass\\s+)\\w+/i,\n    lookbehind: true\n  },\n  'decorator': {\n    pattern: /(^[\\t ]*)@\\w+(?:\\.\\w+)*/m,\n    lookbehind: true,\n    alias: ['annotation', 'punctuation'],\n    inside: {\n      'punctuation': /\\./\n    }\n  },\n  'keyword': /\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/,\n  'builtin': /\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/,\n  'boolean': /\\b(?:False|None|True)\\b/,\n  'number': /\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i,\n  'operator': /[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n  'punctuation': /[{}[\\];(),.:]/\n};\nprism.languages.python['string-interpolation'].inside['interpolation'].inside.rest = prism.languages.python;\nprism.languages.py = prism.languages.python;\n/* \"prismjs/components/prism-reason\" */\n\nprism.languages.reason = prism.languages.extend('clike', {\n  'string': {\n    pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n\"])*\"/,\n    greedy: true\n  },\n  // 'class-name' must be matched *after* 'constructor' defined below\n  'class-name': /\\b[A-Z]\\w*/,\n  'keyword': /\\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\\b/,\n  'operator': /\\.{3}|:[:=]|\\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\\-*\\/]\\.?|\\b(?:asr|land|lor|lsl|lsr|lxor|mod)\\b/\n});\nprism.languages.insertBefore('reason', 'class-name', {\n  'char': {\n    pattern: /'(?:\\\\x[\\da-f]{2}|\\\\o[0-3][0-7][0-7]|\\\\\\d{3}|\\\\.|[^'\\\\\\r\\n])'/,\n    greedy: true\n  },\n  // Negative look-ahead prevents from matching things like String.capitalize\n  'constructor': /\\b[A-Z]\\w*\\b(?!\\s*\\.)/,\n  'label': {\n    pattern: /\\b[a-z]\\w*(?=::)/,\n    alias: 'symbol'\n  }\n}); // We can't match functions property, so let's not even try.\n\ndelete prism.languages.reason.function;\n/* \"prismjs/components/prism-sass\" */\n\n(function (Prism) {\n  Prism.languages.sass = Prism.languages.extend('css', {\n    // Sass comments don't need to be closed, only indented\n    'comment': {\n      pattern: /^([ \\t]*)\\/[\\/*].*(?:(?:\\r?\\n|\\r)\\1[ \\t].+)*/m,\n      lookbehind: true,\n      greedy: true\n    }\n  });\n  Prism.languages.insertBefore('sass', 'atrule', {\n    // We want to consume the whole line\n    'atrule-line': {\n      // Includes support for = and + shortcuts\n      pattern: /^(?:[ \\t]*)[@+=].+/m,\n      greedy: true,\n      inside: {\n        'atrule': /(?:@[\\w-]+|[+=])/\n      }\n    }\n  });\n  delete Prism.languages.sass.atrule;\n  var variable = /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/;\n  var operator = [/[+*\\/%]|[=!]=|<=?|>=?|\\b(?:and|not|or)\\b/, {\n    pattern: /(\\s)-(?=\\s)/,\n    lookbehind: true\n  }];\n  Prism.languages.insertBefore('sass', 'property', {\n    // We want to consume the whole line\n    'variable-line': {\n      pattern: /^[ \\t]*\\$.+/m,\n      greedy: true,\n      inside: {\n        'punctuation': /:/,\n        'variable': variable,\n        'operator': operator\n      }\n    },\n    // We want to consume the whole line\n    'property-line': {\n      pattern: /^[ \\t]*(?:[^:\\s]+ *:.*|:[^:\\s].*)/m,\n      greedy: true,\n      inside: {\n        'property': [/[^:\\s]+(?=\\s*:)/, {\n          pattern: /(:)[^:\\s]+/,\n          lookbehind: true\n        }],\n        'punctuation': /:/,\n        'variable': variable,\n        'operator': operator,\n        'important': Prism.languages.sass.important\n      }\n    }\n  });\n  delete Prism.languages.sass.property;\n  delete Prism.languages.sass.important; // Now that whole lines for other patterns are consumed,\n  // what's left should be selectors\n\n  Prism.languages.insertBefore('sass', 'punctuation', {\n    'selector': {\n      pattern: /^([ \\t]*)\\S(?:,[^,\\r\\n]+|[^,\\r\\n]*)(?:,[^,\\r\\n]+)*(?:,(?:\\r?\\n|\\r)\\1[ \\t]+\\S(?:,[^,\\r\\n]+|[^,\\r\\n]*)(?:,[^,\\r\\n]+)*)*/m,\n      lookbehind: true,\n      greedy: true\n    }\n  });\n})(prism);\n/* \"prismjs/components/prism-scss\" */\n\n\nprism.languages.scss = prism.languages.extend('css', {\n  'comment': {\n    pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n    lookbehind: true\n  },\n  'atrule': {\n    pattern: /@[\\w-](?:\\([^()]+\\)|[^()\\s]|\\s+(?!\\s))*?(?=\\s+[{;])/,\n    inside: {\n      'rule': /@[\\w-]+/ // See rest below\n\n    }\n  },\n  // url, compassified\n  'url': /(?:[-a-z]+-)?url(?=\\()/i,\n  // CSS selector regex is not appropriate for Sass\n  // since there can be lot more things (var, @ directive, nesting..)\n  // a selector must start at the end of a property or after a brace (end of other rules or nesting)\n  // it can contain some characters that aren't used for defining rules or end of selector, & (parent selector), or interpolated variable\n  // the end of a selector is found when there is no rules in it ( {} or {\\s}) or if there is a property (because an interpolated var\n  // can \"pass\" as a selector- e.g: proper#{$erty})\n  // this one was hard to do, so please be careful if you edit this one :)\n  'selector': {\n    // Initial look-ahead is used to prevent matching of blank selectors\n    pattern: /(?=\\S)[^@;{}()]?(?:[^@;{}()\\s]|\\s+(?!\\s)|#\\{\\$[-\\w]+\\})+(?=\\s*\\{(?:\\}|\\s|[^}][^:{}]*[:{][^}]))/,\n    inside: {\n      'parent': {\n        pattern: /&/,\n        alias: 'important'\n      },\n      'placeholder': /%[-\\w]+/,\n      'variable': /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n    }\n  },\n  'property': {\n    pattern: /(?:[-\\w]|\\$[-\\w]|#\\{\\$[-\\w]+\\})+(?=\\s*:)/,\n    inside: {\n      'variable': /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n    }\n  }\n});\nprism.languages.insertBefore('scss', 'atrule', {\n  'keyword': [/@(?:content|debug|each|else(?: if)?|extend|for|forward|function|if|import|include|mixin|return|use|warn|while)\\b/i, {\n    pattern: /( )(?:from|through)(?= )/,\n    lookbehind: true\n  }]\n});\nprism.languages.insertBefore('scss', 'important', {\n  // var and interpolated vars\n  'variable': /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n});\nprism.languages.insertBefore('scss', 'function', {\n  'module-modifier': {\n    pattern: /\\b(?:as|hide|show|with)\\b/i,\n    alias: 'keyword'\n  },\n  'placeholder': {\n    pattern: /%[-\\w]+/,\n    alias: 'selector'\n  },\n  'statement': {\n    pattern: /\\B!(?:default|optional)\\b/i,\n    alias: 'keyword'\n  },\n  'boolean': /\\b(?:false|true)\\b/,\n  'null': {\n    pattern: /\\bnull\\b/,\n    alias: 'keyword'\n  },\n  'operator': {\n    pattern: /(\\s)(?:[-+*\\/%]|[=!]=|<=?|>=?|and|not|or)(?=\\s)/,\n    lookbehind: true\n  }\n});\nprism.languages.scss['atrule'].inside.rest = prism.languages.scss;\n/* \"prismjs/components/prism-stylus\" */\n\n(function (Prism) {\n  var unit = {\n    pattern: /(\\b\\d+)(?:%|[a-z]+)/,\n    lookbehind: true\n  }; // 123 -123 .123 -.123 12.3 -12.3\n\n  var number = {\n    pattern: /(^|[^\\w.-])-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/,\n    lookbehind: true\n  };\n  var inside = {\n    'comment': {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n      lookbehind: true\n    },\n    'url': {\n      pattern: /\\burl\\(([\"']?).*?\\1\\)/i,\n      greedy: true\n    },\n    'string': {\n      pattern: /(\"|')(?:(?!\\1)[^\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\\1/,\n      greedy: true\n    },\n    'interpolation': null,\n    // See below\n    'func': null,\n    // See below\n    'important': /\\B!(?:important|optional)\\b/i,\n    'keyword': {\n      pattern: /(^|\\s+)(?:(?:else|for|if|return|unless)(?=\\s|$)|@[\\w-]+)/,\n      lookbehind: true\n    },\n    'hexcode': /#[\\da-f]{3,6}/i,\n    'color': [/\\b(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)\\b/i, {\n      pattern: /\\b(?:hsl|rgb)\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*\\)\\B|\\b(?:hsl|rgb)a\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*,\\s*(?:0|0?\\.\\d+|1)\\s*\\)\\B/i,\n      inside: {\n        'unit': unit,\n        'number': number,\n        'function': /[\\w-]+(?=\\()/,\n        'punctuation': /[(),]/\n      }\n    }],\n    'entity': /\\\\[\\da-f]{1,8}/i,\n    'unit': unit,\n    'boolean': /\\b(?:false|true)\\b/,\n    'operator': [// We want non-word chars around \"-\" because it is\n    // accepted in property names.\n    /~|[+!\\/%<>?=]=?|[-:]=|\\*[*=]?|\\.{2,3}|&&|\\|\\||\\B-\\B|\\b(?:and|in|is(?: a| defined| not|nt)?|not|or)\\b/],\n    'number': number,\n    'punctuation': /[{}()\\[\\];:,]/\n  };\n  inside['interpolation'] = {\n    pattern: /\\{[^\\r\\n}:]+\\}/,\n    alias: 'variable',\n    inside: {\n      'delimiter': {\n        pattern: /^\\{|\\}$/,\n        alias: 'punctuation'\n      },\n      rest: inside\n    }\n  };\n  inside['func'] = {\n    pattern: /[\\w-]+\\([^)]*\\).*/,\n    inside: {\n      'function': /^[^(]+/,\n      rest: inside\n    }\n  };\n  Prism.languages.stylus = {\n    'atrule-declaration': {\n      pattern: /(^[ \\t]*)@.+/m,\n      lookbehind: true,\n      inside: {\n        'atrule': /^@[\\w-]+/,\n        rest: inside\n      }\n    },\n    'variable-declaration': {\n      pattern: /(^[ \\t]*)[\\w$-]+\\s*.?=[ \\t]*(?:\\{[^{}]*\\}|\\S.*|$)/m,\n      lookbehind: true,\n      inside: {\n        'variable': /^\\S+/,\n        rest: inside\n      }\n    },\n    'statement': {\n      pattern: /(^[ \\t]*)(?:else|for|if|return|unless)[ \\t].+/m,\n      lookbehind: true,\n      inside: {\n        'keyword': /^\\S+/,\n        rest: inside\n      }\n    },\n    // A property/value pair cannot end with a comma or a brace\n    // It cannot have indented content unless it ended with a semicolon\n    'property-declaration': {\n      pattern: /((?:^|\\{)([ \\t]*))(?:[\\w-]|\\{[^}\\r\\n]+\\})+(?:\\s*:\\s*|[ \\t]+)(?!\\s)[^{\\r\\n]*(?:;|[^{\\r\\n,]$(?!(?:\\r?\\n|\\r)(?:\\{|\\2[ \\t])))/m,\n      lookbehind: true,\n      inside: {\n        'property': {\n          pattern: /^[^\\s:]+/,\n          inside: {\n            'interpolation': inside.interpolation\n          }\n        },\n        rest: inside\n      }\n    },\n    // A selector can contain parentheses only as part of a pseudo-element\n    // It can span multiple lines.\n    // It must end with a comma or an accolade or have indented content.\n    'selector': {\n      pattern: /(^[ \\t]*)(?:(?=\\S)(?:[^{}\\r\\n:()]|::?[\\w-]+(?:\\([^)\\r\\n]*\\)|(?![\\w-]))|\\{[^}\\r\\n]+\\})+)(?:(?:\\r?\\n|\\r)(?:\\1(?:(?=\\S)(?:[^{}\\r\\n:()]|::?[\\w-]+(?:\\([^)\\r\\n]*\\)|(?![\\w-]))|\\{[^}\\r\\n]+\\})+)))*(?:,$|\\{|(?=(?:\\r?\\n|\\r)(?:\\{|\\1[ \\t])))/m,\n      lookbehind: true,\n      inside: {\n        'interpolation': inside.interpolation,\n        'comment': inside.comment,\n        'punctuation': /[{},]/\n      }\n    },\n    'func': inside.func,\n    'string': inside.string,\n    'comment': {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'interpolation': inside.interpolation,\n    'punctuation': /[{}()\\[\\];:.]/\n  };\n})(prism);\n/* \"prismjs/components/prism-tsx\" */\n\n\n(function (Prism) {\n  var typescript = Prism.util.clone(Prism.languages.typescript);\n  Prism.languages.tsx = Prism.languages.extend('jsx', typescript); // doesn't work with TS because TS is too complex\n\n  delete Prism.languages.tsx['parameter'];\n  delete Prism.languages.tsx['literal-property']; // This will prevent collisions between TSX tags and TS generic types.\n  // Idea by https://github.com/karlhorky\n  // Discussion: https://github.com/PrismJS/prism/issues/2594#issuecomment-710666928\n\n  var tag = Prism.languages.tsx.tag;\n  tag.pattern = RegExp(/(^|[^\\w$]|(?=<\\/))/.source + '(?:' + tag.pattern.source + ')', tag.pattern.flags);\n  tag.lookbehind = true;\n})(prism);\n/* \"prismjs/components/prism-wasm\" */\n\n\nprism.languages.wasm = {\n  'comment': [/\\(;[\\s\\S]*?;\\)/, {\n    pattern: /;;.*/,\n    greedy: true\n  }],\n  'string': {\n    pattern: /\"(?:\\\\[\\s\\S]|[^\"\\\\])*\"/,\n    greedy: true\n  },\n  'keyword': [{\n    pattern: /\\b(?:align|offset)=/,\n    inside: {\n      'operator': /=/\n    }\n  }, {\n    pattern: /\\b(?:(?:f32|f64|i32|i64)(?:\\.(?:abs|add|and|ceil|clz|const|convert_[su]\\/i(?:32|64)|copysign|ctz|demote\\/f64|div(?:_[su])?|eqz?|extend_[su]\\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|neg?|nearest|or|popcnt|promote\\/f32|reinterpret\\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|sqrt|store(?:8|16|32)?|sub|trunc(?:_[su]\\/f(?:32|64))?|wrap\\/i64|xor))?|memory\\.(?:grow|size))\\b/,\n    inside: {\n      'punctuation': /\\./\n    }\n  }, /\\b(?:anyfunc|block|br(?:_if|_table)?|call(?:_indirect)?|data|drop|elem|else|end|export|func|get_(?:global|local)|global|if|import|local|loop|memory|module|mut|nop|offset|param|result|return|select|set_(?:global|local)|start|table|tee_local|then|type|unreachable)\\b/],\n  'variable': /\\$[\\w!#$%&'*+\\-./:<=>?@\\\\^`|~]+/,\n  'number': /[+-]?\\b(?:\\d(?:_?\\d)*(?:\\.\\d(?:_?\\d)*)?(?:[eE][+-]?\\d(?:_?\\d)*)?|0x[\\da-fA-F](?:_?[\\da-fA-F])*(?:\\.[\\da-fA-F](?:_?[\\da-fA-D])*)?(?:[pP][+-]?\\d(?:_?\\d)*)?)\\b|\\binf\\b|\\bnan(?::0x[\\da-fA-F](?:_?[\\da-fA-D])*)?\\b/,\n  'punctuation': /[()]/\n};\n\nexport default prism;\n", "import prism from '../prism/index.js';\nexport { default as Prism } from '../prism/index.js';\nimport theme from '../themes/duotoneDark';\nimport { Component } from 'react';\n\nvar defaultProps = {\n  // $FlowFixMe\n  Prism: prism,\n  theme: theme\n};\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nvar newlineRe = /\\r\\n|\\r|\\n/; // Empty lines need to contain a single empty token, denoted with { empty: true }\n\nvar normalizeEmptyLines = function (line) {\n  if (line.length === 0) {\n    line.push({\n      types: [\"plain\"],\n      content: \"\\n\",\n      empty: true\n    });\n  } else if (line.length === 1 && line[0].content === \"\") {\n    line[0].content = \"\\n\";\n    line[0].empty = true;\n  }\n};\n\nvar appendTypes = function (types, add) {\n  var typesSize = types.length;\n\n  if (typesSize > 0 && types[typesSize - 1] === add) {\n    return types;\n  }\n\n  return types.concat(add);\n}; // Takes an array of Prism's tokens and groups them by line, turning plain\n// strings into tokens as well. Tokens can become recursive in some cases,\n// which means that their types are concatenated. Plain-string tokens however\n// are always of type \"plain\".\n// This is not recursive to avoid exceeding the call-stack limit, since it's unclear\n// how nested Prism's tokens can become\n\n\nvar normalizeTokens = function (tokens) {\n  var typeArrStack = [[]];\n  var tokenArrStack = [tokens];\n  var tokenArrIndexStack = [0];\n  var tokenArrSizeStack = [tokens.length];\n  var i = 0;\n  var stackIndex = 0;\n  var currentLine = [];\n  var acc = [currentLine];\n\n  while (stackIndex > -1) {\n    while ((i = tokenArrIndexStack[stackIndex]++) < tokenArrSizeStack[stackIndex]) {\n      var content = void 0;\n      var types = typeArrStack[stackIndex];\n      var tokenArr = tokenArrStack[stackIndex];\n      var token = tokenArr[i]; // Determine content and append type to types if necessary\n\n      if (typeof token === \"string\") {\n        types = stackIndex > 0 ? types : [\"plain\"];\n        content = token;\n      } else {\n        types = appendTypes(types, token.type);\n\n        if (token.alias) {\n          types = appendTypes(types, token.alias);\n        }\n\n        content = token.content;\n      } // If token.content is an array, increase the stack depth and repeat this while-loop\n\n\n      if (typeof content !== \"string\") {\n        stackIndex++;\n        typeArrStack.push(types);\n        tokenArrStack.push(content);\n        tokenArrIndexStack.push(0);\n        tokenArrSizeStack.push(content.length);\n        continue;\n      } // Split by newlines\n\n\n      var splitByNewlines = content.split(newlineRe);\n      var newlineCount = splitByNewlines.length;\n      currentLine.push({\n        types: types,\n        content: splitByNewlines[0]\n      }); // Create a new line for each string on a new line\n\n      for (var i$1 = 1; i$1 < newlineCount; i$1++) {\n        normalizeEmptyLines(currentLine);\n        acc.push(currentLine = []);\n        currentLine.push({\n          types: types,\n          content: splitByNewlines[i$1]\n        });\n      }\n    } // Decreate the stack depth\n\n\n    stackIndex--;\n    typeArrStack.pop();\n    tokenArrStack.pop();\n    tokenArrIndexStack.pop();\n    tokenArrSizeStack.pop();\n  }\n\n  normalizeEmptyLines(currentLine);\n  return acc;\n};\n\nvar themeToDict = function (theme, language) {\n  var plain = theme.plain; // $FlowFixMe\n\n  var base = Object.create(null);\n  var themeDict = theme.styles.reduce(function (acc, themeEntry) {\n    var languages = themeEntry.languages;\n    var style = themeEntry.style;\n\n    if (languages && !languages.includes(language)) {\n      return acc;\n    }\n\n    themeEntry.types.forEach(function (type) {\n      // $FlowFixMe\n      var accStyle = _extends({}, acc[type], style);\n\n      acc[type] = accStyle;\n    });\n    return acc;\n  }, base); // $FlowFixMe\n\n  themeDict.root = plain; // $FlowFixMe\n\n  themeDict.plain = _extends({}, plain, {\n    backgroundColor: null\n  });\n  return themeDict;\n};\n\nfunction objectWithoutProperties(obj, exclude) {\n  var target = {};\n\n  for (var k in obj) if (Object.prototype.hasOwnProperty.call(obj, k) && exclude.indexOf(k) === -1) target[k] = obj[k];\n\n  return target;\n}\n\nvar Highlight = /*@__PURE__*/function (Component) {\n  function Highlight() {\n    var this$1 = this;\n    var args = [],\n        len = arguments.length;\n\n    while (len--) args[len] = arguments[len];\n\n    Component.apply(this, args);\n\n    _defineProperty(this, \"getThemeDict\", function (props) {\n      if (this$1.themeDict !== undefined && props.theme === this$1.prevTheme && props.language === this$1.prevLanguage) {\n        return this$1.themeDict;\n      }\n\n      this$1.prevTheme = props.theme;\n      this$1.prevLanguage = props.language;\n      var themeDict = props.theme ? themeToDict(props.theme, props.language) : undefined;\n      return this$1.themeDict = themeDict;\n    });\n\n    _defineProperty(this, \"getLineProps\", function (ref) {\n      var key = ref.key;\n      var className = ref.className;\n      var style = ref.style;\n      var rest$1 = objectWithoutProperties(ref, [\"key\", \"className\", \"style\", \"line\"]);\n      var rest = rest$1;\n\n      var output = _extends({}, rest, {\n        className: \"token-line\",\n        style: undefined,\n        key: undefined\n      });\n\n      var themeDict = this$1.getThemeDict(this$1.props);\n\n      if (themeDict !== undefined) {\n        output.style = themeDict.plain;\n      }\n\n      if (style !== undefined) {\n        output.style = output.style !== undefined ? _extends({}, output.style, style) : style;\n      }\n\n      if (key !== undefined) {\n        output.key = key;\n      }\n\n      if (className) {\n        output.className += \" \" + className;\n      }\n\n      return output;\n    });\n\n    _defineProperty(this, \"getStyleForToken\", function (ref) {\n      var types = ref.types;\n      var empty = ref.empty;\n      var typesSize = types.length;\n      var themeDict = this$1.getThemeDict(this$1.props);\n\n      if (themeDict === undefined) {\n        return undefined;\n      } else if (typesSize === 1 && types[0] === \"plain\") {\n        return empty ? {\n          display: \"inline-block\"\n        } : undefined;\n      } else if (typesSize === 1 && !empty) {\n        return themeDict[types[0]];\n      }\n\n      var baseStyle = empty ? {\n        display: \"inline-block\"\n      } : {}; // $FlowFixMe\n\n      var typeStyles = types.map(function (type) {\n        return themeDict[type];\n      });\n      return Object.assign.apply(Object, [baseStyle].concat(typeStyles));\n    });\n\n    _defineProperty(this, \"getTokenProps\", function (ref) {\n      var key = ref.key;\n      var className = ref.className;\n      var style = ref.style;\n      var token = ref.token;\n      var rest$1 = objectWithoutProperties(ref, [\"key\", \"className\", \"style\", \"token\"]);\n      var rest = rest$1;\n\n      var output = _extends({}, rest, {\n        className: \"token \" + token.types.join(\" \"),\n        children: token.content,\n        style: this$1.getStyleForToken(token),\n        key: undefined\n      });\n\n      if (style !== undefined) {\n        output.style = output.style !== undefined ? _extends({}, output.style, style) : style;\n      }\n\n      if (key !== undefined) {\n        output.key = key;\n      }\n\n      if (className) {\n        output.className += \" \" + className;\n      }\n\n      return output;\n    });\n\n    _defineProperty(this, \"tokenize\", function (Prism, code, grammar, language) {\n      var env = {\n        code: code,\n        grammar: grammar,\n        language: language,\n        tokens: []\n      };\n      Prism.hooks.run(\"before-tokenize\", env);\n      var tokens = env.tokens = Prism.tokenize(env.code, env.grammar, env.language);\n      Prism.hooks.run(\"after-tokenize\", env);\n      return tokens;\n    });\n  }\n\n  if (Component) Highlight.__proto__ = Component;\n  Highlight.prototype = Object.create(Component && Component.prototype);\n  Highlight.prototype.constructor = Highlight;\n\n  Highlight.prototype.render = function render() {\n    var ref = this.props;\n    var Prism = ref.Prism;\n    var language = ref.language;\n    var code = ref.code;\n    var children = ref.children;\n    var themeDict = this.getThemeDict(this.props);\n    var grammar = Prism.languages[language];\n    var mixedTokens = grammar !== undefined ? this.tokenize(Prism, code, grammar, language) : [code];\n    var tokens = normalizeTokens(mixedTokens);\n    return children({\n      tokens: tokens,\n      className: \"prism-code language-\" + language,\n      style: themeDict !== undefined ? themeDict.root : {},\n      getLineProps: this.getLineProps,\n      getTokenProps: this.getTokenProps\n    });\n  };\n\n  return Highlight;\n}(Component);\n\nexport default Highlight;\nexport { defaultProps };\n", "/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { useState, useMemo, useEffect } from 'react';\nimport debounce from 'lodash.debounce';\nimport PropTypes from 'prop-types';\n\nfunction useScrollRegion(_ref) {\n  let {\n    containerRef,\n    dependency\n  } = _ref;\n  const [containerTabIndex, setContainerTabIndex] = useState();\n  const updateContainerTabIndex = useMemo(() => debounce(() => {\n    if (containerRef.current) {\n      const regionContent = containerRef.current.children[0];\n      const regionContentHeight = regionContent.scrollHeight;\n      const regionContentWidth = regionContent.scrollWidth;\n      const containerHeight = containerRef.current.offsetHeight;\n      const containerWidth = containerRef.current.offsetWidth;\n      if (regionContentWidth > containerWidth || regionContentHeight > containerHeight) {\n        setContainerTabIndex(0);\n      } else {\n        setContainerTabIndex(undefined);\n      }\n    }\n  }, 100), [containerRef, setContainerTabIndex]);\n  useEffect(() => {\n    addEventListener('resize', updateContainerTabIndex);\n    updateContainerTabIndex();\n    return () => {\n      removeEventListener('resize', updateContainerTabIndex);\n      updateContainerTabIndex.cancel();\n    };\n  }, [updateContainerTabIndex, dependency]);\n  return containerTabIndex;\n}\n\nconst ScrollRegionContainer = _ref => {\n  let {\n    children,\n    render = children,\n    ...props\n  } = _ref;\n  return React.createElement(React.Fragment, null, render(useScrollRegion(props)));\n};\nScrollRegionContainer.propTypes = {\n  children: PropTypes.func,\n  render: PropTypes.func,\n  containerRef: PropTypes.any.isRequired,\n  dependency: PropTypes.any\n};\n\nexport { ScrollRegionContainer, useScrollRegion };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAAA,gBAAwF;AACxF,IAAAC,qBAAsB;;;ACWrB,IAAI,QAAS,WAAY;AAGzB,MAAI,OAAO;AACX,MAAI,WAAW;AAGf,MAAI,mBAAmB,CAAC;AAGxB,MAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUP,MAAM;AAAA,MACL,QAAQ,SAAS,OAAO,QAAQ;AAC/B,YAAI,kBAAkB,OAAO;AAC5B,iBAAO,IAAI,MAAM,OAAO,MAAM,OAAO,OAAO,OAAO,GAAG,OAAO,KAAK;AAAA,QACnE,WAAW,MAAM,QAAQ,MAAM,GAAG;AACjC,iBAAO,OAAO,IAAI,MAAM;AAAA,QACzB,OAAO;AACN,iBAAO,OAAO,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,WAAW,GAAG;AAAA,QAClF;AAAA,MACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBA,MAAM,SAAU,GAAG;AAClB,eAAO,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,MACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,OAAO,SAAU,KAAK;AACrB,YAAI,CAAC,IAAI,MAAM,GAAG;AACjB,iBAAO,eAAe,KAAK,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AAAA,QACzD;AACA,eAAO,IAAI,MAAM;AAAA,MAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYA,OAAO,SAAS,UAAU,GAAG,SAAS;AACrC,kBAAU,WAAW,CAAC;AAEtB,YAAI;AAAO,YAAI;AACf,gBAAQ,EAAE,KAAK,KAAK,CAAC,GAAG;AAAA,UACvB,KAAK;AACJ,iBAAK,EAAE,KAAK,MAAM,CAAC;AACnB,gBAAI,QAAQ,EAAE,GAAG;AAChB,qBAAO,QAAQ,EAAE;AAAA,YAClB;AACA;AAAA,YAA4C,CAAC;AAC7C,oBAAQ,EAAE,IAAI;AAEd,qBAAS,OAAO,GAAG;AAClB,kBAAI,EAAE,eAAe,GAAG,GAAG;AAC1B,sBAAM,GAAG,IAAI,UAAU,EAAE,GAAG,GAAG,OAAO;AAAA,cACvC;AAAA,YACD;AAEA;AAAA;AAAA,cAA2B;AAAA;AAAA,UAE5B,KAAK;AACJ,iBAAK,EAAE,KAAK,MAAM,CAAC;AACnB,gBAAI,QAAQ,EAAE,GAAG;AAChB,qBAAO,QAAQ,EAAE;AAAA,YAClB;AACA,oBAAQ,CAAC;AACT,oBAAQ,EAAE,IAAI;AAEd;AAAA;AAAA,YAAyC,EAAK,QAAQ,SAAU,GAAG,GAAG;AACrE,oBAAM,CAAC,IAAI,UAAU,GAAG,OAAO;AAAA,YAChC,CAAC;AAED;AAAA;AAAA,cAA2B;AAAA;AAAA,UAE5B;AACC,mBAAO;AAAA,QACT;AAAA,MACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,aAAa,SAAU,SAAS;AAC/B,eAAO,SAAS;AACf,cAAI,IAAI,KAAK,KAAK,QAAQ,SAAS;AACnC,cAAI,GAAG;AACN,mBAAO,EAAE,CAAC,EAAE,YAAY;AAAA,UACzB;AACA,oBAAU,QAAQ;AAAA,QACnB;AACA,eAAO;AAAA,MACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,aAAa,SAAU,SAAS,UAAU;AAGzC,gBAAQ,YAAY,QAAQ,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,EAAE;AAIpE,gBAAQ,UAAU,IAAI,cAAc,QAAQ;AAAA,MAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBA,UAAU,SAAU,SAAS,WAAW,mBAAmB;AAC1D,YAAI,KAAK,QAAQ;AAEjB,eAAO,SAAS;AACf,cAAI,YAAY,QAAQ;AACxB,cAAI,UAAU,SAAS,SAAS,GAAG;AAClC,mBAAO;AAAA,UACR;AACA,cAAI,UAAU,SAAS,EAAE,GAAG;AAC3B,mBAAO;AAAA,UACR;AACA,oBAAU,QAAQ;AAAA,QACnB;AACA,eAAO,CAAC,CAAC;AAAA,MACV;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,WAAW;AAAA;AAAA;AAAA;AAAA,MAIV,OAAO;AAAA,MACP,WAAW;AAAA,MACX,MAAM;AAAA,MACN,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8BL,QAAQ,SAAU,IAAI,OAAO;AAC5B,YAAIC,QAAO,EAAE,KAAK,MAAM,EAAE,UAAU,EAAE,CAAC;AAEvC,iBAAS,OAAO,OAAO;AACtB,UAAAA,MAAK,GAAG,IAAI,MAAM,GAAG;AAAA,QACtB;AAEA,eAAOA;AAAA,MACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA6EA,cAAc,SAAU,QAAQ,QAAQ,QAAQ,MAAM;AACrD,eAAO;AAAA,QAA4B,EAAE;AACrC,YAAI,UAAU,KAAK,MAAM;AAEzB,YAAI,MAAM,CAAC;AAEX,iBAAS,SAAS,SAAS;AAC1B,cAAI,QAAQ,eAAe,KAAK,GAAG;AAElC,gBAAI,SAAS,QAAQ;AACpB,uBAAS,YAAY,QAAQ;AAC5B,oBAAI,OAAO,eAAe,QAAQ,GAAG;AACpC,sBAAI,QAAQ,IAAI,OAAO,QAAQ;AAAA,gBAChC;AAAA,cACD;AAAA,YACD;AAGA,gBAAI,CAAC,OAAO,eAAe,KAAK,GAAG;AAClC,kBAAI,KAAK,IAAI,QAAQ,KAAK;AAAA,YAC3B;AAAA,UACD;AAAA,QACD;AAEA,YAAI,MAAM,KAAK,MAAM;AACrB,aAAK,MAAM,IAAI;AAGf,UAAE,UAAU,IAAI,EAAE,WAAW,SAAU,KAAK,OAAO;AAClD,cAAI,UAAU,OAAO,OAAO,QAAQ;AACnC,iBAAK,GAAG,IAAI;AAAA,UACb;AAAA,QACD,CAAC;AAED,eAAO;AAAA,MACR;AAAA;AAAA,MAGA,KAAK,SAAS,IAAI,GAAG,UAAU,MAAM,SAAS;AAC7C,kBAAU,WAAW,CAAC;AAEtB,YAAI,QAAQ,EAAE,KAAK;AAEnB,iBAAS,KAAK,GAAG;AAChB,cAAI,EAAE,eAAe,CAAC,GAAG;AACxB,qBAAS,KAAK,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC;AAEnC,gBAAI,WAAW,EAAE,CAAC;AAClB,gBAAI,eAAe,EAAE,KAAK,KAAK,QAAQ;AAEvC,gBAAI,iBAAiB,YAAY,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG;AAC3D,sBAAQ,MAAM,QAAQ,CAAC,IAAI;AAC3B,kBAAI,UAAU,UAAU,MAAM,OAAO;AAAA,YACtC,WAAW,iBAAiB,WAAW,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG;AACjE,sBAAQ,MAAM,QAAQ,CAAC,IAAI;AAC3B,kBAAI,UAAU,UAAU,GAAG,OAAO;AAAA,YACnC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IAEA,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAuBV,WAAW,SAAU,MAAM,SAAS,UAAU;AAC7C,UAAI,MAAM;AAAA,QACT,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACD;AACA,QAAE,MAAM,IAAI,mBAAmB,GAAG;AAClC,UAAI,SAAS,EAAE,SAAS,IAAI,MAAM,IAAI,OAAO;AAC7C,QAAE,MAAM,IAAI,kBAAkB,GAAG;AACjC,aAAO,MAAM,UAAU,EAAE,KAAK,OAAO,IAAI,MAAM,GAAG,IAAI,QAAQ;AAAA,IAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA0BA,UAAU,SAAU,MAAM,SAAS;AAClC,UAAI,OAAO,QAAQ;AACnB,UAAI,MAAM;AACT,iBAAS,SAAS,MAAM;AACvB,kBAAQ,KAAK,IAAI,KAAK,KAAK;AAAA,QAC5B;AAEA,eAAO,QAAQ;AAAA,MAChB;AAEA,UAAI,YAAY,IAAI,WAAW;AAC/B,eAAS,WAAW,UAAU,MAAM,IAAI;AAExC,mBAAa,MAAM,WAAW,SAAS,UAAU,MAAM,CAAC;AAExD,aAAO,QAAQ,SAAS;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,OAAO;AAAA,MACN,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcN,KAAK,SAAU,MAAM,UAAU;AAC9B,YAAI,QAAQ,EAAE,MAAM;AAEpB,cAAM,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AAE9B,cAAM,IAAI,EAAE,KAAK,QAAQ;AAAA,MAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWA,KAAK,SAAU,MAAM,KAAK;AACzB,YAAI,YAAY,EAAE,MAAM,IAAI,IAAI;AAEhC,YAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AACpC;AAAA,QACD;AAEA,iBAAS,IAAI,GAAG,UAAW,WAAW,UAAU,GAAG,KAAK;AACvD,mBAAS,GAAG;AAAA,QACb;AAAA,MACD;AAAA,IACD;AAAA,IAEA;AAAA,EACD;AAmBA,WAAS,MAAM,MAAM,SAAS,OAAO,YAAY;AAUhD,SAAK,OAAO;AASZ,SAAK,UAAU;AAQf,SAAK,QAAQ;AAEb,SAAK,UAAU,cAAc,IAAI,SAAS;AAAA,EAC3C;AA8BA,QAAM,YAAY,SAAS,UAAU,GAAG,UAAU;AACjD,QAAI,OAAO,KAAK,UAAU;AACzB,aAAO;AAAA,IACR;AACA,QAAI,MAAM,QAAQ,CAAC,GAAG;AACrB,UAAI,IAAI;AACR,QAAE,QAAQ,SAAU,GAAG;AACtB,aAAK,UAAU,GAAG,QAAQ;AAAA,MAC3B,CAAC;AACD,aAAO;AAAA,IACR;AAEA,QAAI,MAAM;AAAA,MACT,MAAM,EAAE;AAAA,MACR,SAAS,UAAU,EAAE,SAAS,QAAQ;AAAA,MACtC,KAAK;AAAA,MACL,SAAS,CAAC,SAAS,EAAE,IAAI;AAAA,MACzB,YAAY,CAAC;AAAA,MACb;AAAA,IACD;AAEA,QAAI,UAAU,EAAE;AAChB,QAAI,SAAS;AACZ,UAAI,MAAM,QAAQ,OAAO,GAAG;AAC3B,cAAM,UAAU,KAAK,MAAM,IAAI,SAAS,OAAO;AAAA,MAChD,OAAO;AACN,YAAI,QAAQ,KAAK,OAAO;AAAA,MACzB;AAAA,IACD;AAEA,MAAE,MAAM,IAAI,QAAQ,GAAG;AAEvB,QAAI,aAAa;AACjB,aAAS,QAAQ,IAAI,YAAY;AAChC,oBAAc,MAAM,OAAO,QAAQ,IAAI,WAAW,IAAI,KAAK,IAAI,QAAQ,MAAM,QAAQ,IAAI;AAAA,IAC1F;AAEA,WAAO,MAAM,IAAI,MAAM,aAAa,IAAI,QAAQ,KAAK,GAAG,IAAI,MAAM,aAAa,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM;AAAA,EACrH;AASA,WAAS,aAAa,SAAS,KAAK,MAAM,YAAY;AACrD,YAAQ,YAAY;AACpB,QAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,QAAI,SAAS,cAAc,MAAM,CAAC,GAAG;AAEpC,UAAI,mBAAmB,MAAM,CAAC,EAAE;AAChC,YAAM,SAAS;AACf,YAAM,CAAC,IAAI,MAAM,CAAC,EAAE,MAAM,gBAAgB;AAAA,IAC3C;AACA,WAAO;AAAA,EACR;AAgBA,WAAS,aAAa,MAAM,WAAW,SAAS,WAAW,UAAU,SAAS;AAC7E,aAAS,SAAS,SAAS;AAC1B,UAAI,CAAC,QAAQ,eAAe,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;AACtD;AAAA,MACD;AAEA,UAAI,WAAW,QAAQ,KAAK;AAC5B,iBAAW,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAEzD,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACzC,YAAI,WAAW,QAAQ,SAAS,QAAQ,MAAM,GAAG;AAChD;AAAA,QACD;AAEA,YAAI,aAAa,SAAS,CAAC;AAC3B,YAAI,SAAS,WAAW;AACxB,YAAI,aAAa,CAAC,CAAC,WAAW;AAC9B,YAAI,SAAS,CAAC,CAAC,WAAW;AAC1B,YAAI,QAAQ,WAAW;AAEvB,YAAI,UAAU,CAAC,WAAW,QAAQ,QAAQ;AAEzC,cAAI,QAAQ,WAAW,QAAQ,SAAS,EAAE,MAAM,WAAW,EAAE,CAAC;AAC9D,qBAAW,UAAU,OAAO,WAAW,QAAQ,QAAQ,QAAQ,GAAG;AAAA,QACnE;AAGA,YAAI,UAAU,WAAW,WAAW;AAEpC,iBACK,cAAc,UAAU,MAAM,MAAM,UACxC,gBAAgB,UAAU,MAC1B,OAAO,YAAY,MAAM,QAAQ,cAAc,YAAY,MAC1D;AAED,cAAI,WAAW,OAAO,QAAQ,OAAO;AACpC;AAAA,UACD;AAEA,cAAI,MAAM,YAAY;AAEtB,cAAI,UAAU,SAAS,KAAK,QAAQ;AAEnC;AAAA,UACD;AAEA,cAAI,eAAe,OAAO;AACzB;AAAA,UACD;AAEA,cAAI,cAAc;AAClB,cAAI;AAEJ,cAAI,QAAQ;AACX,oBAAQ,aAAa,SAAS,KAAK,MAAM,UAAU;AACnD,gBAAI,CAAC,SAAS,MAAM,SAAS,KAAK,QAAQ;AACzC;AAAA,YACD;AAEA,gBAAI,OAAO,MAAM;AACjB,gBAAI,KAAK,MAAM,QAAQ,MAAM,CAAC,EAAE;AAChC,gBAAI,IAAI;AAGR,iBAAK,YAAY,MAAM;AACvB,mBAAO,QAAQ,GAAG;AACjB,4BAAc,YAAY;AAC1B,mBAAK,YAAY,MAAM;AAAA,YACxB;AAEA,iBAAK,YAAY,MAAM;AACvB,kBAAM;AAGN,gBAAI,YAAY,iBAAiB,OAAO;AACvC;AAAA,YACD;AAGA,qBACK,IAAI,aACR,MAAM,UAAU,SAAS,IAAI,MAAM,OAAO,EAAE,UAAU,WACtD,IAAI,EAAE,MACL;AACD;AACA,mBAAK,EAAE,MAAM;AAAA,YACd;AACA;AAGA,kBAAM,KAAK,MAAM,KAAK,CAAC;AACvB,kBAAM,SAAS;AAAA,UAChB,OAAO;AACN,oBAAQ,aAAa,SAAS,GAAG,KAAK,UAAU;AAChD,gBAAI,CAAC,OAAO;AACX;AAAA,YACD;AAAA,UACD;AAGA,cAAI,OAAO,MAAM;AACjB,cAAI,WAAW,MAAM,CAAC;AACtB,cAAI,SAAS,IAAI,MAAM,GAAG,IAAI;AAC9B,cAAI,QAAQ,IAAI,MAAM,OAAO,SAAS,MAAM;AAE5C,cAAI,QAAQ,MAAM,IAAI;AACtB,cAAI,WAAW,QAAQ,QAAQ,OAAO;AACrC,oBAAQ,QAAQ;AAAA,UACjB;AAEA,cAAI,aAAa,YAAY;AAE7B,cAAI,QAAQ;AACX,yBAAa,SAAS,WAAW,YAAY,MAAM;AACnD,mBAAO,OAAO;AAAA,UACf;AAEA,sBAAY,WAAW,YAAY,WAAW;AAE9C,cAAI,UAAU,IAAI,MAAM,OAAO,SAAS,EAAE,SAAS,UAAU,MAAM,IAAI,UAAU,OAAO,QAAQ;AAChG,wBAAc,SAAS,WAAW,YAAY,OAAO;AAErD,cAAI,OAAO;AACV,qBAAS,WAAW,aAAa,KAAK;AAAA,UACvC;AAEA,cAAI,cAAc,GAAG;AAKpB,gBAAI,gBAAgB;AAAA,cACnB,OAAO,QAAQ,MAAM;AAAA,cACrB;AAAA,YACD;AACA,yBAAa,MAAM,WAAW,SAAS,YAAY,MAAM,KAAK,aAAa;AAG3E,gBAAI,WAAW,cAAc,QAAQ,QAAQ,OAAO;AACnD,sBAAQ,QAAQ,cAAc;AAAA,YAC/B;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAeA,WAAS,aAAa;AAErB,QAAI,OAAO,EAAE,OAAO,MAAM,MAAM,MAAM,MAAM,KAAK;AAEjD,QAAI,OAAO,EAAE,OAAO,MAAM,MAAM,MAAM,MAAM,KAAK;AACjD,SAAK,OAAO;AAGZ,SAAK,OAAO;AAEZ,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EACf;AAWA,WAAS,SAAS,MAAM,MAAM,OAAO;AAEpC,QAAI,OAAO,KAAK;AAEhB,QAAI,UAAU,EAAE,OAAc,MAAM,MAAM,KAAW;AACrD,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK;AAEL,WAAO;AAAA,EACR;AASA,WAAS,YAAY,MAAM,MAAM,OAAO;AACvC,QAAI,OAAO,KAAK;AAChB,aAAS,IAAI,GAAG,IAAI,SAAS,SAAS,KAAK,MAAM,KAAK;AACrD,aAAO,KAAK;AAAA,IACb;AACA,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EAChB;AAMA,WAAS,QAAQ,MAAM;AACtB,QAAI,QAAQ,CAAC;AACb,QAAI,OAAO,KAAK,KAAK;AACrB,WAAO,SAAS,KAAK,MAAM;AAC1B,YAAM,KAAK,KAAK,KAAK;AACrB,aAAO,KAAK;AAAA,IACb;AACA,WAAO;AAAA,EACR;AAEA,SAAO;AAER,EAAE;AAEF,IAAI,QAAQ;AACZ,MAAM,UAAU;AAMhB,MAAM,UAAU,SAAS;AAAA,EACvB,WAAW;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,WAAW;AAAA;AAAA,IAET,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,mBAAmB;AAAA,QACjB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA;AAAA,MAEV;AAAA,MACA,UAAU;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,eAAe;AAAA,MACf,eAAe;AAAA,MACf,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,OAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,eAAe;AAAA,UACf,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,gBAAgB,CAAC;AAAA,MACjB,cAAc;AAAA,QACZ,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,eAAe,CAAC;AAAA,YACd,SAAS;AAAA,YACT,OAAO;AAAA,UACT,GAAG,KAAK;AAAA,QACV;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,aAAa;AAAA,QACX,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,aAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,CAAC;AAAA,IACT,SAAS;AAAA,IACT,OAAO;AAAA,EACT,GAAG,oBAAoB;AACzB;AACA,MAAM,UAAU,OAAO,KAAK,EAAE,OAAO,YAAY,EAAE,OAAO,QAAQ,IAAI,MAAM,UAAU,OAAO,QAAQ;AACrG,MAAM,UAAU,OAAO,SAAS,EAAE,OAAO,iBAAiB,EAAE,SAAS,MAAM,UAAU;AAErF,MAAM,MAAM,IAAI,QAAQ,SAAU,KAAK;AACrC,MAAI,IAAI,SAAS,UAAU;AACzB,QAAI,WAAW,OAAO,IAAI,IAAI,QAAQ,QAAQ,SAAS,GAAG;AAAA,EAC5D;AACF,CAAC;AACD,OAAO,eAAe,MAAM,UAAU,OAAO,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY9D,OAAO,SAAS,WAAW,SAAS,MAAM;AACxC,QAAI,sBAAsB,CAAC;AAC3B,wBAAoB,cAAc,IAAI,IAAI;AAAA,MACxC,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ,MAAM,UAAU,IAAI;AAAA,IAC9B;AACA,wBAAoB,OAAO,IAAI;AAC/B,QAAI,SAAS;AAAA,MACX,kBAAkB;AAAA,QAChB,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,IACF;AACA,WAAO,cAAc,IAAI,IAAI;AAAA,MAC3B,SAAS;AAAA,MACT,QAAQ,MAAM,UAAU,IAAI;AAAA,IAC9B;AACA,QAAI,MAAM,CAAC;AACX,QAAI,OAAO,IAAI;AAAA,MACb,SAAS,OAAO,wFAAwF,OAAO,QAAQ,OAAO,WAAY;AACxI,eAAO;AAAA,MACT,CAAC,GAAG,GAAG;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR;AAAA,IACF;AACA,UAAM,UAAU,aAAa,UAAU,SAAS,GAAG;AAAA,EACrD;AACF,CAAC;AACD,OAAO,eAAe,MAAM,UAAU,OAAO,KAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYhE,OAAO,SAAU,UAAU,MAAM;AAC/B,UAAM,UAAU,OAAO,IAAI,OAAO,cAAc,EAAE,KAAK;AAAA,MACrD,SAAS,OAAO,aAAa,SAAS,QAAQ,WAAW,MAAM,iDAAiD,QAAQ,GAAG;AAAA,MAC3H,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO,CAAC,MAAM,cAAc,IAAI;AAAA,cAChC,QAAQ,MAAM,UAAU,IAAI;AAAA,YAC9B;AAAA,YACA,eAAe,CAAC;AAAA,cACd,SAAS;AAAA,cACT,OAAO;AAAA,YACT,GAAG,KAAK;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF,CAAC;AACD,MAAM,UAAU,OAAO,MAAM,UAAU;AACvC,MAAM,UAAU,SAAS,MAAM,UAAU;AACzC,MAAM,UAAU,MAAM,MAAM,UAAU;AACtC,MAAM,UAAU,MAAM,MAAM,UAAU,OAAO,UAAU,CAAC,CAAC;AACzD,MAAM,UAAU,OAAO,MAAM,UAAU;AACvC,MAAM,UAAU,OAAO,MAAM,UAAU;AACvC,MAAM,UAAU,MAAM,MAAM,UAAU;AAAA,CAGrC,SAAUC,QAAO;AAKhB,MAAI,UAAU;AACd,MAAI,sBAAsB;AAAA,IACxB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA;AAAA,IAEP,QAAQ;AAAA;AAAA,EAEV;AACA,MAAI,eAAe;AAAA,IACjB,QAAQ;AAAA,IACR,eAAe;AAAA,MACb,SAAS,OAAO,QAAQ,OAAO;AAAA,MAC/B,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA;AAAA,UAEN,YAAY,CAAC;AAAA,YACX,SAAS;AAAA,YACT,YAAY;AAAA,UACd,GAAG,SAAS;AAAA,UACZ,UAAU;AAAA;AAAA,UAEV,YAAY;AAAA;AAAA,UAEZ,eAAe;AAAA,QACjB;AAAA,MACF;AAAA;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,YAAY;AAAA,QACd;AAAA,MACF;AAAA;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,eAAe;AAAA,YACb,SAAS,OAAO,UAAU,OAAO;AAAA,YACjC,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,MAAG;AAAA,IAAoB;AAAA;AAAA,IAEvB,UAAU;AAAA,EACZ;AACA,EAAAA,OAAM,UAAU,OAAO;AAAA,IACrB,WAAW;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,MAIjB;AAAA;AAAA,QAEE,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,MACT;AAAA,MAAG;AAAA;AAAA,QAED,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IAAC;AAAA;AAAA,IAED,iBAAiB;AAAA,MACf,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA;AAAA;AAAA,IAGA,eAAe;AAAA,MACb,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,eAAe;AAAA,UACb,SAAS,OAAO,yBAAyB,OAAO;AAAA,UAChD,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA,UAAU;AAAA;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA;AAAA;AAAA,MAEA;AAAA,QACE,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF;AAAA;AAAA,MACA;AAAA;AAAA,QAEE,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MAAG;AAAA;AAAA,QAED,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV;AAAA,MAAG;AAAA;AAAA,QAED,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,UAAU,aAAa;AAAA,QACzB;AAAA,MACF;AAAA,IAAC;AAAA,IACD,eAAe;AAAA,MACb,SAAS,OAAO,SAAS,OAAO;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,IACA,YAAY,aAAa;AAAA,IACzB,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA;AAAA,IAEA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA;AAAA,MAEZ,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,mBAAmB;AAAA,MACjB,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA;AAAA,MAEV,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,mBAAmB;AAAA,UACjB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,UAAU;AAAA,MACR,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,EACF;AACA,sBAAoB,SAASA,OAAM,UAAU;AAG7C,MAAI,aAAa,CAAC,WAAW,iBAAiB,iBAAiB,eAAe,UAAU,eAAe,YAAY,WAAW,WAAW,WAAW,mBAAmB,YAAY,eAAe,QAAQ;AAC1M,MAAI,SAAS,aAAa,SAAS,CAAC,EAAE;AAEtC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,WAAO,WAAW,CAAC,CAAC,IAAIA,OAAM,UAAU,KAAK,WAAW,CAAC,CAAC;AAAA,EAC5D;AAEA,EAAAA,OAAM,UAAU,QAAQA,OAAM,UAAU;AAC1C,GAAG,KAAK;AAIR,MAAM,UAAU,QAAQ;AAAA,EACtB,WAAW,CAAC;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV,CAAC;AAAA,EACD,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,MACN,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AACjB;AAGA,MAAM,UAAU,IAAI,MAAM,UAAU,OAAO,SAAS;AAAA,EAClD,WAAW;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,UAAU;AAAA;AAAA,IAER,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,YAAY;AACd,CAAC;AACD,MAAM,UAAU,aAAa,KAAK,UAAU;AAAA,EAC1C,QAAQ;AAAA;AAAA,IAEN,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AACF,CAAC;AACD,MAAM,UAAU,aAAa,KAAK,UAAU;AAAA,EAC1C,SAAS;AAAA;AAAA;AAAA,IAGP,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,MACN,UAAU,CAAC;AAAA;AAAA,QAET,SAAS;AAAA,QACT,YAAY;AAAA,MACd,GAAG,MAAM,UAAU,EAAE,QAAQ,CAAC;AAAA,MAC9B,QAAQ,MAAM,UAAU,EAAE,MAAM;AAAA,MAChC,WAAW,MAAM,UAAU,EAAE,SAAS;AAAA,MACtC,cAAc,CAAC;AAAA,QACb,SAAS;AAAA,QACT,YAAY;AAAA,MACd,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,MACT,CAAC;AAAA;AAAA,MAED,aAAa;AAAA,QACX,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,cAAc;AAAA,QACZ,SAAS;AAAA,QACT,QAAQ,MAAM,UAAU;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,MAAM,UAAU,aAAa,KAAK,YAAY;AAAA;AAAA,EAE5C,YAAY;AACd,CAAC;AACD,OAAO,MAAM,UAAU,EAAE,SAAS;AAAA,CAGjC,SAAUA,QAAO;AAChB,MAAI,UAAU;AACd,MAAI,UAAU,uCAAuC,OAAO,QAAQ,cAAc,WAAY;AAC5F,WAAO,QAAQ;AAAA,EACjB,CAAC;AACD,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,KAAK;AAAA,IAChD,cAAc;AAAA,MAAC;AAAA,QACb,SAAS,OAAO,gEAAgE,OAAO,QAAQ,cAAc,WAAY;AACvH,iBAAO,QAAQ;AAAA,QACjB,CAAC,CAAC;AAAA,QACF,YAAY;AAAA,MACd;AAAA;AAAA;AAAA;AAAA;AAAA,MAIA;AAAA;AAAA;AAAA,MAEA;AAAA;AAAA;AAAA,MAEA;AAAA,IAAgE;AAAA,IAChE,WAAW;AAAA,IACX,UAAU;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,IACZ,WAAW;AAAA,EACb,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,OAAO,UAAU;AAAA,IAC5C,UAAU;AAAA;AAAA,MAER,SAAS,OAAO,2BAA2B,SAAS;AAAA,MACpD,mDAAmD,SAAS;AAAA,MAC5D,kDAAkD,OAAO,QAAQ,eAAe,WAAY;AAC1F,eAAO;AAAA,MACT,CAAC,IAAI,GAAG;AAAA,MACR,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,OAAO,WAAW;AAAA,IAC7C,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQA,OAAM,UAAU;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,IAC9C,gBAAgB;AAAA,MACd,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,OAAO,cAAc;AAAA;AAAA;AAAA,IAGhD,eAAe;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQA,OAAM,UAAU,OAAO,OAAO,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,UAAU,gBAAgB;AAAA;AAAA,IAErD,cAAc;AAAA,EAChB,GAAGA,OAAM,UAAU,IAAI,aAAa,CAAC;AACvC,GAAG,KAAK;AAAA,CAIP,SAAUA,QAAO;AAChB,MAAI,SAAS;AACb,EAAAA,OAAM,UAAU,MAAM;AAAA,IACpB,WAAW;AAAA,IACX,UAAU;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,8BAA8B;AAAA,UAC5B,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA;AAAA,MAEF;AAAA,IACF;AAAA,IACA,OAAO;AAAA;AAAA,MAEL,SAAS,OAAO,iBAAiB,OAAO,SAAS,MAAM,8BAA8B,SAAS,QAAQ,GAAG;AAAA,MACzG,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,UAAU;AAAA,UACR,SAAS,OAAO,MAAM,OAAO,SAAS,GAAG;AAAA,UACzC,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV,SAAS,OAAO,sDAAuD,OAAO,SAAS,eAAe;AAAA,MACtG,YAAY;AAAA,IACd;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,aAAa;AAAA,IACb,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,eAAe;AAAA,EACjB;AACA,EAAAA,OAAM,UAAU,IAAI,QAAQ,EAAE,OAAO,OAAOA,OAAM,UAAU;AAC5D,MAAI,SAASA,OAAM,UAAU;AAE7B,MAAI,QAAQ;AACV,WAAO,IAAI,WAAW,SAAS,KAAK;AACpC,WAAO,IAAI,aAAa,SAAS,KAAK;AAAA,EACxC;AACF,GAAG,KAAK;AAAA,CAIP,SAAUA,QAAO;AAChB,MAAI,SAAS;AACb,MAAI;AACJ,EAAAA,OAAM,UAAU,IAAI,WAAW;AAAA,IAC7B,SAASA,OAAM,UAAU,IAAI,SAAS;AAAA,IACtC,YAAY;AAAA,IACZ,QAAQ,iBAAiB;AAAA,MACvB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,aAAa;AAAA,QACX,SAAS,OAAO,qBAAsB,OAAO,SAAS,OAAO;AAAA,QAC7D,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,eAAe;AAAA,UACf,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,aAAa;AAAA,YACX,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,eAAe;AAAA,YACjB;AAAA,UACF;AAAA,UACA,aAAa;AAAA,YACX,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,cAAc,CAAC,QAAQ;AAAA,YACrB,SAAS;AAAA,YACT,YAAY;AAAA,UACd,CAAC;AAAA,UACD,YAAY;AAAA,QACd;AAAA,MACF;AAAA,MACA,QAAQ,CAAC;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,UACN,UAAU;AAAA,UACV,YAAY;AAAA,QACd;AAAA,MACF,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,MACD,cAAc;AAAA;AAAA;AAAA;AAAA,MAId,eAAe;AAAA,IACjB;AAAA,EACF;AACA,EAAAA,OAAM,UAAU,IAAI,QAAQ,EAAE,OAAO,4BAA4B,EAAE,SAAS;AAC5E,EAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,IAC9C,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,EACF,CAAC;AACD,MAAI,OAAO;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAEA,MAAI,SAAS;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AACA,EAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,IAC9C,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA;AAAA;AAAA,IAGA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,SAAS,CAAC;AAAA,MACR,SAAS;AAAA,MACT,YAAY;AAAA,IACd,GAAG;AAAA,MACD,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAAA;AAAA,IAED,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,CAAC;AACH,GAAG,KAAK;AAIR,MAAM,UAAU,aAAa,MAAM,UAAU,OAAO,SAAS;AAAA,EAC3D,cAAc,CAAC,MAAM,UAAU,MAAM,YAAY,GAAG;AAAA,IAClD,SAAS;AAAA,IACT,YAAY;AAAA,EACd,CAAC;AAAA,EACD,WAAW,CAAC;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,EACd,CAAC;AAAA;AAAA,EAED,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,SAAS,OAAO,aAAa,SAAS;AAAA,KACtC,eAAe,SAAS;AAAA,IACxB,0BAA0B,SAAS;AAAA,IACnC,4BAA4B,SAAS;AAAA,IACrC,sCAAsC,SAAS;AAAA,IAC/C,gBAAgB,SAAS;AAAA,IACzB,oFAAoF,UAAU,MAAM,YAAY,MAAM;AAAA,IACtH,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AACd,CAAC;AACD,MAAM,UAAU,WAAW,YAAY,EAAE,CAAC,EAAE,UAAU;AACtD,MAAM,UAAU,aAAa,cAAc,WAAW;AAAA,EACpD,SAAS;AAAA;AAAA,IAEP,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,gBAAgB;AAAA,QACd,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ,MAAM,UAAU;AAAA,MAC1B;AAAA,MACA,mBAAmB;AAAA,MACnB,eAAe;AAAA,IACjB;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AAAA,IACnB,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa,CAAC;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ,MAAM,UAAU;AAAA,EAC1B,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ,MAAM,UAAU;AAAA,EAC1B,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ,MAAM,UAAU;AAAA,EAC1B,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ,MAAM,UAAU;AAAA,EAC1B,CAAC;AAAA,EACD,YAAY;AACd,CAAC;AACD,MAAM,UAAU,aAAa,cAAc,UAAU;AAAA,EACnD,YAAY;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,IACjB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,wBAAwB;AAAA,QACtB,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,UACN,6BAA6B;AAAA,YAC3B,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,MAAM,MAAM,UAAU;AAAA,QACxB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,mBAAmB;AAAA,IACjB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AACF,CAAC;AACD,MAAM,UAAU,aAAa,cAAc,YAAY;AAAA,EACrD,oBAAoB;AAAA,IAClB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA,EACT;AACF,CAAC;AAED,IAAI,MAAM,UAAU,QAAQ;AAC1B,QAAM,UAAU,OAAO,IAAI,WAAW,UAAU,YAAY;AAG5D,QAAM,UAAU,OAAO,IAAI,aAAa,yNAAyN,QAAQ,YAAY;AACvR;AAEA,MAAM,UAAU,KAAK,MAAM,UAAU;AAAA,CAGpC,SAAUA,QAAO;AAEhB,MAAI,UAAU;AACd,MAAI,gBAAgB;AAAA,IAClB,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACA,EAAAA,OAAM,UAAU,eAAeA,OAAM,UAAU,OAAO,cAAc;AAAA,IAClE,WAAW;AAAA,IACX,UAAU;AAAA;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MAAG;AAAA;AAAA,QAED,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,iBAAiB;AAAA,QACnB;AAAA,MACF;AAAA,IAAC;AAAA,IACD,WAAW;AAAA,IACX,gBAAgB;AAAA,MACd,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,gBAAgB,WAAW;AAAA,IACtD,qBAAqB;AAAA,MACnB,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA;AAAA,IAEA,eAAe;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,iBAAiB;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,gBAAgB,UAAU;AAAA,IACrD,qBAAqB;AAAA,MACnB,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,aAAa;AAAA,UACX,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQA,OAAM,UAAU;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,oBAAoB,CAAC;AAAA,MACnB,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,IACT,GAAG;AAAA,MACD,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,gBAAgB,WAAW;AAAA;AAAA,IAEtD,YAAY;AAAA,EACd,CAAC;AACD,SAAOA,OAAM,UAAU,aAAa,iBAAiB;AACrD,EAAAA,OAAM,UAAU,SAASA,OAAM,UAAU;AAC3C,GAAG,KAAK;AAAA,CAIP,SAAUA,QAAO;AAGhB,MAAI,gBAAgB;AAEpB,MAAI,MAAM;AAEV,MAAI,aAAa,QAAQ,IAAI,SAAS,aAAc,cAAc,SAAS,QAAQ,cAAc,SAAS,aAAc,IAAI,SAAS;AAIrI,MAAI,WAAW,kJAAkJ,OAAO,QAAQ,YAAY,WAAY;AACtM,WAAO,2EAA2E;AAAA,EACpF,CAAC;AACD,MAAI,SAAS,8CAA8C;AAQ3D,WAAS,mBAAmB,OAAO,OAAO;AACxC,aAAS,SAAS,IAAI,QAAQ,MAAM,EAAE,IAAI;AAE1C,QAAI,UAAU,yFAAyF,OAAO,QAAQ,aAAa,WAAY;AAC7I,aAAO;AAAA,IACT,CAAC,EAAE,QAAQ,cAAc,WAAY;AACnC,aAAO;AAAA,IACT,CAAC;AACD,WAAO,OAAO,SAAS,KAAK;AAAA,EAC9B;AAEA,EAAAA,OAAM,UAAU,OAAO;AAAA,IACrB,UAAU;AAAA,MACR,SAAS,OAAO,6FAA6F,OAAO,QAAQ,aAAa,WAAY;AACnJ,eAAO;AAAA,MACT,CAAC,CAAC;AAAA,MACF,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,IACX,OAAO;AAAA,MACL,SAAS,OAAO,kEAAkE,OAAO,QAAQ,aAAa,WAAY;AACxH,eAAO;AAAA,MACT,CAAC,EAAE,QAAQ,YAAY,WAAY;AACjC,eAAO,QAAQ,WAAW,MAAM,SAAS;AAAA,MAC3C,CAAC,CAAC;AAAA,MACF,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,SAAS,mBAAmB,sJAAsJ,MAAM;AAAA,MACxL,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,SAAS,mBAAmB,aAAa,QAAQ,GAAG;AAAA,MACpD,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,SAAS,mBAAmB,SAAS,QAAQ,GAAG;AAAA,MAChD,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,SAAS,mBAAmB,MAAM;AAAA,MAClC,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,IACA,UAAU;AAAA,MACR,SAAS,mBAAmB,iFAAiF,QAAQ,GAAG;AAAA,MACxH,YAAY;AAAA,IACd;AAAA,IACA,OAAO;AAAA,IACP,aAAa;AAAA,IACb,eAAe;AAAA,EACjB;AACA,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AACxC,GAAG,KAAK;AAAA,CAIP,SAAUA,QAAO;AAEhB,MAAI,QAAQ,2CAA2C;AAYvD,WAAS,aAAa,SAAS;AAC7B,cAAU,QAAQ,QAAQ,YAAY,WAAY;AAChD,aAAO;AAAA,IACT,CAAC;AACD,WAAO,OAAO,0BAA0B,SAAS,QAAQ,UAAU,GAAG;AAAA,EACxE;AAEA,MAAI,YAAY,4DAA4D;AAC5E,MAAI,WAAW,+CAA+C,OAAO,QAAQ,OAAO,WAAY;AAC9F,WAAO;AAAA,EACT,CAAC;AACD,MAAI,YAAY,sEAAsE;AACtF,EAAAA,OAAM,UAAU,WAAWA,OAAM,UAAU,OAAO,UAAU,CAAC,CAAC;AAC9D,EAAAA,OAAM,UAAU,aAAa,YAAY,UAAU;AAAA,IACjD,sBAAsB;AAAA,MACpB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,eAAe;AAAA,QACf,gBAAgB;AAAA,UACd,SAAS;AAAA,UACT,OAAO,CAAC,QAAQ,eAAe;AAAA,UAC/B,QAAQA,OAAM,UAAU;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAAA,IACA,cAAc;AAAA;AAAA,MAEZ,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS,OAAO,MAAM,WAAW,YAAY,QAAQ,WAAW,MAAM,GAAG;AAAA,MACzE,QAAQ;AAAA,QACN,mBAAmB;AAAA,UACjB,SAAS,OAAO,OAAO,WAAW,YAAY,SAAS,WAAW,KAAK;AAAA,UACvE,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,cAAc;AAAA,cACZ,SAAS,OAAO,SAAS;AAAA,cACzB,QAAQA,OAAM,UAAU;AAAA,YAC1B;AAAA,YACA,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,QACA,cAAc;AAAA,UACZ,SAAS,OAAO,OAAO,WAAW,MAAM,YAAY,GAAG;AAAA,UACvD,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,QACA,oBAAoB;AAAA,UAClB,SAAS,OAAO,MAAM,WAAW,GAAG;AAAA,UACpC,QAAQ;AAAA,YACN,gBAAgB;AAAA,cACd,SAAS,OAAO,SAAS;AAAA,cACzB,OAAO;AAAA,cACP,QAAQA,OAAM,UAAU;AAAA,YAC1B;AAAA,YACA,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ,CAAC;AAAA;AAAA,MAEP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACT,GAAG;AAAA;AAAA;AAAA;AAAA,MAID,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,IACD,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKR,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,aAAa;AAAA,MACf;AAAA,IACF,GAAG;AAAA;AAAA;AAAA,MAGD,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,aAAa;AAAA,MACf;AAAA,IACF,CAAC;AAAA,IACD,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,MAKJ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAKN,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKf,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,YAAY;AAAA,UACV,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,QACV,eAAe;AAAA,MACjB;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIN,SAAS,aAAa,kGAAkG,MAAM;AAAA,MAC9H,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,WAAW;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ,CAAC;AAAA;AAAA,QAEX;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,IACA,UAAU;AAAA;AAAA;AAAA;AAAA,MAIR,SAAS,aAAa,kGAAkG,MAAM;AAAA,MAC9H,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,WAAW;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ,CAAC;AAAA;AAAA,QAEX;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,IACA,UAAU;AAAA;AAAA;AAAA;AAAA,MAIR,SAAS,aAAa,2BAA2B,MAAM;AAAA,MACvD,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,WAAW;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ,CAAC;AAAA;AAAA,QAEX;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA;AAAA;AAAA,MAGd,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO,CAAC,QAAQ,SAAS;AAAA,IAC3B;AAAA,IACA,OAAO;AAAA;AAAA;AAAA;AAAA,MAIL,SAAS,aAAa,mGAAmG,MAAM;AAAA,MAC/H,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,WAAW;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ,CAAC;AAAA;AAAA,QAEX;AAAA,QACA,YAAY;AAAA,UACV,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,GAAC,OAAO,QAAQ,UAAU,QAAQ,EAAE,QAAQ,SAAU,OAAO;AAC3D,KAAC,OAAO,QAAQ,UAAU,UAAU,cAAc,EAAE,QAAQ,SAAU,QAAQ;AAC5E,UAAI,UAAU,QAAQ;AACpB,QAAAA,OAAM,UAAU,SAAS,KAAK,EAAE,OAAO,QAAQ,OAAO,MAAM,IAAIA,OAAM,UAAU,SAAS,MAAM;AAAA,MACjG;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,EAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,QAAI,IAAI,aAAa,cAAc,IAAI,aAAa,MAAM;AACxD;AAAA,IACF;AAEA,aAAS,WAAW,QAAQ;AAC1B,UAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC;AAAA,MACF;AAEA,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,YAAI,QAAQ,OAAO,CAAC;AAEpB,YAAI,MAAM,SAAS,QAAQ;AACzB,qBAAW,MAAM,OAAO;AACxB;AAAA,QACF;AAgBA,YAAI,WAAW,MAAM,QAAQ,CAAC;AAC9B,YAAI,YAAY,MAAM,QAAQ,CAAC;AAE/B,YAAI,YAAY,aAAa,SAAS,SAAS,mBAAmB,UAAU,SAAS,gBAAgB,OAAO,SAAS,YAAY,UAAU;AAGzI,cAAI,OAAO,SAAS,QAAQ,QAAQ,QAAQ,OAAO,EAAE,QAAQ,WAAW,IAAI;AAE5E,kBAAQ,eAAe,KAAK,IAAI,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,YAAY;AAC1D,cAAI,QAAQ,cAAc;AAE1B,cAAI,CAAC,UAAU,OAAO;AACpB,sBAAU,QAAQ,CAAC,KAAK;AAAA,UAC1B,WAAW,OAAO,UAAU,UAAU,UAAU;AAC9C,sBAAU,QAAQ,CAAC,UAAU,OAAO,KAAK;AAAA,UAC3C,OAAO;AACL,sBAAU,MAAM,KAAK,KAAK;AAAA,UAC5B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,eAAW,IAAI,MAAM;AAAA,EACvB,CAAC;AACD,EAAAA,OAAM,MAAM,IAAI,QAAQ,SAAU,KAAK;AACrC,QAAI,IAAI,SAAS,cAAc;AAC7B;AAAA,IACF;AAEA,QAAI,WAAW;AAEf,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAClD,UAAI,MAAM,IAAI,QAAQ,CAAC;AACvB,UAAI,QAAQ,gBAAgB,KAAK,GAAG;AAEpC,UAAI,OAAO;AACT,mBAAW,MAAM,CAAC;AAClB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,UAAUA,OAAM,UAAU,QAAQ;AAEtC,QAAI,CAAC,SAAS;AACZ,UAAI,YAAY,aAAa,UAAUA,OAAM,QAAQ,YAAY;AAC/D,YAAI,KAAK,SAAQ,oBAAI,KAAK,GAAE,QAAQ,IAAI,MAAM,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI;AAC7E,YAAI,WAAW,IAAI,IAAI;AACvB,QAAAA,OAAM,QAAQ,WAAW,cAAc,UAAU,WAAY;AAC3D,cAAI,MAAM,SAAS,eAAe,EAAE;AAEpC,cAAI,KAAK;AACP,gBAAI,YAAYA,OAAM,UAAU,IAAI,aAAaA,OAAM,UAAU,QAAQ,GAAG,QAAQ;AAAA,UACtF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,UAAUA,OAAM,UAAU,YAAY,IAAI,OAAO,GAAG,SAAS,QAAQ;AAAA,IAC3E;AAAA,EACF,CAAC;AACD,MAAI,aAAa,OAAOA,OAAM,UAAU,OAAO,IAAI,QAAQ,QAAQ,IAAI;AASvE,MAAI,qBAAqB;AAAA,IACvB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,EACV;AAEA,MAAI,gBAAgB,OAAO,iBAAiB,OAAO;AAQnD,WAAS,YAAY,MAAM;AAEzB,QAAI,OAAO,KAAK,QAAQ,YAAY,EAAE;AAEtC,WAAO,KAAK,QAAQ,iCAAiC,SAAU,GAAG,MAAM;AACtE,aAAO,KAAK,YAAY;AAExB,UAAI,KAAK,CAAC,MAAM,KAAK;AACnB,YAAI;AAEJ,YAAI,KAAK,CAAC,MAAM,KAAK;AACnB,kBAAQ,SAAS,KAAK,MAAM,CAAC,GAAG,EAAE;AAAA,QACpC,OAAO;AACL,kBAAQ,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,QAC9B;AAEA,eAAO,cAAc,KAAK;AAAA,MAC5B,OAAO;AACL,YAAI,QAAQ,mBAAmB,IAAI;AAEnC,YAAI,OAAO;AACT,iBAAO;AAAA,QACT;AAGA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAEA,EAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AACvC,GAAG,KAAK;AAIR,MAAM,UAAU,UAAU;AAAA,EACxB,WAAW;AAAA,EACX,eAAe;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,MACN,qBAAqB;AAAA,QACnB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ,MAAM,UAAU;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,aAAa;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,uBAAuB;AAAA,IACrB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAAA,IAClB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,YAAY;AACd;AACA,MAAM,MAAM,IAAI,kBAAkB,SAAS,qBAAqB,KAAK;AACnE,MAAI,IAAI,aAAa,WAAW;AAC9B;AAAA,EACF;AASA,MAAI,cAAc,IAAI,OAAO,OAAO,SAAU,OAAO;AACnD,WAAO,OAAO,UAAU,YAAY,MAAM,SAAS,aAAa,MAAM,SAAS;AAAA,EACjF,CAAC;AACD,MAAI,eAAe;AAQnB,WAAS,SAAS,QAAQ;AACxB,WAAO,YAAY,eAAe,MAAM;AAAA,EAC1C;AAUA,WAAS,YAAY,OAAO,QAAQ;AAClC,aAAS,UAAU;AAEnB,aAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,UAAI,QAAQ,SAASA,KAAI,MAAM;AAE/B,UAAI,CAAC,SAAS,MAAM,SAAS,MAAMA,EAAC,GAAG;AACrC,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAcA,WAAS,mBAAmB,MAAM,OAAO;AACvC,QAAI,cAAc;AAElB,aAASA,KAAI,cAAcA,KAAI,YAAY,QAAQA,MAAK;AACtD,UAAI,QAAQ,YAAYA,EAAC;AACzB,UAAI,UAAU,MAAM;AAEpB,UAAI,MAAM,SAAS,iBAAiB,OAAO,YAAY,UAAU;AAC/D,YAAI,KAAK,KAAK,OAAO,GAAG;AACtB;AAAA,QACF,WAAW,MAAM,KAAK,OAAO,GAAG;AAC9B;AAEA,cAAI,gBAAgB,GAAG;AACrB,mBAAOA;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAUA,WAAS,SAAS,OAAO,OAAO;AAC9B,QAAI,UAAU,MAAM;AAEpB,QAAI,CAAC,SAAS;AACZ,YAAM,QAAQ,UAAU,CAAC;AAAA,IAC3B,WAAW,CAAC,MAAM,QAAQ,OAAO,GAAG;AAClC,YAAM,QAAQ,UAAU,CAAC,OAAO;AAAA,IAClC;AAEA,YAAQ,KAAK,KAAK;AAAA,EACpB;AAEA,SAAO,eAAe,YAAY,UAAS;AACzC,QAAI,aAAa,YAAY,cAAc;AAE3C,QAAI,WAAW,SAAS,aAAa,WAAW,YAAY,YAAY;AAEtE,UAAI,iBAAiB,CAAC;AAEtB,UAAI,YAAY,CAAC,uBAAuB,aAAa,CAAC,KAAK,SAAS,CAAC,EAAE,YAAY,KAAK;AAEtF,wBAAgB;AAEhB,YAAI,gBAAgB,mBAAmB,QAAQ,MAAM;AAErD,YAAI,kBAAkB,IAAI;AACxB;AAAA,QACF;AAGA,eAAO,eAAe,eAAe,gBAAgB;AACnD,cAAI,IAAI,SAAS,CAAC;AAElB,cAAI,EAAE,SAAS,YAAY;AACzB,qBAAS,GAAG,gBAAgB;AAC5B,2BAAe,KAAK,EAAE,OAAO;AAAA,UAC/B;AAAA,QACF;AAEA,uBAAe,gBAAgB;AAAA,MACjC;AAEA,UAAI,YAAY,CAAC,eAAe,gBAAgB,CAAC,KAAK,SAAS,CAAC,EAAE,YAAY,KAAK;AACjF;AAEA,iBAAS,SAAS,CAAC,GAAG,mBAAmB;AAEzC,YAAI,eAAe,SAAS,GAAG;AAC7B,cAAI,cAAc,mBAAmB,QAAQ,MAAM;AAEnD,cAAI,gBAAgB,IAAI;AACtB;AAAA,UACF;AAGA,mBAAS,IAAI,cAAc,IAAI,aAAa,KAAK;AAC/C,gBAAI,WAAW,YAAY,CAAC;AAE5B,gBAAI,SAAS,SAAS,cAAc,eAAe,QAAQ,SAAS,OAAO,KAAK,GAAG;AACjF,uBAAS,UAAU,gBAAgB;AAAA,YACrC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAGD,MAAM,UAAU,MAAM;AAAA,EACpB,WAAW;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,YAAY,CAAC;AAAA,IACX,SAAS;AAAA,IACT,QAAQ;AAAA,EACV,GAAG,UAAU;AAAA,EACb,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,QAAQ;AAAA,MACN,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AACjB;AAAA,CAGC,SAAUD,QAAO;AAChB,MAAI,iBAAiBA,OAAM,UAAU,WAAW,iBAAiB;AAEjE,MAAI,yBAAyB,eAAe,QAAQ;AACpD,MAAI,sBAAsB,eAAe,OAAO,eAAe;AAC/D,MAAI,iCAAiC,oBAAoB,OAAO,2BAA2B;AAC3F,MAAI,uBAAuB,oBAAoB,QAAQ;AAavD,WAAS,eAAe,UAAU,KAAK;AACrC,QAAI,CAACA,OAAM,UAAU,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL,SAAS,OAAO,SAAS,MAAM,WAAW,sBAAsB;AAAA,MAChE,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,wBAAwB;AAAA,UACtB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,EAAAA,OAAM,UAAU,WAAW,iBAAiB,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,IAIhD,eAAe,OAAO,0HAA0H,MAAM;AAAA;AAAA;AAAA,IAEtJ,eAAe,QAAQ,yCAAyC,MAAM;AAAA;AAAA,IACtE,eAAe,OAAO,QAAQ,MAAM;AAAA;AAAA,IACpC,eAAe,YAAY,oBAAoB,MAAM;AAAA;AAAA,IACrD,eAAe,WAAW,6CAA6C,MAAM;AAAA;AAAA,IAC7E,eAAe,OAAO,QAAQ,MAAM;AAAA;AAAA,IACpC;AAAA,EAAc,EAAE,OAAO,OAAO;AAS9B,WAAS,eAAe,SAAS,UAAU;AACzC,WAAO,QAAQ,SAAS,YAAY,IAAI,MAAM,UAAU;AAAA,EAC1D;AAWA,WAAS,kBAAkB,MAAM,SAAS,UAAU;AAClD,QAAI,MAAM;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAAA,OAAM,MAAM,IAAI,mBAAmB,GAAG;AACtC,QAAI,SAASA,OAAM,SAAS,IAAI,MAAM,IAAI,OAAO;AACjD,IAAAA,OAAM,MAAM,IAAI,kBAAkB,GAAG;AACrC,WAAO,IAAI;AAAA,EACb;AASA,WAAS,gCAAgC,YAAY;AACnD,QAAI,cAAc,CAAC;AACnB,gBAAY,2BAA2B,IAAI;AAG3C,QAAI,SAASA,OAAM,SAAS,YAAY,WAAW;AAEnD,QAAI,OAAO,WAAW,GAAG;AASvB,UAAI,OAAO,CAAC,GAAG,CAAC;AAChB,WAAK,KAAK,MAAM,MAAM,kBAAkB,OAAO,CAAC,GAAGA,OAAM,UAAU,YAAY,YAAY,CAAC;AAC5F,aAAO,OAAO,MAAM,QAAQ,IAAI;AAAA,IAClC;AAEA,WAAO,IAAIA,OAAM,MAAM,iBAAiB,QAAQ,oBAAoB,OAAO,UAAU;AAAA,EACvF;AAoBA,WAAS,iBAAiB,MAAM,SAAS,UAAU;AAKjD,QAAI,UAAUA,OAAM,SAAS,MAAM;AAAA,MACjC,iBAAiB;AAAA,QACf,SAAS,OAAO,oBAAoB;AAAA,QACpC,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AAGD,QAAI,qBAAqB;AAGzB,QAAI,iBAAiB,CAAC;AAEtB,QAAI,eAAe,QAAQ,IAAI,SAAU,OAAO;AAC9C,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO;AAAA,MACT,OAAO;AACL,YAAI,0BAA0B,MAAM;AACpC,YAAI;AAEJ,eAAO,KAAK,QAAQ,cAAc,eAAe,sBAAsB,QAAQ,CAAC,MAAM,IAAI;AAAA,QAE1F;AAEA,uBAAe,WAAW,IAAI;AAC9B,eAAO;AAAA,MACT;AAAA,IACF,CAAC,EAAE,KAAK,EAAE;AAGV,QAAI,iBAAiB,kBAAkB,cAAc,SAAS,QAAQ;AAEtE,QAAI,eAAe,OAAO,KAAK,cAAc;AAC7C,yBAAqB;AAOrB,aAAS,WAAW,QAAQ;AAC1B,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,sBAAsB,aAAa,QAAQ;AAC7C;AAAA,QACF;AAEA,YAAI,QAAQ,OAAO,CAAC;AAEpB,YAAI,OAAO,UAAU,YAAY,OAAO,MAAM,YAAY,UAAU;AAClE,cAAI,cAAc,aAAa,kBAAkB;AACjD,cAAI,IAAI,OAAO,UAAU,WAAW;AAAA;AAAA,YAEpC,MAAM;AAAA;AACN,cAAI,QAAQ,EAAE,QAAQ,WAAW;AAEjC,cAAI,UAAU,IAAI;AAChB,cAAE;AACF,gBAAI,SAAS,EAAE,UAAU,GAAG,KAAK;AACjC,gBAAI,SAAS,gCAAgC,eAAe,WAAW,CAAC;AACxE,gBAAI,QAAQ,EAAE,UAAU,QAAQ,YAAY,MAAM;AAClD,gBAAI,cAAc,CAAC;AAEnB,gBAAI,QAAQ;AACV,0BAAY,KAAK,MAAM;AAAA,YACzB;AAEA,wBAAY,KAAK,MAAM;AAEvB,gBAAI,OAAO;AACT,kBAAI,cAAc,CAAC,KAAK;AACxB,yBAAW,WAAW;AACtB,0BAAY,KAAK,MAAM,aAAa,WAAW;AAAA,YACjD;AAEA,gBAAI,OAAO,UAAU,UAAU;AAC7B,qBAAO,OAAO,MAAM,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,WAAW,CAAC;AACtD,mBAAK,YAAY,SAAS;AAAA,YAC5B,OAAO;AACL,oBAAM,UAAU;AAAA,YAClB;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAI,UAAU,MAAM;AAEpB,cAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,uBAAW,OAAO;AAAA,UACpB,OAAO;AACL,uBAAW,CAAC,OAAO,CAAC;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,eAAW,cAAc;AACzB,WAAO,IAAIA,OAAM,MAAM,UAAU,gBAAgB,cAAc,UAAU,IAAI;AAAA,EAC/E;AAQA,MAAI,qBAAqB;AAAA,IACvB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,EACT;AACA,EAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,QAAI,EAAE,IAAI,YAAY,qBAAqB;AACzC;AAAA,IACF;AASA,aAAS,oBAAoB,QAAQ;AACnC,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,YAAI,QAAQ,OAAO,CAAC;AAEpB,YAAI,OAAO,UAAU,UAAU;AAC7B;AAAA,QACF;AAEA,YAAI,UAAU,MAAM;AAEpB,YAAI,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC3B,cAAI,OAAO,YAAY,UAAU;AAC/B,gCAAoB,CAAC,OAAO,CAAC;AAAA,UAC/B;AAEA;AAAA,QACF;AAEA,YAAI,MAAM,SAAS,mBAAmB;AAepC,cAAI,WAAW,QAAQ,CAAC;AAExB,cAAI,QAAQ,WAAW,KAAK,OAAO,aAAa,YAAY,SAAS,SAAS,iBAAiB;AAE7F,gBAAI,OAAO,cAAc,QAAQ;AACjC,gBAAI,QAAQ,SAAS;AACrB,gBAAI,WAAW,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI;AACjD,gBAAI,UAAUA,OAAM,UAAU,QAAQ;AAEtC,gBAAI,CAAC,SAAS;AAEZ;AAAA,YACF;AAEA,oBAAQ,CAAC,IAAI,iBAAiB,MAAM,SAAS,QAAQ;AAAA,UACvD;AAAA,QACF,OAAO;AACL,8BAAoB,OAAO;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAEA,wBAAoB,IAAI,MAAM;AAAA,EAChC,CAAC;AAQD,WAAS,cAAc,OAAO;AAC5B,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA,IACT,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,aAAO,MAAM,IAAI,aAAa,EAAE,KAAK,EAAE;AAAA,IACzC,OAAO;AACL,aAAO,cAAc,MAAM,OAAO;AAAA,IACpC;AAAA,EACF;AACF,GAAG,KAAK;AAAA,CAIP,SAAUA,QAAO;AAChB,EAAAA,OAAM,UAAU,aAAaA,OAAM,UAAU,OAAO,cAAc;AAAA,IAChE,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA;AAAA,IAEV;AAAA,IACA,WAAW;AAAA,EACb,CAAC;AAED,EAAAA,OAAM,UAAU,WAAW,QAAQ;AAAA,IAAK;AAAA;AAAA,IACxC;AAAA;AAAA,IACA;AAAA,EAA4B;AAE5B,SAAOA,OAAM,UAAU,WAAW,WAAW;AAC7C,SAAOA,OAAM,UAAU,WAAW,kBAAkB;AAEpD,MAAI,aAAaA,OAAM,UAAU,OAAO,cAAc,CAAC,CAAC;AACxD,SAAO,WAAW,YAAY;AAC9B,EAAAA,OAAM,UAAU,WAAW,YAAY,EAAE,SAAS;AAClD,EAAAA,OAAM,UAAU,aAAa,cAAc,YAAY;AAAA,IACrD,aAAa;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA;AAAA,MAElB,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,WAAW;AAAA,UACT,SAAS;AAAA;AAAA,UAET,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AACvC,GAAG,KAAK;AAAA,CAIP,SAAUA,QAAO;AAChB,EAAAA,OAAM,UAAU,aAAa,cAAc,qBAAqB;AAAA,IAC9D,mBAAmB;AAAA,MACjB,SAAS,OAAO,cAAcA,OAAM,UAAU,WAAW,mBAAmB,EAAE,QAAQ,MAAM;AAAA,MAC5F,YAAY;AAAA,MACZ,OAAO,CAAC,qBAAqB,UAAU,YAAY,iBAAiB;AAAA,IACtE;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,cAAc,YAAY;AAAA,IACrD,UAAU;AAAA,MACR,SAAS,OAAO,cAAcA,OAAM,UAAU,WAAW,UAAU,EAAE,MAAM;AAAA,MAC3E,YAAY;AAAA,MACZ,OAAO,CAAC,YAAY,iBAAiB;AAAA,IACvC;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,cAAc,YAAY;AAAA,IACrD,oBAAoB,CAAC;AAAA;AAAA;AAAA,MAGnB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG;AAAA;AAAA,MAED,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AASD,WAAS,OAAO,QAAQ,OAAO;AAC7B,WAAO,OAAO,OAAO,QAAQ,SAAS,WAAY;AAChD,aAAO,yDAAyD;AAAA,IAClE,CAAC,GAAG,KAAK;AAAA,EACX;AAEA,EAAAA,OAAM,UAAU,aAAa,cAAc,WAAW;AAAA,IACpD,WAAW;AAAA;AAAA,MAET,SAAS,OAAO,4GAA4G,MAAM;AAAA,MAClI,YAAY;AAAA,MACZ,QAAQA,OAAM,UAAU;AAAA,IAC1B;AAAA,IACA,WAAW;AAAA;AAAA,MAET,SAAS,OAAO,mEAAmE,MAAM;AAAA,MACzF,YAAY;AAAA,MACZ,QAAQA,OAAM,UAAU;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,WAAW,SAAS,EAAE,QAAQ;AAAA,IAC5C,SAAS;AAAA,IACT,OAAO;AAAA,EACT,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,EACT,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO,CAAC,QAAQ,KAAK;AAAA,EACvB,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,EACT,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,cAAc,YAAY;AAAA,IACrD,UAAU;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,cAAc,eAAe;AAAA,IACxD,mBAAmB;AAAA,MACjB,SAAS,OAAO,gBAAgB,MAAM;AAAA,MACtC,YAAY;AAAA,IACd;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,OAAO;AAAA;AAAA,MAEL,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AAED,MAAI,uBAAuB,CAAC,YAAY,qBAAqB,UAAU,mBAAmB,iBAAiB;AAE3G,WAAS,IAAI,GAAG,IAAI,qBAAqB,QAAQ,KAAK;AACpD,QAAI,QAAQ,qBAAqB,CAAC;AAClC,QAAI,QAAQA,OAAM,UAAU,WAAW,KAAK;AAE5C,QAAIA,OAAM,KAAK,KAAK,KAAK,MAAM,UAAU;AACvC,cAAQA,OAAM,UAAU,WAAW,KAAK,IAAI;AAAA,QAC1C,SAAS;AAAA,MACX;AAAA,IACF;AAGA,QAAI,SAAS,MAAM,UAAU,CAAC;AAC9B,UAAM,SAAS;AACf,WAAO,kBAAkB,IAAI;AAAA,EAC/B;AACF,GAAG,KAAK;AAAA,CAIP,SAAUA,QAAO;AAChB,MAAI,aAAaA,OAAM,KAAK,MAAMA,OAAM,UAAU,UAAU;AAC5D,MAAI,QAAQ,+CAA+C;AAC3D,MAAI,SAAS,+CAA+C;AAC5D,MAAI,SAAS,uCAAuC;AAMpD,WAAS,GAAG,QAAQ,OAAO;AACzB,aAAS,OAAO,QAAQ,QAAQ,WAAY;AAC1C,aAAO;AAAA,IACT,CAAC,EAAE,QAAQ,aAAa,WAAY;AAClC,aAAO;AAAA,IACT,CAAC,EAAE,QAAQ,aAAa,WAAY;AAClC,aAAO;AAAA,IACT,CAAC;AACD,WAAO,OAAO,QAAQ,KAAK;AAAA,EAC7B;AAEA,WAAS,GAAG,MAAM,EAAE;AACpB,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,UAAU,UAAU;AACjE,EAAAA,OAAM,UAAU,IAAI,IAAI,UAAU,GAAG,wIAAwI,MAAM;AACnL,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,KAAK,EAAE,UAAU;AAChD,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,YAAY,EAAE,UAAU;AACvD,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,KAAK,EAAE,OAAO,YAAY,IAAI;AAC7D,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,SAAS,IAAI,WAAW,SAAS;AAChE,EAAAA,OAAM,UAAU,aAAa,UAAU,aAAa;AAAA,IAClD,UAAU;AAAA,MACR,SAAS,GAAG,WAAW,MAAM;AAAA,MAC7B,QAAQA,OAAM,UAAU;AAAA,IAC1B;AAAA,EACF,GAAGA,OAAM,UAAU,IAAI,GAAG;AAC1B,EAAAA,OAAM,UAAU,aAAa,UAAU,gBAAgB;AAAA,IACrD,UAAU;AAAA;AAAA,MAER,SAAS,GAAG,YAAY,MAAM;AAAA,MAC9B,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,sBAAsB;AAAA,UACpB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,MAAMA,OAAM,UAAU;AAAA,MACxB;AAAA,IACF;AAAA,EACF,GAAGA,OAAM,UAAU,IAAI,GAAG;AAE1B,MAAI,iBAAiB,SAAU,OAAO;AACpC,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,MAAM,YAAY,UAAU;AACrC,aAAO,MAAM;AAAA,IACf;AAEA,WAAO,MAAM,QAAQ,IAAI,cAAc,EAAE,KAAK,EAAE;AAAA,EAClD;AAEA,MAAI,aAAa,SAAU,QAAQ;AACjC,QAAI,aAAa,CAAC;AAElB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,iBAAiB;AAErB,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,SAAS,SAAS,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,EAAE,SAAS,OAAO;AAE/E,cAAI,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,YAAY,MAAM;AAEhD,gBAAI,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC,EAAE,YAAY,eAAe,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG;AAEtH,yBAAW,IAAI;AAAA,YACjB;AAAA,UACF,OAAO;AACL,gBAAI,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,EAAE,YAAY;AAAM;AAAA,iBAAO;AAEnE,yBAAW,KAAK;AAAA,gBACd,SAAS,eAAe,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;AAAA,gBACnD,cAAc;AAAA,cAChB,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,WAAW,WAAW,SAAS,KAAK,MAAM,SAAS,iBAAiB,MAAM,YAAY,KAAK;AAEzF,qBAAW,WAAW,SAAS,CAAC,EAAE;AAAA,QACpC,WAAW,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC,EAAE,eAAe,KAAK,MAAM,SAAS,iBAAiB,MAAM,YAAY,KAAK;AAE/I,qBAAW,WAAW,SAAS,CAAC,EAAE;AAAA,QACpC,OAAO;AACL,2BAAiB;AAAA,QACnB;AAAA,MACF;AAEA,UAAI,kBAAkB,OAAO,UAAU,UAAU;AAC/C,YAAI,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC,EAAE,iBAAiB,GAAG;AAGjF,cAAI,YAAY,eAAe,KAAK;AAEpC,cAAI,IAAI,OAAO,SAAS,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,YAAY,OAAO,IAAI,CAAC,EAAE,SAAS,eAAe;AACvG,yBAAa,eAAe,OAAO,IAAI,CAAC,CAAC;AACzC,mBAAO,OAAO,IAAI,GAAG,CAAC;AAAA,UACxB;AAEA,cAAI,IAAI,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,YAAY,OAAO,IAAI,CAAC,EAAE,SAAS,eAAe;AACvF,wBAAY,eAAe,OAAO,IAAI,CAAC,CAAC,IAAI;AAC5C,mBAAO,OAAO,IAAI,GAAG,CAAC;AACtB;AAAA,UACF;AAEA,iBAAO,CAAC,IAAI,IAAIA,OAAM,MAAM,cAAc,WAAW,MAAM,SAAS;AAAA,QACtE;AAAA,MACF;AAEA,UAAI,MAAM,WAAW,OAAO,MAAM,YAAY,UAAU;AACtD,mBAAW,MAAM,OAAO;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAEA,EAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,QAAI,IAAI,aAAa,SAAS,IAAI,aAAa,OAAO;AACpD;AAAA,IACF;AAEA,eAAW,IAAI,MAAM;AAAA,EACvB,CAAC;AACH,GAAG,KAAK;AAAA,CAIP,SAAUA,QAAO;AAChB,EAAAA,OAAM,UAAU,OAAO;AAAA,IACrB,SAAS;AAAA;AAAA,MACT;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,IAAS;AAAA;AAAA,EAEX;AAOA,MAAI,WAAW;AAAA,IACb,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAEA,SAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAC5C,QAAI,SAAS,SAAS,IAAI;AAC1B,QAAI,QAAQ,CAAC;AAEb,QAAI,CAAC,QAAQ,KAAK,IAAI,GAAG;AAEvB,YAAM,KAAK,MAAM,KAAK,IAAI,EAAE,CAAC,CAAC;AAAA,IAChC;AAEA,QAAI,SAAS,QAAQ;AACnB,YAAM,KAAK,MAAM;AAAA,IACnB;AAEA,IAAAA,OAAM,UAAU,KAAK,IAAI,IAAI;AAAA,MAC3B,SAAS,OAAO,UAAU,SAAS,kCAAkC,GAAG;AAAA,MACxE;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,OAAO,MAAM,KAAK,IAAI,EAAE,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO,eAAeA,OAAM,UAAU,MAAM,YAAY;AAAA,IACtD,OAAO;AAAA,EACT,CAAC;AACH,GAAG,KAAK;AAIR,MAAM,UAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUpB,WAAW;AAAA;AAAA;AAAA;AAAA,EAKX,WAAW;AAAA,EACX,YAAY;AAAA;AAAA;AAAA;AAAA,EAKZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOV,WAAW;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAON,aAAa;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYT,eAAe;AACjB;AAGA,MAAM,UAAU,KAAK,MAAM,UAAU,OAAO,SAAS;AAAA,EACnD,UAAU;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA;AAAA,IACV;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA,EAAoE;AAAA,EACpE,YAAY;AAAA,EACZ,WAAW;AACb,CAAC;AACD,MAAM,UAAU,aAAa,MAAM,UAAU;AAAA,EAC3C,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AACF,CAAC;AACD,OAAO,MAAM,UAAU,GAAG,YAAY;AAAA,CAGrC,SAAUA,QAAO;AAQhB,WAAS,eAAe,UAAU,OAAO;AACvC,WAAO,QAAQ,SAAS,YAAY,IAAI,QAAQ;AAAA,EAClD;AAEA,SAAO,iBAAiBA,OAAM,UAAU,mBAAmB,IAAI,CAAC,GAAG;AAAA,IACjE,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYjB,OAAO,SAAU,KAAK,UAAU,oBAAoB,eAAe;AACjE,YAAI,IAAI,aAAa,UAAU;AAC7B;AAAA,QACF;AAEA,YAAI,aAAa,IAAI,aAAa,CAAC;AACnC,YAAI,OAAO,IAAI,KAAK,QAAQ,oBAAoB,SAAU,OAAO;AAC/D,cAAI,OAAO,kBAAkB,cAAc,CAAC,cAAc,KAAK,GAAG;AAChE,mBAAO;AAAA,UACT;AAEA,cAAI,IAAI,WAAW;AACnB,cAAI;AAEJ,iBAAO,IAAI,KAAK,QAAQ,cAAc,eAAe,UAAU,CAAC,CAAC,MAAM,IAAI;AACzE,cAAE;AAAA,UACJ;AAGA,qBAAW,CAAC,IAAI;AAChB,iBAAO;AAAA,QACT,CAAC;AAED,YAAI,UAAUA,OAAM,UAAU;AAAA,MAChC;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOpB,OAAO,SAAU,KAAK,UAAU;AAC9B,YAAI,IAAI,aAAa,YAAY,CAAC,IAAI,YAAY;AAChD;AAAA,QACF;AAGA,YAAI,UAAUA,OAAM,UAAU,QAAQ;AACtC,YAAI,IAAI;AACR,YAAI,OAAO,OAAO,KAAK,IAAI,UAAU;AAErC,iBAAS,WAAW,QAAQ;AAC1B,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAEtC,gBAAI,KAAK,KAAK,QAAQ;AACpB;AAAA,YACF;AAEA,gBAAI,QAAQ,OAAO,CAAC;AAEpB,gBAAI,OAAO,UAAU,YAAY,MAAM,WAAW,OAAO,MAAM,YAAY,UAAU;AACnF,kBAAI,IAAI,KAAK,CAAC;AACd,kBAAI,IAAI,IAAI,WAAW,CAAC;AACxB,kBAAI,IAAI,OAAO,UAAU,WAAW,QAAQ,MAAM;AAClD,kBAAI,cAAc,eAAe,UAAU,CAAC;AAC5C,kBAAI,QAAQ,EAAE,QAAQ,WAAW;AAEjC,kBAAI,QAAQ,IAAI;AACd,kBAAE;AACF,oBAAI,SAAS,EAAE,UAAU,GAAG,KAAK;AACjC,oBAAI,SAAS,IAAIA,OAAM,MAAM,UAAUA,OAAM,SAAS,GAAG,IAAI,OAAO,GAAG,cAAc,UAAU,CAAC;AAChG,oBAAI,QAAQ,EAAE,UAAU,QAAQ,YAAY,MAAM;AAClD,oBAAI,cAAc,CAAC;AAEnB,oBAAI,QAAQ;AACV,8BAAY,KAAK,MAAM,aAAa,WAAW,CAAC,MAAM,CAAC,CAAC;AAAA,gBAC1D;AAEA,4BAAY,KAAK,MAAM;AAEvB,oBAAI,OAAO;AACT,8BAAY,KAAK,MAAM,aAAa,WAAW,CAAC,KAAK,CAAC,CAAC;AAAA,gBACzD;AAEA,oBAAI,OAAO,UAAU,UAAU;AAC7B,yBAAO,OAAO,MAAM,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,WAAW,CAAC;AAAA,gBACxD,OAAO;AACL,wBAAM,UAAU;AAAA,gBAClB;AAAA,cACF;AAAA,YACF,WAAW,MAAM,SAEf;AACE,yBAAW,MAAM,OAAO;AAAA,YAC1B;AAAA,UACJ;AAEA,iBAAO;AAAA,QACT;AAEA,mBAAW,IAAI,MAAM;AAAA,MACvB;AAAA,IACF;AAAA,EACF,CAAC;AACH,GAAG,KAAK;AAAA,CAIP,SAAUA,QAAO;AAChB,EAAAA,OAAM,UAAU,aAAa;AAAA,IAC3B,WAAW;AAAA,IACX,aAAa;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,YAAY;AAAA,EACd;AACA,EAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,QAAI,oBAAoB;AACxB,IAAAA,OAAM,UAAU,mBAAmB,EAAE,kBAAkB,KAAK,cAAc,iBAAiB;AAAA,EAC7F,CAAC;AACD,EAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,IAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,YAAY;AAAA,EAC7E,CAAC;AACD,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AACxC,GAAG,KAAK;AAKR,MAAM,UAAU,OAAO;AAAA,EACrB,YAAY;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,UAAU;AAAA,EACV,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,MAAM,UAAU,cAAc,MAAM,UAAU;AAW9C,MAAM,UAAU,OAAO,MAAM,UAAU,OAAO,OAAO;AAAA,EACnD,WAAW,CAAC,oBAAoB;AAAA,IAC9B,SAAS;AAAA,IACT,YAAY;AAAA,EACd,CAAC;AAAA,EACD,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,MACN,eAAe;AAAA,IACjB;AAAA,EACF;AAAA;AAAA,EAEA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA;AAAA,MAEN,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,YAAY;AACd,CAAC;AACD,MAAM,UAAU,aAAa,QAAQ,YAAY;AAAA,EAC/C,YAAY;AAAA;AAAA,IACZ;AAAA,MACE,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,eAAe;AAAA,MACjB;AAAA,IACF;AAAA;AAAA,IACA;AAAA,EAAW;AAAA,EACX,eAAe;AAAA,IACb,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA,EACT;AACF,CAAC;AAGD,MAAM,UAAU,WAAW;AAAA,EACzB,WAAW;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,kBAAkB;AAAA,IAChB,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,MACN,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,WAAW;AAAA,EACX,YAAY;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,EACZ,eAAe;AACjB;AAGA,MAAM,UAAU,aAAa,MAAM,UAAU,OAAO,KAAK;AAAA,EACvD,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,WAAW;AAAA,EACX,YAAY;AACd,CAAC;AACD,OAAO,MAAM,UAAU,WAAW,YAAY;AAC9C,MAAM,UAAU,OAAO,MAAM,UAAU;AAIvC,MAAM,UAAU,QAAQ;AAAA,EACtB,WAAW;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,UAAU,CAAC;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACV,GAAG;AAAA,IACD,SAAS;AAAA,IACT,QAAQ;AAAA,EACV,CAAC;AAAA,EACD,UAAU;AAAA;AAAA,IACV;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA,EAAmD;AAAA,EACnD,aAAa;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAGA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,6BAA6B;AAAA,IAC3B,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA;AAAA,EAEA,YAAY;AAAA,EACZ,eAAe;AACjB;AAGA,MAAM,UAAU,SAAS;AAAA,EACvB,WAAW;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,wBAAwB;AAAA,IACtB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,iBAAiB;AAAA;AAAA,QAEf,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,UACN,eAAe;AAAA,YACb,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,wBAAwB;AAAA,IACtB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO,CAAC,cAAc,aAAa;AAAA,IACnC,QAAQ;AAAA,MACN,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AACjB;AACA,MAAM,UAAU,OAAO,sBAAsB,EAAE,OAAO,eAAe,EAAE,OAAO,OAAO,MAAM,UAAU;AACrG,MAAM,UAAU,KAAK,MAAM,UAAU;AAGrC,MAAM,UAAU,SAAS,MAAM,UAAU,OAAO,SAAS;AAAA,EACvD,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA;AAAA,EAEA,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AACd,CAAC;AACD,MAAM,UAAU,aAAa,UAAU,cAAc;AAAA,EACnD,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA;AAAA,EAEA,eAAe;AAAA,EACf,SAAS;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF,CAAC;AAED,OAAO,MAAM,UAAU,OAAO;AAAA,CAG7B,SAAUA,QAAO;AAChB,EAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,OAAO;AAAA;AAAA,IAEnD,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA;AAAA,IAE7C,eAAe;AAAA;AAAA,MAEb,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAOA,OAAM,UAAU,KAAK;AAC5B,MAAI,WAAW;AACf,MAAI,WAAW,CAAC,4CAA4C;AAAA,IAC1D,SAAS;AAAA,IACT,YAAY;AAAA,EACd,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,QAAQ,YAAY;AAAA;AAAA,IAE/C,iBAAiB;AAAA,MACf,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA;AAAA,IAEA,iBAAiB;AAAA,MACf,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,YAAY,CAAC,mBAAmB;AAAA,UAC9B,SAAS;AAAA,UACT,YAAY;AAAA,QACd,CAAC;AAAA,QACD,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAaA,OAAM,UAAU,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAOA,OAAM,UAAU,KAAK;AAC5B,SAAOA,OAAM,UAAU,KAAK;AAG5B,EAAAA,OAAM,UAAU,aAAa,QAAQ,eAAe;AAAA,IAClD,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH,GAAG,KAAK;AAIR,MAAM,UAAU,OAAO,MAAM,UAAU,OAAO,OAAO;AAAA,EACnD,WAAW;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,MACN,QAAQ;AAAA;AAAA,IAEV;AAAA,EACF;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQP,YAAY;AAAA;AAAA,IAEV,SAAS;AAAA,IACT,QAAQ;AAAA,MACN,UAAU;AAAA,QACR,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,MACf,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA,MACN,YAAY;AAAA,IACd;AAAA,EACF;AACF,CAAC;AACD,MAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,EAC7C,WAAW,CAAC,qHAAqH;AAAA,IAC/H,SAAS;AAAA,IACT,YAAY;AAAA,EACd,CAAC;AACH,CAAC;AACD,MAAM,UAAU,aAAa,QAAQ,aAAa;AAAA;AAAA,EAEhD,YAAY;AACd,CAAC;AACD,MAAM,UAAU,aAAa,QAAQ,YAAY;AAAA,EAC/C,mBAAmB;AAAA,IACjB,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,EACX,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AACF,CAAC;AACD,MAAM,UAAU,KAAK,QAAQ,EAAE,OAAO,OAAO,MAAM,UAAU;AAAA,CAG5D,SAAUA,QAAO;AAChB,MAAI,OAAO;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAEA,MAAI,SAAS;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AACA,MAAI,SAAS;AAAA,IACX,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,iBAAiB;AAAA;AAAA,IAEjB,QAAQ;AAAA;AAAA,IAER,aAAa;AAAA,IACb,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,WAAW;AAAA,IACX,SAAS,CAAC,s5CAAs5C;AAAA,MAC95C,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,IACD,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA;AAAA;AAAA,MAEZ;AAAA,IAAsG;AAAA,IACtG,UAAU;AAAA,IACV,eAAe;AAAA,EACjB;AACA,SAAO,eAAe,IAAI;AAAA,IACxB,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,MACN,aAAa;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,MAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO,MAAM,IAAI;AAAA,IACf,SAAS;AAAA,IACT,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,MAAM;AAAA,IACR;AAAA,EACF;AACA,EAAAA,OAAM,UAAU,SAAS;AAAA,IACvB,sBAAsB;AAAA,MACpB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,MAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA,IAGA,wBAAwB;AAAA,MACtB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,YAAY;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,iBAAiB,OAAO;AAAA,UAC1B;AAAA,QACF;AAAA,QACA,MAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,iBAAiB,OAAO;AAAA,QACxB,WAAW,OAAO;AAAA,QAClB,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,IACA,QAAQ,OAAO;AAAA,IACf,UAAU,OAAO;AAAA,IACjB,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,IACA,iBAAiB,OAAO;AAAA,IACxB,eAAe;AAAA,EACjB;AACF,GAAG,KAAK;AAAA,CAIP,SAAUA,QAAO;AAChB,MAAI,aAAaA,OAAM,KAAK,MAAMA,OAAM,UAAU,UAAU;AAC5D,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,OAAO,UAAU;AAE9D,SAAOA,OAAM,UAAU,IAAI,WAAW;AACtC,SAAOA,OAAM,UAAU,IAAI,kBAAkB;AAI7C,MAAI,MAAMA,OAAM,UAAU,IAAI;AAC9B,MAAI,UAAU,OAAO,qBAAqB,SAAS,QAAQ,IAAI,QAAQ,SAAS,KAAK,IAAI,QAAQ,KAAK;AACtG,MAAI,aAAa;AACnB,GAAG,KAAK;AAIR,MAAM,UAAU,OAAO;AAAA,EACrB,WAAW,CAAC,kBAAkB;AAAA,IAC5B,SAAS;AAAA,IACT,QAAQ;AAAA,EACV,CAAC;AAAA,EACD,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,WAAW,CAAC;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA,MACN,YAAY;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,SAAS;AAAA,IACT,QAAQ;AAAA,MACN,eAAe;AAAA,IACjB;AAAA,EACF,GAAG,0QAA0Q;AAAA,EAC7Q,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,eAAe;AACjB;AAEA,IAAO,gBAAQ;;;AC9hIf,mBAA0B;AAQ1B,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,WAAW;AAClB,aAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AAExB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEA,IAAI,YAAY;AAEhB,IAAI,sBAAsB,SAAU,MAAM;AACxC,MAAI,KAAK,WAAW,GAAG;AACrB,SAAK,KAAK;AAAA,MACR,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC;AAAA,EACH,WAAW,KAAK,WAAW,KAAK,KAAK,CAAC,EAAE,YAAY,IAAI;AACtD,SAAK,CAAC,EAAE,UAAU;AAClB,SAAK,CAAC,EAAE,QAAQ;AAAA,EAClB;AACF;AAEA,IAAI,cAAc,SAAU,OAAO,KAAK;AACtC,MAAI,YAAY,MAAM;AAEtB,MAAI,YAAY,KAAK,MAAM,YAAY,CAAC,MAAM,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,OAAO,GAAG;AACzB;AAQA,IAAI,kBAAkB,SAAU,QAAQ;AACtC,MAAI,eAAe,CAAC,CAAC,CAAC;AACtB,MAAI,gBAAgB,CAAC,MAAM;AAC3B,MAAI,qBAAqB,CAAC,CAAC;AAC3B,MAAI,oBAAoB,CAAC,OAAO,MAAM;AACtC,MAAI,IAAI;AACR,MAAI,aAAa;AACjB,MAAI,cAAc,CAAC;AACnB,MAAI,MAAM,CAAC,WAAW;AAEtB,SAAO,aAAa,IAAI;AACtB,YAAQ,IAAI,mBAAmB,UAAU,OAAO,kBAAkB,UAAU,GAAG;AAC7E,UAAI,UAAU;AACd,UAAI,QAAQ,aAAa,UAAU;AACnC,UAAI,WAAW,cAAc,UAAU;AACvC,UAAI,QAAQ,SAAS,CAAC;AAEtB,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,aAAa,IAAI,QAAQ,CAAC,OAAO;AACzC,kBAAU;AAAA,MACZ,OAAO;AACL,gBAAQ,YAAY,OAAO,MAAM,IAAI;AAErC,YAAI,MAAM,OAAO;AACf,kBAAQ,YAAY,OAAO,MAAM,KAAK;AAAA,QACxC;AAEA,kBAAU,MAAM;AAAA,MAClB;AAGA,UAAI,OAAO,YAAY,UAAU;AAC/B;AACA,qBAAa,KAAK,KAAK;AACvB,sBAAc,KAAK,OAAO;AAC1B,2BAAmB,KAAK,CAAC;AACzB,0BAAkB,KAAK,QAAQ,MAAM;AACrC;AAAA,MACF;AAGA,UAAI,kBAAkB,QAAQ,MAAM,SAAS;AAC7C,UAAI,eAAe,gBAAgB;AACnC,kBAAY,KAAK;AAAA,QACf;AAAA,QACA,SAAS,gBAAgB,CAAC;AAAA,MAC5B,CAAC;AAED,eAAS,MAAM,GAAG,MAAM,cAAc,OAAO;AAC3C,4BAAoB,WAAW;AAC/B,YAAI,KAAK,cAAc,CAAC,CAAC;AACzB,oBAAY,KAAK;AAAA,UACf;AAAA,UACA,SAAS,gBAAgB,GAAG;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,IACF;AAGA;AACA,iBAAa,IAAI;AACjB,kBAAc,IAAI;AAClB,uBAAmB,IAAI;AACvB,sBAAkB,IAAI;AAAA,EACxB;AAEA,sBAAoB,WAAW;AAC/B,SAAO;AACT;AAEA,IAAI,cAAc,SAAU,OAAO,UAAU;AAC3C,MAAI,QAAQ,MAAM;AAElB,MAAI,OAAO,uBAAO,OAAO,IAAI;AAC7B,MAAI,YAAY,MAAM,OAAO,OAAO,SAAU,KAAK,YAAY;AAC7D,QAAI,YAAY,WAAW;AAC3B,QAAI,QAAQ,WAAW;AAEvB,QAAI,aAAa,CAAC,UAAU,SAAS,QAAQ,GAAG;AAC9C,aAAO;AAAA,IACT;AAEA,eAAW,MAAM,QAAQ,SAAU,MAAM;AAEvC,UAAI,WAAW,SAAS,CAAC,GAAG,IAAI,IAAI,GAAG,KAAK;AAE5C,UAAI,IAAI,IAAI;AAAA,IACd,CAAC;AACD,WAAO;AAAA,EACT,GAAG,IAAI;AAEP,YAAU,OAAO;AAEjB,YAAU,QAAQ,SAAS,CAAC,GAAG,OAAO;AAAA,IACpC,iBAAiB;AAAA,EACnB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,wBAAwB,KAAK,SAAS;AAC7C,MAAI,SAAS,CAAC;AAEd,WAAS,KAAK;AAAK,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,KAAK,QAAQ,QAAQ,CAAC,MAAM;AAAI,aAAO,CAAC,IAAI,IAAI,CAAC;AAEnH,SAAO;AACT;AAEA,IAAI,YAAyB,SAAUE,YAAW;AAChD,WAASC,aAAY;AACnB,QAAI,SAAS;AACb,QAAI,OAAO,CAAC,GACR,MAAM,UAAU;AAEpB,WAAO;AAAO,WAAK,GAAG,IAAI,UAAU,GAAG;AAEvC,IAAAD,WAAU,MAAM,MAAM,IAAI;AAE1B,oBAAgB,MAAM,gBAAgB,SAAU,OAAO;AACrD,UAAI,OAAO,cAAc,UAAa,MAAM,UAAU,OAAO,aAAa,MAAM,aAAa,OAAO,cAAc;AAChH,eAAO,OAAO;AAAA,MAChB;AAEA,aAAO,YAAY,MAAM;AACzB,aAAO,eAAe,MAAM;AAC5B,UAAI,YAAY,MAAM,QAAQ,YAAY,MAAM,OAAO,MAAM,QAAQ,IAAI;AACzE,aAAO,OAAO,YAAY;AAAA,IAC5B,CAAC;AAED,oBAAgB,MAAM,gBAAgB,SAAU,KAAK;AACnD,UAAI,MAAM,IAAI;AACd,UAAI,YAAY,IAAI;AACpB,UAAI,QAAQ,IAAI;AAChB,UAAI,SAAS,wBAAwB,KAAK,CAAC,OAAO,aAAa,SAAS,MAAM,CAAC;AAC/E,UAAI,OAAO;AAEX,UAAI,SAAS,SAAS,CAAC,GAAG,MAAM;AAAA,QAC9B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP,CAAC;AAED,UAAI,YAAY,OAAO,aAAa,OAAO,KAAK;AAEhD,UAAI,cAAc,QAAW;AAC3B,eAAO,QAAQ,UAAU;AAAA,MAC3B;AAEA,UAAI,UAAU,QAAW;AACvB,eAAO,QAAQ,OAAO,UAAU,SAAY,SAAS,CAAC,GAAG,OAAO,OAAO,KAAK,IAAI;AAAA,MAClF;AAEA,UAAI,QAAQ,QAAW;AACrB,eAAO,MAAM;AAAA,MACf;AAEA,UAAI,WAAW;AACb,eAAO,aAAa,MAAM;AAAA,MAC5B;AAEA,aAAO;AAAA,IACT,CAAC;AAED,oBAAgB,MAAM,oBAAoB,SAAU,KAAK;AACvD,UAAI,QAAQ,IAAI;AAChB,UAAI,QAAQ,IAAI;AAChB,UAAI,YAAY,MAAM;AACtB,UAAI,YAAY,OAAO,aAAa,OAAO,KAAK;AAEhD,UAAI,cAAc,QAAW;AAC3B,eAAO;AAAA,MACT,WAAW,cAAc,KAAK,MAAM,CAAC,MAAM,SAAS;AAClD,eAAO,QAAQ;AAAA,UACb,SAAS;AAAA,QACX,IAAI;AAAA,MACN,WAAW,cAAc,KAAK,CAAC,OAAO;AACpC,eAAO,UAAU,MAAM,CAAC,CAAC;AAAA,MAC3B;AAEA,UAAI,YAAY,QAAQ;AAAA,QACtB,SAAS;AAAA,MACX,IAAI,CAAC;AAEL,UAAI,aAAa,MAAM,IAAI,SAAU,MAAM;AACzC,eAAO,UAAU,IAAI;AAAA,MACvB,CAAC;AACD,aAAO,OAAO,OAAO,MAAM,QAAQ,CAAC,SAAS,EAAE,OAAO,UAAU,CAAC;AAAA,IACnE,CAAC;AAED,oBAAgB,MAAM,iBAAiB,SAAU,KAAK;AACpD,UAAI,MAAM,IAAI;AACd,UAAI,YAAY,IAAI;AACpB,UAAI,QAAQ,IAAI;AAChB,UAAI,QAAQ,IAAI;AAChB,UAAI,SAAS,wBAAwB,KAAK,CAAC,OAAO,aAAa,SAAS,OAAO,CAAC;AAChF,UAAI,OAAO;AAEX,UAAI,SAAS,SAAS,CAAC,GAAG,MAAM;AAAA,QAC9B,WAAW,WAAW,MAAM,MAAM,KAAK,GAAG;AAAA,QAC1C,UAAU,MAAM;AAAA,QAChB,OAAO,OAAO,iBAAiB,KAAK;AAAA,QACpC,KAAK;AAAA,MACP,CAAC;AAED,UAAI,UAAU,QAAW;AACvB,eAAO,QAAQ,OAAO,UAAU,SAAY,SAAS,CAAC,GAAG,OAAO,OAAO,KAAK,IAAI;AAAA,MAClF;AAEA,UAAI,QAAQ,QAAW;AACrB,eAAO,MAAM;AAAA,MACf;AAEA,UAAI,WAAW;AACb,eAAO,aAAa,MAAM;AAAA,MAC5B;AAEA,aAAO;AAAA,IACT,CAAC;AAED,oBAAgB,MAAM,YAAY,SAAUE,QAAO,MAAM,SAAS,UAAU;AAC1E,UAAI,MAAM;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ,CAAC;AAAA,MACX;AACA,MAAAA,OAAM,MAAM,IAAI,mBAAmB,GAAG;AACtC,UAAI,SAAS,IAAI,SAASA,OAAM,SAAS,IAAI,MAAM,IAAI,SAAS,IAAI,QAAQ;AAC5E,MAAAA,OAAM,MAAM,IAAI,kBAAkB,GAAG;AACrC,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,MAAIF;AAAW,IAAAC,WAAU,YAAYD;AACrC,EAAAC,WAAU,YAAY,OAAO,OAAOD,cAAaA,WAAU,SAAS;AACpE,EAAAC,WAAU,UAAU,cAAcA;AAElC,EAAAA,WAAU,UAAU,SAAS,SAAS,SAAS;AAC7C,QAAI,MAAM,KAAK;AACf,QAAIC,SAAQ,IAAI;AAChB,QAAI,WAAW,IAAI;AACnB,QAAI,OAAO,IAAI;AACf,QAAI,WAAW,IAAI;AACnB,QAAI,YAAY,KAAK,aAAa,KAAK,KAAK;AAC5C,QAAI,UAAUA,OAAM,UAAU,QAAQ;AACtC,QAAI,cAAc,YAAY,SAAY,KAAK,SAASA,QAAO,MAAM,SAAS,QAAQ,IAAI,CAAC,IAAI;AAC/F,QAAI,SAAS,gBAAgB,WAAW;AACxC,WAAO,SAAS;AAAA,MACd;AAAA,MACA,WAAW,yBAAyB;AAAA,MACpC,OAAO,cAAc,SAAY,UAAU,OAAO,CAAC;AAAA,MACnD,cAAc,KAAK;AAAA,MACnB,eAAe,KAAK;AAAA,IACtB,CAAC;AAAA,EACH;AAEA,SAAOD;AACT,EAAE,sBAAS;AAEX,IAAO,eAAQ;;;ACtUf,IAAAE,gBAAoD;AACpD,oBAAqB;AACrB,wBAAsB;AAEtB,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,mBAAmB,oBAAoB,QAAI,wBAAS;AAC3D,QAAM,8BAA0B,uBAAQ,UAAM,cAAAC,SAAS,MAAM;AAC3D,QAAI,aAAa,SAAS;AACxB,YAAM,gBAAgB,aAAa,QAAQ,SAAS,CAAC;AACrD,YAAM,sBAAsB,cAAc;AAC1C,YAAM,qBAAqB,cAAc;AACzC,YAAM,kBAAkB,aAAa,QAAQ;AAC7C,YAAM,iBAAiB,aAAa,QAAQ;AAC5C,UAAI,qBAAqB,kBAAkB,sBAAsB,iBAAiB;AAChF,6BAAqB,CAAC;AAAA,MACxB,OAAO;AACL,6BAAqB,MAAS;AAAA,MAChC;AAAA,IACF;AAAA,EACF,GAAG,GAAG,GAAG,CAAC,cAAc,oBAAoB,CAAC;AAC7C,+BAAU,MAAM;AACd,qBAAiB,UAAU,uBAAuB;AAClD,4BAAwB;AACxB,WAAO,MAAM;AACX,0BAAoB,UAAU,uBAAuB;AACrD,8BAAwB,OAAO;AAAA,IACjC;AAAA,EACF,GAAG,CAAC,yBAAyB,UAAU,CAAC;AACxC,SAAO;AACT;AAEA,IAAM,wBAAwB,UAAQ;AACpC,MAAI;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAC,QAAM,cAAc,cAAAA,QAAM,UAAU,MAAM,OAAO,gBAAgB,KAAK,CAAC,CAAC;AACjF;AACA,sBAAsB,YAAY;AAAA,EAChC,UAAU,kBAAAC,QAAU;AAAA,EACpB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,cAAc,kBAAAA,QAAU,IAAI;AAAA,EAC5B,YAAY,kBAAAA,QAAU;AACxB;;;AHxCA,SAASC,YAAW;AAClB,EAAAA,YAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACvC;AAEA,IAAM,MAAM,CAAC,QAAQ,OAAO,SAAS,QAAQ;AAC7C,IAAM,OAAO,CAAC,SAAS,UAAU,OAAO;AACxC,IAAM,eAAe,CAAC,WAAW,GAAG,IAAI;AACxC,IAAM,oBAAoB,CAAC,WAAW,wBAAwB,eAAe,eAAe,eAAe,aAAa;AACxH,IAAM,sBAAsB,CAAC,UAAU,QAAQ,QAAQ;AACvD,IAAM,YAAY,CAAC,UAAU,QAAQ,SAAS,KAAK,OAAO,OAAO,cAAc,OAAO,gBAAgB,gBAAgB,YAAY,QAAQ,OAAO,MAAM,WAAW,cAAc,QAAQ,QAAQ,YAAY,YAAY,cAAc,SAAS,UAAU,UAAU,QAAQ,QAAQ,OAAO,UAAU,OAAO,cAAc,QAAQ,MAAM;AAEvU,IAAM,iBAAiB;AACvB,CAAC,GAAG,MAAM,cAAc,WAAW,SAAS;AAC5C,IAAM,cAAc;AAAA,EAClB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AACb;AACA,IAAM,aAAa,WAAS;AAC1B,QAAM,YAAY,MAAM,eAAe,CAAC,WAAW,SAAS,UAAU,OAAO,EAAE,QAAQ,MAAM,IAAI,MAAM;AACvG,QAAM,aAAa,aAAa,MAAM,MAAM,MAAM;AAClD,QAAM,YAAY,MAAM,MAAM,MAAM,QAAQ;AAC5C,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,WAAW;AACb,QAAI,MAAM,SAAS,WAAW;AAC5B,iBAAW;AACX,mBAAa;AAAA,IACf,OAAO;AACL,YAAM,YAAY,YAAY,MAAM,IAAI;AACxC,iBAAW,KAAK,GAAG,MAAM,MAAM,UAAU,SAAS,SAAS;AAC3D,mBAAa,KAAK,GAAG,MAAM,MAAM,YAAY,SAAS,SAAS;AAAA,IACjE;AAAA,EACF,WAAW,MAAM,SAAS,WAAW;AACnC,UAAM,YAAY,YAAY,MAAM,IAAI;AACxC,eAAW,MAAM,MAAM,UAAU,SAAS;AAC1C,iBAAa,MAAM,MAAM,YAAY,SAAS;AAAA,EAChD;AACA,MAAI,MAAM,WAAW,MAAM;AACzB,iBAAa,MAAM,MAAM,YAAY;AAAA,EACvC,WAAW,MAAM,WAAW,SAAS,MAAM,SAAS,WAAW;AAC7D,iBAAa,MAAM,MAAM,YAAY;AAAA,EACvC;AACA,MAAI,MAAM,KAAK;AACb,UAAM,QAAQ,MAAM,QAAQ,WAAW,MAAM;AAC7C,YAAQ,SAAS,MAAM,KAAK,OAAO,MAAM,KAAK;AAAA,EAChD;AACA,SAAO,GAAI,CAAC,gBAAgB,WAAW,iBAAiB,eAAe,iBAAiB,eAAe,GAAG,GAAG,YAAY,OAAO,YAAY,UAAU,YAAY,SAAS;AAC7K;AACA,IAAM,aAAa,sCAAO,IAAI,MAAM;AAAA,EAClC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,GAAG,WAAS,WAAW,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACtG,WAAW,eAAe;AAAA,EACxB,OAAO;AAAA,EACP,MAAM;AACR;AAEA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB,sCAAO,WAAW,MAAM;AAAA,EAC/C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,oBAAoB,KAAK,wBAAwB,uBAAuB,KAAK,iBAAiB,4BAA4B,MAAM,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,MAAM,MAAM,aAAa,IAAI,WAAS,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,MAAM,MAAM,QAAQ,OAAO,WAAS,MAAM,MAAM,YAAY,YAAY,MAAM,IAAI,CAAC,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC9f,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,QAAM,MAAM,MAAM,OAAO;AACzB,QAAM,kBAAkB,SAAS,KAAK,KAAK,MAAM,KAAK;AACtD,QAAM,QAAQ,QAAQ,WAAW,MAAM;AACvC,QAAM,kBAAkB,SAAS,KAAK,OAAO,MAAM,KAAK;AACxD,SAAO,GAAI,CAAC,qBAAqB,WAAW,sBAAsB,GAAG,iBAAiB,eAAe;AACvG;AACA,IAAM,aAAa,sCAAO,UAAU,EAAE,MAAM;AAAA,EAC1C,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,IAAI;AACN,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,kBAAkB,mBAAmB,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,YAAY,IAAI,WAAS,cAAc,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC/K,WAAW,eAAe;AAAA,EACxB,OAAO;AAAA,EACP,aAAa;AAAA,EACb,KAAK;AAAA,EACL,MAAM;AACR;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,QAAM,kBAAkB,SAAS,cAAc,MAAM,UAAU,MAAM,KAAM,MAAM,KAAK;AACtF,QAAM,kBAAkB,MAAM,UAAU,MAAM,MAAM,OAAO,aAAa,SAAS,cAAc,KAAK,MAAM,KAAK;AAC/G,SAAO,GAAI,CAAC,qBAAqB,WAAW,GAAG,GAAG,iBAAiB,eAAe;AACpF;AACA,IAAM,kBAAkB,sCAAO,IAAI,MAAM;AAAA,EACvC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,mCAAmC,+FAA+F,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,cAAc,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC5Q,gBAAgB,eAAe;AAAA,EAC7B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,2BAA2B,sCAAO,IAAI,MAAM;AAAA,EAChD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,gFAAgF,MAAM,GAAG,GAAG,WAAS,MAAM,MAAM,QAAQ,GAAG,SAAS,cAAc,KAAK,MAAM,OAAO,IAAI,CAAC,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACxO,yBAAyB,eAAe;AAAA,EACtC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,MAAI;AACJ,MAAI,MAAM,MAAM;AACd,QAAI;AACJ,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,cAAM;AACN;AAAA,MACF,KAAK;AACH,cAAM;AACN;AAAA,MACF,KAAK;AACH,cAAM;AACN;AAAA,MACF,KAAK;AACH,cAAM;AACN;AAAA,IACJ;AACA,sBAAkB,SAAS,KAAK,KAAK,MAAM,OAAO,GAAG;AAAA,EACvD,WAAW,MAAM,eAAe;AAC9B,UAAM,MAAM,MAAM,UAAU,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ;AAC5E,sBAAkB,SAAS,KAAK,KAAK,MAAM,OAAO,GAAG;AAAA,EACvD;AACA,SAAO,GAAI,CAAC,qBAAqB,GAAG,GAAG,eAAe;AACxD;AACA,IAAM,mBAAmB,WAAS;AAChC,QAAM,QAAQ,SAAS,cAAc,MAAM,UAAU,MAAM,KAAK,MAAM,KAAK;AAC3E,MAAI;AACJ,MAAI,MAAM,YAAY,MAAM,aAAa,QAAQ;AAC/C,cAAU;AAAA,EACZ,WAAW,MAAM,SAAS,SAAS;AACjC,cAAU,MAAM,MAAM,MAAM,OAAO;AAAA,EACrC,WAAW,MAAM,SAAS,SAAS;AACjC,cAAU,MAAM,MAAM,MAAM,OAAO;AAAA,EACrC,OAAO;AACL,cAAU,MAAM,MAAM,MAAM,OAAO;AAAA,EACrC;AACA,SAAO;AAAA;AAAA;AAAA,uBAGc;AAAA;AAAA;AAAA,eAGR;AAAA;AAAA;AAAA;AAAA;AAKf;AACA,IAAM,sBAAsB,sCAAO,UAAU,EAAE,MAAM;AAAA,EACnD,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,IAAI;AAAA,EACJ,aAAa;AACf,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,6BAA6B,mBAAmB,KAAK,yCAAyC,mBAAmB,GAAG,GAAG,WAAS,MAAM,MAAM,YAAY,YAAY,MAAM,IAAI,CAAC,GAAG,WAAS,cAAc,KAAK,GAAG,WAAS,MAAM,cAAc,iBAAiB,KAAK,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACvW,oBAAoB,eAAe;AAAA,EACjC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,cAAc,WAAS;AAC3B,QAAM,UAAU,MAAM,MAAM;AAC5B,QAAM,SAAS;AAAA,IACb,SAAS,MAAM,UAAU,QAAQ,MAAM,GAAG,IAAI,QAAQ,MAAM,GAAG;AAAA,IAC/D,SAAS,QAAQ,KAAK,GAAG;AAAA,IACzB,SAAS,MAAM,UAAU,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG;AAAA,IAC7D,UAAU,MAAM,UAAU,QAAQ,MAAM,GAAG,IAAI,QAAQ,KAAK,GAAG;AAAA,IAC/D,OAAO,MAAM,UAAU,QAAQ,OAAO,GAAG,IAAI,QAAQ,KAAK,GAAG;AAAA,IAC7D,SAAS,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG;AAAA,IAC3D,MAAM,MAAM,UAAU,QAAQ,OAAO,GAAG,IAAI,QAAQ,OAAO,GAAG;AAAA,IAC9D,UAAU,MAAM,UAAU,QAAQ,OAAO,MAAM,IAAI,QAAQ,OAAO,GAAG;AAAA,IACrE,UAAU,MAAM,UAAU,QAAQ,MAAM,GAAG,IAAI,QAAQ,MAAM,GAAG;AAAA,IAChE,SAAS,QAAQ,QAAQ,MAAM;AAAA,IAC/B,MAAM,MAAM,UAAU,QAAQ,QAAQ,GAAG,IAAI,QAAQ,KAAK,GAAG;AAAA,IAC7D,QAAQ,MAAM,UAAU,QAAQ,MAAM,GAAG,IAAI,QAAQ,MAAM,GAAG;AAAA,IAC9D,aAAa,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAAA,IAChE,OAAO,QAAQ,IAAI,GAAG;AAAA,IACtB,OAAO,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI,QAAQ,QAAQ,MAAM;AAAA,EAClE;AACA,SAAO,GAAI,CAAC,uGAAuG,4FAA4F,mFAAmF,mDAAmD,8HAA8H,iGAAiG,oJAAoJ,4BAA4B,gEAAgE,oCAAoC,6CAA6C,MAAM,iCAAiC,MAAM,kCAAkC,MAAM,oCAAoC,MAAM,iCAAiC,MAAM,qCAAqC,IAAI,GAAG,OAAO,SAAS,OAAO,aAAa,OAAO,OAAO,OAAO,UAAU,OAAO,MAAM,OAAO,OAAO,OAAO,SAAS,OAAO,QAAQ,OAAO,UAAU,OAAO,SAAS,OAAO,SAAS,iBAAiB,OAAO,OAAO,iBAAiB,OAAO,OAAO,iBAAiB,OAAO,SAAS,iBAAiB,OAAO,MAAM,iBAAiB,OAAO,QAAQ;AACp5C;AACA,IAAM,uBAAuB,sCAAO,KAAK,MAAM;AAAA,EAC7C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,uDAAuD,qGAAqG,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,YAAY,UAAU,WAAS,YAAY,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC1S,qBAAqB,eAAe;AAAA,EAClC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,sCAAO,IAAI,MAAM;AAAA,EACtC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,wEAAwE,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,QAAQ,OAAO,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACxL,eAAe,eAAe;AAAA,EAC5B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,aAAa,WAAS;AAC1B,QAAM,SAAS,MAAM,WAAW,GAAG,MAAM,MAAM,MAAM,OAAO;AAC5D,QAAM,OAAO,MAAM,MAAM,UAAU;AACnC,SAAO,GAAI,CAAC,WAAW,KAAK,WAAW,YAAY,GAAG,GAAG,MAAM,MAAM,MAAM,SAAS,SAAS,QAAQ,MAAM,IAAI;AACjH;AACA,IAAM,aAAa,sCAAO,UAAQ;AAChC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAC,QAAM,aAAa,uBAAS,KAAK,QAAQ,GAAG,KAAK;AAC1D,CAAC,EAAE,MAAM;AAAA,EACP,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,qDAAqD,KAAK,GAAG,GAAG,WAAS,WAAW,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACvJ,WAAW,eAAe;AAAA,EACxB,OAAO;AACT;AAEA,IAAM,aAAa,WAAS;AAC1B,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,GAAI,CAAC,cAAc,qBAAqB,gEAAgE,GAAG,GAAG,MAAM,QAAQ,OAAO,MAAM,UAAU,QAAQ,MAAM,QAAQ;AAClL;AACA,IAAM,eAAe;AACrB,IAAM,oBAAoB,sCAAO,GAAG,MAAM;AAAA,EACxC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,GAAG,WAAS,WAAW,KAAK,GAAG,WAAS,wBAAwB,cAAc,KAAK,CAAC;AACpG,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AACA,IAAM,iBAAiB;AACvB,IAAM,sBAAsB,sCAAO,GAAG,MAAM;AAAA,EAC1C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,GAAG,WAAS,WAAW,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACtG,oBAAoB,eAAe;AAAA,EACjC,OAAO;AACT;AAEA,IAAM,wBAAwB,WAAS;AACrC,QAAM,OAAO,MAAM,MAAM,MAAM;AAC/B,QAAM,aAAa,MAAM,UAAU,UAAU,GAAG,OAAO,QAAQ,GAAG;AAClE,SAAO,GAAI,CAAC,gBAAgB,KAAK,qBAAqB,oCAAoC,KAAK,qBAAqB,KAAK,qBAAqB,KAAK,qBAAqB,KAAK,iCAAiC,IAAI,GAAG,YAAY,mBAAmB,qBAAqB,mBAAmB,mBAAmB,mBAAmB,qBAAqB,qBAAqB,qBAAqB,qBAAqB,qBAAqB,UAAU;AACvb;AACA,IAAM,iBAAiB,WAAS;AAC9B,SAAO,GAAI,CAAC,gBAAgB,KAAK,GAAG,GAAG,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,MAAM,UAAU,EAAE,GAAG,MAAM,UAAU,WAAW,sBAAsB,KAAK,CAAC;AACrK;AACA,IAAM,aAAa;AACnB,IAAM,wBAAwB,sCAAO,UAAU,EAAE,MAAM;AAAA,EACrD,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,IAAI;AACN,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,WAAW,KAAK,aAAa,KAAK,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,KAAK,GAAG,MAAM,MAAM,MAAM,aAAa,GAAG,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,KAAK,GAAG,MAAM,MAAM,MAAM,YAAY,GAAG,WAAS,eAAe,KAAK,GAAG,WAAS,wBAAwB,YAAY,KAAK,CAAC;AACxU,sBAAsB,eAAe;AAAA,EACnC,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAM,eAAe;AACrB,IAAM,0BAA0B,sCAAO,UAAU,EAAE,MAAM;AAAA,EACvD,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,IAAI;AACN,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,GAAG,WAAS,eAAe,KAAK,GAAG,WAAS,wBAAwB,cAAc,KAAK,CAAC;AACxG,wBAAwB,eAAe;AAAA,EACrC,OAAO;AAAA,EACP,OAAO;AACT;AAEA,IAAM,eAAe;AACrB,IAAM,kBAAkB,sCAAO,EAAE,MAAM;AAAA,EACrC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,iCAAiC,qCAAqC,MAAM,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,QAAQ,OAAO,WAAS,MAAM,MAAM,YAAY,YAAY,MAAM,IAAI,CAAC,GAAG,WAAS,wBAAwB,cAAc,KAAK,CAAC;AAChP,gBAAgB,eAAe;AAAA,EAC7B,OAAO;AACT;AAEA,IAAM,SAAK,0BAAW,CAAC,MAAM,QAAQ;AACnC,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAA,QAAM,cAAc,YAAYD,UAAS;AAAA,IAC9C,IAAI;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,EACR,GAAG,KAAK,CAAC;AACX,CAAC;AACD,GAAG,cAAc;AACjB,GAAG,YAAY;AAAA,EACb,KAAK,mBAAAE,QAAU;AAAA,EACf,QAAQ,mBAAAA,QAAU;AAAA,EAClB,aAAa,mBAAAA,QAAU;AACzB;AACA,GAAG,eAAe;AAAA,EAChB,KAAK;AACP;AAEA,IAAM,SAAK,0BAAW,CAAC,MAAM,QAAQ;AACnC,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAD,QAAM,cAAc,YAAYD,UAAS;AAAA,IAC9C,IAAI;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,EACR,GAAG,KAAK,CAAC;AACX,CAAC;AACD,GAAG,cAAc;AACjB,GAAG,YAAY;AAAA,EACb,KAAK,mBAAAE,QAAU;AAAA,EACf,QAAQ,mBAAAA,QAAU;AAAA,EAClB,aAAa,mBAAAA,QAAU;AACzB;AACA,GAAG,eAAe;AAAA,EAChB,KAAK;AACP;AAEA,IAAM,SAAK,0BAAW,CAAC,MAAM,QAAQ;AACnC,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAD,QAAM,cAAc,YAAYD,UAAS;AAAA,IAC9C,IAAI;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,EACR,GAAG,KAAK,CAAC;AACX,CAAC;AACD,GAAG,cAAc;AACjB,GAAG,YAAY;AAAA,EACb,KAAK,mBAAAE,QAAU;AAAA,EACf,QAAQ,mBAAAA,QAAU;AAAA,EAClB,aAAa,mBAAAA,QAAU;AACzB;AACA,GAAG,eAAe;AAAA,EAChB,KAAK;AACP;AAEA,IAAM,SAAK,0BAAW,CAAC,MAAM,QAAQ;AACnC,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAD,QAAM,cAAc,YAAYD,UAAS;AAAA,IAC9C,IAAI;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,EACR,GAAG,KAAK,CAAC;AACX,CAAC;AACD,GAAG,cAAc;AACjB,GAAG,YAAY;AAAA,EACb,KAAK,mBAAAE,QAAU;AAAA,EACf,QAAQ,mBAAAA,QAAU;AACpB;AACA,GAAG,eAAe;AAAA,EAChB,KAAK;AACP;AAEA,IAAM,UAAM,0BAAW,CAAC,MAAM,QAAQ;AACpC,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAD,QAAM,cAAc,YAAYD,UAAS;AAAA,IAC9C,IAAI;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,EACR,GAAG,KAAK,CAAC;AACX,CAAC;AACD,IAAI,cAAc;AAClB,IAAI,YAAY;AAAA,EACd,KAAK,mBAAAE,QAAU;AAAA,EACf,QAAQ,mBAAAA,QAAU;AACpB;AACA,IAAI,eAAe;AAAA,EACjB,KAAK;AACP;AAEA,IAAM,WAAO,0BAAW,CAAC,MAAM,QAAQ;AACrC,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAD,QAAM,cAAc,YAAYD,UAAS;AAAA,IAC9C,IAAI;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,EACR,GAAG,KAAK,CAAC;AACX,CAAC;AACD,KAAK,cAAc;AACnB,KAAK,YAAY;AAAA,EACf,KAAK,mBAAAE,QAAU;AAAA,EACf,QAAQ,mBAAAA,QAAU;AACpB;AACA,KAAK,eAAe;AAAA,EAClB,KAAK;AACP;AAEA,IAAM,aAAa,cAAAD,QAAM,WAAW,CAAC,OAAO,QAAQ,cAAAA,QAAM,cAAc,kBAAkBD,UAAS;AAAA,EACjG;AACF,GAAG,KAAK,CAAC,CAAC;AACV,WAAW,cAAc;AACzB,WAAW,YAAY;AAAA,EACrB,MAAM,mBAAAE,QAAU,MAAM,IAAI;AAC5B;AACA,WAAW,eAAe;AAAA,EACxB,MAAM;AACR;AAEA,IAAM,WAAO,0BAAW,CAAC,MAAM,QAAQ;AACrC,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAD,QAAM,cAAc,YAAYD,UAAS;AAAA,IAC9C;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,KAAK,cAAc;AACnB,KAAK,YAAY;AAAA,EACf,KAAK,mBAAAE,QAAU,MAAM,GAAG;AAAA,EACxB,MAAM,mBAAAA,QAAU,MAAM,YAAY;AACpC;AACA,KAAK,eAAe;AAAA,EAClB,KAAK;AAAA,EACL,MAAM;AACR;AAEA,IAAM,YAAY,cAAAD,QAAM,WAAW,CAAC,MAAM,QAAQ;AAChD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,mBAAe,sBAAO,IAAI;AAChC,QAAM,OAAO,MAAM,QAAQ,QAAQ,IAAI,SAAS,CAAC,IAAI;AACrD,QAAM,iBAAa,uBAAQ,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,MAAM,QAAQ,CAAC;AACnE,QAAM,oBAAoB,gBAAgB;AAAA,IACxC;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,UAAQ;AACtB,QAAI;AACJ,QAAI,aAAa,QAAQ;AACvB,YAAM,QAAQ,KAAK,KAAK,WAAS,EAAE,MAAM,SAAS,MAAM,YAAY,GAAG;AACvE,UAAI,OAAO;AACT,YAAI,MAAM,MAAM,SAAS,SAAS,GAAG;AACnC,mBAAS;AAAA,QACX,WAAW,MAAM,MAAM,SAAS,UAAU,GAAG;AAC3C,mBAAS;AAAA,QACX,WAAW,MAAM,MAAM,SAAS,OAAO,GAAG;AACxC,mBAAS;AAAA,QACX,WAAW,MAAM,MAAM,SAAS,MAAM,GAAG;AACvC,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,cAAAA,QAAM,cAAc,0BAA0BD,UAAS,CAAC,GAAG,gBAAgB;AAAA,IAChF,KAAK;AAAA,IACL,UAAU;AAAA,EACZ,CAAC,GAAG,cAAAC,QAAM,cAAc,cAAW;AAAA,IACjC,OAAO;AAAA,IACP,MAAM,OAAO,KAAK,KAAK,IAAI;AAAA,IAC3B,UAAU,UAAU,SAAS,QAAQ,IAAI,WAAW;AAAA,EACtD,GAAG,WAAS;AACV,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,cAAAA,QAAM,cAAc,iBAAiBD,UAAS;AAAA,MACnD;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,KAAK,GAAG,OAAO,IAAI,CAAC,MAAM,UAAU,cAAAC,QAAM,cAAc,qBAAqBD,UAAS,CAAC,GAAG,aAAa;AAAA,MACxG;AAAA,IACF,CAAC,GAAG;AAAA,MACF,KAAK;AAAA,MACL;AAAA,MACA,eAAe,kBAAkB,eAAe,SAAS,QAAQ,CAAC;AAAA,MAClE;AAAA,MACA;AAAA,MACA,MAAM,QAAQ,IAAI;AAAA,MAClB;AAAA,IACF,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,aAAa,cAAAC,QAAM,cAAc,sBAAsBD,UAAS,CAAC,GAAG,cAAc;AAAA,MACrG;AAAA,IACF,CAAC,GAAG;AAAA,MACF,KAAK;AAAA,MACL;AAAA,IACF,CAAC,GAAG,MAAM,QAAQ,OAAO,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC;AAAA,EAC5C,CAAC,CAAC;AACJ,CAAC;AACD,UAAU,cAAc;AACxB,UAAU,eAAe;AAAA,EACvB,UAAU;AAAA,EACV,MAAM;AACR;AAEA,IAAM,eAAW,0BAAW,CAAC,MAAM,QAAQ;AACzC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,MAAI,cAAc;AAClB,MAAI,UAAU,QAAW;AACvB,kBAAc;AAAA,EAChB,WAAW,OAAO,aAAa,UAAU;AACvC,kBAAc;AAAA,EAChB;AACA,SAAO,cAAAC,QAAM,cAAc,gBAAgBD,UAAS;AAAA,IAClD,IAAI;AAAA,IACJ;AAAA,IACA,OAAO;AAAA,EACT,GAAG,KAAK,GAAG,QAAQ;AACrB,CAAC;AACD,SAAS,cAAc;AACvB,SAAS,YAAY;AAAA,EACnB,OAAO,mBAAAE,QAAU;AAAA,EACjB,KAAK,mBAAAA,QAAU;AACjB;AACA,SAAS,eAAe;AAAA,EACtB,KAAK;AACP;AAEA,IAAM,gBAAY,0BAAW,CAAC,OAAO,QAAQ,cAAAD,QAAM,cAAc,iBAAiBD,UAAS;AAAA,EACzF;AACF,GAAG,KAAK,CAAC,CAAC;AACV,UAAU,cAAc;AACxB,UAAU,YAAY;AAAA,EACpB,MAAM,mBAAAE,QAAU,MAAM,IAAI;AAC5B;AACA,UAAU,eAAe;AAAA,EACvB,MAAM;AACR;AAEA,IAAM,yBAAqB,6BAAc,MAAS;AAClD,IAAM,wBAAwB,MAAM;AAClC,QAAM,kBAAc,0BAAW,kBAAkB;AACjD,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,oEAAoE;AAAA,EACtF;AACA,SAAO;AACT;AAEA,IAAM,sBAAkB,0BAAW,CAAC,OAAO,QAAQ;AACjD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,sBAAsB;AAC1B,SAAO,cAAAD,QAAM,cAAc,uBAAuBD,UAAS;AAAA,IACzD;AAAA,IACA,OAAO;AAAA,EACT,GAAG,KAAK,CAAC;AACX,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,SAAS;AAEf,IAAM,uBAAuB,cAAAC,QAAM,WAAW,CAAC,MAAM,QAAQ;AAC3D,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAQ,uBAAQ,OAAO;AAAA,IAC3B;AAAA,EACF,IAAI,CAAC,IAAI,CAAC;AACV,SAAO,cAAAA,QAAM,cAAc,mBAAmB,UAAU;AAAA,IACtD;AAAA,EACF,GAAG,cAAAA,QAAM,cAAc,mBAAmBD,UAAS;AAAA,IACjD;AAAA,IACA,UAAU;AAAA,EACZ,GAAG,KAAK,CAAC,CAAC;AACZ,CAAC;AACD,qBAAqB,cAAc;AACnC,qBAAqB,YAAY;AAAA,EAC/B,MAAM,mBAAAE,QAAU,MAAM,IAAI;AAAA,EAC1B,MAAM,mBAAAA,QAAU,MAAM,iBAAiB;AACzC;AACA,qBAAqB,eAAe;AAAA,EAClC,MAAM;AAAA,EACN,MAAM;AACR;AACA,IAAM,cAAc;AACpB,YAAY,OAAO;AAEnB,IAAM,2BAAuB,6BAAc,MAAS;AACpD,IAAM,0BAA0B,MAAM;AACpC,QAAM,kBAAc,0BAAW,oBAAoB;AACnD,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,sEAAsE;AAAA,EACxF;AACA,SAAO;AACT;AAEA,IAAM,wBAAoB,0BAAW,CAAC,OAAO,QAAQ;AACnD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,wBAAwB;AAC5B,SAAO,cAAAD,QAAM,cAAc,yBAAyBD,UAAS;AAAA,IAC3D;AAAA,IACA,OAAO;AAAA,EACT,GAAG,KAAK,CAAC;AACX,CAAC;AACD,kBAAkB,cAAc;AAChC,IAAM,OAAO;AAEb,IAAM,6BAAyB,0BAAW,CAAC,MAAM,QAAQ;AACvD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAQ,uBAAQ,OAAO;AAAA,IAC3B;AAAA,EACF,IAAI,CAAC,IAAI,CAAC;AACV,SAAO,cAAAC,QAAM,cAAc,qBAAqB,UAAU;AAAA,IACxD;AAAA,EACF,GAAG,cAAAA,QAAM,cAAc,qBAAqBD,UAAS;AAAA,IACnD;AAAA,IACA,UAAU;AAAA,EACZ,GAAG,KAAK,CAAC,CAAC;AACZ,CAAC;AACD,uBAAuB,cAAc;AACrC,uBAAuB,YAAY;AAAA,EACjC,MAAM,mBAAAE,QAAU,MAAM,IAAI;AAAA,EAC1B,MAAM,mBAAAA,QAAU,MAAM,mBAAmB;AAC3C;AACA,uBAAuB,eAAe;AAAA,EACpC,MAAM;AAAA,EACN,MAAM;AACR;AACA,IAAM,gBAAgB;AACtB,cAAc,OAAO;AAErB,IAAM,qBAAqB,WAAS,cAAAD,QAAM,cAAc,YAAYD,UAAS;AAAA,EAC3E,SAAS;AACX,GAAG,KAAK,CAAC;AACT,mBAAmB,cAAc;AACjC,IAAM,YAAY;AAElB,IAAM,gBAAgB,WAAS,cAAAC,QAAM,cAAc,YAAY,KAAK;AACpE,cAAc,cAAc;AAC5B,IAAM,OAAO;AAEb,IAAM,oBAAgB,0BAAW,CAAC,MAAM,QAAQ;AAC9C,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAA,QAAM,cAAc,YAAYD,UAAS;AAAA,IAC9C,IAAI;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,EACR,GAAG,KAAK,CAAC;AACX,CAAC;AACD,cAAc,cAAc;AAC5B,cAAc,YAAY;AAAA,EACxB,KAAK,mBAAAE,QAAU;AAAA,EACf,QAAQ,mBAAAA,QAAU;AAAA,EAClB,aAAa,mBAAAA,QAAU;AAAA,EACvB,KAAK,mBAAAA,QAAU;AACjB;AACA,cAAc,eAAe;AAAA,EAC3B,KAAK;AACP;AACA,IAAM,OAAO;AACb,KAAK,OAAO;AACZ,KAAK,YAAY;", "names": ["import_react", "import_prop_types", "lang", "Prism", "i", "Component", "Highlight", "Prism", "import_react", "debounce", "React", "PropTypes", "_extends", "React", "PropTypes"]}