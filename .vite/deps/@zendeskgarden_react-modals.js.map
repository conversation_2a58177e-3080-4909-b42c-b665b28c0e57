{"version": 3, "sources": ["../../node_modules/react-fast-compare/index.js", "../../node_modules/@zendeskgarden/react-modals/dist/index.esm.js", "../../node_modules/@zendeskgarden/container-modal/dist/index.esm.js", "../../node_modules/@zendeskgarden/container-focusjail/dist/index.esm.js", "../../node_modules/tabbable/src/index.js", "../../node_modules/react-popper/lib/esm/Popper.js", "../../node_modules/react-popper/lib/esm/Manager.js", "../../node_modules/react-popper/lib/esm/utils.js", "../../node_modules/react-popper/lib/esm/usePopper.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/userAgent.js", "../../node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/format.js", "../../node_modules/@popperjs/core/lib/utils/validateModifiers.js", "../../node_modules/@popperjs/core/lib/utils/uniqueBy.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../node_modules/react-popper/lib/esm/Reference.js"], "sourcesContent": ["/* global Map:readonly, Set:readonly, ArrayBuffer:readonly */\n\nvar hasElementType = typeof Element !== 'undefined';\nvar hasMap = typeof Map === 'function';\nvar hasSet = typeof Set === 'function';\nvar hasArrayBuffer = typeof ArrayBuffer === 'function' && !!ArrayBuffer.isView;\n\n// Note: We **don't** need `envHasBigInt64Array` in fde es6/index.js\n\nfunction equal(a, b) {\n  // START: fast-deep-equal es6/index.js 3.1.3\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    // START: Modifications:\n    // 1. Extra `has<Type> &&` helpers in initial condition allow es6 code\n    //    to co-exist with es5.\n    // 2. Replace `for of` with es5 compliant iteration using `for`.\n    //    Basically, take:\n    //\n    //    ```js\n    //    for (i of a.entries())\n    //      if (!b.has(i[0])) return false;\n    //    ```\n    //\n    //    ... and convert to:\n    //\n    //    ```js\n    //    it = a.entries();\n    //    while (!(i = it.next()).done)\n    //      if (!b.has(i.value[0])) return false;\n    //    ```\n    //\n    //    **Note**: `i` access switches to `i.value`.\n    var it;\n    if (hasMap && (a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!equal(i.value[1], b.get(i.value[0]))) return false;\n      return true;\n    }\n\n    if (hasSet && (a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      return true;\n    }\n    // END: Modifications\n\n    if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    // START: Modifications:\n    // Apply guards for `Object.create(null)` handling. See:\n    // - https://github.com/FormidableLabs/react-fast-compare/issues/64\n    // - https://github.com/epoberezkin/fast-deep-equal/issues/49\n    if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === 'function' && typeof b.valueOf === 'function') return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString && typeof a.toString === 'function' && typeof b.toString === 'function') return a.toString() === b.toString();\n    // END: Modifications\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    // END: fast-deep-equal\n\n    // START: react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element) return false;\n\n    // custom handling for React/Preact\n    for (i = length; i-- !== 0;) {\n      if ((keys[i] === '_owner' || keys[i] === '__v' || keys[i] === '__o') && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner\n        // Preact-specific: avoid traversing Preact elements' __v and __o\n        //    __v = $_original / $_vnode\n        //    __o = $_owner\n        // These properties contain circular references and are not needed when\n        // comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of elements\n\n        continue;\n      }\n\n      // all other properties should be traversed as usual\n      if (!equal(a[keys[i]], b[keys[i]])) return false;\n    }\n    // END: react-fast-compare\n\n    // START: fast-deep-equal\n    return true;\n  }\n\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function isEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if (((error.message || '').match(/stack|recursion/i))) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('react-fast-compare cannot handle circular refs');\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};\n", "/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport * as React from 'react';\nimport React__default, { createContext, useContext, forwardRef, useRef, useState, useEffect, useMemo } from 'react';\nimport ReactDOM, { createPortal } from 'react-dom';\nimport styled, { keyframes, css, ThemeContext } from 'styled-components';\nimport PropTypes from 'prop-types';\nimport { getColor, retrieveComponentStyles, DEFAULT_THEME, getLineHeight, mediaQuery, menuStyles, arrowStyles, useDocument, useText } from '@zendeskgarden/react-theming';\nimport { useModal } from '@zendeskgarden/container-modal';\nimport { useFocusVisible } from '@zendeskgarden/container-focusvisible';\nimport mergeRefs from 'react-merge-refs';\nimport { usePopper } from 'react-popper';\nimport { CSSTransition } from 'react-transition-group';\n\nfunction _extends$2() {\n  _extends$2 = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends$2.apply(this, arguments);\n}\n\nfunction isDocument(element) {\n  return 'nodeType' in element && element.nodeType === document.DOCUMENT_NODE;\n}\n\nfunction isWindow(node) {\n  if ('window' in node && node.window === node) return node;\n  if (isDocument(node)) return node.defaultView || false;\n  return false;\n}\n\nfunction ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}\n\nfunction ownerWindow(node) {\n  var doc = ownerDocument(node);\n  return doc && doc.defaultView || window;\n}\n\nfunction getComputedStyle(node, psuedoElement) {\n  return ownerWindow(node).getComputedStyle(node, psuedoElement);\n}\n\nvar rUpper = /([A-Z])/g;\nfunction hyphenate(string) {\n  return string.replace(rUpper, '-$1').toLowerCase();\n}\n\nvar msPattern = /^ms-/;\nfunction hyphenateStyleName(string) {\n  return hyphenate(string).replace(msPattern, '-ms-');\n}\n\nvar supportedTransforms = /^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;\nfunction isTransform(value) {\n  return !!(value && supportedTransforms.test(value));\n}\n\nfunction style(node, property) {\n  var css = '';\n  var transforms = '';\n  if (typeof property === 'string') {\n    return node.style.getPropertyValue(hyphenateStyleName(property)) || getComputedStyle(node).getPropertyValue(hyphenateStyleName(property));\n  }\n  Object.keys(property).forEach(function (key) {\n    var value = property[key];\n    if (!value && value !== 0) {\n      node.style.removeProperty(hyphenateStyleName(key));\n    } else if (isTransform(key)) {\n      transforms += key + \"(\" + value + \") \";\n    } else {\n      css += hyphenateStyleName(key) + \": \" + value + \";\";\n    }\n  });\n  if (transforms) {\n    css += \"transform: \" + transforms + \";\";\n  }\n  node.style.cssText += \";\" + css;\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n\nvar size;\nfunction scrollbarSize(recalc) {\n  if (!size && size !== 0 || recalc) {\n    if (canUseDOM) {\n      var scrollDiv = document.createElement('div');\n      scrollDiv.style.position = 'absolute';\n      scrollDiv.style.top = '-9999px';\n      scrollDiv.style.width = '50px';\n      scrollDiv.style.height = '50px';\n      scrollDiv.style.overflow = 'scroll';\n      document.body.appendChild(scrollDiv);\n      size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      document.body.removeChild(scrollDiv);\n    }\n  }\n  return size;\n}\n\nconst COMPONENT_ID$j = 'modals.backdrop';\nconst animationName$1 = keyframes([\"0%{opacity:0;}100%{opacity:1;}\"]);\nconst StyledBackdrop = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$j,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledBackdrop\",\n  componentId: \"sc-mzdjpo-0\"\n})([\"display:flex;position:fixed;top:0;right:0;bottom:0;left:0;align-items:\", \";justify-content:\", \";z-index:400;background-color:\", \";overflow:auto;-webkit-overflow-scrolling:touch;font-family:\", \";direction:\", \";animation:\", \";\", \";\"], props => props.isCentered && 'center', props => props.isCentered && 'center', props => getColor('neutralHue', 800, props.theme, 0.85), props => props.theme.fonts.system, props => props.theme.rtl && 'rtl', props => props.isAnimated && css([\"\", \" 0.15s ease-in\"], animationName$1), props => retrieveComponentStyles(COMPONENT_ID$j, props));\nStyledBackdrop.defaultProps = {\n  theme: DEFAULT_THEME\n};\nStyledBackdrop.propTypes = {\n  isCentered: PropTypes.bool,\n  isAnimated: PropTypes.bool\n};\n\nconst COMPONENT_ID$i = 'modals.body';\nconst StyledBody = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$i,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledBody\",\n  componentId: \"sc-14rzecg-0\"\n})([\"display:block;margin:0;padding:\", \";height:100%;overflow:auto;line-height:\", \";color:\", \";font-size:\", \";\", \";\"], props => `${props.theme.space.base * 5}px ${props.theme.space.base * 10}px`, props => getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md), props => props.theme.colors.foreground, props => props.theme.fontSizes.md, props => retrieveComponentStyles(COMPONENT_ID$i, props));\nStyledBody.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$h = 'modals.close';\nconst colorStyles = props => {\n  const backgroundColor = 'primaryHue';\n  const foregroundColor = 'neutralHue';\n  return css([\"background-color:transparent;color:\", \";&:hover{background-color:\", \";color:\", \";}&[data-garden-focus-visible]{box-shadow:\", \";}&:active{transition:background-color 0.1s ease-in-out,color 0.1s ease-in-out;background-color:\", \";color:\", \";}\"], getColor(foregroundColor, 600, props.theme), getColor(backgroundColor, 600, props.theme, 0.08), getColor(foregroundColor, 700, props.theme), props.theme.shadows.md(getColor(backgroundColor, 600, props.theme, 0.35)), getColor(backgroundColor, 600, props.theme, 0.2), getColor(foregroundColor, 800, props.theme));\n};\nconst BASE_MULTIPLIERS$1 = {\n  top: 2.5,\n  side: 6.5,\n  size: 10\n};\nconst StyledClose = styled.button.attrs({\n  'data-garden-id': COMPONENT_ID$h,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledClose\",\n  componentId: \"sc-iseudj-0\"\n})([\"display:block;position:absolute;top:\", \"px;\", \":\", \";transition:box-shadow 0.1s ease-in-out,background-color 0.25s ease-in-out,color 0.25s ease-in-out;border:none;border-radius:50%;background-color:transparent;cursor:pointer;padding:0;width:\", \"px;height:\", \"px;overflow:hidden;text-decoration:none;font-size:0;user-select:none;&::-moz-focus-inner{border:0;}&:focus{outline:none;}\", \" & > svg{vertical-align:middle;}\", \";\"], props => props.theme.space.base * BASE_MULTIPLIERS$1.top, props => props.theme.rtl ? 'left' : 'right', props => `${props.theme.space.base * BASE_MULTIPLIERS$1.side}px`, props => props.theme.space.base * BASE_MULTIPLIERS$1.size, props => props.theme.space.base * BASE_MULTIPLIERS$1.size, props => colorStyles(props), props => retrieveComponentStyles(COMPONENT_ID$h, props));\nStyledClose.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$g = 'modals.footer';\nconst StyledFooter = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$g,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledFooter\",\n  componentId: \"sc-d8pfdu-0\"\n})([\"display:flex;flex-shrink:0;align-items:center;justify-content:flex-end;border-top:\", \";padding:\", \";\", \";\"], props => props.isLarge && `${props.theme.borders.sm} ${getColor('neutralHue', 200, props.theme)}`, props => props.isLarge ? `${props.theme.space.base * 8}px ${props.theme.space.base * 10}px` : `${props.theme.space.base * 5}px ${props.theme.space.base * 10}px ${props.theme.space.base * 8}px`, props => retrieveComponentStyles(COMPONENT_ID$g, props));\nStyledFooter.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$f = 'modals.footer_item';\nconst StyledFooterItem = styled.span.attrs({\n  'data-garden-id': COMPONENT_ID$f,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledFooterItem\",\n  componentId: \"sc-1mb76hl-0\"\n})([\"display:flex;margin-\", \":\", \"px;min-width:0;&:first-child{margin-\", \":0;}\", \";\"], props => props.theme.rtl ? 'right' : 'left', props => props.theme.space.base * 5, props => props.theme.rtl ? 'right' : 'left', props => retrieveComponentStyles(COMPONENT_ID$f, props));\nStyledFooterItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$e = 'modals.header';\nconst StyledHeader = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$e,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledHeader\",\n  componentId: \"sc-1787r9v-0\"\n})([\"display:block;position:\", \";margin:0;border-bottom:\", \" \", \";padding:\", \";\", \"  line-height:\", \";color:\", \";font-size:\", \";font-weight:\", \";\", \";\"], props => props.isDanger && 'relative', props => props.theme.borders.sm, getColor('neutralHue', 200), props => `${props.theme.space.base * 5}px ${props.theme.space.base * 10}px`, props => props.isCloseButtonPresent && `padding-${props.theme.rtl ? 'left' : 'right'}: ${props.theme.space.base * (BASE_MULTIPLIERS$1.size + BASE_MULTIPLIERS$1.side + 2)}px;`, props => getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md), props => props.isDanger ? getColor('dangerHue', 600, props.theme) : props.theme.colors.foreground, props => props.theme.fontSizes.md, props => props.theme.fontWeights.semibold, props => retrieveComponentStyles(COMPONENT_ID$e, props));\nStyledHeader.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nvar _g, _circle;\nfunction _extends$1() { _extends$1 = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends$1.apply(this, arguments); }\nvar SvgAlertErrorStroke = function SvgAlertErrorStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends$1({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 16,\n    height: 16,\n    focusable: \"false\",\n    viewBox: \"0 0 16 16\",\n    \"aria-hidden\": \"true\"\n  }, props), _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    fill: \"none\",\n    stroke: \"currentColor\"\n  }, /*#__PURE__*/React.createElement(\"circle\", {\n    cx: 7.5,\n    cy: 8.5,\n    r: 7\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    d: \"M7.5 4.5V9\"\n  }))), _circle || (_circle = /*#__PURE__*/React.createElement(\"circle\", {\n    cx: 7.5,\n    cy: 12,\n    r: 1,\n    fill: \"currentColor\"\n  })));\n};\n\nconst StyledDangerIcon = styled(SvgAlertErrorStroke).withConfig({\n  displayName: \"StyledDangerIcon\",\n  componentId: \"sc-1kwbb39-0\"\n})([\"position:absolute;top:\", \"px;\", \":\", \";\"], props => props.theme.space.base * 5.5, props => props.theme.rtl ? 'right' : 'left', props => `${props.theme.space.base * 4}px`);\nStyledDangerIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$d = 'modals.modal';\nconst animationName = keyframes([\"0%{transform:scale(0);opacity:0;}50%{transform:scale(1.05);}100%{opacity:1;}\"]);\nconst boxShadow$1 = props => {\n  const {\n    theme\n  } = props;\n  const {\n    space,\n    shadows\n  } = theme;\n  const offsetY = `${space.base * 5}px`;\n  const blurRadius = `${space.base * 7}px`;\n  const color = getColor('neutralHue', 800, theme, 0.35);\n  return shadows.lg(offsetY, blurRadius, color);\n};\nconst sizeStyles$1 = props => {\n  return css([\"\", \"{width:\", \";}\"], mediaQuery('up', props.isLarge ? 'md' : 'sm', props.theme), props.isLarge ? props.theme.breakpoints.md : props.theme.breakpoints.sm);\n};\nconst StyledModal = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$d,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledModal\",\n  componentId: \"sc-1pe1axu-0\"\n})([\"display:flex;position:fixed;flex-direction:column;margin:\", \";border-radius:\", \";box-shadow:\", \";background-color:\", \";min-height:60px;max-height:calc(100vh - \", \"px);animation:\", \";animation-delay:0.01s;overflow:auto;direction:\", \";\", \" &:focus{outline:none;}@media (max-height:399px){top:\", \"px;bottom:auto;margin-bottom:\", \"px;max-height:none;}@media screen and (-ms-high-contrast:active),screen and (-ms-high-contrast:none){right:\", \";bottom:\", \";transform:\", \";}\", \";\"], props => props.isCentered ? '0' : `${props.theme.space.base * 12}px`, props => props.theme.borderRadii.md, boxShadow$1, props => props.theme.colors.background, props => props.theme.space.base * 24, props => props.isAnimated && css([\"\", \" 0.3s ease-in-out\"], animationName), props => props.theme.rtl && 'rtl', sizeStyles$1, props => props.theme.space.base * 6, props => props.theme.space.base * 6, props => props.isCentered && '50%', props => props.isCentered && '50%', props => props.isCentered && 'translate(50%, 50%)', props => retrieveComponentStyles(COMPONENT_ID$d, props));\nStyledModal.propTypes = {\n  isLarge: PropTypes.bool,\n  isAnimated: PropTypes.bool\n};\nStyledModal.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$c = 'modals.tooltip_modal.backdrop';\nconst StyledTooltipModalBackdrop = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$c,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledTooltipModalBackdrop\",\n  componentId: \"sc-1yaomgq-0\"\n})([\"position:fixed;top:0;right:0;bottom:0;left:0;z-index:400;overflow:hidden;-webkit-overflow-scrolling:touch;font-family:\", \";direction:\", \";&.garden-tooltip-modal-transition-exit-active{pointer-events:none;}&.garden-tooltip-modal-transition-exit-active div{transition:opacity 200ms;opacity:0;}\", \";\"], props => props.theme.fonts.system, props => props.theme.rtl && 'rtl', props => retrieveComponentStyles(COMPONENT_ID$c, props));\nStyledTooltipModalBackdrop.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nfunction getPopperPlacement(gardenPlacement) {\n  const gardenToPopperMapping = {\n    auto: 'auto',\n    top: 'top',\n    'top-start': 'top-start',\n    'top-end': 'top-end',\n    bottom: 'bottom',\n    'bottom-start': 'bottom-start',\n    'bottom-end': 'bottom-end',\n    end: 'right',\n    'end-top': 'right-start',\n    'end-bottom': 'right-end',\n    start: 'left',\n    'start-top': 'left-start',\n    'start-bottom': 'left-end'\n  };\n  return gardenToPopperMapping[gardenPlacement];\n}\nfunction getRtlPopperPlacement(gardenPlacement) {\n  const rtlPlacementMappings = {\n    left: 'right',\n    'left-start': 'right-start',\n    'left-end': 'right-end',\n    'top-start': 'top-end',\n    'top-end': 'top-start',\n    right: 'left',\n    'right-start': 'left-start',\n    'right-end': 'left-end',\n    'bottom-start': 'bottom-end',\n    'bottom-end': 'bottom-start'\n  };\n  const popperPlacement = getPopperPlacement(gardenPlacement);\n  return rtlPlacementMappings[popperPlacement] || popperPlacement;\n}\nfunction getArrowPosition(popperPlacement) {\n  const arrowPositionMappings = {\n    top: 'bottom',\n    'top-start': 'bottom-left',\n    'top-end': 'bottom-right',\n    right: 'left',\n    'right-start': 'left-top',\n    'right-end': 'left-bottom',\n    bottom: 'top',\n    'bottom-start': 'top-left',\n    'bottom-end': 'top-right',\n    left: 'right',\n    'left-start': 'right-top',\n    'left-end': 'right-bottom'\n  };\n  return arrowPositionMappings[popperPlacement] || 'top';\n}\nfunction getMenuPosition(popperPlacement) {\n  return popperPlacement ? popperPlacement.split('-')[0] : 'bottom';\n}\n\nconst StyledTooltipWrapper = styled.div.attrs(props => ({\n  className: props.isAnimated && 'is-animated'\n})).withConfig({\n  displayName: \"StyledTooltipWrapper\",\n  componentId: \"sc-1xk05kf-0\"\n})([\"\", \";\"], props => menuStyles(getMenuPosition(props.placement), {\n  theme: props.theme,\n  hidden: false,\n  margin: '0',\n  zIndex: props.zIndex,\n  animationModifier: '.is-animated'\n}));\nStyledTooltipWrapper.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$b = 'modals.tooltip_modal';\nconst StyledTooltipModal = styled.div.attrs(props => ({\n  'data-garden-id': COMPONENT_ID$b,\n  'data-garden-version': '8.67.0',\n  className: props.isAnimated && 'is-animated'\n})).withConfig({\n  displayName: \"StyledTooltipModal\",\n  componentId: \"sc-42ubfr-0\"\n})([\"padding:\", \"px;width:400px;\", \";\", \";\"], props => props.theme.space.base * 5, props => {\n  const computedArrowStyles = arrowStyles(getArrowPosition(props.placement), {\n    size: `${props.theme.space.base * 2}px`,\n    inset: '1px',\n    animationModifier: '.is-animated'\n  });\n  if (props.isAnimated) {\n    return props.hasArrow && props.transitionState === 'entered' && computedArrowStyles;\n  }\n  return props.hasArrow && computedArrowStyles;\n}, props => retrieveComponentStyles(COMPONENT_ID$b, props));\nStyledTooltipModal.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$a = 'modals.tooltip_modal.title';\nconst sizeStyles = props => `\n  /* stylelint-disable-next-line property-no-unknown */\n  padding-${props.theme.rtl ? 'left' : 'right'}: ${props.theme.space.base * 8}px;\n  line-height: ${getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md)};\n  font-size: ${props.theme.fontSizes.md};\n`;\nconst StyledTooltipModalTitle = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$a,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledTooltipModalTitle\",\n  componentId: \"sc-11xjgjs-0\"\n})([\"margin:0;color:\", \";font-weight:\", \";\", \";\", \";\"], props => props.theme.colors.foreground, props => props.theme.fontWeights.semibold, props => sizeStyles(props), props => retrieveComponentStyles(COMPONENT_ID$a, props));\nStyledTooltipModalTitle.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$9 = 'modals.tooltip_modal.body';\nconst StyledTooltipModalBody = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$9,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledTooltipModalBody\",\n  componentId: \"sc-195dkzj-0\"\n})([\"display:block;margin:0;padding-top:\", \"px;line-height:\", \";color:\", \";font-size:\", \";\", \";\"], props => props.theme.space.base * 1.5, props => getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md), props => props.theme.colors.foreground, props => props.theme.fontSizes.md, props => retrieveComponentStyles(COMPONENT_ID$9, props));\nStyledTooltipModalBody.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$8 = 'modals.tooltip_modal.footer';\nconst StyledTooltipModalFooter = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$8,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledTooltipModalFooter\",\n  componentId: \"sc-fm36a9-0\"\n})([\"display:flex;flex-shrink:0;align-items:center;justify-content:flex-end;padding-top:\", \"px;\", \";\"], p => p.theme.space.base * 5, props => retrieveComponentStyles(COMPONENT_ID$8, props));\nStyledTooltipModalFooter.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$7 = 'modals.tooltip_modal.footer_item';\nconst StyledTooltipModalFooterItem = styled(StyledFooterItem).attrs({\n  'data-garden-id': COMPONENT_ID$7,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledTooltipModalFooterItem\",\n  componentId: \"sc-1nahj6p-0\"\n})([\"\", \";\"], props => retrieveComponentStyles(COMPONENT_ID$7, props));\nStyledTooltipModalFooterItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$6 = 'modals.tooltip_modal.close';\nconst StyledTooltipModalClose = styled(StyledClose).attrs({\n  'data-garden-id': COMPONENT_ID$6,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledTooltipModalClose\",\n  componentId: \"sc-1h2ke3q-0\"\n})([\"top:\", \"px;\", \":\", \";width:\", \"px;height:\", \"px;\", \";\"], props => props.theme.space.base * 3.5, props => props.theme.rtl ? 'left' : 'right', props => `${props.theme.space.base * 3}px`, props => props.theme.space.base * 8, props => props.theme.space.base * 8, props => retrieveComponentStyles(COMPONENT_ID$6, props));\nStyledTooltipModalClose.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$5 = 'modals.drawer_modal';\nconst DRAWER_WIDTH = 380;\nconst boxShadow = props => {\n  const {\n    theme\n  } = props;\n  const {\n    space,\n    shadows\n  } = theme;\n  const offsetY = `${space.base * 5}px`;\n  const blurRadius = `${space.base * 7}px`;\n  const color = getColor('neutralHue', 800, theme, 0.35);\n  return shadows.lg(offsetY, blurRadius, color);\n};\nconst StyledDrawerModal = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$5,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledDrawerModal\",\n  componentId: \"sc-i1sake-0\"\n})([\"display:flex;position:fixed;top:0;\", \":0;flex-direction:column;z-index:500;box-shadow:\", \";background:\", \";width:\", \"px;height:100%;overflow:auto;-webkit-overflow-scrolling:touch;font-family:\", \";direction:\", \";&.garden-drawer-transition-enter{transform:translateX(\", \"px);}&.garden-drawer-transition-enter-active{transform:translateX(0);transition:transform 0.25s ease-in-out;}&.garden-drawer-transition-exit-active{transform:translateX(\", \"px);transition:transform 0.25s ease-in-out;}&:focus{outline:none;}\", \";\"], props => props.theme.rtl ? 'left' : 'right', boxShadow, props => props.theme.colors.background, DRAWER_WIDTH, props => props.theme.fonts.system, props => props.theme.rtl && 'rtl', props => props.theme.rtl ? -DRAWER_WIDTH : DRAWER_WIDTH, props => props.theme.rtl ? -DRAWER_WIDTH : DRAWER_WIDTH, props => retrieveComponentStyles(COMPONENT_ID$5, props));\nStyledDrawerModal.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$4 = 'modals.drawer_modal.close';\nconst BASE_MULTIPLIERS = {\n  top: BASE_MULTIPLIERS$1.top,\n  side: 2,\n  size: BASE_MULTIPLIERS$1.size\n};\nconst StyledDrawerModalClose = styled(StyledClose).attrs({\n  'data-garden-id': COMPONENT_ID$4,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledDrawerModalClose\",\n  componentId: \"sc-hrnaom-0\"\n})([\"\", \":\", \";\", \";\"], props => props.theme.rtl ? 'left' : 'right', props => `${props.theme.space.base * BASE_MULTIPLIERS.side}px`, props => retrieveComponentStyles(COMPONENT_ID$4, props));\nStyledDrawerModalClose.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$3 = 'modals.drawer_modal.header';\nconst StyledDrawerModalHeader = styled(StyledHeader).attrs({\n  'data-garden-id': COMPONENT_ID$3,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledDrawerModalHeader\",\n  componentId: \"sc-1u04rqw-0\"\n})([\"padding:\", \"px;\", \"  \", \";\"], props => props.theme.space.base * 5, props => props.isCloseButtonPresent && `padding-${props.theme.rtl ? 'left' : 'right'}: ${props.theme.space.base * (BASE_MULTIPLIERS.size + BASE_MULTIPLIERS.side + 2)}px;`, props => retrieveComponentStyles(COMPONENT_ID$3, props));\nStyledDrawerModalHeader.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$2 = 'modals.drawer_modal.body';\nconst StyledDrawerModalBody = styled(StyledBody).attrs({\n  'data-garden-id': COMPONENT_ID$2,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledDrawerModalBody\",\n  componentId: \"sc-yafe2y-0\"\n})([\"padding:\", \"px;\", \";\"], props => props.theme.space.base * 5, props => retrieveComponentStyles(COMPONENT_ID$2, props));\nStyledDrawerModalBody.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$1 = 'modals.drawer_modal.footer';\nconst StyledDrawerModalFooter = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$1,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledDrawerModalFooter\",\n  componentId: \"sc-17if4ka-0\"\n})([\"display:flex;flex-shrink:0;justify-content:flex-end;border-top:\", \";padding:\", \"px;\", \";\"], props => `${props.theme.borders.sm} ${getColor('neutralHue', 200, props.theme)}`, props => props.theme.space.base * 5, props => retrieveComponentStyles(COMPONENT_ID$1, props));\nStyledDrawerModalFooter.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID = 'modals.drawer_modal.footer_item';\nconst StyledDrawerModalFooterItem = styled(StyledFooterItem).attrs({\n  'data-garden-id': COMPONENT_ID,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledDrawerModalFooterItem\",\n  componentId: \"sc-1vbl885-0\"\n})([\"\", \";\"], props => retrieveComponentStyles(COMPONENT_ID, props));\nStyledDrawerModalFooterItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst ModalsContext = createContext(undefined);\nconst useModalContext = () => {\n  const context = useContext(ModalsContext);\n  if (context === undefined) {\n    throw new Error('useModalContext must be used within a ModalsContext.Provider');\n  }\n  return context;\n};\n\nconst isOverflowing = element => {\n  const doc = ownerDocument(element);\n  const win = ownerWindow(doc);\n  const isBody = element && element.tagName.toLowerCase() === 'body';\n  if (!isWindow(doc) && !isBody) {\n    return element.scrollHeight > element.clientHeight;\n  }\n  const style = win.getComputedStyle(doc.body);\n  const marginLeft = parseInt(style.getPropertyValue('margin-left'), 10);\n  const marginRight = parseInt(style.getPropertyValue('margin-right'), 10);\n  return marginLeft + doc.body.clientWidth + marginRight < win.innerWidth;\n};\nconst Modal = forwardRef((_ref, ref) => {\n  let {\n    backdropProps,\n    children,\n    onClose,\n    isLarge,\n    isCentered,\n    isAnimated,\n    id,\n    appendToNode,\n    focusOnMount,\n    restoreFocus,\n    ...modalProps\n  } = _ref;\n  const theme = useContext(ThemeContext);\n  const modalRef = useRef(null);\n  const environment = useDocument(theme);\n  const [isCloseButtonPresent, setIsCloseButtonPresent] = useState(false);\n  const [hasHeader, setHasHeader] = useState(false);\n  const {\n    getBackdropProps,\n    getModalProps,\n    getTitleProps,\n    getContentProps,\n    getCloseProps\n  } = useModal({\n    idPrefix: id,\n    onClose,\n    modalRef,\n    focusOnMount,\n    restoreFocus\n  });\n  useFocusVisible({\n    scope: modalRef,\n    relativeDocument: environment\n  });\n  useEffect(() => {\n    if (!environment) {\n      return undefined;\n    }\n    const htmlElement = environment.querySelector('html');\n    const bodyElement = environment.querySelector('body');\n    let previousHtmlOverflow;\n    let previousBodyPaddingRight;\n    if (bodyElement) {\n      if (isOverflowing(bodyElement)) {\n        const scrollbarSize$1 = scrollbarSize();\n        const bodyPaddingRight = parseInt(style(bodyElement, 'paddingRight') || '0', 10);\n        previousBodyPaddingRight = bodyElement.style.paddingRight;\n        bodyElement.style.paddingRight = `${bodyPaddingRight + scrollbarSize$1}px`;\n      }\n      if (htmlElement) {\n        previousHtmlOverflow = htmlElement.style.overflow;\n        htmlElement.style.overflow = 'hidden';\n      }\n      return () => {\n        if (htmlElement) {\n          htmlElement.style.overflow = previousHtmlOverflow;\n        }\n        bodyElement.style.paddingRight = previousBodyPaddingRight;\n      };\n    }\n    return undefined;\n  }, [environment]);\n  const rootNode = useMemo(() => {\n    if (appendToNode) {\n      return appendToNode;\n    }\n    if (environment) {\n      return environment.body;\n    }\n    return undefined;\n  }, [appendToNode, environment]);\n  const value = useMemo(() => ({\n    isLarge,\n    isCloseButtonPresent,\n    hasHeader,\n    setHasHeader,\n    getTitleProps,\n    getContentProps,\n    getCloseProps,\n    setIsCloseButtonPresent\n  }), [isLarge, hasHeader, isCloseButtonPresent, getTitleProps, getContentProps, getCloseProps]);\n  const modalContainerProps = getModalProps({\n    'aria-describedby': undefined,\n    ...(hasHeader ? {} : {\n      'aria-labelledby': undefined\n    })\n  });\n  const attribute = hasHeader ? 'aria-labelledby' : 'aria-label';\n  const defaultValue = hasHeader ? modalContainerProps['aria-labelledby'] : 'Modal dialog';\n  const labelValue = hasHeader ? modalContainerProps['aria-labelledby'] : modalProps['aria-label'];\n  const ariaProps = {\n    [attribute]: useText(Modal, {\n      [attribute]: labelValue\n    }, attribute, defaultValue)\n  };\n  if (!rootNode) {\n    return null;\n  }\n  return createPortal( React__default.createElement(ModalsContext.Provider, {\n    value: value\n  }, React__default.createElement(StyledBackdrop, _extends$2({\n    isCentered: isCentered,\n    isAnimated: isAnimated\n  }, getBackdropProps(backdropProps)), React__default.createElement(StyledModal, _extends$2({\n    isCentered: isCentered,\n    isAnimated: isAnimated,\n    isLarge: isLarge\n  }, modalContainerProps, ariaProps, modalProps, {\n    ref: mergeRefs([ref, modalRef])\n  }), children))), rootNode);\n});\nModal.displayName = 'Modal';\nModal.propTypes = {\n  backdropProps: PropTypes.object,\n  isLarge: PropTypes.bool,\n  isAnimated: PropTypes.bool,\n  isCentered: PropTypes.bool,\n  focusOnMount: PropTypes.bool,\n  restoreFocus: PropTypes.bool,\n  onClose: PropTypes.func,\n  appendToNode: PropTypes.any\n};\nModal.defaultProps = {\n  isAnimated: true,\n  isCentered: true\n};\n\nconst Body$2 = forwardRef((props, ref) => {\n  const {\n    getContentProps\n  } = useModalContext();\n  return React__default.createElement(StyledBody, _extends$2({}, getContentProps(props), {\n    ref: ref\n  }));\n});\nBody$2.displayName = 'Body';\n\nvar _path;\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nvar SvgXStroke = function SvgXStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 16,\n    height: 16,\n    focusable: \"false\",\n    viewBox: \"0 0 16 16\",\n    \"aria-hidden\": \"true\"\n  }, props), _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    stroke: \"currentColor\",\n    strokeLinecap: \"round\",\n    d: \"M3 13L13 3m0 10L3 3\"\n  })));\n};\n\nconst Close$2 = forwardRef((props, ref) => {\n  const {\n    getCloseProps,\n    setIsCloseButtonPresent\n  } = useModalContext();\n  useEffect(() => {\n    setIsCloseButtonPresent(true);\n    return () => setIsCloseButtonPresent(false);\n  });\n  const ariaLabel = useText(Close$2, props, 'aria-label', 'Close modal');\n  return React__default.createElement(StyledClose, _extends$2({}, getCloseProps({\n    ...props,\n    'aria-label': ariaLabel\n  }), {\n    ref: ref\n  }), React__default.createElement(SvgXStroke, null));\n});\nClose$2.displayName = 'Close';\n\nconst Footer$2 = React__default.forwardRef((props, ref) => {\n  const {\n    isLarge\n  } = useModalContext();\n  return React__default.createElement(StyledFooter, _extends$2({\n    ref: ref,\n    isLarge: isLarge\n  }, props));\n});\nFooter$2.displayName = 'Footer';\n\nconst FooterItem$2 = React__default.forwardRef((props, ref) => React__default.createElement(StyledFooterItem, _extends$2({\n  ref: ref\n}, props)));\nFooterItem$2.displayName = 'FooterItem';\n\nconst Header$1 = forwardRef((_ref, ref) => {\n  let {\n    children,\n    tag,\n    ...other\n  } = _ref;\n  const {\n    isCloseButtonPresent,\n    hasHeader,\n    setHasHeader,\n    getTitleProps\n  } = useModalContext();\n  useEffect(() => {\n    if (!hasHeader && setHasHeader) {\n      setHasHeader(true);\n    }\n    return () => {\n      if (hasHeader && setHasHeader) {\n        setHasHeader(false);\n      }\n    };\n  }, [hasHeader, setHasHeader]);\n  return React__default.createElement(StyledHeader, _extends$2({}, getTitleProps(other), {\n    as: tag,\n    isCloseButtonPresent: isCloseButtonPresent,\n    ref: ref\n  }), other.isDanger && React__default.createElement(StyledDangerIcon, null), children);\n});\nHeader$1.displayName = 'Header';\nHeader$1.propTypes = {\n  isDanger: PropTypes.bool,\n  tag: PropTypes.any\n};\nHeader$1.defaultProps = {\n  tag: 'div'\n};\n\nconst TooltipModalContext = createContext(undefined);\nconst useTooltipModalContext = () => {\n  const context = useContext(TooltipModalContext);\n  if (context === undefined) {\n    throw new Error('Element must be used within a TooltipModal component.');\n  }\n  return context;\n};\n\nconst TitleComponent = forwardRef((_ref, ref) => {\n  let {\n    children,\n    tag,\n    ...other\n  } = _ref;\n  const {\n    getTitleProps,\n    hasTitle,\n    setHasTitle\n  } = useTooltipModalContext();\n  useEffect(() => {\n    if (!hasTitle && setHasTitle) {\n      setHasTitle(true);\n    }\n    return () => {\n      if (hasTitle && setHasTitle) {\n        setHasTitle(false);\n      }\n    };\n  }, [hasTitle, setHasTitle]);\n  return React__default.createElement(StyledTooltipModalTitle, _extends$2({}, getTitleProps(other), {\n    as: tag,\n    ref: ref\n  }), children);\n});\nTitleComponent.displayName = 'TooltipModal.Title';\nTitleComponent.propTypes = {\n  tag: PropTypes.any\n};\nTitleComponent.defaultProps = {\n  tag: 'div'\n};\nconst Title = TitleComponent;\n\nconst BodyComponent$1 = forwardRef((props, ref) => {\n  const {\n    getContentProps\n  } = useTooltipModalContext();\n  return React__default.createElement(StyledTooltipModalBody, _extends$2({}, getContentProps(props), {\n    ref: ref\n  }));\n});\nBodyComponent$1.displayName = 'TooltipModal.Body';\nconst Body$1 = BodyComponent$1;\n\nconst CloseComponent$1 = forwardRef((props, ref) => {\n  const {\n    getCloseProps\n  } = useTooltipModalContext();\n  const ariaLabel = useText(CloseComponent$1, props, 'aria-label', 'Close tooltip');\n  return React__default.createElement(StyledTooltipModalClose, _extends$2({}, getCloseProps({\n    ...props,\n    'aria-label': ariaLabel\n  }), {\n    ref: ref\n  }), React__default.createElement(SvgXStroke, null));\n});\nCloseComponent$1.displayName = 'TooltipModal.Close';\nconst Close$1 = CloseComponent$1;\n\nconst FooterComponent$1 = forwardRef((props, ref) => React__default.createElement(StyledTooltipModalFooter, _extends$2({\n  ref: ref\n}, props)));\nFooterComponent$1.displayName = 'TooltipModal.Footer';\nconst Footer$1 = FooterComponent$1;\n\nconst FooterItemComponent$1 = forwardRef((props, ref) => React__default.createElement(StyledTooltipModalFooterItem, _extends$2({\n  ref: ref\n}, props)));\nFooterItemComponent$1.displayName = 'TooltipModal.FooterItem';\nconst FooterItem$1 = FooterItemComponent$1;\n\nconst TooltipModalComponent = React__default.forwardRef((_ref, ref) => {\n  let {\n    referenceElement,\n    popperModifiers,\n    placement,\n    onClose,\n    hasArrow,\n    isAnimated,\n    zIndex,\n    backdropProps,\n    focusOnMount,\n    restoreFocus,\n    id,\n    ...props\n  } = _ref;\n  const theme = useContext(ThemeContext);\n  const previousReferenceElementRef = useRef();\n  const modalRef = useRef(null);\n  const transitionRef = useRef(null);\n  const [popperElement, setPopperElement] = useState();\n  const [hasTitle, setHasTitle] = useState(false);\n  const {\n    getTitleProps,\n    getCloseProps,\n    getContentProps,\n    getBackdropProps,\n    getModalProps\n  } = useModal({\n    idPrefix: id,\n    onClose,\n    modalRef,\n    focusOnMount,\n    restoreFocus: false\n  });\n  useEffect(() => {\n    if (!referenceElement && previousReferenceElementRef.current && restoreFocus) {\n      previousReferenceElementRef.current.focus();\n    }\n    previousReferenceElementRef.current = referenceElement;\n  }, [referenceElement, restoreFocus]);\n  const popperPlacement = useMemo(() => theme.rtl ? getRtlPopperPlacement(placement) : getPopperPlacement(placement), [placement, theme.rtl]);\n  const {\n    styles,\n    attributes,\n    state\n  } = usePopper(referenceElement, popperElement, {\n    placement: popperPlacement,\n    modifiers: [{\n      name: 'offset',\n      options: {\n        offset: [0, theme.space.base * 3]\n      }\n    }, ...(popperModifiers || [])]\n  });\n  const modalProps = getModalProps({\n    'aria-describedby': undefined,\n    ...(hasTitle ? {} : {\n      'aria-labelledby': undefined\n    })\n  });\n  const attribute = hasTitle ? 'aria-labelledby' : 'aria-label';\n  const defaultValue = hasTitle ? modalProps['aria-labelledby'] : 'Modal dialog';\n  const labelValue = hasTitle ? modalProps['aria-labelledby'] : props['aria-label'];\n  const ariaProps = {\n    [attribute]: useText(TooltipModalComponent, {\n      [attribute]: labelValue\n    }, attribute, defaultValue)\n  };\n  const value = {\n    hasTitle,\n    setHasTitle,\n    getTitleProps,\n    getContentProps,\n    getCloseProps\n  };\n  return React__default.createElement(CSSTransition, {\n    unmountOnExit: true,\n    timeout: isAnimated ? 200 : 0,\n    in: Boolean(referenceElement),\n    classNames: isAnimated ? 'garden-tooltip-modal-transition' : '',\n    nodeRef: transitionRef\n  }, transitionState => {\n    return React__default.createElement(TooltipModalContext.Provider, {\n      value: value\n    }, React__default.createElement(StyledTooltipModalBackdrop, _extends$2({}, getBackdropProps(), backdropProps, {\n      ref: transitionRef\n    }), React__default.createElement(StyledTooltipWrapper, _extends$2({\n      ref: setPopperElement,\n      style: styles.popper,\n      placement: state ? state.placement : undefined,\n      zIndex: zIndex,\n      isAnimated: isAnimated\n    }, attributes.popper), React__default.createElement(StyledTooltipModal, _extends$2({\n      transitionState: transitionState,\n      placement: state ? state.placement : 'top',\n      hasArrow: hasArrow,\n      isAnimated: isAnimated\n    }, modalProps, ariaProps, props, {\n      ref: mergeRefs([modalRef, ref])\n    })))));\n  });\n});\nTooltipModalComponent.displayName = 'TooltipModal';\nTooltipModalComponent.defaultProps = {\n  placement: 'auto',\n  hasArrow: true,\n  focusOnMount: true,\n  restoreFocus: true\n};\nTooltipModalComponent.propTypes = {\n  referenceElement: PropTypes.any,\n  popperModifiers: PropTypes.any,\n  placement: PropTypes.any,\n  isAnimated: PropTypes.bool,\n  hasArrow: PropTypes.bool,\n  zIndex: PropTypes.number,\n  onClose: PropTypes.func,\n  backdropProps: PropTypes.any,\n  focusOnMount: PropTypes.bool,\n  restoreFocus: PropTypes.bool\n};\nconst TooltipModal = TooltipModalComponent;\nTooltipModal.Body = Body$1;\nTooltipModal.Close = Close$1;\nTooltipModal.Footer = Footer$1;\nTooltipModal.FooterItem = FooterItem$1;\nTooltipModal.Title = Title;\n\nfunction activeElement(doc) {\n  if (doc === void 0) {\n    doc = ownerDocument();\n  }\n  try {\n    var active = doc.activeElement;\n    if (!active || !active.nodeName) return null;\n    return active;\n  } catch (e) {\n    return doc.body;\n  }\n}\n\nconst HeaderComponent = forwardRef((_ref, ref) => {\n  let {\n    tag,\n    ...other\n  } = _ref;\n  const {\n    isCloseButtonPresent,\n    hasHeader,\n    setHasHeader,\n    getTitleProps\n  } = useModalContext();\n  useEffect(() => {\n    if (!hasHeader && setHasHeader) {\n      setHasHeader(true);\n    }\n    return () => {\n      if (hasHeader && setHasHeader) {\n        setHasHeader(false);\n      }\n    };\n  }, [hasHeader, setHasHeader]);\n  return React__default.createElement(StyledDrawerModalHeader, _extends$2({}, getTitleProps(other), {\n    as: tag,\n    isCloseButtonPresent: isCloseButtonPresent,\n    ref: ref\n  }));\n});\nHeaderComponent.displayName = 'DrawerModal.Header';\nHeaderComponent.propTypes = {\n  tag: PropTypes.any\n};\nHeaderComponent.defaultProps = {\n  tag: 'div'\n};\nconst Header = HeaderComponent;\n\nconst BodyComponent = forwardRef((props, ref) => {\n  const {\n    getContentProps\n  } = useModalContext();\n  return React__default.createElement(StyledDrawerModalBody, _extends$2({}, getContentProps(props), {\n    ref: ref\n  }), props.children);\n});\nBodyComponent.displayName = 'DrawerModal.Body';\nconst Body = BodyComponent;\n\nconst CloseComponent = forwardRef((props, ref) => {\n  const {\n    getCloseProps,\n    setIsCloseButtonPresent\n  } = useModalContext();\n  useEffect(() => {\n    setIsCloseButtonPresent(true);\n    return () => setIsCloseButtonPresent(false);\n  });\n  const ariaLabel = useText(CloseComponent, props, 'aria-label', 'Close drawer');\n  return React__default.createElement(StyledDrawerModalClose, _extends$2({}, getCloseProps({\n    ...props,\n    'aria-label': ariaLabel\n  }), {\n    ref: ref\n  }), React__default.createElement(SvgXStroke, null));\n});\nCloseComponent.displayName = 'DrawerModal.Close';\nconst Close = CloseComponent;\n\nconst FooterComponent = forwardRef((props, ref) => React__default.createElement(StyledDrawerModalFooter, _extends$2({\n  ref: ref\n}, props)));\nFooterComponent.displayName = 'DrawerModal.Footer';\nconst Footer = FooterComponent;\n\nconst FooterItemComponent = forwardRef((props, ref) => React__default.createElement(StyledDrawerModalFooterItem, _extends$2({\n  ref: ref\n}, props)));\nFooterItemComponent.displayName = 'DrawerModal.FooterItem';\nconst FooterItem = FooterItemComponent;\n\nconst DrawerModalComponent = forwardRef((_ref, ref) => {\n  let {\n    id,\n    isOpen,\n    onClose,\n    backdropProps,\n    appendToNode,\n    focusOnMount,\n    restoreFocus,\n    ...props\n  } = _ref;\n  const modalRef = useRef(null);\n  const transitionRef = useRef(null);\n  const triggerRef = useRef(null);\n  const theme = useContext(ThemeContext);\n  const environment = useDocument(theme);\n  const [isCloseButtonPresent, setIsCloseButtonPresent] = useState(false);\n  const [hasHeader, setHasHeader] = useState(false);\n  useFocusVisible({\n    scope: modalRef,\n    relativeDocument: modalRef.current\n  });\n  const {\n    getTitleProps,\n    getCloseProps,\n    getContentProps,\n    getBackdropProps,\n    getModalProps\n  } = useModal({\n    idPrefix: id,\n    modalRef,\n    focusOnMount: false ,\n    restoreFocus: false ,\n    environment,\n    onClose\n  });\n  useEffect(() => {\n    if (environment) {\n      if (isOpen && modalRef.current) {\n        if (restoreFocus) {\n          triggerRef.current = activeElement(environment);\n        }\n        if (focusOnMount) {\n          modalRef.current.focus();\n        }\n      }\n      if (!isOpen && triggerRef.current) {\n        triggerRef.current.focus();\n      }\n    }\n    return () => {\n      if (!(restoreFocus && isOpen)) {\n        triggerRef.current = null;\n      }\n    };\n  }, [environment, restoreFocus, focusOnMount, isOpen]);\n  useEffect(() => {\n    if (!environment) {\n      return undefined;\n    }\n    const htmlElement = environment.querySelector('html');\n    let previousHtmlOverflow;\n    if (htmlElement && isOpen) {\n      previousHtmlOverflow = htmlElement.style.overflow;\n      htmlElement.style.overflow = 'hidden';\n    }\n    return () => {\n      if (htmlElement && isOpen) {\n        htmlElement.style.overflow = previousHtmlOverflow;\n      }\n    };\n  }, [environment, isOpen]);\n  const rootNode = useMemo(() => {\n    if (appendToNode) {\n      return appendToNode;\n    }\n    if (environment) {\n      return environment.body;\n    }\n    return undefined;\n  }, [appendToNode, environment]);\n  const value = useMemo(() => ({\n    isCloseButtonPresent,\n    hasHeader,\n    setHasHeader,\n    getTitleProps,\n    getContentProps,\n    getCloseProps,\n    setIsCloseButtonPresent\n  }), [isCloseButtonPresent, hasHeader, getTitleProps, getContentProps, getCloseProps]);\n  const modalProps = getModalProps({\n    'aria-describedby': undefined,\n    ...(hasHeader ? {} : {\n      'aria-labelledby': undefined\n    })\n  });\n  const attribute = hasHeader ? 'aria-labelledby' : 'aria-label';\n  const defaultValue = hasHeader ? modalProps['aria-labelledby'] : 'Modal dialog';\n  const labelValue = hasHeader ? modalProps['aria-labelledby'] : props['aria-label'];\n  const ariaProps = {\n    [attribute]: useText(DrawerModalComponent, {\n      [attribute]: labelValue\n    }, attribute, defaultValue)\n  };\n  if (!rootNode) {\n    return null;\n  }\n  return ReactDOM.createPortal( React__default.createElement(ModalsContext.Provider, {\n    value: value\n  }, React__default.createElement(CSSTransition, {\n    in: isOpen,\n    timeout: 250,\n    unmountOnExit: true,\n    classNames: \"garden-drawer-transition\",\n    nodeRef: transitionRef\n  }, React__default.createElement(StyledBackdrop, _extends$2({\n    isAnimated: true\n  }, getBackdropProps(backdropProps)), React__default.createElement(StyledDrawerModal, _extends$2({}, modalProps, ariaProps, props, {\n    ref: mergeRefs([ref, modalRef, transitionRef])\n  }))))), rootNode);\n});\nDrawerModalComponent.displayName = 'DrawerModal';\nDrawerModalComponent.propTypes = {\n  backdropProps: PropTypes.object,\n  focusOnMount: PropTypes.bool,\n  restoreFocus: PropTypes.bool,\n  onClose: PropTypes.func,\n  appendToNode: PropTypes.any,\n  isOpen: PropTypes.bool\n};\nDrawerModalComponent.defaultProps = {\n  focusOnMount: true ,\n  restoreFocus: true\n};\nconst DrawerModal = DrawerModalComponent;\nDrawerModal.Body = Body;\nDrawerModal.Close = Close;\nDrawerModal.Footer = Footer;\nDrawerModal.FooterItem = FooterItem;\nDrawerModal.Header = Header;\n\nconst PLACEMENT = ['auto', 'top', 'top-start', 'top-end', 'bottom', 'bottom-start', 'bottom-end', 'end', 'end-top', 'end-bottom', 'start', 'start-top', 'start-bottom'];\n\nexport { Body$2 as Body, Close$2 as Close, DrawerModal, Footer$2 as Footer, FooterItem$2 as FooterItem, Header$1 as Header, Modal, PLACEMENT, TooltipModal };\n", "/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { useRef } from 'react';\nimport { useId, composeEventHandlers, KEYS } from '@zendeskgarden/container-utilities';\nimport { useFocusJail } from '@zendeskgarden/container-focusjail';\nimport PropTypes from 'prop-types';\n\nconst useModal = _ref => {\n  let {\n    onClose,\n    modalRef,\n    idPrefix,\n    focusOnMount,\n    restoreFocus,\n    environment\n  } = _ref;\n  const prefix = useId(idPrefix);\n  const titleId = `${prefix}__title`;\n  const contentId = `${prefix}__content`;\n  const isModalMousedDownRef = useRef(false);\n  const closeModal = event => {\n    onClose && onClose(event);\n  };\n  const getBackdropProps = function (_temp) {\n    let {\n      onMouseUp,\n      ...other\n    } = _temp === void 0 ? {} : _temp;\n    const containerId = 'containers.modal';\n    const handleMouseUp = event => {\n      const target = event.target;\n      const isModalContainer = containerId === target.getAttribute('data-garden-container-id');\n      if (!isModalMousedDownRef.current && isModalContainer) {\n        closeModal(event);\n      }\n      isModalMousedDownRef.current = false;\n    };\n    return {\n      onMouseUp: composeEventHandlers(onMouseUp, handleMouseUp),\n      'data-garden-container-id': containerId,\n      'data-garden-container-version': '1.0.5',\n      ...other\n    };\n  };\n  const getModalProps = function (_temp2) {\n    let {\n      role = 'dialog',\n      onKeyDown,\n      onMouseDown,\n      ...other\n    } = _temp2 === void 0 ? {} : _temp2;\n    return {\n      role: role === null ? undefined : role,\n      tabIndex: -1,\n      'aria-modal': true,\n      'aria-labelledby': titleId,\n      'aria-describedby': contentId,\n      onMouseDown: composeEventHandlers(onMouseDown, () => {\n        isModalMousedDownRef.current = true;\n      }),\n      onKeyDown: composeEventHandlers(onKeyDown, event => {\n        if (event.key === KEYS.ESCAPE) {\n          closeModal(event);\n        }\n      }),\n      ...other\n    };\n  };\n  const getTitleProps = function (_temp3) {\n    let {\n      id = titleId,\n      ...other\n    } = _temp3 === void 0 ? {} : _temp3;\n    return {\n      id,\n      ...other\n    };\n  };\n  const getContentProps = function (_temp4) {\n    let {\n      id = contentId,\n      ...other\n    } = _temp4 === void 0 ? {} : _temp4;\n    return {\n      id,\n      ...other\n    };\n  };\n  const getCloseProps = _ref2 => {\n    let {\n      onClick,\n      ...other\n    } = _ref2;\n    return {\n      onClick: composeEventHandlers(onClick, event => {\n        closeModal(event);\n      }),\n      ...other\n    };\n  };\n  const {\n    getContainerProps\n  } = useFocusJail({\n    containerRef: modalRef,\n    focusOnMount,\n    restoreFocus,\n    environment\n  });\n  return {\n    getBackdropProps,\n    getModalProps: props => getContainerProps(getModalProps(props)),\n    getTitleProps,\n    getContentProps,\n    getCloseProps,\n    closeModal\n  };\n};\n\nconst ModalContainer = _ref => {\n  let {\n    children,\n    render = children,\n    ...options\n  } = _ref;\n  return React.createElement(React.Fragment, null, render(useModal(options)));\n};\nModalContainer.propTypes = {\n  children: PropTypes.func,\n  render: PropTypes.func,\n  onClose: PropTypes.func,\n  modalRef: PropTypes.any.isRequired,\n  idPrefix: PropTypes.string,\n  focusOnMount: PropTypes.bool,\n  restoreFocus: PropTypes.bool,\n  environment: PropTypes.any\n};\nModalContainer.defaultProps = {\n  focusOnMount: true,\n  restoreFocus: true\n};\n\nexport { ModalContainer, useModal };\n", "/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { createRef, useRef, useState, useEffect, useCallback } from 'react';\nimport { composeEventHandlers, KEYS } from '@zendeskgarden/container-utilities';\nimport { tabbable } from 'tabbable';\nimport PropTypes from 'prop-types';\n\nfunction ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}\n\nfunction activeElement(doc) {\n  if (doc === void 0) {\n    doc = ownerDocument();\n  }\n  try {\n    var active = doc.activeElement;\n    if (!active || !active.nodeName) return null;\n    return active;\n  } catch (e) {\n    return doc.body;\n  }\n}\n\nconst useFocusJail = function (_temp) {\n  let {\n    focusOnMount = true,\n    restoreFocus = true,\n    environment,\n    focusElem,\n    containerRef\n  } = _temp === void 0 ? {\n    containerRef: createRef()\n  } : _temp;\n  const restoreFocusElement = useRef(null);\n  const [currentRef, setCurrentRef] = useState(containerRef.current);\n  useEffect(() => {\n    if (containerRef.current !== currentRef) {\n      setCurrentRef(containerRef.current);\n    }\n  });\n  const focusElement = useCallback(element => {\n    if (focusElem) {\n      focusElem(element);\n    } else {\n      element && element.focus();\n    }\n  }, [focusElem]);\n  const validateContainerRef = () => {\n    if (!currentRef) {\n      throw new Error('Accessibility Error: You must apply the ref prop to your containing element.');\n    }\n  };\n  const getInitialFocusNode = () => {\n    const doc = environment ? environment : document;\n    const activeElem = activeElement(doc);\n    const containerElem = currentRef;\n    return containerElem.contains(activeElem) ? activeElem : containerElem;\n  };\n  const getTabbableNodes = () => {\n    const elements = tabbable(currentRef);\n    return {\n      firstItem: elements[0] || getInitialFocusNode(),\n      lastItem: elements[elements.length - 1] || getInitialFocusNode()\n    };\n  };\n  const getContainerProps = function (_temp2) {\n    let {\n      onKeyDown,\n      ...other\n    } = _temp2 === void 0 ? {} : _temp2;\n    return {\n      onKeyDown: composeEventHandlers(onKeyDown, event => {\n        if (event.key !== KEYS.TAB) {\n          return;\n        }\n        validateContainerRef();\n        const tabbableNodes = getTabbableNodes();\n        if (event.shiftKey && (event.target === tabbableNodes.firstItem || event.target === currentRef)) {\n          focusElement(tabbableNodes.lastItem);\n          event.preventDefault();\n        }\n        if (!event.shiftKey && event.target === tabbableNodes.lastItem) {\n          focusElement(tabbableNodes.firstItem);\n          event.preventDefault();\n        }\n      }),\n      'data-garden-container-id': 'containers.focusjail',\n      'data-garden-container-version': '2.0.5',\n      ...other\n    };\n  };\n  useEffect(() => {\n    const doc = environment || document;\n    restoreFocusElement.current = activeElement(doc);\n    if (focusOnMount) {\n      focusElement(currentRef);\n    }\n    return () => {\n      const isBodyInactive = restoreFocusElement.current !== doc.body;\n      const hasActiveElement = restoreFocusElement.current !== null;\n      if (isBodyInactive && hasActiveElement && restoreFocus) {\n        focusElement(restoreFocusElement.current);\n      }\n    };\n  }, [focusOnMount, restoreFocus, environment, focusElement, currentRef]);\n  return {\n    getContainerProps,\n    focusElement\n  };\n};\n\nconst FocusJailContainer = _ref => {\n  let {\n    children,\n    render = children,\n    ...options\n  } = _ref;\n  return React.createElement(React.Fragment, null, render(useFocusJail(options)));\n};\nFocusJailContainer.propTypes = {\n  children: PropTypes.func,\n  render: PropTypes.func,\n  focusOnMount: PropTypes.bool,\n  restoreFocus: PropTypes.bool,\n  environment: PropTypes.any,\n  containerRef: PropTypes.any.isRequired,\n  focusElem: PropTypes.func\n};\nFocusJailContainer.defaultProps = {\n  focusOnMount: true,\n  restoreFocus: true\n};\n\nexport { FocusJailContainer, useFocusJail };\n", "// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\nconst getTabindex = function (node, isScope) {\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    //\n    // isScope is positive for custom element with shadow root or slot that by default\n    // have tabIndex -1, but need to be sorted by document order in order for their\n    // content to be inserted in the correct position\n    if (\n      (isScope ||\n        /^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      isNaN(parseInt(node.getAttribute('tabindex'), 10))\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabindex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getTabindex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (el, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([el], options.includeContainer, {\n      filter: isNodeMatchingSelectorTabbable.bind(null, options),\n      flatten: false,\n      getShadowRoot: options.getShadowRoot,\n      shadowRootFilter: isValidShadowRootTabbable,\n    });\n  } else {\n    candidates = getCandidates(\n      el,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (el, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([el], options.includeContainer, {\n      filter: isNodeMatchingSelectorFocusable.bind(null, options),\n      flatten: true,\n      getShadowRoot: options.getShadowRoot,\n    });\n  } else {\n    candidates = getCandidates(\n      el,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable };\n", "import * as React from 'react';\nimport { ManagerReferenceNodeContext } from './Manager';\nimport { unwrapArray, setRef } from './utils';\nimport { usePopper } from './usePopper';\n\nvar NOOP = function NOOP() {\n  return void 0;\n};\n\nvar NOOP_PROMISE = function NOOP_PROMISE() {\n  return Promise.resolve(null);\n};\n\nvar EMPTY_MODIFIERS = [];\nexport function Popper(_ref) {\n  var _ref$placement = _ref.placement,\n      placement = _ref$placement === void 0 ? 'bottom' : _ref$placement,\n      _ref$strategy = _ref.strategy,\n      strategy = _ref$strategy === void 0 ? 'absolute' : _ref$strategy,\n      _ref$modifiers = _ref.modifiers,\n      modifiers = _ref$modifiers === void 0 ? EMPTY_MODIFIERS : _ref$modifiers,\n      referenceElement = _ref.referenceElement,\n      onFirstUpdate = _ref.onFirstUpdate,\n      innerRef = _ref.innerRef,\n      children = _ref.children;\n  var referenceNode = React.useContext(ManagerReferenceNodeContext);\n\n  var _React$useState = React.useState(null),\n      popperElement = _React$useState[0],\n      setPopperElement = _React$useState[1];\n\n  var _React$useState2 = React.useState(null),\n      arrowElement = _React$useState2[0],\n      setArrowElement = _React$useState2[1];\n\n  React.useEffect(function () {\n    setRef(innerRef, popperElement);\n  }, [innerRef, popperElement]);\n  var options = React.useMemo(function () {\n    return {\n      placement: placement,\n      strategy: strategy,\n      onFirstUpdate: onFirstUpdate,\n      modifiers: [].concat(modifiers, [{\n        name: 'arrow',\n        enabled: arrowElement != null,\n        options: {\n          element: arrowElement\n        }\n      }])\n    };\n  }, [placement, strategy, onFirstUpdate, modifiers, arrowElement]);\n\n  var _usePopper = usePopper(referenceElement || referenceNode, popperElement, options),\n      state = _usePopper.state,\n      styles = _usePopper.styles,\n      forceUpdate = _usePopper.forceUpdate,\n      update = _usePopper.update;\n\n  var childrenProps = React.useMemo(function () {\n    return {\n      ref: setPopperElement,\n      style: styles.popper,\n      placement: state ? state.placement : placement,\n      hasPopperEscaped: state && state.modifiersData.hide ? state.modifiersData.hide.hasPopperEscaped : null,\n      isReferenceHidden: state && state.modifiersData.hide ? state.modifiersData.hide.isReferenceHidden : null,\n      arrowProps: {\n        style: styles.arrow,\n        ref: setArrowElement\n      },\n      forceUpdate: forceUpdate || NOOP,\n      update: update || NOOP_PROMISE\n    };\n  }, [setPopperElement, setArrowElement, placement, state, styles, update, forceUpdate]);\n  return unwrapArray(children)(childrenProps);\n}", "import * as React from 'react';\nexport var ManagerReferenceNodeContext = React.createContext();\nexport var ManagerReferenceNodeSetterContext = React.createContext();\nexport function Manager(_ref) {\n  var children = _ref.children;\n\n  var _React$useState = React.useState(null),\n      referenceNode = _React$useState[0],\n      setReferenceNode = _React$useState[1];\n\n  var hasUnmounted = React.useRef(false);\n  React.useEffect(function () {\n    return function () {\n      hasUnmounted.current = true;\n    };\n  }, []);\n  var handleSetReferenceNode = React.useCallback(function (node) {\n    if (!hasUnmounted.current) {\n      setReferenceNode(node);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ManagerReferenceNodeContext.Provider, {\n    value: referenceNode\n  }, /*#__PURE__*/React.createElement(ManagerReferenceNodeSetterContext.Provider, {\n    value: handleSetReferenceNode\n  }, children));\n}", "import * as React from 'react';\n\n/**\n * Takes an argument and if it's an array, returns the first item in the array,\n * otherwise returns the argument. Used for Preact compatibility.\n */\nexport var unwrapArray = function unwrapArray(arg) {\n  return Array.isArray(arg) ? arg[0] : arg;\n};\n/**\n * Takes a maybe-undefined function and arbitrary args and invokes the function\n * only if it is defined.\n */\n\nexport var safeInvoke = function safeInvoke(fn) {\n  if (typeof fn === 'function') {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return fn.apply(void 0, args);\n  }\n};\n/**\n * Sets a ref using either a ref callback or a ref object\n */\n\nexport var setRef = function setRef(ref, node) {\n  // if its a function call it\n  if (typeof ref === 'function') {\n    return safeInvoke(ref, node);\n  } // otherwise we should treat it as a ref object\n  else if (ref != null) {\n      ref.current = node;\n    }\n};\n/**\n * Simple ponyfill for Object.fromEntries\n */\n\nexport var fromEntries = function fromEntries(entries) {\n  return entries.reduce(function (acc, _ref) {\n    var key = _ref[0],\n        value = _ref[1];\n    acc[key] = value;\n    return acc;\n  }, {});\n};\n/**\n * Small wrapper around `useLayoutEffect` to get rid of the warning on SSR envs\n */\n\nexport var useIsomorphicLayoutEffect = typeof window !== 'undefined' && window.document && window.document.createElement ? React.useLayoutEffect : React.useEffect;", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createPopper as defaultCreatePopper } from '@popperjs/core';\nimport isEqual from 'react-fast-compare';\nimport { fromEntries, useIsomorphicLayoutEffect } from './utils';\nvar EMPTY_MODIFIERS = [];\nexport var usePopper = function usePopper(referenceElement, popperElement, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var prevOptions = React.useRef(null);\n  var optionsWithDefaults = {\n    onFirstUpdate: options.onFirstUpdate,\n    placement: options.placement || 'bottom',\n    strategy: options.strategy || 'absolute',\n    modifiers: options.modifiers || EMPTY_MODIFIERS\n  };\n\n  var _React$useState = React.useState({\n    styles: {\n      popper: {\n        position: optionsWithDefaults.strategy,\n        left: '0',\n        top: '0'\n      },\n      arrow: {\n        position: 'absolute'\n      }\n    },\n    attributes: {}\n  }),\n      state = _React$useState[0],\n      setState = _React$useState[1];\n\n  var updateStateModifier = React.useMemo(function () {\n    return {\n      name: 'updateState',\n      enabled: true,\n      phase: 'write',\n      fn: function fn(_ref) {\n        var state = _ref.state;\n        var elements = Object.keys(state.elements);\n        ReactDOM.flushSync(function () {\n          setState({\n            styles: fromEntries(elements.map(function (element) {\n              return [element, state.styles[element] || {}];\n            })),\n            attributes: fromEntries(elements.map(function (element) {\n              return [element, state.attributes[element]];\n            }))\n          });\n        });\n      },\n      requires: ['computeStyles']\n    };\n  }, []);\n  var popperOptions = React.useMemo(function () {\n    var newOptions = {\n      onFirstUpdate: optionsWithDefaults.onFirstUpdate,\n      placement: optionsWithDefaults.placement,\n      strategy: optionsWithDefaults.strategy,\n      modifiers: [].concat(optionsWithDefaults.modifiers, [updateStateModifier, {\n        name: 'applyStyles',\n        enabled: false\n      }])\n    };\n\n    if (isEqual(prevOptions.current, newOptions)) {\n      return prevOptions.current || newOptions;\n    } else {\n      prevOptions.current = newOptions;\n      return newOptions;\n    }\n  }, [optionsWithDefaults.onFirstUpdate, optionsWithDefaults.placement, optionsWithDefaults.strategy, optionsWithDefaults.modifiers, updateStateModifier]);\n  var popperInstanceRef = React.useRef();\n  useIsomorphicLayoutEffect(function () {\n    if (popperInstanceRef.current) {\n      popperInstanceRef.current.setOptions(popperOptions);\n    }\n  }, [popperOptions]);\n  useIsomorphicLayoutEffect(function () {\n    if (referenceElement == null || popperElement == null) {\n      return;\n    }\n\n    var createPopper = options.createPopper || defaultCreatePopper;\n    var popperInstance = createPopper(referenceElement, popperElement, popperOptions);\n    popperInstanceRef.current = popperInstance;\n    return function () {\n      popperInstance.destroy();\n      popperInstanceRef.current = null;\n    };\n  }, [referenceElement, popperElement, options.createPopper]);\n  return {\n    state: popperInstanceRef.current ? popperInstanceRef.current.state : null,\n    styles: state.styles,\n    attributes: state.attributes,\n    update: popperInstanceRef.current ? popperInstanceRef.current.update : null,\n    forceUpdate: popperInstanceRef.current ? popperInstanceRef.current.forceUpdate : null\n  };\n};", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function format(str) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n\n  return [].concat(args).reduce(function (p, c) {\n    return p.replace(/%s/, c);\n  }, str);\n}", "import format from \"./format.js\";\nimport { modifierPhases } from \"../enums.js\";\nvar INVALID_MODIFIER_ERROR = 'Popper: modifier \"%s\" provided an invalid %s property, expected %s but got %s';\nvar MISSING_DEPENDENCY_ERROR = 'Popper: modifier \"%s\" requires \"%s\", but \"%s\" modifier is not available';\nvar VALID_PROPERTIES = ['name', 'enabled', 'phase', 'fn', 'effect', 'requires', 'options'];\nexport default function validateModifiers(modifiers) {\n  modifiers.forEach(function (modifier) {\n    [].concat(Object.keys(modifier), VALID_PROPERTIES) // IE11-compatible replacement for `new Set(iterable)`\n    .filter(function (value, index, self) {\n      return self.indexOf(value) === index;\n    }).forEach(function (key) {\n      switch (key) {\n        case 'name':\n          if (typeof modifier.name !== 'string') {\n            console.error(format(INVALID_MODIFIER_ERROR, String(modifier.name), '\"name\"', '\"string\"', \"\\\"\" + String(modifier.name) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'enabled':\n          if (typeof modifier.enabled !== 'boolean') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"enabled\"', '\"boolean\"', \"\\\"\" + String(modifier.enabled) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'phase':\n          if (modifierPhases.indexOf(modifier.phase) < 0) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"phase\"', \"either \" + modifierPhases.join(', '), \"\\\"\" + String(modifier.phase) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'fn':\n          if (typeof modifier.fn !== 'function') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"fn\"', '\"function\"', \"\\\"\" + String(modifier.fn) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'effect':\n          if (modifier.effect != null && typeof modifier.effect !== 'function') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"effect\"', '\"function\"', \"\\\"\" + String(modifier.fn) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'requires':\n          if (modifier.requires != null && !Array.isArray(modifier.requires)) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"requires\"', '\"array\"', \"\\\"\" + String(modifier.requires) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'requiresIfExists':\n          if (!Array.isArray(modifier.requiresIfExists)) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"requiresIfExists\"', '\"array\"', \"\\\"\" + String(modifier.requiresIfExists) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'options':\n        case 'data':\n          break;\n\n        default:\n          console.error(\"PopperJS: an invalid property has been provided to the \\\"\" + modifier.name + \"\\\" modifier, valid properties are \" + VALID_PROPERTIES.map(function (s) {\n            return \"\\\"\" + s + \"\\\"\";\n          }).join(', ') + \"; but \\\"\" + key + \"\\\" was provided.\");\n      }\n\n      modifier.requires && modifier.requires.forEach(function (requirement) {\n        if (modifiers.find(function (mod) {\n          return mod.name === requirement;\n        }) == null) {\n          console.error(format(MISSING_DEPENDENCY_ERROR, String(modifier.name), requirement, requirement));\n        }\n      });\n    });\n  });\n}", "export default function uniqueBy(arr, fn) {\n  var identifiers = new Set();\n  return arr.filter(function (item) {\n    var identifier = fn(item);\n\n    if (!identifiers.has(identifier)) {\n      identifiers.add(identifier);\n      return true;\n    }\n  });\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "import * as React from 'react';\nimport warning from 'warning';\nimport { ManagerReferenceNodeSetterContext } from './Manager';\nimport { safeInvoke, unwrapArray, setRef } from './utils';\nexport function Reference(_ref) {\n  var children = _ref.children,\n      innerRef = _ref.innerRef;\n  var setReferenceNode = React.useContext(ManagerReferenceNodeSetterContext);\n  var refHandler = React.useCallback(function (node) {\n    setRef(innerRef, node);\n    safeInvoke(setReferenceNode, node);\n  }, [innerRef, setReferenceNode]); // ran on unmount\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n\n  React.useEffect(function () {\n    return function () {\n      return setRef(innerRef, null);\n    };\n  }, []);\n  React.useEffect(function () {\n    warning(Boolean(setReferenceNode), '`Reference` should not be used outside of a `Manager` component.');\n  }, [setReferenceNode]);\n  return unwrapArray(children)({\n    ref: refHand<PERSON>\n  });\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAEA,QAAI,iBAAiB,OAAO,YAAY;AACxC,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,iBAAiB,OAAO,gBAAgB,cAAc,CAAC,CAAC,YAAY;AAIxE,aAAS,MAAM,GAAG,GAAG;AAEnB,UAAI,MAAM;AAAG,eAAO;AAEpB,UAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,YAAI,EAAE,gBAAgB,EAAE;AAAa,iBAAO;AAE5C,YAAI,QAAQ,GAAG;AACf,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE;AAAQ,mBAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAG,qBAAO;AACjC,iBAAO;AAAA,QACT;AAsBA,YAAI;AACJ,YAAI,UAAW,aAAa,OAAS,aAAa,KAAM;AACtD,cAAI,EAAE,SAAS,EAAE;AAAM,mBAAO;AAC9B,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAAG,qBAAO;AACjC,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;AAAG,qBAAO;AACpD,iBAAO;AAAA,QACT;AAEA,YAAI,UAAW,aAAa,OAAS,aAAa,KAAM;AACtD,cAAI,EAAE,SAAS,EAAE;AAAM,mBAAO;AAC9B,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAAG,qBAAO;AACjC,iBAAO;AAAA,QACT;AAGA,YAAI,kBAAkB,YAAY,OAAO,CAAC,KAAK,YAAY,OAAO,CAAC,GAAG;AACpE,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE;AAAQ,mBAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG,qBAAO;AAC5B,iBAAO;AAAA,QACT;AAEA,YAAI,EAAE,gBAAgB;AAAQ,iBAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAK5E,YAAI,EAAE,YAAY,OAAO,UAAU,WAAW,OAAO,EAAE,YAAY,cAAc,OAAO,EAAE,YAAY;AAAY,iBAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AACnJ,YAAI,EAAE,aAAa,OAAO,UAAU,YAAY,OAAO,EAAE,aAAa,cAAc,OAAO,EAAE,aAAa;AAAY,iBAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AAGzJ,eAAO,OAAO,KAAK,CAAC;AACpB,iBAAS,KAAK;AACd,YAAI,WAAW,OAAO,KAAK,CAAC,EAAE;AAAQ,iBAAO;AAE7C,aAAK,IAAI,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC;AAAG,mBAAO;AAKhE,YAAI,kBAAkB,aAAa;AAAS,iBAAO;AAGnD,aAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,eAAK,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,MAAM,SAAS,KAAK,CAAC,MAAM,UAAU,EAAE,UAAU;AASlF;AAAA,UACF;AAGA,cAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AAAG,mBAAO;AAAA,QAC7C;AAIA,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,KAAK,MAAM;AAAA,IAC1B;AAGA,WAAO,UAAU,SAASA,SAAQ,GAAG,GAAG;AACtC,UAAI;AACF,eAAO,MAAM,GAAG,CAAC;AAAA,MACnB,SAAS,OAAP;AACA,aAAM,MAAM,WAAW,IAAI,MAAM,kBAAkB,GAAI;AAMrD,kBAAQ,KAAK,gDAAgD;AAC7D,iBAAO;AAAA,QACT;AAEA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;;;ACnIA,IAAAC,SAAuB;AACvB,IAAAC,gBAA4G;AAC5G,uBAAuC;AAEvC,IAAAC,qBAAsB;;;ACJtB,IAAAC,gBAA8B;;;ACA9B,mBAA2E;;;ACF3E,IAAMC,qBAAqB,CACzB,sBACA,uBACA,yBACA,wBACA,uBACA,qCACA,gCACA,gCACA,iEACA,8CACA,sBAAsB;AAExB,IAAMC,oBAAoCD,mBAAmBE,KAAK,GAAG;AAErE,IAAMC,YAAY,OAAOC,YAAY;AAErC,IAAMC,UAAUF,YACZ,WAAY;AAAA,IACZC,QAAQE,UAAUD,WAClBD,QAAQE,UAAUC,qBAClBH,QAAQE,UAAUE;AAEtB,IAAMC,cACJ,CAACN,aAAaC,QAAQE,UAAUG,cAC5B,SAACC,SAAO;AAAA,MAAAC;AAAA,SAAKD,YAAAA,QAAAA,YAAOC,SAAAA,UAAAA,uBAAPD,QAASD,iBAAW,QAAAE,yBAApBA,SAAAA,SAAAA,qBAAAC,KAAAF,OAAuB;AAAC,IACrC,SAACA,SAAO;AAAA,SAAKA,YAAAA,QAAAA,YAAAA,SAAAA,SAAAA,QAASG;AAAa;AAUzC,IAAMC,UAAU,SAAVA,SAAoBC,MAAMC,QAAe;AAAA,MAAAC;AAAA,MAAfD,WAAM,QAAA;AAANA,aAAS;EAAI;AAI3C,MAAME,WAAWH,SAAI,QAAJA,SAAIE,SAAAA,UAAAA,qBAAJF,KAAMI,kBAAYF,QAAAA,uBAAA,SAAA,SAAlBA,mBAAAL,KAAAG,MAAqB,OAAO;AAC7C,MAAMK,QAAQF,aAAa,MAAMA,aAAa;AAO9C,MAAMG,SAASD,SAAUJ,UAAUD,QAAQD,SAAQC,KAAKO,UAAU;AAElE,SAAOD;AACT;AAOA,IAAME,oBAAoB,SAApBA,mBAA8BR,MAAM;AAAA,MAAAS;AAIxC,MAAMC,WAAWV,SAAI,QAAJA,SAAIS,SAAAA,UAAAA,sBAAJT,KAAMI,kBAAYK,QAAAA,wBAAA,SAAA,SAAlBA,oBAAAZ,KAAAG,MAAqB,iBAAiB;AACvD,SAAOU,aAAa,MAAMA,aAAa;AACzC;AAQA,IAAMC,gBAAgB,SAAhBA,eAA0BC,IAAIC,kBAAkBC,QAAQ;AAG5D,MAAIf,QAAQa,EAAE,GAAG;AACf,WAAO,CAAA;EACT;AAEA,MAAIG,aAAaC,MAAMzB,UAAU0B,MAAMC,MACrCN,GAAGO,iBAAiBjC,iBAAiB,CACvC;AACA,MAAI2B,oBAAoBvB,QAAQO,KAAKe,IAAI1B,iBAAiB,GAAG;AAC3D6B,eAAWK,QAAQR,EAAE;EACvB;AACAG,eAAaA,WAAWD,OAAOA,MAAM;AACrC,SAAOC;AACT;AAoCA,IAAMM,2BAA2B,SAA3BA,0BACJC,UACAT,kBACAU,SACA;AACA,MAAMR,aAAa,CAAA;AACnB,MAAMS,kBAAkBR,MAAMS,KAAKH,QAAQ;AAC3C,SAAOE,gBAAgBE,QAAQ;AAC7B,QAAM/B,UAAU6B,gBAAgBG,MAAK;AACrC,QAAI5B,QAAQJ,SAAS,KAAK,GAAG;AAG3B;IACF;AAEA,QAAIA,QAAQiC,YAAY,QAAQ;AAE9B,UAAMC,WAAWlC,QAAQmC,iBAAgB;AACzC,UAAMC,UAAUF,SAASH,SAASG,WAAWlC,QAAQqC;AACrD,UAAMC,mBAAmBZ,0BAAyBU,SAAS,MAAMR,OAAO;AACxE,UAAIA,QAAQW,SAAS;AACnBnB,mBAAWoB,KAAIjB,MAAfH,YAAmBkB,gBAAgB;MACrC,OAAO;AACLlB,mBAAWoB,KAAK;UACdC,aAAazC;UACboB,YAAYkB;QACd,CAAC;MACH;IACF,OAAO;AAEL,UAAMI,iBAAiB/C,QAAQO,KAAKF,SAAST,iBAAiB;AAC9D,UACEmD,kBACAd,QAAQT,OAAOnB,OAAO,MACrBkB,oBAAoB,CAACS,SAASgB,SAAS3C,OAAO,IAC/C;AACAoB,mBAAWoB,KAAKxC,OAAO;MACzB;AAGA,UAAM4C,aACJ5C,QAAQ4C;MAEP,OAAOhB,QAAQiB,kBAAkB,cAChCjB,QAAQiB,cAAc7C,OAAO;AAKjC,UAAM8C,kBACJ,CAAC1C,QAAQwC,YAAY,KAAK,MACzB,CAAChB,QAAQmB,oBAAoBnB,QAAQmB,iBAAiB/C,OAAO;AAEhE,UAAI4C,cAAcE,iBAAiB;AAOjC,YAAMR,oBAAmBZ,0BACvBkB,eAAe,OAAO5C,QAAQqC,WAAWO,WAAWP,UACpD,MACAT,OACF;AAEA,YAAIA,QAAQW,SAAS;AACnBnB,qBAAWoB,KAAIjB,MAAfH,YAAmBkB,iBAAgB;QACrC,OAAO;AACLlB,qBAAWoB,KAAK;YACdC,aAAazC;YACboB,YAAYkB;UACd,CAAC;QACH;MACF,OAAO;AAGLT,wBAAgBJ,QAAOF,MAAvBM,iBAA2B7B,QAAQqC,QAAQ;MAC7C;IACF;EACF;AACA,SAAOjB;AACT;AAEA,IAAM4B,cAAc,SAAdA,aAAwB3C,MAAM4C,SAAS;AAC3C,MAAI5C,KAAK6C,WAAW,GAAG;AAYrB,SACGD,WACC,0BAA0BE,KAAK9C,KAAK4B,OAAO,KAC3CpB,kBAAkBR,IAAI,MACxB+C,MAAMC,SAAShD,KAAKI,aAAa,UAAU,GAAG,EAAE,CAAC,GACjD;AACA,aAAO;IACT;EACF;AAEA,SAAOJ,KAAK6C;AACd;AAEA,IAAMI,uBAAuB,SAAvBA,sBAAiCC,GAAGC,GAAG;AAC3C,SAAOD,EAAEL,aAAaM,EAAEN,WACpBK,EAAEE,gBAAgBD,EAAEC,gBACpBF,EAAEL,WAAWM,EAAEN;AACrB;AAEA,IAAMQ,UAAU,SAAVA,SAAoBrD,MAAM;AAC9B,SAAOA,KAAK4B,YAAY;AAC1B;AAEA,IAAM0B,gBAAgB,SAAhBA,eAA0BtD,MAAM;AACpC,SAAOqD,QAAQrD,IAAI,KAAKA,KAAKuD,SAAS;AACxC;AAEA,IAAMC,uBAAuB,SAAvBA,sBAAiCxD,MAAM;AAC3C,MAAMyD,IACJzD,KAAK4B,YAAY,aACjBZ,MAAMzB,UAAU0B,MACbC,MAAMlB,KAAKgC,QAAQ,EACnB0B,KAAK,SAACC,OAAK;AAAA,WAAKA,MAAM/B,YAAY;GAAU;AACjD,SAAO6B;AACT;AAEA,IAAMG,kBAAkB,SAAlBA,iBAA4BC,OAAOC,MAAM;AAC7C,WAASC,IAAI,GAAGA,IAAIF,MAAMnC,QAAQqC,KAAK;AACrC,QAAIF,MAAME,CAAC,EAAEC,WAAWH,MAAME,CAAC,EAAED,SAASA,MAAM;AAC9C,aAAOD,MAAME,CAAC;IAChB;EACF;AACF;AAEA,IAAME,kBAAkB,SAAlBA,iBAA4BjE,MAAM;AACtC,MAAI,CAACA,KAAKkE,MAAM;AACd,WAAO;EACT;AACA,MAAMC,aAAanE,KAAK8D,QAAQpE,YAAYM,IAAI;AAChD,MAAMoE,cAAc,SAAdA,aAAwBF,MAAM;AAClC,WAAOC,WAAWhD,iBAChB,+BAA+B+C,OAAO,IACxC;;AAGF,MAAIG;AACJ,MACE,OAAOC,WAAW,eAClB,OAAOA,OAAOC,QAAQ,eACtB,OAAOD,OAAOC,IAAIC,WAAW,YAC7B;AACAH,eAAWD,YAAYE,OAAOC,IAAIC,OAAOxE,KAAKkE,IAAI,CAAC;EACrD,OAAO;AACL,QAAI;AACFG,iBAAWD,YAAYpE,KAAKkE,IAAI;aACzBO,KAAP;AAEAC,cAAQC,MACN,4IACAF,IAAIG,OACN;AACA,aAAO;IACT;EACF;AAEA,MAAMZ,UAAUJ,gBAAgBS,UAAUrE,KAAK8D,IAAI;AACnD,SAAO,CAACE,WAAWA,YAAYhE;AACjC;AAEA,IAAM6E,UAAU,SAAVA,SAAoB7E,MAAM;AAC9B,SAAOqD,QAAQrD,IAAI,KAAKA,KAAKuD,SAAS;AACxC;AAEA,IAAMuB,qBAAqB,SAArBA,oBAA+B9E,MAAM;AACzC,SAAO6E,QAAQ7E,IAAI,KAAK,CAACiE,gBAAgBjE,IAAI;AAC/C;AAGA,IAAM+E,iBAAiB,SAAjBA,gBAA2B/E,MAAM;AAAA,MAAAgF;AAwBrC,MAAIC,WAAWjF,QAAQN,YAAYM,IAAI;AACvC,MAAIkF,gBAAYF,YAAGC,cAAQ,QAAAD,cAAA,SAAA,SAARA,UAAUG;AAI7B,MAAIC,WAAW;AACf,MAAIH,YAAYA,aAAajF,MAAM;AAAA,QAAAqF,eAAAC,uBAAAC;AACjCH,eAAW,CAAC,GACVC,gBAAAH,kBAAYG,QAAAA,kBAAA,WAAAC,wBAAZD,cAAcvF,mBAAa,QAAAwF,0BAAA,UAA3BA,sBAA6BE,SAASN,YAAY,KAClDlF,SAAI,QAAJA,SAAIuF,WAAAA,sBAAJvF,KAAMF,mBAAayF,QAAAA,wBAAA,UAAnBA,oBAAqBC,SAASxF,IAAI;AAGpC,WAAO,CAACoF,YAAYF,cAAc;AAAA,UAAAO,YAAAC,gBAAAC;AAIhCV,iBAAWvF,YAAYwF,YAAY;AACnCA,sBAAYO,aAAGR,cAAQ,QAAAQ,eAAA,SAAA,SAARA,WAAUN;AACzBC,iBAAW,CAAC,GAAAM,iBAACR,kBAAY,QAAAQ,mBAAA,WAAAC,wBAAZD,eAAc5F,mBAAa,QAAA6F,0BAAA,UAA3BA,sBAA6BH,SAASN,YAAY;IACjE;EACF;AAEA,SAAOE;AACT;AAEA,IAAMQ,aAAa,SAAbA,YAAuB5F,MAAM;AACjC,MAAA6F,wBAA0B7F,KAAK8F,sBAAqB,GAA5CC,QAAKF,sBAALE,OAAOC,SAAMH,sBAANG;AACf,SAAOD,UAAU,KAAKC,WAAW;AACnC;AACA,IAAMC,WAAW,SAAXA,UAAqBjG,MAAIkG,MAAmC;AAAA,MAA/BC,eAAYD,KAAZC,cAAc3D,gBAAa0D,KAAb1D;AAM/C,MAAI4D,iBAAiBpG,IAAI,EAAEqG,eAAe,UAAU;AAClD,WAAO;EACT;AAEA,MAAMC,kBAAkBhH,QAAQO,KAAKG,MAAM,+BAA+B;AAC1E,MAAMuG,mBAAmBD,kBAAkBtG,KAAKwG,gBAAgBxG;AAChE,MAAIV,QAAQO,KAAK0G,kBAAkB,uBAAuB,GAAG;AAC3D,WAAO;EACT;AAEA,MACE,CAACJ,gBACDA,iBAAiB,UACjBA,iBAAiB,eACjB;AACA,QAAI,OAAO3D,kBAAkB,YAAY;AAGvC,UAAMiE,eAAezG;AACrB,aAAOA,MAAM;AACX,YAAMwG,gBAAgBxG,KAAKwG;AAC3B,YAAME,WAAWhH,YAAYM,IAAI;AACjC,YACEwG,iBACA,CAACA,cAAcjE,cACfC,cAAcgE,aAAa,MAAM,MACjC;AAGA,iBAAOZ,WAAW5F,IAAI;QACxB,WAAWA,KAAK2G,cAAc;AAE5B3G,iBAAOA,KAAK2G;mBACH,CAACH,iBAAiBE,aAAa1G,KAAKF,eAAe;AAE5DE,iBAAO0G,SAASvB;QAClB,OAAO;AAELnF,iBAAOwG;QACT;MACF;AAEAxG,aAAOyG;IACT;AAWA,QAAI1B,eAAe/E,IAAI,GAAG;AAKxB,aAAO,CAACA,KAAK4G,eAAc,EAAGlF;IAChC;AAkBA,QAAIyE,iBAAiB,eAAe;AAClC,aAAO;IACT;EAEF,WAAWA,iBAAiB,iBAAiB;AAM3C,WAAOP,WAAW5F,IAAI;EACxB;AAIA,SAAO;AACT;AAKA,IAAM6G,yBAAyB,SAAzBA,wBAAmC7G,MAAM;AAC7C,MAAI,mCAAmC8C,KAAK9C,KAAK4B,OAAO,GAAG;AACzD,QAAIrB,aAAaP,KAAKwG;AAEtB,WAAOjG,YAAY;AACjB,UAAIA,WAAWqB,YAAY,cAAcrB,WAAWuG,UAAU;AAE5D,iBAAS/C,IAAI,GAAGA,IAAIxD,WAAWyB,SAASN,QAAQqC,KAAK;AACnD,cAAMJ,QAAQpD,WAAWyB,SAAS+E,KAAKhD,CAAC;AAExC,cAAIJ,MAAM/B,YAAY,UAAU;AAG9B,mBAAOtC,QAAQO,KAAKU,YAAY,sBAAsB,IAClD,OACA,CAACoD,MAAM6B,SAASxF,IAAI;UAC1B;QACF;AAEA,eAAO;MACT;AACAO,mBAAaA,WAAWiG;IAC1B;EACF;AAIA,SAAO;AACT;AAEA,IAAMQ,kCAAkC,SAAlCA,iCAA4CzF,SAASvB,MAAM;AAC/D,MACEA,KAAK8G;;;EAIL/G,QAAQC,IAAI,KACZsD,cAActD,IAAI,KAClBiG,SAASjG,MAAMuB,OAAO;EAEtBiC,qBAAqBxD,IAAI,KACzB6G,uBAAuB7G,IAAI,GAC3B;AACA,WAAO;EACT;AACA,SAAO;AACT;AAEA,IAAMiH,iCAAiC,SAAjCA,gCAA2C1F,SAASvB,MAAM;AAC9D,MACE8E,mBAAmB9E,IAAI,KACvB2C,YAAY3C,IAAI,IAAI,KACpB,CAACgH,gCAAgCzF,SAASvB,IAAI,GAC9C;AACA,WAAO;EACT;AACA,SAAO;AACT;AAEA,IAAMkH,4BAA4B,SAA5BA,2BAAsCC,gBAAgB;AAC1D,MAAMtE,WAAWG,SAASmE,eAAe/G,aAAa,UAAU,GAAG,EAAE;AACrE,MAAI2C,MAAMF,QAAQ,KAAKA,YAAY,GAAG;AACpC,WAAO;EACT;AAGA,SAAO;AACT;AAMA,IAAMuE,cAAc,SAAdA,aAAwBrG,YAAY;AACxC,MAAMsG,mBAAmB,CAAA;AACzB,MAAMC,mBAAmB,CAAA;AACzBvG,aAAWwG,QAAQ,SAAUR,MAAMhD,GAAG;AACpC,QAAMnB,UAAU,CAAC,CAACmE,KAAK3E;AACvB,QAAMzC,UAAUiD,UAAUmE,KAAK3E,cAAc2E;AAC7C,QAAMS,oBAAoB7E,YAAYhD,SAASiD,OAAO;AACtD,QAAMtB,WAAWsB,UAAUwE,aAAYL,KAAKhG,UAAU,IAAIpB;AAC1D,QAAI6H,sBAAsB,GAAG;AAC3B5E,gBACIyE,iBAAiBlF,KAAIjB,MAArBmG,kBAAyB/F,QAAQ,IACjC+F,iBAAiBlF,KAAKxC,OAAO;IACnC,OAAO;AACL2H,uBAAiBnF,KAAK;QACpBiB,eAAeW;QACflB,UAAU2E;QACVT;QACAnE;QACAb,SAAST;MACX,CAAC;IACH;EACF,CAAC;AAED,SAAOgG,iBACJG,KAAKxE,oBAAoB,EACzByE,OAAO,SAACC,KAAKC,UAAa;AACzBA,aAAShF,UACL+E,IAAIxF,KAAIjB,MAARyG,KAAYC,SAAS7F,OAAO,IAC5B4F,IAAIxF,KAAKyF,SAAS7F,OAAO;AAC7B,WAAO4F;EACT,GAAG,CAAA,CAAE,EACJE,OAAOR,gBAAgB;AAC5B;AAEMS,IAAAA,WAAW,SAAXA,UAAqBlH,IAAIW,SAAS;AACtCA,YAAUA,WAAW,CAAA;AAErB,MAAIR;AACJ,MAAIQ,QAAQiB,eAAe;AACzBzB,iBAAaM,yBAAyB,CAACT,EAAE,GAAGW,QAAQV,kBAAkB;MACpEC,QAAQmG,+BAA+Bc,KAAK,MAAMxG,OAAO;MACzDW,SAAS;MACTM,eAAejB,QAAQiB;MACvBE,kBAAkBwE;IACpB,CAAC;EACH,OAAO;AACLnG,iBAAaJ,cACXC,IACAW,QAAQV,kBACRoG,+BAA+Bc,KAAK,MAAMxG,OAAO,CACnD;EACF;AACA,SAAO6F,YAAYrG,UAAU;AAC/B;AAkCA,IAAMiH,6BAA6CC,mBAChDC,OAAO,QAAQ,EACfC,KAAK,GAAG;;;AD/mBX,wBAAsB;AAEtB,SAAS,cAAc,MAAM;AAC3B,SAAO,QAAQ,KAAK,iBAAiB;AACvC;AAEA,SAAS,cAAc,KAAK;AAC1B,MAAI,QAAQ,QAAQ;AAClB,UAAM,cAAc;AAAA,EACtB;AACA,MAAI;AACF,QAAI,SAAS,IAAI;AACjB,QAAI,CAAC,UAAU,CAAC,OAAO;AAAU,aAAO;AACxC,WAAO;AAAA,EACT,SAAS,GAAP;AACA,WAAO,IAAI;AAAA,EACb;AACF;AAEA,IAAM,eAAe,SAAU,OAAO;AACpC,MAAI;AAAA,IACF,eAAe;AAAA,IACf,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,UAAU,SAAS;AAAA,IACrB,kBAAc,wBAAU;AAAA,EAC1B,IAAI;AACJ,QAAM,0BAAsB,qBAAO,IAAI;AACvC,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,aAAa,OAAO;AACjE,8BAAU,MAAM;AACd,QAAI,aAAa,YAAY,YAAY;AACvC,oBAAc,aAAa,OAAO;AAAA,IACpC;AAAA,EACF,CAAC;AACD,QAAM,mBAAe,0BAAY,aAAW;AAC1C,QAAI,WAAW;AACb,gBAAU,OAAO;AAAA,IACnB,OAAO;AACL,iBAAW,QAAQ,MAAM;AAAA,IAC3B;AAAA,EACF,GAAG,CAAC,SAAS,CAAC;AACd,QAAM,uBAAuB,MAAM;AACjC,QAAI,CAAC,YAAY;AACf,YAAM,IAAI,MAAM,8EAA8E;AAAA,IAChG;AAAA,EACF;AACA,QAAM,sBAAsB,MAAM;AAChC,UAAM,MAAM,cAAc,cAAc;AACxC,UAAM,aAAa,cAAc,GAAG;AACpC,UAAM,gBAAgB;AACtB,WAAO,cAAc,SAAS,UAAU,IAAI,aAAa;AAAA,EAC3D;AACA,QAAM,mBAAmB,MAAM;AAC7B,UAAM,WAAW,SAAS,UAAU;AACpC,WAAO;AAAA,MACL,WAAW,SAAS,CAAC,KAAK,oBAAoB;AAAA,MAC9C,UAAU,SAAS,SAAS,SAAS,CAAC,KAAK,oBAAoB;AAAA,IACjE;AAAA,EACF;AACA,QAAM,oBAAoB,SAAU,QAAQ;AAC1C,QAAI;AAAA,MACF;AAAA,MACA,GAAG;AAAA,IACL,IAAI,WAAW,SAAS,CAAC,IAAI;AAC7B,WAAO;AAAA,MACL,WAAW,qBAAqB,WAAW,WAAS;AAClD,YAAI,MAAM,QAAQ,KAAK,KAAK;AAC1B;AAAA,QACF;AACA,6BAAqB;AACrB,cAAM,gBAAgB,iBAAiB;AACvC,YAAI,MAAM,aAAa,MAAM,WAAW,cAAc,aAAa,MAAM,WAAW,aAAa;AAC/F,uBAAa,cAAc,QAAQ;AACnC,gBAAM,eAAe;AAAA,QACvB;AACA,YAAI,CAAC,MAAM,YAAY,MAAM,WAAW,cAAc,UAAU;AAC9D,uBAAa,cAAc,SAAS;AACpC,gBAAM,eAAe;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,MACD,4BAA4B;AAAA,MAC5B,iCAAiC;AAAA,MACjC,GAAG;AAAA,IACL;AAAA,EACF;AACA,8BAAU,MAAM;AACd,UAAM,MAAM,eAAe;AAC3B,wBAAoB,UAAU,cAAc,GAAG;AAC/C,QAAI,cAAc;AAChB,mBAAa,UAAU;AAAA,IACzB;AACA,WAAO,MAAM;AACX,YAAM,iBAAiB,oBAAoB,YAAY,IAAI;AAC3D,YAAM,mBAAmB,oBAAoB,YAAY;AACzD,UAAI,kBAAkB,oBAAoB,cAAc;AACtD,qBAAa,oBAAoB,OAAO;AAAA,MAC1C;AAAA,IACF;AAAA,EACF,GAAG,CAAC,cAAc,cAAc,aAAa,cAAc,UAAU,CAAC;AACtE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,qBAAqB,UAAQ;AACjC,MAAI;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,aAAAC,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,OAAO,aAAa,OAAO,CAAC,CAAC;AAChF;AACA,mBAAmB,YAAY;AAAA,EAC7B,UAAU,kBAAAC,QAAU;AAAA,EACpB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,cAAc,kBAAAA,QAAU;AAAA,EACxB,cAAc,kBAAAA,QAAU;AAAA,EACxB,aAAa,kBAAAA,QAAU;AAAA,EACvB,cAAc,kBAAAA,QAAU,IAAI;AAAA,EAC5B,WAAW,kBAAAA,QAAU;AACvB;AACA,mBAAmB,eAAe;AAAA,EAChC,cAAc;AAAA,EACd,cAAc;AAChB;;;AD/HA,IAAAC,qBAAsB;AAEtB,IAAM,WAAW,UAAQ;AACvB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,SAAS,MAAM,QAAQ;AAC7B,QAAM,UAAU,GAAG;AACnB,QAAM,YAAY,GAAG;AACrB,QAAM,2BAAuB,sBAAO,KAAK;AACzC,QAAM,aAAa,WAAS;AAC1B,eAAW,QAAQ,KAAK;AAAA,EAC1B;AACA,QAAM,mBAAmB,SAAU,OAAO;AACxC,QAAI;AAAA,MACF;AAAA,MACA,GAAG;AAAA,IACL,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,UAAM,cAAc;AACpB,UAAM,gBAAgB,WAAS;AAC7B,YAAM,SAAS,MAAM;AACrB,YAAM,mBAAmB,gBAAgB,OAAO,aAAa,0BAA0B;AACvF,UAAI,CAAC,qBAAqB,WAAW,kBAAkB;AACrD,mBAAW,KAAK;AAAA,MAClB;AACA,2BAAqB,UAAU;AAAA,IACjC;AACA,WAAO;AAAA,MACL,WAAW,qBAAqB,WAAW,aAAa;AAAA,MACxD,4BAA4B;AAAA,MAC5B,iCAAiC;AAAA,MACjC,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,gBAAgB,SAAU,QAAQ;AACtC,QAAI;AAAA,MACF,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI,WAAW,SAAS,CAAC,IAAI;AAC7B,WAAO;AAAA,MACL,MAAM,SAAS,OAAO,SAAY;AAAA,MAClC,UAAU;AAAA,MACV,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,aAAa,qBAAqB,aAAa,MAAM;AACnD,6BAAqB,UAAU;AAAA,MACjC,CAAC;AAAA,MACD,WAAW,qBAAqB,WAAW,WAAS;AAClD,YAAI,MAAM,QAAQ,KAAK,QAAQ;AAC7B,qBAAW,KAAK;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,gBAAgB,SAAU,QAAQ;AACtC,QAAI;AAAA,MACF,KAAK;AAAA,MACL,GAAG;AAAA,IACL,IAAI,WAAW,SAAS,CAAC,IAAI;AAC7B,WAAO;AAAA,MACL;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,kBAAkB,SAAU,QAAQ;AACxC,QAAI;AAAA,MACF,KAAK;AAAA,MACL,GAAG;AAAA,IACL,IAAI,WAAW,SAAS,CAAC,IAAI;AAC7B,WAAO;AAAA,MACL;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,gBAAgB,WAAS;AAC7B,QAAI;AAAA,MACF;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,WAAO;AAAA,MACL,SAAS,qBAAqB,SAAS,WAAS;AAC9C,mBAAW,KAAK;AAAA,MAClB,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,aAAa;AAAA,IACf,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA,eAAe,WAAS,kBAAkB,cAAc,KAAK,CAAC;AAAA,IAC9D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,iBAAiB,UAAQ;AAC7B,MAAI;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAC,QAAM,cAAc,cAAAA,QAAM,UAAU,MAAM,OAAO,SAAS,OAAO,CAAC,CAAC;AAC5E;AACA,eAAe,YAAY;AAAA,EACzB,UAAU,mBAAAC,QAAU;AAAA,EACpB,QAAQ,mBAAAA,QAAU;AAAA,EAClB,SAAS,mBAAAA,QAAU;AAAA,EACnB,UAAU,mBAAAA,QAAU,IAAI;AAAA,EACxB,UAAU,mBAAAA,QAAU;AAAA,EACpB,cAAc,mBAAAA,QAAU;AAAA,EACxB,cAAc,mBAAAA,QAAU;AAAA,EACxB,aAAa,mBAAAA,QAAU;AACzB;AACA,eAAe,eAAe;AAAA,EAC5B,cAAc;AAAA,EACd,cAAc;AAChB;;;AGhJA,IAAAC,SAAuB;;;ACAvB,IAAAC,SAAuB;AAChB,IAAI,8BAAoC,qBAAc;AACtD,IAAI,oCAA0C,qBAAc;;;ACFnE,IAAAC,SAAuB;AAwChB,IAAI,cAAc,SAASC,aAAY,SAAS;AACrD,SAAO,QAAQ,OAAO,SAAU,KAAK,MAAM;AACzC,QAAI,MAAM,KAAK,CAAC,GACZ,QAAQ,KAAK,CAAC;AAClB,QAAI,GAAG,IAAI;AACX,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAKO,IAAI,4BAA4B,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS,gBAAsB,yBAAwB;;;ACpDzJ,IAAAC,SAAuB;AACvB,eAA0B;;;ACDnB,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,iBAAiB,CAAC,KAAK,QAAQ,OAAO,IAAI;AAC9C,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,sBAAmC,eAAe,OAAO,SAAU,KAAK,WAAW;AAC5F,SAAO,IAAI,OAAO,CAAC,YAAY,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC;AACpE,GAAG,CAAC,CAAC;AACE,IAAI,aAA0B,CAAC,EAAE,OAAO,gBAAgB,CAAC,IAAI,CAAC,EAAE,OAAO,SAAU,KAAK,WAAW;AACtG,SAAO,IAAI,OAAO,CAAC,WAAW,YAAY,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC;AAC/E,GAAG,CAAC,CAAC;AAEE,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY;AAEhB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY;AAEhB,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,iBAAiB,CAAC,YAAY,MAAM,WAAW,YAAY,MAAM,WAAW,aAAa,OAAO,UAAU;;;AC9BtG,SAAR,YAA6B,SAAS;AAC3C,SAAO,WAAW,QAAQ,YAAY,IAAI,YAAY,IAAI;AAC5D;;;ACFe,SAAR,UAA2B,MAAM;AACtC,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,SAAS,MAAM,mBAAmB;AACzC,QAAIC,iBAAgB,KAAK;AACzB,WAAOA,iBAAgBA,eAAc,eAAe,SAAS;AAAA,EAC/D;AAEA,SAAO;AACT;;;ACTA,SAAS,UAAU,MAAM;AACvB,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,aAAa,MAAM;AAE1B,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;;;AChBA,SAAS,YAAY,MAAM;AACzB,MAAI,QAAQ,KAAK;AACjB,SAAO,KAAK,MAAM,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAClD,QAAIC,SAAQ,MAAM,OAAO,IAAI,KAAK,CAAC;AACnC,QAAI,aAAa,MAAM,WAAW,IAAI,KAAK,CAAC;AAC5C,QAAI,UAAU,MAAM,SAAS,IAAI;AAEjC,QAAI,CAAC,cAAc,OAAO,KAAK,CAAC,YAAY,OAAO,GAAG;AACpD;AAAA,IACF;AAKA,WAAO,OAAO,QAAQ,OAAOA,MAAK;AAClC,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAUC,OAAM;AAC9C,UAAI,QAAQ,WAAWA,KAAI;AAE3B,UAAI,UAAU,OAAO;AACnB,gBAAQ,gBAAgBA,KAAI;AAAA,MAC9B,OAAO;AACL,gBAAQ,aAAaA,OAAM,UAAU,OAAO,KAAK,KAAK;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,QAAQ,MAAM;AAClB,MAAI,gBAAgB;AAAA,IAClB,QAAQ;AAAA,MACN,UAAU,MAAM,QAAQ;AAAA,MACxB,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,WAAW,CAAC;AAAA,EACd;AACA,SAAO,OAAO,MAAM,SAAS,OAAO,OAAO,cAAc,MAAM;AAC/D,QAAM,SAAS;AAEf,MAAI,MAAM,SAAS,OAAO;AACxB,WAAO,OAAO,MAAM,SAAS,MAAM,OAAO,cAAc,KAAK;AAAA,EAC/D;AAEA,SAAO,WAAY;AACjB,WAAO,KAAK,MAAM,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAClD,UAAI,UAAU,MAAM,SAAS,IAAI;AACjC,UAAI,aAAa,MAAM,WAAW,IAAI,KAAK,CAAC;AAC5C,UAAI,kBAAkB,OAAO,KAAK,MAAM,OAAO,eAAe,IAAI,IAAI,MAAM,OAAO,IAAI,IAAI,cAAc,IAAI,CAAC;AAE9G,UAAID,SAAQ,gBAAgB,OAAO,SAAUA,QAAO,UAAU;AAC5D,QAAAA,OAAM,QAAQ,IAAI;AAClB,eAAOA;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,UAAI,CAAC,cAAc,OAAO,KAAK,CAAC,YAAY,OAAO,GAAG;AACpD;AAAA,MACF;AAEA,aAAO,OAAO,QAAQ,OAAOA,MAAK;AAClC,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,WAAW;AACnD,gBAAQ,gBAAgB,SAAS;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAGA,IAAO,sBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ;AAAA,EACA,UAAU,CAAC,eAAe;AAC5B;;;AClFe,SAAR,iBAAkC,WAAW;AAClD,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;;;ACHO,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,QAAQ,KAAK;;;ACFT,SAAR,cAA+B;AACpC,MAAI,SAAS,UAAU;AAEvB,MAAI,UAAU,QAAQ,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM,GAAG;AACnE,WAAO,OAAO,OAAO,IAAI,SAAU,MAAM;AACvC,aAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,IACjC,CAAC,EAAE,KAAK,GAAG;AAAA,EACb;AAEA,SAAO,UAAU;AACnB;;;ACTe,SAAR,mBAAoC;AACzC,SAAO,CAAC,iCAAiC,KAAK,YAAY,CAAC;AAC7D;;;ACCe,SAAR,sBAAuC,SAAS,cAAc,iBAAiB;AACpF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AAEA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AAEA,MAAI,aAAa,QAAQ,sBAAsB;AAC/C,MAAI,SAAS;AACb,MAAI,SAAS;AAEb,MAAI,gBAAgB,cAAc,OAAO,GAAG;AAC1C,aAAS,QAAQ,cAAc,IAAI,MAAM,WAAW,KAAK,IAAI,QAAQ,eAAe,IAAI;AACxF,aAAS,QAAQ,eAAe,IAAI,MAAM,WAAW,MAAM,IAAI,QAAQ,gBAAgB,IAAI;AAAA,EAC7F;AAEA,MAAI,OAAO,UAAU,OAAO,IAAI,UAAU,OAAO,IAAI,QACjD,iBAAiB,KAAK;AAE1B,MAAI,mBAAmB,CAAC,iBAAiB,KAAK;AAC9C,MAAI,KAAK,WAAW,QAAQ,oBAAoB,iBAAiB,eAAe,aAAa,MAAM;AACnG,MAAI,KAAK,WAAW,OAAO,oBAAoB,iBAAiB,eAAe,YAAY,MAAM;AACjG,MAAI,QAAQ,WAAW,QAAQ;AAC/B,MAAI,SAAS,WAAW,SAAS;AACjC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;;;ACrCe,SAAR,cAA+B,SAAS;AAC7C,MAAI,aAAa,sBAAsB,OAAO;AAG9C,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,QAAQ;AAErB,MAAI,KAAK,IAAI,WAAW,QAAQ,KAAK,KAAK,GAAG;AAC3C,YAAQ,WAAW;AAAA,EACrB;AAEA,MAAI,KAAK,IAAI,WAAW,SAAS,MAAM,KAAK,GAAG;AAC7C,aAAS,WAAW;AAAA,EACtB;AAEA,SAAO;AAAA,IACL,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;;;ACvBe,SAAR,SAA0B,QAAQ,OAAO;AAC9C,MAAI,WAAW,MAAM,eAAe,MAAM,YAAY;AAEtD,MAAI,OAAO,SAAS,KAAK,GAAG;AAC1B,WAAO;AAAA,EACT,WACS,YAAY,aAAa,QAAQ,GAAG;AACzC,QAAI,OAAO;AAEX,OAAG;AACD,UAAI,QAAQ,OAAO,WAAW,IAAI,GAAG;AACnC,eAAO;AAAA,MACT;AAGA,aAAO,KAAK,cAAc,KAAK;AAAA,IACjC,SAAS;AAAA,EACX;AAGF,SAAO;AACT;;;ACrBe,SAARE,kBAAkC,SAAS;AAChD,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;;;ACFe,SAAR,eAAgC,SAAS;AAC9C,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,QAAQ,YAAY,OAAO,CAAC,KAAK;AAChE;;;ACFe,SAAR,mBAAoC,SAAS;AAElD,WAAS,UAAU,OAAO,IAAI,QAAQ;AAAA;AAAA,IACtC,QAAQ;AAAA,QAAa,OAAO,UAAU;AACxC;;;ACFe,SAAR,cAA+B,SAAS;AAC7C,MAAI,YAAY,OAAO,MAAM,QAAQ;AACnC,WAAO;AAAA,EACT;AAEA;AAAA;AAAA;AAAA;AAAA,IAGE,QAAQ;AAAA,IACR,QAAQ;AAAA,KACR,aAAa,OAAO,IAAI,QAAQ,OAAO;AAAA;AAAA,IAEvC,mBAAmB,OAAO;AAAA;AAG9B;;;ACVA,SAAS,oBAAoB,SAAS;AACpC,MAAI,CAAC,cAAc,OAAO;AAAA,EAC1BC,kBAAiB,OAAO,EAAE,aAAa,SAAS;AAC9C,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ;AACjB;AAIA,SAAS,mBAAmB,SAAS;AACnC,MAAI,YAAY,WAAW,KAAK,YAAY,CAAC;AAC7C,MAAI,OAAO,WAAW,KAAK,YAAY,CAAC;AAExC,MAAI,QAAQ,cAAc,OAAO,GAAG;AAElC,QAAI,aAAaA,kBAAiB,OAAO;AAEzC,QAAI,WAAW,aAAa,SAAS;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,cAAc,cAAc,OAAO;AAEvC,MAAI,aAAa,WAAW,GAAG;AAC7B,kBAAc,YAAY;AAAA,EAC5B;AAEA,SAAO,cAAc,WAAW,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,YAAY,WAAW,CAAC,IAAI,GAAG;AAC3F,QAAI,MAAMA,kBAAiB,WAAW;AAItC,QAAI,IAAI,cAAc,UAAU,IAAI,gBAAgB,UAAU,IAAI,YAAY,WAAW,CAAC,aAAa,aAAa,EAAE,QAAQ,IAAI,UAAU,MAAM,MAAM,aAAa,IAAI,eAAe,YAAY,aAAa,IAAI,UAAU,IAAI,WAAW,QAAQ;AACpP,aAAO;AAAA,IACT,OAAO;AACL,oBAAc,YAAY;AAAA,IAC5B;AAAA,EACF;AAEA,SAAO;AACT;AAIe,SAAR,gBAAiC,SAAS;AAC/C,MAAIC,UAAS,UAAU,OAAO;AAC9B,MAAI,eAAe,oBAAoB,OAAO;AAE9C,SAAO,gBAAgB,eAAe,YAAY,KAAKD,kBAAiB,YAAY,EAAE,aAAa,UAAU;AAC3G,mBAAe,oBAAoB,YAAY;AAAA,EACjD;AAEA,MAAI,iBAAiB,YAAY,YAAY,MAAM,UAAU,YAAY,YAAY,MAAM,UAAUA,kBAAiB,YAAY,EAAE,aAAa,WAAW;AAC1J,WAAOC;AAAA,EACT;AAEA,SAAO,gBAAgB,mBAAmB,OAAO,KAAKA;AACxD;;;ACpEe,SAAR,yBAA0C,WAAW;AAC1D,SAAO,CAAC,OAAO,QAAQ,EAAE,QAAQ,SAAS,KAAK,IAAI,MAAM;AAC3D;;;ACDO,SAAS,OAAOC,MAAK,OAAOC,MAAK;AACtC,SAAO,IAAQD,MAAK,IAAQ,OAAOC,IAAG,CAAC;AACzC;AACO,SAAS,eAAeD,MAAK,OAAOC,MAAK;AAC9C,MAAI,IAAI,OAAOD,MAAK,OAAOC,IAAG;AAC9B,SAAO,IAAIA,OAAMA,OAAM;AACzB;;;ACPe,SAAR,qBAAsC;AAC3C,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;;;ACNe,SAAR,mBAAoC,eAAe;AACxD,SAAO,OAAO,OAAO,CAAC,GAAG,mBAAmB,GAAG,aAAa;AAC9D;;;ACHe,SAAR,gBAAiC,OAAO,MAAM;AACnD,SAAO,KAAK,OAAO,SAAU,SAAS,KAAK;AACzC,YAAQ,GAAG,IAAI;AACf,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;;;ACMA,IAAI,kBAAkB,SAASC,iBAAgB,SAAS,OAAO;AAC7D,YAAU,OAAO,YAAY,aAAa,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO;AAAA,IAC/E,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC,IAAI;AACN,SAAO,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS,cAAc,CAAC;AAC5G;AAEA,SAAS,MAAM,MAAM;AACnB,MAAI;AAEJ,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK,MACZ,UAAU,KAAK;AACnB,MAAI,eAAe,MAAM,SAAS;AAClC,MAAIC,iBAAgB,MAAM,cAAc;AACxC,MAAI,gBAAgB,iBAAiB,MAAM,SAAS;AACpD,MAAI,OAAO,yBAAyB,aAAa;AACjD,MAAI,aAAa,CAAC,MAAM,KAAK,EAAE,QAAQ,aAAa,KAAK;AACzD,MAAI,MAAM,aAAa,WAAW;AAElC,MAAI,CAAC,gBAAgB,CAACA,gBAAe;AACnC;AAAA,EACF;AAEA,MAAI,gBAAgB,gBAAgB,QAAQ,SAAS,KAAK;AAC1D,MAAI,YAAY,cAAc,YAAY;AAC1C,MAAI,UAAU,SAAS,MAAM,MAAM;AACnC,MAAI,UAAU,SAAS,MAAM,SAAS;AACtC,MAAI,UAAU,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM,UAAU,IAAI,IAAIA,eAAc,IAAI,IAAI,MAAM,MAAM,OAAO,GAAG;AACrH,MAAI,YAAYA,eAAc,IAAI,IAAI,MAAM,MAAM,UAAU,IAAI;AAChE,MAAI,oBAAoB,gBAAgB,YAAY;AACpD,MAAI,aAAa,oBAAoB,SAAS,MAAM,kBAAkB,gBAAgB,IAAI,kBAAkB,eAAe,IAAI;AAC/H,MAAI,oBAAoB,UAAU,IAAI,YAAY;AAGlD,MAAIC,OAAM,cAAc,OAAO;AAC/B,MAAIC,OAAM,aAAa,UAAU,GAAG,IAAI,cAAc,OAAO;AAC7D,MAAI,SAAS,aAAa,IAAI,UAAU,GAAG,IAAI,IAAI;AACnD,MAAIC,UAAS,OAAOF,MAAK,QAAQC,IAAG;AAEpC,MAAI,WAAW;AACf,QAAM,cAAc,IAAI,KAAK,wBAAwB,CAAC,GAAG,sBAAsB,QAAQ,IAAIC,SAAQ,sBAAsB,eAAeA,UAAS,QAAQ;AAC3J;AAEA,SAASC,QAAO,OAAO;AACrB,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,MAAI,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,wBAAwB;AAEzE,MAAI,gBAAgB,MAAM;AACxB;AAAA,EACF;AAGA,MAAI,OAAO,iBAAiB,UAAU;AACpC,mBAAe,MAAM,SAAS,OAAO,cAAc,YAAY;AAE/D,QAAI,CAAC,cAAc;AACjB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAAuC;AACzC,QAAI,CAAC,cAAc,YAAY,GAAG;AAChC,cAAQ,MAAM,CAAC,uEAAuE,uEAAuE,YAAY,EAAE,KAAK,GAAG,CAAC;AAAA,IACtL;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,MAAM,SAAS,QAAQ,YAAY,GAAG;AAClD,QAAI,MAAuC;AACzC,cAAQ,MAAM,CAAC,uEAAuE,UAAU,EAAE,KAAK,GAAG,CAAC;AAAA,IAC7G;AAEA;AAAA,EACF;AAEA,QAAM,SAAS,QAAQ;AACzB;AAGA,IAAO,gBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,QAAQA;AAAA,EACR,UAAU,CAAC,eAAe;AAAA,EAC1B,kBAAkB,CAAC,iBAAiB;AACtC;;;ACpGe,SAAR,aAA8B,WAAW;AAC9C,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;;;ACOA,IAAI,aAAa;AAAA,EACf,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AAIA,SAAS,kBAAkB,MAAM,KAAK;AACpC,MAAI,IAAI,KAAK,GACT,IAAI,KAAK;AACb,MAAI,MAAM,IAAI,oBAAoB;AAClC,SAAO;AAAA,IACL,GAAG,MAAM,IAAI,GAAG,IAAI,OAAO;AAAA,IAC3B,GAAG,MAAM,IAAI,GAAG,IAAI,OAAO;AAAA,EAC7B;AACF;AAEO,SAAS,YAAY,OAAO;AACjC,MAAI;AAEJ,MAAIC,UAAS,MAAM,QACf,aAAa,MAAM,YACnB,YAAY,MAAM,WAClB,YAAY,MAAM,WAClB,UAAU,MAAM,SAChB,WAAW,MAAM,UACjB,kBAAkB,MAAM,iBACxB,WAAW,MAAM,UACjB,eAAe,MAAM,cACrB,UAAU,MAAM;AACpB,MAAI,aAAa,QAAQ,GACrB,IAAI,eAAe,SAAS,IAAI,YAChC,aAAa,QAAQ,GACrB,IAAI,eAAe,SAAS,IAAI;AAEpC,MAAI,QAAQ,OAAO,iBAAiB,aAAa,aAAa;AAAA,IAC5D;AAAA,IACA;AAAA,EACF,CAAC,IAAI;AAAA,IACH;AAAA,IACA;AAAA,EACF;AAEA,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,OAAO,QAAQ,eAAe,GAAG;AACrC,MAAI,OAAO,QAAQ,eAAe,GAAG;AACrC,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,MAAM;AAEV,MAAI,UAAU;AACZ,QAAI,eAAe,gBAAgBA,OAAM;AACzC,QAAI,aAAa;AACjB,QAAI,YAAY;AAEhB,QAAI,iBAAiB,UAAUA,OAAM,GAAG;AACtC,qBAAe,mBAAmBA,OAAM;AAExC,UAAIC,kBAAiB,YAAY,EAAE,aAAa,YAAY,aAAa,YAAY;AACnF,qBAAa;AACb,oBAAY;AAAA,MACd;AAAA,IACF;AAGA,mBAAe;AAEf,QAAI,cAAc,QAAQ,cAAc,QAAQ,cAAc,UAAU,cAAc,KAAK;AACzF,cAAQ;AACR,UAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,eAAe;AAAA;AAAA,QACzF,aAAa,UAAU;AAAA;AACvB,WAAK,UAAU,WAAW;AAC1B,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAEA,QAAI,cAAc,SAAS,cAAc,OAAO,cAAc,WAAW,cAAc,KAAK;AAC1F,cAAQ;AACR,UAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,eAAe;AAAA;AAAA,QACzF,aAAa,SAAS;AAAA;AACtB,WAAK,UAAU,WAAW;AAC1B,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,eAAe,OAAO,OAAO;AAAA,IAC/B;AAAA,EACF,GAAG,YAAY,UAAU;AAEzB,MAAI,QAAQ,iBAAiB,OAAO,kBAAkB;AAAA,IACpD;AAAA,IACA;AAAA,EACF,GAAG,UAAUD,OAAM,CAAC,IAAI;AAAA,IACtB;AAAA,IACA;AAAA,EACF;AAEA,MAAI,MAAM;AACV,MAAI,MAAM;AAEV,MAAI,iBAAiB;AACnB,QAAI;AAEJ,WAAO,OAAO,OAAO,CAAC,GAAG,eAAe,iBAAiB,CAAC,GAAG,eAAe,KAAK,IAAI,OAAO,MAAM,IAAI,eAAe,KAAK,IAAI,OAAO,MAAM,IAAI,eAAe,aAAa,IAAI,oBAAoB,MAAM,IAAI,eAAe,IAAI,SAAS,IAAI,QAAQ,iBAAiB,IAAI,SAAS,IAAI,UAAU,eAAe;AAAA,EAClT;AAEA,SAAO,OAAO,OAAO,CAAC,GAAG,eAAe,kBAAkB,CAAC,GAAG,gBAAgB,KAAK,IAAI,OAAO,IAAI,OAAO,IAAI,gBAAgB,KAAK,IAAI,OAAO,IAAI,OAAO,IAAI,gBAAgB,YAAY,IAAI,gBAAgB;AAC9M;AAEA,SAAS,cAAc,OAAO;AAC5B,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,MAAI,wBAAwB,QAAQ,iBAChC,kBAAkB,0BAA0B,SAAS,OAAO,uBAC5D,oBAAoB,QAAQ,UAC5B,WAAW,sBAAsB,SAAS,OAAO,mBACjD,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,OAAO;AAE7D,MAAI,MAAuC;AACzC,QAAI,qBAAqBC,kBAAiB,MAAM,SAAS,MAAM,EAAE,sBAAsB;AAEvF,QAAI,YAAY,CAAC,aAAa,OAAO,SAAS,UAAU,MAAM,EAAE,KAAK,SAAU,UAAU;AACvF,aAAO,mBAAmB,QAAQ,QAAQ,KAAK;AAAA,IACjD,CAAC,GAAG;AACF,cAAQ,KAAK,CAAC,qEAAqE,kEAAkE,QAAQ,sEAAsE,mEAAmE,sEAAsE,4CAA4C,QAAQ,sEAAsE,qEAAqE,EAAE,KAAK,GAAG,CAAC;AAAA,IACxjB;AAAA,EACF;AAEA,MAAI,eAAe;AAAA,IACjB,WAAW,iBAAiB,MAAM,SAAS;AAAA,IAC3C,WAAW,aAAa,MAAM,SAAS;AAAA,IACvC,QAAQ,MAAM,SAAS;AAAA,IACvB,YAAY,MAAM,MAAM;AAAA,IACxB;AAAA,IACA,SAAS,MAAM,QAAQ,aAAa;AAAA,EACtC;AAEA,MAAI,MAAM,cAAc,iBAAiB,MAAM;AAC7C,UAAM,OAAO,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACvG,SAAS,MAAM,cAAc;AAAA,MAC7B,UAAU,MAAM,QAAQ;AAAA,MACxB;AAAA,MACA;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAEA,MAAI,MAAM,cAAc,SAAS,MAAM;AACrC,UAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,OAAO,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACrG,SAAS,MAAM,cAAc;AAAA,MAC7B,UAAU;AAAA,MACV,UAAU;AAAA,MACV;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAEA,QAAM,WAAW,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IACnE,yBAAyB,MAAM;AAAA,EACjC,CAAC;AACH;AAGA,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM,CAAC;AACT;;;ACjLA,IAAI,UAAU;AAAA,EACZ,SAAS;AACX;AAEA,SAASC,QAAO,MAAM;AACpB,MAAI,QAAQ,KAAK,OACb,WAAW,KAAK,UAChB,UAAU,KAAK;AACnB,MAAI,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO,iBAC7C,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO;AACjD,MAAIC,UAAS,UAAU,MAAM,SAAS,MAAM;AAC5C,MAAI,gBAAgB,CAAC,EAAE,OAAO,MAAM,cAAc,WAAW,MAAM,cAAc,MAAM;AAEvF,MAAI,QAAQ;AACV,kBAAc,QAAQ,SAAU,cAAc;AAC5C,mBAAa,iBAAiB,UAAU,SAAS,QAAQ,OAAO;AAAA,IAClE,CAAC;AAAA,EACH;AAEA,MAAI,QAAQ;AACV,IAAAA,QAAO,iBAAiB,UAAU,SAAS,QAAQ,OAAO;AAAA,EAC5D;AAEA,SAAO,WAAY;AACjB,QAAI,QAAQ;AACV,oBAAc,QAAQ,SAAU,cAAc;AAC5C,qBAAa,oBAAoB,UAAU,SAAS,QAAQ,OAAO;AAAA,MACrE,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ;AACV,MAAAA,QAAO,oBAAoB,UAAU,SAAS,QAAQ,OAAO;AAAA,IAC/D;AAAA,EACF;AACF;AAGA,IAAO,yBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI,SAAS,KAAK;AAAA,EAAC;AAAA,EACnB,QAAQD;AAAA,EACR,MAAM,CAAC;AACT;;;AChDA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACe,SAAR,qBAAsC,WAAW;AACtD,SAAO,UAAU,QAAQ,0BAA0B,SAAU,SAAS;AACpE,WAAO,KAAK,OAAO;AAAA,EACrB,CAAC;AACH;;;ACVA,IAAIE,QAAO;AAAA,EACT,OAAO;AAAA,EACP,KAAK;AACP;AACe,SAAR,8BAA+C,WAAW;AAC/D,SAAO,UAAU,QAAQ,cAAc,SAAU,SAAS;AACxD,WAAOA,MAAK,OAAO;AAAA,EACrB,CAAC;AACH;;;ACPe,SAAR,gBAAiC,MAAM;AAC5C,MAAI,MAAM,UAAU,IAAI;AACxB,MAAI,aAAa,IAAI;AACrB,MAAI,YAAY,IAAI;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;;;ACNe,SAAR,oBAAqC,SAAS;AAQnD,SAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO,gBAAgB,OAAO,EAAE;AAC5F;;;ACRe,SAAR,gBAAiC,SAAS,UAAU;AACzD,MAAI,MAAM,UAAU,OAAO;AAC3B,MAAI,OAAO,mBAAmB,OAAO;AACrC,MAAI,iBAAiB,IAAI;AACzB,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,QAAI,iBAAiB,iBAAiB;AAEtC,QAAI,kBAAkB,CAAC,kBAAkB,aAAa,SAAS;AAC7D,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG,IAAI,oBAAoB,OAAO;AAAA,IAClC;AAAA,EACF;AACF;;;ACvBe,SAAR,gBAAiC,SAAS;AAC/C,MAAI;AAEJ,MAAI,OAAO,mBAAmB,OAAO;AACrC,MAAI,YAAY,gBAAgB,OAAO;AACvC,MAAI,QAAQ,wBAAwB,QAAQ,kBAAkB,OAAO,SAAS,sBAAsB;AACpG,MAAI,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,OAAO,KAAK,cAAc,GAAG,OAAO,KAAK,cAAc,CAAC;AAC5G,MAAI,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,OAAO,KAAK,eAAe,GAAG,OAAO,KAAK,eAAe,CAAC;AACjH,MAAI,IAAI,CAAC,UAAU,aAAa,oBAAoB,OAAO;AAC3D,MAAI,IAAI,CAAC,UAAU;AAEnB,MAAIC,kBAAiB,QAAQ,IAAI,EAAE,cAAc,OAAO;AACtD,SAAK,IAAI,KAAK,aAAa,OAAO,KAAK,cAAc,CAAC,IAAI;AAAA,EAC5D;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC3Be,SAAR,eAAgC,SAAS;AAE9C,MAAI,oBAAoBC,kBAAiB,OAAO,GAC5C,WAAW,kBAAkB,UAC7B,YAAY,kBAAkB,WAC9B,YAAY,kBAAkB;AAElC,SAAO,6BAA6B,KAAK,WAAW,YAAY,SAAS;AAC3E;;;ACLe,SAAR,gBAAiC,MAAM;AAC5C,MAAI,CAAC,QAAQ,QAAQ,WAAW,EAAE,QAAQ,YAAY,IAAI,CAAC,KAAK,GAAG;AAEjE,WAAO,KAAK,cAAc;AAAA,EAC5B;AAEA,MAAI,cAAc,IAAI,KAAK,eAAe,IAAI,GAAG;AAC/C,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,cAAc,IAAI,CAAC;AAC5C;;;ACJe,SAAR,kBAAmC,SAAS,MAAM;AACvD,MAAI;AAEJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,eAAe,gBAAgB,OAAO;AAC1C,MAAI,SAAS,mBAAmB,wBAAwB,QAAQ,kBAAkB,OAAO,SAAS,sBAAsB;AACxH,MAAI,MAAM,UAAU,YAAY;AAChC,MAAI,SAAS,SAAS,CAAC,GAAG,EAAE,OAAO,IAAI,kBAAkB,CAAC,GAAG,eAAe,YAAY,IAAI,eAAe,CAAC,CAAC,IAAI;AACjH,MAAI,cAAc,KAAK,OAAO,MAAM;AACpC,SAAO,SAAS;AAAA;AAAA,IAChB,YAAY,OAAO,kBAAkB,cAAc,MAAM,CAAC,CAAC;AAAA;AAC7D;;;ACzBe,SAAR,iBAAkC,MAAM;AAC7C,SAAO,OAAO,OAAO,CAAC,GAAG,MAAM;AAAA,IAC7B,MAAM,KAAK;AAAA,IACX,KAAK,KAAK;AAAA,IACV,OAAO,KAAK,IAAI,KAAK;AAAA,IACrB,QAAQ,KAAK,IAAI,KAAK;AAAA,EACxB,CAAC;AACH;;;ACQA,SAAS,2BAA2B,SAAS,UAAU;AACrD,MAAI,OAAO,sBAAsB,SAAS,OAAO,aAAa,OAAO;AACrE,OAAK,MAAM,KAAK,MAAM,QAAQ;AAC9B,OAAK,OAAO,KAAK,OAAO,QAAQ;AAChC,OAAK,SAAS,KAAK,MAAM,QAAQ;AACjC,OAAK,QAAQ,KAAK,OAAO,QAAQ;AACjC,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,IAAI,KAAK;AACd,OAAK,IAAI,KAAK;AACd,SAAO;AACT;AAEA,SAAS,2BAA2B,SAAS,gBAAgB,UAAU;AACrE,SAAO,mBAAmB,WAAW,iBAAiB,gBAAgB,SAAS,QAAQ,CAAC,IAAI,UAAU,cAAc,IAAI,2BAA2B,gBAAgB,QAAQ,IAAI,iBAAiB,gBAAgB,mBAAmB,OAAO,CAAC,CAAC;AAC9O;AAKA,SAAS,mBAAmB,SAAS;AACnC,MAAIC,mBAAkB,kBAAkB,cAAc,OAAO,CAAC;AAC9D,MAAI,oBAAoB,CAAC,YAAY,OAAO,EAAE,QAAQC,kBAAiB,OAAO,EAAE,QAAQ,KAAK;AAC7F,MAAI,iBAAiB,qBAAqB,cAAc,OAAO,IAAI,gBAAgB,OAAO,IAAI;AAE9F,MAAI,CAAC,UAAU,cAAc,GAAG;AAC9B,WAAO,CAAC;AAAA,EACV;AAGA,SAAOD,iBAAgB,OAAO,SAAU,gBAAgB;AACtD,WAAO,UAAU,cAAc,KAAK,SAAS,gBAAgB,cAAc,KAAK,YAAY,cAAc,MAAM;AAAA,EAClH,CAAC;AACH;AAIe,SAAR,gBAAiC,SAAS,UAAU,cAAc,UAAU;AACjF,MAAI,sBAAsB,aAAa,oBAAoB,mBAAmB,OAAO,IAAI,CAAC,EAAE,OAAO,QAAQ;AAC3G,MAAIA,mBAAkB,CAAC,EAAE,OAAO,qBAAqB,CAAC,YAAY,CAAC;AACnE,MAAI,sBAAsBA,iBAAgB,CAAC;AAC3C,MAAI,eAAeA,iBAAgB,OAAO,SAAU,SAAS,gBAAgB;AAC3E,QAAI,OAAO,2BAA2B,SAAS,gBAAgB,QAAQ;AACvE,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,2BAA2B,SAAS,qBAAqB,QAAQ,CAAC;AACrE,eAAa,QAAQ,aAAa,QAAQ,aAAa;AACvD,eAAa,SAAS,aAAa,SAAS,aAAa;AACzD,eAAa,IAAI,aAAa;AAC9B,eAAa,IAAI,aAAa;AAC9B,SAAO;AACT;;;ACjEe,SAAR,eAAgC,MAAM;AAC3C,MAAIE,aAAY,KAAK,WACjB,UAAU,KAAK,SACf,YAAY,KAAK;AACrB,MAAI,gBAAgB,YAAY,iBAAiB,SAAS,IAAI;AAC9D,MAAI,YAAY,YAAY,aAAa,SAAS,IAAI;AACtD,MAAI,UAAUA,WAAU,IAAIA,WAAU,QAAQ,IAAI,QAAQ,QAAQ;AAClE,MAAI,UAAUA,WAAU,IAAIA,WAAU,SAAS,IAAI,QAAQ,SAAS;AACpE,MAAI;AAEJ,UAAQ,eAAe;AAAA,IACrB,KAAK;AACH,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAGA,WAAU,IAAI,QAAQ;AAAA,MAC3B;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAGA,WAAU,IAAIA,WAAU;AAAA,MAC7B;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAGA,WAAU,IAAIA,WAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAGA,WAAU,IAAI,QAAQ;AAAA,QACzB,GAAG;AAAA,MACL;AACA;AAAA,IAEF;AACE,gBAAU;AAAA,QACR,GAAGA,WAAU;AAAA,QACb,GAAGA,WAAU;AAAA,MACf;AAAA,EACJ;AAEA,MAAI,WAAW,gBAAgB,yBAAyB,aAAa,IAAI;AAEzE,MAAI,YAAY,MAAM;AACpB,QAAI,MAAM,aAAa,MAAM,WAAW;AAExC,YAAQ,WAAW;AAAA,MACjB,KAAK;AACH,gBAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAKA,WAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI;AAC7E;AAAA,MAEF,KAAK;AACH,gBAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAKA,WAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI;AAC7E;AAAA,MAEF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AC3De,SAAR,eAAgC,OAAO,SAAS;AACrD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,qBAAqB,SAAS,WAC9B,YAAY,uBAAuB,SAAS,MAAM,YAAY,oBAC9D,oBAAoB,SAAS,UAC7B,WAAW,sBAAsB,SAAS,MAAM,WAAW,mBAC3D,oBAAoB,SAAS,UAC7B,WAAW,sBAAsB,SAAS,kBAAkB,mBAC5D,wBAAwB,SAAS,cACjC,eAAe,0BAA0B,SAAS,WAAW,uBAC7D,wBAAwB,SAAS,gBACjC,iBAAiB,0BAA0B,SAAS,SAAS,uBAC7D,uBAAuB,SAAS,aAChC,cAAc,yBAAyB,SAAS,QAAQ,sBACxD,mBAAmB,SAAS,SAC5B,UAAU,qBAAqB,SAAS,IAAI;AAChD,MAAI,gBAAgB,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS,cAAc,CAAC;AACvH,MAAI,aAAa,mBAAmB,SAAS,YAAY;AACzD,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,UAAU,MAAM,SAAS,cAAc,aAAa,cAAc;AACtE,MAAI,qBAAqB,gBAAgB,UAAU,OAAO,IAAI,UAAU,QAAQ,kBAAkB,mBAAmB,MAAM,SAAS,MAAM,GAAG,UAAU,cAAc,QAAQ;AAC7K,MAAI,sBAAsB,sBAAsB,MAAM,SAAS,SAAS;AACxE,MAAIC,iBAAgB,eAAe;AAAA,IACjC,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV;AAAA,EACF,CAAC;AACD,MAAI,mBAAmB,iBAAiB,OAAO,OAAO,CAAC,GAAG,YAAYA,cAAa,CAAC;AACpF,MAAI,oBAAoB,mBAAmB,SAAS,mBAAmB;AAGvE,MAAI,kBAAkB;AAAA,IACpB,KAAK,mBAAmB,MAAM,kBAAkB,MAAM,cAAc;AAAA,IACpE,QAAQ,kBAAkB,SAAS,mBAAmB,SAAS,cAAc;AAAA,IAC7E,MAAM,mBAAmB,OAAO,kBAAkB,OAAO,cAAc;AAAA,IACvE,OAAO,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc;AAAA,EAC5E;AACA,MAAI,aAAa,MAAM,cAAc;AAErC,MAAI,mBAAmB,UAAU,YAAY;AAC3C,QAAIC,UAAS,WAAW,SAAS;AACjC,WAAO,KAAK,eAAe,EAAE,QAAQ,SAAU,KAAK;AAClD,UAAI,WAAW,CAAC,OAAO,MAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,IAAI;AACvD,UAAI,OAAO,CAAC,KAAK,MAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,MAAM;AACnD,sBAAgB,GAAG,KAAKA,QAAO,IAAI,IAAI;AAAA,IACzC,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;AC5De,SAAR,qBAAsC,OAAO,SAAS;AAC3D,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,YAAY,SAAS,WACrB,WAAW,SAAS,UACpB,eAAe,SAAS,cACxB,UAAU,SAAS,SACnB,iBAAiB,SAAS,gBAC1B,wBAAwB,SAAS,uBACjC,wBAAwB,0BAA0B,SAAS,aAAgB;AAC/E,MAAI,YAAY,aAAa,SAAS;AACtC,MAAIC,cAAa,YAAY,iBAAiB,sBAAsB,oBAAoB,OAAO,SAAUC,YAAW;AAClH,WAAO,aAAaA,UAAS,MAAM;AAAA,EACrC,CAAC,IAAI;AACL,MAAI,oBAAoBD,YAAW,OAAO,SAAUC,YAAW;AAC7D,WAAO,sBAAsB,QAAQA,UAAS,KAAK;AAAA,EACrD,CAAC;AAED,MAAI,kBAAkB,WAAW,GAAG;AAClC,wBAAoBD;AAEpB,QAAI,MAAuC;AACzC,cAAQ,MAAM,CAAC,gEAAgE,mEAAmE,8BAA8B,+DAA+D,2BAA2B,EAAE,KAAK,GAAG,CAAC;AAAA,IACvR;AAAA,EACF;AAGA,MAAI,YAAY,kBAAkB,OAAO,SAAU,KAAKC,YAAW;AACjE,QAAIA,UAAS,IAAI,eAAe,OAAO;AAAA,MACrC,WAAWA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,iBAAiBA,UAAS,CAAC;AAC9B,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,OAAO,KAAK,SAAS,EAAE,KAAK,SAAU,GAAG,GAAG;AACjD,WAAO,UAAU,CAAC,IAAI,UAAU,CAAC;AAAA,EACnC,CAAC;AACH;;;ACtCA,SAAS,8BAA8B,WAAW;AAChD,MAAI,iBAAiB,SAAS,MAAM,MAAM;AACxC,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,oBAAoB,qBAAqB,SAAS;AACtD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AAEA,SAAS,KAAK,MAAM;AAClB,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,OAAO,KAAK;AAEhB,MAAI,MAAM,cAAc,IAAI,EAAE,OAAO;AACnC;AAAA,EACF;AAEA,MAAI,oBAAoB,QAAQ,UAC5B,gBAAgB,sBAAsB,SAAS,OAAO,mBACtD,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,OAAO,kBACpD,8BAA8B,QAAQ,oBACtC,UAAU,QAAQ,SAClB,WAAW,QAAQ,UACnB,eAAe,QAAQ,cACvB,cAAc,QAAQ,aACtB,wBAAwB,QAAQ,gBAChC,iBAAiB,0BAA0B,SAAS,OAAO,uBAC3D,wBAAwB,QAAQ;AACpC,MAAI,qBAAqB,MAAM,QAAQ;AACvC,MAAI,gBAAgB,iBAAiB,kBAAkB;AACvD,MAAI,kBAAkB,kBAAkB;AACxC,MAAI,qBAAqB,gCAAgC,mBAAmB,CAAC,iBAAiB,CAAC,qBAAqB,kBAAkB,CAAC,IAAI,8BAA8B,kBAAkB;AAC3L,MAAIC,cAAa,CAAC,kBAAkB,EAAE,OAAO,kBAAkB,EAAE,OAAO,SAAU,KAAKC,YAAW;AAChG,WAAO,IAAI,OAAO,iBAAiBA,UAAS,MAAM,OAAO,qBAAqB,OAAO;AAAA,MACnF,WAAWA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,IAAIA,UAAS;AAAA,EAChB,GAAG,CAAC,CAAC;AACL,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,YAAY,oBAAI,IAAI;AACxB,MAAI,qBAAqB;AACzB,MAAI,wBAAwBD,YAAW,CAAC;AAExC,WAAS,IAAI,GAAG,IAAIA,YAAW,QAAQ,KAAK;AAC1C,QAAI,YAAYA,YAAW,CAAC;AAE5B,QAAI,iBAAiB,iBAAiB,SAAS;AAE/C,QAAI,mBAAmB,aAAa,SAAS,MAAM;AACnD,QAAI,aAAa,CAAC,KAAK,MAAM,EAAE,QAAQ,cAAc,KAAK;AAC1D,QAAI,MAAM,aAAa,UAAU;AACjC,QAAI,WAAW,eAAe,OAAO;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,oBAAoB,aAAa,mBAAmB,QAAQ,OAAO,mBAAmB,SAAS;AAEnG,QAAI,cAAc,GAAG,IAAI,WAAW,GAAG,GAAG;AACxC,0BAAoB,qBAAqB,iBAAiB;AAAA,IAC5D;AAEA,QAAI,mBAAmB,qBAAqB,iBAAiB;AAC7D,QAAI,SAAS,CAAC;AAEd,QAAI,eAAe;AACjB,aAAO,KAAK,SAAS,cAAc,KAAK,CAAC;AAAA,IAC3C;AAEA,QAAI,cAAc;AAChB,aAAO,KAAK,SAAS,iBAAiB,KAAK,GAAG,SAAS,gBAAgB,KAAK,CAAC;AAAA,IAC/E;AAEA,QAAI,OAAO,MAAM,SAAU,OAAO;AAChC,aAAO;AAAA,IACT,CAAC,GAAG;AACF,8BAAwB;AACxB,2BAAqB;AACrB;AAAA,IACF;AAEA,cAAU,IAAI,WAAW,MAAM;AAAA,EACjC;AAEA,MAAI,oBAAoB;AAEtB,QAAI,iBAAiB,iBAAiB,IAAI;AAE1C,QAAI,QAAQ,SAASE,OAAMC,KAAI;AAC7B,UAAI,mBAAmBH,YAAW,KAAK,SAAUC,YAAW;AAC1D,YAAIG,UAAS,UAAU,IAAIH,UAAS;AAEpC,YAAIG,SAAQ;AACV,iBAAOA,QAAO,MAAM,GAAGD,GAAE,EAAE,MAAM,SAAU,OAAO;AAChD,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,kBAAkB;AACpB,gCAAwB;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,KAAK,gBAAgB,KAAK,GAAG,MAAM;AAC1C,UAAI,OAAO,MAAM,EAAE;AAEnB,UAAI,SAAS;AAAS;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,MAAM,cAAc,uBAAuB;AAC7C,UAAM,cAAc,IAAI,EAAE,QAAQ;AAClC,UAAM,YAAY;AAClB,UAAM,QAAQ;AAAA,EAChB;AACF;AAGA,IAAO,eAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,QAAQ;AAAA,EAC3B,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AACF;;;AC/IA,SAAS,eAAe,UAAU,MAAM,kBAAkB;AACxD,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,MACjB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAEA,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK,SAAS,iBAAiB;AAAA,IACnD,OAAO,SAAS,QAAQ,KAAK,QAAQ,iBAAiB;AAAA,IACtD,QAAQ,SAAS,SAAS,KAAK,SAAS,iBAAiB;AAAA,IACzD,MAAM,SAAS,OAAO,KAAK,QAAQ,iBAAiB;AAAA,EACtD;AACF;AAEA,SAAS,sBAAsB,UAAU;AACvC,SAAO,CAAC,KAAK,OAAO,QAAQ,IAAI,EAAE,KAAK,SAAU,MAAM;AACrD,WAAO,SAAS,IAAI,KAAK;AAAA,EAC3B,CAAC;AACH;AAEA,SAAS,KAAK,MAAM;AAClB,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAChB,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,mBAAmB,MAAM,cAAc;AAC3C,MAAI,oBAAoB,eAAe,OAAO;AAAA,IAC5C,gBAAgB;AAAA,EAClB,CAAC;AACD,MAAI,oBAAoB,eAAe,OAAO;AAAA,IAC5C,aAAa;AAAA,EACf,CAAC;AACD,MAAI,2BAA2B,eAAe,mBAAmB,aAAa;AAC9E,MAAI,sBAAsB,eAAe,mBAAmB,YAAY,gBAAgB;AACxF,MAAI,oBAAoB,sBAAsB,wBAAwB;AACtE,MAAI,mBAAmB,sBAAsB,mBAAmB;AAChE,QAAM,cAAc,IAAI,IAAI;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IACnE,gCAAgC;AAAA,IAChC,uBAAuB;AAAA,EACzB,CAAC;AACH;AAGA,IAAO,eAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,kBAAkB,CAAC,iBAAiB;AAAA,EACpC,IAAI;AACN;;;ACzDO,SAAS,wBAAwB,WAAW,OAAOE,SAAQ;AAChE,MAAI,gBAAgB,iBAAiB,SAAS;AAC9C,MAAI,iBAAiB,CAAC,MAAM,GAAG,EAAE,QAAQ,aAAa,KAAK,IAAI,KAAK;AAEpE,MAAI,OAAO,OAAOA,YAAW,aAAaA,QAAO,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,IACxE;AAAA,EACF,CAAC,CAAC,IAAIA,SACF,WAAW,KAAK,CAAC,GACjB,WAAW,KAAK,CAAC;AAErB,aAAW,YAAY;AACvB,cAAY,YAAY,KAAK;AAC7B,SAAO,CAAC,MAAM,KAAK,EAAE,QAAQ,aAAa,KAAK,IAAI;AAAA,IACjD,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM,SAChB,OAAO,MAAM;AACjB,MAAI,kBAAkB,QAAQ,QAC1BA,UAAS,oBAAoB,SAAS,CAAC,GAAG,CAAC,IAAI;AACnD,MAAI,OAAO,WAAW,OAAO,SAAU,KAAK,WAAW;AACrD,QAAI,SAAS,IAAI,wBAAwB,WAAW,MAAM,OAAOA,OAAM;AACvE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,MAAI,wBAAwB,KAAK,MAAM,SAAS,GAC5C,IAAI,sBAAsB,GAC1B,IAAI,sBAAsB;AAE9B,MAAI,MAAM,cAAc,iBAAiB,MAAM;AAC7C,UAAM,cAAc,cAAc,KAAK;AACvC,UAAM,cAAc,cAAc,KAAK;AAAA,EACzC;AAEA,QAAM,cAAc,IAAI,IAAI;AAC9B;AAGA,IAAO,iBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,UAAU,CAAC,eAAe;AAAA,EAC1B,IAAI;AACN;;;ACnDA,SAAS,cAAc,MAAM;AAC3B,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAKhB,QAAM,cAAc,IAAI,IAAI,eAAe;AAAA,IACzC,WAAW,MAAM,MAAM;AAAA,IACvB,SAAS,MAAM,MAAM;AAAA,IACrB,UAAU;AAAA,IACV,WAAW,MAAM;AAAA,EACnB,CAAC;AACH;AAGA,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM,CAAC;AACT;;;ACxBe,SAAR,WAA4B,MAAM;AACvC,SAAO,SAAS,MAAM,MAAM;AAC9B;;;ACUA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,OAAO,KAAK;AAChB,MAAI,oBAAoB,QAAQ,UAC5B,gBAAgB,sBAAsB,SAAS,OAAO,mBACtD,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,QAAQ,kBACrD,WAAW,QAAQ,UACnB,eAAe,QAAQ,cACvB,cAAc,QAAQ,aACtB,UAAU,QAAQ,SAClB,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO,iBAC7C,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,IAAI;AAC1D,MAAI,WAAW,eAAe,OAAO;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,gBAAgB,iBAAiB,MAAM,SAAS;AACpD,MAAI,YAAY,aAAa,MAAM,SAAS;AAC5C,MAAI,kBAAkB,CAAC;AACvB,MAAI,WAAW,yBAAyB,aAAa;AACrD,MAAI,UAAU,WAAW,QAAQ;AACjC,MAAIC,iBAAgB,MAAM,cAAc;AACxC,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,oBAAoB,OAAO,iBAAiB,aAAa,aAAa,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO;AAAA,IACvG,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC,IAAI;AACN,MAAI,8BAA8B,OAAO,sBAAsB,WAAW;AAAA,IACxE,UAAU;AAAA,IACV,SAAS;AAAA,EACX,IAAI,OAAO,OAAO;AAAA,IAChB,UAAU;AAAA,IACV,SAAS;AAAA,EACX,GAAG,iBAAiB;AACpB,MAAI,sBAAsB,MAAM,cAAc,SAAS,MAAM,cAAc,OAAO,MAAM,SAAS,IAAI;AACrG,MAAI,OAAO;AAAA,IACT,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,CAACA,gBAAe;AAClB;AAAA,EACF;AAEA,MAAI,eAAe;AACjB,QAAI;AAEJ,QAAI,WAAW,aAAa,MAAM,MAAM;AACxC,QAAI,UAAU,aAAa,MAAM,SAAS;AAC1C,QAAI,MAAM,aAAa,MAAM,WAAW;AACxC,QAAIC,UAASD,eAAc,QAAQ;AACnC,QAAIE,OAAMD,UAAS,SAAS,QAAQ;AACpC,QAAIE,OAAMF,UAAS,SAAS,OAAO;AACnC,QAAI,WAAW,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI;AAC/C,QAAI,SAAS,cAAc,QAAQ,cAAc,GAAG,IAAI,WAAW,GAAG;AACtE,QAAI,SAAS,cAAc,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,GAAG;AAGxE,QAAI,eAAe,MAAM,SAAS;AAClC,QAAI,YAAY,UAAU,eAAe,cAAc,YAAY,IAAI;AAAA,MACrE,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,QAAI,qBAAqB,MAAM,cAAc,kBAAkB,IAAI,MAAM,cAAc,kBAAkB,EAAE,UAAU,mBAAmB;AACxI,QAAI,kBAAkB,mBAAmB,QAAQ;AACjD,QAAI,kBAAkB,mBAAmB,OAAO;AAMhD,QAAI,WAAW,OAAO,GAAG,cAAc,GAAG,GAAG,UAAU,GAAG,CAAC;AAC3D,QAAI,YAAY,kBAAkB,cAAc,GAAG,IAAI,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,WAAW,SAAS,WAAW,kBAAkB,4BAA4B;AAC5M,QAAI,YAAY,kBAAkB,CAAC,cAAc,GAAG,IAAI,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,WAAW,SAAS,WAAW,kBAAkB,4BAA4B;AAC7M,QAAI,oBAAoB,MAAM,SAAS,SAAS,gBAAgB,MAAM,SAAS,KAAK;AACpF,QAAI,eAAe,oBAAoB,aAAa,MAAM,kBAAkB,aAAa,IAAI,kBAAkB,cAAc,IAAI;AACjI,QAAI,uBAAuB,wBAAwB,uBAAuB,OAAO,SAAS,oBAAoB,QAAQ,MAAM,OAAO,wBAAwB;AAC3J,QAAI,YAAYA,UAAS,YAAY,sBAAsB;AAC3D,QAAI,YAAYA,UAAS,YAAY;AACrC,QAAI,kBAAkB,OAAO,SAAS,IAAQC,MAAK,SAAS,IAAIA,MAAKD,SAAQ,SAAS,IAAQE,MAAK,SAAS,IAAIA,IAAG;AACnH,IAAAH,eAAc,QAAQ,IAAI;AAC1B,SAAK,QAAQ,IAAI,kBAAkBC;AAAA,EACrC;AAEA,MAAI,cAAc;AAChB,QAAI;AAEJ,QAAI,YAAY,aAAa,MAAM,MAAM;AAEzC,QAAI,WAAW,aAAa,MAAM,SAAS;AAE3C,QAAI,UAAUD,eAAc,OAAO;AAEnC,QAAI,OAAO,YAAY,MAAM,WAAW;AAExC,QAAI,OAAO,UAAU,SAAS,SAAS;AAEvC,QAAI,OAAO,UAAU,SAAS,QAAQ;AAEtC,QAAI,eAAe,CAAC,KAAK,IAAI,EAAE,QAAQ,aAAa,MAAM;AAE1D,QAAI,wBAAwB,yBAAyB,uBAAuB,OAAO,SAAS,oBAAoB,OAAO,MAAM,OAAO,yBAAyB;AAE7J,QAAI,aAAa,eAAe,OAAO,UAAU,cAAc,IAAI,IAAI,WAAW,IAAI,IAAI,uBAAuB,4BAA4B;AAE7I,QAAI,aAAa,eAAe,UAAU,cAAc,IAAI,IAAI,WAAW,IAAI,IAAI,uBAAuB,4BAA4B,UAAU;AAEhJ,QAAI,mBAAmB,UAAU,eAAe,eAAe,YAAY,SAAS,UAAU,IAAI,OAAO,SAAS,aAAa,MAAM,SAAS,SAAS,aAAa,IAAI;AAExK,IAAAA,eAAc,OAAO,IAAI;AACzB,SAAK,OAAO,IAAI,mBAAmB;AAAA,EACrC;AAEA,QAAM,cAAc,IAAI,IAAI;AAC9B;AAGA,IAAO,0BAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,QAAQ;AAC7B;;;AC7Ie,SAAR,qBAAsC,SAAS;AACpD,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;;;ACDe,SAAR,cAA+B,MAAM;AAC1C,MAAI,SAAS,UAAU,IAAI,KAAK,CAAC,cAAc,IAAI,GAAG;AACpD,WAAO,gBAAgB,IAAI;AAAA,EAC7B,OAAO;AACL,WAAO,qBAAqB,IAAI;AAAA,EAClC;AACF;;;ACDA,SAAS,gBAAgB,SAAS;AAChC,MAAI,OAAO,QAAQ,sBAAsB;AACzC,MAAI,SAAS,MAAM,KAAK,KAAK,IAAI,QAAQ,eAAe;AACxD,MAAI,SAAS,MAAM,KAAK,MAAM,IAAI,QAAQ,gBAAgB;AAC1D,SAAO,WAAW,KAAK,WAAW;AACpC;AAIe,SAAR,iBAAkC,yBAAyB,cAAc,SAAS;AACvF,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAEA,MAAI,0BAA0B,cAAc,YAAY;AACxD,MAAI,uBAAuB,cAAc,YAAY,KAAK,gBAAgB,YAAY;AACtF,MAAI,kBAAkB,mBAAmB,YAAY;AACrD,MAAI,OAAO,sBAAsB,yBAAyB,sBAAsB,OAAO;AACvF,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,UAAU;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM;AAAA,IAClC,eAAe,eAAe,GAAG;AAC/B,eAAS,cAAc,YAAY;AAAA,IACrC;AAEA,QAAI,cAAc,YAAY,GAAG;AAC/B,gBAAU,sBAAsB,cAAc,IAAI;AAClD,cAAQ,KAAK,aAAa;AAC1B,cAAQ,KAAK,aAAa;AAAA,IAC5B,WAAW,iBAAiB;AAC1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAG,KAAK,OAAO,OAAO,aAAa,QAAQ;AAAA,IAC3C,GAAG,KAAK,MAAM,OAAO,YAAY,QAAQ;AAAA,IACzC,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;;;ACvDA,SAAS,MAAM,WAAW;AACxB,MAAI,MAAM,oBAAI,IAAI;AAClB,MAAI,UAAU,oBAAI,IAAI;AACtB,MAAI,SAAS,CAAC;AACd,YAAU,QAAQ,SAAU,UAAU;AACpC,QAAI,IAAI,SAAS,MAAM,QAAQ;AAAA,EACjC,CAAC;AAED,WAAS,KAAK,UAAU;AACtB,YAAQ,IAAI,SAAS,IAAI;AACzB,QAAI,WAAW,CAAC,EAAE,OAAO,SAAS,YAAY,CAAC,GAAG,SAAS,oBAAoB,CAAC,CAAC;AACjF,aAAS,QAAQ,SAAU,KAAK;AAC9B,UAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACrB,YAAI,cAAc,IAAI,IAAI,GAAG;AAE7B,YAAI,aAAa;AACf,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,KAAK,QAAQ;AAAA,EACtB;AAEA,YAAU,QAAQ,SAAU,UAAU;AACpC,QAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,GAAG;AAE/B,WAAK,QAAQ;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEe,SAAR,eAAgC,WAAW;AAEhD,MAAI,mBAAmB,MAAM,SAAS;AAEtC,SAAO,eAAe,OAAO,SAAU,KAAK,OAAO;AACjD,WAAO,IAAI,OAAO,iBAAiB,OAAO,SAAU,UAAU;AAC5D,aAAO,SAAS,UAAU;AAAA,IAC5B,CAAC,CAAC;AAAA,EACJ,GAAG,CAAC,CAAC;AACP;;;AC3Ce,SAAR,SAA0BI,KAAI;AACnC,MAAI;AACJ,SAAO,WAAY;AACjB,QAAI,CAAC,SAAS;AACZ,gBAAU,IAAI,QAAQ,SAAU,SAAS;AACvC,gBAAQ,QAAQ,EAAE,KAAK,WAAY;AACjC,oBAAU;AACV,kBAAQA,IAAG,CAAC;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AACF;;;ACde,SAAR,OAAwB,KAAK;AAClC,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,SAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACjC;AAEA,SAAO,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,SAAU,GAAG,GAAG;AAC5C,WAAO,EAAE,QAAQ,MAAM,CAAC;AAAA,EAC1B,GAAG,GAAG;AACR;;;ACNA,IAAI,yBAAyB;AAC7B,IAAI,2BAA2B;AAC/B,IAAI,mBAAmB,CAAC,QAAQ,WAAW,SAAS,MAAM,UAAU,YAAY,SAAS;AAC1E,SAAR,kBAAmC,WAAW;AACnD,YAAU,QAAQ,SAAU,UAAU;AACpC,KAAC,EAAE,OAAO,OAAO,KAAK,QAAQ,GAAG,gBAAgB,EAChD,OAAO,SAAU,OAAO,OAAO,MAAM;AACpC,aAAO,KAAK,QAAQ,KAAK,MAAM;AAAA,IACjC,CAAC,EAAE,QAAQ,SAAU,KAAK;AACxB,cAAQ,KAAK;AAAA,QACX,KAAK;AACH,cAAI,OAAO,SAAS,SAAS,UAAU;AACrC,oBAAQ,MAAM,OAAO,wBAAwB,OAAO,SAAS,IAAI,GAAG,UAAU,YAAY,MAAO,OAAO,SAAS,IAAI,IAAI,GAAI,CAAC;AAAA,UAChI;AAEA;AAAA,QAEF,KAAK;AACH,cAAI,OAAO,SAAS,YAAY,WAAW;AACzC,oBAAQ,MAAM,OAAO,wBAAwB,SAAS,MAAM,aAAa,aAAa,MAAO,OAAO,SAAS,OAAO,IAAI,GAAI,CAAC;AAAA,UAC/H;AAEA;AAAA,QAEF,KAAK;AACH,cAAI,eAAe,QAAQ,SAAS,KAAK,IAAI,GAAG;AAC9C,oBAAQ,MAAM,OAAO,wBAAwB,SAAS,MAAM,WAAW,YAAY,eAAe,KAAK,IAAI,GAAG,MAAO,OAAO,SAAS,KAAK,IAAI,GAAI,CAAC;AAAA,UACrJ;AAEA;AAAA,QAEF,KAAK;AACH,cAAI,OAAO,SAAS,OAAO,YAAY;AACrC,oBAAQ,MAAM,OAAO,wBAAwB,SAAS,MAAM,QAAQ,cAAc,MAAO,OAAO,SAAS,EAAE,IAAI,GAAI,CAAC;AAAA,UACtH;AAEA;AAAA,QAEF,KAAK;AACH,cAAI,SAAS,UAAU,QAAQ,OAAO,SAAS,WAAW,YAAY;AACpE,oBAAQ,MAAM,OAAO,wBAAwB,SAAS,MAAM,YAAY,cAAc,MAAO,OAAO,SAAS,EAAE,IAAI,GAAI,CAAC;AAAA,UAC1H;AAEA;AAAA,QAEF,KAAK;AACH,cAAI,SAAS,YAAY,QAAQ,CAAC,MAAM,QAAQ,SAAS,QAAQ,GAAG;AAClE,oBAAQ,MAAM,OAAO,wBAAwB,SAAS,MAAM,cAAc,WAAW,MAAO,OAAO,SAAS,QAAQ,IAAI,GAAI,CAAC;AAAA,UAC/H;AAEA;AAAA,QAEF,KAAK;AACH,cAAI,CAAC,MAAM,QAAQ,SAAS,gBAAgB,GAAG;AAC7C,oBAAQ,MAAM,OAAO,wBAAwB,SAAS,MAAM,sBAAsB,WAAW,MAAO,OAAO,SAAS,gBAAgB,IAAI,GAAI,CAAC;AAAA,UAC/I;AAEA;AAAA,QAEF,KAAK;AAAA,QACL,KAAK;AACH;AAAA,QAEF;AACE,kBAAQ,MAAM,6DAA8D,SAAS,OAAO,sCAAuC,iBAAiB,IAAI,SAAU,GAAG;AACnK,mBAAO,MAAO,IAAI;AAAA,UACpB,CAAC,EAAE,KAAK,IAAI,IAAI,YAAa,MAAM,iBAAkB;AAAA,MACzD;AAEA,eAAS,YAAY,SAAS,SAAS,QAAQ,SAAU,aAAa;AACpE,YAAI,UAAU,KAAK,SAAU,KAAK;AAChC,iBAAO,IAAI,SAAS;AAAA,QACtB,CAAC,KAAK,MAAM;AACV,kBAAQ,MAAM,OAAO,0BAA0B,OAAO,SAAS,IAAI,GAAG,aAAa,WAAW,CAAC;AAAA,QACjG;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH;;;AChFe,SAAR,SAA0B,KAAKC,KAAI;AACxC,MAAI,cAAc,oBAAI,IAAI;AAC1B,SAAO,IAAI,OAAO,SAAU,MAAM;AAChC,QAAI,aAAaA,IAAG,IAAI;AAExB,QAAI,CAAC,YAAY,IAAI,UAAU,GAAG;AAChC,kBAAY,IAAI,UAAU;AAC1B,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;;;ACVe,SAAR,YAA6B,WAAW;AAC7C,MAAI,SAAS,UAAU,OAAO,SAAUC,SAAQ,SAAS;AACvD,QAAI,WAAWA,QAAO,QAAQ,IAAI;AAClC,IAAAA,QAAO,QAAQ,IAAI,IAAI,WAAW,OAAO,OAAO,CAAC,GAAG,UAAU,SAAS;AAAA,MACrE,SAAS,OAAO,OAAO,CAAC,GAAG,SAAS,SAAS,QAAQ,OAAO;AAAA,MAC5D,MAAM,OAAO,OAAO,CAAC,GAAG,SAAS,MAAM,QAAQ,IAAI;AAAA,IACrD,CAAC,IAAI;AACL,WAAOA;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO,OAAO,KAAK,MAAM,EAAE,IAAI,SAAU,KAAK;AAC5C,WAAO,OAAO,GAAG;AAAA,EACnB,CAAC;AACH;;;ACCA,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB;AAC1B,IAAI,kBAAkB;AAAA,EACpB,WAAW;AAAA,EACX,WAAW,CAAC;AAAA,EACZ,UAAU;AACZ;AAEA,SAAS,mBAAmB;AAC1B,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,SAAK,IAAI,IAAI,UAAU,IAAI;AAAA,EAC7B;AAEA,SAAO,CAAC,KAAK,KAAK,SAAU,SAAS;AACnC,WAAO,EAAE,WAAW,OAAO,QAAQ,0BAA0B;AAAA,EAC/D,CAAC;AACH;AAEO,SAAS,gBAAgB,kBAAkB;AAChD,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB,CAAC;AAAA,EACtB;AAEA,MAAI,oBAAoB,kBACpB,wBAAwB,kBAAkB,kBAC1CC,oBAAmB,0BAA0B,SAAS,CAAC,IAAI,uBAC3D,yBAAyB,kBAAkB,gBAC3C,iBAAiB,2BAA2B,SAAS,kBAAkB;AAC3E,SAAO,SAASC,cAAaC,YAAWC,SAAQ,SAAS;AACvD,QAAI,YAAY,QAAQ;AACtB,gBAAU;AAAA,IACZ;AAEA,QAAI,QAAQ;AAAA,MACV,WAAW;AAAA,MACX,kBAAkB,CAAC;AAAA,MACnB,SAAS,OAAO,OAAO,CAAC,GAAG,iBAAiB,cAAc;AAAA,MAC1D,eAAe,CAAC;AAAA,MAChB,UAAU;AAAA,QACR,WAAWD;AAAA,QACX,QAAQC;AAAA,MACV;AAAA,MACA,YAAY,CAAC;AAAA,MACb,QAAQ,CAAC;AAAA,IACX;AACA,QAAI,mBAAmB,CAAC;AACxB,QAAI,cAAc;AAClB,QAAI,WAAW;AAAA,MACb;AAAA,MACA,YAAY,SAAS,WAAW,kBAAkB;AAChD,YAAIC,WAAU,OAAO,qBAAqB,aAAa,iBAAiB,MAAM,OAAO,IAAI;AACzF,+BAAuB;AACvB,cAAM,UAAU,OAAO,OAAO,CAAC,GAAG,gBAAgB,MAAM,SAASA,QAAO;AACxE,cAAM,gBAAgB;AAAA,UACpB,WAAW,UAAUF,UAAS,IAAI,kBAAkBA,UAAS,IAAIA,WAAU,iBAAiB,kBAAkBA,WAAU,cAAc,IAAI,CAAC;AAAA,UAC3I,QAAQ,kBAAkBC,OAAM;AAAA,QAClC;AAGA,YAAI,mBAAmB,eAAe,YAAY,CAAC,EAAE,OAAOH,mBAAkB,MAAM,QAAQ,SAAS,CAAC,CAAC;AAEvG,cAAM,mBAAmB,iBAAiB,OAAO,SAAU,GAAG;AAC5D,iBAAO,EAAE;AAAA,QACX,CAAC;AAGD,YAAI,MAAuC;AACzC,cAAI,YAAY,SAAS,CAAC,EAAE,OAAO,kBAAkB,MAAM,QAAQ,SAAS,GAAG,SAAU,MAAM;AAC7F,gBAAI,OAAO,KAAK;AAChB,mBAAO;AAAA,UACT,CAAC;AACD,4BAAkB,SAAS;AAE3B,cAAI,iBAAiB,MAAM,QAAQ,SAAS,MAAM,MAAM;AACtD,gBAAI,eAAe,MAAM,iBAAiB,KAAK,SAAU,OAAO;AAC9D,kBAAI,OAAO,MAAM;AACjB,qBAAO,SAAS;AAAA,YAClB,CAAC;AAED,gBAAI,CAAC,cAAc;AACjB,sBAAQ,MAAM,CAAC,4DAA4D,8BAA8B,EAAE,KAAK,GAAG,CAAC;AAAA,YACtH;AAAA,UACF;AAEA,cAAI,oBAAoBK,kBAAiBF,OAAM,GAC3C,YAAY,kBAAkB,WAC9B,cAAc,kBAAkB,aAChC,eAAe,kBAAkB,cACjC,aAAa,kBAAkB;AAInC,cAAI,CAAC,WAAW,aAAa,cAAc,UAAU,EAAE,KAAK,SAAU,QAAQ;AAC5E,mBAAO,WAAW,MAAM;AAAA,UAC1B,CAAC,GAAG;AACF,oBAAQ,KAAK,CAAC,+DAA+D,6DAA6D,8DAA8D,4DAA4D,YAAY,EAAE,KAAK,GAAG,CAAC;AAAA,UAC7R;AAAA,QACF;AAEA,2BAAmB;AACnB,eAAO,SAAS,OAAO;AAAA,MACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,aAAa,SAAS,cAAc;AAClC,YAAI,aAAa;AACf;AAAA,QACF;AAEA,YAAI,kBAAkB,MAAM,UACxBD,aAAY,gBAAgB,WAC5BC,UAAS,gBAAgB;AAG7B,YAAI,CAAC,iBAAiBD,YAAWC,OAAM,GAAG;AACxC,cAAI,MAAuC;AACzC,oBAAQ,MAAM,qBAAqB;AAAA,UACrC;AAEA;AAAA,QACF;AAGA,cAAM,QAAQ;AAAA,UACZ,WAAW,iBAAiBD,YAAW,gBAAgBC,OAAM,GAAG,MAAM,QAAQ,aAAa,OAAO;AAAA,UAClG,QAAQ,cAAcA,OAAM;AAAA,QAC9B;AAMA,cAAM,QAAQ;AACd,cAAM,YAAY,MAAM,QAAQ;AAKhC,cAAM,iBAAiB,QAAQ,SAAU,UAAU;AACjD,iBAAO,MAAM,cAAc,SAAS,IAAI,IAAI,OAAO,OAAO,CAAC,GAAG,SAAS,IAAI;AAAA,QAC7E,CAAC;AACD,YAAI,kBAAkB;AAEtB,iBAAS,QAAQ,GAAG,QAAQ,MAAM,iBAAiB,QAAQ,SAAS;AAClE,cAAI,MAAuC;AACzC,+BAAmB;AAEnB,gBAAI,kBAAkB,KAAK;AACzB,sBAAQ,MAAM,mBAAmB;AACjC;AAAA,YACF;AAAA,UACF;AAEA,cAAI,MAAM,UAAU,MAAM;AACxB,kBAAM,QAAQ;AACd,oBAAQ;AACR;AAAA,UACF;AAEA,cAAI,wBAAwB,MAAM,iBAAiB,KAAK,GACpDG,MAAK,sBAAsB,IAC3B,yBAAyB,sBAAsB,SAC/C,WAAW,2BAA2B,SAAS,CAAC,IAAI,wBACpD,OAAO,sBAAsB;AAEjC,cAAI,OAAOA,QAAO,YAAY;AAC5B,oBAAQA,IAAG;AAAA,cACT;AAAA,cACA,SAAS;AAAA,cACT;AAAA,cACA;AAAA,YACF,CAAC,KAAK;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA,MAGA,QAAQ,SAAS,WAAY;AAC3B,eAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,mBAAS,YAAY;AACrB,kBAAQ,KAAK;AAAA,QACf,CAAC;AAAA,MACH,CAAC;AAAA,MACD,SAAS,SAAS,UAAU;AAC1B,+BAAuB;AACvB,sBAAc;AAAA,MAChB;AAAA,IACF;AAEA,QAAI,CAAC,iBAAiBJ,YAAWC,OAAM,GAAG;AACxC,UAAI,MAAuC;AACzC,gBAAQ,MAAM,qBAAqB;AAAA,MACrC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,OAAO,EAAE,KAAK,SAAUI,QAAO;AACjD,UAAI,CAAC,eAAe,QAAQ,eAAe;AACzC,gBAAQ,cAAcA,MAAK;AAAA,MAC7B;AAAA,IACF,CAAC;AAMD,aAAS,qBAAqB;AAC5B,YAAM,iBAAiB,QAAQ,SAAU,OAAO;AAC9C,YAAI,OAAO,MAAM,MACb,gBAAgB,MAAM,SACtBH,WAAU,kBAAkB,SAAS,CAAC,IAAI,eAC1CI,UAAS,MAAM;AAEnB,YAAI,OAAOA,YAAW,YAAY;AAChC,cAAI,YAAYA,QAAO;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAASJ;AAAA,UACX,CAAC;AAED,cAAI,SAAS,SAASK,UAAS;AAAA,UAAC;AAEhC,2BAAiB,KAAK,aAAa,MAAM;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,yBAAyB;AAChC,uBAAiB,QAAQ,SAAUH,KAAI;AACrC,eAAOA,IAAG;AAAA,MACZ,CAAC;AACD,yBAAmB,CAAC;AAAA,IACtB;AAEA,WAAO;AAAA,EACT;AACF;AACO,IAAI,eAA4B,gBAAgB;;;AC3PvD,IAAI,mBAAmB,CAAC,wBAAgB,uBAAe,uBAAe,mBAAW;AACjF,IAAII,gBAA4B,gBAAgB;AAAA,EAC9C;AACF,CAAC;;;ACED,IAAIC,oBAAmB,CAAC,wBAAgB,uBAAe,uBAAe,qBAAa,gBAAQ,cAAM,yBAAiB,eAAO,YAAI;AAC7H,IAAIC,gBAA4B,gBAAgB;AAAA,EAC9C,kBAAkBD;AACpB,CAAC;;;A1DVD,gCAAoB;AAEpB,IAAI,kBAAkB,CAAC;AAChB,IAAI,YAAY,SAASE,WAAU,kBAAkB,eAAe,SAAS;AAClF,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,cAAoB,cAAO,IAAI;AACnC,MAAI,sBAAsB;AAAA,IACxB,eAAe,QAAQ;AAAA,IACvB,WAAW,QAAQ,aAAa;AAAA,IAChC,UAAU,QAAQ,YAAY;AAAA,IAC9B,WAAW,QAAQ,aAAa;AAAA,EAClC;AAEA,MAAI,kBAAwB,gBAAS;AAAA,IACnC,QAAQ;AAAA,MACN,QAAQ;AAAA,QACN,UAAU,oBAAoB;AAAA,QAC9B,MAAM;AAAA,QACN,KAAK;AAAA,MACP;AAAA,MACA,OAAO;AAAA,QACL,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY,CAAC;AAAA,EACf,CAAC,GACG,QAAQ,gBAAgB,CAAC,GACzB,WAAW,gBAAgB,CAAC;AAEhC,MAAI,sBAA4B,eAAQ,WAAY;AAClD,WAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,IAAI,SAASC,IAAG,MAAM;AACpB,YAAIC,SAAQ,KAAK;AACjB,YAAI,WAAW,OAAO,KAAKA,OAAM,QAAQ;AACzC,QAAS,mBAAU,WAAY;AAC7B,mBAAS;AAAA,YACP,QAAQ,YAAY,SAAS,IAAI,SAAU,SAAS;AAClD,qBAAO,CAAC,SAASA,OAAM,OAAO,OAAO,KAAK,CAAC,CAAC;AAAA,YAC9C,CAAC,CAAC;AAAA,YACF,YAAY,YAAY,SAAS,IAAI,SAAU,SAAS;AACtD,qBAAO,CAAC,SAASA,OAAM,WAAW,OAAO,CAAC;AAAA,YAC5C,CAAC,CAAC;AAAA,UACJ,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,IAC5B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,gBAAsB,eAAQ,WAAY;AAC5C,QAAI,aAAa;AAAA,MACf,eAAe,oBAAoB;AAAA,MACnC,WAAW,oBAAoB;AAAA,MAC/B,UAAU,oBAAoB;AAAA,MAC9B,WAAW,CAAC,EAAE,OAAO,oBAAoB,WAAW,CAAC,qBAAqB;AAAA,QACxE,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC,CAAC;AAAA,IACJ;AAEA,YAAI,0BAAAC,SAAQ,YAAY,SAAS,UAAU,GAAG;AAC5C,aAAO,YAAY,WAAW;AAAA,IAChC,OAAO;AACL,kBAAY,UAAU;AACtB,aAAO;AAAA,IACT;AAAA,EACF,GAAG,CAAC,oBAAoB,eAAe,oBAAoB,WAAW,oBAAoB,UAAU,oBAAoB,WAAW,mBAAmB,CAAC;AACvJ,MAAI,oBAA0B,cAAO;AACrC,4BAA0B,WAAY;AACpC,QAAI,kBAAkB,SAAS;AAC7B,wBAAkB,QAAQ,WAAW,aAAa;AAAA,IACpD;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAClB,4BAA0B,WAAY;AACpC,QAAI,oBAAoB,QAAQ,iBAAiB,MAAM;AACrD;AAAA,IACF;AAEA,QAAIC,gBAAe,QAAQ,gBAAgBA;AAC3C,QAAI,iBAAiBA,cAAa,kBAAkB,eAAe,aAAa;AAChF,sBAAkB,UAAU;AAC5B,WAAO,WAAY;AACjB,qBAAe,QAAQ;AACvB,wBAAkB,UAAU;AAAA,IAC9B;AAAA,EACF,GAAG,CAAC,kBAAkB,eAAe,QAAQ,YAAY,CAAC;AAC1D,SAAO;AAAA,IACL,OAAO,kBAAkB,UAAU,kBAAkB,QAAQ,QAAQ;AAAA,IACrE,QAAQ,MAAM;AAAA,IACd,YAAY,MAAM;AAAA,IAClB,QAAQ,kBAAkB,UAAU,kBAAkB,QAAQ,SAAS;AAAA,IACvE,aAAa,kBAAkB,UAAU,kBAAkB,QAAQ,cAAc;AAAA,EACnF;AACF;;;A2DrGA,IAAAC,SAAuB;AACvB,qBAAoB;;;AlEkBpB,SAAS,aAAa;AACpB,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,WAAW,MAAM,MAAM,SAAS;AACzC;AAEA,SAAS,WAAW,SAAS;AAC3B,SAAO,cAAc,WAAW,QAAQ,aAAa,SAAS;AAChE;AAEA,SAAS,SAAS,MAAM;AACtB,MAAI,YAAY,QAAQ,KAAK,WAAW;AAAM,WAAO;AACrD,MAAI,WAAW,IAAI;AAAG,WAAO,KAAK,eAAe;AACjD,SAAO;AACT;AAEA,SAASC,eAAc,MAAM;AAC3B,SAAO,QAAQ,KAAK,iBAAiB;AACvC;AAEA,SAAS,YAAY,MAAM;AACzB,MAAI,MAAMA,eAAc,IAAI;AAC5B,SAAO,OAAO,IAAI,eAAe;AACnC;AAEA,SAASC,kBAAiB,MAAM,eAAe;AAC7C,SAAO,YAAY,IAAI,EAAE,iBAAiB,MAAM,aAAa;AAC/D;AAEA,IAAI,SAAS;AACb,SAAS,UAAU,QAAQ;AACzB,SAAO,OAAO,QAAQ,QAAQ,KAAK,EAAE,YAAY;AACnD;AAEA,IAAI,YAAY;AAChB,SAAS,mBAAmB,QAAQ;AAClC,SAAO,UAAU,MAAM,EAAE,QAAQ,WAAW,MAAM;AACpD;AAEA,IAAI,sBAAsB;AAC1B,SAAS,YAAY,OAAO;AAC1B,SAAO,CAAC,EAAE,SAAS,oBAAoB,KAAK,KAAK;AACnD;AAEA,SAAS,MAAM,MAAM,UAAU;AAC7B,MAAI,MAAM;AACV,MAAI,aAAa;AACjB,MAAI,OAAO,aAAa,UAAU;AAChC,WAAO,KAAK,MAAM,iBAAiB,mBAAmB,QAAQ,CAAC,KAAKA,kBAAiB,IAAI,EAAE,iBAAiB,mBAAmB,QAAQ,CAAC;AAAA,EAC1I;AACA,SAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,KAAK;AAC3C,QAAI,QAAQ,SAAS,GAAG;AACxB,QAAI,CAAC,SAAS,UAAU,GAAG;AACzB,WAAK,MAAM,eAAe,mBAAmB,GAAG,CAAC;AAAA,IACnD,WAAW,YAAY,GAAG,GAAG;AAC3B,oBAAc,MAAM,MAAM,QAAQ;AAAA,IACpC,OAAO;AACL,aAAO,mBAAmB,GAAG,IAAI,OAAO,QAAQ;AAAA,IAClD;AAAA,EACF,CAAC;AACD,MAAI,YAAY;AACd,WAAO,gBAAgB,aAAa;AAAA,EACtC;AACA,OAAK,MAAM,WAAW,MAAM;AAC9B;AAEA,IAAI,YAAY,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS;AAEvF,IAAI;AACJ,SAAS,cAAc,QAAQ;AAC7B,MAAI,CAAC,QAAQ,SAAS,KAAK,QAAQ;AACjC,QAAI,WAAW;AACb,UAAI,YAAY,SAAS,cAAc,KAAK;AAC5C,gBAAU,MAAM,WAAW;AAC3B,gBAAU,MAAM,MAAM;AACtB,gBAAU,MAAM,QAAQ;AACxB,gBAAU,MAAM,SAAS;AACzB,gBAAU,MAAM,WAAW;AAC3B,eAAS,KAAK,YAAY,SAAS;AACnC,aAAO,UAAU,cAAc,UAAU;AACzC,eAAS,KAAK,YAAY,SAAS;AAAA,IACrC;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,GAAU,CAAC,gCAAgC,CAAC;AACpE,IAAM,iBAAiB,sCAAO,IAAI,MAAM;AAAA,EACtC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,0EAA0E,qBAAqB,kCAAkC,gEAAgE,eAAe,eAAe,KAAK,GAAG,GAAG,WAAS,MAAM,cAAc,UAAU,WAAS,MAAM,cAAc,UAAU,WAAS,SAAS,cAAc,KAAK,MAAM,OAAO,IAAI,GAAG,WAAS,MAAM,MAAM,MAAM,QAAQ,WAAS,MAAM,MAAM,OAAO,OAAO,WAAS,MAAM,cAAc,GAAI,CAAC,IAAI,gBAAgB,GAAG,eAAe,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC7jB,eAAe,eAAe;AAAA,EAC5B,OAAO;AACT;AACA,eAAe,YAAY;AAAA,EACzB,YAAY,mBAAAC,QAAU;AAAA,EACtB,YAAY,mBAAAA,QAAU;AACxB;AAEA,IAAM,iBAAiB;AACvB,IAAM,aAAa,sCAAO,IAAI,MAAM;AAAA,EAClC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,mCAAmC,2CAA2C,WAAW,eAAe,KAAK,GAAG,GAAG,WAAS,GAAG,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,QAAQ,WAAS,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,MAAM,UAAU,EAAE,GAAG,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACpZ,WAAW,eAAe;AAAA,EACxB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,cAAc,WAAS;AAC3B,QAAM,kBAAkB;AACxB,QAAM,kBAAkB;AACxB,SAAO,GAAI,CAAC,uCAAuC,8BAA8B,WAAW,8CAA8C,oGAAoG,WAAW,IAAI,GAAG,SAAS,iBAAiB,KAAK,MAAM,KAAK,GAAG,SAAS,iBAAiB,KAAK,MAAM,OAAO,IAAI,GAAG,SAAS,iBAAiB,KAAK,MAAM,KAAK,GAAG,MAAM,MAAM,QAAQ,GAAG,SAAS,iBAAiB,KAAK,MAAM,OAAO,IAAI,CAAC,GAAG,SAAS,iBAAiB,KAAK,MAAM,OAAO,GAAG,GAAG,SAAS,iBAAiB,KAAK,MAAM,KAAK,CAAC;AACvjB;AACA,IAAM,qBAAqB;AAAA,EACzB,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AACR;AACA,IAAM,cAAc,sCAAO,OAAO,MAAM;AAAA,EACtC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,wCAAwC,OAAO,KAAK,iMAAiM,cAAc,6HAA6H,oCAAoC,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,mBAAmB,KAAK,WAAS,MAAM,MAAM,MAAM,SAAS,SAAS,WAAS,GAAG,MAAM,MAAM,MAAM,OAAO,mBAAmB,UAAU,WAAS,MAAM,MAAM,MAAM,OAAO,mBAAmB,MAAM,WAAS,MAAM,MAAM,MAAM,OAAO,mBAAmB,MAAM,WAAS,YAAY,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACjyB,YAAY,eAAe;AAAA,EACzB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,eAAe,sCAAO,IAAI,MAAM;AAAA,EACpC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,sFAAsF,aAAa,KAAK,GAAG,GAAG,WAAS,MAAM,WAAW,GAAG,MAAM,MAAM,QAAQ,MAAM,SAAS,cAAc,KAAK,MAAM,KAAK,KAAK,WAAS,MAAM,UAAU,GAAG,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,SAAS,GAAG,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,QAAQ,MAAM,MAAM,MAAM,OAAO,OAAO,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC9c,aAAa,eAAe;AAAA,EAC1B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB,sCAAO,KAAK,MAAM;AAAA,EACzC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,wBAAwB,KAAK,wCAAwC,QAAQ,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC7Q,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,eAAe,sCAAO,IAAI,MAAM;AAAA,EACpC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,2BAA2B,4BAA4B,KAAK,aAAa,KAAK,kBAAkB,WAAW,eAAe,iBAAiB,KAAK,GAAG,GAAG,WAAS,MAAM,YAAY,YAAY,WAAS,MAAM,MAAM,QAAQ,IAAI,SAAS,cAAc,GAAG,GAAG,WAAS,GAAG,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,QAAQ,WAAS,MAAM,wBAAwB,WAAW,MAAM,MAAM,MAAM,SAAS,YAAY,MAAM,MAAM,MAAM,QAAQ,mBAAmB,OAAO,mBAAmB,OAAO,SAAS,WAAS,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,MAAM,UAAU,EAAE,GAAG,WAAS,MAAM,WAAW,SAAS,aAAa,KAAK,MAAM,KAAK,IAAI,MAAM,MAAM,OAAO,YAAY,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,MAAM,YAAY,UAAU,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAClzB,aAAa,eAAe;AAAA,EAC1B,OAAO;AACT;AAEA,IAAI;AAAJ,IAAQ;AACR,SAAS,aAAa;AAAE,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,WAAW,MAAM,MAAM,SAAS;AAAG;AACxV,IAAI,sBAAsB,SAASC,qBAAoB,OAAO;AAC5D,SAA0B,qBAAc,OAAO,WAAW;AAAA,IACxD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,OAAO,KAAwB,qBAAc,KAAK;AAAA,IAC3D,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,GAAsB,qBAAc,UAAU;AAAA,IAC5C,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,GAAG;AAAA,EACL,CAAC,GAAsB,qBAAc,QAAQ;AAAA,IAC3C,eAAe;AAAA,IACf,GAAG;AAAA,EACL,CAAC,CAAC,IAAI,YAAY,UAA6B,qBAAc,UAAU;AAAA,IACrE,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,GAAG;AAAA,IACH,MAAM;AAAA,EACR,CAAC,EAAE;AACL;AAEA,IAAM,mBAAmB,sCAAO,mBAAmB,EAAE,WAAW;AAAA,EAC9D,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,0BAA0B,OAAO,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,KAAK,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,GAAG,MAAM,MAAM,MAAM,OAAO,KAAK;AAC9K,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,GAAU,CAAC,8EAA8E,CAAC;AAChH,IAAM,cAAc,WAAS;AAC3B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,GAAG,MAAM,OAAO;AAChC,QAAM,aAAa,GAAG,MAAM,OAAO;AACnC,QAAM,QAAQ,SAAS,cAAc,KAAK,OAAO,IAAI;AACrD,SAAO,QAAQ,GAAG,SAAS,YAAY,KAAK;AAC9C;AACA,IAAM,eAAe,WAAS;AAC5B,SAAO,GAAI,CAAC,IAAI,WAAW,IAAI,GAAG,WAAW,MAAM,MAAM,UAAU,OAAO,MAAM,MAAM,KAAK,GAAG,MAAM,UAAU,MAAM,MAAM,YAAY,KAAK,MAAM,MAAM,YAAY,EAAE;AACvK;AACA,IAAM,cAAc,sCAAO,IAAI,MAAM;AAAA,EACnC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,6DAA6D,mBAAmB,gBAAgB,sBAAsB,6CAA6C,kBAAkB,mDAAmD,KAAK,yDAAyD,iCAAiC,+GAA+G,YAAY,eAAe,MAAM,GAAG,GAAG,WAAS,MAAM,aAAa,MAAM,GAAG,MAAM,MAAM,MAAM,OAAO,QAAQ,WAAS,MAAM,MAAM,YAAY,IAAI,aAAa,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,MAAM,MAAM,MAAM,OAAO,IAAI,WAAS,MAAM,cAAc,GAAI,CAAC,IAAI,mBAAmB,GAAG,aAAa,GAAG,WAAS,MAAM,MAAM,OAAO,OAAO,cAAc,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,cAAc,OAAO,WAAS,MAAM,cAAc,OAAO,WAAS,MAAM,cAAc,uBAAuB,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACjiC,YAAY,YAAY;AAAA,EACtB,SAAS,mBAAAD,QAAU;AAAA,EACnB,YAAY,mBAAAA,QAAU;AACxB;AACA,YAAY,eAAe;AAAA,EACzB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,6BAA6B,sCAAO,IAAI,MAAM;AAAA,EAClD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,0HAA0H,eAAe,8JAA8J,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,QAAQ,WAAS,MAAM,MAAM,OAAO,OAAO,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC9a,2BAA2B,eAAe;AAAA,EACxC,OAAO;AACT;AAEA,SAAS,mBAAmB,iBAAiB;AAC3C,QAAM,wBAAwB;AAAA,IAC5B,MAAM;AAAA,IACN,KAAK;AAAA,IACL,aAAa;AAAA,IACb,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,KAAK;AAAA,IACL,WAAW;AAAA,IACX,cAAc;AAAA,IACd,OAAO;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,EAClB;AACA,SAAO,sBAAsB,eAAe;AAC9C;AACA,SAAS,sBAAsB,iBAAiB;AAC9C,QAAM,uBAAuB;AAAA,IAC3B,MAAM;AAAA,IACN,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,OAAO;AAAA,IACP,eAAe;AAAA,IACf,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,cAAc;AAAA,EAChB;AACA,QAAM,kBAAkB,mBAAmB,eAAe;AAC1D,SAAO,qBAAqB,eAAe,KAAK;AAClD;AACA,SAAS,iBAAiB,iBAAiB;AACzC,QAAM,wBAAwB;AAAA,IAC5B,KAAK;AAAA,IACL,aAAa;AAAA,IACb,WAAW;AAAA,IACX,OAAO;AAAA,IACP,eAAe;AAAA,IACf,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,cAAc;AAAA,IACd,YAAY;AAAA,EACd;AACA,SAAO,sBAAsB,eAAe,KAAK;AACnD;AACA,SAAS,gBAAgB,iBAAiB;AACxC,SAAO,kBAAkB,gBAAgB,MAAM,GAAG,EAAE,CAAC,IAAI;AAC3D;AAEA,IAAM,uBAAuB,sCAAO,IAAI,MAAM,YAAU;AAAA,EACtD,WAAW,MAAM,cAAc;AACjC,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,WAAS,WAAW,gBAAgB,MAAM,SAAS,GAAG;AAAA,EAClE,OAAO,MAAM;AAAA,EACb,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ,MAAM;AAAA,EACd,mBAAmB;AACrB,CAAC,CAAC;AACF,qBAAqB,eAAe;AAAA,EAClC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,qBAAqB,sCAAO,IAAI,MAAM,YAAU;AAAA,EACpD,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,WAAW,MAAM,cAAc;AACjC,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,YAAY,mBAAmB,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS;AAC1F,QAAM,sBAAsB,YAAY,iBAAiB,MAAM,SAAS,GAAG;AAAA,IACzE,MAAM,GAAG,MAAM,MAAM,MAAM,OAAO;AAAA,IAClC,OAAO;AAAA,IACP,mBAAmB;AAAA,EACrB,CAAC;AACD,MAAI,MAAM,YAAY;AACpB,WAAO,MAAM,YAAY,MAAM,oBAAoB,aAAa;AAAA,EAClE;AACA,SAAO,MAAM,YAAY;AAC3B,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC1D,mBAAmB,eAAe;AAAA,EAChC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,aAAa,WAAS;AAAA;AAAA,YAEhB,MAAM,MAAM,MAAM,SAAS,YAAY,MAAM,MAAM,MAAM,OAAO;AAAA,iBAC3D,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,MAAM,UAAU,EAAE;AAAA,eACpE,MAAM,MAAM,UAAU;AAAA;AAErC,IAAM,0BAA0B,sCAAO,IAAI,MAAM;AAAA,EAC/C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,mBAAmB,iBAAiB,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,MAAM,MAAM,YAAY,UAAU,WAAS,WAAW,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC9N,wBAAwB,eAAe;AAAA,EACrC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,yBAAyB,sCAAO,IAAI,MAAM;AAAA,EAC9C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,uCAAuC,mBAAmB,WAAW,eAAe,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,KAAK,WAAS,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,MAAM,UAAU,EAAE,GAAG,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC1V,uBAAuB,eAAe;AAAA,EACpC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,2BAA2B,sCAAO,IAAI,MAAM;AAAA,EAChD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,uFAAuF,OAAO,GAAG,GAAG,OAAK,EAAE,MAAM,MAAM,OAAO,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC5L,yBAAyB,eAAe;AAAA,EACtC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,+BAA+B,sCAAO,gBAAgB,EAAE,MAAM;AAAA,EAClE,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACrE,6BAA6B,eAAe;AAAA,EAC1C,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,0BAA0B,sCAAO,WAAW,EAAE,MAAM;AAAA,EACxD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,QAAQ,OAAO,KAAK,WAAW,cAAc,OAAO,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,KAAK,WAAS,MAAM,MAAM,MAAM,SAAS,SAAS,WAAS,GAAG,MAAM,MAAM,MAAM,OAAO,OAAO,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC/T,wBAAwB,eAAe;AAAA,EACrC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,eAAe;AACrB,IAAM,YAAY,WAAS;AACzB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,GAAG,MAAM,OAAO;AAChC,QAAM,aAAa,GAAG,MAAM,OAAO;AACnC,QAAM,QAAQ,SAAS,cAAc,KAAK,OAAO,IAAI;AACrD,SAAO,QAAQ,GAAG,SAAS,YAAY,KAAK;AAC9C;AACA,IAAM,oBAAoB,sCAAO,IAAI,MAAM;AAAA,EACzC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,sCAAsC,oDAAoD,gBAAgB,WAAW,8EAA8E,eAAe,2DAA2D,6KAA6K,sEAAsE,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,SAAS,SAAS,WAAW,WAAS,MAAM,MAAM,OAAO,YAAY,cAAc,WAAS,MAAM,MAAM,MAAM,QAAQ,WAAS,MAAM,MAAM,OAAO,OAAO,WAAS,MAAM,MAAM,MAAM,CAAC,eAAe,cAAc,WAAS,MAAM,MAAM,MAAM,CAAC,eAAe,cAAc,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACv2B,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AAAA,EACvB,KAAK,mBAAmB;AAAA,EACxB,MAAM;AAAA,EACN,MAAM,mBAAmB;AAC3B;AACA,IAAM,yBAAyB,sCAAO,WAAW,EAAE,MAAM;AAAA,EACvD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,SAAS,SAAS,WAAS,GAAG,MAAM,MAAM,MAAM,OAAO,iBAAiB,UAAU,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC5L,uBAAuB,eAAe;AAAA,EACpC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,0BAA0B,sCAAO,YAAY,EAAE,MAAM;AAAA,EACzD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,YAAY,OAAO,MAAM,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,wBAAwB,WAAW,MAAM,MAAM,MAAM,SAAS,YAAY,MAAM,MAAM,MAAM,QAAQ,iBAAiB,OAAO,iBAAiB,OAAO,SAAS,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC3S,wBAAwB,eAAe;AAAA,EACrC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,wBAAwB,sCAAO,UAAU,EAAE,MAAM;AAAA,EACrD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,YAAY,OAAO,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACzH,sBAAsB,eAAe;AAAA,EACnC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,0BAA0B,sCAAO,IAAI,MAAM;AAAA,EAC/C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,mEAAmE,aAAa,OAAO,GAAG,GAAG,WAAS,GAAG,MAAM,MAAM,QAAQ,MAAM,SAAS,cAAc,KAAK,MAAM,KAAK,KAAK,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC/Q,wBAAwB,eAAe;AAAA,EACrC,OAAO;AACT;AAEA,IAAM,eAAe;AACrB,IAAM,8BAA8B,sCAAO,gBAAgB,EAAE,MAAM;AAAA,EACjE,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,WAAS,wBAAwB,cAAc,KAAK,CAAC;AACnE,4BAA4B,eAAe;AAAA,EACzC,OAAO;AACT;AAEA,IAAM,oBAAgB,6BAAc,MAAS;AAC7C,IAAM,kBAAkB,MAAM;AAC5B,QAAM,cAAU,0BAAW,aAAa;AACxC,MAAI,YAAY,QAAW;AACzB,UAAM,IAAI,MAAM,8DAA8D;AAAA,EAChF;AACA,SAAO;AACT;AAEA,IAAM,gBAAgB,aAAW;AAC/B,QAAM,MAAMF,eAAc,OAAO;AACjC,QAAM,MAAM,YAAY,GAAG;AAC3B,QAAM,SAAS,WAAW,QAAQ,QAAQ,YAAY,MAAM;AAC5D,MAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ;AAC7B,WAAO,QAAQ,eAAe,QAAQ;AAAA,EACxC;AACA,QAAMI,SAAQ,IAAI,iBAAiB,IAAI,IAAI;AAC3C,QAAM,aAAa,SAASA,OAAM,iBAAiB,aAAa,GAAG,EAAE;AACrE,QAAM,cAAc,SAASA,OAAM,iBAAiB,cAAc,GAAG,EAAE;AACvE,SAAO,aAAa,IAAI,KAAK,cAAc,cAAc,IAAI;AAC/D;AACA,IAAM,YAAQ,0BAAW,CAAC,MAAM,QAAQ;AACtC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAQ,0BAAW,EAAY;AACrC,QAAM,eAAW,sBAAO,IAAI;AAC5B,QAAM,cAAc,YAAY,KAAK;AACrC,QAAM,CAAC,sBAAsB,uBAAuB,QAAI,wBAAS,KAAK;AACtE,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,SAAS;AAAA,IACX,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,kBAAgB;AAAA,IACd,OAAO;AAAA,IACP,kBAAkB;AAAA,EACpB,CAAC;AACD,+BAAU,MAAM;AACd,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,YAAY,cAAc,MAAM;AACpD,UAAM,cAAc,YAAY,cAAc,MAAM;AACpD,QAAI;AACJ,QAAI;AACJ,QAAI,aAAa;AACf,UAAI,cAAc,WAAW,GAAG;AAC9B,cAAM,kBAAkB,cAAc;AACtC,cAAM,mBAAmB,SAAS,MAAM,aAAa,cAAc,KAAK,KAAK,EAAE;AAC/E,mCAA2B,YAAY,MAAM;AAC7C,oBAAY,MAAM,eAAe,GAAG,mBAAmB;AAAA,MACzD;AACA,UAAI,aAAa;AACf,+BAAuB,YAAY,MAAM;AACzC,oBAAY,MAAM,WAAW;AAAA,MAC/B;AACA,aAAO,MAAM;AACX,YAAI,aAAa;AACf,sBAAY,MAAM,WAAW;AAAA,QAC/B;AACA,oBAAY,MAAM,eAAe;AAAA,MACnC;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,CAAC;AAChB,QAAM,eAAW,uBAAQ,MAAM;AAC7B,QAAI,cAAc;AAChB,aAAO;AAAA,IACT;AACA,QAAI,aAAa;AACf,aAAO,YAAY;AAAA,IACrB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,cAAc,WAAW,CAAC;AAC9B,QAAM,YAAQ,uBAAQ,OAAO;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,SAAS,WAAW,sBAAsB,eAAe,iBAAiB,aAAa,CAAC;AAC7F,QAAM,sBAAsB,cAAc;AAAA,IACxC,oBAAoB;AAAA,IACpB,GAAI,YAAY,CAAC,IAAI;AAAA,MACnB,mBAAmB;AAAA,IACrB;AAAA,EACF,CAAC;AACD,QAAM,YAAY,YAAY,oBAAoB;AAClD,QAAM,eAAe,YAAY,oBAAoB,iBAAiB,IAAI;AAC1E,QAAM,aAAa,YAAY,oBAAoB,iBAAiB,IAAI,WAAW,YAAY;AAC/F,QAAM,YAAY;AAAA,IAChB,CAAC,SAAS,GAAG,QAAQ,OAAO;AAAA,MAC1B,CAAC,SAAS,GAAG;AAAA,IACf,GAAG,WAAW,YAAY;AAAA,EAC5B;AACA,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,aAAO,+BAAc,cAAAC,QAAe,cAAc,cAAc,UAAU;AAAA,IACxE;AAAA,EACF,GAAG,cAAAA,QAAe,cAAc,gBAAgB,WAAW;AAAA,IACzD;AAAA,IACA;AAAA,EACF,GAAG,iBAAiB,aAAa,CAAC,GAAG,cAAAA,QAAe,cAAc,aAAa,WAAW;AAAA,IACxF;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,qBAAqB,WAAW,YAAY;AAAA,IAC7C,KAAK,6BAAU,CAAC,KAAK,QAAQ,CAAC;AAAA,EAChC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ;AAC3B,CAAC;AACD,MAAM,cAAc;AACpB,MAAM,YAAY;AAAA,EAChB,eAAe,mBAAAH,QAAU;AAAA,EACzB,SAAS,mBAAAA,QAAU;AAAA,EACnB,YAAY,mBAAAA,QAAU;AAAA,EACtB,YAAY,mBAAAA,QAAU;AAAA,EACtB,cAAc,mBAAAA,QAAU;AAAA,EACxB,cAAc,mBAAAA,QAAU;AAAA,EACxB,SAAS,mBAAAA,QAAU;AAAA,EACnB,cAAc,mBAAAA,QAAU;AAC1B;AACA,MAAM,eAAe;AAAA,EACnB,YAAY;AAAA,EACZ,YAAY;AACd;AAEA,IAAM,aAAS,0BAAW,CAAC,OAAO,QAAQ;AACxC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,SAAO,cAAAG,QAAe,cAAc,YAAY,WAAW,CAAC,GAAG,gBAAgB,KAAK,GAAG;AAAA,IACrF;AAAA,EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,cAAc;AAErB,IAAI;AACJ,SAAS,WAAW;AAAE,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,SAAS,MAAM,MAAM,SAAS;AAAG;AAClV,IAAI,aAAa,SAASC,YAAW,OAAO;AAC1C,SAA0B,qBAAc,OAAO,SAAS;AAAA,IACtD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,UAAU,QAA2B,qBAAc,QAAQ;AAAA,IACpE,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAM,cAAU,0BAAW,CAAC,OAAO,QAAQ;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB;AACpB,+BAAU,MAAM;AACd,4BAAwB,IAAI;AAC5B,WAAO,MAAM,wBAAwB,KAAK;AAAA,EAC5C,CAAC;AACD,QAAM,YAAY,QAAQ,SAAS,OAAO,cAAc,aAAa;AACrE,SAAO,cAAAD,QAAe,cAAc,aAAa,WAAW,CAAC,GAAG,cAAc;AAAA,IAC5E,GAAG;AAAA,IACH,cAAc;AAAA,EAChB,CAAC,GAAG;AAAA,IACF;AAAA,EACF,CAAC,GAAG,cAAAA,QAAe,cAAc,YAAY,IAAI,CAAC;AACpD,CAAC;AACD,QAAQ,cAAc;AAEtB,IAAM,WAAW,cAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ;AACzD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,SAAO,cAAAA,QAAe,cAAc,cAAc,WAAW;AAAA,IAC3D;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,SAAS,cAAc;AAEvB,IAAM,eAAe,cAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ,cAAAA,QAAe,cAAc,kBAAkB,WAAW;AAAA,EACvH;AACF,GAAG,KAAK,CAAC,CAAC;AACV,aAAa,cAAc;AAE3B,IAAM,eAAW,0BAAW,CAAC,MAAM,QAAQ;AACzC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB;AACpB,+BAAU,MAAM;AACd,QAAI,CAAC,aAAa,cAAc;AAC9B,mBAAa,IAAI;AAAA,IACnB;AACA,WAAO,MAAM;AACX,UAAI,aAAa,cAAc;AAC7B,qBAAa,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,WAAW,YAAY,CAAC;AAC5B,SAAO,cAAAA,QAAe,cAAc,cAAc,WAAW,CAAC,GAAG,cAAc,KAAK,GAAG;AAAA,IACrF,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,EACF,CAAC,GAAG,MAAM,YAAY,cAAAA,QAAe,cAAc,kBAAkB,IAAI,GAAG,QAAQ;AACtF,CAAC;AACD,SAAS,cAAc;AACvB,SAAS,YAAY;AAAA,EACnB,UAAU,mBAAAH,QAAU;AAAA,EACpB,KAAK,mBAAAA,QAAU;AACjB;AACA,SAAS,eAAe;AAAA,EACtB,KAAK;AACP;AAEA,IAAM,0BAAsB,6BAAc,MAAS;AACnD,IAAM,yBAAyB,MAAM;AACnC,QAAM,cAAU,0BAAW,mBAAmB;AAC9C,MAAI,YAAY,QAAW;AACzB,UAAM,IAAI,MAAM,uDAAuD;AAAA,EACzE;AACA,SAAO;AACT;AAEA,IAAM,qBAAiB,0BAAW,CAAC,MAAM,QAAQ;AAC/C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,uBAAuB;AAC3B,+BAAU,MAAM;AACd,QAAI,CAAC,YAAY,aAAa;AAC5B,kBAAY,IAAI;AAAA,IAClB;AACA,WAAO,MAAM;AACX,UAAI,YAAY,aAAa;AAC3B,oBAAY,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,WAAW,CAAC;AAC1B,SAAO,cAAAG,QAAe,cAAc,yBAAyB,WAAW,CAAC,GAAG,cAAc,KAAK,GAAG;AAAA,IAChG,IAAI;AAAA,IACJ;AAAA,EACF,CAAC,GAAG,QAAQ;AACd,CAAC;AACD,eAAe,cAAc;AAC7B,eAAe,YAAY;AAAA,EACzB,KAAK,mBAAAH,QAAU;AACjB;AACA,eAAe,eAAe;AAAA,EAC5B,KAAK;AACP;AACA,IAAM,QAAQ;AAEd,IAAM,sBAAkB,0BAAW,CAAC,OAAO,QAAQ;AACjD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,uBAAuB;AAC3B,SAAO,cAAAG,QAAe,cAAc,wBAAwB,WAAW,CAAC,GAAG,gBAAgB,KAAK,GAAG;AAAA,IACjG;AAAA,EACF,CAAC,CAAC;AACJ,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,SAAS;AAEf,IAAM,uBAAmB,0BAAW,CAAC,OAAO,QAAQ;AAClD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,uBAAuB;AAC3B,QAAM,YAAY,QAAQ,kBAAkB,OAAO,cAAc,eAAe;AAChF,SAAO,cAAAA,QAAe,cAAc,yBAAyB,WAAW,CAAC,GAAG,cAAc;AAAA,IACxF,GAAG;AAAA,IACH,cAAc;AAAA,EAChB,CAAC,GAAG;AAAA,IACF;AAAA,EACF,CAAC,GAAG,cAAAA,QAAe,cAAc,YAAY,IAAI,CAAC;AACpD,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,UAAU;AAEhB,IAAM,wBAAoB,0BAAW,CAAC,OAAO,QAAQ,cAAAA,QAAe,cAAc,0BAA0B,WAAW;AAAA,EACrH;AACF,GAAG,KAAK,CAAC,CAAC;AACV,kBAAkB,cAAc;AAChC,IAAM,WAAW;AAEjB,IAAM,4BAAwB,0BAAW,CAAC,OAAO,QAAQ,cAAAA,QAAe,cAAc,8BAA8B,WAAW;AAAA,EAC7H;AACF,GAAG,KAAK,CAAC,CAAC;AACV,sBAAsB,cAAc;AACpC,IAAM,eAAe;AAErB,IAAM,wBAAwB,cAAAA,QAAe,WAAW,CAAC,MAAM,QAAQ;AACrE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAQ,0BAAW,EAAY;AACrC,QAAM,kCAA8B,sBAAO;AAC3C,QAAM,eAAW,sBAAO,IAAI;AAC5B,QAAM,oBAAgB,sBAAO,IAAI;AACjC,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAS;AACnD,QAAM,CAAC,UAAU,WAAW,QAAI,wBAAS,KAAK;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,SAAS;AAAA,IACX,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,EAChB,CAAC;AACD,+BAAU,MAAM;AACd,QAAI,CAAC,oBAAoB,4BAA4B,WAAW,cAAc;AAC5E,kCAA4B,QAAQ,MAAM;AAAA,IAC5C;AACA,gCAA4B,UAAU;AAAA,EACxC,GAAG,CAAC,kBAAkB,YAAY,CAAC;AACnC,QAAM,sBAAkB,uBAAQ,MAAM,MAAM,MAAM,sBAAsB,SAAS,IAAI,mBAAmB,SAAS,GAAG,CAAC,WAAW,MAAM,GAAG,CAAC;AAC1I,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,UAAU,kBAAkB,eAAe;AAAA,IAC7C,WAAW;AAAA,IACX,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,QACP,QAAQ,CAAC,GAAG,MAAM,MAAM,OAAO,CAAC;AAAA,MAClC;AAAA,IACF,GAAG,GAAI,mBAAmB,CAAC,CAAE;AAAA,EAC/B,CAAC;AACD,QAAM,aAAa,cAAc;AAAA,IAC/B,oBAAoB;AAAA,IACpB,GAAI,WAAW,CAAC,IAAI;AAAA,MAClB,mBAAmB;AAAA,IACrB;AAAA,EACF,CAAC;AACD,QAAM,YAAY,WAAW,oBAAoB;AACjD,QAAM,eAAe,WAAW,WAAW,iBAAiB,IAAI;AAChE,QAAM,aAAa,WAAW,WAAW,iBAAiB,IAAI,MAAM,YAAY;AAChF,QAAM,YAAY;AAAA,IAChB,CAAC,SAAS,GAAG,QAAQ,uBAAuB;AAAA,MAC1C,CAAC,SAAS,GAAG;AAAA,IACf,GAAG,WAAW,YAAY;AAAA,EAC5B;AACA,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO,cAAAA,QAAe,cAAc,uBAAe;AAAA,IACjD,eAAe;AAAA,IACf,SAAS,aAAa,MAAM;AAAA,IAC5B,IAAI,QAAQ,gBAAgB;AAAA,IAC5B,YAAY,aAAa,oCAAoC;AAAA,IAC7D,SAAS;AAAA,EACX,GAAG,qBAAmB;AACpB,WAAO,cAAAA,QAAe,cAAc,oBAAoB,UAAU;AAAA,MAChE;AAAA,IACF,GAAG,cAAAA,QAAe,cAAc,4BAA4B,WAAW,CAAC,GAAG,iBAAiB,GAAG,eAAe;AAAA,MAC5G,KAAK;AAAA,IACP,CAAC,GAAG,cAAAA,QAAe,cAAc,sBAAsB,WAAW;AAAA,MAChE,KAAK;AAAA,MACL,OAAO,OAAO;AAAA,MACd,WAAW,QAAQ,MAAM,YAAY;AAAA,MACrC;AAAA,MACA;AAAA,IACF,GAAG,WAAW,MAAM,GAAG,cAAAA,QAAe,cAAc,oBAAoB,WAAW;AAAA,MACjF;AAAA,MACA,WAAW,QAAQ,MAAM,YAAY;AAAA,MACrC;AAAA,MACA;AAAA,IACF,GAAG,YAAY,WAAW,OAAO;AAAA,MAC/B,KAAK,6BAAU,CAAC,UAAU,GAAG,CAAC;AAAA,IAChC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,EACP,CAAC;AACH,CAAC;AACD,sBAAsB,cAAc;AACpC,sBAAsB,eAAe;AAAA,EACnC,WAAW;AAAA,EACX,UAAU;AAAA,EACV,cAAc;AAAA,EACd,cAAc;AAChB;AACA,sBAAsB,YAAY;AAAA,EAChC,kBAAkB,mBAAAH,QAAU;AAAA,EAC5B,iBAAiB,mBAAAA,QAAU;AAAA,EAC3B,WAAW,mBAAAA,QAAU;AAAA,EACrB,YAAY,mBAAAA,QAAU;AAAA,EACtB,UAAU,mBAAAA,QAAU;AAAA,EACpB,QAAQ,mBAAAA,QAAU;AAAA,EAClB,SAAS,mBAAAA,QAAU;AAAA,EACnB,eAAe,mBAAAA,QAAU;AAAA,EACzB,cAAc,mBAAAA,QAAU;AAAA,EACxB,cAAc,mBAAAA,QAAU;AAC1B;AACA,IAAM,eAAe;AACrB,aAAa,OAAO;AACpB,aAAa,QAAQ;AACrB,aAAa,SAAS;AACtB,aAAa,aAAa;AAC1B,aAAa,QAAQ;AAErB,SAASK,eAAc,KAAK;AAC1B,MAAI,QAAQ,QAAQ;AAClB,UAAMP,eAAc;AAAA,EACtB;AACA,MAAI;AACF,QAAI,SAAS,IAAI;AACjB,QAAI,CAAC,UAAU,CAAC,OAAO;AAAU,aAAO;AACxC,WAAO;AAAA,EACT,SAAS,GAAP;AACA,WAAO,IAAI;AAAA,EACb;AACF;AAEA,IAAM,sBAAkB,0BAAW,CAAC,MAAM,QAAQ;AAChD,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB;AACpB,+BAAU,MAAM;AACd,QAAI,CAAC,aAAa,cAAc;AAC9B,mBAAa,IAAI;AAAA,IACnB;AACA,WAAO,MAAM;AACX,UAAI,aAAa,cAAc;AAC7B,qBAAa,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,WAAW,YAAY,CAAC;AAC5B,SAAO,cAAAK,QAAe,cAAc,yBAAyB,WAAW,CAAC,GAAG,cAAc,KAAK,GAAG;AAAA,IAChG,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACJ,CAAC;AACD,gBAAgB,cAAc;AAC9B,gBAAgB,YAAY;AAAA,EAC1B,KAAK,mBAAAH,QAAU;AACjB;AACA,gBAAgB,eAAe;AAAA,EAC7B,KAAK;AACP;AACA,IAAM,SAAS;AAEf,IAAM,oBAAgB,0BAAW,CAAC,OAAO,QAAQ;AAC/C,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,SAAO,cAAAG,QAAe,cAAc,uBAAuB,WAAW,CAAC,GAAG,gBAAgB,KAAK,GAAG;AAAA,IAChG;AAAA,EACF,CAAC,GAAG,MAAM,QAAQ;AACpB,CAAC;AACD,cAAc,cAAc;AAC5B,IAAM,OAAO;AAEb,IAAM,qBAAiB,0BAAW,CAAC,OAAO,QAAQ;AAChD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB;AACpB,+BAAU,MAAM;AACd,4BAAwB,IAAI;AAC5B,WAAO,MAAM,wBAAwB,KAAK;AAAA,EAC5C,CAAC;AACD,QAAM,YAAY,QAAQ,gBAAgB,OAAO,cAAc,cAAc;AAC7E,SAAO,cAAAA,QAAe,cAAc,wBAAwB,WAAW,CAAC,GAAG,cAAc;AAAA,IACvF,GAAG;AAAA,IACH,cAAc;AAAA,EAChB,CAAC,GAAG;AAAA,IACF;AAAA,EACF,CAAC,GAAG,cAAAA,QAAe,cAAc,YAAY,IAAI,CAAC;AACpD,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,QAAQ;AAEd,IAAM,sBAAkB,0BAAW,CAAC,OAAO,QAAQ,cAAAA,QAAe,cAAc,yBAAyB,WAAW;AAAA,EAClH;AACF,GAAG,KAAK,CAAC,CAAC;AACV,gBAAgB,cAAc;AAC9B,IAAM,SAAS;AAEf,IAAM,0BAAsB,0BAAW,CAAC,OAAO,QAAQ,cAAAA,QAAe,cAAc,6BAA6B,WAAW;AAAA,EAC1H;AACF,GAAG,KAAK,CAAC,CAAC;AACV,oBAAoB,cAAc;AAClC,IAAM,aAAa;AAEnB,IAAM,2BAAuB,0BAAW,CAAC,MAAM,QAAQ;AACrD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,eAAW,sBAAO,IAAI;AAC5B,QAAM,oBAAgB,sBAAO,IAAI;AACjC,QAAM,iBAAa,sBAAO,IAAI;AAC9B,QAAM,YAAQ,0BAAW,EAAY;AACrC,QAAM,cAAc,YAAY,KAAK;AACrC,QAAM,CAAC,sBAAsB,uBAAuB,QAAI,wBAAS,KAAK;AACtE,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,kBAAgB;AAAA,IACd,OAAO;AAAA,IACP,kBAAkB,SAAS;AAAA,EAC7B,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,SAAS;AAAA,IACX,UAAU;AAAA,IACV;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,IACd;AAAA,IACA;AAAA,EACF,CAAC;AACD,+BAAU,MAAM;AACd,QAAI,aAAa;AACf,UAAI,UAAU,SAAS,SAAS;AAC9B,YAAI,cAAc;AAChB,qBAAW,UAAUE,eAAc,WAAW;AAAA,QAChD;AACA,YAAI,cAAc;AAChB,mBAAS,QAAQ,MAAM;AAAA,QACzB;AAAA,MACF;AACA,UAAI,CAAC,UAAU,WAAW,SAAS;AACjC,mBAAW,QAAQ,MAAM;AAAA,MAC3B;AAAA,IACF;AACA,WAAO,MAAM;AACX,UAAI,EAAE,gBAAgB,SAAS;AAC7B,mBAAW,UAAU;AAAA,MACvB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,aAAa,cAAc,cAAc,MAAM,CAAC;AACpD,+BAAU,MAAM;AACd,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,YAAY,cAAc,MAAM;AACpD,QAAI;AACJ,QAAI,eAAe,QAAQ;AACzB,6BAAuB,YAAY,MAAM;AACzC,kBAAY,MAAM,WAAW;AAAA,IAC/B;AACA,WAAO,MAAM;AACX,UAAI,eAAe,QAAQ;AACzB,oBAAY,MAAM,WAAW;AAAA,MAC/B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,aAAa,MAAM,CAAC;AACxB,QAAM,eAAW,uBAAQ,MAAM;AAC7B,QAAI,cAAc;AAChB,aAAO;AAAA,IACT;AACA,QAAI,aAAa;AACf,aAAO,YAAY;AAAA,IACrB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,cAAc,WAAW,CAAC;AAC9B,QAAM,YAAQ,uBAAQ,OAAO;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,sBAAsB,WAAW,eAAe,iBAAiB,aAAa,CAAC;AACpF,QAAM,aAAa,cAAc;AAAA,IAC/B,oBAAoB;AAAA,IACpB,GAAI,YAAY,CAAC,IAAI;AAAA,MACnB,mBAAmB;AAAA,IACrB;AAAA,EACF,CAAC;AACD,QAAM,YAAY,YAAY,oBAAoB;AAClD,QAAM,eAAe,YAAY,WAAW,iBAAiB,IAAI;AACjE,QAAM,aAAa,YAAY,WAAW,iBAAiB,IAAI,MAAM,YAAY;AACjF,QAAM,YAAY;AAAA,IAChB,CAAC,SAAS,GAAG,QAAQ,sBAAsB;AAAA,MACzC,CAAC,SAAS,GAAG;AAAA,IACf,GAAG,WAAW,YAAY;AAAA,EAC5B;AACA,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,SAAO,iBAAAC,QAAS,aAAc,cAAAH,QAAe,cAAc,cAAc,UAAU;AAAA,IACjF;AAAA,EACF,GAAG,cAAAA,QAAe,cAAc,uBAAe;AAAA,IAC7C,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,SAAS;AAAA,EACX,GAAG,cAAAA,QAAe,cAAc,gBAAgB,WAAW;AAAA,IACzD,YAAY;AAAA,EACd,GAAG,iBAAiB,aAAa,CAAC,GAAG,cAAAA,QAAe,cAAc,mBAAmB,WAAW,CAAC,GAAG,YAAY,WAAW,OAAO;AAAA,IAChI,KAAK,6BAAU,CAAC,KAAK,UAAU,aAAa,CAAC;AAAA,EAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ;AAClB,CAAC;AACD,qBAAqB,cAAc;AACnC,qBAAqB,YAAY;AAAA,EAC/B,eAAe,mBAAAH,QAAU;AAAA,EACzB,cAAc,mBAAAA,QAAU;AAAA,EACxB,cAAc,mBAAAA,QAAU;AAAA,EACxB,SAAS,mBAAAA,QAAU;AAAA,EACnB,cAAc,mBAAAA,QAAU;AAAA,EACxB,QAAQ,mBAAAA,QAAU;AACpB;AACA,qBAAqB,eAAe;AAAA,EAClC,cAAc;AAAA,EACd,cAAc;AAChB;AACA,IAAM,cAAc;AACpB,YAAY,OAAO;AACnB,YAAY,QAAQ;AACpB,YAAY,SAAS;AACrB,YAAY,aAAa;AACzB,YAAY,SAAS;AAErB,IAAM,YAAY,CAAC,QAAQ,OAAO,aAAa,WAAW,UAAU,gBAAgB,cAAc,OAAO,WAAW,cAAc,SAAS,aAAa,cAAc;", "names": ["isEqual", "React", "import_react", "import_prop_types", "import_react", "candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "isInert", "node", "lookUp", "_node$getAttribute", "inertAtt", "getAttribute", "inert", "result", "parentNode", "isContentEditable", "_node$getAttribute2", "attValue", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "content", "children", "nestedCandidates", "flatten", "push", "scopeParent", "validCandidate", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getTabindex", "isScope", "tabIndex", "test", "isNaN", "parseInt", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isHiddenInput", "type", "isDetailsWithSummary", "r", "some", "child", "getCheckedRadio", "nodes", "form", "i", "checked", "isTabbableRadio", "name", "radioScope", "queryRadios", "radioSet", "window", "CSS", "escape", "err", "console", "error", "message", "isRadio", "isNonTabbableRadio", "isNodeAttached", "_nodeRoot", "nodeRoot", "nodeRootHost", "host", "attached", "_nodeRootHost", "_nodeRootHost$ownerDo", "_node$ownerDocument", "contains", "_nodeRoot2", "_nodeRootHost2", "_nodeRootHost2$ownerD", "isZeroArea", "_node$getBoundingClie", "getBoundingClientRect", "width", "height", "isHidden", "_ref", "displayCheck", "getComputedStyle", "visibility", "isDirectSummary", "nodeUnderDetails", "parentElement", "originalNode", "rootNode", "assignedSlot", "getClientRects", "isDisabledFromFieldset", "disabled", "item", "isNodeMatchingSelectorFocusable", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidateTabindex", "sort", "reduce", "acc", "sortable", "concat", "tabbable", "bind", "focusableCandidateSelector", "candidateSelectors", "concat", "join", "React", "PropTypes", "import_prop_types", "React", "PropTypes", "React", "React", "React", "fromEntries", "React", "ownerDocument", "style", "name", "getComputedStyle", "getComputedStyle", "window", "min", "max", "toPaddingObject", "popperOffsets", "min", "max", "offset", "effect", "popper", "getComputedStyle", "effect", "window", "hash", "getComputedStyle", "getComputedStyle", "clippingParents", "getComputedStyle", "reference", "popperOffsets", "offset", "placements", "placement", "placements", "placement", "_loop", "_i", "checks", "offset", "popperOffsets", "offset", "min", "max", "fn", "fn", "merged", "defaultModifiers", "createPopper", "reference", "popper", "options", "getComputedStyle", "fn", "state", "effect", "noopFn", "createPopper", "defaultModifiers", "createPopper", "usePopper", "fn", "state", "isEqual", "createPopper", "React", "ownerDocument", "getComputedStyle", "PropTypes", "SvgAlertErrorStroke", "style", "React__default", "SvgXStroke", "activeElement", "ReactDOM"]}