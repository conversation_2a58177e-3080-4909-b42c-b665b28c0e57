import {
  useAccordion
} from "./chunk-NT5RQ2AN.js";
import {
  react_merge_refs_esm_default
} from "./chunk-JLWORYXM.js";
import {
  useUIDSeed
} from "./chunk-JQPVIOLG.js";
import {
  DEFAULT_THEME,
  PALETTE,
  getColor,
  getLineHeight,
  math,
  readableColor,
  retrieveComponentStyles,
  rgba,
  useDocument
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import {
  getControlledValue
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  A<PERSON>,
  Me,
  We,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-chrome/dist/index.esm.js
var React = __toESM(require_react());
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
function _extends$3() {
  _extends$3 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$3.apply(this, arguments);
}
var ChromeContext = import_react.default.createContext({
  hue: "chromeHue"
});
var useChromeContext = () => {
  return (0, import_react.useContext)(ChromeContext);
};
var COMPONENT_ID$A = "chrome.chrome";
var StyledChrome = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$A,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledChrome",
  componentId: "sc-1uqm6u6-0"
})(["display:flex;position:relative;margin:0;height:100vh;overflow-y:hidden;font-family:", ";direction:", ";", ";"], (props) => props.theme.fonts.system, (props) => props.theme.rtl && "rtl", (props) => retrieveComponentStyles(COMPONENT_ID$A, props));
StyledChrome.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$z = "chrome.header_item_icon";
var StyledHeaderItemIcon = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$z,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledHeaderItemIcon",
  componentId: "sc-1jhhp6z-0"
})(["transition:transform 0.25s ease-in-out;margin:0 3px;width:", ";min-width:", ";height:", ";", ";"], (props) => props.theme.iconSizes.md, (props) => props.theme.iconSizes.md, (props) => props.theme.iconSizes.md, (props) => retrieveComponentStyles(COMPONENT_ID$z, props));
StyledHeaderItemIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$y = "chrome.base_header_item";
var getHeaderItemSize = (props) => `${props.theme.space.base * 7.5}px`;
var sizeStyles$4 = (props) => {
  const size = props.theme.space.base * 7.5;
  return Ae(["padding:0 3px;min-width:", "px;height:", ";line-height:", ";"], size, props.maxY ? "100%" : `${size}px`, getLineHeight(size, props.theme.fontSizes.md));
};
var StyledBaseHeaderItem = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$y,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledBaseHeaderItem",
  componentId: "sc-1qua7h6-0"
})(["display:inline-flex;position:relative;flex:", ";align-items:center;justify-content:", ";order:1;transition:box-shadow 0.1s ease-in-out,color 0.1s ease-in-out;z-index:0;margin:", ";border:none;border-radius:", ";background:transparent;text-decoration:none;white-space:nowrap;color:inherit;font-size:inherit;", " ", ";"], (props) => props.maxX && "1", (props) => props.maxX ? "start" : "center", (props) => `0 ${props.theme.space.base * 3}px`, (props) => {
  if (props.isRound) {
    return "100%";
  }
  if (props.maxY) {
    return "0";
  }
  return props.theme.borderRadii.md;
}, sizeStyles$4, (props) => retrieveComponentStyles(COMPONENT_ID$y, props));
StyledBaseHeaderItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$x = "chrome.header_item_text";
var clippedStyling = Ae(["position:absolute;margin:0;clip:rect(1px,1px,1px,1px);width:1px;height:1px;overflow:hidden;white-space:nowrap;"]);
var StyledHeaderItemText = styled_components_browser_esm_default.span.attrs({
  "data-garden-id": COMPONENT_ID$x,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledHeaderItemText",
  componentId: "sc-goz7la-0"
})(["margin:0 3px;", " ", ";"], (props) => props.isClipped && clippedStyling, (props) => retrieveComponentStyles(COMPONENT_ID$x, props));
StyledHeaderItemText.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$w = "chrome.nav";
var colorStyles$6 = (props) => {
  const shade = props.isDark || props.isLight ? 600 : 700;
  const backgroundColor = getColor(props.hue, shade, props.theme);
  const foregroundColor = props.isLight ? props.theme.palette.grey[800] : props.theme.palette.white;
  return Ae(["background-color:", ";color:", ";"], backgroundColor, foregroundColor);
};
var getNavWidth = (props) => {
  return `${props.theme.space.base * 15}px`;
};
var getExpandedNavWidth = () => {
  return `200px`;
};
var StyledNav = styled_components_browser_esm_default.nav.attrs({
  "data-garden-id": COMPONENT_ID$w,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledNav",
  componentId: "sc-6j462r-0"
})(["display:flex;position:relative;flex-direction:column;flex-shrink:0;order:-1;width:", ";font-size:", ";", ";", ";"], (props) => props.isExpanded ? getExpandedNavWidth : getNavWidth, (props) => props.theme.fontSizes.md, (props) => colorStyles$6(props), (props) => retrieveComponentStyles(COMPONENT_ID$w, props));
StyledNav.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$v = "chrome.header_item";
var retrieveProductColor$1 = (props) => {
  switch (props.product) {
    case "chat":
      return PALETTE.product.chat;
    case "connect":
      return PALETTE.product.connect;
    case "explore":
      return PALETTE.product.explore;
    case "guide":
      return PALETTE.product.guide;
    case "message":
      return PALETTE.product.message;
    case "support":
      return PALETTE.product.support;
    case "talk":
      return PALETTE.product.talk;
    default:
      return "inherit";
  }
};
var StyledLogoHeaderItem = styled_components_browser_esm_default(StyledBaseHeaderItem).attrs({
  "data-garden-id": COMPONENT_ID$v,
  "data-garden-version": "8.67.0",
  as: "div"
}).withConfig({
  displayName: "StyledLogoHeaderItem",
  componentId: "sc-1n1d1yv-0"
})(["display:none;order:0;margin-right:", ";margin-left:", ";border-", ":", ";border-radius:0;padding:0;width:", ";height:100%;overflow:hidden;fill:", ";text-decoration:none;color:", ";", "{", "}", "{margin:0;width:", ";height:", ";}", ";"], (props) => props.theme.rtl ? `-${props.theme.space.base}px` : "auto", (props) => props.theme.rtl ? "auto" : `-${props.theme.space.base}px`, (props) => props.theme.rtl ? "left" : "right", (props) => `${props.theme.borders.sm} ${getColor("neutralHue", 300, props.theme)}`, (props) => getNavWidth(props), (props) => getColor("chromeHue", 700, props.theme), (props) => retrieveProductColor$1(props), StyledHeaderItemText, clippedStyling, StyledHeaderItemIcon, (props) => props.theme.iconSizes.lg, (props) => props.theme.iconSizes.lg, (props) => retrieveComponentStyles(COMPONENT_ID$v, props));
StyledLogoHeaderItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$u = "chrome.base_nav_item";
var getNavItemHeight = (props) => {
  return `${props.theme.space.base * 13}px`;
};
var sizeStyles$3 = (props) => {
  const verticalPadding = math(`(${getNavItemHeight(props)} - ${props.theme.iconSizes.lg}) / 2`);
  const horizontalPadding = math(`(${getNavWidth(props)} - ${props.theme.iconSizes.lg}) / 4`);
  return Ae(["padding:", " ", ";min-height:", ";"], verticalPadding, horizontalPadding, getNavItemHeight);
};
var StyledBaseNavItem = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$u,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledBaseNavItem",
  componentId: "sc-zvo43f-0"
})(["display:flex;flex-shrink:0;align-items:center;justify-content:center;transition:box-shadow 0.1s ease-in-out,background-color 0.1s ease-in-out,opacity 0.1s ease-in-out;border:none;box-sizing:border-box;background:transparent;text-decoration:none;color:inherit;font-size:inherit;", ""], (props) => sizeStyles$3(props));
StyledBaseNavItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$t = "chrome.header";
var getHeaderHeight = (props) => {
  return getNavItemHeight(props);
};
var StyledHeader = styled_components_browser_esm_default.header.attrs({
  "data-garden-id": COMPONENT_ID$t,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledHeader",
  componentId: "sc-1cexpz-0"
})(["display:flex;position:", ";align-items:center;justify-content:flex-end;box-sizing:border-box;border-bottom:", ";box-shadow:", ";background-color:", ";padding:0 ", "px;height:", ";color:", ";font-size:", ";", " ", ";"], (props) => props.isStandalone && "relative", (props) => `${props.theme.borders.sm} ${getColor("neutralHue", 300, props.theme)}`, (props) => props.isStandalone && props.theme.shadows.lg("0", "10px", getColor("chromeHue", 600, props.theme, 0.15)), (props) => props.theme.colors.background, (props) => props.theme.space.base, getHeaderHeight, (props) => getColor("neutralHue", 600, props.theme), (props) => props.theme.fontSizes.md, (props) => props.isStandalone && `
    ${StyledLogoHeaderItem} {
      display: inline-flex;
    }
  `, (props) => retrieveComponentStyles(COMPONENT_ID$t, props));
StyledHeader.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$s = "chrome.skipnav";
var animationStyles = () => {
  const animationName = We(["0%{transform:translate(-50%,-50%);}"]);
  return Ae(["transition:opacity 0.2s ease-out,clip 0s linear 0.2s;opacity:0;clip:rect(0,0,0,0);&:focus{transition:opacity 0.2s ease-in-out;animation:0.2s cubic-bezier(0.15,0.85,0.35,1.2) ", ";opacity:1;clip:rect(0,150vw,100vh,-50vw);}"], animationName);
};
var colorStyles$5 = (theme) => {
  const color = getColor("primaryHue", 600, theme);
  const borderColor = getColor("neutralHue", 300, theme);
  const boxShadow = theme.shadows.lg(`${theme.space.base * 5}px`, `${theme.space.base * 7}px`, getColor("chromeHue", 600, theme, 0.15));
  return Ae(["border-color:", ";box-shadow:", ";background-color:", ";color:", ";&:hover,&:focus{color:", ";}"], borderColor, boxShadow, theme.colors.background, color, color);
};
var sizeStyles$2 = (props) => {
  const top = math(`${getHeaderHeight(props)} / 2`);
  const padding = `${props.theme.space.base * 5}px`;
  const paddingStart = `${props.theme.space.base * 4}px`;
  const fontSize = props.theme.fontSizes.md;
  const lineHeight = getLineHeight(padding, fontSize);
  return Ae(["top:", ";padding:", ";padding-", ":", ";line-height:", ";font-size:", ";"], top, padding, props.theme.rtl ? "right" : "left", paddingStart, lineHeight, fontSize);
};
var StyledSkipNav = styled_components_browser_esm_default.a.attrs({
  "data-garden-id": COMPONENT_ID$s,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSkipNav",
  componentId: "sc-1tsro34-0"
})(["", ";display:inline-flex;position:absolute;left:50%;align-items:center;justify-content:center;transform:translateX(-50%);direction:", ";z-index:", ";border:", ";border-radius:", ";text-decoration:underline;white-space:nowrap;", ";&:focus{outline:none;}", ";", ";"], animationStyles(), (props) => props.theme.rtl && "rtl", (props) => props.zIndex, (props) => props.theme.borders.sm, (props) => props.theme.borderRadii.md, (props) => sizeStyles$2(props), (props) => colorStyles$5(props.theme), (props) => retrieveComponentStyles(COMPONENT_ID$s, props));
StyledSkipNav.defaultProps = {
  theme: DEFAULT_THEME
};
var _path$2;
function _extends$2() {
  _extends$2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$2.apply(this, arguments);
}
var SvgLinkStroke = function SvgLinkStroke2(props) {
  return React.createElement("svg", _extends$2({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$2 || (_path$2 = React.createElement("path", {
    fill: "currentColor",
    d: "M4.441 7.38l.095.083.939.939-.708.707-.939-.939-2 2-.132.142a2.829 2.829 0 003.99 3.99l.142-.132 2-2-.939-.939.707-.708.94.94a1 1 0 01.083 1.32l-.083.094-2 2A3.828 3.828 0 01.972 9.621l.15-.158 2-2A1 1 0 014.34 7.31l.101.07zm7.413-3.234a.5.5 0 01.057.638l-.057.07-7 7a.5.5 0 01-.765-.638l.057-.07 7-7a.5.5 0 01.708 0zm3.023-3.025a3.829 3.829 0 01.15 5.257l-.15.158-2 2a1 1 0 01-1.32.083l-.094-.083-.94-.94.708-.707.939.94 2-2 .132-.142a2.829 2.829 0 00-3.99-3.99l-.142.131-2 2 .939.939-.707.708-.94-.94a1 1 0 01-.082-1.32l.083-.094 2-2a3.828 3.828 0 015.414 0z"
  })));
};
var COMPONENT_ID$r = "chrome.skipnav_icon";
var sizeStyles$1 = (theme) => {
  const margin = `${theme.space.base * 2}px`;
  const size = theme.iconSizes.md;
  return Ae(["margin-", ":", ";width:", ";height:", ";"], theme.rtl ? "left" : "right", margin, size, size);
};
var StyledSkipNavIcon = styled_components_browser_esm_default(SvgLinkStroke).attrs({
  "data-garden-id": COMPONENT_ID$r,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSkipNavIcon",
  componentId: "sc-1ika5z4-0"
})(["transform:", ";color:", ";", ";", ";"], (props) => props.theme.rtl && "scaleX(-1)", (props) => getColor("neutralHue", 600, props.theme), (props) => sizeStyles$1(props.theme), (props) => retrieveComponentStyles(COMPONENT_ID$r, props));
StyledSkipNavIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$q = "chrome.body";
var StyledBody = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$q,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledBody",
  componentId: "sc-c7h9kv-0"
})(["flex:1;order:1;background-color:", ";min-width:0;", ";"], (props) => getColor("neutralHue", 100, props.theme), (props) => retrieveComponentStyles(COMPONENT_ID$q, props));
StyledBody.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$p = "chrome.footer";
var getFooterHeight = (props) => {
  return `${props.theme.space.base * 20}px`;
};
var StyledFooter = styled_components_browser_esm_default.footer.attrs({
  "data-garden-id": COMPONENT_ID$p,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledFooter",
  componentId: "sc-v7lib2-0"
})(["display:flex;align-items:center;justify-content:flex-end;box-sizing:border-box;border-top:", ";background-color:", ";padding:0 ", "px;height:", ";", ";"], (props) => `${props.theme.borders.sm} ${getColor("neutralHue", 300, props.theme)}`, (props) => props.theme.colors.background, (props) => props.theme.space.base * 9, getFooterHeight, (props) => retrieveComponentStyles(COMPONENT_ID$p, props));
StyledFooter.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$o = "chrome.content";
var StyledContent = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$o,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledContent",
  componentId: "sc-qcuzxn-0"
})(["display:flex;height:", ";line-height:", ";color:", ";font-size:", ";&:focus{outline:none;}", ";"], (props) => props.hasFooter ? `calc(100% - ${math(`${getHeaderHeight(props)} + ${getFooterHeight(props)}`)})` : `calc(100% - ${getHeaderHeight(props)})`, (props) => getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md), (props) => props.theme.colors.foreground, (props) => props.theme.fontSizes.md, (props) => retrieveComponentStyles(COMPONENT_ID$o, props));
StyledContent.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$n = "chrome.main";
var StyledMain = styled_components_browser_esm_default.main.attrs({
  "data-garden-id": COMPONENT_ID$n,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledMain",
  componentId: "sc-t61cre-0"
})(["flex:1;order:1;background-color:", ";overflow:auto;:focus{outline:none;}", ";"], (props) => props.theme.colors.background, (props) => retrieveComponentStyles(COMPONENT_ID$n, props));
StyledMain.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$m = "chrome.sidebar";
var StyledSidebar = styled_components_browser_esm_default.aside.attrs({
  "data-garden-id": COMPONENT_ID$m,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSidebar",
  componentId: "sc-1q77fuw-0"
})(["flex-shrink:0;order:0;box-sizing:border-box;border-", ":", ";width:330px;overflow:auto;&:focus{outline:none;}", ";"], (props) => props.theme.rtl ? "left" : "right", (props) => `${props.theme.borders.sm} ${getColor("neutralHue", 300, props.theme)}`, (props) => retrieveComponentStyles(COMPONENT_ID$m, props));
StyledSidebar.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$l = "chrome.footer_item";
var StyledFooterItem = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$l,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledFooterItem",
  componentId: "sc-1cktm85-0"
})(["margin:", ";", ";"], (props) => `0 ${props.theme.space.base}px`, (props) => retrieveComponentStyles(COMPONENT_ID$l, props));
StyledFooterItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$k = "chrome.header_item";
var imgStyles = (props) => {
  const size = math(`${getHeaderItemSize(props)} - ${props.theme.space.base * 2}`);
  return Ae(["img{margin:0;border-radius:", ";width:", ";height:", ";}"], math(`${props.theme.borderRadii.md} - 1`), size, size);
};
var StyledHeaderItem = styled_components_browser_esm_default(StyledBaseHeaderItem).attrs({
  "data-garden-id": COMPONENT_ID$k,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledHeaderItem",
  componentId: "sc-14sft6n-0"
})(["&:hover,&:focus{text-decoration:none;color:inherit;}&:focus{outline:none;}&[data-garden-focus-visible]{box-shadow:", ";}&[data-garden-focus-visible]:active{box-shadow:none;}&:hover ", ",&:hover ", ",&:active ", ",&:active ", "{color:", ";}", " ", " ", " ", ";"], (props) => props.theme.shadows.md(getColor("chromeHue", 400, props.theme, 0.35)), StyledHeaderItemIcon, StyledHeaderItemText, StyledHeaderItemIcon, StyledHeaderItemText, (props) => getColor("chromeHue", 700, props.theme), (props) => props.maxY && `
      &[data-garden-focus-visible] {
        box-shadow: inset ${props.theme.shadows.lg(props.theme.shadowWidths.md, "0", getColor("chromeHue", 400, props.theme, 0.35))},
        ${props.theme.shadowWidths.md} 0 0 0 ${getColor("chromeHue", 400, props.theme, 0.35)},
        inset ${props.theme.shadows.lg(`-${props.theme.shadowWidths.md}`, "0", getColor("chromeHue", 400, props.theme, 0.35))},
        -${props.theme.shadowWidths.md} 0 0 0 ${getColor("chromeHue", 400, props.theme, 0.35)};
      }
  `, imgStyles, (props) => props.isRound && `
    ${StyledHeaderItemIcon} {
      border-radius: 100px;
    }
  `, (props) => retrieveComponentStyles(COMPONENT_ID$k, props));
StyledHeaderItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$j = "chrome.header_item_wrapper";
var StyledHeaderItemWrapper = styled_components_browser_esm_default(StyledBaseHeaderItem).attrs({
  "data-garden-id": COMPONENT_ID$j,
  "data-garden-version": "8.67.0",
  as: "div"
}).withConfig({
  displayName: "StyledHeaderItemWrapper",
  componentId: "sc-1uieu55-0"
})(["", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$j, props));
StyledHeaderItemWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$i = "chrome.logo_nav_item";
var retrieveProductColor = (product) => {
  switch (product) {
    case "chat":
      return PALETTE.product.chat;
    case "connect":
      return PALETTE.product.connect;
    case "explore":
      return PALETTE.product.explore;
    case "guide":
      return PALETTE.product.guide;
    case "message":
      return PALETTE.product.message;
    case "support":
      return PALETTE.product.support;
    case "talk":
      return PALETTE.product.talk;
    default:
      return "inherit";
  }
};
var colorStyles$4 = (props) => {
  const fillColor = props.isLight ? props.theme.palette.grey[800] : props.theme.palette.white;
  const color = props.isLight || props.isDark ? fillColor : retrieveProductColor(props.product);
  return Ae(["color:", ";fill:", ";"], color, fillColor);
};
var StyledLogoNavItem = styled_components_browser_esm_default(StyledBaseNavItem).attrs({
  "data-garden-id": COMPONENT_ID$i,
  "data-garden-version": "8.67.0",
  as: "div"
}).withConfig({
  displayName: "StyledLogoNavItem",
  componentId: "sc-saaydx-0"
})(["order:0;opacity:1;cursor:default;", ";"], (props) => colorStyles$4(props));
StyledLogoNavItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$h = "chrome.brandmark_nav_item";
var StyledBrandmarkNavItem = styled_components_browser_esm_default(StyledBaseNavItem).attrs({
  "data-garden-id": COMPONENT_ID$h,
  "data-garden-version": "8.67.0",
  as: "div"
}).withConfig({
  displayName: "StyledBrandmarkNavItem",
  componentId: "sc-8kynd4-0"
})(["order:1;opacity:0.3;margin-top:auto;"]);
StyledBrandmarkNavItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$g = "chrome.nav_item_icon";
var StyledNavItemIcon = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$g,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledNavItemIcon",
  componentId: "sc-7w9rpt-0"
})(["align-self:flex-start;order:0;border-radius:", ";width:", ";height:", ";", ";"], (props) => props.theme.borderRadii.md, (props) => props.theme.iconSizes.lg, (props) => props.theme.iconSizes.lg, (props) => retrieveComponentStyles(COMPONENT_ID$g, props));
StyledNavItemIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$f = "chrome.nav_item";
var colorStyles$3 = (props) => {
  const BLACK = props.theme.palette.black;
  const WHITE = props.theme.palette.white;
  let currentColor;
  let hoverColor;
  if (props.isCurrent) {
    if (props.isLight) {
      currentColor = rgba(BLACK, 0.3);
    } else if (props.isDark) {
      currentColor = rgba(WHITE, 0.3);
    } else {
      currentColor = getColor(props.hue, 500, props.theme);
    }
  } else {
    hoverColor = rgba(props.isLight ? WHITE : BLACK, 0.1);
  }
  const activeColor = rgba(props.isLight ? BLACK : WHITE, 0.1);
  const focusColor = rgba(props.isLight ? BLACK : WHITE, 0.2);
  return Ae(["opacity:", ";background-color:", ";&:hover{opacity:1;background-color:", ";}&[data-garden-focus-visible]{opacity:1;box-shadow:inset ", ";}&:active{background-color:", ";}"], props.isCurrent ? 1 : 0.6, currentColor, hoverColor, props.theme.shadows.md(focusColor), activeColor);
};
var StyledNavItem = styled_components_browser_esm_default(StyledBaseNavItem).attrs({
  "data-garden-id": COMPONENT_ID$f,
  "data-garden-version": "8.67.0",
  as: "button"
}).withConfig({
  displayName: "StyledNavItem",
  componentId: "sc-gs8mjz-0"
})(["justify-content:", ";order:1;margin:0;cursor:", ";text-align:", ";&:hover,&:focus{text-decoration:none;color:inherit;}&:focus{outline:none;}", ";", " ", ";"], (props) => props.isExpanded && "start", (props) => props.isCurrent ? "default" : "pointer", (props) => props.isExpanded && "inherit", (props) => colorStyles$3(props), (props) => props.isExpanded && `
    ${StyledNavItemIcon} {
      margin: 0 ${math(`(${getNavWidth(props)} - ${props.theme.iconSizes.lg}) / 4`)};
    }
  `, (props) => retrieveComponentStyles(COMPONENT_ID$f, props));
StyledNavItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$e = "chrome.nav_item_text";
var StyledNavItemText = styled_components_browser_esm_default.span.attrs({
  "data-garden-id": COMPONENT_ID$e,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledNavItemText",
  componentId: "sc-13m84xl-0"
})(["position:absolute;order:1;clip:rect(1px,1px,1px,1px);margin:", ";width:1px;height:1px;overflow:hidden;line-height:", ";white-space:", ";", " ", ";"], (props) => props.isExpanded && `0 ${math(`(${getNavWidth(props)} - ${props.theme.iconSizes.lg}) / 4`)}`, (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.isWrapped ? "normal" : "nowrap", (props) => props.isExpanded && `
    ${StyledNavItem} > && {
      position: static;
      flex: 1;
      clip: auto;
      width: auto;
      height: auto;
      text-overflow: ellipsis;
    }
  `, (props) => retrieveComponentStyles(COMPONENT_ID$e, props));
StyledNavItemText.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$d = "chrome.subnav_item";
var colorStyles$2 = (props) => {
  const BLACK = props.theme.palette.black;
  const WHITE = props.theme.palette.white;
  let currentColor;
  let hoverColor;
  if (props.isCurrent) {
    if (props.isLight) {
      currentColor = rgba(BLACK, 0.1);
    } else {
      currentColor = rgba(WHITE, 0.1);
    }
  } else {
    hoverColor = rgba(props.isLight ? WHITE : BLACK, 0.1);
  }
  const activeColor = rgba(props.isLight ? BLACK : WHITE, 0.03);
  const focusColor = rgba(props.isLight ? BLACK : WHITE, 0.2);
  return Ae(["opacity:", ";background-color:", ";&:hover{opacity:1;background-color:", ";}&[data-garden-focus-visible]{opacity:1;box-shadow:", ";}&:not([data-garden-header='true']):active{background-color:", ";}"], props.isCurrent ? "1" : "0.6", currentColor, hoverColor, props.theme.shadows.md(focusColor), activeColor);
};
var getSubNavItemHeight = (props) => {
  return `${props.theme.space.base * 7.5}px`;
};
var StyledSubNavItem = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$d,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSubNavItem",
  componentId: "sc-1yg9dpx-0"
})(["display:flex;align-items:center;transition:box-shadow 0.1s ease-in-out,background-color 0.1s ease-in-out,opacity 0.1s ease-in-out;margin:", "px 0 0;border:none;border-radius:", ";box-sizing:border-box;background:transparent;cursor:", ";padding:", ";width:100%;min-height:", ";text-align:inherit;font-size:inherit;&,&:hover,&:focus{text-decoration:none;color:inherit;}&:focus{outline:none;}", ";", ";"], (props) => props.theme.space.base * 2, (props) => props.theme.borderRadii.md, (props) => props.isCurrent ? "default" : "pointer", (props) => `0 ${props.theme.space.base * 2}px`, getSubNavItemHeight, (props) => colorStyles$2(props), (props) => retrieveComponentStyles(COMPONENT_ID$d, props));
StyledSubNavItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$c = "chrome.subnav";
var colorStyles$1 = (props) => {
  let shade;
  if (props.isLight) {
    shade = 500;
  } else {
    shade = props.isDark ? 700 : 800;
  }
  const backgroundColor = getColor(props.hue, shade, props.theme);
  const foregroundColor = props.isLight ? props.theme.palette.grey[800] : props.theme.palette.white;
  return Ae(["background-color:", ";color:", ";"], backgroundColor, foregroundColor);
};
var StyledSubNav = styled_components_browser_esm_default.nav.attrs({
  "data-garden-id": COMPONENT_ID$c,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSubNav",
  componentId: "sc-19hjou6-0"
})(["flex-direction:column;order:0;padding:", ";min-width:220px;overflow:auto;font-size:", ";", ";& > ", ":first-child{margin-top:0;}", ";"], (props) => `${props.theme.space.base * 6}px ${props.theme.space.base * 5}px`, (props) => props.theme.fontSizes.md, (props) => colorStyles$1(props), StyledSubNavItem, (props) => retrieveComponentStyles("chrome.subnav", props));
StyledSubNav.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$b = "chrome.subnav_item_text";
var sizeStyles = (props) => {
  const baseLineHeight = props.theme.space.base * 5;
  const verticalMargin = math(`(${getSubNavItemHeight(props)} - ${baseLineHeight}) / 2`);
  const lineHeight = getLineHeight(baseLineHeight, props.theme.fontSizes.md);
  return Ae(["margin:", " 0;line-height:", ";"], verticalMargin, lineHeight);
};
var StyledSubNavItemText = styled_components_browser_esm_default.span.attrs({
  "data-garden-id": COMPONENT_ID$b,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSubNavItemText",
  componentId: "sc-1hy0pn7-0"
})(["overflow:hidden;text-overflow:ellipsis;white-space:", ";", " ", ";"], (props) => props.isWrapped ? "normal" : "nowrap", (props) => sizeStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID$b, props));
StyledSubNavItemText.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$a = "chrome.collapsible_sub_nav_item";
var StyledSubNavItemHeader = styled_components_browser_esm_default(StyledSubNavItem).attrs({
  "data-garden-id": COMPONENT_ID$a,
  "data-garden-version": "8.67.0",
  "data-garden-header": "true"
}).withConfig({
  displayName: "StyledSubNavItemHeader",
  componentId: "sc-1vniter-0"
})(["position:relative;padding-", ":", "px;", ";"], (props) => props.theme.rtl ? "left" : "right", (props) => props.theme.space.base * 7, (props) => retrieveComponentStyles(COMPONENT_ID$a, props));
StyledSubNavItemHeader.defaultProps = {
  theme: DEFAULT_THEME
};
var _path$1;
function _extends$1() {
  _extends$1 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$1.apply(this, arguments);
}
var SvgChevronDownStroke = function SvgChevronDownStroke2(props) {
  return React.createElement("svg", _extends$1({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$1 || (_path$1 = React.createElement("path", {
    fill: "currentColor",
    d: "M1.646 3.646a.5.5 0 01.638-.057l.07.057L6 7.293l3.646-3.647a.5.5 0 01.638-.057l.07.057a.5.5 0 01.057.638l-.057.07-4 4a.5.5 0 01-.638.057l-.07-.057-4-4a.5.5 0 010-.708z"
  })));
};
var COMPONENT_ID$9 = "chrome.collapsible_sub_nav_item_icon";
var FilteredChevronDownStrokeIcon = import_react.default.forwardRef((_ref, ref) => {
  let {
    theme,
    isExpanded,
    ...validProps
  } = _ref;
  return import_react.default.createElement(SvgChevronDownStroke, _extends$3({
    ref
  }, validProps));
});
FilteredChevronDownStrokeIcon.displayName = "FilteredChevronDownStrokeIcon";
var StyledSubNavItemIcon = styled_components_browser_esm_default(FilteredChevronDownStrokeIcon).withConfig({
  displayName: "StyledSubNavItemIcon",
  componentId: "sc-1d02hho-0"
})(["width:", ";height:", ";"], (props) => props.theme.iconSizes.sm, (props) => props.theme.iconSizes.sm);
StyledSubNavItemIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledSubNavItemIconWrapper = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$9,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSubNavItemIcon__StyledSubNavItemIconWrapper",
  componentId: "sc-1d02hho-1"
})(["display:flex;position:absolute;top:0;right:", ";left:", ";align-items:center;justify-content:center;width:", "px;height:", ";", "{transform:", ";transition:transform 0.25s ease-in-out;}", ";"], (props) => props.theme.rtl ? "auto" : 0, (props) => props.theme.rtl && 0, (props) => props.theme.space.base * 7, getSubNavItemHeight, StyledSubNavItemIcon, (props) => {
  if (props.isExpanded) {
    return Ae(["rotate(", "180deg)"], props.theme.rtl && "-");
  }
  return void 0;
}, (props) => retrieveComponentStyles(COMPONENT_ID$9, props));
StyledSubNavItemIconWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var PANEL_COMPONENT_ID = "chrome.collapsible_sub_nav_item_panel";
var hiddenStyling = Ae(["visibility:hidden;max-height:0 !important;overflow:hidden;"]);
var StyledSubNavPanel = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": PANEL_COMPONENT_ID,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSubNavPanel",
  componentId: "sc-1jv3rpv-0"
})(["transition:max-height 0.25s cubic-bezier(0.15,0.85,0.35,1.2),0.25s visibility 0s linear;height:auto;max-height:100%;", " ", "{padding-", ":", ";}", ";"], (props) => props.isHidden && hiddenStyling, StyledSubNavItem, (props) => props.theme.rtl ? "right" : "left", (props) => `${props.theme.space.base * 5}px`, (props) => retrieveComponentStyles(PANEL_COMPONENT_ID, props));
StyledSubNavPanel.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$8 = "chrome.sheet";
var borderStyle = (_ref) => {
  let {
    theme,
    placement,
    isOpen
  } = _ref;
  const borderColor = isOpen ? getColor("neutralHue", 300, theme) : "transparent";
  const borderSides = ["-left", "-right"];
  let borderSide = "";
  if (theme.rtl) {
    borderSides.reverse();
  }
  if (placement === "end") {
    borderSide = borderSides[0];
  } else if (placement === "start") {
    borderSide = borderSides[1];
  }
  return `border${borderSide}: ${theme.borders.sm} ${borderColor};`;
};
var StyledSheet = styled_components_browser_esm_default.aside.attrs({
  "data-garden-id": COMPONENT_ID$8,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSheet",
  componentId: "sc-dx8ijk-0"
})(["display:flex;order:1;transition:", ";background-color:", ";width:", ";height:100%;overflow:hidden;font-size:", ";&:focus{outline:none;}", ";", ";"], (props) => props.isAnimated && "width 250ms ease-in-out", (props) => props.theme.colors.background, (props) => props.isOpen ? props.size : "0px", (props) => props.theme.fontSizes.md, (props) => borderStyle(props), (props) => retrieveComponentStyles(COMPONENT_ID$8, props));
StyledSheet.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$7 = "chrome.sheet_wrapper";
var StyledSheetWrapper = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$7,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSheetWrapper",
  componentId: "sc-f6x9zb-0"
})(["display:flex;position:relative;flex-direction:column;transform:", ";transition:", ";min-width:", ";", ";"], (props) => {
  const translateValues = [-100, 100];
  let translation = "translateX(0%)";
  if (props.isOpen) {
    return translation;
  }
  if (props.theme.rtl) {
    translateValues.reverse();
  }
  if (props.placement === "end") {
    translation = `translateX(${translateValues[1]}%)`;
  } else if (props.placement === "start") {
    translation = `translateX(${translateValues[0]}%)`;
  }
  return translation;
}, (props) => props.isAnimated && "transform 250ms ease-in-out", (props) => props.size, (props) => retrieveComponentStyles(COMPONENT_ID$7, props));
StyledSheetWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$6 = "chrome.sheet_title";
var StyledSheetTitle = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$6,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSheetTitle",
  componentId: "sc-1gogk75-0"
})(["line-height:", ";color:", ";font-weight:", ";", ";"], (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.theme.colors.foreground, (props) => props.theme.fontWeights.semibold, (props) => retrieveComponentStyles(COMPONENT_ID$6, props));
StyledSheetTitle.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$5 = "chrome.sheet_description";
var StyledSheetDescription = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$5,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSheetDescription",
  componentId: "sc-1puglb6-0"
})(["line-height:", ";color:", ";", ";"], (props) => getLineHeight(props.theme.space.base * 4, props.theme.fontSizes.md), (props) => getColor("neutralHue", 600, props.theme), (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledSheetDescription.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$4 = "chrome.sheet_body";
var StyledSheetBody = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$4,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSheetBody",
  componentId: "sc-bt4eoj-0"
})(["flex:1;overflow-y:auto;padding:", "px;", ";"], (props) => props.theme.space.base * 5, (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledSheetBody.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "chrome.sheet_close";
var BASE_MULTIPLIERS = {
  top: 2.5,
  side: 2,
  size: 10
};
var colorStyles = (props) => {
  const backgroundColor = "primaryHue";
  const foregroundColor = "neutralHue";
  return Ae(["background-color:transparent;color:", ";&:hover{background-color:", ";color:", ";}&[data-garden-focus-visible]{box-shadow:", ";}&:active{transition:background-color 0.1s ease-in-out,color 0.1s ease-in-out;background-color:", ";color:", ";}"], getColor(foregroundColor, 600, props.theme), getColor(backgroundColor, 600, props.theme, 0.08), getColor(foregroundColor, 700, props.theme), props.theme.shadows.md(getColor(backgroundColor, 600, props.theme, 0.35)), getColor(backgroundColor, 600, props.theme, 0.2), getColor(foregroundColor, 800, props.theme));
};
var StyledSheetClose = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$3,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSheetClose",
  componentId: "sc-1ab02oq-0"
})(["display:flex;position:absolute;top:", "px;", ":", ";align-items:center;justify-content:center;transition:box-shadow 0.1s ease-in-out,background-color 0.25s ease-in-out,color 0.25s ease-in-out;border:none;border-radius:50%;cursor:pointer;padding:0;width:", "px;height:", "px;overflow:hidden;text-decoration:none;font-size:0;user-select:none;&::-moz-focus-inner{border:0;}&:focus{outline:none;}", ";& > svg{vertical-align:middle;}", ";"], (props) => props.theme.space.base * BASE_MULTIPLIERS.top, (props) => props.theme.rtl ? "left" : "right", (props) => `${props.theme.space.base * BASE_MULTIPLIERS.side}px`, (props) => props.theme.space.base * BASE_MULTIPLIERS.size, (props) => props.theme.space.base * BASE_MULTIPLIERS.size, (props) => colorStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledSheetClose.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "chrome.sheet_footer";
var StyledSheetFooter = styled_components_browser_esm_default.footer.attrs({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSheetFooter",
  componentId: "sc-2cktos-0"
})(["display:flex;flex-flow:row wrap;align-items:center;justify-content:", ";border-top:", ";padding:", "px;", ";"], (props) => props.isCompact ? "center" : "flex-end", (props) => `${props.theme.borders.sm} ${getColor("neutralHue", 300, props.theme)}}`, (props) => props.theme.space.base * (props.isCompact ? 2.5 : 5), (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledSheetFooter.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "chrome.sheet_footer_item";
var StyledSheetFooterItem = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSheetFooterItem",
  componentId: "sc-r9ixh-0"
})(["", " ", ";"], (props) => `margin-${props.theme.rtl ? "right" : "left"}: ${props.theme.space.base * 5}px;`, (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledSheetFooterItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "chrome.sheet_header";
var StyledSheetHeader = styled_components_browser_esm_default.header.attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSheetHeader",
  componentId: "sc-o2ry8i-0"
})(["border-bottom:", ";padding:", "px;", "  ", ";"], (props) => `${props.theme.borders.sm} ${getColor("neutralHue", 300, props.theme)}}`, (props) => props.theme.space.base * 5, (props) => props.isCloseButtonPresent && `padding-${props.theme.rtl ? "left" : "right"}: ${props.theme.space.base * (BASE_MULTIPLIERS.size + BASE_MULTIPLIERS.side + 2)}px;`, (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledSheetHeader.defaultProps = {
  theme: DEFAULT_THEME
};
var Chrome = import_react.default.forwardRef((_ref, ref) => {
  let {
    hue,
    isFluid,
    ...props
  } = _ref;
  const theme = (0, import_react.useContext)(Me);
  const isLightMemoized = (0, import_react.useMemo)(() => {
    if (hue) {
      const backgroundColor = getColor(hue, 600, theme);
      const LIGHT_COLOR = "white";
      return readableColor(backgroundColor, LIGHT_COLOR, void 0, false) === LIGHT_COLOR;
    }
    return false;
  }, [hue, theme]);
  const isLight = hue ? isLightMemoized : false;
  const isDark = hue ? !isLightMemoized : false;
  const chromeContextValue = (0, import_react.useMemo)(() => ({
    hue: hue || "chromeHue",
    isLight,
    isDark
  }), [hue, isLight, isDark]);
  const environment = useDocument(theme);
  (0, import_react.useEffect)(() => {
    if (environment && !isFluid) {
      const htmlElement = environment.querySelector("html");
      if (htmlElement) {
        const defaultHtmlPosition = htmlElement.style.position;
        htmlElement.style.position = "fixed";
        return () => {
          htmlElement.style.position = defaultHtmlPosition;
        };
      }
    }
    return void 0;
  }, [environment, isFluid]);
  return import_react.default.createElement(ChromeContext.Provider, {
    value: chromeContextValue
  }, import_react.default.createElement(StyledChrome, _extends$3({
    ref
  }, props)));
});
Chrome.displayName = "Chrome";
Chrome.propTypes = {
  hue: import_prop_types.default.string
};
var SkipNav = import_react.default.forwardRef((_ref, ref) => {
  let {
    targetId,
    zIndex,
    children,
    ...props
  } = _ref;
  return import_react.default.createElement(StyledSkipNav, _extends$3({
    href: `#${targetId}`,
    zIndex,
    ref
  }, props), import_react.default.createElement(StyledSkipNavIcon, null), children);
});
SkipNav.displayName = "SkipNav";
SkipNav.propTypes = {
  targetId: import_prop_types.default.string.isRequired,
  zIndex: import_prop_types.default.number
};
SkipNav.defaultProps = {
  zIndex: 1
};
var BodyContext = import_react.default.createContext({
  hasFooter: true
});
var useBodyContext = () => {
  return (0, import_react.useContext)(BodyContext);
};
var Body$1 = import_react.default.forwardRef((_ref, ref) => {
  let {
    hasFooter,
    ...props
  } = _ref;
  const bodyContextValue = (0, import_react.useMemo)(() => ({
    hasFooter: !!hasFooter
  }), [hasFooter]);
  return import_react.default.createElement(BodyContext.Provider, {
    value: bodyContextValue
  }, import_react.default.createElement(StyledBody, _extends$3({
    ref
  }, props)));
});
Body$1.displayName = "Body";
Body$1.propTypes = {
  hasFooter: import_prop_types.default.bool
};
var Content = import_react.default.forwardRef((props, ref) => {
  const {
    hasFooter
  } = useBodyContext();
  return import_react.default.createElement(StyledContent, _extends$3({
    ref,
    hasFooter
  }, props));
});
Content.displayName = "Content";
var Main = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledMain, _extends$3({
  ref
}, props)));
Main.displayName = "Main";
var Sidebar = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledSidebar, _extends$3({
  ref
}, props)));
Sidebar.displayName = "Sidebar";
var Header$1 = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledHeader, _extends$3({
  ref
}, props)));
Header$1.displayName = "Header";
Header$1.propTypes = {
  isStandalone: import_prop_types.default.bool
};
var PLACEMENT = ["end", "start"];
var PRODUCT = ["chat", "connect", "explore", "guide", "message", "support", "talk"];
var HeaderItem = import_react.default.forwardRef((_ref, ref) => {
  let {
    hasLogo,
    product,
    ...other
  } = _ref;
  if (hasLogo) {
    return import_react.default.createElement(StyledLogoHeaderItem, _extends$3({
      ref,
      product
    }, other));
  }
  return import_react.default.createElement(StyledHeaderItem, _extends$3({
    ref
  }, other));
});
HeaderItem.displayName = "HeaderItem";
HeaderItem.propTypes = {
  maxX: import_prop_types.default.bool,
  maxY: import_prop_types.default.bool,
  isRound: import_prop_types.default.bool,
  product: import_prop_types.default.oneOf(PRODUCT),
  hasLogo: import_prop_types.default.bool
};
var HeaderItemIcon = (_ref) => {
  let {
    children,
    ...props
  } = _ref;
  const element = import_react.Children.only(children);
  if ((0, import_react.isValidElement)(element)) {
    const Icon = (_ref2) => {
      let {
        theme,
        ...iconProps
      } = _ref2;
      return (0, import_react.cloneElement)(element, {
        ...props,
        ...iconProps
      });
    };
    return import_react.default.createElement(StyledHeaderItemIcon, _extends$3({
      as: Icon
    }, props));
  }
  return null;
};
var HeaderItemText = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledHeaderItemText, _extends$3({
  ref
}, props)));
HeaderItemText.displayName = "HeaderItemText";
HeaderItemText.propTypes = {
  isClipped: import_prop_types.default.bool
};
var HeaderItemWrapper = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledHeaderItemWrapper, _extends$3({
  ref
}, props)));
HeaderItemWrapper.displayName = "HeaderItemWrapper";
var Footer$1 = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledFooter, _extends$3({
  ref
}, props)));
Footer$1.displayName = "Footer";
var FooterItem$1 = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledFooterItem, _extends$3({
  ref
}, props)));
FooterItem$1.displayName = "FooterItem";
var NavContext = import_react.default.createContext({
  isExpanded: false
});
var useNavContext = () => {
  return (0, import_react.useContext)(NavContext);
};
var Nav = import_react.default.forwardRef((props, ref) => {
  const {
    hue,
    isLight,
    isDark
  } = useChromeContext();
  const navContextValue = (0, import_react.useMemo)(() => ({
    isExpanded: !!props.isExpanded
  }), [props.isExpanded]);
  return import_react.default.createElement(NavContext.Provider, {
    value: navContextValue
  }, import_react.default.createElement(StyledNav, _extends$3({
    ref
  }, props, {
    hue,
    isLight,
    isDark
  })));
});
Nav.displayName = "Nav";
Nav.propTypes = {
  isExpanded: import_prop_types.default.bool
};
var NavItem = import_react.default.forwardRef((_ref, ref) => {
  let {
    hasLogo,
    hasBrandmark,
    product,
    ...other
  } = _ref;
  const {
    hue,
    isLight,
    isDark
  } = useChromeContext();
  const {
    isExpanded
  } = useNavContext();
  const ariaCurrent = other.isCurrent || void 0;
  if (hasLogo) {
    return import_react.default.createElement(StyledLogoNavItem, _extends$3({
      ref,
      isDark,
      isLight,
      product,
      "aria-current": ariaCurrent
    }, other));
  }
  if (hasBrandmark) {
    return import_react.default.createElement(StyledBrandmarkNavItem, _extends$3({
      ref
    }, other));
  }
  return import_react.default.createElement(StyledNavItem, _extends$3({
    tabIndex: 0,
    ref,
    isExpanded,
    hue,
    isDark,
    isLight,
    "aria-current": ariaCurrent
  }, other));
});
NavItem.displayName = "NavItem";
NavItem.propTypes = {
  product: import_prop_types.default.oneOf(PRODUCT),
  hasLogo: import_prop_types.default.bool,
  hasBrandmark: import_prop_types.default.bool
};
var NavItemIcon = (_ref) => {
  let {
    children,
    ...props
  } = _ref;
  const element = import_react.Children.only(children);
  if ((0, import_react.isValidElement)(element)) {
    const Icon = (_ref2) => {
      let {
        theme,
        ...iconProps
      } = _ref2;
      return (0, import_react.cloneElement)(element, {
        ...props,
        ...iconProps
      });
    };
    return import_react.default.createElement(StyledNavItemIcon, _extends$3({
      as: Icon
    }, props));
  }
  return null;
};
var NavItemText = import_react.default.forwardRef((props, ref) => {
  const {
    isExpanded
  } = useNavContext();
  return import_react.default.createElement(StyledNavItemText, _extends$3({
    ref,
    isExpanded
  }, props));
});
NavItemText.displayName = "NavItemText";
NavItemText.propTypes = {
  isWrapped: import_prop_types.default.bool
};
var SubNav = import_react.default.forwardRef((props, ref) => {
  const {
    hue,
    isLight,
    isDark
  } = useChromeContext();
  return import_react.default.createElement(StyledSubNav, _extends$3({
    ref,
    hue,
    isLight,
    isDark
  }, props));
});
SubNav.displayName = "SubNav";
var SubNavItem = import_react.default.forwardRef((props, ref) => {
  const {
    isDark,
    isLight
  } = useChromeContext();
  return import_react.default.createElement(StyledSubNavItem, _extends$3({
    ref,
    isDark,
    isLight
  }, props));
});
SubNavItem.displayName = "SubNavItem";
SubNavItem.propTypes = {
  isCurrent: import_prop_types.default.bool
};
var SubNavItemText = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledSubNavItemText, _extends$3({
  ref
}, props)));
SubNavItemText.displayName = "SubNavItemText";
SubNavItemText.propTypes = {
  isWrapped: import_prop_types.default.bool
};
var CollapsibleSubNavItem = import_react.default.forwardRef((_ref, ref) => {
  let {
    header,
    children,
    isExpanded: controlledExpanded,
    onChange,
    ...other
  } = _ref;
  const panelRef = (0, import_react.useRef)();
  const [internalExpanded, setInternalExpanded] = (0, import_react.useState)(controlledExpanded);
  const expanded = getControlledValue(controlledExpanded, internalExpanded);
  const expandedSections = expanded ? [0] : [];
  const {
    getHeaderProps,
    getTriggerProps,
    getPanelProps
  } = useAccordion({
    expandedSections,
    onChange: () => {
      const isExpanded = expandedSections.length === 0;
      if (onChange) {
        onChange(isExpanded);
      } else {
        setInternalExpanded(isExpanded);
      }
    }
  });
  (0, import_react.useEffect)(() => {
    if (expanded && panelRef.current) {
      panelRef.current.style.maxHeight = `${panelRef.current.scrollHeight}px`;
    }
  }, [expanded, children]);
  return import_react.default.createElement("div", {
    ref
  }, import_react.default.createElement("div", getHeaderProps({
    ariaLevel: 2
  }), import_react.default.createElement(StyledSubNavItemHeader, getTriggerProps({
    isExpanded: expanded,
    index: 0,
    role: null,
    tabIndex: null,
    ...other
  }), import_react.default.createElement(import_react.default.Fragment, null, header, import_react.default.createElement(StyledSubNavItemIconWrapper, {
    isExpanded: expanded
  }, import_react.default.createElement(StyledSubNavItemIcon, null))))), import_react.default.createElement(StyledSubNavPanel, getPanelProps({
    index: 0,
    isHidden: !expanded,
    ref: panelRef
  }), children));
});
CollapsibleSubNavItem.propTypes = {
  header: import_prop_types.default.any,
  isExpanded: import_prop_types.default.bool,
  onChange: import_prop_types.default.func,
  children: import_prop_types.default.node
};
CollapsibleSubNavItem.displayName = "CollapsibleSubNavItem";
var SheetContext = (0, import_react.createContext)({
  setIsCloseButtonPresent() {
  }
});
var useSheetContext = () => {
  return (0, import_react.useContext)(SheetContext);
};
function ownerDocument(node) {
  return node && node.ownerDocument || document;
}
function activeElement(doc) {
  if (doc === void 0) {
    doc = ownerDocument();
  }
  try {
    var active = doc.activeElement;
    if (!active || !active.nodeName)
      return null;
    return active;
  } catch (e) {
    return doc.body;
  }
}
function useFocusableMount(_ref) {
  let {
    isMounted,
    focusOnMount,
    restoreFocus,
    targetRef
  } = _ref;
  const triggerRef = (0, import_react.useRef)();
  (0, import_react.useEffect)(() => {
    if (isMounted && focusOnMount && targetRef.current) {
      triggerRef.current = activeElement();
      targetRef.current.focus();
    }
  }, [isMounted, focusOnMount, targetRef]);
  (0, import_react.useEffect)(() => {
    if (!isMounted && restoreFocus && triggerRef.current) {
      triggerRef.current.focus();
    }
  }, [isMounted, restoreFocus, triggerRef]);
}
var SheetTitle = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    id,
    ...props
  } = _ref;
  const {
    titleId
  } = useSheetContext();
  return import_react.default.createElement(StyledSheetTitle, _extends$3({
    id: id || titleId,
    ref
  }, props));
});
SheetTitle.displayName = "Sheet.Title";
var Title = SheetTitle;
var SheetDescription = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    id,
    ...props
  } = _ref;
  const {
    descriptionId
  } = useSheetContext();
  return import_react.default.createElement(StyledSheetDescription, _extends$3({
    id: id || descriptionId,
    ref
  }, props));
});
SheetDescription.displayName = "Sheet.Description";
var Description = SheetDescription;
var SheetHeader = (0, import_react.forwardRef)((props, ref) => {
  const {
    isCloseButtonPresent
  } = useSheetContext();
  return import_react.default.createElement(StyledSheetHeader, _extends$3({
    ref,
    isCloseButtonPresent
  }, props));
});
SheetHeader.displayName = "Sheet.Header";
var Header = SheetHeader;
var SheetBody = (0, import_react.forwardRef)((props, ref) => {
  return import_react.default.createElement(StyledSheetBody, _extends$3({
    ref
  }, props));
});
SheetBody.displayName = "Sheet.Body";
var Body = SheetBody;
var SheetFooter = (0, import_react.forwardRef)((props, ref) => {
  return import_react.default.createElement(StyledSheetFooter, _extends$3({
    ref
  }, props));
});
SheetFooter.displayName = "Sheet.Footer";
var Footer = SheetFooter;
var SheetFooterItem = (0, import_react.forwardRef)((props, ref) => {
  return import_react.default.createElement(StyledSheetFooterItem, _extends$3({
    ref
  }, props));
});
SheetFooterItem.displayName = "Sheet.FooterItem";
var FooterItem = SheetFooterItem;
var _path;
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var SvgXStroke = function SvgXStroke2(props) {
  return React.createElement("svg", _extends({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path || (_path = React.createElement("path", {
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M3 13L13 3m0 10L3 3"
  })));
};
var SheetClose = (0, import_react.forwardRef)((props, ref) => {
  const {
    setIsCloseButtonPresent
  } = useSheetContext();
  (0, import_react.useEffect)(() => {
    setIsCloseButtonPresent(true);
    return () => setIsCloseButtonPresent(false);
  }, [setIsCloseButtonPresent]);
  return import_react.default.createElement(StyledSheetClose, _extends$3({
    "aria-label": "Close Sheet",
    ref
  }, props), import_react.default.createElement(SvgXStroke, null));
});
SheetClose.displayName = "Sheet.Close";
var Close = SheetClose;
var SheetComponent = import_react.default.forwardRef((_ref, ref) => {
  let {
    id,
    isOpen,
    isAnimated,
    focusOnMount,
    restoreFocus,
    placement,
    size,
    children,
    ...props
  } = _ref;
  const sheetRef = (0, import_react.useRef)(null);
  const seed = useUIDSeed();
  const [isCloseButtonPresent, setIsCloseButtonPresent] = (0, import_react.useState)(false);
  const idPrefix = (0, import_react.useMemo)(() => id || seed(`sheet_${"8.67.0"}`), [id, seed]);
  const titleId = `${idPrefix}--title`;
  const descriptionId = `${idPrefix}--description`;
  const sheetContext = (0, import_react.useMemo)(() => ({
    titleId,
    descriptionId,
    isCloseButtonPresent,
    setIsCloseButtonPresent
  }), [titleId, descriptionId, isCloseButtonPresent]);
  useFocusableMount({
    targetRef: sheetRef,
    isMounted: isOpen,
    focusOnMount,
    restoreFocus
  });
  return import_react.default.createElement(SheetContext.Provider, {
    value: sheetContext
  }, import_react.default.createElement(StyledSheet, _extends$3({
    isOpen,
    isAnimated,
    placement,
    size,
    tabIndex: -1,
    id: idPrefix,
    "aria-labelledby": titleId,
    "aria-describedby": descriptionId,
    ref: react_merge_refs_esm_default([sheetRef, ref])
  }, props), import_react.default.createElement(StyledSheetWrapper, {
    isOpen,
    isAnimated,
    placement,
    size
  }, children)));
});
SheetComponent.displayName = "Sheet";
SheetComponent.propTypes = {
  id: import_prop_types.default.string,
  isOpen: import_prop_types.default.bool,
  isAnimated: import_prop_types.default.bool,
  focusOnMount: import_prop_types.default.bool,
  restoreFocus: import_prop_types.default.bool,
  placement: import_prop_types.default.oneOf(PLACEMENT),
  size: import_prop_types.default.string
};
SheetComponent.defaultProps = {
  isAnimated: true,
  placement: "end",
  size: "380px"
};
var Sheet = SheetComponent;
Sheet.Body = Body;
Sheet.Close = Close;
Sheet.Description = Description;
Sheet.Footer = Footer;
Sheet.FooterItem = FooterItem;
Sheet.Header = Header;
Sheet.Title = Title;
export {
  Body$1 as Body,
  Chrome,
  CollapsibleSubNavItem,
  Content,
  Footer$1 as Footer,
  FooterItem$1 as FooterItem,
  Header$1 as Header,
  HeaderItem,
  HeaderItemIcon,
  HeaderItemText,
  HeaderItemWrapper,
  Main,
  Nav,
  NavItem,
  NavItemIcon,
  NavItemText,
  PRODUCT as PRODUCTS,
  Sheet,
  Sidebar,
  SkipNav,
  SubNav,
  SubNavItem,
  SubNavItemText
};
//# sourceMappingURL=@zendeskgarden_react-chrome.js.map
