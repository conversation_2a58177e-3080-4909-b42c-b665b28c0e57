{"version": 3, "sources": ["../../node_modules/@zendeskgarden/react-grid/dist/index.esm.js", "../../node_modules/use-resize-observer/dist/bundle.esm.js", "../../node_modules/@zendeskgarden/container-splitter/dist/index.esm.js"], "sourcesContent": ["/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { createContext, useContext, useMemo, useState, useCallback, forwardRef, useRef, useEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport styled, { css, ThemeContext } from 'styled-components';\nimport { math, rgba, stripUnit } from 'polished';\nimport { retrieveComponentStyles, DEFAULT_THEME, getColor, useDocument, useText } from '@zendeskgarden/react-theming';\nimport { ChevronButton } from '@zendeskgarden/react-buttons';\nimport { useId, composeEventHandlers } from '@zendeskgarden/container-utilities';\nimport useResizeObserver from 'use-resize-observer';\nimport mergeRefs from 'react-merge-refs';\nimport { useSplitter } from '@zendeskgarden/container-splitter';\nimport { Tooltip } from '@zendeskgarden/react-tooltips';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nconst ALIGN_ITEMS = ['start', 'end', 'center', 'baseline', 'stretch'];\nconst ALIGN_SELF = ['auto', ...ALIGN_ITEMS];\nconst DIRECTION = ['row', 'row-reverse', 'column', 'column-reverse'];\nconst JUSTIFY_CONTENT = ['start', 'end', 'center', 'between', 'around'];\nconst TEXT_ALIGN = ['start', 'end', 'center', 'justify'];\nconst SPACE = [false, 'xxs', 'xs', 'sm', 'md', 'lg', 'xl', 'xxl'];\nconst WRAP = ['nowrap', 'wrap', 'wrap-reverse'];\nconst ORIENTATION = ['top', 'bottom', 'start', 'end'];\n\nconst COMPONENT_ID$6 = 'grid.col';\nconst colorStyles$4 = props => {\n  const backgroundColor = getColor('primaryHue', 600, props.theme, 0.1);\n  return css([\"background-clip:content-box;background-color:\", \";\"], backgroundColor);\n};\nconst flexStyles$1 = (size, alignSelf, textAlign, offset, order, props) => {\n  const margin = offset && `${math(`${offset} / ${props.columns} * 100`)}%`;\n  let flexBasis;\n  let flexGrow;\n  let maxWidth;\n  let width;\n  if (typeof size === 'boolean') {\n    flexBasis = 0;\n    flexGrow = 1;\n    maxWidth = '100%';\n  } else if (size === 'auto') {\n    flexBasis = 'auto';\n    flexGrow = 0;\n    maxWidth = '100%';\n    width = 'auto';\n  } else if (size !== undefined) {\n    flexBasis = `${math(`${size} / ${props.columns} * 100`)}%`;\n    flexGrow = 0;\n    maxWidth = flexBasis;\n  }\n  let horizontalAlign;\n  if (textAlign === 'start') {\n    horizontalAlign = props.theme.rtl ? 'right' : 'left';\n  } else if (textAlign === 'end') {\n    horizontalAlign = props.theme.rtl ? 'left' : 'right';\n  } else {\n    horizontalAlign = textAlign;\n  }\n  let flexOrder;\n  if (order === 'first') {\n    flexOrder = -1;\n  } else if (order === 'last') {\n    flexOrder = math(`${props.columns} + 1`);\n  } else {\n    flexOrder = order;\n  }\n  return css([\"flex-basis:\", \";flex-grow:\", \";flex-shrink:\", \";align-self:\", \";order:\", \";margin-\", \":\", \";width:\", \";max-width:\", \";text-align:\", \";\"], flexBasis, flexGrow, size && 0, alignSelf === 'start' || alignSelf === 'end' ? `flex-${alignSelf}` : alignSelf, flexOrder, props.theme.rtl ? 'right' : 'left', margin, width, maxWidth, horizontalAlign);\n};\nconst mediaStyles$1 = (minWidth, size, alignSelf, textAlign, offset, order, props) => {\n  return css([\"@media (min-width:\", \"){\", \";}\"], minWidth, flexStyles$1(size, alignSelf, textAlign, offset, order, props));\n};\nconst sizeStyles$4 = props => {\n  const padding = props.gutters ? math(`${props.theme.space[props.gutters]} / 2`) : 0;\n  return css([\"padding-right:\", \";padding-left:\", \";\"], padding, padding);\n};\nconst StyledCol = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$6,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledCol\",\n  componentId: \"sc-inuw62-0\"\n})([\"box-sizing:border-box;width:100%;\", \";\", \";\", \";\", \";\", \";\", \";\", \";\", \";\", \";\"], props => flexStyles$1(!props.sizeAll && (props.xs || props.sm || props.md || props.lg || props.xl) ? undefined : props.sizeAll || false, props.alignSelf, props.textAlign, props.offset, props.order, props), props => sizeStyles$4(props), props => props.debug && colorStyles$4(props), props => mediaStyles$1(props.theme.breakpoints.xs, props.xs, props.alignSelfXs, props.textAlignXs, props.offsetXs, props.orderXs, props), props => mediaStyles$1(props.theme.breakpoints.sm, props.sm, props.alignSelfSm, props.textAlignSm, props.offsetSm, props.orderSm, props), props => mediaStyles$1(props.theme.breakpoints.md, props.md, props.alignSelfMd, props.textAlignMd, props.offsetMd, props.orderMd, props), props => mediaStyles$1(props.theme.breakpoints.lg, props.lg, props.alignSelfLg, props.textAlignLg, props.offsetLg, props.orderLg, props), props => mediaStyles$1(props.theme.breakpoints.xl, props.xl, props.alignSelfXl, props.textAlignXl, props.offsetXl, props.orderXl, props), props => retrieveComponentStyles(COMPONENT_ID$6, props));\nStyledCol.defaultProps = {\n  columns: 12,\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$5 = 'grid.grid';\nconst colorStyles$3 = props => {\n  const borderColor = getColor(props.theme.palette.crimson, 400, props.theme, 0.5);\n  const borderWidth = math(`${props.theme.borderWidths.sm} * 2`);\n  return css([\"box-shadow:-\", \" 0 0 0 \", \",\", \" 0 0 0 \", \";\"], borderWidth, borderColor, borderWidth, borderColor);\n};\nconst sizeStyles$3 = props => {\n  const padding = props.gutters ? math(`${props.theme.space[props.gutters]} / 2`) : 0;\n  return css([\"padding-right:\", \";padding-left:\", \";\"], padding, padding);\n};\nconst StyledGrid = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$5,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledGrid\",\n  componentId: \"sc-oxgg5i-0\"\n})([\"direction:\", \";margin-right:auto;margin-left:auto;width:100%;box-sizing:border-box;\", \";\", \";\", \";\"], props => props.theme.rtl && 'rtl', props => sizeStyles$3(props), props => props.debug && colorStyles$3(props), props => retrieveComponentStyles(COMPONENT_ID$5, props));\nStyledGrid.defaultProps = {\n  gutters: 'md',\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$4 = 'grid.row';\nconst colorStyles$2 = props => {\n  const borderColor = getColor(props.theme.palette.mint, 600, props.theme, 0.5);\n  const borderWidth = props.theme.borderWidths.sm;\n  return css([\"box-shadow:inset 0 \", \" 0 0 \", \",inset 0 -\", \" 0 0 \", \";\"], borderWidth, borderColor, borderWidth, borderColor);\n};\nconst flexStyles = (alignItems, justifyContent, wrap) => {\n  let flexAlignItems;\n  let flexJustifyContent;\n  if (alignItems === 'start' || alignItems === 'end') {\n    flexAlignItems = `flex-${alignItems}`;\n  } else {\n    flexAlignItems = alignItems;\n  }\n  if (justifyContent === 'start' || justifyContent === 'end') {\n    flexJustifyContent = `flex-${justifyContent}`;\n  } else if (justifyContent === 'between' || justifyContent === 'around') {\n    flexJustifyContent = `space-${justifyContent}`;\n  } else {\n    flexJustifyContent = justifyContent;\n  }\n  return css([\"flex-wrap:\", \";align-items:\", \";justify-content:\", \";\"], wrap, flexAlignItems, flexJustifyContent);\n};\nconst mediaStyles = (minWidth, alignItems, justifyContent, wrap) => {\n  return css([\"@media (min-width:\", \"){\", \";}\"], minWidth, flexStyles(alignItems, justifyContent, wrap));\n};\nconst sizeStyles$2 = props => {\n  const margin = props.gutters ? math(`${props.theme.space[props.gutters]} / 2`) : 0;\n  return css([\"margin-right:-\", \";margin-left:-\", \";\"], margin, margin);\n};\nconst StyledRow = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$4,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledRow\",\n  componentId: \"sc-xjsdg1-0\"\n})([\"display:flex;box-sizing:border-box;\", \" \", \";\", \";\", \";\", \";\", \";\", \";\", \";\", \";\"], props => flexStyles(props.alignItems, props.justifyContent, props.wrapAll), props => sizeStyles$2(props), props => props.debug && colorStyles$2(props), props => mediaStyles(props.theme.breakpoints.xs, props.alignItemsXs, props.justifyContentXs, props.wrapXs), props => mediaStyles(props.theme.breakpoints.sm, props.alignItemsSm, props.justifyContentSm, props.wrapSm), props => mediaStyles(props.theme.breakpoints.md, props.alignItemsMd, props.justifyContentMd, props.wrapMd), props => mediaStyles(props.theme.breakpoints.lg, props.alignItemsLg, props.justifyContentLg, props.wrapLg), props => mediaStyles(props.theme.breakpoints.xl, props.alignItemsXl, props.justifyContentXl, props.wrapXl), props => retrieveComponentStyles(COMPONENT_ID$4, props));\nStyledRow.defaultProps = {\n  wrapAll: 'wrap',\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$3 = 'pane';\nconst StyledPane = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$3,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledPane\",\n  componentId: \"sc-1ltjst7-0\"\n})([\"position:relative;min-width:0;min-height:0;\", \";\"], props => retrieveComponentStyles(COMPONENT_ID$3, props));\nStyledPane.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$2 = 'pane.content';\nconst StyledPaneContent = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$2,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledPaneContent\",\n  componentId: \"sc-1b38mbh-0\"\n})([\"height:100%;overflow:auto;&[hidden]{display:none;}\", \";\"], props => retrieveComponentStyles(COMPONENT_ID$2, props));\nStyledPaneContent.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$1 = 'pane.splitter';\nconst colorStyles$1 = props => {\n  const color = getColor('neutralHue', 300, props.theme);\n  const hoverColor = getColor('primaryHue', 600, props.theme);\n  const activeColor = getColor('primaryHue', 800, props.theme);\n  const boxShadow = props.theme.shadows.md(rgba(hoverColor, 0.35));\n  return css([\"&::before{background-color:\", \";}&:hover::before{background-color:\", \";}&[data-garden-focus-visible]::before{box-shadow:\", \";background-color:\", \";}&:active::before{background-color:\", \";}\"], color, props.isHovered && hoverColor, boxShadow, hoverColor, props.isHovered && activeColor);\n};\nconst sizeStyles$1 = props => {\n  const size = math(`${props.theme.shadowWidths.md} * 2`);\n  const separatorSize = math(`${props.theme.borderWidths.sm} * 2`);\n  const offset = math(`-${size} / 2`);\n  let cursor;\n  let top;\n  let right;\n  let left;\n  let bottom;\n  let width;\n  let height;\n  let separatorWidth;\n  let separatorHeight;\n  switch (props.orientation) {\n    case 'top':\n      cursor = 'row-resize';\n      top = offset;\n      width = '100%';\n      height = size;\n      separatorWidth = width;\n      separatorHeight = props.theme.borderWidths.sm;\n      break;\n    case 'bottom':\n      cursor = 'row-resize';\n      bottom = offset;\n      width = '100%';\n      height = size;\n      separatorWidth = width;\n      separatorHeight = props.theme.borderWidths.sm;\n      break;\n    case 'start':\n      cursor = 'col-resize';\n      top = 0;\n      width = size;\n      height = '100%';\n      separatorWidth = props.theme.borderWidths.sm;\n      separatorHeight = height;\n      if (props.theme.rtl) {\n        right = offset;\n      } else {\n        left = offset;\n      }\n      break;\n    case 'end':\n    default:\n      cursor = 'col-resize';\n      top = 0;\n      width = size;\n      height = '100%';\n      separatorWidth = props.theme.borderWidths.sm;\n      separatorHeight = height;\n      if (props.theme.rtl) {\n        left = offset;\n      } else {\n        right = offset;\n      }\n      break;\n  }\n  const dimensionProperty = width === '100%' ? 'height' : 'width';\n  return css([\"top:\", \";right:\", \";bottom:\", \";left:\", \";cursor:\", \";width:\", \";height:\", \";&::before{width:\", \";height:\", \";}&:hover::before{\", \":\", \";}&[data-garden-focus-visible]::before,&:focus::before{\", \":\", \";}&[data-garden-focus-visible]::before{border-radius:\", \";}\"], top, right, bottom, left, props.isFixed ? 'pointer' : cursor, width, height, separatorWidth, separatorHeight, dimensionProperty, props.isHovered && separatorSize, dimensionProperty, separatorSize, props.theme.borderRadii.md);\n};\nconst StyledPaneSplitter = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$1,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledPaneSplitter\",\n  componentId: \"sc-jylemn-0\"\n})([\"display:flex;position:absolute;align-items:center;justify-content:center;z-index:1;user-select:none;\", \";&:focus{outline:none;}&::before{position:absolute;transition:box-shadow 0.1s ease-in-out,background-color 0.25s ease-in-out;z-index:-1;content:'';}\", \";\", \";\"], sizeStyles$1, colorStyles$1, props => retrieveComponentStyles(COMPONENT_ID$1, props));\nStyledPaneSplitter.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID = 'pane.splitter_button';\nconst transformStyles = props => {\n  let degrees = 0;\n  if (props.isRotated) {\n    degrees = props.theme.rtl ? -180 : 180;\n  }\n  if (props.orientation === 'end') {\n    degrees += props.theme.rtl ? -90 : 90;\n  } else if (props.orientation === 'start') {\n    degrees += props.theme.rtl ? 90 : -90;\n  } else if (props.orientation === 'bottom') {\n    degrees += 180;\n  }\n  return css([\"& > svg{transform:rotate(\", \"deg);}\"], degrees);\n};\nconst colorStyles = _ref => {\n  let {\n    theme\n  } = _ref;\n  const boxShadow = theme.shadows.lg(`${theme.space.base}px`, `${theme.space.base * 2}px`, getColor('chromeHue', 600, theme, 0.15));\n  const focusBoxShadow = theme.shadows.md(getColor('primaryHue', 600, theme, 0.35));\n  return css([\"box-shadow:\", \";&[data-garden-focus-visible]{box-shadow:\", \",\", \";}\"], boxShadow, focusBoxShadow, boxShadow);\n};\nconst sizeStyles = props => {\n  const size = `${props.theme.space.base * 6}px`;\n  const display = props.splitterSize <= stripUnit(math(`${props.theme.shadowWidths.md} * 2 + ${size}`)) && 'none';\n  const isVertical = props.orientation === 'start' || props.orientation === 'end';\n  let top;\n  let left;\n  let right;\n  let bottom;\n  if (props.splitterSize >= stripUnit(math(`${size} * 3`))) {\n    if (props.placement === 'start') {\n      if (isVertical) {\n        top = size;\n      } else if (props.theme.rtl) {\n        right = size;\n      } else {\n        left = size;\n      }\n    } else if (props.placement === 'end') {\n      if (isVertical) {\n        bottom = size;\n      } else if (props.theme.rtl) {\n        left = size;\n      } else {\n        right = size;\n      }\n    }\n  }\n  return css([\"display:\", \";top:\", \";right:\", \";bottom:\", \";left:\", \";width:\", \";min-width:\", \";height:\", \";\"], display, top, right, bottom, left, size, size, size);\n};\nconst StyledPaneSplitterButton = styled(ChevronButton).attrs({\n  'data-garden-id': COMPONENT_ID,\n  'data-garden-version': '8.67.0',\n  isBasic: true,\n  isPill: true,\n  size: 'small'\n}).withConfig({\n  displayName: \"StyledPaneSplitterButton\",\n  componentId: \"sc-zh032e-0\"\n})([\"position:absolute;transition:background-color 0.25s ease-in-out,opacity 0.25s ease-in-out 0.1s;opacity:0;&[data-garden-focus-visible],\", \":hover &,\", \"[data-garden-focus-visible] &{opacity:1;}\", \";\", \";\", \";&::before{position:absolute;z-index:-1;background-color:\", \";width:100%;height:100%;content:'';}\", \";\"], StyledPaneSplitter, StyledPaneSplitter, sizeStyles, transformStyles, colorStyles, props => props.theme.colors.background, props => retrieveComponentStyles(COMPONENT_ID, props));\nStyledPaneSplitterButton.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst GridContext = createContext({\n  gutters: 'md'\n});\nconst useGridContext = () => {\n  return useContext(GridContext);\n};\n\nconst Col = React.forwardRef((_ref, ref) => {\n  let {\n    size,\n    ...props\n  } = _ref;\n  const {\n    columns,\n    gutters,\n    debug\n  } = useGridContext();\n  return React.createElement(StyledCol, _extends({\n    sizeAll: size,\n    columns: columns,\n    gutters: gutters,\n    debug: debug,\n    ref: ref\n  }, props));\n});\nCol.displayName = 'Col';\nCol.propTypes = {\n  size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  xs: PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.bool]),\n  sm: PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.bool]),\n  md: PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.bool]),\n  lg: PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.bool]),\n  xl: PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.bool]),\n  alignSelf: PropTypes.oneOf(ALIGN_SELF),\n  alignSelfXs: PropTypes.oneOf(ALIGN_SELF),\n  alignSelfSm: PropTypes.oneOf(ALIGN_SELF),\n  alignSelfMd: PropTypes.oneOf(ALIGN_SELF),\n  alignSelfLg: PropTypes.oneOf(ALIGN_SELF),\n  alignSelfXl: PropTypes.oneOf(ALIGN_SELF),\n  textAlign: PropTypes.oneOf(TEXT_ALIGN),\n  textAlignXs: PropTypes.oneOf(TEXT_ALIGN),\n  textAlignSm: PropTypes.oneOf(TEXT_ALIGN),\n  textAlignMd: PropTypes.oneOf(TEXT_ALIGN),\n  textAlignLg: PropTypes.oneOf(TEXT_ALIGN),\n  textAlignXl: PropTypes.oneOf(TEXT_ALIGN),\n  offset: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  offsetXs: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  offsetSm: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  offsetMd: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  offsetLg: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  offsetXl: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  order: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  orderXs: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  orderSm: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  orderMd: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  orderLg: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  orderXl: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n};\n\nconst Grid = React.forwardRef((_ref, ref) => {\n  let {\n    columns,\n    debug,\n    ...props\n  } = _ref;\n  const value = useMemo(() => ({\n    columns,\n    gutters: props.gutters,\n    debug\n  }), [columns, props.gutters, debug]);\n  return React.createElement(GridContext.Provider, {\n    value: value\n  }, React.createElement(StyledGrid, _extends({\n    debug: debug,\n    ref: ref\n  }, props)));\n});\nGrid.displayName = 'Grid';\nGrid.propTypes = {\n  columns: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  gutters: PropTypes.oneOf(SPACE),\n  debug: PropTypes.bool\n};\nGrid.defaultProps = {\n  columns: 12,\n  gutters: 'md'\n};\n\nconst Row = React.forwardRef((_ref, ref) => {\n  let {\n    wrap,\n    ...props\n  } = _ref;\n  const {\n    gutters,\n    debug\n  } = useGridContext();\n  return React.createElement(StyledRow, _extends({\n    gutters: gutters,\n    debug: debug,\n    wrapAll: wrap,\n    ref: ref\n  }, props));\n});\nRow.displayName = 'Row';\nRow.propTypes = {\n  alignItems: PropTypes.oneOf(ALIGN_ITEMS),\n  alignItemsXs: PropTypes.oneOf(ALIGN_ITEMS),\n  alignItemsSm: PropTypes.oneOf(ALIGN_ITEMS),\n  alignItemsMd: PropTypes.oneOf(ALIGN_ITEMS),\n  alignItemsLg: PropTypes.oneOf(ALIGN_ITEMS),\n  alignItemsXl: PropTypes.oneOf(ALIGN_ITEMS),\n  justifyContent: PropTypes.oneOf(JUSTIFY_CONTENT),\n  justifyContentXs: PropTypes.oneOf(JUSTIFY_CONTENT),\n  justifyContentSm: PropTypes.oneOf(JUSTIFY_CONTENT),\n  justifyContentMd: PropTypes.oneOf(JUSTIFY_CONTENT),\n  justifyContentLg: PropTypes.oneOf(JUSTIFY_CONTENT),\n  justifyContentXl: PropTypes.oneOf(JUSTIFY_CONTENT),\n  wrap: PropTypes.oneOf(WRAP),\n  wrapXs: PropTypes.oneOf(WRAP),\n  wrapSm: PropTypes.oneOf(WRAP),\n  wrapMd: PropTypes.oneOf(WRAP),\n  wrapLg: PropTypes.oneOf(WRAP),\n  wrapXl: PropTypes.oneOf(WRAP)\n};\n\nconst PaneProviderContext = createContext({});\nconst usePaneProviderContextData = providerId => {\n  const context = useContext(PaneProviderContext);\n  const id = providerId || context.providerId;\n  return id && context.contextData ? context.contextData[id] : undefined;\n};\nconst usePaneProviderContext = () => useContext(PaneProviderContext);\n\nconst getPixelsPerFr = (totalFrs, totalDimension) => {\n  return totalDimension / totalFrs;\n};\nconst convertToPixels = (values, pixelsPerFr) => {\n  return Object.entries(values).reduce((prev, _ref) => {\n    let [key, value] = _ref;\n    prev[key] = value * pixelsPerFr;\n    return prev;\n  }, {});\n};\nconst PaneProvider = _ref2 => {\n  let {\n    id,\n    totalPanesWidth,\n    totalPanesHeight,\n    defaultRowValues,\n    defaultColumnValues,\n    rowValues,\n    columnValues,\n    onChange,\n    children\n  } = _ref2;\n  const isControlled = rowValues !== undefined && rowValues !== null && columnValues !== undefined && columnValues !== null;\n  const [rowState, setRowState] = useState(defaultRowValues || {});\n  const [columnState, setColumnState] = useState(defaultColumnValues || {});\n  const rowsTrack = isControlled ? rowValues : rowState;\n  const columnsTrack = isControlled ? columnValues : columnState;\n  const setRowsTrack = useCallback(values => {\n    if (isControlled && onChange) {\n      return onChange(values(rowsTrack), columnsTrack);\n    }\n    return setRowState(values);\n  }, [isControlled, onChange, setRowState, columnsTrack, rowsTrack]);\n  const setColumnsTrack = useCallback(values => {\n    if (isControlled && onChange) {\n      return onChange(rowsTrack, values(columnsTrack));\n    }\n    return setColumnState(values);\n  }, [isControlled, onChange, setColumnState, rowsTrack, columnsTrack]);\n  const totalFractions = useMemo(() => ({\n    rows: Object.values(rowsTrack).reduce((prev, value) => value + prev, 0),\n    columns: Object.values(columnsTrack).reduce((prev, value) => value + prev, 0)\n  }), [rowsTrack, columnsTrack]);\n  const pixelsPerFr = useMemo(() => ({\n    rows: getPixelsPerFr(totalFractions.rows, totalPanesHeight),\n    columns: getPixelsPerFr(totalFractions.columns, totalPanesWidth)\n  }), [totalFractions, totalPanesHeight, totalPanesWidth]);\n  const layoutStateInPixels = useMemo(() => ({\n    rows: convertToPixels(rowsTrack, pixelsPerFr.rows),\n    columns: convertToPixels(columnsTrack, pixelsPerFr.columns)\n  }), [rowsTrack, columnsTrack, pixelsPerFr]);\n  const layoutIndices = useMemo(() => {\n    const rowArray = Object.keys(rowsTrack);\n    const columnArray = Object.keys(columnsTrack);\n    const rows = rowArray.reduce((prev, key, index) => {\n      prev[key] = index;\n      return prev;\n    }, {});\n    const columns = columnArray.reduce((prev, key, index) => {\n      prev[key] = index;\n      return prev;\n    }, {});\n    return {\n      rows,\n      columns,\n      rowArray,\n      columnArray\n    };\n  }, [rowsTrack, columnsTrack]);\n  const setRowValue = useCallback((isTop, splitterId, value) => {\n    const {\n      rows,\n      rowArray\n    } = layoutIndices;\n    const stealFromTraversal = isTop ? -1 : 1;\n    const addToTraversal = 0;\n    setRowsTrack(state => {\n      const oldValue = rowsTrack[splitterId];\n      const stealFromIndex = rows[splitterId] + stealFromTraversal;\n      const addToIndex = rows[splitterId] + addToTraversal;\n      const stealFromKey = rowArray[stealFromIndex];\n      const addToKey = rowArray[addToIndex];\n      const difference = oldValue - value;\n      const nextState = {\n        ...state\n      };\n      nextState[addToKey] = rowsTrack[addToKey] - difference;\n      nextState[stealFromKey] = rowsTrack[stealFromKey] + difference;\n      return nextState;\n    });\n  }, [layoutIndices, rowsTrack, setRowsTrack]);\n  const setColumnValue = useCallback((isStart, splitterId, value) => {\n    const {\n      columns,\n      columnArray\n    } = layoutIndices;\n    const stealFromTraversal = isStart ? -1 : 1;\n    const addToTraversal = 0;\n    setColumnsTrack(state => {\n      const stealFromIndex = columns[splitterId] + stealFromTraversal;\n      const addToIndex = columns[splitterId] + addToTraversal;\n      const oldValue = columnsTrack[splitterId];\n      const stealFromKey = columnArray[stealFromIndex];\n      const addToKey = columnArray[addToIndex];\n      const difference = oldValue - value;\n      const nextState = {\n        ...state\n      };\n      nextState[addToKey] = columnsTrack[addToKey] - difference;\n      nextState[stealFromKey] = columnsTrack[stealFromKey] + difference;\n      return nextState;\n    });\n  }, [layoutIndices, columnsTrack, setColumnsTrack]);\n  const getColumnValue = useCallback((splitterKey, isPixels) => {\n    if (isPixels) {\n      return layoutStateInPixels.columns[splitterKey];\n    }\n    return columnsTrack[splitterKey];\n  }, [columnsTrack, layoutStateInPixels]);\n  const getRowValue = useCallback((splitterKey, isPixels) => {\n    if (isPixels) {\n      return layoutStateInPixels.rows[splitterKey];\n    }\n    return rowsTrack[splitterKey];\n  }, [rowsTrack, layoutStateInPixels]);\n  const getGridTemplateColumns = useCallback(isPixels => {\n    const {\n      columnArray\n    } = layoutIndices;\n    if (isPixels) {\n      return columnArray.map(col => `${layoutStateInPixels.columns[col]}px`).join(' ');\n    }\n    return columnArray.map(col => `${columnsTrack[col]}fr`).join(' ');\n  }, [layoutIndices, columnsTrack, layoutStateInPixels]);\n  const getGridTemplateRows = useCallback(isPixels => {\n    const {\n      rowArray\n    } = layoutIndices;\n    if (isPixels) {\n      return rowArray.map(row => `${layoutStateInPixels.rows[row]}px`).join(' ');\n    }\n    return rowArray.map(row => `${rowsTrack[row]}fr`).join(' ');\n  }, [layoutIndices, rowsTrack, layoutStateInPixels]);\n  const providerId = useId(id);\n  const parentPaneProviderContext = usePaneProviderContext();\n  const paneProviderContext = useMemo(() => providerId ? {\n    providerId,\n    contextData: {\n      ...parentPaneProviderContext.contextData,\n      [providerId]: {\n        columnState,\n        rowState,\n        setRowValue,\n        setColumnValue,\n        getRowValue,\n        getColumnValue,\n        totalPanesHeight,\n        totalPanesWidth,\n        pixelsPerFr\n      }\n    }\n  } : {}, [providerId, parentPaneProviderContext, rowState, columnState, setRowValue, setColumnValue, getRowValue, getColumnValue, totalPanesHeight, totalPanesWidth, pixelsPerFr]);\n  return React.createElement(PaneProviderContext.Provider, {\n    value: paneProviderContext\n  }, children?.({\n    id: providerId,\n    getRowValue,\n    getColumnValue,\n    getGridTemplateColumns,\n    getGridTemplateRows\n  }));\n};\nPaneProvider.displayName = 'PaneProvider';\nPaneProvider.propTypes = {\n  id: PropTypes.string,\n  totalPanesWidth: PropTypes.number.isRequired,\n  totalPanesHeight: PropTypes.number.isRequired,\n  defaultRowValues: PropTypes.object,\n  defaultColumnValues: PropTypes.object,\n  rowValues: PropTypes.object,\n  columnValues: PropTypes.object,\n  onChange: PropTypes.func,\n  children: PropTypes.func\n};\n\nconst PaneContext = createContext({\n  setId: () => undefined\n});\nconst usePaneContext = () => {\n  return useContext(PaneContext);\n};\n\nconst PaneSplitterContext = createContext({\n  orientation: 'start',\n  min: 0,\n  max: 0,\n  layoutKey: '',\n  valueNow: 0,\n  size: 0,\n  isRow: false\n});\nconst usePaneSplitterContext = () => {\n  return useContext(PaneSplitterContext);\n};\n\nconst paneToSplitterOrientation = {\n  start: 'vertical',\n  end: 'vertical',\n  top: 'horizontal',\n  bottom: 'horizontal'\n};\nconst orientationToDimension = {\n  start: 'columns',\n  end: 'columns',\n  top: 'rows',\n  bottom: 'rows'\n};\nconst SplitterComponent = forwardRef((_ref, ref) => {\n  let {\n    providerId,\n    layoutKey,\n    min,\n    max,\n    orientation,\n    isFixed,\n    onMouseDown,\n    onTouchStart,\n    onKeyDown,\n    onClick,\n    ...props\n  } = _ref;\n  const paneProviderContext = usePaneProviderContextData(providerId);\n  const paneContext = usePaneContext();\n  const themeContext = useContext(ThemeContext);\n  const environment = useDocument(themeContext);\n  const [isHovered, setIsHovered] = useState(false);\n  const isRow = orientationToDimension[orientation] === 'rows';\n  const separatorRef = useRef(null);\n  const splitterOrientation = paneToSplitterOrientation[orientation || 'end'];\n  const pixelsPerFr = paneProviderContext ? paneProviderContext.pixelsPerFr[orientationToDimension[orientation]] : 0;\n  const value = isRow ? paneProviderContext?.getRowValue(layoutKey, true) : paneProviderContext?.getColumnValue(layoutKey, true);\n  const valueInFr = isRow ? paneProviderContext?.getRowValue(layoutKey) : paneProviderContext?.getColumnValue(layoutKey);\n  const {\n    getSeparatorProps,\n    getPrimaryPaneProps\n  } = useSplitter({\n    orientation: splitterOrientation,\n    isLeading: orientation === 'start' || orientation === 'top',\n    min: min * pixelsPerFr,\n    max: max * pixelsPerFr,\n    rtl: themeContext.rtl,\n    isFixed,\n    environment,\n    onChange: valueNow => {\n      if (isRow) {\n        return paneProviderContext?.setRowValue(orientation === 'top', layoutKey, valueNow / pixelsPerFr);\n      }\n      return paneProviderContext?.setColumnValue(orientation === 'start', layoutKey, valueNow / pixelsPerFr);\n    },\n    valueNow: value,\n    separatorRef\n  });\n  useEffect(() => {\n    if (!paneContext.id) {\n      paneContext.setId(getPrimaryPaneProps().id);\n    }\n  }, [paneContext, getPrimaryPaneProps]);\n  const ariaLabel = useText(SplitterComponent, props, 'aria-label', `${splitterOrientation} splitter`);\n  const separatorProps = getSeparatorProps({\n    'aria-controls': paneContext.id,\n    'aria-label': ariaLabel,\n    onMouseDown,\n    onTouchStart,\n    onKeyDown,\n    onClick\n  });\n  const size = isRow ? separatorRef.current?.clientWidth : separatorRef.current?.clientHeight;\n  const onMouseOver = useMemo(() => composeEventHandlers(props.onMouseOver, event => setIsHovered(event.target === separatorRef.current)), [props.onMouseOver, separatorRef]);\n  return React.createElement(PaneSplitterContext.Provider, {\n    value: useMemo(() => ({\n      orientation,\n      layoutKey,\n      min,\n      max,\n      valueNow: valueInFr,\n      size,\n      isRow\n    }), [orientation, layoutKey, min, max, valueInFr, size, isRow])\n  }, React.createElement(StyledPaneSplitter, _extends({\n    isHovered: isHovered,\n    isFixed: isFixed,\n    orientation: orientation\n  }, separatorProps, props, {\n    onMouseOver: onMouseOver,\n    ref: mergeRefs([separatorRef, ref])\n  })));\n});\nSplitterComponent.displayName = 'Pane.Splitter';\nSplitterComponent.propTypes = {\n  layoutKey: PropTypes.string.isRequired,\n  min: PropTypes.number.isRequired,\n  max: PropTypes.number.isRequired,\n  orientation: PropTypes.oneOf(ORIENTATION),\n  isFixed: PropTypes.bool\n};\nSplitterComponent.defaultProps = {\n  orientation: 'end'\n};\nconst Splitter = SplitterComponent;\n\nconst ContentComponent = forwardRef((props, ref) => {\n  const {\n    isVisible\n  } = usePaneContext();\n  return React.createElement(StyledPaneContent, _extends({\n    hidden: !isVisible,\n    ref: ref\n  }, props));\n});\nContentComponent.displayName = 'Pane.Content';\nconst Content = ContentComponent;\n\nconst SplitterButtonComponent = forwardRef((props, ref) => {\n  const {\n    label,\n    placement: defaultPlacement\n  } = props;\n  const {\n    orientation,\n    layoutKey,\n    min,\n    max,\n    isRow,\n    valueNow,\n    size,\n    providerId\n  } = usePaneSplitterContext();\n  const paneProviderContext = usePaneProviderContextData(providerId);\n  const isTop = orientation === 'top';\n  const isStart = orientation === 'start';\n  const isMin = valueNow === min;\n  let placement = defaultPlacement;\n  if (!defaultPlacement) {\n    if (isRow) {\n      placement = 'center';\n    } else {\n      placement = 'start';\n    }\n  }\n  const setValue = useCallback(value => {\n    if (isRow) {\n      paneProviderContext.setRowValue(isTop, layoutKey, value);\n    } else {\n      paneProviderContext.setColumnValue(isStart, layoutKey, value);\n    }\n  }, [isRow, isTop, isStart, layoutKey, paneProviderContext]);\n  const onClick = composeEventHandlers(props.onClick, () => {\n    if (isMin) {\n      setValue(max);\n    } else {\n      setValue(min);\n    }\n  });\n  const onKeyDown = composeEventHandlers(props.onKeyDown, event => event.stopPropagation()\n  );\n  const onMouseDown = composeEventHandlers(props.onMouseDown, event => event.stopPropagation()\n  );\n  return React.createElement(Tooltip, {\n    content: label,\n    style: {\n      cursor: 'default'\n    },\n    onMouseDown: e => e.stopPropagation()\n  }, React.createElement(StyledPaneSplitterButton, _extends({\n    \"aria-label\": label\n  }, props, {\n    placement: placement,\n    orientation: orientation,\n    isRotated: isMin,\n    splitterSize: size || 0,\n    ref: ref,\n    onClick: onClick,\n    onKeyDown: onKeyDown,\n    onMouseDown: onMouseDown\n  })));\n});\nSplitterButtonComponent.displayName = 'Pane.SplitterButton';\nconst SplitterButton = SplitterButtonComponent;\n\nconst PaneComponent = forwardRef((_ref, ref) => {\n  let {\n    children,\n    ...props\n  } = _ref;\n  const [paneId, setPaneId] = useState();\n  const observerRef = useRef(null);\n  const {\n    width = 0,\n    height = 0\n  } = useResizeObserver({\n    ref: observerRef\n  });\n  const isVisible = useMemo(() => observerRef.current ? width > 0 && height > 0 : true, [width, height]);\n  const paneContext = useMemo(() => ({\n    isVisible,\n    id: paneId,\n    setId: id => setPaneId(id)\n  }), [paneId, isVisible]);\n  return React.createElement(PaneContext.Provider, {\n    value: paneContext\n  }, React.createElement(StyledPane, _extends({\n    id: paneId,\n    ref: mergeRefs([ref, observerRef])\n  }, props), children));\n});\nPaneComponent.displayName = 'Pane';\nconst Pane = PaneComponent;\nPane.Content = Content;\nPane.Splitter = Splitter;\nPane.SplitterButton = SplitterButton;\n\nexport { ALIGN_ITEMS as ARRAY_ALIGN_ITEMS, ALIGN_SELF as ARRAY_ALIGN_SELF, DIRECTION as ARRAY_DIRECTION, JUSTIFY_CONTENT as ARRAY_JUSTIFY_CONTENT, SPACE as ARRAY_SPACE, TEXT_ALIGN as ARRAY_TEXT_ALIGN, WRAP as ARRAY_WRAP, Col, Grid, Pane, PaneProvider, Row };\n", "import { useRef, useEffect, useCallback, useState, useMemo } from 'react';\n\n// This could've been more streamlined with internal state instead of abusing\n// refs to such extent, but then composing hooks and components could not opt out of unnecessary renders.\nfunction useResolvedElement(subscriber, refOrElement) {\n  var lastReportRef = useRef(null);\n  var refOrElementRef = useRef(null);\n  refOrElementRef.current = refOrElement;\n  var cbElementRef = useRef(null); // Calling re-evaluation after each render without using a dep array,\n  // as the ref object's current value could've changed since the last render.\n\n  useEffect(function () {\n    evaluateSubscription();\n  });\n  var evaluateSubscription = useCallback(function () {\n    var cbElement = cbElementRef.current;\n    var refOrElement = refOrElementRef.current; // Ugly ternary. But smaller than an if-else block.\n\n    var element = cbElement ? cbElement : refOrElement ? refOrElement instanceof Element ? refOrElement : refOrElement.current : null;\n\n    if (lastReportRef.current && lastReportRef.current.element === element && lastReportRef.current.subscriber === subscriber) {\n      return;\n    }\n\n    if (lastReportRef.current && lastReportRef.current.cleanup) {\n      lastReportRef.current.cleanup();\n    }\n\n    lastReportRef.current = {\n      element: element,\n      subscriber: subscriber,\n      // Only calling the subscriber, if there's an actual element to report.\n      // Setting cleanup to undefined unless a subscriber returns one, as an existing cleanup function would've been just called.\n      cleanup: element ? subscriber(element) : undefined\n    };\n  }, [subscriber]); // making sure we call the cleanup function on unmount\n\n  useEffect(function () {\n    return function () {\n      if (lastReportRef.current && lastReportRef.current.cleanup) {\n        lastReportRef.current.cleanup();\n        lastReportRef.current = null;\n      }\n    };\n  }, []);\n  return useCallback(function (element) {\n    cbElementRef.current = element;\n    evaluateSubscription();\n  }, [evaluateSubscription]);\n}\n\n// We're only using the first element of the size sequences, until future versions of the spec solidify on how\n// exactly it'll be used for fragments in multi-column scenarios:\n// From the spec:\n// > The box size properties are exposed as FrozenArray in order to support elements that have multiple fragments,\n// > which occur in multi-column scenarios. However the current definitions of content rect and border box do not\n// > mention how those boxes are affected by multi-column layout. In this spec, there will only be a single\n// > ResizeObserverSize returned in the FrozenArray, which will correspond to the dimensions of the first column.\n// > A future version of this spec will extend the returned FrozenArray to contain the per-fragment size information.\n// (https://drafts.csswg.org/resize-observer/#resize-observer-entry-interface)\n//\n// Also, testing these new box options revealed that in both Chrome and FF everything is returned in the callback,\n// regardless of the \"box\" option.\n// The spec states the following on this:\n// > This does not have any impact on which box dimensions are returned to the defined callback when the event\n// > is fired, it solely defines which box the author wishes to observe layout changes on.\n// (https://drafts.csswg.org/resize-observer/#resize-observer-interface)\n// I'm not exactly clear on what this means, especially when you consider a later section stating the following:\n// > This section is non-normative. An author may desire to observe more than one CSS box.\n// > In this case, author will need to use multiple ResizeObservers.\n// (https://drafts.csswg.org/resize-observer/#resize-observer-interface)\n// Which is clearly not how current browser implementations behave, and seems to contradict the previous quote.\n// For this reason I decided to only return the requested size,\n// even though it seems we have access to results for all box types.\n// This also means that we get to keep the current api, being able to return a simple { width, height } pair,\n// regardless of box option.\nfunction extractSize(entry, boxProp, sizeType) {\n  if (!entry[boxProp]) {\n    if (boxProp === \"contentBoxSize\") {\n      // The dimensions in `contentBoxSize` and `contentRect` are equivalent according to the spec.\n      // See the 6th step in the description for the RO algorithm:\n      // https://drafts.csswg.org/resize-observer/#create-and-populate-resizeobserverentry-h\n      // > Set this.contentRect to logical this.contentBoxSize given target and observedBox of \"content-box\".\n      // In real browser implementations of course these objects differ, but the width/height values should be equivalent.\n      return entry.contentRect[sizeType === \"inlineSize\" ? \"width\" : \"height\"];\n    }\n\n    return undefined;\n  } // A couple bytes smaller than calling Array.isArray() and just as effective here.\n\n\n  return entry[boxProp][0] ? entry[boxProp][0][sizeType] : // TS complains about this, because the RO entry type follows the spec and does not reflect Firefox's current\n  // behaviour of returning objects instead of arrays for `borderBoxSize` and `contentBoxSize`.\n  // @ts-ignore\n  entry[boxProp][sizeType];\n}\n\nfunction useResizeObserver(opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n\n  // Saving the callback as a ref. With this, I don't need to put onResize in the\n  // effect dep array, and just passing in an anonymous function without memoising\n  // will not reinstantiate the hook's ResizeObserver.\n  var onResize = opts.onResize;\n  var onResizeRef = useRef(undefined);\n  onResizeRef.current = onResize;\n  var round = opts.round || Math.round; // Using a single instance throughout the hook's lifetime\n\n  var resizeObserverRef = useRef();\n\n  var _useState = useState({\n    width: undefined,\n    height: undefined\n  }),\n      size = _useState[0],\n      setSize = _useState[1]; // In certain edge cases the RO might want to report a size change just after\n  // the component unmounted.\n\n\n  var didUnmount = useRef(false);\n  useEffect(function () {\n    didUnmount.current = false;\n    return function () {\n      didUnmount.current = true;\n    };\n  }, []); // Using a ref to track the previous width / height to avoid unnecessary renders.\n\n  var previous = useRef({\n    width: undefined,\n    height: undefined\n  }); // This block is kinda like a useEffect, only it's called whenever a new\n  // element could be resolved based on the ref option. It also has a cleanup\n  // function.\n\n  var refCallback = useResolvedElement(useCallback(function (element) {\n    // We only use a single Resize Observer instance, and we're instantiating it on demand, only once there's something to observe.\n    // This instance is also recreated when the `box` option changes, so that a new observation is fired if there was a previously observed element with a different box option.\n    if (!resizeObserverRef.current || resizeObserverRef.current.box !== opts.box || resizeObserverRef.current.round !== round) {\n      resizeObserverRef.current = {\n        box: opts.box,\n        round: round,\n        instance: new ResizeObserver(function (entries) {\n          var entry = entries[0];\n          var boxProp = opts.box === \"border-box\" ? \"borderBoxSize\" : opts.box === \"device-pixel-content-box\" ? \"devicePixelContentBoxSize\" : \"contentBoxSize\";\n          var reportedWidth = extractSize(entry, boxProp, \"inlineSize\");\n          var reportedHeight = extractSize(entry, boxProp, \"blockSize\");\n          var newWidth = reportedWidth ? round(reportedWidth) : undefined;\n          var newHeight = reportedHeight ? round(reportedHeight) : undefined;\n\n          if (previous.current.width !== newWidth || previous.current.height !== newHeight) {\n            var newSize = {\n              width: newWidth,\n              height: newHeight\n            };\n            previous.current.width = newWidth;\n            previous.current.height = newHeight;\n\n            if (onResizeRef.current) {\n              onResizeRef.current(newSize);\n            } else {\n              if (!didUnmount.current) {\n                setSize(newSize);\n              }\n            }\n          }\n        })\n      };\n    }\n\n    resizeObserverRef.current.instance.observe(element, {\n      box: opts.box\n    });\n    return function () {\n      if (resizeObserverRef.current) {\n        resizeObserverRef.current.instance.unobserve(element);\n      }\n    };\n  }, [opts.box, round]), opts.ref);\n  return useMemo(function () {\n    return {\n      ref: refCallback,\n      width: size.width,\n      height: size.height\n    };\n  }, [refCallback, size.width, size.height]);\n}\n\nexport { useResizeObserver as default };\n", "/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';\nimport { useId, composeEventHandlers, KEYS } from '@zendeskgarden/container-utilities';\nimport PropTypes from 'prop-types';\n\nconst KEYBOARD_STEP = 48;\nconst normalizePointerToSeparator = function (bodyPadding, pointerPosition, separatorHeightOrWidth, viewportWidthOrHeight) {\n  if (separatorHeightOrWidth === void 0) {\n    separatorHeightOrWidth = 0;\n  }\n  if (viewportWidthOrHeight === void 0) {\n    viewportWidthOrHeight = 0;\n  }\n  if (viewportWidthOrHeight === 0) {\n    return pointerPosition - bodyPadding - Math.floor(separatorHeightOrWidth / 2);\n  }\n  return viewportWidthOrHeight - pointerPosition - bodyPadding - Math.floor(separatorHeightOrWidth / 2);\n};\nconst xor = (a, b) => {\n  if (a && b) {\n    return false;\n  }\n  return a || b;\n};\nconst useSplitter = _ref => {\n  let {\n    idPrefix,\n    environment,\n    isFixed,\n    min,\n    max,\n    orientation = 'vertical',\n    keyboardStep = KEYBOARD_STEP,\n    defaultValueNow = min,\n    valueNow,\n    onChange = () => undefined,\n    separatorRef,\n    isLeading,\n    rtl\n  } = _ref;\n  const prefix = useId(idPrefix);\n  const primaryPaneId = `${prefix}--primary-pane`;\n  const isControlled = valueNow !== undefined && valueNow !== null;\n  const [state, setState] = useState(defaultValueNow);\n  const [separatorElement, setSeparatorElement] = useState(separatorRef.current);\n  const offsetRef = useRef({\n    left: 0,\n    right: 0,\n    top: 0,\n    bottom: 0\n  });\n  const separatorPosition = isControlled ? valueNow : state;\n  const [lastPosition, setLastPosition] = useState(separatorPosition);\n  const doc = environment || document;\n  useEffect(() => {\n    if (separatorRef.current !== separatorElement) {\n      setSeparatorElement(separatorRef.current);\n    }\n  });\n  const setSeparatorPosition = isControlled ? onChange : setState;\n  const setRangedSeparatorPosition = useCallback(nextDimension => {\n    if (nextDimension >= max) {\n      setSeparatorPosition(max);\n    } else if (nextDimension <= min) {\n      setSeparatorPosition(min);\n    } else {\n      setSeparatorPosition(nextDimension);\n    }\n  }, [max, min, setSeparatorPosition]);\n  const move = useCallback((pageX, pageY) => {\n    if (separatorElement) {\n      const clientWidth = xor(rtl, isLeading) ? doc.body.clientWidth : undefined;\n      const clientHeight = isLeading ? doc.body.clientHeight : undefined;\n      if (orientation === 'horizontal') {\n        const offset = isLeading ? offsetRef.current.bottom : offsetRef.current.top;\n        setRangedSeparatorPosition(\n        normalizePointerToSeparator(offset, pageY, separatorElement.offsetHeight, clientHeight));\n      } else {\n        const offset = xor(rtl, isLeading) ? offsetRef.current.right : offsetRef.current.left;\n        setRangedSeparatorPosition(\n        normalizePointerToSeparator(offset, pageX, separatorElement.offsetWidth, clientWidth));\n      }\n    }\n  }, [doc, isLeading, orientation, rtl, separatorElement, setRangedSeparatorPosition]);\n  const getSeparatorProps = useCallback(_ref2 => {\n    let {\n      role = 'separator',\n      onMouseDown,\n      onTouchStart,\n      onKeyDown,\n      onClick,\n      ...other\n    } = _ref2;\n    const onMouseMove = event => {\n      move(event.pageX, event.pageY);\n    };\n    const onTouchMove = event => {\n      const {\n        pageY,\n        pageX\n      } = event.targetTouches[0];\n      move(pageX, pageY);\n    };\n    const onMouseUp = () => {\n      doc.removeEventListener('mouseup', onMouseUp);\n      doc.removeEventListener('mousemove', onMouseMove);\n    };\n    const onTouchEnd = () => {\n      doc.removeEventListener('touchend', onTouchEnd);\n      doc.removeEventListener('touchmove', onTouchMove);\n    };\n    const updateOffsets = () => {\n      if (separatorElement) {\n        const rect = separatorElement.getBoundingClientRect();\n        const clientWidth = doc.body.clientWidth;\n        const clientHeight = doc.body.clientHeight;\n        const win = doc.documentElement || doc.body.parentNode || doc.body;\n        offsetRef.current.left = rect.left - separatorPosition + win.scrollLeft;\n        offsetRef.current.right = clientWidth - rect.right - separatorPosition - win.scrollLeft;\n        offsetRef.current.top = rect.top - separatorPosition + win.scrollTop;\n        offsetRef.current.bottom = clientHeight - rect.bottom - separatorPosition - win.scrollTop;\n      }\n    };\n    const handleMouseDown = () => {\n      if (!isFixed) {\n        updateOffsets();\n        doc.addEventListener('mouseup', onMouseUp);\n        doc.addEventListener('mousemove', onMouseMove);\n      }\n    };\n    const handleTouchStart = () => {\n      if (!isFixed) {\n        updateOffsets();\n        doc.addEventListener('touchend', onTouchEnd);\n        doc.addEventListener('touchmove', onTouchMove);\n      }\n    };\n    const handleKeyDown = event => {\n      if (event.key === KEYS.ENTER) {\n        if (separatorPosition === min) {\n          setSeparatorPosition(lastPosition === min ? max : lastPosition);\n        } else {\n          setLastPosition(separatorPosition);\n          setSeparatorPosition(min);\n        }\n      } else if (event.key === KEYS.HOME) {\n        separatorPosition !== min && setLastPosition(separatorPosition);\n        setSeparatorPosition(min);\n      } else if (event.key === KEYS.END) {\n        setSeparatorPosition(max);\n      } else if (!isFixed) {\n        if (event.key === KEYS.RIGHT && orientation === 'vertical') {\n          let position;\n          if (rtl) {\n            position = separatorPosition + (isLeading ? keyboardStep : -keyboardStep);\n          } else {\n            position = separatorPosition + (isLeading ? -keyboardStep : keyboardStep);\n          }\n          setRangedSeparatorPosition(position);\n          event.preventDefault();\n        } else if (event.key === KEYS.LEFT && orientation === 'vertical') {\n          let position;\n          if (rtl) {\n            position = separatorPosition + (isLeading ? -keyboardStep : keyboardStep);\n          } else {\n            position = separatorPosition + (isLeading ? keyboardStep : -keyboardStep);\n          }\n          setRangedSeparatorPosition(position);\n          event.preventDefault();\n        } else if (event.key === KEYS.UP && orientation === 'horizontal') {\n          setRangedSeparatorPosition(separatorPosition + (isLeading ? keyboardStep : -keyboardStep));\n          event.preventDefault();\n        } else if (event.key === KEYS.DOWN && orientation === 'horizontal') {\n          setRangedSeparatorPosition(separatorPosition + (isLeading ? -keyboardStep : keyboardStep));\n          event.preventDefault();\n        }\n      }\n    };\n    const handleClick = event => {\n      if (isFixed || event.detail === 2 ) {\n        handleKeyDown({\n          key: KEYS.ENTER\n        });\n      }\n    };\n    const ariaValueNow = (separatorPosition - min) / (max - min) * 100;\n    const ariaValueMin = isFinite(ariaValueNow) ? 0 : min;\n    const ariaValueMax = isFinite(ariaValueNow) ? 100 : max;\n    return {\n      role: role === null ? undefined : role,\n      onMouseDown: composeEventHandlers(onMouseDown, handleMouseDown),\n      onTouchStart: composeEventHandlers(onTouchStart, handleTouchStart),\n      onKeyDown: composeEventHandlers(onKeyDown, handleKeyDown),\n      onClick: composeEventHandlers(onClick, handleClick),\n      'aria-controls': primaryPaneId,\n      'aria-valuenow': isFinite(ariaValueNow) ? ariaValueNow : separatorPosition,\n      'aria-valuemin': ariaValueMin,\n      'aria-valuemax': ariaValueMax,\n      'aria-orientation': orientation,\n      'data-garden-container-id': 'containers.splitter.separator',\n      'data-garden-container-version': '2.0.7',\n      tabIndex: 0,\n      ...other\n    };\n  }, [doc, isFixed, isLeading, keyboardStep, lastPosition, max, min, move, orientation, primaryPaneId, rtl, separatorPosition, separatorElement, setRangedSeparatorPosition, setSeparatorPosition]);\n  const getPrimaryPaneProps = useCallback(function (other) {\n    if (other === void 0) {\n      other = {};\n    }\n    return {\n      'data-garden-container-id': 'containers.splitter.primaryPane',\n      'data-garden-container-version': '2.0.7',\n      id: primaryPaneId,\n      ...other\n    };\n  }, [primaryPaneId]);\n  return useMemo(() => ({\n    getSeparatorProps,\n    getPrimaryPaneProps,\n    valueNow: separatorPosition\n  }), [getSeparatorProps, getPrimaryPaneProps, separatorPosition]);\n};\n\nconst SplitterContainer = _ref => {\n  let {\n    children,\n    render = children,\n    ...options\n  } = _ref;\n  return React.createElement(React.Fragment, null, render(useSplitter(options)));\n};\nSplitterContainer.propTypes = {\n  children: PropTypes.func,\n  render: PropTypes.func,\n  idPrefix: PropTypes.string,\n  environment: PropTypes.any,\n  isFixed: PropTypes.bool,\n  min: PropTypes.number.isRequired,\n  max: PropTypes.number.isRequired,\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  keyboardStep: PropTypes.number,\n  defaultValueNow: PropTypes.number,\n  valueNow: PropTypes.number,\n  onChange: PropTypes.func,\n  separatorRef: PropTypes.any.isRequired,\n  isLeading: PropTypes.bool,\n  rtl: PropTypes.bool\n};\nSplitterContainer.defaultProps = {\n  keyboardStep: KEYBOARD_STEP,\n  orientation: 'vertical'\n};\n\nexport { SplitterContainer, useSplitter };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAAA,gBAAgH;AAChH,IAAAC,qBAAsB;;;ACRtB,mBAAkE;AAIlE,SAAS,mBAAmB,YAAY,cAAc;AACpD,MAAI,oBAAgB,qBAAO,IAAI;AAC/B,MAAI,sBAAkB,qBAAO,IAAI;AACjC,kBAAgB,UAAU;AAC1B,MAAI,mBAAe,qBAAO,IAAI;AAG9B,8BAAU,WAAY;AACpB,yBAAqB;AAAA,EACvB,CAAC;AACD,MAAI,2BAAuB,0BAAY,WAAY;AACjD,QAAI,YAAY,aAAa;AAC7B,QAAIC,gBAAe,gBAAgB;AAEnC,QAAI,UAAU,YAAY,YAAYA,gBAAeA,yBAAwB,UAAUA,gBAAeA,cAAa,UAAU;AAE7H,QAAI,cAAc,WAAW,cAAc,QAAQ,YAAY,WAAW,cAAc,QAAQ,eAAe,YAAY;AACzH;AAAA,IACF;AAEA,QAAI,cAAc,WAAW,cAAc,QAAQ,SAAS;AAC1D,oBAAc,QAAQ,QAAQ;AAAA,IAChC;AAEA,kBAAc,UAAU;AAAA,MACtB;AAAA,MACA;AAAA;AAAA;AAAA,MAGA,SAAS,UAAU,WAAW,OAAO,IAAI;AAAA,IAC3C;AAAA,EACF,GAAG,CAAC,UAAU,CAAC;AAEf,8BAAU,WAAY;AACpB,WAAO,WAAY;AACjB,UAAI,cAAc,WAAW,cAAc,QAAQ,SAAS;AAC1D,sBAAc,QAAQ,QAAQ;AAC9B,sBAAc,UAAU;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,aAAO,0BAAY,SAAU,SAAS;AACpC,iBAAa,UAAU;AACvB,yBAAqB;AAAA,EACvB,GAAG,CAAC,oBAAoB,CAAC;AAC3B;AA2BA,SAAS,YAAY,OAAO,SAAS,UAAU;AAC7C,MAAI,CAAC,MAAM,OAAO,GAAG;AACnB,QAAI,YAAY,kBAAkB;AAMhC,aAAO,MAAM,YAAY,aAAa,eAAe,UAAU,QAAQ;AAAA,IACzE;AAEA,WAAO;AAAA,EACT;AAGA,SAAO,MAAM,OAAO,EAAE,CAAC,IAAI,MAAM,OAAO,EAAE,CAAC,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,IAGrD,MAAM,OAAO,EAAE,QAAQ;AAAA;AACzB;AAEA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAKA,MAAI,WAAW,KAAK;AACpB,MAAI,kBAAc,qBAAO,MAAS;AAClC,cAAY,UAAU;AACtB,MAAI,QAAQ,KAAK,SAAS,KAAK;AAE/B,MAAI,wBAAoB,qBAAO;AAE/B,MAAI,gBAAY,uBAAS;AAAA,IACvB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC,GACG,OAAO,UAAU,CAAC,GAClB,UAAU,UAAU,CAAC;AAIzB,MAAI,iBAAa,qBAAO,KAAK;AAC7B,8BAAU,WAAY;AACpB,eAAW,UAAU;AACrB,WAAO,WAAY;AACjB,iBAAW,UAAU;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,MAAI,eAAW,qBAAO;AAAA,IACpB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AAID,MAAI,cAAc,uBAAmB,0BAAY,SAAU,SAAS;AAGlE,QAAI,CAAC,kBAAkB,WAAW,kBAAkB,QAAQ,QAAQ,KAAK,OAAO,kBAAkB,QAAQ,UAAU,OAAO;AACzH,wBAAkB,UAAU;AAAA,QAC1B,KAAK,KAAK;AAAA,QACV;AAAA,QACA,UAAU,IAAI,eAAe,SAAU,SAAS;AAC9C,cAAI,QAAQ,QAAQ,CAAC;AACrB,cAAI,UAAU,KAAK,QAAQ,eAAe,kBAAkB,KAAK,QAAQ,6BAA6B,8BAA8B;AACpI,cAAI,gBAAgB,YAAY,OAAO,SAAS,YAAY;AAC5D,cAAI,iBAAiB,YAAY,OAAO,SAAS,WAAW;AAC5D,cAAI,WAAW,gBAAgB,MAAM,aAAa,IAAI;AACtD,cAAI,YAAY,iBAAiB,MAAM,cAAc,IAAI;AAEzD,cAAI,SAAS,QAAQ,UAAU,YAAY,SAAS,QAAQ,WAAW,WAAW;AAChF,gBAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AACA,qBAAS,QAAQ,QAAQ;AACzB,qBAAS,QAAQ,SAAS;AAE1B,gBAAI,YAAY,SAAS;AACvB,0BAAY,QAAQ,OAAO;AAAA,YAC7B,OAAO;AACL,kBAAI,CAAC,WAAW,SAAS;AACvB,wBAAQ,OAAO;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,sBAAkB,QAAQ,SAAS,QAAQ,SAAS;AAAA,MAClD,KAAK,KAAK;AAAA,IACZ,CAAC;AACD,WAAO,WAAY;AACjB,UAAI,kBAAkB,SAAS;AAC7B,0BAAkB,QAAQ,SAAS,UAAU,OAAO;AAAA,MACtD;AAAA,IACF;AAAA,EACF,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG;AAC/B,aAAO,sBAAQ,WAAY;AACzB,WAAO;AAAA,MACL,KAAK;AAAA,MACL,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACf;AAAA,EACF,GAAG,CAAC,aAAa,KAAK,OAAO,KAAK,MAAM,CAAC;AAC3C;;;ACpLA,IAAAC,gBAAyE;AAEzE,wBAAsB;AAEtB,IAAM,gBAAgB;AACtB,IAAM,8BAA8B,SAAU,aAAa,iBAAiB,wBAAwB,uBAAuB;AACzH,MAAI,2BAA2B,QAAQ;AACrC,6BAAyB;AAAA,EAC3B;AACA,MAAI,0BAA0B,QAAQ;AACpC,4BAAwB;AAAA,EAC1B;AACA,MAAI,0BAA0B,GAAG;AAC/B,WAAO,kBAAkB,cAAc,KAAK,MAAM,yBAAyB,CAAC;AAAA,EAC9E;AACA,SAAO,wBAAwB,kBAAkB,cAAc,KAAK,MAAM,yBAAyB,CAAC;AACtG;AACA,IAAM,MAAM,CAAC,GAAG,MAAM;AACpB,MAAI,KAAK,GAAG;AACV,WAAO;AAAA,EACT;AACA,SAAO,KAAK;AACd;AACA,IAAM,cAAc,UAAQ;AAC1B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB;AAAA,IACA,WAAW,MAAM;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,SAAS,MAAM,QAAQ;AAC7B,QAAM,gBAAgB,GAAG;AACzB,QAAM,eAAe,aAAa,UAAa,aAAa;AAC5D,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,eAAe;AAClD,QAAM,CAAC,kBAAkB,mBAAmB,QAAI,wBAAS,aAAa,OAAO;AAC7E,QAAM,gBAAY,sBAAO;AAAA,IACvB,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,oBAAoB,eAAe,WAAW;AACpD,QAAM,CAAC,cAAc,eAAe,QAAI,wBAAS,iBAAiB;AAClE,QAAM,MAAM,eAAe;AAC3B,+BAAU,MAAM;AACd,QAAI,aAAa,YAAY,kBAAkB;AAC7C,0BAAoB,aAAa,OAAO;AAAA,IAC1C;AAAA,EACF,CAAC;AACD,QAAM,uBAAuB,eAAe,WAAW;AACvD,QAAM,iCAA6B,2BAAY,mBAAiB;AAC9D,QAAI,iBAAiB,KAAK;AACxB,2BAAqB,GAAG;AAAA,IAC1B,WAAW,iBAAiB,KAAK;AAC/B,2BAAqB,GAAG;AAAA,IAC1B,OAAO;AACL,2BAAqB,aAAa;AAAA,IACpC;AAAA,EACF,GAAG,CAAC,KAAK,KAAK,oBAAoB,CAAC;AACnC,QAAM,WAAO,2BAAY,CAAC,OAAO,UAAU;AACzC,QAAI,kBAAkB;AACpB,YAAM,cAAc,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,cAAc;AACjE,YAAM,eAAe,YAAY,IAAI,KAAK,eAAe;AACzD,UAAI,gBAAgB,cAAc;AAChC,cAAM,SAAS,YAAY,UAAU,QAAQ,SAAS,UAAU,QAAQ;AACxE;AAAA,UACA,4BAA4B,QAAQ,OAAO,iBAAiB,cAAc,YAAY;AAAA,QAAC;AAAA,MACzF,OAAO;AACL,cAAM,SAAS,IAAI,KAAK,SAAS,IAAI,UAAU,QAAQ,QAAQ,UAAU,QAAQ;AACjF;AAAA,UACA,4BAA4B,QAAQ,OAAO,iBAAiB,aAAa,WAAW;AAAA,QAAC;AAAA,MACvF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,KAAK,WAAW,aAAa,KAAK,kBAAkB,0BAA0B,CAAC;AACnF,QAAM,wBAAoB,2BAAY,WAAS;AAC7C,QAAI;AAAA,MACF,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,cAAc,WAAS;AAC3B,WAAK,MAAM,OAAO,MAAM,KAAK;AAAA,IAC/B;AACA,UAAM,cAAc,WAAS;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM,cAAc,CAAC;AACzB,WAAK,OAAO,KAAK;AAAA,IACnB;AACA,UAAM,YAAY,MAAM;AACtB,UAAI,oBAAoB,WAAW,SAAS;AAC5C,UAAI,oBAAoB,aAAa,WAAW;AAAA,IAClD;AACA,UAAM,aAAa,MAAM;AACvB,UAAI,oBAAoB,YAAY,UAAU;AAC9C,UAAI,oBAAoB,aAAa,WAAW;AAAA,IAClD;AACA,UAAM,gBAAgB,MAAM;AAC1B,UAAI,kBAAkB;AACpB,cAAM,OAAO,iBAAiB,sBAAsB;AACpD,cAAM,cAAc,IAAI,KAAK;AAC7B,cAAM,eAAe,IAAI,KAAK;AAC9B,cAAM,MAAM,IAAI,mBAAmB,IAAI,KAAK,cAAc,IAAI;AAC9D,kBAAU,QAAQ,OAAO,KAAK,OAAO,oBAAoB,IAAI;AAC7D,kBAAU,QAAQ,QAAQ,cAAc,KAAK,QAAQ,oBAAoB,IAAI;AAC7E,kBAAU,QAAQ,MAAM,KAAK,MAAM,oBAAoB,IAAI;AAC3D,kBAAU,QAAQ,SAAS,eAAe,KAAK,SAAS,oBAAoB,IAAI;AAAA,MAClF;AAAA,IACF;AACA,UAAM,kBAAkB,MAAM;AAC5B,UAAI,CAAC,SAAS;AACZ,sBAAc;AACd,YAAI,iBAAiB,WAAW,SAAS;AACzC,YAAI,iBAAiB,aAAa,WAAW;AAAA,MAC/C;AAAA,IACF;AACA,UAAM,mBAAmB,MAAM;AAC7B,UAAI,CAAC,SAAS;AACZ,sBAAc;AACd,YAAI,iBAAiB,YAAY,UAAU;AAC3C,YAAI,iBAAiB,aAAa,WAAW;AAAA,MAC/C;AAAA,IACF;AACA,UAAM,gBAAgB,WAAS;AAC7B,UAAI,MAAM,QAAQ,KAAK,OAAO;AAC5B,YAAI,sBAAsB,KAAK;AAC7B,+BAAqB,iBAAiB,MAAM,MAAM,YAAY;AAAA,QAChE,OAAO;AACL,0BAAgB,iBAAiB;AACjC,+BAAqB,GAAG;AAAA,QAC1B;AAAA,MACF,WAAW,MAAM,QAAQ,KAAK,MAAM;AAClC,8BAAsB,OAAO,gBAAgB,iBAAiB;AAC9D,6BAAqB,GAAG;AAAA,MAC1B,WAAW,MAAM,QAAQ,KAAK,KAAK;AACjC,6BAAqB,GAAG;AAAA,MAC1B,WAAW,CAAC,SAAS;AACnB,YAAI,MAAM,QAAQ,KAAK,SAAS,gBAAgB,YAAY;AAC1D,cAAI;AACJ,cAAI,KAAK;AACP,uBAAW,qBAAqB,YAAY,eAAe,CAAC;AAAA,UAC9D,OAAO;AACL,uBAAW,qBAAqB,YAAY,CAAC,eAAe;AAAA,UAC9D;AACA,qCAA2B,QAAQ;AACnC,gBAAM,eAAe;AAAA,QACvB,WAAW,MAAM,QAAQ,KAAK,QAAQ,gBAAgB,YAAY;AAChE,cAAI;AACJ,cAAI,KAAK;AACP,uBAAW,qBAAqB,YAAY,CAAC,eAAe;AAAA,UAC9D,OAAO;AACL,uBAAW,qBAAqB,YAAY,eAAe,CAAC;AAAA,UAC9D;AACA,qCAA2B,QAAQ;AACnC,gBAAM,eAAe;AAAA,QACvB,WAAW,MAAM,QAAQ,KAAK,MAAM,gBAAgB,cAAc;AAChE,qCAA2B,qBAAqB,YAAY,eAAe,CAAC,aAAa;AACzF,gBAAM,eAAe;AAAA,QACvB,WAAW,MAAM,QAAQ,KAAK,QAAQ,gBAAgB,cAAc;AAClE,qCAA2B,qBAAqB,YAAY,CAAC,eAAe,aAAa;AACzF,gBAAM,eAAe;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,UAAM,cAAc,WAAS;AAC3B,UAAI,WAAW,MAAM,WAAW,GAAI;AAClC,sBAAc;AAAA,UACZ,KAAK,KAAK;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,gBAAgB,oBAAoB,QAAQ,MAAM,OAAO;AAC/D,UAAM,eAAe,SAAS,YAAY,IAAI,IAAI;AAClD,UAAM,eAAe,SAAS,YAAY,IAAI,MAAM;AACpD,WAAO;AAAA,MACL,MAAM,SAAS,OAAO,SAAY;AAAA,MAClC,aAAa,qBAAqB,aAAa,eAAe;AAAA,MAC9D,cAAc,qBAAqB,cAAc,gBAAgB;AAAA,MACjE,WAAW,qBAAqB,WAAW,aAAa;AAAA,MACxD,SAAS,qBAAqB,SAAS,WAAW;AAAA,MAClD,iBAAiB;AAAA,MACjB,iBAAiB,SAAS,YAAY,IAAI,eAAe;AAAA,MACzD,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,4BAA4B;AAAA,MAC5B,iCAAiC;AAAA,MACjC,UAAU;AAAA,MACV,GAAG;AAAA,IACL;AAAA,EACF,GAAG,CAAC,KAAK,SAAS,WAAW,cAAc,cAAc,KAAK,KAAK,MAAM,aAAa,eAAe,KAAK,mBAAmB,kBAAkB,4BAA4B,oBAAoB,CAAC;AAChM,QAAM,0BAAsB,2BAAY,SAAU,OAAO;AACvD,QAAI,UAAU,QAAQ;AACpB,cAAQ,CAAC;AAAA,IACX;AACA,WAAO;AAAA,MACL,4BAA4B;AAAA,MAC5B,iCAAiC;AAAA,MACjC,IAAI;AAAA,MACJ,GAAG;AAAA,IACL;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAClB,aAAO,uBAAQ,OAAO;AAAA,IACpB;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACZ,IAAI,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACjE;AAEA,IAAM,oBAAoB,UAAQ;AAChC,MAAI;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAC,QAAM,cAAc,cAAAA,QAAM,UAAU,MAAM,OAAO,YAAY,OAAO,CAAC,CAAC;AAC/E;AACA,kBAAkB,YAAY;AAAA,EAC5B,UAAU,kBAAAC,QAAU;AAAA,EACpB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,UAAU,kBAAAA,QAAU;AAAA,EACpB,aAAa,kBAAAA,QAAU;AAAA,EACvB,SAAS,kBAAAA,QAAU;AAAA,EACnB,KAAK,kBAAAA,QAAU,OAAO;AAAA,EACtB,KAAK,kBAAAA,QAAU,OAAO;AAAA,EACtB,aAAa,kBAAAA,QAAU,MAAM,CAAC,cAAc,UAAU,CAAC;AAAA,EACvD,cAAc,kBAAAA,QAAU;AAAA,EACxB,iBAAiB,kBAAAA,QAAU;AAAA,EAC3B,UAAU,kBAAAA,QAAU;AAAA,EACpB,UAAU,kBAAAA,QAAU;AAAA,EACpB,cAAc,kBAAAA,QAAU,IAAI;AAAA,EAC5B,WAAW,kBAAAA,QAAU;AAAA,EACrB,KAAK,kBAAAA,QAAU;AACjB;AACA,kBAAkB,eAAe;AAAA,EAC/B,cAAc;AAAA,EACd,aAAa;AACf;;;AF9OA,SAAS,WAAW;AAClB,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEA,IAAM,cAAc,CAAC,SAAS,OAAO,UAAU,YAAY,SAAS;AACpE,IAAM,aAAa,CAAC,QAAQ,GAAG,WAAW;AAC1C,IAAM,YAAY,CAAC,OAAO,eAAe,UAAU,gBAAgB;AACnE,IAAM,kBAAkB,CAAC,SAAS,OAAO,UAAU,WAAW,QAAQ;AACtE,IAAM,aAAa,CAAC,SAAS,OAAO,UAAU,SAAS;AACvD,IAAM,QAAQ,CAAC,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK;AAChE,IAAM,OAAO,CAAC,UAAU,QAAQ,cAAc;AAC9C,IAAM,cAAc,CAAC,OAAO,UAAU,SAAS,KAAK;AAEpD,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,QAAM,kBAAkB,SAAS,cAAc,KAAK,MAAM,OAAO,GAAG;AACpE,SAAO,GAAI,CAAC,iDAAiD,GAAG,GAAG,eAAe;AACpF;AACA,IAAM,eAAe,CAAC,MAAM,WAAW,WAAW,QAAQ,OAAO,UAAU;AACzE,QAAM,SAAS,UAAU,GAAG,KAAK,GAAG,YAAY,MAAM,eAAe;AACrE,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,SAAS,WAAW;AAC7B,gBAAY;AACZ,eAAW;AACX,eAAW;AAAA,EACb,WAAW,SAAS,QAAQ;AAC1B,gBAAY;AACZ,eAAW;AACX,eAAW;AACX,YAAQ;AAAA,EACV,WAAW,SAAS,QAAW;AAC7B,gBAAY,GAAG,KAAK,GAAG,UAAU,MAAM,eAAe;AACtD,eAAW;AACX,eAAW;AAAA,EACb;AACA,MAAI;AACJ,MAAI,cAAc,SAAS;AACzB,sBAAkB,MAAM,MAAM,MAAM,UAAU;AAAA,EAChD,WAAW,cAAc,OAAO;AAC9B,sBAAkB,MAAM,MAAM,MAAM,SAAS;AAAA,EAC/C,OAAO;AACL,sBAAkB;AAAA,EACpB;AACA,MAAI;AACJ,MAAI,UAAU,SAAS;AACrB,gBAAY;AAAA,EACd,WAAW,UAAU,QAAQ;AAC3B,gBAAY,KAAK,GAAG,MAAM,aAAa;AAAA,EACzC,OAAO;AACL,gBAAY;AAAA,EACd;AACA,SAAO,GAAI,CAAC,eAAe,eAAe,iBAAiB,gBAAgB,WAAW,YAAY,KAAK,WAAW,eAAe,gBAAgB,GAAG,GAAG,WAAW,UAAU,QAAQ,GAAG,cAAc,WAAW,cAAc,QAAQ,QAAQ,cAAc,WAAW,WAAW,MAAM,MAAM,MAAM,UAAU,QAAQ,QAAQ,OAAO,UAAU,eAAe;AAChW;AACA,IAAM,gBAAgB,CAAC,UAAU,MAAM,WAAW,WAAW,QAAQ,OAAO,UAAU;AACpF,SAAO,GAAI,CAAC,sBAAsB,MAAM,IAAI,GAAG,UAAU,aAAa,MAAM,WAAW,WAAW,QAAQ,OAAO,KAAK,CAAC;AACzH;AACA,IAAM,eAAe,WAAS;AAC5B,QAAM,UAAU,MAAM,UAAU,KAAK,GAAG,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,IAAI;AAClF,SAAO,GAAI,CAAC,kBAAkB,kBAAkB,GAAG,GAAG,SAAS,OAAO;AACxE;AACA,IAAM,YAAY,sCAAO,IAAI,MAAM;AAAA,EACjC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,qCAAqC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,WAAS,aAAa,CAAC,MAAM,YAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,SAAY,MAAM,WAAW,OAAO,MAAM,WAAW,MAAM,WAAW,MAAM,QAAQ,MAAM,OAAO,KAAK,GAAG,WAAS,aAAa,KAAK,GAAG,WAAS,MAAM,SAAS,cAAc,KAAK,GAAG,WAAS,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,IAAI,MAAM,aAAa,MAAM,aAAa,MAAM,UAAU,MAAM,SAAS,KAAK,GAAG,WAAS,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,IAAI,MAAM,aAAa,MAAM,aAAa,MAAM,UAAU,MAAM,SAAS,KAAK,GAAG,WAAS,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,IAAI,MAAM,aAAa,MAAM,aAAa,MAAM,UAAU,MAAM,SAAS,KAAK,GAAG,WAAS,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,IAAI,MAAM,aAAa,MAAM,aAAa,MAAM,UAAU,MAAM,SAAS,KAAK,GAAG,WAAS,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,IAAI,MAAM,aAAa,MAAM,aAAa,MAAM,UAAU,MAAM,SAAS,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC1lC,UAAU,eAAe;AAAA,EACvB,SAAS;AAAA,EACT,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,QAAM,cAAc,SAAS,MAAM,MAAM,QAAQ,SAAS,KAAK,MAAM,OAAO,GAAG;AAC/E,QAAM,cAAc,KAAK,GAAG,MAAM,MAAM,aAAa,QAAQ;AAC7D,SAAO,GAAI,CAAC,gBAAgB,WAAW,KAAK,WAAW,GAAG,GAAG,aAAa,aAAa,aAAa,WAAW;AACjH;AACA,IAAM,eAAe,WAAS;AAC5B,QAAM,UAAU,MAAM,UAAU,KAAK,GAAG,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,IAAI;AAClF,SAAO,GAAI,CAAC,kBAAkB,kBAAkB,GAAG,GAAG,SAAS,OAAO;AACxE;AACA,IAAM,aAAa,sCAAO,IAAI,MAAM;AAAA,EAClC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,cAAc,yEAAyE,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,OAAO,OAAO,WAAS,aAAa,KAAK,GAAG,WAAS,MAAM,SAAS,cAAc,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACjR,WAAW,eAAe;AAAA,EACxB,SAAS;AAAA,EACT,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,QAAM,cAAc,SAAS,MAAM,MAAM,QAAQ,MAAM,KAAK,MAAM,OAAO,GAAG;AAC5E,QAAM,cAAc,MAAM,MAAM,aAAa;AAC7C,SAAO,GAAI,CAAC,uBAAuB,SAAS,cAAc,SAAS,GAAG,GAAG,aAAa,aAAa,aAAa,WAAW;AAC7H;AACA,IAAM,aAAa,CAAC,YAAY,gBAAgB,SAAS;AACvD,MAAI;AACJ,MAAI;AACJ,MAAI,eAAe,WAAW,eAAe,OAAO;AAClD,qBAAiB,QAAQ;AAAA,EAC3B,OAAO;AACL,qBAAiB;AAAA,EACnB;AACA,MAAI,mBAAmB,WAAW,mBAAmB,OAAO;AAC1D,yBAAqB,QAAQ;AAAA,EAC/B,WAAW,mBAAmB,aAAa,mBAAmB,UAAU;AACtE,yBAAqB,SAAS;AAAA,EAChC,OAAO;AACL,yBAAqB;AAAA,EACvB;AACA,SAAO,GAAI,CAAC,cAAc,iBAAiB,qBAAqB,GAAG,GAAG,MAAM,gBAAgB,kBAAkB;AAChH;AACA,IAAM,cAAc,CAAC,UAAU,YAAY,gBAAgB,SAAS;AAClE,SAAO,GAAI,CAAC,sBAAsB,MAAM,IAAI,GAAG,UAAU,WAAW,YAAY,gBAAgB,IAAI,CAAC;AACvG;AACA,IAAM,eAAe,WAAS;AAC5B,QAAM,SAAS,MAAM,UAAU,KAAK,GAAG,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,IAAI;AACjF,SAAO,GAAI,CAAC,kBAAkB,kBAAkB,GAAG,GAAG,QAAQ,MAAM;AACtE;AACA,IAAM,YAAY,sCAAO,IAAI,MAAM;AAAA,EACjC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,uCAAuC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,WAAS,WAAW,MAAM,YAAY,MAAM,gBAAgB,MAAM,OAAO,GAAG,WAAS,aAAa,KAAK,GAAG,WAAS,MAAM,SAAS,cAAc,KAAK,GAAG,WAAS,YAAY,MAAM,MAAM,YAAY,IAAI,MAAM,cAAc,MAAM,kBAAkB,MAAM,MAAM,GAAG,WAAS,YAAY,MAAM,MAAM,YAAY,IAAI,MAAM,cAAc,MAAM,kBAAkB,MAAM,MAAM,GAAG,WAAS,YAAY,MAAM,MAAM,YAAY,IAAI,MAAM,cAAc,MAAM,kBAAkB,MAAM,MAAM,GAAG,WAAS,YAAY,MAAM,MAAM,YAAY,IAAI,MAAM,cAAc,MAAM,kBAAkB,MAAM,MAAM,GAAG,WAAS,YAAY,MAAM,MAAM,YAAY,IAAI,MAAM,cAAc,MAAM,kBAAkB,MAAM,MAAM,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACp0B,UAAU,eAAe;AAAA,EACvB,SAAS;AAAA,EACT,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,aAAa,sCAAO,IAAI,MAAM;AAAA,EAClC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,+CAA+C,GAAG,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAChH,WAAW,eAAe;AAAA,EACxB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,oBAAoB,sCAAO,IAAI,MAAM;AAAA,EACzC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,sDAAsD,GAAG,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACvH,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,QAAM,QAAQ,SAAS,cAAc,KAAK,MAAM,KAAK;AACrD,QAAM,aAAa,SAAS,cAAc,KAAK,MAAM,KAAK;AAC1D,QAAM,cAAc,SAAS,cAAc,KAAK,MAAM,KAAK;AAC3D,QAAM,YAAY,MAAM,MAAM,QAAQ,GAAG,KAAK,YAAY,IAAI,CAAC;AAC/D,SAAO,GAAI,CAAC,+BAA+B,uCAAuC,sDAAsD,sBAAsB,wCAAwC,IAAI,GAAG,OAAO,MAAM,aAAa,YAAY,WAAW,YAAY,MAAM,aAAa,WAAW;AAC1S;AACA,IAAM,eAAe,WAAS;AAC5B,QAAM,OAAO,KAAK,GAAG,MAAM,MAAM,aAAa,QAAQ;AACtD,QAAM,gBAAgB,KAAK,GAAG,MAAM,MAAM,aAAa,QAAQ;AAC/D,QAAM,SAAS,KAAK,IAAI,UAAU;AAClC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,UAAQ,MAAM,aAAa;AAAA,IACzB,KAAK;AACH,eAAS;AACT,YAAM;AACN,cAAQ;AACR,eAAS;AACT,uBAAiB;AACjB,wBAAkB,MAAM,MAAM,aAAa;AAC3C;AAAA,IACF,KAAK;AACH,eAAS;AACT,eAAS;AACT,cAAQ;AACR,eAAS;AACT,uBAAiB;AACjB,wBAAkB,MAAM,MAAM,aAAa;AAC3C;AAAA,IACF,KAAK;AACH,eAAS;AACT,YAAM;AACN,cAAQ;AACR,eAAS;AACT,uBAAiB,MAAM,MAAM,aAAa;AAC1C,wBAAkB;AAClB,UAAI,MAAM,MAAM,KAAK;AACnB,gBAAQ;AAAA,MACV,OAAO;AACL,eAAO;AAAA,MACT;AACA;AAAA,IACF,KAAK;AAAA,IACL;AACE,eAAS;AACT,YAAM;AACN,cAAQ;AACR,eAAS;AACT,uBAAiB,MAAM,MAAM,aAAa;AAC1C,wBAAkB;AAClB,UAAI,MAAM,MAAM,KAAK;AACnB,eAAO;AAAA,MACT,OAAO;AACL,gBAAQ;AAAA,MACV;AACA;AAAA,EACJ;AACA,QAAM,oBAAoB,UAAU,SAAS,WAAW;AACxD,SAAO,GAAI,CAAC,QAAQ,WAAW,YAAY,UAAU,YAAY,WAAW,YAAY,qBAAqB,YAAY,sBAAsB,KAAK,2DAA2D,KAAK,yDAAyD,IAAI,GAAG,KAAK,OAAO,QAAQ,MAAM,MAAM,UAAU,YAAY,QAAQ,OAAO,QAAQ,gBAAgB,iBAAiB,mBAAmB,MAAM,aAAa,eAAe,mBAAmB,eAAe,MAAM,MAAM,YAAY,EAAE;AACrf;AACA,IAAM,qBAAqB,sCAAO,IAAI,MAAM;AAAA,EAC1C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,wGAAwG,wJAAwJ,KAAK,GAAG,GAAG,cAAc,eAAe,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACnW,mBAAmB,eAAe;AAAA,EAChC,OAAO;AACT;AAEA,IAAM,eAAe;AACrB,IAAM,kBAAkB,WAAS;AAC/B,MAAI,UAAU;AACd,MAAI,MAAM,WAAW;AACnB,cAAU,MAAM,MAAM,MAAM,OAAO;AAAA,EACrC;AACA,MAAI,MAAM,gBAAgB,OAAO;AAC/B,eAAW,MAAM,MAAM,MAAM,MAAM;AAAA,EACrC,WAAW,MAAM,gBAAgB,SAAS;AACxC,eAAW,MAAM,MAAM,MAAM,KAAK;AAAA,EACpC,WAAW,MAAM,gBAAgB,UAAU;AACzC,eAAW;AAAA,EACb;AACA,SAAO,GAAI,CAAC,6BAA6B,QAAQ,GAAG,OAAO;AAC7D;AACA,IAAM,cAAc,UAAQ;AAC1B,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,MAAM,QAAQ,GAAG,GAAG,MAAM,MAAM,UAAU,GAAG,MAAM,MAAM,OAAO,OAAO,SAAS,aAAa,KAAK,OAAO,IAAI,CAAC;AAChI,QAAM,iBAAiB,MAAM,QAAQ,GAAG,SAAS,cAAc,KAAK,OAAO,IAAI,CAAC;AAChF,SAAO,GAAI,CAAC,eAAe,6CAA6C,KAAK,IAAI,GAAG,WAAW,gBAAgB,SAAS;AAC1H;AACA,IAAM,aAAa,WAAS;AAC1B,QAAM,OAAO,GAAG,MAAM,MAAM,MAAM,OAAO;AACzC,QAAM,UAAU,MAAM,gBAAgB,UAAU,KAAK,GAAG,MAAM,MAAM,aAAa,YAAY,MAAM,CAAC,KAAK;AACzG,QAAM,aAAa,MAAM,gBAAgB,WAAW,MAAM,gBAAgB;AAC1E,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM,gBAAgB,UAAU,KAAK,GAAG,UAAU,CAAC,GAAG;AACxD,QAAI,MAAM,cAAc,SAAS;AAC/B,UAAI,YAAY;AACd,cAAM;AAAA,MACR,WAAW,MAAM,MAAM,KAAK;AAC1B,gBAAQ;AAAA,MACV,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,WAAW,MAAM,cAAc,OAAO;AACpC,UAAI,YAAY;AACd,iBAAS;AAAA,MACX,WAAW,MAAM,MAAM,KAAK;AAC1B,eAAO;AAAA,MACT,OAAO;AACL,gBAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACA,SAAO,GAAI,CAAC,YAAY,SAAS,WAAW,YAAY,UAAU,WAAW,eAAe,YAAY,GAAG,GAAG,SAAS,KAAK,OAAO,QAAQ,MAAM,MAAM,MAAM,IAAI;AACnK;AACA,IAAM,2BAA2B,sCAAO,aAAa,EAAE,MAAM;AAAA,EAC3D,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,MAAM;AACR,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,0IAA0I,aAAa,6CAA6C,KAAK,KAAK,6DAA6D,wCAAwC,GAAG,GAAG,oBAAoB,oBAAoB,YAAY,iBAAiB,aAAa,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,wBAAwB,cAAc,KAAK,CAAC;AAC5e,yBAAyB,eAAe;AAAA,EACtC,OAAO;AACT;AAEA,IAAM,kBAAc,6BAAc;AAAA,EAChC,SAAS;AACX,CAAC;AACD,IAAM,iBAAiB,MAAM;AAC3B,aAAO,0BAAW,WAAW;AAC/B;AAEA,IAAM,MAAM,cAAAC,QAAM,WAAW,CAAC,MAAM,QAAQ;AAC1C,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAA,QAAM,cAAc,WAAW,SAAS;AAAA,IAC7C,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,IAAI,cAAc;AAClB,IAAI,YAAY;AAAA,EACd,MAAM,mBAAAC,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAC9D,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC;AAAA,EAC5E,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC;AAAA,EAC5E,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC;AAAA,EAC5E,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC;AAAA,EAC5E,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC;AAAA,EAC5E,WAAW,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACrC,aAAa,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACvC,aAAa,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACvC,aAAa,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACvC,aAAa,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACvC,aAAa,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACvC,WAAW,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACrC,aAAa,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACvC,aAAa,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACvC,aAAa,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACvC,aAAa,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACvC,aAAa,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACvC,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAChE,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAClE,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAClE,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAClE,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAClE,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAClE,OAAO,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAC/D,SAAS,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACjE,SAAS,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACjE,SAAS,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACjE,SAAS,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACjE,SAAS,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AACnE;AAEA,IAAM,OAAO,cAAAD,QAAM,WAAW,CAAC,MAAM,QAAQ;AAC3C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAQ,uBAAQ,OAAO;AAAA,IAC3B;AAAA,IACA,SAAS,MAAM;AAAA,IACf;AAAA,EACF,IAAI,CAAC,SAAS,MAAM,SAAS,KAAK,CAAC;AACnC,SAAO,cAAAA,QAAM,cAAc,YAAY,UAAU;AAAA,IAC/C;AAAA,EACF,GAAG,cAAAA,QAAM,cAAc,YAAY,SAAS;AAAA,IAC1C;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC,CAAC;AACZ,CAAC;AACD,KAAK,cAAc;AACnB,KAAK,YAAY;AAAA,EACf,SAAS,mBAAAC,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACjE,SAAS,mBAAAA,QAAU,MAAM,KAAK;AAAA,EAC9B,OAAO,mBAAAA,QAAU;AACnB;AACA,KAAK,eAAe;AAAA,EAClB,SAAS;AAAA,EACT,SAAS;AACX;AAEA,IAAM,MAAM,cAAAD,QAAM,WAAW,CAAC,MAAM,QAAQ;AAC1C,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAA,QAAM,cAAc,WAAW,SAAS;AAAA,IAC7C;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,IAAI,cAAc;AAClB,IAAI,YAAY;AAAA,EACd,YAAY,mBAAAC,QAAU,MAAM,WAAW;AAAA,EACvC,cAAc,mBAAAA,QAAU,MAAM,WAAW;AAAA,EACzC,cAAc,mBAAAA,QAAU,MAAM,WAAW;AAAA,EACzC,cAAc,mBAAAA,QAAU,MAAM,WAAW;AAAA,EACzC,cAAc,mBAAAA,QAAU,MAAM,WAAW;AAAA,EACzC,cAAc,mBAAAA,QAAU,MAAM,WAAW;AAAA,EACzC,gBAAgB,mBAAAA,QAAU,MAAM,eAAe;AAAA,EAC/C,kBAAkB,mBAAAA,QAAU,MAAM,eAAe;AAAA,EACjD,kBAAkB,mBAAAA,QAAU,MAAM,eAAe;AAAA,EACjD,kBAAkB,mBAAAA,QAAU,MAAM,eAAe;AAAA,EACjD,kBAAkB,mBAAAA,QAAU,MAAM,eAAe;AAAA,EACjD,kBAAkB,mBAAAA,QAAU,MAAM,eAAe;AAAA,EACjD,MAAM,mBAAAA,QAAU,MAAM,IAAI;AAAA,EAC1B,QAAQ,mBAAAA,QAAU,MAAM,IAAI;AAAA,EAC5B,QAAQ,mBAAAA,QAAU,MAAM,IAAI;AAAA,EAC5B,QAAQ,mBAAAA,QAAU,MAAM,IAAI;AAAA,EAC5B,QAAQ,mBAAAA,QAAU,MAAM,IAAI;AAAA,EAC5B,QAAQ,mBAAAA,QAAU,MAAM,IAAI;AAC9B;AAEA,IAAM,0BAAsB,6BAAc,CAAC,CAAC;AAC5C,IAAM,6BAA6B,gBAAc;AAC/C,QAAM,cAAU,0BAAW,mBAAmB;AAC9C,QAAM,KAAK,cAAc,QAAQ;AACjC,SAAO,MAAM,QAAQ,cAAc,QAAQ,YAAY,EAAE,IAAI;AAC/D;AACA,IAAM,yBAAyB,UAAM,0BAAW,mBAAmB;AAEnE,IAAM,iBAAiB,CAAC,UAAU,mBAAmB;AACnD,SAAO,iBAAiB;AAC1B;AACA,IAAM,kBAAkB,CAAC,QAAQ,gBAAgB;AAC/C,SAAO,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,MAAM,SAAS;AACnD,QAAI,CAAC,KAAK,KAAK,IAAI;AACnB,SAAK,GAAG,IAAI,QAAQ;AACpB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,IAAM,eAAe,WAAS;AAC5B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,eAAe,cAAc,UAAa,cAAc,QAAQ,iBAAiB,UAAa,iBAAiB;AACrH,QAAM,CAAC,UAAU,WAAW,QAAI,wBAAS,oBAAoB,CAAC,CAAC;AAC/D,QAAM,CAAC,aAAa,cAAc,QAAI,wBAAS,uBAAuB,CAAC,CAAC;AACxE,QAAM,YAAY,eAAe,YAAY;AAC7C,QAAM,eAAe,eAAe,eAAe;AACnD,QAAM,mBAAe,2BAAY,YAAU;AACzC,QAAI,gBAAgB,UAAU;AAC5B,aAAO,SAAS,OAAO,SAAS,GAAG,YAAY;AAAA,IACjD;AACA,WAAO,YAAY,MAAM;AAAA,EAC3B,GAAG,CAAC,cAAc,UAAU,aAAa,cAAc,SAAS,CAAC;AACjE,QAAM,sBAAkB,2BAAY,YAAU;AAC5C,QAAI,gBAAgB,UAAU;AAC5B,aAAO,SAAS,WAAW,OAAO,YAAY,CAAC;AAAA,IACjD;AACA,WAAO,eAAe,MAAM;AAAA,EAC9B,GAAG,CAAC,cAAc,UAAU,gBAAgB,WAAW,YAAY,CAAC;AACpE,QAAM,qBAAiB,uBAAQ,OAAO;AAAA,IACpC,MAAM,OAAO,OAAO,SAAS,EAAE,OAAO,CAAC,MAAM,UAAU,QAAQ,MAAM,CAAC;AAAA,IACtE,SAAS,OAAO,OAAO,YAAY,EAAE,OAAO,CAAC,MAAM,UAAU,QAAQ,MAAM,CAAC;AAAA,EAC9E,IAAI,CAAC,WAAW,YAAY,CAAC;AAC7B,QAAM,kBAAc,uBAAQ,OAAO;AAAA,IACjC,MAAM,eAAe,eAAe,MAAM,gBAAgB;AAAA,IAC1D,SAAS,eAAe,eAAe,SAAS,eAAe;AAAA,EACjE,IAAI,CAAC,gBAAgB,kBAAkB,eAAe,CAAC;AACvD,QAAM,0BAAsB,uBAAQ,OAAO;AAAA,IACzC,MAAM,gBAAgB,WAAW,YAAY,IAAI;AAAA,IACjD,SAAS,gBAAgB,cAAc,YAAY,OAAO;AAAA,EAC5D,IAAI,CAAC,WAAW,cAAc,WAAW,CAAC;AAC1C,QAAM,oBAAgB,uBAAQ,MAAM;AAClC,UAAM,WAAW,OAAO,KAAK,SAAS;AACtC,UAAM,cAAc,OAAO,KAAK,YAAY;AAC5C,UAAM,OAAO,SAAS,OAAO,CAAC,MAAM,KAAK,UAAU;AACjD,WAAK,GAAG,IAAI;AACZ,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,UAAM,UAAU,YAAY,OAAO,CAAC,MAAM,KAAK,UAAU;AACvD,WAAK,GAAG,IAAI;AACZ,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,WAAW,YAAY,CAAC;AAC5B,QAAM,kBAAc,2BAAY,CAAC,OAAO,YAAY,UAAU;AAC5D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,qBAAqB,QAAQ,KAAK;AACxC,UAAM,iBAAiB;AACvB,iBAAa,WAAS;AACpB,YAAM,WAAW,UAAU,UAAU;AACrC,YAAM,iBAAiB,KAAK,UAAU,IAAI;AAC1C,YAAM,aAAa,KAAK,UAAU,IAAI;AACtC,YAAM,eAAe,SAAS,cAAc;AAC5C,YAAM,WAAW,SAAS,UAAU;AACpC,YAAM,aAAa,WAAW;AAC9B,YAAM,YAAY;AAAA,QAChB,GAAG;AAAA,MACL;AACA,gBAAU,QAAQ,IAAI,UAAU,QAAQ,IAAI;AAC5C,gBAAU,YAAY,IAAI,UAAU,YAAY,IAAI;AACpD,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,eAAe,WAAW,YAAY,CAAC;AAC3C,QAAM,qBAAiB,2BAAY,CAAC,SAAS,YAAY,UAAU;AACjE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,qBAAqB,UAAU,KAAK;AAC1C,UAAM,iBAAiB;AACvB,oBAAgB,WAAS;AACvB,YAAM,iBAAiB,QAAQ,UAAU,IAAI;AAC7C,YAAM,aAAa,QAAQ,UAAU,IAAI;AACzC,YAAM,WAAW,aAAa,UAAU;AACxC,YAAM,eAAe,YAAY,cAAc;AAC/C,YAAM,WAAW,YAAY,UAAU;AACvC,YAAM,aAAa,WAAW;AAC9B,YAAM,YAAY;AAAA,QAChB,GAAG;AAAA,MACL;AACA,gBAAU,QAAQ,IAAI,aAAa,QAAQ,IAAI;AAC/C,gBAAU,YAAY,IAAI,aAAa,YAAY,IAAI;AACvD,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,eAAe,cAAc,eAAe,CAAC;AACjD,QAAM,qBAAiB,2BAAY,CAAC,aAAa,aAAa;AAC5D,QAAI,UAAU;AACZ,aAAO,oBAAoB,QAAQ,WAAW;AAAA,IAChD;AACA,WAAO,aAAa,WAAW;AAAA,EACjC,GAAG,CAAC,cAAc,mBAAmB,CAAC;AACtC,QAAM,kBAAc,2BAAY,CAAC,aAAa,aAAa;AACzD,QAAI,UAAU;AACZ,aAAO,oBAAoB,KAAK,WAAW;AAAA,IAC7C;AACA,WAAO,UAAU,WAAW;AAAA,EAC9B,GAAG,CAAC,WAAW,mBAAmB,CAAC;AACnC,QAAM,6BAAyB,2BAAY,cAAY;AACrD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,UAAU;AACZ,aAAO,YAAY,IAAI,SAAO,GAAG,oBAAoB,QAAQ,GAAG,KAAK,EAAE,KAAK,GAAG;AAAA,IACjF;AACA,WAAO,YAAY,IAAI,SAAO,GAAG,aAAa,GAAG,KAAK,EAAE,KAAK,GAAG;AAAA,EAClE,GAAG,CAAC,eAAe,cAAc,mBAAmB,CAAC;AACrD,QAAM,0BAAsB,2BAAY,cAAY;AAClD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,UAAU;AACZ,aAAO,SAAS,IAAI,SAAO,GAAG,oBAAoB,KAAK,GAAG,KAAK,EAAE,KAAK,GAAG;AAAA,IAC3E;AACA,WAAO,SAAS,IAAI,SAAO,GAAG,UAAU,GAAG,KAAK,EAAE,KAAK,GAAG;AAAA,EAC5D,GAAG,CAAC,eAAe,WAAW,mBAAmB,CAAC;AAClD,QAAM,aAAa,MAAM,EAAE;AAC3B,QAAM,4BAA4B,uBAAuB;AACzD,QAAM,0BAAsB,uBAAQ,MAAM,aAAa;AAAA,IACrD;AAAA,IACA,aAAa;AAAA,MACX,GAAG,0BAA0B;AAAA,MAC7B,CAAC,UAAU,GAAG;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,IAAI,CAAC,GAAG,CAAC,YAAY,2BAA2B,UAAU,aAAa,aAAa,gBAAgB,aAAa,gBAAgB,kBAAkB,iBAAiB,WAAW,CAAC;AAChL,SAAO,cAAAD,QAAM,cAAc,oBAAoB,UAAU;AAAA,IACvD,OAAO;AAAA,EACT,GAAG,qCAAW;AAAA,IACZ,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE;AACJ;AACA,aAAa,cAAc;AAC3B,aAAa,YAAY;AAAA,EACvB,IAAI,mBAAAC,QAAU;AAAA,EACd,iBAAiB,mBAAAA,QAAU,OAAO;AAAA,EAClC,kBAAkB,mBAAAA,QAAU,OAAO;AAAA,EACnC,kBAAkB,mBAAAA,QAAU;AAAA,EAC5B,qBAAqB,mBAAAA,QAAU;AAAA,EAC/B,WAAW,mBAAAA,QAAU;AAAA,EACrB,cAAc,mBAAAA,QAAU;AAAA,EACxB,UAAU,mBAAAA,QAAU;AAAA,EACpB,UAAU,mBAAAA,QAAU;AACtB;AAEA,IAAM,kBAAc,6BAAc;AAAA,EAChC,OAAO,MAAM;AACf,CAAC;AACD,IAAM,iBAAiB,MAAM;AAC3B,aAAO,0BAAW,WAAW;AAC/B;AAEA,IAAM,0BAAsB,6BAAc;AAAA,EACxC,aAAa;AAAA,EACb,KAAK;AAAA,EACL,KAAK;AAAA,EACL,WAAW;AAAA,EACX,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AACT,CAAC;AACD,IAAM,yBAAyB,MAAM;AACnC,aAAO,0BAAW,mBAAmB;AACvC;AAEA,IAAM,4BAA4B;AAAA,EAChC,OAAO;AAAA,EACP,KAAK;AAAA,EACL,KAAK;AAAA,EACL,QAAQ;AACV;AACA,IAAM,yBAAyB;AAAA,EAC7B,OAAO;AAAA,EACP,KAAK;AAAA,EACL,KAAK;AAAA,EACL,QAAQ;AACV;AACA,IAAM,wBAAoB,0BAAW,CAAC,MAAM,QAAQ;AAlrBpD;AAmrBE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,sBAAsB,2BAA2B,UAAU;AACjE,QAAM,cAAc,eAAe;AACnC,QAAM,mBAAe,0BAAW,EAAY;AAC5C,QAAM,cAAc,YAAY,YAAY;AAC5C,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,QAAM,QAAQ,uBAAuB,WAAW,MAAM;AACtD,QAAM,mBAAe,sBAAO,IAAI;AAChC,QAAM,sBAAsB,0BAA0B,eAAe,KAAK;AAC1E,QAAM,cAAc,sBAAsB,oBAAoB,YAAY,uBAAuB,WAAW,CAAC,IAAI;AACjH,QAAM,QAAQ,QAAQ,2DAAqB,YAAY,WAAW,QAAQ,2DAAqB,eAAe,WAAW;AACzH,QAAM,YAAY,QAAQ,2DAAqB,YAAY,aAAa,2DAAqB,eAAe;AAC5G,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,YAAY;AAAA,IACd,aAAa;AAAA,IACb,WAAW,gBAAgB,WAAW,gBAAgB;AAAA,IACtD,KAAK,MAAM;AAAA,IACX,KAAK,MAAM;AAAA,IACX,KAAK,aAAa;AAAA,IAClB;AAAA,IACA;AAAA,IACA,UAAU,cAAY;AACpB,UAAI,OAAO;AACT,eAAO,2DAAqB,YAAY,gBAAgB,OAAO,WAAW,WAAW;AAAA,MACvF;AACA,aAAO,2DAAqB,eAAe,gBAAgB,SAAS,WAAW,WAAW;AAAA,IAC5F;AAAA,IACA,UAAU;AAAA,IACV;AAAA,EACF,CAAC;AACD,+BAAU,MAAM;AACd,QAAI,CAAC,YAAY,IAAI;AACnB,kBAAY,MAAM,oBAAoB,EAAE,EAAE;AAAA,IAC5C;AAAA,EACF,GAAG,CAAC,aAAa,mBAAmB,CAAC;AACrC,QAAM,YAAY,QAAQ,mBAAmB,OAAO,cAAc,GAAG,8BAA8B;AACnG,QAAM,iBAAiB,kBAAkB;AAAA,IACvC,iBAAiB,YAAY;AAAA,IAC7B,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,OAAO,SAAQ,kBAAa,YAAb,mBAAsB,eAAc,kBAAa,YAAb,mBAAsB;AAC/E,QAAM,kBAAc,uBAAQ,MAAM,qBAAqB,MAAM,aAAa,WAAS,aAAa,MAAM,WAAW,aAAa,OAAO,CAAC,GAAG,CAAC,MAAM,aAAa,YAAY,CAAC;AAC1K,SAAO,cAAAD,QAAM,cAAc,oBAAoB,UAAU;AAAA,IACvD,WAAO,uBAAQ,OAAO;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,IACF,IAAI,CAAC,aAAa,WAAW,KAAK,KAAK,WAAW,MAAM,KAAK,CAAC;AAAA,EAChE,GAAG,cAAAA,QAAM,cAAc,oBAAoB,SAAS;AAAA,IAClD;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,gBAAgB,OAAO;AAAA,IACxB;AAAA,IACA,KAAK,6BAAU,CAAC,cAAc,GAAG,CAAC;AAAA,EACpC,CAAC,CAAC,CAAC;AACL,CAAC;AACD,kBAAkB,cAAc;AAChC,kBAAkB,YAAY;AAAA,EAC5B,WAAW,mBAAAC,QAAU,OAAO;AAAA,EAC5B,KAAK,mBAAAA,QAAU,OAAO;AAAA,EACtB,KAAK,mBAAAA,QAAU,OAAO;AAAA,EACtB,aAAa,mBAAAA,QAAU,MAAM,WAAW;AAAA,EACxC,SAAS,mBAAAA,QAAU;AACrB;AACA,kBAAkB,eAAe;AAAA,EAC/B,aAAa;AACf;AACA,IAAM,WAAW;AAEjB,IAAM,uBAAmB,0BAAW,CAAC,OAAO,QAAQ;AAClD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAD,QAAM,cAAc,mBAAmB,SAAS;AAAA,IACrD,QAAQ,CAAC;AAAA,IACT;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,UAAU;AAEhB,IAAM,8BAA0B,0BAAW,CAAC,OAAO,QAAQ;AACzD,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,EACb,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,uBAAuB;AAC3B,QAAM,sBAAsB,2BAA2B,UAAU;AACjE,QAAM,QAAQ,gBAAgB;AAC9B,QAAM,UAAU,gBAAgB;AAChC,QAAM,QAAQ,aAAa;AAC3B,MAAI,YAAY;AAChB,MAAI,CAAC,kBAAkB;AACrB,QAAI,OAAO;AACT,kBAAY;AAAA,IACd,OAAO;AACL,kBAAY;AAAA,IACd;AAAA,EACF;AACA,QAAM,eAAW,2BAAY,WAAS;AACpC,QAAI,OAAO;AACT,0BAAoB,YAAY,OAAO,WAAW,KAAK;AAAA,IACzD,OAAO;AACL,0BAAoB,eAAe,SAAS,WAAW,KAAK;AAAA,IAC9D;AAAA,EACF,GAAG,CAAC,OAAO,OAAO,SAAS,WAAW,mBAAmB,CAAC;AAC1D,QAAM,UAAU,qBAAqB,MAAM,SAAS,MAAM;AACxD,QAAI,OAAO;AACT,eAAS,GAAG;AAAA,IACd,OAAO;AACL,eAAS,GAAG;AAAA,IACd;AAAA,EACF,CAAC;AACD,QAAM,YAAY;AAAA,IAAqB,MAAM;AAAA,IAAW,WAAS,MAAM,gBAAgB;AAAA,EACvF;AACA,QAAM,cAAc;AAAA,IAAqB,MAAM;AAAA,IAAa,WAAS,MAAM,gBAAgB;AAAA,EAC3F;AACA,SAAO,cAAAA,QAAM,cAAc,SAAS;AAAA,IAClC,SAAS;AAAA,IACT,OAAO;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,aAAa,OAAK,EAAE,gBAAgB;AAAA,EACtC,GAAG,cAAAA,QAAM,cAAc,0BAA0B,SAAS;AAAA,IACxD,cAAc;AAAA,EAChB,GAAG,OAAO;AAAA,IACR;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX,cAAc,QAAQ;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,wBAAwB,cAAc;AACtC,IAAM,iBAAiB;AAEvB,IAAM,oBAAgB,0BAAW,CAAC,MAAM,QAAQ;AAC9C,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,CAAC,QAAQ,SAAS,QAAI,wBAAS;AACrC,QAAM,kBAAc,sBAAO,IAAI;AAC/B,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,IAAI,kBAAkB;AAAA,IACpB,KAAK;AAAA,EACP,CAAC;AACD,QAAM,gBAAY,uBAAQ,MAAM,YAAY,UAAU,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,MAAM,CAAC;AACrG,QAAM,kBAAc,uBAAQ,OAAO;AAAA,IACjC;AAAA,IACA,IAAI;AAAA,IACJ,OAAO,QAAM,UAAU,EAAE;AAAA,EAC3B,IAAI,CAAC,QAAQ,SAAS,CAAC;AACvB,SAAO,cAAAA,QAAM,cAAc,YAAY,UAAU;AAAA,IAC/C,OAAO;AAAA,EACT,GAAG,cAAAA,QAAM,cAAc,YAAY,SAAS;AAAA,IAC1C,IAAI;AAAA,IACJ,KAAK,6BAAU,CAAC,KAAK,WAAW,CAAC;AAAA,EACnC,GAAG,KAAK,GAAG,QAAQ,CAAC;AACtB,CAAC;AACD,cAAc,cAAc;AAC5B,IAAM,OAAO;AACb,KAAK,UAAU;AACf,KAAK,WAAW;AAChB,KAAK,iBAAiB;", "names": ["import_react", "import_prop_types", "refOrElement", "import_react", "React", "PropTypes", "React", "PropTypes"]}