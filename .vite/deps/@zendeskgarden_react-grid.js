import {
  Tooltip
} from "./chunk-RFVHSKWS.js";
import "./chunk-B7B3EFFH.js";
import "./chunk-MLNIKOSA.js";
import "./chunk-NWUESYL6.js";
import {
  react_merge_refs_esm_default
} from "./chunk-JLWORYXM.js";
import "./chunk-JQPVIOLG.js";
import "./chunk-PSGUSLG5.js";
import {
  ChevronButton
} from "./chunk-V3K7YCP3.js";
import "./chunk-YKXCMXPN.js";
import {
  DEFAULT_THEME,
  getColor,
  math,
  retrieveComponentStyles,
  rgba,
  stripUnit,
  useDocument,
  useText
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import {
  KEYS,
  composeEventHandlers,
  useId
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  A<PERSON>,
  Me,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import "./chunk-JHQZW6XF.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-grid/dist/index.esm.js
var import_react3 = __toESM(require_react());
var import_prop_types2 = __toESM(require_prop_types());

// node_modules/use-resize-observer/dist/bundle.esm.js
var import_react = __toESM(require_react());
function useResolvedElement(subscriber, refOrElement) {
  var lastReportRef = (0, import_react.useRef)(null);
  var refOrElementRef = (0, import_react.useRef)(null);
  refOrElementRef.current = refOrElement;
  var cbElementRef = (0, import_react.useRef)(null);
  (0, import_react.useEffect)(function() {
    evaluateSubscription();
  });
  var evaluateSubscription = (0, import_react.useCallback)(function() {
    var cbElement = cbElementRef.current;
    var refOrElement2 = refOrElementRef.current;
    var element = cbElement ? cbElement : refOrElement2 ? refOrElement2 instanceof Element ? refOrElement2 : refOrElement2.current : null;
    if (lastReportRef.current && lastReportRef.current.element === element && lastReportRef.current.subscriber === subscriber) {
      return;
    }
    if (lastReportRef.current && lastReportRef.current.cleanup) {
      lastReportRef.current.cleanup();
    }
    lastReportRef.current = {
      element,
      subscriber,
      // Only calling the subscriber, if there's an actual element to report.
      // Setting cleanup to undefined unless a subscriber returns one, as an existing cleanup function would've been just called.
      cleanup: element ? subscriber(element) : void 0
    };
  }, [subscriber]);
  (0, import_react.useEffect)(function() {
    return function() {
      if (lastReportRef.current && lastReportRef.current.cleanup) {
        lastReportRef.current.cleanup();
        lastReportRef.current = null;
      }
    };
  }, []);
  return (0, import_react.useCallback)(function(element) {
    cbElementRef.current = element;
    evaluateSubscription();
  }, [evaluateSubscription]);
}
function extractSize(entry, boxProp, sizeType) {
  if (!entry[boxProp]) {
    if (boxProp === "contentBoxSize") {
      return entry.contentRect[sizeType === "inlineSize" ? "width" : "height"];
    }
    return void 0;
  }
  return entry[boxProp][0] ? entry[boxProp][0][sizeType] : (
    // TS complains about this, because the RO entry type follows the spec and does not reflect Firefox's current
    // behaviour of returning objects instead of arrays for `borderBoxSize` and `contentBoxSize`.
    // @ts-ignore
    entry[boxProp][sizeType]
  );
}
function useResizeObserver(opts) {
  if (opts === void 0) {
    opts = {};
  }
  var onResize = opts.onResize;
  var onResizeRef = (0, import_react.useRef)(void 0);
  onResizeRef.current = onResize;
  var round = opts.round || Math.round;
  var resizeObserverRef = (0, import_react.useRef)();
  var _useState = (0, import_react.useState)({
    width: void 0,
    height: void 0
  }), size = _useState[0], setSize = _useState[1];
  var didUnmount = (0, import_react.useRef)(false);
  (0, import_react.useEffect)(function() {
    didUnmount.current = false;
    return function() {
      didUnmount.current = true;
    };
  }, []);
  var previous = (0, import_react.useRef)({
    width: void 0,
    height: void 0
  });
  var refCallback = useResolvedElement((0, import_react.useCallback)(function(element) {
    if (!resizeObserverRef.current || resizeObserverRef.current.box !== opts.box || resizeObserverRef.current.round !== round) {
      resizeObserverRef.current = {
        box: opts.box,
        round,
        instance: new ResizeObserver(function(entries) {
          var entry = entries[0];
          var boxProp = opts.box === "border-box" ? "borderBoxSize" : opts.box === "device-pixel-content-box" ? "devicePixelContentBoxSize" : "contentBoxSize";
          var reportedWidth = extractSize(entry, boxProp, "inlineSize");
          var reportedHeight = extractSize(entry, boxProp, "blockSize");
          var newWidth = reportedWidth ? round(reportedWidth) : void 0;
          var newHeight = reportedHeight ? round(reportedHeight) : void 0;
          if (previous.current.width !== newWidth || previous.current.height !== newHeight) {
            var newSize = {
              width: newWidth,
              height: newHeight
            };
            previous.current.width = newWidth;
            previous.current.height = newHeight;
            if (onResizeRef.current) {
              onResizeRef.current(newSize);
            } else {
              if (!didUnmount.current) {
                setSize(newSize);
              }
            }
          }
        })
      };
    }
    resizeObserverRef.current.instance.observe(element, {
      box: opts.box
    });
    return function() {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.instance.unobserve(element);
      }
    };
  }, [opts.box, round]), opts.ref);
  return (0, import_react.useMemo)(function() {
    return {
      ref: refCallback,
      width: size.width,
      height: size.height
    };
  }, [refCallback, size.width, size.height]);
}

// node_modules/@zendeskgarden/container-splitter/dist/index.esm.js
var import_react2 = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var KEYBOARD_STEP = 48;
var normalizePointerToSeparator = function(bodyPadding, pointerPosition, separatorHeightOrWidth, viewportWidthOrHeight) {
  if (separatorHeightOrWidth === void 0) {
    separatorHeightOrWidth = 0;
  }
  if (viewportWidthOrHeight === void 0) {
    viewportWidthOrHeight = 0;
  }
  if (viewportWidthOrHeight === 0) {
    return pointerPosition - bodyPadding - Math.floor(separatorHeightOrWidth / 2);
  }
  return viewportWidthOrHeight - pointerPosition - bodyPadding - Math.floor(separatorHeightOrWidth / 2);
};
var xor = (a, b) => {
  if (a && b) {
    return false;
  }
  return a || b;
};
var useSplitter = (_ref) => {
  let {
    idPrefix,
    environment,
    isFixed,
    min,
    max,
    orientation = "vertical",
    keyboardStep = KEYBOARD_STEP,
    defaultValueNow = min,
    valueNow,
    onChange = () => void 0,
    separatorRef,
    isLeading,
    rtl
  } = _ref;
  const prefix = useId(idPrefix);
  const primaryPaneId = `${prefix}--primary-pane`;
  const isControlled = valueNow !== void 0 && valueNow !== null;
  const [state, setState] = (0, import_react2.useState)(defaultValueNow);
  const [separatorElement, setSeparatorElement] = (0, import_react2.useState)(separatorRef.current);
  const offsetRef = (0, import_react2.useRef)({
    left: 0,
    right: 0,
    top: 0,
    bottom: 0
  });
  const separatorPosition = isControlled ? valueNow : state;
  const [lastPosition, setLastPosition] = (0, import_react2.useState)(separatorPosition);
  const doc = environment || document;
  (0, import_react2.useEffect)(() => {
    if (separatorRef.current !== separatorElement) {
      setSeparatorElement(separatorRef.current);
    }
  });
  const setSeparatorPosition = isControlled ? onChange : setState;
  const setRangedSeparatorPosition = (0, import_react2.useCallback)((nextDimension) => {
    if (nextDimension >= max) {
      setSeparatorPosition(max);
    } else if (nextDimension <= min) {
      setSeparatorPosition(min);
    } else {
      setSeparatorPosition(nextDimension);
    }
  }, [max, min, setSeparatorPosition]);
  const move = (0, import_react2.useCallback)((pageX, pageY) => {
    if (separatorElement) {
      const clientWidth = xor(rtl, isLeading) ? doc.body.clientWidth : void 0;
      const clientHeight = isLeading ? doc.body.clientHeight : void 0;
      if (orientation === "horizontal") {
        const offset = isLeading ? offsetRef.current.bottom : offsetRef.current.top;
        setRangedSeparatorPosition(
          normalizePointerToSeparator(offset, pageY, separatorElement.offsetHeight, clientHeight)
        );
      } else {
        const offset = xor(rtl, isLeading) ? offsetRef.current.right : offsetRef.current.left;
        setRangedSeparatorPosition(
          normalizePointerToSeparator(offset, pageX, separatorElement.offsetWidth, clientWidth)
        );
      }
    }
  }, [doc, isLeading, orientation, rtl, separatorElement, setRangedSeparatorPosition]);
  const getSeparatorProps = (0, import_react2.useCallback)((_ref2) => {
    let {
      role = "separator",
      onMouseDown,
      onTouchStart,
      onKeyDown,
      onClick,
      ...other
    } = _ref2;
    const onMouseMove = (event) => {
      move(event.pageX, event.pageY);
    };
    const onTouchMove = (event) => {
      const {
        pageY,
        pageX
      } = event.targetTouches[0];
      move(pageX, pageY);
    };
    const onMouseUp = () => {
      doc.removeEventListener("mouseup", onMouseUp);
      doc.removeEventListener("mousemove", onMouseMove);
    };
    const onTouchEnd = () => {
      doc.removeEventListener("touchend", onTouchEnd);
      doc.removeEventListener("touchmove", onTouchMove);
    };
    const updateOffsets = () => {
      if (separatorElement) {
        const rect = separatorElement.getBoundingClientRect();
        const clientWidth = doc.body.clientWidth;
        const clientHeight = doc.body.clientHeight;
        const win = doc.documentElement || doc.body.parentNode || doc.body;
        offsetRef.current.left = rect.left - separatorPosition + win.scrollLeft;
        offsetRef.current.right = clientWidth - rect.right - separatorPosition - win.scrollLeft;
        offsetRef.current.top = rect.top - separatorPosition + win.scrollTop;
        offsetRef.current.bottom = clientHeight - rect.bottom - separatorPosition - win.scrollTop;
      }
    };
    const handleMouseDown = () => {
      if (!isFixed) {
        updateOffsets();
        doc.addEventListener("mouseup", onMouseUp);
        doc.addEventListener("mousemove", onMouseMove);
      }
    };
    const handleTouchStart = () => {
      if (!isFixed) {
        updateOffsets();
        doc.addEventListener("touchend", onTouchEnd);
        doc.addEventListener("touchmove", onTouchMove);
      }
    };
    const handleKeyDown = (event) => {
      if (event.key === KEYS.ENTER) {
        if (separatorPosition === min) {
          setSeparatorPosition(lastPosition === min ? max : lastPosition);
        } else {
          setLastPosition(separatorPosition);
          setSeparatorPosition(min);
        }
      } else if (event.key === KEYS.HOME) {
        separatorPosition !== min && setLastPosition(separatorPosition);
        setSeparatorPosition(min);
      } else if (event.key === KEYS.END) {
        setSeparatorPosition(max);
      } else if (!isFixed) {
        if (event.key === KEYS.RIGHT && orientation === "vertical") {
          let position;
          if (rtl) {
            position = separatorPosition + (isLeading ? keyboardStep : -keyboardStep);
          } else {
            position = separatorPosition + (isLeading ? -keyboardStep : keyboardStep);
          }
          setRangedSeparatorPosition(position);
          event.preventDefault();
        } else if (event.key === KEYS.LEFT && orientation === "vertical") {
          let position;
          if (rtl) {
            position = separatorPosition + (isLeading ? -keyboardStep : keyboardStep);
          } else {
            position = separatorPosition + (isLeading ? keyboardStep : -keyboardStep);
          }
          setRangedSeparatorPosition(position);
          event.preventDefault();
        } else if (event.key === KEYS.UP && orientation === "horizontal") {
          setRangedSeparatorPosition(separatorPosition + (isLeading ? keyboardStep : -keyboardStep));
          event.preventDefault();
        } else if (event.key === KEYS.DOWN && orientation === "horizontal") {
          setRangedSeparatorPosition(separatorPosition + (isLeading ? -keyboardStep : keyboardStep));
          event.preventDefault();
        }
      }
    };
    const handleClick = (event) => {
      if (isFixed || event.detail === 2) {
        handleKeyDown({
          key: KEYS.ENTER
        });
      }
    };
    const ariaValueNow = (separatorPosition - min) / (max - min) * 100;
    const ariaValueMin = isFinite(ariaValueNow) ? 0 : min;
    const ariaValueMax = isFinite(ariaValueNow) ? 100 : max;
    return {
      role: role === null ? void 0 : role,
      onMouseDown: composeEventHandlers(onMouseDown, handleMouseDown),
      onTouchStart: composeEventHandlers(onTouchStart, handleTouchStart),
      onKeyDown: composeEventHandlers(onKeyDown, handleKeyDown),
      onClick: composeEventHandlers(onClick, handleClick),
      "aria-controls": primaryPaneId,
      "aria-valuenow": isFinite(ariaValueNow) ? ariaValueNow : separatorPosition,
      "aria-valuemin": ariaValueMin,
      "aria-valuemax": ariaValueMax,
      "aria-orientation": orientation,
      "data-garden-container-id": "containers.splitter.separator",
      "data-garden-container-version": "2.0.7",
      tabIndex: 0,
      ...other
    };
  }, [doc, isFixed, isLeading, keyboardStep, lastPosition, max, min, move, orientation, primaryPaneId, rtl, separatorPosition, separatorElement, setRangedSeparatorPosition, setSeparatorPosition]);
  const getPrimaryPaneProps = (0, import_react2.useCallback)(function(other) {
    if (other === void 0) {
      other = {};
    }
    return {
      "data-garden-container-id": "containers.splitter.primaryPane",
      "data-garden-container-version": "2.0.7",
      id: primaryPaneId,
      ...other
    };
  }, [primaryPaneId]);
  return (0, import_react2.useMemo)(() => ({
    getSeparatorProps,
    getPrimaryPaneProps,
    valueNow: separatorPosition
  }), [getSeparatorProps, getPrimaryPaneProps, separatorPosition]);
};
var SplitterContainer = (_ref) => {
  let {
    children,
    render = children,
    ...options
  } = _ref;
  return import_react2.default.createElement(import_react2.default.Fragment, null, render(useSplitter(options)));
};
SplitterContainer.propTypes = {
  children: import_prop_types.default.func,
  render: import_prop_types.default.func,
  idPrefix: import_prop_types.default.string,
  environment: import_prop_types.default.any,
  isFixed: import_prop_types.default.bool,
  min: import_prop_types.default.number.isRequired,
  max: import_prop_types.default.number.isRequired,
  orientation: import_prop_types.default.oneOf(["horizontal", "vertical"]),
  keyboardStep: import_prop_types.default.number,
  defaultValueNow: import_prop_types.default.number,
  valueNow: import_prop_types.default.number,
  onChange: import_prop_types.default.func,
  separatorRef: import_prop_types.default.any.isRequired,
  isLeading: import_prop_types.default.bool,
  rtl: import_prop_types.default.bool
};
SplitterContainer.defaultProps = {
  keyboardStep: KEYBOARD_STEP,
  orientation: "vertical"
};

// node_modules/@zendeskgarden/react-grid/dist/index.esm.js
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var ALIGN_ITEMS = ["start", "end", "center", "baseline", "stretch"];
var ALIGN_SELF = ["auto", ...ALIGN_ITEMS];
var DIRECTION = ["row", "row-reverse", "column", "column-reverse"];
var JUSTIFY_CONTENT = ["start", "end", "center", "between", "around"];
var TEXT_ALIGN = ["start", "end", "center", "justify"];
var SPACE = [false, "xxs", "xs", "sm", "md", "lg", "xl", "xxl"];
var WRAP = ["nowrap", "wrap", "wrap-reverse"];
var ORIENTATION = ["top", "bottom", "start", "end"];
var COMPONENT_ID$6 = "grid.col";
var colorStyles$4 = (props) => {
  const backgroundColor = getColor("primaryHue", 600, props.theme, 0.1);
  return Ae(["background-clip:content-box;background-color:", ";"], backgroundColor);
};
var flexStyles$1 = (size, alignSelf, textAlign, offset, order, props) => {
  const margin = offset && `${math(`${offset} / ${props.columns} * 100`)}%`;
  let flexBasis;
  let flexGrow;
  let maxWidth;
  let width;
  if (typeof size === "boolean") {
    flexBasis = 0;
    flexGrow = 1;
    maxWidth = "100%";
  } else if (size === "auto") {
    flexBasis = "auto";
    flexGrow = 0;
    maxWidth = "100%";
    width = "auto";
  } else if (size !== void 0) {
    flexBasis = `${math(`${size} / ${props.columns} * 100`)}%`;
    flexGrow = 0;
    maxWidth = flexBasis;
  }
  let horizontalAlign;
  if (textAlign === "start") {
    horizontalAlign = props.theme.rtl ? "right" : "left";
  } else if (textAlign === "end") {
    horizontalAlign = props.theme.rtl ? "left" : "right";
  } else {
    horizontalAlign = textAlign;
  }
  let flexOrder;
  if (order === "first") {
    flexOrder = -1;
  } else if (order === "last") {
    flexOrder = math(`${props.columns} + 1`);
  } else {
    flexOrder = order;
  }
  return Ae(["flex-basis:", ";flex-grow:", ";flex-shrink:", ";align-self:", ";order:", ";margin-", ":", ";width:", ";max-width:", ";text-align:", ";"], flexBasis, flexGrow, size && 0, alignSelf === "start" || alignSelf === "end" ? `flex-${alignSelf}` : alignSelf, flexOrder, props.theme.rtl ? "right" : "left", margin, width, maxWidth, horizontalAlign);
};
var mediaStyles$1 = (minWidth, size, alignSelf, textAlign, offset, order, props) => {
  return Ae(["@media (min-width:", "){", ";}"], minWidth, flexStyles$1(size, alignSelf, textAlign, offset, order, props));
};
var sizeStyles$4 = (props) => {
  const padding = props.gutters ? math(`${props.theme.space[props.gutters]} / 2`) : 0;
  return Ae(["padding-right:", ";padding-left:", ";"], padding, padding);
};
var StyledCol = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$6,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledCol",
  componentId: "sc-inuw62-0"
})(["box-sizing:border-box;width:100%;", ";", ";", ";", ";", ";", ";", ";", ";", ";"], (props) => flexStyles$1(!props.sizeAll && (props.xs || props.sm || props.md || props.lg || props.xl) ? void 0 : props.sizeAll || false, props.alignSelf, props.textAlign, props.offset, props.order, props), (props) => sizeStyles$4(props), (props) => props.debug && colorStyles$4(props), (props) => mediaStyles$1(props.theme.breakpoints.xs, props.xs, props.alignSelfXs, props.textAlignXs, props.offsetXs, props.orderXs, props), (props) => mediaStyles$1(props.theme.breakpoints.sm, props.sm, props.alignSelfSm, props.textAlignSm, props.offsetSm, props.orderSm, props), (props) => mediaStyles$1(props.theme.breakpoints.md, props.md, props.alignSelfMd, props.textAlignMd, props.offsetMd, props.orderMd, props), (props) => mediaStyles$1(props.theme.breakpoints.lg, props.lg, props.alignSelfLg, props.textAlignLg, props.offsetLg, props.orderLg, props), (props) => mediaStyles$1(props.theme.breakpoints.xl, props.xl, props.alignSelfXl, props.textAlignXl, props.offsetXl, props.orderXl, props), (props) => retrieveComponentStyles(COMPONENT_ID$6, props));
StyledCol.defaultProps = {
  columns: 12,
  theme: DEFAULT_THEME
};
var COMPONENT_ID$5 = "grid.grid";
var colorStyles$3 = (props) => {
  const borderColor = getColor(props.theme.palette.crimson, 400, props.theme, 0.5);
  const borderWidth = math(`${props.theme.borderWidths.sm} * 2`);
  return Ae(["box-shadow:-", " 0 0 0 ", ",", " 0 0 0 ", ";"], borderWidth, borderColor, borderWidth, borderColor);
};
var sizeStyles$3 = (props) => {
  const padding = props.gutters ? math(`${props.theme.space[props.gutters]} / 2`) : 0;
  return Ae(["padding-right:", ";padding-left:", ";"], padding, padding);
};
var StyledGrid = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$5,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledGrid",
  componentId: "sc-oxgg5i-0"
})(["direction:", ";margin-right:auto;margin-left:auto;width:100%;box-sizing:border-box;", ";", ";", ";"], (props) => props.theme.rtl && "rtl", (props) => sizeStyles$3(props), (props) => props.debug && colorStyles$3(props), (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledGrid.defaultProps = {
  gutters: "md",
  theme: DEFAULT_THEME
};
var COMPONENT_ID$4 = "grid.row";
var colorStyles$2 = (props) => {
  const borderColor = getColor(props.theme.palette.mint, 600, props.theme, 0.5);
  const borderWidth = props.theme.borderWidths.sm;
  return Ae(["box-shadow:inset 0 ", " 0 0 ", ",inset 0 -", " 0 0 ", ";"], borderWidth, borderColor, borderWidth, borderColor);
};
var flexStyles = (alignItems, justifyContent, wrap) => {
  let flexAlignItems;
  let flexJustifyContent;
  if (alignItems === "start" || alignItems === "end") {
    flexAlignItems = `flex-${alignItems}`;
  } else {
    flexAlignItems = alignItems;
  }
  if (justifyContent === "start" || justifyContent === "end") {
    flexJustifyContent = `flex-${justifyContent}`;
  } else if (justifyContent === "between" || justifyContent === "around") {
    flexJustifyContent = `space-${justifyContent}`;
  } else {
    flexJustifyContent = justifyContent;
  }
  return Ae(["flex-wrap:", ";align-items:", ";justify-content:", ";"], wrap, flexAlignItems, flexJustifyContent);
};
var mediaStyles = (minWidth, alignItems, justifyContent, wrap) => {
  return Ae(["@media (min-width:", "){", ";}"], minWidth, flexStyles(alignItems, justifyContent, wrap));
};
var sizeStyles$2 = (props) => {
  const margin = props.gutters ? math(`${props.theme.space[props.gutters]} / 2`) : 0;
  return Ae(["margin-right:-", ";margin-left:-", ";"], margin, margin);
};
var StyledRow = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$4,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledRow",
  componentId: "sc-xjsdg1-0"
})(["display:flex;box-sizing:border-box;", " ", ";", ";", ";", ";", ";", ";", ";", ";"], (props) => flexStyles(props.alignItems, props.justifyContent, props.wrapAll), (props) => sizeStyles$2(props), (props) => props.debug && colorStyles$2(props), (props) => mediaStyles(props.theme.breakpoints.xs, props.alignItemsXs, props.justifyContentXs, props.wrapXs), (props) => mediaStyles(props.theme.breakpoints.sm, props.alignItemsSm, props.justifyContentSm, props.wrapSm), (props) => mediaStyles(props.theme.breakpoints.md, props.alignItemsMd, props.justifyContentMd, props.wrapMd), (props) => mediaStyles(props.theme.breakpoints.lg, props.alignItemsLg, props.justifyContentLg, props.wrapLg), (props) => mediaStyles(props.theme.breakpoints.xl, props.alignItemsXl, props.justifyContentXl, props.wrapXl), (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledRow.defaultProps = {
  wrapAll: "wrap",
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "pane";
var StyledPane = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$3,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledPane",
  componentId: "sc-1ltjst7-0"
})(["position:relative;min-width:0;min-height:0;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledPane.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "pane.content";
var StyledPaneContent = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledPaneContent",
  componentId: "sc-1b38mbh-0"
})(["height:100%;overflow:auto;&[hidden]{display:none;}", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledPaneContent.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "pane.splitter";
var colorStyles$1 = (props) => {
  const color = getColor("neutralHue", 300, props.theme);
  const hoverColor = getColor("primaryHue", 600, props.theme);
  const activeColor = getColor("primaryHue", 800, props.theme);
  const boxShadow = props.theme.shadows.md(rgba(hoverColor, 0.35));
  return Ae(["&::before{background-color:", ";}&:hover::before{background-color:", ";}&[data-garden-focus-visible]::before{box-shadow:", ";background-color:", ";}&:active::before{background-color:", ";}"], color, props.isHovered && hoverColor, boxShadow, hoverColor, props.isHovered && activeColor);
};
var sizeStyles$1 = (props) => {
  const size = math(`${props.theme.shadowWidths.md} * 2`);
  const separatorSize = math(`${props.theme.borderWidths.sm} * 2`);
  const offset = math(`-${size} / 2`);
  let cursor;
  let top;
  let right;
  let left;
  let bottom;
  let width;
  let height;
  let separatorWidth;
  let separatorHeight;
  switch (props.orientation) {
    case "top":
      cursor = "row-resize";
      top = offset;
      width = "100%";
      height = size;
      separatorWidth = width;
      separatorHeight = props.theme.borderWidths.sm;
      break;
    case "bottom":
      cursor = "row-resize";
      bottom = offset;
      width = "100%";
      height = size;
      separatorWidth = width;
      separatorHeight = props.theme.borderWidths.sm;
      break;
    case "start":
      cursor = "col-resize";
      top = 0;
      width = size;
      height = "100%";
      separatorWidth = props.theme.borderWidths.sm;
      separatorHeight = height;
      if (props.theme.rtl) {
        right = offset;
      } else {
        left = offset;
      }
      break;
    case "end":
    default:
      cursor = "col-resize";
      top = 0;
      width = size;
      height = "100%";
      separatorWidth = props.theme.borderWidths.sm;
      separatorHeight = height;
      if (props.theme.rtl) {
        left = offset;
      } else {
        right = offset;
      }
      break;
  }
  const dimensionProperty = width === "100%" ? "height" : "width";
  return Ae(["top:", ";right:", ";bottom:", ";left:", ";cursor:", ";width:", ";height:", ";&::before{width:", ";height:", ";}&:hover::before{", ":", ";}&[data-garden-focus-visible]::before,&:focus::before{", ":", ";}&[data-garden-focus-visible]::before{border-radius:", ";}"], top, right, bottom, left, props.isFixed ? "pointer" : cursor, width, height, separatorWidth, separatorHeight, dimensionProperty, props.isHovered && separatorSize, dimensionProperty, separatorSize, props.theme.borderRadii.md);
};
var StyledPaneSplitter = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledPaneSplitter",
  componentId: "sc-jylemn-0"
})(["display:flex;position:absolute;align-items:center;justify-content:center;z-index:1;user-select:none;", ";&:focus{outline:none;}&::before{position:absolute;transition:box-shadow 0.1s ease-in-out,background-color 0.25s ease-in-out;z-index:-1;content:'';}", ";", ";"], sizeStyles$1, colorStyles$1, (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledPaneSplitter.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "pane.splitter_button";
var transformStyles = (props) => {
  let degrees = 0;
  if (props.isRotated) {
    degrees = props.theme.rtl ? -180 : 180;
  }
  if (props.orientation === "end") {
    degrees += props.theme.rtl ? -90 : 90;
  } else if (props.orientation === "start") {
    degrees += props.theme.rtl ? 90 : -90;
  } else if (props.orientation === "bottom") {
    degrees += 180;
  }
  return Ae(["& > svg{transform:rotate(", "deg);}"], degrees);
};
var colorStyles = (_ref) => {
  let {
    theme
  } = _ref;
  const boxShadow = theme.shadows.lg(`${theme.space.base}px`, `${theme.space.base * 2}px`, getColor("chromeHue", 600, theme, 0.15));
  const focusBoxShadow = theme.shadows.md(getColor("primaryHue", 600, theme, 0.35));
  return Ae(["box-shadow:", ";&[data-garden-focus-visible]{box-shadow:", ",", ";}"], boxShadow, focusBoxShadow, boxShadow);
};
var sizeStyles = (props) => {
  const size = `${props.theme.space.base * 6}px`;
  const display = props.splitterSize <= stripUnit(math(`${props.theme.shadowWidths.md} * 2 + ${size}`)) && "none";
  const isVertical = props.orientation === "start" || props.orientation === "end";
  let top;
  let left;
  let right;
  let bottom;
  if (props.splitterSize >= stripUnit(math(`${size} * 3`))) {
    if (props.placement === "start") {
      if (isVertical) {
        top = size;
      } else if (props.theme.rtl) {
        right = size;
      } else {
        left = size;
      }
    } else if (props.placement === "end") {
      if (isVertical) {
        bottom = size;
      } else if (props.theme.rtl) {
        left = size;
      } else {
        right = size;
      }
    }
  }
  return Ae(["display:", ";top:", ";right:", ";bottom:", ";left:", ";width:", ";min-width:", ";height:", ";"], display, top, right, bottom, left, size, size, size);
};
var StyledPaneSplitterButton = styled_components_browser_esm_default(ChevronButton).attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.67.0",
  isBasic: true,
  isPill: true,
  size: "small"
}).withConfig({
  displayName: "StyledPaneSplitterButton",
  componentId: "sc-zh032e-0"
})(["position:absolute;transition:background-color 0.25s ease-in-out,opacity 0.25s ease-in-out 0.1s;opacity:0;&[data-garden-focus-visible],", ":hover &,", "[data-garden-focus-visible] &{opacity:1;}", ";", ";", ";&::before{position:absolute;z-index:-1;background-color:", ";width:100%;height:100%;content:'';}", ";"], StyledPaneSplitter, StyledPaneSplitter, sizeStyles, transformStyles, colorStyles, (props) => props.theme.colors.background, (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledPaneSplitterButton.defaultProps = {
  theme: DEFAULT_THEME
};
var GridContext = (0, import_react3.createContext)({
  gutters: "md"
});
var useGridContext = () => {
  return (0, import_react3.useContext)(GridContext);
};
var Col = import_react3.default.forwardRef((_ref, ref) => {
  let {
    size,
    ...props
  } = _ref;
  const {
    columns,
    gutters,
    debug
  } = useGridContext();
  return import_react3.default.createElement(StyledCol, _extends({
    sizeAll: size,
    columns,
    gutters,
    debug,
    ref
  }, props));
});
Col.displayName = "Col";
Col.propTypes = {
  size: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  xs: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string, import_prop_types2.default.bool]),
  sm: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string, import_prop_types2.default.bool]),
  md: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string, import_prop_types2.default.bool]),
  lg: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string, import_prop_types2.default.bool]),
  xl: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string, import_prop_types2.default.bool]),
  alignSelf: import_prop_types2.default.oneOf(ALIGN_SELF),
  alignSelfXs: import_prop_types2.default.oneOf(ALIGN_SELF),
  alignSelfSm: import_prop_types2.default.oneOf(ALIGN_SELF),
  alignSelfMd: import_prop_types2.default.oneOf(ALIGN_SELF),
  alignSelfLg: import_prop_types2.default.oneOf(ALIGN_SELF),
  alignSelfXl: import_prop_types2.default.oneOf(ALIGN_SELF),
  textAlign: import_prop_types2.default.oneOf(TEXT_ALIGN),
  textAlignXs: import_prop_types2.default.oneOf(TEXT_ALIGN),
  textAlignSm: import_prop_types2.default.oneOf(TEXT_ALIGN),
  textAlignMd: import_prop_types2.default.oneOf(TEXT_ALIGN),
  textAlignLg: import_prop_types2.default.oneOf(TEXT_ALIGN),
  textAlignXl: import_prop_types2.default.oneOf(TEXT_ALIGN),
  offset: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  offsetXs: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  offsetSm: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  offsetMd: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  offsetLg: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  offsetXl: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  order: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  orderXs: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  orderSm: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  orderMd: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  orderLg: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  orderXl: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string])
};
var Grid = import_react3.default.forwardRef((_ref, ref) => {
  let {
    columns,
    debug,
    ...props
  } = _ref;
  const value = (0, import_react3.useMemo)(() => ({
    columns,
    gutters: props.gutters,
    debug
  }), [columns, props.gutters, debug]);
  return import_react3.default.createElement(GridContext.Provider, {
    value
  }, import_react3.default.createElement(StyledGrid, _extends({
    debug,
    ref
  }, props)));
});
Grid.displayName = "Grid";
Grid.propTypes = {
  columns: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  gutters: import_prop_types2.default.oneOf(SPACE),
  debug: import_prop_types2.default.bool
};
Grid.defaultProps = {
  columns: 12,
  gutters: "md"
};
var Row = import_react3.default.forwardRef((_ref, ref) => {
  let {
    wrap,
    ...props
  } = _ref;
  const {
    gutters,
    debug
  } = useGridContext();
  return import_react3.default.createElement(StyledRow, _extends({
    gutters,
    debug,
    wrapAll: wrap,
    ref
  }, props));
});
Row.displayName = "Row";
Row.propTypes = {
  alignItems: import_prop_types2.default.oneOf(ALIGN_ITEMS),
  alignItemsXs: import_prop_types2.default.oneOf(ALIGN_ITEMS),
  alignItemsSm: import_prop_types2.default.oneOf(ALIGN_ITEMS),
  alignItemsMd: import_prop_types2.default.oneOf(ALIGN_ITEMS),
  alignItemsLg: import_prop_types2.default.oneOf(ALIGN_ITEMS),
  alignItemsXl: import_prop_types2.default.oneOf(ALIGN_ITEMS),
  justifyContent: import_prop_types2.default.oneOf(JUSTIFY_CONTENT),
  justifyContentXs: import_prop_types2.default.oneOf(JUSTIFY_CONTENT),
  justifyContentSm: import_prop_types2.default.oneOf(JUSTIFY_CONTENT),
  justifyContentMd: import_prop_types2.default.oneOf(JUSTIFY_CONTENT),
  justifyContentLg: import_prop_types2.default.oneOf(JUSTIFY_CONTENT),
  justifyContentXl: import_prop_types2.default.oneOf(JUSTIFY_CONTENT),
  wrap: import_prop_types2.default.oneOf(WRAP),
  wrapXs: import_prop_types2.default.oneOf(WRAP),
  wrapSm: import_prop_types2.default.oneOf(WRAP),
  wrapMd: import_prop_types2.default.oneOf(WRAP),
  wrapLg: import_prop_types2.default.oneOf(WRAP),
  wrapXl: import_prop_types2.default.oneOf(WRAP)
};
var PaneProviderContext = (0, import_react3.createContext)({});
var usePaneProviderContextData = (providerId) => {
  const context = (0, import_react3.useContext)(PaneProviderContext);
  const id = providerId || context.providerId;
  return id && context.contextData ? context.contextData[id] : void 0;
};
var usePaneProviderContext = () => (0, import_react3.useContext)(PaneProviderContext);
var getPixelsPerFr = (totalFrs, totalDimension) => {
  return totalDimension / totalFrs;
};
var convertToPixels = (values, pixelsPerFr) => {
  return Object.entries(values).reduce((prev, _ref) => {
    let [key, value] = _ref;
    prev[key] = value * pixelsPerFr;
    return prev;
  }, {});
};
var PaneProvider = (_ref2) => {
  let {
    id,
    totalPanesWidth,
    totalPanesHeight,
    defaultRowValues,
    defaultColumnValues,
    rowValues,
    columnValues,
    onChange,
    children
  } = _ref2;
  const isControlled = rowValues !== void 0 && rowValues !== null && columnValues !== void 0 && columnValues !== null;
  const [rowState, setRowState] = (0, import_react3.useState)(defaultRowValues || {});
  const [columnState, setColumnState] = (0, import_react3.useState)(defaultColumnValues || {});
  const rowsTrack = isControlled ? rowValues : rowState;
  const columnsTrack = isControlled ? columnValues : columnState;
  const setRowsTrack = (0, import_react3.useCallback)((values) => {
    if (isControlled && onChange) {
      return onChange(values(rowsTrack), columnsTrack);
    }
    return setRowState(values);
  }, [isControlled, onChange, setRowState, columnsTrack, rowsTrack]);
  const setColumnsTrack = (0, import_react3.useCallback)((values) => {
    if (isControlled && onChange) {
      return onChange(rowsTrack, values(columnsTrack));
    }
    return setColumnState(values);
  }, [isControlled, onChange, setColumnState, rowsTrack, columnsTrack]);
  const totalFractions = (0, import_react3.useMemo)(() => ({
    rows: Object.values(rowsTrack).reduce((prev, value) => value + prev, 0),
    columns: Object.values(columnsTrack).reduce((prev, value) => value + prev, 0)
  }), [rowsTrack, columnsTrack]);
  const pixelsPerFr = (0, import_react3.useMemo)(() => ({
    rows: getPixelsPerFr(totalFractions.rows, totalPanesHeight),
    columns: getPixelsPerFr(totalFractions.columns, totalPanesWidth)
  }), [totalFractions, totalPanesHeight, totalPanesWidth]);
  const layoutStateInPixels = (0, import_react3.useMemo)(() => ({
    rows: convertToPixels(rowsTrack, pixelsPerFr.rows),
    columns: convertToPixels(columnsTrack, pixelsPerFr.columns)
  }), [rowsTrack, columnsTrack, pixelsPerFr]);
  const layoutIndices = (0, import_react3.useMemo)(() => {
    const rowArray = Object.keys(rowsTrack);
    const columnArray = Object.keys(columnsTrack);
    const rows = rowArray.reduce((prev, key, index) => {
      prev[key] = index;
      return prev;
    }, {});
    const columns = columnArray.reduce((prev, key, index) => {
      prev[key] = index;
      return prev;
    }, {});
    return {
      rows,
      columns,
      rowArray,
      columnArray
    };
  }, [rowsTrack, columnsTrack]);
  const setRowValue = (0, import_react3.useCallback)((isTop, splitterId, value) => {
    const {
      rows,
      rowArray
    } = layoutIndices;
    const stealFromTraversal = isTop ? -1 : 1;
    const addToTraversal = 0;
    setRowsTrack((state) => {
      const oldValue = rowsTrack[splitterId];
      const stealFromIndex = rows[splitterId] + stealFromTraversal;
      const addToIndex = rows[splitterId] + addToTraversal;
      const stealFromKey = rowArray[stealFromIndex];
      const addToKey = rowArray[addToIndex];
      const difference = oldValue - value;
      const nextState = {
        ...state
      };
      nextState[addToKey] = rowsTrack[addToKey] - difference;
      nextState[stealFromKey] = rowsTrack[stealFromKey] + difference;
      return nextState;
    });
  }, [layoutIndices, rowsTrack, setRowsTrack]);
  const setColumnValue = (0, import_react3.useCallback)((isStart, splitterId, value) => {
    const {
      columns,
      columnArray
    } = layoutIndices;
    const stealFromTraversal = isStart ? -1 : 1;
    const addToTraversal = 0;
    setColumnsTrack((state) => {
      const stealFromIndex = columns[splitterId] + stealFromTraversal;
      const addToIndex = columns[splitterId] + addToTraversal;
      const oldValue = columnsTrack[splitterId];
      const stealFromKey = columnArray[stealFromIndex];
      const addToKey = columnArray[addToIndex];
      const difference = oldValue - value;
      const nextState = {
        ...state
      };
      nextState[addToKey] = columnsTrack[addToKey] - difference;
      nextState[stealFromKey] = columnsTrack[stealFromKey] + difference;
      return nextState;
    });
  }, [layoutIndices, columnsTrack, setColumnsTrack]);
  const getColumnValue = (0, import_react3.useCallback)((splitterKey, isPixels) => {
    if (isPixels) {
      return layoutStateInPixels.columns[splitterKey];
    }
    return columnsTrack[splitterKey];
  }, [columnsTrack, layoutStateInPixels]);
  const getRowValue = (0, import_react3.useCallback)((splitterKey, isPixels) => {
    if (isPixels) {
      return layoutStateInPixels.rows[splitterKey];
    }
    return rowsTrack[splitterKey];
  }, [rowsTrack, layoutStateInPixels]);
  const getGridTemplateColumns = (0, import_react3.useCallback)((isPixels) => {
    const {
      columnArray
    } = layoutIndices;
    if (isPixels) {
      return columnArray.map((col) => `${layoutStateInPixels.columns[col]}px`).join(" ");
    }
    return columnArray.map((col) => `${columnsTrack[col]}fr`).join(" ");
  }, [layoutIndices, columnsTrack, layoutStateInPixels]);
  const getGridTemplateRows = (0, import_react3.useCallback)((isPixels) => {
    const {
      rowArray
    } = layoutIndices;
    if (isPixels) {
      return rowArray.map((row) => `${layoutStateInPixels.rows[row]}px`).join(" ");
    }
    return rowArray.map((row) => `${rowsTrack[row]}fr`).join(" ");
  }, [layoutIndices, rowsTrack, layoutStateInPixels]);
  const providerId = useId(id);
  const parentPaneProviderContext = usePaneProviderContext();
  const paneProviderContext = (0, import_react3.useMemo)(() => providerId ? {
    providerId,
    contextData: {
      ...parentPaneProviderContext.contextData,
      [providerId]: {
        columnState,
        rowState,
        setRowValue,
        setColumnValue,
        getRowValue,
        getColumnValue,
        totalPanesHeight,
        totalPanesWidth,
        pixelsPerFr
      }
    }
  } : {}, [providerId, parentPaneProviderContext, rowState, columnState, setRowValue, setColumnValue, getRowValue, getColumnValue, totalPanesHeight, totalPanesWidth, pixelsPerFr]);
  return import_react3.default.createElement(PaneProviderContext.Provider, {
    value: paneProviderContext
  }, children == null ? void 0 : children({
    id: providerId,
    getRowValue,
    getColumnValue,
    getGridTemplateColumns,
    getGridTemplateRows
  }));
};
PaneProvider.displayName = "PaneProvider";
PaneProvider.propTypes = {
  id: import_prop_types2.default.string,
  totalPanesWidth: import_prop_types2.default.number.isRequired,
  totalPanesHeight: import_prop_types2.default.number.isRequired,
  defaultRowValues: import_prop_types2.default.object,
  defaultColumnValues: import_prop_types2.default.object,
  rowValues: import_prop_types2.default.object,
  columnValues: import_prop_types2.default.object,
  onChange: import_prop_types2.default.func,
  children: import_prop_types2.default.func
};
var PaneContext = (0, import_react3.createContext)({
  setId: () => void 0
});
var usePaneContext = () => {
  return (0, import_react3.useContext)(PaneContext);
};
var PaneSplitterContext = (0, import_react3.createContext)({
  orientation: "start",
  min: 0,
  max: 0,
  layoutKey: "",
  valueNow: 0,
  size: 0,
  isRow: false
});
var usePaneSplitterContext = () => {
  return (0, import_react3.useContext)(PaneSplitterContext);
};
var paneToSplitterOrientation = {
  start: "vertical",
  end: "vertical",
  top: "horizontal",
  bottom: "horizontal"
};
var orientationToDimension = {
  start: "columns",
  end: "columns",
  top: "rows",
  bottom: "rows"
};
var SplitterComponent = (0, import_react3.forwardRef)((_ref, ref) => {
  var _a, _b;
  let {
    providerId,
    layoutKey,
    min,
    max,
    orientation,
    isFixed,
    onMouseDown,
    onTouchStart,
    onKeyDown,
    onClick,
    ...props
  } = _ref;
  const paneProviderContext = usePaneProviderContextData(providerId);
  const paneContext = usePaneContext();
  const themeContext = (0, import_react3.useContext)(Me);
  const environment = useDocument(themeContext);
  const [isHovered, setIsHovered] = (0, import_react3.useState)(false);
  const isRow = orientationToDimension[orientation] === "rows";
  const separatorRef = (0, import_react3.useRef)(null);
  const splitterOrientation = paneToSplitterOrientation[orientation || "end"];
  const pixelsPerFr = paneProviderContext ? paneProviderContext.pixelsPerFr[orientationToDimension[orientation]] : 0;
  const value = isRow ? paneProviderContext == null ? void 0 : paneProviderContext.getRowValue(layoutKey, true) : paneProviderContext == null ? void 0 : paneProviderContext.getColumnValue(layoutKey, true);
  const valueInFr = isRow ? paneProviderContext == null ? void 0 : paneProviderContext.getRowValue(layoutKey) : paneProviderContext == null ? void 0 : paneProviderContext.getColumnValue(layoutKey);
  const {
    getSeparatorProps,
    getPrimaryPaneProps
  } = useSplitter({
    orientation: splitterOrientation,
    isLeading: orientation === "start" || orientation === "top",
    min: min * pixelsPerFr,
    max: max * pixelsPerFr,
    rtl: themeContext.rtl,
    isFixed,
    environment,
    onChange: (valueNow) => {
      if (isRow) {
        return paneProviderContext == null ? void 0 : paneProviderContext.setRowValue(orientation === "top", layoutKey, valueNow / pixelsPerFr);
      }
      return paneProviderContext == null ? void 0 : paneProviderContext.setColumnValue(orientation === "start", layoutKey, valueNow / pixelsPerFr);
    },
    valueNow: value,
    separatorRef
  });
  (0, import_react3.useEffect)(() => {
    if (!paneContext.id) {
      paneContext.setId(getPrimaryPaneProps().id);
    }
  }, [paneContext, getPrimaryPaneProps]);
  const ariaLabel = useText(SplitterComponent, props, "aria-label", `${splitterOrientation} splitter`);
  const separatorProps = getSeparatorProps({
    "aria-controls": paneContext.id,
    "aria-label": ariaLabel,
    onMouseDown,
    onTouchStart,
    onKeyDown,
    onClick
  });
  const size = isRow ? (_a = separatorRef.current) == null ? void 0 : _a.clientWidth : (_b = separatorRef.current) == null ? void 0 : _b.clientHeight;
  const onMouseOver = (0, import_react3.useMemo)(() => composeEventHandlers(props.onMouseOver, (event) => setIsHovered(event.target === separatorRef.current)), [props.onMouseOver, separatorRef]);
  return import_react3.default.createElement(PaneSplitterContext.Provider, {
    value: (0, import_react3.useMemo)(() => ({
      orientation,
      layoutKey,
      min,
      max,
      valueNow: valueInFr,
      size,
      isRow
    }), [orientation, layoutKey, min, max, valueInFr, size, isRow])
  }, import_react3.default.createElement(StyledPaneSplitter, _extends({
    isHovered,
    isFixed,
    orientation
  }, separatorProps, props, {
    onMouseOver,
    ref: react_merge_refs_esm_default([separatorRef, ref])
  })));
});
SplitterComponent.displayName = "Pane.Splitter";
SplitterComponent.propTypes = {
  layoutKey: import_prop_types2.default.string.isRequired,
  min: import_prop_types2.default.number.isRequired,
  max: import_prop_types2.default.number.isRequired,
  orientation: import_prop_types2.default.oneOf(ORIENTATION),
  isFixed: import_prop_types2.default.bool
};
SplitterComponent.defaultProps = {
  orientation: "end"
};
var Splitter = SplitterComponent;
var ContentComponent = (0, import_react3.forwardRef)((props, ref) => {
  const {
    isVisible
  } = usePaneContext();
  return import_react3.default.createElement(StyledPaneContent, _extends({
    hidden: !isVisible,
    ref
  }, props));
});
ContentComponent.displayName = "Pane.Content";
var Content = ContentComponent;
var SplitterButtonComponent = (0, import_react3.forwardRef)((props, ref) => {
  const {
    label,
    placement: defaultPlacement
  } = props;
  const {
    orientation,
    layoutKey,
    min,
    max,
    isRow,
    valueNow,
    size,
    providerId
  } = usePaneSplitterContext();
  const paneProviderContext = usePaneProviderContextData(providerId);
  const isTop = orientation === "top";
  const isStart = orientation === "start";
  const isMin = valueNow === min;
  let placement = defaultPlacement;
  if (!defaultPlacement) {
    if (isRow) {
      placement = "center";
    } else {
      placement = "start";
    }
  }
  const setValue = (0, import_react3.useCallback)((value) => {
    if (isRow) {
      paneProviderContext.setRowValue(isTop, layoutKey, value);
    } else {
      paneProviderContext.setColumnValue(isStart, layoutKey, value);
    }
  }, [isRow, isTop, isStart, layoutKey, paneProviderContext]);
  const onClick = composeEventHandlers(props.onClick, () => {
    if (isMin) {
      setValue(max);
    } else {
      setValue(min);
    }
  });
  const onKeyDown = composeEventHandlers(
    props.onKeyDown,
    (event) => event.stopPropagation()
  );
  const onMouseDown = composeEventHandlers(
    props.onMouseDown,
    (event) => event.stopPropagation()
  );
  return import_react3.default.createElement(Tooltip, {
    content: label,
    style: {
      cursor: "default"
    },
    onMouseDown: (e) => e.stopPropagation()
  }, import_react3.default.createElement(StyledPaneSplitterButton, _extends({
    "aria-label": label
  }, props, {
    placement,
    orientation,
    isRotated: isMin,
    splitterSize: size || 0,
    ref,
    onClick,
    onKeyDown,
    onMouseDown
  })));
});
SplitterButtonComponent.displayName = "Pane.SplitterButton";
var SplitterButton = SplitterButtonComponent;
var PaneComponent = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    children,
    ...props
  } = _ref;
  const [paneId, setPaneId] = (0, import_react3.useState)();
  const observerRef = (0, import_react3.useRef)(null);
  const {
    width = 0,
    height = 0
  } = useResizeObserver({
    ref: observerRef
  });
  const isVisible = (0, import_react3.useMemo)(() => observerRef.current ? width > 0 && height > 0 : true, [width, height]);
  const paneContext = (0, import_react3.useMemo)(() => ({
    isVisible,
    id: paneId,
    setId: (id) => setPaneId(id)
  }), [paneId, isVisible]);
  return import_react3.default.createElement(PaneContext.Provider, {
    value: paneContext
  }, import_react3.default.createElement(StyledPane, _extends({
    id: paneId,
    ref: react_merge_refs_esm_default([ref, observerRef])
  }, props), children));
});
PaneComponent.displayName = "Pane";
var Pane = PaneComponent;
Pane.Content = Content;
Pane.Splitter = Splitter;
Pane.SplitterButton = SplitterButton;
export {
  ALIGN_ITEMS as ARRAY_ALIGN_ITEMS,
  ALIGN_SELF as ARRAY_ALIGN_SELF,
  DIRECTION as ARRAY_DIRECTION,
  JUSTIFY_CONTENT as ARRAY_JUSTIFY_CONTENT,
  SPACE as ARRAY_SPACE,
  TEXT_ALIGN as ARRAY_TEXT_ALIGN,
  WRAP as ARRAY_WRAP,
  Col,
  Grid,
  Pane,
  PaneProvider,
  Row
};
//# sourceMappingURL=@zendeskgarden_react-grid.js.map
