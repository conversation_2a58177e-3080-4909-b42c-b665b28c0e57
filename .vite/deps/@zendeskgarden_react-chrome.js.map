{"version": 3, "sources": ["../../node_modules/@zendeskgarden/react-chrome/dist/index.esm.js"], "sourcesContent": ["/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport * as React from 'react';\nimport React__default, { useContext, useMemo, useEffect, Children, isValidElement, cloneElement, useRef, useState, createContext, forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nimport styled, { css, keyframes, ThemeContext } from 'styled-components';\nimport { math, rgba, readableColor } from 'polished';\nimport { retrieveComponentStyles, DEFAULT_THEME, getLineHeight, getColor, PALETTE, useDocument } from '@zendeskgarden/react-theming';\nimport { useAccordion } from '@zendeskgarden/container-accordion';\nimport { getControlledValue } from '@zendeskgarden/container-utilities';\nimport { useUIDSeed } from 'react-uid';\nimport mergeRefs from 'react-merge-refs';\n\nfunction _extends$3() {\n  _extends$3 = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends$3.apply(this, arguments);\n}\n\nconst ChromeContext = React__default.createContext({\n  hue: 'chromeHue'\n});\nconst useChromeContext = () => {\n  return useContext(ChromeContext);\n};\n\nconst COMPONENT_ID$A = 'chrome.chrome';\nconst StyledChrome = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$A,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledChrome\",\n  componentId: \"sc-1uqm6u6-0\"\n})([\"display:flex;position:relative;margin:0;height:100vh;overflow-y:hidden;font-family:\", \";direction:\", \";\", \";\"], props => props.theme.fonts.system, props => props.theme.rtl && 'rtl', props => retrieveComponentStyles(COMPONENT_ID$A, props));\nStyledChrome.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$z = 'chrome.header_item_icon';\nconst StyledHeaderItemIcon = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$z,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledHeaderItemIcon\",\n  componentId: \"sc-1jhhp6z-0\"\n})([\"transition:transform 0.25s ease-in-out;margin:0 3px;width:\", \";min-width:\", \";height:\", \";\", \";\"], props => props.theme.iconSizes.md, props => props.theme.iconSizes.md, props => props.theme.iconSizes.md, props => retrieveComponentStyles(COMPONENT_ID$z, props));\nStyledHeaderItemIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$y = 'chrome.base_header_item';\nconst getHeaderItemSize = props => `${props.theme.space.base * 7.5}px`;\nconst sizeStyles$4 = props => {\n  const size = props.theme.space.base * 7.5;\n  return css([\"padding:0 3px;min-width:\", \"px;height:\", \";line-height:\", \";\"], size, props.maxY ? '100%' : `${size}px`, getLineHeight(size, props.theme.fontSizes.md));\n};\nconst StyledBaseHeaderItem = styled.button.attrs({\n  'data-garden-id': COMPONENT_ID$y,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledBaseHeaderItem\",\n  componentId: \"sc-1qua7h6-0\"\n})([\"display:inline-flex;position:relative;flex:\", \";align-items:center;justify-content:\", \";order:1;transition:box-shadow 0.1s ease-in-out,color 0.1s ease-in-out;z-index:0;margin:\", \";border:none;border-radius:\", \";background:transparent;text-decoration:none;white-space:nowrap;color:inherit;font-size:inherit;\", \" \", \";\"], props => props.maxX && '1', props => props.maxX ? 'start' : 'center', props => `0 ${props.theme.space.base * 3}px`, props => {\n  if (props.isRound) {\n    return '100%';\n  }\n  if (props.maxY) {\n    return '0';\n  }\n  return props.theme.borderRadii.md;\n}, sizeStyles$4, props => retrieveComponentStyles(COMPONENT_ID$y, props));\nStyledBaseHeaderItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$x = 'chrome.header_item_text';\nconst clippedStyling = css([\"position:absolute;margin:0;clip:rect(1px,1px,1px,1px);width:1px;height:1px;overflow:hidden;white-space:nowrap;\"]);\nconst StyledHeaderItemText = styled.span.attrs({\n  'data-garden-id': COMPONENT_ID$x,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledHeaderItemText\",\n  componentId: \"sc-goz7la-0\"\n})([\"margin:0 3px;\", \" \", \";\"], props => props.isClipped && clippedStyling, props => retrieveComponentStyles(COMPONENT_ID$x, props));\nStyledHeaderItemText.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$w = 'chrome.nav';\nconst colorStyles$6 = props => {\n  const shade = props.isDark || props.isLight ? 600 : 700;\n  const backgroundColor = getColor(props.hue, shade, props.theme);\n  const foregroundColor = props.isLight ? props.theme.palette.grey[800] : props.theme.palette.white;\n  return css([\"background-color:\", \";color:\", \";\"], backgroundColor, foregroundColor);\n};\nconst getNavWidth = props => {\n  return `${props.theme.space.base * 15}px`;\n};\nconst getExpandedNavWidth = () => {\n  return `200px`;\n};\nconst StyledNav = styled.nav.attrs({\n  'data-garden-id': COMPONENT_ID$w,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledNav\",\n  componentId: \"sc-6j462r-0\"\n})([\"display:flex;position:relative;flex-direction:column;flex-shrink:0;order:-1;width:\", \";font-size:\", \";\", \";\", \";\"], props => props.isExpanded ? getExpandedNavWidth : getNavWidth, props => props.theme.fontSizes.md, props => colorStyles$6(props), props => retrieveComponentStyles(COMPONENT_ID$w, props));\nStyledNav.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$v = 'chrome.header_item';\nconst retrieveProductColor$1 = props => {\n  switch (props.product) {\n    case 'chat':\n      return PALETTE.product.chat;\n    case 'connect':\n      return PALETTE.product.connect;\n    case 'explore':\n      return PALETTE.product.explore;\n    case 'guide':\n      return PALETTE.product.guide;\n    case 'message':\n      return PALETTE.product.message;\n    case 'support':\n      return PALETTE.product.support;\n    case 'talk':\n      return PALETTE.product.talk;\n    default:\n      return 'inherit';\n  }\n};\nconst StyledLogoHeaderItem = styled(StyledBaseHeaderItem).attrs({\n  'data-garden-id': COMPONENT_ID$v,\n  'data-garden-version': '8.67.0',\n  as: 'div'\n}).withConfig({\n  displayName: \"StyledLogoHeaderItem\",\n  componentId: \"sc-1n1d1yv-0\"\n})([\"display:none;order:0;margin-right:\", \";margin-left:\", \";border-\", \":\", \";border-radius:0;padding:0;width:\", \";height:100%;overflow:hidden;fill:\", \";text-decoration:none;color:\", \";\", \"{\", \"}\", \"{margin:0;width:\", \";height:\", \";}\", \";\"], props => props.theme.rtl ? `-${props.theme.space.base}px` : 'auto', props => props.theme.rtl ? 'auto' : `-${props.theme.space.base}px`, props => props.theme.rtl ? 'left' : 'right', props => `${props.theme.borders.sm} ${getColor('neutralHue', 300, props.theme)}`, props => getNavWidth(props), props => getColor('chromeHue', 700, props.theme), props => retrieveProductColor$1(props), StyledHeaderItemText, clippedStyling, StyledHeaderItemIcon, props => props.theme.iconSizes.lg, props => props.theme.iconSizes.lg, props => retrieveComponentStyles(COMPONENT_ID$v, props));\nStyledLogoHeaderItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$u = 'chrome.base_nav_item';\nconst getNavItemHeight = props => {\n  return `${props.theme.space.base * 13}px`;\n};\nconst sizeStyles$3 = props => {\n  const verticalPadding = math(`(${getNavItemHeight(props)} - ${props.theme.iconSizes.lg}) / 2`);\n  const horizontalPadding = math(`(${getNavWidth(props)} - ${props.theme.iconSizes.lg}) / 4`);\n  return css([\"padding:\", \" \", \";min-height:\", \";\"], verticalPadding, horizontalPadding, getNavItemHeight);\n};\nconst StyledBaseNavItem = styled.button.attrs({\n  'data-garden-id': COMPONENT_ID$u,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledBaseNavItem\",\n  componentId: \"sc-zvo43f-0\"\n})([\"display:flex;flex-shrink:0;align-items:center;justify-content:center;transition:box-shadow 0.1s ease-in-out,background-color 0.1s ease-in-out,opacity 0.1s ease-in-out;border:none;box-sizing:border-box;background:transparent;text-decoration:none;color:inherit;font-size:inherit;\", \"\"], props => sizeStyles$3(props));\nStyledBaseNavItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$t = 'chrome.header';\nconst getHeaderHeight = props => {\n  return getNavItemHeight(props);\n};\nconst StyledHeader = styled.header.attrs({\n  'data-garden-id': COMPONENT_ID$t,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledHeader\",\n  componentId: \"sc-1cexpz-0\"\n})([\"display:flex;position:\", \";align-items:center;justify-content:flex-end;box-sizing:border-box;border-bottom:\", \";box-shadow:\", \";background-color:\", \";padding:0 \", \"px;height:\", \";color:\", \";font-size:\", \";\", \" \", \";\"], props => props.isStandalone && 'relative', props => `${props.theme.borders.sm} ${getColor('neutralHue', 300, props.theme)}`, props => props.isStandalone && props.theme.shadows.lg('0', '10px', getColor('chromeHue', 600, props.theme, 0.15)), props => props.theme.colors.background, props => props.theme.space.base, getHeaderHeight, props => getColor('neutralHue', 600, props.theme), props => props.theme.fontSizes.md, props => props.isStandalone && `\n    ${StyledLogoHeaderItem} {\n      display: inline-flex;\n    }\n  `, props => retrieveComponentStyles(COMPONENT_ID$t, props));\nStyledHeader.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$s = 'chrome.skipnav';\nconst animationStyles = () => {\n  const animationName = keyframes([\"0%{transform:translate(-50%,-50%);}\"]);\n  return css([\"transition:opacity 0.2s ease-out,clip 0s linear 0.2s;opacity:0;clip:rect(0,0,0,0);&:focus{transition:opacity 0.2s ease-in-out;animation:0.2s cubic-bezier(0.15,0.85,0.35,1.2) \", \";opacity:1;clip:rect(0,150vw,100vh,-50vw);}\"], animationName);\n};\nconst colorStyles$5 = theme => {\n  const color = getColor('primaryHue', 600, theme);\n  const borderColor = getColor('neutralHue', 300, theme);\n  const boxShadow = theme.shadows.lg(`${theme.space.base * 5}px`, `${theme.space.base * 7}px`, getColor('chromeHue', 600, theme, 0.15));\n  return css([\"border-color:\", \";box-shadow:\", \";background-color:\", \";color:\", \";&:hover,&:focus{color:\", \";}\"], borderColor, boxShadow, theme.colors.background, color, color);\n};\nconst sizeStyles$2 = props => {\n  const top = math(`${getHeaderHeight(props)} / 2`);\n  const padding = `${props.theme.space.base * 5}px`;\n  const paddingStart = `${props.theme.space.base * 4}px`;\n  const fontSize = props.theme.fontSizes.md;\n  const lineHeight = getLineHeight(padding, fontSize);\n  return css([\"top:\", \";padding:\", \";padding-\", \":\", \";line-height:\", \";font-size:\", \";\"], top, padding, props.theme.rtl ? 'right' : 'left', paddingStart, lineHeight, fontSize);\n};\nconst StyledSkipNav = styled.a.attrs({\n  'data-garden-id': COMPONENT_ID$s,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSkipNav\",\n  componentId: \"sc-1tsro34-0\"\n})([\"\", \";display:inline-flex;position:absolute;left:50%;align-items:center;justify-content:center;transform:translateX(-50%);direction:\", \";z-index:\", \";border:\", \";border-radius:\", \";text-decoration:underline;white-space:nowrap;\", \";&:focus{outline:none;}\", \";\", \";\"], animationStyles(), props => props.theme.rtl && 'rtl', props => props.zIndex, props => props.theme.borders.sm, props => props.theme.borderRadii.md, props => sizeStyles$2(props), props => colorStyles$5(props.theme), props => retrieveComponentStyles(COMPONENT_ID$s, props));\nStyledSkipNav.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nvar _path$2;\nfunction _extends$2() { _extends$2 = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends$2.apply(this, arguments); }\nvar SvgLinkStroke = function SvgLinkStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends$2({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 16,\n    height: 16,\n    focusable: \"false\",\n    viewBox: \"0 0 16 16\",\n    \"aria-hidden\": \"true\"\n  }, props), _path$2 || (_path$2 = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"currentColor\",\n    d: \"M4.441 7.38l.095.083.939.939-.708.707-.939-.939-2 2-.132.142a2.829 2.829 0 003.99 3.99l.142-.132 2-2-.939-.939.707-.708.94.94a1 1 0 01.083 1.32l-.083.094-2 2A3.828 3.828 0 01.972 9.621l.15-.158 2-2A1 1 0 014.34 7.31l.101.07zm7.413-3.234a.5.5 0 01.057.638l-.057.07-7 7a.5.5 0 01-.765-.638l.057-.07 7-7a.5.5 0 01.708 0zm3.023-3.025a3.829 3.829 0 01.15 5.257l-.15.158-2 2a1 1 0 01-1.32.083l-.094-.083-.94-.94.708-.707.939.94 2-2 .132-.142a2.829 2.829 0 00-3.99-3.99l-.142.131-2 2 .939.939-.707.708-.94-.94a1 1 0 01-.082-1.32l.083-.094 2-2a3.828 3.828 0 015.414 0z\"\n  })));\n};\n\nconst COMPONENT_ID$r = 'chrome.skipnav_icon';\nconst sizeStyles$1 = theme => {\n  const margin = `${theme.space.base * 2}px`;\n  const size = theme.iconSizes.md;\n  return css([\"margin-\", \":\", \";width:\", \";height:\", \";\"], theme.rtl ? 'left' : 'right', margin, size, size);\n};\nconst StyledSkipNavIcon = styled(SvgLinkStroke).attrs({\n  'data-garden-id': COMPONENT_ID$r,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSkipNavIcon\",\n  componentId: \"sc-1ika5z4-0\"\n})([\"transform:\", \";color:\", \";\", \";\", \";\"], props => props.theme.rtl && 'scaleX(-1)', props => getColor('neutralHue', 600, props.theme), props => sizeStyles$1(props.theme), props => retrieveComponentStyles(COMPONENT_ID$r, props));\nStyledSkipNavIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$q = 'chrome.body';\nconst StyledBody = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$q,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledBody\",\n  componentId: \"sc-c7h9kv-0\"\n})([\"flex:1;order:1;background-color:\", \";min-width:0;\", \";\"], props => getColor('neutralHue', 100, props.theme), props => retrieveComponentStyles(COMPONENT_ID$q, props));\nStyledBody.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$p = 'chrome.footer';\nconst getFooterHeight = props => {\n  return `${props.theme.space.base * 20}px`;\n};\nconst StyledFooter = styled.footer.attrs({\n  'data-garden-id': COMPONENT_ID$p,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledFooter\",\n  componentId: \"sc-v7lib2-0\"\n})([\"display:flex;align-items:center;justify-content:flex-end;box-sizing:border-box;border-top:\", \";background-color:\", \";padding:0 \", \"px;height:\", \";\", \";\"], props => `${props.theme.borders.sm} ${getColor('neutralHue', 300, props.theme)}`, props => props.theme.colors.background, props => props.theme.space.base * 9, getFooterHeight, props => retrieveComponentStyles(COMPONENT_ID$p, props));\nStyledFooter.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$o = 'chrome.content';\nconst StyledContent = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$o,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledContent\",\n  componentId: \"sc-qcuzxn-0\"\n})([\"display:flex;height:\", \";line-height:\", \";color:\", \";font-size:\", \";&:focus{outline:none;}\", \";\"], props => props.hasFooter ? `calc(100% - ${math(`${getHeaderHeight(props)} + ${getFooterHeight(props)}`)})` : `calc(100% - ${getHeaderHeight(props)})`, props => getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md), props => props.theme.colors.foreground, props => props.theme.fontSizes.md, props => retrieveComponentStyles(COMPONENT_ID$o, props));\nStyledContent.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$n = 'chrome.main';\nconst StyledMain = styled.main.attrs({\n  'data-garden-id': COMPONENT_ID$n,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledMain\",\n  componentId: \"sc-t61cre-0\"\n})([\"flex:1;order:1;background-color:\", \";overflow:auto;:focus{outline:none;}\", \";\"], props => props.theme.colors.background, props => retrieveComponentStyles(COMPONENT_ID$n, props));\nStyledMain.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$m = 'chrome.sidebar';\nconst StyledSidebar = styled.aside.attrs({\n  'data-garden-id': COMPONENT_ID$m,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSidebar\",\n  componentId: \"sc-1q77fuw-0\"\n})([\"flex-shrink:0;order:0;box-sizing:border-box;border-\", \":\", \";width:330px;overflow:auto;&:focus{outline:none;}\", \";\"], props => props.theme.rtl ? 'left' : 'right', props => `${props.theme.borders.sm} ${getColor('neutralHue', 300, props.theme)}`, props => retrieveComponentStyles(COMPONENT_ID$m, props));\nStyledSidebar.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$l = 'chrome.footer_item';\nconst StyledFooterItem = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$l,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledFooterItem\",\n  componentId: \"sc-1cktm85-0\"\n})([\"margin:\", \";\", \";\"], props => `0 ${props.theme.space.base}px`, props => retrieveComponentStyles(COMPONENT_ID$l, props));\nStyledFooterItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$k = 'chrome.header_item';\nconst imgStyles = props => {\n  const size = math(`${getHeaderItemSize(props)} - ${props.theme.space.base * 2}`);\n  return css([\"img{margin:0;border-radius:\", \";width:\", \";height:\", \";}\"], math(`${props.theme.borderRadii.md} - 1`), size, size);\n};\nconst StyledHeaderItem = styled(StyledBaseHeaderItem).attrs({\n  'data-garden-id': COMPONENT_ID$k,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledHeaderItem\",\n  componentId: \"sc-14sft6n-0\"\n})([\"&:hover,&:focus{text-decoration:none;color:inherit;}&:focus{outline:none;}&[data-garden-focus-visible]{box-shadow:\", \";}&[data-garden-focus-visible]:active{box-shadow:none;}&:hover \", \",&:hover \", \",&:active \", \",&:active \", \"{color:\", \";}\", \" \", \" \", \" \", \";\"], props => props.theme.shadows.md(getColor('chromeHue', 400, props.theme, 0.35)), StyledHeaderItemIcon, StyledHeaderItemText, StyledHeaderItemIcon, StyledHeaderItemText, props => getColor('chromeHue', 700, props.theme), props => props.maxY && `\n      &[data-garden-focus-visible] {\n        box-shadow: inset ${props.theme.shadows.lg(props.theme.shadowWidths.md, '0', getColor('chromeHue', 400, props.theme, 0.35))},\n        ${props.theme.shadowWidths.md} 0 0 0 ${getColor('chromeHue', 400, props.theme, 0.35)},\n        inset ${props.theme.shadows.lg(`-${props.theme.shadowWidths.md}`, '0', getColor('chromeHue', 400, props.theme, 0.35))},\n        -${props.theme.shadowWidths.md} 0 0 0 ${getColor('chromeHue', 400, props.theme, 0.35)};\n      }\n  `, imgStyles, props => props.isRound && `\n    ${StyledHeaderItemIcon} {\n      border-radius: 100px;\n    }\n  `, props => retrieveComponentStyles(COMPONENT_ID$k, props));\nStyledHeaderItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$j = 'chrome.header_item_wrapper';\nconst StyledHeaderItemWrapper = styled(StyledBaseHeaderItem).attrs({\n  'data-garden-id': COMPONENT_ID$j,\n  'data-garden-version': '8.67.0',\n  as: 'div'\n}).withConfig({\n  displayName: \"StyledHeaderItemWrapper\",\n  componentId: \"sc-1uieu55-0\"\n})([\"\", \";\"], props => retrieveComponentStyles(COMPONENT_ID$j, props));\nStyledHeaderItemWrapper.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$i = 'chrome.logo_nav_item';\nconst retrieveProductColor = product => {\n  switch (product) {\n    case 'chat':\n      return PALETTE.product.chat;\n    case 'connect':\n      return PALETTE.product.connect;\n    case 'explore':\n      return PALETTE.product.explore;\n    case 'guide':\n      return PALETTE.product.guide;\n    case 'message':\n      return PALETTE.product.message;\n    case 'support':\n      return PALETTE.product.support;\n    case 'talk':\n      return PALETTE.product.talk;\n    default:\n      return 'inherit';\n  }\n};\nconst colorStyles$4 = props => {\n  const fillColor = props.isLight ? props.theme.palette.grey[800] : props.theme.palette.white;\n  const color = props.isLight || props.isDark ? fillColor : retrieveProductColor(props.product);\n  return css([\"color:\", \";fill:\", \";\"], color, fillColor);\n};\nconst StyledLogoNavItem = styled(StyledBaseNavItem).attrs({\n  'data-garden-id': COMPONENT_ID$i,\n  'data-garden-version': '8.67.0',\n  as: 'div'\n}).withConfig({\n  displayName: \"StyledLogoNavItem\",\n  componentId: \"sc-saaydx-0\"\n})([\"order:0;opacity:1;cursor:default;\", \";\"], props => colorStyles$4(props));\nStyledLogoNavItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$h = 'chrome.brandmark_nav_item';\nconst StyledBrandmarkNavItem = styled(StyledBaseNavItem).attrs({\n  'data-garden-id': COMPONENT_ID$h,\n  'data-garden-version': '8.67.0',\n  as: 'div'\n}).withConfig({\n  displayName: \"StyledBrandmarkNavItem\",\n  componentId: \"sc-8kynd4-0\"\n})([\"order:1;opacity:0.3;margin-top:auto;\"]);\nStyledBrandmarkNavItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$g = 'chrome.nav_item_icon';\nconst StyledNavItemIcon = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$g,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledNavItemIcon\",\n  componentId: \"sc-7w9rpt-0\"\n})([\"align-self:flex-start;order:0;border-radius:\", \";width:\", \";height:\", \";\", \";\"], props => props.theme.borderRadii.md, props => props.theme.iconSizes.lg, props => props.theme.iconSizes.lg, props => retrieveComponentStyles(COMPONENT_ID$g, props));\nStyledNavItemIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$f = 'chrome.nav_item';\nconst colorStyles$3 = props => {\n  const BLACK = props.theme.palette.black;\n  const WHITE = props.theme.palette.white;\n  let currentColor;\n  let hoverColor;\n  if (props.isCurrent) {\n    if (props.isLight) {\n      currentColor = rgba(BLACK, 0.3);\n    } else if (props.isDark) {\n      currentColor = rgba(WHITE, 0.3);\n    } else {\n      currentColor = getColor(props.hue, 500, props.theme);\n    }\n  } else {\n    hoverColor = rgba(props.isLight ? WHITE : BLACK, 0.1);\n  }\n  const activeColor = rgba(props.isLight ? BLACK : WHITE, 0.1);\n  const focusColor = rgba(props.isLight ? BLACK : WHITE, 0.2);\n  return css([\"opacity:\", \";background-color:\", \";&:hover{opacity:1;background-color:\", \";}&[data-garden-focus-visible]{opacity:1;box-shadow:inset \", \";}&:active{background-color:\", \";}\"], props.isCurrent ? 1 : 0.6, currentColor, hoverColor, props.theme.shadows.md(focusColor), activeColor);\n};\nconst StyledNavItem = styled(StyledBaseNavItem).attrs({\n  'data-garden-id': COMPONENT_ID$f,\n  'data-garden-version': '8.67.0',\n  as: 'button'\n}).withConfig({\n  displayName: \"StyledNavItem\",\n  componentId: \"sc-gs8mjz-0\"\n})([\"justify-content:\", \";order:1;margin:0;cursor:\", \";text-align:\", \";&:hover,&:focus{text-decoration:none;color:inherit;}&:focus{outline:none;}\", \";\", \" \", \";\"], props => props.isExpanded && 'start', props => props.isCurrent ? 'default' : 'pointer', props => props.isExpanded && 'inherit', props => colorStyles$3(props), props => props.isExpanded && `\n    ${StyledNavItemIcon} {\n      margin: 0 ${math(`(${getNavWidth(props)} - ${props.theme.iconSizes.lg}) / 4`)};\n    }\n  `, props => retrieveComponentStyles(COMPONENT_ID$f, props));\nStyledNavItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$e = 'chrome.nav_item_text';\nconst StyledNavItemText = styled.span.attrs({\n  'data-garden-id': COMPONENT_ID$e,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledNavItemText\",\n  componentId: \"sc-13m84xl-0\"\n})([\"position:absolute;order:1;clip:rect(1px,1px,1px,1px);margin:\", \";width:1px;height:1px;overflow:hidden;line-height:\", \";white-space:\", \";\", \" \", \";\"], props => props.isExpanded && `0 ${math(`(${getNavWidth(props)} - ${props.theme.iconSizes.lg}) / 4`)}`, props => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), props => props.isWrapped ? 'normal' : 'nowrap', props => props.isExpanded && `\n    ${StyledNavItem} > && {\n      position: static;\n      flex: 1;\n      clip: auto;\n      width: auto;\n      height: auto;\n      text-overflow: ellipsis;\n    }\n  `, props => retrieveComponentStyles(COMPONENT_ID$e, props));\nStyledNavItemText.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$d = 'chrome.subnav_item';\nconst colorStyles$2 = props => {\n  const BLACK = props.theme.palette.black;\n  const WHITE = props.theme.palette.white;\n  let currentColor;\n  let hoverColor;\n  if (props.isCurrent) {\n    if (props.isLight) {\n      currentColor = rgba(BLACK, 0.1);\n    } else {\n      currentColor = rgba(WHITE, 0.1);\n    }\n  } else {\n    hoverColor = rgba(props.isLight ? WHITE : BLACK, 0.1);\n  }\n  const activeColor = rgba(props.isLight ? BLACK : WHITE, 0.03);\n  const focusColor = rgba(props.isLight ? BLACK : WHITE, 0.2);\n  return css([\"opacity:\", \";background-color:\", \";&:hover{opacity:1;background-color:\", \";}&[data-garden-focus-visible]{opacity:1;box-shadow:\", \";}&:not([data-garden-header='true']):active{background-color:\", \";}\"], props.isCurrent ? '1' : '0.6', currentColor, hoverColor, props.theme.shadows.md(focusColor), activeColor);\n};\nconst getSubNavItemHeight = props => {\n  return `${props.theme.space.base * 7.5}px`;\n};\nconst StyledSubNavItem = styled.button.attrs({\n  'data-garden-id': COMPONENT_ID$d,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSubNavItem\",\n  componentId: \"sc-1yg9dpx-0\"\n})([\"display:flex;align-items:center;transition:box-shadow 0.1s ease-in-out,background-color 0.1s ease-in-out,opacity 0.1s ease-in-out;margin:\", \"px 0 0;border:none;border-radius:\", \";box-sizing:border-box;background:transparent;cursor:\", \";padding:\", \";width:100%;min-height:\", \";text-align:inherit;font-size:inherit;&,&:hover,&:focus{text-decoration:none;color:inherit;}&:focus{outline:none;}\", \";\", \";\"], props => props.theme.space.base * 2, props => props.theme.borderRadii.md, props => props.isCurrent ? 'default' : 'pointer', props => `0 ${props.theme.space.base * 2}px`, getSubNavItemHeight, props => colorStyles$2(props), props => retrieveComponentStyles(COMPONENT_ID$d, props));\nStyledSubNavItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$c = 'chrome.subnav';\nconst colorStyles$1 = props => {\n  let shade;\n  if (props.isLight) {\n    shade = 500;\n  } else {\n    shade = props.isDark ? 700 : 800;\n  }\n  const backgroundColor = getColor(props.hue, shade, props.theme);\n  const foregroundColor = props.isLight ? props.theme.palette.grey[800] : props.theme.palette.white;\n  return css([\"background-color:\", \";color:\", \";\"], backgroundColor, foregroundColor);\n};\nconst StyledSubNav = styled.nav.attrs({\n  'data-garden-id': COMPONENT_ID$c,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSubNav\",\n  componentId: \"sc-19hjou6-0\"\n})([\"flex-direction:column;order:0;padding:\", \";min-width:220px;overflow:auto;font-size:\", \";\", \";& > \", \":first-child{margin-top:0;}\", \";\"], props => `${props.theme.space.base * 6}px ${props.theme.space.base * 5}px`, props => props.theme.fontSizes.md, props => colorStyles$1(props), StyledSubNavItem, props => retrieveComponentStyles('chrome.subnav', props));\nStyledSubNav.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$b = 'chrome.subnav_item_text';\nconst sizeStyles = props => {\n  const baseLineHeight = props.theme.space.base * 5;\n  const verticalMargin = math(`(${getSubNavItemHeight(props)} - ${baseLineHeight}) / 2`);\n  const lineHeight = getLineHeight(baseLineHeight, props.theme.fontSizes.md);\n  return css([\"margin:\", \" 0;line-height:\", \";\"], verticalMargin, lineHeight);\n};\nconst StyledSubNavItemText = styled.span.attrs({\n  'data-garden-id': COMPONENT_ID$b,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSubNavItemText\",\n  componentId: \"sc-1hy0pn7-0\"\n})([\"overflow:hidden;text-overflow:ellipsis;white-space:\", \";\", \" \", \";\"], props => props.isWrapped ? 'normal' : 'nowrap', props => sizeStyles(props), props => retrieveComponentStyles(COMPONENT_ID$b, props));\nStyledSubNavItemText.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$a = 'chrome.collapsible_sub_nav_item';\nconst StyledSubNavItemHeader = styled(StyledSubNavItem).attrs({\n  'data-garden-id': COMPONENT_ID$a,\n  'data-garden-version': '8.67.0',\n  'data-garden-header': 'true'\n}).withConfig({\n  displayName: \"StyledSubNavItemHeader\",\n  componentId: \"sc-1vniter-0\"\n})([\"position:relative;padding-\", \":\", \"px;\", \";\"], props => props.theme.rtl ? 'left' : 'right', props => props.theme.space.base * 7, props => retrieveComponentStyles(COMPONENT_ID$a, props));\nStyledSubNavItemHeader.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nvar _path$1;\nfunction _extends$1() { _extends$1 = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends$1.apply(this, arguments); }\nvar SvgChevronDownStroke = function SvgChevronDownStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends$1({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 12,\n    height: 12,\n    focusable: \"false\",\n    viewBox: \"0 0 12 12\",\n    \"aria-hidden\": \"true\"\n  }, props), _path$1 || (_path$1 = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"currentColor\",\n    d: \"M1.646 3.646a.5.5 0 01.638-.057l.07.057L6 7.293l3.646-3.647a.5.5 0 01.638-.057l.07.057a.5.5 0 01.057.638l-.057.07-4 4a.5.5 0 01-.638.057l-.07-.057-4-4a.5.5 0 010-.708z\"\n  })));\n};\n\nconst COMPONENT_ID$9 = 'chrome.collapsible_sub_nav_item_icon';\nconst FilteredChevronDownStrokeIcon = React__default.forwardRef((_ref, ref) => {\n  let {\n    theme,\n    isExpanded,\n    ...validProps\n  } = _ref;\n  return React__default.createElement(SvgChevronDownStroke, _extends$3({\n    ref: ref\n  }, validProps));\n});\nFilteredChevronDownStrokeIcon.displayName = 'FilteredChevronDownStrokeIcon';\nconst StyledSubNavItemIcon = styled(FilteredChevronDownStrokeIcon).withConfig({\n  displayName: \"StyledSubNavItemIcon\",\n  componentId: \"sc-1d02hho-0\"\n})([\"width:\", \";height:\", \";\"], props => props.theme.iconSizes.sm, props => props.theme.iconSizes.sm);\nStyledSubNavItemIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\nconst StyledSubNavItemIconWrapper = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$9,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSubNavItemIcon__StyledSubNavItemIconWrapper\",\n  componentId: \"sc-1d02hho-1\"\n})([\"display:flex;position:absolute;top:0;right:\", \";left:\", \";align-items:center;justify-content:center;width:\", \"px;height:\", \";\", \"{transform:\", \";transition:transform 0.25s ease-in-out;}\", \";\"], props => props.theme.rtl ? 'auto' : 0, props => props.theme.rtl && 0, props => props.theme.space.base * 7, getSubNavItemHeight, StyledSubNavItemIcon, props => {\n  if (props.isExpanded) {\n    return css([\"rotate(\", \"180deg)\"], props.theme.rtl && '-');\n  }\n  return undefined;\n}, props => retrieveComponentStyles(COMPONENT_ID$9, props));\nStyledSubNavItemIconWrapper.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst PANEL_COMPONENT_ID = 'chrome.collapsible_sub_nav_item_panel';\nconst hiddenStyling = css([\"visibility:hidden;max-height:0 !important;overflow:hidden;\"]);\nconst StyledSubNavPanel = styled.div.attrs({\n  'data-garden-id': PANEL_COMPONENT_ID,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSubNavPanel\",\n  componentId: \"sc-1jv3rpv-0\"\n})([\"transition:max-height 0.25s cubic-bezier(0.15,0.85,0.35,1.2),0.25s visibility 0s linear;height:auto;max-height:100%;\", \" \", \"{padding-\", \":\", \";}\", \";\"], props => props.isHidden && hiddenStyling, StyledSubNavItem, props => props.theme.rtl ? 'right' : 'left', props => `${props.theme.space.base * 5}px`, props => retrieveComponentStyles(PANEL_COMPONENT_ID, props));\nStyledSubNavPanel.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$8 = 'chrome.sheet';\nconst borderStyle = _ref => {\n  let {\n    theme,\n    placement,\n    isOpen\n  } = _ref;\n  const borderColor = isOpen ? getColor('neutralHue', 300, theme) : 'transparent';\n  const borderSides = ['-left', '-right'];\n  let borderSide = '';\n  if (theme.rtl) {\n    borderSides.reverse();\n  }\n  if (placement === 'end') {\n    borderSide = borderSides[0];\n  } else if (placement === 'start') {\n    borderSide = borderSides[1];\n  }\n  return `border${borderSide}: ${theme.borders.sm} ${borderColor};`;\n};\nconst StyledSheet = styled.aside.attrs({\n  'data-garden-id': COMPONENT_ID$8,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSheet\",\n  componentId: \"sc-dx8ijk-0\"\n})([\"display:flex;order:1;transition:\", \";background-color:\", \";width:\", \";height:100%;overflow:hidden;font-size:\", \";&:focus{outline:none;}\", \";\", \";\"], props => props.isAnimated && 'width 250ms ease-in-out', props => props.theme.colors.background, props => props.isOpen ? props.size : '0px', props => props.theme.fontSizes.md, props => borderStyle(props), props => retrieveComponentStyles(COMPONENT_ID$8, props));\nStyledSheet.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$7 = 'chrome.sheet_wrapper';\nconst StyledSheetWrapper = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$7,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSheetWrapper\",\n  componentId: \"sc-f6x9zb-0\"\n})([\"display:flex;position:relative;flex-direction:column;transform:\", \";transition:\", \";min-width:\", \";\", \";\"], props => {\n  const translateValues = [-100, 100];\n  let translation = 'translateX(0%)';\n  if (props.isOpen) {\n    return translation;\n  }\n  if (props.theme.rtl) {\n    translateValues.reverse();\n  }\n  if (props.placement === 'end') {\n    translation = `translateX(${translateValues[1]}%)`;\n  } else if (props.placement === 'start') {\n    translation = `translateX(${translateValues[0]}%)`;\n  }\n  return translation;\n}, props => props.isAnimated && 'transform 250ms ease-in-out', props => props.size, props => retrieveComponentStyles(COMPONENT_ID$7, props));\nStyledSheetWrapper.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$6 = 'chrome.sheet_title';\nconst StyledSheetTitle = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$6,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSheetTitle\",\n  componentId: \"sc-1gogk75-0\"\n})([\"line-height:\", \";color:\", \";font-weight:\", \";\", \";\"], props => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), props => props.theme.colors.foreground, props => props.theme.fontWeights.semibold, props => retrieveComponentStyles(COMPONENT_ID$6, props));\nStyledSheetTitle.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$5 = 'chrome.sheet_description';\nconst StyledSheetDescription = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$5,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSheetDescription\",\n  componentId: \"sc-1puglb6-0\"\n})([\"line-height:\", \";color:\", \";\", \";\"], props => getLineHeight(props.theme.space.base * 4, props.theme.fontSizes.md), props => getColor('neutralHue', 600, props.theme), props => retrieveComponentStyles(COMPONENT_ID$5, props));\nStyledSheetDescription.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$4 = 'chrome.sheet_body';\nconst StyledSheetBody = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$4,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSheetBody\",\n  componentId: \"sc-bt4eoj-0\"\n})([\"flex:1;overflow-y:auto;padding:\", \"px;\", \";\"], props => props.theme.space.base * 5, props => retrieveComponentStyles(COMPONENT_ID$4, props));\nStyledSheetBody.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$3 = 'chrome.sheet_close';\nconst BASE_MULTIPLIERS = {\n  top: 2.5,\n  side: 2,\n  size: 10\n};\nconst colorStyles = props => {\n  const backgroundColor = 'primaryHue';\n  const foregroundColor = 'neutralHue';\n  return css([\"background-color:transparent;color:\", \";&:hover{background-color:\", \";color:\", \";}&[data-garden-focus-visible]{box-shadow:\", \";}&:active{transition:background-color 0.1s ease-in-out,color 0.1s ease-in-out;background-color:\", \";color:\", \";}\"], getColor(foregroundColor, 600, props.theme), getColor(backgroundColor, 600, props.theme, 0.08), getColor(foregroundColor, 700, props.theme), props.theme.shadows.md(getColor(backgroundColor, 600, props.theme, 0.35)), getColor(backgroundColor, 600, props.theme, 0.2), getColor(foregroundColor, 800, props.theme));\n};\nconst StyledSheetClose = styled.button.attrs({\n  'data-garden-id': COMPONENT_ID$3,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSheetClose\",\n  componentId: \"sc-1ab02oq-0\"\n})([\"display:flex;position:absolute;top:\", \"px;\", \":\", \";align-items:center;justify-content:center;transition:box-shadow 0.1s ease-in-out,background-color 0.25s ease-in-out,color 0.25s ease-in-out;border:none;border-radius:50%;cursor:pointer;padding:0;width:\", \"px;height:\", \"px;overflow:hidden;text-decoration:none;font-size:0;user-select:none;&::-moz-focus-inner{border:0;}&:focus{outline:none;}\", \";& > svg{vertical-align:middle;}\", \";\"], props => props.theme.space.base * BASE_MULTIPLIERS.top, props => props.theme.rtl ? 'left' : 'right', props => `${props.theme.space.base * BASE_MULTIPLIERS.side}px`, props => props.theme.space.base * BASE_MULTIPLIERS.size, props => props.theme.space.base * BASE_MULTIPLIERS.size, props => colorStyles(props), props => retrieveComponentStyles(COMPONENT_ID$3, props));\nStyledSheetClose.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$2 = 'chrome.sheet_footer';\nconst StyledSheetFooter = styled.footer.attrs({\n  'data-garden-id': COMPONENT_ID$2,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSheetFooter\",\n  componentId: \"sc-2cktos-0\"\n})([\"display:flex;flex-flow:row wrap;align-items:center;justify-content:\", \";border-top:\", \";padding:\", \"px;\", \";\"], props => props.isCompact ? 'center' : 'flex-end', props => `${props.theme.borders.sm} ${getColor('neutralHue', 300, props.theme)}}`, props => props.theme.space.base * (props.isCompact ? 2.5 : 5), props => retrieveComponentStyles(COMPONENT_ID$2, props));\nStyledSheetFooter.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$1 = 'chrome.sheet_footer_item';\nconst StyledSheetFooterItem = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$1,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSheetFooterItem\",\n  componentId: \"sc-r9ixh-0\"\n})([\"\", \" \", \";\"], props => `margin-${props.theme.rtl ? 'right' : 'left'}: ${props.theme.space.base * 5}px;`, props => retrieveComponentStyles(COMPONENT_ID$1, props));\nStyledSheetFooterItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID = 'chrome.sheet_header';\nconst StyledSheetHeader = styled.header.attrs({\n  'data-garden-id': COMPONENT_ID,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSheetHeader\",\n  componentId: \"sc-o2ry8i-0\"\n})([\"border-bottom:\", \";padding:\", \"px;\", \"  \", \";\"], props => `${props.theme.borders.sm} ${getColor('neutralHue', 300, props.theme)}}`, props => props.theme.space.base * 5, props => props.isCloseButtonPresent && `padding-${props.theme.rtl ? 'left' : 'right'}: ${props.theme.space.base * (BASE_MULTIPLIERS.size + BASE_MULTIPLIERS.side + 2)}px;`, props => retrieveComponentStyles(COMPONENT_ID, props));\nStyledSheetHeader.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst Chrome = React__default.forwardRef((_ref, ref) => {\n  let {\n    hue,\n    isFluid,\n    ...props\n  } = _ref;\n  const theme = useContext(ThemeContext);\n  const isLightMemoized = useMemo(() => {\n    if (hue) {\n      const backgroundColor = getColor(hue, 600, theme);\n      const LIGHT_COLOR = 'white';\n      return readableColor(backgroundColor, LIGHT_COLOR, undefined, false ) === LIGHT_COLOR;\n    }\n    return false;\n  }, [hue, theme]);\n  const isLight = hue ? isLightMemoized : false;\n  const isDark = hue ? !isLightMemoized : false;\n  const chromeContextValue = useMemo(() => ({\n    hue: hue || 'chromeHue',\n    isLight,\n    isDark\n  }), [hue, isLight, isDark]);\n  const environment = useDocument(theme);\n  useEffect(() => {\n    if (environment && !isFluid) {\n      const htmlElement = environment.querySelector('html');\n      if (htmlElement) {\n        const defaultHtmlPosition = htmlElement.style.position;\n        htmlElement.style.position = 'fixed';\n        return () => {\n          htmlElement.style.position = defaultHtmlPosition;\n        };\n      }\n    }\n    return undefined;\n  }, [environment, isFluid]);\n  return React__default.createElement(ChromeContext.Provider, {\n    value: chromeContextValue\n  }, React__default.createElement(StyledChrome, _extends$3({\n    ref: ref\n  }, props)));\n});\nChrome.displayName = 'Chrome';\nChrome.propTypes = {\n  hue: PropTypes.string\n};\n\nconst SkipNav = React__default.forwardRef((_ref, ref) => {\n  let {\n    targetId,\n    zIndex,\n    children,\n    ...props\n  } = _ref;\n  return React__default.createElement(StyledSkipNav, _extends$3({\n    href: `#${targetId}`,\n    zIndex: zIndex,\n    ref: ref\n  }, props), React__default.createElement(StyledSkipNavIcon, null), children);\n});\nSkipNav.displayName = 'SkipNav';\nSkipNav.propTypes = {\n  targetId: PropTypes.string.isRequired,\n  zIndex: PropTypes.number\n};\nSkipNav.defaultProps = {\n  zIndex: 1\n};\n\nconst BodyContext = React__default.createContext({\n  hasFooter: true\n});\nconst useBodyContext = () => {\n  return useContext(BodyContext);\n};\n\nconst Body$1 = React__default.forwardRef((_ref, ref) => {\n  let {\n    hasFooter,\n    ...props\n  } = _ref;\n  const bodyContextValue = useMemo(() => ({\n    hasFooter: !!hasFooter\n  }), [hasFooter]);\n  return React__default.createElement(BodyContext.Provider, {\n    value: bodyContextValue\n  }, React__default.createElement(StyledBody, _extends$3({\n    ref: ref\n  }, props)));\n});\nBody$1.displayName = 'Body';\nBody$1.propTypes = {\n  hasFooter: PropTypes.bool\n};\n\nconst Content = React__default.forwardRef((props, ref) => {\n  const {\n    hasFooter\n  } = useBodyContext();\n  return React__default.createElement(StyledContent, _extends$3({\n    ref: ref,\n    hasFooter: hasFooter\n  }, props));\n});\nContent.displayName = 'Content';\n\nconst Main = React__default.forwardRef((props, ref) => React__default.createElement(StyledMain, _extends$3({\n  ref: ref\n}, props)));\nMain.displayName = 'Main';\n\nconst Sidebar = React__default.forwardRef((props, ref) => React__default.createElement(StyledSidebar, _extends$3({\n  ref: ref\n}, props)));\nSidebar.displayName = 'Sidebar';\n\nconst Header$1 = React__default.forwardRef((props, ref) => React__default.createElement(StyledHeader, _extends$3({\n  ref: ref\n}, props)));\nHeader$1.displayName = 'Header';\nHeader$1.propTypes = {\n  isStandalone: PropTypes.bool\n};\n\nconst PLACEMENT = ['end', 'start'];\nconst PRODUCT = ['chat', 'connect', 'explore', 'guide', 'message', 'support', 'talk'];\n\nconst HeaderItem = React__default.forwardRef((_ref, ref) => {\n  let {\n    hasLogo,\n    product,\n    ...other\n  } = _ref;\n  if (hasLogo) {\n    return React__default.createElement(StyledLogoHeaderItem, _extends$3({\n      ref: ref,\n      product: product\n    }, other));\n  }\n  return React__default.createElement(StyledHeaderItem, _extends$3({\n    ref: ref\n  }, other));\n});\nHeaderItem.displayName = 'HeaderItem';\nHeaderItem.propTypes = {\n  maxX: PropTypes.bool,\n  maxY: PropTypes.bool,\n  isRound: PropTypes.bool,\n  product: PropTypes.oneOf(PRODUCT),\n  hasLogo: PropTypes.bool\n};\n\nconst HeaderItemIcon = _ref => {\n  let {\n    children,\n    ...props\n  } = _ref;\n  const element = Children.only(children);\n  if ( isValidElement(element)) {\n    const Icon = _ref2 => {\n      let {\n        theme,\n        ...iconProps\n      } = _ref2;\n      return cloneElement(element, {\n        ...props,\n        ...iconProps\n      });\n    };\n    return React__default.createElement(StyledHeaderItemIcon, _extends$3({\n      as: Icon\n    }, props));\n  }\n  return null;\n};\n\nconst HeaderItemText = React__default.forwardRef((props, ref) => React__default.createElement(StyledHeaderItemText, _extends$3({\n  ref: ref\n}, props)));\nHeaderItemText.displayName = 'HeaderItemText';\nHeaderItemText.propTypes = {\n  isClipped: PropTypes.bool\n};\n\nconst HeaderItemWrapper = React__default.forwardRef((props, ref) => React__default.createElement(StyledHeaderItemWrapper, _extends$3({\n  ref: ref\n}, props)));\nHeaderItemWrapper.displayName = 'HeaderItemWrapper';\n\nconst Footer$1 = React__default.forwardRef((props, ref) => React__default.createElement(StyledFooter, _extends$3({\n  ref: ref\n}, props)));\nFooter$1.displayName = 'Footer';\n\nconst FooterItem$1 = React__default.forwardRef((props, ref) => React__default.createElement(StyledFooterItem, _extends$3({\n  ref: ref\n}, props)));\nFooterItem$1.displayName = 'FooterItem';\n\nconst NavContext = React__default.createContext({\n  isExpanded: false\n});\nconst useNavContext = () => {\n  return useContext(NavContext);\n};\n\nconst Nav = React__default.forwardRef((props, ref) => {\n  const {\n    hue,\n    isLight,\n    isDark\n  } = useChromeContext();\n  const navContextValue = useMemo(() => ({\n    isExpanded: !!props.isExpanded\n  }), [props.isExpanded]);\n  return React__default.createElement(NavContext.Provider, {\n    value: navContextValue\n  }, React__default.createElement(StyledNav, _extends$3({\n    ref: ref\n  }, props, {\n    hue: hue,\n    isLight: isLight,\n    isDark: isDark\n  })));\n});\nNav.displayName = 'Nav';\nNav.propTypes = {\n  isExpanded: PropTypes.bool\n};\n\nconst NavItem = React__default.forwardRef((_ref, ref) => {\n  let {\n    hasLogo,\n    hasBrandmark,\n    product,\n    ...other\n  } = _ref;\n  const {\n    hue,\n    isLight,\n    isDark\n  } = useChromeContext();\n  const {\n    isExpanded\n  } = useNavContext();\n  const ariaCurrent = other.isCurrent || undefined;\n  if (hasLogo) {\n    return React__default.createElement(StyledLogoNavItem, _extends$3({\n      ref: ref,\n      isDark: isDark,\n      isLight: isLight,\n      product: product,\n      \"aria-current\": ariaCurrent\n    }, other));\n  }\n  if (hasBrandmark) {\n    return React__default.createElement(StyledBrandmarkNavItem, _extends$3({\n      ref: ref\n    }, other));\n  }\n  return React__default.createElement(StyledNavItem, _extends$3({\n    tabIndex: 0,\n    ref: ref,\n    isExpanded: isExpanded,\n    hue: hue,\n    isDark: isDark,\n    isLight: isLight,\n    \"aria-current\": ariaCurrent\n  }, other));\n});\nNavItem.displayName = 'NavItem';\nNavItem.propTypes = {\n  product: PropTypes.oneOf(PRODUCT),\n  hasLogo: PropTypes.bool,\n  hasBrandmark: PropTypes.bool\n};\n\nconst NavItemIcon = _ref => {\n  let {\n    children,\n    ...props\n  } = _ref;\n  const element = Children.only(children);\n  if ( isValidElement(element)) {\n    const Icon = _ref2 => {\n      let {\n        theme,\n        ...iconProps\n      } = _ref2;\n      return cloneElement(element, {\n        ...props,\n        ...iconProps\n      });\n    };\n    return React__default.createElement(StyledNavItemIcon, _extends$3({\n      as: Icon\n    }, props));\n  }\n  return null;\n};\n\nconst NavItemText = React__default.forwardRef((props, ref) => {\n  const {\n    isExpanded\n  } = useNavContext();\n  return React__default.createElement(StyledNavItemText, _extends$3({\n    ref: ref,\n    isExpanded: isExpanded\n  }, props));\n});\nNavItemText.displayName = 'NavItemText';\nNavItemText.propTypes = {\n  isWrapped: PropTypes.bool\n};\n\nconst SubNav = React__default.forwardRef((props, ref) => {\n  const {\n    hue,\n    isLight,\n    isDark\n  } = useChromeContext();\n  return React__default.createElement(StyledSubNav, _extends$3({\n    ref: ref,\n    hue: hue,\n    isLight: isLight,\n    isDark: isDark\n  }, props));\n});\nSubNav.displayName = 'SubNav';\n\nconst SubNavItem = React__default.forwardRef((props, ref) => {\n  const {\n    isDark,\n    isLight\n  } = useChromeContext();\n  return React__default.createElement(StyledSubNavItem, _extends$3({\n    ref: ref,\n    isDark: isDark,\n    isLight: isLight\n  }, props));\n});\nSubNavItem.displayName = 'SubNavItem';\nSubNavItem.propTypes = {\n  isCurrent: PropTypes.bool\n};\n\nconst SubNavItemText = React__default.forwardRef((props, ref) => React__default.createElement(StyledSubNavItemText, _extends$3({\n  ref: ref\n}, props)));\nSubNavItemText.displayName = 'SubNavItemText';\nSubNavItemText.propTypes = {\n  isWrapped: PropTypes.bool\n};\n\nconst CollapsibleSubNavItem = React__default.forwardRef((_ref, ref) => {\n  let {\n    header,\n    children,\n    isExpanded: controlledExpanded,\n    onChange,\n    ...other\n  } = _ref;\n  const panelRef = useRef();\n  const [internalExpanded, setInternalExpanded] = useState(controlledExpanded);\n  const expanded = getControlledValue(controlledExpanded, internalExpanded);\n  const expandedSections = expanded ? [0] : [];\n  const {\n    getHeaderProps,\n    getTriggerProps,\n    getPanelProps\n  } = useAccordion({\n    expandedSections,\n    onChange: () => {\n      const isExpanded = expandedSections.length === 0;\n      if (onChange) {\n        onChange(isExpanded);\n      } else {\n        setInternalExpanded(isExpanded);\n      }\n    }\n  });\n  useEffect(() => {\n    if (expanded && panelRef.current) {\n      panelRef.current.style.maxHeight = `${panelRef.current.scrollHeight}px`;\n    }\n  }, [expanded, children]);\n  return React__default.createElement(\"div\", {\n    ref: ref\n  }, React__default.createElement(\"div\", getHeaderProps({\n    ariaLevel: 2\n  }), React__default.createElement(StyledSubNavItemHeader, getTriggerProps({\n    isExpanded: expanded,\n    index: 0,\n    role: null,\n    tabIndex: null,\n    ...other\n  }), React__default.createElement(React__default.Fragment, null, header, React__default.createElement(StyledSubNavItemIconWrapper, {\n    isExpanded: expanded\n  }, React__default.createElement(StyledSubNavItemIcon, null))))), React__default.createElement(StyledSubNavPanel, getPanelProps({\n    index: 0,\n    isHidden: !expanded,\n    ref: panelRef\n  }), children));\n});\nCollapsibleSubNavItem.propTypes = {\n  header: PropTypes.any,\n  isExpanded: PropTypes.bool,\n  onChange: PropTypes.func,\n  children: PropTypes.node\n};\nCollapsibleSubNavItem.displayName = 'CollapsibleSubNavItem';\n\nconst SheetContext = createContext({\n  setIsCloseButtonPresent() {}\n});\nconst useSheetContext = () => {\n  return useContext(SheetContext);\n};\n\nfunction ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}\n\nfunction activeElement(doc) {\n  if (doc === void 0) {\n    doc = ownerDocument();\n  }\n  try {\n    var active = doc.activeElement;\n    if (!active || !active.nodeName) return null;\n    return active;\n  } catch (e) {\n    return doc.body;\n  }\n}\n\nfunction useFocusableMount(_ref) {\n  let {\n    isMounted,\n    focusOnMount,\n    restoreFocus,\n    targetRef\n  } = _ref;\n  const triggerRef = useRef();\n  useEffect(() => {\n    if (isMounted && focusOnMount && targetRef.current) {\n      triggerRef.current = activeElement();\n      targetRef.current.focus();\n    }\n  }, [isMounted, focusOnMount, targetRef]);\n  useEffect(() => {\n    if (!isMounted && restoreFocus && triggerRef.current) {\n      triggerRef.current.focus();\n    }\n  }, [isMounted, restoreFocus, triggerRef]);\n}\n\nconst SheetTitle = forwardRef((_ref, ref) => {\n  let {\n    id,\n    ...props\n  } = _ref;\n  const {\n    titleId\n  } = useSheetContext();\n  return React__default.createElement(StyledSheetTitle, _extends$3({\n    id: id || titleId,\n    ref: ref\n  }, props));\n});\nSheetTitle.displayName = 'Sheet.Title';\nconst Title = SheetTitle;\n\nconst SheetDescription = forwardRef((_ref, ref) => {\n  let {\n    id,\n    ...props\n  } = _ref;\n  const {\n    descriptionId\n  } = useSheetContext();\n  return React__default.createElement(StyledSheetDescription, _extends$3({\n    id: id || descriptionId,\n    ref: ref\n  }, props));\n});\nSheetDescription.displayName = 'Sheet.Description';\nconst Description = SheetDescription;\n\nconst SheetHeader = forwardRef((props, ref) => {\n  const {\n    isCloseButtonPresent\n  } = useSheetContext();\n  return React__default.createElement(StyledSheetHeader, _extends$3({\n    ref: ref,\n    isCloseButtonPresent: isCloseButtonPresent\n  }, props));\n});\nSheetHeader.displayName = 'Sheet.Header';\nconst Header = SheetHeader;\n\nconst SheetBody = forwardRef((props, ref) => {\n  return React__default.createElement(StyledSheetBody, _extends$3({\n    ref: ref\n  }, props));\n});\nSheetBody.displayName = 'Sheet.Body';\nconst Body = SheetBody;\n\nconst SheetFooter = forwardRef((props, ref) => {\n  return React__default.createElement(StyledSheetFooter, _extends$3({\n    ref: ref\n  }, props));\n});\nSheetFooter.displayName = 'Sheet.Footer';\nconst Footer = SheetFooter;\n\nconst SheetFooterItem = forwardRef((props, ref) => {\n  return React__default.createElement(StyledSheetFooterItem, _extends$3({\n    ref: ref\n  }, props));\n});\nSheetFooterItem.displayName = 'Sheet.FooterItem';\nconst FooterItem = SheetFooterItem;\n\nvar _path;\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nvar SvgXStroke = function SvgXStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 16,\n    height: 16,\n    focusable: \"false\",\n    viewBox: \"0 0 16 16\",\n    \"aria-hidden\": \"true\"\n  }, props), _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    stroke: \"currentColor\",\n    strokeLinecap: \"round\",\n    d: \"M3 13L13 3m0 10L3 3\"\n  })));\n};\n\nconst SheetClose = forwardRef((props, ref) => {\n  const {\n    setIsCloseButtonPresent\n  } = useSheetContext();\n  useEffect(() => {\n    setIsCloseButtonPresent(true);\n    return () => setIsCloseButtonPresent(false);\n  }, [setIsCloseButtonPresent]);\n  return React__default.createElement(StyledSheetClose, _extends$3({\n    \"aria-label\": \"Close Sheet\",\n    ref: ref\n  }, props), React__default.createElement(SvgXStroke, null));\n});\nSheetClose.displayName = 'Sheet.Close';\nconst Close = SheetClose;\n\nconst SheetComponent = React__default.forwardRef((_ref, ref) => {\n  let {\n    id,\n    isOpen,\n    isAnimated,\n    focusOnMount,\n    restoreFocus,\n    placement,\n    size,\n    children,\n    ...props\n  } = _ref;\n  const sheetRef = useRef(null);\n  const seed = useUIDSeed();\n  const [isCloseButtonPresent, setIsCloseButtonPresent] = useState(false);\n  const idPrefix = useMemo(() => id || seed(`sheet_${'8.67.0'}`), [id, seed]);\n  const titleId = `${idPrefix}--title`;\n  const descriptionId = `${idPrefix}--description`;\n  const sheetContext = useMemo(() => ({\n    titleId,\n    descriptionId,\n    isCloseButtonPresent,\n    setIsCloseButtonPresent\n  }), [titleId, descriptionId, isCloseButtonPresent]);\n  useFocusableMount({\n    targetRef: sheetRef,\n    isMounted: isOpen,\n    focusOnMount,\n    restoreFocus\n  });\n  return React__default.createElement(SheetContext.Provider, {\n    value: sheetContext\n  }, React__default.createElement(StyledSheet, _extends$3({\n    isOpen: isOpen,\n    isAnimated: isAnimated,\n    placement: placement,\n    size: size,\n    tabIndex: -1,\n    id: idPrefix,\n    \"aria-labelledby\": titleId,\n    \"aria-describedby\": descriptionId,\n    ref: mergeRefs([sheetRef, ref])\n  }, props), React__default.createElement(StyledSheetWrapper, {\n    isOpen: isOpen,\n    isAnimated: isAnimated,\n    placement: placement,\n    size: size\n  }, children)));\n});\nSheetComponent.displayName = 'Sheet';\nSheetComponent.propTypes = {\n  id: PropTypes.string,\n  isOpen: PropTypes.bool,\n  isAnimated: PropTypes.bool,\n  focusOnMount: PropTypes.bool,\n  restoreFocus: PropTypes.bool,\n  placement: PropTypes.oneOf(PLACEMENT),\n  size: PropTypes.string\n};\nSheetComponent.defaultProps = {\n  isAnimated: true,\n  placement: 'end',\n  size: '380px'\n};\nconst Sheet = SheetComponent;\nSheet.Body = Body;\nSheet.Close = Close;\nSheet.Description = Description;\nSheet.Footer = Footer;\nSheet.FooterItem = FooterItem;\nSheet.Header = Header;\nSheet.Title = Title;\n\nexport { Body$1 as Body, Chrome, CollapsibleSubNavItem, Content, Footer$1 as Footer, FooterItem$1 as FooterItem, Header$1 as Header, HeaderItem, HeaderItemIcon, HeaderItemText, HeaderItemWrapper, Main, Nav, NavItem, NavItemIcon, NavItemText, PRODUCT as PRODUCTS, Sheet, Sidebar, SkipNav, SubNav, SubNavItem, SubNavItemText };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,YAAuB;AACvB,mBAAoJ;AACpJ,wBAAsB;AAStB,SAAS,aAAa;AACpB,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,WAAW,MAAM,MAAM,SAAS;AACzC;AAEA,IAAM,gBAAgB,aAAAA,QAAe,cAAc;AAAA,EACjD,KAAK;AACP,CAAC;AACD,IAAM,mBAAmB,MAAM;AAC7B,aAAO,yBAAW,aAAa;AACjC;AAEA,IAAM,iBAAiB;AACvB,IAAM,eAAe,sCAAO,IAAI,MAAM;AAAA,EACpC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,uFAAuF,eAAe,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,QAAQ,WAAS,MAAM,MAAM,OAAO,OAAO,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAClP,aAAa,eAAe;AAAA,EAC1B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,uBAAuB,sCAAO,IAAI,MAAM;AAAA,EAC5C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,8DAA8D,eAAe,YAAY,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACxQ,qBAAqB,eAAe;AAAA,EAClC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,oBAAoB,WAAS,GAAG,MAAM,MAAM,MAAM,OAAO;AAC/D,IAAM,eAAe,WAAS;AAC5B,QAAM,OAAO,MAAM,MAAM,MAAM,OAAO;AACtC,SAAO,GAAI,CAAC,4BAA4B,cAAc,iBAAiB,GAAG,GAAG,MAAM,MAAM,OAAO,SAAS,GAAG,UAAU,cAAc,MAAM,MAAM,MAAM,UAAU,EAAE,CAAC;AACrK;AACA,IAAM,uBAAuB,sCAAO,OAAO,MAAM;AAAA,EAC/C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,+CAA+C,wCAAwC,4FAA4F,+BAA+B,oGAAoG,KAAK,GAAG,GAAG,WAAS,MAAM,QAAQ,KAAK,WAAS,MAAM,OAAO,UAAU,UAAU,WAAS,KAAK,MAAM,MAAM,MAAM,OAAO,OAAO,WAAS;AAChc,MAAI,MAAM,SAAS;AACjB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,MAAM;AACd,WAAO;AAAA,EACT;AACA,SAAO,MAAM,MAAM,YAAY;AACjC,GAAG,cAAc,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACxE,qBAAqB,eAAe;AAAA,EAClC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,GAAI,CAAC,gHAAgH,CAAC;AAC7I,IAAM,uBAAuB,sCAAO,KAAK,MAAM;AAAA,EAC7C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,iBAAiB,KAAK,GAAG,GAAG,WAAS,MAAM,aAAa,gBAAgB,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACnI,qBAAqB,eAAe;AAAA,EAClC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,QAAM,QAAQ,MAAM,UAAU,MAAM,UAAU,MAAM;AACpD,QAAM,kBAAkB,SAAS,MAAM,KAAK,OAAO,MAAM,KAAK;AAC9D,QAAM,kBAAkB,MAAM,UAAU,MAAM,MAAM,QAAQ,KAAK,GAAG,IAAI,MAAM,MAAM,QAAQ;AAC5F,SAAO,GAAI,CAAC,qBAAqB,WAAW,GAAG,GAAG,iBAAiB,eAAe;AACpF;AACA,IAAM,cAAc,WAAS;AAC3B,SAAO,GAAG,MAAM,MAAM,MAAM,OAAO;AACrC;AACA,IAAM,sBAAsB,MAAM;AAChC,SAAO;AACT;AACA,IAAM,YAAY,sCAAO,IAAI,MAAM;AAAA,EACjC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,sFAAsF,eAAe,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,aAAa,sBAAsB,aAAa,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,cAAc,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACjT,UAAU,eAAe;AAAA,EACvB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,yBAAyB,WAAS;AACtC,UAAQ,MAAM,SAAS;AAAA,IACrB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACE,aAAO;AAAA,EACX;AACF;AACA,IAAM,uBAAuB,sCAAO,oBAAoB,EAAE,MAAM;AAAA,EAC9D,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,IAAI;AACN,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,sCAAsC,iBAAiB,YAAY,KAAK,qCAAqC,sCAAsC,gCAAgC,KAAK,KAAK,KAAK,oBAAoB,YAAY,MAAM,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,WAAW,QAAQ,WAAS,MAAM,MAAM,MAAM,SAAS,IAAI,MAAM,MAAM,MAAM,UAAU,WAAS,MAAM,MAAM,MAAM,SAAS,SAAS,WAAS,GAAG,MAAM,MAAM,QAAQ,MAAM,SAAS,cAAc,KAAK,MAAM,KAAK,KAAK,WAAS,YAAY,KAAK,GAAG,WAAS,SAAS,aAAa,KAAK,MAAM,KAAK,GAAG,WAAS,uBAAuB,KAAK,GAAG,sBAAsB,gBAAgB,sBAAsB,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACzyB,qBAAqB,eAAe;AAAA,EAClC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB,WAAS;AAChC,SAAO,GAAG,MAAM,MAAM,MAAM,OAAO;AACrC;AACA,IAAM,eAAe,WAAS;AAC5B,QAAM,kBAAkB,KAAK,IAAI,iBAAiB,KAAK,OAAO,MAAM,MAAM,UAAU,SAAS;AAC7F,QAAM,oBAAoB,KAAK,IAAI,YAAY,KAAK,OAAO,MAAM,MAAM,UAAU,SAAS;AAC1F,SAAO,GAAI,CAAC,YAAY,KAAK,gBAAgB,GAAG,GAAG,iBAAiB,mBAAmB,gBAAgB;AACzG;AACA,IAAM,oBAAoB,sCAAO,OAAO,MAAM;AAAA,EAC5C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,yRAAyR,EAAE,GAAG,WAAS,aAAa,KAAK,CAAC;AAC9T,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,WAAS;AAC/B,SAAO,iBAAiB,KAAK;AAC/B;AACA,IAAM,eAAe,sCAAO,OAAO,MAAM;AAAA,EACvC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,0BAA0B,qFAAqF,gBAAgB,sBAAsB,eAAe,cAAc,WAAW,eAAe,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,gBAAgB,YAAY,WAAS,GAAG,MAAM,MAAM,QAAQ,MAAM,SAAS,cAAc,KAAK,MAAM,KAAK,KAAK,WAAS,MAAM,gBAAgB,MAAM,MAAM,QAAQ,GAAG,KAAK,QAAQ,SAAS,aAAa,KAAK,MAAM,OAAO,IAAI,CAAC,GAAG,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,MAAM,MAAM,MAAM,MAAM,iBAAiB,WAAS,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,gBAAgB;AAAA,MACzpB;AAAA;AAAA;AAAA,KAGD,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC5D,aAAa,eAAe;AAAA,EAC1B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,MAAM;AAC5B,QAAM,gBAAgB,GAAU,CAAC,qCAAqC,CAAC;AACvE,SAAO,GAAI,CAAC,kLAAkL,6CAA6C,GAAG,aAAa;AAC7P;AACA,IAAM,gBAAgB,WAAS;AAC7B,QAAM,QAAQ,SAAS,cAAc,KAAK,KAAK;AAC/C,QAAM,cAAc,SAAS,cAAc,KAAK,KAAK;AACrD,QAAM,YAAY,MAAM,QAAQ,GAAG,GAAG,MAAM,MAAM,OAAO,OAAO,GAAG,MAAM,MAAM,OAAO,OAAO,SAAS,aAAa,KAAK,OAAO,IAAI,CAAC;AACpI,SAAO,GAAI,CAAC,iBAAiB,gBAAgB,sBAAsB,WAAW,2BAA2B,IAAI,GAAG,aAAa,WAAW,MAAM,OAAO,YAAY,OAAO,KAAK;AAC/K;AACA,IAAM,eAAe,WAAS;AAC5B,QAAM,MAAM,KAAK,GAAG,gBAAgB,KAAK,OAAO;AAChD,QAAM,UAAU,GAAG,MAAM,MAAM,MAAM,OAAO;AAC5C,QAAM,eAAe,GAAG,MAAM,MAAM,MAAM,OAAO;AACjD,QAAM,WAAW,MAAM,MAAM,UAAU;AACvC,QAAM,aAAa,cAAc,SAAS,QAAQ;AAClD,SAAO,GAAI,CAAC,QAAQ,aAAa,aAAa,KAAK,iBAAiB,eAAe,GAAG,GAAG,KAAK,SAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,cAAc,YAAY,QAAQ;AAC/K;AACA,IAAM,gBAAgB,sCAAO,EAAE,MAAM;AAAA,EACnC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,mIAAmI,aAAa,YAAY,mBAAmB,kDAAkD,2BAA2B,KAAK,GAAG,GAAG,gBAAgB,GAAG,WAAS,MAAM,MAAM,OAAO,OAAO,WAAS,MAAM,QAAQ,WAAS,MAAM,MAAM,QAAQ,IAAI,WAAS,MAAM,MAAM,YAAY,IAAI,WAAS,aAAa,KAAK,GAAG,WAAS,cAAc,MAAM,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC5hB,cAAc,eAAe;AAAA,EAC3B,OAAO;AACT;AAEA,IAAI;AACJ,SAAS,aAAa;AAAE,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,WAAW,MAAM,MAAM,SAAS;AAAG;AACxV,IAAI,gBAAgB,SAASC,eAAc,OAAO;AAChD,SAA0B,oBAAc,OAAO,WAAW;AAAA,IACxD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,YAAY,UAA6B,oBAAc,QAAQ;AAAA,IACxE,MAAM;AAAA,IACN,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAM,iBAAiB;AACvB,IAAM,eAAe,WAAS;AAC5B,QAAM,SAAS,GAAG,MAAM,MAAM,OAAO;AACrC,QAAM,OAAO,MAAM,UAAU;AAC7B,SAAO,GAAI,CAAC,WAAW,KAAK,WAAW,YAAY,GAAG,GAAG,MAAM,MAAM,SAAS,SAAS,QAAQ,MAAM,IAAI;AAC3G;AACA,IAAM,oBAAoB,sCAAO,aAAa,EAAE,MAAM;AAAA,EACpD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,cAAc,WAAW,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,OAAO,cAAc,WAAS,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,aAAa,MAAM,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACrO,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,aAAa,sCAAO,IAAI,MAAM;AAAA,EAClC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,oCAAoC,iBAAiB,GAAG,GAAG,WAAS,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACzK,WAAW,eAAe;AAAA,EACxB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,WAAS;AAC/B,SAAO,GAAG,MAAM,MAAM,MAAM,OAAO;AACrC;AACA,IAAM,eAAe,sCAAO,OAAO,MAAM;AAAA,EACvC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,8FAA8F,sBAAsB,eAAe,cAAc,KAAK,GAAG,GAAG,WAAS,GAAG,MAAM,MAAM,QAAQ,MAAM,SAAS,cAAc,KAAK,MAAM,KAAK,KAAK,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,iBAAiB,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACvY,aAAa,eAAe;AAAA,EAC1B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,sCAAO,IAAI,MAAM;AAAA,EACrC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,wBAAwB,iBAAiB,WAAW,eAAe,2BAA2B,GAAG,GAAG,WAAS,MAAM,YAAY,eAAe,KAAK,GAAG,gBAAgB,KAAK,OAAO,gBAAgB,KAAK,GAAG,OAAO,eAAe,gBAAgB,KAAK,MAAM,WAAS,cAAc,MAAM,MAAM,YAAY,IAAI,MAAM,MAAM,UAAU,EAAE,GAAG,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC/c,cAAc,eAAe;AAAA,EAC3B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,aAAa,sCAAO,KAAK,MAAM;AAAA,EACnC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,oCAAoC,wCAAwC,GAAG,GAAG,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACrL,WAAW,eAAe;AAAA,EACxB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,sCAAO,MAAM,MAAM;AAAA,EACvC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,uDAAuD,KAAK,qDAAqD,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,SAAS,SAAS,WAAS,GAAG,MAAM,MAAM,QAAQ,MAAM,SAAS,cAAc,KAAK,MAAM,KAAK,KAAK,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACjT,cAAc,eAAe;AAAA,EAC3B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB,sCAAO,IAAI,MAAM;AAAA,EACxC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,WAAW,KAAK,GAAG,GAAG,WAAS,KAAK,MAAM,MAAM,MAAM,UAAU,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC3H,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,YAAY,WAAS;AACzB,QAAM,OAAO,KAAK,GAAG,kBAAkB,KAAK,OAAO,MAAM,MAAM,MAAM,OAAO,GAAG;AAC/E,SAAO,GAAI,CAAC,+BAA+B,WAAW,YAAY,IAAI,GAAG,KAAK,GAAG,MAAM,MAAM,YAAY,QAAQ,GAAG,MAAM,IAAI;AAChI;AACA,IAAM,mBAAmB,sCAAO,oBAAoB,EAAE,MAAM;AAAA,EAC1D,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,sHAAsH,mEAAmE,aAAa,cAAc,cAAc,WAAW,MAAM,KAAK,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,QAAQ,GAAG,SAAS,aAAa,KAAK,MAAM,OAAO,IAAI,CAAC,GAAG,sBAAsB,sBAAsB,sBAAsB,sBAAsB,WAAS,SAAS,aAAa,KAAK,MAAM,KAAK,GAAG,WAAS,MAAM,QAAQ;AAAA;AAAA,4BAEje,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,aAAa,IAAI,KAAK,SAAS,aAAa,KAAK,MAAM,OAAO,IAAI,CAAC;AAAA,UACxH,MAAM,MAAM,aAAa,YAAY,SAAS,aAAa,KAAK,MAAM,OAAO,IAAI;AAAA,gBAC3E,MAAM,MAAM,QAAQ,GAAG,IAAI,MAAM,MAAM,aAAa,MAAM,KAAK,SAAS,aAAa,KAAK,MAAM,OAAO,IAAI,CAAC;AAAA,WACjH,MAAM,MAAM,aAAa,YAAY,SAAS,aAAa,KAAK,MAAM,OAAO,IAAI;AAAA;AAAA,KAEvF,WAAW,WAAS,MAAM,WAAW;AAAA,MACpC;AAAA;AAAA;AAAA,KAGD,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC5D,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,0BAA0B,sCAAO,oBAAoB,EAAE,MAAM;AAAA,EACjE,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,IAAI;AACN,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACrE,wBAAwB,eAAe;AAAA,EACrC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,uBAAuB,aAAW;AACtC,UAAQ,SAAS;AAAA,IACf,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB,KAAK;AACH,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACE,aAAO;AAAA,EACX;AACF;AACA,IAAM,gBAAgB,WAAS;AAC7B,QAAM,YAAY,MAAM,UAAU,MAAM,MAAM,QAAQ,KAAK,GAAG,IAAI,MAAM,MAAM,QAAQ;AACtF,QAAM,QAAQ,MAAM,WAAW,MAAM,SAAS,YAAY,qBAAqB,MAAM,OAAO;AAC5F,SAAO,GAAI,CAAC,UAAU,UAAU,GAAG,GAAG,OAAO,SAAS;AACxD;AACA,IAAM,oBAAoB,sCAAO,iBAAiB,EAAE,MAAM;AAAA,EACxD,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,IAAI;AACN,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,qCAAqC,GAAG,GAAG,WAAS,cAAc,KAAK,CAAC;AAC5E,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,yBAAyB,sCAAO,iBAAiB,EAAE,MAAM;AAAA,EAC7D,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,IAAI;AACN,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,sCAAsC,CAAC;AAC3C,uBAAuB,eAAe;AAAA,EACpC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,oBAAoB,sCAAO,IAAI,MAAM;AAAA,EACzC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,gDAAgD,WAAW,YAAY,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,YAAY,IAAI,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACxP,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,QAAM,QAAQ,MAAM,MAAM,QAAQ;AAClC,QAAM,QAAQ,MAAM,MAAM,QAAQ;AAClC,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM,WAAW;AACnB,QAAI,MAAM,SAAS;AACjB,qBAAe,KAAK,OAAO,GAAG;AAAA,IAChC,WAAW,MAAM,QAAQ;AACvB,qBAAe,KAAK,OAAO,GAAG;AAAA,IAChC,OAAO;AACL,qBAAe,SAAS,MAAM,KAAK,KAAK,MAAM,KAAK;AAAA,IACrD;AAAA,EACF,OAAO;AACL,iBAAa,KAAK,MAAM,UAAU,QAAQ,OAAO,GAAG;AAAA,EACtD;AACA,QAAM,cAAc,KAAK,MAAM,UAAU,QAAQ,OAAO,GAAG;AAC3D,QAAM,aAAa,KAAK,MAAM,UAAU,QAAQ,OAAO,GAAG;AAC1D,SAAO,GAAI,CAAC,YAAY,sBAAsB,wCAAwC,8DAA8D,gCAAgC,IAAI,GAAG,MAAM,YAAY,IAAI,KAAK,cAAc,YAAY,MAAM,MAAM,QAAQ,GAAG,UAAU,GAAG,WAAW;AACjS;AACA,IAAM,gBAAgB,sCAAO,iBAAiB,EAAE,MAAM;AAAA,EACpD,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,IAAI;AACN,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,oBAAoB,6BAA6B,gBAAgB,+EAA+E,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,cAAc,SAAS,WAAS,MAAM,YAAY,YAAY,WAAW,WAAS,MAAM,cAAc,WAAW,WAAS,cAAc,KAAK,GAAG,WAAS,MAAM,cAAc;AAAA,MAC1V;AAAA,kBACY,KAAK,IAAI,YAAY,KAAK,OAAO,MAAM,MAAM,UAAU,SAAS;AAAA;AAAA,KAE7E,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC5D,cAAc,eAAe;AAAA,EAC3B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,oBAAoB,sCAAO,KAAK,MAAM;AAAA,EAC1C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,gEAAgE,sDAAsD,iBAAiB,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,cAAc,KAAK,KAAK,IAAI,YAAY,KAAK,OAAO,MAAM,MAAM,UAAU,SAAS,KAAK,WAAS,cAAc,MAAM,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,UAAU,EAAE,GAAG,WAAS,MAAM,YAAY,WAAW,UAAU,WAAS,MAAM,cAAc;AAAA,MACvZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAQD,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC5D,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,QAAM,QAAQ,MAAM,MAAM,QAAQ;AAClC,QAAM,QAAQ,MAAM,MAAM,QAAQ;AAClC,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM,WAAW;AACnB,QAAI,MAAM,SAAS;AACjB,qBAAe,KAAK,OAAO,GAAG;AAAA,IAChC,OAAO;AACL,qBAAe,KAAK,OAAO,GAAG;AAAA,IAChC;AAAA,EACF,OAAO;AACL,iBAAa,KAAK,MAAM,UAAU,QAAQ,OAAO,GAAG;AAAA,EACtD;AACA,QAAM,cAAc,KAAK,MAAM,UAAU,QAAQ,OAAO,IAAI;AAC5D,QAAM,aAAa,KAAK,MAAM,UAAU,QAAQ,OAAO,GAAG;AAC1D,SAAO,GAAI,CAAC,YAAY,sBAAsB,wCAAwC,wDAAwD,iEAAiE,IAAI,GAAG,MAAM,YAAY,MAAM,OAAO,cAAc,YAAY,MAAM,MAAM,QAAQ,GAAG,UAAU,GAAG,WAAW;AAChU;AACA,IAAM,sBAAsB,WAAS;AACnC,SAAO,GAAG,MAAM,MAAM,MAAM,OAAO;AACrC;AACA,IAAM,mBAAmB,sCAAO,OAAO,MAAM;AAAA,EAC3C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,6IAA6I,qCAAqC,yDAAyD,aAAa,2BAA2B,sHAAsH,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,MAAM,YAAY,IAAI,WAAS,MAAM,YAAY,YAAY,WAAW,WAAS,KAAK,MAAM,MAAM,MAAM,OAAO,OAAO,qBAAqB,WAAS,cAAc,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC7qB,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,MAAI;AACJ,MAAI,MAAM,SAAS;AACjB,YAAQ;AAAA,EACV,OAAO;AACL,YAAQ,MAAM,SAAS,MAAM;AAAA,EAC/B;AACA,QAAM,kBAAkB,SAAS,MAAM,KAAK,OAAO,MAAM,KAAK;AAC9D,QAAM,kBAAkB,MAAM,UAAU,MAAM,MAAM,QAAQ,KAAK,GAAG,IAAI,MAAM,MAAM,QAAQ;AAC5F,SAAO,GAAI,CAAC,qBAAqB,WAAW,GAAG,GAAG,iBAAiB,eAAe;AACpF;AACA,IAAM,eAAe,sCAAO,IAAI,MAAM;AAAA,EACpC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,0CAA0C,6CAA6C,KAAK,SAAS,+BAA+B,GAAG,GAAG,WAAS,GAAG,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,cAAc,KAAK,GAAG,kBAAkB,WAAS,wBAAwB,iBAAiB,KAAK,CAAC;AACtW,aAAa,eAAe;AAAA,EAC1B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,aAAa,WAAS;AAC1B,QAAM,iBAAiB,MAAM,MAAM,MAAM,OAAO;AAChD,QAAM,iBAAiB,KAAK,IAAI,oBAAoB,KAAK,OAAO,qBAAqB;AACrF,QAAM,aAAa,cAAc,gBAAgB,MAAM,MAAM,UAAU,EAAE;AACzE,SAAO,GAAI,CAAC,WAAW,mBAAmB,GAAG,GAAG,gBAAgB,UAAU;AAC5E;AACA,IAAM,uBAAuB,sCAAO,KAAK,MAAM;AAAA,EAC7C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,uDAAuD,KAAK,KAAK,GAAG,GAAG,WAAS,MAAM,YAAY,WAAW,UAAU,WAAS,WAAW,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC9M,qBAAqB,eAAe;AAAA,EAClC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,yBAAyB,sCAAO,gBAAgB,EAAE,MAAM;AAAA,EAC5D,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,sBAAsB;AACxB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,8BAA8B,KAAK,OAAO,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,SAAS,SAAS,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC7L,uBAAuB,eAAe;AAAA,EACpC,OAAO;AACT;AAEA,IAAI;AACJ,SAAS,aAAa;AAAE,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,WAAW,MAAM,MAAM,SAAS;AAAG;AACxV,IAAI,uBAAuB,SAASC,sBAAqB,OAAO;AAC9D,SAA0B,oBAAc,OAAO,WAAW;AAAA,IACxD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,YAAY,UAA6B,oBAAc,QAAQ;AAAA,IACxE,MAAM;AAAA,IACN,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAM,iBAAiB;AACvB,IAAM,gCAAgC,aAAAF,QAAe,WAAW,CAAC,MAAM,QAAQ;AAC7E,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,aAAAA,QAAe,cAAc,sBAAsB,WAAW;AAAA,IACnE;AAAA,EACF,GAAG,UAAU,CAAC;AAChB,CAAC;AACD,8BAA8B,cAAc;AAC5C,IAAM,uBAAuB,sCAAO,6BAA6B,EAAE,WAAW;AAAA,EAC5E,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,UAAU,YAAY,GAAG,GAAG,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,MAAM,UAAU,EAAE;AACpG,qBAAqB,eAAe;AAAA,EAClC,OAAO;AACT;AACA,IAAM,8BAA8B,sCAAO,IAAI,MAAM;AAAA,EACnD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,+CAA+C,UAAU,qDAAqD,cAAc,KAAK,eAAe,6CAA6C,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,SAAS,GAAG,WAAS,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,qBAAqB,sBAAsB,WAAS;AACpW,MAAI,MAAM,YAAY;AACpB,WAAO,GAAI,CAAC,WAAW,SAAS,GAAG,MAAM,MAAM,OAAO,GAAG;AAAA,EAC3D;AACA,SAAO;AACT,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC1D,4BAA4B,eAAe;AAAA,EACzC,OAAO;AACT;AAEA,IAAM,qBAAqB;AAC3B,IAAM,gBAAgB,GAAI,CAAC,4DAA4D,CAAC;AACxF,IAAM,oBAAoB,sCAAO,IAAI,MAAM;AAAA,EACzC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,wHAAwH,KAAK,aAAa,KAAK,MAAM,GAAG,GAAG,WAAS,MAAM,YAAY,eAAe,kBAAkB,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,GAAG,MAAM,MAAM,MAAM,OAAO,OAAO,WAAS,wBAAwB,oBAAoB,KAAK,CAAC;AAC/W,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,cAAc,UAAQ;AAC1B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,cAAc,SAAS,SAAS,cAAc,KAAK,KAAK,IAAI;AAClE,QAAM,cAAc,CAAC,SAAS,QAAQ;AACtC,MAAI,aAAa;AACjB,MAAI,MAAM,KAAK;AACb,gBAAY,QAAQ;AAAA,EACtB;AACA,MAAI,cAAc,OAAO;AACvB,iBAAa,YAAY,CAAC;AAAA,EAC5B,WAAW,cAAc,SAAS;AAChC,iBAAa,YAAY,CAAC;AAAA,EAC5B;AACA,SAAO,SAAS,eAAe,MAAM,QAAQ,MAAM;AACrD;AACA,IAAM,cAAc,sCAAO,MAAM,MAAM;AAAA,EACrC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,oCAAoC,sBAAsB,WAAW,2CAA2C,2BAA2B,KAAK,GAAG,GAAG,WAAS,MAAM,cAAc,2BAA2B,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,MAAM,SAAS,MAAM,OAAO,OAAO,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,YAAY,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC7Z,YAAY,eAAe;AAAA,EACzB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,qBAAqB,sCAAO,IAAI,MAAM;AAAA,EAC1C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,mEAAmE,gBAAgB,eAAe,KAAK,GAAG,GAAG,WAAS;AACxH,QAAM,kBAAkB,CAAC,MAAM,GAAG;AAClC,MAAI,cAAc;AAClB,MAAI,MAAM,QAAQ;AAChB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,MAAM,KAAK;AACnB,oBAAgB,QAAQ;AAAA,EAC1B;AACA,MAAI,MAAM,cAAc,OAAO;AAC7B,kBAAc,cAAc,gBAAgB,CAAC;AAAA,EAC/C,WAAW,MAAM,cAAc,SAAS;AACtC,kBAAc,cAAc,gBAAgB,CAAC;AAAA,EAC/C;AACA,SAAO;AACT,GAAG,WAAS,MAAM,cAAc,+BAA+B,WAAS,MAAM,MAAM,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC3I,mBAAmB,eAAe;AAAA,EAChC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB,sCAAO,IAAI,MAAM;AAAA,EACxC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,gBAAgB,WAAW,iBAAiB,KAAK,GAAG,GAAG,WAAS,cAAc,MAAM,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,UAAU,EAAE,GAAG,WAAS,MAAM,MAAM,OAAO,YAAY,WAAS,MAAM,MAAM,YAAY,UAAU,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACnR,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,yBAAyB,sCAAO,IAAI,MAAM;AAAA,EAC9C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,gBAAgB,WAAW,KAAK,GAAG,GAAG,WAAS,cAAc,MAAM,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,UAAU,EAAE,GAAG,WAAS,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAClO,uBAAuB,eAAe;AAAA,EACpC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,sCAAO,IAAI,MAAM;AAAA,EACvC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,mCAAmC,OAAO,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAChJ,gBAAgB,eAAe;AAAA,EAC7B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AAAA,EACvB,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AACR;AACA,IAAM,cAAc,WAAS;AAC3B,QAAM,kBAAkB;AACxB,QAAM,kBAAkB;AACxB,SAAO,GAAI,CAAC,uCAAuC,8BAA8B,WAAW,8CAA8C,oGAAoG,WAAW,IAAI,GAAG,SAAS,iBAAiB,KAAK,MAAM,KAAK,GAAG,SAAS,iBAAiB,KAAK,MAAM,OAAO,IAAI,GAAG,SAAS,iBAAiB,KAAK,MAAM,KAAK,GAAG,MAAM,MAAM,QAAQ,GAAG,SAAS,iBAAiB,KAAK,MAAM,OAAO,IAAI,CAAC,GAAG,SAAS,iBAAiB,KAAK,MAAM,OAAO,GAAG,GAAG,SAAS,iBAAiB,KAAK,MAAM,KAAK,CAAC;AACvjB;AACA,IAAM,mBAAmB,sCAAO,OAAO,MAAM;AAAA,EAC3C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,uCAAuC,OAAO,KAAK,8MAA8M,cAAc,6HAA6H,oCAAoC,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,iBAAiB,KAAK,WAAS,MAAM,MAAM,MAAM,SAAS,SAAS,WAAS,GAAG,MAAM,MAAM,MAAM,OAAO,iBAAiB,UAAU,WAAS,MAAM,MAAM,MAAM,OAAO,iBAAiB,MAAM,WAAS,MAAM,MAAM,MAAM,OAAO,iBAAiB,MAAM,WAAS,YAAY,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACryB,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,oBAAoB,sCAAO,OAAO,MAAM;AAAA,EAC5C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,uEAAuE,gBAAgB,aAAa,OAAO,GAAG,GAAG,WAAS,MAAM,YAAY,WAAW,YAAY,WAAS,GAAG,MAAM,MAAM,QAAQ,MAAM,SAAS,cAAc,KAAK,MAAM,KAAK,MAAM,WAAS,MAAM,MAAM,MAAM,QAAQ,MAAM,YAAY,MAAM,IAAI,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAChX,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,wBAAwB,sCAAO,IAAI,MAAM;AAAA,EAC7C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,GAAG,WAAS,UAAU,MAAM,MAAM,MAAM,UAAU,WAAW,MAAM,MAAM,MAAM,OAAO,QAAQ,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACrK,sBAAsB,eAAe;AAAA,EACnC,OAAO;AACT;AAEA,IAAM,eAAe;AACrB,IAAM,oBAAoB,sCAAO,OAAO,MAAM;AAAA,EAC5C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,kBAAkB,aAAa,OAAO,MAAM,GAAG,GAAG,WAAS,GAAG,MAAM,MAAM,QAAQ,MAAM,SAAS,cAAc,KAAK,MAAM,KAAK,MAAM,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,wBAAwB,WAAW,MAAM,MAAM,MAAM,SAAS,YAAY,MAAM,MAAM,MAAM,QAAQ,iBAAiB,OAAO,iBAAiB,OAAO,SAAS,WAAS,wBAAwB,cAAc,KAAK,CAAC;AAC/Y,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,SAAS,aAAAA,QAAe,WAAW,CAAC,MAAM,QAAQ;AACtD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAQ,yBAAW,EAAY;AACrC,QAAM,sBAAkB,sBAAQ,MAAM;AACpC,QAAI,KAAK;AACP,YAAM,kBAAkB,SAAS,KAAK,KAAK,KAAK;AAChD,YAAM,cAAc;AACpB,aAAO,cAAc,iBAAiB,aAAa,QAAW,KAAM,MAAM;AAAA,IAC5E;AACA,WAAO;AAAA,EACT,GAAG,CAAC,KAAK,KAAK,CAAC;AACf,QAAM,UAAU,MAAM,kBAAkB;AACxC,QAAM,SAAS,MAAM,CAAC,kBAAkB;AACxC,QAAM,yBAAqB,sBAAQ,OAAO;AAAA,IACxC,KAAK,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,EACF,IAAI,CAAC,KAAK,SAAS,MAAM,CAAC;AAC1B,QAAM,cAAc,YAAY,KAAK;AACrC,8BAAU,MAAM;AACd,QAAI,eAAe,CAAC,SAAS;AAC3B,YAAM,cAAc,YAAY,cAAc,MAAM;AACpD,UAAI,aAAa;AACf,cAAM,sBAAsB,YAAY,MAAM;AAC9C,oBAAY,MAAM,WAAW;AAC7B,eAAO,MAAM;AACX,sBAAY,MAAM,WAAW;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,SAAO,aAAAA,QAAe,cAAc,cAAc,UAAU;AAAA,IAC1D,OAAO;AAAA,EACT,GAAG,aAAAA,QAAe,cAAc,cAAc,WAAW;AAAA,IACvD;AAAA,EACF,GAAG,KAAK,CAAC,CAAC;AACZ,CAAC;AACD,OAAO,cAAc;AACrB,OAAO,YAAY;AAAA,EACjB,KAAK,kBAAAG,QAAU;AACjB;AAEA,IAAM,UAAU,aAAAH,QAAe,WAAW,CAAC,MAAM,QAAQ;AACvD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,aAAAA,QAAe,cAAc,eAAe,WAAW;AAAA,IAC5D,MAAM,IAAI;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG,KAAK,GAAG,aAAAA,QAAe,cAAc,mBAAmB,IAAI,GAAG,QAAQ;AAC5E,CAAC;AACD,QAAQ,cAAc;AACtB,QAAQ,YAAY;AAAA,EAClB,UAAU,kBAAAG,QAAU,OAAO;AAAA,EAC3B,QAAQ,kBAAAA,QAAU;AACpB;AACA,QAAQ,eAAe;AAAA,EACrB,QAAQ;AACV;AAEA,IAAM,cAAc,aAAAH,QAAe,cAAc;AAAA,EAC/C,WAAW;AACb,CAAC;AACD,IAAM,iBAAiB,MAAM;AAC3B,aAAO,yBAAW,WAAW;AAC/B;AAEA,IAAM,SAAS,aAAAA,QAAe,WAAW,CAAC,MAAM,QAAQ;AACtD,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,uBAAmB,sBAAQ,OAAO;AAAA,IACtC,WAAW,CAAC,CAAC;AAAA,EACf,IAAI,CAAC,SAAS,CAAC;AACf,SAAO,aAAAA,QAAe,cAAc,YAAY,UAAU;AAAA,IACxD,OAAO;AAAA,EACT,GAAG,aAAAA,QAAe,cAAc,YAAY,WAAW;AAAA,IACrD;AAAA,EACF,GAAG,KAAK,CAAC,CAAC;AACZ,CAAC;AACD,OAAO,cAAc;AACrB,OAAO,YAAY;AAAA,EACjB,WAAW,kBAAAG,QAAU;AACvB;AAEA,IAAM,UAAU,aAAAH,QAAe,WAAW,CAAC,OAAO,QAAQ;AACxD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,aAAAA,QAAe,cAAc,eAAe,WAAW;AAAA,IAC5D;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,QAAQ,cAAc;AAEtB,IAAM,OAAO,aAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ,aAAAA,QAAe,cAAc,YAAY,WAAW;AAAA,EACzG;AACF,GAAG,KAAK,CAAC,CAAC;AACV,KAAK,cAAc;AAEnB,IAAM,UAAU,aAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ,aAAAA,QAAe,cAAc,eAAe,WAAW;AAAA,EAC/G;AACF,GAAG,KAAK,CAAC,CAAC;AACV,QAAQ,cAAc;AAEtB,IAAM,WAAW,aAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ,aAAAA,QAAe,cAAc,cAAc,WAAW;AAAA,EAC/G;AACF,GAAG,KAAK,CAAC,CAAC;AACV,SAAS,cAAc;AACvB,SAAS,YAAY;AAAA,EACnB,cAAc,kBAAAG,QAAU;AAC1B;AAEA,IAAM,YAAY,CAAC,OAAO,OAAO;AACjC,IAAM,UAAU,CAAC,QAAQ,WAAW,WAAW,SAAS,WAAW,WAAW,MAAM;AAEpF,IAAM,aAAa,aAAAH,QAAe,WAAW,CAAC,MAAM,QAAQ;AAC1D,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,MAAI,SAAS;AACX,WAAO,aAAAA,QAAe,cAAc,sBAAsB,WAAW;AAAA,MACnE;AAAA,MACA;AAAA,IACF,GAAG,KAAK,CAAC;AAAA,EACX;AACA,SAAO,aAAAA,QAAe,cAAc,kBAAkB,WAAW;AAAA,IAC/D;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,WAAW,cAAc;AACzB,WAAW,YAAY;AAAA,EACrB,MAAM,kBAAAG,QAAU;AAAA,EAChB,MAAM,kBAAAA,QAAU;AAAA,EAChB,SAAS,kBAAAA,QAAU;AAAA,EACnB,SAAS,kBAAAA,QAAU,MAAM,OAAO;AAAA,EAChC,SAAS,kBAAAA,QAAU;AACrB;AAEA,IAAM,iBAAiB,UAAQ;AAC7B,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,UAAU,sBAAS,KAAK,QAAQ;AACtC,UAAK,6BAAe,OAAO,GAAG;AAC5B,UAAM,OAAO,WAAS;AACpB,UAAI;AAAA,QACF;AAAA,QACA,GAAG;AAAA,MACL,IAAI;AACJ,iBAAO,2BAAa,SAAS;AAAA,QAC3B,GAAG;AAAA,QACH,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AACA,WAAO,aAAAH,QAAe,cAAc,sBAAsB,WAAW;AAAA,MACnE,IAAI;AAAA,IACN,GAAG,KAAK,CAAC;AAAA,EACX;AACA,SAAO;AACT;AAEA,IAAM,iBAAiB,aAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ,aAAAA,QAAe,cAAc,sBAAsB,WAAW;AAAA,EAC7H;AACF,GAAG,KAAK,CAAC,CAAC;AACV,eAAe,cAAc;AAC7B,eAAe,YAAY;AAAA,EACzB,WAAW,kBAAAG,QAAU;AACvB;AAEA,IAAM,oBAAoB,aAAAH,QAAe,WAAW,CAAC,OAAO,QAAQ,aAAAA,QAAe,cAAc,yBAAyB,WAAW;AAAA,EACnI;AACF,GAAG,KAAK,CAAC,CAAC;AACV,kBAAkB,cAAc;AAEhC,IAAM,WAAW,aAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ,aAAAA,QAAe,cAAc,cAAc,WAAW;AAAA,EAC/G;AACF,GAAG,KAAK,CAAC,CAAC;AACV,SAAS,cAAc;AAEvB,IAAM,eAAe,aAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ,aAAAA,QAAe,cAAc,kBAAkB,WAAW;AAAA,EACvH;AACF,GAAG,KAAK,CAAC,CAAC;AACV,aAAa,cAAc;AAE3B,IAAM,aAAa,aAAAA,QAAe,cAAc;AAAA,EAC9C,YAAY;AACd,CAAC;AACD,IAAM,gBAAgB,MAAM;AAC1B,aAAO,yBAAW,UAAU;AAC9B;AAEA,IAAM,MAAM,aAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ;AACpD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB;AACrB,QAAM,sBAAkB,sBAAQ,OAAO;AAAA,IACrC,YAAY,CAAC,CAAC,MAAM;AAAA,EACtB,IAAI,CAAC,MAAM,UAAU,CAAC;AACtB,SAAO,aAAAA,QAAe,cAAc,WAAW,UAAU;AAAA,IACvD,OAAO;AAAA,EACT,GAAG,aAAAA,QAAe,cAAc,WAAW,WAAW;AAAA,IACpD;AAAA,EACF,GAAG,OAAO;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAI,cAAc;AAClB,IAAI,YAAY;AAAA,EACd,YAAY,kBAAAG,QAAU;AACxB;AAEA,IAAM,UAAU,aAAAH,QAAe,WAAW,CAAC,MAAM,QAAQ;AACvD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB;AACrB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,cAAc;AAClB,QAAM,cAAc,MAAM,aAAa;AACvC,MAAI,SAAS;AACX,WAAO,aAAAA,QAAe,cAAc,mBAAmB,WAAW;AAAA,MAChE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBAAgB;AAAA,IAClB,GAAG,KAAK,CAAC;AAAA,EACX;AACA,MAAI,cAAc;AAChB,WAAO,aAAAA,QAAe,cAAc,wBAAwB,WAAW;AAAA,MACrE;AAAA,IACF,GAAG,KAAK,CAAC;AAAA,EACX;AACA,SAAO,aAAAA,QAAe,cAAc,eAAe,WAAW;AAAA,IAC5D,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,EAClB,GAAG,KAAK,CAAC;AACX,CAAC;AACD,QAAQ,cAAc;AACtB,QAAQ,YAAY;AAAA,EAClB,SAAS,kBAAAG,QAAU,MAAM,OAAO;AAAA,EAChC,SAAS,kBAAAA,QAAU;AAAA,EACnB,cAAc,kBAAAA,QAAU;AAC1B;AAEA,IAAM,cAAc,UAAQ;AAC1B,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,UAAU,sBAAS,KAAK,QAAQ;AACtC,UAAK,6BAAe,OAAO,GAAG;AAC5B,UAAM,OAAO,WAAS;AACpB,UAAI;AAAA,QACF;AAAA,QACA,GAAG;AAAA,MACL,IAAI;AACJ,iBAAO,2BAAa,SAAS;AAAA,QAC3B,GAAG;AAAA,QACH,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AACA,WAAO,aAAAH,QAAe,cAAc,mBAAmB,WAAW;AAAA,MAChE,IAAI;AAAA,IACN,GAAG,KAAK,CAAC;AAAA,EACX;AACA,SAAO;AACT;AAEA,IAAM,cAAc,aAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ;AAC5D,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,cAAc;AAClB,SAAO,aAAAA,QAAe,cAAc,mBAAmB,WAAW;AAAA,IAChE;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,YAAY,cAAc;AAC1B,YAAY,YAAY;AAAA,EACtB,WAAW,kBAAAG,QAAU;AACvB;AAEA,IAAM,SAAS,aAAAH,QAAe,WAAW,CAAC,OAAO,QAAQ;AACvD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB;AACrB,SAAO,aAAAA,QAAe,cAAc,cAAc,WAAW;AAAA,IAC3D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,OAAO,cAAc;AAErB,IAAM,aAAa,aAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ;AAC3D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB;AACrB,SAAO,aAAAA,QAAe,cAAc,kBAAkB,WAAW;AAAA,IAC/D;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,WAAW,cAAc;AACzB,WAAW,YAAY;AAAA,EACrB,WAAW,kBAAAG,QAAU;AACvB;AAEA,IAAM,iBAAiB,aAAAH,QAAe,WAAW,CAAC,OAAO,QAAQ,aAAAA,QAAe,cAAc,sBAAsB,WAAW;AAAA,EAC7H;AACF,GAAG,KAAK,CAAC,CAAC;AACV,eAAe,cAAc;AAC7B,eAAe,YAAY;AAAA,EACzB,WAAW,kBAAAG,QAAU;AACvB;AAEA,IAAM,wBAAwB,aAAAH,QAAe,WAAW,CAAC,MAAM,QAAQ;AACrE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,eAAW,qBAAO;AACxB,QAAM,CAAC,kBAAkB,mBAAmB,QAAI,uBAAS,kBAAkB;AAC3E,QAAM,WAAW,mBAAmB,oBAAoB,gBAAgB;AACxE,QAAM,mBAAmB,WAAW,CAAC,CAAC,IAAI,CAAC;AAC3C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,aAAa;AAAA,IACf;AAAA,IACA,UAAU,MAAM;AACd,YAAM,aAAa,iBAAiB,WAAW;AAC/C,UAAI,UAAU;AACZ,iBAAS,UAAU;AAAA,MACrB,OAAO;AACL,4BAAoB,UAAU;AAAA,MAChC;AAAA,IACF;AAAA,EACF,CAAC;AACD,8BAAU,MAAM;AACd,QAAI,YAAY,SAAS,SAAS;AAChC,eAAS,QAAQ,MAAM,YAAY,GAAG,SAAS,QAAQ;AAAA,IACzD;AAAA,EACF,GAAG,CAAC,UAAU,QAAQ,CAAC;AACvB,SAAO,aAAAA,QAAe,cAAc,OAAO;AAAA,IACzC;AAAA,EACF,GAAG,aAAAA,QAAe,cAAc,OAAO,eAAe;AAAA,IACpD,WAAW;AAAA,EACb,CAAC,GAAG,aAAAA,QAAe,cAAc,wBAAwB,gBAAgB;AAAA,IACvE,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,GAAG;AAAA,EACL,CAAC,GAAG,aAAAA,QAAe,cAAc,aAAAA,QAAe,UAAU,MAAM,QAAQ,aAAAA,QAAe,cAAc,6BAA6B;AAAA,IAChI,YAAY;AAAA,EACd,GAAG,aAAAA,QAAe,cAAc,sBAAsB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,aAAAA,QAAe,cAAc,mBAAmB,cAAc;AAAA,IAC7H,OAAO;AAAA,IACP,UAAU,CAAC;AAAA,IACX,KAAK;AAAA,EACP,CAAC,GAAG,QAAQ,CAAC;AACf,CAAC;AACD,sBAAsB,YAAY;AAAA,EAChC,QAAQ,kBAAAG,QAAU;AAAA,EAClB,YAAY,kBAAAA,QAAU;AAAA,EACtB,UAAU,kBAAAA,QAAU;AAAA,EACpB,UAAU,kBAAAA,QAAU;AACtB;AACA,sBAAsB,cAAc;AAEpC,IAAM,mBAAe,4BAAc;AAAA,EACjC,0BAA0B;AAAA,EAAC;AAC7B,CAAC;AACD,IAAM,kBAAkB,MAAM;AAC5B,aAAO,yBAAW,YAAY;AAChC;AAEA,SAAS,cAAc,MAAM;AAC3B,SAAO,QAAQ,KAAK,iBAAiB;AACvC;AAEA,SAAS,cAAc,KAAK;AAC1B,MAAI,QAAQ,QAAQ;AAClB,UAAM,cAAc;AAAA,EACtB;AACA,MAAI;AACF,QAAI,SAAS,IAAI;AACjB,QAAI,CAAC,UAAU,CAAC,OAAO;AAAU,aAAO;AACxC,WAAO;AAAA,EACT,SAAS,GAAP;AACA,WAAO,IAAI;AAAA,EACb;AACF;AAEA,SAAS,kBAAkB,MAAM;AAC/B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,iBAAa,qBAAO;AAC1B,8BAAU,MAAM;AACd,QAAI,aAAa,gBAAgB,UAAU,SAAS;AAClD,iBAAW,UAAU,cAAc;AACnC,gBAAU,QAAQ,MAAM;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,WAAW,cAAc,SAAS,CAAC;AACvC,8BAAU,MAAM;AACd,QAAI,CAAC,aAAa,gBAAgB,WAAW,SAAS;AACpD,iBAAW,QAAQ,MAAM;AAAA,IAC3B;AAAA,EACF,GAAG,CAAC,WAAW,cAAc,UAAU,CAAC;AAC1C;AAEA,IAAM,iBAAa,yBAAW,CAAC,MAAM,QAAQ;AAC3C,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,SAAO,aAAAH,QAAe,cAAc,kBAAkB,WAAW;AAAA,IAC/D,IAAI,MAAM;AAAA,IACV;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,WAAW,cAAc;AACzB,IAAM,QAAQ;AAEd,IAAM,uBAAmB,yBAAW,CAAC,MAAM,QAAQ;AACjD,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,SAAO,aAAAA,QAAe,cAAc,wBAAwB,WAAW;AAAA,IACrE,IAAI,MAAM;AAAA,IACV;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,iBAAiB,cAAc;AAC/B,IAAM,cAAc;AAEpB,IAAM,kBAAc,yBAAW,CAAC,OAAO,QAAQ;AAC7C,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,SAAO,aAAAA,QAAe,cAAc,mBAAmB,WAAW;AAAA,IAChE;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,YAAY,cAAc;AAC1B,IAAM,SAAS;AAEf,IAAM,gBAAY,yBAAW,CAAC,OAAO,QAAQ;AAC3C,SAAO,aAAAA,QAAe,cAAc,iBAAiB,WAAW;AAAA,IAC9D;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,UAAU,cAAc;AACxB,IAAM,OAAO;AAEb,IAAM,kBAAc,yBAAW,CAAC,OAAO,QAAQ;AAC7C,SAAO,aAAAA,QAAe,cAAc,mBAAmB,WAAW;AAAA,IAChE;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,YAAY,cAAc;AAC1B,IAAM,SAAS;AAEf,IAAM,sBAAkB,yBAAW,CAAC,OAAO,QAAQ;AACjD,SAAO,aAAAA,QAAe,cAAc,uBAAuB,WAAW;AAAA,IACpE;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,gBAAgB,cAAc;AAC9B,IAAM,aAAa;AAEnB,IAAI;AACJ,SAAS,WAAW;AAAE,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,SAAS,MAAM,MAAM,SAAS;AAAG;AAClV,IAAI,aAAa,SAASI,YAAW,OAAO;AAC1C,SAA0B,oBAAc,OAAO,SAAS;AAAA,IACtD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,UAAU,QAA2B,oBAAc,QAAQ;AAAA,IACpE,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAM,iBAAa,yBAAW,CAAC,OAAO,QAAQ;AAC5C,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,8BAAU,MAAM;AACd,4BAAwB,IAAI;AAC5B,WAAO,MAAM,wBAAwB,KAAK;AAAA,EAC5C,GAAG,CAAC,uBAAuB,CAAC;AAC5B,SAAO,aAAAJ,QAAe,cAAc,kBAAkB,WAAW;AAAA,IAC/D,cAAc;AAAA,IACd;AAAA,EACF,GAAG,KAAK,GAAG,aAAAA,QAAe,cAAc,YAAY,IAAI,CAAC;AAC3D,CAAC;AACD,WAAW,cAAc;AACzB,IAAM,QAAQ;AAEd,IAAM,iBAAiB,aAAAA,QAAe,WAAW,CAAC,MAAM,QAAQ;AAC9D,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,eAAW,qBAAO,IAAI;AAC5B,QAAM,OAAO,WAAW;AACxB,QAAM,CAAC,sBAAsB,uBAAuB,QAAI,uBAAS,KAAK;AACtE,QAAM,eAAW,sBAAQ,MAAM,MAAM,KAAK,SAAS,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC;AAC1E,QAAM,UAAU,GAAG;AACnB,QAAM,gBAAgB,GAAG;AACzB,QAAM,mBAAe,sBAAQ,OAAO;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,SAAS,eAAe,oBAAoB,CAAC;AAClD,oBAAkB;AAAA,IAChB,WAAW;AAAA,IACX,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO,aAAAA,QAAe,cAAc,aAAa,UAAU;AAAA,IACzD,OAAO;AAAA,EACT,GAAG,aAAAA,QAAe,cAAc,aAAa,WAAW;AAAA,IACtD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,IAAI;AAAA,IACJ,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,KAAK,6BAAU,CAAC,UAAU,GAAG,CAAC;AAAA,EAChC,GAAG,KAAK,GAAG,aAAAA,QAAe,cAAc,oBAAoB;AAAA,IAC1D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,QAAQ,CAAC,CAAC;AACf,CAAC;AACD,eAAe,cAAc;AAC7B,eAAe,YAAY;AAAA,EACzB,IAAI,kBAAAG,QAAU;AAAA,EACd,QAAQ,kBAAAA,QAAU;AAAA,EAClB,YAAY,kBAAAA,QAAU;AAAA,EACtB,cAAc,kBAAAA,QAAU;AAAA,EACxB,cAAc,kBAAAA,QAAU;AAAA,EACxB,WAAW,kBAAAA,QAAU,MAAM,SAAS;AAAA,EACpC,MAAM,kBAAAA,QAAU;AAClB;AACA,eAAe,eAAe;AAAA,EAC5B,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AACR;AACA,IAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,cAAc;AACpB,MAAM,SAAS;AACf,MAAM,aAAa;AACnB,MAAM,SAAS;AACf,MAAM,QAAQ;", "names": ["React__default", "SvgLinkStroke", "SvgChevronDownStroke", "PropTypes", "SvgXStroke"]}