{"version": 3, "sources": ["../../node_modules/@zendeskgarden/react-tooltips/dist/index.esm.js", "../../node_modules/@zendeskgarden/container-tooltip/dist/index.esm.js", "../../node_modules/@zendeskgarden/react-tooltips/node_modules/react-popper/lib/esm/Popper.js", "../../node_modules/@zendeskgarden/react-tooltips/node_modules/react-popper/lib/esm/Manager.js", "../../node_modules/@zendeskgarden/react-tooltips/node_modules/react-popper/lib/esm/utils.js", "../../node_modules/@zendeskgarden/react-tooltips/node_modules/react-popper/lib/esm/Reference.js"], "sourcesContent": ["/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { useContext, useRef, useEffect, cloneElement, forwardRef } from 'react';\nimport { createPortal } from 'react-dom';\nimport PropTypes from 'prop-types';\nimport styled, { css, ThemeContext } from 'styled-components';\nimport mergeRefs from 'react-merge-refs';\nimport { useTooltip } from '@zendeskgarden/container-tooltip';\nimport { getControlledValue, composeEventHandlers } from '@zendeskgarden/container-utilities';\nimport { Manager, Reference, Popper } from 'react-popper';\nimport { retrieveComponentStyles, DEFAULT_THEME, getLineHeight, arrowStyles, getColor } from '@zendeskgarden/react-theming';\n\nfunction getPopperPlacement(gardenPlacement) {\n  const gardenToPopperMapping = {\n    auto: 'auto',\n    top: 'top',\n    'top-start': 'top-start',\n    'top-end': 'top-end',\n    bottom: 'bottom',\n    'bottom-start': 'bottom-start',\n    'bottom-end': 'bottom-end',\n    end: 'right',\n    'end-top': 'right-start',\n    'end-bottom': 'right-end',\n    start: 'left',\n    'start-top': 'left-start',\n    'start-bottom': 'left-end'\n  };\n  return gardenToPopperMapping[gardenPlacement];\n}\nfunction getRtlPopperPlacement(gardenPlacement) {\n  const rtlPlacementMappings = {\n    left: 'right',\n    'left-start': 'right-start',\n    'left-end': 'right-end',\n    'top-start': 'top-end',\n    'top-end': 'top-start',\n    right: 'left',\n    'right-start': 'left-start',\n    'right-end': 'left-end',\n    'bottom-start': 'bottom-end',\n    'bottom-end': 'bottom-start'\n  };\n  const popperPlacement = getPopperPlacement(gardenPlacement);\n  return rtlPlacementMappings[popperPlacement] || popperPlacement;\n}\nfunction getArrowPosition(popperPlacement) {\n  const arrowPositionMappings = {\n    top: 'bottom',\n    'top-start': 'bottom-left',\n    'top-end': 'bottom-right',\n    right: 'left',\n    'right-start': 'left-top',\n    'right-end': 'left-bottom',\n    bottom: 'top',\n    'bottom-start': 'top-left',\n    'bottom-end': 'top-right',\n    left: 'right',\n    'left-start': 'right-top',\n    'left-end': 'right-bottom'\n  };\n  return arrowPositionMappings[popperPlacement] || 'top';\n}\n\nconst COMPONENT_ID$2 = 'tooltip.paragraph';\nconst StyledParagraph = styled.p.attrs({\n  'data-garden-id': COMPONENT_ID$2,\n  'data-garden-version': '8.69.8'\n}).withConfig({\n  displayName: \"StyledParagraph\",\n  componentId: \"sc-wuqkfc-0\"\n})([\"margin:0;\", \";\"], props => retrieveComponentStyles(COMPONENT_ID$2, props));\nStyledParagraph.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$1 = 'tooltip.title';\nconst StyledTitle = styled.strong.attrs({\n  'data-garden-id': COMPONENT_ID$1,\n  'data-garden-version': '8.69.8'\n}).withConfig({\n  displayName: \"StyledTitle\",\n  componentId: \"sc-vnjcvz-0\"\n})([\"display:none;margin:0;font-weight:\", \";\", \";\"], props => props.theme.fontWeights.semibold, props => retrieveComponentStyles(COMPONENT_ID$1, props));\nStyledTitle.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID = 'tooltip.tooltip';\nconst sizeStyles = _ref => {\n  let {\n    theme,\n    size,\n    type,\n    placement,\n    hasArrow\n  } = _ref;\n  let margin = `${theme.space.base * 1.5}px`;\n  let borderRadius = theme.borderRadii.sm;\n  let padding = '0 1em';\n  let maxWidth;\n  let overflowWrap;\n  let whiteSpace = 'nowrap';\n  let lineHeight = getLineHeight(theme.space.base * 5, theme.fontSizes.sm);\n  let fontSize = theme.fontSizes.sm;\n  let titleDisplay;\n  let paragraphMarginTop;\n  let wordWrap;\n  if (size !== 'small') {\n    borderRadius = theme.borderRadii.md;\n    overflowWrap = 'break-word';\n    whiteSpace = 'normal';\n    wordWrap = 'break-word';\n  }\n  if (size === 'extra-large') {\n    padding = `${theme.space.base * 10}px`;\n    maxWidth = `460px`;\n    lineHeight = getLineHeight(theme.space.base * 5, theme.fontSizes.md);\n    paragraphMarginTop = `${theme.space.base * 2.5}px`;\n  } else if (size === 'large') {\n    padding = `${theme.space.base * 5}px`;\n    maxWidth = `270px`;\n    lineHeight = getLineHeight(theme.space.base * 5, theme.fontSizes.md);\n    paragraphMarginTop = `${theme.space.base * 2}px`;\n  } else if (size === 'medium') {\n    padding = '1em';\n    maxWidth = `140px`;\n    lineHeight = getLineHeight(theme.space.base * 4, theme.fontSizes.sm);\n  }\n  if (size === 'extra-large' || size === 'large') {\n    fontSize = theme.fontSizes.md;\n    titleDisplay = 'block';\n  }\n  let arrowSize;\n  let arrowInset;\n  if (hasArrow) {\n    if (size === 'small' || size === 'medium') {\n      arrowSize = margin;\n      arrowInset = type === 'dark' ? '1px' : '0';\n    } else {\n      arrowInset = type === 'dark' ? '2px' : '1px';\n      if (size === 'large') {\n        margin = `${theme.space.base * 2}px`;\n        arrowSize = margin;\n      } else if (size === 'extra-large') {\n        margin = `${theme.space.base * 3}px`;\n        arrowSize = `${theme.space.base * 2.5}px`;\n      }\n    }\n  }\n  return css([\"margin:\", \";border-radius:\", \";padding:\", \";max-width:\", \";line-height:\", \";word-wrap:\", \";white-space:\", \";font-size:\", \";overflow-wrap:\", \";\", \";\", \"{margin-top:\", \";}\", \"{display:\", \";}\"], margin, borderRadius, padding, maxWidth, lineHeight, wordWrap, whiteSpace, fontSize, overflowWrap, hasArrow && arrowStyles(getArrowPosition(placement), {\n    size: arrowSize,\n    inset: arrowInset\n  }), StyledParagraph, paragraphMarginTop, StyledTitle, titleDisplay);\n};\nconst colorStyles = _ref2 => {\n  let {\n    theme,\n    type\n  } = _ref2;\n  let border;\n  let boxShadow = theme.shadows.lg(`${theme.space.base}px`, `${theme.space.base * 2}px`, getColor('chromeHue', 600, theme, 0.15));\n  let backgroundColor = getColor('chromeHue', 700, theme);\n  let color = theme.colors.background;\n  let titleColor;\n  if (type === 'light') {\n    boxShadow = theme.shadows.lg(`${theme.space.base * 3}px`, `${theme.space.base * 5}px`, getColor('chromeHue', 600, theme, 0.15));\n    border = `${theme.borders.sm} ${getColor('neutralHue', 300, theme)}`;\n    backgroundColor = theme.colors.background;\n    color = getColor('neutralHue', 700, theme);\n    titleColor = theme.colors.foreground;\n  }\n  return css([\"border:\", \";box-shadow:\", \";background-color:\", \";color:\", \";\", \"{color:\", \";}\"], border, boxShadow, backgroundColor, color, StyledTitle, titleColor);\n};\nconst StyledTooltip = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID,\n  'data-garden-version': '8.69.8'\n}).withConfig({\n  displayName: \"StyledTooltip\",\n  componentId: \"sc-gzzjq4-0\"\n})([\"display:inline-block;box-sizing:border-box;direction:\", \";text-align:\", \";font-weight:\", \";\", \";&[aria-hidden='true']{display:none;}\", \";\", \";\"], props => props.theme.rtl && 'rtl', props => props.theme.rtl ? 'right' : 'left', props => props.theme.fontWeights.regular, props => sizeStyles(props), colorStyles, props => retrieveComponentStyles(COMPONENT_ID, props));\nStyledTooltip.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst StyledTooltipWrapper = styled.div.withConfig({\n  displayName: \"StyledTooltipWrapper\",\n  componentId: \"sc-1b7q9q6-0\"\n})([\"transition:opacity 10ms;opacity:1;z-index:\", \";&[aria-hidden='true']{visibility:hidden;opacity:0;}\"], props => props.zIndex);\nStyledTooltipWrapper.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst SHARED_PLACEMENT = ['auto', 'top', 'top-start', 'top-end', 'bottom', 'bottom-start', 'bottom-end'];\nconst PLACEMENT = [...SHARED_PLACEMENT, 'end', 'end-top', 'end-bottom', 'start', 'start-top', 'start-bottom'];\nconst SIZE = ['small', 'medium', 'large', 'extra-large'];\nconst TYPE = ['light', 'dark'];\n\nconst Tooltip = _ref => {\n  let {\n    id,\n    delayMS,\n    isInitialVisible,\n    content,\n    refKey,\n    placement,\n    eventsEnabled,\n    popperModifiers,\n    children,\n    hasArrow,\n    size,\n    type,\n    appendToNode,\n    zIndex,\n    isVisible: externalIsVisible,\n    ...otherProps\n  } = _ref;\n  const {\n    rtl\n  } = useContext(ThemeContext);\n  const scheduleUpdateRef = useRef();\n  const {\n    isVisible,\n    getTooltipProps,\n    getTriggerProps,\n    openTooltip,\n    closeTooltip\n  } = useTooltip({\n    id,\n    delayMilliseconds: delayMS,\n    isVisible: isInitialVisible\n  });\n  const controlledIsVisible = getControlledValue(externalIsVisible, isVisible);\n  useEffect(() => {\n    if (controlledIsVisible && scheduleUpdateRef.current) {\n      scheduleUpdateRef.current();\n    }\n  }, [controlledIsVisible, content]);\n  const popperPlacement = rtl ? getRtlPopperPlacement(placement) : getPopperPlacement(placement);\n  const singleChild = React.Children.only(children);\n  const modifiers = {\n    preventOverflow: {\n      boundariesElement: 'window'\n    },\n    ...popperModifiers\n  };\n  return React.createElement(Manager, null, React.createElement(Reference, null, _ref2 => {\n    let {\n      ref\n    } = _ref2;\n    return cloneElement(singleChild, getTriggerProps({\n      ...singleChild.props,\n      [refKey]: mergeRefs([ref, singleChild.ref ? singleChild.ref : null])\n    }));\n  }), React.createElement(Popper, {\n    placement: popperPlacement,\n    eventsEnabled: controlledIsVisible && eventsEnabled,\n    modifiers: modifiers\n  }, _ref3 => {\n    let {\n      ref,\n      style,\n      scheduleUpdate,\n      placement: currentPlacement\n    } = _ref3;\n    scheduleUpdateRef.current = scheduleUpdate;\n    const {\n      onFocus,\n      onBlur,\n      ...otherTooltipProps\n    } = otherProps;\n    let computedSize = size;\n    if (computedSize === undefined) {\n      if (type === 'dark') {\n        computedSize = 'small';\n      } else {\n        computedSize = 'large';\n      }\n    }\n    const tooltipProps = {\n      hasArrow,\n      placement: currentPlacement,\n      size: computedSize,\n      onFocus: composeEventHandlers(onFocus, () => {\n        openTooltip();\n      }),\n      onBlur: composeEventHandlers(onBlur, () => {\n        closeTooltip(0);\n      }),\n      'aria-hidden': !controlledIsVisible,\n      type,\n      ...otherTooltipProps\n    };\n    const tooltip = React.createElement(StyledTooltipWrapper, {\n      ref: controlledIsVisible ? ref : null,\n      style: style,\n      zIndex: zIndex,\n      \"aria-hidden\": !controlledIsVisible\n    }, React.createElement(StyledTooltip, getTooltipProps(tooltipProps), content));\n    if (appendToNode) {\n      return createPortal(tooltip, appendToNode);\n    }\n    return tooltip;\n  }));\n};\nTooltip.displayName = 'Tooltip';\nTooltip.propTypes = {\n  appendToNode: PropTypes.any,\n  hasArrow: PropTypes.bool,\n  delayMS: PropTypes.number,\n  eventsEnabled: PropTypes.bool,\n  id: PropTypes.string,\n  content: PropTypes.node.isRequired,\n  placement: PropTypes.oneOf(PLACEMENT),\n  popperModifiers: PropTypes.any,\n  size: PropTypes.oneOf(SIZE),\n  type: PropTypes.oneOf(TYPE),\n  zIndex: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  isInitialVisible: PropTypes.bool,\n  refKey: PropTypes.string\n};\nTooltip.defaultProps = {\n  hasArrow: true,\n  eventsEnabled: true,\n  type: 'dark',\n  placement: 'top',\n  delayMS: 500,\n  refKey: 'ref'\n};\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nconst Paragraph = forwardRef((props, ref) => React.createElement(StyledParagraph, _extends({\n  ref: ref\n}, props)));\nParagraph.displayName = 'Paragraph';\n\nconst Title = forwardRef((props, ref) => React.createElement(StyledTitle, _extends({\n  ref: ref\n}, props)));\nTitle.displayName = 'Title';\n\nexport { Paragraph, Title, Tooltip };\n", "/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { useState, useMemo, useRef, useEffect } from 'react';\nimport { useUIDSeed } from 'react-uid';\nimport { composeEventHandlers, KEY_CODES } from '@zendeskgarden/container-utilities';\nimport PropTypes from 'prop-types';\n\nconst useTooltip = function (_temp) {\n  let {\n    delayMilliseconds = 500,\n    id,\n    isVisible\n  } = _temp === void 0 ? {} : _temp;\n  const [visibility, setVisibility] = useState(isVisible);\n  const seed = useUIDSeed();\n  const _id = useMemo(() => id || seed(`tooltip_${'1.0.5'}`), [id, seed]);\n  const isMounted = useRef(false);\n  const openTooltipTimeoutId = useRef();\n  const closeTooltipTimeoutId = useRef();\n  const openTooltip = function (delayMs) {\n    if (delayMs === void 0) {\n      delayMs = delayMilliseconds;\n    }\n    clearTimeout(closeTooltipTimeoutId.current);\n    const timerId = setTimeout(() => {\n      if (isMounted.current) {\n        setVisibility(true);\n      }\n    }, delayMs);\n    openTooltipTimeoutId.current = Number(timerId);\n  };\n  const closeTooltip = function (delayMs) {\n    if (delayMs === void 0) {\n      delayMs = delayMilliseconds;\n    }\n    clearTimeout(openTooltipTimeoutId.current);\n    const timerId = setTimeout(() => {\n      if (isMounted.current) {\n        setVisibility(false);\n      }\n    }, delayMs);\n    closeTooltipTimeoutId.current = Number(timerId);\n  };\n  useEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  useEffect(() => {\n    return () => {\n      clearTimeout(openTooltipTimeoutId.current);\n      clearTimeout(closeTooltipTimeoutId.current);\n    };\n  }, [closeTooltipTimeoutId, openTooltipTimeoutId]);\n  const getTriggerProps = function (_temp2) {\n    let {\n      tabIndex = 0,\n      onMouseEnter,\n      onMouseLeave,\n      onFocus,\n      onBlur,\n      onKeyDown,\n      ...other\n    } = _temp2 === void 0 ? {} : _temp2;\n    return {\n      tabIndex,\n      onMouseEnter: composeEventHandlers(onMouseEnter, () => openTooltip()),\n      onMouseLeave: composeEventHandlers(onMouseLeave, () => closeTooltip()),\n      onFocus: composeEventHandlers(onFocus, () => openTooltip()),\n      onBlur: composeEventHandlers(onBlur, () => closeTooltip(0)),\n      onKeyDown: composeEventHandlers(onKeyDown, event => {\n        if (event.keyCode === KEY_CODES.ESCAPE && visibility) {\n          closeTooltip(0);\n        }\n      }),\n      'aria-describedby': _id,\n      'data-garden-container-id': 'containers.tooltip',\n      'data-garden-container-version': '1.0.5',\n      ...other\n    };\n  };\n  const getTooltipProps = function (_temp3) {\n    let {\n      role = 'tooltip',\n      onMouseEnter,\n      onMouseLeave,\n      ...other\n    } = _temp3 === void 0 ? {} : _temp3;\n    return {\n      role,\n      onMouseEnter: composeEventHandlers(onMouseEnter, () => openTooltip()),\n      onMouseLeave: composeEventHandlers(onMouseLeave, () => closeTooltip()),\n      'aria-hidden': !visibility,\n      id: _id,\n      ...other\n    };\n  };\n  return {\n    isVisible: visibility,\n    getTooltipProps,\n    getTriggerProps,\n    openTooltip,\n    closeTooltip\n  };\n};\n\nconst TooltipContainer = _ref => {\n  let {\n    children,\n    render = children,\n    ...options\n  } = _ref;\n  return React.createElement(React.Fragment, null, render(useTooltip(options)));\n};\nTooltipContainer.defaultProps = {\n  delayMilliseconds: 500\n};\nTooltipContainer.propTypes = {\n  children: PropTypes.func,\n  render: PropTypes.func,\n  delayMilliseconds: PropTypes.number,\n  isVisible: PropTypes.bool\n};\n\nexport { TooltipContainer, useTooltip };\n", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/inheritsLoose\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport deepEqual from \"deep-equal\";\nimport * as React from 'react';\nimport PopperJS from 'popper.js';\nimport { ManagerReferenceNodeContext } from './Manager';\nimport { unwrapArray, setRef, shallowEqual } from './utils';\nvar initialStyle = {\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  opacity: 0,\n  pointerEvents: 'none'\n};\nvar initialArrowStyle = {};\nexport var InnerPopper =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(InnerPopper, _React$Component);\n\n  function InnerPopper() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"state\", {\n      data: undefined,\n      placement: undefined\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"popperInstance\", void 0);\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"popperNode\", null);\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"arrowNode\", null);\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"setPopperNode\", function (popperNode) {\n      if (!popperNode || _this.popperNode === popperNode) return;\n      setRef(_this.props.innerRef, popperNode);\n      _this.popperNode = popperNode;\n\n      _this.updatePopperInstance();\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"setArrowNode\", function (arrowNode) {\n      _this.arrowNode = arrowNode;\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"updateStateModifier\", {\n      enabled: true,\n      order: 900,\n      fn: function fn(data) {\n        var placement = data.placement;\n\n        _this.setState({\n          data: data,\n          placement: placement\n        });\n\n        return data;\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"getOptions\", function () {\n      return {\n        placement: _this.props.placement,\n        eventsEnabled: _this.props.eventsEnabled,\n        positionFixed: _this.props.positionFixed,\n        modifiers: _extends({}, _this.props.modifiers, {\n          arrow: _extends({}, _this.props.modifiers && _this.props.modifiers.arrow, {\n            enabled: !!_this.arrowNode,\n            element: _this.arrowNode\n          }),\n          applyStyle: {\n            enabled: false\n          },\n          updateStateModifier: _this.updateStateModifier\n        })\n      };\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"getPopperStyle\", function () {\n      return !_this.popperNode || !_this.state.data ? initialStyle : _extends({\n        position: _this.state.data.offsets.popper.position\n      }, _this.state.data.styles);\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"getPopperPlacement\", function () {\n      return !_this.state.data ? undefined : _this.state.placement;\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"getArrowStyle\", function () {\n      return !_this.arrowNode || !_this.state.data ? initialArrowStyle : _this.state.data.arrowStyles;\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"getOutOfBoundariesState\", function () {\n      return _this.state.data ? _this.state.data.hide : undefined;\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"destroyPopperInstance\", function () {\n      if (!_this.popperInstance) return;\n\n      _this.popperInstance.destroy();\n\n      _this.popperInstance = null;\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"updatePopperInstance\", function () {\n      _this.destroyPopperInstance();\n\n      var _assertThisInitialize = _assertThisInitialized(_assertThisInitialized(_this)),\n          popperNode = _assertThisInitialize.popperNode;\n\n      var referenceElement = _this.props.referenceElement;\n      if (!referenceElement || !popperNode) return;\n      _this.popperInstance = new PopperJS(referenceElement, popperNode, _this.getOptions());\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"scheduleUpdate\", function () {\n      if (_this.popperInstance) {\n        _this.popperInstance.scheduleUpdate();\n      }\n    });\n\n    return _this;\n  }\n\n  var _proto = InnerPopper.prototype;\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n    // If the Popper.js options have changed, update the instance (destroy + create)\n    if (this.props.placement !== prevProps.placement || this.props.referenceElement !== prevProps.referenceElement || this.props.positionFixed !== prevProps.positionFixed || !deepEqual(this.props.modifiers, prevProps.modifiers, {\n      strict: true\n    })) {\n      // develop only check that modifiers isn't being updated needlessly\n      if (process.env.NODE_ENV === \"development\") {\n        if (this.props.modifiers !== prevProps.modifiers && this.props.modifiers != null && prevProps.modifiers != null && shallowEqual(this.props.modifiers, prevProps.modifiers)) {\n          console.warn(\"'modifiers' prop reference updated even though all values appear the same.\\nConsider memoizing the 'modifiers' object to avoid needless rendering.\");\n        }\n      }\n\n      this.updatePopperInstance();\n    } else if (this.props.eventsEnabled !== prevProps.eventsEnabled && this.popperInstance) {\n      this.props.eventsEnabled ? this.popperInstance.enableEventListeners() : this.popperInstance.disableEventListeners();\n    } // A placement difference in state means popper determined a new placement\n    // apart from the props value. By the time the popper element is rendered with\n    // the new position Popper has already measured it, if the place change triggers\n    // a size change it will result in a misaligned popper. So we schedule an update to be sure.\n\n\n    if (prevState.placement !== this.state.placement) {\n      this.scheduleUpdate();\n    }\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    setRef(this.props.innerRef, null);\n    this.destroyPopperInstance();\n  };\n\n  _proto.render = function render() {\n    return unwrapArray(this.props.children)({\n      ref: this.setPopperNode,\n      style: this.getPopperStyle(),\n      placement: this.getPopperPlacement(),\n      outOfBoundaries: this.getOutOfBoundariesState(),\n      scheduleUpdate: this.scheduleUpdate,\n      arrowProps: {\n        ref: this.setArrowNode,\n        style: this.getArrowStyle()\n      }\n    });\n  };\n\n  return InnerPopper;\n}(React.Component);\n\n_defineProperty(InnerPopper, \"defaultProps\", {\n  placement: 'bottom',\n  eventsEnabled: true,\n  referenceElement: undefined,\n  positionFixed: false\n});\n\nvar placements = PopperJS.placements;\nexport { placements };\nexport default function Popper(_ref) {\n  var referenceElement = _ref.referenceElement,\n      props = _objectWithoutPropertiesLoose(_ref, [\"referenceElement\"]);\n\n  return React.createElement(ManagerReferenceNodeContext.Consumer, null, function (referenceNode) {\n    return React.createElement(InnerPopper, _extends({\n      referenceElement: referenceElement !== undefined ? referenceElement : referenceNode\n    }, props));\n  });\n}", "import _inherits<PERSON>oose from \"@babel/runtime/helpers/inheritsLoose\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport * as React from 'react';\nimport createContext from '@hypnosphi/create-react-context';\nexport var ManagerReferenceNodeContext = createContext();\nexport var ManagerReferenceNodeSetterContext = createContext();\n\nvar Manager =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(Manager, _React$Component);\n\n  function Manager() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"referenceNode\", void 0);\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"setReferenceNode\", function (newReferenceNode) {\n      if (newReferenceNode && _this.referenceNode !== newReferenceNode) {\n        _this.referenceNode = newReferenceNode;\n\n        _this.forceUpdate();\n      }\n    });\n\n    return _this;\n  }\n\n  var _proto = Manager.prototype;\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.referenceNode = null;\n  };\n\n  _proto.render = function render() {\n    return React.createElement(ManagerReferenceNodeContext.Provider, {\n      value: this.referenceNode\n    }, React.createElement(ManagerReferenceNodeSetterContext.Provider, {\n      value: this.setReferenceNode\n    }, this.props.children));\n  };\n\n  return Manager;\n}(React.Component);\n\nexport { Manager as default };", "/**\n * Takes an argument and if it's an array, returns the first item in the array,\n * otherwise returns the argument. Used for Preact compatibility.\n */\nexport var unwrapArray = function unwrapArray(arg) {\n  return Array.isArray(arg) ? arg[0] : arg;\n};\n/**\n * Takes a maybe-undefined function and arbitrary args and invokes the function\n * only if it is defined.\n */\n\nexport var safeInvoke = function safeInvoke(fn) {\n  if (typeof fn === \"function\") {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return fn.apply(void 0, args);\n  }\n};\n/**\n * Does a shallow equality check of two objects by comparing the reference\n * equality of each value.\n */\n\nexport var shallowEqual = function shallowEqual(objA, objB) {\n  var aKeys = Object.keys(objA);\n  var bKeys = Object.keys(objB);\n\n  if (bKeys.length !== aKeys.length) {\n    return false;\n  }\n\n  for (var i = 0; i < bKeys.length; i++) {\n    var key = aKeys[i];\n\n    if (objA[key] !== objB[key]) {\n      return false;\n    }\n  }\n\n  return true;\n};\n/**\n * Sets a ref using either a ref callback or a ref object\n */\n\nexport var setRef = function setRef(ref, node) {\n  // if its a function call it\n  if (typeof ref === \"function\") {\n    return safeInvoke(ref, node);\n  } // otherwise we should treat it as a ref object\n  else if (ref != null) {\n      ref.current = node;\n    }\n};", "import _extends from \"@babel/runtime/helpers/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/inheritsLoose\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport * as React from 'react';\nimport warning from 'warning';\nimport { ManagerReferenceNodeSetterContext } from './Manager';\nimport { safeInvoke, unwrapArray, setRef } from './utils';\n\nvar InnerReference =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(InnerReference, _React$Component);\n\n  function InnerReference() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"refHandler\", function (node) {\n      setRef(_this.props.innerRef, node);\n      safeInvoke(_this.props.setReferenceNode, node);\n    });\n\n    return _this;\n  }\n\n  var _proto = InnerReference.prototype;\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    setRef(this.props.innerRef, null);\n  };\n\n  _proto.render = function render() {\n    warning(Boolean(this.props.setReferenceNode), '`Reference` should not be used outside of a `Manager` component.');\n    return unwrapArray(this.props.children)({\n      ref: this.refHandler\n    });\n  };\n\n  return InnerReference;\n}(React.Component);\n\nexport default function Reference(props) {\n  return React.createElement(ManagerReferenceNodeSetterContext.Consumer, null, function (setReferenceNode) {\n    return React.createElement(InnerReference, _extends({\n      setReferenceNode: setReferenceNode\n    }, props));\n  });\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAAA,gBAA+E;AAC/E,uBAA6B;AAC7B,IAAAC,qBAAsB;;;ACFtB,mBAA4D;AAG5D,wBAAsB;AAEtB,IAAM,aAAa,SAAU,OAAO;AAClC,MAAI;AAAA,IACF,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,EACF,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,SAAS;AACtD,QAAM,OAAO,WAAW;AACxB,QAAM,UAAM,sBAAQ,MAAM,MAAM,KAAK,WAAW,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC;AACtE,QAAM,gBAAY,qBAAO,KAAK;AAC9B,QAAM,2BAAuB,qBAAO;AACpC,QAAM,4BAAwB,qBAAO;AACrC,QAAM,cAAc,SAAU,SAAS;AACrC,QAAI,YAAY,QAAQ;AACtB,gBAAU;AAAA,IACZ;AACA,iBAAa,sBAAsB,OAAO;AAC1C,UAAM,UAAU,WAAW,MAAM;AAC/B,UAAI,UAAU,SAAS;AACrB,sBAAc,IAAI;AAAA,MACpB;AAAA,IACF,GAAG,OAAO;AACV,yBAAqB,UAAU,OAAO,OAAO;AAAA,EAC/C;AACA,QAAM,eAAe,SAAU,SAAS;AACtC,QAAI,YAAY,QAAQ;AACtB,gBAAU;AAAA,IACZ;AACA,iBAAa,qBAAqB,OAAO;AACzC,UAAM,UAAU,WAAW,MAAM;AAC/B,UAAI,UAAU,SAAS;AACrB,sBAAc,KAAK;AAAA,MACrB;AAAA,IACF,GAAG,OAAO;AACV,0BAAsB,UAAU,OAAO,OAAO;AAAA,EAChD;AACA,8BAAU,MAAM;AACd,cAAU,UAAU;AACpB,WAAO,MAAM;AACX,gBAAU,UAAU;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,8BAAU,MAAM;AACd,WAAO,MAAM;AACX,mBAAa,qBAAqB,OAAO;AACzC,mBAAa,sBAAsB,OAAO;AAAA,IAC5C;AAAA,EACF,GAAG,CAAC,uBAAuB,oBAAoB,CAAC;AAChD,QAAM,kBAAkB,SAAU,QAAQ;AACxC,QAAI;AAAA,MACF,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI,WAAW,SAAS,CAAC,IAAI;AAC7B,WAAO;AAAA,MACL;AAAA,MACA,cAAc,qBAAqB,cAAc,MAAM,YAAY,CAAC;AAAA,MACpE,cAAc,qBAAqB,cAAc,MAAM,aAAa,CAAC;AAAA,MACrE,SAAS,qBAAqB,SAAS,MAAM,YAAY,CAAC;AAAA,MAC1D,QAAQ,qBAAqB,QAAQ,MAAM,aAAa,CAAC,CAAC;AAAA,MAC1D,WAAW,qBAAqB,WAAW,WAAS;AAClD,YAAI,MAAM,YAAY,UAAU,UAAU,YAAY;AACpD,uBAAa,CAAC;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,MACD,oBAAoB;AAAA,MACpB,4BAA4B;AAAA,MAC5B,iCAAiC;AAAA,MACjC,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,kBAAkB,SAAU,QAAQ;AACxC,QAAI;AAAA,MACF,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI,WAAW,SAAS,CAAC,IAAI;AAC7B,WAAO;AAAA,MACL;AAAA,MACA,cAAc,qBAAqB,cAAc,MAAM,YAAY,CAAC;AAAA,MACpE,cAAc,qBAAqB,cAAc,MAAM,aAAa,CAAC;AAAA,MACrE,eAAe,CAAC;AAAA,MAChB,IAAI;AAAA,MACJ,GAAG;AAAA,IACL;AAAA,EACF;AACA,SAAO;AAAA,IACL,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,mBAAmB,UAAQ;AAC/B,MAAI;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,aAAAC,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,OAAO,WAAW,OAAO,CAAC,CAAC;AAC9E;AACA,iBAAiB,eAAe;AAAA,EAC9B,mBAAmB;AACrB;AACA,iBAAiB,YAAY;AAAA,EAC3B,UAAU,kBAAAC,QAAU;AAAA,EACpB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,mBAAmB,kBAAAA,QAAU;AAAA,EAC7B,WAAW,kBAAAA,QAAU;AACvB;;;AC3HA,wBAAsB;AACtB,IAAAC,SAAuB;;;ACHvB,IAAAC,SAAuB;AACvB,kCAA0B;AACnB,IAAI,kCAA8B,4BAAAC,SAAc;AAChD,IAAI,wCAAoC,4BAAAA,SAAc;AAE7D,IAAI,UAEJ,SAAU,kBAAkB;AAC1B,iBAAeC,UAAS,gBAAgB;AAExC,WAASA,WAAU;AACjB,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAE9E,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,iBAAiB,MAAM;AAE9F,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,oBAAoB,SAAU,kBAAkB;AACrH,UAAI,oBAAoB,MAAM,kBAAkB,kBAAkB;AAChE,cAAM,gBAAgB;AAEtB,cAAM,YAAY;AAAA,MACpB;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,SAAQ;AAErB,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,SAAK,gBAAgB;AAAA,EACvB;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,WAAa,qBAAc,4BAA4B,UAAU;AAAA,MAC/D,OAAO,KAAK;AAAA,IACd,GAAS,qBAAc,kCAAkC,UAAU;AAAA,MACjE,OAAO,KAAK;AAAA,IACd,GAAG,KAAK,MAAM,QAAQ,CAAC;AAAA,EACzB;AAEA,SAAOA;AACT,EAAQ,gBAAS;;;AC9CV,IAAI,cAAc,SAASC,aAAY,KAAK;AACjD,SAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI;AACvC;AAMO,IAAI,aAAa,SAASC,YAAW,IAAI;AAC9C,MAAI,OAAO,OAAO,YAAY;AAC5B,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,WAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,IACjC;AAEA,WAAO,GAAG,MAAM,QAAQ,IAAI;AAAA,EAC9B;AACF;AAMO,IAAI,eAAe,SAASC,cAAa,MAAM,MAAM;AAC1D,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,QAAQ,OAAO,KAAK,IAAI;AAE5B,MAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,MAAM,MAAM,CAAC;AAEjB,QAAI,KAAK,GAAG,MAAM,KAAK,GAAG,GAAG;AAC3B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAKO,IAAI,SAAS,SAASC,QAAO,KAAK,MAAM;AAE7C,MAAI,OAAO,QAAQ,YAAY;AAC7B,WAAO,WAAW,KAAK,IAAI;AAAA,EAC7B,WACS,OAAO,MAAM;AAClB,QAAI,UAAU;AAAA,EAChB;AACJ;;;AF9CA,IAAI,eAAe;AAAA,EACjB,UAAU;AAAA,EACV,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,eAAe;AACjB;AACA,IAAI,oBAAoB,CAAC;AAClB,IAAI,cAEX,SAAU,kBAAkB;AAC1B,iBAAeC,cAAa,gBAAgB;AAE5C,WAASA,eAAc;AACrB,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAE9E,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,SAAS;AAAA,MAC9E,MAAM;AAAA,MACN,WAAW;AAAA,IACb,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,kBAAkB,MAAM;AAE/F,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,cAAc,IAAI;AAEzF,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,aAAa,IAAI;AAExF,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,iBAAiB,SAAU,YAAY;AAC5G,UAAI,CAAC,cAAc,MAAM,eAAe;AAAY;AACpD,aAAO,MAAM,MAAM,UAAU,UAAU;AACvC,YAAM,aAAa;AAEnB,YAAM,qBAAqB;AAAA,IAC7B,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,gBAAgB,SAAU,WAAW;AAC1G,YAAM,YAAY;AAAA,IACpB,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,uBAAuB;AAAA,MAC5F,SAAS;AAAA,MACT,OAAO;AAAA,MACP,IAAI,SAAS,GAAG,MAAM;AACpB,YAAI,YAAY,KAAK;AAErB,cAAM,SAAS;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,cAAc,WAAY;AAC/F,aAAO;AAAA,QACL,WAAW,MAAM,MAAM;AAAA,QACvB,eAAe,MAAM,MAAM;AAAA,QAC3B,eAAe,MAAM,MAAM;AAAA,QAC3B,WAAW,SAAS,CAAC,GAAG,MAAM,MAAM,WAAW;AAAA,UAC7C,OAAO,SAAS,CAAC,GAAG,MAAM,MAAM,aAAa,MAAM,MAAM,UAAU,OAAO;AAAA,YACxE,SAAS,CAAC,CAAC,MAAM;AAAA,YACjB,SAAS,MAAM;AAAA,UACjB,CAAC;AAAA,UACD,YAAY;AAAA,YACV,SAAS;AAAA,UACX;AAAA,UACA,qBAAqB,MAAM;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,kBAAkB,WAAY;AACnG,aAAO,CAAC,MAAM,cAAc,CAAC,MAAM,MAAM,OAAO,eAAe,SAAS;AAAA,QACtE,UAAU,MAAM,MAAM,KAAK,QAAQ,OAAO;AAAA,MAC5C,GAAG,MAAM,MAAM,KAAK,MAAM;AAAA,IAC5B,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,sBAAsB,WAAY;AACvG,aAAO,CAAC,MAAM,MAAM,OAAO,SAAY,MAAM,MAAM;AAAA,IACrD,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,iBAAiB,WAAY;AAClG,aAAO,CAAC,MAAM,aAAa,CAAC,MAAM,MAAM,OAAO,oBAAoB,MAAM,MAAM,KAAK;AAAA,IACtF,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,2BAA2B,WAAY;AAC5G,aAAO,MAAM,MAAM,OAAO,MAAM,MAAM,KAAK,OAAO;AAAA,IACpD,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,yBAAyB,WAAY;AAC1G,UAAI,CAAC,MAAM;AAAgB;AAE3B,YAAM,eAAe,QAAQ;AAE7B,YAAM,iBAAiB;AAAA,IACzB,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,wBAAwB,WAAY;AACzG,YAAM,sBAAsB;AAE5B,UAAI,wBAAwB,uBAAuB,uBAAuB,KAAK,CAAC,GAC5E,aAAa,sBAAsB;AAEvC,UAAI,mBAAmB,MAAM,MAAM;AACnC,UAAI,CAAC,oBAAoB,CAAC;AAAY;AACtC,YAAM,iBAAiB,IAAI,eAAS,kBAAkB,YAAY,MAAM,WAAW,CAAC;AAAA,IACtF,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,kBAAkB,WAAY;AACnG,UAAI,MAAM,gBAAgB;AACxB,cAAM,eAAe,eAAe;AAAA,MACtC;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,aAAY;AAEzB,SAAO,qBAAqB,SAAS,mBAAmB,WAAW,WAAW;AAE5E,QAAI,KAAK,MAAM,cAAc,UAAU,aAAa,KAAK,MAAM,qBAAqB,UAAU,oBAAoB,KAAK,MAAM,kBAAkB,UAAU,iBAAiB,KAAC,kBAAAC,SAAU,KAAK,MAAM,WAAW,UAAU,WAAW;AAAA,MAC9N,QAAQ;AAAA,IACV,CAAC,GAAG;AAEF,UAAI,MAAwC;AAC1C,YAAI,KAAK,MAAM,cAAc,UAAU,aAAa,KAAK,MAAM,aAAa,QAAQ,UAAU,aAAa,QAAQ,aAAa,KAAK,MAAM,WAAW,UAAU,SAAS,GAAG;AAC1K,kBAAQ,KAAK,oJAAoJ;AAAA,QACnK;AAAA,MACF;AAEA,WAAK,qBAAqB;AAAA,IAC5B,WAAW,KAAK,MAAM,kBAAkB,UAAU,iBAAiB,KAAK,gBAAgB;AACtF,WAAK,MAAM,gBAAgB,KAAK,eAAe,qBAAqB,IAAI,KAAK,eAAe,sBAAsB;AAAA,IACpH;AAMA,QAAI,UAAU,cAAc,KAAK,MAAM,WAAW;AAChD,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAEA,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,WAAO,KAAK,MAAM,UAAU,IAAI;AAChC,SAAK,sBAAsB;AAAA,EAC7B;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,WAAO,YAAY,KAAK,MAAM,QAAQ,EAAE;AAAA,MACtC,KAAK,KAAK;AAAA,MACV,OAAO,KAAK,eAAe;AAAA,MAC3B,WAAW,KAAK,mBAAmB;AAAA,MACnC,iBAAiB,KAAK,wBAAwB;AAAA,MAC9C,gBAAgB,KAAK;AAAA,MACrB,YAAY;AAAA,QACV,KAAK,KAAK;AAAA,QACV,OAAO,KAAK,cAAc;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAOD;AACT,EAAQ,gBAAS;AAEjB,gBAAgB,aAAa,gBAAgB;AAAA,EAC3C,WAAW;AAAA,EACX,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,eAAe;AACjB,CAAC;AAED,IAAI,aAAa,eAAS;AAEX,SAAR,OAAwB,MAAM;AACnC,MAAI,mBAAmB,KAAK,kBACxB,QAAQ,8BAA8B,MAAM,CAAC,kBAAkB,CAAC;AAEpE,SAAa,qBAAc,4BAA4B,UAAU,MAAM,SAAU,eAAe;AAC9F,WAAa,qBAAc,aAAa,SAAS;AAAA,MAC/C,kBAAkB,qBAAqB,SAAY,mBAAmB;AAAA,IACxE,GAAG,KAAK,CAAC;AAAA,EACX,CAAC;AACH;;;AGtMA,IAAAE,SAAuB;AACvB,qBAAoB;AAIpB,IAAI,iBAEJ,SAAU,kBAAkB;AAC1B,iBAAeC,iBAAgB,gBAAgB;AAE/C,WAASA,kBAAiB;AACxB,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAE9E,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,cAAc,SAAU,MAAM;AACnG,aAAO,MAAM,MAAM,UAAU,IAAI;AACjC,iBAAW,MAAM,MAAM,kBAAkB,IAAI;AAAA,IAC/C,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,gBAAe;AAE5B,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,WAAO,KAAK,MAAM,UAAU,IAAI;AAAA,EAClC;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,uBAAAC,SAAQ,QAAQ,KAAK,MAAM,gBAAgB,GAAG,kEAAkE;AAChH,WAAO,YAAY,KAAK,MAAM,QAAQ,EAAE;AAAA,MACtC,KAAK,KAAK;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,SAAOD;AACT,EAAQ,gBAAS;AAEF,SAAR,UAA2B,OAAO;AACvC,SAAa,qBAAc,kCAAkC,UAAU,MAAM,SAAU,kBAAkB;AACvG,WAAa,qBAAc,gBAAgB,SAAS;AAAA,MAClD;AAAA,IACF,GAAG,KAAK,CAAC;AAAA,EACX,CAAC;AACH;;;ALpCA,SAAS,mBAAmB,iBAAiB;AAC3C,QAAM,wBAAwB;AAAA,IAC5B,MAAM;AAAA,IACN,KAAK;AAAA,IACL,aAAa;AAAA,IACb,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,KAAK;AAAA,IACL,WAAW;AAAA,IACX,cAAc;AAAA,IACd,OAAO;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,EAClB;AACA,SAAO,sBAAsB,eAAe;AAC9C;AACA,SAAS,sBAAsB,iBAAiB;AAC9C,QAAM,uBAAuB;AAAA,IAC3B,MAAM;AAAA,IACN,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,OAAO;AAAA,IACP,eAAe;AAAA,IACf,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,cAAc;AAAA,EAChB;AACA,QAAM,kBAAkB,mBAAmB,eAAe;AAC1D,SAAO,qBAAqB,eAAe,KAAK;AAClD;AACA,SAAS,iBAAiB,iBAAiB;AACzC,QAAM,wBAAwB;AAAA,IAC5B,KAAK;AAAA,IACL,aAAa;AAAA,IACb,WAAW;AAAA,IACX,OAAO;AAAA,IACP,eAAe;AAAA,IACf,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,cAAc;AAAA,IACd,YAAY;AAAA,EACd;AACA,SAAO,sBAAsB,eAAe,KAAK;AACnD;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,sCAAO,EAAE,MAAM;AAAA,EACrC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,aAAa,GAAG,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC9E,gBAAgB,eAAe;AAAA,EAC7B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,cAAc,sCAAO,OAAO,MAAM;AAAA,EACtC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,sCAAsC,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,YAAY,UAAU,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACvJ,YAAY,eAAe;AAAA,EACzB,OAAO;AACT;AAEA,IAAM,eAAe;AACrB,IAAM,aAAa,UAAQ;AACzB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,SAAS,GAAG,MAAM,MAAM,OAAO;AACnC,MAAI,eAAe,MAAM,YAAY;AACrC,MAAI,UAAU;AACd,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa;AACjB,MAAI,aAAa,cAAc,MAAM,MAAM,OAAO,GAAG,MAAM,UAAU,EAAE;AACvE,MAAI,WAAW,MAAM,UAAU;AAC/B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,SAAS,SAAS;AACpB,mBAAe,MAAM,YAAY;AACjC,mBAAe;AACf,iBAAa;AACb,eAAW;AAAA,EACb;AACA,MAAI,SAAS,eAAe;AAC1B,cAAU,GAAG,MAAM,MAAM,OAAO;AAChC,eAAW;AACX,iBAAa,cAAc,MAAM,MAAM,OAAO,GAAG,MAAM,UAAU,EAAE;AACnE,yBAAqB,GAAG,MAAM,MAAM,OAAO;AAAA,EAC7C,WAAW,SAAS,SAAS;AAC3B,cAAU,GAAG,MAAM,MAAM,OAAO;AAChC,eAAW;AACX,iBAAa,cAAc,MAAM,MAAM,OAAO,GAAG,MAAM,UAAU,EAAE;AACnE,yBAAqB,GAAG,MAAM,MAAM,OAAO;AAAA,EAC7C,WAAW,SAAS,UAAU;AAC5B,cAAU;AACV,eAAW;AACX,iBAAa,cAAc,MAAM,MAAM,OAAO,GAAG,MAAM,UAAU,EAAE;AAAA,EACrE;AACA,MAAI,SAAS,iBAAiB,SAAS,SAAS;AAC9C,eAAW,MAAM,UAAU;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU;AACZ,QAAI,SAAS,WAAW,SAAS,UAAU;AACzC,kBAAY;AACZ,mBAAa,SAAS,SAAS,QAAQ;AAAA,IACzC,OAAO;AACL,mBAAa,SAAS,SAAS,QAAQ;AACvC,UAAI,SAAS,SAAS;AACpB,iBAAS,GAAG,MAAM,MAAM,OAAO;AAC/B,oBAAY;AAAA,MACd,WAAW,SAAS,eAAe;AACjC,iBAAS,GAAG,MAAM,MAAM,OAAO;AAC/B,oBAAY,GAAG,MAAM,MAAM,OAAO;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACA,SAAO,GAAI,CAAC,WAAW,mBAAmB,aAAa,eAAe,iBAAiB,eAAe,iBAAiB,eAAe,mBAAmB,KAAK,KAAK,gBAAgB,MAAM,aAAa,IAAI,GAAG,QAAQ,cAAc,SAAS,UAAU,YAAY,UAAU,YAAY,UAAU,cAAc,YAAY,YAAY,iBAAiB,SAAS,GAAG;AAAA,IACnW,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC,GAAG,iBAAiB,oBAAoB,aAAa,YAAY;AACpE;AACA,IAAM,cAAc,WAAS;AAC3B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI;AACJ,MAAI,YAAY,MAAM,QAAQ,GAAG,GAAG,MAAM,MAAM,UAAU,GAAG,MAAM,MAAM,OAAO,OAAO,SAAS,aAAa,KAAK,OAAO,IAAI,CAAC;AAC9H,MAAI,kBAAkB,SAAS,aAAa,KAAK,KAAK;AACtD,MAAI,QAAQ,MAAM,OAAO;AACzB,MAAI;AACJ,MAAI,SAAS,SAAS;AACpB,gBAAY,MAAM,QAAQ,GAAG,GAAG,MAAM,MAAM,OAAO,OAAO,GAAG,MAAM,MAAM,OAAO,OAAO,SAAS,aAAa,KAAK,OAAO,IAAI,CAAC;AAC9H,aAAS,GAAG,MAAM,QAAQ,MAAM,SAAS,cAAc,KAAK,KAAK;AACjE,sBAAkB,MAAM,OAAO;AAC/B,YAAQ,SAAS,cAAc,KAAK,KAAK;AACzC,iBAAa,MAAM,OAAO;AAAA,EAC5B;AACA,SAAO,GAAI,CAAC,WAAW,gBAAgB,sBAAsB,WAAW,KAAK,WAAW,IAAI,GAAG,QAAQ,WAAW,iBAAiB,OAAO,aAAa,UAAU;AACnK;AACA,IAAM,gBAAgB,sCAAO,IAAI,MAAM;AAAA,EACrC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,yDAAyD,gBAAgB,iBAAiB,KAAK,yCAAyC,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,OAAO,OAAO,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,MAAM,MAAM,YAAY,SAAS,WAAS,WAAW,KAAK,GAAG,aAAa,WAAS,wBAAwB,cAAc,KAAK,CAAC;AAC/W,cAAc,eAAe;AAAA,EAC3B,OAAO;AACT;AAEA,IAAM,uBAAuB,sCAAO,IAAI,WAAW;AAAA,EACjD,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,8CAA8C,sDAAsD,GAAG,WAAS,MAAM,MAAM;AAChI,qBAAqB,eAAe;AAAA,EAClC,OAAO;AACT;AAEA,IAAM,mBAAmB,CAAC,QAAQ,OAAO,aAAa,WAAW,UAAU,gBAAgB,YAAY;AACvG,IAAM,YAAY,CAAC,GAAG,kBAAkB,OAAO,WAAW,cAAc,SAAS,aAAa,cAAc;AAC5G,IAAM,OAAO,CAAC,SAAS,UAAU,SAAS,aAAa;AACvD,IAAM,OAAO,CAAC,SAAS,MAAM;AAE7B,IAAM,UAAU,UAAQ;AACtB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,QAAI,0BAAW,EAAY;AAC3B,QAAM,wBAAoB,sBAAO;AACjC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,WAAW;AAAA,IACb;AAAA,IACA,mBAAmB;AAAA,IACnB,WAAW;AAAA,EACb,CAAC;AACD,QAAM,sBAAsB,mBAAmB,mBAAmB,SAAS;AAC3E,+BAAU,MAAM;AACd,QAAI,uBAAuB,kBAAkB,SAAS;AACpD,wBAAkB,QAAQ;AAAA,IAC5B;AAAA,EACF,GAAG,CAAC,qBAAqB,OAAO,CAAC;AACjC,QAAM,kBAAkB,MAAM,sBAAsB,SAAS,IAAI,mBAAmB,SAAS;AAC7F,QAAM,cAAc,cAAAE,QAAM,SAAS,KAAK,QAAQ;AAChD,QAAM,YAAY;AAAA,IAChB,iBAAiB;AAAA,MACf,mBAAmB;AAAA,IACrB;AAAA,IACA,GAAG;AAAA,EACL;AACA,SAAO,cAAAA,QAAM,cAAc,SAAS,MAAM,cAAAA,QAAM,cAAc,WAAW,MAAM,WAAS;AACtF,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,eAAO,4BAAa,aAAa,gBAAgB;AAAA,MAC/C,GAAG,YAAY;AAAA,MACf,CAAC,MAAM,GAAG,6BAAU,CAAC,KAAK,YAAY,MAAM,YAAY,MAAM,IAAI,CAAC;AAAA,IACrE,CAAC,CAAC;AAAA,EACJ,CAAC,GAAG,cAAAA,QAAM,cAAc,QAAQ;AAAA,IAC9B,WAAW;AAAA,IACX,eAAe,uBAAuB;AAAA,IACtC;AAAA,EACF,GAAG,WAAS;AACV,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb,IAAI;AACJ,sBAAkB,UAAU;AAC5B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,QAAI,eAAe;AACnB,QAAI,iBAAiB,QAAW;AAC9B,UAAI,SAAS,QAAQ;AACnB,uBAAe;AAAA,MACjB,OAAO;AACL,uBAAe;AAAA,MACjB;AAAA,IACF;AACA,UAAM,eAAe;AAAA,MACnB;AAAA,MACA,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS,qBAAqB,SAAS,MAAM;AAC3C,oBAAY;AAAA,MACd,CAAC;AAAA,MACD,QAAQ,qBAAqB,QAAQ,MAAM;AACzC,qBAAa,CAAC;AAAA,MAChB,CAAC;AAAA,MACD,eAAe,CAAC;AAAA,MAChB;AAAA,MACA,GAAG;AAAA,IACL;AACA,UAAM,UAAU,cAAAA,QAAM,cAAc,sBAAsB;AAAA,MACxD,KAAK,sBAAsB,MAAM;AAAA,MACjC;AAAA,MACA;AAAA,MACA,eAAe,CAAC;AAAA,IAClB,GAAG,cAAAA,QAAM,cAAc,eAAe,gBAAgB,YAAY,GAAG,OAAO,CAAC;AAC7E,QAAI,cAAc;AAChB,iBAAO,+BAAa,SAAS,YAAY;AAAA,IAC3C;AACA,WAAO;AAAA,EACT,CAAC,CAAC;AACJ;AACA,QAAQ,cAAc;AACtB,QAAQ,YAAY;AAAA,EAClB,cAAc,mBAAAC,QAAU;AAAA,EACxB,UAAU,mBAAAA,QAAU;AAAA,EACpB,SAAS,mBAAAA,QAAU;AAAA,EACnB,eAAe,mBAAAA,QAAU;AAAA,EACzB,IAAI,mBAAAA,QAAU;AAAA,EACd,SAAS,mBAAAA,QAAU,KAAK;AAAA,EACxB,WAAW,mBAAAA,QAAU,MAAM,SAAS;AAAA,EACpC,iBAAiB,mBAAAA,QAAU;AAAA,EAC3B,MAAM,mBAAAA,QAAU,MAAM,IAAI;AAAA,EAC1B,MAAM,mBAAAA,QAAU,MAAM,IAAI;AAAA,EAC1B,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAChE,kBAAkB,mBAAAA,QAAU;AAAA,EAC5B,QAAQ,mBAAAA,QAAU;AACpB;AACA,QAAQ,eAAe;AAAA,EACrB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AACV;AAEA,SAASC,YAAW;AAClB,EAAAA,YAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACvC;AAEA,IAAM,gBAAY,0BAAW,CAAC,OAAO,QAAQ,cAAAF,QAAM,cAAc,iBAAiBE,UAAS;AAAA,EACzF;AACF,GAAG,KAAK,CAAC,CAAC;AACV,UAAU,cAAc;AAExB,IAAM,YAAQ,0BAAW,CAAC,OAAO,QAAQ,cAAAF,QAAM,cAAc,aAAaE,UAAS;AAAA,EACjF;AACF,GAAG,KAAK,CAAC,CAAC;AACV,MAAM,cAAc;", "names": ["import_react", "import_prop_types", "React", "PropTypes", "React", "React", "createContext", "Manager", "unwrapArray", "safeInvoke", "shallowEqual", "setRef", "InnerPopper", "deepEqual", "React", "InnerReference", "warning", "React", "PropTypes", "_extends"]}