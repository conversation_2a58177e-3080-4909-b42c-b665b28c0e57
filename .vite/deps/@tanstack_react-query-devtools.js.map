{"version": 3, "sources": ["../../node_modules/@tanstack/react-query-devtools/build/lib/_virtual/_rollupPluginBabelHelpers.mjs", "../../node_modules/@tanstack/match-sorter-utils/src/remove-accents.ts", "../../node_modules/@tanstack/match-sorter-utils/src/index.ts", "../../node_modules/@tanstack/react-query-devtools/src/useLocalStorage.ts", "../../node_modules/superjson/src/double-indexed-kv.ts", "../../node_modules/superjson/src/registry.ts", "../../node_modules/superjson/src/class-registry.ts", "../../node_modules/superjson/src/util.ts", "../../node_modules/superjson/src/custom-transformer-registry.ts", "../../node_modules/superjson/src/is.ts", "../../node_modules/superjson/src/pathstringifier.ts", "../../node_modules/superjson/src/transformer.ts", "../../node_modules/superjson/src/accessDeep.ts", "../../node_modules/superjson/src/plainer.ts", "../../node_modules/is-what/dist/index.js", "../../node_modules/copy-anything/dist/index.js", "../../node_modules/superjson/src/index.ts", "../../node_modules/@tanstack/react-query-devtools/src/theme.tsx", "../../node_modules/@tanstack/react-query-devtools/src/useMediaQuery.ts", "../../node_modules/@tanstack/react-query-devtools/src/utils.ts", "../../node_modules/@tanstack/react-query-devtools/src/styledComponents.ts", "../../node_modules/@tanstack/react-query-devtools/src/screenreader.tsx", "../../node_modules/@tanstack/react-query-devtools/src/Explorer.tsx", "../../node_modules/@tanstack/react-query-devtools/src/Logo.tsx", "../../node_modules/@tanstack/react-query-devtools/src/devtools.tsx", "../../node_modules/@tanstack/react-query-devtools/src/index.ts"], "sourcesContent": ["function _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nexport { _extends as extends };\n//# sourceMappingURL=_rollupPluginBabelHelpers.mjs.map\n", "const characterMap: Record<string, string> = {\n  À: 'A',\n  Á: 'A',\n  Â: 'A',\n  Ã: 'A',\n  Ä: 'A',\n  Å: 'A',\n  Ấ: 'A',\n  Ắ: 'A',\n  Ẳ: 'A',\n  Ẵ: 'A',\n  Ặ: 'A',\n  Æ: 'AE',\n  Ầ: 'A',\n  Ằ: 'A',\n  Ȃ: 'A',\n  Ç: 'C',\n  Ḉ: 'C',\n  È: 'E',\n  É: 'E',\n  Ê: 'E',\n  Ë: 'E',\n  Ế: 'E',\n  Ḗ: 'E',\n  Ề: 'E',\n  Ḕ: 'E',\n  Ḝ: 'E',\n  Ȇ: 'E',\n  Ì: 'I',\n  Í: 'I',\n  Î: 'I',\n  Ï: 'I',\n  Ḯ: 'I',\n  Ȋ: 'I',\n  Ð: 'D',\n  Ñ: 'N',\n  Ò: 'O',\n  Ó: 'O',\n  Ô: 'O',\n  Õ: 'O',\n  Ö: 'O',\n  Ø: 'O',\n  Ố: 'O',\n  Ṍ: 'O',\n  Ṓ: 'O',\n  Ȏ: 'O',\n  Ù: 'U',\n  Ú: 'U',\n  Û: 'U',\n  Ü: 'U',\n  Ý: 'Y',\n  à: 'a',\n  á: 'a',\n  â: 'a',\n  ã: 'a',\n  ä: 'a',\n  å: 'a',\n  ấ: 'a',\n  ắ: 'a',\n  ẳ: 'a',\n  ẵ: 'a',\n  ặ: 'a',\n  æ: 'ae',\n  ầ: 'a',\n  ằ: 'a',\n  ȃ: 'a',\n  ç: 'c',\n  ḉ: 'c',\n  è: 'e',\n  é: 'e',\n  ê: 'e',\n  ë: 'e',\n  ế: 'e',\n  ḗ: 'e',\n  ề: 'e',\n  ḕ: 'e',\n  ḝ: 'e',\n  ȇ: 'e',\n  ì: 'i',\n  í: 'i',\n  î: 'i',\n  ï: 'i',\n  ḯ: 'i',\n  ȋ: 'i',\n  ð: 'd',\n  ñ: 'n',\n  ò: 'o',\n  ó: 'o',\n  ô: 'o',\n  õ: 'o',\n  ö: 'o',\n  ø: 'o',\n  ố: 'o',\n  ṍ: 'o',\n  ṓ: 'o',\n  ȏ: 'o',\n  ù: 'u',\n  ú: 'u',\n  û: 'u',\n  ü: 'u',\n  ý: 'y',\n  ÿ: 'y',\n  Ā: 'A',\n  ā: 'a',\n  Ă: 'A',\n  ă: 'a',\n  Ą: 'A',\n  ą: 'a',\n  Ć: 'C',\n  ć: 'c',\n  Ĉ: 'C',\n  ĉ: 'c',\n  Ċ: 'C',\n  ċ: 'c',\n  Č: 'C',\n  č: 'c',\n  C̆: 'C',\n  c̆: 'c',\n  Ď: 'D',\n  ď: 'd',\n  Đ: 'D',\n  đ: 'd',\n  Ē: 'E',\n  ē: 'e',\n  Ĕ: 'E',\n  ĕ: 'e',\n  Ė: 'E',\n  ė: 'e',\n  Ę: 'E',\n  ę: 'e',\n  Ě: 'E',\n  ě: 'e',\n  Ĝ: 'G',\n  Ǵ: 'G',\n  ĝ: 'g',\n  ǵ: 'g',\n  Ğ: 'G',\n  ğ: 'g',\n  Ġ: 'G',\n  ġ: 'g',\n  Ģ: 'G',\n  ģ: 'g',\n  Ĥ: 'H',\n  ĥ: 'h',\n  Ħ: 'H',\n  ħ: 'h',\n  Ḫ: 'H',\n  ḫ: 'h',\n  Ĩ: 'I',\n  ĩ: 'i',\n  Ī: 'I',\n  ī: 'i',\n  Ĭ: 'I',\n  ĭ: 'i',\n  Į: 'I',\n  į: 'i',\n  İ: 'I',\n  ı: 'i',\n  Ĳ: 'IJ',\n  ĳ: 'ij',\n  Ĵ: 'J',\n  ĵ: 'j',\n  Ķ: 'K',\n  ķ: 'k',\n  Ḱ: 'K',\n  ḱ: 'k',\n  K̆: 'K',\n  k̆: 'k',\n  Ĺ: 'L',\n  ĺ: 'l',\n  Ļ: 'L',\n  ļ: 'l',\n  Ľ: 'L',\n  ľ: 'l',\n  Ŀ: 'L',\n  ŀ: 'l',\n  Ł: 'l',\n  ł: 'l',\n  Ḿ: 'M',\n  ḿ: 'm',\n  M̆: 'M',\n  m̆: 'm',\n  Ń: 'N',\n  ń: 'n',\n  Ņ: 'N',\n  ņ: 'n',\n  Ň: 'N',\n  ň: 'n',\n  ŉ: 'n',\n  N̆: 'N',\n  n̆: 'n',\n  Ō: 'O',\n  ō: 'o',\n  Ŏ: 'O',\n  ŏ: 'o',\n  Ő: 'O',\n  ő: 'o',\n  Œ: 'OE',\n  œ: 'oe',\n  P̆: 'P',\n  p̆: 'p',\n  Ŕ: 'R',\n  ŕ: 'r',\n  Ŗ: 'R',\n  ŗ: 'r',\n  Ř: 'R',\n  ř: 'r',\n  R̆: 'R',\n  r̆: 'r',\n  Ȓ: 'R',\n  ȓ: 'r',\n  Ś: 'S',\n  ś: 's',\n  Ŝ: 'S',\n  ŝ: 's',\n  Ş: 'S',\n  Ș: 'S',\n  ș: 's',\n  ş: 's',\n  Š: 'S',\n  š: 's',\n  Ţ: 'T',\n  ţ: 't',\n  ț: 't',\n  Ț: 'T',\n  Ť: 'T',\n  ť: 't',\n  Ŧ: 'T',\n  ŧ: 't',\n  T̆: 'T',\n  t̆: 't',\n  Ũ: 'U',\n  ũ: 'u',\n  Ū: 'U',\n  ū: 'u',\n  Ŭ: 'U',\n  ŭ: 'u',\n  Ů: 'U',\n  ů: 'u',\n  Ű: 'U',\n  ű: 'u',\n  Ų: 'U',\n  ų: 'u',\n  Ȗ: 'U',\n  ȗ: 'u',\n  V̆: 'V',\n  v̆: 'v',\n  Ŵ: 'W',\n  ŵ: 'w',\n  Ẃ: 'W',\n  ẃ: 'w',\n  X̆: 'X',\n  x̆: 'x',\n  Ŷ: 'Y',\n  ŷ: 'y',\n  Ÿ: 'Y',\n  Y̆: 'Y',\n  y̆: 'y',\n  Ź: 'Z',\n  ź: 'z',\n  Ż: 'Z',\n  ż: 'z',\n  Ž: 'Z',\n  ž: 'z',\n  ſ: 's',\n  ƒ: 'f',\n  Ơ: 'O',\n  ơ: 'o',\n  Ư: 'U',\n  ư: 'u',\n  Ǎ: 'A',\n  ǎ: 'a',\n  Ǐ: 'I',\n  ǐ: 'i',\n  Ǒ: 'O',\n  ǒ: 'o',\n  Ǔ: 'U',\n  ǔ: 'u',\n  Ǖ: 'U',\n  ǖ: 'u',\n  Ǘ: 'U',\n  ǘ: 'u',\n  Ǚ: 'U',\n  ǚ: 'u',\n  Ǜ: 'U',\n  ǜ: 'u',\n  Ứ: 'U',\n  ứ: 'u',\n  Ṹ: 'U',\n  ṹ: 'u',\n  Ǻ: 'A',\n  ǻ: 'a',\n  Ǽ: 'AE',\n  ǽ: 'ae',\n  Ǿ: 'O',\n  ǿ: 'o',\n  Þ: 'TH',\n  þ: 'th',\n  Ṕ: 'P',\n  ṕ: 'p',\n  Ṥ: 'S',\n  ṥ: 's',\n  X́: 'X',\n  x́: 'x',\n  Ѓ: 'Г',\n  ѓ: 'г',\n  Ќ: 'К',\n  ќ: 'к',\n  A̋: 'A',\n  a̋: 'a',\n  E̋: 'E',\n  e̋: 'e',\n  I̋: 'I',\n  i̋: 'i',\n  Ǹ: 'N',\n  ǹ: 'n',\n  Ồ: 'O',\n  ồ: 'o',\n  Ṑ: 'O',\n  ṑ: 'o',\n  Ừ: 'U',\n  ừ: 'u',\n  Ẁ: 'W',\n  ẁ: 'w',\n  Ỳ: 'Y',\n  ỳ: 'y',\n  Ȁ: 'A',\n  ȁ: 'a',\n  Ȅ: 'E',\n  ȅ: 'e',\n  Ȉ: 'I',\n  ȉ: 'i',\n  Ȍ: 'O',\n  ȍ: 'o',\n  Ȑ: 'R',\n  ȑ: 'r',\n  Ȕ: 'U',\n  ȕ: 'u',\n  B̌: 'B',\n  b̌: 'b',\n  Č̣: 'C',\n  č̣: 'c',\n  Ê̌: 'E',\n  ê̌: 'e',\n  F̌: 'F',\n  f̌: 'f',\n  Ǧ: 'G',\n  ǧ: 'g',\n  Ȟ: 'H',\n  ȟ: 'h',\n  J̌: 'J',\n  ǰ: 'j',\n  Ǩ: 'K',\n  ǩ: 'k',\n  M̌: 'M',\n  m̌: 'm',\n  P̌: 'P',\n  p̌: 'p',\n  Q̌: 'Q',\n  q̌: 'q',\n  Ř̩: 'R',\n  ř̩: 'r',\n  Ṧ: 'S',\n  ṧ: 's',\n  V̌: 'V',\n  v̌: 'v',\n  W̌: 'W',\n  w̌: 'w',\n  X̌: 'X',\n  x̌: 'x',\n  Y̌: 'Y',\n  y̌: 'y',\n  A̧: 'A',\n  a̧: 'a',\n  B̧: 'B',\n  b̧: 'b',\n  Ḑ: 'D',\n  ḑ: 'd',\n  Ȩ: 'E',\n  ȩ: 'e',\n  Ɛ̧: 'E',\n  ɛ̧: 'e',\n  Ḩ: 'H',\n  ḩ: 'h',\n  I̧: 'I',\n  i̧: 'i',\n  Ɨ̧: 'I',\n  ɨ̧: 'i',\n  M̧: 'M',\n  m̧: 'm',\n  O̧: 'O',\n  o̧: 'o',\n  Q̧: 'Q',\n  q̧: 'q',\n  U̧: 'U',\n  u̧: 'u',\n  X̧: 'X',\n  x̧: 'x',\n  Z̧: 'Z',\n  z̧: 'z',\n}\n\nconst chars = Object.keys(characterMap).join('|')\nconst allAccents = new RegExp(chars, 'g')\n\nexport function removeAccents(str: string) {\n  return str.replace(allAccents, match => {\n    return characterMap[match]!\n  })\n}\n", "/**\n * @name match-sorter\n * @license MIT license.\n * @copyright (c) 2099 Kent <PERSON><PERSON>\n * <AUTHOR> <<EMAIL>> (https://kentcdodds.com)\n */\n\n// This is a fork of match-sorter. Instead of offering\n// a unified API for filtering and sorting in a single pass,\n// match-sorter-utils provides the lower-level utilities of\n// ranking items and comparing ranks in a way that can\n// be incrementally applied to a system rather than\n// all-at-once.\n\n// 1. Use the rankItem function to rank an item\n// 2. Use the resulting rankingInfo.passed to filter\n// 3. Use the resulting rankingInfo.rank to sort\n\n// For bundling purposes (mainly remove-accents not being esm safe/ready),\n// we've also hard-coded remove-accents into this source.\n// The remove-accents package is still included as a dependency\n// for attribution purposes, but it will not be imported and bundled.\n\nimport { removeAccents } from './remove-accents'\n\nexport type AccessorAttributes = {\n  threshold?: Ranking\n  maxRanking: Ranking\n  minRanking: Ranking\n}\n\nexport interface RankingInfo {\n  rankedValue: any\n  rank: Ranking\n  accessorIndex: number\n  accessorThreshold: Ranking | undefined\n  passed: boolean\n}\n\nexport interface AccessorOptions<TItem> {\n  accessor: AccessorFn<TItem>\n  threshold?: Ranking\n  maxRanking?: Ranking\n  minRanking?: Ranking\n}\n\nexport type AccessorFn<TItem> = (item: TItem) => string | Array<string>\n\nexport type Accessor<TItem> = AccessorFn<TItem> | AccessorOptions<TItem>\n\nexport interface RankItemOptions<TItem = unknown> {\n  accessors?: ReadonlyArray<Accessor<TItem>>\n  threshold?: Ranking\n  keepDiacritics?: boolean\n}\n\nexport const rankings = {\n  CASE_SENSITIVE_EQUAL: 7,\n  EQUAL: 6,\n  STARTS_WITH: 5,\n  WORD_STARTS_WITH: 4,\n  CONTAINS: 3,\n  ACRONYM: 2,\n  MATCHES: 1,\n  NO_MATCH: 0,\n} as const\n\nexport type Ranking = (typeof rankings)[keyof typeof rankings]\n\n/**\n * Gets the highest ranking for value for the given item based on its values for the given keys\n * @param {*} item - the item to rank\n * @param {Array} keys - the keys to get values from the item for the ranking\n * @param {String} value - the value to rank against\n * @param {Object} options - options to control the ranking\n * @return {{rank: Number, accessorIndex: Number, accessorThreshold: Number}} - the highest ranking\n */\nexport function rankItem<TItem>(\n  item: TItem,\n  value: string,\n  options?: RankItemOptions<TItem>\n): RankingInfo {\n  options = options || {}\n\n  options.threshold = options.threshold ?? rankings.MATCHES\n\n  if (!options.accessors) {\n    // if keys is not specified, then we assume the item given is ready to be matched\n    const rank = getMatchRanking(item as unknown as string, value, options)\n    return {\n      // ends up being duplicate of 'item' in matches but consistent\n      rankedValue: item,\n      rank,\n      accessorIndex: -1,\n      accessorThreshold: options.threshold,\n      passed: rank >= options.threshold,\n    }\n  }\n\n  const valuesToRank = getAllValuesToRank(item, options.accessors)\n\n  const rankingInfo: RankingInfo = {\n    rankedValue: item,\n    rank: rankings.NO_MATCH as Ranking,\n    accessorIndex: -1,\n    accessorThreshold: options.threshold,\n    passed: false,\n  }\n\n  for (let i = 0; i < valuesToRank.length; i++) {\n    const rankValue = valuesToRank[i]!\n\n    let newRank = getMatchRanking(rankValue.itemValue, value, options)\n\n    const {\n      minRanking,\n      maxRanking,\n      threshold = options.threshold,\n    } = rankValue.attributes\n\n    if (newRank < minRanking && newRank >= rankings.MATCHES) {\n      newRank = minRanking\n    } else if (newRank > maxRanking) {\n      newRank = maxRanking\n    }\n\n    newRank = Math.min(newRank, maxRanking) as Ranking\n\n    if (newRank >= threshold && newRank > rankingInfo.rank) {\n      rankingInfo.rank = newRank\n      rankingInfo.passed = true\n      rankingInfo.accessorIndex = i\n      rankingInfo.accessorThreshold = threshold\n      rankingInfo.rankedValue = rankValue.itemValue\n    }\n  }\n\n  return rankingInfo\n}\n\n/**\n * Gives a rankings score based on how well the two strings match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @param {Object} options - options for the match (like keepDiacritics for comparison)\n * @returns {Number} the ranking for how well stringToRank matches testString\n */\nfunction getMatchRanking<TItem>(\n  testString: string,\n  stringToRank: string,\n  options: RankItemOptions<TItem>\n): Ranking {\n  testString = prepareValueForComparison(testString, options)\n  stringToRank = prepareValueForComparison(stringToRank, options)\n\n  // too long\n  if (stringToRank.length > testString.length) {\n    return rankings.NO_MATCH\n  }\n\n  // case sensitive equals\n  if (testString === stringToRank) {\n    return rankings.CASE_SENSITIVE_EQUAL\n  }\n\n  // Lower casing before further comparison\n  testString = testString.toLowerCase()\n  stringToRank = stringToRank.toLowerCase()\n\n  // case insensitive equals\n  if (testString === stringToRank) {\n    return rankings.EQUAL\n  }\n\n  // starts with\n  if (testString.startsWith(stringToRank)) {\n    return rankings.STARTS_WITH\n  }\n\n  // word starts with\n  if (testString.includes(` ${stringToRank}`)) {\n    return rankings.WORD_STARTS_WITH\n  }\n\n  // contains\n  if (testString.includes(stringToRank)) {\n    return rankings.CONTAINS\n  } else if (stringToRank.length === 1) {\n    // If the only character in the given stringToRank\n    //   isn't even contained in the testString, then\n    //   it's definitely not a match.\n    return rankings.NO_MATCH\n  }\n\n  // acronym\n  if (getAcronym(testString).includes(stringToRank)) {\n    return rankings.ACRONYM\n  }\n\n  // will return a number between rankings.MATCHES and\n  // rankings.MATCHES + 1 depending  on how close of a match it is.\n  return getClosenessRanking(testString, stringToRank)\n}\n\n/**\n * Generates an acronym for a string.\n *\n * @param {String} string the string for which to produce the acronym\n * @returns {String} the acronym\n */\nfunction getAcronym(string: string): string {\n  let acronym = ''\n  const wordsInString = string.split(' ')\n  wordsInString.forEach(wordInString => {\n    const splitByHyphenWords = wordInString.split('-')\n    splitByHyphenWords.forEach(splitByHyphenWord => {\n      acronym += splitByHyphenWord.substr(0, 1)\n    })\n  })\n  return acronym\n}\n\n/**\n * Returns a score based on how spread apart the\n * characters from the stringToRank are within the testString.\n * A number close to rankings.MATCHES represents a loose match. A number close\n * to rankings.MATCHES + 1 represents a tighter match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @returns {Number} the number between rankings.MATCHES and\n * rankings.MATCHES + 1 for how well stringToRank matches testString\n */\nfunction getClosenessRanking(\n  testString: string,\n  stringToRank: string\n): Ranking {\n  let matchingInOrderCharCount = 0\n  let charNumber = 0\n  function findMatchingCharacter(\n    matchChar: undefined | string,\n    string: string,\n    index: number\n  ) {\n    for (let j = index, J = string.length; j < J; j++) {\n      const stringChar = string[j]\n      if (stringChar === matchChar) {\n        matchingInOrderCharCount += 1\n        return j + 1\n      }\n    }\n    return -1\n  }\n  function getRanking(spread: number) {\n    const spreadPercentage = 1 / spread\n    const inOrderPercentage = matchingInOrderCharCount / stringToRank.length\n    const ranking = rankings.MATCHES + inOrderPercentage * spreadPercentage\n    return ranking as Ranking\n  }\n  const firstIndex = findMatchingCharacter(stringToRank[0], testString, 0)\n  if (firstIndex < 0) {\n    return rankings.NO_MATCH\n  }\n  charNumber = firstIndex\n  for (let i = 1, I = stringToRank.length; i < I; i++) {\n    const matchChar = stringToRank[i]\n    charNumber = findMatchingCharacter(matchChar, testString, charNumber)\n    const found = charNumber > -1\n    if (!found) {\n      return rankings.NO_MATCH\n    }\n  }\n\n  const spread = charNumber - firstIndex\n  return getRanking(spread)\n}\n\n/**\n * Sorts items that have a rank, index, and accessorIndex\n * @param {Object} a - the first item to sort\n * @param {Object} b - the second item to sort\n * @return {Number} -1 if a should come first, 1 if b should come first, 0 if equal\n */\nexport function compareItems<TItem>(a: RankingInfo, b: RankingInfo): number {\n  return a.rank === b.rank ? 0 : a.rank > b.rank ? -1 : 1\n}\n\n/**\n * Prepares value for comparison by stringifying it, removing diacritics (if specified)\n * @param {String} value - the value to clean\n * @param {Object} options - {keepDiacritics: whether to remove diacritics}\n * @return {String} the prepared value\n */\nfunction prepareValueForComparison<TItem>(\n  value: string,\n  { keepDiacritics }: RankItemOptions<TItem>\n): string {\n  // value might not actually be a string at this point (we don't get to choose)\n  // so part of preparing the value for comparison is ensure that it is a string\n  value = `${value}` // toString\n  if (!keepDiacritics) {\n    value = removeAccents(value)\n  }\n  return value\n}\n\n/**\n * Gets value for key in item at arbitrarily nested keypath\n * @param {Object} item - the item\n * @param {Object|Function} key - the potentially nested keypath or property callback\n * @return {Array} - an array containing the value(s) at the nested keypath\n */\nfunction getItemValues<TItem>(\n  item: TItem,\n  accessor: Accessor<TItem>\n): Array<string> {\n  let accessorFn = accessor as AccessorFn<TItem>\n\n  if (typeof accessor === 'object') {\n    accessorFn = accessor.accessor\n  }\n\n  const value = accessorFn(item)\n\n  // because `value` can also be undefined\n  if (value == null) {\n    return []\n  }\n\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  return [String(value)]\n}\n\n/**\n * Gets all the values for the given keys in the given item and returns an array of those values\n * @param item - the item from which the values will be retrieved\n * @param keys - the keys to use to retrieve the values\n * @return objects with {itemValue, attributes}\n */\nfunction getAllValuesToRank<TItem>(\n  item: TItem,\n  accessors: ReadonlyArray<Accessor<TItem>>\n) {\n  const allValues: Array<{\n    itemValue: string\n    attributes: AccessorAttributes\n  }> = []\n  for (let j = 0, J = accessors.length; j < J; j++) {\n    const accessor = accessors[j]!\n    const attributes = getAccessorAttributes(accessor)\n    const itemValues = getItemValues(item, accessor)\n    for (let i = 0, I = itemValues.length; i < I; i++) {\n      allValues.push({\n        itemValue: itemValues[i]!,\n        attributes,\n      })\n    }\n  }\n  return allValues\n}\n\nconst defaultKeyAttributes = {\n  maxRanking: Infinity as Ranking,\n  minRanking: -Infinity as Ranking,\n}\n/**\n * Gets all the attributes for the given accessor\n * @param accessor - the accessor from which the attributes will be retrieved\n * @return object containing the accessor's attributes\n */\nfunction getAccessorAttributes<TItem>(\n  accessor: Accessor<TItem>\n): AccessorAttributes {\n  if (typeof accessor === 'function') {\n    return defaultKeyAttributes\n  }\n  return { ...defaultKeyAttributes, ...accessor }\n}\n", "import * as React from 'react'\n\nconst getItem = (key: string): unknown => {\n  try {\n    const itemValue = localStorage.getItem(key)\n    if (typeof itemValue === 'string') {\n      return JSON.parse(itemValue)\n    }\n    return undefined\n  } catch {\n    return undefined\n  }\n}\n\nexport default function useLocalStorage<T>(\n  key: string,\n  defaultValue: T | undefined,\n): [T | undefined, (newVal: T | ((prevVal: T) => T)) => void] {\n  const [value, setValue] = React.useState<T>()\n\n  React.useEffect(() => {\n    const initialValue = getItem(key) as T | undefined\n\n    if (typeof initialValue === 'undefined' || initialValue === null) {\n      setValue(\n        typeof defaultValue === 'function' ? defaultValue() : defaultValue,\n      )\n    } else {\n      setValue(initialValue)\n    }\n  }, [defaultValue, key])\n\n  const setter = React.useCallback(\n    (updater: any) => {\n      setValue((old) => {\n        let newVal = updater\n\n        if (typeof updater == 'function') {\n          newVal = updater(old)\n        }\n        try {\n          localStorage.setItem(key, JSON.stringify(newVal))\n        } catch {}\n\n        return newVal\n      })\n    },\n    [key],\n  )\n\n  return [value, setter]\n}\n", "export class DoubleIndexedKV<K, V> {\n  keyToValue = new Map<K, V>();\n  valueToKey = new Map<V, K>();\n\n  set(key: K, value: V) {\n    this.keyToValue.set(key, value);\n    this.valueToKey.set(value, key);\n  }\n\n  getByKey(key: K): V | undefined {\n    return this.keyToValue.get(key);\n  }\n\n  getByValue(value: V): K | undefined {\n    return this.valueToKey.get(value);\n  }\n\n  clear() {\n    this.keyToValue.clear();\n    this.valueToKey.clear();\n  }\n}\n", "import { DoubleIndexedKV } from './double-indexed-kv';\n\nexport class Registry<T> {\n  private kv = new DoubleIndexedKV<string, T>();\n\n  constructor(private readonly generateIdentifier: (v: T) => string) {}\n\n  register(value: T, identifier?: string): void {\n    if (this.kv.getByValue(value)) {\n      return;\n    }\n\n    if (!identifier) {\n      identifier = this.generateIdentifier(value);\n    }\n\n    this.kv.set(identifier, value);\n  }\n\n  clear(): void {\n    this.kv.clear();\n  }\n\n  getIdentifier(value: T) {\n    return this.kv.getByValue(value);\n  }\n\n  getValue(identifier: string) {\n    return this.kv.getByKey(identifier);\n  }\n}\n", "import { Registry } from './registry';\nimport { Class } from './types';\n\nexport interface RegisterOptions {\n  identifier?: string;\n  allowProps?: string[];\n}\n\nexport class ClassRegistry extends Registry<Class> {\n  constructor() {\n    super(c => c.name);\n  }\n\n  private classToAllowedProps = new Map<Class, string[]>();\n\n  register(value: Class, options?: string | RegisterOptions): void {\n    if (typeof options === 'object') {\n      if (options.allowProps) {\n        this.classToAllowedProps.set(value, options.allowProps);\n      }\n\n      super.register(value, options.identifier);\n    } else {\n      super.register(value, options);\n    }\n  }\n\n  getAllowedProps(value: Class): string[] | undefined {\n    return this.classToAllowedProps.get(value);\n  }\n}\n", "function valuesOfObj<T>(record: Record<string, T>): T[] {\n  if ('values' in Object) {\n    // eslint-disable-next-line es5/no-es6-methods\n    return Object.values(record);\n  }\n\n  const values: T[] = [];\n\n  // eslint-disable-next-line no-restricted-syntax\n  for (const key in record) {\n    if (record.hasOwnProperty(key)) {\n      values.push(record[key]);\n    }\n  }\n\n  return values;\n}\n\nexport function find<T>(\n  record: Record<string, T>,\n  predicate: (v: T) => boolean\n): T | undefined {\n  const values = valuesOfObj(record);\n  if ('find' in values) {\n    // eslint-disable-next-line es5/no-es6-methods\n    return values.find(predicate);\n  }\n\n  const valuesNotNever = values as T[];\n\n  for (let i = 0; i < valuesNotNever.length; i++) {\n    const value = valuesNotNever[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n\n  return undefined;\n}\n\nexport function forEach<T>(\n  record: Record<string, T>,\n  run: (v: T, key: string) => void\n) {\n  Object.entries(record).forEach(([key, value]) => run(value, key));\n}\n\nexport function includes<T>(arr: T[], value: T) {\n  return arr.indexOf(value) !== -1;\n}\n\nexport function findArr<T>(\n  record: T[],\n  predicate: (v: T) => boolean\n): T | undefined {\n  for (let i = 0; i < record.length; i++) {\n    const value = record[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n\n  return undefined;\n}\n", "import { JSONValue } from './types';\nimport { find } from './util';\n\nexport interface CustomTransfomer<I, O extends JSONValue> {\n  name: string;\n  isApplicable: (v: any) => v is I;\n  serialize: (v: I) => O;\n  deserialize: (v: O) => I;\n}\n\nexport class CustomTransformerRegistry {\n  private transfomers: Record<string, CustomTransfomer<any, any>> = {};\n\n  register<I, O extends JSONValue>(transformer: CustomTransfomer<I, O>) {\n    this.transfomers[transformer.name] = transformer;\n  }\n\n  findApplicable<T>(v: T) {\n    return find(this.transfomers, transformer =>\n      transformer.isApplicable(v)\n    ) as CustomTransfomer<T, JSONValue> | undefined;\n  }\n\n  findByName(name: string) {\n    return this.transfomers[name];\n  }\n}\n", "const getType = (payload: any): string =>\n  Object.prototype.toString.call(payload).slice(8, -1);\n\nexport const isUndefined = (payload: any): payload is undefined =>\n  typeof payload === 'undefined';\n\nexport const isNull = (payload: any): payload is null => payload === null;\n\nexport const isPlainObject = (\n  payload: any\n): payload is { [key: string]: any } => {\n  if (typeof payload !== 'object' || payload === null) return false;\n  if (payload === Object.prototype) return false;\n  if (Object.getPrototypeOf(payload) === null) return true;\n\n  return (\n    payload.constructor === Object &&\n    Object.getPrototypeOf(payload) === Object.prototype\n  );\n};\n\nexport const isEmptyObject = (payload: any): payload is {} =>\n  isPlainObject(payload) && Object.keys(payload).length === 0;\n\nexport const isArray = (payload: any): payload is any[] =>\n  Array.isArray(payload);\n\nexport const isString = (payload: any): payload is string =>\n  typeof payload === 'string';\n\nexport const isNumber = (payload: any): payload is number =>\n  typeof payload === 'number' && !isNaN(payload);\n\nexport const isBoolean = (payload: any): payload is boolean =>\n  typeof payload === 'boolean';\n\nexport const isRegExp = (payload: any): payload is RegExp =>\n  payload instanceof RegExp;\n\nexport const isMap = (payload: any): payload is Map<any, any> =>\n  payload instanceof Map;\n\nexport const isSet = (payload: any): payload is Set<any> =>\n  payload instanceof Set;\n\nexport const isSymbol = (payload: any): payload is symbol =>\n  getType(payload) === 'Symbol';\n\nexport const isDate = (payload: any): payload is Date =>\n  payload instanceof Date && !isNaN(payload.valueOf());\n\nexport const isError = (payload: any): payload is Error =>\n  payload instanceof Error;\n\nexport const isNaNValue = (payload: any): payload is typeof NaN =>\n  typeof payload === 'number' && isNaN(payload);\n\nexport const isPrimitive = (\n  payload: any\n): payload is boolean | null | undefined | number | string | symbol =>\n  isBoolean(payload) ||\n  isNull(payload) ||\n  isUndefined(payload) ||\n  isNumber(payload) ||\n  isString(payload) ||\n  isSymbol(payload);\n\nexport const isBigint = (payload: any): payload is bigint =>\n  typeof payload === 'bigint';\n\nexport const isInfinite = (payload: any): payload is number =>\n  payload === Infinity || payload === -Infinity;\n\nexport type TypedArrayConstructor =\n  | Int8ArrayConstructor\n  | Uint8ArrayConstructor\n  | Uint8ClampedArrayConstructor\n  | Int16ArrayConstructor\n  | Uint16ArrayConstructor\n  | Int32ArrayConstructor\n  | Uint32ArrayConstructor\n  | Float32ArrayConstructor\n  | Float64ArrayConstructor;\n\nexport type TypedArray = InstanceType<TypedArrayConstructor>;\n\nexport const isTypedArray = (payload: any): payload is TypedArray =>\n  ArrayBuffer.isView(payload) && !(payload instanceof DataView);\n\nexport const isURL = (payload: any): payload is URL => payload instanceof URL;\n", "export type StringifiedPath = string;\ntype Path = string[];\n\nexport const escapeKey = (key: string) => key.replace(/\\./g, '\\\\.');\n\nexport const stringifyPath = (path: Path): StringifiedPath =>\n  path\n    .map(String)\n    .map(escapeKey)\n    .join('.');\n\nexport const parsePath = (string: StringifiedPath) => {\n  const result: string[] = [];\n\n  let segment = '';\n  for (let i = 0; i < string.length; i++) {\n    let char = string.charAt(i);\n\n    const isEscapedDot = char === '\\\\' && string.charAt(i + 1) === '.';\n    if (isEscapedDot) {\n      segment += '.';\n      i++;\n      continue;\n    }\n\n    const isEndOfSegment = char === '.';\n    if (isEndOfSegment) {\n      result.push(segment);\n      segment = '';\n      continue;\n    }\n\n    segment += char;\n  }\n\n  const lastSegment = segment;\n  result.push(lastSegment);\n\n  return result;\n};\n", "import {\n  isBigint,\n  isDate,\n  isInfinite,\n  isMap,\n  isNaNValue,\n  isRegExp,\n  isSet,\n  isUndefined,\n  isSymbol,\n  isArray,\n  isError,\n  isTypedArray,\n  TypedArrayConstructor,\n  isURL,\n} from './is';\nimport { findArr } from './util';\nimport SuperJSON from '.';\n\nexport type PrimitiveTypeAnnotation = 'number' | 'undefined' | 'bigint';\n\ntype LeafTypeAnnotation =\n  | PrimitiveTypeAnnotation\n  | 'regexp'\n  | 'Date'\n  | 'Error'\n  | 'URL';\n\ntype TypedArrayAnnotation = ['typed-array', string];\ntype ClassTypeAnnotation = ['class', string];\ntype SymbolTypeAnnotation = ['symbol', string];\ntype CustomTypeAnnotation = ['custom', string];\n\ntype SimpleTypeAnnotation = LeafTypeAnnotation | 'map' | 'set';\n\ntype CompositeTypeAnnotation =\n  | TypedArrayAnnotation\n  | ClassTypeAnnotation\n  | SymbolTypeAnnotation\n  | CustomTypeAnnotation;\n\nexport type TypeAnnotation = SimpleTypeAnnotation | CompositeTypeAnnotation;\n\nfunction simpleTransformation<I, O, A extends SimpleTypeAnnotation>(\n  isApplicable: (v: any, superJson: SuperJSON) => v is I,\n  annotation: A,\n  transform: (v: I, superJson: SuperJSON) => O,\n  untransform: (v: O, superJson: SuperJSON) => I\n) {\n  return {\n    isApplicable,\n    annotation,\n    transform,\n    untransform,\n  };\n}\n\nconst simpleRules = [\n  simpleTransformation(\n    isUndefined,\n    'undefined',\n    () => null,\n    () => undefined\n  ),\n  simpleTransformation(\n    isBigint,\n    'bigint',\n    v => v.toString(),\n    v => {\n      if (typeof BigInt !== 'undefined') {\n        return BigInt(v);\n      }\n\n      console.error('Please add a BigInt polyfill.');\n\n      return v as any;\n    }\n  ),\n  simpleTransformation(\n    isDate,\n    'Date',\n    v => v.toISOString(),\n    v => new Date(v)\n  ),\n\n  simpleTransformation(\n    isError,\n    'Error',\n    (v, superJson) => {\n      const baseError: any = {\n        name: v.name,\n        message: v.message,\n      };\n\n      superJson.allowedErrorProps.forEach(prop => {\n        baseError[prop] = (v as any)[prop];\n      });\n\n      return baseError;\n    },\n    (v, superJson) => {\n      const e = new Error(v.message);\n      e.name = v.name;\n      e.stack = v.stack;\n\n      superJson.allowedErrorProps.forEach(prop => {\n        (e as any)[prop] = v[prop];\n      });\n\n      return e;\n    }\n  ),\n\n  simpleTransformation(\n    isRegExp,\n    'regexp',\n    v => '' + v,\n    regex => {\n      const body = regex.slice(1, regex.lastIndexOf('/'));\n      const flags = regex.slice(regex.lastIndexOf('/') + 1);\n      return new RegExp(body, flags);\n    }\n  ),\n\n  simpleTransformation(\n    isSet,\n    'set',\n    // (sets only exist in es6+)\n    // eslint-disable-next-line es5/no-es6-methods\n    v => [...v.values()],\n    v => new Set(v)\n  ),\n  simpleTransformation(\n    isMap,\n    'map',\n    v => [...v.entries()],\n    v => new Map(v)\n  ),\n\n  simpleTransformation<number, 'NaN' | 'Infinity' | '-Infinity', 'number'>(\n    (v): v is number => isNaNValue(v) || isInfinite(v),\n    'number',\n    v => {\n      if (isNaNValue(v)) {\n        return 'NaN';\n      }\n\n      if (v > 0) {\n        return 'Infinity';\n      } else {\n        return '-Infinity';\n      }\n    },\n    Number\n  ),\n\n  simpleTransformation<number, '-0', 'number'>(\n    (v): v is number => v === 0 && 1 / v === -Infinity,\n    'number',\n    () => {\n      return '-0';\n    },\n    Number\n  ),\n\n  simpleTransformation(\n    isURL,\n    'URL',\n    v => v.toString(),\n    v => new URL(v)\n  ),\n];\n\nfunction compositeTransformation<I, O, A extends CompositeTypeAnnotation>(\n  isApplicable: (v: any, superJson: SuperJSON) => v is I,\n  annotation: (v: I, superJson: SuperJSON) => A,\n  transform: (v: I, superJson: SuperJSON) => O,\n  untransform: (v: O, a: A, superJson: SuperJSON) => I\n) {\n  return {\n    isApplicable,\n    annotation,\n    transform,\n    untransform,\n  };\n}\n\nconst symbolRule = compositeTransformation(\n  (s, superJson): s is Symbol => {\n    if (isSymbol(s)) {\n      const isRegistered = !!superJson.symbolRegistry.getIdentifier(s);\n      return isRegistered;\n    }\n    return false;\n  },\n  (s, superJson) => {\n    const identifier = superJson.symbolRegistry.getIdentifier(s);\n    return ['symbol', identifier!];\n  },\n  v => v.description,\n  (_, a, superJson) => {\n    const value = superJson.symbolRegistry.getValue(a[1]);\n    if (!value) {\n      throw new Error('Trying to deserialize unknown symbol');\n    }\n    return value;\n  }\n);\n\nconst constructorToName = [\n  Int8Array,\n  Uint8Array,\n  Int16Array,\n  Uint16Array,\n  Int32Array,\n  Uint32Array,\n  Float32Array,\n  Float64Array,\n  Uint8ClampedArray,\n].reduce<Record<string, TypedArrayConstructor>>((obj, ctor) => {\n  obj[ctor.name] = ctor;\n  return obj;\n}, {});\n\nconst typedArrayRule = compositeTransformation(\n  isTypedArray,\n  v => ['typed-array', v.constructor.name],\n  v => [...v],\n  (v, a) => {\n    const ctor = constructorToName[a[1]];\n\n    if (!ctor) {\n      throw new Error('Trying to deserialize unknown typed array');\n    }\n\n    return new ctor(v);\n  }\n);\n\nexport function isInstanceOfRegisteredClass(\n  potentialClass: any,\n  superJson: SuperJSON\n): potentialClass is any {\n  if (potentialClass?.constructor) {\n    const isRegistered = !!superJson.classRegistry.getIdentifier(\n      potentialClass.constructor\n    );\n    return isRegistered;\n  }\n  return false;\n}\n\nconst classRule = compositeTransformation(\n  isInstanceOfRegisteredClass,\n  (clazz, superJson) => {\n    const identifier = superJson.classRegistry.getIdentifier(clazz.constructor);\n    return ['class', identifier!];\n  },\n  (clazz, superJson) => {\n    const allowedProps = superJson.classRegistry.getAllowedProps(\n      clazz.constructor\n    );\n    if (!allowedProps) {\n      return { ...clazz };\n    }\n\n    const result: any = {};\n    allowedProps.forEach(prop => {\n      result[prop] = clazz[prop];\n    });\n    return result;\n  },\n  (v, a, superJson) => {\n    const clazz = superJson.classRegistry.getValue(a[1]);\n\n    if (!clazz) {\n      throw new Error(\n        'Trying to deserialize unknown class - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564'\n      );\n    }\n\n    return Object.assign(Object.create(clazz.prototype), v);\n  }\n);\n\nconst customRule = compositeTransformation(\n  (value, superJson): value is any => {\n    return !!superJson.customTransformerRegistry.findApplicable(value);\n  },\n  (value, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findApplicable(\n      value\n    )!;\n    return ['custom', transformer.name];\n  },\n  (value, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findApplicable(\n      value\n    )!;\n    return transformer.serialize(value);\n  },\n  (v, a, superJson) => {\n    const transformer = superJson.customTransformerRegistry.findByName(a[1]);\n    if (!transformer) {\n      throw new Error('Trying to deserialize unknown custom value');\n    }\n    return transformer.deserialize(v);\n  }\n);\n\nconst compositeRules = [classRule, symbolRule, customRule, typedArrayRule];\n\nexport const transformValue = (\n  value: any,\n  superJson: SuperJSON\n): { value: any; type: TypeAnnotation } | undefined => {\n  const applicableCompositeRule = findArr(compositeRules, rule =>\n    rule.isApplicable(value, superJson)\n  );\n  if (applicableCompositeRule) {\n    return {\n      value: applicableCompositeRule.transform(value as never, superJson),\n      type: applicableCompositeRule.annotation(value, superJson),\n    };\n  }\n\n  const applicableSimpleRule = findArr(simpleRules, rule =>\n    rule.isApplicable(value, superJson)\n  );\n\n  if (applicableSimpleRule) {\n    return {\n      value: applicableSimpleRule.transform(value as never, superJson),\n      type: applicableSimpleRule.annotation,\n    };\n  }\n\n  return undefined;\n};\n\nconst simpleRulesByAnnotation: Record<string, typeof simpleRules[0]> = {};\nsimpleRules.forEach(rule => {\n  simpleRulesByAnnotation[rule.annotation] = rule;\n});\n\nexport const untransformValue = (\n  json: any,\n  type: TypeAnnotation,\n  superJson: SuperJSON\n) => {\n  if (isArray(type)) {\n    switch (type[0]) {\n      case 'symbol':\n        return symbolRule.untransform(json, type, superJson);\n      case 'class':\n        return classRule.untransform(json, type, superJson);\n      case 'custom':\n        return customRule.untransform(json, type, superJson);\n      case 'typed-array':\n        return typedArrayRule.untransform(json, type, superJson);\n      default:\n        throw new Error('Unknown transformation: ' + type);\n    }\n  } else {\n    const transformation = simpleRulesByAnnotation[type];\n    if (!transformation) {\n      throw new Error('Unknown transformation: ' + type);\n    }\n\n    return transformation.untransform(json as never, superJson);\n  }\n};\n", "import { isMap, isArray, isPlainObject, isSet } from './is';\nimport { includes } from './util';\n\nconst getNthKey = (value: Map<any, any> | Set<any>, n: number): any => {\n  const keys = value.keys();\n  while (n > 0) {\n    keys.next();\n    n--;\n  }\n\n  return keys.next().value;\n};\n\nfunction validatePath(path: (string | number)[]) {\n  if (includes(path, '__proto__')) {\n    throw new Error('__proto__ is not allowed as a property');\n  }\n  if (includes(path, 'prototype')) {\n    throw new Error('prototype is not allowed as a property');\n  }\n  if (includes(path, 'constructor')) {\n    throw new Error('constructor is not allowed as a property');\n  }\n}\n\nexport const getDeep = (object: object, path: (string | number)[]): object => {\n  validatePath(path);\n\n  for (let i = 0; i < path.length; i++) {\n    const key = path[i];\n    if (isSet(object)) {\n      object = getNthKey(object, +key);\n    } else if (isMap(object)) {\n      const row = +key;\n      const type = +path[++i] === 0 ? 'key' : 'value';\n\n      const keyOfRow = getNthKey(object, row);\n      switch (type) {\n        case 'key':\n          object = keyOfRow;\n          break;\n        case 'value':\n          object = object.get(keyOfRow);\n          break;\n      }\n    } else {\n      object = (object as any)[key];\n    }\n  }\n\n  return object;\n};\n\nexport const setDeep = (\n  object: any,\n  path: (string | number)[],\n  mapper: (v: any) => any\n): any => {\n  validatePath(path);\n\n  if (path.length === 0) {\n    return mapper(object);\n  }\n\n  let parent = object;\n\n  for (let i = 0; i < path.length - 1; i++) {\n    const key = path[i];\n\n    if (isArray(parent)) {\n      const index = +key;\n      parent = parent[index];\n    } else if (isPlainObject(parent)) {\n      parent = parent[key];\n    } else if (isSet(parent)) {\n      const row = +key;\n      parent = getNthKey(parent, row);\n    } else if (isMap(parent)) {\n      const isEnd = i === path.length - 2;\n      if (isEnd) {\n        break;\n      }\n\n      const row = +key;\n      const type = +path[++i] === 0 ? 'key' : 'value';\n\n      const keyOfRow = getNthKey(parent, row);\n      switch (type) {\n        case 'key':\n          parent = keyOfRow;\n          break;\n        case 'value':\n          parent = parent.get(keyOfRow);\n          break;\n      }\n    }\n  }\n\n  const lastKey = path[path.length - 1];\n\n  if (isArray(parent)) {\n    parent[+lastKey] = mapper(parent[+lastKey]);\n  } else if (isPlainObject(parent)) {\n    parent[lastKey] = mapper(parent[lastKey]);\n  }\n\n  if (isSet(parent)) {\n    const oldValue = getNthKey(parent, +lastKey);\n    const newValue = mapper(oldValue);\n    if (oldValue !== newValue) {\n      parent.delete(oldValue);\n      parent.add(newValue);\n    }\n  }\n\n  if (isMap(parent)) {\n    const row = +path[path.length - 2];\n    const keyToRow = getNthKey(parent, row);\n\n    const type = +lastKey === 0 ? 'key' : 'value';\n    switch (type) {\n      case 'key': {\n        const newKey = mapper(keyToRow);\n        parent.set(newKey, parent.get(keyToRow));\n\n        if (newKey !== keyToRow) {\n          parent.delete(keyToRow);\n        }\n        break;\n      }\n\n      case 'value': {\n        parent.set(keyToRow, mapper(parent.get(keyToRow)));\n        break;\n      }\n    }\n  }\n\n  return object;\n};\n", "import {\n  isArray,\n  isEmptyObject,\n  isMap,\n  isPlainObject,\n  isPrimitive,\n  isSet,\n} from './is';\nimport { escapeKey, stringifyPath } from './pathstringifier';\nimport {\n  isInstanceOfRegisteredClass,\n  transformValue,\n  TypeAnnotation,\n  untransformValue,\n} from './transformer';\nimport { includes, forEach } from './util';\nimport { parsePath } from './pathstringifier';\nimport { getDeep, setDeep } from './accessDeep';\nimport SuperJSON from '.';\n\ntype Tree<T> = InnerNode<T> | Leaf<T>;\ntype Leaf<T> = [T];\ntype InnerNode<T> = [T, Record<string, Tree<T>>];\n\nexport type MinimisedTree<T> = Tree<T> | Record<string, Tree<T>> | undefined;\n\nfunction traverse<T>(\n  tree: MinimisedTree<T>,\n  walker: (v: T, path: string[]) => void,\n  origin: string[] = []\n): void {\n  if (!tree) {\n    return;\n  }\n\n  if (!isArray(tree)) {\n    forEach(tree, (subtree, key) =>\n      traverse(subtree, walker, [...origin, ...parsePath(key)])\n    );\n    return;\n  }\n\n  const [nodeValue, children] = tree;\n  if (children) {\n    forEach(children, (child, key) => {\n      traverse(child, walker, [...origin, ...parsePath(key)]);\n    });\n  }\n\n  walker(nodeValue, origin);\n}\n\nexport function applyValueAnnotations(\n  plain: any,\n  annotations: MinimisedTree<TypeAnnotation>,\n  superJson: SuperJSON\n) {\n  traverse(annotations, (type, path) => {\n    plain = setDeep(plain, path, v => untransformValue(v, type, superJson));\n  });\n\n  return plain;\n}\n\nexport function applyReferentialEqualityAnnotations(\n  plain: any,\n  annotations: ReferentialEqualityAnnotations\n) {\n  function apply(identicalPaths: string[], path: string) {\n    const object = getDeep(plain, parsePath(path));\n\n    identicalPaths.map(parsePath).forEach(identicalObjectPath => {\n      plain = setDeep(plain, identicalObjectPath, () => object);\n    });\n  }\n\n  if (isArray(annotations)) {\n    const [root, other] = annotations;\n    root.forEach(identicalPath => {\n      plain = setDeep(plain, parsePath(identicalPath), () => plain);\n    });\n\n    if (other) {\n      forEach(other, apply);\n    }\n  } else {\n    forEach(annotations, apply);\n  }\n\n  return plain;\n}\n\nconst isDeep = (object: any, superJson: SuperJSON): boolean =>\n  isPlainObject(object) ||\n  isArray(object) ||\n  isMap(object) ||\n  isSet(object) ||\n  isInstanceOfRegisteredClass(object, superJson);\n\nfunction addIdentity(object: any, path: any[], identities: Map<any, any[][]>) {\n  const existingSet = identities.get(object);\n\n  if (existingSet) {\n    existingSet.push(path);\n  } else {\n    identities.set(object, [path]);\n  }\n}\n\ninterface Result {\n  transformedValue: any;\n  annotations?: MinimisedTree<TypeAnnotation>;\n}\n\nexport type ReferentialEqualityAnnotations =\n  | Record<string, string[]>\n  | [string[]]\n  | [string[], Record<string, string[]>];\n\nexport function generateReferentialEqualityAnnotations(\n  identitites: Map<any, any[][]>\n): ReferentialEqualityAnnotations | undefined {\n  const result: Record<string, string[]> = {};\n  let rootEqualityPaths: string[] | undefined = undefined;\n\n  identitites.forEach(paths => {\n    if (paths.length <= 1) {\n      return;\n    }\n\n    const [shortestPath, ...identicalPaths] = paths\n      .map(path => path.map(String))\n      .sort((a, b) => a.length - b.length);\n\n    if (shortestPath.length === 0) {\n      rootEqualityPaths = identicalPaths.map(stringifyPath);\n    } else {\n      result[stringifyPath(shortestPath)] = identicalPaths.map(stringifyPath);\n    }\n  });\n\n  if (rootEqualityPaths) {\n    if (isEmptyObject(result)) {\n      return [rootEqualityPaths];\n    } else {\n      return [rootEqualityPaths, result];\n    }\n  } else {\n    return isEmptyObject(result) ? undefined : result;\n  }\n}\n\nexport const walker = (\n  object: any,\n  identities: Map<any, any[][]>,\n  superJson: SuperJSON,\n  path: any[] = [],\n  objectsInThisPath: any[] = []\n): Result => {\n  if (!isPrimitive(object)) {\n    addIdentity(object, path, identities);\n  }\n\n  if (!isDeep(object, superJson)) {\n    const transformed = transformValue(object, superJson);\n    if (transformed) {\n      return {\n        transformedValue: transformed.value,\n        annotations: [transformed.type],\n      };\n    } else {\n      return {\n        transformedValue: object,\n      };\n    }\n  }\n\n  if (includes(objectsInThisPath, object)) {\n    return {\n      transformedValue: null,\n    };\n  }\n\n  const transformationResult = transformValue(object, superJson);\n  const transformed = transformationResult?.value ?? object;\n\n  if (!isPrimitive(object)) {\n    objectsInThisPath = [...objectsInThisPath, object];\n  }\n\n  const transformedValue: any = isArray(transformed) ? [] : {};\n  const innerAnnotations: Record<string, Tree<TypeAnnotation>> = {};\n\n  forEach(transformed, (value, index) => {\n    const recursiveResult = walker(\n      value,\n      identities,\n      superJson,\n      [...path, index],\n      objectsInThisPath\n    );\n\n    transformedValue[index] = recursiveResult.transformedValue;\n\n    if (isArray(recursiveResult.annotations)) {\n      innerAnnotations[index] = recursiveResult.annotations;\n    } else if (isPlainObject(recursiveResult.annotations)) {\n      forEach(recursiveResult.annotations, (tree, key) => {\n        innerAnnotations[escapeKey(index) + '.' + key] = tree;\n      });\n    }\n  });\n\n  if (isEmptyObject(innerAnnotations)) {\n    return {\n      transformedValue,\n      annotations: !!transformationResult\n        ? [transformationResult.type]\n        : undefined,\n    };\n  } else {\n    return {\n      transformedValue,\n      annotations: !!transformationResult\n        ? [transformationResult.type, innerAnnotations]\n        : innerAnnotations,\n    };\n  }\n};\n", "function getType(payload) {\n  return Object.prototype.toString.call(payload).slice(8, -1);\n}\nfunction isUndefined(payload) {\n  return getType(payload) === \"Undefined\";\n}\nfunction isNull(payload) {\n  return getType(payload) === \"Null\";\n}\nfunction isPlainObject(payload) {\n  if (getType(payload) !== \"Object\")\n    return false;\n  const prototype = Object.getPrototypeOf(payload);\n  return !!prototype && prototype.constructor === Object && prototype === Object.prototype;\n}\nfunction isObject(payload) {\n  return isPlainObject(payload);\n}\nfunction isEmptyObject(payload) {\n  return isPlainObject(payload) && Object.keys(payload).length === 0;\n}\nfunction isFullObject(payload) {\n  return isPlainObject(payload) && Object.keys(payload).length > 0;\n}\nfunction isAnyObject(payload) {\n  return getType(payload) === \"Object\";\n}\nfunction isObjectLike(payload) {\n  return isAnyObject(payload);\n}\nfunction isFunction(payload) {\n  return typeof payload === \"function\";\n}\nfunction isArray(payload) {\n  return getType(payload) === \"Array\";\n}\nfunction isFullArray(payload) {\n  return isArray(payload) && payload.length > 0;\n}\nfunction isEmptyArray(payload) {\n  return isArray(payload) && payload.length === 0;\n}\nfunction isString(payload) {\n  return getType(payload) === \"String\";\n}\nfunction isFullString(payload) {\n  return isString(payload) && payload !== \"\";\n}\nfunction isEmptyString(payload) {\n  return payload === \"\";\n}\nfunction isNumber(payload) {\n  return getType(payload) === \"Number\" && !isNaN(payload);\n}\nfunction isPositiveNumber(payload) {\n  return isNumber(payload) && payload > 0;\n}\nfunction isNegativeNumber(payload) {\n  return isNumber(payload) && payload < 0;\n}\nfunction isBoolean(payload) {\n  return getType(payload) === \"Boolean\";\n}\nfunction isRegExp(payload) {\n  return getType(payload) === \"RegExp\";\n}\nfunction isMap(payload) {\n  return getType(payload) === \"Map\";\n}\nfunction isWeakMap(payload) {\n  return getType(payload) === \"WeakMap\";\n}\nfunction isSet(payload) {\n  return getType(payload) === \"Set\";\n}\nfunction isWeakSet(payload) {\n  return getType(payload) === \"WeakSet\";\n}\nfunction isSymbol(payload) {\n  return getType(payload) === \"Symbol\";\n}\nfunction isDate(payload) {\n  return getType(payload) === \"Date\" && !isNaN(payload);\n}\nfunction isBlob(payload) {\n  return getType(payload) === \"Blob\";\n}\nfunction isFile(payload) {\n  return getType(payload) === \"File\";\n}\nfunction isPromise(payload) {\n  return getType(payload) === \"Promise\";\n}\nfunction isError(payload) {\n  return getType(payload) === \"Error\";\n}\nfunction isNaNValue(payload) {\n  return getType(payload) === \"Number\" && isNaN(payload);\n}\nfunction isPrimitive(payload) {\n  return isBoolean(payload) || isNull(payload) || isUndefined(payload) || isNumber(payload) || isString(payload) || isSymbol(payload);\n}\nconst isNullOrUndefined = isOneOf(isNull, isUndefined);\nfunction isOneOf(a, b, c, d, e) {\n  return (value) => a(value) || b(value) || !!c && c(value) || !!d && d(value) || !!e && e(value);\n}\nfunction isType(payload, type) {\n  if (!(type instanceof Function)) {\n    throw new TypeError(\"Type must be a function\");\n  }\n  if (!Object.prototype.hasOwnProperty.call(type, \"prototype\")) {\n    throw new TypeError(\"Type is not a class\");\n  }\n  const name = type.name;\n  return getType(payload) === name || Boolean(payload && payload.constructor === type);\n}\nfunction isInstanceOf(value, classOrClassName) {\n  if (typeof classOrClassName === \"function\") {\n    for (let p = value; p; p = Object.getPrototypeOf(p)) {\n      if (isType(p, classOrClassName)) {\n        return true;\n      }\n    }\n    return false;\n  } else {\n    for (let p = value; p; p = Object.getPrototypeOf(p)) {\n      if (getType(p) === classOrClassName) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nexport { getType, isAnyObject, isArray, isBlob, isBoolean, isDate, isEmptyArray, isEmptyObject, isEmptyString, isError, isFile, isFullArray, isFullObject, isFullString, isFunction, isInstanceOf, isMap, isNaNValue, isNegativeNumber, isNull, isNullOrUndefined, isNumber, isObject, isObjectLike, isOneOf, isPlainObject, isPositiveNumber, isPrimitive, isPromise, isRegExp, isSet, isString, isSymbol, isType, isUndefined, isWeakMap, isWeakSet };\n", "import { isArray, isPlainObject } from 'is-what';\n\nfunction assignProp(carry, key, newVal, originalObject, includeNonenumerable) {\n  const propType = {}.propertyIsEnumerable.call(originalObject, key) ? \"enumerable\" : \"nonenumerable\";\n  if (propType === \"enumerable\")\n    carry[key] = newVal;\n  if (includeNonenumerable && propType === \"nonenumerable\") {\n    Object.defineProperty(carry, key, {\n      value: newVal,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    });\n  }\n}\nfunction copy(target, options = {}) {\n  if (isArray(target)) {\n    return target.map((item) => copy(item, options));\n  }\n  if (!isPlainObject(target)) {\n    return target;\n  }\n  const props = Object.getOwnPropertyNames(target);\n  const symbols = Object.getOwnPropertySymbols(target);\n  return [...props, ...symbols].reduce((carry, key) => {\n    if (isArray(options.props) && !options.props.includes(key)) {\n      return carry;\n    }\n    const val = target[key];\n    const newVal = copy(val, options);\n    assignProp(carry, key, newVal, target, options.nonenumerable);\n    return carry;\n  }, {});\n}\n\nexport { copy };\n", "import { SuperJSONResult, SuperJSONValue, Class, JSONValue } from './types';\nimport { ClassRegistry, RegisterOptions } from './class-registry';\nimport { Registry } from './registry';\nimport {\n  CustomTransfomer,\n  CustomTransformerRegistry,\n} from './custom-transformer-registry';\nimport {\n  walker,\n  applyReferentialEqualityAnnotations,\n  applyValueAnnotations,\n  generateReferentialEqualityAnnotations,\n} from './plainer';\nimport { copy } from 'copy-anything';\n\nexport default class SuperJSON {\n  serialize(object: SuperJSONValue): SuperJSONResult {\n    const identities = new Map<any, any[][]>();\n    const output = walker(object, identities, this);\n    const res: SuperJSONResult = {\n      json: output.transformedValue,\n    };\n\n    if (output.annotations) {\n      res.meta = {\n        ...res.meta,\n        values: output.annotations,\n      };\n    }\n\n    const equalityAnnotations = generateReferentialEqualityAnnotations(\n      identities\n    );\n    if (equalityAnnotations) {\n      res.meta = {\n        ...res.meta,\n        referentialEqualities: equalityAnnotations,\n      };\n    }\n\n    return res;\n  }\n\n  deserialize<T = unknown>(payload: SuperJSONResult): T {\n    const { json, meta } = payload;\n\n    let result: T = copy(json) as any;\n\n    if (meta?.values) {\n      result = applyValueAnnotations(result, meta.values, this);\n    }\n\n    if (meta?.referentialEqualities) {\n      result = applyReferentialEqualityAnnotations(\n        result,\n        meta.referentialEqualities\n      );\n    }\n\n    return result;\n  }\n\n  stringify(object: SuperJSONValue): string {\n    return JSON.stringify(this.serialize(object));\n  }\n\n  parse<T = unknown>(string: string): T {\n    return this.deserialize(JSON.parse(string));\n  }\n\n  readonly classRegistry = new ClassRegistry();\n  registerClass(v: Class, options?: RegisterOptions | string) {\n    this.classRegistry.register(v, options);\n  }\n\n  readonly symbolRegistry = new Registry<Symbol>(s => s.description ?? '');\n  registerSymbol(v: Symbol, identifier?: string) {\n    this.symbolRegistry.register(v, identifier);\n  }\n\n  readonly customTransformerRegistry = new CustomTransformerRegistry();\n  registerCustom<I, O extends JSONValue>(\n    transformer: Omit<CustomTransfomer<I, O>, 'name'>,\n    name: string\n  ) {\n    this.customTransformerRegistry.register({\n      name,\n      ...transformer,\n    });\n  }\n\n  readonly allowedErrorProps: string[] = [];\n  allowErrorProps(...props: string[]) {\n    this.allowedErrorProps.push(...props);\n  }\n\n  private static defaultInstance = new SuperJSON();\n  static serialize = SuperJSON.defaultInstance.serialize.bind(\n    SuperJSON.defaultInstance\n  );\n  static deserialize = SuperJSON.defaultInstance.deserialize.bind(\n    SuperJSON.defaultInstance\n  );\n  static stringify = SuperJSON.defaultInstance.stringify.bind(\n    SuperJSON.defaultInstance\n  );\n  static parse = SuperJSON.defaultInstance.parse.bind(\n    SuperJSON.defaultInstance\n  );\n  static registerClass = SuperJSON.defaultInstance.registerClass.bind(\n    SuperJSON.defaultInstance\n  );\n  static registerSymbol = SuperJSON.defaultInstance.registerSymbol.bind(\n    SuperJSON.defaultInstance\n  );\n  static registerCustom = SuperJSON.defaultInstance.registerCustom.bind(\n    SuperJSON.defaultInstance\n  );\n  static allowErrorProps = SuperJSON.defaultInstance.allowErrorProps.bind(\n    SuperJSON.defaultInstance\n  );\n}\n\nexport const serialize = SuperJSON.serialize;\nexport const deserialize = SuperJSON.deserialize;\n\nexport const stringify = SuperJSON.stringify;\nexport const parse = SuperJSON.parse;\n\nexport const registerClass = SuperJSON.registerClass;\nexport const registerCustom = SuperJSON.registerCustom;\nexport const registerSymbol = SuperJSON.registerSymbol;\nexport const allowErrorProps = SuperJSON.allowErrorProps;\n", "'use client'\nimport * as React from 'react'\n\nexport const defaultTheme = {\n  background: '#0b1521',\n  backgroundAlt: '#132337',\n  foreground: 'white',\n  gray: '#3f4e60',\n  grayAlt: '#222e3e',\n  inputBackgroundColor: '#fff',\n  inputTextColor: '#000',\n  success: '#00ab52',\n  danger: '#ff0085',\n  active: '#006bff',\n  paused: '#8c49eb',\n  warning: '#ffb200',\n} as const\n\nexport type Theme = typeof defaultTheme\ninterface ProviderProps {\n  theme: Theme\n  children?: React.ReactNode\n}\n\nconst ThemeContext = React.createContext(defaultTheme)\n\nexport function ThemeProvider({ theme, ...rest }: ProviderProps) {\n  return <ThemeContext.Provider value={theme} {...rest} />\n}\n\nexport function useTheme() {\n  return React.useContext(ThemeContext)\n}\n", "import * as React from 'react'\n\nexport default function useMediaQuery(query: string): boolean | undefined {\n  // Keep track of the preference in state, start with the current match\n  const [isMatch, setIsMatch] = React.useState(() => {\n    if (typeof window !== 'undefined') {\n      return window.matchMedia(query).matches\n    }\n    return\n  })\n\n  // Watch for changes\n  React.useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Create a matcher\n      const matcher = window.matchMedia(query)\n\n      // Create our handler\n      const onChange = ({ matches }: { matches: boolean }) =>\n        setIsMatch(matches)\n\n      // Listen for changes\n      matcher.addListener(onChange)\n\n      return () => {\n        // Stop listening for changes\n        matcher.removeListener(onChange)\n      }\n    }\n    return\n  }, [isMatch, query, setIsMatch])\n\n  return isMatch\n}\n", "import * as React from 'react'\nimport type { Query } from '@tanstack/react-query'\nimport SuperJSON from 'superjson'\n\nimport type { Theme } from './theme'\nimport { useTheme } from './theme'\nimport useMediaQuery from './useMediaQuery'\n\ntype StyledComponent<T> = T extends 'button'\n  ? React.DetailedHTMLProps<\n      React.ButtonHTMLAttributes<HTMLButtonElement>,\n      HTMLButtonElement\n    >\n  : T extends 'input'\n  ? React.DetailedHTMLProps<\n      React.InputHTMLAttributes<HTMLInputElement>,\n      HTMLInputElement\n    >\n  : T extends 'select'\n  ? React.DetailedHTMLProps<\n      React.SelectHTMLAttributes<HTMLSelectElement>,\n      HTMLSelectElement\n    >\n  : T extends keyof HTMLElementTagNameMap\n  ? React.HTMLAttributes<HTMLElementTagNameMap[T]>\n  : never\n\nexport function getQueryStatusColor({\n  queryState,\n  observerCount,\n  isStale,\n  theme,\n}: {\n  queryState: Query['state']\n  observerCount: number\n  isStale: boolean\n  theme: Theme\n}) {\n  return queryState.fetchStatus === 'fetching'\n    ? theme.active\n    : !observerCount\n    ? theme.gray\n    : queryState.fetchStatus === 'paused'\n    ? theme.paused\n    : isStale\n    ? theme.warning\n    : theme.success\n}\n\nexport function getQueryStatusLabel(query: Query) {\n  return query.state.fetchStatus === 'fetching'\n    ? 'fetching'\n    : !query.getObserversCount()\n    ? 'inactive'\n    : query.state.fetchStatus === 'paused'\n    ? 'paused'\n    : query.isStale()\n    ? 'stale'\n    : 'fresh'\n}\n\ntype Styles =\n  | React.CSSProperties\n  | ((props: Record<string, any>, theme: Theme) => React.CSSProperties)\n\nexport function styled<T extends keyof HTMLElementTagNameMap>(\n  type: T,\n  newStyles: Styles,\n  queries: Record<string, Styles> = {},\n) {\n  return React.forwardRef<HTMLElementTagNameMap[T], StyledComponent<T>>(\n    ({ style, ...rest }, ref) => {\n      const theme = useTheme()\n\n      const mediaStyles = Object.entries(queries).reduce(\n        (current, [key, value]) => {\n          // eslint-disable-next-line react-hooks/rules-of-hooks\n          return useMediaQuery(key)\n            ? {\n                ...current,\n                ...(typeof value === 'function' ? value(rest, theme) : value),\n              }\n            : current\n        },\n        {},\n      )\n\n      return React.createElement(type, {\n        ...rest,\n        style: {\n          ...(typeof newStyles === 'function'\n            ? newStyles(rest, theme)\n            : newStyles),\n          ...style,\n          ...mediaStyles,\n        },\n        ref,\n      })\n    },\n  )\n}\n\nexport function useIsMounted() {\n  const mountedRef = React.useRef(false)\n  const isMounted = React.useCallback(() => mountedRef.current, [])\n\n  React.useEffect(() => {\n    mountedRef.current = true\n    return () => {\n      mountedRef.current = false\n    }\n  }, [])\n\n  return isMounted\n}\n\n/**\n * Displays a string regardless the type of the data\n * @param {unknown} value Value to be stringified\n * @param {boolean} beautify Formats json to multiline\n */\nexport const displayValue = (value: unknown, beautify: boolean = false) => {\n  const { json } = SuperJSON.serialize(value)\n\n  return JSON.stringify(json, null, beautify ? 2 : undefined)\n}\n\n// Sorting functions\ntype SortFn = (a: Query, b: Query) => number\n\nconst getStatusRank = (q: Query) =>\n  q.state.fetchStatus !== 'idle'\n    ? 0\n    : !q.getObserversCount()\n    ? 3\n    : q.isStale()\n    ? 2\n    : 1\n\nconst queryHashSort: SortFn = (a, b) => a.queryHash.localeCompare(b.queryHash)\n\nconst dateSort: SortFn = (a, b) =>\n  a.state.dataUpdatedAt < b.state.dataUpdatedAt ? 1 : -1\n\nconst statusAndDateSort: SortFn = (a, b) => {\n  if (getStatusRank(a) === getStatusRank(b)) {\n    return dateSort(a, b)\n  }\n\n  return getStatusRank(a) > getStatusRank(b) ? 1 : -1\n}\n\nexport const sortFns: Record<string, SortFn> = {\n  'Status > Last Updated': statusAndDateSort,\n  'Query Hash': queryHashSort,\n  'Last Updated': dateSort,\n}\n\nexport const minPanelSize = 70\nexport const defaultPanelSize = 500\nexport const sides: Record<Side, Side> = {\n  top: 'bottom',\n  bottom: 'top',\n  left: 'right',\n  right: 'left',\n}\n\nexport type Corner = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\nexport type Side = 'left' | 'right' | 'top' | 'bottom'\n/**\n * Check if the given side is vertical (left/right)\n */\nexport function isVerticalSide(side: Side) {\n  return ['left', 'right'].includes(side)\n}\n/**\n * Get the opposite side, eg 'left' => 'right'. 'top' => 'bottom', etc\n */\nexport function getOppositeSide(side: Side): Side {\n  return sides[side]\n}\n/**\n * Given as css prop it will return a sided css prop based on a given side\n * Example given `border` and `right` it return `borderRight`\n */\nexport function getSidedProp<T extends string>(prop: T, side: Side) {\n  return `${prop}${\n    side.charAt(0).toUpperCase() + side.slice(1)\n  }` as `${T}${Capitalize<Side>}`\n}\n\nexport interface SidePanelStyleOptions {\n  /**\n   * Position of the panel\n   * Defaults to 'bottom'\n   */\n  position?: Side\n  /**\n   * Staring height for the panel, it is set if the position is horizontal eg 'top' or 'bottom'\n   * Defaults to 500\n   */\n  height?: number\n  /**\n   * Staring width for the panel, it is set if the position is vertical eg 'left' or 'right'\n   * Defaults to 500\n   */\n  width?: number\n  /**\n   * RQ devtools theme\n   */\n  devtoolsTheme: Theme\n  /**\n   * Sets the correct transition and visibility styles\n   */\n  isOpen?: boolean\n  /**\n   * If the panel is resizing set to true to apply the correct transition styles\n   */\n  isResizing?: boolean\n  /**\n   * Extra panel style passed by the user\n   */\n  panelStyle?: React.CSSProperties\n}\n\nexport function getSidePanelStyle({\n  position = 'bottom',\n  height,\n  width,\n  devtoolsTheme,\n  isOpen,\n  isResizing,\n  panelStyle,\n}: SidePanelStyleOptions): React.CSSProperties {\n  const oppositeSide = getOppositeSide(position)\n  const borderSide = getSidedProp('border', oppositeSide)\n  const isVertical = isVerticalSide(position)\n\n  return {\n    ...panelStyle,\n    direction: 'ltr',\n    position: 'fixed',\n    [position]: 0,\n    [borderSide]: `1px solid ${devtoolsTheme.gray}`,\n    transformOrigin: oppositeSide,\n    boxShadow: '0 0 20px rgba(0,0,0,.3)',\n    zIndex: 99999,\n    // visibility will be toggled after transitions, but set initial state here\n    visibility: isOpen ? 'visible' : 'hidden',\n    ...(isResizing\n      ? {\n          transition: `none`,\n        }\n      : { transition: `all .2s ease` }),\n    ...(isOpen\n      ? {\n          opacity: 1,\n          pointerEvents: 'all',\n          transform: isVertical\n            ? `translateX(0) scale(1)`\n            : `translateY(0) scale(1)`,\n        }\n      : {\n          opacity: 0,\n          pointerEvents: 'none',\n          transform: isVertical\n            ? `translateX(15px) scale(1.02)`\n            : `translateY(15px) scale(1.02)`,\n        }),\n    ...(isVertical\n      ? {\n          top: 0,\n          height: '100vh',\n          maxWidth: '90%',\n          width:\n            typeof width === 'number' && width >= minPanelSize\n              ? width\n              : defaultPanelSize,\n        }\n      : {\n          left: 0,\n          width: '100%',\n          maxHeight: '90%',\n          height:\n            typeof height === 'number' && height >= minPanelSize\n              ? height\n              : defaultPanelSize,\n        }),\n  }\n}\n\n/**\n * Get resize handle style based on a given side\n */\nexport function getResizeHandleStyle(\n  position: Side = 'bottom',\n): React.CSSProperties {\n  const isVertical = isVerticalSide(position)\n  const oppositeSide = getOppositeSide(position)\n  const marginSide = getSidedProp('margin', oppositeSide)\n\n  return {\n    position: 'absolute',\n    cursor: isVertical ? 'col-resize' : 'row-resize',\n    zIndex: 100000,\n    [oppositeSide]: 0,\n    [marginSide]: `-4px`,\n    ...(isVertical\n      ? {\n          top: 0,\n          height: '100%',\n          width: '4px',\n        }\n      : {\n          width: '100%',\n          height: '4px',\n        }),\n  }\n}\n", "import { styled } from './utils'\n\nexport const Panel = styled(\n  'div',\n  (_props, theme) => ({\n    fontSize: 'clamp(12px, 1.5vw, 14px)',\n    fontFamily: `sans-serif`,\n    display: 'flex',\n    backgroundColor: theme.background,\n    color: theme.foreground,\n  }),\n  {\n    '(max-width: 700px)': {\n      flexDirection: 'column',\n    },\n    '(max-width: 600px)': {\n      fontSize: '.9em',\n      // flexDirection: 'column',\n    },\n  },\n)\n\nexport const ActiveQueryPanel = styled(\n  'div',\n  () => ({\n    flex: '1 1 500px',\n    display: 'flex',\n    flexDirection: 'column',\n    overflow: 'auto',\n    height: '100%',\n  }),\n  {\n    '(max-width: 700px)': (_props, theme) => ({\n      borderTop: `2px solid ${theme.gray}`,\n    }),\n  },\n)\n\nexport const Button = styled('button', (props, theme) => ({\n  appearance: 'none',\n  fontSize: '.9em',\n  fontWeight: 'bold',\n  background: theme.gray,\n  border: '0',\n  borderRadius: '.3em',\n  color: 'white',\n  padding: '.5em',\n  opacity: props.disabled ? '.5' : undefined,\n  cursor: 'pointer',\n}))\n\nexport const QueryKeys = styled('span', {\n  display: 'flex',\n  flexWrap: 'wrap',\n  gap: '0.5em',\n  fontSize: '0.9em',\n})\n\nexport const QueryKey = styled('span', {\n  display: 'inline-flex',\n  alignItems: 'center',\n  padding: '.2em .4em',\n  fontWeight: 'bold',\n  textShadow: '0 0 10px black',\n  borderRadius: '.2em',\n})\n\nexport const Code = styled('code', {\n  fontSize: '.9em',\n  color: 'inherit',\n  background: 'inherit',\n})\n\nexport const Input = styled('input', (_props, theme) => ({\n  backgroundColor: theme.inputBackgroundColor,\n  border: 0,\n  borderRadius: '.2em',\n  color: theme.inputTextColor,\n  fontSize: '.9em',\n  lineHeight: `1.3`,\n  padding: '.3em .4em',\n}))\n\nexport const Select = styled(\n  'select',\n  (_props, theme) => ({\n    display: `inline-block`,\n    fontSize: `.9em`,\n    fontFamily: `sans-serif`,\n    fontWeight: 'normal',\n    lineHeight: `1.3`,\n    padding: `.3em 1.5em .3em .5em`,\n    height: 'auto',\n    border: 0,\n    borderRadius: `.2em`,\n    appearance: `none`,\n    WebkitAppearance: 'none',\n    backgroundColor: theme.inputBackgroundColor,\n    backgroundImage: `url(\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>\")`,\n    backgroundRepeat: `no-repeat`,\n    backgroundPosition: `right .55em center`,\n    backgroundSize: `.65em auto, 100%`,\n    color: theme.inputTextColor,\n  }),\n  {\n    '(max-width: 500px)': {\n      display: 'none',\n    },\n  },\n)\n", "import * as React from 'react'\n\nexport default function ScreenReader({ text }: { text: string }) {\n  return (\n    <span\n      style={{\n        position: 'absolute',\n        width: '0.1px',\n        height: '0.1px',\n        overflow: 'hidden',\n      }}\n    >\n      {text}\n    </span>\n  )\n}\n", "'use client'\nimport * as React from 'react'\n\nimport { displayValue, styled } from './utils'\nimport superjson from 'superjson'\n\nexport const Entry = styled('div', {\n  fontFamily: 'Menlo, monospace',\n  fontSize: '1em',\n  lineHeight: '1.7',\n  outline: 'none',\n  wordBreak: 'break-word',\n})\n\nexport const Label = styled('span', {\n  color: 'white',\n})\n\nexport const LabelButton = styled('button', {\n  cursor: 'pointer',\n  color: 'white',\n})\n\nexport const ExpandButton = styled('button', {\n  cursor: 'pointer',\n  color: 'inherit',\n  font: 'inherit',\n  outline: 'inherit',\n  background: 'transparent',\n  border: 'none',\n  padding: 0,\n})\n\ntype CopyState = 'NoCopy' | 'SuccessCopy' | 'ErrorCopy'\n\nexport const CopyButton = ({ value }: { value: unknown }) => {\n  const [copyState, setCopyState] = React.useState<CopyState>('NoCopy')\n\n  return (\n    <button\n      onClick={\n        copyState === 'NoCopy'\n          ? () => {\n              navigator.clipboard.writeText(superjson.stringify(value)).then(\n                () => {\n                  setCopyState('SuccessCopy')\n                  setTimeout(() => {\n                    setCopyState('NoCopy')\n                  }, 1500)\n                },\n                (err) => {\n                  console.error('Failed to copy: ', err)\n                  setCopyState('ErrorCopy')\n                  setTimeout(() => {\n                    setCopyState('NoCopy')\n                  }, 1500)\n                },\n              )\n            }\n          : undefined\n      }\n      style={{\n        cursor: 'pointer',\n        color: 'inherit',\n        font: 'inherit',\n        outline: 'inherit',\n        background: 'transparent',\n        border: 'none',\n        padding: 0,\n      }}\n    >\n      {copyState === 'NoCopy' ? (\n        <Copier />\n      ) : copyState === 'SuccessCopy' ? (\n        <CopiedCopier />\n      ) : (\n        <ErrorCopier />\n      )}\n    </button>\n  )\n}\n\nexport const Value = styled('span', (_props, theme) => ({\n  color: theme.danger,\n}))\n\nexport const SubEntries = styled('div', {\n  marginLeft: '.1em',\n  paddingLeft: '1em',\n  borderLeft: '2px solid rgba(0,0,0,.15)',\n})\n\nexport const Info = styled('span', {\n  color: 'grey',\n  fontSize: '.7em',\n})\n\ntype ExpanderProps = {\n  expanded: boolean\n  style?: React.CSSProperties\n}\n\nexport const Expander = ({ expanded, style = {} }: ExpanderProps) => (\n  <span\n    style={{\n      display: 'inline-block',\n      transition: 'all .1s ease',\n      transform: `rotate(${expanded ? 90 : 0}deg) ${style.transform || ''}`,\n      ...style,\n    }}\n  >\n    ▶\n  </span>\n)\n\nconst Copier = () => (\n  <span\n    aria-label=\"Copy object to clipboard\"\n    title=\"Copy object to clipboard\"\n    style={{\n      paddingLeft: '1em',\n    }}\n  >\n    <svg height=\"12\" viewBox=\"0 0 16 12\" width=\"10\">\n      <path\n        fill=\"currentColor\"\n        d=\"M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 010 1.5h-1.5a.25.25 0 00-.25.25v7.5c0 .*************.25h7.5a.25.25 0 00.25-.25v-1.5a.75.75 0 011.5 0v1.5A1.75 1.75 0 019.25 16h-7.5A1.75 1.75 0 010 14.25v-7.5z\"\n      ></path>\n      <path\n        fill=\"currentColor\"\n        d=\"M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0114.25 11h-7.5A1.75 1.75 0 015 9.25v-7.5zm1.75-.25a.25.25 0 00-.25.25v7.5c0 .*************.25h7.5a.25.25 0 00.25-.25v-7.5a.25.25 0 00-.25-.25h-7.5z\"\n      ></path>\n    </svg>\n  </span>\n)\n\nconst ErrorCopier = () => (\n  <span\n    aria-label=\"Failed copying to clipboard\"\n    title=\"Failed copying to clipboard\"\n    style={{\n      paddingLeft: '1em',\n      display: 'flex',\n      alignItems: 'center',\n    }}\n  >\n    <svg height=\"12\" viewBox=\"0 0 16 12\" width=\"10\" display=\"block\">\n      <path\n        fill=\"red\"\n        d=\"M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z\"\n      ></path>\n    </svg>\n    <span\n      style={{\n        color: 'red',\n        fontSize: '12px',\n        paddingLeft: '4px',\n        position: 'relative',\n        top: '2px',\n      }}\n    >\n      See console\n    </span>\n  </span>\n)\n\nconst CopiedCopier = () => (\n  <span\n    aria-label=\"Object copied to clipboard\"\n    title=\"Object copied to clipboard\"\n    style={{\n      paddingLeft: '1em',\n      display: 'inline-block',\n      verticalAlign: 'middle',\n    }}\n  >\n    <svg height=\"16\" viewBox=\"0 0 16 16\" width=\"16\" display=\"block\">\n      <path\n        fill=\"green\"\n        d=\"M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z\"\n      ></path>\n    </svg>\n  </span>\n)\n\ntype Entry = {\n  label: string\n}\n\ntype RendererProps = {\n  handleEntry: (entry: Entry) => JSX.Element\n  label?: string\n  value: unknown\n  subEntries: Entry[]\n  subEntryPages: Entry[][]\n  type: string\n  expanded: boolean\n  copyable: boolean\n  toggleExpanded: () => void\n  pageSize: number\n}\n\n/**\n * Chunk elements in the array by size\n *\n * when the array cannot be chunked evenly by size, the last chunk will be\n * filled with the remaining elements\n *\n * @example\n * chunkArray(['a','b', 'c', 'd', 'e'], 2) // returns [['a','b'], ['c', 'd'], ['e']]\n */\nexport function chunkArray<T>(array: T[], size: number): T[][] {\n  if (size < 1) return []\n  let i = 0\n  const result: T[][] = []\n  while (i < array.length) {\n    result.push(array.slice(i, i + size))\n    i = i + size\n  }\n  return result\n}\n\ntype Renderer = (props: RendererProps) => JSX.Element\n\nexport const DefaultRenderer: Renderer = ({\n  handleEntry,\n  label,\n  value,\n  subEntries = [],\n  subEntryPages = [],\n  type,\n  expanded = false,\n  copyable = false,\n  toggleExpanded,\n  pageSize,\n}) => {\n  const [expandedPages, setExpandedPages] = React.useState<number[]>([])\n\n  return (\n    <Entry key={label}>\n      {subEntryPages.length ? (\n        <>\n          <ExpandButton onClick={() => toggleExpanded()}>\n            <Expander expanded={expanded} /> {label}{' '}\n            <Info>\n              {String(type).toLowerCase() === 'iterable' ? '(Iterable) ' : ''}\n              {subEntries.length} {subEntries.length > 1 ? `items` : `item`}\n            </Info>\n          </ExpandButton>\n          {copyable ? <CopyButton value={value} /> : null}\n          {expanded ? (\n            subEntryPages.length === 1 ? (\n              <SubEntries>{subEntries.map(handleEntry)}</SubEntries>\n            ) : (\n              <SubEntries>\n                {subEntryPages.map((entries, index) => (\n                  <div key={index}>\n                    <Entry>\n                      <LabelButton\n                        onClick={() =>\n                          setExpandedPages((old) =>\n                            old.includes(index)\n                              ? old.filter((d) => d !== index)\n                              : [...old, index],\n                          )\n                        }\n                      >\n                        <Expander expanded={expanded} /> [{index * pageSize} ...{' '}\n                        {index * pageSize + pageSize - 1}]\n                      </LabelButton>\n                      {expandedPages.includes(index) ? (\n                        <SubEntries>{entries.map(handleEntry)}</SubEntries>\n                      ) : null}\n                    </Entry>\n                  </div>\n                ))}\n              </SubEntries>\n            )\n          ) : null}\n        </>\n      ) : (\n        <>\n          <Label>{label}:</Label> <Value>{displayValue(value)}</Value>\n        </>\n      )}\n    </Entry>\n  )\n}\n\ntype ExplorerProps = Partial<RendererProps> & {\n  renderer?: Renderer\n  defaultExpanded?: true | Record<string, boolean>\n  copyable?: boolean\n}\n\ntype Property = {\n  defaultExpanded?: boolean | Record<string, boolean>\n  label: string\n  value: unknown\n}\n\nfunction isIterable(x: any): x is Iterable<unknown> {\n  return Symbol.iterator in x\n}\n\nexport default function Explorer({\n  value,\n  defaultExpanded,\n  renderer = DefaultRenderer,\n  pageSize = 100,\n  copyable = false,\n  ...rest\n}: ExplorerProps) {\n  const [expanded, setExpanded] = React.useState(Boolean(defaultExpanded))\n  const toggleExpanded = React.useCallback(() => setExpanded((old) => !old), [])\n\n  let type: string = typeof value\n  let subEntries: Property[] = []\n\n  const makeProperty = (sub: { label: string; value: unknown }): Property => {\n    const subDefaultExpanded =\n      defaultExpanded === true\n        ? { [sub.label]: true }\n        : defaultExpanded?.[sub.label]\n    return {\n      ...sub,\n      defaultExpanded: subDefaultExpanded,\n    }\n  }\n\n  if (Array.isArray(value)) {\n    type = 'array'\n    subEntries = value.map((d, i) =>\n      makeProperty({\n        label: i.toString(),\n        value: d,\n      }),\n    )\n  } else if (\n    value !== null &&\n    typeof value === 'object' &&\n    isIterable(value) &&\n    typeof value[Symbol.iterator] === 'function'\n  ) {\n    type = 'Iterable'\n    subEntries = Array.from(value, (val, i) =>\n      makeProperty({\n        label: i.toString(),\n        value: val,\n      }),\n    )\n  } else if (typeof value === 'object' && value !== null) {\n    type = 'object'\n    subEntries = Object.entries(value).map(([key, val]) =>\n      makeProperty({\n        label: key,\n        value: val,\n      }),\n    )\n  }\n\n  const subEntryPages = chunkArray(subEntries, pageSize)\n\n  return renderer({\n    handleEntry: (entry) => (\n      <Explorer\n        key={entry.label}\n        value={value}\n        renderer={renderer}\n        copyable={copyable}\n        {...rest}\n        {...entry}\n      />\n    ),\n    type,\n    subEntries,\n    subEntryPages,\n    value,\n    expanded,\n    copyable,\n    toggleExpanded,\n    pageSize,\n    ...rest,\n  })\n}\n", "import * as React from 'react'\n\nexport default function Logo(props: any) {\n  return (\n    <svg\n      width=\"40px\"\n      height=\"40px\"\n      viewBox=\"0 0 190 190\"\n      version=\"1.1\"\n      {...props}\n    >\n      <g stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n        <g transform=\"translate(-33.000000, 0.000000)\">\n          <path\n            d=\"M72.7239712,61.3436237 C69.631224,46.362877 68.9675112,34.8727722 70.9666331,26.5293551 C72.1555965,21.5671678 74.3293088,17.5190846 77.6346064,14.5984631 C81.1241394,11.5150478 85.5360327,10.0020122 90.493257,10.0020122 C98.6712013,10.0020122 107.26826,13.7273214 116.455725,20.8044264 C120.20312,23.6910458 124.092437,27.170411 128.131651,31.2444746 C128.45314,30.8310265 128.816542,30.4410453 129.22143,30.0806152 C140.64098,19.9149716 150.255245,13.5989272 158.478408,11.1636507 C163.367899,9.715636 167.958526,9.57768202 172.138936,10.983031 C176.551631,12.4664684 180.06766,15.5329489 182.548314,19.8281091 C186.642288,26.9166735 187.721918,36.2310983 186.195595,47.7320243 C185.573451,52.4199112 184.50985,57.5263831 183.007094,63.0593153 C183.574045,63.1277086 184.142416,63.2532808 184.705041,63.4395297 C199.193932,68.2358678 209.453582,73.3937462 215.665021,79.2882839 C219.360669,82.7953831 221.773972,86.6998434 222.646365,91.0218204 C223.567176,95.5836746 222.669313,100.159332 220.191548,104.451297 C216.105211,111.529614 208.591643,117.11221 197.887587,121.534031 C193.589552,123.309539 188.726579,124.917559 183.293259,126.363748 C183.541176,126.92292 183.733521,127.516759 183.862138,128.139758 C186.954886,143.120505 187.618598,154.61061 185.619477,162.954027 C184.430513,167.916214 182.256801,171.964297 178.951503,174.884919 C175.46197,177.968334 171.050077,179.48137 166.092853,179.48137 C157.914908,179.48137 149.31785,175.756061 140.130385,168.678956 C136.343104,165.761613 132.410866,162.238839 128.325434,158.108619 C127.905075,158.765474 127.388968,159.376011 126.77857,159.919385 C115.35902,170.085028 105.744755,176.401073 97.5215915,178.836349 C92.6321009,180.284364 88.0414736,180.422318 83.8610636,179.016969 C79.4483686,177.533532 75.9323404,174.467051 73.4516862,170.171891 C69.3577116,163.083327 68.2780823,153.768902 69.8044053,142.267976 C70.449038,137.410634 71.56762,132.103898 73.1575891,126.339009 C72.5361041,126.276104 71.9120754,126.144816 71.2949591,125.940529 C56.8060684,121.144191 46.5464184,115.986312 40.3349789,110.091775 C36.6393312,106.584675 34.2260275,102.680215 33.3536352,98.3582381 C32.4328237,93.7963839 33.3306866,89.2207269 35.8084524,84.9287618 C39.8947886,77.8504443 47.4083565,72.2678481 58.1124133,67.8460273 C62.5385143,66.0176154 67.5637208,64.366822 73.1939394,62.8874674 C72.9933393,62.3969171 72.8349374,61.8811235 72.7239712,61.3436237 Z\"\n            fill=\"#002C4B\"\n            fillRule=\"nonzero\"\n            transform=\"translate(128.000000, 95.000000) scale(-1, 1) translate(-128.000000, -95.000000) \"\n          ></path>\n          <path\n            d=\"M113.396882,64 L142.608177,64 C144.399254,64 146.053521,64.958025 146.944933,66.5115174 L161.577138,92.0115174 C162.461464,93.5526583 162.461464,95.4473417 161.577138,96.9884826 L146.944933,122.488483 C146.053521,124.041975 144.399254,125 142.608177,125 L113.396882,125 C111.605806,125 109.951539,124.041975 109.060126,122.488483 L94.4279211,96.9884826 C93.543596,95.4473417 93.543596,93.5526583 94.4279211,92.0115174 L109.060126,66.5115174 C109.951539,64.958025 111.605806,64 113.396882,64 Z M138.987827,70.2765273 C140.779849,70.2765273 142.434839,71.2355558 143.325899,72.7903404 L154.343038,92.0138131 C155.225607,93.5537825 155.225607,95.4462175 154.343038,96.9861869 L143.325899,116.20966 C142.434839,117.764444 140.779849,118.723473 138.987827,118.723473 L117.017233,118.723473 C115.225211,118.723473 113.570221,117.764444 112.67916,116.20966 L101.662022,96.9861869 C100.779452,95.4462175 100.779452,93.5537825 101.662022,92.0138131 L112.67916,72.7903404 C113.570221,71.2355558 115.225211,70.2765273 117.017233,70.2765273 L138.987827,70.2765273 Z M135.080648,77.1414791 L120.924411,77.1414791 C119.134228,77.1414791 117.480644,78.0985567 116.5889,79.6508285 L116.5889,79.6508285 L109.489217,92.0093494 C108.603232,93.5515958 108.603232,95.4484042 109.489217,96.9906506 L109.489217,96.9906506 L116.5889,109.349172 C117.480644,110.901443 119.134228,111.858521 120.924411,111.858521 L120.924411,111.858521 L135.080648,111.858521 C136.870831,111.858521 138.524416,110.901443 139.41616,109.349172 L139.41616,109.349172 L146.515843,96.9906506 C147.401828,95.4484042 147.401828,93.5515958 146.515843,92.0093494 L146.515843,92.0093494 L139.41616,79.6508285 C138.524416,78.0985567 136.870831,77.1414791 135.080648,77.1414791 L135.080648,77.1414791 Z M131.319186,83.7122186 C133.108028,83.7122186 134.760587,84.6678753 135.652827,86.2183156 L138.983552,92.0060969 C139.87203,93.5500005 139.87203,95.4499995 138.983552,96.9939031 L135.652827,102.781684 C134.760587,104.332125 133.108028,105.287781 131.319186,105.287781 L124.685874,105.287781 C122.897032,105.287781 121.244473,104.332125 120.352233,102.781684 L117.021508,96.9939031 C116.13303,95.4499995 116.13303,93.5500005 117.021508,92.0060969 L120.352233,86.2183156 C121.244473,84.6678753 122.897032,83.7122186 124.685874,83.7122186 L131.319186,83.7122186 Z M128.003794,90.1848875 C126.459294,90.1848875 125.034382,91.0072828 124.263005,92.3424437 C123.491732,93.6774232 123.491732,95.3225768 124.263005,96.6575563 C125.034382,97.9927172 126.459294,98.8151125 128.001266,98.8151125 L128.001266,98.8151125 C129.545766,98.8151125 130.970678,97.9927172 131.742055,96.6575563 C132.513327,95.3225768 132.513327,93.6774232 131.742055,92.3424437 C130.970678,91.0072828 129.545766,90.1848875 128.003794,90.1848875 L128.003794,90.1848875 Z M93,94.5009646 L100.767764,94.5009646\"\n            fill=\"#FFD94C\"\n          ></path>\n          <path\n            d=\"M87.8601729,108.357758 C89.1715224,107.608286 90.8360246,108.074601 91.5779424,109.399303 L91.5779424,109.399303 L92.0525843,110.24352 C95.8563392,116.982993 99.8190116,123.380176 103.940602,129.435068 C108.807881,136.585427 114.28184,143.82411 120.362479,151.151115 C121.316878,152.30114 121.184944,154.011176 120.065686,154.997937 L120.065686,154.997937 L119.454208,155.534625 C99.3465389,173.103314 86.2778188,176.612552 80.2480482,166.062341 C74.3500652,155.742717 76.4844915,136.982888 86.6513274,109.782853 C86.876818,109.179582 87.3045861,108.675291 87.8601729,108.357758 Z M173.534177,129.041504 C174.986131,128.785177 176.375496,129.742138 176.65963,131.194242 L176.65963,131.194242 L176.812815,131.986376 C181.782365,157.995459 178.283348,171 166.315764,171 C154.609745,171 139.708724,159.909007 121.612702,137.727022 C121.211349,137.235047 120.994572,136.617371 121,135.981509 C121.013158,134.480686 122.235785,133.274651 123.730918,133.287756 L123.730918,133.287756 L124.684654,133.294531 C132.305698,133.335994 139.714387,133.071591 146.910723,132.501323 C155.409039,131.82788 164.283523,130.674607 173.534177,129.041504 Z M180.408726,73.8119663 C180.932139,72.4026903 182.508386,71.6634537 183.954581,72.149012 L183.954581,72.149012 L184.742552,72.4154854 C210.583763,81.217922 220.402356,90.8916805 214.198332,101.436761 C208.129904,111.751366 190.484347,119.260339 161.26166,123.963678 C160.613529,124.067994 159.948643,123.945969 159.382735,123.618843 C158.047025,122.846729 157.602046,121.158214 158.388848,119.847438 L158.388848,119.847438 L158.889328,119.0105 C162.877183,112.31633 166.481358,105.654262 169.701854,99.0242957 C173.50501,91.1948179 177.073967,82.7907081 180.408726,73.8119663 Z M94.7383398,66.0363218 C95.3864708,65.9320063 96.0513565,66.0540315 96.6172646,66.3811573 C97.9529754,67.153271 98.3979538,68.8417862 97.6111517,70.1525615 L97.6111517,70.1525615 L97.1106718,70.9895001 C93.1228168,77.6836699 89.5186416,84.3457379 86.2981462,90.9757043 C82.49499,98.8051821 78.9260328,107.209292 75.5912744,116.188034 C75.0678608,117.59731 73.4916142,118.336546 72.045419,117.850988 L72.045419,117.850988 L71.2574475,117.584515 C45.4162372,108.782078 35.597644,99.1083195 41.8016679,88.5632391 C47.8700957,78.2486335 65.515653,70.7396611 94.7383398,66.0363218 Z M136.545792,34.4653746 C156.653461,16.8966864 169.722181,13.3874478 175.751952,23.9376587 C181.649935,34.2572826 179.515508,53.0171122 169.348673,80.2171474 C169.123182,80.8204179 168.695414,81.324709 168.139827,81.6422422 C166.828478,82.3917144 165.163975,81.9253986 164.422058,80.6006966 L164.422058,80.6006966 L163.947416,79.7564798 C160.143661,73.0170065 156.180988,66.6198239 152.059398,60.564932 C147.192119,53.4145727 141.71816,46.1758903 135.637521,38.8488847 C134.683122,37.6988602 134.815056,35.9888243 135.934314,35.0020629 L135.934314,35.0020629 Z M90.6842361,18 C102.390255,18 117.291276,29.0909926 135.387298,51.2729777 C135.788651,51.7649527 136.005428,52.3826288 136,53.0184911 C135.986842,54.5193144 134.764215,55.7253489 133.269082,55.7122445 L133.269082,55.7122445 L132.315346,55.7054689 C124.694302,55.6640063 117.285613,55.9284091 110.089277,56.4986773 C101.590961,57.17212 92.7164767,58.325393 83.4658235,59.9584962 C82.0138691,60.2148231 80.6245044,59.2578618 80.3403697,57.805758 L80.3403697,57.805758 L80.1871846,57.0136235 C75.2176347,31.0045412 78.7166519,18 90.6842361,18 Z\"\n            fill=\"#FF4154\"\n          ></path>\n        </g>\n      </g>\n    </svg>\n  )\n}\n", "'use client'\nimport * as React from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport type {\n  QueryCache,\n  QueryClient,\n  QueryKey as QueryKeyType,\n  ContextOptions,\n  Query,\n} from '@tanstack/react-query'\nimport {\n  useQueryClient,\n  onlineManager,\n  notifyManager,\n} from '@tanstack/react-query'\nimport { rankItem } from '@tanstack/match-sorter-utils'\nimport useLocalStorage from './useLocalStorage'\nimport {\n  isVerticalSide,\n  sortFns,\n  useIsMounted,\n  getSidePanelStyle,\n  minPanelSize,\n  getResizeHandleStyle,\n  getSidedProp,\n  defaultPanelSize,\n  displayValue,\n} from './utils'\nimport type { Corner, Side } from './utils'\nimport {\n  Panel,\n  QueryKeys,\n  QueryKey,\n  Button,\n  Code,\n  Input,\n  Select,\n  ActiveQueryPanel,\n} from './styledComponents'\nimport ScreenReader from './screenreader'\nimport { ThemeProvider, defaultTheme as theme } from './theme'\nimport { getQueryStatusLabel, getQueryStatusColor } from './utils'\nimport Explorer from './Explorer'\nimport Logo from './Logo'\nimport { useMemo } from 'react'\n\nexport interface DevToolsErrorType {\n  /**\n   * The name of the error.\n   */\n  name: string\n  /**\n   * How the error is initialized. Whatever it returns MUST implement toString() so\n   * we can check against the current error.\n   */\n  initializer: (query: Query) => { toString(): string }\n}\n\nexport interface DevtoolsOptions extends ContextOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * Use this to add props to the panel. For example, you can add className, style (merge and override default style), etc.\n   */\n  panelProps?: React.ComponentPropsWithoutRef<'div'>\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * Use this to add props to the toggle button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  toggleButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * The position of the React Query logo to open and close the devtools panel.\n   * Defaults to 'bottom-left'.\n   */\n  position?: Corner\n  /**\n   * The position of the React Query devtools panel.\n   * Defaults to 'bottom'.\n   */\n  panelPosition?: Side\n  /**\n   * Use this to render the devtools inside a different type of container element for a11y purposes.\n   * Any string which corresponds to a valid intrinsic JSX element is allowed.\n   * Defaults to 'aside'.\n   */\n  containerElement?: string | any\n  /**\n   * nonce for style element for CSP\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: DevToolsErrorType[]\n}\n\ninterface DevtoolsPanelOptions extends ContextOptions {\n  /**\n   * The standard React style object used to style a component with inline styles\n   */\n  style?: React.CSSProperties\n  /**\n   * The standard React className property used to style a component with classes\n   */\n  className?: string\n  /**\n   * A boolean variable indicating whether the panel is open or closed\n   */\n  isOpen?: boolean\n  /**\n   * nonce for style element for CSP\n   */\n  styleNonce?: string\n  /**\n   * A function that toggles the open and close state of the panel\n   */\n  setIsOpen: (isOpen: boolean) => void\n  /**\n   * Handles the opening and closing the devtools panel\n   */\n  onDragStart: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void\n  /**\n   * The position of the React Query devtools panel.\n   * Defaults to 'bottom'.\n   */\n  position?: Side\n  /**\n   * Handles the panel position select change\n   */\n  onPositionChange?: (side: Side) => void\n  /**\n   * Show a close button inside the panel\n   */\n  showCloseButton?: boolean\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: DevToolsErrorType[]\n}\n\nexport function ReactQueryDevtools({\n  initialIsOpen,\n  panelProps = {},\n  closeButtonProps = {},\n  toggleButtonProps = {},\n  position = 'bottom-left',\n  containerElement: Container = 'aside',\n  context,\n  styleNonce,\n  panelPosition: initialPanelPosition = 'bottom',\n  errorTypes = [],\n}: DevtoolsOptions): React.ReactElement | null {\n  const rootRef = React.useRef<HTMLDivElement>(null)\n  const panelRef = React.useRef<HTMLDivElement>(null)\n  const [isOpen, setIsOpen] = useLocalStorage(\n    'reactQueryDevtoolsOpen',\n    initialIsOpen,\n  )\n  const [devtoolsHeight, setDevtoolsHeight] = useLocalStorage<number>(\n    'reactQueryDevtoolsHeight',\n    defaultPanelSize,\n  )\n  const [devtoolsWidth, setDevtoolsWidth] = useLocalStorage<number>(\n    'reactQueryDevtoolsWidth',\n    defaultPanelSize,\n  )\n\n  const [panelPosition = 'bottom', setPanelPosition] = useLocalStorage<Side>(\n    'reactQueryDevtoolsPanelPosition',\n    initialPanelPosition,\n  )\n\n  const [isResolvedOpen, setIsResolvedOpen] = React.useState(false)\n  const [isResizing, setIsResizing] = React.useState(false)\n  const isMounted = useIsMounted()\n\n  const handleDragStart = (\n    panelElement: HTMLDivElement | null,\n    startEvent: React.MouseEvent<HTMLDivElement, MouseEvent>,\n  ) => {\n    if (!panelElement) return\n    if (startEvent.button !== 0) return // Only allow left click for drag\n    const isVertical = isVerticalSide(panelPosition)\n    setIsResizing(true)\n\n    const { height, width } = panelElement.getBoundingClientRect()\n    const startX = startEvent.clientX\n    const startY = startEvent.clientY\n    let newSize = 0\n\n    const run = (moveEvent: MouseEvent) => {\n      // prevent mouse selecting stuff with mouse drag\n      moveEvent.preventDefault()\n\n      // calculate the correct size based on mouse position and current panel position\n      // hint: it is different formula for the opposite sides\n      if (isVertical) {\n        newSize =\n          width +\n          (panelPosition === 'right'\n            ? startX - moveEvent.clientX\n            : moveEvent.clientX - startX)\n        setDevtoolsWidth(newSize)\n      } else {\n        newSize =\n          height +\n          (panelPosition === 'bottom'\n            ? startY - moveEvent.clientY\n            : moveEvent.clientY - startY)\n        setDevtoolsHeight(newSize)\n      }\n\n      if (newSize < minPanelSize) {\n        setIsOpen(false)\n      } else {\n        setIsOpen(true)\n      }\n    }\n\n    const unsub = () => {\n      if (isResizing) {\n        setIsResizing(false)\n      }\n\n      document.removeEventListener('mousemove', run, false)\n      document.removeEventListener('mouseUp', unsub, false)\n    }\n\n    document.addEventListener('mousemove', run, false)\n    document.addEventListener('mouseup', unsub, false)\n  }\n\n  React.useEffect(() => {\n    setIsResolvedOpen(isOpen ?? false)\n  }, [isOpen, isResolvedOpen, setIsResolvedOpen])\n\n  // Toggle panel visibility before/after transition (depending on direction).\n  // Prevents focusing in a closed panel.\n  React.useEffect(() => {\n    const ref = panelRef.current\n    if (ref) {\n      const handlePanelTransitionStart = () => {\n        if (isResolvedOpen) {\n          ref.style.visibility = 'visible'\n        }\n      }\n\n      const handlePanelTransitionEnd = () => {\n        if (!isResolvedOpen) {\n          ref.style.visibility = 'hidden'\n        }\n      }\n\n      ref.addEventListener('transitionstart', handlePanelTransitionStart)\n      ref.addEventListener('transitionend', handlePanelTransitionEnd)\n\n      return () => {\n        ref.removeEventListener('transitionstart', handlePanelTransitionStart)\n        ref.removeEventListener('transitionend', handlePanelTransitionEnd)\n      }\n    }\n    return\n  }, [isResolvedOpen])\n\n  React.useEffect(() => {\n    if (isResolvedOpen && rootRef.current?.parentElement) {\n      const { parentElement } = rootRef.current\n      const styleProp = getSidedProp('padding', panelPosition)\n      const isVertical = isVerticalSide(panelPosition)\n\n      const previousPaddings = (({\n        padding,\n        paddingTop,\n        paddingBottom,\n        paddingLeft,\n        paddingRight,\n      }) => ({\n        padding,\n        paddingTop,\n        paddingBottom,\n        paddingLeft,\n        paddingRight,\n      }))(parentElement.style)\n\n      const run = () => {\n        // reset the padding\n        parentElement.style.padding = '0px'\n        parentElement.style.paddingTop = '0px'\n        parentElement.style.paddingBottom = '0px'\n        parentElement.style.paddingLeft = '0px'\n        parentElement.style.paddingRight = '0px'\n        // set the new padding based on the new panel position\n\n        parentElement.style[styleProp] = `${\n          isVertical ? devtoolsWidth : devtoolsHeight\n        }px`\n      }\n\n      run()\n\n      if (typeof window !== 'undefined') {\n        window.addEventListener('resize', run)\n\n        return () => {\n          window.removeEventListener('resize', run)\n          Object.entries(previousPaddings).forEach(\n            ([property, previousValue]) => {\n              parentElement.style[property as keyof typeof previousPaddings] =\n                previousValue\n            },\n          )\n        }\n      }\n    }\n    return\n  }, [isResolvedOpen, panelPosition, devtoolsHeight, devtoolsWidth])\n\n  const { style: panelStyle = {}, ...otherPanelProps } = panelProps\n\n  const {\n    style: toggleButtonStyle = {},\n    onClick: onToggleClick,\n    ...otherToggleButtonProps\n  } = toggleButtonProps\n\n  // get computed style based on panel position\n  const style = getSidePanelStyle({\n    position: panelPosition,\n    devtoolsTheme: theme,\n    isOpen: isResolvedOpen,\n    height: devtoolsHeight,\n    width: devtoolsWidth,\n    isResizing,\n    panelStyle,\n  })\n\n  // Do not render on the server\n  if (!isMounted()) return null\n\n  return (\n    <Container\n      ref={rootRef}\n      className=\"ReactQueryDevtools\"\n      aria-label=\"React Query Devtools\"\n    >\n      <ThemeProvider theme={theme}>\n        <ReactQueryDevtoolsPanel\n          ref={panelRef as any}\n          context={context}\n          styleNonce={styleNonce}\n          position={panelPosition}\n          onPositionChange={setPanelPosition}\n          showCloseButton\n          closeButtonProps={closeButtonProps}\n          {...otherPanelProps}\n          style={style}\n          isOpen={isResolvedOpen}\n          setIsOpen={setIsOpen}\n          onDragStart={(e) => handleDragStart(panelRef.current, e)}\n          errorTypes={errorTypes}\n        />\n      </ThemeProvider>\n      {!isResolvedOpen ? (\n        <button\n          type=\"button\"\n          {...otherToggleButtonProps}\n          aria-label=\"Open React Query Devtools\"\n          aria-controls=\"ReactQueryDevtoolsPanel\"\n          aria-haspopup=\"true\"\n          aria-expanded=\"false\"\n          onClick={(e) => {\n            setIsOpen(true)\n            onToggleClick?.(e)\n          }}\n          style={{\n            background: 'none',\n            border: 0,\n            padding: 0,\n            position: 'fixed',\n            zIndex: 99999,\n            display: 'inline-flex',\n            fontSize: '1.5em',\n            margin: '.5em',\n            cursor: 'pointer',\n            width: 'fit-content',\n            ...(position === 'top-right'\n              ? {\n                  top: '0',\n                  right: '0',\n                }\n              : position === 'top-left'\n              ? {\n                  top: '0',\n                  left: '0',\n                }\n              : position === 'bottom-right'\n              ? {\n                  bottom: '0',\n                  right: '0',\n                }\n              : {\n                  bottom: '0',\n                  left: '0',\n                }),\n            ...toggleButtonStyle,\n          }}\n        >\n          <Logo aria-hidden />\n          <ScreenReader text=\"Open React Query Devtools\" />\n        </button>\n      ) : null}\n    </Container>\n  )\n}\n\nconst useSubscribeToQueryCache = <T,>(\n  queryCache: QueryCache,\n  getSnapshot: () => T,\n  skip: boolean = false,\n): T => {\n  return useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        if (!skip)\n          return queryCache.subscribe(notifyManager.batchCalls(onStoreChange))\n        return () => {\n          return\n        }\n      },\n      [queryCache, skip],\n    ),\n    getSnapshot,\n    getSnapshot,\n  )\n}\n\nexport const ReactQueryDevtoolsPanel = React.forwardRef<\n  HTMLDivElement,\n  DevtoolsPanelOptions\n>(function ReactQueryDevtoolsPanel(props, ref): React.ReactElement {\n  const {\n    isOpen = true,\n    styleNonce,\n    setIsOpen,\n    context,\n    onDragStart,\n    onPositionChange,\n    showCloseButton,\n    position,\n    closeButtonProps = {},\n    errorTypes = [],\n    ...panelProps\n  } = props\n\n  const { onClick: onCloseClick, ...otherCloseButtonProps } = closeButtonProps\n\n  const queryClient = useQueryClient({ context })\n  const queryCache = queryClient.getQueryCache()\n\n  const [sort, setSort] = useLocalStorage(\n    'reactQueryDevtoolsSortFn',\n    Object.keys(sortFns)[0],\n  )\n\n  const [filter, setFilter] = useLocalStorage('reactQueryDevtoolsFilter', '')\n\n  const [baseSort, setBaseSort] = useLocalStorage(\n    'reactQueryDevtoolsBaseSort',\n    1,\n  )\n\n  const sortFn = React.useMemo(() => sortFns[sort as string], [sort])\n\n  const queriesCount = useSubscribeToQueryCache(\n    queryCache,\n    () => queryCache.getAll().length,\n    !isOpen,\n  )\n\n  const [activeQueryHash, setActiveQueryHash] = useLocalStorage(\n    'reactQueryDevtoolsActiveQueryHash',\n    '',\n  )\n\n  const queries = React.useMemo(() => {\n    const unsortedQueries = queryCache.getAll()\n\n    if (queriesCount === 0) {\n      return []\n    }\n\n    const filtered = filter\n      ? unsortedQueries.filter(\n          (item) => rankItem(item.queryHash, filter).passed,\n        )\n      : [...unsortedQueries]\n\n    const sorted = sortFn\n      ? filtered.sort((a, b) => sortFn(a, b) * (baseSort as number))\n      : filtered\n\n    return sorted\n  }, [baseSort, sortFn, filter, queriesCount, queryCache])\n\n  const [isMockOffline, setMockOffline] = React.useState(false)\n\n  return (\n    <ThemeProvider theme={theme}>\n      <Panel\n        ref={ref}\n        className=\"ReactQueryDevtoolsPanel\"\n        aria-label=\"React Query Devtools Panel\"\n        id=\"ReactQueryDevtoolsPanel\"\n        {...panelProps}\n        style={{\n          height: defaultPanelSize,\n          position: 'relative',\n          ...panelProps.style,\n        }}\n      >\n        <style\n          nonce={styleNonce}\n          dangerouslySetInnerHTML={{\n            __html: `\n            .ReactQueryDevtoolsPanel * {\n              scrollbar-color: ${theme.backgroundAlt} ${theme.gray};\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar, .ReactQueryDevtoolsPanel scrollbar {\n              width: 1em;\n              height: 1em;\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-track, .ReactQueryDevtoolsPanel scrollbar-track {\n              background: ${theme.backgroundAlt};\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-thumb, .ReactQueryDevtoolsPanel scrollbar-thumb {\n              background: ${theme.gray};\n              border-radius: .5em;\n              border: 3px solid ${theme.backgroundAlt};\n            }\n          `,\n          }}\n        />\n        <div\n          style={getResizeHandleStyle(position)}\n          onMouseDown={onDragStart}\n        ></div>\n\n        {isOpen && (\n          <div\n            style={{\n              flex: '1 1 500px',\n              minHeight: '40%',\n              maxHeight: '100%',\n              overflow: 'auto',\n              borderRight: `1px solid ${theme.grayAlt}`,\n              display: 'flex',\n              flexDirection: 'column',\n            }}\n          >\n            <div\n              style={{\n                padding: '.5em',\n                background: theme.backgroundAlt,\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n              }}\n            >\n              <button\n                type=\"button\"\n                aria-label=\"Close React Query Devtools\"\n                aria-controls=\"ReactQueryDevtoolsPanel\"\n                aria-haspopup=\"true\"\n                aria-expanded=\"true\"\n                onClick={() => setIsOpen(false)}\n                style={{\n                  display: 'inline-flex',\n                  background: 'none',\n                  border: 0,\n                  padding: 0,\n                  marginRight: '.5em',\n                  cursor: 'pointer',\n                }}\n              >\n                <Logo aria-hidden />\n                <ScreenReader text=\"Close React Query Devtools\" />\n              </button>\n\n              <div\n                style={{\n                  display: 'flex',\n                  flexDirection: 'column',\n                }}\n              >\n                <div\n                  style={{\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    marginBottom: '.5em',\n                  }}\n                >\n                  <QueryStatusCount queryCache={queryCache} />\n                  {position && onPositionChange ? (\n                    <Select\n                      aria-label=\"Panel position\"\n                      value={position}\n                      style={{ marginInlineStart: '.5em' }}\n                      onChange={(e) => onPositionChange(e.target.value as Side)}\n                    >\n                      <option value=\"left\">Left</option>\n                      <option value=\"right\">Right</option>\n                      <option value=\"top\">Top</option>\n                      <option value=\"bottom\">Bottom</option>\n                    </Select>\n                  ) : null}\n                </div>\n                <div\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    flexWrap: 'wrap',\n                    gap: '0.5em',\n                  }}\n                >\n                  <Input\n                    placeholder=\"Filter\"\n                    aria-label=\"Filter by queryhash\"\n                    value={filter ?? ''}\n                    onChange={(e) => setFilter(e.target.value)}\n                    onKeyDown={(e) => {\n                      if (e.key === 'Escape') setFilter('')\n                    }}\n                    style={{\n                      flex: '1',\n                      width: '100%',\n                    }}\n                  />\n                  <Select\n                    aria-label=\"Sort queries\"\n                    value={sort}\n                    onChange={(e) => setSort(e.target.value)}\n                    style={{\n                      flex: '1',\n                      minWidth: 75,\n                      marginRight: '.5em',\n                    }}\n                  >\n                    {Object.keys(sortFns).map((key) => (\n                      <option key={key} value={key}>\n                        Sort by {key}\n                      </option>\n                    ))}\n                  </Select>\n                  <Button\n                    type=\"button\"\n                    onClick={() => setBaseSort((old) => old * -1)}\n                    style={{\n                      padding: '.3em .4em',\n                      marginRight: '.5em',\n                    }}\n                  >\n                    {baseSort === 1 ? '⬆ Asc' : '⬇ Desc'}\n                  </Button>\n                  <Button\n                    title=\"Clear cache\"\n                    aria-label=\"Clear cache\"\n                    type=\"button\"\n                    onClick={() => queryCache.clear()}\n                    style={{\n                      padding: '.3em .4em',\n                      marginRight: '.5em',\n                    }}\n                  >\n                    Clear\n                  </Button>\n                  <Button\n                    type=\"button\"\n                    onClick={() => {\n                      if (isMockOffline) {\n                        onlineManager.setOnline(undefined)\n                        setMockOffline(false)\n                        window.dispatchEvent(new Event('online'))\n                      } else {\n                        onlineManager.setOnline(false)\n                        setMockOffline(true)\n                      }\n                    }}\n                    aria-label={\n                      isMockOffline\n                        ? 'Restore offline mock'\n                        : 'Mock offline behavior'\n                    }\n                    title={\n                      isMockOffline\n                        ? 'Restore offline mock'\n                        : 'Mock offline behavior'\n                    }\n                    style={{\n                      padding: '0',\n                      height: '2em',\n                    }}\n                  >\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      width=\"2em\"\n                      height=\"2em\"\n                      viewBox=\"0 0 24 24\"\n                      stroke={isMockOffline ? theme.danger : 'currentColor'}\n                      fill=\"none\"\n                    >\n                      {isMockOffline ? (\n                        <>\n                          <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                          <line x1=\"12\" y1=\"18\" x2=\"12.01\" y2=\"18\" />\n                          <path d=\"M9.172 15.172a4 4 0 0 1 5.656 0\" />\n                          <path d=\"M6.343 12.343a7.963 7.963 0 0 1 3.864 -2.14m4.163 .155a7.965 7.965 0 0 1 3.287 2\" />\n                          <path d=\"M3.515 9.515a12 12 0 0 1 3.544 -2.455m3.101 -.92a12 12 0 0 1 10.325 3.374\" />\n                          <line x1=\"3\" y1=\"3\" x2=\"21\" y2=\"21\" />\n                        </>\n                      ) : (\n                        <>\n                          <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                          <line x1=\"12\" y1=\"18\" x2=\"12.01\" y2=\"18\" />\n                          <path d=\"M9.172 15.172a4 4 0 0 1 5.656 0\" />\n                          <path d=\"M6.343 12.343a8 8 0 0 1 11.314 0\" />\n                          <path d=\"M3.515 9.515c4.686 -4.687 12.284 -4.687 17 0\" />\n                        </>\n                      )}\n                    </svg>\n                    <ScreenReader\n                      text={\n                        isMockOffline\n                          ? 'Restore offline mock'\n                          : 'Mock offline behavior'\n                      }\n                    />\n                  </Button>\n                </div>\n              </div>\n            </div>\n            <div\n              style={{\n                overflowY: 'auto',\n                flex: '1',\n              }}\n            >\n              {queries.map((query) => {\n                return (\n                  <QueryRow\n                    queryKey={query.queryKey}\n                    activeQueryHash={activeQueryHash}\n                    setActiveQueryHash={setActiveQueryHash}\n                    key={query.queryHash}\n                    queryCache={queryCache}\n                  />\n                )\n              })}\n            </div>\n          </div>\n        )}\n\n        {activeQueryHash && isOpen ? (\n          <ActiveQuery\n            activeQueryHash={activeQueryHash}\n            queryCache={queryCache}\n            queryClient={queryClient}\n            errorTypes={errorTypes}\n          />\n        ) : null}\n\n        {showCloseButton ? (\n          <Button\n            type=\"button\"\n            aria-controls=\"ReactQueryDevtoolsPanel\"\n            aria-haspopup=\"true\"\n            aria-expanded=\"true\"\n            {...(otherCloseButtonProps as Record<string, unknown>)}\n            style={{\n              position: 'absolute',\n              zIndex: 99999,\n              margin: '.5em',\n              bottom: 0,\n              left: 0,\n              ...otherCloseButtonProps.style,\n            }}\n            onClick={(e) => {\n              setIsOpen(false)\n              onCloseClick?.(e)\n            }}\n          >\n            Close\n          </Button>\n        ) : null}\n      </Panel>\n    </ThemeProvider>\n  )\n})\n\nconst ActiveQuery = ({\n  queryCache,\n  activeQueryHash,\n  queryClient,\n  errorTypes,\n}: {\n  queryCache: QueryCache\n  activeQueryHash: string\n  queryClient: QueryClient\n  errorTypes: DevToolsErrorType[]\n}) => {\n  const activeQuery = useSubscribeToQueryCache(queryCache, () =>\n    queryCache.getAll().find((query) => query.queryHash === activeQueryHash),\n  )\n\n  const activeQueryState = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().find((query) => query.queryHash === activeQueryHash)\n        ?.state,\n  )\n\n  const isStale =\n    useSubscribeToQueryCache(queryCache, () =>\n      queryCache\n        .getAll()\n        .find((query) => query.queryHash === activeQueryHash)\n        ?.isStale(),\n    ) ?? false\n\n  const observerCount =\n    useSubscribeToQueryCache(queryCache, () =>\n      queryCache\n        .getAll()\n        .find((query) => query.queryHash === activeQueryHash)\n        ?.getObserversCount(),\n    ) ?? 0\n\n  const handleRefetch = () => {\n    const promise = activeQuery?.fetch()\n    promise?.catch(noop)\n  }\n\n  const currentErrorTypeName = useMemo(() => {\n    if (activeQuery && activeQueryState?.error) {\n      const errorType = errorTypes.find(\n        (type) =>\n          type.initializer(activeQuery).toString() ===\n          activeQueryState.error?.toString(),\n      )\n      return errorType?.name\n    }\n    return undefined\n  }, [activeQuery, activeQueryState?.error, errorTypes])\n\n  if (!activeQuery || !activeQueryState) {\n    return null\n  }\n\n  const triggerError = (errorType?: DevToolsErrorType) => {\n    const error =\n      errorType?.initializer(activeQuery) ??\n      new Error('Unknown error from devtools')\n\n    const __previousQueryOptions = activeQuery.options\n\n    activeQuery.setState({\n      status: 'error',\n      error,\n      fetchMeta: {\n        ...activeQuery.state.fetchMeta,\n        __previousQueryOptions,\n      },\n    })\n  }\n\n  const restoreQueryAfterLoadingOrError = () => {\n    activeQuery.fetch(activeQuery.state.fetchMeta.__previousQueryOptions, {\n      // Make sure this fetch will cancel the previous one\n      cancelRefetch: true,\n    })\n  }\n\n  return (\n    <ActiveQueryPanel>\n      <div\n        style={{\n          padding: '.5em',\n          background: theme.backgroundAlt,\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Query Details\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <div\n          style={{\n            marginBottom: '.5em',\n            display: 'flex',\n            alignItems: 'flex-start',\n            justifyContent: 'space-between',\n          }}\n        >\n          <Code\n            style={{\n              lineHeight: '1.8em',\n            }}\n          >\n            <pre\n              style={{\n                margin: 0,\n                padding: 0,\n                overflow: 'auto',\n              }}\n            >\n              {displayValue(activeQuery.queryKey, true)}\n            </pre>\n          </Code>\n          <span\n            style={{\n              padding: '0.3em .6em',\n              borderRadius: '0.4em',\n              fontWeight: 'bold',\n              textShadow: '0 2px 10px black',\n              background: getQueryStatusColor({\n                queryState: activeQueryState,\n                isStale: isStale,\n                observerCount: observerCount,\n                theme,\n              }),\n              flexShrink: 0,\n            }}\n          >\n            {getQueryStatusLabel(activeQuery)}\n          </span>\n        </div>\n        <div\n          style={{\n            marginBottom: '.5em',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n          }}\n        >\n          Observers: <Code>{observerCount}</Code>\n        </div>\n        <div\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n          }}\n        >\n          Last Updated:{' '}\n          <Code>\n            {new Date(activeQueryState.dataUpdatedAt).toLocaleTimeString()}\n          </Code>\n        </div>\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Actions\n      </div>\n      <div\n        style={{\n          padding: '0.5em',\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: '0.5em',\n          alignItems: 'flex-end',\n        }}\n      >\n        <Button\n          type=\"button\"\n          onClick={handleRefetch}\n          disabled={activeQueryState.fetchStatus === 'fetching'}\n          style={{\n            background: theme.active,\n          }}\n        >\n          Refetch\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.invalidateQueries(activeQuery)}\n          style={{\n            background: theme.warning,\n            color: theme.inputTextColor,\n          }}\n        >\n          Invalidate\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.resetQueries(activeQuery)}\n          style={{\n            background: theme.gray,\n          }}\n        >\n          Reset\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.removeQueries(activeQuery)}\n          style={{\n            background: theme.danger,\n          }}\n        >\n          Remove\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => {\n            // Return early if the query is already restoring\n            if (\n              activeQuery.state.fetchStatus === 'fetching' &&\n              typeof activeQuery.state.fetchMeta?.__previousQueryOptions ===\n                'undefined'\n            ) {\n              return\n            }\n\n            if (activeQuery.state.data === undefined) {\n              restoreQueryAfterLoadingOrError()\n            } else {\n              const __previousQueryOptions = activeQuery.options\n              // Trigger a fetch in order to trigger suspense as well.\n              activeQuery.fetch({\n                ...__previousQueryOptions,\n                queryFn: () => {\n                  return new Promise(() => {\n                    // Never resolve\n                  })\n                },\n                cacheTime: -1,\n              })\n              activeQuery.setState({\n                data: undefined,\n                status: 'loading',\n                fetchMeta: {\n                  ...activeQuery.state.fetchMeta,\n                  __previousQueryOptions,\n                },\n              })\n            }\n          }}\n          style={{\n            background: theme.paused,\n          }}\n        >\n          {activeQuery.state.status === 'loading' ? 'Restore' : 'Trigger'}{' '}\n          loading\n        </Button>{' '}\n        {errorTypes.length === 0 || activeQuery.state.status === 'error' ? (\n          <Button\n            type=\"button\"\n            onClick={() => {\n              if (!activeQuery.state.error) {\n                triggerError()\n              } else {\n                queryClient.resetQueries(activeQuery)\n              }\n            }}\n            style={{\n              background: theme.danger,\n            }}\n          >\n            {activeQuery.state.status === 'error' ? 'Restore' : 'Trigger'} error\n          </Button>\n        ) : (\n          <label>\n            Trigger error:\n            <Select\n              value={currentErrorTypeName ?? ''}\n              style={{ marginInlineStart: '.5em' }}\n              onChange={(e) => {\n                const errorType = errorTypes.find(\n                  (t) => t.name === e.target.value,\n                )\n\n                triggerError(errorType)\n              }}\n            >\n              <option key=\"\" value=\"\" />\n              {errorTypes.map((errorType) => (\n                <option key={errorType.name} value={errorType.name}>\n                  {errorType.name}\n                </option>\n              ))}\n            </Select>\n          </label>\n        )}\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Data Explorer\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <Explorer\n          label=\"Data\"\n          value={activeQueryState.data}\n          defaultExpanded={{}}\n          copyable\n        />\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Query Explorer\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <Explorer\n          label=\"Query\"\n          value={activeQuery}\n          defaultExpanded={{\n            queryKey: true,\n          }}\n        />\n      </div>\n    </ActiveQueryPanel>\n  )\n}\n\nconst QueryStatusCount = ({ queryCache }: { queryCache: QueryCache }) => {\n  const hasFresh = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'fresh')\n        .length,\n  )\n  const hasFetching = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'fetching')\n        .length,\n  )\n  const hasPaused = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'paused')\n        .length,\n  )\n  const hasStale = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'stale')\n        .length,\n  )\n  const hasInactive = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'inactive')\n        .length,\n  )\n  return (\n    <QueryKeys>\n      <QueryKey\n        style={{\n          background: theme.success,\n          opacity: hasFresh ? 1 : 0.3,\n        }}\n      >\n        fresh <Code>({hasFresh})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.active,\n          opacity: hasFetching ? 1 : 0.3,\n        }}\n      >\n        fetching <Code>({hasFetching})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.paused,\n          opacity: hasPaused ? 1 : 0.3,\n        }}\n      >\n        paused <Code>({hasPaused})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.warning,\n          color: 'black',\n          textShadow: '0',\n          opacity: hasStale ? 1 : 0.3,\n        }}\n      >\n        stale <Code>({hasStale})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.gray,\n          opacity: hasInactive ? 1 : 0.3,\n        }}\n      >\n        inactive <Code>({hasInactive})</Code>\n      </QueryKey>\n    </QueryKeys>\n  )\n}\n\ninterface QueryRowProps {\n  queryKey: QueryKeyType\n  setActiveQueryHash: (hash: string) => void\n  activeQueryHash?: string\n  queryCache: QueryCache\n}\n\nconst QueryRow = React.memo(\n  ({\n    queryKey,\n    setActiveQueryHash,\n    activeQueryHash,\n    queryCache,\n  }: QueryRowProps) => {\n    const queryHash =\n      useSubscribeToQueryCache(\n        queryCache,\n        () => queryCache.find(queryKey)?.queryHash,\n      ) ?? ''\n\n    const queryState = useSubscribeToQueryCache(\n      queryCache,\n      () => queryCache.find(queryKey)?.state,\n    )\n\n    const isStale =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.isStale(),\n      ) ?? false\n\n    const isDisabled =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.isDisabled(),\n      ) ?? false\n\n    const observerCount =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.getObserversCount(),\n      ) ?? 0\n\n    if (!queryState) {\n      return null\n    }\n\n    return (\n      <div\n        role=\"button\"\n        aria-label={`Open query details for ${queryHash}`}\n        onClick={() =>\n          setActiveQueryHash(activeQueryHash === queryHash ? '' : queryHash)\n        }\n        style={{\n          display: 'flex',\n          borderBottom: `solid 1px ${theme.grayAlt}`,\n          cursor: 'pointer',\n          background:\n            queryHash === activeQueryHash ? 'rgba(255,255,255,.1)' : undefined,\n        }}\n      >\n        <div\n          style={{\n            flex: '0 0 auto',\n            width: '2em',\n            height: '2em',\n            background: getQueryStatusColor({\n              queryState,\n              isStale,\n              observerCount,\n              theme,\n            }),\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontWeight: 'bold',\n            textShadow: isStale ? '0' : '0 0 10px black',\n            color: isStale ? 'black' : 'white',\n          }}\n        >\n          {observerCount}\n        </div>\n        {isDisabled ? (\n          <div\n            style={{\n              flex: '0 0 auto',\n              height: '2em',\n              background: theme.gray,\n              display: 'flex',\n              alignItems: 'center',\n              fontWeight: 'bold',\n              padding: '0 0.5em',\n            }}\n          >\n            disabled\n          </div>\n        ) : null}\n        <Code\n          style={{\n            padding: '.5em',\n          }}\n        >\n          {`${queryHash}`}\n        </Code>\n      </div>\n    )\n  },\n)\n\nQueryRow.displayName = 'QueryRow'\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n", "'use client'\n\nimport * as devtools from './devtools'\n\nexport const ReactQueryDevtools: typeof devtools['ReactQueryDevtools'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : devtools.ReactQueryDevtools\n\nexport const ReactQueryDevtoolsPanel: typeof devtools['ReactQueryDevtoolsPanel'] =\n  process.env.NODE_ENV !== 'development'\n    ? (function () {\n        return null\n      } as any)\n    : devtools.ReactQueryDevtoolsPanel\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,SAAS,WAAW;AAClB,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AAExB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;;;;;;;ACfA,IAAMA,eAAuC;EAC3CC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,GAAG;EACHC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;AACN;AAEA,IAAMC,QAAQC,OAAOC,KAAKlZ,YAAY,EAAEmZ,KAAK,GAAG;AAChD,IAAMC,aAAa,IAAIC,OAAOL,OAAO,GAAG;AAEjC,SAASM,cAAcC,KAAa;AACzC,SAAOA,IAAIC,QAAQJ,YAAYK,WAAS;AACtC,WAAOzZ,aAAayZ,KAAK;EAC3B,CAAC;AACH;ACjWO,IAAMC,WAAW;EACtBC,sBAAsB;EACtBC,OAAO;EACPC,aAAa;EACbC,kBAAkB;EAClBC,UAAU;EACVC,SAAS;EACTC,SAAS;EACTC,UAAU;AACZ;AAYO,SAASC,SACdC,MACAC,OACAC,SACa;AAAA,MAAAC;AACbD,YAAUA,WAAW,CAAA;AAErBA,UAAQE,aAASD,qBAAGD,QAAQE,cAASD,OAAAA,qBAAIb,SAASO;AAElD,MAAI,CAACK,QAAQG,WAAW;AAEtB,UAAMC,OAAOC,gBAAgBP,MAA2BC,OAAOC,OAAO;AACtE,WAAO;;MAELM,aAAaR;MACbM;MACAG,eAAe;MACfC,mBAAmBR,QAAQE;MAC3BO,QAAQL,QAAQJ,QAAQE;;EAE5B;AAEA,QAAMQ,eAAeC,mBAAmBb,MAAME,QAAQG,SAAS;AAE/D,QAAMS,cAA2B;IAC/BN,aAAaR;IACbM,MAAMhB,SAASQ;IACfW,eAAe;IACfC,mBAAmBR,QAAQE;IAC3BO,QAAQ;;AAGV,WAASI,IAAI,GAAGA,IAAIH,aAAaI,QAAQD,KAAK;AAC5C,UAAME,YAAYL,aAAaG,CAAC;AAEhC,QAAIG,UAAUX,gBAAgBU,UAAUE,WAAWlB,OAAOC,OAAO;AAEjE,UAAM;MACJkB;MACAC;MACAjB,YAAYF,QAAQE;QAClBa,UAAUK;AAEd,QAAIJ,UAAUE,cAAcF,WAAW5B,SAASO,SAAS;AACvDqB,gBAAUE;IACZ,WAAWF,UAAUG,YAAY;AAC/BH,gBAAUG;IACZ;AAEAH,cAAUK,KAAKC,IAAIN,SAASG,UAAU;AAEtC,QAAIH,WAAWd,aAAac,UAAUJ,YAAYR,MAAM;AACtDQ,kBAAYR,OAAOY;AACnBJ,kBAAYH,SAAS;AACrBG,kBAAYL,gBAAgBM;AAC5BD,kBAAYJ,oBAAoBN;AAChCU,kBAAYN,cAAcS,UAAUE;IACtC;EACF;AAEA,SAAOL;AACT;AASA,SAASP,gBACPkB,YACAC,cACAxB,SACS;AACTuB,eAAaE,0BAA0BF,YAAYvB,OAAO;AAC1DwB,iBAAeC,0BAA0BD,cAAcxB,OAAO;AAG9D,MAAIwB,aAAaV,SAASS,WAAWT,QAAQ;AAC3C,WAAO1B,SAASQ;EAClB;AAGA,MAAI2B,eAAeC,cAAc;AAC/B,WAAOpC,SAASC;EAClB;AAGAkC,eAAaA,WAAWG,YAAW;AACnCF,iBAAeA,aAAaE,YAAW;AAGvC,MAAIH,eAAeC,cAAc;AAC/B,WAAOpC,SAASE;EAClB;AAGA,MAAIiC,WAAWI,WAAWH,YAAY,GAAG;AACvC,WAAOpC,SAASG;EAClB;AAGA,MAAIgC,WAAWK,SAAU,IAAGJ,cAAc,GAAG;AAC3C,WAAOpC,SAASI;EAClB;AAGA,MAAI+B,WAAWK,SAASJ,YAAY,GAAG;AACrC,WAAOpC,SAASK;EAClB,WAAW+B,aAAaV,WAAW,GAAG;AAIpC,WAAO1B,SAASQ;EAClB;AAGA,MAAIiC,WAAWN,UAAU,EAAEK,SAASJ,YAAY,GAAG;AACjD,WAAOpC,SAASM;EAClB;AAIA,SAAOoC,oBAAoBP,YAAYC,YAAY;AACrD;AAQA,SAASK,WAAWE,QAAwB;AAC1C,MAAIC,UAAU;AACd,QAAMC,gBAAgBF,OAAOG,MAAM,GAAG;AACtCD,gBAAcE,QAAQC,kBAAgB;AACpC,UAAMC,qBAAqBD,aAAaF,MAAM,GAAG;AACjDG,uBAAmBF,QAAQG,uBAAqB;AAC9CN,iBAAWM,kBAAkBC,OAAO,GAAG,CAAC;IAC1C,CAAC;EACH,CAAC;AACD,SAAOP;AACT;AAYA,SAASF,oBACPP,YACAC,cACS;AACT,MAAIgB,2BAA2B;AAC/B,MAAIC,aAAa;AACjB,WAASC,sBACPC,WACAZ,QACAa,OACA;AACA,aAASC,IAAID,OAAOE,IAAIf,OAAOjB,QAAQ+B,IAAIC,GAAGD,KAAK;AACjD,YAAME,aAAahB,OAAOc,CAAC;AAC3B,UAAIE,eAAeJ,WAAW;AAC5BH,oCAA4B;AAC5B,eAAOK,IAAI;MACb;IACF;AACA,WAAO;EACT;AACA,WAASG,WAAWC,SAAgB;AAClC,UAAMC,mBAAmB,IAAID;AAC7B,UAAME,oBAAoBX,2BAA2BhB,aAAaV;AAClE,UAAMsC,UAAUhE,SAASO,UAAUwD,oBAAoBD;AACvD,WAAOE;EACT;AACA,QAAMC,aAAaX,sBAAsBlB,aAAa,CAAC,GAAGD,YAAY,CAAC;AACvE,MAAI8B,aAAa,GAAG;AAClB,WAAOjE,SAASQ;EAClB;AACA6C,eAAaY;AACb,WAASxC,IAAI,GAAGyC,IAAI9B,aAAaV,QAAQD,IAAIyC,GAAGzC,KAAK;AACnD,UAAM8B,YAAYnB,aAAaX,CAAC;AAChC4B,iBAAaC,sBAAsBC,WAAWpB,YAAYkB,UAAU;AACpE,UAAMc,QAAQd,aAAa;AAC3B,QAAI,CAACc,OAAO;AACV,aAAOnE,SAASQ;IAClB;EACF;AAEA,QAAMqD,SAASR,aAAaY;AAC5B,SAAOL,WAAWC,MAAM;AAC1B;AAkBA,SAASO,0BACPC,OAAaC,MAEL;AAAA,MADR;IAAEC;EAAuC,IAACD;AAI1CD,UAAS,GAAEA;AACX,MAAI,CAACE,gBAAgB;AACnBF,YAAQG,cAAcH,KAAK;EAC7B;AACA,SAAOA;AACT;AAQA,SAASI,cACPC,MACAC,UACe;AACf,MAAIC,aAAaD;AAEjB,MAAI,OAAOA,aAAa,UAAU;AAChCC,iBAAaD,SAASA;EACxB;AAEA,QAAMN,QAAQO,WAAWF,IAAI;AAG7B,MAAIL,SAAS,MAAM;AACjB,WAAO,CAAA;EACT;AAEA,MAAIQ,MAAMC,QAAQT,KAAK,GAAG;AACxB,WAAOA;EACT;AAEA,SAAO,CAACU,OAAOV,KAAK,CAAC;AACvB;AAQA,SAASW,mBACPN,MACAO,WACA;AACA,QAAMC,YAGD,CAAA;AACL,WAASC,IAAI,GAAGC,IAAIH,UAAUI,QAAQF,IAAIC,GAAGD,KAAK;AAChD,UAAMR,WAAWM,UAAUE,CAAC;AAC5B,UAAMG,aAAaC,sBAAsBZ,QAAQ;AACjD,UAAMa,aAAaf,cAAcC,MAAMC,QAAQ;AAC/C,aAASc,IAAI,GAAGC,IAAIF,WAAWH,QAAQI,IAAIC,GAAGD,KAAK;AACjDP,gBAAUS,KAAK;QACbC,WAAWJ,WAAWC,CAAC;QACvBH;MACF,CAAC;IACH;EACF;AACA,SAAOJ;AACT;AAEA,IAAMW,uBAAuB;EAC3BC,YAAYC;EACZC,YAAY;AACd;AAMA,SAAST,sBACPZ,UACoB;AACpB,MAAI,OAAOA,aAAa,YAAY;AAClC,WAAOkB;EACT;AACA,SAAO;IAAE,GAAGA;IAAsB,GAAGlB;;AACvC;;;;ACzXA,IAAMsB,UAAWC,SAAyB;AACxC,MAAI;AACF,UAAMC,YAAYC,aAAaH,QAAQC,GAArB;AAClB,QAAI,OAAOC,cAAc,UAAU;AACjC,aAAOE,KAAKC,MAAMH,SAAX;IACR;AACD,WAAOI;EACR,QAAC;AACA,WAAOA;EACR;AACF;AAEc,SAASC,gBACtBN,KACAO,cAC4D;AAC5D,QAAM,CAACC,OAAOC,QAAR,IAA0BC,eAAN;AAE1BC,EAAMC,gBAAU,MAAM;AACpB,UAAMC,eAAed,QAAQC,GAAD;AAE5B,QAAI,OAAOa,iBAAiB,eAAeA,iBAAiB,MAAM;AAChEJ,eACE,OAAOF,iBAAiB,aAAaA,aAAY,IAAKA,YADhD;IAGT,OAAM;AACLE,eAASI,YAAD;IACT;EACF,GAAE,CAACN,cAAcP,GAAf,CAVH;AAYA,QAAMc,SAAeC,kBAClBC,aAAiB;AAChBP,aAAUQ,SAAQ;AAChB,UAAIC,SAASF;AAEb,UAAI,OAAOA,WAAW,YAAY;AAChCE,iBAASF,QAAQC,GAAD;MACjB;AACD,UAAI;AACFf,qBAAaiB,QAAQnB,KAAKG,KAAKiB,UAAUF,MAAf,CAA1B;cACA;MAAM;AAER,aAAOA;IACR,CAXO;EAYT,GACD,CAAClB,GAAD,CAfa;AAkBf,SAAO,CAACQ,OAAOM,MAAR;AACR;;;;;;ACnDD,IAAA;;EAAA,WAAA;AAAA,aAAAO,mBAAA;AACE,WAAA,aAAa,oBAAI,IAAG;AACpB,WAAA,aAAa,oBAAI,IAAG;IAmBtB;AAjBE,IAAAA,iBAAA,UAAA,MAAA,SAAI,KAAQ,OAAQ;AAClB,WAAK,WAAW,IAAI,KAAK,KAAK;AAC9B,WAAK,WAAW,IAAI,OAAO,GAAG;IAChC;AAEA,IAAAA,iBAAA,UAAA,WAAA,SAAS,KAAM;AACb,aAAO,KAAK,WAAW,IAAI,GAAG;IAChC;AAEA,IAAAA,iBAAA,UAAA,aAAA,SAAW,OAAQ;AACjB,aAAO,KAAK,WAAW,IAAI,KAAK;IAClC;AAEA,IAAAA,iBAAA,UAAA,QAAA,WAAA;AACE,WAAK,WAAW,MAAK;AACrB,WAAK,WAAW,MAAK;IACvB;AACF,WAAAA;EAAA,EArBA;;;;ACEA,IAAA;;EAAA,WAAA;AAGE,aAAAC,UAA6B,oBAAoC;AAApC,WAAA,qBAAA;AAFrB,WAAA,KAAK,IAAI,gBAAe;IAEoC;AAEpE,IAAAA,UAAA,UAAA,WAAA,SAAS,OAAU,YAAmB;AACpC,UAAI,KAAK,GAAG,WAAW,KAAK,GAAG;AAC7B;;AAGF,UAAI,CAAC,YAAY;AACf,qBAAa,KAAK,mBAAmB,KAAK;;AAG5C,WAAK,GAAG,IAAI,YAAY,KAAK;IAC/B;AAEA,IAAAA,UAAA,UAAA,QAAA,WAAA;AACE,WAAK,GAAG,MAAK;IACf;AAEA,IAAAA,UAAA,UAAA,gBAAA,SAAc,OAAQ;AACpB,aAAO,KAAK,GAAG,WAAW,KAAK;IACjC;AAEA,IAAAA,UAAA,UAAA,WAAA,SAAS,YAAkB;AACzB,aAAO,KAAK,GAAG,SAAS,UAAU;IACpC;AACF,WAAAA;EAAA,EA5BA;;;;;;;;;;;;;;;;;;;;;;;;;ACMA,IAAA;;EAAA,SAAA,QAAA;AAAmC,cAAAC,gBAAA,MAAA;AACjC,aAAAA,iBAAA;AAAA,UAAA,QACE,OAAA,KAAA,MAAM,SAAA,GAAC;AAAI,eAAA,EAAE;MAAF,CAAM,KAAC;AAGZ,YAAA,sBAAsB,oBAAI,IAAG;;IAFrC;AAIA,IAAAA,eAAA,UAAA,WAAA,SAAS,OAAc,SAAkC;AACvD,UAAI,OAAO,YAAY,UAAU;AAC/B,YAAI,QAAQ,YAAY;AACtB,eAAK,oBAAoB,IAAI,OAAO,QAAQ,UAAU;;AAGxD,eAAA,UAAM,SAAQ,KAAA,MAAC,OAAO,QAAQ,UAAU;aACnC;AACL,eAAA,UAAM,SAAQ,KAAA,MAAC,OAAO,OAAO;;IAEjC;AAEA,IAAAA,eAAA,UAAA,kBAAA,SAAgB,OAAY;AAC1B,aAAO,KAAK,oBAAoB,IAAI,KAAK;IAC3C;AACF,WAAAA;EAAA,EAtBmC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;ACR3C,SAAS,YAAe,QAAyB;AAC/C,MAAI,YAAY,QAAQ;AAEtB,WAAO,OAAO,OAAO,MAAM;;AAG7B,MAAM,SAAc,CAAA;AAGpB,WAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,aAAO,KAAK,OAAO,GAAG,CAAC;;;AAI3B,SAAO;AACT;AAEM,SAAU,KACd,QACA,WAA4B;AAE5B,MAAM,SAAS,YAAY,MAAM;AACjC,MAAI,UAAU,QAAQ;AAEpB,WAAO,OAAO,KAAK,SAAS;;AAG9B,MAAM,iBAAiB;AAEvB,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,QAAM,QAAQ,eAAe,CAAC;AAC9B,QAAI,UAAU,KAAK,GAAG;AACpB,aAAO;;;AAIX,SAAO;AACT;AAEM,SAAU,QACd,QACA,KAAgC;AAEhC,SAAO,QAAQ,MAAM,EAAE,QAAQ,SAAC,IAAY;QAAZ,KAAA,OAAA,IAAA,CAAA,GAAC,MAAG,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AAAM,WAAA,IAAI,OAAO,GAAG;EAAd,CAAe;AAClE;AAEM,SAAU,SAAY,KAAU,OAAQ;AAC5C,SAAO,IAAI,QAAQ,KAAK,MAAM;AAChC;AAEM,SAAU,QACd,QACA,WAA4B;AAE5B,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAM,QAAQ,OAAO,CAAC;AACtB,QAAI,UAAU,KAAK,GAAG;AACpB,aAAO;;;AAIX,SAAO;AACT;;;ACrDA,IAAA;;EAAA,WAAA;AAAA,aAAAC,6BAAA;AACU,WAAA,cAA0D,CAAA;IAepE;AAbE,IAAAA,2BAAA,UAAA,WAAA,SAAiC,aAAmC;AAClE,WAAK,YAAY,YAAY,IAAI,IAAI;IACvC;AAEA,IAAAA,2BAAA,UAAA,iBAAA,SAAkB,GAAI;AACpB,aAAO,KAAK,KAAK,aAAa,SAAA,aAAW;AACvC,eAAA,YAAY,aAAa,CAAC;MAA1B,CAA2B;IAE/B;AAEA,IAAAA,2BAAA,UAAA,aAAA,SAAW,MAAY;AACrB,aAAO,KAAK,YAAY,IAAI;IAC9B;AACF,WAAAA;EAAA,EAhBA;;;;ACVA,IAAM,UAAU,SAAC,SAAY;AAC3B,SAAA,OAAO,UAAU,SAAS,KAAK,OAAO,EAAE,MAAM,GAAG,EAAE;AAAnD;AAEK,IAAM,cAAc,SAAC,SAAY;AACtC,SAAA,OAAO,YAAY;AAAnB;AAEK,IAAM,SAAS,SAAC,SAAY;AAAsB,SAAA,YAAY;AAAZ;AAElD,IAAM,gBAAgB,SAC3B,SAAY;AAEZ,MAAI,OAAO,YAAY,YAAY,YAAY;AAAM,WAAO;AAC5D,MAAI,YAAY,OAAO;AAAW,WAAO;AACzC,MAAI,OAAO,eAAe,OAAO,MAAM;AAAM,WAAO;AAEpD,SACE,QAAQ,gBAAgB,UACxB,OAAO,eAAe,OAAO,MAAM,OAAO;AAE9C;AAEO,IAAM,gBAAgB,SAAC,SAAY;AACxC,SAAA,cAAc,OAAO,KAAK,OAAO,KAAK,OAAO,EAAE,WAAW;AAA1D;AAEK,IAAM,UAAU,SAAC,SAAY;AAClC,SAAA,MAAM,QAAQ,OAAO;AAArB;AAEK,IAAM,WAAW,SAAC,SAAY;AACnC,SAAA,OAAO,YAAY;AAAnB;AAEK,IAAM,WAAW,SAAC,SAAY;AACnC,SAAA,OAAO,YAAY,YAAY,CAAC,MAAM,OAAO;AAA7C;AAEK,IAAM,YAAY,SAAC,SAAY;AACpC,SAAA,OAAO,YAAY;AAAnB;AAEK,IAAM,WAAW,SAAC,SAAY;AACnC,SAAA,mBAAmB;AAAnB;AAEK,IAAM,QAAQ,SAAC,SAAY;AAChC,SAAA,mBAAmB;AAAnB;AAEK,IAAM,QAAQ,SAAC,SAAY;AAChC,SAAA,mBAAmB;AAAnB;AAEK,IAAM,WAAW,SAAC,SAAY;AACnC,SAAA,QAAQ,OAAO,MAAM;AAArB;AAEK,IAAM,SAAS,SAAC,SAAY;AACjC,SAAA,mBAAmB,QAAQ,CAAC,MAAM,QAAQ,QAAO,CAAE;AAAnD;AAEK,IAAM,UAAU,SAAC,SAAY;AAClC,SAAA,mBAAmB;AAAnB;AAEK,IAAM,aAAa,SAAC,SAAY;AACrC,SAAA,OAAO,YAAY,YAAY,MAAM,OAAO;AAA5C;AAEK,IAAM,cAAc,SACzB,SAAY;AAEZ,SAAA,UAAU,OAAO,KACjB,OAAO,OAAO,KACd,YAAY,OAAO,KACnB,SAAS,OAAO,KAChB,SAAS,OAAO,KAChB,SAAS,OAAO;AALhB;AAOK,IAAM,WAAW,SAAC,SAAY;AACnC,SAAA,OAAO,YAAY;AAAnB;AAEK,IAAM,aAAa,SAAC,SAAY;AACrC,SAAA,YAAY,YAAY,YAAY;AAApC;AAeK,IAAM,eAAe,SAAC,SAAY;AACvC,SAAA,YAAY,OAAO,OAAO,KAAK,EAAE,mBAAmB;AAApD;AAEK,IAAM,QAAQ,SAAC,SAAY;AAAqB,SAAA,mBAAmB;AAAnB;;;ACtFhD,IAAM,YAAY,SAAC,KAAW;AAAK,SAAA,IAAI,QAAQ,OAAO,KAAK;AAAxB;AAEnC,IAAM,gBAAgB,SAAC,MAAU;AACtC,SAAA,KACG,IAAI,MAAM,EACV,IAAI,SAAS,EACb,KAAK,GAAG;AAHX;AAKK,IAAM,YAAY,SAAC,QAAuB;AAC/C,MAAM,SAAmB,CAAA;AAEzB,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,OAAO,OAAO,OAAO,CAAC;AAE1B,QAAM,eAAe,SAAS,QAAQ,OAAO,OAAO,IAAI,CAAC,MAAM;AAC/D,QAAI,cAAc;AAChB,iBAAW;AACX;AACA;;AAGF,QAAM,iBAAiB,SAAS;AAChC,QAAI,gBAAgB;AAClB,aAAO,KAAK,OAAO;AACnB,gBAAU;AACV;;AAGF,eAAW;;AAGb,MAAM,cAAc;AACpB,SAAO,KAAK,WAAW;AAEvB,SAAO;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACIA,SAAS,qBACP,cACA,YACA,WACA,aAA8C;AAE9C,SAAO;IACL;IACA;IACA;IACA;;AAEJ;AAEA,IAAM,cAAc;EAClB,qBACE,aACA,aACA,WAAA;AAAM,WAAA;EAAA,GACN,WAAA;AAAM,WAAA;EAAA,CAAS;EAEjB,qBACE,UACA,UACA,SAAA,GAAC;AAAI,WAAA,EAAE,SAAQ;EAAV,GACL,SAAA,GAAC;AACC,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,OAAO,CAAC;;AAGjB,YAAQ,MAAM,+BAA+B;AAE7C,WAAO;EACT,CAAC;EAEH,qBACE,QACA,QACA,SAAA,GAAC;AAAI,WAAA,EAAE,YAAW;EAAb,GACL,SAAA,GAAC;AAAI,WAAA,IAAI,KAAK,CAAC;EAAV,CAAW;EAGlB,qBACE,SACA,SACA,SAAC,GAAG,WAAS;AACX,QAAM,YAAiB;MACrB,MAAM,EAAE;MACR,SAAS,EAAE;;AAGb,cAAU,kBAAkB,QAAQ,SAAA,MAAI;AACtC,gBAAU,IAAI,IAAK,EAAU,IAAI;IACnC,CAAC;AAED,WAAO;EACT,GACA,SAAC,GAAG,WAAS;AACX,QAAM,IAAI,IAAI,MAAM,EAAE,OAAO;AAC7B,MAAE,OAAO,EAAE;AACX,MAAE,QAAQ,EAAE;AAEZ,cAAU,kBAAkB,QAAQ,SAAA,MAAI;AACrC,QAAU,IAAI,IAAI,EAAE,IAAI;IAC3B,CAAC;AAED,WAAO;EACT,CAAC;EAGH,qBACE,UACA,UACA,SAAA,GAAC;AAAI,WAAA,KAAK;EAAL,GACL,SAAA,OAAK;AACH,QAAM,OAAO,MAAM,MAAM,GAAG,MAAM,YAAY,GAAG,CAAC;AAClD,QAAM,QAAQ,MAAM,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC;AACpD,WAAO,IAAI,OAAO,MAAM,KAAK;EAC/B,CAAC;EAGH;IACE;IACA;;;IAGA,SAAA,GAAC;AAAI,aAAA,cAAA,CAAA,GAAAC,QAAI,EAAE,OAAM,CAAE,CAAA;IAAd;IACL,SAAA,GAAC;AAAI,aAAA,IAAI,IAAI,CAAC;IAAT;EAAU;EAEjB,qBACE,OACA,OACA,SAAA,GAAC;AAAI,WAAA,cAAA,CAAA,GAAAA,QAAI,EAAE,QAAO,CAAE,CAAA;EAAf,GACL,SAAA,GAAC;AAAI,WAAA,IAAI,IAAI,CAAC;EAAT,CAAU;EAGjB,qBACE,SAAC,GAAC;AAAkB,WAAA,WAAW,CAAC,KAAK,WAAW,CAAC;EAA7B,GACpB,UACA,SAAA,GAAC;AACC,QAAI,WAAW,CAAC,GAAG;AACjB,aAAO;;AAGT,QAAI,IAAI,GAAG;AACT,aAAO;WACF;AACL,aAAO;;EAEX,GACA,MAAM;EAGR,qBACE,SAAC,GAAC;AAAkB,WAAA,MAAM,KAAK,IAAI,MAAM;EAArB,GACpB,UACA,WAAA;AACE,WAAO;EACT,GACA,MAAM;EAGR,qBACE,OACA,OACA,SAAA,GAAC;AAAI,WAAA,EAAE,SAAQ;EAAV,GACL,SAAA,GAAC;AAAI,WAAA,IAAI,IAAI,CAAC;EAAT,CAAU;;AAInB,SAAS,wBACP,cACA,YACA,WACA,aAAoD;AAEpD,SAAO;IACL;IACA;IACA;IACA;;AAEJ;AAEA,IAAM,aAAa,wBACjB,SAAC,GAAG,WAAS;AACX,MAAI,SAAS,CAAC,GAAG;AACf,QAAM,eAAe,CAAC,CAAC,UAAU,eAAe,cAAc,CAAC;AAC/D,WAAO;;AAET,SAAO;AACT,GACA,SAAC,GAAG,WAAS;AACX,MAAM,aAAa,UAAU,eAAe,cAAc,CAAC;AAC3D,SAAO,CAAC,UAAU,UAAW;AAC/B,GACA,SAAA,GAAC;AAAI,SAAA,EAAE;AAAF,GACL,SAAC,GAAG,GAAG,WAAS;AACd,MAAM,QAAQ,UAAU,eAAe,SAAS,EAAE,CAAC,CAAC;AACpD,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,sCAAsC;;AAExD,SAAO;AACT,CAAC;AAGH,IAAM,oBAAoB;EACxB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAA8C,SAAC,KAAK,MAAI;AACxD,MAAI,KAAK,IAAI,IAAI;AACjB,SAAO;AACT,GAAG,CAAA,CAAE;AAEL,IAAM,iBAAiB,wBACrB,cACA,SAAA,GAAC;AAAI,SAAA,CAAC,eAAe,EAAE,YAAY,IAAI;AAAlC,GACL,SAAA,GAAC;AAAI,SAAA,cAAA,CAAA,GAAAA,QAAI,CAAC,CAAA;AAAL,GACL,SAAC,GAAG,GAAC;AACH,MAAM,OAAO,kBAAkB,EAAE,CAAC,CAAC;AAEnC,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,2CAA2C;;AAG7D,SAAO,IAAI,KAAK,CAAC;AACnB,CAAC;AAGG,SAAU,4BACd,gBACA,WAAoB;AAEpB,MAAI,mBAAc,QAAd,mBAAc,SAAA,SAAd,eAAgB,aAAa;AAC/B,QAAM,eAAe,CAAC,CAAC,UAAU,cAAc,cAC7C,eAAe,WAAW;AAE5B,WAAO;;AAET,SAAO;AACT;AAEA,IAAM,YAAY,wBAChB,6BACA,SAAC,OAAO,WAAS;AACf,MAAM,aAAa,UAAU,cAAc,cAAc,MAAM,WAAW;AAC1E,SAAO,CAAC,SAAS,UAAW;AAC9B,GACA,SAAC,OAAO,WAAS;AACf,MAAM,eAAe,UAAU,cAAc,gBAC3C,MAAM,WAAW;AAEnB,MAAI,CAAC,cAAc;AACjB,WAAA,SAAA,CAAA,GAAY,KAAK;;AAGnB,MAAM,SAAc,CAAA;AACpB,eAAa,QAAQ,SAAA,MAAI;AACvB,WAAO,IAAI,IAAI,MAAM,IAAI;EAC3B,CAAC;AACD,SAAO;AACT,GACA,SAAC,GAAG,GAAG,WAAS;AACd,MAAM,QAAQ,UAAU,cAAc,SAAS,EAAE,CAAC,CAAC;AAEnD,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MACR,qHAAqH;;AAIzH,SAAO,OAAO,OAAO,OAAO,OAAO,MAAM,SAAS,GAAG,CAAC;AACxD,CAAC;AAGH,IAAM,aAAa,wBACjB,SAAC,OAAO,WAAS;AACf,SAAO,CAAC,CAAC,UAAU,0BAA0B,eAAe,KAAK;AACnE,GACA,SAAC,OAAO,WAAS;AACf,MAAM,cAAc,UAAU,0BAA0B,eACtD,KAAK;AAEP,SAAO,CAAC,UAAU,YAAY,IAAI;AACpC,GACA,SAAC,OAAO,WAAS;AACf,MAAM,cAAc,UAAU,0BAA0B,eACtD,KAAK;AAEP,SAAO,YAAY,UAAU,KAAK;AACpC,GACA,SAAC,GAAG,GAAG,WAAS;AACd,MAAM,cAAc,UAAU,0BAA0B,WAAW,EAAE,CAAC,CAAC;AACvE,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,4CAA4C;;AAE9D,SAAO,YAAY,YAAY,CAAC;AAClC,CAAC;AAGH,IAAM,iBAAiB,CAAC,WAAW,YAAY,YAAY,cAAc;AAElE,IAAM,iBAAiB,SAC5B,OACA,WAAoB;AAEpB,MAAM,0BAA0B,QAAQ,gBAAgB,SAAA,MAAI;AAC1D,WAAA,KAAK,aAAa,OAAO,SAAS;EAAlC,CAAmC;AAErC,MAAI,yBAAyB;AAC3B,WAAO;MACL,OAAO,wBAAwB,UAAU,OAAgB,SAAS;MAClE,MAAM,wBAAwB,WAAW,OAAO,SAAS;;;AAI7D,MAAM,uBAAuB,QAAQ,aAAa,SAAA,MAAI;AACpD,WAAA,KAAK,aAAa,OAAO,SAAS;EAAlC,CAAmC;AAGrC,MAAI,sBAAsB;AACxB,WAAO;MACL,OAAO,qBAAqB,UAAU,OAAgB,SAAS;MAC/D,MAAM,qBAAqB;;;AAI/B,SAAO;AACT;AAEA,IAAM,0BAAiE,CAAA;AACvE,YAAY,QAAQ,SAAA,MAAI;AACtB,0BAAwB,KAAK,UAAU,IAAI;AAC7C,CAAC;AAEM,IAAM,mBAAmB,SAC9B,MACA,MACA,WAAoB;AAEpB,MAAI,QAAQ,IAAI,GAAG;AACjB,YAAQ,KAAK,CAAC,GAAG;MACf,KAAK;AACH,eAAO,WAAW,YAAY,MAAM,MAAM,SAAS;MACrD,KAAK;AACH,eAAO,UAAU,YAAY,MAAM,MAAM,SAAS;MACpD,KAAK;AACH,eAAO,WAAW,YAAY,MAAM,MAAM,SAAS;MACrD,KAAK;AACH,eAAO,eAAe,YAAY,MAAM,MAAM,SAAS;MACzD;AACE,cAAM,IAAI,MAAM,6BAA6B,IAAI;;SAEhD;AACL,QAAM,iBAAiB,wBAAwB,IAAI;AACnD,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI,MAAM,6BAA6B,IAAI;;AAGnD,WAAO,eAAe,YAAY,MAAe,SAAS;;AAE9D;;;AChXA,IAAM,YAAY,SAAC,OAAiC,GAAS;AAC3D,MAAM,OAAO,MAAM,KAAI;AACvB,SAAO,IAAI,GAAG;AACZ,SAAK,KAAI;AACT;;AAGF,SAAO,KAAK,KAAI,EAAG;AACrB;AAEA,SAAS,aAAa,MAAyB;AAC7C,MAAI,SAAS,MAAM,WAAW,GAAG;AAC/B,UAAM,IAAI,MAAM,wCAAwC;;AAE1D,MAAI,SAAS,MAAM,WAAW,GAAG;AAC/B,UAAM,IAAI,MAAM,wCAAwC;;AAE1D,MAAI,SAAS,MAAM,aAAa,GAAG;AACjC,UAAM,IAAI,MAAM,0CAA0C;;AAE9D;AAEO,IAAM,UAAU,SAAC,QAAgB,MAAyB;AAC/D,eAAa,IAAI;AAEjB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAM,MAAM,KAAK,CAAC;AAClB,QAAI,MAAM,MAAM,GAAG;AACjB,eAAS,UAAU,QAAQ,CAAC,GAAG;eACtB,MAAM,MAAM,GAAG;AACxB,UAAM,MAAM,CAAC;AACb,UAAM,OAAO,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,QAAQ;AAExC,UAAM,WAAW,UAAU,QAAQ,GAAG;AACtC,cAAQ,MAAM;QACZ,KAAK;AACH,mBAAS;AACT;QACF,KAAK;AACH,mBAAS,OAAO,IAAI,QAAQ;AAC5B;;WAEC;AACL,eAAU,OAAe,GAAG;;;AAIhC,SAAO;AACT;AAEO,IAAM,UAAU,SACrB,QACA,MACA,QAAuB;AAEvB,eAAa,IAAI;AAEjB,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO,OAAO,MAAM;;AAGtB,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,QAAM,MAAM,KAAK,CAAC;AAElB,QAAI,QAAQ,MAAM,GAAG;AACnB,UAAM,QAAQ,CAAC;AACf,eAAS,OAAO,KAAK;eACZ,cAAc,MAAM,GAAG;AAChC,eAAS,OAAO,GAAG;eACV,MAAM,MAAM,GAAG;AACxB,UAAM,MAAM,CAAC;AACb,eAAS,UAAU,QAAQ,GAAG;eACrB,MAAM,MAAM,GAAG;AACxB,UAAM,QAAQ,MAAM,KAAK,SAAS;AAClC,UAAI,OAAO;AACT;;AAGF,UAAM,MAAM,CAAC;AACb,UAAM,OAAO,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,QAAQ;AAExC,UAAM,WAAW,UAAU,QAAQ,GAAG;AACtC,cAAQ,MAAM;QACZ,KAAK;AACH,mBAAS;AACT;QACF,KAAK;AACH,mBAAS,OAAO,IAAI,QAAQ;AAC5B;;;;AAKR,MAAM,UAAU,KAAK,KAAK,SAAS,CAAC;AAEpC,MAAI,QAAQ,MAAM,GAAG;AACnB,WAAO,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC;aACjC,cAAc,MAAM,GAAG;AAChC,WAAO,OAAO,IAAI,OAAO,OAAO,OAAO,CAAC;;AAG1C,MAAI,MAAM,MAAM,GAAG;AACjB,QAAM,WAAW,UAAU,QAAQ,CAAC,OAAO;AAC3C,QAAM,WAAW,OAAO,QAAQ;AAChC,QAAI,aAAa,UAAU;AACzB,aAAO,QAAM,EAAC,QAAQ;AACtB,aAAO,IAAI,QAAQ;;;AAIvB,MAAI,MAAM,MAAM,GAAG;AACjB,QAAM,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC;AACjC,QAAM,WAAW,UAAU,QAAQ,GAAG;AAEtC,QAAM,OAAO,CAAC,YAAY,IAAI,QAAQ;AACtC,YAAQ,MAAM;MACZ,KAAK,OAAO;AACV,YAAM,SAAS,OAAO,QAAQ;AAC9B,eAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ,CAAC;AAEvC,YAAI,WAAW,UAAU;AACvB,iBAAO,QAAM,EAAC,QAAQ;;AAExB;;MAGF,KAAK,SAAS;AACZ,eAAO,IAAI,UAAU,OAAO,OAAO,IAAI,QAAQ,CAAC,CAAC;AACjD;;;;AAKN,SAAO;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjHA,SAAS,SACP,MACAC,SACA,QAAqB;AAArB,MAAA,WAAA,QAAA;AAAA,aAAA,CAAA;EAAqB;AAErB,MAAI,CAAC,MAAM;AACT;;AAGF,MAAI,CAAC,QAAQ,IAAI,GAAG;AAClB,YAAQ,MAAM,SAAC,SAAS,KAAG;AACzB,aAAA,SAAS,SAASA,SAAMC,eAAAA,eAAA,CAAA,GAAAC,QAAM,MAAM,CAAA,GAAAA,QAAK,UAAU,GAAG,CAAC,CAAA,CAAA;IAAvD,CAAyD;AAE3D;;AAGI,MAAA,KAAAA,QAAwB,MAAI,CAAA,GAA3B,YAAS,GAAA,CAAA,GAAE,WAAQ,GAAA,CAAA;AAC1B,MAAI,UAAU;AACZ,YAAQ,UAAU,SAAC,OAAO,KAAG;AAC3B,eAAS,OAAOF,SAAMC,eAAAA,eAAA,CAAA,GAAAC,QAAM,MAAM,CAAA,GAAAA,QAAK,UAAU,GAAG,CAAC,CAAA,CAAA;IACvD,CAAC;;AAGH,EAAAF,QAAO,WAAW,MAAM;AAC1B;AAEM,SAAU,sBACd,OACA,aACA,WAAoB;AAEpB,WAAS,aAAa,SAAC,MAAM,MAAI;AAC/B,YAAQ,QAAQ,OAAO,MAAM,SAAA,GAAC;AAAI,aAAA,iBAAiB,GAAG,MAAM,SAAS;IAAnC,CAAoC;EACxE,CAAC;AAED,SAAO;AACT;AAEM,SAAU,oCACd,OACA,aAA2C;AAE3C,WAAS,MAAM,gBAA0B,MAAY;AACnD,QAAM,SAAS,QAAQ,OAAO,UAAU,IAAI,CAAC;AAE7C,mBAAe,IAAI,SAAS,EAAE,QAAQ,SAAA,qBAAmB;AACvD,cAAQ,QAAQ,OAAO,qBAAqB,WAAA;AAAM,eAAA;MAAA,CAAM;IAC1D,CAAC;EACH;AAEA,MAAI,QAAQ,WAAW,GAAG;AAClB,QAAA,KAAAE,QAAgB,aAAW,CAAA,GAA1B,OAAI,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AAClB,SAAK,QAAQ,SAAA,eAAa;AACxB,cAAQ,QAAQ,OAAO,UAAU,aAAa,GAAG,WAAA;AAAM,eAAA;MAAA,CAAK;IAC9D,CAAC;AAED,QAAI,OAAO;AACT,cAAQ,OAAO,KAAK;;SAEjB;AACL,YAAQ,aAAa,KAAK;;AAG5B,SAAO;AACT;AAEA,IAAM,SAAS,SAAC,QAAa,WAAoB;AAC/C,SAAA,cAAc,MAAM,KACpB,QAAQ,MAAM,KACd,MAAM,MAAM,KACZ,MAAM,MAAM,KACZ,4BAA4B,QAAQ,SAAS;AAJ7C;AAMF,SAAS,YAAY,QAAa,MAAa,YAA6B;AAC1E,MAAM,cAAc,WAAW,IAAI,MAAM;AAEzC,MAAI,aAAa;AACf,gBAAY,KAAK,IAAI;SAChB;AACL,eAAW,IAAI,QAAQ,CAAC,IAAI,CAAC;;AAEjC;AAYM,SAAU,uCACd,aAA8B;AAE9B,MAAM,SAAmC,CAAA;AACzC,MAAI,oBAA0C;AAE9C,cAAY,QAAQ,SAAA,OAAK;AACvB,QAAI,MAAM,UAAU,GAAG;AACrB;;AAGI,QAAA,KAAAA,QAAoC,MACvC,IAAI,SAAA,MAAI;AAAI,aAAA,KAAK,IAAI,MAAM;IAAf,CAAgB,EAC5B,KAAK,SAAC,GAAG,GAAC;AAAK,aAAA,EAAE,SAAS,EAAE;IAAb,CAAmB,CAAC,GAF/B,eAAY,GAAA,CAAA,GAAK,iBAAc,GAAA,MAAA,CAAA;AAItC,QAAI,aAAa,WAAW,GAAG;AAC7B,0BAAoB,eAAe,IAAI,aAAa;WAC/C;AACL,aAAO,cAAc,YAAY,CAAC,IAAI,eAAe,IAAI,aAAa;;EAE1E,CAAC;AAED,MAAI,mBAAmB;AACrB,QAAI,cAAc,MAAM,GAAG;AACzB,aAAO,CAAC,iBAAiB;WACpB;AACL,aAAO,CAAC,mBAAmB,MAAM;;SAE9B;AACL,WAAO,cAAc,MAAM,IAAI,SAAY;;AAE/C;AAEO,IAAM,SAAS,SACpB,QACA,YACA,WACA,MACA,mBAA6B;;AAD7B,MAAA,SAAA,QAAA;AAAA,WAAA,CAAA;EAAgB;AAChB,MAAA,sBAAA,QAAA;AAAA,wBAAA,CAAA;EAA6B;AAE7B,MAAI,CAAC,YAAY,MAAM,GAAG;AACxB,gBAAY,QAAQ,MAAM,UAAU;;AAGtC,MAAI,CAAC,OAAO,QAAQ,SAAS,GAAG;AAC9B,QAAM,gBAAc,eAAe,QAAQ,SAAS;AACpD,QAAI,eAAa;AACf,aAAO;QACL,kBAAkB,cAAY;QAC9B,aAAa,CAAC,cAAY,IAAI;;WAE3B;AACL,aAAO;QACL,kBAAkB;;;;AAKxB,MAAI,SAAS,mBAAmB,MAAM,GAAG;AACvC,WAAO;MACL,kBAAkB;;;AAItB,MAAM,uBAAuB,eAAe,QAAQ,SAAS;AAC7D,MAAM,eAAc,KAAA,yBAAoB,QAApB,yBAAoB,SAAA,SAApB,qBAAsB,WAAK,QAAA,OAAA,SAAA,KAAI;AAEnD,MAAI,CAAC,YAAY,MAAM,GAAG;AACxB,wBAAiBD,eAAAA,eAAA,CAAA,GAAAC,QAAO,iBAAiB,CAAA,GAAA,CAAE,MAAM,CAAA;;AAGnD,MAAM,mBAAwB,QAAQ,WAAW,IAAI,CAAA,IAAK,CAAA;AAC1D,MAAM,mBAAyD,CAAA;AAE/D,UAAQ,aAAa,SAAC,OAAO,OAAK;AAChC,QAAM,kBAAkB,OACtB,OACA,YACA,WAASD,eAAAA,eAAA,CAAA,GAAAC,QACL,IAAI,CAAA,GAAA,CAAE,KAAK,CAAA,GACf,iBAAiB;AAGnB,qBAAiB,KAAK,IAAI,gBAAgB;AAE1C,QAAI,QAAQ,gBAAgB,WAAW,GAAG;AACxC,uBAAiB,KAAK,IAAI,gBAAgB;eACjC,cAAc,gBAAgB,WAAW,GAAG;AACrD,cAAQ,gBAAgB,aAAa,SAAC,MAAM,KAAG;AAC7C,yBAAiB,UAAU,KAAK,IAAI,MAAM,GAAG,IAAI;MACnD,CAAC;;EAEL,CAAC;AAED,MAAI,cAAc,gBAAgB,GAAG;AACnC,WAAO;MACL;MACA,aAAa,CAAC,CAAC,uBACX,CAAC,qBAAqB,IAAI,IAC1B;;SAED;AACL,WAAO;MACL;MACA,aAAa,CAAC,CAAC,uBACX,CAAC,qBAAqB,MAAM,gBAAgB,IAC5C;;;AAGV;;;ACpOA,SAASC,SAAQ,SAAS;AACxB,SAAO,OAAO,UAAU,SAAS,KAAK,OAAO,EAAE,MAAM,GAAG,EAAE;AAC5D;AACA,SAASC,aAAY,SAAS;AAC5B,SAAOD,SAAQ,OAAO,MAAM;AAC9B;AACA,SAASE,QAAO,SAAS;AACvB,SAAOF,SAAQ,OAAO,MAAM;AAC9B;AACA,SAASG,eAAc,SAAS;AAC9B,MAAIH,SAAQ,OAAO,MAAM;AACvB,WAAO;AACT,QAAM,YAAY,OAAO,eAAe,OAAO;AAC/C,SAAO,CAAC,CAAC,aAAa,UAAU,gBAAgB,UAAU,cAAc,OAAO;AACjF;AAmBA,SAASI,SAAQ,SAAS;AACxB,SAAOC,SAAQ,OAAO,MAAM;AAC9B;AAmEA,IAAM,oBAAoB,QAAQC,SAAQC,YAAW;AACrD,SAAS,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG;AAC9B,SAAO,CAAC,UAAU,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK;AAChG;;;ACvGA,SAAS,WAAW,OAAO,KAAK,QAAQ,gBAAgB,sBAAsB;AAC5E,QAAM,WAAW,CAAC,EAAE,qBAAqB,KAAK,gBAAgB,GAAG,IAAI,eAAe;AACpF,MAAI,aAAa;AACf,UAAM,GAAG,IAAI;AACf,MAAI,wBAAwB,aAAa,iBAAiB;AACxD,WAAO,eAAe,OAAO,KAAK;AAAA,MAChC,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AACF;AACA,SAAS,KAAK,QAAQ,UAAU,CAAC,GAAG;AAClC,MAAIC,SAAQ,MAAM,GAAG;AACnB,WAAO,OAAO,IAAI,CAAC,SAAS,KAAK,MAAM,OAAO,CAAC;AAAA,EACjD;AACA,MAAI,CAACC,eAAc,MAAM,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,oBAAoB,MAAM;AAC/C,QAAM,UAAU,OAAO,sBAAsB,MAAM;AACnD,SAAO,CAAC,GAAG,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC,OAAO,QAAQ;AACnD,QAAID,SAAQ,QAAQ,KAAK,KAAK,CAAC,QAAQ,MAAM,SAAS,GAAG,GAAG;AAC1D,aAAO;AAAA,IACT;AACA,UAAM,MAAM,OAAO,GAAG;AACtB,UAAM,SAAS,KAAK,KAAK,OAAO;AAChC,eAAW,OAAO,KAAK,QAAQ,QAAQ,QAAQ,aAAa;AAC5D,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClBA,IAAA;;EAAA,WAAA;AAAA,aAAAE,aAAA;AAuDW,WAAA,gBAAgB,IAAI,cAAa;AAKjC,WAAA,iBAAiB,IAAI,SAAiB,SAAA,GAAC;AAAA,YAAA;AAAI,gBAAA,KAAA,EAAE,iBAAW,QAAA,OAAA,SAAA,KAAI;MAAE,CAAA;AAK9D,WAAA,4BAA4B,IAAI,0BAAyB;AAWzD,WAAA,oBAA8B,CAAA;IA8BzC;AAzGE,IAAAA,WAAA,UAAA,YAAA,SAAU,QAAsB;AAC9B,UAAM,aAAa,oBAAI,IAAG;AAC1B,UAAM,SAAS,OAAO,QAAQ,YAAY,IAAI;AAC9C,UAAM,MAAuB;QAC3B,MAAM,OAAO;;AAGf,UAAI,OAAO,aAAa;AACtB,YAAI,OAAIC,UAAAA,UAAA,CAAA,GACH,IAAI,IAAI,GAAA,EACX,QAAQ,OAAO,YAAW,CAAA;;AAI9B,UAAM,sBAAsB,uCAC1B,UAAU;AAEZ,UAAI,qBAAqB;AACvB,YAAI,OAAIA,UAAAA,UAAA,CAAA,GACH,IAAI,IAAI,GAAA,EACX,uBAAuB,oBAAmB,CAAA;;AAI9C,aAAO;IACT;AAEA,IAAAD,WAAA,UAAA,cAAA,SAAyB,SAAwB;AACvC,UAAA,OAAe,QAAO,MAAhB,OAAS,QAAO;AAE9B,UAAI,SAAY,KAAK,IAAI;AAEzB,UAAI,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,QAAQ;AAChB,iBAAS,sBAAsB,QAAQ,KAAK,QAAQ,IAAI;;AAG1D,UAAI,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,uBAAuB;AAC/B,iBAAS,oCACP,QACA,KAAK,qBAAqB;;AAI9B,aAAO;IACT;AAEA,IAAAA,WAAA,UAAA,YAAA,SAAU,QAAsB;AAC9B,aAAO,KAAK,UAAU,KAAK,UAAU,MAAM,CAAC;IAC9C;AAEA,IAAAA,WAAA,UAAA,QAAA,SAAmB,QAAc;AAC/B,aAAO,KAAK,YAAY,KAAK,MAAM,MAAM,CAAC;IAC5C;AAGA,IAAAA,WAAA,UAAA,gBAAA,SAAc,GAAU,SAAkC;AACxD,WAAK,cAAc,SAAS,GAAG,OAAO;IACxC;AAGA,IAAAA,WAAA,UAAA,iBAAA,SAAe,GAAW,YAAmB;AAC3C,WAAK,eAAe,SAAS,GAAG,UAAU;IAC5C;AAGA,IAAAA,WAAA,UAAA,iBAAA,SACE,aACA,MAAY;AAEZ,WAAK,0BAA0B,SAAQC,UAAA,EACrC,KAAI,GACD,WAAW,CAAA;IAElB;AAGA,IAAAD,WAAA,UAAA,kBAAA,WAAA;;AAAgB,UAAA,QAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAkB;AAAlB,cAAA,EAAA,IAAA,UAAA,EAAA;;AACd,OAAA,KAAA,KAAK,mBAAkB,KAAI,MAAA,IAAAE,eAAA,CAAA,GAAAC,QAAI,KAAK,CAAA,CAAA;IACtC;AAEe,IAAAH,WAAA,kBAAkB,IAAIA,WAAS;AACvC,IAAAA,WAAA,YAAYA,WAAU,gBAAgB,UAAU,KACrDA,WAAU,eAAe;AAEpB,IAAAA,WAAA,cAAcA,WAAU,gBAAgB,YAAY,KACzDA,WAAU,eAAe;AAEpB,IAAAA,WAAA,YAAYA,WAAU,gBAAgB,UAAU,KACrDA,WAAU,eAAe;AAEpB,IAAAA,WAAA,QAAQA,WAAU,gBAAgB,MAAM,KAC7CA,WAAU,eAAe;AAEpB,IAAAA,WAAA,gBAAgBA,WAAU,gBAAgB,cAAc,KAC7DA,WAAU,eAAe;AAEpB,IAAAA,WAAA,iBAAiBA,WAAU,gBAAgB,eAAe,KAC/DA,WAAU,eAAe;AAEpB,IAAAA,WAAA,iBAAiBA,WAAU,gBAAgB,eAAe,KAC/DA,WAAU,eAAe;AAEpB,IAAAA,WAAA,kBAAkBA,WAAU,gBAAgB,gBAAgB,KACjEA,WAAU,eAAe;AAE7B,WAAAA;IA1GA;;kBAAqB;AA4Gd,IAAM,YAAY,UAAU;AAC5B,IAAM,cAAc,UAAU;AAE9B,IAAM,YAAY,UAAU;AAC5B,IAAM,QAAQ,UAAU;AAExB,IAAM,gBAAgB,UAAU;AAChC,IAAM,iBAAiB,UAAU;AACjC,IAAM,iBAAiB,UAAU;AACjC,IAAM,kBAAkB,UAAU;;;;ACjIlC,IAAA,eAAA;EACLI,YAAAA;EACAC,eAAAA;EACAC,YAAAA;EACAC,MAAAA;EACAC,SAAAA;EACAC,sBAAAA;EACAC,gBAAAA;EACAC,SAAAA;EACAC,QAAAA;EACAC,QAAAA;EACAC,QAAAA;EACAC,SAAAA;AAZ0B;AAqB5B,IAAA,eAAA,qBAAA,YAAA;AAEO,SAAA,cAAA;;;AAAuB,GAAA;;IACE,OAAA;EAAvB,GAAA,IAAA,CAAA;AACR;AAEM,SAAA,WAAA;AACL,SAAA,kBAAA,YAAA;AACD;;;;AC9Bc,SAASC,cAAcC,OAAoC;AAExE,QAAM,CAACC,SAASC,UAAV,IAA8BC,gBAAS,MAAM;AACjD,QAAI,OAAOC,WAAW,aAAa;AACjC,aAAOA,OAAOC,WAAWL,KAAlB,EAAyBM;IACjC;AACD;GAJ4B;AAQ9BC,EAAMC,iBAAU,MAAM;AACpB,QAAI,OAAOJ,WAAW,aAAa;AAEjC,YAAMK,UAAUL,OAAOC,WAAWL,KAAlB;AAGhB,YAAMU,WAAW,CAAC;QAAEJ;MAAF,MAChBJ,WAAWI,OAAD;AAGZG,cAAQE,YAAYD,QAApB;AAEA,aAAO,MAAM;AAEXD,gBAAQG,eAAeF,QAAvB;;IAEH;AACD;EACD,GAAE,CAACT,SAASD,OAAOE,UAAjB,CAlBH;AAoBA,SAAOD;AACR;;;ACNM,SAASY,oBAAoB;EAClCC;EACAC;EACAC;EACAC;AAJkC,GAUjC;AACD,SAAOH,WAAWI,gBAAgB,aAC9BD,MAAME,SACN,CAACJ,gBACDE,MAAMG,OACNN,WAAWI,gBAAgB,WAC3BD,MAAMI,SACNL,UACAC,MAAMK,UACNL,MAAMM;AACX;AAEM,SAASC,oBAAoBC,OAAc;AAChD,SAAOA,MAAMC,MAAMR,gBAAgB,aAC/B,aACA,CAACO,MAAME,kBAAN,IACD,aACAF,MAAMC,MAAMR,gBAAgB,WAC5B,WACAO,MAAMT,QAAN,IACA,UACA;AACL;AAMM,SAASY,OACdC,MACAC,WACAC,UAAkC,CAAA,GAClC;AACA,SAAaC,kBACX,CAAC;IAAEC;IAAO,GAAGC;KAAQC,QAAQ;AAC3B,UAAMlB,QAAQmB,SAAQ;AAEtB,UAAMC,cAAcC,OAAOC,QAAQR,OAAf,EAAwBS,OAC1C,CAACC,SAAS,CAACC,KAAKC,KAAN,MAAiB;AAEzB,aAAOC,cAAcF,GAAD,IAChB;QACE,GAAGD;QACH,GAAI,OAAOE,UAAU,aAAaA,MAAMT,MAAMjB,KAAP,IAAgB0B;MAFzD,IAIAF;OAEN,CAAA,CAVkB;AAapB,WAAaI,qBAAchB,MAAM;MAC/B,GAAGK;MACHD,OAAO;QACL,GAAI,OAAOH,cAAc,aACrBA,UAAUI,MAAMjB,KAAP,IACTa;QACJ,GAAGG;QACH,GAAGI;;MAELF;IAT+B,CAA1B;EAWR,CA5BI;AA8BR;AAEM,SAASW,eAAe;AAC7B,QAAMC,aAAmBC,cAAO,KAAb;AACnB,QAAMC,YAAkBC,mBAAY,MAAMH,WAAWN,SAAS,CAAA,CAA5C;AAElBU,EAAMC,iBAAU,MAAM;AACpBL,eAAWN,UAAU;AACrB,WAAO,MAAM;AACXM,iBAAWN,UAAU;;EAExB,GAAE,CAAA,CALH;AAOA,SAAOQ;AACR;AAOM,IAAMI,eAAe,CAACV,OAAgBW,WAAoB,UAAU;AACzE,QAAM;IAAEC;EAAF,IAAWC,YAAUC,UAAUd,KAApB;AAEjB,SAAOe,KAAKC,UAAUJ,MAAM,MAAMD,WAAW,IAAIM,MAA1C;AACR;AAKD,IAAMC,gBAAiBC,OACrBA,EAAEpC,MAAMR,gBAAgB,SACpB,IACA,CAAC4C,EAAEnC,kBAAF,IACD,IACAmC,EAAE9C,QAAF,IACA,IACA;AAEN,IAAM+C,gBAAwB,CAACC,GAAGC,MAAMD,EAAEE,UAAUC,cAAcF,EAAEC,SAA5B;AAExC,IAAME,WAAmB,CAACJ,GAAGC,MAC3BD,EAAEtC,MAAM2C,gBAAgBJ,EAAEvC,MAAM2C,gBAAgB,IAAI;AAEtD,IAAMC,oBAA4B,CAACN,GAAGC,MAAM;AAC1C,MAAIJ,cAAcG,CAAD,MAAQH,cAAcI,CAAD,GAAK;AACzC,WAAOG,SAASJ,GAAGC,CAAJ;EAChB;AAED,SAAOJ,cAAcG,CAAD,IAAMH,cAAcI,CAAD,IAAM,IAAI;AAClD;AAEM,IAAMM,UAAkC;EAC7C,yBAAyBD;EACzB,cAAcP;EACd,gBAAgBK;AAH6B;AAMxC,IAAMI,eAAe;AACrB,IAAMC,mBAAmB;AACzB,IAAMC,QAA4B;EACvCC,KAAK;EACLC,QAAQ;EACRC,MAAM;EACNC,OAAO;AAJgC;AAYlC,SAASC,eAAeC,MAAY;AACzC,SAAO,CAAC,QAAQ,OAAT,EAAkBC,SAASD,IAA3B;AACR;AAIM,SAASE,gBAAgBF,MAAkB;AAChD,SAAON,MAAMM,IAAD;AACb;AAKM,SAASG,aAA+BC,MAASJ,MAAY;AAClE,SAAA,KAAUI,QACRJ,KAAKK,OAAO,CAAZ,EAAeC,YAAf,IAA+BN,KAAKO,MAAM,CAAX;AAElC;AAoCM,SAASC,kBAAkB;EAChCC,WAAW;EACXC;EACAC;EACAC;EACAC;EACAC;EACAC;AAPgC,GAQa;AAC7C,QAAMC,eAAed,gBAAgBO,QAAD;AACpC,QAAMQ,aAAad,aAAa,UAAUa,YAAX;AAC/B,QAAME,aAAanB,eAAeU,QAAD;AAEjC,SAAO;IACL,GAAGM;IACHI,WAAW;IACXV,UAAU;IACV,CAACA,QAAD,GAAY;IACZ,CAACQ,UAAD,GAAA,eAA2BL,cAAcxE;IACzCgF,iBAAiBJ;IACjBK,WAAW;IACXC,QAAQ;;IAERC,YAAYV,SAAS,YAAY;IACjC,GAAIC,aACA;MACEU,YAAU;IADZ,IAGA;MAAEA,YAAU;IAAZ;IACJ,GAAIX,SACA;MACEY,SAAS;MACTC,eAAe;MACfC,WAAWT,aAAU,2BAAA;IAHvB,IAOA;MACEO,SAAS;MACTC,eAAe;MACfC,WAAWT,aAAU,iCAAA;IAHvB;IAOJ,GAAIA,aACA;MACEvB,KAAK;MACLe,QAAQ;MACRkB,UAAU;MACVjB,OACE,OAAOA,UAAU,YAAYA,SAASnB,eAClCmB,QACAlB;IAPR,IASA;MACEI,MAAM;MACNc,OAAO;MACPkB,WAAW;MACXnB,QACE,OAAOA,WAAW,YAAYA,UAAUlB,eACpCkB,SACAjB;;;AAGf;AAKM,SAASqC,qBACdrB,WAAiB,UACI;AACrB,QAAMS,aAAanB,eAAeU,QAAD;AACjC,QAAMO,eAAed,gBAAgBO,QAAD;AACpC,QAAMsB,aAAa5B,aAAa,UAAUa,YAAX;AAE/B,SAAO;IACLP,UAAU;IACVuB,QAAQd,aAAa,eAAe;IACpCI,QAAQ;IACR,CAACN,YAAD,GAAgB;IAChB,CAACe,UAAD,GALK;IAML,GAAIb,aACA;MACEvB,KAAK;MACLe,QAAQ;MACRC,OAAO;IAHT,IAKA;MACEA,OAAO;MACPD,QAAQ;;;AAGjB;;;AC5TM,IAAMuB,QAAQC,OACnB,OACA,CAACC,QAAQC,WAAW;EAClBC,UAAU;EACVC,YAFkB;EAGlBC,SAAS;EACTC,iBAAiBJ,MAAMK;EACvBC,OAAON,MAAMO;AALK,IAOpB;EACE,sBAAsB;IACpBC,eAAe;;EAEjB,sBAAsB;IACpBP,UAAU;;EADU;AAJxB,CATyB;IAoBdQ,mBAAmBX,OAC9B,OACA,OAAO;EACLY,MAAM;EACNP,SAAS;EACTK,eAAe;EACfG,UAAU;EACVC,QAAQ;AALH,IAOP;EACE,sBAAsB,CAACb,QAAQC,WAAW;IACxCa,WAAS,eAAeb,MAAMc;;AAFlC,CAToC;AAgB/B,IAAMC,SAASjB,OAAO,UAAU,CAACkB,OAAOhB,WAAW;EACxDiB,YAAY;EACZhB,UAAU;EACViB,YAAY;EACZb,YAAYL,MAAMc;EAClBK,QAAQ;EACRC,cAAc;EACdd,OAAO;EACPe,SAAS;EACTC,SAASN,MAAMO,WAAW,OAAOC;EACjCC,QAAQ;AAVgD,EAA9B;IAafC,YAAY5B,OAAO,QAAQ;EACtCK,SAAS;EACTwB,UAAU;EACVC,KAAK;EACL3B,UAAU;AAJ4B,CAAT;IAOlB4B,WAAW/B,OAAO,QAAQ;EACrCK,SAAS;EACT2B,YAAY;EACZT,SAAS;EACTH,YAAY;EACZa,YAAY;EACZX,cAAc;AANuB,CAAT;IASjBY,OAAOlC,OAAO,QAAQ;EACjCG,UAAU;EACVK,OAAO;EACPD,YAAY;AAHqB,CAAT;AAMnB,IAAM4B,QAAQnC,OAAO,SAAS,CAACC,QAAQC,WAAW;EACvDI,iBAAiBJ,MAAMkC;EACvBf,QAAQ;EACRC,cAAc;EACdd,OAAON,MAAMmC;EACblC,UAAU;EACVmC,YANuD;EAOvDf,SAAS;AAP8C,EAA9B;AAUpB,IAAMgB,SAASvC,OACpB,UACA,CAACC,QAAQC,WAAW;EAClBG,SADkB;EAElBF,UAFkB;EAGlBC,YAHkB;EAIlBgB,YAAY;EACZkB,YALkB;EAMlBf,SANkB;EAOlBT,QAAQ;EACRO,QAAQ;EACRC,cATkB;EAUlBH,YAVkB;EAWlBqB,kBAAkB;EAClBlC,iBAAiBJ,MAAMkC;EACvBK,iBAbkB;EAclBC,kBAdkB;EAelBC,oBAfkB;EAgBlBC,gBAhBkB;EAiBlBpC,OAAON,MAAMmC;AAjBK,IAmBpB;EACE,sBAAsB;IACpBhC,SAAS;EADW;AADxB,CArB0B;;;;ACjFb,SAASwC,aAAa;EAAEC;AAAF,GAA4B;AAC/D,SACE,qBAAA,QAAA;IACE,OAAO;MACLC,UAAU;MACVC,OAAO;MACPC,QAAQ;MACRC,UAAU;IAJL;EADT,GAQGJ,IARH;AAWH;;;;;ECRCK,YAAAA;EACAC,UAAAA;EACAC,YAAAA;EACAC,SAAAA;EACAC,WAAAA;AALiC,CAAA;;EASjCC,OAAAA;AADkC,CAAA;;EAKlCC,QAAAA;EACAD,OAAAA;AAF0C,CAAA;;EAM1CC,QAAAA;EACAD,OAAAA;EACAE,MAAAA;EACAJ,SAAAA;EACAK,YAAAA;EACAC,QAAAA;EACAC,SAAAA;AAP2C,CAAA;AAYtC,IAAA,aAAA,CAAA;EAAsBC;AAAF,MAAA;;;IAKrB,SAAA,cAAA,WAAA,MAAA;AAGQC,gBAAAA,UAAAA,UAAAA,YAAAA,UAAAA,KAAAA,CAAAA,EAAAA,KAAAA,MAAAA;;AAGIC,mBAAAA,MAAAA;;;;AAKAC,gBAAAA,MAAAA,oBAAAA,GAAAA;;AAEAD,mBAAAA,MAAAA;;;;IAKL,IAAA;IAGP,OAAA;MACEP,QAAAA;MACAD,OAAAA;MACAE,MAAAA;MACAJ,SAAAA;MACAK,YAAAA;MACAC,QAAAA;MACAC,SAAAA;IAPK;EAtBT,GAAA,cAAA,WAAA,qBAAA,QAAA,IAAA,IAAA,cAAA,gBAAA,qBAAA,cAAA,IAAA,IAAA,qBAAA,aAAA,IAAA,CAAA;AAyCH;AAEM,IAAA,QAAA,OAAA,QAAA,CAAA,QAAA,WAAA;;AAAiD,EAAA;;EAKtDK,YAAAA;EACAC,aAAAA;EACAC,YAAAA;AAHsC,CAAA;;EAOtCZ,OAAAA;EACAJ,UAAAA;AAFiC,CAAA;AAU5B,IAAA,WAAA,CAAA;;EAA8BiB,QAAAA,CAAAA;AAAZ,MAAA,qBAAA,QAAA;EAErB,OAAA;IACEC,SAAAA;IACAC,YAAAA;IACAC,WAAAA,aAAAA,WAAAA,KAAAA,KAAAA,WAAAA,MAAAA,aAAAA;;EAHK;AADT,GAAA,GAAA;AAYF,IAAA,SAAA,MAAA,qBAAA,QAAA;EAEI,cAAA;EACA,OAAA;EACA,OAAA;IACEL,aAAAA;EADK;AAHT,GAAA,qBAAA,OAAA;EAOO,QAAA;EAAY,SAAA;EAAoB,OAAA;AAArC,GAAA,qBAAA,QAAA;EAEI,MAAA;EACA,GAAA;AAFF,CAAA,GAAA,qBAAA,QAAA;EAKE,MAAA;EACA,GAAA;AAFF,CAAA,CAAA,CAAA;AAQN,IAAA,cAAA,MAAA,qBAAA,QAAA;EAEI,cAAA;EACA,OAAA;EACA,OAAA;IACEA,aAAAA;IACAG,SAAAA;IACAG,YAAAA;EAHK;AAHT,GAAA,qBAAA,OAAA;EASO,QAAA;EAAY,SAAA;EAAoB,OAAA;EAAW,SAAA;AAAhD,GAAA,qBAAA,QAAA;EAEI,MAAA;EACA,GAAA;AAFF,CAAA,CAAA,GAAA,qBAAA,QAAA;EAMA,OAAA;IACEjB,OAAAA;IACAJ,UAAAA;IACAe,aAAAA;IACAO,UAAAA;IACAC,KAAAA;EALK;AADT,GAAA,aAAA,CAAA;AAcJ,IAAA,eAAA,MAAA,qBAAA,QAAA;EAEI,cAAA;EACA,OAAA;EACA,OAAA;IACER,aAAAA;IACAG,SAAAA;IACAM,eAAAA;EAHK;AAHT,GAAA,qBAAA,OAAA;EASO,QAAA;EAAY,SAAA;EAAoB,OAAA;EAAW,SAAA;AAAhD,GAAA,qBAAA,QAAA;EAEI,MAAA;EACA,GAAA;AAFF,CAAA,CAAA,CAAA;AAkCC,SAAA,WAAA,OAAA,MAAA;AACL,MAAA,OAAA;AAAA,WAAA,CAAA;;;AAGA,SAAA,IAAA,MAAA,QAAA;AACEC,WAAAA,KAAAA,MAAAA,MAAAA,GAAAA,IAAAA,IAAAA,CAAAA;;EAED;AACD,SAAA;AACD;AAIM,IAAA,kBAAA,CAAA;;;;EAILC,aAAAA,CAAAA;EACAC,gBAAAA,CAAAA;;EAEAC,WAAAA;EACAC,WAAAA;;EAEAC;AAVwC,MAAA;;AAcxC,SAAA,qBAAA,OAAA;IACS,KAAA;EAAP,GAAA,cAAA,SAAA,qBAAA,iBAAA,MAAA,qBAAA,cAAA;;EAGM,GAAA,qBAAA,UAAA;IACY;EAAV,CAAA,GAAA,KAAA,OAAA,KAAA,qBAAA,MAAA,MAAA,OAAA,IAAA,EAAA,YAAA,MAAA,aAAA,gBAAA,IAAA,WAAA,QAAA,KAAA,WAAA,SAAA,IAAA,UAAA,MAAA,CAAA,GAAA,WAAA,qBAAA,YAAA;IAMsB;EAAZ,CAAA,IAAA,MAAA,WAAA,cAAA,WAAA,IAAA,qBAAA,YAAA,MAAA,WAAA,IAAA,WAAA,CAAA,IAAA,qBAAA,YAAA,MAAA,cAAA,IAAA,CAAA,SAAA,UAAA,qBAAA,OAAA;IAOC,KAAA;EAAL,GAAA,qBAAA,OAAA,MAAA,qBAAA,aAAA;IAGM,SAAA,MAAA,iBAAA,SAAA,IAAA,SAAA,KAAA,IAAA,IAAA,OAAA,OAAA,MAAA,KAAA,IAAA,CAAA,GAAA,KAAA,KAAA,CAAA;EADF,GAAA,qBAAA,UAAA;IASY;;AAoBjC;AAcD,SAAA,WAAA,GAAA;AACE,SAAA,OAAA,YAAA;AACD;AAEc,SAAA,SAAA;;;EAGbC,WAAAA;EACAD,WAAAA;EACAD,WAAAA;;AAL+B,GAAA;AAQ/B,QAAA,CAAA,UAAA,WAAA,IAAA,gBAAA,QAAA,eAAA,CAAA;AACA,QAAA,iBAAA,mBAAA,MAAA,YAAA,SAAA,CAAA,GAAA,GAAA,CAAA,CAAA;;;;AAME,UAAA,qBAAA,oBAAA,OAAA;;;;;MAMEG,iBAAAA;;;AAIJ,MAAA,MAAA,QAAA,KAAA,GAAA;AACEC,WAAAA;;MAGIC,OAAAA,EAAAA,SAAAA;MACAxB,OAAAA;IAFW,CAAA,CAAA;;AAWfuB,WAAAA;AACAP,iBAAAA,MAAAA,KAAAA,OAAAA,CAAAA,KAAAA,MAAAA,aAAAA;MAEIQ,OAAAA,EAAAA,SAAAA;MACAxB,OAAAA;IAFW,CAAA,CAAA;;AAMfuB,WAAAA;AACAP,iBAAAA,OAAAA,QAAAA,KAAAA,EAAAA,IAAAA,CAAAA,CAAAA,KAAAA,GAAAA,MAAAA,aAAAA;MAEIQ,OAAAA;MACAxB,OAAAA;IAFW,CAAA,CAAA;EAKhB;AAED,QAAA,gBAAA,WAAA,YAAA,QAAA;AAEA,SAAA,SAAA;IACEyB,aAAAA,WAAAA,qBAAAA,UAAAA,SAAAA;;MAGI;MACA;MACA;;;;;;;;;;;EANU,CAAA;AAqBjB;;;;AC9Xc,SAASC,KAAKC,OAAY;AACvC,SACE,qBAAA,OAAA,SAAA;IACE,OAAM;IACN,QAAO;IACP,SAAQ;IACR,SAAQ;EAJV,GAKMA,KALN,GAOE,qBAAA,KAAA;IAAG,QAAO;IAAO,aAAY;IAAI,MAAK;IAAO,UAAS;KACpD,qBAAA,KAAA;IAAG,WAAU;KACX,qBAAA,QAAA;IACE,GAAE;IACF,MAAK;IACL,UAAS;IACT,WAAU;EAJZ,CAAA,GAMA,qBAAA,QAAA;IACE,GAAE;IACF,MAAK;EAFP,CAAA,GAIA,qBAAA,QAAA;IACE,GAAE;IACF,MAAK;GAbT,CAAA,CADF,CAPF;AA2BH;;;;ACsHM,SAAA,mBAAA;;EAELC,aAAAA,CAAAA;EACAC,mBAAAA,CAAAA;EACAC,oBAAAA,CAAAA;EACAC,WAAAA;;;;;EAKAC,aAAAA,CAAAA;AAViC,GAAA;AAYjC,QAAA,UAAA,cAAA,IAAA;AACA,QAAA,WAAA,cAAA,IAAA;;;;AAcA,QAAA,CAAA,gBAAA,UAAA,gBAAA,IAAA,gBAAA,mCAAA,oBAAA;;;;AASA,QAAA,kBAAA,CAAA,cAAA,eAAA;;;AAKE,QAAA,WAAA,WAAA;AAAA;AACA,UAAA,aAAA,eAAA,aAAA;;;;MAGgBC;;AAChB,UAAA,SAAA,WAAA;AACA,UAAA,SAAA,WAAA;;;;AASE,UAAA,YAAA;AACEC,kBAAAA,SAAAA,kBAAAA,UAAAA,SAAAA,UAAAA,UAAAA,UAAAA,UAAAA;;MAMD,OAAA;AACCA,kBAAAA,UAAAA,kBAAAA,WAAAA,SAAAA,UAAAA,UAAAA,UAAAA,UAAAA;;MAMD;;;MAIA,OAAA;;MAEA;;;AAID,UAAA,YAAA;;MAEC;AAEDC,eAAAA,oBAAAA,aAAAA,KAAAA,KAAAA;AACAA,eAAAA,oBAAAA,WAAAA,OAAAA,KAAAA;;AAGFA,aAAAA,iBAAAA,aAAAA,KAAAA,KAAAA;AACAA,aAAAA,iBAAAA,WAAAA,OAAAA,KAAAA;;;AAIAC,sBAAAA,UAAAA,OAAAA,SAAAA,KAAAA;;;AAMA,UAAA,MAAA,SAAA;AACA,QAAA,KAAA;;AAEI,YAAA,gBAAA;AACEC,cAAAA,MAAAA,aAAAA;QACD;;;;AAKCA,cAAAA,MAAAA,aAAAA;QACD;;AAGHA,UAAAA,iBAAAA,mBAAAA,0BAAAA;AACAA,UAAAA,iBAAAA,iBAAAA,wBAAAA;AAEA,aAAA,MAAA;AACEA,YAAAA,oBAAAA,mBAAAA,0BAAAA;AACAA,YAAAA,oBAAAA,iBAAAA,wBAAAA;;IAEH;AACD;;;AAGoB,QAAA;;;QAEVC;;AACR,YAAA,YAAA,aAAA,WAAA,aAAA;AACA,YAAA,aAAA,eAAA,aAAA;;;;;;QAOEC;MALyB,OAAA;;;;;QAWzBA;MALK,IAAA,cAAA,KAAA;;AAULD,sBAAAA,MAAAA,UAAAA;AACAA,sBAAAA,MAAAA,aAAAA;AACAA,sBAAAA,MAAAA,gBAAAA;AACAA,sBAAAA,MAAAA,cAAAA;AACAA,sBAAAA,MAAAA,eAAAA;;;;AAUF,UAAA,OAAA,WAAA,aAAA;AACEE,eAAAA,iBAAAA,UAAAA,GAAAA;AAEA,eAAA,MAAA;AACEA,iBAAAA,oBAAAA,UAAAA,GAAAA;AACAC,iBAAAA,QAAAA,gBAAAA,EAAAA,QAAAA,CAAAA,CAAAA,UAAAA,aAAAA,MAAAA;AAEIH,0BAAAA,MAAAA,QAAAA,IAAAA;;;MAKP;IACF;AACD;;;;;EAGI,IAAA;;;IAIJI,SAAAA;;;;IAMAX,UAAAA;IACAY,eAAAA;IACAC,QAAAA;IACAC,QAAAA;IACAZ,OAAAA;;IAEAa;;AAIF,MAAA,CAAA,UAAA;AAAA,WAAA;AAEA,SAAA,qBAAA,WAAA;IAEI,KAAA;IACA,WAAA;;EAFF,GAAA,qBAAA,eAAA;IAKiB,OAAA;EAAf,GAAA,qBAAA,yBAAA,SAAA;IAEI,KAAA;IACA;IACA;IACA,UAAA;IACA,kBAAA;IACA,iBAAA;IACA;EAPF,GAAA,iBAAA;IASE;IACA,QAAA;IACA;;IAEA;EAbF,CAAA,CAAA,CAAA,GAAA,CAAA,iBAAA,qBAAA,UAAA,SAAA;IAkBE,MAAA;EADF,GAAA,wBAAA;IAGE,cAAA;IACA,iBAAA;IACA,iBAAA;IACA,iBAAA;;;AAGEC,uBAAAA,OAAAA,SAAAA,cAAAA,CAAAA;;IAEF,OAAA;MACEC,YAAAA;MACAC,QAAAA;MACAC,SAAAA;MACAnB,UAAAA;MACAoB,QAAAA;MACAC,SAAAA;MACAC,UAAAA;MACAC,QAAAA;MACAC,QAAAA;MACAtB,OAAAA;;QAGMuB,KAAAA;QACAC,OAAAA;MAFF,IAAA,aAAA,aAAA;QAMED,KAAAA;QACAE,MAAAA;MAFF,IAAA,aAAA,iBAAA;QAMEC,QAAAA;QACAF,OAAAA;MAFF,IAAA;QAKEE,QAAAA;QACAD,MAAAA;MAFF;;IA1BC;EAXT,CAAA,GAAA,qBAAA,MAAA;IA4CQ,eAAA;;IACQ,MAAA;;AAKvB;AAED,IAAA,2BAAA,CAAA,YAAA,aAAA,OAAA,UAAA;AAKE,aAAA,kCAAA,mBAAA,mBAAA;AAGM,QAAA,CAAA;AAAA,aAAA,WAAA,UAAA,cAAA,WAAA,aAAA,CAAA;AAEA,WAAA,MAAA;AACE;;;AAQT;AAEYE,IAAAA,0BAAAA,kBAAAA,SAAAA,yBAAAA,OAAAA,KAAAA;;IAKThB,SAAAA;;;;;;;;IAQAf,mBAAAA,CAAAA;IACAG,aAAAA,CAAAA;;EAVI,IAAA;;IAcEU,SAAAA;;EAAF,IAAA;;IAE+BmB;EAAF,CAAA;AACnC,QAAA,aAAA,YAAA,cAAA;AAEA,QAAA,CAAA,MAAA,OAAA,IAAA,gBAAA,4BAAA,OAAA,KAAA,OAAA,EAAA,CAAA,CAAA;;;AAYA,QAAA,SAAA,eAAA,MAAA,QAAA,IAAA,GAAA,CAAA,IAAA,CAAA;AAEA,QAAA,eAAA,yBAAA,YAAA,MAAA,WAAA,OAAA,EAAA,QAAA,CAAA,MAAA;;AAWA,QAAA,UAAA,eAAA,MAAA;AACE,UAAA,kBAAA,WAAA,OAAA;;AAGE,aAAA,CAAA;IACD;;;AAYD,WAAA;EACD,GAAA,CAAA,UAAA,QAAA,QAAA,cAAA,UAAA,CAAA;;AAID,SAAA,qBAAA,eAAA;IACiB,OAAA;EAAf,GAAA,qBAAA,OAAA,SAAA;IAEI;IACA,WAAA;IACA,cAAA;IACA,IAAA;EAJF,GAAA,YAAA;IAME,OAAA;MACEhB,QAAAA;MACAd,UAAAA;MACA,GAAA,WAAA;IAHK;;IAOL,OAAA;IACA,yBAAA;MACE+B,QAAAA,gFAAAA,aAAAA,gBAAAA,MAAAA,aAAAA,OAAAA,yUAAAA,aAAAA,gBAAAA,gKAAAA,aAAAA,OAAAA,4EAAAA,aAAAA,gBAAAA;IADuB;EAF3B,CAAA,GAAA,qBAAA,OAAA;IA0BE,OAAA,qBAAA,QAAA;IACA,aAAA;;IAKE,OAAA;MACEC,MAAAA;MACAC,WAAAA;MACAC,WAAAA;MACAC,UAAAA;;MAEAd,SAAAA;MACAe,eAAAA;IAPK;;IAWL,OAAA;MACEjB,SAAAA;;MAEAE,SAAAA;MACAgB,gBAAAA;MACAC,YAAAA;IALK;;IASL,MAAA;IACA,cAAA;IACA,iBAAA;IACA,iBAAA;IACA,iBAAA;IACA,SAAA,MAAA,UAAA,KAAA;IACA,OAAA;MACEjB,SAAAA;MACAJ,YAAAA;MACAC,QAAAA;MACAC,SAAAA;MACAoB,aAAAA;MACAf,QAAAA;IANK;EAPT,GAAA,qBAAA,MAAA;IAgBQ,eAAA;;IACQ,MAAA;EAAd,CAAA,CAAA,GAAA,qBAAA,OAAA;IAIA,OAAA;MACEH,SAAAA;MACAe,eAAAA;IAFK;;IAML,OAAA;MACEf,SAAAA;MACAgB,gBAAAA;MACAC,YAAAA;MACAE,cAAAA;IAJK;EADT,GAAA,qBAAA,kBAAA;IAQoB;EAAlB,CAAA,GAAA,YAAA,mBAAA,qBAAA,QAAA;IAGI,cAAA;IACA,OAAA;IACA,OAAA;MAASC,mBAAAA;;;;IAGD,OAAA;EAAR,GAAA,MAAA,GAAA,qBAAA,UAAA;IACQ,OAAA;EAAR,GAAA,OAAA,GAAA,qBAAA,UAAA;IACQ,OAAA;EAAR,GAAA,KAAA,GAAA,qBAAA,UAAA;IACQ,OAAA;EAAR,GAAA,QAAA,CAAA,IAAA,IAAA,GAAA,qBAAA,OAAA;IAKJ,OAAA;MACEpB,SAAAA;MACAiB,YAAAA;MACAI,UAAAA;MACAC,KAAAA;IAJK;EADT,GAAA,qBAAA,OAAA;IASI,aAAA;IACA,cAAA;IACA,OAAA,UAAA,OAAA,SAAA;;;;;;IAKA,OAAA;MACEX,MAAAA;MACA9B,OAAAA;IAFK;;IAMP,cAAA;IACA,OAAA;;IAEA,OAAA;MACE8B,MAAAA;MACAY,UAAAA;MACAL,aAAAA;IAHK;;IAOG;IAAU,OAAA;EAAlB,GAAA,YAAA,GAAA,CAAA,CAAA,GAAA,qBAAA,QAAA;IAMF,MAAA;;IAEA,OAAA;MACEpB,SAAAA;MACAoB,aAAAA;IAFK;;IAQP,OAAA;IACA,cAAA;IACA,MAAA;IACA,SAAA,MAAA,WAAA,MAAA;IACA,OAAA;MACEpB,SAAAA;MACAoB,aAAAA;IAFK;;IAQP,MAAA;IACA,SAAA,MAAA;AACE,UAAA,eAAA;;;AAGE9B,eAAAA,cAAAA,IAAAA,MAAAA,QAAAA,CAAAA;MACD,OAAA;;;MAGA;;IAEH,cAAA,gBAAA,yBAAA;IAKA,OAAA,gBAAA,yBAAA;IAKA,OAAA;MACEU,SAAAA;MACAL,QAAAA;IAFK;;IAML,OAAA;IACA,OAAA;IACA,QAAA;IACA,SAAA;IACA,QAAA,gBAAA,aAAA,SAAA;IACA,MAAA;;IAIU,QAAA;IAAc,GAAA;IAAkB,MAAA;EAAtC,CAAA,GAAA,qBAAA,QAAA;IACM,IAAA;IAAQ,IAAA;IAAQ,IAAA;IAAW,IAAA;EAAjC,CAAA,GAAA,qBAAA,QAAA;IACM,GAAA;EAAN,CAAA,GAAA,qBAAA,QAAA;IACM,GAAA;EAAN,CAAA,GAAA,qBAAA,QAAA;IACM,GAAA;EAAN,CAAA,GAAA,qBAAA,QAAA;IACM,IAAA;IAAO,IAAA;IAAO,IAAA;IAAQ,IAAA;;IAItB,QAAA;IAAc,GAAA;IAAkB,MAAA;EAAtC,CAAA,GAAA,qBAAA,QAAA;IACM,IAAA;IAAQ,IAAA;IAAQ,IAAA;IAAW,IAAA;EAAjC,CAAA,GAAA,qBAAA,QAAA;IACM,GAAA;EAAN,CAAA,GAAA,qBAAA,QAAA;IACM,GAAA;EAAN,CAAA,GAAA,qBAAA,QAAA;IACM,GAAA;EAAN,CAAA,CAAA,CAAA,GAAA,qBAAA,cAAA;IAKJ,MAAA,gBAAA,yBAAA;EADF,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,qBAAA,OAAA;IAYN,OAAA;MACE+B,WAAAA;MACAb,MAAAA;IAFK;EADT,GAAA,QAAA,IAAA,WAAA;AAOI,WAAA,qBAAA,UAAA;;MAGI;MACA;;MAEA;;;IAUR;IACA;IACA;IACA;EAJF,CAAA,IAAA,MAAA,kBAAA,qBAAA,QAAA,SAAA;IAUE,MAAA;IACA,iBAAA;IACA,iBAAA;;EAHF,GAAA,uBAAA;IAME,OAAA;MACEhC,UAAAA;MACAoB,QAAAA;MACAG,QAAAA;MACAK,QAAAA;MACAD,MAAAA;MACA,GAAA,sBAAA;;;;AAIAmB,sBAAAA,OAAAA,SAAAA,aAAAA,CAAAA;IACD;;AAQZ,CAAA;AAED,IAAA,cAAA,CAAA;;;;EAIE7C;AAJmB,MAAA;AAUf,MAAA,uBAAA;;AAKJ,QAAA,mBAAA,yBAAA,YAAA,MAAA;AAEE,QAAA;AAAA,YAAA,wBAAA,WAAA,OAAA,EAAA,KAAA,WAAA,MAAA,cAAA,eAAA,MAAA,OAAA,SAAA,sBAAA;EAAA,CAAA;AAKF,QAAA,WAAA,wBAAA,yBAAA,YAAA,MAAA;AACuC,QAAA;AAAA,YAAA,yBAAA,WAAA,OAAA,EAAA,KAAA,WAAA,MAAA,cAAA,eAAA,MAAA,OAAA,SAAA,uBAAA,QAAA;;AAOvC,QAAA,iBAAA,yBAAA,yBAAA,YAAA,MAAA;AACuC,QAAA;AAAA,YAAA,yBAAA,WAAA,OAAA,EAAA,KAAA,WAAA,MAAA,cAAA,eAAA,MAAA,OAAA,SAAA,uBAAA,kBAAA;;;AAQrC,UAAA,UAAA,eAAA,OAAA,SAAA,YAAA,MAAA;AACA8C,eAAAA,OAAAA,SAAAA,QAAAA,MAAAA,IAAAA;;AAGF,QAAA,2BAAA,sBAAA,MAAA;AACE,QAAA,eAAA,oBAAA,QAAA,iBAAA,OAAA;AACE,YAAA,YAAA,WAAA,KAAA,UAAA;AACE,YAAA;AAAA,eAAA,KAAA,YAAA,WAAA,EAAA,SAAA,QAAA,wBAAA,iBAAA,UAAA,OAAA,SAAA,sBAAA,SAAA;MAAA,CAAA;AAIF,aAAA,aAAA,OAAA,SAAA,UAAA;IACD;AACD,WAAA;EACD,GAAA,CAAA,aAAA,oBAAA,OAAA,SAAA,iBAAA,OAAA,UAAA,CAAA;AAED,MAAA,CAAA,eAAA,CAAA,kBAAA;AACE,WAAA;EACD;;AAEuD,QAAA;AACtD,UAAA,SAAA,wBAAA,aAAA,OAAA,SAAA,UAAA,YAAA,WAAA,MAAA,OAAA,wBAAA,IAAA,MAAA,6BAAA;AAIA,UAAA,yBAAA,YAAA;;MAGEC,QAAAA;;MAEAC,WAAAA;QAAAA,GAAAA,YAAAA,MAAAA;QAEEC;MAFS;;;;;;MAUXC,eAAAA;;;;IAOE,OAAA;MACEhC,SAAAA;;MAEAnB,UAAAA;MACAyB,KAAAA;MACAL,QAAAA;IALK;EADT,GAAA,eAAA,GAAA,qBAAA,OAAA;IAYE,OAAA;MACED,SAAAA;IADK;;IAKL,OAAA;MACEqB,cAAAA;MACAnB,SAAAA;MACAiB,YAAAA;MACAD,gBAAAA;IAJK;EADT,GAAA,qBAAA,MAAA;IASI,OAAA;MACEe,YAAAA;IADK;;IAKL,OAAA;MACE7B,QAAAA;MACAJ,SAAAA;MACAgB,UAAAA;IAHK;;IAUT,OAAA;MACEhB,SAAAA;MACAkC,cAAAA;MACAC,YAAAA;MACAC,YAAAA;;QAEEC,YAAAA;QACAC;QACAC;QACAC,OAAAA;MAJ8B,CAAA;MAMhCC,YAAAA;IAXK;EADT,GAAA,oBAAA,WAAA,CAAA,CAAA,GAAA,qBAAA,OAAA;IAmBA,OAAA;MACEpB,cAAAA;MACAnB,SAAAA;MACAiB,YAAAA;MACAD,gBAAAA;IAJK;EADT,GAAA,eAAA,qBAAA,MAAA,MAAA,aAAA,CAAA,GAAA,qBAAA,OAAA;IAWE,OAAA;MACEhB,SAAAA;MACAiB,YAAAA;MACAD,gBAAAA;IAHK;EADT,GAAA,iBAAA,KAAA,qBAAA,MAAA,MAAA,IAAA,KAAA,iBAAA,aAAA,EAAA,mBAAA,CAAA,CAAA,CAAA,GAAA,qBAAA,OAAA;IAcA,OAAA;;MAEElB,SAAAA;MACAnB,UAAAA;MACAyB,KAAAA;MACAL,QAAAA;IALK;EADT,GAAA,SAAA,GAAA,qBAAA,OAAA;IAYE,OAAA;MACED,SAAAA;MACAE,SAAAA;MACAqB,UAAAA;MACAC,KAAAA;MACAL,YAAAA;IALK;EADT,GAAA,qBAAA,QAAA;IAUI,MAAA;IACA,SAAA;IACA,UAAA,iBAAA,gBAAA;IACA,OAAA;;IAAO;EAJT,GAAA,SAAA,GAAA,KAAA,qBAAA,QAAA;IAWE,MAAA;IACA,SAAA,MAAA,YAAA,kBAAA,WAAA;IACA,OAAA;;;IAAO;EAHT,GAAA,YAAA,GAAA,KAAA,qBAAA,QAAA;IAWE,MAAA;IACA,SAAA,MAAA,YAAA,aAAA,WAAA;IACA,OAAA;;IAAO;EAHT,GAAA,OAAA,GAAA,KAAA,qBAAA,QAAA;IAUE,MAAA;IACA,SAAA,MAAA,YAAA,cAAA,WAAA;IACA,OAAA;;IAAO;EAHT,GAAA,QAAA,GAAA,KAAA,qBAAA,QAAA;IAUE,MAAA;IACA,SAAA,MAAA;AAAe,UAAA;AAEb,UAAA,YAAA,MAAA,gBAAA,cAAA,SAAA,wBAAA,YAAA,MAAA,cAAA,OAAA,SAAA,sBAAA,4BAAA,aAAA;AAKE;MACD;AAED,UAAA,YAAA,MAAA,SAAA,QAAA;;MAEC,OAAA;AACC,cAAA,yBAAA,YAAA;AAEAuB,oBAAAA,MAAAA;UAAAA,GAAAA;UAEEC,SAAAA,MAAAA;AACE,mBAAA,IAAA,QAAA,MAAA;YAEC,CAAA;;UAEHC,WAAAA;;;UAGAC,MAAAA;UACAhB,QAAAA;UACAC,WAAAA;YAAAA,GAAAA,YAAAA,MAAAA;YAEEC;UAFS;;MAKd;;IAEH,OAAA;;IAAO;EApCT,GAAA,YAAA,MAAA,WAAA,YAAA,YAAA,WAAA,KAAA,SAAA,GAAA,KAAA,WAAA,WAAA,KAAA,YAAA,MAAA,WAAA,UAAA,qBAAA,QAAA;IA6CI,MAAA;IACA,SAAA,MAAA;AACE,UAAA,CAAA,YAAA,MAAA,OAAA;;MAEC,OAAA;;MAEA;;IAEH,OAAA;;IAAO;EATT,GAAA,YAAA,MAAA,WAAA,UAAA,YAAA,WAAA,QAAA,IAAA,qBAAA,SAAA,MAAA,kBAAA,qBAAA,QAAA;IAmBI,OAAA,wBAAA,OAAA,uBAAA;IACA,OAAA;MAAST,mBAAAA;;;AAEP,YAAA,YAAA,WAAA,KAAA,OAAA,EAAA,SAAA,EAAA,OAAA,KAAA;;IAKD;;IAEO,KAAA;IAAO,OAAA;EAAf,CAAA,GAAA,WAAA,IAAA,eAAA,qBAAA,UAAA;;;;IAWN,OAAA;;MAEEtB,SAAAA;MACAnB,UAAAA;MACAyB,KAAAA;MACAL,QAAAA;IALK;EADT,GAAA,eAAA,GAAA,qBAAA,OAAA;IAYE,OAAA;MACED,SAAAA;IADK;EADT,GAAA,qBAAA,UAAA;IAMI,OAAA;;IAEA,iBAAA,CAAA;;EAHF,CAAA,CAAA,GAAA,qBAAA,OAAA;IAQA,OAAA;;MAEEA,SAAAA;MACAnB,UAAAA;MACAyB,KAAAA;MACAL,QAAAA;IALK;EADT,GAAA,gBAAA,GAAA,qBAAA,OAAA;IAYE,OAAA;MACED,SAAAA;IADK;EADT,GAAA,qBAAA,UAAA;IAMI,OAAA;IACA,OAAA;IACA,iBAAA;MACE8C,UAAAA;IADe;EAHnB,CAAA,CAAA,CAAA;AAUP;AAED,IAAA,mBAAA,CAAA;EAA4BC;AAAF,MAAA;;;;;;AA+BxB,SAAA,qBAAA,WAAA,MAAA,qBAAA,UAAA;IAGM,OAAA;;MAEEC,SAAAA,WAAAA,IAAAA;IAFK;;IAQP,OAAA;;MAEEA,SAAAA,cAAAA,IAAAA;IAFK;;IAQP,OAAA;;MAEEA,SAAAA,YAAAA,IAAAA;IAFK;;IAQP,OAAA;;MAEEC,OAAAA;MACAb,YAAAA;MACAY,SAAAA,WAAAA,IAAAA;IAJK;;IAUP,OAAA;;MAEEA,SAAAA,cAAAA,IAAAA;IAFK;EADT,GAAA,aAAA,qBAAA,MAAA,MAAA,KAAA,aAAA,GAAA,CAAA,CAAA;AAUL;AASD,IAAA,WAAA,YAAA,CAAA;;;;EAKID;AAJD,MAAA;AAKoB,MAAA,wBAAA,wBAAA,wBAAA;AACnB,QAAA,aAAA,yBAAA,yBAAA,YAAA,MAAA;AAGI,QAAA;;;AAGJ,QAAA,aAAA,yBAAA,YAAA,MAAA;AAEE,QAAA;;EAAA,CAAA;AAGF,QAAA,WAAA,yBAAA,yBAAA,YAAA,MAAA;AACuC,QAAA;;;AAIvC,QAAA,cAAA,yBAAA,yBAAA,YAAA,MAAA;AACuC,QAAA;;;AAIvC,QAAA,iBAAA,yBAAA,yBAAA,YAAA,MAAA;AACuC,QAAA;;;;AAKrC,WAAA;EACD;;IAIG,MAAA;IACA,cAAA,4BAAA;;IAIA,OAAA;MACE7C,SAAAA;;MAEAG,QAAAA;MACAP,YAAAA,cAAAA,kBAAAA,yBAAAA;IAJK;;IASL,OAAA;MACEe,MAAAA;MACA9B,OAAAA;MACAY,QAAAA;;;;;QAKE6C,OAAAA;MAJ8B,CAAA;MAMhCtC,SAAAA;MACAiB,YAAAA;MACAD,gBAAAA;MACAiB,YAAAA;MACAC,YAAAA,UAAAA,MAAAA;MACAa,OAAAA,UAAAA,UAAAA;IAfK;EADT,GAAA,aAAA,GAAA,aAAA,qBAAA,OAAA;IAuBI,OAAA;MACEpC,MAAAA;MACAlB,QAAAA;;MAEAO,SAAAA;MACAiB,YAAAA;MACAgB,YAAAA;MACAnC,SAAAA;IAPK;EADT,GAAA,UAAA,IAAA,MAAA,qBAAA,MAAA;IAeA,OAAA;MACEA,SAAAA;IADK;;AAQd,CAAA;AAGHkD,SAAAA,cAAAA;AAGA,SAAA,OAAA;AAAA;;;ACz0CO,IAAAC,sBAAA,QAAA,WAAA;AAGC,SAAA;AACD,IAAA;AAGA,IAAAC,2BAAA,QAAA,WAAA;AAGC,SAAA;AACD,IAAA;", "names": ["characterMap", "À", "Á", "Â", "Ã", "Ä", "Å", "Ấ", "Ắ", "Ẳ", "Ẵ", "Ặ", "<PERSON>", "Ầ", "Ằ", "Ȃ", "Ç", "Ḉ", "È", "É", "Ê", "Ë", "Ế", "Ḗ", "Ề", "Ḕ", "Ḝ", "Ȇ", "Ì", "Í", "Î", "Ï", "Ḯ", "Ȋ", "Ð", "Ñ", "Ò", "<PERSON>", "Ô", "Õ", "Ö", "Ø", "Ố", "Ṍ", "Ṓ", "Ȏ", "Ù", "Ú", "Û", "Ü", "Ý", "à", "á", "â", "ã", "ä", "å", "ấ", "ắ", "ẳ", "ẵ", "ặ", "æ", "ầ", "ằ", "ȃ", "ç", "ḉ", "è", "é", "ê", "ë", "ế", "ḗ", "ề", "ḕ", "ḝ", "ȇ", "ì", "í", "î", "ï", "ḯ", "ȋ", "ð", "ñ", "ò", "ó", "ô", "õ", "ö", "ø", "ố", "ṍ", "ṓ", "ȏ", "ù", "ú", "û", "ü", "ý", "ÿ", "Ā", "ā", "Ă", "ă", "Ą", "ą", "Ć", "ć", "Ĉ", "ĉ", "Ċ", "ċ", "Č", "č", "C̆", "c̆", "Ď", "ď", "Đ", "đ", "Ē", "ē", "Ĕ", "ĕ", "Ė", "ė", "Ę", "ę", "Ě", "ě", "Ĝ", "Ǵ", "ĝ", "ǵ", "Ğ", "ğ", "Ġ", "ġ", "Ģ", "ģ", "Ĥ", "ĥ", "Ħ", "ħ", "Ḫ", "ḫ", "Ĩ", "ĩ", "Ī", "ī", "Ĭ", "ĭ", "Į", "į", "İ", "ı", "Ĳ", "ĳ", "Ĵ", "ĵ", "Ķ", "ķ", "Ḱ", "ḱ", "K̆", "k̆", "Ĺ", "ĺ", "Ļ", "ļ", "Ľ", "ľ", "Ŀ", "ŀ", "Ł", "ł", "Ḿ", "ḿ", "M̆", "m̆", "Ń", "ń", "Ņ", "ņ", "Ň", "ň", "ŉ", "N̆", "n̆", "Ō", "<PERSON>", "Ŏ", "ŏ", "Ő", "ő", "Œ", "œ", "P̆", "p̆", "Ŕ", "ŕ", "Ŗ", "ŗ", "Ř", "ř", "R̆", "r̆", "Ȓ", "ȓ", "Ś", "ś", "Ŝ", "ŝ", "Ş", "Ș", "ș", "ş", "Š", "š", "Ţ", "ţ", "ț", "Ț", "Ť", "ť", "Ŧ", "ŧ", "T̆", "t̆", "Ũ", "ũ", "Ū", "ū", "Ŭ", "ŭ", "Ů", "ů", "Ű", "ű", "Ų", "ų", "Ȗ", "ȗ", "V̆", "v̆", "Ŵ", "ŵ", "Ẃ", "ẃ", "X̆", "x̆", "Ŷ", "ŷ", "Ÿ", "Y̆", "y̆", "Ź", "ź", "Ż", "ż", "Ž", "ž", "ſ", "ƒ", "Ơ", "ơ", "Ư", "ư", "Ǎ", "ǎ", "Ǐ", "ǐ", "Ǒ", "ǒ", "Ǔ", "ǔ", "Ǖ", "ǖ", "Ǘ", "ǘ", "Ǚ", "ǚ", "Ǜ", "ǜ", "Ứ", "ứ", "Ṹ", "ṹ", "Ǻ", "ǻ", "Ǽ", "ǽ", "Ǿ", "ǿ", "Þ", "þ", "Ṕ", "ṕ", "Ṥ", "ṥ", "X́", "x́", "Ѓ", "ѓ", "Ќ", "ќ", "A̋", "a̋", "E̋", "e̋", "I̋", "i̋", "Ǹ", "ǹ", "Ồ", "ồ", "Ṑ", "ṑ", "Ừ", "ừ", "Ẁ", "ẁ", "Ỳ", "ỳ", "Ȁ", "ȁ", "Ȅ", "ȅ", "Ȉ", "ȉ", "Ȍ", "ȍ", "Ȑ", "ȑ", "Ȕ", "ȕ", "B̌", "b̌", "Č̣", "č̣", "Ê̌", "ê̌", "F̌", "f̌", "Ǧ", "ǧ", "Ȟ", "ȟ", "J̌", "ǰ", "Ǩ", "ǩ", "M̌", "m̌", "P̌", "p̌", "Q̌", "q̌", "Ř̩", "ř̩", "Ṧ", "ṧ", "V̌", "v̌", "W̌", "w̌", "X̌", "x̌", "Y̌", "y̌", "A̧", "a̧", "B̧", "b̧", "Ḑ", "ḑ", "Ȩ", "ȩ", "Ɛ̧", "ɛ̧", "Ḩ", "ḩ", "I̧", "i̧", "Ɨ̧", "ɨ̧", "M̧", "m̧", "O̧", "o̧", "Q̧", "q̧", "U̧", "u̧", "X̧", "x̧", "Z̧", "z̧", "chars", "Object", "keys", "join", "allAccents", "RegExp", "removeAccents", "str", "replace", "match", "rankings", "CASE_SENSITIVE_EQUAL", "EQUAL", "STARTS_WITH", "WORD_STARTS_WITH", "CONTAINS", "ACRONYM", "MATCHES", "NO_MATCH", "rankItem", "item", "value", "options", "_options$threshold", "threshold", "accessors", "rank", "getMatchRanking", "rankedValue", "accessorIndex", "accessorThreshold", "passed", "valuesToRank", "getAllValuesToRank", "rankingInfo", "i", "length", "rankValue", "newRank", "itemValue", "minRanking", "maxRanking", "attributes", "Math", "min", "testString", "stringToRank", "prepareValueForComparison", "toLowerCase", "startsWith", "includes", "getAcronym", "getClosenessRanking", "string", "acronym", "wordsInString", "split", "for<PERSON>ach", "wordInString", "splitByHyphenWords", "splitByHyphenWord", "substr", "matchingInOrderCharCount", "char<PERSON><PERSON>ber", "findMatchingCharacter", "matchChar", "index", "j", "J", "stringChar", "getRanking", "spread", "spreadPercentage", "inOrderPercentage", "ranking", "firstIndex", "I", "found", "prepareValueForComparison", "value", "_ref", "keepDiacritics", "removeAccents", "getItemValues", "item", "accessor", "accessorFn", "Array", "isArray", "String", "getAllValuesToRank", "accessors", "allValues", "j", "J", "length", "attributes", "getAccessorAttributes", "itemValues", "i", "I", "push", "itemValue", "defaultKeyAttributes", "maxRanking", "Infinity", "minRanking", "getItem", "key", "itemValue", "localStorage", "JSON", "parse", "undefined", "useLocalStorage", "defaultValue", "value", "setValue", "useState", "React", "useEffect", "initialValue", "setter", "useCallback", "updater", "old", "newVal", "setItem", "stringify", "DoubleIndexedKV", "Registry", "ClassRegistry", "CustomTransformerRegistry", "__read", "walker", "__spread<PERSON><PERSON>y", "__read", "getType", "isUndefined", "isNull", "isPlainObject", "isArray", "getType", "isNull", "isUndefined", "isArray", "isPlainObject", "SuperJSON", "__assign", "__spread<PERSON><PERSON>y", "__read", "background", "backgroundAlt", "foreground", "gray", "grayAlt", "inputBackgroundColor", "inputTextColor", "success", "danger", "active", "paused", "warning", "useMediaQuery", "query", "isMatch", "setIsMatch", "useState", "window", "matchMedia", "matches", "React", "useEffect", "matcher", "onChange", "addListener", "removeListener", "getQueryStatusColor", "queryState", "observerCount", "isStale", "theme", "fetchStatus", "active", "gray", "paused", "warning", "success", "getQueryStatusLabel", "query", "state", "getObserversCount", "styled", "type", "newStyles", "queries", "forwardRef", "style", "rest", "ref", "useTheme", "mediaStyles", "Object", "entries", "reduce", "current", "key", "value", "useMediaQuery", "createElement", "useIsMounted", "mountedRef", "useRef", "isMounted", "useCallback", "React", "useEffect", "displayValue", "beautify", "json", "SuperJSON", "serialize", "JSON", "stringify", "undefined", "getStatusRank", "q", "queryHashSort", "a", "b", "queryHash", "localeCompare", "dateSort", "dataUpdatedAt", "statusAndDateSort", "sortFns", "minPanelSize", "defaultPanelSize", "sides", "top", "bottom", "left", "right", "isVerticalSide", "side", "includes", "getOppositeSide", "getSidedProp", "prop", "char<PERSON>t", "toUpperCase", "slice", "getSidePanelStyle", "position", "height", "width", "devtoolsTheme", "isOpen", "isResizing", "panelStyle", "oppositeSide", "borderSide", "isVertical", "direction", "transform<PERSON><PERSON>in", "boxShadow", "zIndex", "visibility", "transition", "opacity", "pointerEvents", "transform", "max<PERSON><PERSON><PERSON>", "maxHeight", "getResizeHandleStyle", "marginSide", "cursor", "Panel", "styled", "_props", "theme", "fontSize", "fontFamily", "display", "backgroundColor", "background", "color", "foreground", "flexDirection", "ActiveQueryPanel", "flex", "overflow", "height", "borderTop", "gray", "<PERSON><PERSON>", "props", "appearance", "fontWeight", "border", "borderRadius", "padding", "opacity", "disabled", "undefined", "cursor", "Query<PERSON><PERSON>s", "flexWrap", "gap", "Query<PERSON>ey", "alignItems", "textShadow", "Code", "Input", "inputBackgroundColor", "inputTextColor", "lineHeight", "Select", "WebkitAppearance", "backgroundImage", "backgroundRepeat", "backgroundPosition", "backgroundSize", "ScreenReader", "text", "position", "width", "height", "overflow", "fontFamily", "fontSize", "lineHeight", "outline", "wordBreak", "color", "cursor", "font", "background", "border", "padding", "value", "navigator", "setTimeout", "console", "marginLeft", "paddingLeft", "borderLeft", "style", "display", "transition", "transform", "alignItems", "position", "top", "verticalAlign", "result", "subEntries", "subEntryPages", "expanded", "copyable", "pageSize", "renderer", "defaultExpanded", "type", "label", "handleEntry", "Logo", "props", "panelProps", "closeButtonProps", "toggleButtonProps", "position", "errorTypes", "width", "newSize", "document", "setIsResolvedOpen", "ref", "parentElement", "paddingRight", "window", "Object", "onClick", "devtoolsTheme", "isOpen", "height", "panelStyle", "onToggleClick", "background", "border", "padding", "zIndex", "display", "fontSize", "margin", "cursor", "top", "right", "left", "bottom", "ReactQueryDevtoolsPanel", "context", "__html", "flex", "minHeight", "maxHeight", "overflow", "flexDirection", "justifyContent", "alignItems", "marginRight", "marginBottom", "marginInlineStart", "flexWrap", "gap", "min<PERSON><PERSON><PERSON>", "overflowY", "onCloseClick", "promise", "status", "fetchMeta", "__previousQueryOptions", "cancelRefetch", "lineHeight", "borderRadius", "fontWeight", "textShadow", "queryState", "isStale", "observerCount", "theme", "flexShrink", "activeQuery", "queryFn", "cacheTime", "data", "query<PERSON><PERSON>", "queryCache", "opacity", "color", "QueryRow", "ReactQueryDevtools", "ReactQueryDevtoolsPanel"]}