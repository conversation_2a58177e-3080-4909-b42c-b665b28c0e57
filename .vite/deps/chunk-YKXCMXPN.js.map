{"version": 3, "sources": ["../../node_modules/@zendeskgarden/container-selection/dist/index.esm.js"], "sourcesContent": ["/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { useReducer, useEffect } from 'react';\nimport { getControlledValue, composeEventHandlers, KEYS } from '@zendeskgarden/container-utilities';\nimport PropTypes from 'prop-types';\n\nconst stateReducer = (state, action) => {\n  switch (action.type) {\n    case 'END':\n    case 'HOME':\n    case 'FOCUS':\n    case 'INCREMENT':\n    case 'DECREMENT':\n      {\n        return {\n          ...state,\n          focusedItem: action.payload\n        };\n      }\n    case 'MOUSE_SELECT':\n      {\n        return {\n          ...state,\n          selectedItem: action.payload,\n          focusedItem: undefined\n        };\n      }\n    case 'KEYBOARD_SELECT':\n      {\n        return {\n          ...state,\n          selectedItem: action.payload\n        };\n      }\n    case 'EXIT_WIDGET':\n      {\n        return {\n          ...state,\n          focusedItem: undefined\n        };\n      }\n    default:\n      return state;\n  }\n};\n\nconst useSelection = function (_temp) {\n  let {\n    direction = 'horizontal',\n    defaultFocusedIndex = 0,\n    defaultSelectedIndex,\n    rtl,\n    selectedItem,\n    focusedItem,\n    onSelect,\n    onFocus\n  } = _temp === void 0 ? {} : _temp;\n  const isSelectedItemControlled = selectedItem !== undefined;\n  const isFocusedItemControlled = focusedItem !== undefined;\n  const refs = [];\n  const items = [];\n  const [state, dispatch] = useReducer(stateReducer, {\n    selectedItem,\n    focusedItem\n  });\n  const controlledFocusedItem = getControlledValue(focusedItem, state.focusedItem);\n  const controlledSelectedItem = getControlledValue(selectedItem, state.selectedItem);\n  useEffect(() => {\n    if (controlledFocusedItem !== undefined) {\n      const focusedIndex = items.indexOf(controlledFocusedItem);\n      refs[focusedIndex] && refs[focusedIndex].current.focus();\n    }\n  }, [controlledFocusedItem]);\n  useEffect(() => {\n    if (selectedItem === undefined && defaultSelectedIndex !== undefined) {\n      onSelect && onSelect(items[defaultSelectedIndex]);\n      if (!isSelectedItemControlled) {\n        dispatch({\n          type: 'KEYBOARD_SELECT',\n          payload: items[defaultSelectedIndex]\n        });\n      }\n    }\n  }, []);\n  const getContainerProps = function (_temp2) {\n    let {\n      role = 'listbox',\n      ...other\n    } = _temp2 === void 0 ? {} : _temp2;\n    return {\n      role: role === null ? undefined : role,\n      'data-garden-container-id': 'containers.selection',\n      'data-garden-container-version': '2.0.5',\n      ...other\n    };\n  };\n  const getItemProps = _ref => {\n    let {\n      selectedAriaKey = 'aria-selected',\n      role = 'option',\n      onFocus: onFocusCallback,\n      onKeyDown,\n      onClick,\n      item,\n      focusRef,\n      refKey = 'ref',\n      ...other\n    } = _ref;\n    refs.push(focusRef);\n    items.push(item);\n    const isSelected = controlledSelectedItem === item;\n    const isFocused = controlledFocusedItem === undefined ? isSelected : controlledFocusedItem === item;\n    const tabIndex = isFocused || controlledSelectedItem === undefined && controlledFocusedItem === undefined && items.indexOf(item) === defaultFocusedIndex ? 0 : -1;\n    const verticalDirection = direction === 'vertical' || direction === 'both';\n    const horizontalDirection = direction === 'horizontal' || direction === 'both';\n    const handleFocus = () => {\n      onFocus && onFocus(item);\n      if (!isFocusedItemControlled) {\n        dispatch({\n          type: 'FOCUS',\n          payload: item\n        });\n      }\n    };\n    const handleClick = () => {\n      onSelect && onSelect(item);\n      onFocus && onFocus();\n      if (!isSelectedItemControlled) {\n        dispatch({\n          type: 'MOUSE_SELECT',\n          payload: item\n        });\n      }\n    };\n    const handleKeyDown = event => {\n      let nextIndex;\n      let currentIndex;\n      if (isFocusedItemControlled) {\n        currentIndex = items.indexOf(focusedItem);\n      } else {\n        currentIndex = items.indexOf(state.focusedItem || state.selectedItem);\n      }\n      const onIncrement = () => {\n        nextIndex = currentIndex + 1;\n        if (currentIndex === items.length - 1) {\n          nextIndex = 0;\n        }\n        !isFocusedItemControlled && dispatch({\n          type: 'INCREMENT',\n          payload: items[nextIndex]\n        });\n        onFocus && onFocus(items[nextIndex]);\n      };\n      const onDecrement = () => {\n        nextIndex = currentIndex - 1;\n        if (currentIndex === 0) {\n          nextIndex = items.length - 1;\n        }\n        !isFocusedItemControlled && dispatch({\n          type: 'DECREMENT',\n          payload: items[nextIndex]\n        });\n        onFocus && onFocus(items[nextIndex]);\n      };\n      const hasModifierKeyPressed = event.ctrlKey || event.metaKey || event.shiftKey || event.altKey;\n      if (!hasModifierKeyPressed) {\n        if (event.key === KEYS.UP && verticalDirection || event.key === KEYS.LEFT && horizontalDirection) {\n          if (rtl && horizontalDirection) {\n            onIncrement();\n          } else {\n            onDecrement();\n          }\n          event.preventDefault();\n        } else if (event.key === KEYS.DOWN && verticalDirection || event.key === KEYS.RIGHT && horizontalDirection) {\n          if (rtl && horizontalDirection) {\n            onDecrement();\n          } else {\n            onIncrement();\n          }\n          event.preventDefault();\n        } else if (event.key === KEYS.HOME) {\n          if (!isFocusedItemControlled) {\n            dispatch({\n              type: 'HOME',\n              payload: items[0]\n            });\n          }\n          onFocus && onFocus(items[0]);\n          event.preventDefault();\n        } else if (event.key === KEYS.END) {\n          if (!isFocusedItemControlled) {\n            dispatch({\n              type: 'END',\n              payload: items[items.length - 1]\n            });\n          }\n          onFocus && onFocus(items[items.length - 1]);\n          event.preventDefault();\n        } else if (event.key === KEYS.SPACE || event.key === KEYS.ENTER) {\n          onSelect && onSelect(item);\n          if (!isSelectedItemControlled) {\n            dispatch({\n              type: 'KEYBOARD_SELECT',\n              payload: item\n            });\n          }\n          event.preventDefault();\n        }\n      }\n    };\n    const onBlur = event => {\n      if (event.target.tabIndex === 0) {\n        if (!isFocusedItemControlled) {\n          dispatch({\n            type: 'EXIT_WIDGET'\n          });\n        }\n        onFocus && onFocus();\n      }\n    };\n    return {\n      role: role === null ? undefined : role,\n      tabIndex,\n      [selectedAriaKey]: selectedAriaKey ? isSelected : undefined,\n      [refKey]: focusRef,\n      onFocus: composeEventHandlers(onFocusCallback, handleFocus),\n      onClick: composeEventHandlers(onClick, handleClick),\n      onKeyDown: composeEventHandlers(onKeyDown, handleKeyDown),\n      onBlur,\n      ...other\n    };\n  };\n  return {\n    focusedItem: controlledFocusedItem,\n    selectedItem: controlledSelectedItem,\n    getItemProps,\n    getContainerProps\n  };\n};\n\nconst SelectionContainer = _ref => {\n  let {\n    children,\n    render = children,\n    ...options\n  } = _ref;\n  return React.createElement(React.Fragment, null, render(useSelection(options)));\n};\nSelectionContainer.defaultProps = {\n  direction: 'horizontal',\n  defaultFocusedIndex: 0\n};\nSelectionContainer.propTypes = {\n  children: PropTypes.func,\n  render: PropTypes.func,\n  rtl: PropTypes.bool,\n  direction: PropTypes.oneOf(['horizontal', 'vertical', 'both']),\n  defaultFocusedIndex: PropTypes.number,\n  defaultSelectedIndex: PropTypes.number,\n  focusedItem: PropTypes.any,\n  selectedItem: PropTypes.any,\n  onSelect: PropTypes.func,\n  onFocus: PropTypes.func\n};\n\nexport { SelectionContainer, useSelection };\n"], "mappings": ";;;;;;;;;;;;;;;;AAOA,mBAA6C;AAE7C,wBAAsB;AAEtB,IAAM,eAAe,CAAC,OAAO,WAAW;AACtC,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,aACH;AACE,aAAO;AAAA,QACL,GAAG;AAAA,QACH,aAAa,OAAO;AAAA,MACtB;AAAA,IACF;AAAA,IACF,KAAK,gBACH;AACE,aAAO;AAAA,QACL,GAAG;AAAA,QACH,cAAc,OAAO;AAAA,QACrB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACF,KAAK,mBACH;AACE,aAAO;AAAA,QACL,GAAG;AAAA,QACH,cAAc,OAAO;AAAA,MACvB;AAAA,IACF;AAAA,IACF,KAAK,eACH;AACE,aAAO;AAAA,QACL,GAAG;AAAA,QACH,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACF;AACE,aAAO;AAAA,EACX;AACF;AAEA,IAAM,eAAe,SAAU,OAAO;AACpC,MAAI;AAAA,IACF,YAAY;AAAA,IACZ,sBAAsB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,QAAM,2BAA2B,iBAAiB;AAClD,QAAM,0BAA0B,gBAAgB;AAChD,QAAM,OAAO,CAAC;AACd,QAAM,QAAQ,CAAC;AACf,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAW,cAAc;AAAA,IACjD;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,wBAAwB,mBAAmB,aAAa,MAAM,WAAW;AAC/E,QAAM,yBAAyB,mBAAmB,cAAc,MAAM,YAAY;AAClF,8BAAU,MAAM;AACd,QAAI,0BAA0B,QAAW;AACvC,YAAM,eAAe,MAAM,QAAQ,qBAAqB;AACxD,WAAK,YAAY,KAAK,KAAK,YAAY,EAAE,QAAQ,MAAM;AAAA,IACzD;AAAA,EACF,GAAG,CAAC,qBAAqB,CAAC;AAC1B,8BAAU,MAAM;AACd,QAAI,iBAAiB,UAAa,yBAAyB,QAAW;AACpE,kBAAY,SAAS,MAAM,oBAAoB,CAAC;AAChD,UAAI,CAAC,0BAA0B;AAC7B,iBAAS;AAAA,UACP,MAAM;AAAA,UACN,SAAS,MAAM,oBAAoB;AAAA,QACrC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,oBAAoB,SAAU,QAAQ;AAC1C,QAAI;AAAA,MACF,OAAO;AAAA,MACP,GAAG;AAAA,IACL,IAAI,WAAW,SAAS,CAAC,IAAI;AAC7B,WAAO;AAAA,MACL,MAAM,SAAS,OAAO,SAAY;AAAA,MAClC,4BAA4B;AAAA,MAC5B,iCAAiC;AAAA,MACjC,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,eAAe,UAAQ;AAC3B,QAAI;AAAA,MACF,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT,GAAG;AAAA,IACL,IAAI;AACJ,SAAK,KAAK,QAAQ;AAClB,UAAM,KAAK,IAAI;AACf,UAAM,aAAa,2BAA2B;AAC9C,UAAM,YAAY,0BAA0B,SAAY,aAAa,0BAA0B;AAC/F,UAAM,WAAW,aAAa,2BAA2B,UAAa,0BAA0B,UAAa,MAAM,QAAQ,IAAI,MAAM,sBAAsB,IAAI;AAC/J,UAAM,oBAAoB,cAAc,cAAc,cAAc;AACpE,UAAM,sBAAsB,cAAc,gBAAgB,cAAc;AACxE,UAAM,cAAc,MAAM;AACxB,iBAAW,QAAQ,IAAI;AACvB,UAAI,CAAC,yBAAyB;AAC5B,iBAAS;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,cAAc,MAAM;AACxB,kBAAY,SAAS,IAAI;AACzB,iBAAW,QAAQ;AACnB,UAAI,CAAC,0BAA0B;AAC7B,iBAAS;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,gBAAgB,WAAS;AAC7B,UAAI;AACJ,UAAI;AACJ,UAAI,yBAAyB;AAC3B,uBAAe,MAAM,QAAQ,WAAW;AAAA,MAC1C,OAAO;AACL,uBAAe,MAAM,QAAQ,MAAM,eAAe,MAAM,YAAY;AAAA,MACtE;AACA,YAAM,cAAc,MAAM;AACxB,oBAAY,eAAe;AAC3B,YAAI,iBAAiB,MAAM,SAAS,GAAG;AACrC,sBAAY;AAAA,QACd;AACA,SAAC,2BAA2B,SAAS;AAAA,UACnC,MAAM;AAAA,UACN,SAAS,MAAM,SAAS;AAAA,QAC1B,CAAC;AACD,mBAAW,QAAQ,MAAM,SAAS,CAAC;AAAA,MACrC;AACA,YAAM,cAAc,MAAM;AACxB,oBAAY,eAAe;AAC3B,YAAI,iBAAiB,GAAG;AACtB,sBAAY,MAAM,SAAS;AAAA,QAC7B;AACA,SAAC,2BAA2B,SAAS;AAAA,UACnC,MAAM;AAAA,UACN,SAAS,MAAM,SAAS;AAAA,QAC1B,CAAC;AACD,mBAAW,QAAQ,MAAM,SAAS,CAAC;AAAA,MACrC;AACA,YAAM,wBAAwB,MAAM,WAAW,MAAM,WAAW,MAAM,YAAY,MAAM;AACxF,UAAI,CAAC,uBAAuB;AAC1B,YAAI,MAAM,QAAQ,KAAK,MAAM,qBAAqB,MAAM,QAAQ,KAAK,QAAQ,qBAAqB;AAChG,cAAI,OAAO,qBAAqB;AAC9B,wBAAY;AAAA,UACd,OAAO;AACL,wBAAY;AAAA,UACd;AACA,gBAAM,eAAe;AAAA,QACvB,WAAW,MAAM,QAAQ,KAAK,QAAQ,qBAAqB,MAAM,QAAQ,KAAK,SAAS,qBAAqB;AAC1G,cAAI,OAAO,qBAAqB;AAC9B,wBAAY;AAAA,UACd,OAAO;AACL,wBAAY;AAAA,UACd;AACA,gBAAM,eAAe;AAAA,QACvB,WAAW,MAAM,QAAQ,KAAK,MAAM;AAClC,cAAI,CAAC,yBAAyB;AAC5B,qBAAS;AAAA,cACP,MAAM;AAAA,cACN,SAAS,MAAM,CAAC;AAAA,YAClB,CAAC;AAAA,UACH;AACA,qBAAW,QAAQ,MAAM,CAAC,CAAC;AAC3B,gBAAM,eAAe;AAAA,QACvB,WAAW,MAAM,QAAQ,KAAK,KAAK;AACjC,cAAI,CAAC,yBAAyB;AAC5B,qBAAS;AAAA,cACP,MAAM;AAAA,cACN,SAAS,MAAM,MAAM,SAAS,CAAC;AAAA,YACjC,CAAC;AAAA,UACH;AACA,qBAAW,QAAQ,MAAM,MAAM,SAAS,CAAC,CAAC;AAC1C,gBAAM,eAAe;AAAA,QACvB,WAAW,MAAM,QAAQ,KAAK,SAAS,MAAM,QAAQ,KAAK,OAAO;AAC/D,sBAAY,SAAS,IAAI;AACzB,cAAI,CAAC,0BAA0B;AAC7B,qBAAS;AAAA,cACP,MAAM;AAAA,cACN,SAAS;AAAA,YACX,CAAC;AAAA,UACH;AACA,gBAAM,eAAe;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,WAAS;AACtB,UAAI,MAAM,OAAO,aAAa,GAAG;AAC/B,YAAI,CAAC,yBAAyB;AAC5B,mBAAS;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AACA,mBAAW,QAAQ;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM,SAAS,OAAO,SAAY;AAAA,MAClC;AAAA,MACA,CAAC,eAAe,GAAG,kBAAkB,aAAa;AAAA,MAClD,CAAC,MAAM,GAAG;AAAA,MACV,SAAS,qBAAqB,iBAAiB,WAAW;AAAA,MAC1D,SAAS,qBAAqB,SAAS,WAAW;AAAA,MAClD,WAAW,qBAAqB,WAAW,aAAa;AAAA,MACxD;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF;AACA,SAAO;AAAA,IACL,aAAa;AAAA,IACb,cAAc;AAAA,IACd;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,qBAAqB,UAAQ;AACjC,MAAI;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,aAAAA,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,OAAO,aAAa,OAAO,CAAC,CAAC;AAChF;AACA,mBAAmB,eAAe;AAAA,EAChC,WAAW;AAAA,EACX,qBAAqB;AACvB;AACA,mBAAmB,YAAY;AAAA,EAC7B,UAAU,kBAAAC,QAAU;AAAA,EACpB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,KAAK,kBAAAA,QAAU;AAAA,EACf,WAAW,kBAAAA,QAAU,MAAM,CAAC,cAAc,YAAY,MAAM,CAAC;AAAA,EAC7D,qBAAqB,kBAAAA,QAAU;AAAA,EAC/B,sBAAsB,kBAAAA,QAAU;AAAA,EAChC,aAAa,kBAAAA,QAAU;AAAA,EACvB,cAAc,kBAAAA,QAAU;AAAA,EACxB,UAAU,kBAAAA,QAAU;AAAA,EACpB,SAAS,kBAAAA,QAAU;AACrB;", "names": ["React", "PropTypes"]}