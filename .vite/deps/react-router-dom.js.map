{"version": 3, "sources": ["../../node_modules/@remix-run/router/history.ts", "../../node_modules/@remix-run/router/utils.ts", "../../node_modules/@remix-run/router/router.ts", "../../node_modules/react-router/lib/context.ts", "../../node_modules/react-router/lib/hooks.tsx", "../../node_modules/react-router/lib/components.tsx", "../../node_modules/react-router/index.ts", "../../node_modules/react-router-dom/dom.ts", "../../node_modules/react-router-dom/index.tsx"], "sourcesContent": ["////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: any;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n\n  /**\n   * The delta between this location and the former location in the history stack\n   */\n  delta: number | null;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. May be either a URL or the pieces of a\n * URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Returns a URL for the given `to` value\n   *\n   * @param to - The destination URL\n   */\n  createURL(to: To): URL;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLACE navigations for data routers\n   * behave the same as POP\n   *\n   * @param to Unencoded path\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n  idx: number;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  function createHref(to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to: To) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\",\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport function warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location, index: number): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  let index = getIndex()!;\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n\n  function getIndex(): number {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n\n  function createURL(to: To): URL {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base =\n      window.location.origin !== \"null\"\n        ? window.location.origin\n        : window.location.href;\n\n    let href = typeof to === \"string\" ? to : createPath(to);\n    invariant(\n      base,\n      `No window.location.(origin|href) available to create URL for href: ${href}`\n    );\n    return new URL(href, base);\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { warning, invariant, parsePath } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: any;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  status: number;\n  location: string;\n  revalidate: boolean;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: any;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\ntype LowerCaseFormMethod = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\ntype UpperCaseFormMethod = Uppercase<LowerCaseFormMethod>;\n\n/**\n * Users can specify either lowercase or uppercase form methods on <Form>,\n * useSubmit(), <fetcher.Form>, etc.\n */\nexport type HTMLFormMethod = LowerCaseFormMethod | UpperCaseFormMethod;\n\n/**\n * Active navigation/fetcher form methods are exposed in lowercase on the\n * RouterState\n */\nexport type FormMethod = LowerCaseFormMethod;\nexport type MutationFormMethod = Exclude<FormMethod, \"get\">;\n\n/**\n * In v7, active navigation/fetcher form methods are exposed in uppercase on the\n * RouterState.  This is to align with the normalization done via fetch().\n */\nexport type V7_FormMethod = UpperCaseFormMethod;\nexport type V7_MutationFormMethod = Exclude<V7_FormMethod, \"GET\">;\n\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\";\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport interface Submission {\n  formMethod: FormMethod | V7_FormMethod;\n  formAction: string;\n  formEncType: FormEncType;\n  formData: FormData;\n}\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs {\n  request: Request;\n  params: Params;\n  context?: any;\n}\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs extends DataFunctionArgs {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs extends DataFunctionArgs {}\n\n/**\n * Loaders and actions can return anything except `undefined` (`null` is a\n * valid return value if there is no data to return).  Responses are preferred\n * and will ease any future migration to Remix\n */\ntype DataFunctionValue = Response | NonNullable<unknown> | null;\n\n/**\n * Route loader function signature\n */\nexport interface LoaderFunction {\n  (args: LoaderFunctionArgs): Promise<DataFunctionValue> | DataFunctionValue;\n}\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction {\n  (args: ActionFunctionArgs): Promise<DataFunctionValue> | DataFunctionValue;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: {\n    currentUrl: URL;\n    currentParams: AgnosticDataRouteMatch[\"params\"];\n    nextUrl: URL;\n    nextParams: AgnosticDataRouteMatch[\"params\"];\n    formMethod?: Submission[\"formMethod\"];\n    formAction?: Submission[\"formAction\"];\n    formEncType?: Submission[\"formEncType\"];\n    formData?: Submission[\"formData\"];\n    actionResult?: DataResult;\n    defaultShouldRevalidate: boolean;\n  }): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set `hasErrorBoundary`\n * from the framework-aware `errorElement` prop\n *\n * @deprecated Use `mapRouteProperties` instead\n */\nexport interface DetectErrorBoundaryFunction {\n  (route: AgnosticRouteObject): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set any framework-specific\n * properties from framework-agnostic properties\n */\nexport interface MapRoutePropertiesFunction {\n  (route: AgnosticRouteObject): {\n    hasErrorBoundary: boolean;\n  } & Record<string, any>;\n}\n\n/**\n * Keys we cannot change from within a lazy() function. We spread all other keys\n * onto the route. Either they're meaningful to the router, or they'll get\n * ignored.\n */\nexport type ImmutableRouteKey =\n  | \"lazy\"\n  | \"caseSensitive\"\n  | \"path\"\n  | \"id\"\n  | \"index\"\n  | \"children\";\n\nexport const immutableRouteKeys = new Set<ImmutableRouteKey>([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"children\",\n]);\n\n/**\n * lazy() function to load a route definition, which can add non-matching\n * related properties to a route\n */\nexport interface LazyRouteFunction<R extends AgnosticRouteObject> {\n  (): Promise<Omit<R, ImmutableRouteKey>>;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction;\n  action?: ActionFunction;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n  lazy?: LazyRouteFunction<AgnosticBaseRouteObject>;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\nexport type RouteManifest = Record<string, AgnosticDataRouteObject | undefined>;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `:${infer Param}`\n    ? Param extends `${infer Optional}?`\n      ? Optional\n      : Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\ntype PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\" | \"/*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  mapRouteProperties: MapRoutePropertiesFunction,\n  parentPath: number[] = [],\n  manifest: RouteManifest = {}\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, index];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !manifest[id],\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n      };\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n        children: undefined,\n      };\n      manifest[id] = pathOrLayoutRoute;\n\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(\n          route.children,\n          mapRouteProperties,\n          treePath,\n          manifest\n        );\n      }\n\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch<string, RouteObjectType>(\n      branches[i],\n      // Incoming pathnames are generally encoded from either window.location\n      // or from router.navigate, but we want to match against the unencoded\n      // paths in the route definitions.  Memory router locations won't be\n      // encoded here but there also shouldn't be anything to decode so this\n      // should be a safe operation.  This avoids needing matchRoutes to be\n      // history-aware.\n      safelyDecodeURI(pathname)\n    );\n  }\n\n  return matches;\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  let flattenRoute = (\n    route: RouteObjectType,\n    index: number,\n    relativePath?: string\n  ) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath:\n        relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta,\n    });\n  };\n  routes.forEach((route, index) => {\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n\n  return branches;\n}\n\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path: string): string[] {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n\n  let [first, ...rest] = segments;\n\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n\n  let result: string[] = [];\n\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explodes _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(\n    ...restExploded.map((subpath) =>\n      subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n\n  // Then if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map((exploded) =>\n    path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    if (!match) return null;\n\n    Object.assign(matchedParams, match.params);\n\n    let route = meta.route;\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  originalPath: Path,\n  params: {\n    [key in PathParam<Path>]: string | null;\n  } = {} as any\n): string {\n  let path: string = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were ` +\n        `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n        `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n        `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\") as Path;\n  }\n\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n\n  const segments = path\n    .split(/\\/+/)\n    .map((segment, index, array) => {\n      const isLastSegment = index === array.length - 1;\n\n      // only apply the splat if it's the last segment\n      if (isLastSegment && segment === \"*\") {\n        const star = \"*\" as PathParam<Path>;\n        const starParam = params[star];\n\n        // Apply the splat\n        return starParam;\n      }\n\n      const keyMatch = segment.match(/^:(\\w+)(\\??)$/);\n      if (keyMatch) {\n        const [, key, optional] = keyMatch;\n        let param = params[key as PathParam<Path>];\n\n        if (optional === \"?\") {\n          return param == null ? \"\" : param;\n        }\n\n        if (param == null) {\n          invariant(false, `Missing \":${key}\" param`);\n        }\n\n        return param;\n      }\n\n      // Remove any optional markers from optional static segments\n      return segment.replace(/\\?$/g, \"\");\n    })\n    // Remove empty segments\n    .filter((segment) => !!segment);\n\n  return prefix + segments.join(\"/\");\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, paramNames] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = paramNames.reduce<Mutable<Params>>(\n    (memo, paramName, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      memo[paramName] = safelyDecodeURIComponent(\n        captureGroups[index] || \"\",\n        paramName\n      );\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, string[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let paramNames: string[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(/\\/:(\\w+)/g, (_: string, paramName: string) => {\n        paramNames.push(paramName);\n        return \"/([^\\\\/]+)\";\n      });\n\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, paramNames];\n}\n\nfunction safelyDecodeURI(value: string) {\n  try {\n    return decodeURI(value);\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\nfunction safelyDecodeURIComponent(value: string, paramName: string) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    warning(\n      false,\n      `The value for the URL param \"${paramName}\" will not be decoded because` +\n        ` the string \"${value}\" is a malformed URL segment. This is probably` +\n        ` due to a bad percent encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (isPathRelative || toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeysSet: Set<string> = new Set<string>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscribers: Set<(aborted: boolean, settledKey?: string) => void> =\n    new Set();\n  data: Record<string, unknown>;\n  init?: ResponseInit;\n  deferredKeys: string[] = [];\n\n  constructor(data: Record<string, unknown>, responseInit?: ResponseInit) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n\n    this.init = responseInit;\n  }\n\n  private trackPromise(\n    key: string,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, null, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeysSet.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    if (error) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    this.emit(false, key);\n    return data;\n  }\n\n  private emit(aborted: boolean, settledKey?: string) {\n    this.subscribers.forEach((subscriber) => subscriber(aborted, settledKey));\n  }\n\n  subscribe(fn: (aborted: boolean, settledKey?: string) => void) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport type DeferFunction = (\n  data: Record<string, unknown>,\n  init?: number | ResponseInit\n) => DeferredData;\n\nexport const defer: DeferFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  return new DeferredData(data, responseInit);\n};\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n */\nexport class ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n  error?: Error;\n  internal: boolean;\n\n  constructor(\n    status: number,\n    statusText: string | undefined,\n    data: any,\n    internal = false\n  ) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nexport function isRouteErrorResponse(error: any): error is ErrorResponse {\n  return (\n    error != null &&\n    typeof error.status === \"number\" &&\n    typeof error.statusText === \"string\" &&\n    typeof error.internal === \"boolean\" &&\n    \"data\" in error\n  );\n}\n", "import type { History, Location, Path, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  invariant,\n  parsePath,\n  warning,\n} from \"./history\";\nimport type {\n  DataResult,\n  DeferredData,\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  DeferredResult,\n  ErrorResult,\n  FormEncType,\n  FormMethod,\n  DetectErrorBoundaryFunction,\n  RedirectResult,\n  RouteData,\n  AgnosticRouteObject,\n  Submission,\n  SuccessResult,\n  AgnosticRouteMatch,\n  ShouldRevalidateFunction,\n  RouteManifest,\n  ImmutableRouteKey,\n  ActionFunction,\n  LoaderFunction,\n  V7_MutationFormMethod,\n  V7_FormMethod,\n  HTMLFormMethod,\n  MutationFormMethod,\n  MapRoutePropertiesFunction,\n} from \"./utils\";\nimport {\n  ErrorResponse,\n  ResultType,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  immutableRouteKeys,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  resolveTo,\n  stripBasename,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): Promise<void>;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To | null, opts?: RouterNavigateOptions): Promise<void>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterNavigateOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to URL encode a destination path according to the internal\n   * history implementation\n   * @param to\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key?: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key?: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get a navigation blocker\n   * @param key The identifier for the blocker\n   * @param fn The blocker function implementation\n   */\n  getBlocker(key: string, fn: BlockerFunction): Blocker;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete a navigation blocker\n   * @param key The identifier for the blocker\n   */\n  deleteBlocker(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * HMR needs to pass in-flight route updates to React Router\n   * TODO: Replace this with granular route update APIs (addRoute, updateRoute, deleteRoute)\n   */\n  _internalSetRoutes(routes: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n\n  /**\n   * Map of current blockers\n   */\n  blockers: Map<string, Blocker>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface FutureConfig {\n  v7_normalizeFormMethod: boolean;\n  v7_prependBasename: boolean;\n}\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  routes: AgnosticRouteObject[];\n  history: History;\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<FutureConfig>;\n  hydrationData?: HydrationState;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  basename: Router[\"basename\"];\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  activeDeferreds: Record<string, DeferredData> | null;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(\n    request: Request,\n    opts?: { requestContext?: unknown }\n  ): Promise<StaticHandlerContext | Response>;\n  queryRoute(\n    request: Request,\n    opts?: { routeId?: string; requestContext?: unknown }\n  ): Promise<any>;\n}\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (state: RouterState): void;\n}\n\ninterface UseMatchesMatch {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: unknown;\n  handle: unknown;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UseMatchesMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\ntype BaseNavigateOptions = {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  fromRouteId?: string;\n};\n\n/**\n * Options for a navigate() call for a Link navigation\n */\ntype LinkNavigateOptions = BaseNavigateOptions;\n\n/**\n * Options for a navigate() call for a Form navigation\n */\ntype SubmissionNavigateOptions = BaseNavigateOptions & {\n  formMethod?: HTMLFormMethod;\n  formEncType?: FormEncType;\n  formData: FormData;\n};\n\n/**\n * Options to pass to navigate() for either a Link or Form navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions =\n  | Omit<LinkNavigateOptions, \"replace\">\n  | Omit<SubmissionNavigateOptions, \"replace\">;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: FormMethod | V7_FormMethod | undefined;\n    formAction: string | undefined;\n    formEncType: FormEncType | undefined;\n    formData: FormData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: FormMethod | V7_FormMethod;\n    formAction: string;\n    formEncType: FormEncType;\n    formData: FormData;\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    data: TData | undefined;\n    \" _hasFetcherDoneAnything \"?: boolean;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: FormMethod | V7_FormMethod | undefined;\n    formAction: string | undefined;\n    formEncType: FormEncType | undefined;\n    formData: FormData | undefined;\n    data: TData | undefined;\n    \" _hasFetcherDoneAnything \"?: boolean;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: FormMethod | V7_FormMethod;\n    formAction: string;\n    formEncType: FormEncType;\n    formData: FormData;\n    data: TData | undefined;\n    \" _hasFetcherDoneAnything \"?: boolean;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface BlockerBlocked {\n  state: \"blocked\";\n  reset(): void;\n  proceed(): void;\n  location: Location;\n}\n\ninterface BlockerUnblocked {\n  state: \"unblocked\";\n  reset: undefined;\n  proceed: undefined;\n  location: undefined;\n}\n\ninterface BlockerProceeding {\n  state: \"proceeding\";\n  reset: undefined;\n  proceed: undefined;\n  location: Location;\n}\n\nexport type Blocker = BlockerUnblocked | BlockerBlocked | BlockerProceeding;\n\nexport type BlockerFunction = (args: {\n  currentLocation: Location;\n  nextLocation: Location;\n  historyAction: HistoryAction;\n}) => boolean;\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Error thrown from the current action, keyed by the route containing the\n   * error boundary to render the error.  To be committed to the state after\n   * loaders have completed\n   */\n  pendingActionError?: RouteData;\n  /**\n   * Data returned from the current action, keyed by the route owning the action.\n   * To be committed to the state after loaders have completed\n   */\n  pendingActionData?: RouteData;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Cached info for active fetcher.load() instances so they can participate\n * in revalidation\n */\ninterface FetchLoadMatch {\n  routeId: string;\n  path: string;\n}\n\n/**\n * Identified fetcher.load() calls that need to be revalidated\n */\ninterface RevalidatingFetcher extends FetchLoadMatch {\n  key: string;\n  match: AgnosticDataRouteMatch | null;\n  matches: AgnosticDataRouteMatch[] | null;\n  controller: AbortController | null;\n}\n\n/**\n * Wrapper object to allow us to throw any response out from callLoaderOrAction\n * for queryRouter while preserving whether or not it was thrown or returned\n * from the loader/action\n */\ninterface QueryRouteResponse {\n  type: ResultType.data | ResultType.error;\n  response: Response;\n}\n\nconst validMutationMethodsArr: MutationFormMethod[] = [\n  \"post\",\n  \"put\",\n  \"patch\",\n  \"delete\",\n];\nconst validMutationMethods = new Set<MutationFormMethod>(\n  validMutationMethodsArr\n);\n\nconst validRequestMethodsArr: FormMethod[] = [\n  \"get\",\n  ...validMutationMethodsArr,\n];\nconst validRequestMethods = new Set<FormMethod>(validRequestMethodsArr);\n\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n};\n\nexport const IDLE_BLOCKER: BlockerUnblocked = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined,\n};\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\nconst isServer = !isBrowser;\n\nconst defaultMapRouteProperties: MapRoutePropertiesFunction = (route) => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary),\n});\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  // Routes keyed by ID\n  let manifest: RouteManifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(\n    init.routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n  let inFlightDataRoutes: AgnosticDataRouteObject[] | undefined;\n  let basename = init.basename || \"/\";\n  // Config driven behavior flags\n  let future: FutureConfig = {\n    v7_normalizeFormMethod: false,\n    v7_prependBasename: false,\n    ...init.future,\n  };\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname,\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  let initialized =\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    !initialMatches.some((m) => m.route.lazy) &&\n    // And we have to either have no loaders or have been provided hydrationData\n    (!initialMatches.some((m) => m.route.loader) || init.hydrationData != null);\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: string[] = [];\n\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set<string>();\n\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map<string, BlockerFunction>();\n\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let ignoreNextHistoryUpdate = false;\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        // Ignore this event if it was just us resetting the URL from a\n        // blocked POP navigation\n        if (ignoreNextHistoryUpdate) {\n          ignoreNextHistoryUpdate = false;\n          return;\n        }\n\n        warning(\n          blockerFunctions.size === 0 || delta != null,\n          \"You are trying to use a blocker on a POP navigation to a location \" +\n            \"that was not created by @remix-run/router. This will fail silently in \" +\n            \"production. This can happen if you are navigating outside the router \" +\n            \"via `window.history.pushState`/`window.location.hash` instead of using \" +\n            \"router navigation APIs.  This can also happen if you are using \" +\n            \"createHashRouter and the user manually changes the URL.\"\n        );\n\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction,\n        });\n\n        if (blockerKey && delta != null) {\n          // Restore the URL to match the current UI, but don't update router state\n          ignoreNextHistoryUpdate = true;\n          init.history.go(delta * -1);\n\n          // Put the blocker into a blocked state\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey!, {\n                state: \"proceeding\",\n                proceed: undefined,\n                reset: undefined,\n                location,\n              });\n              // Re-do the same POP navigation we just blocked\n              init.history.go(delta);\n            },\n            reset() {\n              deleteBlocker(blockerKey!);\n              updateState({ blockers: new Map(router.state.blockers) });\n            },\n          });\n          return;\n        }\n\n        return startNavigation(historyAction, location);\n      }\n    );\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location);\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(newState: Partial<RouterState>): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n    subscribers.forEach((subscriber) => subscriber(state));\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      isMutationMethod(state.navigation.formMethod) &&\n      state.navigation.state === \"loading\" &&\n      location.state?._isRedirect !== true;\n\n    let actionData: RouteData | null;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData\n      ? mergeLoaderData(\n          state.loaderData,\n          newState.loaderData,\n          newState.matches || [],\n          newState.errors\n        )\n      : state.loaderData;\n\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    for (let [key] of blockerFunctions) {\n      deleteBlocker(key);\n    }\n\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset =\n      pendingPreventScrollReset === true ||\n      (state.navigation.formMethod != null &&\n        isMutationMethod(state.navigation.formMethod) &&\n        location.state?._isRedirect !== true);\n\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n\n    updateState({\n      ...newState, // matches, errors, fetchers go through as-is\n      actionData,\n      loaderData,\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      restoreScrollPosition: getSavedScrollPosition(\n        location,\n        newState.matches || state.matches\n      ),\n      preventScrollReset,\n      blockers: new Map(state.blockers),\n    });\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n    cancelledFetcherLoads = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To | null,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      to,\n      opts?.fromRouteId,\n      opts?.relative\n    );\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      false,\n      normalizedPath,\n      opts\n    );\n\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation),\n    };\n\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n\n    let historyAction = HistoryAction.Push;\n\n    if (userReplace === true) {\n      historyAction = HistoryAction.Replace;\n    } else if (userReplace === false) {\n      // no-op\n    } else if (\n      submission != null &&\n      isMutationMethod(submission.formMethod) &&\n      submission.formAction === state.location.pathname + state.location.search\n    ) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = HistoryAction.Replace;\n    }\n\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction,\n    });\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey!, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation,\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          deleteBlocker(blockerKey!);\n          updateState({ blockers: new Map(state.blockers) });\n        },\n      });\n      return;\n    }\n\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      { overrideNavigation: state.navigation }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponse;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(routesToUse, location, basename);\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(routesToUse);\n      // Cancel all pending deferred on 404s since we don't keep any routes\n      cancelActiveDeferreds();\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error,\n        },\n      });\n      return;\n    }\n\n    // Short circuit if it's only a hash change and not a mutation submission.\n    // Ignore on initial page loads because since the initial load will always\n    // be \"same hash\".\n    // For example, on /page#hash and submit a <Form method=\"post\"> which will\n    // default to a navigation to /page\n    if (\n      state.initialized &&\n      isHashChangeOnly(state.location, location) &&\n      !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))\n    ) {\n      completeNavigation(location, { matches });\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionData: RouteData | undefined;\n    let pendingError: RouteData | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingError = {\n        [findNearestBoundary(matches).route.id]: opts.pendingError,\n      };\n    } else if (\n      opts &&\n      opts.submission &&\n      isMutationMethod(opts.submission.formMethod)\n    ) {\n      // Call action if we received an action submission\n      let actionOutput = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        { replace: opts.replace }\n      );\n\n      if (actionOutput.shortCircuited) {\n        return;\n      }\n\n      pendingActionData = actionOutput.pendingActionData;\n      pendingError = actionOutput.pendingActionError;\n\n      let navigation: NavigationStates[\"Loading\"] = {\n        state: \"loading\",\n        location,\n        ...opts.submission,\n      };\n      loadingNavigation = navigation;\n\n      // Create a GET request for the loaders\n      request = new Request(request.url, { signal: request.signal });\n    }\n\n    // Call loaders\n    let { shortCircuited, loaderData, errors } = await handleLoaders(\n      request,\n      location,\n      matches,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.fetcherSubmission,\n      opts && opts.replace,\n      pendingActionData,\n      pendingError\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches,\n      ...(pendingActionData ? { actionData: pendingActionData } : {}),\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    opts?: { replace?: boolean }\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation: NavigationStates[\"Submitting\"] = {\n      state: \"submitting\",\n      location,\n      ...submission,\n    };\n    updateState({ navigation });\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id,\n        }),\n      };\n    } else {\n      result = await callLoaderOrAction(\n        \"action\",\n        request,\n        actionMatch,\n        matches,\n        manifest,\n        mapRouteProperties,\n        basename\n      );\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace: boolean;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        replace =\n          result.location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(state, result, { submission, replace });\n      return { shortCircuited: true };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions are REPLACE navigations, but if the\n      // action threw an error that'll be rendered in an errorElement, we fall\n      // back to PUSH so that the user can use the back button to get back to\n      // the pre-submission form location to try again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        // Send back an empty object we can use to clear out any prior actionData\n        pendingActionData: {},\n        pendingActionError: { [boundaryMatch.route.id]: result.error },\n      };\n    }\n\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    return {\n      pendingActionData: { [actionMatch.route.id]: result.data },\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    fetcherSubmission?: Submission,\n    replace?: boolean,\n    pendingActionData?: RouteData,\n    pendingError?: RouteData\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation = overrideNavigation;\n    if (!loadingNavigation) {\n      let navigation: NavigationStates[\"Loading\"] = {\n        state: \"loading\",\n        location,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        ...submission,\n      };\n      loadingNavigation = navigation;\n    }\n\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission =\n      submission || fetcherSubmission\n        ? submission || fetcherSubmission\n        : loadingNavigation.formMethod &&\n          loadingNavigation.formAction &&\n          loadingNavigation.formData &&\n          loadingNavigation.formEncType\n        ? {\n            formMethod: loadingNavigation.formMethod,\n            formAction: loadingNavigation.formAction,\n            formData: loadingNavigation.formData,\n            formEncType: loadingNavigation.formEncType,\n          }\n        : undefined;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      fetchLoadMatches,\n      routesToUse,\n      basename,\n      pendingActionData,\n      pendingError\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(location, {\n        matches,\n        loaderData: {},\n        // Commit pending error if we're short circuiting\n        errors: pendingError || null,\n        ...(pendingActionData ? { actionData: pendingActionData } : {}),\n        ...(updatedFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n      });\n      return { shortCircuited: true };\n    }\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    if (!isUninterruptedRevalidation) {\n      revalidatingFetchers.forEach((rf) => {\n        let fetcher = state.fetchers.get(rf.key);\n        let revalidatingFetcher: FetcherStates[\"Loading\"] = {\n          state: \"loading\",\n          data: fetcher && fetcher.data,\n          formMethod: undefined,\n          formAction: undefined,\n          formEncType: undefined,\n          formData: undefined,\n          \" _hasFetcherDoneAnything \": true,\n        };\n        state.fetchers.set(rf.key, revalidatingFetcher);\n      });\n      let actionData = pendingActionData || state.actionData;\n      updateState({\n        navigation: loadingNavigation,\n        ...(actionData\n          ? Object.keys(actionData).length === 0\n            ? { actionData: null }\n            : { actionData }\n          : {}),\n        ...(revalidatingFetchers.length > 0\n          ? { fetchers: new Map(state.fetchers) }\n          : {}),\n      });\n    }\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n    revalidatingFetchers.forEach((rf) => {\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((f) => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    let { results, loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(results);\n    if (redirect) {\n      await startRedirectNavigation(state, redirect, { replace });\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      matchesToLoad,\n      loaderResults,\n      pendingError,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers =\n      updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n\n    return {\n      loaderData,\n      errors,\n      ...(shouldUpdateFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n    };\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    if (fetchControllers.has(key)) abortFetcher(key);\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      href,\n      routeId,\n      opts?.relative\n    );\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: normalizedPath })\n      );\n      return;\n    }\n\n    let { path, submission } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      true,\n      normalizedPath,\n      opts\n    );\n    let match = getTargetMatch(matches, path);\n\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(key, routeId, path, match, matches, submission);\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, { routeId, path });\n    handleFetcherLoader(key, routeId, path, match, matches, submission);\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    if (!match.route.action && !match.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: submission.formMethod,\n        pathname: path,\n        routeId: routeId,\n      });\n      setFetcherError(key, routeId, error);\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    let fetcher: FetcherStates[\"Submitting\"] = {\n      state: \"submitting\",\n      ...submission,\n      data: existingFetcher && existingFetcher.data,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    state.fetchers.set(key, fetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    // Call the action for the fetcher\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n    fetchControllers.set(key, abortController);\n\n    let actionResult = await callLoaderOrAction(\n      \"action\",\n      fetchRequest,\n      match,\n      requestMatches,\n      manifest,\n      mapRouteProperties,\n      basename\n    );\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by ou our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    if (isRedirectResult(actionResult)) {\n      fetchControllers.delete(key);\n      fetchRedirectIds.add(key);\n      let loadingFetcher: FetcherStates[\"Loading\"] = {\n        state: \"loading\",\n        ...submission,\n        data: undefined,\n        \" _hasFetcherDoneAnything \": true,\n      };\n      state.fetchers.set(key, loadingFetcher);\n      updateState({ fetchers: new Map(state.fetchers) });\n\n      return startRedirectNavigation(state, actionResult, {\n        submission,\n        isFetchActionRedirect: true,\n      });\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(actionResult)) {\n      setFetcherError(key, routeId, actionResult.error);\n      return;\n    }\n\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n\n      nextLocation,\n      abortController.signal\n    );\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(routesToUse, state.navigation.location, basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      data: actionResult.data,\n      ...submission,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      fetchLoadMatches,\n      routesToUse,\n      basename,\n      { [match.route.id]: actionResult.data },\n      undefined // No need to send through errors since we short circuit above\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter((rf) => rf.key !== key)\n      .forEach((rf) => {\n        let staleKey = rf.key;\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher: FetcherStates[\"Loading\"] = {\n          state: \"loading\",\n          data: existingFetcher && existingFetcher.data,\n          formMethod: undefined,\n          formAction: undefined,\n          formEncType: undefined,\n          formData: undefined,\n          \" _hasFetcherDoneAnything \": true,\n        };\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        if (rf.controller) {\n          fetchControllers.set(staleKey, rf.controller);\n        }\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((rf) => abortFetcher(rf.key));\n\n    abortController.signal.addEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    let { results, loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    abortController.signal.removeEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n\n    let redirect = findRedirect(results);\n    if (redirect) {\n      return startRedirectNavigation(state, redirect);\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      state.matches,\n      matchesToLoad,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    let doneFetcher: FetcherStates[\"Idle\"] = {\n      state: \"idle\",\n      data: actionResult.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    state.fetchers.set(key, doneFetcher);\n\n    let didAbortFetchLoads = abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        ...(didAbortFetchLoads ? { fetchers: new Map(state.fetchers) } : {}),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[],\n    submission?: Submission\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    // Put this fetcher into it's loading state\n    let loadingFetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      ...submission,\n      data: existingFetcher && existingFetcher.data,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    state.fetchers.set(key, loadingFetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    // Call the loader for this fetcher route match\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n    fetchControllers.set(key, abortController);\n\n    let result: DataResult = await callLoaderOrAction(\n      \"loader\",\n      fetchRequest,\n      match,\n      matches,\n      manifest,\n      mapRouteProperties,\n      basename\n    );\n\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      fetchRedirectIds.add(key);\n      await startRedirectNavigation(state, result);\n      return;\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, routeId);\n      state.fetchers.delete(key);\n      // TODO: In remix, this would reset to IDLE_NAVIGATION if it was a catch -\n      // do we need to behave any differently with our non-redirect errors?\n      // What if it was a non-redirect Response?\n      updateState({\n        fetchers: new Map(state.fetchers),\n        errors: {\n          [boundaryMatch.route.id]: result.error,\n        },\n      });\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    let doneFetcher: FetcherStates[\"Idle\"] = {\n      state: \"idle\",\n      data: result.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    state.fetchers.set(key, doneFetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    state: RouterState,\n    redirect: RedirectResult,\n    {\n      submission,\n      replace,\n      isFetchActionRedirect,\n    }: {\n      submission?: Submission;\n      replace?: boolean;\n      isFetchActionRedirect?: boolean;\n    } = {}\n  ) {\n    if (redirect.revalidate) {\n      isRevalidationRequired = true;\n    }\n\n    let redirectLocation = createLocation(\n      state.location,\n      redirect.location,\n      // TODO: This can be removed once we get rid of useTransition in Remix v2\n      {\n        _isRedirect: true,\n        ...(isFetchActionRedirect ? { _isFetchActionRedirect: true } : {}),\n      }\n    );\n    invariant(\n      redirectLocation,\n      \"Expected a location on the redirect navigation\"\n    );\n    // Check if this an absolute external redirect that goes to a new origin\n    if (\n      ABSOLUTE_URL_REGEX.test(redirect.location) &&\n      isBrowser &&\n      typeof window?.location !== \"undefined\"\n    ) {\n      let url = init.history.createURL(redirect.location);\n      let isDifferentBasename = stripBasename(url.pathname, basename) == null;\n\n      if (window.location.origin !== url.origin || isDifferentBasename) {\n        if (replace) {\n          window.location.replace(redirect.location);\n        } else {\n          window.location.assign(redirect.location);\n        }\n        return;\n      }\n    }\n\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true ? HistoryAction.Replace : HistoryAction.Push;\n\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let { formMethod, formAction, formEncType, formData } = state.navigation;\n    if (!submission && formMethod && formAction && formData && formEncType) {\n      submission = {\n        formMethod,\n        formAction,\n        formEncType,\n        formData,\n      };\n    }\n\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    if (\n      redirectPreserveMethodStatusCodes.has(redirect.status) &&\n      submission &&\n      isMutationMethod(submission.formMethod)\n    ) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: {\n          ...submission,\n          formAction: redirect.location,\n        },\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    } else if (isFetchActionRedirect) {\n      // For a fetch action redirect, we kick off a new loading navigation\n      // without the fetcher submission, but we send it along for shouldRevalidate\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation: {\n          state: \"loading\",\n          location: redirectLocation,\n          formMethod: undefined,\n          formAction: undefined,\n          formEncType: undefined,\n          formData: undefined,\n        },\n        fetcherSubmission: submission,\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    } else {\n      // Otherwise, we kick off a new loading navigation, preserving the\n      // submission info for the duration of this navigation\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation: {\n          state: \"loading\",\n          location: redirectLocation,\n          formMethod: submission ? submission.formMethod : undefined,\n          formAction: submission ? submission.formAction : undefined,\n          formEncType: submission ? submission.formEncType : undefined,\n          formData: submission ? submission.formData : undefined,\n        },\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    }\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    currentMatches: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    // Call all navigation loaders and revalidating fetcher loaders in parallel,\n    // then slice off the results into separate arrays so we can handle them\n    // accordingly\n    let results = await Promise.all([\n      ...matchesToLoad.map((match) =>\n        callLoaderOrAction(\n          \"loader\",\n          request,\n          match,\n          matches,\n          manifest,\n          mapRouteProperties,\n          basename\n        )\n      ),\n      ...fetchersToLoad.map((f) => {\n        if (f.matches && f.match && f.controller) {\n          return callLoaderOrAction(\n            \"loader\",\n            createClientSideRequest(init.history, f.path, f.controller.signal),\n            f.match,\n            f.matches,\n            manifest,\n            mapRouteProperties,\n            basename\n          );\n        } else {\n          let error: ErrorResult = {\n            type: ResultType.error,\n            error: getInternalRouterError(404, { pathname: f.path }),\n          };\n          return error;\n        }\n      }),\n    ]);\n    let loaderResults = results.slice(0, matchesToLoad.length);\n    let fetcherResults = results.slice(matchesToLoad.length);\n\n    await Promise.all([\n      resolveDeferredResults(\n        currentMatches,\n        matchesToLoad,\n        loaderResults,\n        loaderResults.map(() => request.signal),\n        false,\n        state.loaderData\n      ),\n      resolveDeferredResults(\n        currentMatches,\n        fetchersToLoad.map((f) => f.match),\n        fetcherResults,\n        fetchersToLoad.map((f) => (f.controller ? f.controller.signal : null)),\n        true\n      ),\n    ]);\n\n    return { results, loaderResults, fetcherResults };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.push(key);\n        abortFetcher(key);\n      }\n    });\n  }\n\n  function setFetcherError(key: string, routeId: string, error: any) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error,\n      },\n      fetchers: new Map(state.fetchers),\n    });\n  }\n\n  function deleteFetcher(key: string): void {\n    if (fetchControllers.has(key)) abortFetcher(key);\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, `Expected fetch controller: ${key}`);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher: FetcherStates[\"Idle\"] = {\n        state: \"idle\",\n        data: fetcher.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        \" _hasFetcherDoneAnything \": true,\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): boolean {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function getBlocker(key: string, fn: BlockerFunction) {\n    let blocker: Blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n\n    return blocker;\n  }\n\n  function deleteBlocker(key: string) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key: string, newBlocker: Blocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(\n      (blocker.state === \"unblocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"proceeding\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"unblocked\") ||\n        (blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\"),\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n\n    state.blockers.set(key, newBlocker);\n    updateState({ blockers: new Map(state.blockers) });\n  }\n\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction,\n  }: {\n    currentLocation: Location;\n    nextLocation: Location;\n    historyAction: HistoryAction;\n  }): string | undefined {\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return blockerKey;\n    }\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || ((location) => location.key);\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map((m) =>\n        createUseMatchesMatch(m, state.loaderData)\n      );\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map((m) =>\n        createUseMatchesMatch(m, state.loaderData)\n      );\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  function _internalSetRoutes(newRoutes: AgnosticDataRouteObject[]) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(\n      newRoutes,\n      mapRouteProperties,\n      undefined,\n      manifest\n    );\n  }\n\n  router = {\n    get basename() {\n      return basename;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    encodeLocation: (to: To) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nexport const UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\n\nexport interface CreateStaticHandlerOptions {\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n}\n\nexport function createStaticHandler(\n  routes: AgnosticRouteObject[],\n  opts?: CreateStaticHandlerOptions\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n\n  let manifest: RouteManifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (opts?.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts?.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   */\n  async function query(\n    request: Request,\n    { requestContext }: { requestContext?: unknown } = {}\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let result = await queryImpl(request, location, matches, requestContext);\n    if (isResponse(result)) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, basename, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   */\n  async function queryRoute(\n    request: Request,\n    {\n      routeId,\n      requestContext,\n    }: { requestContext?: unknown; routeId?: string } = {}\n  ): Promise<any> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId,\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      match\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n\n    if (result.loaderData) {\n      let data = Object.values(result.loaderData)[0];\n      if (result.activeDeferreds?.[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n\n    return undefined;\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    routeMatch?: AgnosticDataRouteMatch\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        routeMatch\n      );\n      return isResponse(result)\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction, we throw\n      // it to bail out and then return or throw here based on whether the user\n      // returned or threw\n      if (isQueryRouteResponse(e)) {\n        if (e.type === ResultType.error && !isRedirectResponse(e.response)) {\n          throw e.response;\n        }\n        return e.response;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    requestContext: unknown,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    let result: DataResult;\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id,\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    } else {\n      result = await callLoaderOrAction(\n        \"action\",\n        request,\n        actionMatch,\n        matches,\n        manifest,\n        mapRouteProperties,\n        basename,\n        true,\n        isRouteRequest,\n        requestContext\n      );\n\n      if (request.signal.aborted) {\n        let method = isRouteRequest ? \"queryRoute\" : \"query\";\n        throw new Error(`${method}() call aborted`);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.status,\n        headers: {\n          Location: result.location,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, { type: \"defer-action\" });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        undefined,\n        {\n          [boundaryMatch.route.id]: result.error,\n        }\n      );\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal,\n    });\n    let context = await loadRouteData(loaderRequest, matches, requestContext);\n\n    return {\n      ...context,\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      actionHeaders: {\n        ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n      },\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    routeMatch?: AgnosticDataRouteMatch,\n    pendingActionError?: RouteData\n  ): Promise<\n    | Omit<\n        StaticHandlerContext,\n        \"location\" | \"basename\" | \"actionData\" | \"actionHeaders\"\n      >\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (\n      isRouteRequest &&\n      !routeMatch?.route.loader &&\n      !routeMatch?.route.lazy\n    ) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id,\n      });\n    }\n\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : getLoaderMatchesUntilBoundary(\n          matches,\n          Object.keys(pendingActionError || {})[0]\n        );\n    let matchesToLoad = requestMatches.filter(\n      (m) => m.route.loader || m.route.lazy\n    );\n\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce(\n          (acc, m) => Object.assign(acc, { [m.route.id]: null }),\n          {}\n        ),\n        errors: pendingActionError || null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let results = await Promise.all([\n      ...matchesToLoad.map((match) =>\n        callLoaderOrAction(\n          \"loader\",\n          request,\n          match,\n          matches,\n          manifest,\n          mapRouteProperties,\n          basename,\n          true,\n          isRouteRequest,\n          requestContext\n        )\n      ),\n    ]);\n\n    if (request.signal.aborted) {\n      let method = isRouteRequest ? \"queryRoute\" : \"query\";\n      throw new Error(`${method}() call aborted`);\n    }\n\n    // Process and commit output from loaders\n    let activeDeferreds = new Map<string, DeferredData>();\n    let context = processRouteLoaderData(\n      matches,\n      matchesToLoad,\n      results,\n      pendingActionError,\n      activeDeferreds\n    );\n\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set<string>(\n      matchesToLoad.map((match) => match.route.id)\n    );\n    matches.forEach((match) => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n\n    return {\n      ...context,\n      matches,\n      activeDeferreds:\n        activeDeferreds.size > 0\n          ? Object.fromEntries(activeDeferreds.entries())\n          : null,\n    };\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\nfunction isSubmissionNavigation(\n  opts: RouterNavigateOptions\n): opts is SubmissionNavigateOptions {\n  return opts != null && \"formData\" in opts;\n}\n\nfunction normalizeTo(\n  location: Path,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  prependBasename: boolean,\n  to: To | null,\n  fromRouteId?: string,\n  relative?: RelativeRoutingType\n) {\n  let contextualMatches: AgnosticDataRouteMatch[];\n  let activeRouteMatch: AgnosticDataRouteMatch | undefined;\n  if (fromRouteId != null && relative !== \"path\") {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route.  When using relative:path,\n    // fromRouteId is ignored since that is always relative to the current\n    // location path\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n\n  // Resolve the relative path\n  let path = resolveTo(\n    to ? to : \".\",\n    getPathContributingMatches(contextualMatches).map((m) => m.pathnameBase),\n    stripBasename(location.pathname, basename) || location.pathname,\n    relative === \"path\"\n  );\n\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n\n  // Add an ?index param for matched index routes if we don't already have one\n  if (\n    (to == null || to === \"\" || to === \".\") &&\n    activeRouteMatch &&\n    activeRouteMatch.route.index &&\n    !hasNakedIndexQuery(path.search)\n  ) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  normalizeFormMethod: boolean,\n  isFetcher: boolean,\n  path: string,\n  opts?: RouterNavigateOptions\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponse;\n} {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod }),\n    };\n  }\n\n  // Create a Submission on non-GET navigations\n  let submission: Submission | undefined;\n  if (opts.formData) {\n    let formMethod = opts.formMethod || \"get\";\n    submission = {\n      formMethod: normalizeFormMethod\n        ? (formMethod.toUpperCase() as V7_FormMethod)\n        : (formMethod.toLowerCase() as FormMethod),\n      formAction: stripHashFromPath(path),\n      formEncType:\n        (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n      formData: opts.formData,\n    };\n\n    if (isMutationMethod(submission.formMethod)) {\n      return { path, submission };\n    }\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  let searchParams = convertFormDataToSearchParams(opts.formData);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n\n  return { path: createPath(parsedPath), submission };\n}\n\n// Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId?: string\n) {\n  let boundaryMatches = matches;\n  if (boundaryId) {\n    let index = matches.findIndex((m) => m.route.id === boundaryId);\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n  return boundaryMatches;\n}\n\nfunction getMatchesToLoad(\n  history: History,\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: string[],\n  fetchLoadMatches: Map<string, FetchLoadMatch>,\n  routesToUse: AgnosticDataRouteObject[],\n  basename: string | undefined,\n  pendingActionData?: RouteData,\n  pendingError?: RouteData\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingError\n    ? Object.values(pendingError)[0]\n    : pendingActionData\n    ? Object.values(pendingActionData)[0]\n    : undefined;\n\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryId = pendingError ? Object.keys(pendingError)[0] : undefined;\n  let boundaryMatches = getLoaderMatchesUntilBoundary(matches, boundaryId);\n\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    if (match.route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n    if (match.route.loader == null) {\n      return false;\n    }\n\n    // Always call the loader on new route instances and pending defer cancellations\n    if (\n      isNewLoader(state.loaderData, state.matches[index], match) ||\n      cancelledDeferredRoutes.some((id) => id === match.route.id)\n    ) {\n      return true;\n    }\n\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n\n    return shouldRevalidateLoader(match, {\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params,\n      ...submission,\n      actionResult,\n      defaultShouldRevalidate:\n        // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n        isRevalidationRequired ||\n        // Clicked the same link, resubmitted a GET form\n        currentUrl.pathname + currentUrl.search ===\n          nextUrl.pathname + nextUrl.search ||\n        // Search params affect all loaders\n        currentUrl.search !== nextUrl.search ||\n        isNewRouteInstance(currentRouteMatch, nextRouteMatch),\n    });\n  });\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate if fetcher won't be present in the subsequent render\n    if (!matches.some((m) => m.route.id === f.routeId)) {\n      return;\n    }\n\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null,\n      });\n      return;\n    }\n\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n\n    if (cancelledFetcherLoads.includes(key)) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController(),\n      });\n      return;\n    }\n\n    // Revalidating fetchers are decoupled from the route matches since they\n    // hit a static href, so they _always_ check shouldRevalidate and the\n    // default is strictly if a revalidation is explicitly required (action\n    // submissions, useRevalidator, X-Remix-Revalidate).\n    let shouldRevalidate = shouldRevalidateLoader(fetcherMatch, {\n      currentUrl,\n      currentParams: state.matches[state.matches.length - 1].params,\n      nextUrl,\n      nextParams: matches[matches.length - 1].params,\n      ...submission,\n      actionResult,\n      // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n      defaultShouldRevalidate: isRevalidationRequired,\n    });\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController(),\n      });\n    }\n  });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath != null &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  loaderMatch: AgnosticDataRouteMatch,\n  arg: Parameters<ShouldRevalidateFunction>[0]\n) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return arg.defaultShouldRevalidate;\n}\n\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(\n  route: AgnosticDataRouteObject,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  manifest: RouteManifest\n) {\n  if (!route.lazy) {\n    return;\n  }\n\n  let lazyRoute = await route.lazy();\n\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates: Record<string, any> = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue =\n      routeToUpdate[lazyRouteProperty as keyof typeof routeToUpdate];\n\n    let isPropertyStaticallyDefined =\n      staticRouteValue !== undefined &&\n      // This property isn't static since it should always be updated based\n      // on the route updates\n      lazyRouteProperty !== \"hasErrorBoundary\";\n\n    warning(\n      !isPropertyStaticallyDefined,\n      `Route \"${routeToUpdate.id}\" has a static property \"${lazyRouteProperty}\" ` +\n        `defined but its lazy function is also returning a value for this property. ` +\n        `The lazy route property \"${lazyRouteProperty}\" will be ignored.`\n    );\n\n    if (\n      !isPropertyStaticallyDefined &&\n      !immutableRouteKeys.has(lazyRouteProperty as ImmutableRouteKey)\n    ) {\n      routeUpdates[lazyRouteProperty] =\n        lazyRoute[lazyRouteProperty as keyof typeof lazyRoute];\n    }\n  }\n\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, {\n    // To keep things framework agnostic, we use the provided\n    // `mapRouteProperties` (or wrapped `detectErrorBoundary`) function to\n    // set the framework-aware properties (`element`/`hasErrorBoundary`) since\n    // the logic will differ between frameworks.\n    ...mapRouteProperties(routeToUpdate),\n    lazy: undefined,\n  });\n}\n\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  matches: AgnosticDataRouteMatch[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  basename: string,\n  isStaticRequest: boolean = false,\n  isRouteRequest: boolean = false,\n  requestContext?: unknown\n): Promise<DataResult> {\n  let resultType;\n  let result;\n  let onReject: (() => void) | undefined;\n\n  let runHandler = (handler: ActionFunction | LoaderFunction) => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject: () => void;\n    let abortPromise = new Promise((_, r) => (reject = r));\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n    return Promise.race([\n      handler({ request, params: match.params, context: requestContext }),\n      abortPromise,\n    ]);\n  };\n\n  try {\n    let handler = match.route[type];\n\n    if (match.route.lazy) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let values = await Promise.all([\n          runHandler(handler),\n          loadLazyRouteModule(match.route, mapRouteProperties, manifest),\n        ]);\n        result = values[0];\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadLazyRouteModule(match.route, mapRouteProperties, manifest);\n\n        handler = match.route[type];\n        if (handler) {\n          // Handler still run even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id,\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return { type: ResultType.data, data: undefined };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname,\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n\n    invariant(\n      result !== undefined,\n      `You defined ${type === \"action\" ? \"an action\" : \"a loader\"} for route ` +\n        `\"${match.route.id}\" but didn't return anything from your \\`${type}\\` ` +\n        `function. Please return a value or \\`null\\`.`\n    );\n  } catch (e) {\n    resultType = ResultType.error;\n    result = e;\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n\n  if (isResponse(result)) {\n    let status = result.status;\n\n    // Process redirects\n    if (redirectStatusCodes.has(status)) {\n      let location = result.headers.get(\"Location\");\n      invariant(\n        location,\n        \"Redirects returned/thrown from loaders/actions must have a Location header\"\n      );\n\n      // Support relative routing in internal redirects\n      if (!ABSOLUTE_URL_REGEX.test(location)) {\n        location = normalizeTo(\n          new URL(request.url),\n          matches.slice(0, matches.indexOf(match) + 1),\n          basename,\n          true,\n          location\n        );\n      } else if (!isStaticRequest) {\n        // Strip off the protocol+origin for same-origin + same-basename absolute\n        // redirects. If this is a static request, we can let it go back to the\n        // browser as-is\n        let currentUrl = new URL(request.url);\n        let url = location.startsWith(\"//\")\n          ? new URL(currentUrl.protocol + location)\n          : new URL(location);\n        let isSameBasename = stripBasename(url.pathname, basename) != null;\n        if (url.origin === currentUrl.origin && isSameBasename) {\n          location = url.pathname + url.search + url.hash;\n        }\n      }\n\n      // Don't process redirects in the router during static requests requests.\n      // Instead, throw the Response and let the server handle it with an HTTP\n      // redirect.  We also update the Location header in place in this flow so\n      // basename and relative routing is taken into account\n      if (isStaticRequest) {\n        result.headers.set(\"Location\", location);\n        throw result;\n      }\n\n      return {\n        type: ResultType.redirect,\n        status,\n        location,\n        revalidate: result.headers.get(\"X-Remix-Revalidate\") !== null,\n      };\n    }\n\n    // For SSR single-route requests, we want to hand Responses back directly\n    // without unwrapping.  We do this with the QueryRouteResponse wrapper\n    // interface so we can know whether it was returned or thrown\n    if (isRouteRequest) {\n      // eslint-disable-next-line no-throw-literal\n      throw {\n        type: resultType || ResultType.data,\n        response: result,\n      };\n    }\n\n    let data: any;\n    let contentType = result.headers.get(\"Content-Type\");\n    // Check between word boundaries instead of startsWith() due to the last\n    // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      data = await result.json();\n    } else {\n      data = await result.text();\n    }\n\n    if (resultType === ResultType.error) {\n      return {\n        type: resultType,\n        error: new ErrorResponse(status, result.statusText, data),\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (resultType === ResultType.error) {\n    return { type: resultType, error: result };\n  }\n\n  if (isDeferredData(result)) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: result.init?.status,\n      headers: result.init?.headers && new Headers(result.init.headers),\n    };\n  }\n\n  return { type: ResultType.data, data: result };\n}\n\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(\n  history: History,\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType, formData } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n    init.body =\n      formEncType === \"application/x-www-form-urlencoded\"\n        ? convertFormDataToSearchParams(formData)\n        : formData;\n  }\n\n  // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, value instanceof File ? value.name : value);\n  }\n\n  return searchParams;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingError: RouteData | undefined,\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n\n  // Process loader results into state.loaderData/state.errors\n  results.forEach((result, index) => {\n    let id = matchesToLoad[index].route.id;\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      // Look upwards from the matched route for the closest ancestor\n      // error boundary, defaulting to the root match\n      let boundaryMatch = findNearestBoundary(matches, id);\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError) {\n        error = Object.values(pendingError)[0];\n        pendingError = undefined;\n      }\n\n      errors = errors || {};\n\n      // Prefer higher error values if lower errors bubble to the same boundary\n      if (errors[boundaryMatch.route.id] == null) {\n        errors[boundaryMatch.route.id] = error;\n      }\n\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n      } else {\n        loaderData[id] = result.data;\n      }\n\n      // Error status codes always override success status codes, but if all\n      // loaders are successful we take the deepest status code.\n      if (\n        result.statusCode != null &&\n        result.statusCode !== 200 &&\n        !foundError\n      ) {\n        statusCode = result.statusCode;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError) {\n    errors = pendingError;\n    loaderData[Object.keys(pendingError)[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingError: RouteData | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: DataResult[],\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    matchesToLoad,\n    results,\n    pendingError,\n    activeDeferreds\n  );\n\n  // Process results from our revalidating fetchers\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let { key, match, controller } = revalidatingFetchers[index];\n    invariant(\n      fetcherResults !== undefined && fetcherResults[index] !== undefined,\n      \"Did not find corresponding fetcher result\"\n    );\n    let result = fetcherResults[index];\n\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      continue;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match?.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher: FetcherStates[\"Idle\"] = {\n        state: \"idle\",\n        data: result.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        \" _hasFetcherDoneAnything \": true,\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[],\n  errors: RouteData | null | undefined\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      } else {\n        // No-op - this is so we ignore existing data if we have a key in the\n        // incoming object with an undefined value, which is how we unset a prior\n        // loaderData if we encounter a loader error\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(routes: AgnosticDataRouteObject[]): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n    id: `__shim-error-route__`,\n  };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n  };\n}\n\nfunction getInternalRouterError(\n  status: number,\n  {\n    pathname,\n    routeId,\n    method,\n    type,\n  }: {\n    pathname?: string;\n    routeId?: string;\n    method?: string;\n    type?: \"defer-action\";\n  } = {}\n) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method} request to \"${pathname}\" but ` +\n        `did not provide a \\`loader\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method.toUpperCase()} request to \"${pathname}\" but ` +\n        `did not provide an \\`action\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n\n  return new ErrorResponse(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(results: DataResult[]): RedirectResult | undefined {\n  for (let i = results.length - 1; i >= 0; i--) {\n    let result = results[i];\n    if (isRedirectResult(result)) {\n      return result;\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nexport function isDeferredData(value: any): value is DeferredData {\n  let deferred: DeferredData = value;\n  return (\n    deferred &&\n    typeof deferred === \"object\" &&\n    typeof deferred.data === \"object\" &&\n    typeof deferred.subscribe === \"function\" &&\n    typeof deferred.cancel === \"function\" &&\n    typeof deferred.resolveData === \"function\"\n  );\n}\n\nfunction isResponse(value: any): value is Response {\n  return (\n    value != null &&\n    typeof value.status === \"number\" &&\n    typeof value.statusText === \"string\" &&\n    typeof value.headers === \"object\" &&\n    typeof value.body !== \"undefined\"\n  );\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isQueryRouteResponse(obj: any): obj is QueryRouteResponse {\n  return (\n    obj &&\n    isResponse(obj.response) &&\n    (obj.type === ResultType.data || ResultType.error)\n  );\n}\n\nfunction isValidMethod(method: string): method is FormMethod | V7_FormMethod {\n  return validRequestMethods.has(method.toLowerCase() as FormMethod);\n}\n\nfunction isMutationMethod(\n  method: string\n): method is MutationFormMethod | V7_MutationFormMethod {\n  return validMutationMethods.has(method.toLowerCase() as MutationFormMethod);\n}\n\nasync function resolveDeferredResults(\n  currentMatches: AgnosticDataRouteMatch[],\n  matchesToLoad: (AgnosticDataRouteMatch | null)[],\n  results: DataResult[],\n  signals: (AbortSignal | null)[],\n  isFetcher: boolean,\n  currentLoaderData?: RouteData\n) {\n  for (let index = 0; index < results.length; index++) {\n    let result = results[index];\n    let match = matchesToLoad[index];\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match!.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && (isFetcher || isRevalidatingLoader)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      let signal = signals[index];\n      invariant(\n        signal,\n        \"Expected an AbortSignal for revalidating fetcher deferred result\"\n      );\n      await resolveDeferredData(result, signal, isFetcher).then((result) => {\n        if (result) {\n          results[index] = result || results[index];\n        }\n      });\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\n// Note: This should match the format exported by useMatches, so if you change\n// this please also change that :)  Eventually we'll DRY this up\nfunction createUseMatchesMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UseMatchesMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id] as unknown,\n    handle: route.handle as unknown,\n  };\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n//#endregion\n", "import * as React from \"react\";\nimport type {\n  AgnosticRouteMatch,\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  History,\n  Location,\n  RelativeRoutingType,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n  LazyRouteFunction,\n} from \"@remix-run/router\";\nimport type { Action as NavigationType } from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject extends NavigationContextObject {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level <Router> API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n  isDataRoute: boolean;\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n  isDataRoute: false,\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  <PERSON>er,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RevalidationState,\n  To,\n} from \"@remix-run/router\";\nimport {\n  Action as NavigationType,\n  UNSAFE_invariant as invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  UNSAFE_getPathContributingMatches as getPathContributingMatches,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n  DataRouteMatch,\n} from \"./context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n  AwaitContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a <Router>.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * <NavLink>.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, pathname),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nconst navigateEffectWarning =\n  `You should call navigate() in a React.useEffect(), not when ` +\n  `your component is first rendered.`;\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(\n  cb: Parameters<typeof React.useLayoutEffect>[0]\n) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by <Link>s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  let { isDataRoute } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\n\nfunction useNavigateUnstable(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getPathContributingMatches(matches).map((match) => match.pathnameBase)\n  );\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our history listener yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history (but only if we're not in a data router,\n      // otherwise it'll prepend the basename inside of the router).\n      // If this is a root navigation, then we navigate to the raw basename\n      // which allows the basename to have full control over the presence of a\n      // trailing slash on root links\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext,\n    ]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by <Outlet> to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getPathContributingMatches(matches).map((match) => match.pathnameBase)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an <Outlet> to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nexport function useRoutesImpl(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string,\n  dataRouterState?: RemixRouter[\"state\"]\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n  let remainingPathname =\n    parentPathnameBase === \"/\"\n      ? pathname\n      : pathname.slice(parentPathnameBase.length) || \"/\";\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined ||\n        matches[matches.length - 1].route.Component !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" ` +\n        `does not have an element or Component. This means it will render an <Outlet /> with a ` +\n        `null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterState\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own <code style={codeStyles}>ErrorBoundary</code> or{\" \"}\n          <code style={codeStyles}>errorElement</code> prop on your route.\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\nconst defaultErrorElement = <DefaultErrorComponent />;\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (\n      state.location !== props.location ||\n      (state.revalidation !== \"idle\" && props.revalidation === \"idle\")\n    ) {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error || state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    (match.route.errorElement || match.route.ErrorBoundary)\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState: RemixRouter[\"state\"] | null = null\n): React.ReactElement | null {\n  if (matches == null) {\n    if (dataRouterState?.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id]\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    let error = match.route.id ? errors?.[match.route.id] : null;\n    // Only data routers handle errors\n    let errorElement: React.ReactNode | null = null;\n    if (dataRouterState) {\n      errorElement = match.route.errorElement || defaultErrorElement;\n    }\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children: React.ReactNode;\n      if (error) {\n        children = errorElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = <match.route.Component />;\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return (\n        <RenderedRoute\n          match={match}\n          routeContext={{\n            outlet,\n            matches,\n            isDataRoute: dataRouterState != null,\n          }}\n          children={children}\n        />\n      );\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState &&\n      (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        revalidation={dataRouterState.revalidation}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches, isDataRoute: true }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n}\n\nenum DataRouterStateHook {\n  UseBlocker = \"useBlocker\",\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n  UseRouteId = \"useRouteId\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nexport function useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return {\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation,\n  };\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches() {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () =>\n      matches.map((match) => {\n        let { pathname, params } = match;\n        // Note: This structure matches that created by createUseMatchesMatch\n        // in the @remix-run/router , so if you change this please also change\n        // that :)  Eventually we'll DRY this up\n        return {\n          id: match.route.id,\n          pathname,\n          params,\n          data: loaderData[match.route.id] as unknown,\n          handle: match.route.handle as unknown,\n        };\n      }),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n\n  let route = React.useContext(RouteContext);\n  invariant(route, `useActionData must be used inside a RouteContext`);\n\n  return Object.values(state?.actionData || {})[0];\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor <Await /> value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor <Await /> value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n  let [blockerKey] = React.useState(() => String(++blockerId));\n\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (args) => {\n      return typeof shouldBlock === \"function\"\n        ? !!shouldBlock(args)\n        : !!shouldBlock;\n    },\n    [shouldBlock]\n  );\n\n  let blocker = router.getBlocker(blockerKey, blockerFunction);\n\n  // Cleanup on unmount\n  React.useEffect(\n    () => () => router.deleteBlocker(blockerKey),\n    [router, blockerKey]\n  );\n\n  // Prefer the blocker from state since DataRouterContext is memoized so this\n  // ensures we update on blocker state updates\n  return state.blockers.get(blockerKey) || blocker;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable(): NavigateFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our router subscriber yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n\n  return navigate;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import * as React from \"react\";\nimport type {\n  TrackedPromise,\n  InitialEntry,\n  Location,\n  MemoryHistory,\n  Router as RemixRouter,\n  To,\n  LazyRouteFunction,\n  RelativeRoutingType,\n  RouterState,\n} from \"@remix-run/router\";\nimport {\n  Action as NavigationType,\n  AbortedDeferredError,\n  createMemoryHistory,\n  UNSAFE_invariant as invariant,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n  UNSAFE_getPathContributingMatches as getPathContributingMatches,\n} from \"@remix-run/router\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  RouteMatch,\n  RouteObject,\n  Navigator,\n  NonIndexRouteObject,\n} from \"./context\";\nimport {\n  LocationContext,\n  NavigationContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  AwaitContext,\n  RouteContext,\n} from \"./context\";\nimport {\n  useAsyncValue,\n  useInRouterContext,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  _renderMatches,\n  useRoutesImpl,\n  useLocation,\n} from \"./hooks\";\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n}: RouterProviderProps): React.ReactElement {\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  let [state, setState] = React.useState(router.state);\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={router.basename}\n            location={router.state.location}\n            navigationType={router.state.historyAction}\n            navigator={navigator}\n          >\n            {router.state.initialized ? (\n              <DataRoutes routes={router.routes} state={state} />\n            ) : (\n              fallbackElement\n            )}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  state,\n}: {\n  routes: DataRouteObject[];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state);\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n}\n\n/**\n * A <Router> that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  warning(\n    !React.useContext(NavigationContext).static,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(\n    to,\n    getPathContributingMatches(matches).map((match) => match.pathnameBase),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n\n  React.useEffect(\n    () => navigate(JSON.parse(jsonPath), { replace, state, relative }),\n    [navigate, jsonPath, relative, replace, state]\n  );\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a <Router> directly. Instead, you'll render a\n * router that is more specific to your environment such as a <BrowserRouter>\n * in web browsers or a <StaticRouter> for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({ basename, navigator, static: staticProp }),\n    [basename, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key,\n      },\n      navigationType,\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (locationContext == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider children={children} value={locationContext} />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of <Route> elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        promise._error !== undefined\n          ? AwaitRenderStatus.error\n          : promise._data !== undefined\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on <Await>\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    let treePath = [...parentPath, index];\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary:\n        element.props.ErrorBoundary != null ||\n        element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "import * as React from \"react\";\nimport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  Fetcher,\n  HydrationState,\n  JsonFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  PathPattern,\n  RedirectFunction,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  ShouldRevalidateFunction,\n  To,\n  InitialEntry,\n  LazyRouteFunction,\n  FutureConfig,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  resolvePath,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  RouteProps,\n  PathRouteProps,\n  LayoutRouteProps,\n  IndexRouteProps,\n  RouterProps,\n  RoutesProps,\n  RouterProviderProps,\n} from \"./lib/components\";\nimport {\n  createRoutesFromChildren,\n  renderMatches,\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NavigateOptions,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigationType,\n  useNavigate,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useRouteId,\n  useLoaderData,\n  useMatches,\n  useNavigation,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutesImpl,\n} from \"./lib/hooks\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker as unstable_Blocker,\n  BlockerFunction as unstable_BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  Fetcher,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  To,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker as unstable_useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nfunction mapRouteProperties(route: RouteObject) {\n  let updates: Partial<RouteObject> & { hasErrorBoundary: boolean } = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null,\n  };\n\n  if (route.Component) {\n    if (__DEV__) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - \" +\n            \"`Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React.createElement(route.Component),\n      Component: undefined,\n    });\n  }\n\n  if (route.ErrorBoundary) {\n    if (__DEV__) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" +\n            \"`ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined,\n    });\n  }\n\n  return updates;\n}\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    future?: Partial<Omit<FutureConfig, \"v7_prependBasename\">>;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    mapRouteProperties,\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  NavigationContext as UNSAFE_NavigationContext,\n  LocationContext as UNSAFE_LocationContext,\n  RouteContext as UNSAFE_RouteContext,\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  mapRouteProperties as UNSAFE_mapRouteProperties,\n  useRouteId as UNSAFE_useRouteId,\n  useRoutesImpl as UNSAFE_useRoutesImpl,\n};\n", "import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    for (let key of defaultSearchParams.keys()) {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    }\n  }\n\n  return searchParams;\n}\n\nexport interface SubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The action URL used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n}\n\nexport function getFormSubmissionInfo(\n  target:\n    | HTMLFormElement\n    | HTMLButtonElement\n    | HTMLInputElement\n    | FormData\n    | URLSearchParams\n    | { [name: string]: string }\n    | null,\n  options: SubmitOptions,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData;\n} {\n  let method: string;\n  let action: string | null = null;\n  let encType: string;\n  let formData: FormData;\n\n  if (isFormElement(target)) {\n    let submissionTrigger: HTMLButtonElement | HTMLInputElement = (\n      options as any\n    ).submissionTrigger;\n\n    if (options.action) {\n      action = options.action;\n    } else {\n      // When grabbing the action from the element, it will have had the basename\n      // prefixed to ensure non-JS scenarios work, so strip it since we'll\n      // re-prefix in the router\n      let attr = target.getAttribute(\"action\");\n      action = attr ? stripBasename(attr, basename) : null;\n    }\n    method = options.method || target.getAttribute(\"method\") || defaultMethod;\n    encType =\n      options.encType || target.getAttribute(\"enctype\") || defaultEncType;\n\n    formData = new FormData(target);\n\n    if (submissionTrigger && submissionTrigger.name) {\n      formData.append(submissionTrigger.name, submissionTrigger.value);\n    }\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    if (options.action) {\n      action = options.action;\n    } else {\n      // When grabbing the action from the element, it will have had the basename\n      // prefixed to ensure non-JS scenarios work, so strip it since we'll\n      // re-prefix in the router\n      let attr =\n        target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n      action = attr ? stripBasename(attr, basename) : null;\n    }\n\n    method =\n      options.method ||\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      options.encType ||\n      target.getAttribute(\"formenctype\") ||\n      form.getAttribute(\"enctype\") ||\n      defaultEncType;\n\n    formData = new FormData(form);\n\n    // Include name + value from a <button>, appending in case the button name\n    // matches an existing input name\n    if (target.name) {\n      formData.append(target.name, target.value);\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = options.method || defaultMethod;\n    action = options.action || null;\n    encType = options.encType || defaultEncType;\n\n    if (target instanceof FormData) {\n      formData = target;\n    } else {\n      formData = new FormData();\n\n      if (target instanceof URLSearchParams) {\n        for (let [name, value] of target) {\n          formData.append(name, value);\n        }\n      } else if (target != null) {\n        for (let name of Object.keys(target)) {\n          formData.append(name, target[name]);\n        }\n      }\n    }\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport type {\n  NavigateOptions,\n  RelativeRoutingType,\n  RouteObject,\n  To,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  unstable_useBlocker as useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  FutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  ErrorResponse,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  unstable_Blocker,\n  unstable_BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  Fetcher,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  To,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  unstable_useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<FutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponse(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      let error = new Error(val.message);\n      // Wipe away the client-side stack trace.  Nothing to fill it in with\n      // because we don't serialize SSR stack traces for security reasons\n      error.stack = \"\";\n      serialized[key] = error;\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({ basename, children, window }: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({ basename, children, history }: HistoryRouterProps) {\n  const [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware <a>.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?:\n    | React.ReactNode\n    | ((props: { isActive: boolean; isPending: boolean }) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?:\n    | string\n    | ((props: {\n        isActive: boolean;\n        isPending: boolean;\n      }) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: {\n        isActive: boolean;\n        isPending: boolean;\n      }) => React.CSSProperties | undefined);\n}\n\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator } = React.useContext(NavigationContext);\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(toPathname.length) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp({ isActive, isPending });\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\"\n        ? styleProp({ isActive, isPending })\n        : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n      >\n        {typeof children === \"function\"\n          ? children({ isActive, isPending })\n          : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\nexport interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (props, ref) => {\n    return <FormImpl {...props} ref={ref} />;\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\ninterface FormImplProps extends FormProps {\n  fetcherKey?: string;\n  routeId?: string;\n}\n\nconst FormImpl = React.forwardRef<HTMLFormElement, FormImplProps>(\n  (\n    {\n      reloadDocument,\n      replace,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      fetcherKey,\n      routeId,\n      relative,\n      preventScrollReset,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmitImpl(fetcherKey, routeId);\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n    let formAction = useFormAction(action, { relative });\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        method: submitMethod,\n        replace,\n        relative,\n        preventScrollReset,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  FormImpl.displayName = \"FormImpl\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmitImpl = \"useSubmitImpl\",\n  UseFetcher = \"useFetcher\",\n}\n\nenum DataRouterStateHook {\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, { replace, state, preventScrollReset, relative });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params\\n\\n` +\n      `If you're unsure how to load polyfills, we recommend you check out ` +\n      `https://polyfill.io/v3/ which provides some recommendations about how ` +\n      `to load polyfills only for users that need them, instead of for every ` +\n      `user.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\ntype SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | { [name: string]: string }\n  | null;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  return useSubmitImpl();\n}\n\nfunction useSubmitImpl(\n  fetcherKey?: string,\n  fetcherRouteId?: string\n): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmitImpl);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback(\n    (target, options = {}) => {\n      if (typeof document === \"undefined\") {\n        throw new Error(\n          \"You are calling submit during the server render. \" +\n            \"Try calling submit within a `useEffect` or callback instead.\"\n        );\n      }\n\n      let { action, method, encType, formData } = getFormSubmissionInfo(\n        target,\n        options,\n        basename\n      );\n\n      // Base options shared between fetch() and navigate()\n      let opts = {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        formMethod: method as HTMLFormMethod,\n        formEncType: encType as FormEncType,\n      };\n\n      if (fetcherKey) {\n        invariant(\n          fetcherRouteId != null,\n          \"No routeId available for useFetcher()\"\n        );\n        router.fetch(fetcherKey, fetcherRouteId, action, opts);\n      } else {\n        router.navigate(action, {\n          ...opts,\n          replace: options.replace,\n          fromRouteId: currentRouteId,\n        });\n      }\n    },\n    [router, basename, fetcherKey, fetcherRouteId, currentRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // Previously we set the default action to \".\". The problem with this is that\n  // `useResolvedPath(\".\")` excludes search params and the hash of the resolved\n  // URL. This is the intended behavior of when \".\" is specifically provided as\n  // the form action, but inconsistent w/ browsers when the action is omitted.\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to these directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    // or hash\n    path.search = location.search;\n    path.hash = location.hash;\n\n    // When grabbing search params from the URL, remove the automatically\n    // inserted ?index param so we match the useResolvedPath search behavior\n    // which would not include ?index\n    if (match.route.index) {\n      let params = new URLSearchParams(path.search);\n      params.delete(\"index\");\n      path.search = params.toString() ? `?${params.toString()}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nfunction createFetcherForm(fetcherKey: string, routeId: string) {\n  let FetcherForm = React.forwardRef<HTMLFormElement, FormProps>(\n    (props, ref) => {\n      return (\n        <FormImpl\n          {...props}\n          ref={ref}\n          fetcherKey={fetcherKey}\n          routeId={routeId}\n        />\n      );\n    }\n  );\n  if (__DEV__) {\n    FetcherForm.displayName = \"fetcher.Form\";\n  }\n  return FetcherForm;\n}\n\nlet fetcherId = 0;\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: ReturnType<typeof createFetcherForm>;\n  submit: (\n    target: SubmitTarget,\n    // Fetchers cannot replace/preventScrollReset because they are not\n    // navigation events\n    options?: Omit<SubmitOptions, \"replace\" | \"preventScrollReset\">\n  ) => void;\n  load: (href: string) => void;\n};\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>(): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n\n  let route = React.useContext(RouteContext);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  let [fetcherKey] = React.useState(() => String(++fetcherId));\n  let [Form] = React.useState(() => {\n    invariant(routeId, `No routeId available for fetcher.Form()`);\n    return createFetcherForm(fetcherKey, routeId);\n  });\n  let [load] = React.useState(() => (href: string) => {\n    invariant(router, \"No router available for fetcher.load()\");\n    invariant(routeId, \"No routeId available for fetcher.load()\");\n    router.fetch(fetcherKey, routeId, href);\n  });\n  let submit = useSubmitImpl(fetcherKey, routeId);\n\n  let fetcher = router.getFetcher<TData>(fetcherKey);\n\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form,\n      submit,\n      load,\n      ...fetcher,\n    }),\n    [fetcher, Form, submit, load]\n  );\n\n  React.useEffect(() => {\n    // Is this busted when the React team gets real weird and calls effects\n    // twice on mount?  We really just need to garbage collect here when this\n    // fetcher is no longer around.\n    return () => {\n      if (!router) {\n        console.warn(`No router available to clean up from useFetcher()`);\n        return;\n      }\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): Fetcher[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return [...state.fetchers.values()];\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      sessionStorage.setItem(\n        storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n        JSON.stringify(savedScrollPositions)\n      );\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKey\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(location.hash.slice(1));\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({ when, message }: { when: boolean; message: string }) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n//#endregion\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOYA;CAAZ,SAAYA,SAAM;AAQhBA,EAAAA,QAAA,KAAA,IAAA;AAOAA,EAAAA,QAAA,MAAA,IAAA;AAMAA,EAAAA,QAAA,SAAA,IAAA;AACD,GAtBWA,WAAAA,SAsBX,CAAA,EAtBD;AAwLA,IAAMC,oBAAoB;AAmCV,SAAAC,oBACdC,SAAkC;AAAA,MAAlCA,YAAkC,QAAA;AAAlCA,cAAgC,CAAA;EAAE;AAElC,MAAI;IAAEC,iBAAiB,CAAC,GAAD;IAAOC;IAAcC,WAAW;EAAnD,IAA6DH;AACjE,MAAII;AACJA,YAAUH,eAAeI,IAAI,CAACC,OAAOC,WACnCC,qBACEF,OACA,OAAOA,UAAU,WAAW,OAAOA,MAAMG,OACzCF,WAAU,IAAI,YAAYG,MAHR,CADZ;AAOV,MAAIH,QAAQI,WACVT,gBAAgB,OAAOE,QAAQQ,SAAS,IAAIV,YADxB;AAGtB,MAAIW,SAAShB,OAAOiB;AACpB,MAAIC,WAA4B;AAEhC,WAASJ,WAAWK,GAAS;AAC3B,WAAOC,KAAKC,IAAID,KAAKE,IAAIH,GAAG,CAAZ,GAAgBZ,QAAQQ,SAAS,CAA1C;EACR;AACD,WAASQ,qBAAkB;AACzB,WAAOhB,QAAQG,KAAD;EACf;AACD,WAASC,qBACPa,IACAZ,OACAa,KAAY;AAAA,QADZb,UACY,QAAA;AADZA,cAAa;IACD;AAEZ,QAAIc,WAAWC,eACbpB,UAAUgB,mBAAkB,EAAGK,WAAW,KAC1CJ,IACAZ,OACAa,GAJ2B;AAM7BI,YACEH,SAASE,SAASE,OAAO,CAAzB,MAAgC,KAD3B,6DAEsDC,KAAKC,UAC9DR,EADyD,CAFtD;AAMP,WAAOE;EACR;AAED,WAASO,WAAWT,IAAM;AACxB,WAAO,OAAOA,OAAO,WAAWA,KAAKU,WAAWV,EAAD;EAChD;AAED,MAAIW,UAAyB;IAC3B,IAAIzB,QAAK;AACP,aAAOA;;IAET,IAAIM,SAAM;AACR,aAAOA;;IAET,IAAIU,WAAQ;AACV,aAAOH,mBAAkB;;IAE3BU;IACAG,UAAUZ,IAAE;AACV,aAAO,IAAIa,IAAIJ,WAAWT,EAAD,GAAM,kBAAxB;;IAETc,eAAed,IAAM;AACnB,UAAIe,OAAO,OAAOf,OAAO,WAAWgB,UAAUhB,EAAD,IAAOA;AACpD,aAAO;QACLI,UAAUW,KAAKX,YAAY;QAC3Ba,QAAQF,KAAKE,UAAU;QACvBC,MAAMH,KAAKG,QAAQ;;;IAGvBC,KAAKnB,IAAIZ,OAAK;AACZI,eAAShB,OAAO4C;AAChB,UAAIC,eAAelC,qBAAqBa,IAAIZ,KAAL;AACvCF,eAAS;AACTH,cAAQuC,OAAOpC,OAAOH,QAAQQ,QAAQ8B,YAAtC;AACA,UAAIvC,YAAYY,UAAU;AACxBA,iBAAS;UAAEF;UAAQU,UAAUmB;UAAcE,OAAO;QAAzC,CAAD;MACT;;IAEHC,QAAQxB,IAAIZ,OAAK;AACfI,eAAShB,OAAOiD;AAChB,UAAIJ,eAAelC,qBAAqBa,IAAIZ,KAAL;AACvCL,cAAQG,KAAD,IAAUmC;AACjB,UAAIvC,YAAYY,UAAU;AACxBA,iBAAS;UAAEF;UAAQU,UAAUmB;UAAcE,OAAO;QAAzC,CAAD;MACT;;IAEHG,GAAGH,OAAK;AACN/B,eAAShB,OAAOiB;AAChB,UAAIkC,YAAYrC,WAAWJ,QAAQqC,KAAT;AAC1B,UAAIF,eAAetC,QAAQ4C,SAAD;AAC1BzC,cAAQyC;AACR,UAAIjC,UAAU;AACZA,iBAAS;UAAEF;UAAQU,UAAUmB;UAAcE;QAAlC,CAAD;MACT;;IAEHK,OAAOC,IAAY;AACjBnC,iBAAWmC;AACX,aAAO,MAAK;AACVnC,mBAAW;;IAEd;;AAGH,SAAOiB;AACR;AAyBe,SAAAmB,qBACdnD,SAAmC;AAAA,MAAnCA,YAAmC,QAAA;AAAnCA,cAAiC,CAAA;EAAE;AAEnC,WAASoD,sBACPC,SACAC,eAAgC;AAEhC,QAAI;MAAE7B;MAAUa;MAAQC;QAASc,QAAO9B;AACxC,WAAOC;MACL;MACA;QAAEC;QAAUa;QAAQC;MAApB;;MAECe,cAAc7C,SAAS6C,cAAc7C,MAAM8C,OAAQ;MACnDD,cAAc7C,SAAS6C,cAAc7C,MAAMa,OAAQ;IALjC;EAOtB;AAED,WAASkC,kBAAkBH,SAAgBhC,IAAM;AAC/C,WAAO,OAAOA,OAAO,WAAWA,KAAKU,WAAWV,EAAD;EAChD;AAED,SAAOoC,mBACLL,uBACAI,mBACA,MACAxD,OAJuB;AAM1B;AA8Be,SAAA0D,kBACd1D,SAAgC;AAAA,MAAhCA,YAAgC,QAAA;AAAhCA,cAA8B,CAAA;EAAE;AAEhC,WAAS2D,mBACPN,SACAC,eAAgC;AAEhC,QAAI;MACF7B,WAAW;MACXa,SAAS;MACTC,OAAO;IAHL,IAIAF,UAAUgB,QAAO9B,SAASgB,KAAKqB,OAAO,CAA5B,CAAD;AACb,WAAOpC;MACL;MACA;QAAEC;QAAUa;QAAQC;MAApB;;MAECe,cAAc7C,SAAS6C,cAAc7C,MAAM8C,OAAQ;MACnDD,cAAc7C,SAAS6C,cAAc7C,MAAMa,OAAQ;IALjC;EAOtB;AAED,WAASuC,eAAeR,SAAgBhC,IAAM;AAC5C,QAAIyC,OAAOT,QAAOU,SAASC,cAAc,MAA9B;AACX,QAAIC,OAAO;AAEX,QAAIH,QAAQA,KAAKI,aAAa,MAAlB,GAA2B;AACrC,UAAIC,MAAMd,QAAO9B,SAAS0C;AAC1B,UAAIG,YAAYD,IAAIE,QAAQ,GAAZ;AAChBJ,aAAOG,cAAc,KAAKD,MAAMA,IAAIG,MAAM,GAAGF,SAAb;IACjC;AAED,WAAOH,OAAO,OAAO,OAAO5C,OAAO,WAAWA,KAAKU,WAAWV,EAAD;EAC9D;AAED,WAASkD,qBAAqBhD,UAAoBF,IAAM;AACtDK,YACEH,SAASE,SAASE,OAAO,CAAzB,MAAgC,KAD3B,+DAEwDC,KAAKC,UAChER,EAD2D,IAF/D,GAAA;EAMD;AAED,SAAOoC,mBACLE,oBACAE,gBACAU,sBACAvE,OAJuB;AAM1B;AAee,SAAAwE,UAAUC,OAAYC,SAAgB;AACpD,MAAID,UAAU,SAASA,UAAU,QAAQ,OAAOA,UAAU,aAAa;AACrE,UAAM,IAAIE,MAAMD,OAAV;EACP;AACF;AAEe,SAAAhD,QAAQkD,MAAWF,SAAe;AAChD,MAAI,CAACE,MAAM;AAET,QAAI,OAAOC,YAAY;AAAaA,cAAQC,KAAKJ,OAAb;AAEpC,QAAI;AAMF,YAAM,IAAIC,MAAMD,OAAV;IAEP,SAAQK,GAAP;IAAU;EACb;AACF;AAED,SAASC,YAAS;AAChB,SAAO/D,KAAKgE,OAAL,EAAcC,SAAS,EAAvB,EAA2BtB,OAAO,GAAG,CAArC;AACR;AAKD,SAASuB,gBAAgB5D,UAAoBhB,OAAa;AACxD,SAAO;IACLgD,KAAKhC,SAASd;IACda,KAAKC,SAASD;IACd8D,KAAK7E;;AAER;AAKK,SAAUiB,eACd6D,SACAhE,IACAZ,OACAa,KAAY;AAAA,MADZb,UACY,QAAA;AADZA,YAAa;EACD;AAEZ,MAAIc,WAAQ,SAAA;IACVE,UAAU,OAAO4D,YAAY,WAAWA,UAAUA,QAAQ5D;IAC1Da,QAAQ;IACRC,MAAM;KACF,OAAOlB,OAAO,WAAWgB,UAAUhB,EAAD,IAAOA,IAJnC;IAKVZ;;;;;IAKAa,KAAMD,MAAOA,GAAgBC,OAAQA,OAAO0D,UAAS;GAVvD;AAYA,SAAOzD;AACR;AAKe,SAAAQ,WAIA,MAAA;AAAA,MAJW;IACzBN,WAAW;IACXa,SAAS;IACTC,OAAO;MACO;AACd,MAAID,UAAUA,WAAW;AACvBb,gBAAYa,OAAOX,OAAO,CAAd,MAAqB,MAAMW,SAAS,MAAMA;AACxD,MAAIC,QAAQA,SAAS;AACnBd,gBAAYc,KAAKZ,OAAO,CAAZ,MAAmB,MAAMY,OAAO,MAAMA;AACpD,SAAOd;AACR;AAKK,SAAUY,UAAUD,MAAY;AACpC,MAAIkD,aAA4B,CAAA;AAEhC,MAAIlD,MAAM;AACR,QAAIgC,YAAYhC,KAAKiC,QAAQ,GAAb;AAChB,QAAID,aAAa,GAAG;AAClBkB,iBAAW/C,OAAOH,KAAKwB,OAAOQ,SAAZ;AAClBhC,aAAOA,KAAKwB,OAAO,GAAGQ,SAAf;IACR;AAED,QAAImB,cAAcnD,KAAKiC,QAAQ,GAAb;AAClB,QAAIkB,eAAe,GAAG;AACpBD,iBAAWhD,SAASF,KAAKwB,OAAO2B,WAAZ;AACpBnD,aAAOA,KAAKwB,OAAO,GAAG2B,WAAf;IACR;AAED,QAAInD,MAAM;AACRkD,iBAAW7D,WAAWW;IACvB;EACF;AAED,SAAOkD;AACR;AASD,SAAS7B,mBACP+B,aACA1D,YACA2D,kBACAzF,SAA+B;AAAA,MAA/BA,YAA+B,QAAA;AAA/BA,cAA6B,CAAA;EAAE;AAE/B,MAAI;IAAEqD,QAAAA,UAASU,SAAS2B;IAAcvF,WAAW;EAA7C,IAAuDH;AAC3D,MAAIsD,gBAAgBD,QAAOrB;AAC3B,MAAInB,SAAShB,OAAOiB;AACpB,MAAIC,WAA4B;AAEhC,MAAIR,QAAQoF,SAAQ;AAIpB,MAAIpF,SAAS,MAAM;AACjBA,YAAQ;AACR+C,kBAAcsC,aAAkBtC,SAAAA,CAAAA,GAAAA,cAAc7C,OAA9C;MAAqD2E,KAAK7E;IAA1D,CAAA,GAAmE,EAAnE;EACD;AAED,WAASoF,WAAQ;AACf,QAAIlF,QAAQ6C,cAAc7C,SAAS;MAAE2E,KAAK;;AAC1C,WAAO3E,MAAM2E;EACd;AAED,WAASS,YAAS;AAChBhF,aAAShB,OAAOiB;AAChB,QAAIkC,YAAY2C,SAAQ;AACxB,QAAI/C,QAAQI,aAAa,OAAO,OAAOA,YAAYzC;AACnDA,YAAQyC;AACR,QAAIjC,UAAU;AACZA,eAAS;QAAEF;QAAQU,UAAUS,QAAQT;QAAUqB;MAAtC,CAAD;IACT;EACF;AAED,WAASJ,KAAKnB,IAAQZ,OAAW;AAC/BI,aAAShB,OAAO4C;AAChB,QAAIlB,WAAWC,eAAeQ,QAAQT,UAAUF,IAAIZ,KAAvB;AAC7B,QAAIgF;AAAkBA,uBAAiBlE,UAAUF,EAAX;AAEtCd,YAAQoF,SAAQ,IAAK;AACrB,QAAIG,eAAeX,gBAAgB5D,UAAUhB,KAAX;AAClC,QAAI4D,MAAMnC,QAAQF,WAAWP,QAAnB;AAGV,QAAI;AACF+B,oBAAcyC,UAAUD,cAAc,IAAI3B,GAA1C;aACO6B,OAAP;AAGA3C,MAAAA,QAAO9B,SAAS0E,OAAO9B,GAAvB;IACD;AAED,QAAIhE,YAAYY,UAAU;AACxBA,eAAS;QAAEF;QAAQU,UAAUS,QAAQT;QAAUqB,OAAO;MAA7C,CAAD;IACT;EACF;AAED,WAASC,QAAQxB,IAAQZ,OAAW;AAClCI,aAAShB,OAAOiD;AAChB,QAAIvB,WAAWC,eAAeQ,QAAQT,UAAUF,IAAIZ,KAAvB;AAC7B,QAAIgF;AAAkBA,uBAAiBlE,UAAUF,EAAX;AAEtCd,YAAQoF,SAAQ;AAChB,QAAIG,eAAeX,gBAAgB5D,UAAUhB,KAAX;AAClC,QAAI4D,MAAMnC,QAAQF,WAAWP,QAAnB;AACV+B,kBAAcsC,aAAaE,cAAc,IAAI3B,GAA7C;AAEA,QAAIhE,YAAYY,UAAU;AACxBA,eAAS;QAAEF;QAAQU,UAAUS,QAAQT;QAAUqB,OAAO;MAA7C,CAAD;IACT;EACF;AAED,WAASX,UAAUZ,IAAM;AAIvB,QAAIyC,OACFT,QAAO9B,SAAS2E,WAAW,SACvB7C,QAAO9B,SAAS2E,SAChB7C,QAAO9B,SAAS0C;AAEtB,QAAIA,OAAO,OAAO5C,OAAO,WAAWA,KAAKU,WAAWV,EAAD;AACnDmD,cACEV,MACsEG,wEAAAA,IAF/D;AAIT,WAAO,IAAI/B,IAAI+B,MAAMH,IAAd;EACR;AAED,MAAI9B,UAAmB;IACrB,IAAInB,SAAM;AACR,aAAOA;;IAET,IAAIU,WAAQ;AACV,aAAOiE,YAAYnC,SAAQC,aAAT;;IAEpBL,OAAOC,IAAY;AACjB,UAAInC,UAAU;AACZ,cAAM,IAAI4D,MAAM,4CAAV;MACP;AACDtB,MAAAA,QAAO8C,iBAAiBrG,mBAAmB+F,SAA3C;AACA9E,iBAAWmC;AAEX,aAAO,MAAK;AACVG,QAAAA,QAAO+C,oBAAoBtG,mBAAmB+F,SAA9C;AACA9E,mBAAW;;;IAGfe,WAAWT,IAAE;AACX,aAAOS,WAAWuB,SAAQhC,EAAT;;IAEnBY;IACAE,eAAed,IAAE;AAEf,UAAI8C,MAAMlC,UAAUZ,EAAD;AACnB,aAAO;QACLI,UAAU0C,IAAI1C;QACda,QAAQ6B,IAAI7B;QACZC,MAAM4B,IAAI5B;;;IAGdC;IACAK;IACAE,GAAG/B,GAAC;AACF,aAAOsC,cAAcP,GAAG/B,CAAjB;IACR;;AAGH,SAAOgB;AACR;ACpsBD,IAAYqE;CAAZ,SAAYA,aAAU;AACpBA,EAAAA,YAAA,MAAA,IAAA;AACAA,EAAAA,YAAA,UAAA,IAAA;AACAA,EAAAA,YAAA,UAAA,IAAA;AACAA,EAAAA,YAAA,OAAA,IAAA;AACD,GALWA,eAAAA,aAKX,CAAA,EALD;AA+LO,IAAMC,qBAAqB,oBAAIC,IAAuB,CAC3D,QACA,iBACA,QACA,MACA,SACA,UAN2D,CAA3B;AAoJlC,SAASC,aACPC,OAA0B;AAE1B,SAAOA,MAAMlG,UAAU;AACxB;AAIK,SAAUmG,0BACdC,QACAC,qBACAC,YACAC,UAA4B;AAAA,MAD5BD,eAC4B,QAAA;AAD5BA,iBAAuB,CAAA;EACK;AAAA,MAA5BC,aAA4B,QAAA;AAA5BA,eAA0B,CAAA;EAAE;AAE5B,SAAOH,OAAOtG,IAAI,CAACoG,OAAOlG,UAAS;AACjC,QAAIwG,WAAW,CAAC,GAAGF,YAAYtG,KAAhB;AACf,QAAIyG,KAAK,OAAOP,MAAMO,OAAO,WAAWP,MAAMO,KAAKD,SAASE,KAAK,GAAd;AACnDzC,cACEiC,MAAMlG,UAAU,QAAQ,CAACkG,MAAMS,UADjC,2CAAA;AAIA1C,cACE,CAACsC,SAASE,EAAD,GACT,uCAAqCA,KACnC,kEAHK;AAMT,QAAIR,aAAaC,KAAD,GAAS;AACvB,UAAIU,aACCV,SAAAA,CAAAA,GAAAA,OACAG,oBAAmBH,KAAD,GAFT;QAGZO;OAHF;AAKAF,eAASE,EAAD,IAAOG;AACf,aAAOA;IACR,OAAM;AACL,UAAIC,oBACCX,SAAAA,CAAAA,GAAAA,OACAG,oBAAmBH,KAAD,GAFF;QAGnBO;QACAE,UAAUxG;OAJZ;AAMAoG,eAASE,EAAD,IAAOI;AAEf,UAAIX,MAAMS,UAAU;AAClBE,0BAAkBF,WAAWR,0BAC3BD,MAAMS,UACNN,qBACAG,UACAD,QAJoD;MAMvD;AAED,aAAOM;IACR;EACF,CAzCM;AA0CR;AAOK,SAAUC,YAGdV,QACAW,aACAC,UAAc;AAAA,MAAdA,aAAc,QAAA;AAAdA,eAAW;EAAG;AAEd,MAAIhG,WACF,OAAO+F,gBAAgB,WAAWjF,UAAUiF,WAAD,IAAgBA;AAE7D,MAAI7F,WAAW+F,cAAcjG,SAASE,YAAY,KAAK8F,QAA3B;AAE5B,MAAI9F,YAAY,MAAM;AACpB,WAAO;EACR;AAED,MAAIgG,WAAWC,cAAcf,MAAD;AAC5BgB,oBAAkBF,QAAD;AAEjB,MAAIG,UAAU;AACd,WAASC,IAAI,GAAGD,WAAW,QAAQC,IAAIJ,SAAS7G,QAAQ,EAAEiH,GAAG;AAC3DD,cAAUE;MACRL,SAASI,CAAD;;;;;;;MAORE,gBAAgBtG,QAAD;IARS;EAU3B;AAED,SAAOmG;AACR;AAmBD,SAASF,cAGPf,QACAc,UACAO,aACAnB,YAAe;AAAA,MAFfY,aAEe,QAAA;AAFfA,eAA2C,CAAA;EAE5B;AAAA,MADfO,gBACe,QAAA;AADfA,kBAA4C,CAAA;EAC7B;AAAA,MAAfnB,eAAe,QAAA;AAAfA,iBAAa;EAAE;AAEf,MAAIoB,eAAe,CACjBxB,OACAlG,OACA2H,iBACE;AACF,QAAIC,OAAmC;MACrCD,cACEA,iBAAiBxH,SAAY+F,MAAMrE,QAAQ,KAAK8F;MAClDE,eAAe3B,MAAM2B,kBAAkB;MACvCC,eAAe9H;MACfkG;;AAGF,QAAI0B,KAAKD,aAAaI,WAAW,GAA7B,GAAmC;AACrC9D,gBACE2D,KAAKD,aAAaI,WAAWzB,UAA7B,GACA,0BAAwBsB,KAAKD,eACvBrB,0BAAAA,MAAAA,aADN,mDAAA,6DAFO;AAOTsB,WAAKD,eAAeC,KAAKD,aAAa5D,MAAMuC,WAAWjG,MAAnC;IACrB;AAED,QAAIwB,OAAOmG,UAAU,CAAC1B,YAAYsB,KAAKD,YAAlB,CAAD;AACpB,QAAIM,aAAaR,YAAYS,OAAON,IAAnB;AAKjB,QAAI1B,MAAMS,YAAYT,MAAMS,SAAStG,SAAS,GAAG;AAC/C4D;;;QAGEiC,MAAMlG,UAAU;QAChB,6DACuC6B,uCAAAA,OADvC;MAJO;AAQTsF,oBAAcjB,MAAMS,UAAUO,UAAUe,YAAYpG,IAAvC;IACd;AAID,QAAIqE,MAAMrE,QAAQ,QAAQ,CAACqE,MAAMlG,OAAO;AACtC;IACD;AAEDkH,aAASjF,KAAK;MACZJ;MACAsG,OAAOC,aAAavG,MAAMqE,MAAMlG,KAAb;MACnBiI;KAHF;;AAMF7B,SAAOiC,QAAQ,CAACnC,OAAOlG,UAAS;AAAA,QAAA;AAE9B,QAAIkG,MAAMrE,SAAS,MAAM,GAACqE,cAAAA,MAAMrE,SAAP,QAAC,YAAYyG,SAAS,GAArB,IAA2B;AACnDZ,mBAAaxB,OAAOlG,KAAR;IACb,OAAM;AACL,eAASuI,YAAYC,wBAAwBtC,MAAMrE,IAAP,GAAc;AACxD6F,qBAAaxB,OAAOlG,OAAOuI,QAAf;MACb;IACF;GARH;AAWA,SAAOrB;AACR;AAgBD,SAASsB,wBAAwB3G,MAAY;AAC3C,MAAI4G,WAAW5G,KAAK6G,MAAM,GAAX;AACf,MAAID,SAASpI,WAAW;AAAG,WAAO,CAAA;AAElC,MAAI,CAACsI,OAAO,GAAGC,IAAX,IAAmBH;AAGvB,MAAII,aAAaF,MAAMG,SAAS,GAAf;AAEjB,MAAIC,WAAWJ,MAAMrG,QAAQ,OAAO,EAArB;AAEf,MAAIsG,KAAKvI,WAAW,GAAG;AAGrB,WAAOwI,aAAa,CAACE,UAAU,EAAX,IAAiB,CAACA,QAAD;EACtC;AAED,MAAIC,eAAeR,wBAAwBI,KAAKlC,KAAK,GAAV,CAAD;AAE1C,MAAIuC,SAAmB,CAAA;AASvBA,SAAOhH,KACL,GAAG+G,aAAalJ,IAAKoJ,aACnBA,YAAY,KAAKH,WAAW,CAACA,UAAUG,OAAX,EAAoBxC,KAAK,GAAzB,CAD3B,CADL;AAOA,MAAImC,YAAY;AACdI,WAAOhH,KAAK,GAAG+G,YAAf;EACD;AAGD,SAAOC,OAAOnJ,IAAKyI,cACjB1G,KAAKkG,WAAW,GAAhB,KAAwBQ,aAAa,KAAK,MAAMA,QAD3C;AAGR;AAED,SAASnB,kBAAkBF,UAAuB;AAChDA,WAASiC,KAAK,CAACC,GAAGC,MAChBD,EAAEjB,UAAUkB,EAAElB,QACVkB,EAAElB,QAAQiB,EAAEjB,QACZmB,eACEF,EAAEnB,WAAWnI,IAAK8H,UAASA,KAAKE,aAAhC,GACAuB,EAAEpB,WAAWnI,IAAK8H,UAASA,KAAKE,aAAhC,CAFY,CAHpB;AAQD;AAED,IAAMyB,UAAU;AAChB,IAAMC,sBAAsB;AAC5B,IAAMC,kBAAkB;AACxB,IAAMC,oBAAoB;AAC1B,IAAMC,qBAAqB;AAC3B,IAAMC,eAAe;AACrB,IAAMC,UAAWC,OAAcA,MAAM;AAErC,SAAS1B,aAAavG,MAAc7B,OAA0B;AAC5D,MAAIyI,WAAW5G,KAAK6G,MAAM,GAAX;AACf,MAAIqB,eAAetB,SAASpI;AAC5B,MAAIoI,SAASuB,KAAKH,OAAd,GAAwB;AAC1BE,oBAAgBH;EACjB;AAED,MAAI5J,OAAO;AACT+J,oBAAgBN;EACjB;AAED,SAAOhB,SACJwB,OAAQH,OAAM,CAACD,QAAQC,CAAD,CADlB,EAEJI,OACC,CAAC/B,OAAOgC,YACNhC,SACCoB,QAAQa,KAAKD,OAAb,IACGX,sBACAW,YAAY,KACZT,oBACAC,qBACNI,YAVG;AAYR;AAED,SAAST,eAAeF,GAAaC,GAAW;AAC9C,MAAIgB,WACFjB,EAAE/I,WAAWgJ,EAAEhJ,UAAU+I,EAAErF,MAAM,GAAG,EAAX,EAAeuG,MAAM,CAAC7J,GAAG6G,MAAM7G,MAAM4I,EAAE/B,CAAD,CAAtC;AAE3B,SAAO+C;;;;;IAKHjB,EAAEA,EAAE/I,SAAS,CAAZ,IAAiBgJ,EAAEA,EAAEhJ,SAAS,CAAZ;;;;IAGnB;;AACL;AAED,SAASkH,iBAIPgD,QACArJ,UAAgB;AAEhB,MAAI;IAAE+G;EAAF,IAAiBsC;AAErB,MAAIC,gBAAgB,CAAA;AACpB,MAAIC,kBAAkB;AACtB,MAAIpD,UAA2D,CAAA;AAC/D,WAASC,IAAI,GAAGA,IAAIW,WAAW5H,QAAQ,EAAEiH,GAAG;AAC1C,QAAIM,OAAOK,WAAWX,CAAD;AACrB,QAAIoD,MAAMpD,MAAMW,WAAW5H,SAAS;AACpC,QAAIsK,oBACFF,oBAAoB,MAChBvJ,WACAA,SAAS6C,MAAM0G,gBAAgBpK,MAA/B,KAA0C;AAChD,QAAIuK,QAAQC,UACV;MAAEhJ,MAAM+F,KAAKD;MAAcE,eAAeD,KAAKC;MAAe6C;OAC9DC,iBAFmB;AAKrB,QAAI,CAACC;AAAO,aAAO;AAEnBE,WAAOpF,OAAO8E,eAAeI,MAAMG,MAAnC;AAEA,QAAI7E,QAAQ0B,KAAK1B;AAEjBmB,YAAQpF,KAAK;;MAEX8I,QAAQP;MACRtJ,UAAU8G,UAAU,CAACyC,iBAAiBG,MAAM1J,QAAxB,CAAD;MACnB8J,cAAcC,kBACZjD,UAAU,CAACyC,iBAAiBG,MAAMI,YAAxB,CAAD,CADoB;MAG/B9E;KAPF;AAUA,QAAI0E,MAAMI,iBAAiB,KAAK;AAC9BP,wBAAkBzC,UAAU,CAACyC,iBAAiBG,MAAMI,YAAxB,CAAD;IAC5B;EACF;AAED,SAAO3D;AACR;SAOe6D,aACdC,cACAJ,QAEa;AAAA,MAFbA,WAEa,QAAA;AAFbA,aAEI,CAAA;EAAS;AAEb,MAAIlJ,OAAesJ;AACnB,MAAItJ,KAAKiH,SAAS,GAAd,KAAsBjH,SAAS,OAAO,CAACA,KAAKiH,SAAS,IAAd,GAAqB;AAC9D3H,YACE,OACA,iBAAeU,OAAf,sCAAA,MACMA,KAAKS,QAAQ,OAAO,IAApB,IADN,uCAAA,sEAAA,sCAGsCT,KAAKS,QAAQ,OAAO,IAApB,IAHtC,KAFK;AAOPT,WAAOA,KAAKS,QAAQ,OAAO,IAApB;EACR;AAGD,QAAM8I,SAASvJ,KAAKkG,WAAW,GAAhB,IAAuB,MAAM;AAE5C,QAAMU,WAAW5G,KACd6G,MAAM,KADQ,EAEd5I,IAAI,CAACqK,SAASnK,OAAOqL,UAAS;AAC7B,UAAMC,gBAAgBtL,UAAUqL,MAAMhL,SAAS;AAG/C,QAAIiL,iBAAiBnB,YAAY,KAAK;AACpC,YAAMoB,OAAO;AACb,YAAMC,YAAYT,OAAOQ,IAAD;AAGxB,aAAOC;IACR;AAED,UAAMC,WAAWtB,QAAQS,MAAM,eAAd;AACjB,QAAIa,UAAU;AACZ,YAAM,CAAA,EAAG1K,KAAK2K,QAAR,IAAoBD;AAC1B,UAAIE,QAAQZ,OAAOhK,GAAD;AAElB,UAAI2K,aAAa,KAAK;AACpB,eAAOC,SAAS,OAAO,KAAKA;MAC7B;AAED,UAAIA,SAAS,MAAM;AACjB1H,kBAAU,OAAoBlD,eAAAA,MAA9B,SAAA;MACD;AAED,aAAO4K;IACR;AAGD,WAAOxB,QAAQ7H,QAAQ,QAAQ,EAAxB;EACR,CAhCc,EAkCd2H,OAAQE,aAAY,CAAC,CAACA,OAlCR;AAoCjB,SAAOiB,SAAS3C,SAAS/B,KAAK,GAAd;AACjB;AAuDe,SAAAmE,UAIde,SACA1K,UAAgB;AAEhB,MAAI,OAAO0K,YAAY,UAAU;AAC/BA,cAAU;MAAE/J,MAAM+J;MAAS/D,eAAe;MAAO6C,KAAK;;EACvD;AAED,MAAI,CAACmB,SAASC,UAAV,IAAwBC,YAC1BH,QAAQ/J,MACR+J,QAAQ/D,eACR+D,QAAQlB,GAH6B;AAMvC,MAAIE,QAAQ1J,SAAS0J,MAAMiB,OAAf;AACZ,MAAI,CAACjB;AAAO,WAAO;AAEnB,MAAIH,kBAAkBG,MAAM,CAAD;AAC3B,MAAII,eAAeP,gBAAgBnI,QAAQ,WAAW,IAAnC;AACnB,MAAI0J,gBAAgBpB,MAAM7G,MAAM,CAAZ;AACpB,MAAIgH,SAAiBe,WAAW5B,OAC9B,CAAC+B,MAAMC,WAAWlM,UAAS;AAGzB,QAAIkM,cAAc,KAAK;AACrB,UAAIC,aAAaH,cAAchM,KAAD,KAAW;AACzCgL,qBAAeP,gBACZ1G,MAAM,GAAG0G,gBAAgBpK,SAAS8L,WAAW9L,MADjC,EAEZiC,QAAQ,WAAW,IAFP;IAGhB;AAED2J,SAAKC,SAAD,IAAcE,yBAChBJ,cAAchM,KAAD,KAAW,IACxBkM,SAFwC;AAI1C,WAAOD;KAET,CAAA,CAjBmB;AAoBrB,SAAO;IACLlB;IACA7J,UAAUuJ;IACVO;IACAY;;AAEH;AAED,SAASG,YACPlK,MACAgG,eACA6C,KAAU;AAAA,MADV7C,kBACU,QAAA;AADVA,oBAAgB;EACN;AAAA,MAAV6C,QAAU,QAAA;AAAVA,UAAM;EAAI;AAEVvJ,UACEU,SAAS,OAAO,CAACA,KAAKiH,SAAS,GAAd,KAAsBjH,KAAKiH,SAAS,IAAd,GACvC,iBAAejH,OAAf,sCAAA,MACMA,KAAKS,QAAQ,OAAO,IAApB,IADN,uCAAA,sEAAA,sCAGsCT,KAAKS,QAAQ,OAAO,IAApB,IAHtC,KAFK;AAQP,MAAIwJ,aAAuB,CAAA;AAC3B,MAAIO,eACF,MACAxK,KACGS,QAAQ,WAAW,EADtB,EAEGA,QAAQ,QAAQ,GAFnB,EAGGA,QAAQ,uBAAuB,MAHlC,EAIGA,QAAQ,aAAa,CAACgK,GAAWJ,cAAqB;AACrDJ,eAAW7J,KAAKiK,SAAhB;AACA,WAAO;EACR,CAPH;AASF,MAAIrK,KAAKiH,SAAS,GAAd,GAAoB;AACtBgD,eAAW7J,KAAK,GAAhB;AACAoK,oBACExK,SAAS,OAAOA,SAAS,OACrB,UACA;aACG6I,KAAK;AAEd2B,oBAAgB;aACPxK,SAAS,MAAMA,SAAS,KAAK;AAQtCwK,oBAAgB;EACjB;AAAM;AAIP,MAAIR,UAAU,IAAIU,OAAOF,cAAcxE,gBAAgB1H,SAAY,GAArD;AAEd,SAAO,CAAC0L,SAASC,UAAV;AACR;AAED,SAAStE,gBAAgBtD,OAAa;AACpC,MAAI;AACF,WAAOsI,UAAUtI,KAAD;WACTuB,OAAP;AACAtE,YACE,OACA,mBAAiB+C,QAEFuB,6GAAAA,eAAAA,QAFf,KAFK;AAOP,WAAOvB;EACR;AACF;AAED,SAASkI,yBAAyBlI,OAAegI,WAAiB;AAChE,MAAI;AACF,WAAOO,mBAAmBvI,KAAD;WAClBuB,OAAP;AACAtE,YACE,OACA,kCAAgC+K,YAAhC,mCAAA,kBACkBhI,QADlB,qDAAA,qCAEqCuB,QAFrC,KAFK;AAOP,WAAOvB;EACR;AACF;AAKe,SAAA+C,cACd/F,UACA8F,UAAgB;AAEhB,MAAIA,aAAa;AAAK,WAAO9F;AAE7B,MAAI,CAACA,SAASwL,YAAT,EAAuB3E,WAAWf,SAAS0F,YAAT,CAAlC,GAA2D;AAC9D,WAAO;EACR;AAID,MAAIC,aAAa3F,SAAS8B,SAAS,GAAlB,IACb9B,SAAS3G,SAAS,IAClB2G,SAAS3G;AACb,MAAIuM,WAAW1L,SAASE,OAAOuL,UAAhB;AACf,MAAIC,YAAYA,aAAa,KAAK;AAEhC,WAAO;EACR;AAED,SAAO1L,SAAS6C,MAAM4I,UAAf,KAA8B;AACtC;SAOeE,YAAY/L,IAAQgM,cAAkB;AAAA,MAAlBA,iBAAkB,QAAA;AAAlBA,mBAAe;EAAG;AACpD,MAAI;IACF5L,UAAU6L;IACVhL,SAAS;IACTC,OAAO;MACL,OAAOlB,OAAO,WAAWgB,UAAUhB,EAAD,IAAOA;AAE7C,MAAII,WAAW6L,aACXA,WAAWhF,WAAW,GAAtB,IACEgF,aACAC,gBAAgBD,YAAYD,YAAb,IACjBA;AAEJ,SAAO;IACL5L;IACAa,QAAQkL,gBAAgBlL,MAAD;IACvBC,MAAMkL,cAAclL,IAAD;;AAEtB;AAED,SAASgL,gBAAgBrF,cAAsBmF,cAAoB;AACjE,MAAIrE,WAAWqE,aAAaxK,QAAQ,QAAQ,EAA7B,EAAiCoG,MAAM,GAAvC;AACf,MAAIyE,mBAAmBxF,aAAae,MAAM,GAAnB;AAEvByE,mBAAiB9E,QAAS8B,aAAW;AACnC,QAAIA,YAAY,MAAM;AAEpB,UAAI1B,SAASpI,SAAS;AAAGoI,iBAAS2E,IAAT;IAC1B,WAAUjD,YAAY,KAAK;AAC1B1B,eAASxG,KAAKkI,OAAd;IACD;GANH;AASA,SAAO1B,SAASpI,SAAS,IAAIoI,SAAS/B,KAAK,GAAd,IAAqB;AACnD;AAED,SAAS2G,oBACPC,MACAC,OACAC,MACA3L,MAAmB;AAEnB,SACE,uBAAqByL,OACbC,0CAAAA,SAAAA,QAAkBlM,cAAAA,KAAKC,UAC7BO,IADwB,IAD1B,yCAAA,SAIQ2L,OALV,8DAAA;AAQD;AAyBK,SAAUC,2BAEdpG,SAAY;AACZ,SAAOA,QAAQ4C,OACb,CAACW,OAAO5K,UACNA,UAAU,KAAM4K,MAAM1E,MAAMrE,QAAQ+I,MAAM1E,MAAMrE,KAAKxB,SAAS,CAF3D;AAIR;AAKK,SAAUqN,UACdC,OACAC,gBACAC,kBACAC,gBAAsB;AAAA,MAAtBA,mBAAsB,QAAA;AAAtBA,qBAAiB;EAAK;AAEtB,MAAIhN;AACJ,MAAI,OAAO6M,UAAU,UAAU;AAC7B7M,SAAKgB,UAAU6L,KAAD;EACf,OAAM;AACL7M,SAAE,SAAA,CAAA,GAAQ6M,KAAR;AAEF1J,cACE,CAACnD,GAAGI,YAAY,CAACJ,GAAGI,SAASoH,SAAS,GAArB,GACjB+E,oBAAoB,KAAK,YAAY,UAAUvM,EAA5B,CAFZ;AAITmD,cACE,CAACnD,GAAGI,YAAY,CAACJ,GAAGI,SAASoH,SAAS,GAArB,GACjB+E,oBAAoB,KAAK,YAAY,QAAQvM,EAA1B,CAFZ;AAITmD,cACE,CAACnD,GAAGiB,UAAU,CAACjB,GAAGiB,OAAOuG,SAAS,GAAnB,GACf+E,oBAAoB,KAAK,UAAU,QAAQvM,EAAxB,CAFZ;EAIV;AAED,MAAIiN,cAAcJ,UAAU,MAAM7M,GAAGI,aAAa;AAClD,MAAI6L,aAAagB,cAAc,MAAMjN,GAAGI;AAExC,MAAI8M;AAWJ,MAAIF,kBAAkBf,cAAc,MAAM;AACxCiB,WAAOH;EACR,OAAM;AACL,QAAII,qBAAqBL,eAAevN,SAAS;AAEjD,QAAI0M,WAAWhF,WAAW,IAAtB,GAA6B;AAC/B,UAAImG,aAAanB,WAAWrE,MAAM,GAAjB;AAKjB,aAAOwF,WAAW,CAAD,MAAQ,MAAM;AAC7BA,mBAAWC,MAAX;AACAF,8BAAsB;MACvB;AAEDnN,SAAGI,WAAWgN,WAAWxH,KAAK,GAAhB;IACf;AAIDsH,WAAOC,sBAAsB,IAAIL,eAAeK,kBAAD,IAAuB;EACvE;AAED,MAAIpM,OAAOgL,YAAY/L,IAAIkN,IAAL;AAGtB,MAAII,2BACFrB,cAAcA,eAAe,OAAOA,WAAWjE,SAAS,GAApB;AAEtC,MAAIuF,2BACDN,eAAehB,eAAe,QAAQc,iBAAiB/E,SAAS,GAA1B;AACzC,MACE,CAACjH,KAAKX,SAAS4H,SAAS,GAAvB,MACAsF,4BAA4BC,0BAC7B;AACAxM,SAAKX,YAAY;EAClB;AAED,SAAOW;AACR;IAiBYyM,YAAaC,WACxBA,MAAMC,KAAK,GAAX,EAAgBC,QAAQ,UAAU,GAAlC;IAKWC,oBAAqBC,cAChCA,SAASF,QAAQ,QAAQ,EAAzB,EAA6BA,QAAQ,QAAQ,GAA7C;AAKK,IAAMG,kBAAmBC,YAC9B,CAACA,UAAUA,WAAW,MAClB,KACAA,OAAOC,WAAW,GAAlB,IACAD,SACA,MAAMA;AAKL,IAAME,gBAAiBC,UAC5B,CAACA,QAAQA,SAAS,MAAM,KAAKA,KAAKF,WAAW,GAAhB,IAAuBE,OAAO,MAAMA;AAW5D,IAAMC,OAAqB,SAArBA,MAAsBC,MAAMC,MAAa;AAAA,MAAbA,SAAa,QAAA;AAAbA,WAAO,CAAA;EAAM;AACpD,MAAIC,eAAe,OAAOD,SAAS,WAAW;IAAEE,QAAQF;EAAV,IAAmBA;AAEjE,MAAIG,UAAU,IAAIC,QAAQH,aAAaE,OAAzB;AACd,MAAI,CAACA,QAAQE,IAAI,cAAZ,GAA6B;AAChCF,YAAQG,IAAI,gBAAgB,iCAA5B;EACD;AAED,SAAO,IAAIC,SAASC,KAAKC,UAAUV,IAAf,GAAb,SAAA,CAAA,GACFE,cADE;IAELE;GAFF,CAAA;AAID;AAQK,IAAOO,uBAAP,cAAoCC,MAAK;AAAA;IAElCC,qBAAY;EAWvBC,YAAYd,MAA+BE,cAA2B;AAV9D,SAAAa,iBAA8B,oBAAIC,IAAJ;AAI9B,SAAAC,cACN,oBAAID,IAAJ;AAGF,SAAYE,eAAa,CAAA;AAGvBC,cACEnB,QAAQ,OAAOA,SAAS,YAAY,CAACoB,MAAMC,QAAQrB,IAAd,GACrC,oCAFO;AAOT,QAAIsB;AACJ,SAAKC,eAAe,IAAIC,QAAQ,CAACC,GAAGC,MAAOJ,SAASI,CAAhC;AACpB,SAAKC,aAAa,IAAIC,gBAAJ;AAClB,QAAIC,UAAU,MACZP,OAAO,IAAIX,qBAAqB,uBAAzB,CAAD;AACR,SAAKmB,sBAAsB,MACzB,KAAKH,WAAWI,OAAOC,oBAAoB,SAASH,OAApD;AACF,SAAKF,WAAWI,OAAOE,iBAAiB,SAASJ,OAAjD;AAEA,SAAK7B,OAAOkC,OAAOC,QAAQnC,IAAf,EAAqBoC,OAC/B,CAACC,KAAD,SAAA;AAAA,UAAM,CAACC,KAAKC,KAAN,IAAN;AAAA,aACEL,OAAOM,OAAOH,KAAK;QACjB,CAACC,GAAD,GAAO,KAAKG,aAAaH,KAAKC,KAAvB;MADU,CAAnB;OAGF,CAAA,CALU;AAQZ,QAAI,KAAKG,MAAM;AAEb,WAAKZ,oBAAL;IACD;AAED,SAAK7B,OAAOC;EACb;EAEOuC,aACNH,KACAC,OAAiC;AAEjC,QAAI,EAAEA,iBAAiBf,UAAU;AAC/B,aAAOe;IACR;AAED,SAAKrB,aAAayB,KAAKL,GAAvB;AACA,SAAKvB,eAAe6B,IAAIN,GAAxB;AAIA,QAAIO,UAA0BrB,QAAQsB,KAAK,CAACP,OAAO,KAAKhB,YAAb,CAAb,EAAyCwB,KACpE/C,UAAS,KAAKgD,SAASH,SAASP,KAAK,MAAMtC,IAAlC,GACTiD,WAAU,KAAKD,SAASH,SAASP,KAAKW,KAA5B,CAFiB;AAO9BJ,YAAQK,MAAM,MAAO;IAAA,CAArB;AAEAhB,WAAOiB,eAAeN,SAAS,YAAY;MAAEO,KAAK,MAAM;KAAxD;AACA,WAAOP;EACR;EAEOG,SACNH,SACAP,KACAW,OACAjD,MAAc;AAEd,QACE,KAAK2B,WAAWI,OAAOsB,WACvBJ,iBAAiBtC,sBACjB;AACA,WAAKmB,oBAAL;AACAI,aAAOiB,eAAeN,SAAS,UAAU;QAAEO,KAAK,MAAMH;OAAtD;AACA,aAAOzB,QAAQF,OAAO2B,KAAf;IACR;AAED,SAAKlC,eAAeuC,OAAOhB,GAA3B;AAEA,QAAI,KAAKI,MAAM;AAEb,WAAKZ,oBAAL;IACD;AAED,QAAImB,OAAO;AACTf,aAAOiB,eAAeN,SAAS,UAAU;QAAEO,KAAK,MAAMH;OAAtD;AACA,WAAKM,KAAK,OAAOjB,GAAjB;AACA,aAAOd,QAAQF,OAAO2B,KAAf;IACR;AAEDf,WAAOiB,eAAeN,SAAS,SAAS;MAAEO,KAAK,MAAMpD;KAArD;AACA,SAAKuD,KAAK,OAAOjB,GAAjB;AACA,WAAOtC;EACR;EAEOuD,KAAKF,SAAkBG,YAAmB;AAChD,SAAKvC,YAAYwC,QAASC,gBAAeA,WAAWL,SAASG,UAAV,CAAnD;EACD;EAEDG,UAAUC,IAAmD;AAC3D,SAAK3C,YAAY2B,IAAIgB,EAArB;AACA,WAAO,MAAM,KAAK3C,YAAYqC,OAAOM,EAAxB;EACd;EAEDC,SAAM;AACJ,SAAKlC,WAAWmC,MAAhB;AACA,SAAK/C,eAAe0C,QAAQ,CAACM,GAAGC,MAAM,KAAKjD,eAAeuC,OAAOU,CAA3B,CAAtC;AACA,SAAKT,KAAK,IAAV;EACD;EAEgB,MAAXU,YAAYlC,QAAmB;AACnC,QAAIsB,UAAU;AACd,QAAI,CAAC,KAAKX,MAAM;AACd,UAAIb,UAAU,MAAM,KAAKgC,OAAL;AACpB9B,aAAOE,iBAAiB,SAASJ,OAAjC;AACAwB,gBAAU,MAAM,IAAI7B,QAAS0C,aAAW;AACtC,aAAKP,UAAWN,CAAAA,aAAW;AACzBtB,iBAAOC,oBAAoB,SAASH,OAApC;AACA,cAAIwB,YAAW,KAAKX,MAAM;AACxBwB,oBAAQb,QAAD;UACR;SAJH;MAMD,CAPe;IAQjB;AACD,WAAOA;EACR;EAEO,IAAJX,OAAI;AACN,WAAO,KAAK3B,eAAeoD,SAAS;EACrC;EAEgB,IAAbC,gBAAa;AACfjD,cACE,KAAKnB,SAAS,QAAQ,KAAK0C,MAC3B,2DAFO;AAKT,WAAOR,OAAOC,QAAQ,KAAKnC,IAApB,EAA0BoC,OAC/B,CAACC,KAAD,UAAA;AAAA,UAAM,CAACC,KAAKC,KAAN,IAAN;AAAA,aACEL,OAAOM,OAAOH,KAAK;QACjB,CAACC,GAAD,GAAO+B,qBAAqB9B,KAAD;MADV,CAAnB;OAGF,CAAA,CALK;EAOR;EAEc,IAAX+B,cAAW;AACb,WAAOlD,MAAMmD,KAAK,KAAKxD,cAAhB;EACR;AA5JsB;AA+JzB,SAASyD,iBAAiBjC,OAAU;AAClC,SACEA,iBAAiBf,WAAYe,MAAyBkC,aAAa;AAEtE;AAED,SAASJ,qBAAqB9B,OAAU;AACtC,MAAI,CAACiC,iBAAiBjC,KAAD,GAAS;AAC5B,WAAOA;EACR;AAED,MAAIA,MAAMmC,QAAQ;AAChB,UAAMnC,MAAMmC;EACb;AACD,SAAOnC,MAAMoC;AACd;AAOM,IAAMC,QAAuB,SAAvBA,OAAwB5E,MAAMC,MAAa;AAAA,MAAbA,SAAa,QAAA;AAAbA,WAAO,CAAA;EAAM;AACtD,MAAIC,eAAe,OAAOD,SAAS,WAAW;IAAEE,QAAQF;EAAV,IAAmBA;AAEjE,SAAO,IAAIY,aAAab,MAAME,YAAvB;AACR;AAWM,IAAM2E,WAA6B,SAA7BA,UAA8BC,KAAK7E,MAAc;AAAA,MAAdA,SAAc,QAAA;AAAdA,WAAO;EAAO;AAC5D,MAAIC,eAAeD;AACnB,MAAI,OAAOC,iBAAiB,UAAU;AACpCA,mBAAe;MAAEC,QAAQD;;aAChB,OAAOA,aAAaC,WAAW,aAAa;AACrDD,iBAAaC,SAAS;EACvB;AAED,MAAIC,UAAU,IAAIC,QAAQH,aAAaE,OAAzB;AACdA,UAAQG,IAAI,YAAYuE,GAAxB;AAEA,SAAO,IAAItE,SAAS,MAAb,SAAA,CAAA,GACFN,cADE;IAELE;GAFF,CAAA;AAID;IAMY2E,sBAAa;EAOxBjE,YACEX,QACA6E,YACAhF,MACAiF,UAAgB;AAAA,QAAhBA,aAAgB,QAAA;AAAhBA,iBAAW;IAAK;AAEhB,SAAK9E,SAASA;AACd,SAAK6E,aAAaA,cAAc;AAChC,SAAKC,WAAWA;AAChB,QAAIjF,gBAAgBY,OAAO;AACzB,WAAKZ,OAAOA,KAAKkF,SAAL;AACZ,WAAKjC,QAAQjD;IACd,OAAM;AACL,WAAKA,OAAOA;IACb;EACF;AAtBuB;AA6BpB,SAAUmF,qBAAqBlC,OAAU;AAC7C,SACEA,SAAS,QACT,OAAOA,MAAM9C,WAAW,YACxB,OAAO8C,MAAM+B,eAAe,YAC5B,OAAO/B,MAAMgC,aAAa,aAC1B,UAAUhC;AAEb;ACn2BD,IAAMmC,0BAAgD,CACpD,QACA,OACA,SACA,QAJoD;AAMtD,IAAMC,uBAAuB,IAAIrE,IAC/BoE,uBAD2B;AAI7B,IAAME,yBAAuC,CAC3C,OACA,GAAGF,uBAFwC;AAI7C,IAAMG,sBAAsB,IAAIvE,IAAgBsE,sBAApB;AAE5B,IAAME,sBAAsB,oBAAIxE,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,GAArB,CAAR;AAC5B,IAAMyE,oCAAoC,oBAAIzE,IAAI,CAAC,KAAK,GAAN,CAAR;AAEnC,IAAM0E,kBAA4C;EACvDC,OAAO;EACPC,UAAUC;EACVC,YAAYD;EACZE,YAAYF;EACZG,aAAaH;EACbI,UAAUJ;AAN6C;AASlD,IAAMK,eAAsC;EACjDP,OAAO;EACP3F,MAAM6F;EACNC,YAAYD;EACZE,YAAYF;EACZG,aAAaH;EACbI,UAAUJ;AANuC;AAS5C,IAAMM,eAAiC;EAC5CR,OAAO;EACPS,SAASP;EACTQ,OAAOR;EACPD,UAAUC;AAJkC;AAO9C,IAAMS,qBAAqB;AAE3B,IAAMC,YACJ,OAAOC,WAAW,eAClB,OAAOA,OAAOC,aAAa,eAC3B,OAAOD,OAAOC,SAASC,kBAAkB;AAC3C,IAAMC,WAAW,CAACJ;AAElB,IAAMK,4BAAyDC,YAAW;EACxEC,kBAAkBC,QAAQF,MAAMC,gBAAP;AAD+C;AAapE,SAAUE,aAAa/G,MAAgB;AAC3CkB,YACElB,KAAKgH,OAAOC,SAAS,GACrB,2DAFO;AAKT,MAAIC;AACJ,MAAIlH,KAAKkH,oBAAoB;AAC3BA,IAAAA,sBAAqBlH,KAAKkH;EAC3B,WAAUlH,KAAKmH,qBAAqB;AAEnC,QAAIA,sBAAsBnH,KAAKmH;AAC/BD,IAAAA,sBAAsBN,YAAW;MAC/BC,kBAAkBM,oBAAoBP,KAAD;IADN;EAGlC,OAAM;AACLM,IAAAA,sBAAqBP;EACtB;AAGD,MAAIS,WAA0B,CAAA;AAE9B,MAAIC,aAAaC,0BACftH,KAAKgH,QACLE,qBACAtB,QACAwB,QAJwC;AAM1C,MAAIG;AACJ,MAAIC,WAAWxH,KAAKwH,YAAY;AAEhC,MAAIC,SAAM,SAAA;IACRC,wBAAwB;IACxBC,oBAAoB;EAFZ,GAGL3H,KAAKyH,MAHA;AAMV,MAAIG,kBAAuC;AAE3C,MAAI5G,cAAc,oBAAID,IAAJ;AAElB,MAAI8G,wBAAsD;AAE1D,MAAIC,0BAAkE;AAEtE,MAAIC,oBAAsD;AAO1D,MAAIC,wBAAwBhI,KAAKiI,iBAAiB;AAElD,MAAIC,iBAAiBC,YAAYd,YAAYrH,KAAKoI,QAAQzC,UAAU6B,QAApC;AAChC,MAAIa,gBAAkC;AAEtC,MAAIH,kBAAkB,MAAM;AAG1B,QAAIlF,QAAQsF,uBAAuB,KAAK;MACtC9I,UAAUQ,KAAKoI,QAAQzC,SAASnG;IADM,CAAN;AAGlC,QAAI;MAAE+I;MAAS3B;QAAU4B,uBAAuBnB,UAAD;AAC/Ca,qBAAiBK;AACjBF,oBAAgB;MAAE,CAACzB,MAAM6B,EAAP,GAAYzF;;EAC/B;AAED,MAAI0F;;;IAGF,CAACR,eAAeS,KAAMC,OAAMA,EAAEhC,MAAMiC,IAAnC;KAEA,CAACX,eAAeS,KAAMC,OAAMA,EAAEhC,MAAMkC,MAAnC,KAA8C9I,KAAKiI,iBAAiB;;AAExE,MAAIc;AACJ,MAAIrD,QAAqB;IACvBsD,eAAehJ,KAAKoI,QAAQa;IAC5BtD,UAAU3F,KAAKoI,QAAQzC;IACvB4C,SAASL;IACTQ;IACAQ,YAAYzD;;IAEZ0D,uBAAuBnJ,KAAKiI,iBAAiB,OAAO,QAAQ;IAC5DmB,oBAAoB;IACpBC,cAAc;IACdC,YAAatJ,KAAKiI,iBAAiBjI,KAAKiI,cAAcqB,cAAe,CAAA;IACrEC,YAAavJ,KAAKiI,iBAAiBjI,KAAKiI,cAAcsB,cAAe;IACrEC,QAASxJ,KAAKiI,iBAAiBjI,KAAKiI,cAAcuB,UAAWnB;IAC7DoB,UAAU,oBAAIC,IAAJ;IACVC,UAAU,oBAAID,IAAJ;EAda;AAmBzB,MAAIE,gBAA+BC,OAAcC;AAIjD,MAAIC,4BAA4B;AAGhC,MAAIC;AAIJ,MAAIC,8BAA8B;AAMlC,MAAIC,yBAAyB;AAI7B,MAAIC,0BAAoC,CAAA;AAIxC,MAAIC,wBAAkC,CAAA;AAGtC,MAAIC,mBAAmB,oBAAIX,IAAJ;AAGvB,MAAIY,qBAAqB;AAKzB,MAAIC,0BAA0B;AAG9B,MAAIC,iBAAiB,oBAAId,IAAJ;AAGrB,MAAIe,mBAAmB,oBAAI1J,IAAJ;AAGvB,MAAI2J,mBAAmB,oBAAIhB,IAAJ;AAMvB,MAAIiB,kBAAkB,oBAAIjB,IAAJ;AAItB,MAAIkB,mBAAmB,oBAAIlB,IAAJ;AAIvB,MAAImB,0BAA0B;AAK9B,WAASC,aAAU;AAGjBlD,sBAAkB5H,KAAKoI,QAAQ2C,OAC7B,UAA+C;AAAA,UAA9C;QAAE9B,QAAQD;QAAerD;QAAUqF;UAAW;AAG7C,UAAIH,yBAAyB;AAC3BA,kCAA0B;AAC1B;MACD;AAEDI,cACEL,iBAAiB1G,SAAS,KAAK8G,SAAS,MACxC,4YAFK;AAUP,UAAIE,aAAaC,sBAAsB;QACrCC,iBAAiB1F,MAAMC;QACvB0F,cAAc1F;QACdqD;MAHqC,CAAD;AAMtC,UAAIkC,cAAcF,SAAS,MAAM;AAE/BH,kCAA0B;AAC1B7K,aAAKoI,QAAQkD,GAAGN,QAAQ,EAAxB;AAGAO,sBAAcL,YAAY;UACxBxF,OAAO;UACPC;UACAQ,UAAO;AACLoF,0BAAcL,YAAa;cACzBxF,OAAO;cACPS,SAASP;cACTQ,OAAOR;cACPD;aAJW;AAOb3F,iBAAKoI,QAAQkD,GAAGN,KAAhB;;UAEF5E,QAAK;AACHoF,0BAAcN,UAAD;AACbO,wBAAY;cAAE9B,UAAU,IAAID,IAAIX,OAAOrD,MAAMiE,QAArB;YAAZ,CAAD;UACZ;QAhBuB,CAAb;AAkBb;MACD;AAED,aAAO+B,gBAAgB1C,eAAerD,QAAhB;KApDR;AA6DlB,QAAI,CAACD,MAAMgD,aAAa;AACtBgD,sBAAgB7B,OAAcC,KAAKpE,MAAMC,QAA1B;IAChB;AAED,WAAOoD;EACR;AAGD,WAAS4C,UAAO;AACd,QAAI/D,iBAAiB;AACnBA,sBAAe;IAChB;AACD5G,gBAAY4K,MAAZ;AACA5B,mCAA+BA,4BAA4BnG,MAA5B;AAC/B6B,UAAM+D,SAASjG,QAAQ,CAAChC,GAAGa,QAAQwJ,cAAcxJ,GAAD,CAAhD;AACAqD,UAAMiE,SAASnG,QAAQ,CAAChC,GAAGa,QAAQmJ,cAAcnJ,GAAD,CAAhD;EACD;AAGD,WAASqB,UAAUC,IAAoB;AACrC3C,gBAAY2B,IAAIgB,EAAhB;AACA,WAAO,MAAM3C,YAAYqC,OAAOM,EAAnB;EACd;AAGD,WAAS8H,YAAYK,UAA8B;AACjDpG,YACKA,SAAAA,CAAAA,GAAAA,OACAoG,QAFA;AAIL9K,gBAAYwC,QAASC,gBAAeA,WAAWiC,KAAD,CAA9C;EACD;AAOD,WAASqG,mBACPpG,UACAmG,UAA0E;AAAA,QAAA,iBAAA;AAO1E,QAAIE,iBACFtG,MAAM6D,cAAc,QACpB7D,MAAMwD,WAAWrD,cAAc,QAC/BoG,iBAAiBvG,MAAMwD,WAAWrD,UAAlB,KAChBH,MAAMwD,WAAWxD,UAAU,eAC3B,kBAAAC,SAASD,UAAOwG,OAAAA,SAAAA,gBAAAA,iBAAgB;AAElC,QAAI3C;AACJ,QAAIuC,SAASvC,YAAY;AACvB,UAAItH,OAAOkK,KAAKL,SAASvC,UAArB,EAAiCtC,SAAS,GAAG;AAC/CsC,qBAAauC,SAASvC;MACvB,OAAM;AAELA,qBAAa;MACd;eACQyC,gBAAgB;AAEzBzC,mBAAa7D,MAAM6D;IACpB,OAAM;AAELA,mBAAa;IACd;AAGD,QAAID,aAAawC,SAASxC,aACtB8C,gBACE1G,MAAM4D,YACNwC,SAASxC,YACTwC,SAASvD,WAAW,CAAA,GACpBuD,SAAStC,MAJI,IAMf9D,MAAM4D;AAIV,aAAS,CAACjH,GAAD,KAASuI,kBAAkB;AAClCY,oBAAcnJ,GAAD;IACd;AAID,QAAI+G,qBACFW,8BAA8B,QAC7BrE,MAAMwD,WAAWrD,cAAc,QAC9BoG,iBAAiBvG,MAAMwD,WAAWrD,UAAlB,OAChBF,mBAAAA,SAASD,UAAOwG,OAAAA,SAAAA,iBAAAA,iBAAgB;AAEpC,QAAI3E,oBAAoB;AACtBF,mBAAaE;AACbA,2BAAqB3B;IACtB;AAED6F,gBAAW,SAAA,CAAA,GACNK,UADM;MAETvC;MACAD;MACAN,eAAeY;MACfjE;MACA+C,aAAa;MACbQ,YAAYzD;MACZ4D,cAAc;MACdF,uBAAuBkD,uBACrB1G,UACAmG,SAASvD,WAAW7C,MAAM6C,OAFiB;MAI7Ca;MACAO,UAAU,IAAID,IAAIhE,MAAMiE,QAAd;KAdZ,CAAA;AAiBA,QAAIM;AAA6B;aAEtBL,kBAAkBC,OAAcC;AAAK;aAErCF,kBAAkBC,OAAcyC,MAAM;AAC/CtM,WAAKoI,QAAQ1F,KAAKiD,UAAUA,SAASD,KAArC;IACD,WAAUkE,kBAAkBC,OAAc0C,SAAS;AAClDvM,WAAKoI,QAAQ9I,QAAQqG,UAAUA,SAASD,KAAxC;IACD;AAGDkE,oBAAgBC,OAAcC;AAC9BC,gCAA4B;AAC5BE,kCAA8B;AAC9BC,6BAAyB;AACzBC,8BAA0B,CAAA;AAC1BC,4BAAwB,CAAA;EACzB;AAID,iBAAeoC,SACbC,IACAC,MAA4B;AAE5B,QAAI,OAAOD,OAAO,UAAU;AAC1BzM,WAAKoI,QAAQkD,GAAGmB,EAAhB;AACA;IACD;AAED,QAAIE,iBAAiBC,YACnBlH,MAAMC,UACND,MAAM6C,SACNf,UACAC,OAAOE,oBACP8E,IACAC,QAN8B,OAAA,SAM9BA,KAAMG,aACNH,QAP8B,OAAA,SAO9BA,KAAMI,QAPwB;AAShC,QAAI;MAAEC;MAAMC;MAAYhK;QAAUiK,yBAChCxF,OAAOC,wBACP,OACAiF,gBACAD,IAJwD;AAO1D,QAAItB,kBAAkB1F,MAAMC;AAC5B,QAAI0F,eAAe6B,eAAexH,MAAMC,UAAUoH,MAAML,QAAQA,KAAKhH,KAApC;AAOjC2F,mBAAY,SAAA,CAAA,GACPA,cACArL,KAAKoI,QAAQ+E,eAAe9B,YAA5B,CAFO;AAKZ,QAAI+B,cAAcV,QAAQA,KAAKpN,WAAW,OAAOoN,KAAKpN,UAAUsG;AAEhE,QAAIoD,gBAAgBa,OAAcyC;AAElC,QAAIc,gBAAgB,MAAM;AACxBpE,sBAAgBa,OAAc0C;IAC/B,WAAUa,gBAAgB;AAAO;aAGhCJ,cAAc,QACdf,iBAAiBe,WAAWnH,UAAZ,KAChBmH,WAAWlH,eAAeJ,MAAMC,SAASnG,WAAWkG,MAAMC,SAASjG,QACnE;AAKAsJ,sBAAgBa,OAAc0C;IAC/B;AAED,QAAInD,qBACFsD,QAAQ,wBAAwBA,OAC5BA,KAAKtD,uBAAuB,OAC5BxD;AAEN,QAAIsF,aAAaC,sBAAsB;MACrCC;MACAC;MACArC;IAHqC,CAAD;AAKtC,QAAIkC,YAAY;AAEdK,oBAAcL,YAAY;QACxBxF,OAAO;QACPC,UAAU0F;QACVlF,UAAO;AACLoF,wBAAcL,YAAa;YACzBxF,OAAO;YACPS,SAASP;YACTQ,OAAOR;YACPD,UAAU0F;WAJC;AAObmB,mBAASC,IAAIC,IAAL;;QAEVtG,QAAK;AACHoF,wBAAcN,UAAD;AACbO,sBAAY;YAAE9B,UAAU,IAAID,IAAIhE,MAAMiE,QAAd;UAAZ,CAAD;QACZ;MAhBuB,CAAb;AAkBb;IACD;AAED,WAAO,MAAM+B,gBAAgB1C,eAAeqC,cAAc;MACxD2B;;;MAGAK,cAAcrK;MACdoG;MACA9J,SAASoN,QAAQA,KAAKpN;IANkC,CAA9B;EAQ7B;AAKD,WAASgO,aAAU;AACjBC,yBAAoB;AACpB9B,gBAAY;MAAEpC,cAAc;KAAjB;AAIX,QAAI3D,MAAMwD,WAAWxD,UAAU,cAAc;AAC3C;IACD;AAKD,QAAIA,MAAMwD,WAAWxD,UAAU,QAAQ;AACrCgG,sBAAgBhG,MAAMsD,eAAetD,MAAMC,UAAU;QACnD6H,gCAAgC;MADmB,CAAtC;AAGf;IACD;AAKD9B,oBACE9B,iBAAiBlE,MAAMsD,eACvBtD,MAAMwD,WAAWvD,UACjB;MAAE8H,oBAAoB/H,MAAMwD;IAA5B,CAHa;EAKhB;AAKD,iBAAewC,gBACb1C,eACArD,UACA+G,MAQC;AAKD1C,mCAA+BA,4BAA4BnG,MAA5B;AAC/BmG,kCAA8B;AAC9BJ,oBAAgBZ;AAChBiB,mCACGyC,QAAQA,KAAKc,oCAAoC;AAIpDE,uBAAmBhI,MAAMC,UAAUD,MAAM6C,OAAvB;AAClBwB,iCAA6B2C,QAAQA,KAAKtD,wBAAwB;AAElE,QAAIuE,cAAcpG,sBAAsBF;AACxC,QAAIuG,oBAAoBlB,QAAQA,KAAKe;AACrC,QAAIlF,UAAUJ,YAAYwF,aAAahI,UAAU6B,QAAxB;AAGzB,QAAI,CAACe,SAAS;AACZ,UAAIvF,QAAQsF,uBAAuB,KAAK;QAAE9I,UAAUmG,SAASnG;MAArB,CAAN;AAClC,UAAI;QAAE+I,SAASsF;QAAiBjH;MAA5B,IACF4B,uBAAuBmF,WAAD;AAExBG,4BAAqB;AACrB/B,yBAAmBpG,UAAU;QAC3B4C,SAASsF;QACTvE,YAAY,CAAA;QACZE,QAAQ;UACN,CAAC5C,MAAM6B,EAAP,GAAYzF;QADN;MAHmB,CAAX;AAOlB;IACD;AAOD,QACE0C,MAAMgD,eACNqF,iBAAiBrI,MAAMC,UAAUA,QAAjB,KAChB,EAAE+G,QAAQA,KAAKM,cAAcf,iBAAiBS,KAAKM,WAAWnH,UAAjB,IAC7C;AACAkG,yBAAmBpG,UAAU;QAAE4C;MAAF,CAAX;AAClB;IACD;AAGDyB,kCAA8B,IAAIrI,gBAAJ;AAC9B,QAAIqM,UAAUC,wBACZjO,KAAKoI,SACLzC,UACAqE,4BAA4BlI,QAC5B4K,QAAQA,KAAKM,UAJsB;AAMrC,QAAIkB;AACJ,QAAIb;AAEJ,QAAIX,QAAQA,KAAKW,cAAc;AAK7BA,qBAAe;QACb,CAACc,oBAAoB5F,OAAD,EAAU3B,MAAM6B,EAApC,GAAyCiE,KAAKW;;IAEjD,WACCX,QACAA,KAAKM,cACLf,iBAAiBS,KAAKM,WAAWnH,UAAjB,GAChB;AAEA,UAAIuI,eAAe,MAAMC,aACvBL,SACArI,UACA+G,KAAKM,YACLzE,SACA;QAAEjJ,SAASoN,KAAKpN;MAAhB,CALmC;AAQrC,UAAI8O,aAAaE,gBAAgB;AAC/B;MACD;AAEDJ,0BAAoBE,aAAaF;AACjCb,qBAAee,aAAaG;AAE5B,UAAIrF,aAAU,SAAA;QACZxD,OAAO;QACPC;SACG+G,KAAKM,UAHI;AAKdY,0BAAoB1E;AAGpB8E,gBAAU,IAAIQ,QAAQR,QAAQnJ,KAAK;QAAE/C,QAAQkM,QAAQlM;MAAlB,CAAzB;IACX;AAGD,QAAI;MAAEwM;MAAgBhF;MAAYE;IAA9B,IAAyC,MAAMiF,cACjDT,SACArI,UACA4C,SACAqF,mBACAlB,QAAQA,KAAKM,YACbN,QAAQA,KAAKgC,mBACbhC,QAAQA,KAAKpN,SACb4O,mBACAb,YAT8D;AAYhE,QAAIiB,gBAAgB;AAClB;IACD;AAKDtE,kCAA8B;AAE9B+B,uBAAmBpG,UAAD,SAAA;MAChB4C;IADgB,GAEZ2F,oBAAoB;MAAE3E,YAAY2E;IAAd,IAAoC,CAAA,GAF5C;MAGhB5E;MACAE;KAJF,CAAA;EAMD;AAID,iBAAe6E,aACbL,SACArI,UACAqH,YACAzE,SACAmE,MAA4B;AAE5Ba,yBAAoB;AAGpB,QAAIrE,aAAU,SAAA;MACZxD,OAAO;MACPC;IAFY,GAGTqH,UAHS;AAKdvB,gBAAY;MAAEvC;KAAH;AAGX,QAAIyF;AACJ,QAAIC,cAAcC,eAAetG,SAAS5C,QAAV;AAEhC,QAAI,CAACiJ,YAAYhI,MAAMqC,UAAU,CAAC2F,YAAYhI,MAAMiC,MAAM;AACxD8F,eAAS;QACPG,MAAMC,WAAW/L;QACjBA,OAAOsF,uBAAuB,KAAK;UACjC0G,QAAQhB,QAAQgB;UAChBxP,UAAUmG,SAASnG;UACnByP,SAASL,YAAYhI,MAAM6B;SAHA;;IAMhC,OAAM;AACLkG,eAAS,MAAMO,mBACb,UACAlB,SACAY,aACArG,SACAnB,UACAF,qBACAM,QAP+B;AAUjC,UAAIwG,QAAQlM,OAAOsB,SAAS;AAC1B,eAAO;UAAEkL,gBAAgB;;MAC1B;IACF;AAED,QAAIa,iBAAiBR,MAAD,GAAU;AAC5B,UAAIrP;AACJ,UAAIoN,QAAQA,KAAKpN,WAAW,MAAM;AAChCA,kBAAUoN,KAAKpN;MAChB,OAAM;AAILA,kBACEqP,OAAOhJ,aAAaD,MAAMC,SAASnG,WAAWkG,MAAMC,SAASjG;MAChE;AACD,YAAM0P,wBAAwB1J,OAAOiJ,QAAQ;QAAE3B;QAAY1N;MAAd,CAAhB;AAC7B,aAAO;QAAEgP,gBAAgB;;IAC1B;AAED,QAAIe,cAAcV,MAAD,GAAU;AAGzB,UAAIW,gBAAgBnB,oBAAoB5F,SAASqG,YAAYhI,MAAM6B,EAA5B;AAMvC,WAAKiE,QAAQA,KAAKpN,aAAa,MAAM;AACnCsK,wBAAgBC,OAAcyC;MAC/B;AAED,aAAO;;QAEL4B,mBAAmB,CAAA;QACnBK,oBAAoB;UAAE,CAACe,cAAc1I,MAAM6B,EAArB,GAA0BkG,OAAO3L;QAAnC;;IAEvB;AAED,QAAIuM,iBAAiBZ,MAAD,GAAU;AAC5B,YAAMrG,uBAAuB,KAAK;QAAEwG,MAAM;MAAR,CAAN;IAC7B;AAED,WAAO;MACLZ,mBAAmB;QAAE,CAACU,YAAYhI,MAAM6B,EAAnB,GAAwBkG,OAAO5O;MAAjC;;EAEtB;AAID,iBAAe0O,cACbT,SACArI,UACA4C,SACAkF,oBACAT,YACA0B,mBACApP,SACA4O,mBACAb,cAAwB;AAGxB,QAAIO,oBAAoBH;AACxB,QAAI,CAACG,mBAAmB;AACtB,UAAI1E,aAAU,SAAA;QACZxD,OAAO;QACPC;QACAE,YAAYD;QACZE,YAAYF;QACZG,aAAaH;QACbI,UAAUJ;MANE,GAOToH,UAPS;AASdY,0BAAoB1E;IACrB;AAID,QAAIsG,mBACFxC,cAAc0B,oBACV1B,cAAc0B,oBACdd,kBAAkB/H,cAClB+H,kBAAkB9H,cAClB8H,kBAAkB5H,YAClB4H,kBAAkB7H,cAClB;MACEF,YAAY+H,kBAAkB/H;MAC9BC,YAAY8H,kBAAkB9H;MAC9BE,UAAU4H,kBAAkB5H;MAC5BD,aAAa6H,kBAAkB7H;IAJjC,IAMAH;AAEN,QAAI+H,cAAcpG,sBAAsBF;AACxC,QAAI,CAACoI,eAAeC,oBAAhB,IAAwCC,iBAC1C3P,KAAKoI,SACL1C,OACA6C,SACAiH,kBACA7J,UACAuE,wBACAC,yBACAC,uBACAM,kBACAiD,aACAnG,UACA0G,mBACAb,YAb0D;AAmB5DS,0BACGmB,aACC,EAAE1G,WAAWA,QAAQI,KAAMC,OAAMA,EAAEhC,MAAM6B,OAAOwG,OAAnC,MACZQ,iBAAiBA,cAAc9G,KAAMC,OAAMA,EAAEhC,MAAM6B,OAAOwG,OAAzC,CAHD;AAOrB,QAAIQ,cAAcxI,WAAW,KAAKyI,qBAAqBzI,WAAW,GAAG;AACnE,UAAI2I,mBAAkBC,uBAAsB;AAC5C9D,yBAAmBpG,UAAD,SAAA;QAChB4C;QACAe,YAAY,CAAA;;QAEZE,QAAQ6D,gBAAgB;MAJR,GAKZa,oBAAoB;QAAE3E,YAAY2E;MAAd,IAAoC,CAAA,GACxD0B,mBAAkB;QAAEnG,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;UAA4B,CAAA,CAN9C,CAAlB;AAQA,aAAO;QAAE6E,gBAAgB;;IAC1B;AAMD,QAAI,CAACrE,6BAA6B;AAChCyF,2BAAqBlM,QAASsM,QAAM;AAClC,YAAIC,UAAUrK,MAAM+D,SAAStG,IAAI2M,GAAGzN,GAAtB;AACd,YAAI2N,sBAAgD;UAClDtK,OAAO;UACP3F,MAAMgQ,WAAWA,QAAQhQ;UACzB8F,YAAYD;UACZE,YAAYF;UACZG,aAAaH;UACbI,UAAUJ;UACV,6BAA6B;;AAE/BF,cAAM+D,SAASnJ,IAAIwP,GAAGzN,KAAK2N,mBAA3B;OAXF;AAaA,UAAIzG,aAAa2E,qBAAqBxI,MAAM6D;AAC5CkC,kBAAW,SAAA;QACTvC,YAAY0E;SACRrE,aACAtH,OAAOkK,KAAK5C,UAAZ,EAAwBtC,WAAW,IACjC;QAAEsC,YAAY;MAAd,IACA;QAAEA;UACJ,CAAA,GACAmG,qBAAqBzI,SAAS,IAC9B;QAAEwC,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;UACZ,CAAA,CATK,CAAX;IAWD;AAEDc,8BAA0B,EAAED;AAC5BoF,yBAAqBlM,QAASsM,QAAM;AAClC,UAAIA,GAAGpO,YAAY;AAIjB2I,yBAAiB/J,IAAIwP,GAAGzN,KAAKyN,GAAGpO,UAAhC;MACD;IACF,CAPD;AAUA,QAAIuO,iCAAiC,MACnCP,qBAAqBlM,QAAS0M,OAAMC,aAAaD,EAAE7N,GAAH,CAAhD;AACF,QAAI2H,6BAA6B;AAC/BA,kCAA4BlI,OAAOE,iBACjC,SACAiO,8BAFF;IAID;AAED,QAAI;MAAEG;MAASC;MAAeC;IAA1B,IACF,MAAMC,+BACJ7K,MAAM6C,SACNA,SACAkH,eACAC,sBACA1B,OALkC;AAQtC,QAAIA,QAAQlM,OAAOsB,SAAS;AAC1B,aAAO;QAAEkL,gBAAgB;;IAC1B;AAKD,QAAItE,6BAA6B;AAC/BA,kCAA4BlI,OAAOC,oBACjC,SACAkO,8BAFF;IAID;AACDP,yBAAqBlM,QAASsM,QAAOzF,iBAAiBhH,OAAOyM,GAAGzN,GAA3B,CAArC;AAGA,QAAIuC,YAAW4L,aAAaJ,OAAD;AAC3B,QAAIxL,WAAU;AACZ,YAAMwK,wBAAwB1J,OAAOd,WAAU;QAAEtF;MAAF,CAAlB;AAC7B,aAAO;QAAEgP,gBAAgB;;IAC1B;AAGD,QAAI;MAAEhF;MAAYE;IAAd,IAAyBiH,kBAC3B/K,OACA6C,SACAkH,eACAY,eACAhD,cACAqC,sBACAY,gBACA3F,eAR4C;AAY9CA,oBAAgBnH,QAAQ,CAACkN,cAAczB,YAAW;AAChDyB,mBAAahN,UAAWN,aAAW;AAIjC,YAAIA,WAAWsN,aAAajO,MAAM;AAChCkI,0BAAgBtH,OAAO4L,OAAvB;QACD;OANH;KADF;AAWA,QAAIW,kBAAkBC,uBAAsB;AAC5C,QAAIc,qBAAqBC,qBAAqBrG,uBAAD;AAC7C,QAAIsG,uBACFjB,mBAAmBe,sBAAsBjB,qBAAqBzI,SAAS;AAEzE,WAAA,SAAA;MACEqC;MACAE;IAFF,GAGMqH,uBAAuB;MAAEpH,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;IAAZ,IAAwC,CAAA,CAHrE;EAKD;AAED,WAASqH,WAAwBzO,KAAW;AAC1C,WAAOqD,MAAM+D,SAAStG,IAAId,GAAnB,KAA2B4D;EACnC;AAGD,WAAS8K,MACP1O,KACA4M,SACA+B,MACAtE,MAAyB;AAEzB,QAAIhG,UAAU;AACZ,YAAM,IAAI/F,MACR,kMADI;IAKP;AAED,QAAI0J,iBAAiBhK,IAAIgC,GAArB;AAA2B8N,mBAAa9N,GAAD;AAE3C,QAAIsL,cAAcpG,sBAAsBF;AACxC,QAAIsF,iBAAiBC,YACnBlH,MAAMC,UACND,MAAM6C,SACNf,UACAC,OAAOE,oBACPqJ,MACA/B,SACAvC,QAAAA,OAAAA,SAAAA,KAAMI,QAPwB;AAShC,QAAIvE,UAAUJ,YAAYwF,aAAahB,gBAAgBnF,QAA9B;AAEzB,QAAI,CAACe,SAAS;AACZ0I,sBACE5O,KACA4M,SACA3G,uBAAuB,KAAK;QAAE9I,UAAUmN;MAAZ,CAAN,CAHT;AAKf;IACD;AAED,QAAI;MAAEI;MAAMC;QAAeC,yBACzBxF,OAAOC,wBACP,MACAiF,gBACAD,IAJiD;AAMnD,QAAIwE,QAAQrC,eAAetG,SAASwE,IAAV;AAE1BhD,iCAA6B2C,QAAQA,KAAKtD,wBAAwB;AAElE,QAAI4D,cAAcf,iBAAiBe,WAAWnH,UAAZ,GAAyB;AACzDsL,0BAAoB9O,KAAK4M,SAASlC,MAAMmE,OAAO3I,SAASyE,UAArC;AACnB;IACD;AAIDtC,qBAAiBpK,IAAI+B,KAAK;MAAE4M;MAASlC;KAArC;AACAqE,wBAAoB/O,KAAK4M,SAASlC,MAAMmE,OAAO3I,SAASyE,UAArC;EACpB;AAID,iBAAemE,oBACb9O,KACA4M,SACAlC,MACAmE,OACAG,gBACArE,YAAsB;AAEtBO,yBAAoB;AACpB7C,qBAAiBrH,OAAOhB,GAAxB;AAEA,QAAI,CAAC6O,MAAMtK,MAAMqC,UAAU,CAACiI,MAAMtK,MAAMiC,MAAM;AAC5C,UAAI7F,QAAQsF,uBAAuB,KAAK;QACtC0G,QAAQhC,WAAWnH;QACnBrG,UAAUuN;QACVkC;MAHsC,CAAN;AAKlCgC,sBAAgB5O,KAAK4M,SAASjM,KAAf;AACf;IACD;AAGD,QAAIsO,kBAAkB5L,MAAM+D,SAAStG,IAAId,GAAnB;AACtB,QAAI0N,UAAO,SAAA;MACTrK,OAAO;IADE,GAENsH,YAFM;MAGTjN,MAAMuR,mBAAmBA,gBAAgBvR;MACzC,6BAA6B;KAJ/B;AAMA2F,UAAM+D,SAASnJ,IAAI+B,KAAK0N,OAAxB;AACAtE,gBAAY;MAAEhC,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;KAAb;AAGX,QAAI8H,kBAAkB,IAAI5P,gBAAJ;AACtB,QAAI6P,eAAevD,wBACjBjO,KAAKoI,SACL2E,MACAwE,gBAAgBzP,QAChBkL,UAJwC;AAM1C3C,qBAAiB/J,IAAI+B,KAAKkP,eAA1B;AAEA,QAAIE,eAAe,MAAMvC,mBACvB,UACAsC,cACAN,OACAG,gBACAjK,UACAF,qBACAM,QAPyC;AAU3C,QAAIgK,aAAa1P,OAAOsB,SAAS;AAG/B,UAAIiH,iBAAiBlH,IAAId,GAArB,MAA8BkP,iBAAiB;AACjDlH,yBAAiBhH,OAAOhB,GAAxB;MACD;AACD;IACD;AAED,QAAI8M,iBAAiBsC,YAAD,GAAgB;AAClCpH,uBAAiBhH,OAAOhB,GAAxB;AACAoI,uBAAiB9H,IAAIN,GAArB;AACA,UAAIqP,iBAAc,SAAA;QAChBhM,OAAO;MADS,GAEbsH,YAFa;QAGhBjN,MAAM6F;QACN,6BAA6B;OAJ/B;AAMAF,YAAM+D,SAASnJ,IAAI+B,KAAKqP,cAAxB;AACAjG,kBAAY;QAAEhC,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;MAAZ,CAAD;AAEX,aAAO2F,wBAAwB1J,OAAO+L,cAAc;QAClDzE;QACA2E,uBAAuB;MAF2B,CAAtB;IAI/B;AAGD,QAAItC,cAAcoC,YAAD,GAAgB;AAC/BR,sBAAgB5O,KAAK4M,SAASwC,aAAazO,KAA5B;AACf;IACD;AAED,QAAIuM,iBAAiBkC,YAAD,GAAgB;AAClC,YAAMnJ,uBAAuB,KAAK;QAAEwG,MAAM;MAAR,CAAN;IAC7B;AAID,QAAIzD,eAAe3F,MAAMwD,WAAWvD,YAAYD,MAAMC;AACtD,QAAIiM,sBAAsB3D,wBACxBjO,KAAKoI,SAELiD,cACAkG,gBAAgBzP,MAJ+B;AAMjD,QAAI6L,cAAcpG,sBAAsBF;AACxC,QAAIkB,UACF7C,MAAMwD,WAAWxD,UAAU,SACvByC,YAAYwF,aAAajI,MAAMwD,WAAWvD,UAAU6B,QAAzC,IACX9B,MAAM6C;AAEZrH,cAAUqH,SAAS,8CAAV;AAET,QAAIsJ,SAAS,EAAEvH;AACfE,mBAAelK,IAAI+B,KAAKwP,MAAxB;AAEA,QAAIC,cAAW,SAAA;MACbpM,OAAO;MACP3F,MAAM0R,aAAa1R;IAFN,GAGViN,YAHU;MAIb,6BAA6B;KAJ/B;AAMAtH,UAAM+D,SAASnJ,IAAI+B,KAAKyP,WAAxB;AAEA,QAAI,CAACrC,eAAeC,oBAAhB,IAAwCC;MAC1C3P,KAAKoI;MACL1C;MACA6C;MACAyE;MACA3B;MACAnB;MACAC;MACAC;MACAM;MACAiD;MACAnG;MACA;QAAE,CAAC0J,MAAMtK,MAAM6B,EAAb,GAAkBgJ,aAAa1R;;MACjC6F;;IAb0D;AAmB5D8J,yBACGqC,OAAQjC,QAAOA,GAAGzN,QAAQA,GAD7B,EAEGmB,QAASsM,QAAM;AACd,UAAIkC,WAAWlC,GAAGzN;AAClB,UAAIiP,mBAAkB5L,MAAM+D,SAAStG,IAAI6O,QAAnB;AACtB,UAAIhC,sBAAgD;QAClDtK,OAAO;QACP3F,MAAMuR,oBAAmBA,iBAAgBvR;QACzC8F,YAAYD;QACZE,YAAYF;QACZG,aAAaH;QACbI,UAAUJ;QACV,6BAA6B;;AAE/BF,YAAM+D,SAASnJ,IAAI0R,UAAUhC,mBAA7B;AACA,UAAIF,GAAGpO,YAAY;AACjB2I,yBAAiB/J,IAAI0R,UAAUlC,GAAGpO,UAAlC;MACD;KAjBL;AAoBA+J,gBAAY;MAAEhC,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;IAAZ,CAAD;AAEX,QAAIwG,iCAAiC,MACnCP,qBAAqBlM,QAASsM,QAAOK,aAAaL,GAAGzN,GAAJ,CAAjD;AAEFkP,oBAAgBzP,OAAOE,iBACrB,SACAiO,8BAFF;AAKA,QAAI;MAAEG;MAASC;MAAeC;IAA1B,IACF,MAAMC,+BACJ7K,MAAM6C,SACNA,SACAkH,eACAC,sBACAkC,mBALkC;AAQtC,QAAIL,gBAAgBzP,OAAOsB,SAAS;AAClC;IACD;AAEDmO,oBAAgBzP,OAAOC,oBACrB,SACAkO,8BAFF;AAKAzF,mBAAenH,OAAOhB,GAAtB;AACAgI,qBAAiBhH,OAAOhB,GAAxB;AACAqN,yBAAqBlM,QAAS/B,OAAM4I,iBAAiBhH,OAAO5B,EAAEY,GAA1B,CAApC;AAEA,QAAIuC,YAAW4L,aAAaJ,OAAD;AAC3B,QAAIxL,WAAU;AACZ,aAAOwK,wBAAwB1J,OAAOd,SAAR;IAC/B;AAGD,QAAI;MAAE0E;MAAYE;IAAd,IAAyBiH,kBAC3B/K,OACAA,MAAM6C,SACNkH,eACAY,eACAzK,QACA8J,sBACAY,gBACA3F,eAR4C;AAW9C,QAAIsH,cAAqC;MACvCvM,OAAO;MACP3F,MAAM0R,aAAa1R;MACnB8F,YAAYD;MACZE,YAAYF;MACZG,aAAaH;MACbI,UAAUJ;MACV,6BAA6B;;AAE/BF,UAAM+D,SAASnJ,IAAI+B,KAAK4P,WAAxB;AAEA,QAAItB,qBAAqBC,qBAAqBiB,MAAD;AAK7C,QACEnM,MAAMwD,WAAWxD,UAAU,aAC3BmM,SAAStH,yBACT;AACArJ,gBAAU0I,eAAe,yBAAhB;AACTI,qCAA+BA,4BAA4BnG,MAA5B;AAE/BkI,yBAAmBrG,MAAMwD,WAAWvD,UAAU;QAC5C4C;QACAe;QACAE;QACAC,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;MAJkC,CAA5B;IAMnB,OAAM;AAILgC,kBAAW,SAAA;QACTjC;QACAF,YAAY8C,gBACV1G,MAAM4D,YACNA,YACAf,SACAiB,MAJyB;MAFlB,GAQLmH,qBAAqB;QAAElH,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;UAA4B,CAAA,CARxD,CAAX;AAUAS,+BAAyB;IAC1B;EACF;AAGD,iBAAekH,oBACb/O,KACA4M,SACAlC,MACAmE,OACA3I,SACAyE,YAAuB;AAEvB,QAAIsE,kBAAkB5L,MAAM+D,SAAStG,IAAId,GAAnB;AAEtB,QAAIqP,iBAAc,SAAA;MAChBhM,OAAO;MACPG,YAAYD;MACZE,YAAYF;MACZG,aAAaH;MACbI,UAAUJ;IALM,GAMboH,YANa;MAOhBjN,MAAMuR,mBAAmBA,gBAAgBvR;MACzC,6BAA6B;KAR/B;AAUA2F,UAAM+D,SAASnJ,IAAI+B,KAAKqP,cAAxB;AACAjG,gBAAY;MAAEhC,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;KAAb;AAGX,QAAI8H,kBAAkB,IAAI5P,gBAAJ;AACtB,QAAI6P,eAAevD,wBACjBjO,KAAKoI,SACL2E,MACAwE,gBAAgBzP,MAHwB;AAK1CuI,qBAAiB/J,IAAI+B,KAAKkP,eAA1B;AAEA,QAAI5C,SAAqB,MAAMO,mBAC7B,UACAsC,cACAN,OACA3I,SACAnB,UACAF,qBACAM,QAP+C;AAcjD,QAAI+H,iBAAiBZ,MAAD,GAAU;AAC5BA,eACG,MAAMuD,oBAAoBvD,QAAQ6C,aAAa1P,QAAQ,IAA9B,KAC1B6M;IACH;AAID,QAAItE,iBAAiBlH,IAAId,GAArB,MAA8BkP,iBAAiB;AACjDlH,uBAAiBhH,OAAOhB,GAAxB;IACD;AAED,QAAImP,aAAa1P,OAAOsB,SAAS;AAC/B;IACD;AAGD,QAAI+L,iBAAiBR,MAAD,GAAU;AAC5BlE,uBAAiB9H,IAAIN,GAArB;AACA,YAAM+M,wBAAwB1J,OAAOiJ,MAAR;AAC7B;IACD;AAGD,QAAIU,cAAcV,MAAD,GAAU;AACzB,UAAIW,gBAAgBnB,oBAAoBzI,MAAM6C,SAAS0G,OAAhB;AACvCvJ,YAAM+D,SAASpG,OAAOhB,GAAtB;AAIAoJ,kBAAY;QACVhC,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;QACVD,QAAQ;UACN,CAAC8F,cAAc1I,MAAM6B,EAArB,GAA0BkG,OAAO3L;QAD3B;MAFE,CAAD;AAMX;IACD;AAED9B,cAAU,CAACqO,iBAAiBZ,MAAD,GAAU,iCAA5B;AAGT,QAAIsD,cAAqC;MACvCvM,OAAO;MACP3F,MAAM4O,OAAO5O;MACb8F,YAAYD;MACZE,YAAYF;MACZG,aAAaH;MACbI,UAAUJ;MACV,6BAA6B;;AAE/BF,UAAM+D,SAASnJ,IAAI+B,KAAK4P,WAAxB;AACAxG,gBAAY;MAAEhC,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;IAAZ,CAAD;EACZ;AAqBD,iBAAe2F,wBACb1J,QACAd,WASM,OAAA;AAAA,QAAA;AAAA,QARN;MACEoI;MACA1N;MACAqS;IAHF,IAQM,UAAA,SAAF,CAAA,IAAE;AAEN,QAAI/M,UAAS0I,YAAY;AACvBpD,+BAAyB;IAC1B;AAED,QAAIiI,mBAAmBjF;MACrBxH,OAAMC;MACNf,UAASe;;MAF0B,SAAA;QAKjCuG,aAAa;MALoB,GAM7ByF,wBAAwB;QAAES,wBAAwB;UAAS,CAAA,CAN9B;IAArC;AASAlR,cACEiR,kBACA,gDAFO;AAKT,QACE9L,mBAAmBgM,KAAKzN,UAASe,QAAjC,KACAW,aACA,SAAA,UAAOC,WAAP,OAAA,SAAO,QAAQZ,cAAa,aAC5B;AACA,UAAId,MAAM7E,KAAKoI,QAAQkK,UAAU1N,UAASe,QAAhC;AACV,UAAI4M,sBAAsBC,cAAc3N,IAAIrF,UAAUgI,QAAf,KAA4B;AAEnE,UAAIjB,OAAOZ,SAAS8M,WAAW5N,IAAI4N,UAAUF,qBAAqB;AAChE,YAAIjT,SAAS;AACXiH,iBAAOZ,SAASrG,QAAQsF,UAASe,QAAjC;QACD,OAAM;AACLY,iBAAOZ,SAASpD,OAAOqC,UAASe,QAAhC;QACD;AACD;MACD;IACF;AAIDqE,kCAA8B;AAE9B,QAAI0I,wBACFpT,YAAY,OAAOuK,OAAc0C,UAAU1C,OAAcyC;AAI3D,QAAI;MAAEzG;MAAYC;MAAYC;MAAaC;QAAaN,OAAMwD;AAC9D,QAAI,CAAC8D,cAAcnH,cAAcC,cAAcE,YAAYD,aAAa;AACtEiH,mBAAa;QACXnH;QACAC;QACAC;QACAC;;IAEH;AAKD,QACER,kCAAkCnF,IAAIuE,UAAS1E,MAA/C,KACA8M,cACAf,iBAAiBe,WAAWnH,UAAZ,GAChB;AACA,YAAM6F,gBAAgBgH,uBAAuBP,kBAAkB;QAC7DnF,YAAU,SAAA,CAAA,GACLA,YADK;UAERlH,YAAYlB,UAASe;SAHsC;;QAM7DyD,oBAAoBW;MANyC,CAA1C;eAQZ4H,uBAAuB;AAGhC,YAAMjG,gBAAgBgH,uBAAuBP,kBAAkB;QAC7D1E,oBAAoB;UAClB/H,OAAO;UACPC,UAAUwM;UACVtM,YAAYD;UACZE,YAAYF;UACZG,aAAaH;UACbI,UAAUJ;;QAEZ8I,mBAAmB1B;;QAEnB5D,oBAAoBW;MAXyC,CAA1C;IAatB,OAAM;AAGL,YAAM2B,gBAAgBgH,uBAAuBP,kBAAkB;QAC7D1E,oBAAoB;UAClB/H,OAAO;UACPC,UAAUwM;UACVtM,YAAYmH,aAAaA,WAAWnH,aAAaD;UACjDE,YAAYkH,aAAaA,WAAWlH,aAAaF;UACjDG,aAAaiH,aAAaA,WAAWjH,cAAcH;UACnDI,UAAUgH,aAAaA,WAAWhH,WAAWJ;;;QAG/CwD,oBAAoBW;MAVyC,CAA1C;IAYtB;EACF;AAED,iBAAewG,+BACboC,gBACApK,SACAkH,eACAmD,gBACA5E,SAAgB;AAKhB,QAAIoC,UAAU,MAAM7O,QAAQsR,IAAI,CAC9B,GAAGpD,cAAcqD,IAAK5B,WACpBhC,mBACE,UACAlB,SACAkD,OACA3I,SACAnB,UACAF,qBACAM,QAPgB,CADjB,GAWH,GAAGoL,eAAeE,IAAK5C,OAAK;AAC1B,UAAIA,EAAE3H,WAAW2H,EAAEgB,SAAShB,EAAExO,YAAY;AACxC,eAAOwN,mBACL,UACAjB,wBAAwBjO,KAAKoI,SAAS8H,EAAEnD,MAAMmD,EAAExO,WAAWI,MAApC,GACvBoO,EAAEgB,OACFhB,EAAE3H,SACFnB,UACAF,qBACAM,QAPuB;MAS1B,OAAM;AACL,YAAIxE,QAAqB;UACvB8L,MAAMC,WAAW/L;UACjBA,OAAOsF,uBAAuB,KAAK;YAAE9I,UAAU0Q,EAAEnD;WAApB;;AAE/B,eAAO/J;MACR;KAjBA,CAZ2B,CAAZ;AAgCpB,QAAIqN,gBAAgBD,QAAQ2C,MAAM,GAAGtD,cAAcxI,MAA/B;AACpB,QAAIqJ,iBAAiBF,QAAQ2C,MAAMtD,cAAcxI,MAA5B;AAErB,UAAM1F,QAAQsR,IAAI,CAChBG,uBACEL,gBACAlD,eACAY,eACAA,cAAcyC,IAAI,MAAM9E,QAAQlM,MAAhC,GACA,OACA4D,MAAM4D,UANc,GAQtB0J,uBACEL,gBACAC,eAAeE,IAAK5C,OAAMA,EAAEgB,KAA5B,GACAZ,gBACAsC,eAAeE,IAAK5C,OAAOA,EAAExO,aAAawO,EAAExO,WAAWI,SAAS,IAAhE,GACA,IALoB,CATN,CAAZ;AAkBN,WAAO;MAAEsO;MAASC;MAAeC;;EAClC;AAED,WAAS/C,uBAAoB;AAE3BrD,6BAAyB;AAIzBC,4BAAwBzH,KAAK,GAAGoL,sBAAqB,CAArD;AAGApD,qBAAiBlH,QAAQ,CAAChC,GAAGa,QAAO;AAClC,UAAIgI,iBAAiBhK,IAAIgC,GAArB,GAA2B;AAC7B+H,8BAAsB1H,KAAKL,GAA3B;AACA8N,qBAAa9N,GAAD;MACb;KAJH;EAMD;AAED,WAAS4O,gBAAgB5O,KAAa4M,SAAiBjM,OAAU;AAC/D,QAAIsM,gBAAgBnB,oBAAoBzI,MAAM6C,SAAS0G,OAAhB;AACvCpD,kBAAcxJ,GAAD;AACboJ,gBAAY;MACVjC,QAAQ;QACN,CAAC8F,cAAc1I,MAAM6B,EAArB,GAA0BzF;;MAE5ByG,UAAU,IAAIC,IAAIhE,MAAM+D,QAAd;IAJA,CAAD;EAMZ;AAED,WAASoC,cAAcxJ,KAAW;AAChC,QAAIgI,iBAAiBhK,IAAIgC,GAArB;AAA2B8N,mBAAa9N,GAAD;AAC3CqI,qBAAiBrH,OAAOhB,GAAxB;AACAmI,mBAAenH,OAAOhB,GAAtB;AACAoI,qBAAiBpH,OAAOhB,GAAxB;AACAqD,UAAM+D,SAASpG,OAAOhB,GAAtB;EACD;AAED,WAAS8N,aAAa9N,KAAW;AAC/B,QAAIX,aAAa2I,iBAAiBlH,IAAId,GAArB;AACjBnB,cAAUQ,YAA0CW,gCAAAA,GAA3C;AACTX,eAAWmC,MAAX;AACAwG,qBAAiBhH,OAAOhB,GAAxB;EACD;AAED,WAAS4Q,iBAAiB9G,MAAc;AACtC,aAAS9J,OAAO8J,MAAM;AACpB,UAAI4D,UAAUe,WAAWzO,GAAD;AACxB,UAAI4P,cAAqC;QACvCvM,OAAO;QACP3F,MAAMgQ,QAAQhQ;QACd8F,YAAYD;QACZE,YAAYF;QACZG,aAAaH;QACbI,UAAUJ;QACV,6BAA6B;;AAE/BF,YAAM+D,SAASnJ,IAAI+B,KAAK4P,WAAxB;IACD;EACF;AAED,WAASpC,yBAAsB;AAC7B,QAAIqD,WAAW,CAAA;AACf,QAAItD,kBAAkB;AACtB,aAASvN,OAAOoI,kBAAkB;AAChC,UAAIsF,UAAUrK,MAAM+D,SAAStG,IAAId,GAAnB;AACdnB,gBAAU6O,SAA8B1N,uBAAAA,GAA/B;AACT,UAAI0N,QAAQrK,UAAU,WAAW;AAC/B+E,yBAAiBpH,OAAOhB,GAAxB;AACA6Q,iBAASxQ,KAAKL,GAAd;AACAuN,0BAAkB;MACnB;IACF;AACDqD,qBAAiBC,QAAD;AAChB,WAAOtD;EACR;AAED,WAASgB,qBAAqBuC,UAAgB;AAC5C,QAAIC,aAAa,CAAA;AACjB,aAAS,CAAC/Q,KAAKoG,EAAN,KAAa+B,gBAAgB;AACpC,UAAI/B,KAAK0K,UAAU;AACjB,YAAIpD,UAAUrK,MAAM+D,SAAStG,IAAId,GAAnB;AACdnB,kBAAU6O,SAA8B1N,uBAAAA,GAA/B;AACT,YAAI0N,QAAQrK,UAAU,WAAW;AAC/ByK,uBAAa9N,GAAD;AACZmI,yBAAenH,OAAOhB,GAAtB;AACA+Q,qBAAW1Q,KAAKL,GAAhB;QACD;MACF;IACF;AACD4Q,qBAAiBG,UAAD;AAChB,WAAOA,WAAWnM,SAAS;EAC5B;AAED,WAASoM,WAAWhR,KAAasB,IAAmB;AAClD,QAAI2P,UAAmB5N,MAAMiE,SAASxG,IAAId,GAAnB,KAA2B6D;AAElD,QAAI0E,iBAAiBzH,IAAId,GAArB,MAA8BsB,IAAI;AACpCiH,uBAAiBtK,IAAI+B,KAAKsB,EAA1B;IACD;AAED,WAAO2P;EACR;AAED,WAAS9H,cAAcnJ,KAAW;AAChCqD,UAAMiE,SAAStG,OAAOhB,GAAtB;AACAuI,qBAAiBvH,OAAOhB,GAAxB;EACD;AAGD,WAASkJ,cAAclJ,KAAakR,YAAmB;AACrD,QAAID,UAAU5N,MAAMiE,SAASxG,IAAId,GAAnB,KAA2B6D;AAIzChF,cACGoS,QAAQ5N,UAAU,eAAe6N,WAAW7N,UAAU,aACpD4N,QAAQ5N,UAAU,aAAa6N,WAAW7N,UAAU,aACpD4N,QAAQ5N,UAAU,aAAa6N,WAAW7N,UAAU,gBACpD4N,QAAQ5N,UAAU,aAAa6N,WAAW7N,UAAU,eACpD4N,QAAQ5N,UAAU,gBAAgB6N,WAAW7N,UAAU,aALnD,uCAM8B4N,QAAQ5N,QANtC,SAMkD6N,WAAW7N,KAN7D;AASTA,UAAMiE,SAASrJ,IAAI+B,KAAKkR,UAAxB;AACA9H,gBAAY;MAAE9B,UAAU,IAAID,IAAIhE,MAAMiE,QAAd;IAAZ,CAAD;EACZ;AAED,WAASwB,sBAQR,OAAA;AAAA,QAR8B;MAC7BC;MACAC;MACArC;QAKD;AACC,QAAI4B,iBAAiB1G,SAAS,GAAG;AAC/B;IACD;AAID,QAAI0G,iBAAiB1G,OAAO,GAAG;AAC7B+G,cAAQ,OAAO,8CAAR;IACR;AAED,QAAI/I,UAAUf,MAAMmD,KAAKsG,iBAAiB1I,QAAjB,CAAX;AACd,QAAI,CAACgJ,YAAYsI,eAAb,IAAgCtR,QAAQA,QAAQ+E,SAAS,CAAlB;AAC3C,QAAIqM,UAAU5N,MAAMiE,SAASxG,IAAI+H,UAAnB;AAEd,QAAIoI,WAAWA,QAAQ5N,UAAU,cAAc;AAG7C;IACD;AAID,QAAI8N,gBAAgB;MAAEpI;MAAiBC;MAAcrC;IAAjC,CAAD,GAAoD;AACrE,aAAOkC;IACR;EACF;AAED,WAAS4C,sBACP2F,WAAwC;AAExC,QAAIC,oBAA8B,CAAA;AAClC/I,oBAAgBnH,QAAQ,CAACmQ,KAAK1E,YAAW;AACvC,UAAI,CAACwE,aAAaA,UAAUxE,OAAD,GAAW;AAIpC0E,YAAI/P,OAAJ;AACA8P,0BAAkBhR,KAAKuM,OAAvB;AACAtE,wBAAgBtH,OAAO4L,OAAvB;MACD;KARH;AAUA,WAAOyE;EACR;AAID,WAASE,wBACPC,WACAC,aACAC,QAAwC;AAExClM,IAAAA,wBAAuBgM;AACvB9L,wBAAoB+L;AACpBhM,8BAA0BiM,WAAYpO,cAAaA,SAAStD;AAK5D,QAAI,CAAC2F,yBAAyBtC,MAAMwD,eAAezD,iBAAiB;AAClEuC,8BAAwB;AACxB,UAAIgM,IAAI3H,uBAAuB3G,MAAMC,UAAUD,MAAM6C,OAAvB;AAC9B,UAAIyL,KAAK,MAAM;AACbvI,oBAAY;UAAEtC,uBAAuB6K;QAAzB,CAAD;MACZ;IACF;AAED,WAAO,MAAK;AACVnM,MAAAA,wBAAuB;AACvBE,0BAAoB;AACpBD,gCAA0B;;EAE7B;AAED,WAAS4F,mBACP/H,UACA4C,SAAiC;AAEjC,QAAIV,yBAAwBC,2BAA2BC,mBAAmB;AACxE,UAAIkM,cAAc1L,QAAQuK,IAAKlK,OAC7BsL,sBAAsBtL,GAAGlD,MAAM4D,UAAV,CADL;AAGlB,UAAIjH,MAAMyF,wBAAwBnC,UAAUsO,WAAX,KAA2BtO,SAAStD;AACrEwF,MAAAA,sBAAqBxF,GAAD,IAAQ0F,kBAAiB;IAC9C;EACF;AAED,WAASsE,uBACP1G,UACA4C,SAAiC;AAEjC,QAAIV,yBAAwBC,2BAA2BC,mBAAmB;AACxE,UAAIkM,cAAc1L,QAAQuK,IAAKlK,OAC7BsL,sBAAsBtL,GAAGlD,MAAM4D,UAAV,CADL;AAGlB,UAAIjH,MAAMyF,wBAAwBnC,UAAUsO,WAAX,KAA2BtO,SAAStD;AACrE,UAAI2R,IAAInM,sBAAqBxF,GAAD;AAC5B,UAAI,OAAO2R,MAAM,UAAU;AACzB,eAAOA;MACR;IACF;AACD,WAAO;EACR;AAED,WAASG,mBAAmBC,WAAoC;AAC9DhN,eAAW,CAAA;AACXG,yBAAqBD,0BACnB8M,WACAlN,qBACAtB,QACAwB,QAJ4C;EAM/C;AAED2B,WAAS;IACP,IAAIvB,WAAQ;AACV,aAAOA;;IAET,IAAI9B,QAAK;AACP,aAAOA;;IAET,IAAIsB,SAAM;AACR,aAAOK;;IAETyD;IACApH;IACAkQ;IACApH;IACAuE;IACAzD;;;IAGA+G,YAAa5H,QAAWzM,KAAKoI,QAAQiM,WAAW5H,EAAxB;IACxBU,gBAAiBV,QAAWzM,KAAKoI,QAAQ+E,eAAeV,EAA5B;IAC5BqE;IACAjF;IACAF;IACA0H;IACA7H;IACA8I,2BAA2BjK;IAC3BkK,0BAA0B5J;;;IAG1BwJ;;AAGF,SAAOpL;AACR;IAOYyL,yBAAyBC,OAAO,UAAD;AA0hB5C,SAASC,uBACPC,MAA2B;AAE3B,SAAOA,QAAQ,QAAQ,cAAcA;AACtC;AAED,SAASC,YACPC,UACAC,SACAC,UACAC,iBACAC,IACAC,aACAC,UAA8B;AAE9B,MAAIC;AACJ,MAAIC;AACJ,MAAIH,eAAe,QAAQC,aAAa,QAAQ;AAK9CC,wBAAoB,CAAA;AACpB,aAASE,SAASR,SAAS;AACzBM,wBAAkBG,KAAKD,KAAvB;AACA,UAAIA,MAAME,MAAMC,OAAOP,aAAa;AAClCG,2BAAmBC;AACnB;MACD;IACF;EACF,OAAM;AACLF,wBAAoBN;AACpBO,uBAAmBP,QAAQA,QAAQY,SAAS,CAAlB;EAC3B;AAGD,MAAIC,OAAOC,UACTX,KAAKA,KAAK,KACVY,2BAA2BT,iBAAD,EAAoBU,IAAKC,OAAMA,EAAEC,YAA3D,GACAC,cAAcpB,SAASqB,UAAUnB,QAApB,KAAiCF,SAASqB,UACvDf,aAAa,MAJK;AAUpB,MAAIF,MAAM,MAAM;AACdU,SAAKQ,SAAStB,SAASsB;AACvBR,SAAKS,OAAOvB,SAASuB;EACtB;AAGD,OACGnB,MAAM,QAAQA,OAAO,MAAMA,OAAO,QACnCI,oBACAA,iBAAiBG,MAAMa,SACvB,CAACC,mBAAmBX,KAAKQ,MAAN,GACnB;AACAR,SAAKQ,SAASR,KAAKQ,SACfR,KAAKQ,OAAOI,QAAQ,OAAO,SAA3B,IACA;EACL;AAMD,MAAIvB,mBAAmBD,aAAa,KAAK;AACvCY,SAAKO,WACHP,KAAKO,aAAa,MAAMnB,WAAWyB,UAAU,CAACzB,UAAUY,KAAKO,QAAhB,CAAD;EAC/C;AAED,SAAOO,WAAWd,IAAD;AAClB;AAID,SAASe,yBACPC,qBACAC,WACAjB,MACAhB,MAA4B;AAO5B,MAAI,CAACA,QAAQ,CAACD,uBAAuBC,IAAD,GAAQ;AAC1C,WAAO;MAAEgB;;EACV;AAED,MAAIhB,KAAKkC,cAAc,CAACC,cAAcnC,KAAKkC,UAAN,GAAmB;AACtD,WAAO;MACLlB;MACAoB,OAAOC,uBAAuB,KAAK;QAAEC,QAAQtC,KAAKkC;OAArB;;EAEhC;AAGD,MAAIK;AACJ,MAAIvC,KAAKwC,UAAU;AACjB,QAAIN,aAAalC,KAAKkC,cAAc;AACpCK,iBAAa;MACXL,YAAYF,sBACPE,WAAWO,YAAX,IACAP,WAAWQ,YAAX;MACLC,YAAYC,kBAAkB5B,IAAD;MAC7B6B,aACG7C,QAAQA,KAAK6C,eAAgB;MAChCL,UAAUxC,KAAKwC;;AAGjB,QAAIM,iBAAiBP,WAAWL,UAAZ,GAAyB;AAC3C,aAAO;QAAElB;QAAMuB;;IAChB;EACF;AAGD,MAAIQ,aAAaC,UAAUhC,IAAD;AAC1B,MAAIiC,eAAeC,8BAA8BlD,KAAKwC,QAAN;AAIhD,MAAIP,aAAac,WAAWvB,UAAUG,mBAAmBoB,WAAWvB,MAAZ,GAAqB;AAC3EyB,iBAAaE,OAAO,SAAS,EAA7B;EACD;AACDJ,aAAWvB,SAAX,MAAwByB;AAExB,SAAO;IAAEjC,MAAMc,WAAWiB,UAAD;IAAcR;;AACxC;AAID,SAASa,8BACPjD,SACAkD,YAAmB;AAEnB,MAAIC,kBAAkBnD;AACtB,MAAIkD,YAAY;AACd,QAAI3B,QAAQvB,QAAQoD,UAAWnC,OAAMA,EAAEP,MAAMC,OAAOuC,UAAxC;AACZ,QAAI3B,SAAS,GAAG;AACd4B,wBAAkBnD,QAAQqD,MAAM,GAAG9B,KAAjB;IACnB;EACF;AACD,SAAO4B;AACR;AAED,SAASG,iBACPC,SACAC,OACAxD,SACAoC,YACArC,UACA0D,wBACAC,yBACAC,uBACAC,kBACAC,aACA5D,UACA6D,mBACAC,cAAwB;AAExB,MAAIC,eAAeD,eACfE,OAAOC,OAAOH,YAAd,EAA4B,CAA5B,IACAD,oBACAG,OAAOC,OAAOJ,iBAAd,EAAiC,CAAjC,IACAK;AAEJ,MAAIC,aAAab,QAAQc,UAAUb,MAAMzD,QAAxB;AACjB,MAAIuE,UAAUf,QAAQc,UAAUtE,QAAlB;AAGd,MAAImD,aAAaa,eAAeE,OAAOM,KAAKR,YAAZ,EAA0B,CAA1B,IAA+BI;AAC/D,MAAIhB,kBAAkBF,8BAA8BjD,SAASkD,UAAV;AAEnD,MAAIsB,oBAAoBrB,gBAAgBsB,OAAO,CAACjE,OAAOe,UAAS;AAC9D,QAAIf,MAAME,MAAMgE,MAAM;AAEpB,aAAO;IACR;AACD,QAAIlE,MAAME,MAAMiE,UAAU,MAAM;AAC9B,aAAO;IACR;AAGD,QACEC,YAAYpB,MAAMqB,YAAYrB,MAAMxD,QAAQuB,KAAd,GAAsBf,KAAzC,KACXkD,wBAAwBoB,KAAMnE,QAAOA,OAAOH,MAAME,MAAMC,EAAxD,GACA;AACA,aAAO;IACR;AAMD,QAAIoE,oBAAoBvB,MAAMxD,QAAQuB,KAAd;AACxB,QAAIyD,iBAAiBxE;AAErB,WAAOyE,uBAAuBzE,OAAD,SAAA;MAC3B4D;MACAc,eAAeH,kBAAkBI;MACjCb;MACAc,YAAYJ,eAAeG;IAJA,GAKxB/C,YALwB;MAM3B4B;MACAqB;;QAEE5B;QAEAW,WAAWhD,WAAWgD,WAAW/C,WAC/BiD,QAAQlD,WAAWkD,QAAQjD;QAE7B+C,WAAW/C,WAAWiD,QAAQjD,UAC9BiE,mBAAmBP,mBAAmBC,cAApB;;KAftB,CAAA;GAxBsB;AA4CxB,MAAIO,uBAA8C,CAAA;AAClD3B,mBAAiB4B,QAAQ,CAACC,GAAGC,QAAO;AAElC,QAAI,CAAC1F,QAAQ8E,KAAM7D,OAAMA,EAAEP,MAAMC,OAAO8E,EAAEE,OAArC,GAA+C;AAClD;IACD;AAED,QAAIC,iBAAiBC,YAAYhC,aAAa4B,EAAE5E,MAAMZ,QAAtB;AAIhC,QAAI,CAAC2F,gBAAgB;AACnBL,2BAAqB9E,KAAK;QACxBiF;QACAC,SAASF,EAAEE;QACX9E,MAAM4E,EAAE5E;QACRb,SAAS;QACTQ,OAAO;QACPsF,YAAY;OANd;AAQA;IACD;AAED,QAAIC,eAAeC,eAAeJ,gBAAgBH,EAAE5E,IAAnB;AAEjC,QAAI8C,sBAAsBsC,SAASP,GAA/B,GAAqC;AACvCH,2BAAqB9E,KAAK;QACxBiF;QACAC,SAASF,EAAEE;QACX9E,MAAM4E,EAAE5E;QACRb,SAAS4F;QACTpF,OAAOuF;QACPD,YAAY,IAAII,gBAAJ;OANd;AAQA;IACD;AAMD,QAAIC,mBAAmBlB,uBAAuBc,cAAD,SAAA;MAC3C3B;MACAc,eAAe1B,MAAMxD,QAAQwD,MAAMxD,QAAQY,SAAS,CAArC,EAAwCuE;MACvDb;MACAc,YAAYpF,QAAQA,QAAQY,SAAS,CAAlB,EAAqBuE;IAJG,GAKxC/C,YALwC;MAM3C4B;;MAEAqB,yBAAyB5B;KAR3B,CAAA;AAUA,QAAI0C,kBAAkB;AACpBZ,2BAAqB9E,KAAK;QACxBiF;QACAC,SAASF,EAAEE;QACX9E,MAAM4E,EAAE5E;QACRb,SAAS4F;QACTpF,OAAOuF;QACPD,YAAY,IAAII,gBAAJ;OANd;IAQD;GA3DH;AA8DA,SAAO,CAAC1B,mBAAmBe,oBAApB;AACR;AAED,SAASX,YACPwB,mBACAC,cACA7F,OAA6B;AAE7B,MAAI8F;;IAEF,CAACD;IAED7F,MAAME,MAAMC,OAAO0F,aAAa3F,MAAMC;;AAIxC,MAAI4F,gBAAgBH,kBAAkB5F,MAAME,MAAMC,EAAb,MAAqBwD;AAG1D,SAAOmC,SAASC;AACjB;AAED,SAASjB,mBACPe,cACA7F,OAA6B;AAE7B,MAAIgG,cAAcH,aAAa3F,MAAMG;AACrC;;IAEEwF,aAAajF,aAAaZ,MAAMY;;IAG/BoF,eAAe,QACdA,YAAYC,SAAS,GAArB,KACAJ,aAAalB,OAAO,GAApB,MAA6B3E,MAAM2E,OAAO,GAAb;;AAElC;AAED,SAASF,uBACPyB,aACAC,KAA4C;AAE5C,MAAID,YAAYhG,MAAMyF,kBAAkB;AACtC,QAAIS,cAAcF,YAAYhG,MAAMyF,iBAAiBQ,GAAnC;AAClB,QAAI,OAAOC,gBAAgB,WAAW;AACpC,aAAOA;IACR;EACF;AAED,SAAOD,IAAItB;AACZ;AAOD,eAAewB,oBACbnG,OACAoG,qBACAC,UAAuB;AAEvB,MAAI,CAACrG,MAAMgE,MAAM;AACf;EACD;AAED,MAAIsC,YAAY,MAAMtG,MAAMgE,KAAN;AAKtB,MAAI,CAAChE,MAAMgE,MAAM;AACf;EACD;AAED,MAAIuC,gBAAgBF,SAASrG,MAAMC,EAAP;AAC5BuG,YAAUD,eAAe,4BAAhB;AAUT,MAAIE,eAAoC,CAAA;AACxC,WAASC,qBAAqBJ,WAAW;AACvC,QAAIK,mBACFJ,cAAcG,iBAAD;AAEf,QAAIE,8BACFD,qBAAqBlD;;IAGrBiD,sBAAsB;AAExBG,YACE,CAACD,6BACD,YAAUL,cAActG,KAA8ByG,8BAAAA,oBAExBA,mFAAAA,8BAAAA,oBAF9B,qBAFK;AAOP,QACE,CAACE,+BACD,CAACE,mBAAmBC,IAAIL,iBAAvB,GACD;AACAD,mBAAaC,iBAAD,IACVJ,UAAUI,iBAAD;IACZ;EACF;AAIDnD,SAAOyD,OAAOT,eAAeE,YAA7B;AAKAlD,SAAOyD,OAAOT,eAAd,SAAA,CAAA,GAKKH,oBAAmBG,aAAD,GALvB;IAMEvC,MAAMP;EANR,CAAA,CAAA;AAQD;AAED,eAAewD,mBACbC,MACAC,SACArH,OACAR,SACA+G,UACAD,qBACA7G,UACA6H,iBACAC,gBACAC,gBAAwB;AAAA,MAFxBF,oBAEwB,QAAA;AAFxBA,sBAA2B;EAEH;AAAA,MADxBC,mBACwB,QAAA;AADxBA,qBAA0B;EACF;AAExB,MAAIE;AACJ,MAAIC;AACJ,MAAIC;AAEJ,MAAIC,aAAcC,aAA4C;AAE5D,QAAIC;AACJ,QAAIC,eAAe,IAAIC,QAAQ,CAACC,GAAGC,MAAOJ,SAASI,CAAhC;AACnBP,eAAW,MAAMG,OAAM;AACvBT,YAAQc,OAAOC,iBAAiB,SAAST,QAAzC;AACA,WAAOK,QAAQK,KAAK,CAClBR,QAAQ;MAAER;MAAS1C,QAAQ3E,MAAM2E;MAAQ2D,SAASd;IAA1C,CAAD,GACPO,YAFkB,CAAb;;AAMT,MAAI;AACF,QAAIF,UAAU7H,MAAME,MAAMkH,IAAZ;AAEd,QAAIpH,MAAME,MAAMgE,MAAM;AACpB,UAAI2D,SAAS;AAEX,YAAInE,SAAS,MAAMsE,QAAQO,IAAI,CAC7BX,WAAWC,OAAD,GACVxB,oBAAoBrG,MAAME,OAAOoG,qBAAoBC,QAAlC,CAFU,CAAZ;AAInBmB,iBAAShE,OAAO,CAAD;MAChB,OAAM;AAEL,cAAM2C,oBAAoBrG,MAAME,OAAOoG,qBAAoBC,QAAlC;AAEzBsB,kBAAU7H,MAAME,MAAMkH,IAAZ;AACV,YAAIS,SAAS;AAIXH,mBAAS,MAAME,WAAWC,OAAD;QAC1B,WAAUT,SAAS,UAAU;AAC5B,cAAIoB,MAAM,IAAIC,IAAIpB,QAAQmB,GAAhB;AACV,cAAI5H,WAAW4H,IAAI5H,WAAW4H,IAAI3H;AAClC,gBAAMa,uBAAuB,KAAK;YAChCC,QAAQ0F,QAAQ1F;YAChBf;YACAuE,SAASnF,MAAME,MAAMC;UAHW,CAAN;QAK7B,OAAM;AAGL,iBAAO;YAAEiH,MAAMsB,WAAWC;YAAMA,MAAMhF;;QACvC;MACF;IACF,WAAU,CAACkE,SAAS;AACnB,UAAIW,MAAM,IAAIC,IAAIpB,QAAQmB,GAAhB;AACV,UAAI5H,WAAW4H,IAAI5H,WAAW4H,IAAI3H;AAClC,YAAMa,uBAAuB,KAAK;QAChCd;MADgC,CAAN;IAG7B,OAAM;AACL8G,eAAS,MAAME,WAAWC,OAAD;IAC1B;AAEDnB,cACEgB,WAAW/D,QACX,kBAAeyD,SAAS,WAAW,cAAc,cAAjD,iBAAA,MACMpH,MAAME,MAAMC,KADlB,8CACgEiH,OADhE,QAAA,4CAFO;WAMFwB,GAAP;AACAnB,iBAAaiB,WAAWjH;AACxBiG,aAASkB;EACV,UAtDD;AAuDE,QAAIjB,UAAU;AACZN,cAAQc,OAAOU,oBAAoB,SAASlB,QAA5C;IACD;EACF;AAED,MAAImB,WAAWpB,MAAD,GAAU;AACtB,QAAIqB,SAASrB,OAAOqB;AAGpB,QAAIC,oBAAoB/B,IAAI8B,MAAxB,GAAiC;AACnC,UAAIxJ,WAAWmI,OAAOuB,QAAQC,IAAI,UAAnB;AACfxC,gBACEnH,UACA,4EAFO;AAMT,UAAI,CAAC4J,mBAAmBC,KAAK7J,QAAxB,GAAmC;AACtCA,mBAAWD,YACT,IAAImJ,IAAIpB,QAAQmB,GAAhB,GACAhJ,QAAQqD,MAAM,GAAGrD,QAAQ6J,QAAQrJ,KAAhB,IAAyB,CAA1C,GACAP,UACA,MACAF,QALoB;MAOvB,WAAU,CAAC+H,iBAAiB;AAI3B,YAAI1D,aAAa,IAAI6E,IAAIpB,QAAQmB,GAAhB;AACjB,YAAIA,MAAMjJ,SAAS+J,WAAW,IAApB,IACN,IAAIb,IAAI7E,WAAW2F,WAAWhK,QAA9B,IACA,IAAIkJ,IAAIlJ,QAAR;AACJ,YAAIiK,iBAAiB7I,cAAc6H,IAAI5H,UAAUnB,QAAf,KAA4B;AAC9D,YAAI+I,IAAIiB,WAAW7F,WAAW6F,UAAUD,gBAAgB;AACtDjK,qBAAWiJ,IAAI5H,WAAW4H,IAAI3H,SAAS2H,IAAI1H;QAC5C;MACF;AAMD,UAAIwG,iBAAiB;AACnBI,eAAOuB,QAAQS,IAAI,YAAYnK,QAA/B;AACA,cAAMmI;MACP;AAED,aAAO;QACLN,MAAMsB,WAAWiB;QACjBZ;QACAxJ;QACAqK,YAAYlC,OAAOuB,QAAQC,IAAI,oBAAnB,MAA6C;;IAE5D;AAKD,QAAI3B,gBAAgB;AAElB,YAAM;QACJH,MAAMK,cAAciB,WAAWC;QAC/BkB,UAAUnC;;IAEb;AAED,QAAIiB;AACJ,QAAImB,cAAcpC,OAAOuB,QAAQC,IAAI,cAAnB;AAGlB,QAAIY,eAAe,wBAAwBV,KAAKU,WAA7B,GAA2C;AAC5DnB,aAAO,MAAMjB,OAAOqC,KAAP;IACd,OAAM;AACLpB,aAAO,MAAMjB,OAAOsC,KAAP;IACd;AAED,QAAIvC,eAAeiB,WAAWjH,OAAO;AACnC,aAAO;QACL2F,MAAMK;QACNhG,OAAO,IAAIwI,cAAclB,QAAQrB,OAAOwC,YAAYvB,IAA7C;QACPM,SAASvB,OAAOuB;;IAEnB;AAED,WAAO;MACL7B,MAAMsB,WAAWC;MACjBA;MACAwB,YAAYzC,OAAOqB;MACnBE,SAASvB,OAAOuB;;EAEnB;AAED,MAAIxB,eAAeiB,WAAWjH,OAAO;AACnC,WAAO;MAAE2F,MAAMK;MAAYhG,OAAOiG;;EACnC;AAED,MAAI0C,eAAe1C,MAAD,GAAU;AAAA,QAAA,cAAA;AAC1B,WAAO;MACLN,MAAMsB,WAAW2B;MACjBC,cAAc5C;MACdyC,aAAU,eAAEzC,OAAO6C,SAAT,OAAA,SAAE,aAAaxB;MACzBE,WAAS,gBAAAvB,OAAO6C,SAAMtB,OAAAA,SAAAA,cAAAA,YAAW,IAAIuB,QAAQ9C,OAAO6C,KAAKtB,OAAxB;;EAEpC;AAED,SAAO;IAAE7B,MAAMsB,WAAWC;IAAMA,MAAMjB;;AACvC;AAKD,SAAS+C,wBACP1H,SACAxD,UACA4I,QACAvG,YAAuB;AAEvB,MAAI4G,MAAMzF,QAAQc,UAAU5B,kBAAkB1C,QAAD,CAAnC,EAA+CmL,SAA/C;AACV,MAAIH,OAAoB;IAAEpC;;AAE1B,MAAIvG,cAAcO,iBAAiBP,WAAWL,UAAZ,GAAyB;AACzD,QAAI;MAAEA;MAAYW;MAAaL;QAAaD;AAI5C2I,SAAK5I,SAASJ,WAAWO,YAAX;AACdyI,SAAKI,OACHzI,gBAAgB,sCACZK,8BAA8BV,QAAD,IAC7BA;EACP;AAGD,SAAO,IAAI+I,QAAQpC,KAAK+B,IAAjB;AACR;AAED,SAAShI,8BAA8BV,UAAkB;AACvD,MAAIS,eAAe,IAAIuI,gBAAJ;AAEnB,WAAS,CAAC3F,KAAK4F,KAAN,KAAgBjJ,SAASkJ,QAAT,GAAoB;AAE3CzI,iBAAaE,OAAO0C,KAAK4F,iBAAiBE,OAAOF,MAAMG,OAAOH,KAA9D;EACD;AAED,SAAOxI;AACR;AAED,SAAS4I,uBACP1L,SACA2L,eACAC,SACA7H,cACA8H,iBAA0C;AAQ1C,MAAIhH,aAAwC,CAAA;AAC5C,MAAIiH,SAAuC;AAC3C,MAAInB;AACJ,MAAIoB,aAAa;AACjB,MAAIC,gBAAyC,CAAA;AAG7CJ,UAAQpG,QAAQ,CAAC0C,QAAQ3G,UAAS;AAChC,QAAIZ,KAAKgL,cAAcpK,KAAD,EAAQb,MAAMC;AACpCuG,cACE,CAAC+E,iBAAiB/D,MAAD,GACjB,qDAFO;AAIT,QAAIgE,cAAchE,MAAD,GAAU;AAGzB,UAAIiE,gBAAgBC,oBAAoBpM,SAASW,EAAV;AACvC,UAAIsB,QAAQiG,OAAOjG;AAInB,UAAI8B,cAAc;AAChB9B,gBAAQgC,OAAOC,OAAOH,YAAd,EAA4B,CAA5B;AACRA,uBAAeI;MAChB;AAED2H,eAASA,UAAU,CAAA;AAGnB,UAAIA,OAAOK,cAAczL,MAAMC,EAArB,KAA4B,MAAM;AAC1CmL,eAAOK,cAAczL,MAAMC,EAArB,IAA2BsB;MAClC;AAGD4C,iBAAWlE,EAAD,IAAOwD;AAIjB,UAAI,CAAC4H,YAAY;AACfA,qBAAa;AACbpB,qBAAa0B,qBAAqBnE,OAAOjG,KAAR,IAC7BiG,OAAOjG,MAAMsH,SACb;MACL;AACD,UAAIrB,OAAOuB,SAAS;AAClBuC,sBAAcrL,EAAD,IAAOuH,OAAOuB;MAC5B;IACF,OAAM;AACL,UAAI6C,iBAAiBpE,MAAD,GAAU;AAC5B2D,wBAAgB3B,IAAIvJ,IAAIuH,OAAO4C,YAA/B;AACAjG,mBAAWlE,EAAD,IAAOuH,OAAO4C,aAAa3B;MACtC,OAAM;AACLtE,mBAAWlE,EAAD,IAAOuH,OAAOiB;MACzB;AAID,UACEjB,OAAOyC,cAAc,QACrBzC,OAAOyC,eAAe,OACtB,CAACoB,YACD;AACApB,qBAAazC,OAAOyC;MACrB;AACD,UAAIzC,OAAOuB,SAAS;AAClBuC,sBAAcrL,EAAD,IAAOuH,OAAOuB;MAC5B;IACF;EACF,CA7DD;AAkEA,MAAI1F,cAAc;AAChB+H,aAAS/H;AACTc,eAAWZ,OAAOM,KAAKR,YAAZ,EAA0B,CAA1B,CAAD,IAAiCI;EAC5C;AAED,SAAO;IACLU;IACAiH;IACAnB,YAAYA,cAAc;IAC1BqB;;AAEH;AAED,SAASO,kBACP/I,OACAxD,SACA2L,eACAC,SACA7H,cACAwB,sBACAiH,gBACAX,iBAA0C;AAK1C,MAAI;IAAEhH;IAAYiH;EAAd,IAAyBJ,uBAC3B1L,SACA2L,eACAC,SACA7H,cACA8H,eALiD;AASnD,WAAStK,QAAQ,GAAGA,QAAQgE,qBAAqB3E,QAAQW,SAAS;AAChE,QAAI;MAAEmE;MAAKlF;MAAOsF;QAAeP,qBAAqBhE,KAAD;AACrD2F,cACEsF,mBAAmBrI,UAAaqI,eAAejL,KAAD,MAAY4C,QAC1D,2CAFO;AAIT,QAAI+D,SAASsE,eAAejL,KAAD;AAG3B,QAAIuE,cAAcA,WAAW6C,OAAO8D,SAAS;AAE3C;IACD,WAAUP,cAAchE,MAAD,GAAU;AAChC,UAAIiE,gBAAgBC,oBAAoB5I,MAAMxD,SAASQ,SAAhB,OAAA,SAAgBA,MAAOE,MAAMC,EAA7B;AACvC,UAAI,EAAEmL,UAAUA,OAAOK,cAAczL,MAAMC,EAArB,IAA2B;AAC/CmL,iBAAM,SAAA,CAAA,GACDA,QADC;UAEJ,CAACK,cAAczL,MAAMC,EAArB,GAA0BuH,OAAOjG;SAFnC;MAID;AACDuB,YAAMkJ,SAASC,OAAOjH,GAAtB;IACD,WAAUuG,iBAAiB/D,MAAD,GAAU;AAGnChB,gBAAU,OAAO,yCAAR;IACV,WAAUoF,iBAAiBpE,MAAD,GAAU;AAGnChB,gBAAU,OAAO,iCAAR;IACV,OAAM;AACL,UAAI0F,cAAqC;QACvCpJ,OAAO;QACP2F,MAAMjB,OAAOiB;QACbpH,YAAYoC;QACZ3B,YAAY2B;QACZzB,aAAayB;QACb9B,UAAU8B;QACV,6BAA6B;;AAE/BX,YAAMkJ,SAASxC,IAAIxE,KAAKkH,WAAxB;IACD;EACF;AAED,SAAO;IAAE/H;IAAYiH;;AACtB;AAED,SAASe,gBACPhI,YACAiI,eACA9M,SACA8L,QAAoC;AAEpC,MAAIiB,mBAAwBD,SAAAA,CAAAA,GAAAA,aAAR;AACpB,WAAStM,SAASR,SAAS;AACzB,QAAIW,KAAKH,MAAME,MAAMC;AACrB,QAAImM,cAAcE,eAAerM,EAA7B,GAAkC;AACpC,UAAImM,cAAcnM,EAAD,MAASwD,QAAW;AACnC4I,yBAAiBpM,EAAD,IAAOmM,cAAcnM,EAAD;MACrC;IAKF,WAAUkE,WAAWlE,EAAD,MAASwD,UAAa3D,MAAME,MAAMiE,QAAQ;AAG7DoI,uBAAiBpM,EAAD,IAAOkE,WAAWlE,EAAD;IAClC;AAED,QAAImL,UAAUA,OAAOkB,eAAerM,EAAtB,GAA2B;AAEvC;IACD;EACF;AACD,SAAOoM;AACR;AAKD,SAASX,oBACPpM,SACA2F,SAAgB;AAEhB,MAAIsH,kBAAkBtH,UAClB3F,QAAQqD,MAAM,GAAGrD,QAAQoD,UAAWnC,OAAMA,EAAEP,MAAMC,OAAOgF,OAAxC,IAAmD,CAApE,IACA,CAAC,GAAG3F,OAAJ;AACJ,SACEiN,gBAAgBC,QAAhB,EAA0BC,KAAMlM,OAAMA,EAAEP,MAAM0M,qBAAqB,IAAnE,KACApN,QAAQ,CAAD;AAEV;AAED,SAASqN,uBAAuBC,QAAiC;AAK/D,MAAI5M,QAAQ4M,OAAOH,KAAMzE,OAAMA,EAAEnH,SAAS,CAACmH,EAAE7H,QAAQ6H,EAAE7H,SAAS,GAApD,KAA4D;IACtEF,IAAE;;AAGJ,SAAO;IACLX,SAAS,CACP;MACEmF,QAAQ,CAAA;MACR/D,UAAU;MACVF,cAAc;MACdR;IAJF,CADO;IAQTA;;AAEH;AAED,SAASwB,uBACPqH,QAWM,QAAA;AAAA,MAVN;IACEnI;IACAuE;IACAxD;IACAyF;EAJF,IAUM,WAAA,SAAF,CAAA,IAAE;AAEN,MAAI8C,aAAa;AACjB,MAAI6C,eAAe;AAEnB,MAAIhE,WAAW,KAAK;AAClBmB,iBAAa;AACb,QAAIvI,UAAUf,YAAYuE,SAAS;AACjC4H,qBACE,gBAAcpL,SAAd,kBAAoCf,WAApC,YAAA,2CAC2CuE,UAF7C,SAAA;IAID,WAAUiC,SAAS,gBAAgB;AAClC2F,qBAAe;IAChB;EACF,WAAUhE,WAAW,KAAK;AACzBmB,iBAAa;AACb6C,mBAAyB5H,YAAAA,UAAgCvE,2BAAAA,WAAzD;EACD,WAAUmI,WAAW,KAAK;AACzBmB,iBAAa;AACb6C,mBAAY,2BAA4BnM,WAAxC;EACD,WAAUmI,WAAW,KAAK;AACzBmB,iBAAa;AACb,QAAIvI,UAAUf,YAAYuE,SAAS;AACjC4H,qBACE,gBAAcpL,OAAOG,YAAP,IAAoClB,kBAAAA,WACNuE,YAAAA,4CAAAA,UAF9C,SAAA;eAISxD,QAAQ;AACjBoL,qBAA0CpL,6BAAAA,OAAOG,YAAP,IAA1C;IACD;EACF;AAED,SAAO,IAAImI,cACTlB,UAAU,KACVmB,YACA,IAAI8C,MAAMD,YAAV,GACA,IAJK;AAMR;AAGD,SAASE,aAAa7B,SAAqB;AACzC,WAAS8B,IAAI9B,QAAQhL,SAAS,GAAG8M,KAAK,GAAGA,KAAK;AAC5C,QAAIxF,SAAS0D,QAAQ8B,CAAD;AACpB,QAAIzB,iBAAiB/D,MAAD,GAAU;AAC5B,aAAOA;IACR;EACF;AACF;AAED,SAASzF,kBAAkB5B,MAAQ;AACjC,MAAI+B,aAAa,OAAO/B,SAAS,WAAWgC,UAAUhC,IAAD,IAASA;AAC9D,SAAOc,WAAU,SAAA,CAAA,GAAMiB,YAAN;IAAkBtB,MAAM;GAAzC,CAAA;AACD;AAED,SAASqM,iBAAiBC,GAAaC,GAAW;AAChD,MAAID,EAAExM,aAAayM,EAAEzM,YAAYwM,EAAEvM,WAAWwM,EAAExM,QAAQ;AACtD,WAAO;EACR;AAED,MAAIuM,EAAEtM,SAAS,IAAI;AAEjB,WAAOuM,EAAEvM,SAAS;aACTsM,EAAEtM,SAASuM,EAAEvM,MAAM;AAE5B,WAAO;EACR,WAAUuM,EAAEvM,SAAS,IAAI;AAExB,WAAO;EACR;AAID,SAAO;AACR;AAED,SAASgL,iBAAiBpE,QAAkB;AAC1C,SAAOA,OAAON,SAASsB,WAAW2B;AACnC;AAED,SAASqB,cAAchE,QAAkB;AACvC,SAAOA,OAAON,SAASsB,WAAWjH;AACnC;AAED,SAASgK,iBAAiB/D,QAAmB;AAC3C,UAAQA,UAAUA,OAAON,UAAUsB,WAAWiB;AAC/C;AAEK,SAAUS,eAAeU,OAAU;AACvC,MAAIT,WAAyBS;AAC7B,SACET,YACA,OAAOA,aAAa,YACpB,OAAOA,SAAS1B,SAAS,YACzB,OAAO0B,SAASiD,cAAc,cAC9B,OAAOjD,SAASkD,WAAW,cAC3B,OAAOlD,SAASmD,gBAAgB;AAEnC;AAED,SAAS1E,WAAWgC,OAAU;AAC5B,SACEA,SAAS,QACT,OAAOA,MAAM/B,WAAW,YACxB,OAAO+B,MAAMZ,eAAe,YAC5B,OAAOY,MAAM7B,YAAY,YACzB,OAAO6B,MAAMH,SAAS;AAEzB;AAoBD,SAAS8C,cAAcC,QAAc;AACnC,SAAOC,oBAAoBC,IAAIF,OAAOG,YAAP,CAAxB;AACR;AAED,SAASC,iBACPJ,QAAc;AAEd,SAAOK,qBAAqBH,IAAIF,OAAOG,YAAP,CAAzB;AACR;AAED,eAAeG,uBACbC,gBACAC,eACAC,SACAC,SACAC,WACAC,mBAA6B;AAE7B,WAASC,QAAQ,GAAGA,QAAQJ,QAAQK,QAAQD,SAAS;AACnD,QAAIE,SAASN,QAAQI,KAAD;AACpB,QAAIG,QAAQR,cAAcK,KAAD;AAIzB,QAAI,CAACG,OAAO;AACV;IACD;AAED,QAAIC,eAAeV,eAAeW,KAC/BC,OAAMA,EAAEC,MAAMC,OAAOL,MAAOI,MAAMC,EADlB;AAGnB,QAAIC,uBACFL,gBAAgB,QAChB,CAACM,mBAAmBN,cAAcD,KAAf,MAClBJ,qBAAqBA,kBAAkBI,MAAMI,MAAMC,EAAb,OAAsBG;AAE/D,QAAIC,iBAAiBV,MAAD,MAAaJ,aAAaW,uBAAuB;AAInE,UAAII,SAAShB,QAAQG,KAAD;AACpBc,gBACED,QACA,kEAFO;AAIT,YAAME,oBAAoBb,QAAQW,QAAQf,SAAjB,EAA4BkB,KAAMd,CAAAA,YAAU;AACnE,YAAIA,SAAQ;AACVN,kBAAQI,KAAD,IAAUE,WAAUN,QAAQI,KAAD;QACnC;MACF,CAJK;IAKP;EACF;AACF;AAED,eAAee,oBACbb,QACAW,QACAI,QAAc;AAAA,MAAdA,WAAc,QAAA;AAAdA,aAAS;EAAK;AAEd,MAAIC,UAAU,MAAMhB,OAAOiB,aAAaC,YAAYP,MAAhC;AACpB,MAAIK,SAAS;AACX;EACD;AAED,MAAID,QAAQ;AACV,QAAI;AACF,aAAO;QACLI,MAAMC,WAAWC;QACjBA,MAAMrB,OAAOiB,aAAaK;;aAErBC,GAAP;AAEA,aAAO;QACLJ,MAAMC,WAAWI;QACjBA,OAAOD;;IAEV;EACF;AAED,SAAO;IACLJ,MAAMC,WAAWC;IACjBA,MAAMrB,OAAOiB,aAAaI;;AAE7B;AAED,SAASI,mBAAmBC,QAAc;AACxC,SAAO,IAAIC,gBAAgBD,MAApB,EAA4BE,OAAO,OAAnC,EAA4CC,KAAMC,OAAMA,MAAM,EAA9D;AACR;AAID,SAASC,sBACP9B,OACA+B,YAAqB;AAErB,MAAI;IAAE3B;IAAO4B;IAAUC;EAAnB,IAA8BjC;AAClC,SAAO;IACLK,IAAID,MAAMC;IACV2B;IACAC;IACAb,MAAMW,WAAW3B,MAAMC,EAAP;IAChB6B,QAAQ9B,MAAM8B;;AAEjB;AAED,SAASC,eACPC,SACAC,UAA2B;AAE3B,MAAIZ,SACF,OAAOY,aAAa,WAAWC,UAAUD,QAAD,EAAWZ,SAASY,SAASZ;AACvE,MACEW,QAAQA,QAAQtC,SAAS,CAAlB,EAAqBM,MAAMP,SAClC2B,mBAAmBC,UAAU,EAAX,GAClB;AAEA,WAAOW,QAAQA,QAAQtC,SAAS,CAAlB;EACf;AAGD,MAAIyC,cAAcC,2BAA2BJ,OAAD;AAC5C,SAAOG,YAAYA,YAAYzC,SAAS,CAAtB;AACnB;;;;;;;;;;;;;;;;;ACxiIM,IAAM2C,oBACLC,oBAA8C,IAApD;AACF,IAAa,MAAA;AACXD,oBAAkBE,cAAc;AACjC;AAEM,IAAMC,yBAA+BF,oBAE1C,IAFoC;AAGtC,IAAa,MAAA;AACXE,yBAAuBD,cAAc;AACtC;AAEM,IAAME,eAAqBH,oBAAqC,IAA3C;AAC5B,IAAa,MAAA;AACXG,eAAaF,cAAc;AAC5B;AAiCM,IAAMG,oBAA0BJ,oBACrC,IAD+B;AAIjC,IAAa,MAAA;AACXI,oBAAkBH,cAAc;AACjC;AAOM,IAAMI,kBAAwBL,oBACnC,IAD6B;AAI/B,IAAa,MAAA;AACXK,kBAAgBJ,cAAc;AAC/B;IAQYK,eAAqBN,oBAAkC;EAClEO,QAAQ;EACRC,SAAS,CAAA;EACTC,aAAa;AAHqD,CAAxC;AAM5B,IAAa,MAAA;AACXH,eAAaL,cAAc;AAC5B;AAEM,IAAMS,oBAA0BV,oBAAmB,IAAzB;AAEjC,IAAa,MAAA;AACXU,oBAAkBT,cAAc;AACjC;AChHM,SAASU,QACdC,IAEQ,OAAA;AAAA,MADR;IAAEC;EAAF,IACQ,UAAA,SAD2C,CAAA,IAC3C;AACR,GACEC,mBAAkB,IADpBC,OAAAA;IAEE;;;IAFO;EAAT,IAAAA,UAAA,KAAA,IAAA;AAOA,MAAI;IAAEC;IAAUC;EAAZ,IAAgCC,iBAAWd,iBAAjB;AAC9B,MAAI;IAAEe;IAAMC;IAAUC;MAAWC,gBAAgBV,IAAI;IAAEC;EAAF,CAAL;AAEhD,MAAIU,iBAAiBH;AAMrB,MAAIJ,aAAa,KAAK;AACpBO,qBACEH,aAAa,MAAMJ,WAAWQ,UAAU,CAACR,UAAUI,QAAX,CAAD;EAC1C;AAED,SAAOH,UAAUQ,WAAW;IAAEL,UAAUG;IAAgBF;IAAQF;EAApC,CAArB;AACR;AAOM,SAASL,qBAA8B;AAC5C,SAAaI,iBAAWb,eAAjB,KAAqC;AAC7C;AAYM,SAASqB,cAAwB;AACtC,GACEZ,mBAAkB,IADpBC,OAAAA;IAEE;;;IAFO;EAAT,IAAAA,UAAA,KAAA,IAAA;AAOA,SAAaG,iBAAWb,eAAjB,EAAkCsB;AAC1C;AAQM,SAASC,oBAAoC;AAClD,SAAaV,iBAAWb,eAAjB,EAAkCwB;AAC1C;AASM,SAASC,SAGdC,SAA+D;AAC/D,GACEjB,mBAAkB,IADpBC,OAAAA;IAEE;;;IAFO;EAAT,IAAAA,UAAA,KAAA,IAAA;AAOA,MAAI;IAAEK;EAAF,IAAeM,YAAW;AAC9B,SAAaM,cACX,MAAMC,UAA0BF,SAASX,QAA1B,GACf,CAACA,UAAUW,OAAX,CAFK;AAIR;AAUD,IAAMG,wBACJ;AAIF,SAASC,0BACPC,IACA;AACA,MAAIC,WAAiBnB,iBAAWd,iBAAjB,EAAoCkC;AACnD,MAAI,CAACD,UAAU;AAIbE,IAAMC,sBAAgBJ,EAAtB;EACD;AACF;AAQM,SAASK,cAAgC;AAC9C,MAAI;IAAEhC;EAAF,IAAwBS,iBAAWZ,YAAjB;AAGtB,SAAOG,cAAciC,kBAAiB,IAAKC,oBAAmB;AAC/D;AAED,SAASA,sBAAwC;AAC/C,GACE7B,mBAAkB,IADpBC,OAAAA;IAEE;;;IAFO;EAAT,IAAAA,UAAA,KAAA,IAAA;AAOA,MAAI6B,oBAA0B1B,iBAAWnB,iBAAjB;AACxB,MAAI;IAAEiB;IAAUC;EAAZ,IAAgCC,iBAAWd,iBAAjB;AAC9B,MAAI;IAAEI;EAAF,IAAoBU,iBAAWZ,YAAjB;AAClB,MAAI;IAAEc,UAAUyB;EAAZ,IAAiCnB,YAAW;AAEhD,MAAIoB,qBAAqBC,KAAKC,UAC5BC,2BAA2BzC,OAAD,EAAU0C,IAAKC,WAAUA,MAAMC,YAAzD,CADuB;AAIzB,MAAIC,YAAkBC,aAAO,KAAb;AAChBnB,4BAA0B,MAAM;AAC9BkB,cAAUE,UAAU;EACrB,CAFwB;AAIzB,MAAIC,WAAmCC,kBACrC,SAAC7C,IAAiB8C,SAAkC;AAAA,QAAlCA,YAAkC,QAAA;AAAlCA,gBAA2B,CAAA;IAAO;AAClD,WAAAC,QAAQN,UAAUE,SAASrB,qBAApB,IAAP;AAIA,QAAI,CAACmB,UAAUE;AAAS;AAExB,QAAI,OAAO3C,OAAO,UAAU;AAC1BK,gBAAU2C,GAAGhD,EAAb;AACA;IACD;AAED,QAAIiD,OAAOC,UACTlD,IACAmC,KAAKgB,MAAMjB,kBAAX,GACAD,kBACAa,QAAQ7C,aAAa,MAJH;AAapB,QAAI+B,qBAAqB,QAAQ5B,aAAa,KAAK;AACjD6C,WAAKzC,WACHyC,KAAKzC,aAAa,MACdJ,WACAQ,UAAU,CAACR,UAAU6C,KAAKzC,QAAhB,CAAD;IAChB;AAED,KAAC,CAAC,CAACsC,QAAQM,UAAU/C,UAAU+C,UAAU/C,UAAUgD,MACjDJ,MACAH,QAAQQ,OACRR,OAHF;EAKD,GACD,CACE1C,UACAC,WACA6B,oBACAD,kBACAD,iBALF,CAvC+B;AAgDjC,SAAOY;AACR;AAED,IAAMW,gBAAsBnE,oBAAuB,IAA7B;AAOf,SAASoE,mBAA+C;AAC7D,SAAalD,iBAAWiD,aAAjB;AACR;AAQM,SAASE,UAAUC,SAA8C;AACtE,MAAI/D,SAAeW,iBAAWZ,YAAjB,EAA+BC;AAC5C,MAAIA,QAAQ;AACV,WACE,oBAAC,cAAc,UAAf;MAAwB,OAAO+D;IAA/B,GAAyC/D,MAAzC;EAEH;AACD,SAAOA;AACR;AAQM,SAASgE,YAId;AACA,MAAI;IAAE/D;EAAF,IAAoBU,iBAAWZ,YAAjB;AAClB,MAAIkE,aAAahE,QAAQA,QAAQiE,SAAS,CAAlB;AACxB,SAAOD,aAAcA,WAAWE,SAAiB,CAAA;AAClD;AAOM,SAASpD,gBACdV,IAEM,QAAA;AAAA,MADN;IAAEC;EAAF,IACM,WAAA,SAD6C,CAAA,IAC7C;AACN,MAAI;IAAEL;EAAF,IAAoBU,iBAAWZ,YAAjB;AAClB,MAAI;IAAEc,UAAUyB;EAAZ,IAAiCnB,YAAW;AAEhD,MAAIoB,qBAAqBC,KAAKC,UAC5BC,2BAA2BzC,OAAD,EAAU0C,IAAKC,WAAUA,MAAMC,YAAzD,CADuB;AAIzB,SAAapB,cACX,MACE8B,UACElD,IACAmC,KAAKgB,MAAMjB,kBAAX,GACAD,kBACAhC,aAAa,MAJN,GAMX,CAACD,IAAIkC,oBAAoBD,kBAAkBhC,QAA3C,CARK;AAUR;AAUM,SAAS8D,UACdC,QACAC,aAC2B;AAC3B,SAAOC,cAAcF,QAAQC,WAAT;AACrB;AAGM,SAASC,cACdF,QACAC,aACAE,iBAC2B;AAC3B,GACEjE,mBAAkB,IADpBC,OAAAA;IAEE;;;IAFO;EAAT,IAAAA,UAAA,KAAA,IAAA;AAOA,MAAI;IAAEE;EAAF,IAAsBC,iBAAWd,iBAAjB;AACpB,MAAI;IAAEI,SAASwE;EAAX,IAAmC9D,iBAAWZ,YAAjB;AACjC,MAAIkE,aAAaQ,cAAcA,cAAcP,SAAS,CAAxB;AAC9B,MAAIQ,eAAeT,aAAaA,WAAWE,SAAS,CAAA;AACpD,MAAIQ,iBAAiBV,aAAaA,WAAWpD,WAAW;AACxD,MAAI+D,qBAAqBX,aAAaA,WAAWpB,eAAe;AAChE,MAAIgC,cAAcZ,cAAcA,WAAWa;AAE3C,MAAa,MAAA;AAqBX,QAAIC,aAAcF,eAAeA,YAAYvB,QAAS;AACtD0B,gBACEL,gBACA,CAACE,eAAeE,WAAWE,SAAS,GAApB,GAChB,oEAAA,MACMN,iBADN,2BAC6CI,aAD7C,kBAAA;;KAAA,2CAK2CA,aAL3C,oBAAA,YAMWA,eAAe,MAAM,MAASA,aAA9B,QANX,MAHS;EAWZ;AAED,MAAIG,sBAAsB/D,YAAW;AAErC,MAAIC;AACJ,MAAIkD,aAAa;AAAA,QAAA;AACf,QAAIa,oBACF,OAAOb,gBAAgB,WAAWc,UAAUd,WAAD,IAAgBA;AAE7D,MACEM,uBAAuB,SACrBO,wBAAAA,kBAAkBtE,aAAlB,OAAA,SAAA,sBAA4BwE,WAAWT,kBAAvC,MAFJ,OAAApE,UAAS,OAGP,8KAEiEoE,iEAAAA,qBAC9CO,SAAAA,mBAAAA,kBAAkBtE,WAN9B,sCAAA,IAATL,UAAA,KAAA,IAAA;AASAY,eAAW+D;EACZ,OAAM;AACL/D,eAAW8D;EACZ;AAED,MAAIrE,WAAWO,SAASP,YAAY;AACpC,MAAIyE,oBACFV,uBAAuB,MACnB/D,WACAA,SAAS0E,MAAMX,mBAAmBV,MAAlC,KAA6C;AAEnD,MAAIjE,UAAUuF,YAAYnB,QAAQ;IAAExD,UAAUyE;EAAZ,CAAT;AAEzB,MAAa,MAAA;AACX,WAAAlC,QACEyB,eAAe5E,WAAW,MACKmB,iCAAAA,SAASP,WAAWO,SAASN,SAASM,SAASR,OAFhF,IAAA,IAAA;AAKA,WAAAwC,QACEnD,WAAW,QACTA,QAAQA,QAAQiE,SAAS,CAAlB,EAAqBY,MAAMW,YAAYC,UAC9CzF,QAAQA,QAAQiE,SAAS,CAAlB,EAAqBY,MAAMa,cAAcD,QAClD,qCAAmCtE,SAASP,WAAWO,SAASN,SAASM,SAASR,OAAlF,6IAJK,IAAP;EAQD;AAED,MAAIgF,kBAAkBC,eACpB5F,WACEA,QAAQ0C,IAAKC,WACXkD,OAAOC,OAAO,CAAA,GAAInD,OAAO;IACvBuB,QAAQ2B,OAAOC,OAAO,CAAA,GAAIrB,cAAc9B,MAAMuB,MAAtC;IACRtD,UAAUI,UAAU;MAClB2D;;MAEAlE,UAAUsF,iBACNtF,UAAUsF,eAAepD,MAAM/B,QAA/B,EAAyCA,WACzC+B,MAAM/B;IALQ,CAAD;IAOnBgC,cACED,MAAMC,iBAAiB,MACnB+B,qBACA3D,UAAU;MACR2D;;MAEAlE,UAAUsF,iBACNtF,UAAUsF,eAAepD,MAAMC,YAA/B,EAA6ChC,WAC7C+B,MAAMC;IALF,CAAD;GAZjB,CADF,GAsBF4B,eACAD,eAzBkC;AA+BpC,MAAIF,eAAesB,iBAAiB;AAClC,WACE,oBAAC,gBAAgB,UAAjB;MACE,OAAO;QACLxE,UAAQ6E,UAAA;UACNpF,UAAU;UACVC,QAAQ;UACRF,MAAM;UACN+C,OAAO;UACPuC,KAAK;QALC,GAMH9E,QANG;QAQRE,gBAAgB6E,OAAeC;MAT1B;IADT,GAaGR,eAbH;EAgBH;AAED,SAAOA;AACR;AAED,SAASS,wBAAwB;AAC/B,MAAIC,QAAQC,cAAa;AACzB,MAAIC,UAAUC,qBAAqBH,KAAD,IAC3BA,MAAMI,SAAUJ,MAAAA,MAAMK,aACzBL,iBAAiBM,QACjBN,MAAME,UACNhE,KAAKC,UAAU6D,KAAf;AACJ,MAAIO,QAAQP,iBAAiBM,QAAQN,MAAMO,QAAQ;AACnD,MAAIC,YAAY;AAChB,MAAIC,YAAY;IAAEC,SAAS;IAAUC,iBAAiBH;;AACtD,MAAII,aAAa;IAAEF,SAAS;IAAWC,iBAAiBH;;AAExD,MAAIK,UAAU;AACd,MAAa,MAAA;AACXC,YAAQd,MACN,wDACAA,KAFF;AAKAa,cACE,oBAAA,gBAAA,MAAA,oBAAA,KAAA,MAAA,qBAAA,GAI0B,oBAAA,KAAA,MAAA,gGAAA,oBAAA,QAAA;MAAM,OAAOD;OAFrC,eAAA,GAAA,OAEyE,KACvE,oBAAA,QAAA;MAAM,OAAOA;IAAb,GAAA,cAAA,GAHF,sBAAA,CAFF;EASH;AAED,SACE,oBAAA,gBAAA,MACE,oBAAA,MAAA,MAAA,+BAAA,GACA,oBAAA,MAAA;IAAI,OAAO;MAAEG,WAAW;IAAb;EAAX,GAAqCb,OAArC,GACCK,QAAQ,oBAAA,OAAA;IAAK,OAAOE;EAAZ,GAAwBF,KAAxB,IAAuC,MAC/CM,OAJH;AAOH;AAED,IAAMG,sBAAuB,oBAAA,uBAA7B,IAAA;AAgBO,IAAMC,sBAAN,cAAwC5B,gBAG7C;EACA6B,YAAYC,OAAiC;AAC3C,UAAMA,KAAN;AACA,SAAK9D,QAAQ;MACXvC,UAAUqG,MAAMrG;MAChBsG,cAAcD,MAAMC;MACpBpB,OAAOmB,MAAMnB;;EAEhB;EAE8B,OAAxBqB,yBAAyBrB,OAAY;AAC1C,WAAO;MAAEA;;EACV;EAE8B,OAAxBsB,yBACLH,OACA9D,OACA;AASA,QACEA,MAAMvC,aAAaqG,MAAMrG,YACxBuC,MAAM+D,iBAAiB,UAAUD,MAAMC,iBAAiB,QACzD;AACA,aAAO;QACLpB,OAAOmB,MAAMnB;QACblF,UAAUqG,MAAMrG;QAChBsG,cAAcD,MAAMC;;IAEvB;AAMD,WAAO;MACLpB,OAAOmB,MAAMnB,SAAS3C,MAAM2C;MAC5BlF,UAAUuC,MAAMvC;MAChBsG,cAAcD,MAAMC,gBAAgB/D,MAAM+D;;EAE7C;EAEDG,kBAAkBvB,OAAYwB,WAAgB;AAC5CV,YAAQd,MACN,yDACAA,OACAwB,SAHF;EAKD;EAEDC,SAAS;AACP,WAAO,KAAKpE,MAAM2C,QACf,oBAAA,aAAa,UAAd;MAAuB,OAAO,KAAKmB,MAAMO;OACvC,oBAAC,kBAAkB,UAAnB;MACE,OAAO,KAAKrE,MAAM2C;MAClB,UAAU,KAAKmB,MAAMQ;IAFvB,CAAA,CADF,IAOA,KAAKR,MAAMS;EAEd;AAnED;AA4EF,SAASC,cAAqE,MAAA;AAAA,MAAvD;IAAEH;IAAcpF;IAAOsF;MAAgC;AAC5E,MAAI7F,oBAA0B1B,iBAAWnB,iBAAjB;AAIxB,MACE6C,qBACAA,kBAAkBN,UAClBM,kBAAkB+F,kBACjBxF,MAAMkC,MAAMuD,gBAAgBzF,MAAMkC,MAAMwD,gBACzC;AACAjG,sBAAkB+F,cAAcG,6BAA6B3F,MAAMkC,MAAM0D;EAC1E;AAED,SACE,oBAAC,aAAa,UAAd;IAAuB,OAAOR;EAA9B,GACGE,QADH;AAIH;AAEM,SAASrC,eACd5F,SACAwE,eACAD,iBAC2B;AAAA,MAAA;AAAA,MAF3BC,kBAE2B,QAAA;AAF3BA,oBAA8B,CAAA;EAEH;AAAA,MAD3BD,oBAC2B,QAAA;AAD3BA,sBAA+C;EACpB;AAC3B,MAAIvE,WAAW,MAAM;AAAA,QAAA;AACnB,SAAA,mBAAIuE,oBAAJ,QAAI,iBAAiBiE,QAAQ;AAG3BxI,gBAAUuE,gBAAgBvE;IAC3B,OAAM;AACL,aAAO;IACR;EACF;AAED,MAAI2F,kBAAkB3F;AAGtB,MAAIwI,UAAM,oBAAGjE,oBAAH,OAAA,SAAG,kBAAiBiE;AAC9B,MAAIA,UAAU,MAAM;AAClB,QAAIC,aAAa9C,gBAAgB+C,UAC9BC,OAAMA,EAAE9D,MAAM0D,OAAMC,UAAAA,OAAAA,SAAAA,OAASG,EAAE9D,MAAM0D,EAAX,EADZ;AAGjB,MACEE,cAAc,KADhB,OAAAlI,UAAS,OAAA,8DAEqDsF,OAAO+C,KACjEJ,MAD0D,EAE1DK,KAAK,GAFqD,CAFrD,IAATtI,UAAA,KAAA,IAAA;AAMAoF,sBAAkBA,gBAAgBL,MAChC,GACAwD,KAAKC,IAAIpD,gBAAgB1B,QAAQwE,aAAa,CAA9C,CAFgB;EAInB;AAED,SAAO9C,gBAAgBqD,YAAY,CAACjJ,QAAQ4C,OAAOsG,UAAU;AAC3D,QAAI5C,QAAQ1D,MAAMkC,MAAM0D,KAAKC,UAAjB,OAAA,SAAiBA,OAAS7F,MAAMkC,MAAM0D,EAAf,IAAqB;AAExD,QAAIH,eAAuC;AAC3C,QAAI7D,iBAAiB;AACnB6D,qBAAezF,MAAMkC,MAAMuD,gBAAgBf;IAC5C;AACD,QAAIrH,WAAUwE,cAAc0E,OAAOvD,gBAAgBL,MAAM,GAAG2D,QAAQ,CAAjC,CAArB;AACd,QAAIE,cAAc,MAAM;AACtB,UAAIlB;AACJ,UAAI5B,OAAO;AACT4B,mBAAWG;MACZ,WAAUzF,MAAMkC,MAAMa,WAAW;AAOhCuC,mBAAY,oBAAA,MAAM,MAAM,WAAxB,IAAA;MACD,WAAUtF,MAAMkC,MAAMW,SAAS;AAC9ByC,mBAAWtF,MAAMkC,MAAMW;MACxB,OAAM;AACLyC,mBAAWlI;MACZ;AACD,aACE,oBAAC,eAAD;QACE;QACA,cAAc;UACZA;UACAC,SAAAA;UACAC,aAAasE,mBAAmB;;QAElC;OARJ;IAWD;AAID,WAAOA,oBACJ5B,MAAMkC,MAAMwD,iBAAiB1F,MAAMkC,MAAMuD,gBAAgBa,UAAU,KACpE,oBAAC,qBAAD;MACE,UAAU1E,gBAAgBpD;MAC1B,cAAcoD,gBAAgBkD;MAC9B,WAAWW;MACX;MACA,UAAUe,YAAW;MACrB,cAAc;QAAEpJ,QAAQ;QAAMC,SAAAA;QAASC,aAAa;MAAtC;KARX,IAWLkJ,YAAW;KAEZ,IArDI;AAsDR;IAEIC;UAAAA,iBAAAA;AAAAA,EAAAA,gBAAAA,YAAAA,IAAAA;AAAAA,EAAAA,gBAAAA,gBAAAA,IAAAA;AAAAA,EAAAA,gBAAAA,mBAAAA,IAAAA;AAAAA,GAAAA,mBAAAA,iBAAAA,CAAAA,EAAAA;IAMAC;UAAAA,sBAAAA;AAAAA,EAAAA,qBAAAA,YAAAA,IAAAA;AAAAA,EAAAA,qBAAAA,eAAAA,IAAAA;AAAAA,EAAAA,qBAAAA,eAAAA,IAAAA;AAAAA,EAAAA,qBAAAA,eAAAA,IAAAA;AAAAA,EAAAA,qBAAAA,eAAAA,IAAAA;AAAAA,EAAAA,qBAAAA,oBAAAA,IAAAA;AAAAA,EAAAA,qBAAAA,YAAAA,IAAAA;AAAAA,EAAAA,qBAAAA,gBAAAA,IAAAA;AAAAA,EAAAA,qBAAAA,mBAAAA,IAAAA;AAAAA,EAAAA,qBAAAA,YAAAA,IAAAA;AAAAA,GAAAA,wBAAAA,sBAAAA,CAAAA,EAAAA;AAaL,SAASC,0BACPC,UACA;AACA,SAAUA,WAAV;AACD;AAED,SAASC,qBAAqBD,UAA0B;AACtD,MAAIE,MAAY/I,iBAAWnB,iBAAjB;AACV,GAAUkK,MAAVlJ,OAAAA,UAAe+I,OAAAA,0BAA0BC,QAAD,CAA/B,IAAThJ,UAAA,KAAA,IAAA;AACA,SAAOkJ;AACR;AAED,SAASC,mBAAmBH,UAA+B;AACzD,MAAI7F,QAAchD,iBAAWhB,sBAAjB;AACZ,GAAUgE,QAAVnD,OAAAA,UAAiB+I,OAAAA,0BAA0BC,QAAD,CAAjC,IAAThJ,UAAA,KAAA,IAAA;AACA,SAAOmD;AACR;AAED,SAASiG,gBAAgBJ,UAA+B;AACtD,MAAI1E,QAAcnE,iBAAWZ,YAAjB;AACZ,GAAU+E,QAAVtE,OAAAA,UAAiB+I,OAAAA,0BAA0BC,QAAD,CAAjC,IAAThJ,UAAA,KAAA,IAAA;AACA,SAAOsE;AACR;AAGD,SAAS+E,kBAAkBL,UAA+B;AACxD,MAAI1E,QAAQ8E,gBAAgBJ,QAAD;AAC3B,MAAIM,YAAYhF,MAAM7E,QAAQ6E,MAAM7E,QAAQiE,SAAS,CAArC;AAChB,GACE4F,UAAUhF,MAAM0D,KADlB,OAAAhI,UAAS,OAEJgJ,WAFI,wDAAA,IAAThJ,UAAA,KAAA,IAAA;AAIA,SAAOsJ,UAAUhF,MAAM0D;AACxB;AAKM,SAASuB,aAAa;AAC3B,SAAOF,kBAAkBP,oBAAoBU,UAArB;AACzB;AAMM,SAASC,gBAAgB;AAC9B,MAAItG,QAAQgG,mBAAmBL,oBAAoBY,aAArB;AAC9B,SAAOvG,MAAMwG;AACd;AAMM,SAASC,iBAAiB;AAC/B,MAAI/H,oBAAoBoH,qBAAqBJ,eAAegB,cAAhB;AAC5C,MAAI1G,QAAQgG,mBAAmBL,oBAAoBe,cAArB;AAC9B,SAAO;IACLC,YAAYjI,kBAAkBkI,OAAOD;IACrC3G,OAAOA,MAAM+D;;AAEhB;AAMM,SAAS8C,aAAa;AAC3B,MAAI;IAAEvK;IAASwK;EAAX,IAA0Bd,mBAC5BL,oBAAoBoB,UAD0B;AAGhD,SAAajJ,cACX,MACExB,QAAQ0C,IAAKC,WAAU;AACrB,QAAI;MAAE/B;MAAUsD;QAAWvB;AAI3B,WAAO;MACL4F,IAAI5F,MAAMkC,MAAM0D;MAChB3H;MACAsD;MACAwG,MAAMF,WAAW7H,MAAMkC,MAAM0D,EAAb;MAChBoC,QAAQhI,MAAMkC,MAAM8F;;EAEvB,CAZD,GAaF,CAAC3K,SAASwK,UAAV,CAfK;AAiBR;AAKM,SAASI,gBAAyB;AACvC,MAAIlH,QAAQgG,mBAAmBL,oBAAoBwB,aAArB;AAC9B,MAAIC,UAAUlB,kBAAkBP,oBAAoBwB,aAArB;AAE/B,MAAInH,MAAM8E,UAAU9E,MAAM8E,OAAOsC,OAAb,KAAyB,MAAM;AACjD3D,YAAQd,MAAR,6DAC+DyE,UAD/D,GAAA;AAGA,WAAOrF;EACR;AACD,SAAO/B,MAAM8G,WAAWM,OAAjB;AACR;AAKM,SAASC,mBAAmBD,SAA0B;AAC3D,MAAIpH,QAAQgG,mBAAmBL,oBAAoB2B,kBAArB;AAC9B,SAAOtH,MAAM8G,WAAWM,OAAjB;AACR;AAKM,SAASG,gBAAyB;AACvC,MAAIvH,QAAQgG,mBAAmBL,oBAAoB6B,aAArB;AAE9B,MAAIrG,QAAcnE,iBAAWZ,YAAjB;AACZ,GAAU+E,QAAVtE,OAAAA,UAAA,OAAA,kDAAA,IAAAA,UAAA,KAAA,IAAA;AAEA,SAAOsF,OAAOsF,QAAOzH,SAAK,OAALA,SAAAA,MAAO0H,eAAc,CAAA,CAAnC,EAAuC,CAAvC;AACR;AAOM,SAAS9E,gBAAyB;AAAA,MAAA;AACvC,MAAID,QAAc3F,iBAAWR,iBAAjB;AACZ,MAAIwD,QAAQgG,mBAAmBL,oBAAoBgC,aAArB;AAC9B,MAAIP,UAAUlB,kBAAkBP,oBAAoBgC,aAArB;AAI/B,MAAIhF,OAAO;AACT,WAAOA;EACR;AAGD,UAAA,gBAAO3C,MAAM8E,WAAN,OAAA,SAAA,cAAesC,OAAf;AACR;AAKM,SAASQ,gBAAyB;AACvC,MAAIC,QAAc7K,iBAAWf,YAAjB;AACZ,SAAO4L,SAAP,OAAA,SAAOA,MAAOC;AACf;AAKM,SAASC,gBAAyB;AACvC,MAAIF,QAAc7K,iBAAWf,YAAjB;AACZ,SAAO4L,SAAP,OAAA,SAAOA,MAAOG;AACf;AAED,IAAIC,YAAY;AAQT,SAASC,WAAWC,aAAiD;AAC1E,MAAI;IAAEvB;EAAF,IAAad,qBAAqBJ,eAAe0C,UAAhB;AACrC,MAAIpI,QAAQgG,mBAAmBL,oBAAoByC,UAArB;AAC9B,MAAI,CAACC,UAAD,IAAqBC,eAAS,MAAMC,OAAO,EAAEN,SAAH,CAA3B;AAEnB,MAAIO,kBAAwBjJ,kBACzBkJ,UAAS;AACR,WAAO,OAAON,gBAAgB,aAC1B,CAAC,CAACA,YAAYM,IAAD,IACb,CAAC,CAACN;EACP,GACD,CAACA,WAAD,CANoB;AAStB,MAAIO,UAAU9B,OAAO+B,WAAWN,YAAYG,eAA9B;AAGdnK,EAAMuK,gBACJ,MAAM,MAAMhC,OAAOiC,cAAcR,UAArB,GACZ,CAACzB,QAAQyB,UAAT,CAFF;AAOA,SAAOrI,MAAM8I,SAASC,IAAIV,UAAnB,KAAkCK;AAC1C;AAMD,SAASlK,oBAAsC;AAC7C,MAAI;IAAEoI;EAAF,IAAad,qBAAqBJ,eAAesD,iBAAhB;AACrC,MAAInE,KAAKqB,kBAAkBP,oBAAoBqD,iBAArB;AAE1B,MAAI7J,YAAkBC,aAAO,KAAb;AAChBnB,4BAA0B,MAAM;AAC9BkB,cAAUE,UAAU;EACrB,CAFwB;AAIzB,MAAIC,WAAmCC,kBACrC,SAAC7C,IAAiB8C,SAAkC;AAAA,QAAlCA,YAAkC,QAAA;AAAlCA,gBAA2B,CAAA;IAAO;AAClD,WAAAC,QAAQN,UAAUE,SAASrB,qBAApB,IAAP;AAIA,QAAI,CAACmB,UAAUE;AAAS;AAExB,QAAI,OAAO3C,OAAO,UAAU;AAC1BkK,aAAOtH,SAAS5C,EAAhB;IACD,OAAM;AACLkK,aAAOtH,SAAS5C,IAAhB4F,UAAA;QAAsB2G,aAAapE;MAAnC,GAA0CrF,OAA1C,CAAA;IACD;EACF,GACD,CAACoH,QAAQ/B,EAAT,CAd+B;AAiBjC,SAAOvF;AACR;AAED,IAAM4J,gBAAyC,CAAA;AAE/C,SAAS7H,YAAYkB,KAAa4G,MAAetG,SAAiB;AAChE,MAAI,CAACsG,QAAQ,CAACD,cAAc3G,GAAD,GAAO;AAChC2G,kBAAc3G,GAAD,IAAQ;AACrB,WAAA9C,QAAQ,OAAOoD,OAAR,IAAP;EACD;AACF;AC96BM,SAASuG,eAG4B,MAAA;AAAA,MAHb;IAC7BC;IACAzC;MAC0C;AAG1C,MAAI,CAAC5G,OAAOsJ,QAAR,IAA0BhB,eAAS1B,OAAO5G,KAAtB;AACxB3B,EAAMC,sBAAgB,MAAMsI,OAAO2C,UAAUD,QAAjB,GAA4B,CAAC1C,QAAQ0C,QAAT,CAAxD;AAEA,MAAIvM,YAAkBe,cAAQ,MAAiB;AAC7C,WAAO;MACLP,YAAYqJ,OAAOrJ;MACnB8E,gBAAgBuE,OAAOvE;MACvB3C,IAAK8J,OAAM5C,OAAOtH,SAASkK,CAAhB;MACXzJ,MAAM,CAACrD,IAAIsD,QAAOyJ,SAChB7C,OAAOtH,SAAS5C,IAAI;QAClBsD,OAAAA;QACA0J,oBAAoBD,QAAAA,OAAAA,SAAAA,KAAMC;MAFR,CAApB;MAIF5J,SAAS,CAACpD,IAAIsD,QAAOyJ,SACnB7C,OAAOtH,SAAS5C,IAAI;QAClBoD,SAAS;QACTE,OAAAA;QACA0J,oBAAoBD,QAAAA,OAAAA,SAAAA,KAAMC;OAH5B;;EAML,GAAE,CAAC9C,MAAD,CAjBa;AAmBhB,MAAI9J,WAAW8J,OAAO9J,YAAY;AAElC,MAAI4B,oBAA0BZ,cAC5B,OAAO;IACL8I;IACA7J;IACAqB,QAAQ;IACRtB;MAEF,CAAC8J,QAAQ7J,WAAWD,QAApB,CAPsB;AAgBxB,SAEI,oBAAA,gBAAA,MAAA,oBAAC,kBAAkB,UAAnB;IAA4B,OAAO4B;KACjC,oBAAC,uBAAuB,UAAxB;IAAiC,OAAOsB;EAAxC,GACE,oBAAC,QAAD;IACE,UAAU4G,OAAO9J;IACjB,UAAU8J,OAAO5G,MAAMvC;IACvB,gBAAgBmJ,OAAO5G,MAAM2J;IAC7B;EAJF,GAMG/C,OAAO5G,MAAM4J,cACZ,oBAAC,YAAD;IAAY,QAAQhD,OAAOlG;IAAQ;EAAnC,CAAA,IAEA2I,eATJ,CADF,CADF,GAgBC,IAjBH;AAoBH;AAED,SAASQ,WAMqB,OAAA;AAAA,MANV;IAClBnJ;IACAV;MAI4B;AAC5B,SAAOY,cAAcF,QAAQqB,QAAW/B,KAApB;AACrB;AAcM,SAAS8J,aAK0B,OAAA;AAAA,MALb;IAC3BhN;IACAyH;IACAwF;IACAC;MACwC;AACxC,MAAIC,aAAmB7K,aAAN;AACjB,MAAI6K,WAAW5K,WAAW,MAAM;AAC9B4K,eAAW5K,UAAU6K,oBAAoB;MACvCH;MACAC;MACAG,UAAU;IAH6B,CAAD;EAKzC;AAED,MAAIC,UAAUH,WAAW5K;AACzB,MAAI,CAACW,OAAOsJ,QAAR,IAA0BhB,eAAS;IACrC+B,QAAQD,QAAQC;IAChB5M,UAAU2M,QAAQ3M;EAFmB,CAAf;AAKxBY,EAAMC,sBAAgB,MAAM8L,QAAQE,OAAOhB,QAAf,GAA0B,CAACc,OAAD,CAAtD;AAEA,SACE,oBAAC,QAAD;IACE;IACA;IACA,UAAUpK,MAAMvC;IAChB,gBAAgBuC,MAAMqK;IACtB,WAAWD;GANf;AASD;AAkBM,SAASG,SAKQ,OAAA;AAAA,MALC;IACvB7N;IACAoD;IACAE;IACArD;MACsB;AACtB,GACEC,mBAAkB,IADpBC,OAAAA;IAEE;;;IAFO;EAAT,IAAAA,UAAA,KAAA,IAAA;AAOA,SAAA4C,QACE,CAAOzC,iBAAWd,iBAAjB,EAAoCkC,QACrC,uNAFK,IAAP;AAOA,MAAI;IAAE9B;EAAF,IAAoBU,iBAAWZ,YAAjB;AAClB,MAAI;IAAEc,UAAUyB;EAAZ,IAAiCnB,YAAW;AAChD,MAAI8B,WAAWf,YAAW;AAI1B,MAAIoB,OAAOC,UACTlD,IACAqC,2BAA2BzC,OAAD,EAAU0C,IAAKC,WAAUA,MAAMC,YAAzD,GACAP,kBACAhC,aAAa,MAJK;AAMpB,MAAI6N,WAAW3L,KAAKC,UAAUa,IAAf;AAEftB,EAAMuK,gBACJ,MAAMtJ,SAAST,KAAKgB,MAAM2K,QAAX,GAAsB;IAAE1K;IAASE;IAAOrD;EAAlB,CAAvB,GACd,CAAC2C,UAAUkL,UAAU7N,UAAUmD,SAASE,KAAxC,CAFF;AAKA,SAAO;AACR;AAWM,SAASyK,OAAO3G,OAA+C;AACpE,SAAO3D,UAAU2D,MAAM1D,OAAP;AACjB;AA+CM,SAASsK,MAAMC,QAA+C;SACnE9N,UAAS,OAEP,sIAFO,IAATA,UAAA,KAAA;AAKD;AAoBM,SAAS+N,OAO2B,OAAA;AAAA,MAPpB;IACrB9N,UAAU+N,eAAe;IACzBtG,WAAW;IACX9G,UAAUqN;IACVnN,iBAAiB6E,OAAeC;IAChC1F;IACAqB,QAAQ2M,aAAa;MACoB;AACzC,GACE,CAACnO,mBAAkB,IADrB,OAAAC,UAAS,OAEP,wGAFO,IAATA,UAAA,KAAA,IAAA;AAQA,MAAIC,WAAW+N,aAAa/K,QAAQ,QAAQ,GAA7B;AACf,MAAIkL,oBAA0BlN,cAC5B,OAAO;IAAEhB;IAAUC;IAAWqB,QAAQ2M;MACtC,CAACjO,UAAUC,WAAWgO,UAAtB,CAFsB;AAKxB,MAAI,OAAOD,iBAAiB,UAAU;AACpCA,mBAAerJ,UAAUqJ,YAAD;EACzB;AAED,MAAI;IACF5N,WAAW;IACXC,SAAS;IACTF,OAAO;IACP+C,QAAQ;IACRuC,MAAM;EALJ,IAMAuI;AAEJ,MAAIG,kBAAwBnN,cAAQ,MAAM;AACxC,QAAIoN,mBAAmBC,cAAcjO,UAAUJ,QAAX;AAEpC,QAAIoO,oBAAoB,MAAM;AAC5B,aAAO;IACR;AAED,WAAO;MACLzN,UAAU;QACRP,UAAUgO;QACV/N;QACAF;QACA+C;QACAuC;;MAEF5E;;EAEH,GAAE,CAACb,UAAUI,UAAUC,QAAQF,MAAM+C,OAAOuC,KAAK5E,cAA/C,CAjBmB;AAmBtB,SAAA8B,QACEwL,mBAAmB,MACnB,uBAAqBnO,WAArB,sCAAA,MACMI,WAAWC,SAASF,OAD1B,2CAAA,kDAFK,IAAP;AAOA,MAAIgO,mBAAmB,MAAM;AAC3B,WAAO;EACR;AAED,SACE,oBAAC,kBAAkB,UAAnB;IAA4B,OAAOD;KACjC,oBAAC,gBAAgB,UAAjB;IAA0B;IAAoB,OAAOC;EAArD,CAAA,CADF;AAIH;AAaM,SAASG,OAG2B,OAAA;AAAA,MAHpB;IACrB7G;IACA9G;MACyC;AACzC,SAAOgD,UAAU4K,yBAAyB9G,QAAD,GAAY9G,QAArC;AACjB;AAgBM,SAAS6N,MAAuD,OAAA;AAAA,MAAjD;IAAE/G;IAAUG;IAAc6G;MAAuB;AACrE,SACE,oBAAC,oBAAD;IAAoB;IAAkB;EAAtC,GACG,oBAAA,cAAchH,MAAAA,QAAf,CADF;AAIH;IAWIiH;UAAAA,oBAAAA;AAAAA,EAAAA,mBAAAA,mBAAAA,SAAAA,IAAAA,CAAAA,IAAAA;AAAAA,EAAAA,mBAAAA,mBAAAA,SAAAA,IAAAA,CAAAA,IAAAA;AAAAA,EAAAA,mBAAAA,mBAAAA,OAAAA,IAAAA,CAAAA,IAAAA;AAAAA,GAAAA,sBAAAA,oBAAAA,CAAAA,EAAAA;AAML,IAAMC,sBAAsB,IAAIC,QAAQ,MAAM;AAAA,CAAlB;AAE5B,IAAMC,qBAAN,cAAuC3J,gBAGrC;EACA6B,YAAYC,OAAgC;AAC1C,UAAMA,KAAN;AACA,SAAK9D,QAAQ;MAAE2C,OAAO;;EACvB;EAE8B,OAAxBqB,yBAAyBrB,OAAY;AAC1C,WAAO;MAAEA;;EACV;EAEDuB,kBAAkBvB,OAAYwB,WAAgB;AAC5CV,YAAQd,MACN,oDACAA,OACAwB,SAHF;EAKD;EAEDC,SAAS;AACP,QAAI;MAAEG;MAAUG;MAAc6G;IAA1B,IAAsC,KAAKzH;AAE/C,QAAI8H,UAAiC;AACrC,QAAI7I,SAA4ByI,kBAAkBK;AAElD,QAAI,EAAEN,mBAAmBG,UAAU;AAEjC3I,eAASyI,kBAAkBM;AAC3BF,gBAAUF,QAAQH,QAAR;AACVpJ,aAAO4J,eAAeH,SAAS,YAAY;QAAE7C,KAAK,MAAM;OAAxD;AACA5G,aAAO4J,eAAeH,SAAS,SAAS;QAAE7C,KAAK,MAAMwC;OAArD;IACD,WAAU,KAAKvL,MAAM2C,OAAO;AAE3BI,eAASyI,kBAAkB7I;AAC3B,UAAIqJ,cAAc,KAAKhM,MAAM2C;AAC7BiJ,gBAAUF,QAAQO,OAAR,EAAiBC,MAAM,MAAM;MAAA,CAA7B;AACV/J,aAAO4J,eAAeH,SAAS,YAAY;QAAE7C,KAAK,MAAM;OAAxD;AACA5G,aAAO4J,eAAeH,SAAS,UAAU;QAAE7C,KAAK,MAAMiD;OAAtD;IACD,WAAWT,QAA2BY,UAAU;AAE/CP,gBAAUL;AACVxI,eACE6I,QAAQ5D,WAAWjG,SACfyJ,kBAAkB7I,QAClBiJ,QAAQ9D,UAAU/F,SAClByJ,kBAAkBM,UAClBN,kBAAkBK;IACzB,OAAM;AAEL9I,eAASyI,kBAAkBK;AAC3B1J,aAAO4J,eAAeR,SAAS,YAAY;QAAExC,KAAK,MAAM;OAAxD;AACA6C,gBAAUL,QAAQa,KACfpF,UACC7E,OAAO4J,eAAeR,SAAS,SAAS;QAAExC,KAAK,MAAM/B;OAArD,GACDrE,WACCR,OAAO4J,eAAeR,SAAS,UAAU;QAAExC,KAAK,MAAMpG;MAAb,CAAzC,CAJM;IAMX;AAED,QACEI,WAAWyI,kBAAkB7I,SAC7BiJ,QAAQ5D,kBAAkBqE,sBAC1B;AAEA,YAAMZ;IACP;AAED,QAAI1I,WAAWyI,kBAAkB7I,SAAS,CAAC+B,cAAc;AAEvD,YAAMkH,QAAQ5D;IACf;AAED,QAAIjF,WAAWyI,kBAAkB7I,OAAO;AAEtC,aAAO,oBAAC,aAAa,UAAd;QAAuB,OAAOiJ;QAAS,UAAUlH;OAAxD;IACD;AAED,QAAI3B,WAAWyI,kBAAkBM,SAAS;AAExC,aAAO,oBAAC,aAAa,UAAd;QAAuB,OAAOF;QAAS;OAA9C;IACD;AAGD,UAAMA;EACP;AAnFD;AA0FF,SAASU,aAIN,OAAA;AAAA,MAJmB;IACpB/H;MAGC;AACD,MAAIyC,OAAOY,cAAa;AACxB,MAAI2E,WAAW,OAAOhI,aAAa,aAAaA,SAASyC,IAAD,IAASzC;AACjE,SAAO,oBAAA,gBAAA,MAAGgI,QAAH;AACR;AAaM,SAASlB,yBACd9G,UACAnD,YACe;AAAA,MADfA,eACe,QAAA;AADfA,iBAAuB,CAAA;EACR;AACf,MAAIV,SAAwB,CAAA;AAE5BrC,EAAMmO,eAASC,QAAQlI,UAAU,CAACzC,SAASyD,UAAU;AACnD,QAAI,CAAOmH,qBAAe5K,OAArB,GAA+B;AAGlC;IACD;AAED,QAAI6K,WAAW,CAAC,GAAGvL,YAAYmE,KAAhB;AAEf,QAAIzD,QAAQ8K,SAAeC,gBAAU;AAEnCnM,aAAOX,KAAK+M,MACVpM,QACA2K,yBAAyBvJ,QAAQgC,MAAMS,UAAUoI,QAAzB,CAF1B;AAIA;IACD;AAED,MACE7K,QAAQ8K,SAASlC,SADnB7N,OAAAA,UAGI,OAAA,OAAA,OAAOiF,QAAQ8K,SAAS,WAAW9K,QAAQ8K,OAAO9K,QAAQ8K,KAAKG,QAH1D,wGAAA,IAATlQ,UAAA,KAAA,IAAA;AAOA,MACE,CAACiF,QAAQgC,MAAMyB,SAAS,CAACzD,QAAQgC,MAAMS,YADzC,OAAA1H,UAAS,OAEP,0CAFO,IAATA,UAAA,KAAA,IAAA;AAKA,QAAIsE,QAAqB;MACvB0D,IAAI/C,QAAQgC,MAAMe,MAAM8H,SAASxH,KAAK,GAAd;MACxB6H,eAAelL,QAAQgC,MAAMkJ;MAC7BlL,SAASA,QAAQgC,MAAMhC;MACvBE,WAAWF,QAAQgC,MAAM9B;MACzBuD,OAAOzD,QAAQgC,MAAMyB;MACrB5F,MAAMmC,QAAQgC,MAAMnE;MACpBsN,QAAQnL,QAAQgC,MAAMmJ;MACtB5C,QAAQvI,QAAQgC,MAAMuG;MACtB3F,cAAc5C,QAAQgC,MAAMY;MAC5BC,eAAe7C,QAAQgC,MAAMa;MAC7BuI,kBACEpL,QAAQgC,MAAMa,iBAAiB,QAC/B7C,QAAQgC,MAAMY,gBAAgB;MAChCyI,kBAAkBrL,QAAQgC,MAAMqJ;MAChClG,QAAQnF,QAAQgC,MAAMmD;MACtBmG,MAAMtL,QAAQgC,MAAMsJ;;AAGtB,QAAItL,QAAQgC,MAAMS,UAAU;AAC1BpD,YAAMoD,WAAW8G,yBACfvJ,QAAQgC,MAAMS,UACdoI,QAFuC;IAI1C;AAEDjM,WAAOX,KAAKoB,KAAZ;GAxDF;AA2DA,SAAOT;AACR;AAKM,SAAS2M,cACd/Q,SAC2B;AAC3B,SAAO4F,eAAe5F,OAAD;AACtB;AC/aD,SAASgR,mBAAmBnM,OAAoB;AAC9C,MAAIoM,UAAgE;;;IAGlEL,kBAAkB/L,MAAMwD,iBAAiB,QAAQxD,MAAMuD,gBAAgB;;AAGzE,MAAIvD,MAAMa,WAAW;AACnB,QAAa,MAAA;AACX,UAAIb,MAAMW,SAAS;AACjB,eAAArC,QACE,OACA,iGAFK,IAAP;MAKD;IACF;AACD0C,WAAOC,OAAOmL,SAAS;MACrBzL,SAAe0L,oBAAcrM,MAAMa,SAA1B;MACTA,WAAWD;KAFb;EAID;AAED,MAAIZ,MAAMwD,eAAe;AACvB,QAAa,MAAA;AACX,UAAIxD,MAAMuD,cAAc;AACtB,eAAAjF,QACE,OACA,8GAFK,IAAP;MAKD;IACF;AACD0C,WAAOC,OAAOmL,SAAS;MACrB7I,cAAoB8I,oBAAcrM,MAAMwD,aAA1B;MACdA,eAAe5C;KAFjB;EAID;AAED,SAAOwL;AACR;AAEM,SAASE,mBACd/M,QACA+I,MAOa;AACb,SAAOiE,aAAa;IAClB5Q,UAAU2M,QAAAA,OAAAA,SAAAA,KAAM3M;IAChB6Q,QACKlE,UAAAA,CAAAA,GAAAA,QAAAA,OAAAA,SAAAA,KAAMkE,QADL;MAEJC,oBAAoB;KAJJ;IAMlBxD,SAASF,oBAAoB;MAC3BH,gBAAgBN,QAAAA,OAAAA,SAAAA,KAAMM;MACtBC,cAAcP,QAAAA,OAAAA,SAAAA,KAAMO;IAFO,CAAD;IAI5B6D,eAAepE,QAAAA,OAAAA,SAAAA,KAAMoE;IACrBnN;IACA4M;GAZiB,EAahBQ,WAbI;AAcR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/QM,IAAMC,gBAAgC;AAC7C,IAAMC,iBAA8B;AAE9B,SAAUC,cAAcC,QAAW;AACvC,SAAOA,UAAU,QAAQ,OAAOA,OAAOC,YAAY;AACpD;AAEK,SAAUC,gBAAgBF,QAAW;AACzC,SAAOD,cAAcC,MAAD,KAAYA,OAAOC,QAAQE,YAAf,MAAiC;AAClE;AAEK,SAAUC,cAAcJ,QAAW;AACvC,SAAOD,cAAcC,MAAD,KAAYA,OAAOC,QAAQE,YAAf,MAAiC;AAClE;AAEK,SAAUE,eAAeL,QAAW;AACxC,SAAOD,cAAcC,MAAD,KAAYA,OAAOC,QAAQE,YAAf,MAAiC;AAClE;AAOD,SAASG,gBAAgBC,OAAwB;AAC/C,SAAO,CAAC,EAAEA,MAAMC,WAAWD,MAAME,UAAUF,MAAMG,WAAWH,MAAMI;AACnE;AAEe,SAAAC,uBACdL,OACAM,QAAe;AAEf,SACEN,MAAMO,WAAW;GAChB,CAACD,UAAUA,WAAW;EACvB,CAACP,gBAAgBC,KAAD;AAEnB;AA+Be,SAAAQ,mBACdC,MAA8B;AAAA,MAA9BA,SAA8B,QAAA;AAA9BA,WAA4B;EAAE;AAE9B,SAAO,IAAIC,gBACT,OAAOD,SAAS,YAChBE,MAAMC,QAAQH,IAAd,KACAA,gBAAgBC,kBACZD,OACAI,OAAOC,KAAKL,IAAZ,EAAkBM,OAAO,CAACC,MAAMC,QAAO;AACrC,QAAIC,QAAQT,KAAKQ,GAAD;AAChB,WAAOD,KAAKG,OACVR,MAAMC,QAAQM,KAAd,IAAuBA,MAAME,IAAKC,OAAM,CAACJ,KAAKI,CAAN,CAAjB,IAA6B,CAAC,CAACJ,KAAKC,KAAN,CAAD,CAD/C;KAGN,CAAA,CALH,CALC;AAYR;AAEe,SAAAI,2BACdC,gBACAC,qBAA2C;AAE3C,MAAIC,eAAejB,mBAAmBe,cAAD;AAErC,MAAIC,qBAAqB;AACvB,aAASP,OAAOO,oBAAoBV,KAApB,GAA4B;AAC1C,UAAI,CAACW,aAAaC,IAAIT,GAAjB,GAAuB;AAC1BO,4BAAoBG,OAAOV,GAA3B,EAAgCW,QAASV,WAAS;AAChDO,uBAAaI,OAAOZ,KAAKC,KAAzB;SADF;MAGD;IACF;EACF;AAED,SAAOO;AACR;SA0CeK,sBACdxB,QAQAyB,SACAC,UAAgB;AAOhB,MAAIC;AACJ,MAAIC,SAAwB;AAC5B,MAAIC;AACJ,MAAIC;AAEJ,MAAIvC,cAAcS,MAAD,GAAU;AACzB,QAAI+B,oBACFN,QACAM;AAEF,QAAIN,QAAQG,QAAQ;AAClBA,eAASH,QAAQG;IAClB,OAAM;AAIL,UAAII,OAAOhC,OAAOiC,aAAa,QAApB;AACXL,eAASI,OAAOE,cAAcF,MAAMN,QAAP,IAAmB;IACjD;AACDC,aAASF,QAAQE,UAAU3B,OAAOiC,aAAa,QAApB,KAAiCjD;AAC5D6C,cACEJ,QAAQI,WAAW7B,OAAOiC,aAAa,SAApB,KAAkChD;AAEvD6C,eAAW,IAAIK,SAASnC,MAAb;AAEX,QAAI+B,qBAAqBA,kBAAkBK,MAAM;AAC/CN,eAASP,OAAOQ,kBAAkBK,MAAML,kBAAkBnB,KAA1D;IACD;aAEDvB,gBAAgBW,MAAD,KACdR,eAAeQ,MAAD,MACZA,OAAOqC,SAAS,YAAYrC,OAAOqC,SAAS,UAC/C;AACA,QAAIC,OAAOtC,OAAOsC;AAElB,QAAIA,QAAQ,MAAM;AAChB,YAAM,IAAIC,MAAV,oEAAA;IAGD;AAID,QAAId,QAAQG,QAAQ;AAClBA,eAASH,QAAQG;IAClB,OAAM;AAIL,UAAII,OACFhC,OAAOiC,aAAa,YAApB,KAAqCK,KAAKL,aAAa,QAAlB;AACvCL,eAASI,OAAOE,cAAcF,MAAMN,QAAP,IAAmB;IACjD;AAEDC,aACEF,QAAQE,UACR3B,OAAOiC,aAAa,YAApB,KACAK,KAAKL,aAAa,QAAlB,KACAjD;AACF6C,cACEJ,QAAQI,WACR7B,OAAOiC,aAAa,aAApB,KACAK,KAAKL,aAAa,SAAlB,KACAhD;AAEF6C,eAAW,IAAIK,SAASG,IAAb;AAIX,QAAItC,OAAOoC,MAAM;AACfN,eAASP,OAAOvB,OAAOoC,MAAMpC,OAAOY,KAApC;IACD;EACF,WAAU1B,cAAcc,MAAD,GAAU;AAChC,UAAM,IAAIuC,MACR,oFADI;EAIP,OAAM;AACLZ,aAASF,QAAQE,UAAU3C;AAC3B4C,aAASH,QAAQG,UAAU;AAC3BC,cAAUJ,QAAQI,WAAW5C;AAE7B,QAAIe,kBAAkBmC,UAAU;AAC9BL,iBAAW9B;IACZ,OAAM;AACL8B,iBAAW,IAAIK,SAAJ;AAEX,UAAInC,kBAAkBI,iBAAiB;AACrC,iBAAS,CAACgC,MAAMxB,KAAP,KAAiBZ,QAAQ;AAChC8B,mBAASP,OAAOa,MAAMxB,KAAtB;QACD;MACF,WAAUZ,UAAU,MAAM;AACzB,iBAASoC,QAAQ7B,OAAOC,KAAKR,MAAZ,GAAqB;AACpC8B,mBAASP,OAAOa,MAAMpC,OAAOoC,IAAD,CAA5B;QACD;MACF;IACF;EACF;AAED,SAAO;IAAER;IAAQD,QAAQA,OAAOrC,YAAP;IAAsBuC;IAASC;;AACzD;;;;ACtDe,SAAAU,oBACdC,QACAC,MAAoB;AAEpB,SAAOC,aAAa;IAClBjB,UAAUgB,QAAAA,OAAAA,SAAAA,KAAMhB;IAChBkB,QACKF,UAAAA,CAAAA,GAAAA,QAAAA,OAAAA,SAAAA,KAAME,QADL;MAEJC,oBAAoB;KAJJ;IAMlBC,SAASC,qBAAqB;MAAEC,QAAQN,QAAAA,OAAAA,SAAAA,KAAMM;IAAhB,CAAD;IAC7BC,gBAAeP,QAAA,OAAA,SAAAA,KAAMO,kBAAiBC,mBAAkB;IACxDT;IACAU;GATiB,EAUhBC,WAVI;AAWR;AAEe,SAAAC,iBACdZ,QACAC,MAAoB;AAEpB,SAAOC,aAAa;IAClBjB,UAAUgB,QAAAA,OAAAA,SAAAA,KAAMhB;IAChBkB,QACKF,UAAAA,CAAAA,GAAAA,QAAAA,OAAAA,SAAAA,KAAME,QADL;MAEJC,oBAAoB;KAJJ;IAMlBC,SAASQ,kBAAkB;MAAEN,QAAQN,QAAAA,OAAAA,SAAAA,KAAMM;IAAhB,CAAD;IAC1BC,gBAAeP,QAAA,OAAA,SAAAA,KAAMO,kBAAiBC,mBAAkB;IACxDT;IACAU;GATiB,EAUhBC,WAVI;AAWR;AAED,SAASF,qBAAkB;AAAA,MAAA;AACzB,MAAIK,SAAK,UAAGP,WAAH,OAAA,SAAG,QAAQQ;AACpB,MAAID,SAASA,MAAME,QAAQ;AACzBF,YAAKG,UAAA,CAAA,GACAH,OADA;MAEHE,QAAQE,kBAAkBJ,MAAME,MAAP;KAF3B;EAID;AACD,SAAOF;AACR;AAED,SAASI,kBACPF,QAAsC;AAEtC,MAAI,CAACA;AAAQ,WAAO;AACpB,MAAIG,UAAUrD,OAAOqD,QAAQH,MAAf;AACd,MAAII,aAA6C,CAAA;AACjD,WAAS,CAAClD,KAAKmD,GAAN,KAAcF,SAAS;AAG9B,QAAIE,OAAOA,IAAIC,WAAW,sBAAsB;AAC9CF,iBAAWlD,GAAD,IAAQ,IAAIqD,cACpBF,IAAIG,QACJH,IAAII,YACJJ,IAAIK,MACJL,IAAIM,aAAa,IAJD;eAMTN,OAAOA,IAAIC,WAAW,SAAS;AACxC,UAAIM,QAAQ,IAAI9B,MAAMuB,IAAIQ,OAAd;AAGZD,YAAME,QAAQ;AACdV,iBAAWlD,GAAD,IAAQ0D;IACnB,OAAM;AACLR,iBAAWlD,GAAD,IAAQmD;IACnB;EACF;AACD,SAAOD;AACR;AAiBK,SAAUW,cAIK,MAAA;AAAA,MAJS;IAC5B9C;IACA+C;IACAzB,QAAAA;MACmB;AACnB,MAAI0B,aAAmBC,cAAN;AACjB,MAAID,WAAWE,WAAW,MAAM;AAC9BF,eAAWE,UAAU7B,qBAAqB;MAAEC,QAAAA;MAAQ6B,UAAU;IAApB,CAAD;EAC1C;AAED,MAAI/B,UAAU4B,WAAWE;AACzB,MAAI,CAACrB,OAAOuB,QAAR,IAA0BC,gBAAS;IACrCnD,QAAQkB,QAAQlB;IAChBoD,UAAUlC,QAAQkC;EAFmB,CAAf;AAKxBC,EAAMC,uBAAgB,MAAMpC,QAAQqC,OAAOL,QAAf,GAA0B,CAAChC,OAAD,CAAtD;AAEA,SACEsC,qBAACC,QAAM;IACL3D;IACA+C;IACAO,UAAUzB,MAAMyB;IAChBM,gBAAgB/B,MAAM3B;IACtB2D,WAAWzC;EALN,CAAP;AAQH;AAYK,SAAU0C,WAA0D,OAAA;AAAA,MAA/C;IAAE9D;IAAU+C;IAAUzB,QAAAA;MAAyB;AACxE,MAAI0B,aAAmBC,cAAN;AACjB,MAAID,WAAWE,WAAW,MAAM;AAC9BF,eAAWE,UAAUtB,kBAAkB;MAAEN,QAAAA;MAAQ6B,UAAU;IAApB,CAAD;EACvC;AAED,MAAI/B,UAAU4B,WAAWE;AACzB,MAAI,CAACrB,OAAOuB,QAAR,IAA0BC,gBAAS;IACrCnD,QAAQkB,QAAQlB;IAChBoD,UAAUlC,QAAQkC;EAFmB,CAAf;AAKxBC,EAAMC,uBAAgB,MAAMpC,QAAQqC,OAAOL,QAAf,GAA0B,CAAChC,OAAD,CAAtD;AAEA,SACEsC,qBAACC,QAAM;IACL3D;IACA+C;IACAO,UAAUzB,MAAMyB;IAChBM,gBAAgB/B,MAAM3B;IACtB2D,WAAWzC;EALN,CAAP;AAQH;AAcD,SAAS2C,cAAiE,OAAA;AAAA,MAAnD;IAAE/D;IAAU+C;IAAU3B;MAA6B;AACxE,QAAM,CAACS,OAAOuB,QAAR,IAA0BC,gBAAS;IACvCnD,QAAQkB,QAAQlB;IAChBoD,UAAUlC,QAAQkC;EAFqB,CAAf;AAK1BC,EAAMC,uBAAgB,MAAMpC,QAAQqC,OAAOL,QAAf,GAA0B,CAAChC,OAAD,CAAtD;AAEA,SACEsC,qBAACC,QAAM;IACL3D;IACA+C;IACAO,UAAUzB,MAAMyB;IAChBM,gBAAgB/B,MAAM3B;IACtB2D,WAAWzC;EALN,CAAP;AAQH;AAED,IAAa,MAAA;AACX2C,gBAAcC,cAAc;AAC7B;AAcD,IAAMC,aACJ,OAAO3C,WAAW,eAClB,OAAOA,OAAO4C,aAAa,eAC3B,OAAO5C,OAAO4C,SAASR,kBAAkB;AAE3C,IAAMS,sBAAqB;AAKpB,IAAMC,OAAaC,kBACxB,SAASC,YAYPC,OAAAA,KAAG;AAAA,MAXH;IACEC;IACAC;IACAC;IACAC;IACA9C;IACAvD;IACAsG;IACAC;MAGC,OAFEC,OAEF,8BAAA,OAAA,SAAA;AAEH,MAAI;IAAE9E;EAAF,IAAqB+E,kBAAWC,iBAAjB;AAGnB,MAAIC;AACJ,MAAIC,aAAa;AAEjB,MAAI,OAAON,OAAO,YAAYT,oBAAmBgB,KAAKP,EAAxB,GAA6B;AAEzDK,mBAAeL;AAGf,QAAIX,YAAW;AACb,UAAI;AACF,YAAImB,aAAa,IAAIC,IAAI/D,OAAOgC,SAASgC,IAAxB;AACjB,YAAIC,YAAYX,GAAGY,WAAW,IAAd,IACZ,IAAIH,IAAID,WAAWK,WAAWb,EAA9B,IACA,IAAIS,IAAIT,EAAR;AACJ,YAAIc,OAAOlF,cAAc+E,UAAUI,UAAU3F,QAArB;AAExB,YAAIuF,UAAUK,WAAWR,WAAWQ,UAAUF,QAAQ,MAAM;AAE1Dd,eAAKc,OAAOH,UAAUM,SAASN,UAAUO;QAC1C,OAAM;AACLZ,uBAAa;QACd;eACMa,GAAP;AAEA,eAAAC,QACE,OACA,eAAapB,KAAb,wGAFK,IAAP;MAKD;IACF;EACF;AAGD,MAAIU,OAAOW,QAAQrB,IAAI;IAAEH;EAAF,CAAL;AAElB,MAAIyB,kBAAkBC,oBAAoBvB,IAAI;IAC5CD;IACA9C;IACAvD;IACAuG;IACAJ;EAL4C,CAAL;AAOzC,WAAS2B,YACPpI,OAAsD;AAEtD,QAAIwG;AAASA,cAAQxG,KAAD;AACpB,QAAI,CAACA,MAAMqI,kBAAkB;AAC3BH,sBAAgBlI,KAAD;IAChB;EACF;AAED;;IAEE0F,qBAAA,KAAA1B,UAAA,CAAA,GACM8C,MADN;MAEEQ,MAAML,gBAAgBK;MACtBd,SAASU,cAAcR,iBAAiBF,UAAU4B;MAClD7B;MACAjG;IALF,CAAA,CAAA;;AAQH,CAhFiB;AAmFpB,IAAa,MAAA;AACX8F,OAAKJ,cAAc;AACpB;AA0BM,IAAMsC,UAAgBjC,kBAC3B,SAASkC,eAWPhC,OAAAA,KAAG;AAAA,MAVH;IACE,gBAAgBiC,kBAAkB;IAClCC,gBAAgB;IAChBC,WAAWC,gBAAgB;IAC3BC,MAAM;IACNC,OAAOC;IACPlC;IACA7B;MAGC,OAFE+B,OAEF,8BAAA,OAAA,UAAA;AAEH,MAAIY,OAAOqB,gBAAgBnC,IAAI;IAAEH,UAAUK,KAAKL;EAAjB,CAAL;AAC1B,MAAInB,WAAW0D,YAAW;AAC1B,MAAIC,cAAoBlC,kBAAWmC,sBAAjB;AAClB,MAAI;IAAErD;EAAF,IAAsBkB,kBAAWC,iBAAjB;AAEpB,MAAImC,aAAatD,UAAUuD,iBACvBvD,UAAUuD,eAAe1B,IAAzB,EAA+BC,WAC/BD,KAAKC;AACT,MAAI0B,mBAAmB/D,SAASqC;AAChC,MAAI2B,uBACFL,eAAeA,YAAYM,cAAcN,YAAYM,WAAWjE,WAC5D2D,YAAYM,WAAWjE,SAASqC,WAChC;AAEN,MAAI,CAACc,eAAe;AAClBY,uBAAmBA,iBAAiBzJ,YAAjB;AACnB0J,2BAAuBA,uBACnBA,qBAAqB1J,YAArB,IACA;AACJuJ,iBAAaA,WAAWvJ,YAAX;EACd;AAED,MAAI4J,WACFH,qBAAqBF,cACpB,CAACP,OACAS,iBAAiB7B,WAAW2B,UAA5B,KACAE,iBAAiBI,OAAON,WAAWO,MAAnC,MAA+C;AAEnD,MAAIC,YACFL,wBAAwB,SACvBA,yBAAyBH,cACvB,CAACP,OACAU,qBAAqB9B,WAAW2B,UAAhC,KACAG,qBAAqBG,OAAON,WAAWO,MAAvC,MAAmD;AAEzD,MAAIE,cAAcJ,WAAWhB,kBAAkBqB;AAE/C,MAAInB;AACJ,MAAI,OAAOC,kBAAkB,YAAY;AACvCD,gBAAYC,cAAc;MAAEa;MAAUG;IAAZ,CAAD;EAC1B,OAAM;AAMLjB,gBAAY,CACVC,eACAa,WAAW,WAAW,MACtBG,YAAY,YAAY,IAHd,EAKTG,OAAOC,OALE,EAMTC,KAAK,GANI;EAOb;AAED,MAAInB,QACF,OAAOC,cAAc,aACjBA,UAAU;IAAEU;IAAUG;GAAb,IACTb;AAEN,SACGpD,qBAAAU,MAADpC,UAAA,CAAA,GACM8C,MADN;IAEgB,gBAAA8C;IACdlB;IACAnC;IACAsC;IACAjC;EANF,CAAA,GAQG,OAAO7B,aAAa,aACjBA,SAAS;IAAEyE;IAAUG;GAAb,IACR5E,QAVN;AAaH,CAxFoB;AA2FvB,IAAa,MAAA;AACXuD,UAAQtC,cAAc;AACvB;AAoDM,IAAMiE,OAAa5D,kBACxB,CAAC6D,OAAO3D,QAAO;AACb,SAAOb,qBAACyE,UAADnG,UAAA,CAAA,GAAckG,OAAd;IAAqB3D;GAA5B,CAAA;AACD,CAHiB;AAMpB,IAAa,MAAA;AACX0D,OAAKjE,cAAc;AACpB;AAeD,IAAMmE,WAAiB9D,kBACrB,CAAA,OAaE+D,iBACE;AAAA,MAbF;IACE1D;IACAC;IACA1E,SAAS3C;IACT4C;IACAmI;IACAC;IACAC;IACA9D;IACAI;MAIA,OAHGqD,QAGH,8BAAA,OAAA,UAAA;AACF,MAAIM,SAASC,cAAcH,YAAYC,OAAb;AAC1B,MAAIG,aACFzI,OAAOrC,YAAP,MAAyB,QAAQ,QAAQ;AAC3C,MAAI+K,aAAaC,cAAc1I,QAAQ;IAAEuE;EAAF,CAAT;AAC9B,MAAIoE,gBAA0D7K,WAAS;AACrEqK,gBAAYA,SAASrK,KAAD;AACpB,QAAIA,MAAMqI;AAAkB;AAC5BrI,UAAM8K,eAAN;AAEA,QAAIC,YAAa/K,MAAqCgL,YACnDD;AAEH,QAAIE,gBACDF,aAAA,OAAA,SAAAA,UAAWxI,aAAa,YAAxB,MACDN;AAEFuI,WAAOO,aAAa/K,MAAMkL,eAAe;MACvCjJ,QAAQgJ;MACRtE;MACAF;MACAI;IAJuC,CAAnC;;AAQR,SACEnB,qBAAA,QAAA1B,UAAA;IACEuC,KAAK6D;IACLnI,QAAQyI;IACRxI,QAAQyI;IACRN,UAAU3D,iBAAiB2D,WAAWQ;EAJxC,GAKMX,KALN,CADF;AASD,CAjDc;AAoDjB,IAAa,MAAA;AACXC,WAASnE,cAAc;AACxB;SAWemF,kBAGS,OAAA;AAAA,MAHS;IAChCC;IACAC;MACuB;AACvBC,uBAAqB;IAAEF;IAAQC;EAAV,CAAD;AACpB,SAAO;AACR;AAED,IAAa,MAAA;AACXF,oBAAkBnF,cAAc;AACjC;AAOD,IAAKuF;CAAL,SAAKA,iBAAc;AACjBA,EAAAA,gBAAA,sBAAA,IAAA;AACAA,EAAAA,gBAAA,eAAA,IAAA;AACAA,EAAAA,gBAAA,YAAA,IAAA;AACD,GAJIA,oBAAAA,kBAIJ,CAAA,EAJD;AAMA,IAAKC;CAAL,SAAKA,sBAAmB;AACtBA,EAAAA,qBAAA,aAAA,IAAA;AACAA,EAAAA,qBAAA,sBAAA,IAAA;AACD,GAHIA,yBAAAA,uBAGJ,CAAA,EAHD;AAKA,SAASC,2BACPC,UAA8C;AAE9C,SAAUA,WAAV;AACD;AAED,SAASC,sBAAqBD,UAAwB;AACpD,MAAIE,MAAY7E,kBAAW8E,iBAAjB;AACV,GAAUD,MAAVE,OAAAA,UAAeL,OAAAA,2BAA0BC,QAAD,CAA/B,IAATI,UAAA,KAAA,IAAA;AACA,SAAOF;AACR;AAED,SAASG,oBAAmBL,UAA6B;AACvD,MAAI7H,QAAckD,kBAAWmC,sBAAjB;AACZ,GAAUrF,QAAViI,OAAAA,UAAiBL,OAAAA,2BAA0BC,QAAD,CAAjC,IAATI,UAAA,KAAA,IAAA;AACA,SAAOjI;AACR;SAOesE,oBACdvB,IAaM,OAAA;AAAA,MAZN;IACEtG;IACAqG,SAASqF;IACTnI;IACAgD;IACAJ;EALF,IAYM,UAAA,SAAF,CAAA,IAAE;AAEN,MAAIwF,WAAWC,YAAW;AAC1B,MAAI5G,WAAW0D,YAAW;AAC1B,MAAItB,OAAOqB,gBAAgBnC,IAAI;IAAEH;EAAF,CAAL;AAE1B,SAAa0F,mBACVnM,WAA0C;AACzC,QAAIK,uBAAuBL,OAAOM,MAAR,GAAiB;AACzCN,YAAM8K,eAAN;AAIA,UAAInE,UACFqF,gBAAgBnC,SACZmC,cACAI,WAAW9G,QAAD,MAAe8G,WAAW1E,IAAD;AAEzCuE,eAASrF,IAAI;QAAED;QAAS9C;QAAOgD;QAAoBJ;MAAtC,CAAL;IACT;KAEH,CACEnB,UACA2G,UACAvE,MACAsE,aACAnI,OACAvD,QACAsG,IACAC,oBACAJ,QATF,CAfK;AA2BR;AAMK,SAAU4F,gBACdC,aAAiC;AAEjC,SAAAtE,QACE,OAAOtH,oBAAoB,aAC3B,gcAFK,IAAP;AAYA,MAAI6L,yBAA+BtH,cAAOzE,mBAAmB8L,WAAD,CAA/B;AAC7B,MAAIE,wBAA8BvH,cAAO,KAAb;AAE5B,MAAIK,WAAW0D,YAAW;AAC1B,MAAIvH,eAAqBgL,eACvB;;;;IAIEnL,2BACEgE,SAASuC,QACT2E,sBAAsBtH,UAAU,OAAOqH,uBAAuBrH,OAFtC;KAI5B,CAACI,SAASuC,MAAV,CATiB;AAYnB,MAAIoE,WAAWC,YAAW;AAC1B,MAAIQ,kBAAwBP,mBAC1B,CAACQ,UAAUC,oBAAmB;AAC5B,UAAMC,kBAAkBrM,mBACtB,OAAOmM,aAAa,aAAaA,SAASlL,YAAD,IAAiBkL,QADlB;AAG1CH,0BAAsBtH,UAAU;AAChC+G,aAAS,MAAMY,iBAAiBD,eAAxB;EACT,GACD,CAACX,UAAUxK,YAAX,CARoB;AAWtB,SAAO,CAACA,cAAciL,eAAf;AACR;SA6CeI,YAAS;AACvB,SAAOrC,cAAa;AACrB;AAED,SAASA,cACPH,YACAyC,gBAAuB;AAEvB,MAAI;IAAEC;EAAF,IAAarB,sBAAqBJ,gBAAe0B,aAAhB;AACrC,MAAI;IAAEjL;EAAF,IAAqB+E,kBAAWC,iBAAjB;AACnB,MAAIkG,iBAAiBC,WAAU;AAE/B,SAAahB,mBACX,SAAC7L,QAAQyB,SAAgB;AAAA,QAAhBA,YAAgB,QAAA;AAAhBA,gBAAU,CAAA;IAAM;AACvB,QAAI,OAAOmE,aAAa,aAAa;AACnC,YAAM,IAAIrD,MACR,+GADI;IAIP;AAED,QAAI;MAAEX;MAAQD;MAAQE;MAASC;QAAaN,sBAC1CxB,QACAyB,SACAC,QAH+D;AAOjE,QAAIgB,OAAO;MACT6D,oBAAoB9E,QAAQ8E;MAC5BzE;MACAsI,YAAYzI;MACZmL,aAAajL;;AAGf,QAAImI,YAAY;AACd,QACEyC,kBAAkB,QADpBjB,OAAAA,UAAS,OAEP,uCAFO,IAATA,UAAA,KAAA,IAAA;AAIAkB,aAAOK,MAAM/C,YAAYyC,gBAAgB7K,QAAQc,IAAjD;IACD,OAAM;AACLgK,aAAOf,SAAS/J,QAAhB8B,UAAA,CAAA,GACKhB,MADL;QAEE2D,SAAS5E,QAAQ4E;QACjB2G,aAAaJ;MAHf,CAAA,CAAA;IAKD;EACF,GACD,CAACF,QAAQhL,UAAUsI,YAAYyC,gBAAgBG,cAA/C,CArCK;AAuCR;AAIK,SAAUtC,cACd1I,QACqD,QAAA;AAAA,MAArD;IAAEuE;EAAF,IAAqD,WAAA,SAAF,CAAA,IAAE;AAErD,MAAI;IAAEzE;EAAF,IAAqB+E,kBAAWC,iBAAjB;AACnB,MAAIuG,eAAqBxG,kBAAWyG,YAAjB;AACnB,GAAUD,eAAV,OAAAzB,UAAS,OAAe,kDAAf,IAATA,UAAA,KAAA,IAAA;AAEA,MAAI,CAAC2B,KAAD,IAAUF,aAAaG,QAAQC,MAAM,EAA3B;AAGd,MAAIjG,OAAI1D,UAAA,CAAA,GAAQ+E,gBAAgB7G,SAASA,SAAS,KAAK;IAAEuE;GAA1B,CAAvB;AAOR,MAAInB,WAAW0D,YAAW;AAC1B,MAAI9G,UAAU,MAAM;AAIlBwF,SAAKG,SAASvC,SAASuC;AACvBH,SAAKI,OAAOxC,SAASwC;AAKrB,QAAI2F,MAAMG,MAAMC,OAAO;AACrB,UAAIC,SAAS,IAAIpN,gBAAgBgH,KAAKG,MAAzB;AACbiG,aAAOC,OAAO,OAAd;AACArG,WAAKG,SAASiG,OAAOE,SAAP,IAAwBF,MAAAA,OAAOE,SAAP,IAAsB;IAC7D;EACF;AAED,OAAK,CAAC9L,UAAUA,WAAW,QAAQuL,MAAMG,MAAMC,OAAO;AACpDnG,SAAKG,SAASH,KAAKG,SACfH,KAAKG,OAAOlB,QAAQ,OAAO,SAA3B,IACA;EACL;AAMD,MAAI3E,aAAa,KAAK;AACpB0F,SAAKC,WACHD,KAAKC,aAAa,MAAM3F,WAAWiM,UAAU,CAACjM,UAAU0F,KAAKC,QAAhB,CAAD;EAC/C;AAED,SAAOyE,WAAW1E,IAAD;AAClB;AAED,SAASwG,kBAAkB5D,YAAoBC,SAAe;AAC5D,MAAI4D,cAAoB9H,kBACtB,CAAC6D,OAAO3D,QAAO;AACb,WACGb,qBAAAyE,UAADnG,UAAA,CAAA,GACMkG,OADN;MAEE3D;MACA+D;MACAC;KALJ,CAAA;EAQD,CAVe;AAYlB,MAAa,MAAA;AACX4D,gBAAYnI,cAAc;EAC3B;AACD,SAAOmI;AACR;AAED,IAAIC,YAAY;SAiBAC,aAAU;AAAA,MAAA;AACxB,MAAI;IAAErB;EAAF,IAAarB,sBAAqBJ,gBAAe+C,UAAhB;AAErC,MAAIV,QAAc7G,kBAAWyG,YAAjB;AACZ,GAAUI,QAAV9B,OAAAA,UAAA,OAAA,+CAAA,IAAAA,UAAA,KAAA,IAAA;AAEA,MAAIvB,WAAUqD,iBAAAA,MAAMF,QAAQE,MAAMF,QAAQhE,SAAS,CAArC,MAAH,OAAA,SAAG,eAAyCkE,MAAMW;AAC7D,IACEhE,WAAW,QADb,OAAAuB,UAAA,OAAA,kEAAA,IAAAA,UAAA,KAAA,IAAA;AAKA,MAAI,CAACxB,UAAD,IAAqBjF,gBAAS,MAAMmJ,OAAO,EAAEJ,SAAH,CAA3B;AACnB,MAAI,CAACnE,KAAD,IAAe5E,gBAAS,MAAK;AAC/B,KAAUkF,UAAVuB,OAAAA,UAAA,OAAA,yCAAA,IAAAA,UAAA,KAAA,IAAA;AACA,WAAOoC,kBAAkB5D,YAAYC,OAAb;EACzB,CAHY;AAIb,MAAI,CAACkE,IAAD,IAAepJ,gBAAS,MAAOiC,UAAgB;AACjD,KAAU0F,SAAV,OAAAlB,UAAS,OAAS,wCAAT,IAATA,UAAA,KAAA,IAAA;AACA,KAAUvB,UAAV,OAAAuB,UAAS,OAAU,yCAAV,IAATA,UAAA,KAAA,IAAA;AACAkB,WAAOK,MAAM/C,YAAYC,SAASjD,IAAlC;EACD,CAJY;AAKb,MAAIkD,SAASC,cAAcH,YAAYC,OAAb;AAE1B,MAAImE,UAAU1B,OAAO2B,WAAkBrE,UAAzB;AAEd,MAAIsE,wBAA8BnC,eAChC,MAAAzI,UAAA;IACEiG,MAAAA;IACAO;IACAiE;EAHF,GAIKC,OAJL,GAMA,CAACA,SAASzE,OAAMO,QAAQiE,IAAxB,CAP0B;AAU5BlJ,EAAMsJ,iBAAU,MAAK;AAInB,WAAO,MAAK;AACV,UAAI,CAAC7B,QAAQ;AACX8B,gBAAQC,KAAR,mDAAA;AACA;MACD;AACD/B,aAAOgC,cAAc1E,UAArB;;EAEH,GAAE,CAAC0C,QAAQ1C,UAAT,CAXH;AAaA,SAAOsE;AACR;SAMeK,cAAW;AACzB,MAAIpL,QAAQkI,oBAAmBP,qBAAoB0D,WAArB;AAC9B,SAAO,CAAC,GAAGrL,MAAMsL,SAASC,OAAf,CAAJ;AACR;AAED,IAAMC,iCAAiC;AACvC,IAAIC,uBAA+C,CAAA;AAKnD,SAAShE,qBAMH,QAAA;AAAA,MANwB;IAC5BF;IACAC;EAF4B,IAMxB,WAAA,SAAF,CAAA,IAAE;AACJ,MAAI;IAAE2B;EAAF,IAAarB,sBAAqBJ,gBAAegE,oBAAhB;AACrC,MAAI;IAAEC;IAAuB3I;EAAzB,IAAgDkF,oBAClDP,qBAAoB+D,oBADgD;AAGtE,MAAIjK,WAAW0D,YAAW;AAC1B,MAAI0E,UAAU+B,WAAU;AACxB,MAAIlG,aAAamG,cAAa;AAG9BnK,EAAMsJ,iBAAU,MAAK;AACnBvL,WAAOF,QAAQuM,oBAAoB;AACnC,WAAO,MAAK;AACVrM,aAAOF,QAAQuM,oBAAoB;;KAEpC,CAAA,CALH;AAQAC,cACQzD,mBAAY,MAAK;AACrB,QAAI5C,WAAW1F,UAAU,QAAQ;AAC/B,UAAI5C,OAAOmK,SAASA,OAAO9F,UAAUoI,OAAX,IAAsB,SAASpI,SAASrE;AAClEqO,2BAAqBrO,GAAD,IAAQqC,OAAOuM;IACpC;AACDC,mBAAeC,QACb1E,cAAcgE,gCACdW,KAAKC,UAAUX,oBAAf,CAFF;AAIAhM,WAAOF,QAAQuM,oBAAoB;EACpC,GAAE,CAACtE,YAAYD,QAAQ7B,WAAW1F,OAAOyB,UAAUoI,OAAjD,CAVH,CADS;AAeX,MAAI,OAAOxH,aAAa,aAAa;AAEnCX,IAAMC,uBAAgB,MAAK;AACzB,UAAI;AACF,YAAI0K,mBAAmBJ,eAAeK,QACpC9E,cAAcgE,8BADO;AAGvB,YAAIa,kBAAkB;AACpBZ,iCAAuBU,KAAKI,MAAMF,gBAAX;QACxB;MACF,SAAQnI,GAAP;MAED;IACF,GAAE,CAACsD,UAAD,CAXH;AAeA9F,IAAMC,uBAAgB,MAAK;AACzB,UAAI6K,2BAA2BrD,UAAAA,OAAAA,SAAAA,OAAQsD,wBACrChB,sBACA,MAAMhM,OAAOuM,SACbzE,MAH6B;AAK/B,aAAO,MAAMiF,4BAA4BA,yBAAwB;IAClE,GAAE,CAACrD,QAAQ5B,MAAT,CAPH;AAWA7F,IAAMC,uBAAgB,MAAK;AAEzB,UAAIgK,0BAA0B,OAAO;AACnC;MACD;AAGD,UAAI,OAAOA,0BAA0B,UAAU;AAC7ClM,eAAOiN,SAAS,GAAGf,qBAAnB;AACA;MACD;AAGD,UAAIlK,SAASwC,MAAM;AACjB,YAAI0I,KAAKtK,SAASuK,eAAenL,SAASwC,KAAK6F,MAAM,CAApB,CAAxB;AACT,YAAI6C,IAAI;AACNA,aAAGE,eAAH;AACA;QACD;MACF;AAGD,UAAI7J,uBAAuB,MAAM;AAC/B;MACD;AAGDvD,aAAOiN,SAAS,GAAG,CAAnB;IACD,GAAE,CAACjL,UAAUkK,uBAAuB3I,kBAAlC,CA5BH;EA6BD;AACF;AAYe,SAAA8J,gBACdC,UACA7O,SAA+B;AAE/B,MAAI;IAAE8O;MAAY9O,WAAW,CAAA;AAC7BwD,EAAMsJ,iBAAU,MAAK;AACnB,QAAI7L,OAAO6N,WAAW,OAAO;MAAEA;IAAF,IAAchH;AAC3CvG,WAAOwN,iBAAiB,gBAAgBF,UAAU5N,IAAlD;AACA,WAAO,MAAK;AACVM,aAAOyN,oBAAoB,gBAAgBH,UAAU5N,IAArD;;EAEH,GAAE,CAAC4N,UAAUC,OAAX,CANH;AAOD;AAUD,SAASjB,YACPgB,UACA7O,SAA+B;AAE/B,MAAI;IAAE8O;MAAY9O,WAAW,CAAA;AAC7BwD,EAAMsJ,iBAAU,MAAK;AACnB,QAAI7L,OAAO6N,WAAW,OAAO;MAAEA;IAAF,IAAchH;AAC3CvG,WAAOwN,iBAAiB,YAAYF,UAAU5N,IAA9C;AACA,WAAO,MAAK;AACVM,aAAOyN,oBAAoB,YAAYH,UAAU5N,IAAjD;;EAEH,GAAE,CAAC4N,UAAUC,OAAX,CANH;AAOD;AAUD,SAASG,UAA+D,OAAA;AAAA,MAArD;IAAEC;IAAMrM;MAA6C;AACtE,MAAIsM,UAAUC,WAAWF,IAAD;AAExB1L,EAAMsJ,iBAAU,MAAK;AACnB,QAAIqC,QAAQrN,UAAU,aAAa,CAACoN,MAAM;AACxCC,cAAQE,MAAR;IACD;EACF,GAAE,CAACF,SAASD,IAAV,CAJH;AAMA1L,EAAMsJ,iBAAU,MAAK;AACnB,QAAIqC,QAAQrN,UAAU,WAAW;AAC/B,UAAIwN,UAAU/N,OAAOgO,QAAQ1M,OAAf;AACd,UAAIyM,SAAS;AACXE,mBAAWL,QAAQG,SAAS,CAAlB;MACX,OAAM;AACLH,gBAAQE,MAAR;MACD;IACF;EACF,GAAE,CAACF,SAAStM,OAAV,CATH;AAUD;", "names": ["Action", "PopStateEventType", "createMemoryHistory", "options", "initialEntries", "initialIndex", "v5Compat", "entries", "map", "entry", "index", "createMemoryLocation", "state", "undefined", "clampIndex", "length", "action", "Pop", "listener", "n", "Math", "min", "max", "getCurrentLocation", "to", "key", "location", "createLocation", "pathname", "warning", "char<PERSON>t", "JSON", "stringify", "createHref", "createPath", "history", "createURL", "URL", "encodeLocation", "path", "parsePath", "search", "hash", "push", "<PERSON><PERSON>", "nextLocation", "splice", "delta", "replace", "Replace", "go", "nextIndex", "listen", "fn", "createBrowserHistory", "createBrowserLocation", "window", "globalHistory", "usr", "createBrowserHref", "getUrlBasedHistory", "createHashHistory", "createHashLocation", "substr", "createHashHref", "base", "document", "querySelector", "href", "getAttribute", "url", "hashIndex", "indexOf", "slice", "validateHashLocation", "invariant", "value", "message", "Error", "cond", "console", "warn", "e", "create<PERSON><PERSON>", "random", "toString", "getHistoryState", "idx", "current", "parsed<PERSON><PERSON>", "searchIndex", "getLocation", "validateLocation", "defaultView", "getIndex", "replaceState", "handlePop", "historyState", "pushState", "error", "assign", "origin", "addEventListener", "removeEventListener", "ResultType", "immutableRouteKeys", "Set", "isIndexRoute", "route", "convertRoutesToDataRoutes", "routes", "mapRouteProperties", "parentPath", "manifest", "treePath", "id", "join", "children", "indexRoute", "pathOrLayoutRoute", "matchRoutes", "locationArg", "basename", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "matches", "i", "matchRouteBranch", "safelyDecodeURI", "parents<PERSON>eta", "flattenRoute", "relativePath", "meta", "caseSensitive", "childrenIndex", "startsWith", "joinPaths", "routesMeta", "concat", "score", "computeScore", "for<PERSON>ach", "includes", "exploded", "explodeOptionalSegments", "segments", "split", "first", "rest", "isOptional", "endsWith", "required", "restExploded", "result", "subpath", "sort", "a", "b", "compareIndexes", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "initialScore", "some", "filter", "reduce", "segment", "test", "siblings", "every", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "match", "matchPath", "Object", "params", "pathnameBase", "normalizePathname", "generatePath", "originalPath", "prefix", "array", "isLastSegment", "star", "starParam", "keyMatch", "optional", "param", "pattern", "matcher", "paramNames", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "safelyDecodeURIComponent", "regexpSource", "_", "RegExp", "decodeURI", "decodeURIComponent", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "getInvalidPathError", "char", "field", "dest", "getPathContributingMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "isEmptyPath", "from", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "joinPaths", "paths", "join", "replace", "normalizePathname", "pathname", "normalizeSearch", "search", "startsWith", "normalizeHash", "hash", "json", "data", "init", "responseInit", "status", "headers", "Headers", "has", "set", "Response", "JSON", "stringify", "Aborted<PERSON>eferredError", "Error", "DeferredData", "constructor", "pendingKeysSet", "Set", "subscribers", "deferred<PERSON><PERSON><PERSON>", "invariant", "Array", "isArray", "reject", "abortPromise", "Promise", "_", "r", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "signal", "removeEventListener", "addEventListener", "Object", "entries", "reduce", "acc", "key", "value", "assign", "trackPromise", "done", "push", "add", "promise", "race", "then", "onSettle", "error", "catch", "defineProperty", "get", "aborted", "delete", "emit", "<PERSON><PERSON><PERSON>", "for<PERSON>ach", "subscriber", "subscribe", "fn", "cancel", "abort", "v", "k", "resolveData", "resolve", "size", "unwrappedData", "unwrapTrackedPromise", "<PERSON><PERSON><PERSON><PERSON>", "from", "isTrackedPromise", "_tracked", "_error", "_data", "defer", "redirect", "url", "ErrorResponse", "statusText", "internal", "toString", "isRouteErrorResponse", "validMutationMethodsArr", "validMutationMethods", "validRequestMethodsArr", "validRequestMethods", "redirectStatusCodes", "redirectPreserveMethodStatusCodes", "IDLE_NAVIGATION", "state", "location", "undefined", "formMethod", "formAction", "formEncType", "formData", "IDLE_FETCHER", "IDLE_BLOCKER", "proceed", "reset", "ABSOLUTE_URL_REGEX", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "createElement", "isServer", "defaultMapRouteProperties", "route", "hasErrorBou<PERSON>ry", "Boolean", "createRouter", "routes", "length", "mapRouteProperties", "detectErrorBoundary", "manifest", "dataRoutes", "convertRoutesToDataRoutes", "inFlightDataRoutes", "basename", "future", "v7_normalizeFormMethod", "v7_prependBasename", "unlistenHistory", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "hydrationData", "initialMatches", "matchRoutes", "history", "initialErrors", "getInternalRouterError", "matches", "getShortCircuitMatches", "id", "initialized", "some", "m", "lazy", "loader", "router", "historyAction", "action", "navigation", "restoreScrollPosition", "preventScrollReset", "revalidation", "loaderData", "actionData", "errors", "fetchers", "Map", "blockers", "pendingAction", "HistoryAction", "Pop", "pendingPreventScrollReset", "pendingNavigationController", "isUninterruptedRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "fetchRedirectIds", "fetchLoadMatches", "activeDeferreds", "blockerFunctions", "ignoreNextHistoryUpdate", "initialize", "listen", "delta", "warning", "blockerKey", "shouldBlockNavigation", "currentLocation", "nextLocation", "go", "updateBlocker", "deleteBlocker", "updateState", "startNavigation", "dispose", "clear", "deleteFetcher", "newState", "completeNavigation", "isActionReload", "isMutationMethod", "_isRedirect", "keys", "mergeLoaderData", "getSavedScrollPosition", "<PERSON><PERSON>", "Replace", "navigate", "to", "opts", "normalizedPath", "normalizeTo", "fromRouteId", "relative", "path", "submission", "normalizeNavigateOptions", "createLocation", "encodeLocation", "userReplace", "pendingError", "revalidate", "interruptActiveLoads", "startUninterruptedRevalidation", "overrideNavigation", "saveScrollPosition", "routesToUse", "loadingNavigation", "notFoundMatches", "cancelActiveDeferreds", "isHashChangeOnly", "request", "createClientSideRequest", "pendingActionData", "findNearestBoundary", "actionOutput", "handleAction", "shortCircuited", "pendingActionError", "Request", "handleLoaders", "fetcherSubmission", "result", "actionMatch", "getTargetMatch", "type", "ResultType", "method", "routeId", "callLoaderOrAction", "isRedirectResult", "startRedirectNavigation", "isErrorResult", "boundaryMatch", "isDeferredResult", "activeSubmission", "matchesToLoad", "revalidatingFetchers", "getMatchesToLoad", "updatedFetchers", "markFetchRedirectsDone", "rf", "fetcher", "revalidatingFetcher", "abortPendingFetchRevalidations", "f", "abort<PERSON><PERSON><PERSON>", "results", "loaderResults", "fetcherResults", "callLoadersAndMaybeResolveData", "findRedirect", "processLoaderData", "deferredData", "didAbortFetchLoads", "abortStaleFetchLoads", "shouldUpdateFetchers", "getFetcher", "fetch", "href", "setFetcherError", "match", "handleFetcherAction", "handleFetcherLoader", "requestMatches", "existingFetcher", "abortController", "fetchRequest", "actionResult", "loadingFetcher", "isFetchActionRedirect", "revalidationRequest", "loadId", "loadFetcher", "filter", "staleKey", "done<PERSON>etcher", "resolveDeferredData", "redirectLocation", "_isFetchActionRedirect", "test", "createURL", "isDifferentBasename", "stripBasename", "origin", "redirectHistoryAction", "currentMatches", "fetchersToLoad", "all", "map", "slice", "resolveDeferredResults", "markFetchersDone", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "get<PERSON><PERSON>er", "blocker", "newBlocker", "blockerFunction", "predicate", "cancelledRouteIds", "dfd", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "y", "userMatches", "createUseMatchesMatch", "_internalSetRoutes", "newRoutes", "createHref", "_internalFetchControllers", "_internalActiveDeferreds", "UNSAFE_DEFERRED_SYMBOL", "Symbol", "isSubmissionNavigation", "opts", "normalizeTo", "location", "matches", "basename", "prependBasename", "to", "fromRouteId", "relative", "contextualMatches", "activeRouteMatch", "match", "push", "route", "id", "length", "path", "resolveTo", "getPathContributingMatches", "map", "m", "pathnameBase", "stripBasename", "pathname", "search", "hash", "index", "hasNakedIndexQuery", "replace", "joinPaths", "createPath", "normalizeNavigateOptions", "normalizeFormMethod", "isFetcher", "formMethod", "isValidMethod", "error", "getInternalRouterError", "method", "submission", "formData", "toUpperCase", "toLowerCase", "formAction", "stripHashFromPath", "formEncType", "isMutationMethod", "parsed<PERSON><PERSON>", "parsePath", "searchParams", "convertFormDataToSearchParams", "append", "getLoaderMatchesUntilBoundary", "boundaryId", "boundaryMatches", "findIndex", "slice", "getMatchesToLoad", "history", "state", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "fetchLoadMatches", "routesToUse", "pendingActionData", "pendingError", "actionResult", "Object", "values", "undefined", "currentUrl", "createURL", "nextUrl", "keys", "navigationMatches", "filter", "lazy", "loader", "is<PERSON>ew<PERSON><PERSON>der", "loaderData", "some", "currentRouteMatch", "nextRouteMatch", "shouldRevalidateLoader", "currentParams", "params", "nextParams", "defaultShouldRevalidate", "isNewRouteInstance", "revalidatingFetchers", "for<PERSON>ach", "f", "key", "routeId", "fetcherMatches", "matchRoutes", "controller", "fetcherMatch", "getTargetMatch", "includes", "AbortController", "shouldRevalidate", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "currentPath", "endsWith", "loaderMatch", "arg", "routeChoice", "loadLazyRouteModule", "mapRouteProperties", "manifest", "lazyRoute", "routeToUpdate", "invariant", "routeUpdates", "lazyRouteProperty", "staticRouteValue", "isPropertyStaticallyDefined", "warning", "immutableRouteKeys", "has", "assign", "callLoaderOrAction", "type", "request", "isStaticRequest", "isRouteRequest", "requestContext", "resultType", "result", "onReject", "<PERSON><PERSON><PERSON><PERSON>", "handler", "reject", "abortPromise", "Promise", "_", "r", "signal", "addEventListener", "race", "context", "all", "url", "URL", "ResultType", "data", "e", "removeEventListener", "isResponse", "status", "redirectStatusCodes", "headers", "get", "ABSOLUTE_URL_REGEX", "test", "indexOf", "startsWith", "protocol", "isSameBasename", "origin", "set", "redirect", "revalidate", "response", "contentType", "json", "text", "ErrorResponse", "statusText", "statusCode", "isDeferredData", "deferred", "deferredData", "init", "Headers", "createClientSideRequest", "toString", "body", "Request", "URLSearchParams", "value", "entries", "File", "name", "processRouteLoaderData", "matchesToLoad", "results", "activeDeferreds", "errors", "found<PERSON><PERSON>r", "loaderHeaders", "isRedirectResult", "isErrorResult", "boundaryMatch", "findNearestBoundary", "isRouteErrorResponse", "isDeferredResult", "processLoaderData", "fetcherResults", "aborted", "fetchers", "delete", "done<PERSON>etcher", "mergeLoaderData", "newLoaderData", "mergedLoaderData", "hasOwnProperty", "eligibleMatches", "reverse", "find", "hasErrorBou<PERSON>ry", "getShortCircuitMatches", "routes", "errorMessage", "Error", "findRedirect", "i", "isHashChangeOnly", "a", "b", "subscribe", "cancel", "resolveData", "isValidMethod", "method", "validRequestMethods", "has", "toLowerCase", "isMutationMethod", "validMutationMethods", "resolveDeferredResults", "currentMatches", "matchesToLoad", "results", "signals", "isFetcher", "currentLoaderData", "index", "length", "result", "match", "currentMatch", "find", "m", "route", "id", "isRevalidatingLoader", "isNewRouteInstance", "undefined", "isDeferredResult", "signal", "invariant", "resolveDeferredData", "then", "unwrap", "aborted", "deferredData", "resolveData", "type", "ResultType", "data", "unwrappedData", "e", "error", "hasNakedIndexQuery", "search", "URLSearchParams", "getAll", "some", "v", "createUseMatchesMatch", "loaderData", "pathname", "params", "handle", "getTargetMatch", "matches", "location", "parsePath", "pathMatches", "getPathContributingMatches", "DataRouterContext", "createContext", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "isDataRoute", "RouteErrorContext", "useHref", "to", "relative", "useInRouterContext", "invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "navigateEffectWarning", "useIsomorphicLayoutEffect", "cb", "isStatic", "static", "React", "useLayoutEffect", "useNavigate", "useNavigateStable", "useNavigateUnstable", "dataRouterContext", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getPathContributingMatches", "map", "match", "pathnameBase", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "useParams", "routeMatch", "length", "params", "useRoutes", "routes", "locationArg", "useRoutesImpl", "dataRouterState", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "slice", "matchRoutes", "element", "undefined", "Component", "renderedMatches", "_renderMatches", "Object", "assign", "encodeLocation", "_extends", "key", "NavigationType", "Pop", "DefaultErrorComponent", "error", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "console", "fontStyle", "defaultErrorElement", "RenderErrorBoundary", "constructor", "props", "revalidation", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "component", "children", "RenderedRoute", "staticContext", "errorElement", "Error<PERSON>ou<PERSON><PERSON>", "_deepestRenderedBoundaryId", "id", "errors", "errorIndex", "findIndex", "m", "keys", "join", "Math", "min", "reduceRight", "index", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useRouteId", "UseRouteId", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "useMatches", "loaderData", "UseMatches", "data", "handle", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "values", "actionData", "UseRouteError", "useAsyncValue", "value", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "useState", "String", "blockerFunction", "args", "blocker", "get<PERSON><PERSON>er", "useEffect", "deleteBlocker", "blockers", "get", "UseNavigateStable", "fromRouteId", "alreadyWarned", "cond", "RouterProvider", "fallbackElement", "setState", "subscribe", "n", "opts", "preventScrollReset", "historyAction", "initialized", "DataRoutes", "MemoryRouter", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "action", "listen", "Navigate", "jsonPath", "Outlet", "Route", "_props", "Router", "basenameProp", "locationProp", "staticProp", "navigationContext", "locationContext", "trailingPathname", "stripBasename", "Routes", "createRoutesFromChildren", "Await", "resolve", "AwaitRenderStatus", "neverSettledPromise", "Promise", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "promise", "pending", "success", "defineProperty", "renderError", "reject", "catch", "_tracked", "then", "Aborted<PERSON>eferredError", "ResolveAwait", "to<PERSON><PERSON>", "Children", "for<PERSON>ach", "isValidElement", "treePath", "type", "Fragment", "apply", "name", "caseSensitive", "loader", "hasErrorBou<PERSON>ry", "shouldRevalidate", "lazy", "renderMatches", "mapRouteProperties", "updates", "createElement", "createMemoryRouter", "createRouter", "future", "v7_prependBasename", "hydrationData", "initialize", "defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "has", "getAll", "for<PERSON>ach", "append", "getFormSubmissionInfo", "options", "basename", "method", "action", "encType", "formData", "submissionTrigger", "attr", "getAttribute", "stripBasename", "FormData", "name", "type", "form", "Error", "createBrowserRouter", "routes", "opts", "createRouter", "future", "v7_prependBasename", "history", "createBrowserHistory", "window", "hydrationData", "parseHydrationData", "mapRouteProperties", "initialize", "createHashRouter", "createHashHistory", "state", "__staticRouterHydrationData", "errors", "_extends", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponse", "status", "statusText", "data", "internal", "error", "message", "stack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "historyRef", "useRef", "current", "v5Compat", "setState", "useState", "location", "React", "useLayoutEffect", "listen", "createElement", "Router", "navigationType", "navigator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HistoryRouter", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "document", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "LinkWithRef", "ref", "onClick", "relative", "reloadDocument", "replace", "to", "preventScrollReset", "rest", "useContext", "NavigationContext", "absoluteHref", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "e", "warning", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "NavLink", "NavLinkWithRef", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "useResolvedPath", "useLocation", "routerState", "DataRouterStateContext", "toPathname", "encodeLocation", "locationPathname", "nextLocationPathname", "navigation", "isActive", "char<PERSON>t", "length", "isPending", "aria<PERSON>urrent", "undefined", "filter", "Boolean", "join", "Form", "props", "FormImpl", "forwardedRef", "onSubmit", "fetcher<PERSON>ey", "routeId", "submit", "useSubmitImpl", "formMethod", "formAction", "useFormAction", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "DataRouterContext", "invariant", "useDataRouterState", "replaceProp", "navigate", "useNavigate", "useCallback", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "useMemo", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "useSubmit", "fetcherRouteId", "router", "UseSubmitImpl", "currentRouteId", "useRouteId", "formEncType", "fetch", "fromRouteId", "routeContext", "RouteContext", "match", "matches", "slice", "route", "index", "params", "delete", "toString", "joinPaths", "createFetcherForm", "FetcherForm", "fetcherId", "useFetcher", "UseFetcher", "id", "String", "load", "fetcher", "getFetcher", "fetcherWithComponents", "useEffect", "console", "warn", "deleteFetcher", "useFetchers", "UseFetchers", "fetchers", "values", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "when", "blocker", "useBlocker", "reset", "proceed", "confirm", "setTimeout"]}