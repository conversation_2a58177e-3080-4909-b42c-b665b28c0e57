import {
  DEFAULT_THEME,
  getColor,
  hideVisually,
  math,
  retrieveComponentStyles
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import {
  composeEventHandlers
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  Ae,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-tables/dist/index.esm.js
var React = __toESM(require_react());
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
function _extends$3() {
  _extends$3 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$3.apply(this, arguments);
}
var COMPONENT_ID$b = "tables.body";
var StyledBody = styled_components_browser_esm_default.tbody.attrs({
  "data-garden-id": COMPONENT_ID$b,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledBody",
  componentId: "sc-14ud6y-0"
})(["", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$b, props));
StyledBody.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$a = "tables.caption";
var StyledCaption = styled_components_browser_esm_default.caption.attrs({
  "data-garden-id": COMPONENT_ID$a,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledCaption",
  componentId: "sc-113y327-0"
})(["display:table-caption;text-align:", ";", ";"], (props) => props.theme.rtl ? "right" : "left", (props) => retrieveComponentStyles(COMPONENT_ID$a, props));
StyledCaption.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$9 = "tables.table";
var getLineHeight = (props) => {
  return `${props.theme.space.base * 5}px`;
};
var StyledTable = styled_components_browser_esm_default.table.attrs({
  "data-garden-id": COMPONENT_ID$9,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledTable",
  componentId: "sc-gje7na-0"
})(["display:table;border:none;width:100%;table-layout:fixed;border-collapse:collapse;border-spacing:0;line-height:", ";color:", ";font-size:", ";direction:", ";", ";"], (props) => getLineHeight(props), (props) => props.theme.colors.foreground, (props) => props.theme.fontSizes.md, (props) => props.theme.rtl && "rtl", (props) => retrieveComponentStyles(COMPONENT_ID$9, props));
StyledTable.defaultProps = {
  theme: DEFAULT_THEME
};
var getRowHeight = (props) => {
  if (props.size === "large") {
    return `${props.theme.space.base * 16}px`;
  } else if (props.size === "small") {
    return `${props.theme.space.base * 8}px`;
  }
  return `${props.theme.space.base * 10}px`;
};
var COMPONENT_ID$8 = "tables.cell";
var truncatedStyling$1 = Ae(["overflow:hidden;text-overflow:ellipsis;white-space:nowrap;"]);
var sizeStyling = (props) => {
  let boxSizing = "border-box";
  let padding;
  let width = props.width;
  let height;
  if (props.hasOverflow) {
    boxSizing = "content-box";
    width = "2em";
    height = "inherit";
    padding = props.theme.rtl ? `0 0 0 ${props.theme.space.base}px` : `0 ${props.theme.space.base}px 0 0`;
  } else {
    const paddingVertical = math(`(${getRowHeight(props)} - ${getLineHeight(props)}) / 2`);
    const paddingHorizontal = `${props.theme.space.base * 3}px`;
    padding = `${paddingVertical} ${paddingHorizontal}`;
  }
  if (props.isMinimum) {
    boxSizing = "content-box";
    width = "1em";
  }
  return Ae(["box-sizing:", ";padding:", ";width:", ";height:", ";"], boxSizing, padding, width, height);
};
var StyledCell = styled_components_browser_esm_default.td.attrs({
  "data-garden-id": COMPONENT_ID$8,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledCell",
  componentId: "sc-8hpncx-0"
})(["display:table-cell;transition:border-color 0.25s ease-in-out,box-shadow 0.1s ease-in-out;", ";", ";", ";"], (props) => sizeStyling(props), (props) => props.isTruncated && truncatedStyling$1, (props) => retrieveComponentStyles(COMPONENT_ID$8, props));
StyledCell.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$7 = "tables.overflow_button";
var OVERFLOW_BUTTON_SIZE = "2em";
var colorStyles$1 = (props) => {
  const boxShadow = props.theme.shadows.md(getColor("primaryHue", 600, props.theme, 0.35));
  const hoverBackgroundColor = getColor("primaryHue", 600, props.theme, 0.08);
  const hoverForegroundColor = getColor("neutralHue", 700, props.theme);
  const activeBackgroundColor = getColor("primaryHue", 600, props.theme, 0.2);
  const activeForegroundColor = getColor("neutralHue", 800, props.theme);
  let foregroundColor;
  if (props.isHovered) {
    foregroundColor = hoverForegroundColor;
  } else if (props.isActive) {
    foregroundColor = activeForegroundColor;
  } else {
    foregroundColor = getColor("neutralHue", 600, props.theme);
  }
  return Ae(["color:", ";&:hover{background-color:", ";color:", ";}&:active{background-color:", ";color:", ";}&:focus{outline:none;}&[data-garden-focus-visible]{box-shadow:inset ", ";}"], foregroundColor, hoverBackgroundColor, hoverForegroundColor, activeBackgroundColor, activeForegroundColor, boxShadow);
};
var StyledOverflowButton = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$7,
  "data-garden-version": "8.67.0",
  type: "button"
}).withConfig({
  displayName: "StyledOverflowButton",
  componentId: "sc-1eba2ml-0"
})(["display:block;transition:opacity 0.25s ease-in-out,background-color 0.1s ease-in-out;opacity:", ";z-index:", ";margin-top:calc(", " - 1em);border:none;border-radius:50%;background-color:transparent;cursor:pointer;padding:0;width:100%;height:", ";text-decoration:none;font-size:inherit;", " &[aria-expanded='true']{opacity:1;}", ";"], (props) => props.isHovered || props.isFocused || props.isActive ? "1" : "0", (props) => props.isActive ? "1" : "0", (props) => math(`${getRowHeight(props)} / 2`), OVERFLOW_BUTTON_SIZE, (props) => colorStyles$1(props), (props) => retrieveComponentStyles(COMPONENT_ID$7, props));
StyledOverflowButton.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledOverflowButtonIconWrapper = styled_components_browser_esm_default.div.withConfig({
  displayName: "StyledOverflowButton__StyledOverflowButtonIconWrapper",
  componentId: "sc-1eba2ml-1"
})(["display:flex;align-items:center;justify-content:center;transform:rotate(90deg);transition:opacity 0.25s ease-in-out,background-color 0.1s ease-in-out;width:", ";height:", ";&:hover{opacity:1;}"], OVERFLOW_BUTTON_SIZE, OVERFLOW_BUTTON_SIZE);
StyledOverflowButtonIconWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$6 = "tables.row";
var StyledBaseRow = styled_components_browser_esm_default.tr.withConfig({
  displayName: "StyledRow__StyledBaseRow",
  componentId: "sc-ek66ow-0"
})(["display:table-row;transition:background-color 0.1s ease-in-out;border-bottom:", ";background-color:", ";vertical-align:top;box-sizing:border-box;"], (props) => `${props.theme.borders.sm} ${getColor("neutralHue", 200, props.theme)}`, (props) => props.isStriped && getColor("neutralHue", 100, props.theme));
StyledBaseRow.defaultProps = {
  theme: DEFAULT_THEME
};
var colorStyles = (props) => {
  const boxShadow = `inset ${props.theme.rtl ? "-" : ""}${props.theme.shadowWidths.md} 0 0 0 ${getColor("primaryHue", 600, props.theme)}`;
  const hoveredBackgroundColor = getColor("primaryHue", 600, props.theme, 0.08);
  const hoveredBorderColor = getColor("primaryHue", 200, props.theme);
  const selectedBackgroundColor = getColor("primaryHue", 600, props.theme, 0.2);
  const selectedBorderColor = getColor("primaryHue", 300, props.theme);
  const hoveredSelectedBackgroundColor = getColor("primaryHue", 600, props.theme, 0.28);
  let backgroundColor = void 0;
  let borderColor = void 0;
  let hoverBorderBottomColor = void 0;
  let hoverBackgroundColor = void 0;
  if (props.isSelected) {
    if (props.isHovered) {
      backgroundColor = hoveredSelectedBackgroundColor;
    } else {
      backgroundColor = selectedBackgroundColor;
    }
    borderColor = selectedBorderColor;
    hoverBorderBottomColor = selectedBorderColor;
    hoverBackgroundColor = hoveredSelectedBackgroundColor;
  } else if (props.isHovered) {
    backgroundColor = hoveredBackgroundColor;
    borderColor = hoveredBorderColor;
  } else if (!props.isReadOnly) {
    hoverBorderBottomColor = hoveredBorderColor;
    hoverBackgroundColor = hoveredBackgroundColor;
  }
  return Ae(["border-bottom-color:", ";background-color:", ";&:hover{border-bottom-color:", ";background-color:", ";", "{opacity:1;}}&:focus{outline:none;}", ":first-of-type{box-shadow:", ";&:focus{box-shadow:", ";}}"], borderColor, backgroundColor, hoverBorderBottomColor, hoverBackgroundColor, StyledOverflowButton, StyledCell, props.isFocused && boxShadow, boxShadow);
};
var StyledRow = styled_components_browser_esm_default(StyledBaseRow).attrs((props) => ({
  "data-garden-id": COMPONENT_ID$6,
  "data-garden-version": "8.67.0",
  tabIndex: props.isReadOnly ? void 0 : -1
})).withConfig({
  displayName: "StyledRow",
  componentId: "sc-ek66ow-1"
})(["height:", ";", " ", ";"], getRowHeight, (props) => colorStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID$6, props));
StyledRow.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$5 = "tables.header_row";
var getHeaderRowHeight = (props) => {
  if (props.size === "large") {
    return `${props.theme.space.base * 18}px`;
  } else if (props.size === "small") {
    return `${props.theme.space.base * 10}px`;
  }
  return `${props.theme.space.base * 12}px`;
};
var StyledHeaderRow = styled_components_browser_esm_default(StyledBaseRow).attrs({
  "data-garden-id": COMPONENT_ID$5,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledHeaderRow",
  componentId: "sc-16ogvdx-0"
})(["border-bottom-color:", ";height:", ";vertical-align:bottom;font-weight:", ";", "{opacity:1;margin-top:0;margin-bottom:calc(", " - 1em);}", ";"], (props) => getColor("neutralHue", 300, props.theme), getHeaderRowHeight, (props) => props.theme.fontWeights.semibold, StyledOverflowButton, (props) => math(`${getHeaderRowHeight(props)} / 2`), (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledHeaderRow.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$4 = "tables.head";
var stickyStyles = (props) => {
  const borderColor = getColor("neutralHue", 300, props.theme);
  return Ae(["position:sticky;top:0;z-index:1;box-shadow:inset 0 -", " 0 ", ";background-color:", ";& > ", ":last-child{border-bottom-color:transparent;}"], props.theme.borderWidths.sm, borderColor, props.theme.colors.background, StyledHeaderRow);
};
var StyledHead = styled_components_browser_esm_default.thead.attrs({
  "data-garden-id": COMPONENT_ID$4,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledHead",
  componentId: "sc-spf23a-0"
})(["", " ", ";"], (props) => props.isSticky && stickyStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledHead.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "tables.group_row";
var sizeStyles$1 = (props) => {
  const height = `${props.theme.space.base * 8}px`;
  const lineHeight = getLineHeight(props);
  return Ae(["height:", ";line-height:", ";font-size:", ";", "{padding:", " ", "px;}"], height, lineHeight, props.theme.fontSizes.sm, StyledCell, math(`(${height} - ${lineHeight}) / 2`), props.theme.space.base * 3);
};
var StyledGroupRow = styled_components_browser_esm_default(StyledBaseRow).attrs({
  "data-garden-id": COMPONENT_ID$3,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledGroupRow",
  componentId: "sc-mpd0r8-0"
})(["background-color:", ";", " ", ";"], (props) => getColor("neutralHue", 100, props.theme), (props) => sizeStyles$1(props), (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledGroupRow.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "tables.sortable";
var StyledBaseIconWrapper = styled_components_browser_esm_default.div.withConfig({
  displayName: "StyledSortableButton__StyledBaseIconWrapper",
  componentId: "sc-2s1dli-0"
})(["display:flex;position:absolute;top:0;", ":0;align-items:center;justify-content:center;opacity:0;width:", ";height:100%;color:inherit;fill:inherit;"], (props) => props.theme.rtl ? "left" : "right", (props) => props.theme.iconSizes.sm);
StyledBaseIconWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledSortableStrokeIconWrapper = styled_components_browser_esm_default(StyledBaseIconWrapper).withConfig({
  displayName: "StyledSortableButton__StyledSortableStrokeIconWrapper",
  componentId: "sc-2s1dli-1"
})([""]);
StyledSortableStrokeIconWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledSortableFillIconWrapper = styled_components_browser_esm_default(StyledBaseIconWrapper).withConfig({
  displayName: "StyledSortableButton__StyledSortableFillIconWrapper",
  componentId: "sc-2s1dli-2"
})([""]);
StyledSortableFillIconWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledSortableButton = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.67.0",
  type: "button"
}).withConfig({
  displayName: "StyledSortableButton",
  componentId: "sc-2s1dli-3"
})(["position:relative;border:none;background-color:transparent;cursor:pointer;padding:0;padding-", ":", ";width:", ";text-decoration:none;color:inherit;font-family:inherit;font-size:inherit;font-weight:", ";", "{opacity:", ";}", "{opacity:", ";color:", ";fill:", ";}&:hover,&[data-garden-focus-visible]{text-decoration:none;color:", ";", ";", " ", "}&:focus{outline:none;}", ";"], (props) => props.theme.rtl ? "left" : "right", (props) => math(`${props.theme.space.base} + ${props.theme.iconSizes.sm}`), (props) => props.width, (props) => props.theme.fontWeights.semibold, StyledSortableStrokeIconWrapper, (props) => props.sort === void 0 && 1, StyledSortableFillIconWrapper, (props) => props.sort !== void 0 && 1, (props) => {
  if (props.sort === "asc") {
    return getColor("neutralHue", 600, props.theme);
  } else if (props.sort === "desc") {
    return getColor("neutralHue", 400, props.theme);
  }
  return void 0;
}, (props) => {
  if (props.sort === "asc") {
    return getColor("neutralHue", 400, props.theme);
  } else if (props.sort === "desc") {
    return getColor("neutralHue", 600, props.theme);
  }
  return void 0;
}, (props) => getColor("primaryHue", 600, props.theme), (props) => props.sort === void 0 && `
      ${StyledSortableFillIconWrapper} {
        opacity: 1;
        color: ${getColor("primaryHue", 600, props.theme)};
        fill: ${getColor("primaryHue", 600, props.theme)};
      }

      ${StyledSortableStrokeIconWrapper} {
        opacity: 0;
      }
    `, (props) => props.sort === "asc" && `
      ${StyledSortableFillIconWrapper} {
        color: ${getColor("primaryHue", 600, props.theme)};
        fill: ${getColor("primaryHue", 600, props.theme, 0.25)};
      }
    `, (props) => props.sort === "desc" && `
      ${StyledSortableFillIconWrapper} {
        color: ${getColor("primaryHue", 600, props.theme, 0.25)};
        fill: ${getColor("primaryHue", 600, props.theme)};
      }
    `, (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledSortableButton.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "tables.header_cell";
var truncatedStyling = Ae(["", "{max-width:100%;overflow:hidden;text-overflow:ellipsis;}"], StyledSortableButton);
var sizeStyles = (props) => {
  let paddingVertical = void 0;
  if (!props.hasOverflow) {
    paddingVertical = math(`(${getRowHeight(props)} - ${getLineHeight(props)}) / 2`);
  }
  return Ae(["padding-top:", ";padding-bottom:", ";"], paddingVertical, paddingVertical);
};
var StyledHeaderCell = styled_components_browser_esm_default(StyledCell).attrs({
  as: "th",
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledHeaderCell",
  componentId: "sc-fzagoe-0"
})(["text-align:", ";font-weight:inherit;", " ", " ", ";"], (props) => {
  if (!props.hasOverflow) {
    if (props.theme.rtl) {
      return "right";
    }
    return "left";
  }
  return void 0;
}, (props) => sizeStyles(props), (props) => props.isTruncated && truncatedStyling, (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledHeaderCell.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "tables.hidden_cell";
var StyledHiddenCell = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledHiddenCell",
  componentId: "sc-1x454xw-0"
})(["", " ", ";"], hideVisually(), (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledHiddenCell.defaultProps = {
  theme: DEFAULT_THEME
};
var Body = (0, import_react.forwardRef)((props, ref) => import_react.default.createElement(StyledBody, _extends$3({
  ref
}, props)));
Body.displayName = "Body";
var Caption = (0, import_react.forwardRef)((props, ref) => import_react.default.createElement(StyledCaption, _extends$3({
  ref
}, props)));
Caption.displayName = "Caption";
var TableContext = import_react.default.createContext({
  size: "medium",
  isReadOnly: false
});
var useTableContext = () => {
  return (0, import_react.useContext)(TableContext);
};
var Cell = import_react.default.forwardRef((_ref, ref) => {
  let {
    hidden,
    ...props
  } = _ref;
  const {
    size
  } = useTableContext();
  return import_react.default.createElement(StyledCell, _extends$3({
    ref,
    size
  }, props), hidden && props.children ? import_react.default.createElement(StyledHiddenCell, null, props.children) : props.children);
});
Cell.displayName = "Cell";
Cell.propTypes = {
  isMinimum: import_prop_types.default.bool,
  isTruncated: import_prop_types.default.bool,
  hasOverflow: import_prop_types.default.bool,
  width: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number])
};
var GroupRow = (0, import_react.forwardRef)((props, ref) => {
  const {
    size
  } = useTableContext();
  return import_react.default.createElement(StyledGroupRow, _extends$3({
    ref,
    size
  }, props));
});
GroupRow.displayName = "GroupRow";
var Head = (0, import_react.forwardRef)((props, ref) => import_react.default.createElement(StyledHead, _extends$3({
  ref
}, props)));
Head.displayName = "Head";
var HeaderCell = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    hidden,
    ...props
  } = _ref;
  const {
    size
  } = useTableContext();
  return import_react.default.createElement(StyledHeaderCell, _extends$3({
    ref,
    size
  }, props), hidden && props.children ? import_react.default.createElement(StyledHiddenCell, null, props.children) : props.children);
});
HeaderCell.displayName = "HeaderCell";
HeaderCell.propTypes = Cell.propTypes;
var HeaderRow = import_react.default.forwardRef((props, ref) => {
  const {
    size
  } = useTableContext();
  return import_react.default.createElement(StyledHeaderRow, _extends$3({
    ref,
    size
  }, props));
});
HeaderRow.displayName = "HeaderRow";
var _g;
function _extends$2() {
  _extends$2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$2.apply(this, arguments);
}
var SvgOverflowStroke = function SvgOverflowStroke2(props) {
  return React.createElement("svg", _extends$2({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _g || (_g = React.createElement("g", {
    fill: "currentColor"
  }, React.createElement("circle", {
    cx: 2.5,
    cy: 8,
    r: 1.5
  }), React.createElement("circle", {
    cx: 8,
    cy: 8,
    r: 1.5
  }), React.createElement("circle", {
    cx: 13.5,
    cy: 8,
    r: 1.5
  }))));
};
var OverflowButton = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    onFocus,
    onBlur,
    isFocused: focused,
    ...other
  } = _ref;
  const [isFocused, setIsFocused] = (0, import_react.useState)(false);
  const {
    size
  } = useTableContext();
  return import_react.default.createElement(StyledOverflowButton, _extends$3({
    onFocus: composeEventHandlers(onFocus, () => {
      setIsFocused(true);
    }),
    onBlur: composeEventHandlers(onBlur, () => {
      setIsFocused(false);
    }),
    size,
    isFocused: typeof focused === "undefined" ? isFocused : focused,
    ref
  }, other), import_react.default.createElement(StyledOverflowButtonIconWrapper, null, import_react.default.createElement(SvgOverflowStroke, null)));
});
OverflowButton.displayName = "OverflowButton";
OverflowButton.propTypes = {
  isHovered: import_prop_types.default.bool,
  isActive: import_prop_types.default.bool,
  isFocused: import_prop_types.default.bool
};
var Row = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    onFocus,
    onBlur,
    isFocused: focused,
    ...otherProps
  } = _ref;
  const [isFocused, setIsFocused] = (0, import_react.useState)(false);
  const {
    size,
    isReadOnly
  } = useTableContext();
  const computedFocused = (0, import_react.useMemo)(() => {
    if (typeof focused !== "undefined") {
      return focused;
    }
    if (isReadOnly) {
      return false;
    }
    return isFocused;
  }, [focused, isFocused, isReadOnly]);
  const onFocusCallback = (0, import_react.useMemo)(() => composeEventHandlers(onFocus, () => {
    setIsFocused(true);
  }), [onFocus, setIsFocused]);
  const onBlurCallback = (0, import_react.useMemo)(() => composeEventHandlers(onBlur, () => {
    setIsFocused(false);
  }), [onBlur, setIsFocused]);
  return import_react.default.createElement(StyledRow, _extends$3({
    onFocus: onFocusCallback,
    onBlur: onBlurCallback,
    size,
    isReadOnly,
    isFocused: computedFocused,
    ref
  }, otherProps));
});
Row.displayName = "Row";
Row.propTypes = {
  isStriped: import_prop_types.default.bool,
  isFocused: import_prop_types.default.bool,
  isHovered: import_prop_types.default.bool,
  isSelected: import_prop_types.default.bool
};
var _path$1;
function _extends$1() {
  _extends$1 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$1.apply(this, arguments);
}
var SvgSortStroke = function SvgSortStroke2(props) {
  return React.createElement("svg", _extends$1({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$1 || (_path$1 = React.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M2.5 4L5.6.9c.2-.2.5-.2.7 0L9.5 4m-7 4l3.1 3.1c.2.2.5.2.7 0L9.5 8"
  })));
};
var _path;
var _path2;
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var SvgSortFill = function SvgSortFill2(props) {
  return React.createElement("svg", _extends({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path || (_path = React.createElement("path", {
    fill: "currentColor",
    d: "M10 5H2a.5.5 0 01-.46-.31.47.47 0 01.11-.54L5.29.5A1 1 0 016.7.5l3.65 3.65a.49.49 0 01.11.54A.51.51 0 0110 5z"
  })), _path2 || (_path2 = React.createElement("path", {
    d: "M2 7a.5.5 0 00-.46.31.47.47 0 00.11.54L5.3 11.5a1 1 0 001.41 0l3.65-3.65a.49.49 0 00.11-.54A.53.53 0 0010 7z"
  })));
};
var SIZE = ["small", "medium", "large"];
var SORT = ["asc", "desc"];
var SortableCell = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    sort,
    cellProps,
    width,
    children,
    ...otherProps
  } = _ref;
  let ariaSortValue = "none";
  if (sort === "asc") {
    ariaSortValue = "ascending";
  } else if (sort === "desc") {
    ariaSortValue = "descending";
  }
  const SortIcon = sort === void 0 ? SvgSortStroke : SvgSortFill;
  return import_react.default.createElement(StyledHeaderCell, _extends$3({
    "aria-sort": ariaSortValue,
    width
  }, cellProps), import_react.default.createElement(StyledSortableButton, _extends$3({
    sort,
    ref
  }, otherProps), children, import_react.default.createElement(StyledSortableStrokeIconWrapper, null, import_react.default.createElement(SortIcon, null)), import_react.default.createElement(StyledSortableFillIconWrapper, null, import_react.default.createElement(SvgSortFill, null))));
});
SortableCell.displayName = "SortableCell";
SortableCell.propTypes = {
  sort: import_prop_types.default.oneOf(SORT),
  cellProps: import_prop_types.default.any,
  width: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number])
};
var Table = import_react.default.forwardRef((props, ref) => {
  const tableContextValue = (0, import_react.useMemo)(() => ({
    size: props.size,
    isReadOnly: props.isReadOnly
  }), [props.size, props.isReadOnly]);
  return import_react.default.createElement(TableContext.Provider, {
    value: tableContextValue
  }, import_react.default.createElement(StyledTable, _extends$3({
    ref
  }, props)));
});
Table.displayName = "Table";
Table.defaultProps = {
  size: "medium"
};
Table.propTypes = {
  size: import_prop_types.default.oneOf(SIZE),
  isReadOnly: import_prop_types.default.bool
};
export {
  Body,
  Caption,
  Cell,
  GroupRow,
  Head,
  HeaderCell,
  HeaderRow,
  OverflowButton,
  Row,
  SortableCell,
  Table
};
//# sourceMappingURL=@zendeskgarden_react-tables.js.map
