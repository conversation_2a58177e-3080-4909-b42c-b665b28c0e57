import {
  DEFAULT_THEME,
  getColor,
  getLineHeight,
  math,
  retrieveComponentStyles,
  useText
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  Ae,
  We,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-avatars/dist/index.esm.js
var React = __toESM(require_react());
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
function _extends$4() {
  _extends$4 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$4.apply(this, arguments);
}
var _g$1;
function _extends$3() {
  _extends$3 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$3.apply(this, arguments);
}
var SvgClockStroke$1 = function SvgClockStroke(props) {
  return React.createElement("svg", _extends$3({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _g$1 || (_g$1 = React.createElement("g", {
    fill: "none",
    stroke: "currentColor"
  }, React.createElement("circle", {
    cx: 6,
    cy: 6,
    r: 5.5
  }), React.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5.5 3v3.5H8"
  }))));
};
var _g;
function _extends$2() {
  _extends$2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$2.apply(this, arguments);
}
var SvgClockStroke2 = function SvgClockStroke3(props) {
  return React.createElement("svg", _extends$2({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _g || (_g = React.createElement("g", {
    fill: "none",
    stroke: "currentColor"
  }, React.createElement("circle", {
    cx: 8,
    cy: 8,
    r: 7.5
  }), React.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7.5 3v5.5H11"
  }))));
};
var _path$1;
function _extends$1() {
  _extends$1 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$1.apply(this, arguments);
}
var SvgArrowLeftSmStroke$1 = function SvgArrowLeftSmStroke(props) {
  return React.createElement("svg", _extends$1({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$1 || (_path$1 = React.createElement("path", {
    fill: "currentColor",
    d: "M2.146 6.854a.5.5 0 0 1 0-.708l2-2a.5.5 0 1 1 .708.708L3.707 6H9.5a.5.5 0 0 1 0 1H3.707l1.147 1.146a.5.5 0 1 1-.708.708l-2-2Z"
  })));
};
var _path;
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var SvgArrowLeftSmStroke2 = function SvgArrowLeftSmStroke3(props) {
  return React.createElement("svg", _extends({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path || (_path = React.createElement("path", {
    fill: "currentColor",
    d: "M3.146 8.854a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L4.707 8H12.5a.5.5 0 0 1 0 1H4.707l2.147 2.146a.5.5 0 1 1-.708.707l-3-3Z"
  })));
};
var SIZE = ["extraextrasmall", "extrasmall", "small", "medium", "large"];
var STATUS = ["available", "away", "transfers", "offline"];
var COMPONENT_ID$6 = "avatars.text";
var StyledText = styled_components_browser_esm_default.span.attrs({
  "data-garden-id": COMPONENT_ID$6,
  "data-garden-version": "8.69.3"
}).withConfig({
  displayName: "StyledText",
  componentId: "sc-1a6hivh-0"
})(["overflow:hidden;text-align:center;white-space:nowrap;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$6, props));
StyledText.defaultProps = {
  theme: DEFAULT_THEME
};
var [xxs$1, xs$1, s$1, m$1, l$1] = SIZE;
var TRANSITION_DURATION = 0.25;
function getStatusColor(type, theme) {
  switch (type) {
    case "active":
      return getColor("crimson", 400, theme);
    case "available":
      return getColor("mint", 400, theme);
    case "away":
      return getColor("orange", 400, theme);
    case "transfers":
      return getColor("azure", 400, theme);
    case "offline":
      return getColor("grey", 500, theme);
    default:
      return "transparent";
  }
}
function getStatusBorderOffset(props) {
  return props.size === xxs$1 ? math(`${props.theme.shadowWidths.sm} - 1`) : props.theme.shadowWidths.sm;
}
function getStatusSize(props, offset) {
  const isActive = props.type === "active";
  switch (props.size) {
    case xxs$1:
      return math(`${props.theme.space.base}px - ${offset}`);
    case xs$1:
      return math(`${props.theme.space.base * 2}px - (${offset} * 2)`);
    case s$1:
      return math(`${props.theme.space.base * 3}px ${isActive ? "" : `- (${offset} * 2)`}`);
    case m$1:
    case l$1:
      return math(`${props.theme.space.base * 4}px ${isActive ? "" : `- (${offset} * 2)`}`);
    default:
      return "0";
  }
}
function includes(array, element) {
  return array.includes(element);
}
var COMPONENT_ID$5 = "avatars.status-indicator.base";
var iconFadeIn = We(["0%{opacity:0;}100%{opacity:1;}"]);
var sizeStyles$3 = (props) => {
  const offset = getStatusBorderOffset(props);
  const size = getStatusSize(props, offset);
  return Ae(["border:", " ", ";border-radius:", ";width:", ";min-width:", ";height:", ";line-height:", ";& > svg{position:absolute;top:-", ";left:-", ";transform-origin:50% 50%;animation:", " ", "s;max-height:unset;&[data-icon-status='transfers']{transform:scale(", ",1);}&[data-icon-status='away'] circle{display:none;}}"], offset, props.theme.borderStyles.solid, size, size, size, size, size, offset, offset, iconFadeIn, TRANSITION_DURATION, props.theme.rtl ? -1 : 1);
};
var colorStyles$2 = (props) => {
  let backgroundColor = getStatusColor(props.type, props.theme);
  let borderColor = backgroundColor;
  if (props.type === "offline") {
    borderColor = getStatusColor(props.type, props.theme);
    backgroundColor = props.theme.palette.white;
  }
  return Ae(["border-color:", ";background-color:", ";color:", ";"], borderColor, backgroundColor, props.theme.palette.white);
};
var StyledStatusIndicatorBase = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$5,
  "data-garden-version": "8.69.3"
}).withConfig({
  displayName: "StyledStatusIndicatorBase",
  componentId: "sc-1rininy-0"
})(["transition:inherit;", " ", " ", ";"], sizeStyles$3, colorStyles$2, (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledStatusIndicatorBase.defaultProps = {
  theme: DEFAULT_THEME,
  size: "small"
};
var COMPONENT_ID$4 = "avatars.status_indicator";
var [xxs, xs, s, m, l] = SIZE;
var sizeStyles$2 = (props) => {
  const isVisible = !includes([xxs, xs], props.size);
  const borderWidth = getStatusBorderOffset(props);
  let padding = "0";
  if (props.size === s) {
    padding = math(`${props.theme.space.base + 1}px - (${borderWidth} * 2)`);
  } else if (includes([m, l], props.size)) {
    padding = math(`${props.theme.space.base + 3}px - (${borderWidth} * 2)`);
  }
  return Ae(["max-width:calc(2em + (", " * 3));box-sizing:content-box;overflow:hidden;text-align:center;text-overflow:ellipsis;white-space:nowrap;font-size:", ";font-weight:", ";& > span{display:", ";padding:0 ", ";max-width:2em;overflow:inherit;text-align:inherit;text-overflow:inherit;white-space:inherit;}& > svg{", "}"], borderWidth, props.theme.fontSizes.xs, props.theme.fontWeights.semibold, isVisible ? "inline-block" : "none", padding, !isVisible && "display: none;");
};
var colorStyles$1 = (props) => {
  const {
    theme,
    type,
    size,
    borderColor,
    surfaceColor
  } = props;
  let boxShadow = theme.shadows.sm(surfaceColor || (type ? theme.colors.background : theme.palette.white));
  if (size === xxs) {
    boxShadow = boxShadow.replace(theme.shadowWidths.sm, "1px");
  }
  return Ae(["border-color:", ";box-shadow:", ";"], borderColor, boxShadow);
};
var StyledStatusIndicator = styled_components_browser_esm_default(StyledStatusIndicatorBase).attrs({
  "data-garden-id": COMPONENT_ID$4,
  "data-garden-version": "8.69.3"
}).withConfig({
  displayName: "StyledStatusIndicator",
  componentId: "sc-16t9if3-0"
})(["", " ", " ", ";"], sizeStyles$2, colorStyles$1, (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledStatusIndicator.defaultProps = {
  size: "medium",
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "avatars.avatar";
var badgeStyles = (props) => {
  const [xxs2, xs2, s2, m2, l2] = SIZE;
  let position = `${props.theme.space.base * -1}px`;
  switch (props.size) {
    case s2:
    case m2:
      position = math(`${position}  + 2`);
      break;
    case xxs2:
    case xs2:
    case l2:
      position = math(`${position}  + 3`);
      break;
  }
  const animation = We(["0%{transform:scale(.1);}"]);
  return Ae(["position:absolute;", ":", ";bottom:", ";transition:all ", "s ease-in-out;", ""], props.theme.rtl ? "left" : "right", position, position, TRANSITION_DURATION, props.status === "active" && Ae(["animation:", " ", "s ease-in-out;"], animation, TRANSITION_DURATION * 1.5));
};
var colorStyles = (props) => {
  const statusColor = getStatusColor(props.status, props.theme);
  const backgroundColor = props.backgroundColor || "transparent";
  const foregroundColor = props.foregroundColor || props.theme.palette.white;
  const surfaceColor = props.status ? props.surfaceColor || props.theme.colors.background : "transparent";
  return Ae(["box-shadow:", ";background-color:", ";color:", ";& > svg,& ", "{color:", ";}"], props.theme.shadows.sm(statusColor), backgroundColor, surfaceColor, StyledText, foregroundColor);
};
var sizeStyles$1 = (props) => {
  let boxShadow;
  let borderRadius;
  let size;
  let fontSize;
  let svgSize;
  if (props.size === "extraextrasmall") {
    boxShadow = `0 0 0 ${math(`${props.theme.shadowWidths.sm} - 1`)}`;
    borderRadius = props.isSystem ? math(`${props.theme.borderRadii.md} - 1`) : "50%";
    size = `${props.theme.space.base * 4}px`;
    fontSize = 0;
    svgSize = `${props.theme.space.base * 3}px`;
  } else if (props.size === "extrasmall") {
    boxShadow = `inset 0 0 0 ${props.theme.shadowWidths.sm}`;
    borderRadius = props.isSystem ? math(`${props.theme.borderRadii.md} - 1`) : "50%";
    size = `${props.theme.space.base * 6}px`;
    fontSize = props.theme.fontSizes.sm;
    svgSize = `${props.theme.space.base * 3}px`;
  } else if (props.size === "small") {
    boxShadow = `inset 0 0 0 ${props.theme.shadowWidths.sm}`;
    borderRadius = props.isSystem ? math(`${props.theme.borderRadii.md} - 1`) : "50%";
    size = `${props.theme.space.base * 8}px`;
    fontSize = props.theme.fontSizes.md;
    svgSize = `${props.theme.space.base * 3}px`;
  } else if (props.size === "large") {
    boxShadow = `inset 0 0 0 ${props.theme.shadowWidths.sm}`;
    borderRadius = props.isSystem ? math(`${props.theme.borderRadii.md} + 1`) : "50%";
    size = `${props.theme.space.base * 12}px`;
    fontSize = props.theme.fontSizes.xl;
    svgSize = `${props.theme.space.base * 6}px`;
  } else {
    boxShadow = `inset 0 0 0 ${props.theme.shadowWidths.sm}`;
    borderRadius = props.isSystem ? props.theme.borderRadii.md : "50%";
    size = `${props.theme.space.base * 10}px`;
    fontSize = props.theme.fontSizes.lg;
    svgSize = `${props.theme.space.base * 4}px`;
  }
  return Ae(["border-radius:", ";width:", " !important;height:", " !important;::before{box-shadow:", ";}& > svg{font-size:", ";}& ", "{line-height:", ";font-size:", ";}"], borderRadius, size, size, boxShadow, svgSize, StyledText, size, fontSize);
};
var StyledAvatar = styled_components_browser_esm_default.figure.attrs({
  "data-garden-id": COMPONENT_ID$3,
  "data-garden-version": "8.69.3"
}).withConfig({
  displayName: "StyledAvatar",
  componentId: "sc-608m04-0"
})(["display:inline-flex;position:relative;align-items:center;justify-content:center;transition:box-shadow ", "s ease-in-out,color 0.1s ease-in-out;margin:0;vertical-align:middle;box-sizing:border-box;", ";", ";&::before{position:absolute;top:0;left:0;transition:box-shadow ", "s ease-in-out;content:'';}&::before,&& > img{border-radius:inherit;width:100%;height:100%;}&& > img{box-sizing:inherit;vertical-align:bottom;object-fit:cover;}&& > svg{width:1em;height:1em;}& > ", "{", ";}", ";"], TRANSITION_DURATION, (props) => sizeStyles$1(props), (props) => colorStyles(props), TRANSITION_DURATION, StyledStatusIndicator, badgeStyles, (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledAvatar.defaultProps = {
  size: "medium",
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "avatars.status-indicator.status";
var StyledStandaloneStatus = styled_components_browser_esm_default.figure.attrs({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.69.3"
}).withConfig({
  displayName: "StyledStandaloneStatus",
  componentId: "sc-1ow4nfj-0"
})(["display:inline-flex;flex-flow:row nowrap;transition:all ", "s ease-in-out;margin:0;box-sizing:content-box;", ";"], TRANSITION_DURATION, (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledStandaloneStatus.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "avatars.status-indicator.caption";
function sizeStyles(props) {
  const marginRule = `margin-${props.theme.rtl ? "right" : "left"}: ${props.theme.space.base * 2}px;`;
  return Ae(["", " line-height:", ";font-size:", ";"], marginRule, getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md), props.theme.fontSizes.md);
}
var StyledStandaloneStatusCaption = styled_components_browser_esm_default.figcaption.attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.69.3"
}).withConfig({
  displayName: "StyledStandaloneStatusCaption",
  componentId: "sc-aalyk1-0"
})(["", " ", ";"], sizeStyles, (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledStandaloneStatusCaption.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "avatars.status-indicator.indicator";
var StyledStandaloneStatusIndicator = styled_components_browser_esm_default(StyledStatusIndicatorBase).attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.69.3"
}).withConfig({
  displayName: "StyledStandaloneStatusIndicator",
  componentId: "sc-1xt1heq-0"
})(["position:relative;box-sizing:content-box;margin-top:", ";", ";"], (props) => `calc((${props.theme.lineHeights.md} - ${getStatusSize(props, "0")}) / 2)`, (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledStandaloneStatusIndicator.defaultProps = {
  type: "offline",
  theme: DEFAULT_THEME
};
var TextComponent = (0, import_react.forwardRef)((props, ref) => import_react.default.createElement(StyledText, _extends$4({
  ref
}, props)));
TextComponent.displayName = "Avatar.Text";
var Text = TextComponent;
var AvatarComponent = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    isSystem,
    size,
    status,
    children,
    badge,
    surfaceColor,
    backgroundColor,
    foregroundColor,
    ...props
  } = _ref;
  const computedStatus = badge === void 0 ? status : "active";
  let ClockIcon = SvgClockStroke$1;
  let ArrowLeftIcon = SvgArrowLeftSmStroke$1;
  if (["large", "medium"].includes(size)) {
    ClockIcon = SvgClockStroke2;
    ArrowLeftIcon = SvgArrowLeftSmStroke2;
  }
  const defaultStatusLabel = (0, import_react.useMemo)(() => {
    let statusMessage = computedStatus;
    if (computedStatus === "active") {
      const count = typeof badge === "string" ? parseInt(badge, 10) : badge;
      statusMessage = `active. ${count > 0 ? `${count} notification${count > 1 ? "s" : ""}` : "no notifications"}`;
    }
    return ["status"].concat(statusMessage || []).join(": ");
  }, [computedStatus, badge]);
  const shouldValidate = computedStatus !== void 0;
  const statusLabel = useText(AvatarComponent, props, "statusLabel", defaultStatusLabel, shouldValidate);
  return import_react.default.createElement(StyledAvatar, _extends$4({
    ref,
    isSystem,
    size,
    status: computedStatus,
    surfaceColor,
    backgroundColor,
    foregroundColor,
    "aria-atomic": "true",
    "aria-live": "polite"
  }, props), import_react.Children.only(children), computedStatus && import_react.default.createElement(StyledStatusIndicator, {
    size,
    type: computedStatus,
    surfaceColor,
    "aria-label": statusLabel,
    as: "figcaption"
  }, computedStatus === "active" ? import_react.default.createElement("span", {
    "aria-hidden": "true"
  }, badge) : import_react.default.createElement(import_react.default.Fragment, null, computedStatus === "away" ? import_react.default.createElement(ClockIcon, {
    "data-icon-status": computedStatus
  }) : null, computedStatus === "transfers" ? import_react.default.createElement(ArrowLeftIcon, {
    "data-icon-status": computedStatus
  }) : null)));
});
AvatarComponent.displayName = "Avatar";
AvatarComponent.propTypes = {
  backgroundColor: import_prop_types.default.string,
  foregroundColor: import_prop_types.default.string,
  surfaceColor: import_prop_types.default.string,
  isSystem: import_prop_types.default.bool,
  badge: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]),
  size: import_prop_types.default.oneOf(SIZE),
  status: import_prop_types.default.oneOf(STATUS),
  statusLabel: import_prop_types.default.string
};
AvatarComponent.defaultProps = {
  size: "medium"
};
var Avatar = AvatarComponent;
Avatar.Text = Text;
var StatusIndicator = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    children,
    type,
    isCompact,
    "aria-label": label,
    ...props
  } = _ref;
  let ClockIcon = SvgClockStroke2;
  let ArrowLeftIcon = SvgArrowLeftSmStroke2;
  if (isCompact) {
    ClockIcon = SvgClockStroke$1;
    ArrowLeftIcon = SvgArrowLeftSmStroke$1;
  }
  const defaultLabel = (0, import_react.useMemo)(() => ["status"].concat(type || []).join(": "), [type]);
  const ariaLabel = useText(StatusIndicator, {
    "aria-label": label
  }, "aria-label", defaultLabel);
  return import_react.default.createElement(StyledStandaloneStatus, _extends$4({
    role: "status",
    ref
  }, props), import_react.default.createElement(StyledStandaloneStatusIndicator, {
    role: "img",
    type,
    size: isCompact ? "small" : "medium",
    "aria-label": ariaLabel
  }, type === "away" ? import_react.default.createElement(ClockIcon, {
    "data-icon-status": type,
    "aria-hidden": "true"
  }) : null, type === "transfers" ? import_react.default.createElement(ArrowLeftIcon, {
    "data-icon-status": type,
    "aria-hidden": "true"
  }) : null), children && import_react.default.createElement(StyledStandaloneStatusCaption, null, children));
});
StatusIndicator.displayName = "StatusIndicator";
StatusIndicator.propTypes = {
  type: import_prop_types.default.oneOf(STATUS),
  isCompact: import_prop_types.default.bool
};
StatusIndicator.defaultProps = {
  type: "offline"
};
export {
  Avatar,
  StatusIndicator
};
//# sourceMappingURL=@zendeskgarden_react-avatars.js.map
