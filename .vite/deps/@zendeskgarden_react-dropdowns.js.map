{"version": 3, "sources": ["../../node_modules/downshift/node_modules/react-is/cjs/react-is.development.js", "../../node_modules/downshift/node_modules/react-is/index.js", "../../node_modules/@zendeskgarden/react-dropdowns/dist/index.esm.js", "../../node_modules/downshift/dist/downshift.esm.js", "../../node_modules/compute-scroll-into-view/dist/index.js", "../../node_modules/downshift/node_modules/tslib/tslib.es6.js", "../../node_modules/@zendeskgarden/react-dropdowns/node_modules/react-popper/lib/esm/Popper.js", "../../node_modules/@zendeskgarden/react-dropdowns/node_modules/react-popper/lib/esm/Manager.js", "../../node_modules/@zendeskgarden/react-dropdowns/node_modules/react-popper/lib/esm/utils.js", "../../node_modules/@zendeskgarden/react-dropdowns/node_modules/react-popper/lib/esm/Reference.js"], "sourcesContent": ["/** @license React v17.0.2\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar REACT_ELEMENT_TYPE = 0xeac7;\nvar REACT_PORTAL_TYPE = 0xeaca;\nvar REACT_FRAGMENT_TYPE = 0xeacb;\nvar REACT_STRICT_MODE_TYPE = 0xeacc;\nvar REACT_PROFILER_TYPE = 0xead2;\nvar REACT_PROVIDER_TYPE = 0xeacd;\nvar REACT_CONTEXT_TYPE = 0xeace;\nvar REACT_FORWARD_REF_TYPE = 0xead0;\nvar REACT_SUSPENSE_TYPE = 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = 0xead8;\nvar REACT_MEMO_TYPE = 0xead3;\nvar REACT_LAZY_TYPE = 0xead4;\nvar REACT_BLOCK_TYPE = 0xead9;\nvar REACT_SERVER_BLOCK_TYPE = 0xeada;\nvar REACT_FUNDAMENTAL_TYPE = 0xead5;\nvar REACT_SCOPE_TYPE = 0xead7;\nvar REACT_OPAQUE_ID_TYPE = 0xeae0;\nvar REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\nvar REACT_OFFSCREEN_TYPE = 0xeae2;\nvar REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n\nif (typeof Symbol === 'function' && Symbol.for) {\n  var symbolFor = Symbol.for;\n  REACT_ELEMENT_TYPE = symbolFor('react.element');\n  REACT_PORTAL_TYPE = symbolFor('react.portal');\n  REACT_FRAGMENT_TYPE = symbolFor('react.fragment');\n  REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n  REACT_PROFILER_TYPE = symbolFor('react.profiler');\n  REACT_PROVIDER_TYPE = symbolFor('react.provider');\n  REACT_CONTEXT_TYPE = symbolFor('react.context');\n  REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n  REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n  REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n  REACT_MEMO_TYPE = symbolFor('react.memo');\n  REACT_LAZY_TYPE = symbolFor('react.lazy');\n  REACT_BLOCK_TYPE = symbolFor('react.block');\n  REACT_SERVER_BLOCK_TYPE = symbolFor('react.server.block');\n  REACT_FUNDAMENTAL_TYPE = symbolFor('react.fundamental');\n  REACT_SCOPE_TYPE = symbolFor('react.scope');\n  REACT_OPAQUE_ID_TYPE = symbolFor('react.opaque.id');\n  REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n  REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n  REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n}\n\n// Filter certain DOM attributes (e.g. src, href) if their values are empty strings.\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_DEBUG_TRACING_MODE_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_LEGACY_HIDDEN_TYPE || enableScopeAPI ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_BLOCK_TYPE || type[0] === REACT_SERVER_BLOCK_TYPE) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n          case REACT_SUSPENSE_LIST_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n}\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false;\nvar hasWarnedAboutDeprecatedIsConcurrentMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 18+.');\n    }\n  }\n\n  return false;\n}\nfunction isConcurrentMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsConcurrentMode) {\n      hasWarnedAboutDeprecatedIsConcurrentMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isConcurrentMode() alias has been deprecated, ' + 'and will be removed in React 18+.');\n    }\n  }\n\n  return false;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport * as React from 'react';\nimport React__default, { useContext, useRef, useState, Children, useEffect, useCallback, forwardRef, useMemo } from 'react';\nimport PropTypes from 'prop-types';\nimport styled, { ThemeContext, css } from 'styled-components';\nimport Downshift from 'downshift';\nexport { resetIdCounter } from 'downshift';\nimport { Manager, Reference, Popper } from 'react-popper';\nimport { composeEventHandlers, KEY_CODES } from '@zendeskgarden/container-utilities';\nimport { arrowStyles, retrieveComponentStyles, DEFAULT_THEME, menuStyles, getColor, getLineHeight, useDocument } from '@zendeskgarden/react-theming';\nimport { rgba, math } from 'polished';\nimport { FauxInput, Input, VALIDATION, MediaInput, Field as Field$1, Hint as Hint$1, Label as Label$1, Message as Message$1 } from '@zendeskgarden/react-forms';\nimport mergeRefs from 'react-merge-refs';\nimport { useSelection } from '@zendeskgarden/container-selection';\nimport { createPortal } from 'react-dom';\n\nfunction _extends$5() {\n  _extends$5 = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends$5.apply(this, arguments);\n}\n\nconst DropdownContext = React__default.createContext(undefined);\nconst useDropdownContext = () => {\n  const dropdownContext = useContext(DropdownContext);\n  if (!dropdownContext) {\n    throw new Error('This component must be rendered within a `Dropdown` component.');\n  }\n  return dropdownContext;\n};\n\nconst REMOVE_ITEM_STATE_TYPE = 'REMOVE_ITEM';\nconst Dropdown = props => {\n  const {\n    children,\n    isOpen,\n    selectedItem,\n    selectedItems,\n    highlightedIndex,\n    inputValue,\n    onSelect,\n    onStateChange,\n    onInputValueChange,\n    downshiftProps\n  } = props;\n  const itemIndexRef = useRef(0);\n  const previousItemRef = useRef(undefined);\n  const previousIndexRef = useRef(undefined);\n  const nextItemsHashRef = useRef({});\n  const containsMultiselectRef = useRef(false);\n  const itemSearchRegistry = useRef([]);\n  const [dropdownType, setDropdownType] = useState('');\n  const {\n    rtl\n  } = useContext(ThemeContext);\n  const hasMenuRef = useRef(false);\n  const popperReferenceElementRef = useRef(null);\n  const customGetInputProps = (_ref, downshift) => {\n    let {\n      onKeyDown,\n      ...other\n    } = _ref;\n    return {\n      onKeyDown: composeEventHandlers(onKeyDown, e => {\n        const PREVIOUS_KEY = rtl ? KEY_CODES.RIGHT : KEY_CODES.LEFT;\n        const NEXT_KEY = rtl ? KEY_CODES.LEFT : KEY_CODES.RIGHT;\n        if (downshift.isOpen) {\n          if (e.keyCode === PREVIOUS_KEY && previousIndexRef.current !== null && previousIndexRef.current !== undefined && !downshift.inputValue) {\n            e.preventDefault();\n            e.stopPropagation();\n            downshift.selectItemAtIndex(previousIndexRef.current);\n          }\n          if (e.keyCode === NEXT_KEY) {\n            const nextItemIndexes = Object.values(nextItemsHashRef.current);\n            if (nextItemIndexes.includes(downshift.highlightedIndex)) {\n              e.preventDefault();\n              e.stopPropagation();\n              downshift.selectItemAtIndex(downshift.highlightedIndex);\n            }\n          }\n        } else if ((e.keyCode === KEY_CODES.ENTER || e.keyCode === KEY_CODES.SPACE) && !downshift.isOpen && dropdownType !== 'combobox') {\n          e.preventDefault();\n          e.stopPropagation();\n          downshift.openMenu();\n        }\n      }),\n      ...other\n    };\n  };\n  const transformDownshift = _ref2 => {\n    let {\n      getInputProps,\n      getToggleButtonProps,\n      ...downshift\n    } = _ref2;\n    return {\n      getInputProps: p => getInputProps(customGetInputProps(p, downshift)),\n      getToggleButtonProps: p => getToggleButtonProps({\n        'aria-label': undefined,\n        ...p\n      }),\n      ...downshift\n    };\n  };\n  return React__default.createElement(Manager, null, React__default.createElement(Downshift, _extends$5({\n    suppressRefError: true\n    ,\n    isOpen: isOpen,\n    highlightedIndex: highlightedIndex,\n    selectedItem: selectedItem || null\n    ,\n    inputValue: inputValue,\n    onInputValueChange: (inputVal, stateAndHelpers) => {\n      if (onInputValueChange) {\n        if (stateAndHelpers.isOpen) {\n          onInputValueChange(inputVal, stateAndHelpers);\n        } else if (dropdownType === 'multiselect') {\n          onInputValueChange('', stateAndHelpers);\n        }\n      }\n    },\n    onStateChange: (changes, stateAndHelpers) => {\n      if (dropdownType === 'autocomplete' && changes.isOpen === false && !changes.selectedItem) {\n        onSelect && onSelect(selectedItem, stateAndHelpers);\n      }\n      if (Object.prototype.hasOwnProperty.call(changes, 'selectedItem') && changes.selectedItem !== null) {\n        if (selectedItems) {\n          const {\n            itemToString\n          } = stateAndHelpers;\n          const existingItemIndex = selectedItems.findIndex(item => {\n            return itemToString(item) === itemToString(changes.selectedItem);\n          });\n          const updatedSelectedItems = Array.from(selectedItems);\n          if (existingItemIndex === -1) {\n            updatedSelectedItems.splice(updatedSelectedItems.length, 0, changes.selectedItem);\n          } else {\n            updatedSelectedItems.splice(existingItemIndex, 1);\n          }\n          changes.selectedItems = updatedSelectedItems;\n          delete changes.selectedItem;\n          onSelect && onSelect(updatedSelectedItems, stateAndHelpers);\n        } else {\n          onSelect && onSelect(changes.selectedItem, stateAndHelpers);\n        }\n        if (dropdownType === 'multiselect') {\n          stateAndHelpers.setState({\n            inputValue: ''\n          });\n        }\n      }\n      onStateChange && onStateChange(changes, stateAndHelpers);\n    },\n    stateReducer: (_state, changes) => {\n      switch (changes.type) {\n        case Downshift.stateChangeTypes.changeInput:\n          if (changes.inputValue === '' && dropdownType === 'combobox') {\n            return {\n              ...changes,\n              isOpen: false\n            };\n          }\n          return changes;\n        default:\n          return changes;\n      }\n    }\n  }, downshiftProps), downshift => React__default.createElement(DropdownContext.Provider, {\n    value: {\n      hasMenuRef,\n      itemIndexRef,\n      previousItemRef,\n      previousIndexRef,\n      nextItemsHashRef,\n      popperReferenceElementRef,\n      selectedItems,\n      downshift: transformDownshift(downshift),\n      containsMultiselectRef,\n      itemSearchRegistry,\n      setDropdownType\n    }\n  }, children)));\n};\nDropdown.propTypes = {\n  isOpen: PropTypes.bool,\n  selectedItem: PropTypes.any,\n  selectedItems: PropTypes.arrayOf(PropTypes.any),\n  highlightedIndex: PropTypes.number,\n  inputValue: PropTypes.string,\n  onSelect: PropTypes.func,\n  onStateChange: PropTypes.func,\n  downshiftProps: PropTypes.object\n};\n\nfunction getPopperPlacement(gardenPlacement) {\n  switch (gardenPlacement) {\n    case 'end':\n      return 'right';\n    case 'end-top':\n      return 'right-start';\n    case 'end-bottom':\n      return 'right-end';\n    case 'start':\n      return 'left';\n    case 'start-top':\n      return 'left-start';\n    case 'start-bottom':\n      return 'left-end';\n    default:\n      return gardenPlacement;\n  }\n}\nfunction getRtlPopperPlacement(gardenPlacement) {\n  const popperPlacement = getPopperPlacement(gardenPlacement);\n  switch (popperPlacement) {\n    case 'left':\n      return 'right';\n    case 'left-start':\n      return 'right-start';\n    case 'left-end':\n      return 'right-end';\n    case 'top-start':\n      return 'top-end';\n    case 'top-end':\n      return 'top-start';\n    case 'right':\n      return 'left';\n    case 'right-start':\n      return 'left-start';\n    case 'right-end':\n      return 'left-end';\n    case 'bottom-start':\n      return 'bottom-end';\n    case 'bottom-end':\n      return 'bottom-start';\n    default:\n      return popperPlacement;\n  }\n}\nfunction getArrowPosition(popperPlacement) {\n  const arrowPositionMappings = {\n    auto: 'top',\n    top: 'bottom',\n    'top-start': 'bottom-left',\n    'top-end': 'bottom-right',\n    right: 'left',\n    'right-start': 'left-top',\n    'right-end': 'left-bottom',\n    bottom: 'top',\n    'bottom-start': 'top-left',\n    'bottom-end': 'top-right',\n    left: 'right',\n    'left-start': 'right-top',\n    'left-end': 'right-bottom'\n  };\n  return popperPlacement ? arrowPositionMappings[popperPlacement] : 'top';\n}\nfunction getMenuPosition(popperPlacement) {\n  if (popperPlacement === 'auto') {\n    return 'bottom';\n  }\n  return popperPlacement ? popperPlacement.split('-')[0] : 'bottom';\n}\n\nconst COMPONENT_ID$m = 'dropdowns.menu';\nconst StyledMenu = styled.ul.attrs(props => ({\n  'data-garden-id': COMPONENT_ID$m,\n  'data-garden-version': '8.67.0',\n  className: props.isAnimated && 'is-animated'\n})).withConfig({\n  displayName: \"StyledMenu\",\n  componentId: \"sc-1vpttfd-0\"\n})([\"position:static !important;max-height:\", \";overflow-y:auto;\", \";\", \";\"], props => props.maxHeight, props => props.hasArrow && arrowStyles(getArrowPosition(props.placement), {\n  size: `${props.theme.space.base * 2}px`,\n  inset: '2px',\n  animationModifier: props.isAnimated ? '.is-animated' : undefined\n}), props => retrieveComponentStyles(COMPONENT_ID$m, props));\nStyledMenu.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$l = 'dropdowns.menu_wrapper';\nconst StyledMenuWrapper = styled.div.attrs(props => ({\n  'data-garden-id': COMPONENT_ID$l,\n  'data-garden-version': '8.67.0',\n  className: props.isAnimated && 'is-animated'\n})).withConfig({\n  displayName: \"StyledMenuWrapper\",\n  componentId: \"sc-tiwdxz-0\"\n})([\"\", \";\", \";\"], props => menuStyles(getMenuPosition(props.placement), {\n  theme: props.theme,\n  hidden: props.isHidden,\n  margin: `${props.theme.space.base * (props.hasArrow ? 2 : 1)}px`,\n  zIndex: props.zIndex,\n  animationModifier: props.isAnimated ? '.is-animated' : undefined\n}), props => retrieveComponentStyles(COMPONENT_ID$l, props));\nStyledMenuWrapper.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$k = 'dropdowns.separator';\nconst StyledSeparator = styled.li.attrs({\n  'data-garden-id': COMPONENT_ID$k,\n  'data-garden-version': '8.67.0',\n  role: 'separator'\n}).withConfig({\n  displayName: \"StyledSeparator\",\n  componentId: \"sc-1mrnp18-0\"\n})([\"display:block;margin:\", \"px 0;border-bottom:\", \";\", \";\"], props => props.theme.space.base, props => `${props.theme.borders.sm} ${getColor('neutralHue', 200, props.theme)}`, props => retrieveComponentStyles(COMPONENT_ID$k, props));\nStyledSeparator.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$j = 'dropdowns.item';\nconst getItemPaddingVertical = props => {\n  if (props.isCompact) {\n    return `${props.theme.space.base}px`;\n  }\n  return `${props.theme.space.base * 2}px`;\n};\nconst getColorStyles = props => {\n  let foregroundColor;\n  let backgroundColor;\n  if (props.disabled) {\n    foregroundColor = getColor('neutralHue', 400, props.theme);\n  } else if (props.isDanger) {\n    foregroundColor = getColor('dangerHue', 600, props.theme);\n    backgroundColor = props.isFocused ? rgba(foregroundColor, 0.08) : 'inherit';\n  } else {\n    foregroundColor = props.theme.colors.foreground;\n    backgroundColor = props.isFocused ? getColor('primaryHue', 600, props.theme, 0.08) : 'inherit';\n  }\n  return css([\"background-color:\", \";color:\", \";& a,& a:hover,& a:focus,& a:active{color:inherit;}\"], backgroundColor, foregroundColor);\n};\nconst StyledItem = styled.li.attrs(props => ({\n  'data-garden-id': COMPONENT_ID$j,\n  'data-garden-version': '8.67.0',\n  'aria-disabled': props.disabled\n})).withConfig({\n  displayName: \"StyledItem\",\n  componentId: \"sc-1xeog7q-0\"\n})([\"display:block;position:relative;z-index:0;cursor:\", \";padding:\", \" \", \"px;text-decoration:none;line-height:\", \"px;word-wrap:break-word;user-select:none;&:first-child{margin-top:\", \"px;}&:last-child{margin-bottom:\", \"px;}&:focus{outline:none;}& a,& a:hover,& a:focus,& a:active{text-decoration:none;}\", \";\", \";\"], props => props.disabled ? 'default' : 'pointer', props => getItemPaddingVertical(props), props => props.theme.space.base * 9, props => props.theme.space.base * 5, props => props.theme.space.base, props => props.theme.space.base, props => getColorStyles(props), props => retrieveComponentStyles(COMPONENT_ID$j, props));\nStyledItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$i = 'dropdowns.add_item';\nconst StyledAddItem = styled(StyledItem).attrs({\n  'data-garden-id': COMPONENT_ID$i,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledAddItem\",\n  componentId: \"sc-ekqk50-0\"\n})([\"color:\", \";\", \";\"], props => !props.disabled && getColor('primaryHue', 600, props.theme), props => retrieveComponentStyles(COMPONENT_ID$i, props));\nStyledAddItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$h = 'dropdowns.item_meta';\nconst StyledItemMeta = styled.span.attrs({\n  'data-garden-id': COMPONENT_ID$h,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledItemMeta\",\n  componentId: \"sc-k6xy28-0\"\n})([\"display:block;line-height:\", \"px;color:\", \";font-size:\", \";\", \";\"], props => props.theme.space.base * (props.isCompact ? 3 : 4), props => getColor('neutralHue', props.isDisabled ? 400 : 600, props.theme), props => props.theme.fontSizes.sm, props => retrieveComponentStyles(COMPONENT_ID$h, props));\nStyledItemMeta.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$g = 'dropdowns.item_icon';\nconst getSizeStyles = props => {\n  return css([\"width:\", \";height:calc(\", \"px + \", \");\"], props.theme.iconSizes.md, props.theme.space.base * 5, math(`${getItemPaddingVertical(props)} * 2`));\n};\nconst StyledItemIcon = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$g,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledItemIcon\",\n  componentId: \"sc-1v0ty11-0\"\n})([\"display:flex;position:absolute;top:0;\", \":\", \"px;align-items:center;justify-content:center;transition:opacity 0.1s ease-in-out;opacity:\", \";color:\", \";\", \";& > *{width:\", \";height:\", \";}\"], props => props.theme.rtl ? 'right' : 'left', props => props.theme.space.base * 3, props => props.isVisible ? '1' : '0', props => props.isDisabled ? 'inherit' : getColor('primaryHue', 600, props.theme), props => getSizeStyles(props), props => props.theme.iconSizes.md, props => props.theme.iconSizes.md);\nStyledItemIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$f = 'dropdowns.next_item';\nconst StyledNextItem = styled(StyledItem).attrs({\n  'data-garden-id': COMPONENT_ID$f,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledNextItem\",\n  componentId: \"sc-1bcygn5-0\"\n})([\"\", \"{right:\", \";left:\", \";}\", \";\"], StyledItemIcon, props => props.theme.rtl ? 'auto' : `${props.theme.space.base * 3}px`, props => props.theme.rtl ? `${props.theme.space.base * 3}px` : 'auto', props => retrieveComponentStyles(COMPONENT_ID$f, props));\nStyledNextItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nvar _path$4;\nfunction _extends$4() { _extends$4 = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends$4.apply(this, arguments); }\nvar SvgChevronRightStroke = function SvgChevronRightStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends$4({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 16,\n    height: 16,\n    focusable: \"false\",\n    viewBox: \"0 0 16 16\",\n    \"aria-hidden\": \"true\"\n  }, props), _path$4 || (_path$4 = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"currentColor\",\n    d: \"M5.61 3.312a.5.5 0 01.718-.69l.062.066 4 5a.5.5 0 01.054.542l-.054.082-4 5a.5.5 0 01-.83-.55l.05-.074L9.359 8l-3.75-4.688z\"\n  })));\n};\n\nconst COMPONENT_ID$e = 'dropdowns.next_item_icon';\nconst NextIconComponent = _ref => {\n  let {\n    className\n  } = _ref;\n  return React__default.createElement(SvgChevronRightStroke, {\n    \"data-garden-id\": COMPONENT_ID$e,\n    \"data-garden-version\": '8.67.0',\n    className: className\n  });\n};\nconst StyledNextIcon = styled(NextIconComponent).withConfig({\n  displayName: \"StyledNextIcon\",\n  componentId: \"sc-1rinki2-0\"\n})([\"transform:\", \";color:\", \";\", \";\"], props => props.theme.rtl && 'rotate(180deg)', props => props.isDisabled ? 'inherit' : getColor('neutralHue', 600, props.theme), props => retrieveComponentStyles(COMPONENT_ID$e, props));\nStyledNextIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$d = 'dropdowns.previous_item';\nconst StyledPreviousItem = styled(StyledItem).attrs({\n  'data-garden-id': COMPONENT_ID$d,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledPreviousItem\",\n  componentId: \"sc-1nmdds9-0\"\n})([\"font-weight:\", \";\", \";\"], props => props.theme.fontWeights.semibold, props => retrieveComponentStyles(COMPONENT_ID$d, props));\nStyledPreviousItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nvar _path$3;\nfunction _extends$3() { _extends$3 = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends$3.apply(this, arguments); }\nvar SvgChevronLeftStroke = function SvgChevronLeftStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends$3({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 16,\n    height: 16,\n    focusable: \"false\",\n    viewBox: \"0 0 16 16\",\n    \"aria-hidden\": \"true\"\n  }, props), _path$3 || (_path$3 = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"currentColor\",\n    d: \"M10.39 12.688a.5.5 0 01-.718.69l-.062-.066-4-5a.5.5 0 01-.054-.542l.054-.082 4-5a.5.5 0 01.83.55l-.05.074L6.641 8l3.75 4.688z\"\n  })));\n};\n\nconst COMPONENT_ID$c = 'dropdowns.previous_item_icon';\nconst PreviousIconComponent = _ref => {\n  let {\n    className\n  } = _ref;\n  return React__default.createElement(SvgChevronLeftStroke, {\n    \"data-garden-id\": COMPONENT_ID$c,\n    \"data-garden-version\": '8.67.0',\n    className: className\n  });\n};\nconst StyledPreviousIcon = styled(PreviousIconComponent).withConfig({\n  displayName: \"StyledPreviousIcon\",\n  componentId: \"sc-czfwj7-0\"\n})([\"transform:\", \";color:\", \";\", \";\"], props => props.theme.rtl && 'rotate(180deg)', props => props.isDisabled ? 'inherit' : getColor('neutralHue', 600, props.theme), props => retrieveComponentStyles(COMPONENT_ID$c, props));\nStyledPreviousIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$b = 'dropdowns.header_icon';\nconst StyledHeaderIcon = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$b,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledHeaderIcon\",\n  componentId: \"sc-1fl6nsz-0\"\n})([\"display:flex;position:absolute;top:0;bottom:0;align-items:center;justify-content:center;\", \":\", \"px;color:\", \";& > *{width:\", \";height:\", \";}\", \";\"], props => props.theme.rtl ? 'right' : 'left', props => props.theme.space.base * 3, props => getColor('neutralHue', 600, props.theme), props => props.theme.iconSizes.md, props => props.theme.iconSizes.md, props => retrieveComponentStyles(COMPONENT_ID$b, props));\nStyledHeaderIcon.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$a = 'dropdowns.header_item';\nconst getHorizontalPadding = props => {\n  if (props.hasIcon) {\n    return undefined;\n  }\n  return `${props.theme.space.base * 3}px`;\n};\nconst StyledHeaderItem = styled(StyledItem).attrs({\n  'data-garden-id': COMPONENT_ID$a,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledHeaderItem\",\n  componentId: \"sc-137filx-0\"\n})([\"cursor:default;padding-right:\", \";padding-left:\", \";font-weight:\", \";\", \";\"], props => getHorizontalPadding(props), props => getHorizontalPadding(props), props => props.theme.fontWeights.semibold, props => retrieveComponentStyles(COMPONENT_ID$a, props));\nStyledHeaderItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$9 = 'dropdowns.media_body';\nconst StyledMediaBody = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$9,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledMediaBody\",\n  componentId: \"sc-36j7ef-0\"\n})([\"display:block;overflow:hidden;padding-\", \":\", \"px;\", \";\"], props => props.theme.rtl ? 'right' : 'left', props => props.theme.space.base * 2, props => retrieveComponentStyles(COMPONENT_ID$9, props));\nStyledMediaBody.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$8 = 'dropdowns.media_figure';\nconst StyledMediaFigure = styled(\n_ref => {\n  let {\n    children,\n    isCompact,\n    theme,\n    ...props\n  } = _ref;\n  return (\n    React__default.cloneElement(Children.only(children), props)\n  );\n}).attrs({\n  'data-garden-id': COMPONENT_ID$8,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledMediaFigure\",\n  componentId: \"sc-2f2x8x-0\"\n})([\"float:\", \";margin-top:\", \"px !important;width:\", \";height:\", \";\", \";\"], props => props.theme.rtl ? 'right' : 'left', props => props.theme.space.base * 0.5, props => props.theme.iconSizes.md, props => props.theme.iconSizes.md, props => retrieveComponentStyles(COMPONENT_ID$8, props));\nStyledMediaFigure.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$7 = 'dropdowns.media_item';\nconst StyledMediaItem = styled(StyledItem).attrs({\n  'data-garden-id': COMPONENT_ID$7,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledMediaItem\",\n  componentId: \"sc-ikwshz-0\"\n})([\"\", \";\"], props => retrieveComponentStyles(COMPONENT_ID$7, props));\nStyledMediaItem.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$6 = 'dropdowns.faux_input';\nconst StyledFauxInput = styled(FauxInput).attrs(props => ({\n  'data-garden-id': COMPONENT_ID$6,\n  'data-garden-version': '8.67.0',\n  mediaLayout: true,\n  theme: props.theme\n})).withConfig({\n  displayName: \"StyledFauxInput\",\n  componentId: \"sc-1l592ed-0\"\n})([\"cursor:\", \";min-width:\", \"px;\", \";\"], props => !props.disabled && 'pointer', props => props.theme.space.base * (props.isCompact ? 25 : 36), props => retrieveComponentStyles(COMPONENT_ID$6, props));\nStyledFauxInput.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$5 = 'dropdowns.input';\nconst hiddenStyling = css([\"position:fixed;border:0;clip:rect(1px,1px,1px,1px);padding:0;width:1px;height:1px;overflow:hidden;white-space:nowrap;\"]);\nconst StyledInput = styled(Input).attrs({\n  'data-garden-id': COMPONENT_ID$5,\n  'data-garden-version': '8.67.0',\n  isBare: true\n}).withConfig({\n  displayName: \"StyledInput\",\n  componentId: \"sc-hzhvmp-0\"\n})([\"\", \";\", \";\"], props => props.isHidden && hiddenStyling, props => retrieveComponentStyles(COMPONENT_ID$5, props));\nStyledInput.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$4 = 'dropdowns.select';\nconst StyledSelect = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$4,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledSelect\",\n  componentId: \"sc-xifmwj-0\"\n})([\"flex-grow:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;\", \";\"], props => retrieveComponentStyles(COMPONENT_ID$4, props));\nStyledSelect.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$3 = 'dropdowns.multiselect_input';\nconst visibleStyling = props => {\n  const margin = props.isVisible ? `${props.theme.space.base / 2}px` : 0;\n  const minWidth = props.isVisible ? `${props.theme.space.base * 15}px` : 0;\n  let height = '0';\n  if (props.isVisible) {\n    height = `${props.theme.space.base * (props.isCompact ? 5 : 8)}px`;\n  }\n  return css([\"opacity:\", \";margin:\", \";width:\", \";min-width:\", \";height:\", \";\"], !props.isVisible && 0, margin, !props.isVisible && 0, minWidth, height);\n};\nconst StyledMultiselectInput = styled(StyledInput).attrs({\n  'data-garden-id': COMPONENT_ID$3,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledMultiselectInput\",\n  componentId: \"sc-1avnf6f-0\"\n})([\"flex-basis:\", \"px;flex-grow:1;align-self:center;min-height:0;\", \";\", \";\"], props => props.theme.space.base * 15, props => visibleStyling(props), props => retrieveComponentStyles(COMPONENT_ID$3, props));\nStyledMultiselectInput.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$2 = 'dropdowns.multiselect_items_container';\nconst sizeStyles = props => {\n  let margin;\n  let padding;\n  if (!props.isBare) {\n    const marginVertical = props.isCompact ? `-${props.theme.space.base * 1.5}px` : `-${props.theme.space.base * 2.5}px`;\n    margin = `${marginVertical} 0`;\n    const paddingVertical = props.isCompact ? '3px' : '1px';\n    const paddingEnd = `${props.theme.space.base}px`;\n    padding = `${paddingVertical} ${props.theme.rtl ? 0 : paddingEnd} ${paddingVertical} ${props.theme.rtl ? paddingEnd : 0}`;\n  }\n  return css([\"margin:\", \";padding:\", \";\"], margin, padding);\n};\nconst StyledMultiselectItemsContainer = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$2,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledMultiselectItemsContainer\",\n  componentId: \"sc-1jzhet8-0\"\n})([\"display:inline-flex;flex-grow:1;flex-wrap:wrap;min-width:0;\", \";\", \";\"], props => sizeStyles(props), props => retrieveComponentStyles(COMPONENT_ID$2, props));\nStyledMultiselectItemsContainer.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$1 = 'dropdowns.multiselect_item_wrapper';\nconst StyledMultiselectItemWrapper = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$1,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledMultiselectItemWrapper\",\n  componentId: \"sc-1rb2bye-0\"\n})([\"display:inline-flex;align-items:center;margin:\", \"px;max-width:100%;\", \";\"], props => props.theme.space.base / 2, props => retrieveComponentStyles(COMPONENT_ID$1, props));\nStyledMultiselectItemWrapper.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID = 'dropdowns.multiselect_more_anchor';\nconst StyledMultiselectMoreAnchor = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID,\n  'data-garden-version': '8.67.0'\n}).withConfig({\n  displayName: \"StyledMultiselectMoreAnchor\",\n  componentId: \"sc-1m9v46e-0\"\n})([\"display:inline-block;cursor:\", \";padding:\", \"px 0;overflow:hidden;user-select:none;text-overflow:ellipsis;line-height:\", \";white-space:nowrap;color:\", \";:hover{text-decoration:\", \";}\", \";\"], props => props.isDisabled ? 'default' : 'pointer', props => props.theme.space.base * (props.isCompact ? 0.75 : 1.5), props => props.isCompact ? '1em' : getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), props => props.isDisabled ? getColor('neutralHue', 400, props.theme) : getColor('primaryHue', 600, props.theme), props => !props.isDisabled && 'underline', props => retrieveComponentStyles(COMPONENT_ID, props));\nStyledMultiselectMoreAnchor.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst Trigger = _ref => {\n  let {\n    children,\n    refKey,\n    ...triggerProps\n  } = _ref;\n  const {\n    hasMenuRef,\n    itemSearchRegistry,\n    downshift: {\n      getRootProps,\n      getToggleButtonProps,\n      getInputProps,\n      isOpen,\n      highlightedIndex,\n      selectItemAtIndex,\n      setHighlightedIndex\n    }\n  } = useDropdownContext();\n  const hiddenInputRef = useRef(null);\n  const triggerRef = useRef(null);\n  const previousIsOpenRef = useRef(undefined);\n  const [searchString, setSearchString] = useState('');\n  const searchTimeoutRef = useRef();\n  const currentSearchIndexRef = useRef(0);\n  useEffect(() => {\n    if (hiddenInputRef.current && isOpen && !previousIsOpenRef.current) {\n      hiddenInputRef.current.focus();\n    }\n    if (triggerRef.current && !isOpen && previousIsOpenRef.current) {\n      triggerRef.current.focus();\n    }\n    previousIsOpenRef.current = isOpen;\n  }, [isOpen, hasMenuRef]);\n  useEffect(() => {\n    if (hasMenuRef.current === false) {\n      hasMenuRef.current = true;\n    }\n  }, [hasMenuRef]);\n  useEffect(() => {\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n    searchTimeoutRef.current = window.setTimeout(() => {\n      setSearchString('');\n    }, 500);\n    return () => {\n      clearTimeout(searchTimeoutRef.current);\n    };\n  }, [searchString]);\n  const searchItems = useCallback((searchValue, startIndex, endIndex) => {\n    for (let index = startIndex; index < endIndex; index++) {\n      const itemTextValue = itemSearchRegistry.current[index];\n      if (itemTextValue && itemTextValue.toUpperCase().indexOf(searchValue.toUpperCase()) === 0) {\n        return index;\n      }\n    }\n    return undefined;\n  }, [itemSearchRegistry]);\n  const onInputKeyDown = useCallback(e => {\n    if (e.keyCode === KEY_CODES.SPACE) {\n      if (searchString) {\n        e.preventDefault();\n        e.stopPropagation();\n      } else if (highlightedIndex !== null && highlightedIndex !== undefined) {\n        e.preventDefault();\n        e.stopPropagation();\n        selectItemAtIndex(highlightedIndex);\n      }\n    }\n    if ((e.keyCode < 48 || e.keyCode > 57) && (e.keyCode < 65 || e.keyCode > 90) && e.keyCode !== KEY_CODES.SPACE) {\n      return;\n    }\n    const character = String.fromCharCode(e.which || e.keyCode);\n    if (!character || character.length === 0) {\n      return;\n    }\n    if (!searchString) {\n      if (highlightedIndex === null || highlightedIndex === undefined) {\n        currentSearchIndexRef.current = -1;\n      } else {\n        currentSearchIndexRef.current = highlightedIndex;\n      }\n    }\n    const newSearchString = searchString + character;\n    setSearchString(newSearchString);\n    let matchingIndex = searchItems(newSearchString, currentSearchIndexRef.current + 1, itemSearchRegistry.current.length);\n    if (matchingIndex === undefined) {\n      matchingIndex = searchItems(newSearchString, 0, currentSearchIndexRef.current);\n    }\n    if (matchingIndex !== undefined) {\n      setHighlightedIndex(matchingIndex);\n    }\n  }, [searchString, searchItems, itemSearchRegistry, highlightedIndex, selectItemAtIndex, setHighlightedIndex]);\n  const renderChildren = popperRef => {\n    const {\n      ref: rootPropsRefCallback,\n      ...rootProps\n    } = getRootProps();\n    const listboxToggleProps = getToggleButtonProps({\n      ...rootProps,\n      role: null,\n      'aria-labelledby': undefined,\n      ...triggerProps,\n      ...children.props\n    });\n    const menuToggleProps = {\n      ...listboxToggleProps,\n      'aria-haspopup': 'true',\n      'aria-controls': listboxToggleProps['aria-owns'],\n      'aria-owns': null\n    };\n    const toggleButtonProps = hasMenuRef.current ? menuToggleProps : listboxToggleProps;\n    return React__default.cloneElement(React__default.Children.only(children), {\n      ...toggleButtonProps,\n      [refKey]: childRef => {\n        popperRef(childRef);\n        triggerRef.current = childRef;\n        rootPropsRefCallback(childRef);\n      }\n    });\n  };\n  return React__default.createElement(Reference, null, _ref2 => {\n    let {\n      ref: popperReference\n    } = _ref2;\n    return React__default.createElement(React__default.Fragment, null, renderChildren(popperReference), React__default.createElement(StyledInput, getInputProps({\n      readOnly: true,\n      isHidden: true,\n      tabIndex: -1,\n      ref: hiddenInputRef,\n      value: '',\n      onClick: e => {\n        if (isOpen) {\n          e.nativeEvent.preventDownshiftDefault = true;\n        }\n      },\n      onKeyDown: onInputKeyDown\n    })));\n  });\n};\nTrigger.propTypes = {\n  children: PropTypes.any,\n  refKey: PropTypes.string\n};\nTrigger.defaultProps = {\n  refKey: 'ref'\n};\n\nvar _path$2;\nfunction _extends$2() { _extends$2 = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends$2.apply(this, arguments); }\nvar SvgChevronDownStroke = function SvgChevronDownStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends$2({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 16,\n    height: 16,\n    focusable: \"false\",\n    viewBox: \"0 0 16 16\",\n    \"aria-hidden\": \"true\"\n  }, props), _path$2 || (_path$2 = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"currentColor\",\n    d: \"M12.688 5.61a.5.5 0 01.69.718l-.066.062-5 4a.5.5 0 01-.542.054l-.082-.054-5-4a.5.5 0 01.55-.83l.074.05L8 9.359l4.688-3.75z\"\n  })));\n};\n\nconst FieldContext = React__default.createContext(undefined);\nconst useFieldContext = () => {\n  const fieldContext = useContext(FieldContext);\n  if (!fieldContext) {\n    throw new Error('This component must be rendered within a `Field` component.');\n  }\n  return fieldContext;\n};\n\nconst Autocomplete = forwardRef((_ref, ref) => {\n  let {\n    children,\n    inputRef: controlledInputRef,\n    start,\n    ...props\n  } = _ref;\n  const {\n    popperReferenceElementRef,\n    downshift: {\n      getToggleButtonProps,\n      getInputProps,\n      getRootProps,\n      isOpen\n    },\n    setDropdownType\n  } = useDropdownContext();\n  const {\n    isLabelHovered\n  } = useFieldContext();\n  const inputRef = useRef();\n  const triggerRef = useRef();\n  const previousIsOpenRef = useRef(isOpen);\n  const [isHovered, setIsHovered] = useState(false);\n  const [isFocused, setIsFocused] = useState(false);\n  useEffect(() => {\n    if (inputRef.current && isOpen !== previousIsOpenRef.current) {\n      inputRef.current.focus();\n    }\n    previousIsOpenRef.current = isOpen;\n  }, [inputRef, isOpen]);\n  const {\n    type,\n    onKeyDown,\n    ...selectProps\n  } = getToggleButtonProps(getRootProps({\n    role: null,\n    ...props,\n    onKeyDown: e => {\n      if (isOpen) {\n        e.nativeEvent.preventDownshiftDefault = true;\n      }\n    },\n    onMouseEnter: composeEventHandlers(props.onMouseEnter, () => setIsHovered(true)),\n    onMouseLeave: composeEventHandlers(props.onMouseLeave, () => setIsHovered(false))\n  }));\n  const onSelectKeyDown = composeEventHandlers(props.onKeyDown, onKeyDown);\n  const isContainerHovered = isLabelHovered && !isOpen;\n  const isContainerFocused = isOpen || isFocused;\n  useEffect(() => {\n    setDropdownType('autocomplete');\n  }, [setDropdownType]);\n  return React__default.createElement(Reference, null, _ref2 => {\n    let {\n      ref: popperReference\n    } = _ref2;\n    return React__default.createElement(StyledFauxInput, _extends$5({\n      isHovered: isContainerHovered,\n      isFocused: isContainerFocused,\n      tabIndex: null,\n      onKeyDown: onSelectKeyDown\n    }, selectProps, {\n      ref: selectRef => {\n        popperReference(selectRef);\n        mergeRefs([triggerRef, ref])(selectRef);\n        popperReferenceElementRef.current = selectRef;\n      }\n    }), start && React__default.createElement(StyledFauxInput.StartIcon, {\n      isHovered: isHovered || isLabelHovered && !isOpen,\n      isFocused: isContainerFocused,\n      isDisabled: props.disabled\n    }, start), !isOpen && React__default.createElement(StyledSelect, null, children), React__default.createElement(StyledInput, getInputProps({\n      isHidden: !isOpen,\n      disabled: props.disabled,\n      onFocus: () => {\n        setIsFocused(true);\n      },\n      onBlur: () => {\n        setIsFocused(false);\n      },\n      onClick: e => {\n        if (isOpen) {\n          e.nativeEvent.preventDownshiftDefault = true;\n        }\n      },\n      role: 'combobox',\n      ref: mergeRefs([inputRef, controlledInputRef || null])\n    })), !props.isBare && React__default.createElement(StyledFauxInput.EndIcon, {\n      isHovered: isHovered || isLabelHovered && !isOpen,\n      isFocused: isContainerFocused,\n      isDisabled: props.disabled,\n      isRotated: isOpen\n    }, React__default.createElement(SvgChevronDownStroke, null)));\n  });\n});\nAutocomplete.displayName = 'Autocomplete';\nAutocomplete.propTypes = {\n  isCompact: PropTypes.bool,\n  isBare: PropTypes.bool,\n  disabled: PropTypes.bool,\n  focusInset: PropTypes.bool,\n  validation: PropTypes.oneOf(VALIDATION)\n};\n\nconst Combobox = forwardRef((_ref, ref) => {\n  let {\n    isCompact,\n    isBare,\n    disabled,\n    focusInset,\n    placeholder,\n    validation,\n    inputRef: inputRefProp = null,\n    start,\n    end,\n    ...props\n  } = _ref;\n  const {\n    popperReferenceElementRef,\n    downshift: {\n      getToggleButtonProps,\n      getInputProps,\n      getRootProps,\n      isOpen\n    },\n    setDropdownType\n  } = useDropdownContext();\n  const wrapperRef = useRef();\n  const inputRef = useRef();\n  const isOpenRef = useRef(isOpen);\n  const wrapperProps = getToggleButtonProps(getRootProps({\n    role: null,\n    type: null,\n    onClick: event => {\n      event.nativeEvent.preventDownshiftDefault = true;\n    },\n    ...props,\n    onKeyDown: event => {\n      event.nativeEvent.preventDownshiftDefault = true;\n    }\n  }));\n  const inputProps = getInputProps({\n    isCompact,\n    isBare,\n    disabled,\n    focusInset,\n    placeholder,\n    validation,\n    start,\n    end,\n    role: 'combobox',\n    onKeyDown: event => {\n      if (event.keyCode === KEY_CODES.SPACE || !isOpen && [KEY_CODES.DOWN, KEY_CODES.UP].includes(event.keyCode)) {\n        event.nativeEvent.preventDownshiftDefault = true;\n      }\n    },\n    onClick: event => {\n      event.nativeEvent.preventDownshiftDefault = true;\n    }\n  });\n  useEffect(() => {\n    if (inputRef.current && isOpen !== isOpenRef.current) {\n      inputRef.current.focus();\n    }\n    isOpenRef.current = isOpen;\n  }, [inputRef, isOpen]);\n  useEffect(() => {\n    setDropdownType('combobox');\n  }, [setDropdownType]);\n  return React__default.createElement(Reference, null, _ref2 => {\n    let {\n      ref: popperReference\n    } = _ref2;\n    const wrapperRefProp = element => {\n      popperReference(element);\n      mergeRefs([wrapperRef, ref])(element);\n      popperReferenceElementRef.current = element;\n    };\n    return React__default.createElement(MediaInput, _extends$5({}, inputProps, {\n      wrapperProps: wrapperProps,\n      wrapperRef: wrapperRefProp,\n      ref: mergeRefs([inputRef, inputRefProp])\n    }));\n  });\n});\nCombobox.displayName = 'Combobox';\nCombobox.propTypes = {\n  isCompact: PropTypes.bool,\n  isBare: PropTypes.bool,\n  disabled: PropTypes.bool,\n  focusInset: PropTypes.bool,\n  placeholder: PropTypes.string,\n  validation: PropTypes.oneOf(VALIDATION)\n};\n\nconst Multiselect = React__default.forwardRef((_ref, ref) => {\n  let {\n    renderItem,\n    placeholder,\n    maxItems,\n    renderShowMore,\n    inputRef: externalInputRef = null,\n    start,\n    onKeyDown,\n    ...props\n  } = _ref;\n  const {\n    popperReferenceElementRef,\n    selectedItems = [],\n    containsMultiselectRef,\n    previousIndexRef,\n    downshift: {\n      getToggleButtonProps,\n      getRootProps,\n      getInputProps,\n      isOpen,\n      closeMenu,\n      inputValue,\n      setState: setDownshiftState,\n      itemToString\n    },\n    setDropdownType\n  } = useDropdownContext();\n  const {\n    isLabelHovered\n  } = useFieldContext();\n  const inputRef = useRef();\n  const triggerRef = useRef();\n  const blurTimeoutRef = useRef();\n  const previousIsOpenRef = useRef(undefined);\n  const previousIsFocusedRef = useRef(undefined);\n  const [isHovered, setIsHovered] = useState(false);\n  const [isFocused, setIsFocused] = useState(false);\n  const [focusedItem, setFocusedItem] = useState(undefined);\n  const themeContext = useContext(ThemeContext);\n  const environment = useDocument(themeContext);\n  const {\n    getContainerProps,\n    getItemProps\n  } = useSelection({\n    rtl: themeContext.rtl,\n    focusedItem,\n    selectedItem: undefined,\n    onFocus: item => {\n      setFocusedItem(item);\n    }\n  });\n  useEffect(() => {\n    containsMultiselectRef.current = true;\n    const tempRef = blurTimeoutRef;\n    return () => {\n      clearTimeout(tempRef.current);\n    };\n  }, []);\n  useEffect(() => {\n    if (inputRef.current) {\n      if (isOpen && !previousIsOpenRef.current) {\n        inputRef.current.focus();\n      } else if (isFocused && !previousIsFocusedRef.current && focusedItem === undefined) {\n        inputRef.current.focus();\n      }\n    }\n    previousIsOpenRef.current = isOpen;\n    previousIsFocusedRef.current = isFocused;\n  }, [isOpen, inputRef, isFocused, focusedItem]);\n  useEffect(() => {\n    if (focusedItem !== undefined && isOpen) {\n      closeMenu();\n    }\n  }, [focusedItem, isOpen, closeMenu]);\n  const {\n    type,\n    ...selectProps\n  } = getToggleButtonProps(getRootProps({\n    tabIndex: props.disabled ? undefined : -1,\n    onKeyDown: composeEventHandlers(onKeyDown, e => {\n      if (isOpen) {\n        e.nativeEvent.preventDownshiftDefault = true;\n      } else if (!inputValue && e.keyCode === KEY_CODES.HOME) {\n        setFocusedItem(selectedItems[0]);\n        e.preventDefault();\n      }\n    }),\n    onFocus: () => {\n      setIsFocused(true);\n    },\n    onBlur: e => {\n      const currentTarget = e.currentTarget;\n      blurTimeoutRef.current = setTimeout(() => {\n        if (environment && !currentTarget.contains(environment.activeElement)) {\n          setIsFocused(false);\n        }\n      }, 0);\n    },\n    onMouseEnter: composeEventHandlers(props.onMouseEnter, () => setIsHovered(true)),\n    onMouseLeave: composeEventHandlers(props.onMouseLeave, () => setIsHovered(false)),\n    role: null,\n    ...props\n  }));\n  const renderSelectableItem = useCallback((item, index) => {\n    const removeValue = () => {\n      setDownshiftState({\n        type: REMOVE_ITEM_STATE_TYPE,\n        selectedItem: item\n      });\n      inputRef.current && inputRef.current.focus();\n    };\n    const renderedItem = renderItem({\n      value: item,\n      removeValue\n    });\n    const focusRef = React__default.createRef();\n    const clonedChild = React__default.cloneElement(renderedItem, {\n      ...getItemProps({\n        item,\n        focusRef,\n        onKeyDown: e => {\n          if (e.keyCode === KEY_CODES.DELETE || e.keyCode === KEY_CODES.BACKSPACE) {\n            e.preventDefault();\n            removeValue();\n          }\n          if (e.keyCode === KEY_CODES.END && !inputValue) {\n            inputRef.current && inputRef.current.focus();\n            e.preventDefault();\n          }\n          if (themeContext.rtl) {\n            if (e.keyCode === KEY_CODES.RIGHT && index === 0) {\n              e.preventDefault();\n            }\n            if (e.keyCode === KEY_CODES.LEFT && index === selectedItems.length - 1) {\n              e.preventDefault();\n              inputRef.current && inputRef.current.focus();\n            }\n          } else {\n            if (e.keyCode === KEY_CODES.LEFT && index === 0) {\n              e.preventDefault();\n            }\n            if (e.keyCode === KEY_CODES.RIGHT && index === selectedItems.length - 1) {\n              e.preventDefault();\n              inputRef.current && inputRef.current.focus();\n            }\n          }\n        },\n        onClick: e => {\n          e.nativeEvent.preventDownshiftDefault = true;\n        },\n        tabIndex: -1\n      }),\n      size: props.isCompact ? 'medium' : 'large'\n    });\n    const key = `${itemToString(item)}-${index}`;\n    return React__default.createElement(StyledMultiselectItemWrapper, {\n      key: key\n    }, clonedChild);\n  }, [getItemProps, inputValue, renderItem, setDownshiftState, itemToString, selectedItems, props, inputRef, themeContext.rtl]);\n  const items = useMemo(() => {\n    const itemValues = selectedItems || [];\n    const output = [];\n    for (let x = 0; x < itemValues.length; x++) {\n      const item = itemValues[x];\n      if (x < maxItems) {\n        if (props.disabled) {\n          const renderedItem = React__default.cloneElement(renderItem({\n            value: item,\n            removeValue: () => {\n              return undefined;\n            }\n          }), {\n            size: props.isCompact ? 'medium' : 'large'\n          });\n          output.push( React__default.createElement(StyledMultiselectItemWrapper, {\n            key: x\n          }, renderedItem));\n        } else {\n          output.push(renderSelectableItem(item, x));\n        }\n      } else if (!isFocused && !inputValue || props.disabled) {\n        output.push( React__default.createElement(StyledMultiselectItemWrapper, {\n          key: \"more-anchor\"\n        }, React__default.createElement(StyledMultiselectMoreAnchor, {\n          isCompact: props.isCompact,\n          isDisabled: props.disabled\n        }, renderShowMore ? renderShowMore(itemValues.length - x) : `+ ${itemValues.length - x} more`)));\n        break;\n      } else {\n        output.push(renderSelectableItem(item, x));\n      }\n    }\n    return output;\n  }, [isFocused, props.disabled, renderSelectableItem, selectedItems, renderItem, inputValue, maxItems, renderShowMore, props.isCompact]);\n  const isContainerHovered = isLabelHovered && !isOpen;\n  const isContainerFocused = isOpen || isFocused;\n  useEffect(() => {\n    setDropdownType('multiselect');\n  }, [setDropdownType]);\n  return React__default.createElement(Reference, null, _ref2 => {\n    let {\n      ref: popperReference\n    } = _ref2;\n    return React__default.createElement(StyledFauxInput, getContainerProps({\n      ...selectProps,\n      isHovered: isContainerHovered,\n      isFocused: isContainerFocused,\n      ref: selectRef => {\n        popperReference(selectRef);\n        mergeRefs([triggerRef, popperReferenceElementRef, ref])(selectRef);\n      }\n    }), start && React__default.createElement(StyledFauxInput.StartIcon, {\n      isHovered: isHovered || isLabelHovered && !isOpen,\n      isFocused: isContainerFocused,\n      isDisabled: props.disabled\n    }, start), React__default.createElement(StyledMultiselectItemsContainer, {\n      isBare: props.isBare,\n      isCompact: props.isCompact\n    }, items, React__default.createElement(StyledMultiselectInput, getInputProps({\n      disabled: props.disabled,\n      onFocus: () => {\n        setFocusedItem(undefined);\n      },\n      onClick: e => {\n        if (inputValue && inputValue.length > 0 && isOpen) {\n          e.nativeEvent.preventDownshiftDefault = true;\n        }\n      },\n      onKeyDown: e => {\n        if (!inputValue) {\n          if (themeContext.rtl && e.keyCode === KEY_CODES.RIGHT && selectedItems.length > 0 && previousIndexRef.current === undefined) {\n            setFocusedItem(selectedItems[selectedItems.length - 1]);\n          } else if (!themeContext.rtl && e.keyCode === KEY_CODES.LEFT && selectedItems.length > 0 && previousIndexRef.current === undefined) {\n            setFocusedItem(selectedItems[selectedItems.length - 1]);\n          } else if (e.keyCode === KEY_CODES.BACKSPACE && selectedItems.length > 0) {\n            setDownshiftState({\n              type: REMOVE_ITEM_STATE_TYPE,\n              selectedItem: selectedItems[selectedItems.length - 1]\n            });\n            e.nativeEvent.preventDownshiftDefault = true;\n            e.preventDefault();\n            e.stopPropagation();\n          }\n        }\n      },\n      isVisible: isFocused || inputValue || selectedItems.length === 0,\n      isCompact: props.isCompact,\n      role: 'combobox',\n      ref: mergeRefs([inputRef, externalInputRef]),\n      placeholder: selectedItems.length === 0 ? placeholder : undefined\n    }))), !props.isBare && React__default.createElement(StyledFauxInput.EndIcon, {\n      isHovered: isHovered || isLabelHovered && !isOpen,\n      isFocused: isContainerFocused,\n      isDisabled: props.disabled,\n      isRotated: isOpen\n    }, React__default.createElement(SvgChevronDownStroke, null)));\n  });\n});\nMultiselect.propTypes = {\n  isCompact: PropTypes.bool,\n  isBare: PropTypes.bool,\n  disabled: PropTypes.bool,\n  focusInset: PropTypes.bool,\n  renderItem: PropTypes.func.isRequired,\n  maxItems: PropTypes.number,\n  validation: PropTypes.oneOf(['success', 'warning', 'error'])\n};\nMultiselect.defaultProps = {\n  maxItems: 4\n};\nMultiselect.displayName = 'Multiselect';\n\nconst Select = React__default.forwardRef((_ref, ref) => {\n  let {\n    children,\n    start,\n    ...props\n  } = _ref;\n  const {\n    popperReferenceElementRef,\n    itemSearchRegistry,\n    downshift: {\n      getToggleButtonProps,\n      getInputProps,\n      isOpen,\n      highlightedIndex,\n      setHighlightedIndex,\n      selectItemAtIndex,\n      closeMenu\n    }\n  } = useDropdownContext();\n  const {\n    isLabelHovered\n  } = useFieldContext();\n  const [isHovered, setIsHovered] = useState(false);\n  const [isFocused, setIsFocused] = useState(false);\n  const hiddenInputRef = useRef();\n  const triggerRef = useRef();\n  const previousIsOpenRef = useRef(undefined);\n  const [searchString, setSearchString] = useState('');\n  const searchTimeoutRef = useRef();\n  const currentSearchIndexRef = useRef(0);\n  useEffect(() => {\n    if (hiddenInputRef.current && isOpen && !previousIsOpenRef.current) {\n      hiddenInputRef.current.focus();\n    }\n    if (triggerRef.current && !isOpen && previousIsOpenRef.current) {\n      triggerRef.current.focus();\n    }\n    previousIsOpenRef.current = isOpen;\n  }, [isOpen, triggerRef]);\n  useEffect(() => {\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n    searchTimeoutRef.current = window.setTimeout(() => {\n      setSearchString('');\n    }, 500);\n    return () => {\n      clearTimeout(searchTimeoutRef.current);\n    };\n  }, [searchString]);\n  const searchItems = useCallback((searchValue, startIndex, endIndex) => {\n    for (let index = startIndex; index < endIndex; index++) {\n      const itemTextValue = itemSearchRegistry.current[index];\n      if (itemTextValue && itemTextValue.toUpperCase().indexOf(searchValue.toUpperCase()) === 0) {\n        return index;\n      }\n    }\n    return undefined;\n  }, [itemSearchRegistry]);\n  const onInputKeyDown = useCallback(e => {\n    if (e.keyCode === KEY_CODES.SPACE) {\n      if (searchString) {\n        e.preventDefault();\n        e.stopPropagation();\n      } else if (highlightedIndex !== null && highlightedIndex !== undefined) {\n        e.preventDefault();\n        e.stopPropagation();\n        selectItemAtIndex(highlightedIndex);\n        closeMenu();\n      }\n    }\n    if ((e.keyCode < 48 || e.keyCode > 57) && (e.keyCode < 65 || e.keyCode > 90) && e.keyCode !== KEY_CODES.SPACE) {\n      return;\n    }\n    const character = String.fromCharCode(e.which || e.keyCode);\n    if (!character || character.length === 0) {\n      return;\n    }\n    if (!searchString) {\n      if (highlightedIndex === null || highlightedIndex === undefined) {\n        currentSearchIndexRef.current = -1;\n      } else {\n        currentSearchIndexRef.current = highlightedIndex;\n      }\n    }\n    const newSearchString = searchString + character;\n    setSearchString(newSearchString);\n    let matchingIndex = searchItems(newSearchString, currentSearchIndexRef.current + 1, itemSearchRegistry.current.length);\n    if (matchingIndex === undefined) {\n      matchingIndex = searchItems(newSearchString, 0, currentSearchIndexRef.current);\n    }\n    if (matchingIndex !== undefined) {\n      setHighlightedIndex(matchingIndex);\n    }\n  }, [searchString, searchItems, itemSearchRegistry, highlightedIndex, selectItemAtIndex, closeMenu, setHighlightedIndex]);\n  const {\n    type,\n    ...selectProps\n  } = getToggleButtonProps({\n    tabIndex: props.disabled ? undefined : 0,\n    onMouseEnter: composeEventHandlers(props.onMouseEnter, () => setIsHovered(true)),\n    onMouseLeave: composeEventHandlers(props.onMouseLeave, () => setIsHovered(false)),\n    onFocus: composeEventHandlers(props.onFocus, () => setIsFocused(true)),\n    onBlur: composeEventHandlers(props.onBlur, () => setIsFocused(false)),\n    ...props\n  });\n  const isContainerHovered = isLabelHovered && !isOpen;\n  const isContainerFocused = isFocused || isOpen;\n  return React__default.createElement(Reference, null, _ref2 => {\n    let {\n      ref: popperReference\n    } = _ref2;\n    return React__default.createElement(StyledFauxInput, _extends$5({\n      isHovered: isContainerHovered,\n      isFocused: isContainerFocused\n    }, selectProps, {\n      role: \"none\",\n      ref: selectRef => {\n        popperReference(selectRef);\n        mergeRefs([triggerRef, ref, popperReferenceElementRef])(selectRef);\n      }\n    }), start && React__default.createElement(StyledFauxInput.StartIcon, {\n      isHovered: isHovered || isLabelHovered && !isOpen,\n      isFocused: isContainerFocused,\n      isDisabled: props.disabled\n    }, start), React__default.createElement(StyledSelect, null, children), React__default.createElement(StyledInput, getInputProps({\n      readOnly: true,\n      isHidden: true,\n      tabIndex: -1,\n      ref: hiddenInputRef,\n      value: '',\n      onClick: e => {\n        if (isOpen) {\n          e.nativeEvent.preventDownshiftDefault = true;\n        }\n      },\n      onKeyDown: onInputKeyDown\n    })), !props.isBare && React__default.createElement(StyledFauxInput.EndIcon, {\n      isHovered: isHovered || isLabelHovered && !isOpen,\n      isFocused: isContainerFocused,\n      isDisabled: props.disabled,\n      isRotated: isOpen\n    }, React__default.createElement(SvgChevronDownStroke, null)));\n  });\n});\nSelect.displayName = 'Select';\nSelect.propTypes = {\n  isCompact: PropTypes.bool,\n  isBare: PropTypes.bool,\n  disabled: PropTypes.bool,\n  focusInset: PropTypes.bool,\n  validation: PropTypes.oneOf(VALIDATION),\n  start: PropTypes.any\n};\n\nconst Field = forwardRef((props, fieldRef) => {\n  const {\n    downshift: {\n      getRootProps\n    }\n  } = useDropdownContext();\n  const [isLabelHovered, setIsLabelHovered] = useState(false);\n  const {\n    ref\n  } = getRootProps();\n  const value = useMemo(() => ({\n    isLabelHovered,\n    setIsLabelHovered\n  }), [isLabelHovered, setIsLabelHovered]);\n  return React__default.createElement(FieldContext.Provider, {\n    value: value\n  }, React__default.createElement(Field$1, _extends$5({\n    ref: mergeRefs([ref, fieldRef])\n  }, props)));\n});\nField.displayName = 'Field';\n\nconst Hint = React__default.forwardRef((props, ref) => React__default.createElement(Hint$1, _extends$5({\n  ref: ref\n}, props)));\nHint.displayName = 'Hint';\n\nconst Label = React__default.forwardRef((_ref, ref) => {\n  let {\n    onMouseEnter,\n    onMouseLeave,\n    ...other\n  } = _ref;\n  const {\n    downshift: {\n      getLabelProps\n    }\n  } = useDropdownContext();\n  const {\n    setIsLabelHovered\n  } = useFieldContext();\n  const labelProps = getLabelProps({\n    onMouseEnter: composeEventHandlers(onMouseEnter, () => {\n      setIsLabelHovered(true);\n    }),\n    onMouseLeave: composeEventHandlers(onMouseLeave, () => {\n      setIsLabelHovered(false);\n    }),\n    ...other\n  });\n  return React__default.createElement(Label$1, _extends$5({\n    ref: ref\n  }, labelProps));\n});\nLabel.displayName = 'Label';\nLabel.propTypes = {\n  isRegular: PropTypes.bool\n};\n\nconst Message = React__default.forwardRef((props, ref) => React__default.createElement(Message$1, _extends$5({\n  ref: ref\n}, props)));\nMessage.displayName = 'Message';\nMessage.propTypes = {\n  validation: PropTypes.oneOf(VALIDATION)\n};\n\nconst SHARED_PLACEMENT = ['auto', 'top', 'top-start', 'top-end', 'bottom', 'bottom-start', 'bottom-end'];\nconst PLACEMENT = [...SHARED_PLACEMENT, 'end', 'end-top', 'end-bottom', 'start', 'start-top', 'start-bottom'];\n\nconst MenuContext = React__default.createContext(undefined);\nconst useMenuContext = () => {\n  const menuContext = useContext(MenuContext);\n  if (!menuContext) {\n    throw new Error('This component must be rendered within a `Menu` component.');\n  }\n  return menuContext;\n};\n\nconst Menu = forwardRef((props, menuRef) => {\n  const {\n    placement,\n    popperModifiers,\n    eventsEnabled,\n    isAnimated,\n    maxHeight,\n    style: menuStyle,\n    zIndex,\n    isCompact,\n    children,\n    appendToNode,\n    ...otherProps\n  } = props;\n  const {\n    hasMenuRef,\n    itemIndexRef,\n    previousIndexRef,\n    nextItemsHashRef,\n    popperReferenceElementRef,\n    itemSearchRegistry,\n    downshift: {\n      isOpen,\n      getMenuProps\n    }\n  } = useDropdownContext();\n  const scheduleUpdateRef = useRef(undefined);\n  useEffect(() => {\n    if (scheduleUpdateRef.current && isOpen) {\n      scheduleUpdateRef.current();\n    }\n  });\n  const [isVisible, setIsVisible] = useState(isOpen);\n  useEffect(() => {\n    let timeout;\n    if (isOpen) {\n      setIsVisible(true);\n    } else if (isAnimated) {\n      timeout = setTimeout(() => setIsVisible(false), 200);\n    } else {\n      setIsVisible(false);\n    }\n    return () => clearTimeout(timeout);\n  }, [isOpen, isAnimated]);\n  const themeContext = useContext(ThemeContext);\n  itemIndexRef.current = 0;\n  nextItemsHashRef.current = {};\n  previousIndexRef.current = undefined;\n  itemSearchRegistry.current = [];\n  const popperPlacement = themeContext.rtl ? getRtlPopperPlacement(placement) : getPopperPlacement(placement);\n  return (\n    React__default.createElement(MenuContext.Provider, {\n      value: {\n        itemIndexRef,\n        isCompact\n      }\n    }, React__default.createElement(Popper, {\n      placement: popperPlacement,\n      modifiers: popperModifiers\n      ,\n      eventsEnabled: isOpen && eventsEnabled\n    }, _ref => {\n      let {\n        ref,\n        style,\n        scheduleUpdate,\n        placement: currentPlacement\n      } = _ref;\n      let computedStyle = menuStyle;\n      scheduleUpdateRef.current = scheduleUpdate;\n      if ((isOpen || isVisible) && popperReferenceElementRef.current && popperReferenceElementRef.current.getBoundingClientRect) {\n        computedStyle = {\n          width: popperReferenceElementRef.current.getBoundingClientRect().width,\n          ...menuStyle\n        };\n      }\n      const menuProps = getMenuProps({\n        role: hasMenuRef.current ? 'menu' : 'listbox',\n        placement: currentPlacement,\n        isAnimated: isAnimated && (isOpen || isVisible),\n        ...otherProps\n      });\n      const menu = React__default.createElement(StyledMenuWrapper, {\n        ref: isOpen ? ref : undefined,\n        hasArrow: menuProps.hasArrow,\n        placement: menuProps.placement,\n        style: style,\n        isHidden: !isOpen,\n        isAnimated: menuProps.isAnimated,\n        zIndex: zIndex\n      }, React__default.createElement(StyledMenu, _extends$5({\n        ref: menuRef,\n        isCompact: isCompact,\n        maxHeight: maxHeight,\n        style: computedStyle\n      }, menuProps), (isOpen || isVisible) && children));\n      return appendToNode ? createPortal(menu, appendToNode) : menu;\n    }))\n  );\n});\nMenu.displayName = 'Menu';\nMenu.propTypes = {\n  popperModifiers: PropTypes.any,\n  eventsEnabled: PropTypes.bool,\n  zIndex: PropTypes.number,\n  style: PropTypes.object,\n  placement: PropTypes.oneOf(PLACEMENT),\n  isAnimated: PropTypes.bool,\n  isCompact: PropTypes.bool,\n  hasArrow: PropTypes.bool,\n  maxHeight: PropTypes.string\n};\nMenu.defaultProps = {\n  placement: 'bottom-start',\n  isAnimated: true,\n  eventsEnabled: true,\n  maxHeight: '400px',\n  zIndex: 1000\n};\n\nconst Separator = React__default.forwardRef((props, ref) => React__default.createElement(StyledSeparator, _extends$5({\n  ref: ref\n}, props)));\nSeparator.displayName = 'Separator';\n\nvar _path$1;\nfunction _extends$1() { _extends$1 = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends$1.apply(this, arguments); }\nvar SvgPlusStroke = function SvgPlusStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends$1({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 16,\n    height: 16,\n    focusable: \"false\",\n    viewBox: \"0 0 16 16\",\n    \"aria-hidden\": \"true\"\n  }, props), _path$1 || (_path$1 = /*#__PURE__*/React.createElement(\"path\", {\n    stroke: \"currentColor\",\n    strokeLinecap: \"round\",\n    d: \"M7.5 2.5v12m6-6h-12\"\n  })));\n};\n\nvar _path;\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nvar SvgCheckLgStroke = function SvgCheckLgStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 16,\n    height: 16,\n    focusable: \"false\",\n    viewBox: \"0 0 16 16\",\n    \"aria-hidden\": \"true\"\n  }, props), _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M1 9l4 4L15 3\"\n  })));\n};\n\nconst ItemContext = React__default.createContext(undefined);\nconst useItemContext = () => {\n  const context = useContext(ItemContext);\n  if (!context) {\n    throw new Error('This component must be rendered within an `Item` component.');\n  }\n  return context;\n};\n\nconst Item = React__default.forwardRef((_ref, forwardRef) => {\n  let {\n    value,\n    disabled,\n    isDanger,\n    component = StyledItem,\n    hasIcon,\n    children,\n    ...props\n  } = _ref;\n  const {\n    selectedItems,\n    hasMenuRef,\n    itemSearchRegistry,\n    downshift: {\n      isOpen,\n      selectedItem,\n      highlightedIndex,\n      getItemProps,\n      setHighlightedIndex,\n      itemToString\n    }\n  } = useDropdownContext();\n  const {\n    itemIndexRef,\n    isCompact\n  } = useMenuContext();\n  const itemRef = useRef();\n  const Component = component;\n  if ((value === undefined || value === null) && !disabled) {\n    throw new Error('All Item components require a `value` prop');\n  }\n  const currentIndex = itemIndexRef.current;\n  const isFocused = highlightedIndex === currentIndex;\n  let isSelected;\n  useEffect(() => {\n    if (!disabled && itemRef.current) {\n      const itemTextValue = itemRef.current.innerText;\n      if (itemTextValue) {\n        itemSearchRegistry.current[currentIndex] = itemTextValue;\n      }\n    }\n  });\n  if (value) {\n    if (selectedItems) {\n      isSelected = selectedItems.some(item => {\n        return itemToString(item) === itemToString(value);\n      });\n    } else {\n      isSelected = itemToString(selectedItem) === itemToString(value);\n    }\n  } else {\n    isSelected = false;\n  }\n  useEffect(() => {\n    if (isOpen && !disabled && !selectedItems && isSelected) {\n      setHighlightedIndex(currentIndex);\n    }\n  }, [currentIndex, disabled, isOpen, isSelected, selectedItems, setHighlightedIndex]);\n  const contextValue = useMemo(() => ({\n    isDisabled: disabled\n  }), [disabled]);\n  const ref = mergeRefs([itemRef, forwardRef]);\n  if (disabled) {\n    return React__default.createElement(ItemContext.Provider, {\n      value: contextValue\n    }, React__default.createElement(Component, _extends$5({\n      ref: ref,\n      disabled: disabled,\n      isDanger: isDanger,\n      isCompact: isCompact\n    }, props), isSelected && !hasIcon && React__default.createElement(StyledItemIcon, {\n      isCompact: isCompact,\n      isVisible: isSelected,\n      isDisabled: disabled\n    }, React__default.createElement(SvgCheckLgStroke, null)), children));\n  }\n  itemIndexRef.current++;\n  return React__default.createElement(ItemContext.Provider, {\n    value: contextValue\n  }, React__default.createElement(Component, getItemProps({\n    item: value,\n    isFocused,\n    ref,\n    isCompact,\n    isDanger,\n    ...(hasMenuRef.current && {\n      role: 'menuitem',\n      'aria-selected': null\n    }),\n    ...props\n  }), isSelected && !hasIcon && React__default.createElement(StyledItemIcon, {\n    isCompact: isCompact,\n    isVisible: isSelected\n  }, React__default.createElement(SvgCheckLgStroke, null)), children));\n});\nItem.displayName = 'Item';\nItem.propTypes = {\n  value: PropTypes.any,\n  disabled: PropTypes.bool\n};\n\nconst AddItemComponent = React__default.forwardRef((_ref, ref) => {\n  let {\n    children,\n    disabled,\n    ...props\n  } = _ref;\n  const {\n    isCompact\n  } = useMenuContext();\n  return React__default.createElement(StyledAddItem, _extends$5({\n    ref: ref,\n    disabled: disabled\n  }, props), React__default.createElement(StyledItemIcon, {\n    isCompact: isCompact,\n    isVisible: true,\n    isDisabled: disabled\n  }, React__default.createElement(SvgPlusStroke, null)), children);\n});\nconst AddItem = React__default.forwardRef((props, ref) => React__default.createElement(Item, _extends$5({\n  component: AddItemComponent,\n  ref: ref\n}, props, {\n  hasIcon: true\n})));\nAddItem.displayName = 'AddItem';\nAddItem.propTypes = {\n  value: PropTypes.any,\n  disabled: PropTypes.bool\n};\n\nconst HeaderIcon = React__default.forwardRef((props, ref) => {\n  const {\n    isCompact\n  } = useMenuContext();\n  return React__default.createElement(StyledHeaderIcon, _extends$5({\n    ref: ref,\n    isCompact: isCompact\n  }, props));\n});\nHeaderIcon.displayName = 'HeaderIcon';\n\nconst HeaderItem = React__default.forwardRef((props, ref) => {\n  const {\n    isCompact\n  } = useMenuContext();\n  return React__default.createElement(StyledHeaderItem, _extends$5({\n    ref: ref,\n    isCompact: isCompact\n  }, props));\n});\nHeaderItem.displayName = 'HeaderItem';\n\nconst ItemMeta = React__default.forwardRef((props, ref) => {\n  const {\n    isCompact\n  } = useMenuContext();\n  const {\n    isDisabled\n  } = useItemContext();\n  return React__default.createElement(StyledItemMeta, _extends$5({\n    ref: ref,\n    isCompact: isCompact,\n    isDisabled: isDisabled\n  }, props));\n});\nItemMeta.displayName = 'ItemMeta';\n\nconst MediaBody = React__default.forwardRef((props, ref) => {\n  const {\n    isCompact\n  } = useMenuContext();\n  return React__default.createElement(StyledMediaBody, _extends$5({\n    ref: ref,\n    isCompact: isCompact\n  }, props));\n});\nMediaBody.displayName = 'MediaBody';\n\nconst MediaFigure = props => {\n  const {\n    isCompact\n  } = useMenuContext();\n  return React__default.createElement(StyledMediaFigure, _extends$5({\n    isCompact: isCompact\n  }, props));\n};\n\nconst MediaItem = React__default.forwardRef((props, ref) => React__default.createElement(Item, _extends$5({\n  component: StyledMediaItem,\n  ref: ref\n}, props)));\nMediaItem.displayName = 'MediaItem';\nMediaItem.propTypes = {\n  value: PropTypes.any,\n  disabled: PropTypes.bool\n};\n\nconst NextItemComponent = React__default.forwardRef((_ref, ref) => {\n  let {\n    children,\n    disabled,\n    ...props\n  } = _ref;\n  const {\n    isCompact\n  } = useMenuContext();\n  return React__default.createElement(StyledNextItem, _extends$5({\n    ref: ref,\n    disabled: disabled\n  }, props), React__default.createElement(StyledItemIcon, {\n    isCompact: isCompact,\n    isDisabled: disabled,\n    isVisible: true\n  }, React__default.createElement(StyledNextIcon, {\n    isDisabled: disabled\n  })), children);\n});\nconst NextItem = React__default.forwardRef((_ref2, ref) => {\n  let {\n    value,\n    disabled,\n    ...props\n  } = _ref2;\n  const {\n    nextItemsHashRef,\n    downshift: {\n      itemToString\n    }\n  } = useDropdownContext();\n  const {\n    itemIndexRef\n  } = useMenuContext();\n  if (!disabled) {\n    nextItemsHashRef.current[itemToString(value)] = itemIndexRef.current;\n  }\n  return React__default.createElement(Item, _extends$5({\n    component: NextItemComponent,\n    \"aria-expanded\": true,\n    disabled: disabled,\n    value: value,\n    ref: ref\n  }, props, {\n    hasIcon: true\n  }));\n});\nNextItem.displayName = 'NextItem';\nNextItem.propTypes = {\n  value: PropTypes.any,\n  disabled: PropTypes.bool\n};\n\nconst PreviousItemComponent = React__default.forwardRef((_ref, ref) => {\n  let {\n    children,\n    disabled,\n    ...props\n  } = _ref;\n  const {\n    isCompact\n  } = useMenuContext();\n  return React__default.createElement(StyledPreviousItem, _extends$5({\n    ref: ref,\n    disabled: disabled\n  }, props), React__default.createElement(StyledItemIcon, {\n    isCompact: isCompact,\n    isDisabled: disabled,\n    isVisible: true\n  }, React__default.createElement(StyledPreviousIcon, {\n    isDisabled: disabled\n  })), children);\n});\nconst PreviousItem = React__default.forwardRef((_ref2, ref) => {\n  let {\n    value,\n    disabled,\n    ...props\n  } = _ref2;\n  const {\n    previousIndexRef\n  } = useDropdownContext();\n  const {\n    itemIndexRef\n  } = useMenuContext();\n  if (!disabled) {\n    previousIndexRef.current = itemIndexRef.current;\n  }\n  return React__default.createElement(Item, _extends$5({\n    component: PreviousItemComponent,\n    \"aria-expanded\": true,\n    value: value,\n    disabled: disabled\n  }, props, {\n    ref: ref,\n    hasIcon: true\n  }));\n});\nPreviousItem.displayName = 'PreviousItem';\nPreviousItem.propTypes = {\n  value: PropTypes.any,\n  disabled: PropTypes.bool\n};\n\nexport { AddItem, Autocomplete, Combobox, Dropdown, Field, HeaderIcon, HeaderItem, Hint, Item, ItemMeta, Label, MediaBody, MediaFigure, MediaItem, Menu, Message, Multiselect, NextItem, PreviousItem, Select, Separator, Trigger };\n", "import _objectWithoutPropertiesLoose from '@babel/runtime/helpers/esm/objectWithoutPropertiesLoose';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport _assertThisInitialized from '@babel/runtime/helpers/esm/assertThisInitialized';\nimport _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport PropTypes from 'prop-types';\nimport { cloneElement, Component, useRef, useEffect, useCallback, useLayoutEffect, useReducer, useMemo } from 'react';\nimport { isForwardRef } from 'react-is';\nimport compute from 'compute-scroll-into-view';\nimport { __assign } from 'tslib';\n\nvar idCounter = 0;\n\n/**\n * Accepts a parameter and returns it if it's a function\n * or a noop function if it's not. This allows us to\n * accept a callback, but not worry about it if it's not\n * passed.\n * @param {Function} cb the callback\n * @return {Function} a function\n */\nfunction cbToCb(cb) {\n  return typeof cb === 'function' ? cb : noop;\n}\nfunction noop() {}\n\n/**\n * Scroll node into view if necessary\n * @param {HTMLElement} node the element that should scroll into view\n * @param {HTMLElement} menuNode the menu element of the component\n */\nfunction scrollIntoView(node, menuNode) {\n  if (!node) {\n    return;\n  }\n  var actions = compute(node, {\n    boundary: menuNode,\n    block: 'nearest',\n    scrollMode: 'if-needed'\n  });\n  actions.forEach(function (_ref) {\n    var el = _ref.el,\n      top = _ref.top,\n      left = _ref.left;\n    el.scrollTop = top;\n    el.scrollLeft = left;\n  });\n}\n\n/**\n * @param {HTMLElement} parent the parent node\n * @param {HTMLElement} child the child node\n * @param {Window} environment The window context where downshift renders.\n * @return {Boolean} whether the parent is the child or the child is in the parent\n */\nfunction isOrContainsNode(parent, child, environment) {\n  var result = parent === child || child instanceof environment.Node && parent.contains && parent.contains(child);\n  return result;\n}\n\n/**\n * Simple debounce implementation. Will call the given\n * function once after the time given has passed since\n * it was last called.\n * @param {Function} fn the function to call after the time\n * @param {Number} time the time to wait\n * @return {Function} the debounced function\n */\nfunction debounce(fn, time) {\n  var timeoutId;\n  function cancel() {\n    if (timeoutId) {\n      clearTimeout(timeoutId);\n    }\n  }\n  function wrapper() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    cancel();\n    timeoutId = setTimeout(function () {\n      timeoutId = null;\n      fn.apply(void 0, args);\n    }, time);\n  }\n  wrapper.cancel = cancel;\n  return wrapper;\n}\n\n/**\n * This is intended to be used to compose event handlers.\n * They are executed in order until one of them sets\n * `event.preventDownshiftDefault = true`.\n * @param {...Function} fns the event handler functions\n * @return {Function} the event handler to add to an element\n */\nfunction callAllEventHandlers() {\n  for (var _len2 = arguments.length, fns = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    fns[_key2] = arguments[_key2];\n  }\n  return function (event) {\n    for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n      args[_key3 - 1] = arguments[_key3];\n    }\n    return fns.some(function (fn) {\n      if (fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n      return event.preventDownshiftDefault || event.hasOwnProperty('nativeEvent') && event.nativeEvent.preventDownshiftDefault;\n    });\n  };\n}\nfunction handleRefs() {\n  for (var _len4 = arguments.length, refs = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n    refs[_key4] = arguments[_key4];\n  }\n  return function (node) {\n    refs.forEach(function (ref) {\n      if (typeof ref === 'function') {\n        ref(node);\n      } else if (ref) {\n        ref.current = node;\n      }\n    });\n  };\n}\n\n/**\n * This generates a unique ID for an instance of Downshift\n * @return {String} the unique ID\n */\nfunction generateId() {\n  return String(idCounter++);\n}\n\n/**\n * Resets idCounter to 0. Used for SSR.\n */\nfunction resetIdCounter() {\n  idCounter = 0;\n}\n\n/**\n * Default implementation for status message. Only added when menu is open.\n * Will specify if there are results in the list, and if so, how many,\n * and what keys are relevant.\n *\n * @param {Object} param the downshift state and other relevant properties\n * @return {String} the a11y status message\n */\nfunction getA11yStatusMessage$1(_ref2) {\n  var isOpen = _ref2.isOpen,\n    resultCount = _ref2.resultCount,\n    previousResultCount = _ref2.previousResultCount;\n  if (!isOpen) {\n    return '';\n  }\n  if (!resultCount) {\n    return 'No results are available.';\n  }\n  if (resultCount !== previousResultCount) {\n    return resultCount + \" result\" + (resultCount === 1 ? ' is' : 's are') + \" available, use up and down arrow keys to navigate. Press Enter key to select.\";\n  }\n  return '';\n}\n\n/**\n * Takes an argument and if it's an array, returns the first item in the array\n * otherwise returns the argument\n * @param {*} arg the maybe-array\n * @param {*} defaultValue the value if arg is falsey not defined\n * @return {*} the arg or it's first item\n */\nfunction unwrapArray(arg, defaultValue) {\n  arg = Array.isArray(arg) ? /* istanbul ignore next (preact) */arg[0] : arg;\n  if (!arg && defaultValue) {\n    return defaultValue;\n  } else {\n    return arg;\n  }\n}\n\n/**\n * @param {Object} element (P)react element\n * @return {Boolean} whether it's a DOM element\n */\nfunction isDOMElement(element) {\n\n  // then we assume this is react\n  return typeof element.type === 'string';\n}\n\n/**\n * @param {Object} element (P)react element\n * @return {Object} the props\n */\nfunction getElementProps(element) {\n  return element.props;\n}\n\n/**\n * Throws a helpful error message for required properties. Useful\n * to be used as a default in destructuring or object params.\n * @param {String} fnName the function name\n * @param {String} propName the prop name\n */\nfunction requiredProp(fnName, propName) {\n  // eslint-disable-next-line no-console\n  console.error(\"The property \\\"\" + propName + \"\\\" is required in \\\"\" + fnName + \"\\\"\");\n}\nvar stateKeys = ['highlightedIndex', 'inputValue', 'isOpen', 'selectedItem', 'type'];\n/**\n * @param {Object} state the state object\n * @return {Object} state that is relevant to downshift\n */\nfunction pickState(state) {\n  if (state === void 0) {\n    state = {};\n  }\n  var result = {};\n  stateKeys.forEach(function (k) {\n    if (state.hasOwnProperty(k)) {\n      result[k] = state[k];\n    }\n  });\n  return result;\n}\n\n/**\n * This will perform a shallow merge of the given state object\n * with the state coming from props\n * (for the controlled component scenario)\n * This is used in state updater functions so they're referencing\n * the right state regardless of where it comes from.\n *\n * @param {Object} state The state of the component/hook.\n * @param {Object} props The props that may contain controlled values.\n * @returns {Object} The merged controlled state.\n */\nfunction getState(state, props) {\n  return Object.keys(state).reduce(function (prevState, key) {\n    prevState[key] = isControlledProp(props, key) ? props[key] : state[key];\n    return prevState;\n  }, {});\n}\n\n/**\n * This determines whether a prop is a \"controlled prop\" meaning it is\n * state which is controlled by the outside of this component rather\n * than within this component.\n *\n * @param {Object} props The props that may contain controlled values.\n * @param {String} key the key to check\n * @return {Boolean} whether it is a controlled controlled prop\n */\nfunction isControlledProp(props, key) {\n  return props[key] !== undefined;\n}\n\n/**\n * Normalizes the 'key' property of a KeyboardEvent in IE/Edge\n * @param {Object} event a keyboardEvent object\n * @return {String} keyboard key\n */\nfunction normalizeArrowKey(event) {\n  var key = event.key,\n    keyCode = event.keyCode;\n  /* istanbul ignore next (ie) */\n  if (keyCode >= 37 && keyCode <= 40 && key.indexOf('Arrow') !== 0) {\n    return \"Arrow\" + key;\n  }\n  return key;\n}\n\n/**\n * Simple check if the value passed is object literal\n * @param {*} obj any things\n * @return {Boolean} whether it's object literal\n */\nfunction isPlainObject(obj) {\n  return Object.prototype.toString.call(obj) === '[object Object]';\n}\n\n/**\n * Returns the new index in the list, in a circular way. If next value is out of bonds from the total,\n * it will wrap to either 0 or itemCount - 1.\n *\n * @param {number} moveAmount Number of positions to move. Negative to move backwards, positive forwards.\n * @param {number} baseIndex The initial position to move from.\n * @param {number} itemCount The total number of items.\n * @param {Function} getItemNodeFromIndex Used to check if item is disabled.\n * @param {boolean} circular Specify if navigation is circular. Default is true.\n * @returns {number} The new index after the move.\n */\nfunction getNextWrappingIndex(moveAmount, baseIndex, itemCount, getItemNodeFromIndex, circular) {\n  if (circular === void 0) {\n    circular = true;\n  }\n  if (itemCount === 0) {\n    return -1;\n  }\n  var itemsLastIndex = itemCount - 1;\n  if (typeof baseIndex !== 'number' || baseIndex < 0 || baseIndex >= itemCount) {\n    baseIndex = moveAmount > 0 ? -1 : itemsLastIndex + 1;\n  }\n  var newIndex = baseIndex + moveAmount;\n  if (newIndex < 0) {\n    newIndex = circular ? itemsLastIndex : 0;\n  } else if (newIndex > itemsLastIndex) {\n    newIndex = circular ? 0 : itemsLastIndex;\n  }\n  var nonDisabledNewIndex = getNextNonDisabledIndex(moveAmount, newIndex, itemCount, getItemNodeFromIndex, circular);\n  if (nonDisabledNewIndex === -1) {\n    return baseIndex >= itemCount ? -1 : baseIndex;\n  }\n  return nonDisabledNewIndex;\n}\n\n/**\n * Returns the next index in the list of an item that is not disabled.\n *\n * @param {number} moveAmount Number of positions to move. Negative to move backwards, positive forwards.\n * @param {number} baseIndex The initial position to move from.\n * @param {number} itemCount The total number of items.\n * @param {Function} getItemNodeFromIndex Used to check if item is disabled.\n * @param {boolean} circular Specify if navigation is circular. Default is true.\n * @returns {number} The new index. Returns baseIndex if item is not disabled. Returns next non-disabled item otherwise. If no non-disabled found it will return -1.\n */\nfunction getNextNonDisabledIndex(moveAmount, baseIndex, itemCount, getItemNodeFromIndex, circular) {\n  var currentElementNode = getItemNodeFromIndex(baseIndex);\n  if (!currentElementNode || !currentElementNode.hasAttribute('disabled')) {\n    return baseIndex;\n  }\n  if (moveAmount > 0) {\n    for (var index = baseIndex + 1; index < itemCount; index++) {\n      if (!getItemNodeFromIndex(index).hasAttribute('disabled')) {\n        return index;\n      }\n    }\n  } else {\n    for (var _index = baseIndex - 1; _index >= 0; _index--) {\n      if (!getItemNodeFromIndex(_index).hasAttribute('disabled')) {\n        return _index;\n      }\n    }\n  }\n  if (circular) {\n    return moveAmount > 0 ? getNextNonDisabledIndex(1, 0, itemCount, getItemNodeFromIndex, false) : getNextNonDisabledIndex(-1, itemCount - 1, itemCount, getItemNodeFromIndex, false);\n  }\n  return -1;\n}\n\n/**\n * Checks if event target is within the downshift elements.\n *\n * @param {EventTarget} target Target to check.\n * @param {HTMLElement[]} downshiftElements The elements that form downshift (list, toggle button etc).\n * @param {Window} environment The window context where downshift renders.\n * @param {boolean} checkActiveElement Whether to also check activeElement.\n *\n * @returns {boolean} Whether or not the target is within downshift elements.\n */\nfunction targetWithinDownshift(target, downshiftElements, environment, checkActiveElement) {\n  if (checkActiveElement === void 0) {\n    checkActiveElement = true;\n  }\n  return downshiftElements.some(function (contextNode) {\n    return contextNode && (isOrContainsNode(contextNode, target, environment) || checkActiveElement && isOrContainsNode(contextNode, environment.document.activeElement, environment));\n  });\n}\n\n// eslint-disable-next-line import/no-mutable-exports\nvar validateControlledUnchanged = noop;\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n  validateControlledUnchanged = function validateControlledUnchanged(state, prevProps, nextProps) {\n    var warningDescription = \"This prop should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled Downshift element for the lifetime of the component. More info: https://github.com/downshift-js/downshift#control-props\";\n    Object.keys(state).forEach(function (propKey) {\n      if (prevProps[propKey] !== undefined && nextProps[propKey] === undefined) {\n        // eslint-disable-next-line no-console\n        console.error(\"downshift: A component has changed the controlled prop \\\"\" + propKey + \"\\\" to be uncontrolled. \" + warningDescription);\n      } else if (prevProps[propKey] === undefined && nextProps[propKey] !== undefined) {\n        // eslint-disable-next-line no-console\n        console.error(\"downshift: A component has changed the uncontrolled prop \\\"\" + propKey + \"\\\" to be controlled. \" + warningDescription);\n      }\n    });\n  };\n}\n\nvar cleanupStatus = debounce(function (documentProp) {\n  getStatusDiv(documentProp).textContent = '';\n}, 500);\n\n/**\n * @param {String} status the status message\n * @param {Object} documentProp document passed by the user.\n */\nfunction setStatus(status, documentProp) {\n  var div = getStatusDiv(documentProp);\n  if (!status) {\n    return;\n  }\n  div.textContent = status;\n  cleanupStatus(documentProp);\n}\n\n/**\n * Get the status node or create it if it does not already exist.\n * @param {Object} documentProp document passed by the user.\n * @return {HTMLElement} the status node.\n */\nfunction getStatusDiv(documentProp) {\n  if (documentProp === void 0) {\n    documentProp = document;\n  }\n  var statusDiv = documentProp.getElementById('a11y-status-message');\n  if (statusDiv) {\n    return statusDiv;\n  }\n  statusDiv = documentProp.createElement('div');\n  statusDiv.setAttribute('id', 'a11y-status-message');\n  statusDiv.setAttribute('role', 'status');\n  statusDiv.setAttribute('aria-live', 'polite');\n  statusDiv.setAttribute('aria-relevant', 'additions text');\n  Object.assign(statusDiv.style, {\n    border: '0',\n    clip: 'rect(0 0 0 0)',\n    height: '1px',\n    margin: '-1px',\n    overflow: 'hidden',\n    padding: '0',\n    position: 'absolute',\n    width: '1px'\n  });\n  documentProp.body.appendChild(statusDiv);\n  return statusDiv;\n}\n\nvar unknown = process.env.NODE_ENV !== \"production\" ? '__autocomplete_unknown__' : 0;\nvar mouseUp = process.env.NODE_ENV !== \"production\" ? '__autocomplete_mouseup__' : 1;\nvar itemMouseEnter = process.env.NODE_ENV !== \"production\" ? '__autocomplete_item_mouseenter__' : 2;\nvar keyDownArrowUp = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_arrow_up__' : 3;\nvar keyDownArrowDown = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_arrow_down__' : 4;\nvar keyDownEscape = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_escape__' : 5;\nvar keyDownEnter = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_enter__' : 6;\nvar keyDownHome = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_home__' : 7;\nvar keyDownEnd = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_end__' : 8;\nvar clickItem = process.env.NODE_ENV !== \"production\" ? '__autocomplete_click_item__' : 9;\nvar blurInput = process.env.NODE_ENV !== \"production\" ? '__autocomplete_blur_input__' : 10;\nvar changeInput = process.env.NODE_ENV !== \"production\" ? '__autocomplete_change_input__' : 11;\nvar keyDownSpaceButton = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_space_button__' : 12;\nvar clickButton = process.env.NODE_ENV !== \"production\" ? '__autocomplete_click_button__' : 13;\nvar blurButton = process.env.NODE_ENV !== \"production\" ? '__autocomplete_blur_button__' : 14;\nvar controlledPropUpdatedSelectedItem = process.env.NODE_ENV !== \"production\" ? '__autocomplete_controlled_prop_updated_selected_item__' : 15;\nvar touchEnd = process.env.NODE_ENV !== \"production\" ? '__autocomplete_touchend__' : 16;\n\nvar stateChangeTypes$3 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  unknown: unknown,\n  mouseUp: mouseUp,\n  itemMouseEnter: itemMouseEnter,\n  keyDownArrowUp: keyDownArrowUp,\n  keyDownArrowDown: keyDownArrowDown,\n  keyDownEscape: keyDownEscape,\n  keyDownEnter: keyDownEnter,\n  keyDownHome: keyDownHome,\n  keyDownEnd: keyDownEnd,\n  clickItem: clickItem,\n  blurInput: blurInput,\n  changeInput: changeInput,\n  keyDownSpaceButton: keyDownSpaceButton,\n  clickButton: clickButton,\n  blurButton: blurButton,\n  controlledPropUpdatedSelectedItem: controlledPropUpdatedSelectedItem,\n  touchEnd: touchEnd\n});\n\nvar _excluded$4 = [\"refKey\", \"ref\"],\n  _excluded2$3 = [\"onClick\", \"onPress\", \"onKeyDown\", \"onKeyUp\", \"onBlur\"],\n  _excluded3$2 = [\"onKeyDown\", \"onBlur\", \"onChange\", \"onInput\", \"onChangeText\"],\n  _excluded4$1 = [\"refKey\", \"ref\"],\n  _excluded5 = [\"onMouseMove\", \"onMouseDown\", \"onClick\", \"onPress\", \"index\", \"item\"];\nvar Downshift = /*#__PURE__*/function () {\n  var Downshift = /*#__PURE__*/function (_Component) {\n    _inheritsLoose(Downshift, _Component);\n    function Downshift(_props) {\n      var _this;\n      _this = _Component.call(this, _props) || this;\n      // fancy destructuring + defaults + aliases\n      // this basically says each value of state should either be set to\n      // the initial value or the default value if the initial value is not provided\n      _this.id = _this.props.id || \"downshift-\" + generateId();\n      _this.menuId = _this.props.menuId || _this.id + \"-menu\";\n      _this.labelId = _this.props.labelId || _this.id + \"-label\";\n      _this.inputId = _this.props.inputId || _this.id + \"-input\";\n      _this.getItemId = _this.props.getItemId || function (index) {\n        return _this.id + \"-item-\" + index;\n      };\n      _this.input = null;\n      _this.items = [];\n      // itemCount can be changed asynchronously\n      // from within downshift (so it can't come from a prop)\n      // this is why we store it as an instance and use\n      // getItemCount rather than just use items.length\n      // (to support windowing + async)\n      _this.itemCount = null;\n      _this.previousResultCount = 0;\n      _this.timeoutIds = [];\n      /**\n       * @param {Function} fn the function to call after the time\n       * @param {Number} time the time to wait\n       */\n      _this.internalSetTimeout = function (fn, time) {\n        var id = setTimeout(function () {\n          _this.timeoutIds = _this.timeoutIds.filter(function (i) {\n            return i !== id;\n          });\n          fn();\n        }, time);\n        _this.timeoutIds.push(id);\n      };\n      _this.setItemCount = function (count) {\n        _this.itemCount = count;\n      };\n      _this.unsetItemCount = function () {\n        _this.itemCount = null;\n      };\n      _this.setHighlightedIndex = function (highlightedIndex, otherStateToSet) {\n        if (highlightedIndex === void 0) {\n          highlightedIndex = _this.props.defaultHighlightedIndex;\n        }\n        if (otherStateToSet === void 0) {\n          otherStateToSet = {};\n        }\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState(_extends({\n          highlightedIndex: highlightedIndex\n        }, otherStateToSet));\n      };\n      _this.clearSelection = function (cb) {\n        _this.internalSetState({\n          selectedItem: null,\n          inputValue: '',\n          highlightedIndex: _this.props.defaultHighlightedIndex,\n          isOpen: _this.props.defaultIsOpen\n        }, cb);\n      };\n      _this.selectItem = function (item, otherStateToSet, cb) {\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState(_extends({\n          isOpen: _this.props.defaultIsOpen,\n          highlightedIndex: _this.props.defaultHighlightedIndex,\n          selectedItem: item,\n          inputValue: _this.props.itemToString(item)\n        }, otherStateToSet), cb);\n      };\n      _this.selectItemAtIndex = function (itemIndex, otherStateToSet, cb) {\n        var item = _this.items[itemIndex];\n        if (item == null) {\n          return;\n        }\n        _this.selectItem(item, otherStateToSet, cb);\n      };\n      _this.selectHighlightedItem = function (otherStateToSet, cb) {\n        return _this.selectItemAtIndex(_this.getState().highlightedIndex, otherStateToSet, cb);\n      };\n      // any piece of our state can live in two places:\n      // 1. Uncontrolled: it's internal (this.state)\n      //    We will call this.setState to update that state\n      // 2. Controlled: it's external (this.props)\n      //    We will call this.props.onStateChange to update that state\n      //\n      // In addition, we'll call this.props.onChange if the\n      // selectedItem is changed.\n      _this.internalSetState = function (stateToSet, cb) {\n        var isItemSelected, onChangeArg;\n        var onStateChangeArg = {};\n        var isStateToSetFunction = typeof stateToSet === 'function';\n\n        // we want to call `onInputValueChange` before the `setState` call\n        // so someone controlling the `inputValue` state gets notified of\n        // the input change as soon as possible. This avoids issues with\n        // preserving the cursor position.\n        // See https://github.com/downshift-js/downshift/issues/217 for more info.\n        if (!isStateToSetFunction && stateToSet.hasOwnProperty('inputValue')) {\n          _this.props.onInputValueChange(stateToSet.inputValue, _extends({}, _this.getStateAndHelpers(), stateToSet));\n        }\n        return _this.setState(function (state) {\n          state = _this.getState(state);\n          var newStateToSet = isStateToSetFunction ? stateToSet(state) : stateToSet;\n\n          // Your own function that could modify the state that will be set.\n          newStateToSet = _this.props.stateReducer(state, newStateToSet);\n\n          // checks if an item is selected, regardless of if it's different from\n          // what was selected before\n          // used to determine if onSelect and onChange callbacks should be called\n          isItemSelected = newStateToSet.hasOwnProperty('selectedItem');\n          // this keeps track of the object we want to call with setState\n          var nextState = {};\n          // we need to call on change if the outside world is controlling any of our state\n          // and we're trying to update that state. OR if the selection has changed and we're\n          // trying to update the selection\n          if (isItemSelected && newStateToSet.selectedItem !== state.selectedItem) {\n            onChangeArg = newStateToSet.selectedItem;\n          }\n          newStateToSet.type = newStateToSet.type || unknown;\n          Object.keys(newStateToSet).forEach(function (key) {\n            // onStateChangeArg should only have the state that is\n            // actually changing\n            if (state[key] !== newStateToSet[key]) {\n              onStateChangeArg[key] = newStateToSet[key];\n            }\n            // the type is useful for the onStateChangeArg\n            // but we don't actually want to set it in internal state.\n            // this is an undocumented feature for now... Not all internalSetState\n            // calls support it and I'm not certain we want them to yet.\n            // But it enables users controlling the isOpen state to know when\n            // the isOpen state changes due to mouseup events which is quite handy.\n            if (key === 'type') {\n              return;\n            }\n            newStateToSet[key];\n            // if it's coming from props, then we don't care to set it internally\n            if (!isControlledProp(_this.props, key)) {\n              nextState[key] = newStateToSet[key];\n            }\n          });\n\n          // if stateToSet is a function, then we weren't able to call onInputValueChange\n          // earlier, so we'll call it now that we know what the inputValue state will be.\n          if (isStateToSetFunction && newStateToSet.hasOwnProperty('inputValue')) {\n            _this.props.onInputValueChange(newStateToSet.inputValue, _extends({}, _this.getStateAndHelpers(), newStateToSet));\n          }\n          return nextState;\n        }, function () {\n          // call the provided callback if it's a function\n          cbToCb(cb)();\n\n          // only call the onStateChange and onChange callbacks if\n          // we have relevant information to pass them.\n          var hasMoreStateThanType = Object.keys(onStateChangeArg).length > 1;\n          if (hasMoreStateThanType) {\n            _this.props.onStateChange(onStateChangeArg, _this.getStateAndHelpers());\n          }\n          if (isItemSelected) {\n            _this.props.onSelect(stateToSet.selectedItem, _this.getStateAndHelpers());\n          }\n          if (onChangeArg !== undefined) {\n            _this.props.onChange(onChangeArg, _this.getStateAndHelpers());\n          }\n          // this is currently undocumented and therefore subject to change\n          // We'll try to not break it, but just be warned.\n          _this.props.onUserAction(onStateChangeArg, _this.getStateAndHelpers());\n        });\n      };\n      //////////////////////////// ROOT\n      _this.rootRef = function (node) {\n        return _this._rootNode = node;\n      };\n      _this.getRootProps = function (_temp, _temp2) {\n        var _extends2;\n        var _ref = _temp === void 0 ? {} : _temp,\n          _ref$refKey = _ref.refKey,\n          refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey,\n          ref = _ref.ref,\n          rest = _objectWithoutPropertiesLoose(_ref, _excluded$4);\n        var _ref2 = _temp2 === void 0 ? {} : _temp2,\n          _ref2$suppressRefErro = _ref2.suppressRefError,\n          suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;\n        // this is used in the render to know whether the user has called getRootProps.\n        // It uses that to know whether to apply the props automatically\n        _this.getRootProps.called = true;\n        _this.getRootProps.refKey = refKey;\n        _this.getRootProps.suppressRefError = suppressRefError;\n        var _this$getState = _this.getState(),\n          isOpen = _this$getState.isOpen;\n        return _extends((_extends2 = {}, _extends2[refKey] = handleRefs(ref, _this.rootRef), _extends2.role = 'combobox', _extends2['aria-expanded'] = isOpen, _extends2['aria-haspopup'] = 'listbox', _extends2['aria-owns'] = isOpen ? _this.menuId : null, _extends2['aria-labelledby'] = _this.labelId, _extends2), rest);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ROOT\n      _this.keyDownHandlers = {\n        ArrowDown: function ArrowDown(event) {\n          var _this2 = this;\n          event.preventDefault();\n          if (this.getState().isOpen) {\n            var amount = event.shiftKey ? 5 : 1;\n            this.moveHighlightedIndex(amount, {\n              type: keyDownArrowDown\n            });\n          } else {\n            this.internalSetState({\n              isOpen: true,\n              type: keyDownArrowDown\n            }, function () {\n              var itemCount = _this2.getItemCount();\n              if (itemCount > 0) {\n                var _this2$getState = _this2.getState(),\n                  highlightedIndex = _this2$getState.highlightedIndex;\n                var nextHighlightedIndex = getNextWrappingIndex(1, highlightedIndex, itemCount, function (index) {\n                  return _this2.getItemNodeFromIndex(index);\n                });\n                _this2.setHighlightedIndex(nextHighlightedIndex, {\n                  type: keyDownArrowDown\n                });\n              }\n            });\n          }\n        },\n        ArrowUp: function ArrowUp(event) {\n          var _this3 = this;\n          event.preventDefault();\n          if (this.getState().isOpen) {\n            var amount = event.shiftKey ? -5 : -1;\n            this.moveHighlightedIndex(amount, {\n              type: keyDownArrowUp\n            });\n          } else {\n            this.internalSetState({\n              isOpen: true,\n              type: keyDownArrowUp\n            }, function () {\n              var itemCount = _this3.getItemCount();\n              if (itemCount > 0) {\n                var _this3$getState = _this3.getState(),\n                  highlightedIndex = _this3$getState.highlightedIndex;\n                var nextHighlightedIndex = getNextWrappingIndex(-1, highlightedIndex, itemCount, function (index) {\n                  return _this3.getItemNodeFromIndex(index);\n                });\n                _this3.setHighlightedIndex(nextHighlightedIndex, {\n                  type: keyDownArrowUp\n                });\n              }\n            });\n          }\n        },\n        Enter: function Enter(event) {\n          if (event.which === 229) {\n            return;\n          }\n          var _this$getState2 = this.getState(),\n            isOpen = _this$getState2.isOpen,\n            highlightedIndex = _this$getState2.highlightedIndex;\n          if (isOpen && highlightedIndex != null) {\n            event.preventDefault();\n            var item = this.items[highlightedIndex];\n            var itemNode = this.getItemNodeFromIndex(highlightedIndex);\n            if (item == null || itemNode && itemNode.hasAttribute('disabled')) {\n              return;\n            }\n            this.selectHighlightedItem({\n              type: keyDownEnter\n            });\n          }\n        },\n        Escape: function Escape(event) {\n          event.preventDefault();\n          this.reset(_extends({\n            type: keyDownEscape\n          }, !this.state.isOpen && {\n            selectedItem: null,\n            inputValue: ''\n          }));\n        }\n      };\n      //////////////////////////// BUTTON\n      _this.buttonKeyDownHandlers = _extends({}, _this.keyDownHandlers, {\n        ' ': function _(event) {\n          event.preventDefault();\n          this.toggleMenu({\n            type: keyDownSpaceButton\n          });\n        }\n      });\n      _this.inputKeyDownHandlers = _extends({}, _this.keyDownHandlers, {\n        Home: function Home(event) {\n          var _this4 = this;\n          var _this$getState3 = this.getState(),\n            isOpen = _this$getState3.isOpen;\n          if (!isOpen) {\n            return;\n          }\n          event.preventDefault();\n          var itemCount = this.getItemCount();\n          if (itemCount <= 0 || !isOpen) {\n            return;\n          }\n\n          // get next non-disabled starting downwards from 0 if that's disabled.\n          var newHighlightedIndex = getNextNonDisabledIndex(1, 0, itemCount, function (index) {\n            return _this4.getItemNodeFromIndex(index);\n          }, false);\n          this.setHighlightedIndex(newHighlightedIndex, {\n            type: keyDownHome\n          });\n        },\n        End: function End(event) {\n          var _this5 = this;\n          var _this$getState4 = this.getState(),\n            isOpen = _this$getState4.isOpen;\n          if (!isOpen) {\n            return;\n          }\n          event.preventDefault();\n          var itemCount = this.getItemCount();\n          if (itemCount <= 0 || !isOpen) {\n            return;\n          }\n\n          // get next non-disabled starting upwards from last index if that's disabled.\n          var newHighlightedIndex = getNextNonDisabledIndex(-1, itemCount - 1, itemCount, function (index) {\n            return _this5.getItemNodeFromIndex(index);\n          }, false);\n          this.setHighlightedIndex(newHighlightedIndex, {\n            type: keyDownEnd\n          });\n        }\n      });\n      _this.getToggleButtonProps = function (_temp3) {\n        var _ref3 = _temp3 === void 0 ? {} : _temp3,\n          onClick = _ref3.onClick;\n          _ref3.onPress;\n          var onKeyDown = _ref3.onKeyDown,\n          onKeyUp = _ref3.onKeyUp,\n          onBlur = _ref3.onBlur,\n          rest = _objectWithoutPropertiesLoose(_ref3, _excluded2$3);\n        var _this$getState5 = _this.getState(),\n          isOpen = _this$getState5.isOpen;\n        var enabledEventHandlers = {\n          onClick: callAllEventHandlers(onClick, _this.buttonHandleClick),\n          onKeyDown: callAllEventHandlers(onKeyDown, _this.buttonHandleKeyDown),\n          onKeyUp: callAllEventHandlers(onKeyUp, _this.buttonHandleKeyUp),\n          onBlur: callAllEventHandlers(onBlur, _this.buttonHandleBlur)\n        };\n        var eventHandlers = rest.disabled ? {} : enabledEventHandlers;\n        return _extends({\n          type: 'button',\n          role: 'button',\n          'aria-label': isOpen ? 'close menu' : 'open menu',\n          'aria-haspopup': true,\n          'data-toggle': true\n        }, eventHandlers, rest);\n      };\n      _this.buttonHandleKeyUp = function (event) {\n        // Prevent click event from emitting in Firefox\n        event.preventDefault();\n      };\n      _this.buttonHandleKeyDown = function (event) {\n        var key = normalizeArrowKey(event);\n        if (_this.buttonKeyDownHandlers[key]) {\n          _this.buttonKeyDownHandlers[key].call(_assertThisInitialized(_this), event);\n        }\n      };\n      _this.buttonHandleClick = function (event) {\n        event.preventDefault();\n        // handle odd case for Safari and Firefox which\n        // don't give the button the focus properly.\n        /* istanbul ignore if (can't reasonably test this) */\n        if (_this.props.environment.document.activeElement === _this.props.environment.document.body) {\n          event.target.focus();\n        }\n        // to simplify testing components that use downshift, we'll not wrap this in a setTimeout\n        // if the NODE_ENV is test. With the proper build system, this should be dead code eliminated\n        // when building for production and should therefore have no impact on production code.\n        if (process.env.NODE_ENV === 'test') {\n          _this.toggleMenu({\n            type: clickButton\n          });\n        } else {\n          // Ensure that toggle of menu occurs after the potential blur event in iOS\n          _this.internalSetTimeout(function () {\n            return _this.toggleMenu({\n              type: clickButton\n            });\n          });\n        }\n      };\n      _this.buttonHandleBlur = function (event) {\n        var blurTarget = event.target; // Save blur target for comparison with activeElement later\n        // Need setTimeout, so that when the user presses Tab, the activeElement is the next focused element, not body element\n        _this.internalSetTimeout(function () {\n          if (!_this.isMouseDown && (_this.props.environment.document.activeElement == null || _this.props.environment.document.activeElement.id !== _this.inputId) && _this.props.environment.document.activeElement !== blurTarget // Do nothing if we refocus the same element again (to solve issue in Safari on iOS)\n          ) {\n            _this.reset({\n              type: blurButton\n            });\n          }\n        });\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ BUTTON\n      /////////////////////////////// LABEL\n      _this.getLabelProps = function (props) {\n        return _extends({\n          htmlFor: _this.inputId,\n          id: _this.labelId\n        }, props);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ LABEL\n      /////////////////////////////// INPUT\n      _this.getInputProps = function (_temp4) {\n        var _ref4 = _temp4 === void 0 ? {} : _temp4,\n          onKeyDown = _ref4.onKeyDown,\n          onBlur = _ref4.onBlur,\n          onChange = _ref4.onChange,\n          onInput = _ref4.onInput;\n          _ref4.onChangeText;\n          var rest = _objectWithoutPropertiesLoose(_ref4, _excluded3$2);\n        var onChangeKey;\n        var eventHandlers = {};\n\n        /* istanbul ignore next (preact) */\n        {\n          onChangeKey = 'onChange';\n        }\n        var _this$getState6 = _this.getState(),\n          inputValue = _this$getState6.inputValue,\n          isOpen = _this$getState6.isOpen,\n          highlightedIndex = _this$getState6.highlightedIndex;\n        if (!rest.disabled) {\n          var _eventHandlers;\n          eventHandlers = (_eventHandlers = {}, _eventHandlers[onChangeKey] = callAllEventHandlers(onChange, onInput, _this.inputHandleChange), _eventHandlers.onKeyDown = callAllEventHandlers(onKeyDown, _this.inputHandleKeyDown), _eventHandlers.onBlur = callAllEventHandlers(onBlur, _this.inputHandleBlur), _eventHandlers);\n        }\n        return _extends({\n          'aria-autocomplete': 'list',\n          'aria-activedescendant': isOpen && typeof highlightedIndex === 'number' && highlightedIndex >= 0 ? _this.getItemId(highlightedIndex) : null,\n          'aria-controls': isOpen ? _this.menuId : null,\n          'aria-labelledby': rest && rest['aria-label'] ? undefined : _this.labelId,\n          // https://developer.mozilla.org/en-US/docs/Web/Security/Securing_your_site/Turning_off_form_autocompletion\n          // revert back since autocomplete=\"nope\" is ignored on latest Chrome and Opera\n          autoComplete: 'off',\n          value: inputValue,\n          id: _this.inputId\n        }, eventHandlers, rest);\n      };\n      _this.inputHandleKeyDown = function (event) {\n        var key = normalizeArrowKey(event);\n        if (key && _this.inputKeyDownHandlers[key]) {\n          _this.inputKeyDownHandlers[key].call(_assertThisInitialized(_this), event);\n        }\n      };\n      _this.inputHandleChange = function (event) {\n        _this.internalSetState({\n          type: changeInput,\n          isOpen: true,\n          inputValue: event.target.value,\n          highlightedIndex: _this.props.defaultHighlightedIndex\n        });\n      };\n      _this.inputHandleBlur = function () {\n        // Need setTimeout, so that when the user presses Tab, the activeElement is the next focused element, not the body element\n        _this.internalSetTimeout(function () {\n          var downshiftButtonIsActive = _this.props.environment.document && !!_this.props.environment.document.activeElement && !!_this.props.environment.document.activeElement.dataset && _this.props.environment.document.activeElement.dataset.toggle && _this._rootNode && _this._rootNode.contains(_this.props.environment.document.activeElement);\n          if (!_this.isMouseDown && !downshiftButtonIsActive) {\n            _this.reset({\n              type: blurInput\n            });\n          }\n        });\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ INPUT\n      /////////////////////////////// MENU\n      _this.menuRef = function (node) {\n        _this._menuNode = node;\n      };\n      _this.getMenuProps = function (_temp5, _temp6) {\n        var _extends3;\n        var _ref5 = _temp5 === void 0 ? {} : _temp5,\n          _ref5$refKey = _ref5.refKey,\n          refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey,\n          ref = _ref5.ref,\n          props = _objectWithoutPropertiesLoose(_ref5, _excluded4$1);\n        var _ref6 = _temp6 === void 0 ? {} : _temp6,\n          _ref6$suppressRefErro = _ref6.suppressRefError,\n          suppressRefError = _ref6$suppressRefErro === void 0 ? false : _ref6$suppressRefErro;\n        _this.getMenuProps.called = true;\n        _this.getMenuProps.refKey = refKey;\n        _this.getMenuProps.suppressRefError = suppressRefError;\n        return _extends((_extends3 = {}, _extends3[refKey] = handleRefs(ref, _this.menuRef), _extends3.role = 'listbox', _extends3['aria-labelledby'] = props && props['aria-label'] ? null : _this.labelId, _extends3.id = _this.menuId, _extends3), props);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ MENU\n      /////////////////////////////// ITEM\n      _this.getItemProps = function (_temp7) {\n        var _enabledEventHandlers;\n        var _ref7 = _temp7 === void 0 ? {} : _temp7,\n          onMouseMove = _ref7.onMouseMove,\n          onMouseDown = _ref7.onMouseDown,\n          onClick = _ref7.onClick;\n          _ref7.onPress;\n          var index = _ref7.index,\n          _ref7$item = _ref7.item,\n          item = _ref7$item === void 0 ? process.env.NODE_ENV === 'production' ? /* istanbul ignore next */undefined : requiredProp('getItemProps', 'item') : _ref7$item,\n          rest = _objectWithoutPropertiesLoose(_ref7, _excluded5);\n        if (index === undefined) {\n          _this.items.push(item);\n          index = _this.items.indexOf(item);\n        } else {\n          _this.items[index] = item;\n        }\n        var onSelectKey = 'onClick';\n        var customClickHandler = onClick;\n        var enabledEventHandlers = (_enabledEventHandlers = {\n          // onMouseMove is used over onMouseEnter here. onMouseMove\n          // is only triggered on actual mouse movement while onMouseEnter\n          // can fire on DOM changes, interrupting keyboard navigation\n          onMouseMove: callAllEventHandlers(onMouseMove, function () {\n            if (index === _this.getState().highlightedIndex) {\n              return;\n            }\n            _this.setHighlightedIndex(index, {\n              type: itemMouseEnter\n            });\n\n            // We never want to manually scroll when changing state based\n            // on `onMouseMove` because we will be moving the element out\n            // from under the user which is currently scrolling/moving the\n            // cursor\n            _this.avoidScrolling = true;\n            _this.internalSetTimeout(function () {\n              return _this.avoidScrolling = false;\n            }, 250);\n          }),\n          onMouseDown: callAllEventHandlers(onMouseDown, function (event) {\n            // This prevents the activeElement from being changed\n            // to the item so it can remain with the current activeElement\n            // which is a more common use case.\n            event.preventDefault();\n          })\n        }, _enabledEventHandlers[onSelectKey] = callAllEventHandlers(customClickHandler, function () {\n          _this.selectItemAtIndex(index, {\n            type: clickItem\n          });\n        }), _enabledEventHandlers);\n\n        // Passing down the onMouseDown handler to prevent redirect\n        // of the activeElement if clicking on disabled items\n        var eventHandlers = rest.disabled ? {\n          onMouseDown: enabledEventHandlers.onMouseDown\n        } : enabledEventHandlers;\n        return _extends({\n          id: _this.getItemId(index),\n          role: 'option',\n          'aria-selected': _this.getState().highlightedIndex === index\n        }, eventHandlers, rest);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ITEM\n      _this.clearItems = function () {\n        _this.items = [];\n      };\n      _this.reset = function (otherStateToSet, cb) {\n        if (otherStateToSet === void 0) {\n          otherStateToSet = {};\n        }\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState(function (_ref8) {\n          var selectedItem = _ref8.selectedItem;\n          return _extends({\n            isOpen: _this.props.defaultIsOpen,\n            highlightedIndex: _this.props.defaultHighlightedIndex,\n            inputValue: _this.props.itemToString(selectedItem)\n          }, otherStateToSet);\n        }, cb);\n      };\n      _this.toggleMenu = function (otherStateToSet, cb) {\n        if (otherStateToSet === void 0) {\n          otherStateToSet = {};\n        }\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState(function (_ref9) {\n          var isOpen = _ref9.isOpen;\n          return _extends({\n            isOpen: !isOpen\n          }, isOpen && {\n            highlightedIndex: _this.props.defaultHighlightedIndex\n          }, otherStateToSet);\n        }, function () {\n          var _this$getState7 = _this.getState(),\n            isOpen = _this$getState7.isOpen,\n            highlightedIndex = _this$getState7.highlightedIndex;\n          if (isOpen) {\n            if (_this.getItemCount() > 0 && typeof highlightedIndex === 'number') {\n              _this.setHighlightedIndex(highlightedIndex, otherStateToSet);\n            }\n          }\n          cbToCb(cb)();\n        });\n      };\n      _this.openMenu = function (cb) {\n        _this.internalSetState({\n          isOpen: true\n        }, cb);\n      };\n      _this.closeMenu = function (cb) {\n        _this.internalSetState({\n          isOpen: false\n        }, cb);\n      };\n      _this.updateStatus = debounce(function () {\n        var state = _this.getState();\n        var item = _this.items[state.highlightedIndex];\n        var resultCount = _this.getItemCount();\n        var status = _this.props.getA11yStatusMessage(_extends({\n          itemToString: _this.props.itemToString,\n          previousResultCount: _this.previousResultCount,\n          resultCount: resultCount,\n          highlightedItem: item\n        }, state));\n        _this.previousResultCount = resultCount;\n        setStatus(status, _this.props.environment.document);\n      }, 200);\n      var _this$props = _this.props,\n        defaultHighlightedIndex = _this$props.defaultHighlightedIndex,\n        _this$props$initialHi = _this$props.initialHighlightedIndex,\n        _highlightedIndex = _this$props$initialHi === void 0 ? defaultHighlightedIndex : _this$props$initialHi,\n        defaultIsOpen = _this$props.defaultIsOpen,\n        _this$props$initialIs = _this$props.initialIsOpen,\n        _isOpen = _this$props$initialIs === void 0 ? defaultIsOpen : _this$props$initialIs,\n        _this$props$initialIn = _this$props.initialInputValue,\n        _inputValue = _this$props$initialIn === void 0 ? '' : _this$props$initialIn,\n        _this$props$initialSe = _this$props.initialSelectedItem,\n        _selectedItem = _this$props$initialSe === void 0 ? null : _this$props$initialSe;\n      var _state = _this.getState({\n        highlightedIndex: _highlightedIndex,\n        isOpen: _isOpen,\n        inputValue: _inputValue,\n        selectedItem: _selectedItem\n      });\n      if (_state.selectedItem != null && _this.props.initialInputValue === undefined) {\n        _state.inputValue = _this.props.itemToString(_state.selectedItem);\n      }\n      _this.state = _state;\n      return _this;\n    }\n    var _proto = Downshift.prototype;\n    /**\n     * Clear all running timeouts\n     */\n    _proto.internalClearTimeouts = function internalClearTimeouts() {\n      this.timeoutIds.forEach(function (id) {\n        clearTimeout(id);\n      });\n      this.timeoutIds = [];\n    }\n\n    /**\n     * Gets the state based on internal state or props\n     * If a state value is passed via props, then that\n     * is the value given, otherwise it's retrieved from\n     * stateToMerge\n     *\n     * @param {Object} stateToMerge defaults to this.state\n     * @return {Object} the state\n     */;\n    _proto.getState = function getState$1(stateToMerge) {\n      if (stateToMerge === void 0) {\n        stateToMerge = this.state;\n      }\n      return getState(stateToMerge, this.props);\n    };\n    _proto.getItemCount = function getItemCount() {\n      // things read better this way. They're in priority order:\n      // 1. `this.itemCount`\n      // 2. `this.props.itemCount`\n      // 3. `this.items.length`\n      var itemCount = this.items.length;\n      if (this.itemCount != null) {\n        itemCount = this.itemCount;\n      } else if (this.props.itemCount !== undefined) {\n        itemCount = this.props.itemCount;\n      }\n      return itemCount;\n    };\n    _proto.getItemNodeFromIndex = function getItemNodeFromIndex(index) {\n      return this.props.environment.document.getElementById(this.getItemId(index));\n    };\n    _proto.scrollHighlightedItemIntoView = function scrollHighlightedItemIntoView() {\n      /* istanbul ignore else (react-native) */\n      {\n        var node = this.getItemNodeFromIndex(this.getState().highlightedIndex);\n        this.props.scrollIntoView(node, this._menuNode);\n      }\n    };\n    _proto.moveHighlightedIndex = function moveHighlightedIndex(amount, otherStateToSet) {\n      var _this6 = this;\n      var itemCount = this.getItemCount();\n      var _this$getState8 = this.getState(),\n        highlightedIndex = _this$getState8.highlightedIndex;\n      if (itemCount > 0) {\n        var nextHighlightedIndex = getNextWrappingIndex(amount, highlightedIndex, itemCount, function (index) {\n          return _this6.getItemNodeFromIndex(index);\n        });\n        this.setHighlightedIndex(nextHighlightedIndex, otherStateToSet);\n      }\n    };\n    _proto.getStateAndHelpers = function getStateAndHelpers() {\n      var _this$getState9 = this.getState(),\n        highlightedIndex = _this$getState9.highlightedIndex,\n        inputValue = _this$getState9.inputValue,\n        selectedItem = _this$getState9.selectedItem,\n        isOpen = _this$getState9.isOpen;\n      var itemToString = this.props.itemToString;\n      var id = this.id;\n      var getRootProps = this.getRootProps,\n        getToggleButtonProps = this.getToggleButtonProps,\n        getLabelProps = this.getLabelProps,\n        getMenuProps = this.getMenuProps,\n        getInputProps = this.getInputProps,\n        getItemProps = this.getItemProps,\n        openMenu = this.openMenu,\n        closeMenu = this.closeMenu,\n        toggleMenu = this.toggleMenu,\n        selectItem = this.selectItem,\n        selectItemAtIndex = this.selectItemAtIndex,\n        selectHighlightedItem = this.selectHighlightedItem,\n        setHighlightedIndex = this.setHighlightedIndex,\n        clearSelection = this.clearSelection,\n        clearItems = this.clearItems,\n        reset = this.reset,\n        setItemCount = this.setItemCount,\n        unsetItemCount = this.unsetItemCount,\n        setState = this.internalSetState;\n      return {\n        // prop getters\n        getRootProps: getRootProps,\n        getToggleButtonProps: getToggleButtonProps,\n        getLabelProps: getLabelProps,\n        getMenuProps: getMenuProps,\n        getInputProps: getInputProps,\n        getItemProps: getItemProps,\n        // actions\n        reset: reset,\n        openMenu: openMenu,\n        closeMenu: closeMenu,\n        toggleMenu: toggleMenu,\n        selectItem: selectItem,\n        selectItemAtIndex: selectItemAtIndex,\n        selectHighlightedItem: selectHighlightedItem,\n        setHighlightedIndex: setHighlightedIndex,\n        clearSelection: clearSelection,\n        clearItems: clearItems,\n        setItemCount: setItemCount,\n        unsetItemCount: unsetItemCount,\n        setState: setState,\n        // props\n        itemToString: itemToString,\n        // derived\n        id: id,\n        // state\n        highlightedIndex: highlightedIndex,\n        inputValue: inputValue,\n        isOpen: isOpen,\n        selectedItem: selectedItem\n      };\n    };\n    _proto.componentDidMount = function componentDidMount() {\n      var _this7 = this;\n      /* istanbul ignore if (react-native) */\n      if (process.env.NODE_ENV !== 'production' && !false && this.getMenuProps.called && !this.getMenuProps.suppressRefError) {\n        validateGetMenuPropsCalledCorrectly(this._menuNode, this.getMenuProps);\n      }\n\n      /* istanbul ignore if (react-native) */\n      {\n        // this.isMouseDown helps us track whether the mouse is currently held down.\n        // This is useful when the user clicks on an item in the list, but holds the mouse\n        // down long enough for the list to disappear (because the blur event fires on the input)\n        // this.isMouseDown is used in the blur handler on the input to determine whether the blur event should\n        // trigger hiding the menu.\n        var onMouseDown = function onMouseDown() {\n          _this7.isMouseDown = true;\n        };\n        var onMouseUp = function onMouseUp(event) {\n          _this7.isMouseDown = false;\n          // if the target element or the activeElement is within a downshift node\n          // then we don't want to reset downshift\n          var contextWithinDownshift = targetWithinDownshift(event.target, [_this7._rootNode, _this7._menuNode], _this7.props.environment);\n          if (!contextWithinDownshift && _this7.getState().isOpen) {\n            _this7.reset({\n              type: mouseUp\n            }, function () {\n              return _this7.props.onOuterClick(_this7.getStateAndHelpers());\n            });\n          }\n        };\n        // Touching an element in iOS gives focus and hover states, but touching out of\n        // the element will remove hover, and persist the focus state, resulting in the\n        // blur event not being triggered.\n        // this.isTouchMove helps us track whether the user is tapping or swiping on a touch screen.\n        // If the user taps outside of Downshift, the component should be reset,\n        // but not if the user is swiping\n        var onTouchStart = function onTouchStart() {\n          _this7.isTouchMove = false;\n        };\n        var onTouchMove = function onTouchMove() {\n          _this7.isTouchMove = true;\n        };\n        var onTouchEnd = function onTouchEnd(event) {\n          var contextWithinDownshift = targetWithinDownshift(event.target, [_this7._rootNode, _this7._menuNode], _this7.props.environment, false);\n          if (!_this7.isTouchMove && !contextWithinDownshift && _this7.getState().isOpen) {\n            _this7.reset({\n              type: touchEnd\n            }, function () {\n              return _this7.props.onOuterClick(_this7.getStateAndHelpers());\n            });\n          }\n        };\n        var environment = this.props.environment;\n        environment.addEventListener('mousedown', onMouseDown);\n        environment.addEventListener('mouseup', onMouseUp);\n        environment.addEventListener('touchstart', onTouchStart);\n        environment.addEventListener('touchmove', onTouchMove);\n        environment.addEventListener('touchend', onTouchEnd);\n        this.cleanup = function () {\n          _this7.internalClearTimeouts();\n          _this7.updateStatus.cancel();\n          environment.removeEventListener('mousedown', onMouseDown);\n          environment.removeEventListener('mouseup', onMouseUp);\n          environment.removeEventListener('touchstart', onTouchStart);\n          environment.removeEventListener('touchmove', onTouchMove);\n          environment.removeEventListener('touchend', onTouchEnd);\n        };\n      }\n    };\n    _proto.shouldScroll = function shouldScroll(prevState, prevProps) {\n      var _ref10 = this.props.highlightedIndex === undefined ? this.getState() : this.props,\n        currentHighlightedIndex = _ref10.highlightedIndex;\n      var _ref11 = prevProps.highlightedIndex === undefined ? prevState : prevProps,\n        prevHighlightedIndex = _ref11.highlightedIndex;\n      var scrollWhenOpen = currentHighlightedIndex && this.getState().isOpen && !prevState.isOpen;\n      var scrollWhenNavigating = currentHighlightedIndex !== prevHighlightedIndex;\n      return scrollWhenOpen || scrollWhenNavigating;\n    };\n    _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n      if (process.env.NODE_ENV !== 'production') {\n        validateControlledUnchanged(this.state, prevProps, this.props);\n        /* istanbul ignore if (react-native) */\n        if (this.getMenuProps.called && !this.getMenuProps.suppressRefError) {\n          validateGetMenuPropsCalledCorrectly(this._menuNode, this.getMenuProps);\n        }\n      }\n      if (isControlledProp(this.props, 'selectedItem') && this.props.selectedItemChanged(prevProps.selectedItem, this.props.selectedItem)) {\n        this.internalSetState({\n          type: controlledPropUpdatedSelectedItem,\n          inputValue: this.props.itemToString(this.props.selectedItem)\n        });\n      }\n      if (!this.avoidScrolling && this.shouldScroll(prevState, prevProps)) {\n        this.scrollHighlightedItemIntoView();\n      }\n\n      /* istanbul ignore else (react-native) */\n      {\n        this.updateStatus();\n      }\n    };\n    _proto.componentWillUnmount = function componentWillUnmount() {\n      this.cleanup(); // avoids memory leak\n    };\n    _proto.render = function render() {\n      var children = unwrapArray(this.props.children, noop);\n      // because the items are rerendered every time we call the children\n      // we clear this out each render and it will be populated again as\n      // getItemProps is called.\n      this.clearItems();\n      // we reset this so we know whether the user calls getRootProps during\n      // this render. If they do then we don't need to do anything,\n      // if they don't then we need to clone the element they return and\n      // apply the props for them.\n      this.getRootProps.called = false;\n      this.getRootProps.refKey = undefined;\n      this.getRootProps.suppressRefError = undefined;\n      // we do something similar for getMenuProps\n      this.getMenuProps.called = false;\n      this.getMenuProps.refKey = undefined;\n      this.getMenuProps.suppressRefError = undefined;\n      // we do something similar for getLabelProps\n      this.getLabelProps.called = false;\n      // and something similar for getInputProps\n      this.getInputProps.called = false;\n      var element = unwrapArray(children(this.getStateAndHelpers()));\n      if (!element) {\n        return null;\n      }\n      if (this.getRootProps.called || this.props.suppressRefError) {\n        if (process.env.NODE_ENV !== 'production' && !this.getRootProps.suppressRefError && !this.props.suppressRefError) {\n          validateGetRootPropsCalledCorrectly(element, this.getRootProps);\n        }\n        return element;\n      } else if (isDOMElement(element)) {\n        // they didn't apply the root props, but we can clone\n        // this and apply the props ourselves\n        return /*#__PURE__*/cloneElement(element, this.getRootProps(getElementProps(element)));\n      }\n\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        // they didn't apply the root props, but they need to\n        // otherwise we can't query around the autocomplete\n\n        throw new Error('downshift: If you return a non-DOM element, you must apply the getRootProps function');\n      }\n\n      /* istanbul ignore next */\n      return undefined;\n    };\n    return Downshift;\n  }(Component);\n  Downshift.defaultProps = {\n    defaultHighlightedIndex: null,\n    defaultIsOpen: false,\n    getA11yStatusMessage: getA11yStatusMessage$1,\n    itemToString: function itemToString(i) {\n      if (i == null) {\n        return '';\n      }\n      if (process.env.NODE_ENV !== 'production' && isPlainObject(i) && !i.hasOwnProperty('toString')) {\n        // eslint-disable-next-line no-console\n        console.warn('downshift: An object was passed to the default implementation of `itemToString`. You should probably provide your own `itemToString` implementation. Please refer to the `itemToString` API documentation.', 'The object that was passed:', i);\n      }\n      return String(i);\n    },\n    onStateChange: noop,\n    onInputValueChange: noop,\n    onUserAction: noop,\n    onChange: noop,\n    onSelect: noop,\n    onOuterClick: noop,\n    selectedItemChanged: function selectedItemChanged(prevItem, item) {\n      return prevItem !== item;\n    },\n    environment: /* istanbul ignore next (ssr) */\n    typeof window === 'undefined' ? {} : window,\n    stateReducer: function stateReducer(state, stateToSet) {\n      return stateToSet;\n    },\n    suppressRefError: false,\n    scrollIntoView: scrollIntoView\n  };\n  Downshift.stateChangeTypes = stateChangeTypes$3;\n  return Downshift;\n}();\nprocess.env.NODE_ENV !== \"production\" ? Downshift.propTypes = {\n  children: PropTypes.func,\n  defaultHighlightedIndex: PropTypes.number,\n  defaultIsOpen: PropTypes.bool,\n  initialHighlightedIndex: PropTypes.number,\n  initialSelectedItem: PropTypes.any,\n  initialInputValue: PropTypes.string,\n  initialIsOpen: PropTypes.bool,\n  getA11yStatusMessage: PropTypes.func,\n  itemToString: PropTypes.func,\n  onChange: PropTypes.func,\n  onSelect: PropTypes.func,\n  onStateChange: PropTypes.func,\n  onInputValueChange: PropTypes.func,\n  onUserAction: PropTypes.func,\n  onOuterClick: PropTypes.func,\n  selectedItemChanged: PropTypes.func,\n  stateReducer: PropTypes.func,\n  itemCount: PropTypes.number,\n  id: PropTypes.string,\n  environment: PropTypes.shape({\n    addEventListener: PropTypes.func,\n    removeEventListener: PropTypes.func,\n    document: PropTypes.shape({\n      getElementById: PropTypes.func,\n      activeElement: PropTypes.any,\n      body: PropTypes.any\n    })\n  }),\n  suppressRefError: PropTypes.bool,\n  scrollIntoView: PropTypes.func,\n  // things we keep in state for uncontrolled components\n  // but can accept as props for controlled components\n  /* eslint-disable react/no-unused-prop-types */\n  selectedItem: PropTypes.any,\n  isOpen: PropTypes.bool,\n  inputValue: PropTypes.string,\n  highlightedIndex: PropTypes.number,\n  labelId: PropTypes.string,\n  inputId: PropTypes.string,\n  menuId: PropTypes.string,\n  getItemId: PropTypes.func\n  /* eslint-enable react/no-unused-prop-types */\n} : void 0;\nvar Downshift$1 = Downshift;\nfunction validateGetMenuPropsCalledCorrectly(node, _ref12) {\n  var refKey = _ref12.refKey;\n  if (!node) {\n    // eslint-disable-next-line no-console\n    console.error(\"downshift: The ref prop \\\"\" + refKey + \"\\\" from getMenuProps was not applied correctly on your menu element.\");\n  }\n}\nfunction validateGetRootPropsCalledCorrectly(element, _ref13) {\n  var refKey = _ref13.refKey;\n  var refKeySpecified = refKey !== 'ref';\n  var isComposite = !isDOMElement(element);\n  if (isComposite && !refKeySpecified && !isForwardRef(element)) {\n    // eslint-disable-next-line no-console\n    console.error('downshift: You returned a non-DOM element. You must specify a refKey in getRootProps');\n  } else if (!isComposite && refKeySpecified) {\n    // eslint-disable-next-line no-console\n    console.error(\"downshift: You returned a DOM element. You should not specify a refKey in getRootProps. You specified \\\"\" + refKey + \"\\\"\");\n  }\n  if (!isForwardRef(element) && !getElementProps(element)[refKey]) {\n    // eslint-disable-next-line no-console\n    console.error(\"downshift: You must apply the ref prop \\\"\" + refKey + \"\\\" from getRootProps onto your root element.\");\n  }\n}\n\nvar _excluded$3 = [\"isInitialMount\", \"highlightedIndex\", \"items\", \"environment\"];\nvar dropdownDefaultStateValues = {\n  highlightedIndex: -1,\n  isOpen: false,\n  selectedItem: null,\n  inputValue: ''\n};\nfunction callOnChangeProps(action, state, newState) {\n  var props = action.props,\n    type = action.type;\n  var changes = {};\n  Object.keys(state).forEach(function (key) {\n    invokeOnChangeHandler(key, action, state, newState);\n    if (newState[key] !== state[key]) {\n      changes[key] = newState[key];\n    }\n  });\n  if (props.onStateChange && Object.keys(changes).length) {\n    props.onStateChange(_extends({\n      type: type\n    }, changes));\n  }\n}\nfunction invokeOnChangeHandler(key, action, state, newState) {\n  var props = action.props,\n    type = action.type;\n  var handler = \"on\" + capitalizeString(key) + \"Change\";\n  if (props[handler] && newState[key] !== undefined && newState[key] !== state[key]) {\n    props[handler](_extends({\n      type: type\n    }, newState));\n  }\n}\n\n/**\n * Default state reducer that returns the changes.\n *\n * @param {Object} s state.\n * @param {Object} a action with changes.\n * @returns {Object} changes.\n */\nfunction stateReducer(s, a) {\n  return a.changes;\n}\n\n/**\n * Returns a message to be added to aria-live region when item is selected.\n *\n * @param {Object} selectionParameters Parameters required to build the message.\n * @returns {string} The a11y message.\n */\nfunction getA11ySelectionMessage(selectionParameters) {\n  var selectedItem = selectionParameters.selectedItem,\n    itemToStringLocal = selectionParameters.itemToString;\n  return selectedItem ? itemToStringLocal(selectedItem) + \" has been selected.\" : '';\n}\n\n/**\n * Debounced call for updating the a11y message.\n */\nvar updateA11yStatus = debounce(function (getA11yMessage, document) {\n  setStatus(getA11yMessage(), document);\n}, 200);\n\n// istanbul ignore next\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;\nfunction useElementIds(_ref) {\n  var _ref$id = _ref.id,\n    id = _ref$id === void 0 ? \"downshift-\" + generateId() : _ref$id,\n    labelId = _ref.labelId,\n    menuId = _ref.menuId,\n    getItemId = _ref.getItemId,\n    toggleButtonId = _ref.toggleButtonId,\n    inputId = _ref.inputId;\n  var elementIdsRef = useRef({\n    labelId: labelId || id + \"-label\",\n    menuId: menuId || id + \"-menu\",\n    getItemId: getItemId || function (index) {\n      return id + \"-item-\" + index;\n    },\n    toggleButtonId: toggleButtonId || id + \"-toggle-button\",\n    inputId: inputId || id + \"-input\"\n  });\n  return elementIdsRef.current;\n}\nfunction getItemAndIndex(itemProp, indexProp, items, errorMessage) {\n  var item, index;\n  if (itemProp === undefined) {\n    if (indexProp === undefined) {\n      throw new Error(errorMessage);\n    }\n    item = items[indexProp];\n    index = indexProp;\n  } else {\n    index = indexProp === undefined ? items.indexOf(itemProp) : indexProp;\n    item = itemProp;\n  }\n  return [item, index];\n}\nfunction itemToString(item) {\n  return item ? String(item) : '';\n}\nfunction isAcceptedCharacterKey(key) {\n  return /^\\S{1}$/.test(key);\n}\nfunction capitalizeString(string) {\n  return \"\" + string.slice(0, 1).toUpperCase() + string.slice(1);\n}\nfunction useLatestRef(val) {\n  var ref = useRef(val);\n  // technically this is not \"concurrent mode safe\" because we're manipulating\n  // the value during render (so it's not idempotent). However, the places this\n  // hook is used is to support memoizing callbacks which will be called\n  // *during* render, so we need the latest values *during* render.\n  // If not for this, then we'd probably want to use useLayoutEffect instead.\n  ref.current = val;\n  return ref;\n}\n\n/**\n * Computes the controlled state using a the previous state, props,\n * two reducers, one from downshift and an optional one from the user.\n * Also calls the onChange handlers for state values that have changed.\n *\n * @param {Function} reducer Reducer function from downshift.\n * @param {Object} initialState Initial state of the hook.\n * @param {Object} props The hook props.\n * @returns {Array} An array with the state and an action dispatcher.\n */\nfunction useEnhancedReducer(reducer, initialState, props) {\n  var prevStateRef = useRef();\n  var actionRef = useRef();\n  var enhancedReducer = useCallback(function (state, action) {\n    actionRef.current = action;\n    state = getState(state, action.props);\n    var changes = reducer(state, action);\n    var newState = action.props.stateReducer(state, _extends({}, action, {\n      changes: changes\n    }));\n    return newState;\n  }, [reducer]);\n  var _useReducer = useReducer(enhancedReducer, initialState),\n    state = _useReducer[0],\n    dispatch = _useReducer[1];\n  var propsRef = useLatestRef(props);\n  var dispatchWithProps = useCallback(function (action) {\n    return dispatch(_extends({\n      props: propsRef.current\n    }, action));\n  }, [propsRef]);\n  var action = actionRef.current;\n  useEffect(function () {\n    if (action && prevStateRef.current && prevStateRef.current !== state) {\n      callOnChangeProps(action, getState(prevStateRef.current, action.props), state);\n    }\n    prevStateRef.current = state;\n  }, [state, props, action]);\n  return [state, dispatchWithProps];\n}\n\n/**\n * Wraps the useEnhancedReducer and applies the controlled prop values before\n * returning the new state.\n *\n * @param {Function} reducer Reducer function from downshift.\n * @param {Object} initialState Initial state of the hook.\n * @param {Object} props The hook props.\n * @returns {Array} An array with the state and an action dispatcher.\n */\nfunction useControlledReducer$1(reducer, initialState, props) {\n  var _useEnhancedReducer = useEnhancedReducer(reducer, initialState, props),\n    state = _useEnhancedReducer[0],\n    dispatch = _useEnhancedReducer[1];\n  return [getState(state, props), dispatch];\n}\nvar defaultProps$3 = {\n  itemToString: itemToString,\n  stateReducer: stateReducer,\n  getA11ySelectionMessage: getA11ySelectionMessage,\n  scrollIntoView: scrollIntoView,\n  environment: /* istanbul ignore next (ssr) */\n  typeof window === 'undefined' ? {} : window\n};\nfunction getDefaultValue$1(props, propKey, defaultStateValues) {\n  if (defaultStateValues === void 0) {\n    defaultStateValues = dropdownDefaultStateValues;\n  }\n  var defaultValue = props[\"default\" + capitalizeString(propKey)];\n  if (defaultValue !== undefined) {\n    return defaultValue;\n  }\n  return defaultStateValues[propKey];\n}\nfunction getInitialValue$1(props, propKey, defaultStateValues) {\n  if (defaultStateValues === void 0) {\n    defaultStateValues = dropdownDefaultStateValues;\n  }\n  var value = props[propKey];\n  if (value !== undefined) {\n    return value;\n  }\n  var initialValue = props[\"initial\" + capitalizeString(propKey)];\n  if (initialValue !== undefined) {\n    return initialValue;\n  }\n  return getDefaultValue$1(props, propKey, defaultStateValues);\n}\nfunction getInitialState$2(props) {\n  var selectedItem = getInitialValue$1(props, 'selectedItem');\n  var isOpen = getInitialValue$1(props, 'isOpen');\n  var highlightedIndex = getInitialValue$1(props, 'highlightedIndex');\n  var inputValue = getInitialValue$1(props, 'inputValue');\n  return {\n    highlightedIndex: highlightedIndex < 0 && selectedItem && isOpen ? props.items.indexOf(selectedItem) : highlightedIndex,\n    isOpen: isOpen,\n    selectedItem: selectedItem,\n    inputValue: inputValue\n  };\n}\nfunction getHighlightedIndexOnOpen(props, state, offset) {\n  var items = props.items,\n    initialHighlightedIndex = props.initialHighlightedIndex,\n    defaultHighlightedIndex = props.defaultHighlightedIndex;\n  var selectedItem = state.selectedItem,\n    highlightedIndex = state.highlightedIndex;\n  if (items.length === 0) {\n    return -1;\n  }\n\n  // initialHighlightedIndex will give value to highlightedIndex on initial state only.\n  if (initialHighlightedIndex !== undefined && highlightedIndex === initialHighlightedIndex) {\n    return initialHighlightedIndex;\n  }\n  if (defaultHighlightedIndex !== undefined) {\n    return defaultHighlightedIndex;\n  }\n  if (selectedItem) {\n    return items.indexOf(selectedItem);\n  }\n  if (offset === 0) {\n    return -1;\n  }\n  return offset < 0 ? items.length - 1 : 0;\n}\n\n/**\n * Reuse the movement tracking of mouse and touch events.\n *\n * @param {boolean} isOpen Whether the dropdown is open or not.\n * @param {Array<Object>} downshiftElementRefs Downshift element refs to track movement (toggleButton, menu etc.)\n * @param {Object} environment Environment where component/hook exists.\n * @param {Function} handleBlur Handler on blur from mouse or touch.\n * @returns {Object} Ref containing whether mouseDown or touchMove event is happening\n */\nfunction useMouseAndTouchTracker(isOpen, downshiftElementRefs, environment, handleBlur) {\n  var mouseAndTouchTrackersRef = useRef({\n    isMouseDown: false,\n    isTouchMove: false\n  });\n  useEffect(function () {\n    if ((environment == null ? void 0 : environment.addEventListener) == null) {\n      return;\n    }\n\n    // The same strategy for checking if a click occurred inside or outside downshift\n    // as in downshift.js.\n    var onMouseDown = function onMouseDown() {\n      mouseAndTouchTrackersRef.current.isMouseDown = true;\n    };\n    var onMouseUp = function onMouseUp(event) {\n      mouseAndTouchTrackersRef.current.isMouseDown = false;\n      if (isOpen && !targetWithinDownshift(event.target, downshiftElementRefs.map(function (ref) {\n        return ref.current;\n      }), environment)) {\n        handleBlur();\n      }\n    };\n    var onTouchStart = function onTouchStart() {\n      mouseAndTouchTrackersRef.current.isTouchMove = false;\n    };\n    var onTouchMove = function onTouchMove() {\n      mouseAndTouchTrackersRef.current.isTouchMove = true;\n    };\n    var onTouchEnd = function onTouchEnd(event) {\n      if (isOpen && !mouseAndTouchTrackersRef.current.isTouchMove && !targetWithinDownshift(event.target, downshiftElementRefs.map(function (ref) {\n        return ref.current;\n      }), environment, false)) {\n        handleBlur();\n      }\n    };\n    environment.addEventListener('mousedown', onMouseDown);\n    environment.addEventListener('mouseup', onMouseUp);\n    environment.addEventListener('touchstart', onTouchStart);\n    environment.addEventListener('touchmove', onTouchMove);\n    environment.addEventListener('touchend', onTouchEnd);\n\n    // eslint-disable-next-line consistent-return\n    return function cleanup() {\n      environment.removeEventListener('mousedown', onMouseDown);\n      environment.removeEventListener('mouseup', onMouseUp);\n      environment.removeEventListener('touchstart', onTouchStart);\n      environment.removeEventListener('touchmove', onTouchMove);\n      environment.removeEventListener('touchend', onTouchEnd);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isOpen, environment]);\n  return mouseAndTouchTrackersRef;\n}\n\n/* istanbul ignore next */\n// eslint-disable-next-line import/no-mutable-exports\nvar useGetterPropsCalledChecker = function useGetterPropsCalledChecker() {\n  return noop;\n};\n/**\n * Custom hook that checks if getter props are called correctly.\n *\n * @param  {...any} propKeys Getter prop names to be handled.\n * @returns {Function} Setter function called inside getter props to set call information.\n */\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n  useGetterPropsCalledChecker = function useGetterPropsCalledChecker() {\n    var isInitialMountRef = useRef(true);\n    for (var _len = arguments.length, propKeys = new Array(_len), _key = 0; _key < _len; _key++) {\n      propKeys[_key] = arguments[_key];\n    }\n    var getterPropsCalledRef = useRef(propKeys.reduce(function (acc, propKey) {\n      acc[propKey] = {};\n      return acc;\n    }, {}));\n    useEffect(function () {\n      Object.keys(getterPropsCalledRef.current).forEach(function (propKey) {\n        var propCallInfo = getterPropsCalledRef.current[propKey];\n        if (isInitialMountRef.current) {\n          if (!Object.keys(propCallInfo).length) {\n            // eslint-disable-next-line no-console\n            console.error(\"downshift: You forgot to call the \" + propKey + \" getter function on your component / element.\");\n            return;\n          }\n        }\n        var suppressRefError = propCallInfo.suppressRefError,\n          refKey = propCallInfo.refKey,\n          elementRef = propCallInfo.elementRef;\n        if ((!elementRef || !elementRef.current) && !suppressRefError) {\n          // eslint-disable-next-line no-console\n          console.error(\"downshift: The ref prop \\\"\" + refKey + \"\\\" from \" + propKey + \" was not applied correctly on your element.\");\n        }\n      });\n      isInitialMountRef.current = false;\n    });\n    var setGetterPropCallInfo = useCallback(function (propKey, suppressRefError, refKey, elementRef) {\n      getterPropsCalledRef.current[propKey] = {\n        suppressRefError: suppressRefError,\n        refKey: refKey,\n        elementRef: elementRef\n      };\n    }, []);\n    return setGetterPropCallInfo;\n  };\n}\nfunction useA11yMessageSetter(getA11yMessage, dependencyArray, _ref2) {\n  var isInitialMount = _ref2.isInitialMount,\n    highlightedIndex = _ref2.highlightedIndex,\n    items = _ref2.items,\n    environment = _ref2.environment,\n    rest = _objectWithoutPropertiesLoose(_ref2, _excluded$3);\n  // Sets a11y status message on changes in state.\n  useEffect(function () {\n    if (isInitialMount || false) {\n      return;\n    }\n    updateA11yStatus(function () {\n      return getA11yMessage(_extends({\n        highlightedIndex: highlightedIndex,\n        highlightedItem: items[highlightedIndex],\n        resultCount: items.length\n      }, rest));\n    }, environment.document);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencyArray);\n}\nfunction useScrollIntoView(_ref3) {\n  var highlightedIndex = _ref3.highlightedIndex,\n    isOpen = _ref3.isOpen,\n    itemRefs = _ref3.itemRefs,\n    getItemNodeFromIndex = _ref3.getItemNodeFromIndex,\n    menuElement = _ref3.menuElement,\n    scrollIntoViewProp = _ref3.scrollIntoView;\n  // used not to scroll on highlight by mouse.\n  var shouldScrollRef = useRef(true);\n  // Scroll on highlighted item if change comes from keyboard.\n  useIsomorphicLayoutEffect(function () {\n    if (highlightedIndex < 0 || !isOpen || !Object.keys(itemRefs.current).length) {\n      return;\n    }\n    if (shouldScrollRef.current === false) {\n      shouldScrollRef.current = true;\n    } else {\n      scrollIntoViewProp(getItemNodeFromIndex(highlightedIndex), menuElement);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [highlightedIndex]);\n  return shouldScrollRef;\n}\n\n// eslint-disable-next-line import/no-mutable-exports\nvar useControlPropsValidator = noop;\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n  useControlPropsValidator = function useControlPropsValidator(_ref4) {\n    var isInitialMount = _ref4.isInitialMount,\n      props = _ref4.props,\n      state = _ref4.state;\n    // used for checking when props are moving from controlled to uncontrolled.\n    var prevPropsRef = useRef(props);\n    useEffect(function () {\n      if (isInitialMount) {\n        return;\n      }\n      validateControlledUnchanged(state, prevPropsRef.current, props);\n      prevPropsRef.current = props;\n    }, [state, props, isInitialMount]);\n  };\n}\n\n/**\n * Handles selection on Enter / Alt + ArrowUp. Closes the menu and resets the highlighted index, unless there is a highlighted.\n * In that case, selects the item and resets to defaults for open state and highlighted idex.\n * @param {Object} props The useCombobox props.\n * @param {number} highlightedIndex The index from the state.\n * @param {boolean} inputValue Also return the input value for state.\n * @returns The changes for the state.\n */\nfunction getChangesOnSelection(props, highlightedIndex, inputValue) {\n  var _props$items;\n  if (inputValue === void 0) {\n    inputValue = true;\n  }\n  var shouldSelect = ((_props$items = props.items) == null ? void 0 : _props$items.length) && highlightedIndex >= 0;\n  return _extends({\n    isOpen: false,\n    highlightedIndex: -1\n  }, shouldSelect && _extends({\n    selectedItem: props.items[highlightedIndex],\n    isOpen: getDefaultValue$1(props, 'isOpen'),\n    highlightedIndex: getDefaultValue$1(props, 'highlightedIndex')\n  }, inputValue && {\n    inputValue: props.itemToString(props.items[highlightedIndex])\n  }));\n}\n\nfunction downshiftCommonReducer(state, action, stateChangeTypes) {\n  var type = action.type,\n    props = action.props;\n  var changes;\n  switch (type) {\n    case stateChangeTypes.ItemMouseMove:\n      changes = {\n        highlightedIndex: action.disabled ? -1 : action.index\n      };\n      break;\n    case stateChangeTypes.MenuMouseLeave:\n      changes = {\n        highlightedIndex: -1\n      };\n      break;\n    case stateChangeTypes.ToggleButtonClick:\n    case stateChangeTypes.FunctionToggleMenu:\n      changes = {\n        isOpen: !state.isOpen,\n        highlightedIndex: state.isOpen ? -1 : getHighlightedIndexOnOpen(props, state, 0)\n      };\n      break;\n    case stateChangeTypes.FunctionOpenMenu:\n      changes = {\n        isOpen: true,\n        highlightedIndex: getHighlightedIndexOnOpen(props, state, 0)\n      };\n      break;\n    case stateChangeTypes.FunctionCloseMenu:\n      changes = {\n        isOpen: false\n      };\n      break;\n    case stateChangeTypes.FunctionSetHighlightedIndex:\n      changes = {\n        highlightedIndex: action.highlightedIndex\n      };\n      break;\n    case stateChangeTypes.FunctionSetInputValue:\n      changes = {\n        inputValue: action.inputValue\n      };\n      break;\n    case stateChangeTypes.FunctionReset:\n      changes = {\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        isOpen: getDefaultValue$1(props, 'isOpen'),\n        selectedItem: getDefaultValue$1(props, 'selectedItem'),\n        inputValue: getDefaultValue$1(props, 'inputValue')\n      };\n      break;\n    default:\n      throw new Error('Reducer called without proper action type.');\n  }\n  return _extends({}, state, changes);\n}\n/* eslint-enable complexity */\n\nfunction getItemIndexByCharacterKey(_a) {\n    var keysSoFar = _a.keysSoFar, highlightedIndex = _a.highlightedIndex, items = _a.items, itemToString = _a.itemToString, getItemNodeFromIndex = _a.getItemNodeFromIndex;\n    var lowerCasedKeysSoFar = keysSoFar.toLowerCase();\n    for (var index = 0; index < items.length; index++) {\n        // if we already have a search query in progress, we also consider the current highlighted item.\n        var offsetIndex = (index + highlightedIndex + (keysSoFar.length < 2 ? 1 : 0)) % items.length;\n        var item = items[offsetIndex];\n        if (item !== undefined &&\n            itemToString(item).toLowerCase().startsWith(lowerCasedKeysSoFar)) {\n            var element = getItemNodeFromIndex(offsetIndex);\n            if (!(element === null || element === void 0 ? void 0 : element.hasAttribute('disabled'))) {\n                return offsetIndex;\n            }\n        }\n    }\n    return highlightedIndex;\n}\nvar propTypes$2 = {\n    items: PropTypes.array.isRequired,\n    itemToString: PropTypes.func,\n    getA11yStatusMessage: PropTypes.func,\n    getA11ySelectionMessage: PropTypes.func,\n    highlightedIndex: PropTypes.number,\n    defaultHighlightedIndex: PropTypes.number,\n    initialHighlightedIndex: PropTypes.number,\n    isOpen: PropTypes.bool,\n    defaultIsOpen: PropTypes.bool,\n    initialIsOpen: PropTypes.bool,\n    selectedItem: PropTypes.any,\n    initialSelectedItem: PropTypes.any,\n    defaultSelectedItem: PropTypes.any,\n    id: PropTypes.string,\n    labelId: PropTypes.string,\n    menuId: PropTypes.string,\n    getItemId: PropTypes.func,\n    toggleButtonId: PropTypes.string,\n    stateReducer: PropTypes.func,\n    onSelectedItemChange: PropTypes.func,\n    onHighlightedIndexChange: PropTypes.func,\n    onStateChange: PropTypes.func,\n    onIsOpenChange: PropTypes.func,\n    environment: PropTypes.shape({\n        addEventListener: PropTypes.func,\n        removeEventListener: PropTypes.func,\n        document: PropTypes.shape({\n            getElementById: PropTypes.func,\n            activeElement: PropTypes.any,\n            body: PropTypes.any\n        })\n    })\n};\n/**\n * Default implementation for status message. Only added when menu is open.\n * Will specift if there are results in the list, and if so, how many,\n * and what keys are relevant.\n *\n * @param {Object} param the downshift state and other relevant properties\n * @return {String} the a11y status message\n */\nfunction getA11yStatusMessage(_a) {\n    var isOpen = _a.isOpen, resultCount = _a.resultCount, previousResultCount = _a.previousResultCount;\n    if (!isOpen) {\n        return '';\n    }\n    if (!resultCount) {\n        return 'No results are available.';\n    }\n    if (resultCount !== previousResultCount) {\n        return \"\".concat(resultCount, \" result\").concat(resultCount === 1 ? ' is' : 's are', \" available, use up and down arrow keys to navigate. Press Enter or Space Bar keys to select.\");\n    }\n    return '';\n}\nvar defaultProps$2 = __assign(__assign({}, defaultProps$3), { getA11yStatusMessage: getA11yStatusMessage });\n// eslint-disable-next-line import/no-mutable-exports\nvar validatePropTypes$2 = noop;\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n    validatePropTypes$2 = function (options, caller) {\n        PropTypes.checkPropTypes(propTypes$2, options, 'prop', caller.name);\n    };\n}\n\nvar ToggleButtonClick$1 = process.env.NODE_ENV !== \"production\" ? '__togglebutton_click__' : 0;\nvar ToggleButtonKeyDownArrowDown = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_arrow_down__' : 1;\nvar ToggleButtonKeyDownArrowUp = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_arrow_up__' : 2;\nvar ToggleButtonKeyDownCharacter = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_character__' : 3;\nvar ToggleButtonKeyDownEscape = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_escape__' : 4;\nvar ToggleButtonKeyDownHome = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_home__' : 5;\nvar ToggleButtonKeyDownEnd = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_end__' : 6;\nvar ToggleButtonKeyDownEnter = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_enter__' : 7;\nvar ToggleButtonKeyDownSpaceButton = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_space_button__' : 8;\nvar ToggleButtonKeyDownPageUp = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_page_up__' : 9;\nvar ToggleButtonKeyDownPageDown = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_page_down__' : 10;\nvar ToggleButtonBlur = process.env.NODE_ENV !== \"production\" ? '__togglebutton_blur__' : 11;\nvar MenuMouseLeave$1 = process.env.NODE_ENV !== \"production\" ? '__menu_mouse_leave__' : 12;\nvar ItemMouseMove$1 = process.env.NODE_ENV !== \"production\" ? '__item_mouse_move__' : 13;\nvar ItemClick$1 = process.env.NODE_ENV !== \"production\" ? '__item_click__' : 14;\nvar FunctionToggleMenu$1 = process.env.NODE_ENV !== \"production\" ? '__function_toggle_menu__' : 15;\nvar FunctionOpenMenu$1 = process.env.NODE_ENV !== \"production\" ? '__function_open_menu__' : 16;\nvar FunctionCloseMenu$1 = process.env.NODE_ENV !== \"production\" ? '__function_close_menu__' : 17;\nvar FunctionSetHighlightedIndex$1 = process.env.NODE_ENV !== \"production\" ? '__function_set_highlighted_index__' : 18;\nvar FunctionSelectItem$1 = process.env.NODE_ENV !== \"production\" ? '__function_select_item__' : 19;\nvar FunctionSetInputValue$1 = process.env.NODE_ENV !== \"production\" ? '__function_set_input_value__' : 20;\nvar FunctionReset$2 = process.env.NODE_ENV !== \"production\" ? '__function_reset__' : 21;\n\nvar stateChangeTypes$2 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  ToggleButtonClick: ToggleButtonClick$1,\n  ToggleButtonKeyDownArrowDown: ToggleButtonKeyDownArrowDown,\n  ToggleButtonKeyDownArrowUp: ToggleButtonKeyDownArrowUp,\n  ToggleButtonKeyDownCharacter: ToggleButtonKeyDownCharacter,\n  ToggleButtonKeyDownEscape: ToggleButtonKeyDownEscape,\n  ToggleButtonKeyDownHome: ToggleButtonKeyDownHome,\n  ToggleButtonKeyDownEnd: ToggleButtonKeyDownEnd,\n  ToggleButtonKeyDownEnter: ToggleButtonKeyDownEnter,\n  ToggleButtonKeyDownSpaceButton: ToggleButtonKeyDownSpaceButton,\n  ToggleButtonKeyDownPageUp: ToggleButtonKeyDownPageUp,\n  ToggleButtonKeyDownPageDown: ToggleButtonKeyDownPageDown,\n  ToggleButtonBlur: ToggleButtonBlur,\n  MenuMouseLeave: MenuMouseLeave$1,\n  ItemMouseMove: ItemMouseMove$1,\n  ItemClick: ItemClick$1,\n  FunctionToggleMenu: FunctionToggleMenu$1,\n  FunctionOpenMenu: FunctionOpenMenu$1,\n  FunctionCloseMenu: FunctionCloseMenu$1,\n  FunctionSetHighlightedIndex: FunctionSetHighlightedIndex$1,\n  FunctionSelectItem: FunctionSelectItem$1,\n  FunctionSetInputValue: FunctionSetInputValue$1,\n  FunctionReset: FunctionReset$2\n});\n\n/* eslint-disable complexity */\nfunction downshiftSelectReducer(state, action) {\n  var _props$items;\n  var type = action.type,\n    props = action.props,\n    altKey = action.altKey;\n  var changes;\n  switch (type) {\n    case ItemClick$1:\n      changes = {\n        isOpen: getDefaultValue$1(props, 'isOpen'),\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        selectedItem: props.items[action.index]\n      };\n      break;\n    case ToggleButtonKeyDownCharacter:\n      {\n        var lowercasedKey = action.key;\n        var inputValue = \"\" + state.inputValue + lowercasedKey;\n        var prevHighlightedIndex = !state.isOpen && state.selectedItem ? props.items.indexOf(state.selectedItem) : state.highlightedIndex;\n        var highlightedIndex = getItemIndexByCharacterKey({\n          keysSoFar: inputValue,\n          highlightedIndex: prevHighlightedIndex,\n          items: props.items,\n          itemToString: props.itemToString,\n          getItemNodeFromIndex: action.getItemNodeFromIndex\n        });\n        changes = {\n          inputValue: inputValue,\n          highlightedIndex: highlightedIndex,\n          isOpen: true\n        };\n      }\n      break;\n    case ToggleButtonKeyDownArrowDown:\n      {\n        var _highlightedIndex = state.isOpen ? getNextWrappingIndex(1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false) : altKey && state.selectedItem == null ? -1 : getHighlightedIndexOnOpen(props, state, 1);\n        changes = {\n          highlightedIndex: _highlightedIndex,\n          isOpen: true\n        };\n      }\n      break;\n    case ToggleButtonKeyDownArrowUp:\n      if (state.isOpen && altKey) {\n        changes = getChangesOnSelection(props, state.highlightedIndex, false);\n      } else {\n        var _highlightedIndex2 = state.isOpen ? getNextWrappingIndex(-1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false) : getHighlightedIndexOnOpen(props, state, -1);\n        changes = {\n          highlightedIndex: _highlightedIndex2,\n          isOpen: true\n        };\n      }\n      break;\n    // only triggered when menu is open.\n    case ToggleButtonKeyDownEnter:\n    case ToggleButtonKeyDownSpaceButton:\n      changes = getChangesOnSelection(props, state.highlightedIndex, false);\n      break;\n    case ToggleButtonKeyDownHome:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(1, 0, props.items.length, action.getItemNodeFromIndex, false),\n        isOpen: true\n      };\n      break;\n    case ToggleButtonKeyDownEnd:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(-1, props.items.length - 1, props.items.length, action.getItemNodeFromIndex, false),\n        isOpen: true\n      };\n      break;\n    case ToggleButtonKeyDownPageUp:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(-10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case ToggleButtonKeyDownPageDown:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case ToggleButtonKeyDownEscape:\n      changes = {\n        isOpen: false,\n        highlightedIndex: -1\n      };\n      break;\n    case ToggleButtonBlur:\n      changes = _extends({\n        isOpen: false,\n        highlightedIndex: -1\n      }, state.highlightedIndex >= 0 && ((_props$items = props.items) == null ? void 0 : _props$items.length) && {\n        selectedItem: props.items[state.highlightedIndex]\n      });\n      break;\n    case FunctionSelectItem$1:\n      changes = {\n        selectedItem: action.selectedItem\n      };\n      break;\n    default:\n      return downshiftCommonReducer(state, action, stateChangeTypes$2);\n  }\n  return _extends({}, state, changes);\n}\n/* eslint-enable complexity */\n\nvar _excluded$2 = [\"onMouseLeave\", \"refKey\", \"onKeyDown\", \"onBlur\", \"ref\"],\n  _excluded2$2 = [\"onBlur\", \"onClick\", \"onPress\", \"onKeyDown\", \"refKey\", \"ref\"],\n  _excluded3$1 = [\"item\", \"index\", \"onMouseMove\", \"onClick\", \"onPress\", \"refKey\", \"ref\", \"disabled\"];\nuseSelect.stateChangeTypes = stateChangeTypes$2;\nfunction useSelect(userProps) {\n  if (userProps === void 0) {\n    userProps = {};\n  }\n  validatePropTypes$2(userProps, useSelect);\n  // Props defaults and destructuring.\n  var props = _extends({}, defaultProps$2, userProps);\n  var items = props.items,\n    scrollIntoView = props.scrollIntoView,\n    environment = props.environment,\n    itemToString = props.itemToString,\n    getA11ySelectionMessage = props.getA11ySelectionMessage,\n    getA11yStatusMessage = props.getA11yStatusMessage;\n  // Initial state depending on controlled props.\n  var initialState = getInitialState$2(props);\n  var _useControlledReducer = useControlledReducer$1(downshiftSelectReducer, initialState, props),\n    state = _useControlledReducer[0],\n    dispatch = _useControlledReducer[1];\n  var isOpen = state.isOpen,\n    highlightedIndex = state.highlightedIndex,\n    selectedItem = state.selectedItem,\n    inputValue = state.inputValue;\n\n  // Element efs.\n  var toggleButtonRef = useRef(null);\n  var menuRef = useRef(null);\n  var itemRefs = useRef({});\n  // used to keep the inputValue clearTimeout object between renders.\n  var clearTimeoutRef = useRef(null);\n  // prevent id re-generation between renders.\n  var elementIds = useElementIds(props);\n  // used to keep track of how many items we had on previous cycle.\n  var previousResultCountRef = useRef();\n  var isInitialMountRef = useRef(true);\n  // utility callback to get item element.\n  var latest = useLatestRef({\n    state: state,\n    props: props\n  });\n\n  // Some utils.\n  var getItemNodeFromIndex = useCallback(function (index) {\n    return itemRefs.current[elementIds.getItemId(index)];\n  }, [elementIds]);\n\n  // Effects.\n  // Sets a11y status message on changes in state.\n  useA11yMessageSetter(getA11yStatusMessage, [isOpen, highlightedIndex, inputValue, items], _extends({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Sets a11y status message on changes in selectedItem.\n  useA11yMessageSetter(getA11ySelectionMessage, [selectedItem], _extends({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Scroll on highlighted item if change comes from keyboard.\n  var shouldScrollRef = useScrollIntoView({\n    menuElement: menuRef.current,\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    itemRefs: itemRefs,\n    scrollIntoView: scrollIntoView,\n    getItemNodeFromIndex: getItemNodeFromIndex\n  });\n\n  // Sets cleanup for the keysSoFar callback, debounded after 500ms.\n  useEffect(function () {\n    // init the clean function here as we need access to dispatch.\n    clearTimeoutRef.current = debounce(function (outerDispatch) {\n      outerDispatch({\n        type: FunctionSetInputValue$1,\n        inputValue: ''\n      });\n    }, 500);\n\n    // Cancel any pending debounced calls on mount\n    return function () {\n      clearTimeoutRef.current.cancel();\n    };\n  }, []);\n\n  // Invokes the keysSoFar callback set up above.\n  useEffect(function () {\n    if (!inputValue) {\n      return;\n    }\n    clearTimeoutRef.current(dispatch);\n  }, [dispatch, inputValue]);\n  useControlPropsValidator({\n    isInitialMount: isInitialMountRef.current,\n    props: props,\n    state: state\n  });\n  useEffect(function () {\n    if (isInitialMountRef.current) {\n      return;\n    }\n    previousResultCountRef.current = items.length;\n  });\n  // Add mouse/touch events to document.\n  var mouseAndTouchTrackersRef = useMouseAndTouchTracker(isOpen, [menuRef, toggleButtonRef], environment, function () {\n    dispatch({\n      type: ToggleButtonBlur\n    });\n  });\n  var setGetterPropCallInfo = useGetterPropsCalledChecker('getMenuProps', 'getToggleButtonProps');\n  // Make initial ref false.\n  useEffect(function () {\n    isInitialMountRef.current = false;\n    return function () {\n      isInitialMountRef.current = true;\n    };\n  }, []);\n  // Reset itemRefs on close.\n  useEffect(function () {\n    if (!isOpen) {\n      itemRefs.current = {};\n    }\n  }, [isOpen]);\n\n  // Event handler functions.\n  var toggleButtonKeyDownHandlers = useMemo(function () {\n    return {\n      ArrowDown: function ArrowDown(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownArrowDown,\n          getItemNodeFromIndex: getItemNodeFromIndex,\n          altKey: event.altKey\n        });\n      },\n      ArrowUp: function ArrowUp(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownArrowUp,\n          getItemNodeFromIndex: getItemNodeFromIndex,\n          altKey: event.altKey\n        });\n      },\n      Home: function Home(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownHome,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      End: function End(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownEnd,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      Escape: function Escape() {\n        if (latest.current.state.isOpen) {\n          dispatch({\n            type: ToggleButtonKeyDownEscape\n          });\n        }\n      },\n      Enter: function Enter(event) {\n        event.preventDefault();\n        dispatch({\n          type: latest.current.state.isOpen ? ToggleButtonKeyDownEnter : ToggleButtonClick$1\n        });\n      },\n      PageUp: function PageUp(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: ToggleButtonKeyDownPageUp,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      },\n      PageDown: function PageDown(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: ToggleButtonKeyDownPageDown,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      },\n      ' ': function _(event) {\n        event.preventDefault();\n        var currentState = latest.current.state;\n        if (!currentState.isOpen) {\n          dispatch({\n            type: ToggleButtonClick$1\n          });\n          return;\n        }\n        if (currentState.inputValue) {\n          dispatch({\n            type: ToggleButtonKeyDownCharacter,\n            key: ' ',\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        } else {\n          dispatch({\n            type: ToggleButtonKeyDownSpaceButton\n          });\n        }\n      }\n    };\n  }, [dispatch, getItemNodeFromIndex, latest]);\n\n  // Action functions.\n  var toggleMenu = useCallback(function () {\n    dispatch({\n      type: FunctionToggleMenu$1\n    });\n  }, [dispatch]);\n  var closeMenu = useCallback(function () {\n    dispatch({\n      type: FunctionCloseMenu$1\n    });\n  }, [dispatch]);\n  var openMenu = useCallback(function () {\n    dispatch({\n      type: FunctionOpenMenu$1\n    });\n  }, [dispatch]);\n  var setHighlightedIndex = useCallback(function (newHighlightedIndex) {\n    dispatch({\n      type: FunctionSetHighlightedIndex$1,\n      highlightedIndex: newHighlightedIndex\n    });\n  }, [dispatch]);\n  var selectItem = useCallback(function (newSelectedItem) {\n    dispatch({\n      type: FunctionSelectItem$1,\n      selectedItem: newSelectedItem\n    });\n  }, [dispatch]);\n  var reset = useCallback(function () {\n    dispatch({\n      type: FunctionReset$2\n    });\n  }, [dispatch]);\n  var setInputValue = useCallback(function (newInputValue) {\n    dispatch({\n      type: FunctionSetInputValue$1,\n      inputValue: newInputValue\n    });\n  }, [dispatch]);\n  // Getter functions.\n  var getLabelProps = useCallback(function (labelProps) {\n    return _extends({\n      id: elementIds.labelId,\n      htmlFor: elementIds.toggleButtonId\n    }, labelProps);\n  }, [elementIds]);\n  var getMenuProps = useCallback(function (_temp, _temp2) {\n    var _extends2;\n    var _ref = _temp === void 0 ? {} : _temp,\n      onMouseLeave = _ref.onMouseLeave,\n      _ref$refKey = _ref.refKey,\n      refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey;\n      _ref.onKeyDown;\n      _ref.onBlur;\n      var ref = _ref.ref,\n      rest = _objectWithoutPropertiesLoose(_ref, _excluded$2);\n    var _ref2 = _temp2 === void 0 ? {} : _temp2,\n      _ref2$suppressRefErro = _ref2.suppressRefError,\n      suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;\n    var menuHandleMouseLeave = function menuHandleMouseLeave() {\n      dispatch({\n        type: MenuMouseLeave$1\n      });\n    };\n    setGetterPropCallInfo('getMenuProps', suppressRefError, refKey, menuRef);\n    return _extends((_extends2 = {}, _extends2[refKey] = handleRefs(ref, function (menuNode) {\n      menuRef.current = menuNode;\n    }), _extends2.id = elementIds.menuId, _extends2.role = 'listbox', _extends2['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends2.onMouseLeave = callAllEventHandlers(onMouseLeave, menuHandleMouseLeave), _extends2), rest);\n  }, [dispatch, setGetterPropCallInfo, elementIds]);\n  var getToggleButtonProps = useCallback(function (_temp3, _temp4) {\n    var _extends3;\n    var _ref3 = _temp3 === void 0 ? {} : _temp3,\n      onBlur = _ref3.onBlur,\n      onClick = _ref3.onClick;\n      _ref3.onPress;\n      var onKeyDown = _ref3.onKeyDown,\n      _ref3$refKey = _ref3.refKey,\n      refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey,\n      ref = _ref3.ref,\n      rest = _objectWithoutPropertiesLoose(_ref3, _excluded2$2);\n    var _ref4 = _temp4 === void 0 ? {} : _temp4,\n      _ref4$suppressRefErro = _ref4.suppressRefError,\n      suppressRefError = _ref4$suppressRefErro === void 0 ? false : _ref4$suppressRefErro;\n    var latestState = latest.current.state;\n    var toggleButtonHandleClick = function toggleButtonHandleClick() {\n      dispatch({\n        type: ToggleButtonClick$1\n      });\n    };\n    var toggleButtonHandleBlur = function toggleButtonHandleBlur() {\n      if (latestState.isOpen && !mouseAndTouchTrackersRef.current.isMouseDown) {\n        dispatch({\n          type: ToggleButtonBlur\n        });\n      }\n    };\n    var toggleButtonHandleKeyDown = function toggleButtonHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && toggleButtonKeyDownHandlers[key]) {\n        toggleButtonKeyDownHandlers[key](event);\n      } else if (isAcceptedCharacterKey(key)) {\n        dispatch({\n          type: ToggleButtonKeyDownCharacter,\n          key: key,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      }\n    };\n    var toggleProps = _extends((_extends3 = {}, _extends3[refKey] = handleRefs(ref, function (toggleButtonNode) {\n      toggleButtonRef.current = toggleButtonNode;\n    }), _extends3['aria-activedescendant'] = latestState.isOpen && latestState.highlightedIndex > -1 ? elementIds.getItemId(latestState.highlightedIndex) : '', _extends3['aria-controls'] = elementIds.menuId, _extends3['aria-expanded'] = latest.current.state.isOpen, _extends3['aria-haspopup'] = 'listbox', _extends3['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends3.id = elementIds.toggleButtonId, _extends3.role = 'combobox', _extends3.tabIndex = 0, _extends3.onBlur = callAllEventHandlers(onBlur, toggleButtonHandleBlur), _extends3), rest);\n    if (!rest.disabled) {\n      /* istanbul ignore if (react-native) */\n      {\n        toggleProps.onClick = callAllEventHandlers(onClick, toggleButtonHandleClick);\n        toggleProps.onKeyDown = callAllEventHandlers(onKeyDown, toggleButtonHandleKeyDown);\n      }\n    }\n    setGetterPropCallInfo('getToggleButtonProps', suppressRefError, refKey, toggleButtonRef);\n    return toggleProps;\n  }, [latest, elementIds, setGetterPropCallInfo, dispatch, mouseAndTouchTrackersRef, toggleButtonKeyDownHandlers, getItemNodeFromIndex]);\n  var getItemProps = useCallback(function (_temp5) {\n    var _extends4;\n    var _ref5 = _temp5 === void 0 ? {} : _temp5,\n      itemProp = _ref5.item,\n      indexProp = _ref5.index,\n      onMouseMove = _ref5.onMouseMove,\n      onClick = _ref5.onClick;\n      _ref5.onPress;\n      var _ref5$refKey = _ref5.refKey,\n      refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey,\n      ref = _ref5.ref,\n      disabled = _ref5.disabled,\n      rest = _objectWithoutPropertiesLoose(_ref5, _excluded3$1);\n    var _latest$current = latest.current,\n      latestState = _latest$current.state,\n      latestProps = _latest$current.props;\n    var _getItemAndIndex = getItemAndIndex(itemProp, indexProp, latestProps.items, 'Pass either item or index to getItemProps!'),\n      item = _getItemAndIndex[0],\n      index = _getItemAndIndex[1];\n    var itemHandleMouseMove = function itemHandleMouseMove() {\n      if (index === latestState.highlightedIndex) {\n        return;\n      }\n      shouldScrollRef.current = false;\n      dispatch({\n        type: ItemMouseMove$1,\n        index: index,\n        disabled: disabled\n      });\n    };\n    var itemHandleClick = function itemHandleClick() {\n      dispatch({\n        type: ItemClick$1,\n        index: index\n      });\n    };\n    var itemProps = _extends((_extends4 = {\n      disabled: disabled,\n      role: 'option',\n      'aria-selected': \"\" + (item === selectedItem),\n      id: elementIds.getItemId(index)\n    }, _extends4[refKey] = handleRefs(ref, function (itemNode) {\n      if (itemNode) {\n        itemRefs.current[elementIds.getItemId(index)] = itemNode;\n      }\n    }), _extends4), rest);\n    if (!disabled) {\n      /* istanbul ignore next (react-native) */\n      {\n        itemProps.onClick = callAllEventHandlers(onClick, itemHandleClick);\n      }\n    }\n    itemProps.onMouseMove = callAllEventHandlers(onMouseMove, itemHandleMouseMove);\n    return itemProps;\n  }, [latest, selectedItem, elementIds, shouldScrollRef, dispatch]);\n  return {\n    // prop getters.\n    getToggleButtonProps: getToggleButtonProps,\n    getLabelProps: getLabelProps,\n    getMenuProps: getMenuProps,\n    getItemProps: getItemProps,\n    // actions.\n    toggleMenu: toggleMenu,\n    openMenu: openMenu,\n    closeMenu: closeMenu,\n    setHighlightedIndex: setHighlightedIndex,\n    selectItem: selectItem,\n    reset: reset,\n    setInputValue: setInputValue,\n    // state.\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    selectedItem: selectedItem,\n    inputValue: inputValue\n  };\n}\n\nvar InputKeyDownArrowDown = process.env.NODE_ENV !== \"production\" ? '__input_keydown_arrow_down__' : 0;\nvar InputKeyDownArrowUp = process.env.NODE_ENV !== \"production\" ? '__input_keydown_arrow_up__' : 1;\nvar InputKeyDownEscape = process.env.NODE_ENV !== \"production\" ? '__input_keydown_escape__' : 2;\nvar InputKeyDownHome = process.env.NODE_ENV !== \"production\" ? '__input_keydown_home__' : 3;\nvar InputKeyDownEnd = process.env.NODE_ENV !== \"production\" ? '__input_keydown_end__' : 4;\nvar InputKeyDownPageUp = process.env.NODE_ENV !== \"production\" ? '__input_keydown_page_up__' : 5;\nvar InputKeyDownPageDown = process.env.NODE_ENV !== \"production\" ? '__input_keydown_page_down__' : 6;\nvar InputKeyDownEnter = process.env.NODE_ENV !== \"production\" ? '__input_keydown_enter__' : 7;\nvar InputChange = process.env.NODE_ENV !== \"production\" ? '__input_change__' : 8;\nvar InputBlur = process.env.NODE_ENV !== \"production\" ? '__input_blur__' : 9;\nvar InputFocus = process.env.NODE_ENV !== \"production\" ? '__input_focus__' : 10;\nvar MenuMouseLeave = process.env.NODE_ENV !== \"production\" ? '__menu_mouse_leave__' : 11;\nvar ItemMouseMove = process.env.NODE_ENV !== \"production\" ? '__item_mouse_move__' : 12;\nvar ItemClick = process.env.NODE_ENV !== \"production\" ? '__item_click__' : 13;\nvar ToggleButtonClick = process.env.NODE_ENV !== \"production\" ? '__togglebutton_click__' : 14;\nvar FunctionToggleMenu = process.env.NODE_ENV !== \"production\" ? '__function_toggle_menu__' : 15;\nvar FunctionOpenMenu = process.env.NODE_ENV !== \"production\" ? '__function_open_menu__' : 16;\nvar FunctionCloseMenu = process.env.NODE_ENV !== \"production\" ? '__function_close_menu__' : 17;\nvar FunctionSetHighlightedIndex = process.env.NODE_ENV !== \"production\" ? '__function_set_highlighted_index__' : 18;\nvar FunctionSelectItem = process.env.NODE_ENV !== \"production\" ? '__function_select_item__' : 19;\nvar FunctionSetInputValue = process.env.NODE_ENV !== \"production\" ? '__function_set_input_value__' : 20;\nvar FunctionReset$1 = process.env.NODE_ENV !== \"production\" ? '__function_reset__' : 21;\nvar ControlledPropUpdatedSelectedItem = process.env.NODE_ENV !== \"production\" ? '__controlled_prop_updated_selected_item__' : 22;\n\nvar stateChangeTypes$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  InputKeyDownArrowDown: InputKeyDownArrowDown,\n  InputKeyDownArrowUp: InputKeyDownArrowUp,\n  InputKeyDownEscape: InputKeyDownEscape,\n  InputKeyDownHome: InputKeyDownHome,\n  InputKeyDownEnd: InputKeyDownEnd,\n  InputKeyDownPageUp: InputKeyDownPageUp,\n  InputKeyDownPageDown: InputKeyDownPageDown,\n  InputKeyDownEnter: InputKeyDownEnter,\n  InputChange: InputChange,\n  InputBlur: InputBlur,\n  InputFocus: InputFocus,\n  MenuMouseLeave: MenuMouseLeave,\n  ItemMouseMove: ItemMouseMove,\n  ItemClick: ItemClick,\n  ToggleButtonClick: ToggleButtonClick,\n  FunctionToggleMenu: FunctionToggleMenu,\n  FunctionOpenMenu: FunctionOpenMenu,\n  FunctionCloseMenu: FunctionCloseMenu,\n  FunctionSetHighlightedIndex: FunctionSetHighlightedIndex,\n  FunctionSelectItem: FunctionSelectItem,\n  FunctionSetInputValue: FunctionSetInputValue,\n  FunctionReset: FunctionReset$1,\n  ControlledPropUpdatedSelectedItem: ControlledPropUpdatedSelectedItem\n});\n\nfunction getInitialState$1(props) {\n  var initialState = getInitialState$2(props);\n  var selectedItem = initialState.selectedItem;\n  var inputValue = initialState.inputValue;\n  if (inputValue === '' && selectedItem && props.defaultInputValue === undefined && props.initialInputValue === undefined && props.inputValue === undefined) {\n    inputValue = props.itemToString(selectedItem);\n  }\n  return _extends({}, initialState, {\n    inputValue: inputValue\n  });\n}\nvar propTypes$1 = {\n  items: PropTypes.array.isRequired,\n  itemToString: PropTypes.func,\n  selectedItemChanged: PropTypes.func,\n  getA11yStatusMessage: PropTypes.func,\n  getA11ySelectionMessage: PropTypes.func,\n  highlightedIndex: PropTypes.number,\n  defaultHighlightedIndex: PropTypes.number,\n  initialHighlightedIndex: PropTypes.number,\n  isOpen: PropTypes.bool,\n  defaultIsOpen: PropTypes.bool,\n  initialIsOpen: PropTypes.bool,\n  selectedItem: PropTypes.any,\n  initialSelectedItem: PropTypes.any,\n  defaultSelectedItem: PropTypes.any,\n  inputValue: PropTypes.string,\n  defaultInputValue: PropTypes.string,\n  initialInputValue: PropTypes.string,\n  id: PropTypes.string,\n  labelId: PropTypes.string,\n  menuId: PropTypes.string,\n  getItemId: PropTypes.func,\n  inputId: PropTypes.string,\n  toggleButtonId: PropTypes.string,\n  stateReducer: PropTypes.func,\n  onSelectedItemChange: PropTypes.func,\n  onHighlightedIndexChange: PropTypes.func,\n  onStateChange: PropTypes.func,\n  onIsOpenChange: PropTypes.func,\n  onInputValueChange: PropTypes.func,\n  environment: PropTypes.shape({\n    addEventListener: PropTypes.func,\n    removeEventListener: PropTypes.func,\n    document: PropTypes.shape({\n      getElementById: PropTypes.func,\n      activeElement: PropTypes.any,\n      body: PropTypes.any\n    })\n  })\n};\n\n/**\n * The useCombobox version of useControlledReducer, which also\n * checks if the controlled prop selectedItem changed between\n * renders. If so, it will also update inputValue with its\n * string equivalent. It uses the common useEnhancedReducer to\n * compute the rest of the state.\n *\n * @param {Function} reducer Reducer function from downshift.\n * @param {Object} initialState Initial state of the hook.\n * @param {Object} props The hook props.\n * @returns {Array} An array with the state and an action dispatcher.\n */\nfunction useControlledReducer(reducer, initialState, props) {\n  var previousSelectedItemRef = useRef();\n  var _useEnhancedReducer = useEnhancedReducer(reducer, initialState, props),\n    state = _useEnhancedReducer[0],\n    dispatch = _useEnhancedReducer[1];\n\n  // ToDo: if needed, make same approach as selectedItemChanged from Downshift.\n  useEffect(function () {\n    if (!isControlledProp(props, 'selectedItem')) {\n      return;\n    }\n    if (props.selectedItemChanged(previousSelectedItemRef.current, props.selectedItem)) {\n      dispatch({\n        type: ControlledPropUpdatedSelectedItem,\n        inputValue: props.itemToString(props.selectedItem)\n      });\n    }\n    previousSelectedItemRef.current = state.selectedItem === previousSelectedItemRef.current ? props.selectedItem : state.selectedItem;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [state.selectedItem, props.selectedItem]);\n  return [getState(state, props), dispatch];\n}\n\n// eslint-disable-next-line import/no-mutable-exports\nvar validatePropTypes$1 = noop;\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n  validatePropTypes$1 = function validatePropTypes(options, caller) {\n    PropTypes.checkPropTypes(propTypes$1, options, 'prop', caller.name);\n  };\n}\nvar defaultProps$1 = _extends({}, defaultProps$3, {\n  selectedItemChanged: function selectedItemChanged(prevItem, item) {\n    return prevItem !== item;\n  },\n  getA11yStatusMessage: getA11yStatusMessage$1\n});\n\n/* eslint-disable complexity */\nfunction downshiftUseComboboxReducer(state, action) {\n  var _props$items;\n  var type = action.type,\n    props = action.props,\n    altKey = action.altKey;\n  var changes;\n  switch (type) {\n    case ItemClick:\n      changes = {\n        isOpen: getDefaultValue$1(props, 'isOpen'),\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        selectedItem: props.items[action.index],\n        inputValue: props.itemToString(props.items[action.index])\n      };\n      break;\n    case InputKeyDownArrowDown:\n      if (state.isOpen) {\n        changes = {\n          highlightedIndex: getNextWrappingIndex(1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, true)\n        };\n      } else {\n        changes = {\n          highlightedIndex: altKey && state.selectedItem == null ? -1 : getHighlightedIndexOnOpen(props, state, 1, action.getItemNodeFromIndex),\n          isOpen: props.items.length >= 0\n        };\n      }\n      break;\n    case InputKeyDownArrowUp:\n      if (state.isOpen) {\n        if (altKey) {\n          changes = getChangesOnSelection(props, state.highlightedIndex);\n        } else {\n          changes = {\n            highlightedIndex: getNextWrappingIndex(-1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, true)\n          };\n        }\n      } else {\n        changes = {\n          highlightedIndex: getHighlightedIndexOnOpen(props, state, -1, action.getItemNodeFromIndex),\n          isOpen: props.items.length >= 0\n        };\n      }\n      break;\n    case InputKeyDownEnter:\n      changes = getChangesOnSelection(props, state.highlightedIndex);\n      break;\n    case InputKeyDownEscape:\n      changes = _extends({\n        isOpen: false,\n        highlightedIndex: -1\n      }, !state.isOpen && {\n        selectedItem: null,\n        inputValue: ''\n      });\n      break;\n    case InputKeyDownPageUp:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(-10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputKeyDownPageDown:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputKeyDownHome:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(1, 0, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputKeyDownEnd:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(-1, props.items.length - 1, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputBlur:\n      changes = _extends({\n        isOpen: false,\n        highlightedIndex: -1\n      }, state.highlightedIndex >= 0 && ((_props$items = props.items) == null ? void 0 : _props$items.length) && action.selectItem && {\n        selectedItem: props.items[state.highlightedIndex],\n        inputValue: props.itemToString(props.items[state.highlightedIndex])\n      });\n      break;\n    case InputChange:\n      changes = {\n        isOpen: true,\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        inputValue: action.inputValue\n      };\n      break;\n    case InputFocus:\n      changes = {\n        isOpen: true,\n        highlightedIndex: getHighlightedIndexOnOpen(props, state, 0)\n      };\n      break;\n    case FunctionSelectItem:\n      changes = {\n        selectedItem: action.selectedItem,\n        inputValue: props.itemToString(action.selectedItem)\n      };\n      break;\n    case ControlledPropUpdatedSelectedItem:\n      changes = {\n        inputValue: action.inputValue\n      };\n      break;\n    default:\n      return downshiftCommonReducer(state, action, stateChangeTypes$1);\n  }\n  return _extends({}, state, changes);\n}\n/* eslint-enable complexity */\n\nvar _excluded$1 = [\"onMouseLeave\", \"refKey\", \"ref\"],\n  _excluded2$1 = [\"item\", \"index\", \"refKey\", \"ref\", \"onMouseMove\", \"onMouseDown\", \"onClick\", \"onPress\", \"disabled\"],\n  _excluded3 = [\"onClick\", \"onPress\", \"refKey\", \"ref\"],\n  _excluded4 = [\"onKeyDown\", \"onChange\", \"onInput\", \"onFocus\", \"onBlur\", \"onChangeText\", \"refKey\", \"ref\"];\nuseCombobox.stateChangeTypes = stateChangeTypes$1;\nfunction useCombobox(userProps) {\n  if (userProps === void 0) {\n    userProps = {};\n  }\n  validatePropTypes$1(userProps, useCombobox);\n  // Props defaults and destructuring.\n  var props = _extends({}, defaultProps$1, userProps);\n  var initialIsOpen = props.initialIsOpen,\n    defaultIsOpen = props.defaultIsOpen,\n    items = props.items,\n    scrollIntoView = props.scrollIntoView,\n    environment = props.environment,\n    getA11yStatusMessage = props.getA11yStatusMessage,\n    getA11ySelectionMessage = props.getA11ySelectionMessage,\n    itemToString = props.itemToString;\n  // Initial state depending on controlled props.\n  var initialState = getInitialState$1(props);\n  var _useControlledReducer = useControlledReducer(downshiftUseComboboxReducer, initialState, props),\n    state = _useControlledReducer[0],\n    dispatch = _useControlledReducer[1];\n  var isOpen = state.isOpen,\n    highlightedIndex = state.highlightedIndex,\n    selectedItem = state.selectedItem,\n    inputValue = state.inputValue;\n\n  // Element refs.\n  var menuRef = useRef(null);\n  var itemRefs = useRef({});\n  var inputRef = useRef(null);\n  var toggleButtonRef = useRef(null);\n  var isInitialMountRef = useRef(true);\n  // prevent id re-generation between renders.\n  var elementIds = useElementIds(props);\n  // used to keep track of how many items we had on previous cycle.\n  var previousResultCountRef = useRef();\n  // utility callback to get item element.\n  var latest = useLatestRef({\n    state: state,\n    props: props\n  });\n  var getItemNodeFromIndex = useCallback(function (index) {\n    return itemRefs.current[elementIds.getItemId(index)];\n  }, [elementIds]);\n\n  // Effects.\n  // Sets a11y status message on changes in state.\n  useA11yMessageSetter(getA11yStatusMessage, [isOpen, highlightedIndex, inputValue, items], _extends({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Sets a11y status message on changes in selectedItem.\n  useA11yMessageSetter(getA11ySelectionMessage, [selectedItem], _extends({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Scroll on highlighted item if change comes from keyboard.\n  var shouldScrollRef = useScrollIntoView({\n    menuElement: menuRef.current,\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    itemRefs: itemRefs,\n    scrollIntoView: scrollIntoView,\n    getItemNodeFromIndex: getItemNodeFromIndex\n  });\n  useControlPropsValidator({\n    isInitialMount: isInitialMountRef.current,\n    props: props,\n    state: state\n  });\n  // Focus the input on first render if required.\n  useEffect(function () {\n    var focusOnOpen = initialIsOpen || defaultIsOpen || isOpen;\n    if (focusOnOpen && inputRef.current) {\n      inputRef.current.focus();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  useEffect(function () {\n    if (isInitialMountRef.current) {\n      return;\n    }\n    previousResultCountRef.current = items.length;\n  });\n  // Add mouse/touch events to document.\n  var mouseAndTouchTrackersRef = useMouseAndTouchTracker(isOpen, [inputRef, menuRef, toggleButtonRef], environment, function () {\n    dispatch({\n      type: InputBlur,\n      selectItem: false\n    });\n  });\n  var setGetterPropCallInfo = useGetterPropsCalledChecker('getInputProps', 'getMenuProps');\n  // Make initial ref false.\n  useEffect(function () {\n    isInitialMountRef.current = false;\n    return function () {\n      isInitialMountRef.current = true;\n    };\n  }, []);\n  // Reset itemRefs on close.\n  useEffect(function () {\n    var _environment$document;\n    if (!isOpen) {\n      itemRefs.current = {};\n    } else if (((_environment$document = environment.document) == null ? void 0 : _environment$document.activeElement) !== inputRef.current) {\n      var _inputRef$current;\n      inputRef == null ? void 0 : (_inputRef$current = inputRef.current) == null ? void 0 : _inputRef$current.focus();\n    }\n  }, [isOpen, environment]);\n\n  /* Event handler functions */\n  var inputKeyDownHandlers = useMemo(function () {\n    return {\n      ArrowDown: function ArrowDown(event) {\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownArrowDown,\n          altKey: event.altKey,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      ArrowUp: function ArrowUp(event) {\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownArrowUp,\n          altKey: event.altKey,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      Home: function Home(event) {\n        if (!latest.current.state.isOpen) {\n          return;\n        }\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownHome,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      End: function End(event) {\n        if (!latest.current.state.isOpen) {\n          return;\n        }\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownEnd,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      Escape: function Escape(event) {\n        var latestState = latest.current.state;\n        if (latestState.isOpen || latestState.inputValue || latestState.selectedItem || latestState.highlightedIndex > -1) {\n          event.preventDefault();\n          dispatch({\n            type: InputKeyDownEscape\n          });\n        }\n      },\n      Enter: function Enter(event) {\n        var latestState = latest.current.state;\n        // if closed or no highlighted index, do nothing.\n        if (!latestState.isOpen || event.which === 229 // if IME composing, wait for next Enter keydown event.\n        ) {\n          return;\n        }\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownEnter,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      PageUp: function PageUp(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: InputKeyDownPageUp,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      },\n      PageDown: function PageDown(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: InputKeyDownPageDown,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      }\n    };\n  }, [dispatch, latest, getItemNodeFromIndex]);\n\n  // Getter props.\n  var getLabelProps = useCallback(function (labelProps) {\n    return _extends({\n      id: elementIds.labelId,\n      htmlFor: elementIds.inputId\n    }, labelProps);\n  }, [elementIds]);\n  var getMenuProps = useCallback(function (_temp, _temp2) {\n    var _extends2;\n    var _ref = _temp === void 0 ? {} : _temp,\n      onMouseLeave = _ref.onMouseLeave,\n      _ref$refKey = _ref.refKey,\n      refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey,\n      ref = _ref.ref,\n      rest = _objectWithoutPropertiesLoose(_ref, _excluded$1);\n    var _ref2 = _temp2 === void 0 ? {} : _temp2,\n      _ref2$suppressRefErro = _ref2.suppressRefError,\n      suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;\n    setGetterPropCallInfo('getMenuProps', suppressRefError, refKey, menuRef);\n    return _extends((_extends2 = {}, _extends2[refKey] = handleRefs(ref, function (menuNode) {\n      menuRef.current = menuNode;\n    }), _extends2.id = elementIds.menuId, _extends2.role = 'listbox', _extends2['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends2.onMouseLeave = callAllEventHandlers(onMouseLeave, function () {\n      dispatch({\n        type: MenuMouseLeave\n      });\n    }), _extends2), rest);\n  }, [dispatch, setGetterPropCallInfo, elementIds]);\n  var getItemProps = useCallback(function (_temp3) {\n    var _extends3, _ref4;\n    var _ref3 = _temp3 === void 0 ? {} : _temp3,\n      itemProp = _ref3.item,\n      indexProp = _ref3.index,\n      _ref3$refKey = _ref3.refKey,\n      refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey,\n      ref = _ref3.ref,\n      onMouseMove = _ref3.onMouseMove,\n      onMouseDown = _ref3.onMouseDown,\n      onClick = _ref3.onClick;\n      _ref3.onPress;\n      var disabled = _ref3.disabled,\n      rest = _objectWithoutPropertiesLoose(_ref3, _excluded2$1);\n    var _latest$current = latest.current,\n      latestProps = _latest$current.props,\n      latestState = _latest$current.state;\n    var _getItemAndIndex = getItemAndIndex(itemProp, indexProp, latestProps.items, 'Pass either item or index to getItemProps!'),\n      index = _getItemAndIndex[1];\n    var onSelectKey = 'onClick';\n    var customClickHandler = onClick;\n    var itemHandleMouseMove = function itemHandleMouseMove() {\n      if (index === latestState.highlightedIndex) {\n        return;\n      }\n      shouldScrollRef.current = false;\n      dispatch({\n        type: ItemMouseMove,\n        index: index,\n        disabled: disabled\n      });\n    };\n    var itemHandleClick = function itemHandleClick() {\n      dispatch({\n        type: ItemClick,\n        index: index\n      });\n    };\n    var itemHandleMouseDown = function itemHandleMouseDown(e) {\n      return e.preventDefault();\n    };\n    return _extends((_extends3 = {}, _extends3[refKey] = handleRefs(ref, function (itemNode) {\n      if (itemNode) {\n        itemRefs.current[elementIds.getItemId(index)] = itemNode;\n      }\n    }), _extends3.disabled = disabled, _extends3.role = 'option', _extends3['aria-selected'] = \"\" + (index === latestState.highlightedIndex), _extends3.id = elementIds.getItemId(index), _extends3), !disabled && (_ref4 = {}, _ref4[onSelectKey] = callAllEventHandlers(customClickHandler, itemHandleClick), _ref4), {\n      onMouseMove: callAllEventHandlers(onMouseMove, itemHandleMouseMove),\n      onMouseDown: callAllEventHandlers(onMouseDown, itemHandleMouseDown)\n    }, rest);\n  }, [dispatch, latest, shouldScrollRef, elementIds]);\n  var getToggleButtonProps = useCallback(function (_temp4) {\n    var _extends4;\n    var _ref5 = _temp4 === void 0 ? {} : _temp4,\n      onClick = _ref5.onClick;\n      _ref5.onPress;\n      var _ref5$refKey = _ref5.refKey,\n      refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey,\n      ref = _ref5.ref,\n      rest = _objectWithoutPropertiesLoose(_ref5, _excluded3);\n    var latestState = latest.current.state;\n    var toggleButtonHandleClick = function toggleButtonHandleClick() {\n      dispatch({\n        type: ToggleButtonClick\n      });\n    };\n    return _extends((_extends4 = {}, _extends4[refKey] = handleRefs(ref, function (toggleButtonNode) {\n      toggleButtonRef.current = toggleButtonNode;\n    }), _extends4['aria-controls'] = elementIds.menuId, _extends4['aria-expanded'] = latestState.isOpen, _extends4.id = elementIds.toggleButtonId, _extends4.tabIndex = -1, _extends4), !rest.disabled && _extends({}, {\n      onClick: callAllEventHandlers(onClick, toggleButtonHandleClick)\n    }), rest);\n  }, [dispatch, latest, elementIds]);\n  var getInputProps = useCallback(function (_temp5, _temp6) {\n    var _extends5;\n    var _ref6 = _temp5 === void 0 ? {} : _temp5,\n      onKeyDown = _ref6.onKeyDown,\n      onChange = _ref6.onChange,\n      onInput = _ref6.onInput,\n      onFocus = _ref6.onFocus,\n      onBlur = _ref6.onBlur;\n      _ref6.onChangeText;\n      var _ref6$refKey = _ref6.refKey,\n      refKey = _ref6$refKey === void 0 ? 'ref' : _ref6$refKey,\n      ref = _ref6.ref,\n      rest = _objectWithoutPropertiesLoose(_ref6, _excluded4);\n    var _ref7 = _temp6 === void 0 ? {} : _temp6,\n      _ref7$suppressRefErro = _ref7.suppressRefError,\n      suppressRefError = _ref7$suppressRefErro === void 0 ? false : _ref7$suppressRefErro;\n    setGetterPropCallInfo('getInputProps', suppressRefError, refKey, inputRef);\n    var latestState = latest.current.state;\n    var inputHandleKeyDown = function inputHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && inputKeyDownHandlers[key]) {\n        inputKeyDownHandlers[key](event);\n      }\n    };\n    var inputHandleChange = function inputHandleChange(event) {\n      dispatch({\n        type: InputChange,\n        inputValue: event.target.value\n      });\n    };\n    var inputHandleBlur = function inputHandleBlur(event) {\n      /* istanbul ignore else */\n      if (latestState.isOpen && !mouseAndTouchTrackersRef.current.isMouseDown) {\n        dispatch({\n          type: InputBlur,\n          selectItem: event.relatedTarget !== null\n        });\n      }\n    };\n    var inputHandleFocus = function inputHandleFocus() {\n      if (!latestState.isOpen) {\n        dispatch({\n          type: InputFocus\n        });\n      }\n    };\n\n    /* istanbul ignore next (preact) */\n    var onChangeKey = 'onChange';\n    var eventHandlers = {};\n    if (!rest.disabled) {\n      var _eventHandlers;\n      eventHandlers = (_eventHandlers = {}, _eventHandlers[onChangeKey] = callAllEventHandlers(onChange, onInput, inputHandleChange), _eventHandlers.onKeyDown = callAllEventHandlers(onKeyDown, inputHandleKeyDown), _eventHandlers.onBlur = callAllEventHandlers(onBlur, inputHandleBlur), _eventHandlers.onFocus = callAllEventHandlers(onFocus, inputHandleFocus), _eventHandlers);\n    }\n    return _extends((_extends5 = {}, _extends5[refKey] = handleRefs(ref, function (inputNode) {\n      inputRef.current = inputNode;\n    }), _extends5['aria-activedescendant'] = latestState.isOpen && latestState.highlightedIndex > -1 ? elementIds.getItemId(latestState.highlightedIndex) : '', _extends5['aria-autocomplete'] = 'list', _extends5['aria-controls'] = elementIds.menuId, _extends5['aria-expanded'] = latestState.isOpen, _extends5['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends5.autoComplete = 'off', _extends5.id = elementIds.inputId, _extends5.role = 'combobox', _extends5.value = latestState.inputValue, _extends5), eventHandlers, rest);\n  }, [dispatch, inputKeyDownHandlers, latest, mouseAndTouchTrackersRef, setGetterPropCallInfo, elementIds]);\n\n  // returns\n  var toggleMenu = useCallback(function () {\n    dispatch({\n      type: FunctionToggleMenu\n    });\n  }, [dispatch]);\n  var closeMenu = useCallback(function () {\n    dispatch({\n      type: FunctionCloseMenu\n    });\n  }, [dispatch]);\n  var openMenu = useCallback(function () {\n    dispatch({\n      type: FunctionOpenMenu\n    });\n  }, [dispatch]);\n  var setHighlightedIndex = useCallback(function (newHighlightedIndex) {\n    dispatch({\n      type: FunctionSetHighlightedIndex,\n      highlightedIndex: newHighlightedIndex\n    });\n  }, [dispatch]);\n  var selectItem = useCallback(function (newSelectedItem) {\n    dispatch({\n      type: FunctionSelectItem,\n      selectedItem: newSelectedItem\n    });\n  }, [dispatch]);\n  var setInputValue = useCallback(function (newInputValue) {\n    dispatch({\n      type: FunctionSetInputValue,\n      inputValue: newInputValue\n    });\n  }, [dispatch]);\n  var reset = useCallback(function () {\n    dispatch({\n      type: FunctionReset$1\n    });\n  }, [dispatch]);\n  return {\n    // prop getters.\n    getItemProps: getItemProps,\n    getLabelProps: getLabelProps,\n    getMenuProps: getMenuProps,\n    getInputProps: getInputProps,\n    getToggleButtonProps: getToggleButtonProps,\n    // actions.\n    toggleMenu: toggleMenu,\n    openMenu: openMenu,\n    closeMenu: closeMenu,\n    setHighlightedIndex: setHighlightedIndex,\n    setInputValue: setInputValue,\n    selectItem: selectItem,\n    reset: reset,\n    // state.\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    selectedItem: selectedItem,\n    inputValue: inputValue\n  };\n}\n\nvar defaultStateValues = {\n  activeIndex: -1,\n  selectedItems: []\n};\n\n/**\n * Returns the initial value for a state key in the following order:\n * 1. controlled prop, 2. initial prop, 3. default prop, 4. default\n * value from Downshift.\n *\n * @param {Object} props Props passed to the hook.\n * @param {string} propKey Props key to generate the value for.\n * @returns {any} The initial value for that prop.\n */\nfunction getInitialValue(props, propKey) {\n  return getInitialValue$1(props, propKey, defaultStateValues);\n}\n\n/**\n * Returns the default value for a state key in the following order:\n * 1. controlled prop, 2. default prop, 3. default value from Downshift.\n *\n * @param {Object} props Props passed to the hook.\n * @param {string} propKey Props key to generate the value for.\n * @returns {any} The initial value for that prop.\n */\nfunction getDefaultValue(props, propKey) {\n  return getDefaultValue$1(props, propKey, defaultStateValues);\n}\n\n/**\n * Gets the initial state based on the provided props. It uses initial, default\n * and controlled props related to state in order to compute the initial value.\n *\n * @param {Object} props Props passed to the hook.\n * @returns {Object} The initial state.\n */\nfunction getInitialState(props) {\n  var activeIndex = getInitialValue(props, 'activeIndex');\n  var selectedItems = getInitialValue(props, 'selectedItems');\n  return {\n    activeIndex: activeIndex,\n    selectedItems: selectedItems\n  };\n}\n\n/**\n * Returns true if dropdown keydown operation is permitted. Should not be\n * allowed on keydown with modifier keys (ctrl, alt, shift, meta), on\n * input element with text content that is either highlighted or selection\n * cursor is not at the starting position.\n *\n * @param {KeyboardEvent} event The event from keydown.\n * @returns {boolean} Whether the operation is allowed.\n */\nfunction isKeyDownOperationPermitted(event) {\n  if (event.shiftKey || event.metaKey || event.ctrlKey || event.altKey) {\n    return false;\n  }\n  var element = event.target;\n  if (element instanceof HTMLInputElement &&\n  // if element is a text input\n  element.value !== '' && (\n  // and we have text in it\n  // and cursor is either not at the start or is currently highlighting text.\n  element.selectionStart !== 0 || element.selectionEnd !== 0)) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * Returns a message to be added to aria-live region when item is removed.\n *\n * @param {Object} selectionParameters Parameters required to build the message.\n * @returns {string} The a11y message.\n */\nfunction getA11yRemovalMessage(selectionParameters) {\n  var removedSelectedItem = selectionParameters.removedSelectedItem,\n    itemToStringLocal = selectionParameters.itemToString;\n  return itemToStringLocal(removedSelectedItem) + \" has been removed.\";\n}\nvar propTypes = {\n  selectedItems: PropTypes.array,\n  initialSelectedItems: PropTypes.array,\n  defaultSelectedItems: PropTypes.array,\n  itemToString: PropTypes.func,\n  getA11yRemovalMessage: PropTypes.func,\n  stateReducer: PropTypes.func,\n  activeIndex: PropTypes.number,\n  initialActiveIndex: PropTypes.number,\n  defaultActiveIndex: PropTypes.number,\n  onActiveIndexChange: PropTypes.func,\n  onSelectedItemsChange: PropTypes.func,\n  keyNavigationNext: PropTypes.string,\n  keyNavigationPrevious: PropTypes.string,\n  environment: PropTypes.shape({\n    addEventListener: PropTypes.func,\n    removeEventListener: PropTypes.func,\n    document: PropTypes.shape({\n      getElementById: PropTypes.func,\n      activeElement: PropTypes.any,\n      body: PropTypes.any\n    })\n  })\n};\nvar defaultProps = {\n  itemToString: defaultProps$3.itemToString,\n  stateReducer: defaultProps$3.stateReducer,\n  environment: defaultProps$3.environment,\n  getA11yRemovalMessage: getA11yRemovalMessage,\n  keyNavigationNext: 'ArrowRight',\n  keyNavigationPrevious: 'ArrowLeft'\n};\n\n// eslint-disable-next-line import/no-mutable-exports\nvar validatePropTypes = noop;\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n  validatePropTypes = function validatePropTypes(options, caller) {\n    PropTypes.checkPropTypes(propTypes, options, 'prop', caller.name);\n  };\n}\n\nvar SelectedItemClick = process.env.NODE_ENV !== \"production\" ? '__selected_item_click__' : 0;\nvar SelectedItemKeyDownDelete = process.env.NODE_ENV !== \"production\" ? '__selected_item_keydown_delete__' : 1;\nvar SelectedItemKeyDownBackspace = process.env.NODE_ENV !== \"production\" ? '__selected_item_keydown_backspace__' : 2;\nvar SelectedItemKeyDownNavigationNext = process.env.NODE_ENV !== \"production\" ? '__selected_item_keydown_navigation_next__' : 3;\nvar SelectedItemKeyDownNavigationPrevious = process.env.NODE_ENV !== \"production\" ? '__selected_item_keydown_navigation_previous__' : 4;\nvar DropdownKeyDownNavigationPrevious = process.env.NODE_ENV !== \"production\" ? '__dropdown_keydown_navigation_previous__' : 5;\nvar DropdownKeyDownBackspace = process.env.NODE_ENV !== \"production\" ? '__dropdown_keydown_backspace__' : 6;\nvar DropdownClick = process.env.NODE_ENV !== \"production\" ? '__dropdown_click__' : 7;\nvar FunctionAddSelectedItem = process.env.NODE_ENV !== \"production\" ? '__function_add_selected_item__' : 8;\nvar FunctionRemoveSelectedItem = process.env.NODE_ENV !== \"production\" ? '__function_remove_selected_item__' : 9;\nvar FunctionSetSelectedItems = process.env.NODE_ENV !== \"production\" ? '__function_set_selected_items__' : 10;\nvar FunctionSetActiveIndex = process.env.NODE_ENV !== \"production\" ? '__function_set_active_index__' : 11;\nvar FunctionReset = process.env.NODE_ENV !== \"production\" ? '__function_reset__' : 12;\n\nvar stateChangeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  SelectedItemClick: SelectedItemClick,\n  SelectedItemKeyDownDelete: SelectedItemKeyDownDelete,\n  SelectedItemKeyDownBackspace: SelectedItemKeyDownBackspace,\n  SelectedItemKeyDownNavigationNext: SelectedItemKeyDownNavigationNext,\n  SelectedItemKeyDownNavigationPrevious: SelectedItemKeyDownNavigationPrevious,\n  DropdownKeyDownNavigationPrevious: DropdownKeyDownNavigationPrevious,\n  DropdownKeyDownBackspace: DropdownKeyDownBackspace,\n  DropdownClick: DropdownClick,\n  FunctionAddSelectedItem: FunctionAddSelectedItem,\n  FunctionRemoveSelectedItem: FunctionRemoveSelectedItem,\n  FunctionSetSelectedItems: FunctionSetSelectedItems,\n  FunctionSetActiveIndex: FunctionSetActiveIndex,\n  FunctionReset: FunctionReset\n});\n\n/* eslint-disable complexity */\nfunction downshiftMultipleSelectionReducer(state, action) {\n  var type = action.type,\n    index = action.index,\n    props = action.props,\n    selectedItem = action.selectedItem;\n  var activeIndex = state.activeIndex,\n    selectedItems = state.selectedItems;\n  var changes;\n  switch (type) {\n    case SelectedItemClick:\n      changes = {\n        activeIndex: index\n      };\n      break;\n    case SelectedItemKeyDownNavigationPrevious:\n      changes = {\n        activeIndex: activeIndex - 1 < 0 ? 0 : activeIndex - 1\n      };\n      break;\n    case SelectedItemKeyDownNavigationNext:\n      changes = {\n        activeIndex: activeIndex + 1 >= selectedItems.length ? -1 : activeIndex + 1\n      };\n      break;\n    case SelectedItemKeyDownBackspace:\n    case SelectedItemKeyDownDelete:\n      {\n        if (activeIndex < 0) {\n          break;\n        }\n        var newActiveIndex = activeIndex;\n        if (selectedItems.length === 1) {\n          newActiveIndex = -1;\n        } else if (activeIndex === selectedItems.length - 1) {\n          newActiveIndex = selectedItems.length - 2;\n        }\n        changes = _extends({\n          selectedItems: [].concat(selectedItems.slice(0, activeIndex), selectedItems.slice(activeIndex + 1))\n        }, {\n          activeIndex: newActiveIndex\n        });\n        break;\n      }\n    case DropdownKeyDownNavigationPrevious:\n      changes = {\n        activeIndex: selectedItems.length - 1\n      };\n      break;\n    case DropdownKeyDownBackspace:\n      changes = {\n        selectedItems: selectedItems.slice(0, selectedItems.length - 1)\n      };\n      break;\n    case FunctionAddSelectedItem:\n      changes = {\n        selectedItems: [].concat(selectedItems, [selectedItem])\n      };\n      break;\n    case DropdownClick:\n      changes = {\n        activeIndex: -1\n      };\n      break;\n    case FunctionRemoveSelectedItem:\n      {\n        var _newActiveIndex = activeIndex;\n        var selectedItemIndex = selectedItems.indexOf(selectedItem);\n        if (selectedItemIndex < 0) {\n          break;\n        }\n        if (selectedItems.length === 1) {\n          _newActiveIndex = -1;\n        } else if (selectedItemIndex === selectedItems.length - 1) {\n          _newActiveIndex = selectedItems.length - 2;\n        }\n        changes = {\n          selectedItems: [].concat(selectedItems.slice(0, selectedItemIndex), selectedItems.slice(selectedItemIndex + 1)),\n          activeIndex: _newActiveIndex\n        };\n        break;\n      }\n    case FunctionSetSelectedItems:\n      {\n        var newSelectedItems = action.selectedItems;\n        changes = {\n          selectedItems: newSelectedItems\n        };\n        break;\n      }\n    case FunctionSetActiveIndex:\n      {\n        var _newActiveIndex2 = action.activeIndex;\n        changes = {\n          activeIndex: _newActiveIndex2\n        };\n        break;\n      }\n    case FunctionReset:\n      changes = {\n        activeIndex: getDefaultValue(props, 'activeIndex'),\n        selectedItems: getDefaultValue(props, 'selectedItems')\n      };\n      break;\n    default:\n      throw new Error('Reducer called without proper action type.');\n  }\n  return _extends({}, state, changes);\n}\n\nvar _excluded = [\"refKey\", \"ref\", \"onClick\", \"onKeyDown\", \"selectedItem\", \"index\"],\n  _excluded2 = [\"refKey\", \"ref\", \"onKeyDown\", \"onClick\", \"preventKeyAction\"];\nuseMultipleSelection.stateChangeTypes = stateChangeTypes;\nfunction useMultipleSelection(userProps) {\n  if (userProps === void 0) {\n    userProps = {};\n  }\n  validatePropTypes(userProps, useMultipleSelection);\n  // Props defaults and destructuring.\n  var props = _extends({}, defaultProps, userProps);\n  var getA11yRemovalMessage = props.getA11yRemovalMessage,\n    itemToString = props.itemToString,\n    environment = props.environment,\n    keyNavigationNext = props.keyNavigationNext,\n    keyNavigationPrevious = props.keyNavigationPrevious;\n\n  // Reducer init.\n  var _useControlledReducer = useControlledReducer$1(downshiftMultipleSelectionReducer, getInitialState(props), props),\n    state = _useControlledReducer[0],\n    dispatch = _useControlledReducer[1];\n  var activeIndex = state.activeIndex,\n    selectedItems = state.selectedItems;\n\n  // Refs.\n  var isInitialMountRef = useRef(true);\n  var dropdownRef = useRef(null);\n  var previousSelectedItemsRef = useRef(selectedItems);\n  var selectedItemRefs = useRef();\n  selectedItemRefs.current = [];\n  var latest = useLatestRef({\n    state: state,\n    props: props\n  });\n\n  // Effects.\n  /* Sets a11y status message on changes in selectedItem. */\n  useEffect(function () {\n    if (isInitialMountRef.current || false) {\n      return;\n    }\n    if (selectedItems.length < previousSelectedItemsRef.current.length) {\n      var removedSelectedItem = previousSelectedItemsRef.current.find(function (item) {\n        return selectedItems.indexOf(item) < 0;\n      });\n      setStatus(getA11yRemovalMessage({\n        itemToString: itemToString,\n        resultCount: selectedItems.length,\n        removedSelectedItem: removedSelectedItem,\n        activeIndex: activeIndex,\n        activeSelectedItem: selectedItems[activeIndex]\n      }), environment.document);\n    }\n    previousSelectedItemsRef.current = selectedItems;\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedItems.length]);\n  // Sets focus on active item.\n  useEffect(function () {\n    if (isInitialMountRef.current) {\n      return;\n    }\n    if (activeIndex === -1 && dropdownRef.current) {\n      dropdownRef.current.focus();\n    } else if (selectedItemRefs.current[activeIndex]) {\n      selectedItemRefs.current[activeIndex].focus();\n    }\n  }, [activeIndex]);\n  useControlPropsValidator({\n    isInitialMount: isInitialMountRef.current,\n    props: props,\n    state: state\n  });\n  var setGetterPropCallInfo = useGetterPropsCalledChecker('getDropdownProps');\n  // Make initial ref false.\n  useEffect(function () {\n    isInitialMountRef.current = false;\n    return function () {\n      isInitialMountRef.current = true;\n    };\n  }, []);\n\n  // Event handler functions.\n  var selectedItemKeyDownHandlers = useMemo(function () {\n    var _ref;\n    return _ref = {}, _ref[keyNavigationPrevious] = function () {\n      dispatch({\n        type: SelectedItemKeyDownNavigationPrevious\n      });\n    }, _ref[keyNavigationNext] = function () {\n      dispatch({\n        type: SelectedItemKeyDownNavigationNext\n      });\n    }, _ref.Delete = function Delete() {\n      dispatch({\n        type: SelectedItemKeyDownDelete\n      });\n    }, _ref.Backspace = function Backspace() {\n      dispatch({\n        type: SelectedItemKeyDownBackspace\n      });\n    }, _ref;\n  }, [dispatch, keyNavigationNext, keyNavigationPrevious]);\n  var dropdownKeyDownHandlers = useMemo(function () {\n    var _ref2;\n    return _ref2 = {}, _ref2[keyNavigationPrevious] = function (event) {\n      if (isKeyDownOperationPermitted(event)) {\n        dispatch({\n          type: DropdownKeyDownNavigationPrevious\n        });\n      }\n    }, _ref2.Backspace = function Backspace(event) {\n      if (isKeyDownOperationPermitted(event)) {\n        dispatch({\n          type: DropdownKeyDownBackspace\n        });\n      }\n    }, _ref2;\n  }, [dispatch, keyNavigationPrevious]);\n\n  // Getter props.\n  var getSelectedItemProps = useCallback(function (_temp) {\n    var _extends2;\n    var _ref3 = _temp === void 0 ? {} : _temp,\n      _ref3$refKey = _ref3.refKey,\n      refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey,\n      ref = _ref3.ref,\n      onClick = _ref3.onClick,\n      onKeyDown = _ref3.onKeyDown,\n      selectedItemProp = _ref3.selectedItem,\n      indexProp = _ref3.index,\n      rest = _objectWithoutPropertiesLoose(_ref3, _excluded);\n    var latestState = latest.current.state;\n    var _getItemAndIndex = getItemAndIndex(selectedItemProp, indexProp, latestState.selectedItems, 'Pass either item or index to getSelectedItemProps!'),\n      index = _getItemAndIndex[1];\n    var isFocusable = index > -1 && index === latestState.activeIndex;\n    var selectedItemHandleClick = function selectedItemHandleClick() {\n      dispatch({\n        type: SelectedItemClick,\n        index: index\n      });\n    };\n    var selectedItemHandleKeyDown = function selectedItemHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && selectedItemKeyDownHandlers[key]) {\n        selectedItemKeyDownHandlers[key](event);\n      }\n    };\n    return _extends((_extends2 = {}, _extends2[refKey] = handleRefs(ref, function (selectedItemNode) {\n      if (selectedItemNode) {\n        selectedItemRefs.current.push(selectedItemNode);\n      }\n    }), _extends2.tabIndex = isFocusable ? 0 : -1, _extends2.onClick = callAllEventHandlers(onClick, selectedItemHandleClick), _extends2.onKeyDown = callAllEventHandlers(onKeyDown, selectedItemHandleKeyDown), _extends2), rest);\n  }, [dispatch, latest, selectedItemKeyDownHandlers]);\n  var getDropdownProps = useCallback(function (_temp2, _temp3) {\n    var _extends3;\n    var _ref4 = _temp2 === void 0 ? {} : _temp2,\n      _ref4$refKey = _ref4.refKey,\n      refKey = _ref4$refKey === void 0 ? 'ref' : _ref4$refKey,\n      ref = _ref4.ref,\n      onKeyDown = _ref4.onKeyDown,\n      onClick = _ref4.onClick,\n      _ref4$preventKeyActio = _ref4.preventKeyAction,\n      preventKeyAction = _ref4$preventKeyActio === void 0 ? false : _ref4$preventKeyActio,\n      rest = _objectWithoutPropertiesLoose(_ref4, _excluded2);\n    var _ref5 = _temp3 === void 0 ? {} : _temp3,\n      _ref5$suppressRefErro = _ref5.suppressRefError,\n      suppressRefError = _ref5$suppressRefErro === void 0 ? false : _ref5$suppressRefErro;\n    setGetterPropCallInfo('getDropdownProps', suppressRefError, refKey, dropdownRef);\n    var dropdownHandleKeyDown = function dropdownHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && dropdownKeyDownHandlers[key]) {\n        dropdownKeyDownHandlers[key](event);\n      }\n    };\n    var dropdownHandleClick = function dropdownHandleClick() {\n      dispatch({\n        type: DropdownClick\n      });\n    };\n    return _extends((_extends3 = {}, _extends3[refKey] = handleRefs(ref, function (dropdownNode) {\n      if (dropdownNode) {\n        dropdownRef.current = dropdownNode;\n      }\n    }), _extends3), !preventKeyAction && {\n      onKeyDown: callAllEventHandlers(onKeyDown, dropdownHandleKeyDown),\n      onClick: callAllEventHandlers(onClick, dropdownHandleClick)\n    }, rest);\n  }, [dispatch, dropdownKeyDownHandlers, setGetterPropCallInfo]);\n\n  // returns\n  var addSelectedItem = useCallback(function (selectedItem) {\n    dispatch({\n      type: FunctionAddSelectedItem,\n      selectedItem: selectedItem\n    });\n  }, [dispatch]);\n  var removeSelectedItem = useCallback(function (selectedItem) {\n    dispatch({\n      type: FunctionRemoveSelectedItem,\n      selectedItem: selectedItem\n    });\n  }, [dispatch]);\n  var setSelectedItems = useCallback(function (newSelectedItems) {\n    dispatch({\n      type: FunctionSetSelectedItems,\n      selectedItems: newSelectedItems\n    });\n  }, [dispatch]);\n  var setActiveIndex = useCallback(function (newActiveIndex) {\n    dispatch({\n      type: FunctionSetActiveIndex,\n      activeIndex: newActiveIndex\n    });\n  }, [dispatch]);\n  var reset = useCallback(function () {\n    dispatch({\n      type: FunctionReset\n    });\n  }, [dispatch]);\n  return {\n    getSelectedItemProps: getSelectedItemProps,\n    getDropdownProps: getDropdownProps,\n    addSelectedItem: addSelectedItem,\n    removeSelectedItem: removeSelectedItem,\n    setSelectedItems: setSelectedItems,\n    setActiveIndex: setActiveIndex,\n    reset: reset,\n    selectedItems: selectedItems,\n    activeIndex: activeIndex\n  };\n}\n\nexport { Downshift$1 as default, resetIdCounter, useCombobox, useMultipleSelection, useSelect };\n", "let e=e=>\"object\"==typeof e&&null!=e&&1===e.nodeType,t=(e,t)=>(!t||\"hidden\"!==e)&&(\"visible\"!==e&&\"clip\"!==e),n=(e,n)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){let l=getComputedStyle(e,null);return t(l.overflowY,n)||t(l.overflowX,n)||(e=>{let t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},l=(e,t,n,l,i,o,r,d)=>o<e&&r>t||o>e&&r<t?0:o<=e&&d<=n||r>=t&&d>=n?o-e-l:r>t&&d<n||o<e&&d>n?r-t+i:0,i=e=>{let t=e.parentElement;return null==t?e.getRootNode().host||null:t};var o=(t,o)=>{var r,d,h,f,u,s;if(\"undefined\"==typeof document)return[];let{scrollMode:a,block:c,inline:g,boundary:m,skipOverflowHiddenElements:p}=o,w=\"function\"==typeof m?m:e=>e!==m;if(!e(t))throw new TypeError(\"Invalid target\");let W=document.scrollingElement||document.documentElement,H=[],b=t;for(;e(b)&&w(b);){if(b=i(b),b===W){H.push(b);break}null!=b&&b===document.body&&n(b)&&!n(document.documentElement)||null!=b&&n(b,p)&&H.push(b)}let v=null!=(d=null==(r=window.visualViewport)?void 0:r.width)?d:innerWidth,y=null!=(f=null==(h=window.visualViewport)?void 0:h.height)?f:innerHeight,E=null!=(u=window.scrollX)?u:pageXOffset,M=null!=(s=window.scrollY)?s:pageYOffset,{height:x,width:I,top:C,right:R,bottom:T,left:V}=t.getBoundingClientRect(),k=\"start\"===c||\"nearest\"===c?C:\"end\"===c?T:C+x/2,B=\"center\"===g?V+I/2:\"end\"===g?R:V,D=[];for(let e=0;e<H.length;e++){let t=H[e],{height:n,width:i,top:o,right:r,bottom:d,left:h}=t.getBoundingClientRect();if(\"if-needed\"===a&&C>=0&&V>=0&&T<=y&&R<=v&&C>=o&&T<=d&&V>=h&&R<=r)return D;let f=getComputedStyle(t),u=parseInt(f.borderLeftWidth,10),s=parseInt(f.borderTopWidth,10),m=parseInt(f.borderRightWidth,10),p=parseInt(f.borderBottomWidth,10),w=0,b=0,O=\"offsetWidth\"in t?t.offsetWidth-t.clientWidth-u-m:0,X=\"offsetHeight\"in t?t.offsetHeight-t.clientHeight-s-p:0,Y=\"offsetWidth\"in t?0===t.offsetWidth?0:i/t.offsetWidth:0,L=\"offsetHeight\"in t?0===t.offsetHeight?0:n/t.offsetHeight:0;if(W===t)w=\"start\"===c?k:\"end\"===c?k-y:\"nearest\"===c?l(M,M+y,y,s,p,M+k,M+k+x,x):k-y/2,b=\"start\"===g?B:\"center\"===g?B-v/2:\"end\"===g?B-v:l(E,E+v,v,u,m,E+B,E+B+I,I),w=Math.max(0,w+M),b=Math.max(0,b+E);else{w=\"start\"===c?k-o-s:\"end\"===c?k-d+p+X:\"nearest\"===c?l(o,d,n,s,p+X,k,k+x,x):k-(o+n/2)+X/2,b=\"start\"===g?B-h-u:\"center\"===g?B-(h+i/2)+O/2:\"end\"===g?B-r+m+O:l(h,r,i,u,m+O,B,B+I,I);let{scrollLeft:e,scrollTop:f}=t;w=Math.max(0,Math.min(f+w/L,t.scrollHeight-n/L+X)),b=Math.max(0,Math.min(e+b/Y,t.scrollWidth-i/Y+O)),k+=f-w,B+=e-b}D.push({el:t,top:w,left:b})}return D};export{o as default};//# sourceMappingURL=index.js.map\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport default {\r\n    __extends,\r\n    __assign,\r\n    __rest,\r\n    __decorate,\r\n    __param,\r\n    __metadata,\r\n    __awaiter,\r\n    __generator,\r\n    __createBinding,\r\n    __exportStar,\r\n    __values,\r\n    __read,\r\n    __spread,\r\n    __spreadArrays,\r\n    __spreadArray,\r\n    __await,\r\n    __asyncGenerator,\r\n    __asyncDelegator,\r\n    __asyncValues,\r\n    __makeTemplateObject,\r\n    __importStar,\r\n    __importDefault,\r\n    __classPrivateFieldGet,\r\n    __classPrivateFieldSet,\r\n    __classPrivateFieldIn,\r\n};\r\n", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/inheritsLoose\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport deepEqual from \"deep-equal\";\nimport * as React from 'react';\nimport PopperJS from 'popper.js';\nimport { ManagerReferenceNodeContext } from './Manager';\nimport { unwrapArray, setRef, shallowEqual } from './utils';\nvar initialStyle = {\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  opacity: 0,\n  pointerEvents: 'none'\n};\nvar initialArrowStyle = {};\nexport var InnerPopper =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(InnerPopper, _React$Component);\n\n  function InnerPopper() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"state\", {\n      data: undefined,\n      placement: undefined\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"popperInstance\", void 0);\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"popperNode\", null);\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"arrowNode\", null);\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"setPopperNode\", function (popperNode) {\n      if (!popperNode || _this.popperNode === popperNode) return;\n      setRef(_this.props.innerRef, popperNode);\n      _this.popperNode = popperNode;\n\n      _this.updatePopperInstance();\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"setArrowNode\", function (arrowNode) {\n      _this.arrowNode = arrowNode;\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"updateStateModifier\", {\n      enabled: true,\n      order: 900,\n      fn: function fn(data) {\n        var placement = data.placement;\n\n        _this.setState({\n          data: data,\n          placement: placement\n        });\n\n        return data;\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"getOptions\", function () {\n      return {\n        placement: _this.props.placement,\n        eventsEnabled: _this.props.eventsEnabled,\n        positionFixed: _this.props.positionFixed,\n        modifiers: _extends({}, _this.props.modifiers, {\n          arrow: _extends({}, _this.props.modifiers && _this.props.modifiers.arrow, {\n            enabled: !!_this.arrowNode,\n            element: _this.arrowNode\n          }),\n          applyStyle: {\n            enabled: false\n          },\n          updateStateModifier: _this.updateStateModifier\n        })\n      };\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"getPopperStyle\", function () {\n      return !_this.popperNode || !_this.state.data ? initialStyle : _extends({\n        position: _this.state.data.offsets.popper.position\n      }, _this.state.data.styles);\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"getPopperPlacement\", function () {\n      return !_this.state.data ? undefined : _this.state.placement;\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"getArrowStyle\", function () {\n      return !_this.arrowNode || !_this.state.data ? initialArrowStyle : _this.state.data.arrowStyles;\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"getOutOfBoundariesState\", function () {\n      return _this.state.data ? _this.state.data.hide : undefined;\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"destroyPopperInstance\", function () {\n      if (!_this.popperInstance) return;\n\n      _this.popperInstance.destroy();\n\n      _this.popperInstance = null;\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"updatePopperInstance\", function () {\n      _this.destroyPopperInstance();\n\n      var _assertThisInitialize = _assertThisInitialized(_assertThisInitialized(_this)),\n          popperNode = _assertThisInitialize.popperNode;\n\n      var referenceElement = _this.props.referenceElement;\n      if (!referenceElement || !popperNode) return;\n      _this.popperInstance = new PopperJS(referenceElement, popperNode, _this.getOptions());\n    });\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"scheduleUpdate\", function () {\n      if (_this.popperInstance) {\n        _this.popperInstance.scheduleUpdate();\n      }\n    });\n\n    return _this;\n  }\n\n  var _proto = InnerPopper.prototype;\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n    // If the Popper.js options have changed, update the instance (destroy + create)\n    if (this.props.placement !== prevProps.placement || this.props.referenceElement !== prevProps.referenceElement || this.props.positionFixed !== prevProps.positionFixed || !deepEqual(this.props.modifiers, prevProps.modifiers, {\n      strict: true\n    })) {\n      // develop only check that modifiers isn't being updated needlessly\n      if (process.env.NODE_ENV === \"development\") {\n        if (this.props.modifiers !== prevProps.modifiers && this.props.modifiers != null && prevProps.modifiers != null && shallowEqual(this.props.modifiers, prevProps.modifiers)) {\n          console.warn(\"'modifiers' prop reference updated even though all values appear the same.\\nConsider memoizing the 'modifiers' object to avoid needless rendering.\");\n        }\n      }\n\n      this.updatePopperInstance();\n    } else if (this.props.eventsEnabled !== prevProps.eventsEnabled && this.popperInstance) {\n      this.props.eventsEnabled ? this.popperInstance.enableEventListeners() : this.popperInstance.disableEventListeners();\n    } // A placement difference in state means popper determined a new placement\n    // apart from the props value. By the time the popper element is rendered with\n    // the new position Popper has already measured it, if the place change triggers\n    // a size change it will result in a misaligned popper. So we schedule an update to be sure.\n\n\n    if (prevState.placement !== this.state.placement) {\n      this.scheduleUpdate();\n    }\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    setRef(this.props.innerRef, null);\n    this.destroyPopperInstance();\n  };\n\n  _proto.render = function render() {\n    return unwrapArray(this.props.children)({\n      ref: this.setPopperNode,\n      style: this.getPopperStyle(),\n      placement: this.getPopperPlacement(),\n      outOfBoundaries: this.getOutOfBoundariesState(),\n      scheduleUpdate: this.scheduleUpdate,\n      arrowProps: {\n        ref: this.setArrowNode,\n        style: this.getArrowStyle()\n      }\n    });\n  };\n\n  return InnerPopper;\n}(React.Component);\n\n_defineProperty(InnerPopper, \"defaultProps\", {\n  placement: 'bottom',\n  eventsEnabled: true,\n  referenceElement: undefined,\n  positionFixed: false\n});\n\nvar placements = PopperJS.placements;\nexport { placements };\nexport default function Popper(_ref) {\n  var referenceElement = _ref.referenceElement,\n      props = _objectWithoutPropertiesLoose(_ref, [\"referenceElement\"]);\n\n  return React.createElement(ManagerReferenceNodeContext.Consumer, null, function (referenceNode) {\n    return React.createElement(InnerPopper, _extends({\n      referenceElement: referenceElement !== undefined ? referenceElement : referenceNode\n    }, props));\n  });\n}", "import _inherits<PERSON>oose from \"@babel/runtime/helpers/inheritsLoose\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport * as React from 'react';\nimport createContext from '@hypnosphi/create-react-context';\nexport var ManagerReferenceNodeContext = createContext();\nexport var ManagerReferenceNodeSetterContext = createContext();\n\nvar Manager =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(Manager, _React$Component);\n\n  function Manager() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"referenceNode\", void 0);\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"setReferenceNode\", function (newReferenceNode) {\n      if (newReferenceNode && _this.referenceNode !== newReferenceNode) {\n        _this.referenceNode = newReferenceNode;\n\n        _this.forceUpdate();\n      }\n    });\n\n    return _this;\n  }\n\n  var _proto = Manager.prototype;\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.referenceNode = null;\n  };\n\n  _proto.render = function render() {\n    return React.createElement(ManagerReferenceNodeContext.Provider, {\n      value: this.referenceNode\n    }, React.createElement(ManagerReferenceNodeSetterContext.Provider, {\n      value: this.setReferenceNode\n    }, this.props.children));\n  };\n\n  return Manager;\n}(React.Component);\n\nexport { Manager as default };", "/**\n * Takes an argument and if it's an array, returns the first item in the array,\n * otherwise returns the argument. Used for Preact compatibility.\n */\nexport var unwrapArray = function unwrapArray(arg) {\n  return Array.isArray(arg) ? arg[0] : arg;\n};\n/**\n * Takes a maybe-undefined function and arbitrary args and invokes the function\n * only if it is defined.\n */\n\nexport var safeInvoke = function safeInvoke(fn) {\n  if (typeof fn === \"function\") {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return fn.apply(void 0, args);\n  }\n};\n/**\n * Does a shallow equality check of two objects by comparing the reference\n * equality of each value.\n */\n\nexport var shallowEqual = function shallowEqual(objA, objB) {\n  var aKeys = Object.keys(objA);\n  var bKeys = Object.keys(objB);\n\n  if (bKeys.length !== aKeys.length) {\n    return false;\n  }\n\n  for (var i = 0; i < bKeys.length; i++) {\n    var key = aKeys[i];\n\n    if (objA[key] !== objB[key]) {\n      return false;\n    }\n  }\n\n  return true;\n};\n/**\n * Sets a ref using either a ref callback or a ref object\n */\n\nexport var setRef = function setRef(ref, node) {\n  // if its a function call it\n  if (typeof ref === \"function\") {\n    return safeInvoke(ref, node);\n  } // otherwise we should treat it as a ref object\n  else if (ref != null) {\n      ref.current = node;\n    }\n};", "import _extends from \"@babel/runtime/helpers/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/inheritsLoose\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport * as React from 'react';\nimport warning from 'warning';\nimport { ManagerReferenceNodeSetterContext } from './Manager';\nimport { safeInvoke, unwrapArray, setRef } from './utils';\n\nvar InnerReference =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(InnerReference, _React$Component);\n\n  function InnerReference() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"refHandler\", function (node) {\n      setRef(_this.props.innerRef, node);\n      safeInvoke(_this.props.setReferenceNode, node);\n    });\n\n    return _this;\n  }\n\n  var _proto = InnerReference.prototype;\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    setRef(this.props.innerRef, null);\n  };\n\n  _proto.render = function render() {\n    warning(Boolean(this.props.setReferenceNode), '`Reference` should not be used outside of a `Manager` component.');\n    return unwrapArray(this.props.children)({\n      ref: this.refHandler\n    });\n  };\n\n  return InnerReference;\n}(React.Component);\n\nexport default function Reference(props) {\n  return React.createElement(ManagerReferenceNodeSetterContext.Consumer, null, function (setReferenceNode) {\n    return React.createElement(InnerReference, _extends({\n      setReferenceNode: setReferenceNode\n    }, props));\n  });\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAOA,YAAI,qBAAqB;AACzB,YAAI,oBAAoB;AACxB,YAAI,sBAAsB;AAC1B,YAAI,yBAAyB;AAC7B,YAAI,sBAAsB;AAC1B,YAAI,sBAAsB;AAC1B,YAAI,qBAAqB;AACzB,YAAI,yBAAyB;AAC7B,YAAI,sBAAsB;AAC1B,YAAI,2BAA2B;AAC/B,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,mBAAmB;AACvB,YAAI,0BAA0B;AAC9B,YAAI,yBAAyB;AAC7B,YAAI,mBAAmB;AACvB,YAAI,uBAAuB;AAC3B,YAAI,gCAAgC;AACpC,YAAI,uBAAuB;AAC3B,YAAI,2BAA2B;AAE/B,YAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,cAAI,YAAY,OAAO;AACvB,+BAAqB,UAAU,eAAe;AAC9C,8BAAoB,UAAU,cAAc;AAC5C,gCAAsB,UAAU,gBAAgB;AAChD,mCAAyB,UAAU,mBAAmB;AACtD,gCAAsB,UAAU,gBAAgB;AAChD,gCAAsB,UAAU,gBAAgB;AAChD,+BAAqB,UAAU,eAAe;AAC9C,mCAAyB,UAAU,mBAAmB;AACtD,gCAAsB,UAAU,gBAAgB;AAChD,qCAA2B,UAAU,qBAAqB;AAC1D,4BAAkB,UAAU,YAAY;AACxC,4BAAkB,UAAU,YAAY;AACxC,6BAAmB,UAAU,aAAa;AAC1C,oCAA0B,UAAU,oBAAoB;AACxD,mCAAyB,UAAU,mBAAmB;AACtD,6BAAmB,UAAU,aAAa;AAC1C,iCAAuB,UAAU,iBAAiB;AAClD,0CAAgC,UAAU,wBAAwB;AAClE,iCAAuB,UAAU,iBAAiB;AAClD,qCAA2B,UAAU,qBAAqB;AAAA,QAC5D;AAIA,YAAI,iBAAiB;AAErB,iBAAS,mBAAmB,MAAM;AAChC,cAAI,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAC1D,mBAAO;AAAA,UACT;AAGA,cAAI,SAAS,uBAAuB,SAAS,uBAAuB,SAAS,iCAAiC,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,SAAS,4BAA4B,gBAAiB;AAC1Q,mBAAO;AAAA,UACT;AAEA,cAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC7C,gBAAI,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,oBAAoB,KAAK,CAAC,MAAM,yBAAyB;AAChU,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AACA,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAC1C,YAAI,2CAA2C;AAE/C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,wFAA6F;AAAA,YAC/G;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AACA,iBAAS,iBAAiB,QAAQ;AAChC;AACE,gBAAI,CAAC,0CAA0C;AAC7C,yDAA2C;AAE3C,sBAAQ,MAAM,EAAE,6FAAkG;AAAA,YACpH;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAASA,cAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAU;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAeA;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACjOA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACCA,IAAAC,SAAuB;AACvB,IAAAC,gBAAoH;AACpH,IAAAC,qBAAsB;;;ACLtB,wBAAsB;AACtB,mBAA8G;AAC9G,sBAA6B;;;ACN7B,IAAI,IAAE,CAAAC,OAAG,YAAU,OAAOA,MAAG,QAAMA,MAAG,MAAIA,GAAE;AAA5C,IAAqD,IAAE,CAACA,IAAEC,QAAK,CAACA,MAAG,aAAWD,QAAK,cAAYA,MAAG,WAASA;AAA3G,IAA8G,IAAE,CAACA,IAAEE,OAAI;AAAC,MAAGF,GAAE,eAAaA,GAAE,gBAAcA,GAAE,cAAYA,GAAE,aAAY;AAAC,QAAIG,KAAE,iBAAiBH,IAAE,IAAI;AAAE,WAAO,EAAEG,GAAE,WAAUD,EAAC,KAAG,EAAEC,GAAE,WAAUD,EAAC,MAAI,CAAAF,OAAG;AAAC,UAAIC,MAAG,CAAAD,OAAG;AAAC,YAAG,CAACA,GAAE,iBAAe,CAACA,GAAE,cAAc;AAAY,iBAAO;AAAK,YAAG;AAAC,iBAAOA,GAAE,cAAc,YAAY;AAAA,QAAY,SAAOA,IAAN;AAAS,iBAAO;AAAA,QAAI;AAAA,MAAC,GAAGA,EAAC;AAAE,aAAM,CAAC,CAACC,OAAIA,GAAE,eAAaD,GAAE,gBAAcC,GAAE,cAAYD,GAAE;AAAA,IAAY,GAAGA,EAAC;AAAA,EAAC;AAAC,SAAM;AAAE;AAAnf,IAAqf,IAAE,CAACA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,GAAE,MAAIA,KAAEL,MAAG,IAAEC,MAAGI,KAAEL,MAAG,IAAEC,KAAE,IAAEI,MAAGL,MAAG,KAAGE,MAAG,KAAGD,MAAG,KAAGC,KAAEG,KAAEL,KAAEG,KAAE,IAAEF,MAAG,IAAEC,MAAGG,KAAEL,MAAG,IAAEE,KAAE,IAAED,KAAEG,KAAE;AAArlB,IAAulB,IAAE,CAAAJ,OAAG;AAAC,MAAIC,KAAED,GAAE;AAAc,SAAO,QAAMC,KAAED,GAAE,YAAY,EAAE,QAAM,OAAKC;AAAC;AAAE,IAAI,IAAE,CAACA,IAAEI,OAAI;AAAC,MAAI,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,MAAG,eAAa,OAAO;AAAS,WAAM,CAAC;AAAE,MAAG,EAAC,YAAW,GAAE,OAAM,GAAE,QAAO,GAAE,UAAS,GAAE,4BAA2B,EAAC,IAAEA,IAAE,IAAE,cAAY,OAAO,IAAE,IAAE,CAAAL,OAAGA,OAAI;AAAE,MAAG,CAAC,EAAEC,EAAC;AAAE,UAAM,IAAI,UAAU,gBAAgB;AAAE,MAAI,IAAE,SAAS,oBAAkB,SAAS,iBAAgB,IAAE,CAAC,GAAE,IAAEA;AAAE,SAAK,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG;AAAC,QAAG,IAAE,EAAE,CAAC,GAAE,MAAI,GAAE;AAAC,QAAE,KAAK,CAAC;AAAE;AAAA,IAAK;AAAC,YAAM,KAAG,MAAI,SAAS,QAAM,EAAE,CAAC,KAAG,CAAC,EAAE,SAAS,eAAe,KAAG,QAAM,KAAG,EAAE,GAAE,CAAC,KAAG,EAAE,KAAK,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE,SAAO,IAAE,SAAO,IAAE,OAAO,kBAAgB,SAAO,EAAE,SAAO,IAAE,YAAW,IAAE,SAAO,IAAE,SAAO,IAAE,OAAO,kBAAgB,SAAO,EAAE,UAAQ,IAAE,aAAY,IAAE,SAAO,IAAE,OAAO,WAAS,IAAE,aAAY,IAAE,SAAO,IAAE,OAAO,WAAS,IAAE,aAAY,EAAC,QAAO,GAAE,OAAM,GAAE,KAAI,GAAE,OAAM,GAAE,QAAO,GAAE,MAAK,EAAC,IAAEA,GAAE,sBAAsB,GAAE,IAAE,YAAU,KAAG,cAAY,IAAE,IAAE,UAAQ,IAAE,IAAE,IAAE,IAAE,GAAE,IAAE,aAAW,IAAE,IAAE,IAAE,IAAE,UAAQ,IAAE,IAAE,GAAE,IAAE,CAAC;AAAE,WAAQD,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,QAAIC,KAAE,EAAED,EAAC,GAAE,EAAC,QAAOE,IAAE,OAAME,IAAE,KAAIC,IAAE,OAAMC,IAAE,QAAOC,IAAE,MAAKC,GAAC,IAAEP,GAAE,sBAAsB;AAAE,QAAG,gBAAc,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAGI,MAAG,KAAGE,MAAG,KAAGC,MAAG,KAAGF;AAAE,aAAO;AAAE,QAAIG,KAAE,iBAAiBR,EAAC,GAAES,KAAE,SAASD,GAAE,iBAAgB,EAAE,GAAEE,KAAE,SAASF,GAAE,gBAAe,EAAE,GAAEG,KAAE,SAASH,GAAE,kBAAiB,EAAE,GAAEI,KAAE,SAASJ,GAAE,mBAAkB,EAAE,GAAEK,KAAE,GAAEC,KAAE,GAAE,IAAE,iBAAgBd,KAAEA,GAAE,cAAYA,GAAE,cAAYS,KAAEE,KAAE,GAAE,IAAE,kBAAiBX,KAAEA,GAAE,eAAaA,GAAE,eAAaU,KAAEE,KAAE,GAAE,IAAE,iBAAgBZ,KAAE,MAAIA,GAAE,cAAY,IAAEG,KAAEH,GAAE,cAAY,GAAE,IAAE,kBAAiBA,KAAE,MAAIA,GAAE,eAAa,IAAEC,KAAED,GAAE,eAAa;AAAE,QAAG,MAAIA;AAAE,MAAAa,KAAE,YAAU,IAAE,IAAE,UAAQ,IAAE,IAAE,IAAE,cAAY,IAAE,EAAE,GAAE,IAAE,GAAE,GAAEH,IAAEE,IAAE,IAAE,GAAE,IAAE,IAAE,GAAE,CAAC,IAAE,IAAE,IAAE,GAAEE,KAAE,YAAU,IAAE,IAAE,aAAW,IAAE,IAAE,IAAE,IAAE,UAAQ,IAAE,IAAE,IAAE,EAAE,GAAE,IAAE,GAAE,GAAEL,IAAEE,IAAE,IAAE,GAAE,IAAE,IAAE,GAAE,CAAC,GAAEE,KAAE,KAAK,IAAI,GAAEA,KAAE,CAAC,GAAEC,KAAE,KAAK,IAAI,GAAEA,KAAE,CAAC;AAAA,SAAM;AAAC,MAAAD,KAAE,YAAU,IAAE,IAAET,KAAEM,KAAE,UAAQ,IAAE,IAAEJ,KAAEM,KAAE,IAAE,cAAY,IAAE,EAAER,IAAEE,IAAEL,IAAES,IAAEE,KAAE,GAAE,GAAE,IAAE,GAAE,CAAC,IAAE,KAAGR,KAAEH,KAAE,KAAG,IAAE,GAAEa,KAAE,YAAU,IAAE,IAAEP,KAAEE,KAAE,aAAW,IAAE,KAAGF,KAAEJ,KAAE,KAAG,IAAE,IAAE,UAAQ,IAAE,IAAEE,KAAEM,KAAE,IAAE,EAAEJ,IAAEF,IAAEF,IAAEM,IAAEE,KAAE,GAAE,GAAE,IAAE,GAAE,CAAC;AAAE,UAAG,EAAC,YAAWZ,IAAE,WAAUS,GAAC,IAAER;AAAE,MAAAa,KAAE,KAAK,IAAI,GAAE,KAAK,IAAIL,KAAEK,KAAE,GAAEb,GAAE,eAAaC,KAAE,IAAE,CAAC,CAAC,GAAEa,KAAE,KAAK,IAAI,GAAE,KAAK,IAAIf,KAAEe,KAAE,GAAEd,GAAE,cAAYG,KAAE,IAAE,CAAC,CAAC,GAAE,KAAGK,KAAEK,IAAE,KAAGd,KAAEe;AAAA,IAAC;AAAC,MAAE,KAAK,EAAC,IAAGd,IAAE,KAAIa,IAAE,MAAKC,GAAC,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;;;AC+B1lF,IAAI,WAAW,WAAW;AAC7B,aAAW,OAAO,UAAU,SAASC,UAASC,IAAG;AAC7C,aAAS,GAAGC,KAAI,GAAGC,KAAI,UAAU,QAAQD,KAAIC,IAAGD,MAAK;AACjD,UAAI,UAAUA,EAAC;AACf,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAG,UAAAD,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAOA;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;;;AF9BA,IAAI,YAAY;AAUhB,SAAS,OAAO,IAAI;AAClB,SAAO,OAAO,OAAO,aAAa,KAAK;AACzC;AACA,SAAS,OAAO;AAAC;AAOjB,SAAS,eAAe,MAAM,UAAU;AACtC,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AACA,MAAI,UAAU,EAAQ,MAAM;AAAA,IAC1B,UAAU;AAAA,IACV,OAAO;AAAA,IACP,YAAY;AAAA,EACd,CAAC;AACD,UAAQ,QAAQ,SAAU,MAAM;AAC9B,QAAI,KAAK,KAAK,IACZ,MAAM,KAAK,KACX,OAAO,KAAK;AACd,OAAG,YAAY;AACf,OAAG,aAAa;AAAA,EAClB,CAAC;AACH;AAQA,SAAS,iBAAiB,QAAQ,OAAO,aAAa;AACpD,MAAI,SAAS,WAAW,SAAS,iBAAiB,YAAY,QAAQ,OAAO,YAAY,OAAO,SAAS,KAAK;AAC9G,SAAO;AACT;AAUA,SAAS,SAAS,IAAI,MAAM;AAC1B,MAAI;AACJ,WAAS,SAAS;AAChB,QAAI,WAAW;AACb,mBAAa,SAAS;AAAA,IACxB;AAAA,EACF;AACA,WAAS,UAAU;AACjB,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AACA,WAAO;AACP,gBAAY,WAAW,WAAY;AACjC,kBAAY;AACZ,SAAG,MAAM,QAAQ,IAAI;AAAA,IACvB,GAAG,IAAI;AAAA,EACT;AACA,UAAQ,SAAS;AACjB,SAAO;AACT;AASA,SAAS,uBAAuB;AAC9B,WAAS,QAAQ,UAAU,QAAQ,MAAM,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC5F,QAAI,KAAK,IAAI,UAAU,KAAK;AAAA,EAC9B;AACA,SAAO,SAAU,OAAO;AACtB,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,WAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,IACnC;AACA,WAAO,IAAI,KAAK,SAAU,IAAI;AAC5B,UAAI,IAAI;AACN,WAAG,MAAM,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;AAAA,MACvC;AACA,aAAO,MAAM,2BAA2B,MAAM,eAAe,aAAa,KAAK,MAAM,YAAY;AAAA,IACnG,CAAC;AAAA,EACH;AACF;AACA,SAAS,aAAa;AACpB,WAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,SAAK,KAAK,IAAI,UAAU,KAAK;AAAA,EAC/B;AACA,SAAO,SAAU,MAAM;AACrB,SAAK,QAAQ,SAAU,KAAK;AAC1B,UAAI,OAAO,QAAQ,YAAY;AAC7B,YAAI,IAAI;AAAA,MACV,WAAW,KAAK;AACd,YAAI,UAAU;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAMA,SAAS,aAAa;AACpB,SAAO,OAAO,WAAW;AAC3B;AAKA,SAAS,iBAAiB;AACxB,cAAY;AACd;AAUA,SAAS,uBAAuB,OAAO;AACrC,MAAI,SAAS,MAAM,QACjB,cAAc,MAAM,aACpB,sBAAsB,MAAM;AAC9B,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,qBAAqB;AACvC,WAAO,cAAc,aAAa,gBAAgB,IAAI,QAAQ,WAAW;AAAA,EAC3E;AACA,SAAO;AACT;AASA,SAAS,YAAY,KAAK,cAAc;AACtC,QAAM,MAAM,QAAQ,GAAG;AAAA;AAAA,IAAuC,IAAI,CAAC;AAAA,MAAI;AACvE,MAAI,CAAC,OAAO,cAAc;AACxB,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAMA,SAAS,aAAa,SAAS;AAG7B,SAAO,OAAO,QAAQ,SAAS;AACjC;AAMA,SAAS,gBAAgB,SAAS;AAChC,SAAO,QAAQ;AACjB;AAQA,SAAS,aAAa,QAAQ,UAAU;AAEtC,UAAQ,MAAM,mBAAoB,WAAW,uBAAyB,SAAS,GAAI;AACrF;AACA,IAAI,YAAY,CAAC,oBAAoB,cAAc,UAAU,gBAAgB,MAAM;AAKnF,SAAS,UAAU,OAAO;AACxB,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,MAAI,SAAS,CAAC;AACd,YAAU,QAAQ,SAAU,GAAG;AAC7B,QAAI,MAAM,eAAe,CAAC,GAAG;AAC3B,aAAO,CAAC,IAAI,MAAM,CAAC;AAAA,IACrB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAaA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,SAAU,WAAW,KAAK;AACzD,cAAU,GAAG,IAAI,iBAAiB,OAAO,GAAG,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG;AACtE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAWA,SAAS,iBAAiB,OAAO,KAAK;AACpC,SAAO,MAAM,GAAG,MAAM;AACxB;AAOA,SAAS,kBAAkB,OAAO;AAChC,MAAI,MAAM,MAAM,KACd,UAAU,MAAM;AAElB,MAAI,WAAW,MAAM,WAAW,MAAM,IAAI,QAAQ,OAAO,MAAM,GAAG;AAChE,WAAO,UAAU;AAAA,EACnB;AACA,SAAO;AACT;AAOA,SAAS,cAAc,KAAK;AAC1B,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACjD;AAaA,SAAS,qBAAqB,YAAY,WAAW,WAAW,sBAAsB,UAAU;AAC9F,MAAI,aAAa,QAAQ;AACvB,eAAW;AAAA,EACb;AACA,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,YAAY;AACjC,MAAI,OAAO,cAAc,YAAY,YAAY,KAAK,aAAa,WAAW;AAC5E,gBAAY,aAAa,IAAI,KAAK,iBAAiB;AAAA,EACrD;AACA,MAAI,WAAW,YAAY;AAC3B,MAAI,WAAW,GAAG;AAChB,eAAW,WAAW,iBAAiB;AAAA,EACzC,WAAW,WAAW,gBAAgB;AACpC,eAAW,WAAW,IAAI;AAAA,EAC5B;AACA,MAAI,sBAAsB,wBAAwB,YAAY,UAAU,WAAW,sBAAsB,QAAQ;AACjH,MAAI,wBAAwB,IAAI;AAC9B,WAAO,aAAa,YAAY,KAAK;AAAA,EACvC;AACA,SAAO;AACT;AAYA,SAAS,wBAAwB,YAAY,WAAW,WAAW,sBAAsB,UAAU;AACjG,MAAI,qBAAqB,qBAAqB,SAAS;AACvD,MAAI,CAAC,sBAAsB,CAAC,mBAAmB,aAAa,UAAU,GAAG;AACvE,WAAO;AAAA,EACT;AACA,MAAI,aAAa,GAAG;AAClB,aAAS,QAAQ,YAAY,GAAG,QAAQ,WAAW,SAAS;AAC1D,UAAI,CAAC,qBAAqB,KAAK,EAAE,aAAa,UAAU,GAAG;AACzD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,OAAO;AACL,aAAS,SAAS,YAAY,GAAG,UAAU,GAAG,UAAU;AACtD,UAAI,CAAC,qBAAqB,MAAM,EAAE,aAAa,UAAU,GAAG;AAC1D,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU;AACZ,WAAO,aAAa,IAAI,wBAAwB,GAAG,GAAG,WAAW,sBAAsB,KAAK,IAAI,wBAAwB,IAAI,YAAY,GAAG,WAAW,sBAAsB,KAAK;AAAA,EACnL;AACA,SAAO;AACT;AAYA,SAAS,sBAAsB,QAAQ,mBAAmB,aAAa,oBAAoB;AACzF,MAAI,uBAAuB,QAAQ;AACjC,yBAAqB;AAAA,EACvB;AACA,SAAO,kBAAkB,KAAK,SAAU,aAAa;AACnD,WAAO,gBAAgB,iBAAiB,aAAa,QAAQ,WAAW,KAAK,sBAAsB,iBAAiB,aAAa,YAAY,SAAS,eAAe,WAAW;AAAA,EAClL,CAAC;AACH;AAGA,IAAI,8BAA8B;AAElC,IAAI,MAAuC;AACzC,gCAA8B,SAASG,6BAA4B,OAAO,WAAW,WAAW;AAC9F,QAAI,qBAAqB;AACzB,WAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,SAAS;AAC5C,UAAI,UAAU,OAAO,MAAM,UAAa,UAAU,OAAO,MAAM,QAAW;AAExE,gBAAQ,MAAM,6DAA8D,UAAU,2BAA4B,kBAAkB;AAAA,MACtI,WAAW,UAAU,OAAO,MAAM,UAAa,UAAU,OAAO,MAAM,QAAW;AAE/E,gBAAQ,MAAM,+DAAgE,UAAU,yBAA0B,kBAAkB;AAAA,MACtI;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,IAAI,gBAAgB,SAAS,SAAU,cAAc;AACnD,eAAa,YAAY,EAAE,cAAc;AAC3C,GAAG,GAAG;AAMN,SAAS,UAAU,QAAQ,cAAc;AACvC,MAAI,MAAM,aAAa,YAAY;AACnC,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,MAAI,cAAc;AAClB,gBAAc,YAAY;AAC5B;AAOA,SAAS,aAAa,cAAc;AAClC,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,YAAY,aAAa,eAAe,qBAAqB;AACjE,MAAI,WAAW;AACb,WAAO;AAAA,EACT;AACA,cAAY,aAAa,cAAc,KAAK;AAC5C,YAAU,aAAa,MAAM,qBAAqB;AAClD,YAAU,aAAa,QAAQ,QAAQ;AACvC,YAAU,aAAa,aAAa,QAAQ;AAC5C,YAAU,aAAa,iBAAiB,gBAAgB;AACxD,SAAO,OAAO,UAAU,OAAO;AAAA,IAC7B,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT,CAAC;AACD,eAAa,KAAK,YAAY,SAAS;AACvC,SAAO;AACT;AAEA,IAAI,UAAU,OAAwC,6BAA6B;AACnF,IAAI,UAAU,OAAwC,6BAA6B;AACnF,IAAI,iBAAiB,OAAwC,qCAAqC;AAClG,IAAI,iBAAiB,OAAwC,sCAAsC;AACnG,IAAI,mBAAmB,OAAwC,wCAAwC;AACvG,IAAI,gBAAgB,OAAwC,oCAAoC;AAChG,IAAI,eAAe,OAAwC,mCAAmC;AAC9F,IAAI,cAAc,OAAwC,kCAAkC;AAC5F,IAAI,aAAa,OAAwC,iCAAiC;AAC1F,IAAI,YAAY,OAAwC,gCAAgC;AACxF,IAAI,YAAY,OAAwC,gCAAgC;AACxF,IAAI,cAAc,OAAwC,kCAAkC;AAC5F,IAAI,qBAAqB,OAAwC,0CAA0C;AAC3G,IAAI,cAAc,OAAwC,kCAAkC;AAC5F,IAAI,aAAa,OAAwC,iCAAiC;AAC1F,IAAI,oCAAoC,OAAwC,2DAA2D;AAC3I,IAAI,WAAW,OAAwC,8BAA8B;AAErF,IAAI,qBAAkC,OAAO,OAAO;AAAA,EAClD,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAI,cAAc,CAAC,UAAU,KAAK;AAAlC,IACE,eAAe,CAAC,WAAW,WAAW,aAAa,WAAW,QAAQ;AADxE,IAEE,eAAe,CAAC,aAAa,UAAU,YAAY,WAAW,cAAc;AAF9E,IAGE,eAAe,CAAC,UAAU,KAAK;AAHjC,IAIE,aAAa,CAAC,eAAe,eAAe,WAAW,WAAW,SAAS,MAAM;AACnF,IAAI,YAAyB,WAAY;AACvC,MAAIC,aAAyB,SAAU,YAAY;AACjD,mBAAeA,YAAW,UAAU;AACpC,aAASA,WAAU,QAAQ;AACzB,UAAI;AACJ,cAAQ,WAAW,KAAK,MAAM,MAAM,KAAK;AAIzC,YAAM,KAAK,MAAM,MAAM,MAAM,eAAe,WAAW;AACvD,YAAM,SAAS,MAAM,MAAM,UAAU,MAAM,KAAK;AAChD,YAAM,UAAU,MAAM,MAAM,WAAW,MAAM,KAAK;AAClD,YAAM,UAAU,MAAM,MAAM,WAAW,MAAM,KAAK;AAClD,YAAM,YAAY,MAAM,MAAM,aAAa,SAAU,OAAO;AAC1D,eAAO,MAAM,KAAK,WAAW;AAAA,MAC/B;AACA,YAAM,QAAQ;AACd,YAAM,QAAQ,CAAC;AAMf,YAAM,YAAY;AAClB,YAAM,sBAAsB;AAC5B,YAAM,aAAa,CAAC;AAKpB,YAAM,qBAAqB,SAAU,IAAI,MAAM;AAC7C,YAAI,KAAK,WAAW,WAAY;AAC9B,gBAAM,aAAa,MAAM,WAAW,OAAO,SAAUC,IAAG;AACtD,mBAAOA,OAAM;AAAA,UACf,CAAC;AACD,aAAG;AAAA,QACL,GAAG,IAAI;AACP,cAAM,WAAW,KAAK,EAAE;AAAA,MAC1B;AACA,YAAM,eAAe,SAAU,OAAO;AACpC,cAAM,YAAY;AAAA,MACpB;AACA,YAAM,iBAAiB,WAAY;AACjC,cAAM,YAAY;AAAA,MACpB;AACA,YAAM,sBAAsB,SAAU,kBAAkB,iBAAiB;AACvE,YAAI,qBAAqB,QAAQ;AAC/B,6BAAmB,MAAM,MAAM;AAAA,QACjC;AACA,YAAI,oBAAoB,QAAQ;AAC9B,4BAAkB,CAAC;AAAA,QACrB;AACA,0BAAkB,UAAU,eAAe;AAC3C,cAAM,iBAAiB,SAAS;AAAA,UAC9B;AAAA,QACF,GAAG,eAAe,CAAC;AAAA,MACrB;AACA,YAAM,iBAAiB,SAAU,IAAI;AACnC,cAAM,iBAAiB;AAAA,UACrB,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,kBAAkB,MAAM,MAAM;AAAA,UAC9B,QAAQ,MAAM,MAAM;AAAA,QACtB,GAAG,EAAE;AAAA,MACP;AACA,YAAM,aAAa,SAAU,MAAM,iBAAiB,IAAI;AACtD,0BAAkB,UAAU,eAAe;AAC3C,cAAM,iBAAiB,SAAS;AAAA,UAC9B,QAAQ,MAAM,MAAM;AAAA,UACpB,kBAAkB,MAAM,MAAM;AAAA,UAC9B,cAAc;AAAA,UACd,YAAY,MAAM,MAAM,aAAa,IAAI;AAAA,QAC3C,GAAG,eAAe,GAAG,EAAE;AAAA,MACzB;AACA,YAAM,oBAAoB,SAAU,WAAW,iBAAiB,IAAI;AAClE,YAAI,OAAO,MAAM,MAAM,SAAS;AAChC,YAAI,QAAQ,MAAM;AAChB;AAAA,QACF;AACA,cAAM,WAAW,MAAM,iBAAiB,EAAE;AAAA,MAC5C;AACA,YAAM,wBAAwB,SAAU,iBAAiB,IAAI;AAC3D,eAAO,MAAM,kBAAkB,MAAM,SAAS,EAAE,kBAAkB,iBAAiB,EAAE;AAAA,MACvF;AASA,YAAM,mBAAmB,SAAU,YAAY,IAAI;AACjD,YAAI,gBAAgB;AACpB,YAAI,mBAAmB,CAAC;AACxB,YAAI,uBAAuB,OAAO,eAAe;AAOjD,YAAI,CAAC,wBAAwB,WAAW,eAAe,YAAY,GAAG;AACpE,gBAAM,MAAM,mBAAmB,WAAW,YAAY,SAAS,CAAC,GAAG,MAAM,mBAAmB,GAAG,UAAU,CAAC;AAAA,QAC5G;AACA,eAAO,MAAM,SAAS,SAAU,OAAO;AACrC,kBAAQ,MAAM,SAAS,KAAK;AAC5B,cAAI,gBAAgB,uBAAuB,WAAW,KAAK,IAAI;AAG/D,0BAAgB,MAAM,MAAM,aAAa,OAAO,aAAa;AAK7D,2BAAiB,cAAc,eAAe,cAAc;AAE5D,cAAI,YAAY,CAAC;AAIjB,cAAI,kBAAkB,cAAc,iBAAiB,MAAM,cAAc;AACvE,0BAAc,cAAc;AAAA,UAC9B;AACA,wBAAc,OAAO,cAAc,QAAQ;AAC3C,iBAAO,KAAK,aAAa,EAAE,QAAQ,SAAU,KAAK;AAGhD,gBAAI,MAAM,GAAG,MAAM,cAAc,GAAG,GAAG;AACrC,+BAAiB,GAAG,IAAI,cAAc,GAAG;AAAA,YAC3C;AAOA,gBAAI,QAAQ,QAAQ;AAClB;AAAA,YACF;AACA,0BAAc,GAAG;AAEjB,gBAAI,CAAC,iBAAiB,MAAM,OAAO,GAAG,GAAG;AACvC,wBAAU,GAAG,IAAI,cAAc,GAAG;AAAA,YACpC;AAAA,UACF,CAAC;AAID,cAAI,wBAAwB,cAAc,eAAe,YAAY,GAAG;AACtE,kBAAM,MAAM,mBAAmB,cAAc,YAAY,SAAS,CAAC,GAAG,MAAM,mBAAmB,GAAG,aAAa,CAAC;AAAA,UAClH;AACA,iBAAO;AAAA,QACT,GAAG,WAAY;AAEb,iBAAO,EAAE,EAAE;AAIX,cAAI,uBAAuB,OAAO,KAAK,gBAAgB,EAAE,SAAS;AAClE,cAAI,sBAAsB;AACxB,kBAAM,MAAM,cAAc,kBAAkB,MAAM,mBAAmB,CAAC;AAAA,UACxE;AACA,cAAI,gBAAgB;AAClB,kBAAM,MAAM,SAAS,WAAW,cAAc,MAAM,mBAAmB,CAAC;AAAA,UAC1E;AACA,cAAI,gBAAgB,QAAW;AAC7B,kBAAM,MAAM,SAAS,aAAa,MAAM,mBAAmB,CAAC;AAAA,UAC9D;AAGA,gBAAM,MAAM,aAAa,kBAAkB,MAAM,mBAAmB,CAAC;AAAA,QACvE,CAAC;AAAA,MACH;AAEA,YAAM,UAAU,SAAU,MAAM;AAC9B,eAAO,MAAM,YAAY;AAAA,MAC3B;AACA,YAAM,eAAe,SAAU,OAAO,QAAQ;AAC5C,YAAIC;AACJ,YAAI,OAAO,UAAU,SAAS,CAAC,IAAI,OACjC,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,QAAQ,aAC1C,MAAM,KAAK,KACX,OAAO,8BAA8B,MAAM,WAAW;AACxD,YAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,QAAQ;AAGhE,cAAM,aAAa,SAAS;AAC5B,cAAM,aAAa,SAAS;AAC5B,cAAM,aAAa,mBAAmB;AACtC,YAAI,iBAAiB,MAAM,SAAS,GAClC,SAAS,eAAe;AAC1B,eAAO,UAAUA,aAAY,CAAC,GAAGA,WAAU,MAAM,IAAI,WAAW,KAAK,MAAM,OAAO,GAAGA,WAAU,OAAO,YAAYA,WAAU,eAAe,IAAI,QAAQA,WAAU,eAAe,IAAI,WAAWA,WAAU,WAAW,IAAI,SAAS,MAAM,SAAS,MAAMA,WAAU,iBAAiB,IAAI,MAAM,SAASA,aAAY,IAAI;AAAA,MACtT;AAEA,YAAM,kBAAkB;AAAA,QACtB,WAAW,SAAS,UAAU,OAAO;AACnC,cAAI,SAAS;AACb,gBAAM,eAAe;AACrB,cAAI,KAAK,SAAS,EAAE,QAAQ;AAC1B,gBAAI,SAAS,MAAM,WAAW,IAAI;AAClC,iBAAK,qBAAqB,QAAQ;AAAA,cAChC,MAAM;AAAA,YACR,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,iBAAiB;AAAA,cACpB,QAAQ;AAAA,cACR,MAAM;AAAA,YACR,GAAG,WAAY;AACb,kBAAI,YAAY,OAAO,aAAa;AACpC,kBAAI,YAAY,GAAG;AACjB,oBAAI,kBAAkB,OAAO,SAAS,GACpC,mBAAmB,gBAAgB;AACrC,oBAAI,uBAAuB,qBAAqB,GAAG,kBAAkB,WAAW,SAAU,OAAO;AAC/F,yBAAO,OAAO,qBAAqB,KAAK;AAAA,gBAC1C,CAAC;AACD,uBAAO,oBAAoB,sBAAsB;AAAA,kBAC/C,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA,SAAS,SAAS,QAAQ,OAAO;AAC/B,cAAI,SAAS;AACb,gBAAM,eAAe;AACrB,cAAI,KAAK,SAAS,EAAE,QAAQ;AAC1B,gBAAI,SAAS,MAAM,WAAW,KAAK;AACnC,iBAAK,qBAAqB,QAAQ;AAAA,cAChC,MAAM;AAAA,YACR,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,iBAAiB;AAAA,cACpB,QAAQ;AAAA,cACR,MAAM;AAAA,YACR,GAAG,WAAY;AACb,kBAAI,YAAY,OAAO,aAAa;AACpC,kBAAI,YAAY,GAAG;AACjB,oBAAI,kBAAkB,OAAO,SAAS,GACpC,mBAAmB,gBAAgB;AACrC,oBAAI,uBAAuB,qBAAqB,IAAI,kBAAkB,WAAW,SAAU,OAAO;AAChG,yBAAO,OAAO,qBAAqB,KAAK;AAAA,gBAC1C,CAAC;AACD,uBAAO,oBAAoB,sBAAsB;AAAA,kBAC/C,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA,OAAO,SAAS,MAAM,OAAO;AAC3B,cAAI,MAAM,UAAU,KAAK;AACvB;AAAA,UACF;AACA,cAAI,kBAAkB,KAAK,SAAS,GAClC,SAAS,gBAAgB,QACzB,mBAAmB,gBAAgB;AACrC,cAAI,UAAU,oBAAoB,MAAM;AACtC,kBAAM,eAAe;AACrB,gBAAI,OAAO,KAAK,MAAM,gBAAgB;AACtC,gBAAI,WAAW,KAAK,qBAAqB,gBAAgB;AACzD,gBAAI,QAAQ,QAAQ,YAAY,SAAS,aAAa,UAAU,GAAG;AACjE;AAAA,YACF;AACA,iBAAK,sBAAsB;AAAA,cACzB,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA,QAAQ,SAAS,OAAO,OAAO;AAC7B,gBAAM,eAAe;AACrB,eAAK,MAAM,SAAS;AAAA,YAClB,MAAM;AAAA,UACR,GAAG,CAAC,KAAK,MAAM,UAAU;AAAA,YACvB,cAAc;AAAA,YACd,YAAY;AAAA,UACd,CAAC,CAAC;AAAA,QACJ;AAAA,MACF;AAEA,YAAM,wBAAwB,SAAS,CAAC,GAAG,MAAM,iBAAiB;AAAA,QAChE,KAAK,SAAS,EAAE,OAAO;AACrB,gBAAM,eAAe;AACrB,eAAK,WAAW;AAAA,YACd,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,YAAM,uBAAuB,SAAS,CAAC,GAAG,MAAM,iBAAiB;AAAA,QAC/D,MAAM,SAAS,KAAK,OAAO;AACzB,cAAI,SAAS;AACb,cAAI,kBAAkB,KAAK,SAAS,GAClC,SAAS,gBAAgB;AAC3B,cAAI,CAAC,QAAQ;AACX;AAAA,UACF;AACA,gBAAM,eAAe;AACrB,cAAI,YAAY,KAAK,aAAa;AAClC,cAAI,aAAa,KAAK,CAAC,QAAQ;AAC7B;AAAA,UACF;AAGA,cAAI,sBAAsB,wBAAwB,GAAG,GAAG,WAAW,SAAU,OAAO;AAClF,mBAAO,OAAO,qBAAqB,KAAK;AAAA,UAC1C,GAAG,KAAK;AACR,eAAK,oBAAoB,qBAAqB;AAAA,YAC5C,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,QACA,KAAK,SAAS,IAAI,OAAO;AACvB,cAAI,SAAS;AACb,cAAI,kBAAkB,KAAK,SAAS,GAClC,SAAS,gBAAgB;AAC3B,cAAI,CAAC,QAAQ;AACX;AAAA,UACF;AACA,gBAAM,eAAe;AACrB,cAAI,YAAY,KAAK,aAAa;AAClC,cAAI,aAAa,KAAK,CAAC,QAAQ;AAC7B;AAAA,UACF;AAGA,cAAI,sBAAsB,wBAAwB,IAAI,YAAY,GAAG,WAAW,SAAU,OAAO;AAC/F,mBAAO,OAAO,qBAAqB,KAAK;AAAA,UAC1C,GAAG,KAAK;AACR,eAAK,oBAAoB,qBAAqB;AAAA,YAC5C,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,YAAM,uBAAuB,SAAU,QAAQ;AAC7C,YAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,UAAU,MAAM;AAChB,cAAM;AACN,YAAI,YAAY,MAAM,WACtB,UAAU,MAAM,SAChB,SAAS,MAAM,QACf,OAAO,8BAA8B,OAAO,YAAY;AAC1D,YAAI,kBAAkB,MAAM,SAAS,GACnC,SAAS,gBAAgB;AAC3B,YAAI,uBAAuB;AAAA,UACzB,SAAS,qBAAqB,SAAS,MAAM,iBAAiB;AAAA,UAC9D,WAAW,qBAAqB,WAAW,MAAM,mBAAmB;AAAA,UACpE,SAAS,qBAAqB,SAAS,MAAM,iBAAiB;AAAA,UAC9D,QAAQ,qBAAqB,QAAQ,MAAM,gBAAgB;AAAA,QAC7D;AACA,YAAI,gBAAgB,KAAK,WAAW,CAAC,IAAI;AACzC,eAAO,SAAS;AAAA,UACd,MAAM;AAAA,UACN,MAAM;AAAA,UACN,cAAc,SAAS,eAAe;AAAA,UACtC,iBAAiB;AAAA,UACjB,eAAe;AAAA,QACjB,GAAG,eAAe,IAAI;AAAA,MACxB;AACA,YAAM,oBAAoB,SAAU,OAAO;AAEzC,cAAM,eAAe;AAAA,MACvB;AACA,YAAM,sBAAsB,SAAU,OAAO;AAC3C,YAAI,MAAM,kBAAkB,KAAK;AACjC,YAAI,MAAM,sBAAsB,GAAG,GAAG;AACpC,gBAAM,sBAAsB,GAAG,EAAE,KAAK,uBAAuB,KAAK,GAAG,KAAK;AAAA,QAC5E;AAAA,MACF;AACA,YAAM,oBAAoB,SAAU,OAAO;AACzC,cAAM,eAAe;AAIrB,YAAI,MAAM,MAAM,YAAY,SAAS,kBAAkB,MAAM,MAAM,YAAY,SAAS,MAAM;AAC5F,gBAAM,OAAO,MAAM;AAAA,QACrB;AAIA,YAAI,OAAiC;AACnC,gBAAM,WAAW;AAAA,YACf,MAAM;AAAA,UACR,CAAC;AAAA,QACH,OAAO;AAEL,gBAAM,mBAAmB,WAAY;AACnC,mBAAO,MAAM,WAAW;AAAA,cACtB,MAAM;AAAA,YACR,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,mBAAmB,SAAU,OAAO;AACxC,YAAI,aAAa,MAAM;AAEvB,cAAM,mBAAmB,WAAY;AACnC,cAAI,CAAC,MAAM,gBAAgB,MAAM,MAAM,YAAY,SAAS,iBAAiB,QAAQ,MAAM,MAAM,YAAY,SAAS,cAAc,OAAO,MAAM,YAAY,MAAM,MAAM,YAAY,SAAS,kBAAkB,YAC9M;AACA,kBAAM,MAAM;AAAA,cACV,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAGA,YAAM,gBAAgB,SAAU,OAAO;AACrC,eAAO,SAAS;AAAA,UACd,SAAS,MAAM;AAAA,UACf,IAAI,MAAM;AAAA,QACZ,GAAG,KAAK;AAAA,MACV;AAGA,YAAM,gBAAgB,SAAU,QAAQ;AACtC,YAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,YAAY,MAAM,WAClB,SAAS,MAAM,QACf,WAAW,MAAM,UACjB,UAAU,MAAM;AAChB,cAAM;AACN,YAAI,OAAO,8BAA8B,OAAO,YAAY;AAC9D,YAAI;AACJ,YAAI,gBAAgB,CAAC;AAGrB;AACE,wBAAc;AAAA,QAChB;AACA,YAAI,kBAAkB,MAAM,SAAS,GACnC,aAAa,gBAAgB,YAC7B,SAAS,gBAAgB,QACzB,mBAAmB,gBAAgB;AACrC,YAAI,CAAC,KAAK,UAAU;AAClB,cAAI;AACJ,2BAAiB,iBAAiB,CAAC,GAAG,eAAe,WAAW,IAAI,qBAAqB,UAAU,SAAS,MAAM,iBAAiB,GAAG,eAAe,YAAY,qBAAqB,WAAW,MAAM,kBAAkB,GAAG,eAAe,SAAS,qBAAqB,QAAQ,MAAM,eAAe,GAAG;AAAA,QAC3S;AACA,eAAO,SAAS;AAAA,UACd,qBAAqB;AAAA,UACrB,yBAAyB,UAAU,OAAO,qBAAqB,YAAY,oBAAoB,IAAI,MAAM,UAAU,gBAAgB,IAAI;AAAA,UACvI,iBAAiB,SAAS,MAAM,SAAS;AAAA,UACzC,mBAAmB,QAAQ,KAAK,YAAY,IAAI,SAAY,MAAM;AAAA;AAAA;AAAA,UAGlE,cAAc;AAAA,UACd,OAAO;AAAA,UACP,IAAI,MAAM;AAAA,QACZ,GAAG,eAAe,IAAI;AAAA,MACxB;AACA,YAAM,qBAAqB,SAAU,OAAO;AAC1C,YAAI,MAAM,kBAAkB,KAAK;AACjC,YAAI,OAAO,MAAM,qBAAqB,GAAG,GAAG;AAC1C,gBAAM,qBAAqB,GAAG,EAAE,KAAK,uBAAuB,KAAK,GAAG,KAAK;AAAA,QAC3E;AAAA,MACF;AACA,YAAM,oBAAoB,SAAU,OAAO;AACzC,cAAM,iBAAiB;AAAA,UACrB,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY,MAAM,OAAO;AAAA,UACzB,kBAAkB,MAAM,MAAM;AAAA,QAChC,CAAC;AAAA,MACH;AACA,YAAM,kBAAkB,WAAY;AAElC,cAAM,mBAAmB,WAAY;AACnC,cAAI,0BAA0B,MAAM,MAAM,YAAY,YAAY,CAAC,CAAC,MAAM,MAAM,YAAY,SAAS,iBAAiB,CAAC,CAAC,MAAM,MAAM,YAAY,SAAS,cAAc,WAAW,MAAM,MAAM,YAAY,SAAS,cAAc,QAAQ,UAAU,MAAM,aAAa,MAAM,UAAU,SAAS,MAAM,MAAM,YAAY,SAAS,aAAa;AAC7U,cAAI,CAAC,MAAM,eAAe,CAAC,yBAAyB;AAClD,kBAAM,MAAM;AAAA,cACV,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAGA,YAAM,UAAU,SAAU,MAAM;AAC9B,cAAM,YAAY;AAAA,MACpB;AACA,YAAM,eAAe,SAAU,QAAQ,QAAQ;AAC7C,YAAI;AACJ,YAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,QAAQ,cAC3C,MAAM,MAAM,KACZ,QAAQ,8BAA8B,OAAO,YAAY;AAC3D,YAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,QAAQ;AAChE,cAAM,aAAa,SAAS;AAC5B,cAAM,aAAa,SAAS;AAC5B,cAAM,aAAa,mBAAmB;AACtC,eAAO,UAAU,YAAY,CAAC,GAAG,UAAU,MAAM,IAAI,WAAW,KAAK,MAAM,OAAO,GAAG,UAAU,OAAO,WAAW,UAAU,iBAAiB,IAAI,SAAS,MAAM,YAAY,IAAI,OAAO,MAAM,SAAS,UAAU,KAAK,MAAM,QAAQ,YAAY,KAAK;AAAA,MACrP;AAGA,YAAM,eAAe,SAAU,QAAQ;AACrC,YAAI;AACJ,YAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,cAAc,MAAM,aACpB,cAAc,MAAM,aACpB,UAAU,MAAM;AAChB,cAAM;AACN,YAAI,QAAQ,MAAM,OAClB,aAAa,MAAM,MACnB,OAAO,eAAe,SAAS;AAAA;AAAA,UAAkE;AAAA,YAAY,aAAa,gBAAgB,MAAM,IAAI,YACpJ,OAAO,8BAA8B,OAAO,UAAU;AACxD,YAAI,UAAU,QAAW;AACvB,gBAAM,MAAM,KAAK,IAAI;AACrB,kBAAQ,MAAM,MAAM,QAAQ,IAAI;AAAA,QAClC,OAAO;AACL,gBAAM,MAAM,KAAK,IAAI;AAAA,QACvB;AACA,YAAI,cAAc;AAClB,YAAI,qBAAqB;AACzB,YAAI,wBAAwB,wBAAwB;AAAA;AAAA;AAAA;AAAA,UAIlD,aAAa,qBAAqB,aAAa,WAAY;AACzD,gBAAI,UAAU,MAAM,SAAS,EAAE,kBAAkB;AAC/C;AAAA,YACF;AACA,kBAAM,oBAAoB,OAAO;AAAA,cAC/B,MAAM;AAAA,YACR,CAAC;AAMD,kBAAM,iBAAiB;AACvB,kBAAM,mBAAmB,WAAY;AACnC,qBAAO,MAAM,iBAAiB;AAAA,YAChC,GAAG,GAAG;AAAA,UACR,CAAC;AAAA,UACD,aAAa,qBAAqB,aAAa,SAAU,OAAO;AAI9D,kBAAM,eAAe;AAAA,UACvB,CAAC;AAAA,QACH,GAAG,sBAAsB,WAAW,IAAI,qBAAqB,oBAAoB,WAAY;AAC3F,gBAAM,kBAAkB,OAAO;AAAA,YAC7B,MAAM;AAAA,UACR,CAAC;AAAA,QACH,CAAC,GAAG;AAIJ,YAAI,gBAAgB,KAAK,WAAW;AAAA,UAClC,aAAa,qBAAqB;AAAA,QACpC,IAAI;AACJ,eAAO,SAAS;AAAA,UACd,IAAI,MAAM,UAAU,KAAK;AAAA,UACzB,MAAM;AAAA,UACN,iBAAiB,MAAM,SAAS,EAAE,qBAAqB;AAAA,QACzD,GAAG,eAAe,IAAI;AAAA,MACxB;AAEA,YAAM,aAAa,WAAY;AAC7B,cAAM,QAAQ,CAAC;AAAA,MACjB;AACA,YAAM,QAAQ,SAAU,iBAAiB,IAAI;AAC3C,YAAI,oBAAoB,QAAQ;AAC9B,4BAAkB,CAAC;AAAA,QACrB;AACA,0BAAkB,UAAU,eAAe;AAC3C,cAAM,iBAAiB,SAAU,OAAO;AACtC,cAAI,eAAe,MAAM;AACzB,iBAAO,SAAS;AAAA,YACd,QAAQ,MAAM,MAAM;AAAA,YACpB,kBAAkB,MAAM,MAAM;AAAA,YAC9B,YAAY,MAAM,MAAM,aAAa,YAAY;AAAA,UACnD,GAAG,eAAe;AAAA,QACpB,GAAG,EAAE;AAAA,MACP;AACA,YAAM,aAAa,SAAU,iBAAiB,IAAI;AAChD,YAAI,oBAAoB,QAAQ;AAC9B,4BAAkB,CAAC;AAAA,QACrB;AACA,0BAAkB,UAAU,eAAe;AAC3C,cAAM,iBAAiB,SAAU,OAAO;AACtC,cAAI,SAAS,MAAM;AACnB,iBAAO,SAAS;AAAA,YACd,QAAQ,CAAC;AAAA,UACX,GAAG,UAAU;AAAA,YACX,kBAAkB,MAAM,MAAM;AAAA,UAChC,GAAG,eAAe;AAAA,QACpB,GAAG,WAAY;AACb,cAAI,kBAAkB,MAAM,SAAS,GACnC,SAAS,gBAAgB,QACzB,mBAAmB,gBAAgB;AACrC,cAAI,QAAQ;AACV,gBAAI,MAAM,aAAa,IAAI,KAAK,OAAO,qBAAqB,UAAU;AACpE,oBAAM,oBAAoB,kBAAkB,eAAe;AAAA,YAC7D;AAAA,UACF;AACA,iBAAO,EAAE,EAAE;AAAA,QACb,CAAC;AAAA,MACH;AACA,YAAM,WAAW,SAAU,IAAI;AAC7B,cAAM,iBAAiB;AAAA,UACrB,QAAQ;AAAA,QACV,GAAG,EAAE;AAAA,MACP;AACA,YAAM,YAAY,SAAU,IAAI;AAC9B,cAAM,iBAAiB;AAAA,UACrB,QAAQ;AAAA,QACV,GAAG,EAAE;AAAA,MACP;AACA,YAAM,eAAe,SAAS,WAAY;AACxC,YAAI,QAAQ,MAAM,SAAS;AAC3B,YAAI,OAAO,MAAM,MAAM,MAAM,gBAAgB;AAC7C,YAAI,cAAc,MAAM,aAAa;AACrC,YAAI,SAAS,MAAM,MAAM,qBAAqB,SAAS;AAAA,UACrD,cAAc,MAAM,MAAM;AAAA,UAC1B,qBAAqB,MAAM;AAAA,UAC3B;AAAA,UACA,iBAAiB;AAAA,QACnB,GAAG,KAAK,CAAC;AACT,cAAM,sBAAsB;AAC5B,kBAAU,QAAQ,MAAM,MAAM,YAAY,QAAQ;AAAA,MACpD,GAAG,GAAG;AACN,UAAI,cAAc,MAAM,OACtB,0BAA0B,YAAY,yBACtC,wBAAwB,YAAY,yBACpC,oBAAoB,0BAA0B,SAAS,0BAA0B,uBACjF,gBAAgB,YAAY,eAC5B,wBAAwB,YAAY,eACpC,UAAU,0BAA0B,SAAS,gBAAgB,uBAC7D,wBAAwB,YAAY,mBACpC,cAAc,0BAA0B,SAAS,KAAK,uBACtD,wBAAwB,YAAY,qBACpC,gBAAgB,0BAA0B,SAAS,OAAO;AAC5D,UAAI,SAAS,MAAM,SAAS;AAAA,QAC1B,kBAAkB;AAAA,QAClB,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB,CAAC;AACD,UAAI,OAAO,gBAAgB,QAAQ,MAAM,MAAM,sBAAsB,QAAW;AAC9E,eAAO,aAAa,MAAM,MAAM,aAAa,OAAO,YAAY;AAAA,MAClE;AACA,YAAM,QAAQ;AACd,aAAO;AAAA,IACT;AACA,QAAI,SAASF,WAAU;AAIvB,WAAO,wBAAwB,SAAS,wBAAwB;AAC9D,WAAK,WAAW,QAAQ,SAAU,IAAI;AACpC,qBAAa,EAAE;AAAA,MACjB,CAAC;AACD,WAAK,aAAa,CAAC;AAAA,IACrB;AAWA,WAAO,WAAW,SAAS,WAAW,cAAc;AAClD,UAAI,iBAAiB,QAAQ;AAC3B,uBAAe,KAAK;AAAA,MACtB;AACA,aAAO,SAAS,cAAc,KAAK,KAAK;AAAA,IAC1C;AACA,WAAO,eAAe,SAAS,eAAe;AAK5C,UAAI,YAAY,KAAK,MAAM;AAC3B,UAAI,KAAK,aAAa,MAAM;AAC1B,oBAAY,KAAK;AAAA,MACnB,WAAW,KAAK,MAAM,cAAc,QAAW;AAC7C,oBAAY,KAAK,MAAM;AAAA,MACzB;AACA,aAAO;AAAA,IACT;AACA,WAAO,uBAAuB,SAAS,qBAAqB,OAAO;AACjE,aAAO,KAAK,MAAM,YAAY,SAAS,eAAe,KAAK,UAAU,KAAK,CAAC;AAAA,IAC7E;AACA,WAAO,gCAAgC,SAAS,gCAAgC;AAE9E;AACE,YAAI,OAAO,KAAK,qBAAqB,KAAK,SAAS,EAAE,gBAAgB;AACrE,aAAK,MAAM,eAAe,MAAM,KAAK,SAAS;AAAA,MAChD;AAAA,IACF;AACA,WAAO,uBAAuB,SAAS,qBAAqB,QAAQ,iBAAiB;AACnF,UAAI,SAAS;AACb,UAAI,YAAY,KAAK,aAAa;AAClC,UAAI,kBAAkB,KAAK,SAAS,GAClC,mBAAmB,gBAAgB;AACrC,UAAI,YAAY,GAAG;AACjB,YAAI,uBAAuB,qBAAqB,QAAQ,kBAAkB,WAAW,SAAU,OAAO;AACpG,iBAAO,OAAO,qBAAqB,KAAK;AAAA,QAC1C,CAAC;AACD,aAAK,oBAAoB,sBAAsB,eAAe;AAAA,MAChE;AAAA,IACF;AACA,WAAO,qBAAqB,SAAS,qBAAqB;AACxD,UAAI,kBAAkB,KAAK,SAAS,GAClC,mBAAmB,gBAAgB,kBACnC,aAAa,gBAAgB,YAC7B,eAAe,gBAAgB,cAC/B,SAAS,gBAAgB;AAC3B,UAAIG,gBAAe,KAAK,MAAM;AAC9B,UAAI,KAAK,KAAK;AACd,UAAI,eAAe,KAAK,cACtB,uBAAuB,KAAK,sBAC5B,gBAAgB,KAAK,eACrB,eAAe,KAAK,cACpB,gBAAgB,KAAK,eACrB,eAAe,KAAK,cACpB,WAAW,KAAK,UAChB,YAAY,KAAK,WACjB,aAAa,KAAK,YAClB,aAAa,KAAK,YAClB,oBAAoB,KAAK,mBACzB,wBAAwB,KAAK,uBAC7B,sBAAsB,KAAK,qBAC3B,iBAAiB,KAAK,gBACtB,aAAa,KAAK,YAClB,QAAQ,KAAK,OACb,eAAe,KAAK,cACpB,iBAAiB,KAAK,gBACtB,WAAW,KAAK;AAClB,aAAO;AAAA;AAAA,QAEL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA,cAAcA;AAAA;AAAA,QAEd;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,oBAAoB,SAAS,oBAAoB;AACtD,UAAI,SAAS;AAEb,UAAuD,KAAK,aAAa,UAAU,CAAC,KAAK,aAAa,kBAAkB;AACtH,4CAAoC,KAAK,WAAW,KAAK,YAAY;AAAA,MACvE;AAGA;AAME,YAAI,cAAc,SAASC,eAAc;AACvC,iBAAO,cAAc;AAAA,QACvB;AACA,YAAI,YAAY,SAASC,WAAU,OAAO;AACxC,iBAAO,cAAc;AAGrB,cAAI,yBAAyB,sBAAsB,MAAM,QAAQ,CAAC,OAAO,WAAW,OAAO,SAAS,GAAG,OAAO,MAAM,WAAW;AAC/H,cAAI,CAAC,0BAA0B,OAAO,SAAS,EAAE,QAAQ;AACvD,mBAAO,MAAM;AAAA,cACX,MAAM;AAAA,YACR,GAAG,WAAY;AACb,qBAAO,OAAO,MAAM,aAAa,OAAO,mBAAmB,CAAC;AAAA,YAC9D,CAAC;AAAA,UACH;AAAA,QACF;AAOA,YAAI,eAAe,SAASC,gBAAe;AACzC,iBAAO,cAAc;AAAA,QACvB;AACA,YAAI,cAAc,SAASC,eAAc;AACvC,iBAAO,cAAc;AAAA,QACvB;AACA,YAAI,aAAa,SAASC,YAAW,OAAO;AAC1C,cAAI,yBAAyB,sBAAsB,MAAM,QAAQ,CAAC,OAAO,WAAW,OAAO,SAAS,GAAG,OAAO,MAAM,aAAa,KAAK;AACtI,cAAI,CAAC,OAAO,eAAe,CAAC,0BAA0B,OAAO,SAAS,EAAE,QAAQ;AAC9E,mBAAO,MAAM;AAAA,cACX,MAAM;AAAA,YACR,GAAG,WAAY;AACb,qBAAO,OAAO,MAAM,aAAa,OAAO,mBAAmB,CAAC;AAAA,YAC9D,CAAC;AAAA,UACH;AAAA,QACF;AACA,YAAI,cAAc,KAAK,MAAM;AAC7B,oBAAY,iBAAiB,aAAa,WAAW;AACrD,oBAAY,iBAAiB,WAAW,SAAS;AACjD,oBAAY,iBAAiB,cAAc,YAAY;AACvD,oBAAY,iBAAiB,aAAa,WAAW;AACrD,oBAAY,iBAAiB,YAAY,UAAU;AACnD,aAAK,UAAU,WAAY;AACzB,iBAAO,sBAAsB;AAC7B,iBAAO,aAAa,OAAO;AAC3B,sBAAY,oBAAoB,aAAa,WAAW;AACxD,sBAAY,oBAAoB,WAAW,SAAS;AACpD,sBAAY,oBAAoB,cAAc,YAAY;AAC1D,sBAAY,oBAAoB,aAAa,WAAW;AACxD,sBAAY,oBAAoB,YAAY,UAAU;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AACA,WAAO,eAAe,SAAS,aAAa,WAAW,WAAW;AAChE,UAAI,SAAS,KAAK,MAAM,qBAAqB,SAAY,KAAK,SAAS,IAAI,KAAK,OAC9E,0BAA0B,OAAO;AACnC,UAAI,SAAS,UAAU,qBAAqB,SAAY,YAAY,WAClE,uBAAuB,OAAO;AAChC,UAAI,iBAAiB,2BAA2B,KAAK,SAAS,EAAE,UAAU,CAAC,UAAU;AACrF,UAAI,uBAAuB,4BAA4B;AACvD,aAAO,kBAAkB;AAAA,IAC3B;AACA,WAAO,qBAAqB,SAAS,mBAAmB,WAAW,WAAW;AAC5E,UAAI,MAAuC;AACzC,oCAA4B,KAAK,OAAO,WAAW,KAAK,KAAK;AAE7D,YAAI,KAAK,aAAa,UAAU,CAAC,KAAK,aAAa,kBAAkB;AACnE,8CAAoC,KAAK,WAAW,KAAK,YAAY;AAAA,QACvE;AAAA,MACF;AACA,UAAI,iBAAiB,KAAK,OAAO,cAAc,KAAK,KAAK,MAAM,oBAAoB,UAAU,cAAc,KAAK,MAAM,YAAY,GAAG;AACnI,aAAK,iBAAiB;AAAA,UACpB,MAAM;AAAA,UACN,YAAY,KAAK,MAAM,aAAa,KAAK,MAAM,YAAY;AAAA,QAC7D,CAAC;AAAA,MACH;AACA,UAAI,CAAC,KAAK,kBAAkB,KAAK,aAAa,WAAW,SAAS,GAAG;AACnE,aAAK,8BAA8B;AAAA,MACrC;AAGA;AACE,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AACA,WAAO,uBAAuB,SAAS,uBAAuB;AAC5D,WAAK,QAAQ;AAAA,IACf;AACA,WAAO,SAAS,SAAS,SAAS;AAChC,UAAI,WAAW,YAAY,KAAK,MAAM,UAAU,IAAI;AAIpD,WAAK,WAAW;AAKhB,WAAK,aAAa,SAAS;AAC3B,WAAK,aAAa,SAAS;AAC3B,WAAK,aAAa,mBAAmB;AAErC,WAAK,aAAa,SAAS;AAC3B,WAAK,aAAa,SAAS;AAC3B,WAAK,aAAa,mBAAmB;AAErC,WAAK,cAAc,SAAS;AAE5B,WAAK,cAAc,SAAS;AAC5B,UAAI,UAAU,YAAY,SAAS,KAAK,mBAAmB,CAAC,CAAC;AAC7D,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AACA,UAAI,KAAK,aAAa,UAAU,KAAK,MAAM,kBAAkB;AAC3D,YAA6C,CAAC,KAAK,aAAa,oBAAoB,CAAC,KAAK,MAAM,kBAAkB;AAChH,8CAAoC,SAAS,KAAK,YAAY;AAAA,QAChE;AACA,eAAO;AAAA,MACT,WAAW,aAAa,OAAO,GAAG;AAGhC,mBAAoB,2BAAa,SAAS,KAAK,aAAa,gBAAgB,OAAO,CAAC,CAAC;AAAA,MACvF;AAGA,UAAI,MAAuC;AAIzC,cAAM,IAAI,MAAM,sFAAsF;AAAA,MACxG;AAGA,aAAO;AAAA,IACT;AACA,WAAOR;AAAA,EACT,EAAE,sBAAS;AACX,EAAAA,WAAU,eAAe;AAAA,IACvB,yBAAyB;AAAA,IACzB,eAAe;AAAA,IACf,sBAAsB;AAAA,IACtB,cAAc,SAASG,cAAaF,IAAG;AACrC,UAAIA,MAAK,MAAM;AACb,eAAO;AAAA,MACT;AACA,UAA6C,cAAcA,EAAC,KAAK,CAACA,GAAE,eAAe,UAAU,GAAG;AAE9F,gBAAQ,KAAK,8MAA8M,+BAA+BA,EAAC;AAAA,MAC7P;AACA,aAAO,OAAOA,EAAC;AAAA,IACjB;AAAA,IACA,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,qBAAqB,SAASQ,qBAAoB,UAAU,MAAM;AAChE,aAAO,aAAa;AAAA,IACtB;AAAA,IACA;AAAA;AAAA,MACA,OAAO,WAAW,cAAc,CAAC,IAAI;AAAA;AAAA,IACrC,cAAc,SAASC,cAAa,OAAO,YAAY;AACrD,aAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,IAClB;AAAA,EACF;AACA,EAAAV,WAAU,mBAAmB;AAC7B,SAAOA;AACT,EAAE;AACF,OAAwC,UAAU,YAAY;AAAA,EAC5D,UAAU,kBAAAW,QAAU;AAAA,EACpB,yBAAyB,kBAAAA,QAAU;AAAA,EACnC,eAAe,kBAAAA,QAAU;AAAA,EACzB,yBAAyB,kBAAAA,QAAU;AAAA,EACnC,qBAAqB,kBAAAA,QAAU;AAAA,EAC/B,mBAAmB,kBAAAA,QAAU;AAAA,EAC7B,eAAe,kBAAAA,QAAU;AAAA,EACzB,sBAAsB,kBAAAA,QAAU;AAAA,EAChC,cAAc,kBAAAA,QAAU;AAAA,EACxB,UAAU,kBAAAA,QAAU;AAAA,EACpB,UAAU,kBAAAA,QAAU;AAAA,EACpB,eAAe,kBAAAA,QAAU;AAAA,EACzB,oBAAoB,kBAAAA,QAAU;AAAA,EAC9B,cAAc,kBAAAA,QAAU;AAAA,EACxB,cAAc,kBAAAA,QAAU;AAAA,EACxB,qBAAqB,kBAAAA,QAAU;AAAA,EAC/B,cAAc,kBAAAA,QAAU;AAAA,EACxB,WAAW,kBAAAA,QAAU;AAAA,EACrB,IAAI,kBAAAA,QAAU;AAAA,EACd,aAAa,kBAAAA,QAAU,MAAM;AAAA,IAC3B,kBAAkB,kBAAAA,QAAU;AAAA,IAC5B,qBAAqB,kBAAAA,QAAU;AAAA,IAC/B,UAAU,kBAAAA,QAAU,MAAM;AAAA,MACxB,gBAAgB,kBAAAA,QAAU;AAAA,MAC1B,eAAe,kBAAAA,QAAU;AAAA,MACzB,MAAM,kBAAAA,QAAU;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AAAA,EACD,kBAAkB,kBAAAA,QAAU;AAAA,EAC5B,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI1B,cAAc,kBAAAA,QAAU;AAAA,EACxB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,YAAY,kBAAAA,QAAU;AAAA,EACtB,kBAAkB,kBAAAA,QAAU;AAAA,EAC5B,SAAS,kBAAAA,QAAU;AAAA,EACnB,SAAS,kBAAAA,QAAU;AAAA,EACnB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,WAAW,kBAAAA,QAAU;AAAA;AAEvB,IAAI;AACJ,IAAI,cAAc;AAClB,SAAS,oCAAoC,MAAM,QAAQ;AACzD,MAAI,SAAS,OAAO;AACpB,MAAI,CAAC,MAAM;AAET,YAAQ,MAAM,8BAA+B,SAAS,qEAAsE;AAAA,EAC9H;AACF;AACA,SAAS,oCAAoC,SAAS,QAAQ;AAC5D,MAAI,SAAS,OAAO;AACpB,MAAI,kBAAkB,WAAW;AACjC,MAAI,cAAc,CAAC,aAAa,OAAO;AACvC,MAAI,eAAe,CAAC,mBAAmB,KAAC,8BAAa,OAAO,GAAG;AAE7D,YAAQ,MAAM,sFAAsF;AAAA,EACtG,WAAW,CAAC,eAAe,iBAAiB;AAE1C,YAAQ,MAAM,4GAA6G,SAAS,GAAI;AAAA,EAC1I;AACA,MAAI,KAAC,8BAAa,OAAO,KAAK,CAAC,gBAAgB,OAAO,EAAE,MAAM,GAAG;AAE/D,YAAQ,MAAM,6CAA8C,SAAS,6CAA8C;AAAA,EACrH;AACF;AAEA,IAAI,cAAc,CAAC,kBAAkB,oBAAoB,SAAS,aAAa;AAC/E,IAAI,6BAA6B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,YAAY;AACd;AACA,SAAS,kBAAkB,QAAQ,OAAO,UAAU;AAClD,MAAI,QAAQ,OAAO,OACjB,OAAO,OAAO;AAChB,MAAI,UAAU,CAAC;AACf,SAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,KAAK;AACxC,0BAAsB,KAAK,QAAQ,OAAO,QAAQ;AAClD,QAAI,SAAS,GAAG,MAAM,MAAM,GAAG,GAAG;AAChC,cAAQ,GAAG,IAAI,SAAS,GAAG;AAAA,IAC7B;AAAA,EACF,CAAC;AACD,MAAI,MAAM,iBAAiB,OAAO,KAAK,OAAO,EAAE,QAAQ;AACtD,UAAM,cAAc,SAAS;AAAA,MAC3B;AAAA,IACF,GAAG,OAAO,CAAC;AAAA,EACb;AACF;AACA,SAAS,sBAAsB,KAAK,QAAQ,OAAO,UAAU;AAC3D,MAAI,QAAQ,OAAO,OACjB,OAAO,OAAO;AAChB,MAAI,UAAU,OAAO,iBAAiB,GAAG,IAAI;AAC7C,MAAI,MAAM,OAAO,KAAK,SAAS,GAAG,MAAM,UAAa,SAAS,GAAG,MAAM,MAAM,GAAG,GAAG;AACjF,UAAM,OAAO,EAAE,SAAS;AAAA,MACtB;AAAA,IACF,GAAG,QAAQ,CAAC;AAAA,EACd;AACF;AASA,SAAS,aAAa,GAAG,GAAG;AAC1B,SAAO,EAAE;AACX;AAQA,SAAS,wBAAwB,qBAAqB;AACpD,MAAI,eAAe,oBAAoB,cACrC,oBAAoB,oBAAoB;AAC1C,SAAO,eAAe,kBAAkB,YAAY,IAAI,wBAAwB;AAClF;AAKA,IAAI,mBAAmB,SAAS,SAAU,gBAAgBC,WAAU;AAClE,YAAU,eAAe,GAAGA,SAAQ;AACtC,GAAG,GAAG;AAGN,IAAI,4BAA4B,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,eAAe,OAAO,OAAO,SAAS,kBAAkB,cAAc,+BAAkB;AACpL,SAAS,cAAc,MAAM;AAC3B,MAAI,UAAU,KAAK,IACjB,KAAK,YAAY,SAAS,eAAe,WAAW,IAAI,SACxD,UAAU,KAAK,SACf,SAAS,KAAK,QACd,YAAY,KAAK,WACjB,iBAAiB,KAAK,gBACtB,UAAU,KAAK;AACjB,MAAI,oBAAgB,qBAAO;AAAA,IACzB,SAAS,WAAW,KAAK;AAAA,IACzB,QAAQ,UAAU,KAAK;AAAA,IACvB,WAAW,aAAa,SAAU,OAAO;AACvC,aAAO,KAAK,WAAW;AAAA,IACzB;AAAA,IACA,gBAAgB,kBAAkB,KAAK;AAAA,IACvC,SAAS,WAAW,KAAK;AAAA,EAC3B,CAAC;AACD,SAAO,cAAc;AACvB;AACA,SAAS,gBAAgB,UAAU,WAAW,OAAO,cAAc;AACjE,MAAI,MAAM;AACV,MAAI,aAAa,QAAW;AAC1B,QAAI,cAAc,QAAW;AAC3B,YAAM,IAAI,MAAM,YAAY;AAAA,IAC9B;AACA,WAAO,MAAM,SAAS;AACtB,YAAQ;AAAA,EACV,OAAO;AACL,YAAQ,cAAc,SAAY,MAAM,QAAQ,QAAQ,IAAI;AAC5D,WAAO;AAAA,EACT;AACA,SAAO,CAAC,MAAM,KAAK;AACrB;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,OAAO,OAAO,IAAI,IAAI;AAC/B;AACA,SAAS,uBAAuB,KAAK;AACnC,SAAO,UAAU,KAAK,GAAG;AAC3B;AACA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,KAAK,OAAO,MAAM,GAAG,CAAC,EAAE,YAAY,IAAI,OAAO,MAAM,CAAC;AAC/D;AACA,SAAS,aAAa,KAAK;AACzB,MAAI,UAAM,qBAAO,GAAG;AAMpB,MAAI,UAAU;AACd,SAAO;AACT;AAYA,SAAS,mBAAmB,SAAS,cAAc,OAAO;AACxD,MAAI,mBAAe,qBAAO;AAC1B,MAAI,gBAAY,qBAAO;AACvB,MAAI,sBAAkB,0BAAY,SAAUC,QAAOC,SAAQ;AACzD,cAAU,UAAUA;AACpB,IAAAD,SAAQ,SAASA,QAAOC,QAAO,KAAK;AACpC,QAAI,UAAU,QAAQD,QAAOC,OAAM;AACnC,QAAI,WAAWA,QAAO,MAAM,aAAaD,QAAO,SAAS,CAAC,GAAGC,SAAQ;AAAA,MACnE;AAAA,IACF,CAAC,CAAC;AACF,WAAO;AAAA,EACT,GAAG,CAAC,OAAO,CAAC;AACZ,MAAI,kBAAc,yBAAW,iBAAiB,YAAY,GACxD,QAAQ,YAAY,CAAC,GACrB,WAAW,YAAY,CAAC;AAC1B,MAAI,WAAW,aAAa,KAAK;AACjC,MAAI,wBAAoB,0BAAY,SAAUA,SAAQ;AACpD,WAAO,SAAS,SAAS;AAAA,MACvB,OAAO,SAAS;AAAA,IAClB,GAAGA,OAAM,CAAC;AAAA,EACZ,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,SAAS,UAAU;AACvB,8BAAU,WAAY;AACpB,QAAI,UAAU,aAAa,WAAW,aAAa,YAAY,OAAO;AACpE,wBAAkB,QAAQ,SAAS,aAAa,SAAS,OAAO,KAAK,GAAG,KAAK;AAAA,IAC/E;AACA,iBAAa,UAAU;AAAA,EACzB,GAAG,CAAC,OAAO,OAAO,MAAM,CAAC;AACzB,SAAO,CAAC,OAAO,iBAAiB;AAClC;AAWA,SAAS,uBAAuB,SAAS,cAAc,OAAO;AAC5D,MAAI,sBAAsB,mBAAmB,SAAS,cAAc,KAAK,GACvE,QAAQ,oBAAoB,CAAC,GAC7B,WAAW,oBAAoB,CAAC;AAClC,SAAO,CAAC,SAAS,OAAO,KAAK,GAAG,QAAQ;AAC1C;AACA,IAAI,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,IACA,OAAO,WAAW,cAAc,CAAC,IAAI;AAAA;AACvC;AACA,SAAS,kBAAkB,OAAO,SAASC,qBAAoB;AAC7D,MAAIA,wBAAuB,QAAQ;AACjC,IAAAA,sBAAqB;AAAA,EACvB;AACA,MAAI,eAAe,MAAM,YAAY,iBAAiB,OAAO,CAAC;AAC9D,MAAI,iBAAiB,QAAW;AAC9B,WAAO;AAAA,EACT;AACA,SAAOA,oBAAmB,OAAO;AACnC;AACA,SAAS,kBAAkB,OAAO,SAASA,qBAAoB;AAC7D,MAAIA,wBAAuB,QAAQ;AACjC,IAAAA,sBAAqB;AAAA,EACvB;AACA,MAAI,QAAQ,MAAM,OAAO;AACzB,MAAI,UAAU,QAAW;AACvB,WAAO;AAAA,EACT;AACA,MAAI,eAAe,MAAM,YAAY,iBAAiB,OAAO,CAAC;AAC9D,MAAI,iBAAiB,QAAW;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,kBAAkB,OAAO,SAASA,mBAAkB;AAC7D;AACA,SAAS,kBAAkB,OAAO;AAChC,MAAI,eAAe,kBAAkB,OAAO,cAAc;AAC1D,MAAI,SAAS,kBAAkB,OAAO,QAAQ;AAC9C,MAAI,mBAAmB,kBAAkB,OAAO,kBAAkB;AAClE,MAAI,aAAa,kBAAkB,OAAO,YAAY;AACtD,SAAO;AAAA,IACL,kBAAkB,mBAAmB,KAAK,gBAAgB,SAAS,MAAM,MAAM,QAAQ,YAAY,IAAI;AAAA,IACvG;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,0BAA0B,OAAO,OAAO,QAAQ;AACvD,MAAI,QAAQ,MAAM,OAChB,0BAA0B,MAAM,yBAChC,0BAA0B,MAAM;AAClC,MAAI,eAAe,MAAM,cACvB,mBAAmB,MAAM;AAC3B,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AAGA,MAAI,4BAA4B,UAAa,qBAAqB,yBAAyB;AACzF,WAAO;AAAA,EACT;AACA,MAAI,4BAA4B,QAAW;AACzC,WAAO;AAAA,EACT;AACA,MAAI,cAAc;AAChB,WAAO,MAAM,QAAQ,YAAY;AAAA,EACnC;AACA,MAAI,WAAW,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,SAAS,IAAI,MAAM,SAAS,IAAI;AACzC;AAWA,SAAS,wBAAwB,QAAQ,sBAAsB,aAAa,YAAY;AACtF,MAAI,+BAA2B,qBAAO;AAAA,IACpC,aAAa;AAAA,IACb,aAAa;AAAA,EACf,CAAC;AACD,8BAAU,WAAY;AACpB,SAAK,eAAe,OAAO,SAAS,YAAY,qBAAqB,MAAM;AACzE;AAAA,IACF;AAIA,QAAI,cAAc,SAASX,eAAc;AACvC,+BAAyB,QAAQ,cAAc;AAAA,IACjD;AACA,QAAI,YAAY,SAASC,WAAU,OAAO;AACxC,+BAAyB,QAAQ,cAAc;AAC/C,UAAI,UAAU,CAAC,sBAAsB,MAAM,QAAQ,qBAAqB,IAAI,SAAU,KAAK;AACzF,eAAO,IAAI;AAAA,MACb,CAAC,GAAG,WAAW,GAAG;AAChB,mBAAW;AAAA,MACb;AAAA,IACF;AACA,QAAI,eAAe,SAASC,gBAAe;AACzC,+BAAyB,QAAQ,cAAc;AAAA,IACjD;AACA,QAAI,cAAc,SAASC,eAAc;AACvC,+BAAyB,QAAQ,cAAc;AAAA,IACjD;AACA,QAAI,aAAa,SAASC,YAAW,OAAO;AAC1C,UAAI,UAAU,CAAC,yBAAyB,QAAQ,eAAe,CAAC,sBAAsB,MAAM,QAAQ,qBAAqB,IAAI,SAAU,KAAK;AAC1I,eAAO,IAAI;AAAA,MACb,CAAC,GAAG,aAAa,KAAK,GAAG;AACvB,mBAAW;AAAA,MACb;AAAA,IACF;AACA,gBAAY,iBAAiB,aAAa,WAAW;AACrD,gBAAY,iBAAiB,WAAW,SAAS;AACjD,gBAAY,iBAAiB,cAAc,YAAY;AACvD,gBAAY,iBAAiB,aAAa,WAAW;AACrD,gBAAY,iBAAiB,YAAY,UAAU;AAGnD,WAAO,SAAS,UAAU;AACxB,kBAAY,oBAAoB,aAAa,WAAW;AACxD,kBAAY,oBAAoB,WAAW,SAAS;AACpD,kBAAY,oBAAoB,cAAc,YAAY;AAC1D,kBAAY,oBAAoB,aAAa,WAAW;AACxD,kBAAY,oBAAoB,YAAY,UAAU;AAAA,IACxD;AAAA,EAEF,GAAG,CAAC,QAAQ,WAAW,CAAC;AACxB,SAAO;AACT;AAIA,IAAI,8BAA8B,SAASQ,+BAA8B;AACvE,SAAO;AACT;AAQA,IAAI,MAAuC;AACzC,gCAA8B,SAASA,+BAA8B;AACnE,QAAI,wBAAoB,qBAAO,IAAI;AACnC,aAAS,OAAO,UAAU,QAAQ,WAAW,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC3F,eAAS,IAAI,IAAI,UAAU,IAAI;AAAA,IACjC;AACA,QAAI,2BAAuB,qBAAO,SAAS,OAAO,SAAU,KAAK,SAAS;AACxE,UAAI,OAAO,IAAI,CAAC;AAChB,aAAO;AAAA,IACT,GAAG,CAAC,CAAC,CAAC;AACN,gCAAU,WAAY;AACpB,aAAO,KAAK,qBAAqB,OAAO,EAAE,QAAQ,SAAU,SAAS;AACnE,YAAI,eAAe,qBAAqB,QAAQ,OAAO;AACvD,YAAI,kBAAkB,SAAS;AAC7B,cAAI,CAAC,OAAO,KAAK,YAAY,EAAE,QAAQ;AAErC,oBAAQ,MAAM,uCAAuC,UAAU,+CAA+C;AAC9G;AAAA,UACF;AAAA,QACF;AACA,YAAI,mBAAmB,aAAa,kBAClC,SAAS,aAAa,QACtB,aAAa,aAAa;AAC5B,aAAK,CAAC,cAAc,CAAC,WAAW,YAAY,CAAC,kBAAkB;AAE7D,kBAAQ,MAAM,8BAA+B,SAAS,YAAa,UAAU,6CAA6C;AAAA,QAC5H;AAAA,MACF,CAAC;AACD,wBAAkB,UAAU;AAAA,IAC9B,CAAC;AACD,QAAI,4BAAwB,0BAAY,SAAU,SAAS,kBAAkB,QAAQ,YAAY;AAC/F,2BAAqB,QAAQ,OAAO,IAAI;AAAA,QACtC;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,GAAG,CAAC,CAAC;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,qBAAqB,gBAAgB,iBAAiB,OAAO;AACpE,MAAI,iBAAiB,MAAM,gBACzB,mBAAmB,MAAM,kBACzB,QAAQ,MAAM,OACd,cAAc,MAAM,aACpB,OAAO,8BAA8B,OAAO,WAAW;AAEzD,8BAAU,WAAY;AACpB,QAAI,kBAAkB,OAAO;AAC3B;AAAA,IACF;AACA,qBAAiB,WAAY;AAC3B,aAAO,eAAe,SAAS;AAAA,QAC7B;AAAA,QACA,iBAAiB,MAAM,gBAAgB;AAAA,QACvC,aAAa,MAAM;AAAA,MACrB,GAAG,IAAI,CAAC;AAAA,IACV,GAAG,YAAY,QAAQ;AAAA,EAEzB,GAAG,eAAe;AACpB;AACA,SAAS,kBAAkB,OAAO;AAChC,MAAI,mBAAmB,MAAM,kBAC3B,SAAS,MAAM,QACf,WAAW,MAAM,UACjB,uBAAuB,MAAM,sBAC7B,cAAc,MAAM,aACpB,qBAAqB,MAAM;AAE7B,MAAI,sBAAkB,qBAAO,IAAI;AAEjC,4BAA0B,WAAY;AACpC,QAAI,mBAAmB,KAAK,CAAC,UAAU,CAAC,OAAO,KAAK,SAAS,OAAO,EAAE,QAAQ;AAC5E;AAAA,IACF;AACA,QAAI,gBAAgB,YAAY,OAAO;AACrC,sBAAgB,UAAU;AAAA,IAC5B,OAAO;AACL,yBAAmB,qBAAqB,gBAAgB,GAAG,WAAW;AAAA,IACxE;AAAA,EAEF,GAAG,CAAC,gBAAgB,CAAC;AACrB,SAAO;AACT;AAGA,IAAI,2BAA2B;AAE/B,IAAI,MAAuC;AACzC,6BAA2B,SAASC,0BAAyB,OAAO;AAClE,QAAI,iBAAiB,MAAM,gBACzB,QAAQ,MAAM,OACd,QAAQ,MAAM;AAEhB,QAAI,mBAAe,qBAAO,KAAK;AAC/B,gCAAU,WAAY;AACpB,UAAI,gBAAgB;AAClB;AAAA,MACF;AACA,kCAA4B,OAAO,aAAa,SAAS,KAAK;AAC9D,mBAAa,UAAU;AAAA,IACzB,GAAG,CAAC,OAAO,OAAO,cAAc,CAAC;AAAA,EACnC;AACF;AAUA,SAAS,sBAAsB,OAAO,kBAAkB,YAAY;AAClE,MAAI;AACJ,MAAI,eAAe,QAAQ;AACzB,iBAAa;AAAA,EACf;AACA,MAAI,iBAAiB,eAAe,MAAM,UAAU,OAAO,SAAS,aAAa,WAAW,oBAAoB;AAChH,SAAO,SAAS;AAAA,IACd,QAAQ;AAAA,IACR,kBAAkB;AAAA,EACpB,GAAG,gBAAgB,SAAS;AAAA,IAC1B,cAAc,MAAM,MAAM,gBAAgB;AAAA,IAC1C,QAAQ,kBAAkB,OAAO,QAAQ;AAAA,IACzC,kBAAkB,kBAAkB,OAAO,kBAAkB;AAAA,EAC/D,GAAG,cAAc;AAAA,IACf,YAAY,MAAM,aAAa,MAAM,MAAM,gBAAgB,CAAC;AAAA,EAC9D,CAAC,CAAC;AACJ;AAEA,SAAS,uBAAuB,OAAO,QAAQC,mBAAkB;AAC/D,MAAI,OAAO,OAAO,MAChB,QAAQ,OAAO;AACjB,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAKA,kBAAiB;AACpB,gBAAU;AAAA,QACR,kBAAkB,OAAO,WAAW,KAAK,OAAO;AAAA,MAClD;AACA;AAAA,IACF,KAAKA,kBAAiB;AACpB,gBAAU;AAAA,QACR,kBAAkB;AAAA,MACpB;AACA;AAAA,IACF,KAAKA,kBAAiB;AAAA,IACtB,KAAKA,kBAAiB;AACpB,gBAAU;AAAA,QACR,QAAQ,CAAC,MAAM;AAAA,QACf,kBAAkB,MAAM,SAAS,KAAK,0BAA0B,OAAO,OAAO,CAAC;AAAA,MACjF;AACA;AAAA,IACF,KAAKA,kBAAiB;AACpB,gBAAU;AAAA,QACR,QAAQ;AAAA,QACR,kBAAkB,0BAA0B,OAAO,OAAO,CAAC;AAAA,MAC7D;AACA;AAAA,IACF,KAAKA,kBAAiB;AACpB,gBAAU;AAAA,QACR,QAAQ;AAAA,MACV;AACA;AAAA,IACF,KAAKA,kBAAiB;AACpB,gBAAU;AAAA,QACR,kBAAkB,OAAO;AAAA,MAC3B;AACA;AAAA,IACF,KAAKA,kBAAiB;AACpB,gBAAU;AAAA,QACR,YAAY,OAAO;AAAA,MACrB;AACA;AAAA,IACF,KAAKA,kBAAiB;AACpB,gBAAU;AAAA,QACR,kBAAkB,kBAAkB,OAAO,kBAAkB;AAAA,QAC7D,QAAQ,kBAAkB,OAAO,QAAQ;AAAA,QACzC,cAAc,kBAAkB,OAAO,cAAc;AAAA,QACrD,YAAY,kBAAkB,OAAO,YAAY;AAAA,MACnD;AACA;AAAA,IACF;AACE,YAAM,IAAI,MAAM,4CAA4C;AAAA,EAChE;AACA,SAAO,SAAS,CAAC,GAAG,OAAO,OAAO;AACpC;AAGA,SAAS,2BAA2B,IAAI;AACpC,MAAI,YAAY,GAAG,WAAW,mBAAmB,GAAG,kBAAkB,QAAQ,GAAG,OAAOf,gBAAe,GAAG,cAAc,uBAAuB,GAAG;AAClJ,MAAI,sBAAsB,UAAU,YAAY;AAChD,WAAS,QAAQ,GAAG,QAAQ,MAAM,QAAQ,SAAS;AAE/C,QAAI,eAAe,QAAQ,oBAAoB,UAAU,SAAS,IAAI,IAAI,MAAM,MAAM;AACtF,QAAI,OAAO,MAAM,WAAW;AAC5B,QAAI,SAAS,UACTA,cAAa,IAAI,EAAE,YAAY,EAAE,WAAW,mBAAmB,GAAG;AAClE,UAAI,UAAU,qBAAqB,WAAW;AAC9C,UAAI,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa,UAAU,IAAI;AACvF,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAI,cAAc;AAAA,EACd,OAAO,kBAAAQ,QAAU,MAAM;AAAA,EACvB,cAAc,kBAAAA,QAAU;AAAA,EACxB,sBAAsB,kBAAAA,QAAU;AAAA,EAChC,yBAAyB,kBAAAA,QAAU;AAAA,EACnC,kBAAkB,kBAAAA,QAAU;AAAA,EAC5B,yBAAyB,kBAAAA,QAAU;AAAA,EACnC,yBAAyB,kBAAAA,QAAU;AAAA,EACnC,QAAQ,kBAAAA,QAAU;AAAA,EAClB,eAAe,kBAAAA,QAAU;AAAA,EACzB,eAAe,kBAAAA,QAAU;AAAA,EACzB,cAAc,kBAAAA,QAAU;AAAA,EACxB,qBAAqB,kBAAAA,QAAU;AAAA,EAC/B,qBAAqB,kBAAAA,QAAU;AAAA,EAC/B,IAAI,kBAAAA,QAAU;AAAA,EACd,SAAS,kBAAAA,QAAU;AAAA,EACnB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,WAAW,kBAAAA,QAAU;AAAA,EACrB,gBAAgB,kBAAAA,QAAU;AAAA,EAC1B,cAAc,kBAAAA,QAAU;AAAA,EACxB,sBAAsB,kBAAAA,QAAU;AAAA,EAChC,0BAA0B,kBAAAA,QAAU;AAAA,EACpC,eAAe,kBAAAA,QAAU;AAAA,EACzB,gBAAgB,kBAAAA,QAAU;AAAA,EAC1B,aAAa,kBAAAA,QAAU,MAAM;AAAA,IACzB,kBAAkB,kBAAAA,QAAU;AAAA,IAC5B,qBAAqB,kBAAAA,QAAU;AAAA,IAC/B,UAAU,kBAAAA,QAAU,MAAM;AAAA,MACtB,gBAAgB,kBAAAA,QAAU;AAAA,MAC1B,eAAe,kBAAAA,QAAU;AAAA,MACzB,MAAM,kBAAAA,QAAU;AAAA,IACpB,CAAC;AAAA,EACL,CAAC;AACL;AASA,SAAS,qBAAqB,IAAI;AAC9B,MAAI,SAAS,GAAG,QAAQ,cAAc,GAAG,aAAa,sBAAsB,GAAG;AAC/E,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACX;AACA,MAAI,CAAC,aAAa;AACd,WAAO;AAAA,EACX;AACA,MAAI,gBAAgB,qBAAqB;AACrC,WAAO,GAAG,OAAO,aAAa,SAAS,EAAE,OAAO,gBAAgB,IAAI,QAAQ,SAAS,8FAA8F;AAAA,EACvL;AACA,SAAO;AACX;AACA,IAAI,iBAAiB,SAAS,SAAS,CAAC,GAAG,cAAc,GAAG,EAAE,qBAA2C,CAAC;AAE1G,IAAI,sBAAsB;AAE1B,IAAI,MAAuC;AACvC,wBAAsB,SAAU,SAAS,QAAQ;AAC7C,sBAAAA,QAAU,eAAe,aAAa,SAAS,QAAQ,OAAO,IAAI;AAAA,EACtE;AACJ;AAEA,IAAI,sBAAsB,OAAwC,2BAA2B;AAC7F,IAAI,+BAA+B,OAAwC,wCAAwC;AACnH,IAAI,6BAA6B,OAAwC,sCAAsC;AAC/G,IAAI,+BAA+B,OAAwC,uCAAuC;AAClH,IAAI,4BAA4B,OAAwC,oCAAoC;AAC5G,IAAI,0BAA0B,OAAwC,kCAAkC;AACxG,IAAI,yBAAyB,OAAwC,iCAAiC;AACtG,IAAI,2BAA2B,OAAwC,mCAAmC;AAC1G,IAAI,iCAAiC,OAAwC,0CAA0C;AACvH,IAAI,4BAA4B,OAAwC,qCAAqC;AAC7G,IAAI,8BAA8B,OAAwC,uCAAuC;AACjH,IAAI,mBAAmB,OAAwC,0BAA0B;AACzF,IAAI,mBAAmB,OAAwC,yBAAyB;AACxF,IAAI,kBAAkB,OAAwC,wBAAwB;AACtF,IAAI,cAAc,OAAwC,mBAAmB;AAC7E,IAAI,uBAAuB,OAAwC,6BAA6B;AAChG,IAAI,qBAAqB,OAAwC,2BAA2B;AAC5F,IAAI,sBAAsB,OAAwC,4BAA4B;AAC9F,IAAI,gCAAgC,OAAwC,uCAAuC;AACnH,IAAI,uBAAuB,OAAwC,6BAA6B;AAChG,IAAI,0BAA0B,OAAwC,iCAAiC;AACvG,IAAI,kBAAkB,OAAwC,uBAAuB;AAErF,IAAI,qBAAkC,OAAO,OAAO;AAAA,EAClD,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,WAAW;AAAA,EACX,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,6BAA6B;AAAA,EAC7B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,eAAe;AACjB,CAAC;AAGD,SAAS,uBAAuB,OAAO,QAAQ;AAC7C,MAAI;AACJ,MAAI,OAAO,OAAO,MAChB,QAAQ,OAAO,OACf,SAAS,OAAO;AAClB,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,gBAAU;AAAA,QACR,QAAQ,kBAAkB,OAAO,QAAQ;AAAA,QACzC,kBAAkB,kBAAkB,OAAO,kBAAkB;AAAA,QAC7D,cAAc,MAAM,MAAM,OAAO,KAAK;AAAA,MACxC;AACA;AAAA,IACF,KAAK;AACH;AACE,YAAI,gBAAgB,OAAO;AAC3B,YAAI,aAAa,KAAK,MAAM,aAAa;AACzC,YAAI,uBAAuB,CAAC,MAAM,UAAU,MAAM,eAAe,MAAM,MAAM,QAAQ,MAAM,YAAY,IAAI,MAAM;AACjH,YAAI,mBAAmB,2BAA2B;AAAA,UAChD,WAAW;AAAA,UACX,kBAAkB;AAAA,UAClB,OAAO,MAAM;AAAA,UACb,cAAc,MAAM;AAAA,UACpB,sBAAsB,OAAO;AAAA,QAC/B,CAAC;AACD,kBAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,MACF;AACA;AAAA,IACF,KAAK;AACH;AACE,YAAI,oBAAoB,MAAM,SAAS,qBAAqB,GAAG,MAAM,kBAAkB,MAAM,MAAM,QAAQ,OAAO,sBAAsB,KAAK,IAAI,UAAU,MAAM,gBAAgB,OAAO,KAAK,0BAA0B,OAAO,OAAO,CAAC;AACtO,kBAAU;AAAA,UACR,kBAAkB;AAAA,UAClB,QAAQ;AAAA,QACV;AAAA,MACF;AACA;AAAA,IACF,KAAK;AACH,UAAI,MAAM,UAAU,QAAQ;AAC1B,kBAAU,sBAAsB,OAAO,MAAM,kBAAkB,KAAK;AAAA,MACtE,OAAO;AACL,YAAI,qBAAqB,MAAM,SAAS,qBAAqB,IAAI,MAAM,kBAAkB,MAAM,MAAM,QAAQ,OAAO,sBAAsB,KAAK,IAAI,0BAA0B,OAAO,OAAO,EAAE;AAC7L,kBAAU;AAAA,UACR,kBAAkB;AAAA,UAClB,QAAQ;AAAA,QACV;AAAA,MACF;AACA;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AACH,gBAAU,sBAAsB,OAAO,MAAM,kBAAkB,KAAK;AACpE;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,kBAAkB,wBAAwB,GAAG,GAAG,MAAM,MAAM,QAAQ,OAAO,sBAAsB,KAAK;AAAA,QACtG,QAAQ;AAAA,MACV;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,kBAAkB,wBAAwB,IAAI,MAAM,MAAM,SAAS,GAAG,MAAM,MAAM,QAAQ,OAAO,sBAAsB,KAAK;AAAA,QAC5H,QAAQ;AAAA,MACV;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,kBAAkB,qBAAqB,KAAK,MAAM,kBAAkB,MAAM,MAAM,QAAQ,OAAO,sBAAsB,KAAK;AAAA,MAC5H;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,kBAAkB,qBAAqB,IAAI,MAAM,kBAAkB,MAAM,MAAM,QAAQ,OAAO,sBAAsB,KAAK;AAAA,MAC3H;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,QAAQ;AAAA,QACR,kBAAkB;AAAA,MACpB;AACA;AAAA,IACF,KAAK;AACH,gBAAU,SAAS;AAAA,QACjB,QAAQ;AAAA,QACR,kBAAkB;AAAA,MACpB,GAAG,MAAM,oBAAoB,OAAO,eAAe,MAAM,UAAU,OAAO,SAAS,aAAa,WAAW;AAAA,QACzG,cAAc,MAAM,MAAM,MAAM,gBAAgB;AAAA,MAClD,CAAC;AACD;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,cAAc,OAAO;AAAA,MACvB;AACA;AAAA,IACF;AACE,aAAO,uBAAuB,OAAO,QAAQ,kBAAkB;AAAA,EACnE;AACA,SAAO,SAAS,CAAC,GAAG,OAAO,OAAO;AACpC;AAGA,IAAI,cAAc,CAAC,gBAAgB,UAAU,aAAa,UAAU,KAAK;AAAzE,IACE,eAAe,CAAC,UAAU,WAAW,WAAW,aAAa,UAAU,KAAK;AAD9E,IAEE,eAAe,CAAC,QAAQ,SAAS,eAAe,WAAW,WAAW,UAAU,OAAO,UAAU;AACnG,UAAU,mBAAmB;AAC7B,SAAS,UAAU,WAAW;AAC5B,MAAI,cAAc,QAAQ;AACxB,gBAAY,CAAC;AAAA,EACf;AACA,sBAAoB,WAAW,SAAS;AAExC,MAAI,QAAQ,SAAS,CAAC,GAAG,gBAAgB,SAAS;AAClD,MAAI,QAAQ,MAAM,OAChBQ,kBAAiB,MAAM,gBACvB,cAAc,MAAM,aACpBhB,gBAAe,MAAM,cACrBiB,2BAA0B,MAAM,yBAChCC,wBAAuB,MAAM;AAE/B,MAAI,eAAe,kBAAkB,KAAK;AAC1C,MAAI,wBAAwB,uBAAuB,wBAAwB,cAAc,KAAK,GAC5F,QAAQ,sBAAsB,CAAC,GAC/B,WAAW,sBAAsB,CAAC;AACpC,MAAI,SAAS,MAAM,QACjB,mBAAmB,MAAM,kBACzB,eAAe,MAAM,cACrB,aAAa,MAAM;AAGrB,MAAI,sBAAkB,qBAAO,IAAI;AACjC,MAAI,cAAU,qBAAO,IAAI;AACzB,MAAI,eAAW,qBAAO,CAAC,CAAC;AAExB,MAAI,sBAAkB,qBAAO,IAAI;AAEjC,MAAI,aAAa,cAAc,KAAK;AAEpC,MAAI,6BAAyB,qBAAO;AACpC,MAAI,wBAAoB,qBAAO,IAAI;AAEnC,MAAI,SAAS,aAAa;AAAA,IACxB;AAAA,IACA;AAAA,EACF,CAAC;AAGD,MAAI,2BAAuB,0BAAY,SAAU,OAAO;AACtD,WAAO,SAAS,QAAQ,WAAW,UAAU,KAAK,CAAC;AAAA,EACrD,GAAG,CAAC,UAAU,CAAC;AAIf,uBAAqBA,uBAAsB,CAAC,QAAQ,kBAAkB,YAAY,KAAK,GAAG,SAAS;AAAA,IACjG,gBAAgB,kBAAkB;AAAA,IAClC,qBAAqB,uBAAuB;AAAA,IAC5C;AAAA,IACA;AAAA,IACA,cAAclB;AAAA,EAChB,GAAG,KAAK,CAAC;AAET,uBAAqBiB,0BAAyB,CAAC,YAAY,GAAG,SAAS;AAAA,IACrE,gBAAgB,kBAAkB;AAAA,IAClC,qBAAqB,uBAAuB;AAAA,IAC5C;AAAA,IACA;AAAA,IACA,cAAcjB;AAAA,EAChB,GAAG,KAAK,CAAC;AAET,MAAI,kBAAkB,kBAAkB;AAAA,IACtC,aAAa,QAAQ;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgBgB;AAAA,IAChB;AAAA,EACF,CAAC;AAGD,8BAAU,WAAY;AAEpB,oBAAgB,UAAU,SAAS,SAAU,eAAe;AAC1D,oBAAc;AAAA,QACZ,MAAM;AAAA,QACN,YAAY;AAAA,MACd,CAAC;AAAA,IACH,GAAG,GAAG;AAGN,WAAO,WAAY;AACjB,sBAAgB,QAAQ,OAAO;AAAA,IACjC;AAAA,EACF,GAAG,CAAC,CAAC;AAGL,8BAAU,WAAY;AACpB,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,oBAAgB,QAAQ,QAAQ;AAAA,EAClC,GAAG,CAAC,UAAU,UAAU,CAAC;AACzB,2BAAyB;AAAA,IACvB,gBAAgB,kBAAkB;AAAA,IAClC;AAAA,IACA;AAAA,EACF,CAAC;AACD,8BAAU,WAAY;AACpB,QAAI,kBAAkB,SAAS;AAC7B;AAAA,IACF;AACA,2BAAuB,UAAU,MAAM;AAAA,EACzC,CAAC;AAED,MAAI,2BAA2B,wBAAwB,QAAQ,CAAC,SAAS,eAAe,GAAG,aAAa,WAAY;AAClH,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACD,MAAI,wBAAwB,4BAA4B,gBAAgB,sBAAsB;AAE9F,8BAAU,WAAY;AACpB,sBAAkB,UAAU;AAC5B,WAAO,WAAY;AACjB,wBAAkB,UAAU;AAAA,IAC9B;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,8BAAU,WAAY;AACpB,QAAI,CAAC,QAAQ;AACX,eAAS,UAAU,CAAC;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,MAAM,CAAC;AAGX,MAAI,kCAA8B,sBAAQ,WAAY;AACpD,WAAO;AAAA,MACL,WAAW,SAAS,UAAU,OAAO;AACnC,cAAM,eAAe;AACrB,iBAAS;AAAA,UACP,MAAM;AAAA,UACN;AAAA,UACA,QAAQ,MAAM;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA,SAAS,SAAS,QAAQ,OAAO;AAC/B,cAAM,eAAe;AACrB,iBAAS;AAAA,UACP,MAAM;AAAA,UACN;AAAA,UACA,QAAQ,MAAM;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA,MAAM,SAAS,KAAK,OAAO;AACzB,cAAM,eAAe;AACrB,iBAAS;AAAA,UACP,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,KAAK,SAAS,IAAI,OAAO;AACvB,cAAM,eAAe;AACrB,iBAAS;AAAA,UACP,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,QAAQ,SAAS,SAAS;AACxB,YAAI,OAAO,QAAQ,MAAM,QAAQ;AAC/B,mBAAS;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,OAAO,SAAS,MAAM,OAAO;AAC3B,cAAM,eAAe;AACrB,iBAAS;AAAA,UACP,MAAM,OAAO,QAAQ,MAAM,SAAS,2BAA2B;AAAA,QACjE,CAAC;AAAA,MACH;AAAA,MACA,QAAQ,SAAS,OAAO,OAAO;AAC7B,YAAI,OAAO,QAAQ,MAAM,QAAQ;AAC/B,gBAAM,eAAe;AACrB,mBAAS;AAAA,YACP,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,UAAU,SAAS,SAAS,OAAO;AACjC,YAAI,OAAO,QAAQ,MAAM,QAAQ;AAC/B,gBAAM,eAAe;AACrB,mBAAS;AAAA,YACP,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,KAAK,SAAS,EAAE,OAAO;AACrB,cAAM,eAAe;AACrB,YAAI,eAAe,OAAO,QAAQ;AAClC,YAAI,CAAC,aAAa,QAAQ;AACxB,mBAAS;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD;AAAA,QACF;AACA,YAAI,aAAa,YAAY;AAC3B,mBAAS;AAAA,YACP,MAAM;AAAA,YACN,KAAK;AAAA,YACL;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,mBAAS;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,sBAAsB,MAAM,CAAC;AAG3C,MAAI,iBAAa,0BAAY,WAAY;AACvC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,gBAAY,0BAAY,WAAY;AACtC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,eAAW,0BAAY,WAAY;AACrC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,0BAAsB,0BAAY,SAAU,qBAAqB;AACnE,aAAS;AAAA,MACP,MAAM;AAAA,MACN,kBAAkB;AAAA,IACpB,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,iBAAa,0BAAY,SAAU,iBAAiB;AACtD,aAAS;AAAA,MACP,MAAM;AAAA,MACN,cAAc;AAAA,IAChB,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,YAAQ,0BAAY,WAAY;AAClC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,oBAAgB,0BAAY,SAAU,eAAe;AACvD,aAAS;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AAEb,MAAI,oBAAgB,0BAAY,SAAU,YAAY;AACpD,WAAO,SAAS;AAAA,MACd,IAAI,WAAW;AAAA,MACf,SAAS,WAAW;AAAA,IACtB,GAAG,UAAU;AAAA,EACf,GAAG,CAAC,UAAU,CAAC;AACf,MAAI,mBAAe,0BAAY,SAAU,OAAO,QAAQ;AACtD,QAAIjB;AACJ,QAAI,OAAO,UAAU,SAAS,CAAC,IAAI,OACjC,eAAe,KAAK,cACpB,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,QAAQ;AAC1C,SAAK;AACL,SAAK;AACL,QAAI,MAAM,KAAK,KACf,OAAO,8BAA8B,MAAM,WAAW;AACxD,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,QAAQ;AAChE,QAAI,uBAAuB,SAASoB,wBAAuB;AACzD,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,0BAAsB,gBAAgB,kBAAkB,QAAQ,OAAO;AACvE,WAAO,UAAUpB,aAAY,CAAC,GAAGA,WAAU,MAAM,IAAI,WAAW,KAAK,SAAU,UAAU;AACvF,cAAQ,UAAU;AAAA,IACpB,CAAC,GAAGA,WAAU,KAAK,WAAW,QAAQA,WAAU,OAAO,WAAWA,WAAU,iBAAiB,IAAI,QAAQ,KAAK,YAAY,IAAI,SAAY,KAAK,WAAW,SAASA,WAAU,eAAe,qBAAqB,cAAc,oBAAoB,GAAGA,aAAY,IAAI;AAAA,EACxQ,GAAG,CAAC,UAAU,uBAAuB,UAAU,CAAC;AAChD,MAAI,2BAAuB,0BAAY,SAAU,QAAQ,QAAQ;AAC/D,QAAI;AACJ,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,SAAS,MAAM,QACf,UAAU,MAAM;AAChB,UAAM;AACN,QAAI,YAAY,MAAM,WACtB,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,QAAQ,cAC3C,MAAM,MAAM,KACZ,OAAO,8BAA8B,OAAO,YAAY;AAC1D,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,QAAQ;AAChE,QAAI,cAAc,OAAO,QAAQ;AACjC,QAAI,0BAA0B,SAASqB,2BAA0B;AAC/D,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,QAAI,yBAAyB,SAASC,0BAAyB;AAC7D,UAAI,YAAY,UAAU,CAAC,yBAAyB,QAAQ,aAAa;AACvE,iBAAS;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,4BAA4B,SAASC,2BAA0B,OAAO;AACxE,UAAI,MAAM,kBAAkB,KAAK;AACjC,UAAI,OAAO,4BAA4B,GAAG,GAAG;AAC3C,oCAA4B,GAAG,EAAE,KAAK;AAAA,MACxC,WAAW,uBAAuB,GAAG,GAAG;AACtC,iBAAS;AAAA,UACP,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,cAAc,UAAU,YAAY,CAAC,GAAG,UAAU,MAAM,IAAI,WAAW,KAAK,SAAU,kBAAkB;AAC1G,sBAAgB,UAAU;AAAA,IAC5B,CAAC,GAAG,UAAU,uBAAuB,IAAI,YAAY,UAAU,YAAY,mBAAmB,KAAK,WAAW,UAAU,YAAY,gBAAgB,IAAI,IAAI,UAAU,eAAe,IAAI,WAAW,QAAQ,UAAU,eAAe,IAAI,OAAO,QAAQ,MAAM,QAAQ,UAAU,eAAe,IAAI,WAAW,UAAU,iBAAiB,IAAI,QAAQ,KAAK,YAAY,IAAI,SAAY,KAAK,WAAW,SAAS,UAAU,KAAK,WAAW,gBAAgB,UAAU,OAAO,YAAY,UAAU,WAAW,GAAG,UAAU,SAAS,qBAAqB,QAAQ,sBAAsB,GAAG,YAAY,IAAI;AACvkB,QAAI,CAAC,KAAK,UAAU;AAElB;AACE,oBAAY,UAAU,qBAAqB,SAAS,uBAAuB;AAC3E,oBAAY,YAAY,qBAAqB,WAAW,yBAAyB;AAAA,MACnF;AAAA,IACF;AACA,0BAAsB,wBAAwB,kBAAkB,QAAQ,eAAe;AACvF,WAAO;AAAA,EACT,GAAG,CAAC,QAAQ,YAAY,uBAAuB,UAAU,0BAA0B,6BAA6B,oBAAoB,CAAC;AACrI,MAAI,mBAAe,0BAAY,SAAU,QAAQ;AAC/C,QAAI;AACJ,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,WAAW,MAAM,MACjB,YAAY,MAAM,OAClB,cAAc,MAAM,aACpB,UAAU,MAAM;AAChB,UAAM;AACN,QAAI,eAAe,MAAM,QACzB,SAAS,iBAAiB,SAAS,QAAQ,cAC3C,MAAM,MAAM,KACZ,WAAW,MAAM,UACjB,OAAO,8BAA8B,OAAO,YAAY;AAC1D,QAAI,kBAAkB,OAAO,SAC3B,cAAc,gBAAgB,OAC9B,cAAc,gBAAgB;AAChC,QAAI,mBAAmB,gBAAgB,UAAU,WAAW,YAAY,OAAO,4CAA4C,GACzH,OAAO,iBAAiB,CAAC,GACzB,QAAQ,iBAAiB,CAAC;AAC5B,QAAI,sBAAsB,SAASC,uBAAsB;AACvD,UAAI,UAAU,YAAY,kBAAkB;AAC1C;AAAA,MACF;AACA,sBAAgB,UAAU;AAC1B,eAAS;AAAA,QACP,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,kBAAkB,SAASC,mBAAkB;AAC/C,eAAS;AAAA,QACP,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,YAAY,UAAU,YAAY;AAAA,MACpC;AAAA,MACA,MAAM;AAAA,MACN,iBAAiB,MAAM,SAAS;AAAA,MAChC,IAAI,WAAW,UAAU,KAAK;AAAA,IAChC,GAAG,UAAU,MAAM,IAAI,WAAW,KAAK,SAAU,UAAU;AACzD,UAAI,UAAU;AACZ,iBAAS,QAAQ,WAAW,UAAU,KAAK,CAAC,IAAI;AAAA,MAClD;AAAA,IACF,CAAC,GAAG,YAAY,IAAI;AACpB,QAAI,CAAC,UAAU;AAEb;AACE,kBAAU,UAAU,qBAAqB,SAAS,eAAe;AAAA,MACnE;AAAA,IACF;AACA,cAAU,cAAc,qBAAqB,aAAa,mBAAmB;AAC7E,WAAO;AAAA,EACT,GAAG,CAAC,QAAQ,cAAc,YAAY,iBAAiB,QAAQ,CAAC;AAChE,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,wBAAwB,OAAwC,iCAAiC;AACrG,IAAI,sBAAsB,OAAwC,+BAA+B;AACjG,IAAI,qBAAqB,OAAwC,6BAA6B;AAC9F,IAAI,mBAAmB,OAAwC,2BAA2B;AAC1F,IAAI,kBAAkB,OAAwC,0BAA0B;AACxF,IAAI,qBAAqB,OAAwC,8BAA8B;AAC/F,IAAI,uBAAuB,OAAwC,gCAAgC;AACnG,IAAI,oBAAoB,OAAwC,4BAA4B;AAC5F,IAAI,cAAc,OAAwC,qBAAqB;AAC/E,IAAI,YAAY,OAAwC,mBAAmB;AAC3E,IAAI,aAAa,OAAwC,oBAAoB;AAC7E,IAAI,iBAAiB,OAAwC,yBAAyB;AACtF,IAAI,gBAAgB,OAAwC,wBAAwB;AACpF,IAAI,YAAY,OAAwC,mBAAmB;AAC3E,IAAI,oBAAoB,OAAwC,2BAA2B;AAC3F,IAAI,qBAAqB,OAAwC,6BAA6B;AAC9F,IAAI,mBAAmB,OAAwC,2BAA2B;AAC1F,IAAI,oBAAoB,OAAwC,4BAA4B;AAC5F,IAAI,8BAA8B,OAAwC,uCAAuC;AACjH,IAAI,qBAAqB,OAAwC,6BAA6B;AAC9F,IAAI,wBAAwB,OAAwC,iCAAiC;AACrG,IAAI,kBAAkB,OAAwC,uBAAuB;AACrF,IAAI,oCAAoC,OAAwC,8CAA8C;AAE9H,IAAI,qBAAkC,OAAO,OAAO;AAAA,EAClD,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf;AACF,CAAC;AAED,SAAS,kBAAkB,OAAO;AAChC,MAAI,eAAe,kBAAkB,KAAK;AAC1C,MAAI,eAAe,aAAa;AAChC,MAAI,aAAa,aAAa;AAC9B,MAAI,eAAe,MAAM,gBAAgB,MAAM,sBAAsB,UAAa,MAAM,sBAAsB,UAAa,MAAM,eAAe,QAAW;AACzJ,iBAAa,MAAM,aAAa,YAAY;AAAA,EAC9C;AACA,SAAO,SAAS,CAAC,GAAG,cAAc;AAAA,IAChC;AAAA,EACF,CAAC;AACH;AACA,IAAI,cAAc;AAAA,EAChB,OAAO,kBAAAhB,QAAU,MAAM;AAAA,EACvB,cAAc,kBAAAA,QAAU;AAAA,EACxB,qBAAqB,kBAAAA,QAAU;AAAA,EAC/B,sBAAsB,kBAAAA,QAAU;AAAA,EAChC,yBAAyB,kBAAAA,QAAU;AAAA,EACnC,kBAAkB,kBAAAA,QAAU;AAAA,EAC5B,yBAAyB,kBAAAA,QAAU;AAAA,EACnC,yBAAyB,kBAAAA,QAAU;AAAA,EACnC,QAAQ,kBAAAA,QAAU;AAAA,EAClB,eAAe,kBAAAA,QAAU;AAAA,EACzB,eAAe,kBAAAA,QAAU;AAAA,EACzB,cAAc,kBAAAA,QAAU;AAAA,EACxB,qBAAqB,kBAAAA,QAAU;AAAA,EAC/B,qBAAqB,kBAAAA,QAAU;AAAA,EAC/B,YAAY,kBAAAA,QAAU;AAAA,EACtB,mBAAmB,kBAAAA,QAAU;AAAA,EAC7B,mBAAmB,kBAAAA,QAAU;AAAA,EAC7B,IAAI,kBAAAA,QAAU;AAAA,EACd,SAAS,kBAAAA,QAAU;AAAA,EACnB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,WAAW,kBAAAA,QAAU;AAAA,EACrB,SAAS,kBAAAA,QAAU;AAAA,EACnB,gBAAgB,kBAAAA,QAAU;AAAA,EAC1B,cAAc,kBAAAA,QAAU;AAAA,EACxB,sBAAsB,kBAAAA,QAAU;AAAA,EAChC,0BAA0B,kBAAAA,QAAU;AAAA,EACpC,eAAe,kBAAAA,QAAU;AAAA,EACzB,gBAAgB,kBAAAA,QAAU;AAAA,EAC1B,oBAAoB,kBAAAA,QAAU;AAAA,EAC9B,aAAa,kBAAAA,QAAU,MAAM;AAAA,IAC3B,kBAAkB,kBAAAA,QAAU;AAAA,IAC5B,qBAAqB,kBAAAA,QAAU;AAAA,IAC/B,UAAU,kBAAAA,QAAU,MAAM;AAAA,MACxB,gBAAgB,kBAAAA,QAAU;AAAA,MAC1B,eAAe,kBAAAA,QAAU;AAAA,MACzB,MAAM,kBAAAA,QAAU;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AACH;AAcA,SAAS,qBAAqB,SAAS,cAAc,OAAO;AAC1D,MAAI,8BAA0B,qBAAO;AACrC,MAAI,sBAAsB,mBAAmB,SAAS,cAAc,KAAK,GACvE,QAAQ,oBAAoB,CAAC,GAC7B,WAAW,oBAAoB,CAAC;AAGlC,8BAAU,WAAY;AACpB,QAAI,CAAC,iBAAiB,OAAO,cAAc,GAAG;AAC5C;AAAA,IACF;AACA,QAAI,MAAM,oBAAoB,wBAAwB,SAAS,MAAM,YAAY,GAAG;AAClF,eAAS;AAAA,QACP,MAAM;AAAA,QACN,YAAY,MAAM,aAAa,MAAM,YAAY;AAAA,MACnD,CAAC;AAAA,IACH;AACA,4BAAwB,UAAU,MAAM,iBAAiB,wBAAwB,UAAU,MAAM,eAAe,MAAM;AAAA,EAExH,GAAG,CAAC,MAAM,cAAc,MAAM,YAAY,CAAC;AAC3C,SAAO,CAAC,SAAS,OAAO,KAAK,GAAG,QAAQ;AAC1C;AAGA,IAAI,sBAAsB;AAE1B,IAAI,MAAuC;AACzC,wBAAsB,SAASiB,mBAAkB,SAAS,QAAQ;AAChE,sBAAAjB,QAAU,eAAe,aAAa,SAAS,QAAQ,OAAO,IAAI;AAAA,EACpE;AACF;AACA,IAAI,iBAAiB,SAAS,CAAC,GAAG,gBAAgB;AAAA,EAChD,qBAAqB,SAAS,oBAAoB,UAAU,MAAM;AAChE,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,sBAAsB;AACxB,CAAC;AAGD,SAAS,4BAA4B,OAAO,QAAQ;AAClD,MAAI;AACJ,MAAI,OAAO,OAAO,MAChB,QAAQ,OAAO,OACf,SAAS,OAAO;AAClB,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,gBAAU;AAAA,QACR,QAAQ,kBAAkB,OAAO,QAAQ;AAAA,QACzC,kBAAkB,kBAAkB,OAAO,kBAAkB;AAAA,QAC7D,cAAc,MAAM,MAAM,OAAO,KAAK;AAAA,QACtC,YAAY,MAAM,aAAa,MAAM,MAAM,OAAO,KAAK,CAAC;AAAA,MAC1D;AACA;AAAA,IACF,KAAK;AACH,UAAI,MAAM,QAAQ;AAChB,kBAAU;AAAA,UACR,kBAAkB,qBAAqB,GAAG,MAAM,kBAAkB,MAAM,MAAM,QAAQ,OAAO,sBAAsB,IAAI;AAAA,QACzH;AAAA,MACF,OAAO;AACL,kBAAU;AAAA,UACR,kBAAkB,UAAU,MAAM,gBAAgB,OAAO,KAAK,0BAA0B,OAAO,OAAO,GAAG,OAAO,oBAAoB;AAAA,UACpI,QAAQ,MAAM,MAAM,UAAU;AAAA,QAChC;AAAA,MACF;AACA;AAAA,IACF,KAAK;AACH,UAAI,MAAM,QAAQ;AAChB,YAAI,QAAQ;AACV,oBAAU,sBAAsB,OAAO,MAAM,gBAAgB;AAAA,QAC/D,OAAO;AACL,oBAAU;AAAA,YACR,kBAAkB,qBAAqB,IAAI,MAAM,kBAAkB,MAAM,MAAM,QAAQ,OAAO,sBAAsB,IAAI;AAAA,UAC1H;AAAA,QACF;AAAA,MACF,OAAO;AACL,kBAAU;AAAA,UACR,kBAAkB,0BAA0B,OAAO,OAAO,IAAI,OAAO,oBAAoB;AAAA,UACzF,QAAQ,MAAM,MAAM,UAAU;AAAA,QAChC;AAAA,MACF;AACA;AAAA,IACF,KAAK;AACH,gBAAU,sBAAsB,OAAO,MAAM,gBAAgB;AAC7D;AAAA,IACF,KAAK;AACH,gBAAU,SAAS;AAAA,QACjB,QAAQ;AAAA,QACR,kBAAkB;AAAA,MACpB,GAAG,CAAC,MAAM,UAAU;AAAA,QAClB,cAAc;AAAA,QACd,YAAY;AAAA,MACd,CAAC;AACD;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,kBAAkB,qBAAqB,KAAK,MAAM,kBAAkB,MAAM,MAAM,QAAQ,OAAO,sBAAsB,KAAK;AAAA,MAC5H;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,kBAAkB,qBAAqB,IAAI,MAAM,kBAAkB,MAAM,MAAM,QAAQ,OAAO,sBAAsB,KAAK;AAAA,MAC3H;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,kBAAkB,wBAAwB,GAAG,GAAG,MAAM,MAAM,QAAQ,OAAO,sBAAsB,KAAK;AAAA,MACxG;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,kBAAkB,wBAAwB,IAAI,MAAM,MAAM,SAAS,GAAG,MAAM,MAAM,QAAQ,OAAO,sBAAsB,KAAK;AAAA,MAC9H;AACA;AAAA,IACF,KAAK;AACH,gBAAU,SAAS;AAAA,QACjB,QAAQ;AAAA,QACR,kBAAkB;AAAA,MACpB,GAAG,MAAM,oBAAoB,OAAO,eAAe,MAAM,UAAU,OAAO,SAAS,aAAa,WAAW,OAAO,cAAc;AAAA,QAC9H,cAAc,MAAM,MAAM,MAAM,gBAAgB;AAAA,QAChD,YAAY,MAAM,aAAa,MAAM,MAAM,MAAM,gBAAgB,CAAC;AAAA,MACpE,CAAC;AACD;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,QAAQ;AAAA,QACR,kBAAkB,kBAAkB,OAAO,kBAAkB;AAAA,QAC7D,YAAY,OAAO;AAAA,MACrB;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,QAAQ;AAAA,QACR,kBAAkB,0BAA0B,OAAO,OAAO,CAAC;AAAA,MAC7D;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,cAAc,OAAO;AAAA,QACrB,YAAY,MAAM,aAAa,OAAO,YAAY;AAAA,MACpD;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,YAAY,OAAO;AAAA,MACrB;AACA;AAAA,IACF;AACE,aAAO,uBAAuB,OAAO,QAAQ,kBAAkB;AAAA,EACnE;AACA,SAAO,SAAS,CAAC,GAAG,OAAO,OAAO;AACpC;AAGA,IAAI,cAAc,CAAC,gBAAgB,UAAU,KAAK;AAAlD,IACE,eAAe,CAAC,QAAQ,SAAS,UAAU,OAAO,eAAe,eAAe,WAAW,WAAW,UAAU;AADlH,IAEE,aAAa,CAAC,WAAW,WAAW,UAAU,KAAK;AAFrD,IAGE,aAAa,CAAC,aAAa,YAAY,WAAW,WAAW,UAAU,gBAAgB,UAAU,KAAK;AACxG,YAAY,mBAAmB;AAC/B,SAAS,YAAY,WAAW;AAC9B,MAAI,cAAc,QAAQ;AACxB,gBAAY,CAAC;AAAA,EACf;AACA,sBAAoB,WAAW,WAAW;AAE1C,MAAI,QAAQ,SAAS,CAAC,GAAG,gBAAgB,SAAS;AAClD,MAAI,gBAAgB,MAAM,eACxB,gBAAgB,MAAM,eACtB,QAAQ,MAAM,OACdQ,kBAAiB,MAAM,gBACvB,cAAc,MAAM,aACpBE,wBAAuB,MAAM,sBAC7BD,2BAA0B,MAAM,yBAChCjB,gBAAe,MAAM;AAEvB,MAAI,eAAe,kBAAkB,KAAK;AAC1C,MAAI,wBAAwB,qBAAqB,6BAA6B,cAAc,KAAK,GAC/F,QAAQ,sBAAsB,CAAC,GAC/B,WAAW,sBAAsB,CAAC;AACpC,MAAI,SAAS,MAAM,QACjB,mBAAmB,MAAM,kBACzB,eAAe,MAAM,cACrB,aAAa,MAAM;AAGrB,MAAI,cAAU,qBAAO,IAAI;AACzB,MAAI,eAAW,qBAAO,CAAC,CAAC;AACxB,MAAI,eAAW,qBAAO,IAAI;AAC1B,MAAI,sBAAkB,qBAAO,IAAI;AACjC,MAAI,wBAAoB,qBAAO,IAAI;AAEnC,MAAI,aAAa,cAAc,KAAK;AAEpC,MAAI,6BAAyB,qBAAO;AAEpC,MAAI,SAAS,aAAa;AAAA,IACxB;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,2BAAuB,0BAAY,SAAU,OAAO;AACtD,WAAO,SAAS,QAAQ,WAAW,UAAU,KAAK,CAAC;AAAA,EACrD,GAAG,CAAC,UAAU,CAAC;AAIf,uBAAqBkB,uBAAsB,CAAC,QAAQ,kBAAkB,YAAY,KAAK,GAAG,SAAS;AAAA,IACjG,gBAAgB,kBAAkB;AAAA,IAClC,qBAAqB,uBAAuB;AAAA,IAC5C;AAAA,IACA;AAAA,IACA,cAAclB;AAAA,EAChB,GAAG,KAAK,CAAC;AAET,uBAAqBiB,0BAAyB,CAAC,YAAY,GAAG,SAAS;AAAA,IACrE,gBAAgB,kBAAkB;AAAA,IAClC,qBAAqB,uBAAuB;AAAA,IAC5C;AAAA,IACA;AAAA,IACA,cAAcjB;AAAA,EAChB,GAAG,KAAK,CAAC;AAET,MAAI,kBAAkB,kBAAkB;AAAA,IACtC,aAAa,QAAQ;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgBgB;AAAA,IAChB;AAAA,EACF,CAAC;AACD,2BAAyB;AAAA,IACvB,gBAAgB,kBAAkB;AAAA,IAClC;AAAA,IACA;AAAA,EACF,CAAC;AAED,8BAAU,WAAY;AACpB,QAAI,cAAc,iBAAiB,iBAAiB;AACpD,QAAI,eAAe,SAAS,SAAS;AACnC,eAAS,QAAQ,MAAM;AAAA,IACzB;AAAA,EAEF,GAAG,CAAC,CAAC;AACL,8BAAU,WAAY;AACpB,QAAI,kBAAkB,SAAS;AAC7B;AAAA,IACF;AACA,2BAAuB,UAAU,MAAM;AAAA,EACzC,CAAC;AAED,MAAI,2BAA2B,wBAAwB,QAAQ,CAAC,UAAU,SAAS,eAAe,GAAG,aAAa,WAAY;AAC5H,aAAS;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC;AACD,MAAI,wBAAwB,4BAA4B,iBAAiB,cAAc;AAEvF,8BAAU,WAAY;AACpB,sBAAkB,UAAU;AAC5B,WAAO,WAAY;AACjB,wBAAkB,UAAU;AAAA,IAC9B;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,8BAAU,WAAY;AACpB,QAAI;AACJ,QAAI,CAAC,QAAQ;AACX,eAAS,UAAU,CAAC;AAAA,IACtB,aAAa,wBAAwB,YAAY,aAAa,OAAO,SAAS,sBAAsB,mBAAmB,SAAS,SAAS;AACvI,UAAI;AACJ,kBAAY,OAAO,UAAU,oBAAoB,SAAS,YAAY,OAAO,SAAS,kBAAkB,MAAM;AAAA,IAChH;AAAA,EACF,GAAG,CAAC,QAAQ,WAAW,CAAC;AAGxB,MAAI,2BAAuB,sBAAQ,WAAY;AAC7C,WAAO;AAAA,MACL,WAAW,SAAS,UAAU,OAAO;AACnC,cAAM,eAAe;AACrB,iBAAS;AAAA,UACP,MAAM;AAAA,UACN,QAAQ,MAAM;AAAA,UACd;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS,SAAS,QAAQ,OAAO;AAC/B,cAAM,eAAe;AACrB,iBAAS;AAAA,UACP,MAAM;AAAA,UACN,QAAQ,MAAM;AAAA,UACd;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,MAAM,SAAS,KAAK,OAAO;AACzB,YAAI,CAAC,OAAO,QAAQ,MAAM,QAAQ;AAChC;AAAA,QACF;AACA,cAAM,eAAe;AACrB,iBAAS;AAAA,UACP,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,KAAK,SAAS,IAAI,OAAO;AACvB,YAAI,CAAC,OAAO,QAAQ,MAAM,QAAQ;AAChC;AAAA,QACF;AACA,cAAM,eAAe;AACrB,iBAAS;AAAA,UACP,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,QAAQ,SAAS,OAAO,OAAO;AAC7B,YAAI,cAAc,OAAO,QAAQ;AACjC,YAAI,YAAY,UAAU,YAAY,cAAc,YAAY,gBAAgB,YAAY,mBAAmB,IAAI;AACjH,gBAAM,eAAe;AACrB,mBAAS;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,OAAO,SAAS,MAAM,OAAO;AAC3B,YAAI,cAAc,OAAO,QAAQ;AAEjC,YAAI,CAAC,YAAY,UAAU,MAAM,UAAU,KACzC;AACA;AAAA,QACF;AACA,cAAM,eAAe;AACrB,iBAAS;AAAA,UACP,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,QAAQ,SAAS,OAAO,OAAO;AAC7B,YAAI,OAAO,QAAQ,MAAM,QAAQ;AAC/B,gBAAM,eAAe;AACrB,mBAAS;AAAA,YACP,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,UAAU,SAAS,SAAS,OAAO;AACjC,YAAI,OAAO,QAAQ,MAAM,QAAQ;AAC/B,gBAAM,eAAe;AACrB,mBAAS;AAAA,YACP,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,QAAQ,oBAAoB,CAAC;AAG3C,MAAI,oBAAgB,0BAAY,SAAU,YAAY;AACpD,WAAO,SAAS;AAAA,MACd,IAAI,WAAW;AAAA,MACf,SAAS,WAAW;AAAA,IACtB,GAAG,UAAU;AAAA,EACf,GAAG,CAAC,UAAU,CAAC;AACf,MAAI,mBAAe,0BAAY,SAAU,OAAO,QAAQ;AACtD,QAAIjB;AACJ,QAAI,OAAO,UAAU,SAAS,CAAC,IAAI,OACjC,eAAe,KAAK,cACpB,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,QAAQ,aAC1C,MAAM,KAAK,KACX,OAAO,8BAA8B,MAAM,WAAW;AACxD,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,QAAQ;AAChE,0BAAsB,gBAAgB,kBAAkB,QAAQ,OAAO;AACvE,WAAO,UAAUA,aAAY,CAAC,GAAGA,WAAU,MAAM,IAAI,WAAW,KAAK,SAAU,UAAU;AACvF,cAAQ,UAAU;AAAA,IACpB,CAAC,GAAGA,WAAU,KAAK,WAAW,QAAQA,WAAU,OAAO,WAAWA,WAAU,iBAAiB,IAAI,QAAQ,KAAK,YAAY,IAAI,SAAY,KAAK,WAAW,SAASA,WAAU,eAAe,qBAAqB,cAAc,WAAY;AACzO,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC,GAAGA,aAAY,IAAI;AAAA,EACtB,GAAG,CAAC,UAAU,uBAAuB,UAAU,CAAC;AAChD,MAAI,mBAAe,0BAAY,SAAU,QAAQ;AAC/C,QAAI,WAAW;AACf,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,WAAW,MAAM,MACjB,YAAY,MAAM,OAClB,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,QAAQ,cAC3C,MAAM,MAAM,KACZ,cAAc,MAAM,aACpB,cAAc,MAAM,aACpB,UAAU,MAAM;AAChB,UAAM;AACN,QAAI,WAAW,MAAM,UACrB,OAAO,8BAA8B,OAAO,YAAY;AAC1D,QAAI,kBAAkB,OAAO,SAC3B,cAAc,gBAAgB,OAC9B,cAAc,gBAAgB;AAChC,QAAI,mBAAmB,gBAAgB,UAAU,WAAW,YAAY,OAAO,4CAA4C,GACzH,QAAQ,iBAAiB,CAAC;AAC5B,QAAI,cAAc;AAClB,QAAI,qBAAqB;AACzB,QAAI,sBAAsB,SAASwB,uBAAsB;AACvD,UAAI,UAAU,YAAY,kBAAkB;AAC1C;AAAA,MACF;AACA,sBAAgB,UAAU;AAC1B,eAAS;AAAA,QACP,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,kBAAkB,SAASC,mBAAkB;AAC/C,eAAS;AAAA,QACP,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,sBAAsB,SAASE,qBAAoBC,IAAG;AACxD,aAAOA,GAAE,eAAe;AAAA,IAC1B;AACA,WAAO,UAAU,YAAY,CAAC,GAAG,UAAU,MAAM,IAAI,WAAW,KAAK,SAAU,UAAU;AACvF,UAAI,UAAU;AACZ,iBAAS,QAAQ,WAAW,UAAU,KAAK,CAAC,IAAI;AAAA,MAClD;AAAA,IACF,CAAC,GAAG,UAAU,WAAW,UAAU,UAAU,OAAO,UAAU,UAAU,eAAe,IAAI,MAAM,UAAU,YAAY,mBAAmB,UAAU,KAAK,WAAW,UAAU,KAAK,GAAG,YAAY,CAAC,aAAa,QAAQ,CAAC,GAAG,MAAM,WAAW,IAAI,qBAAqB,oBAAoB,eAAe,GAAG,QAAQ;AAAA,MAClT,aAAa,qBAAqB,aAAa,mBAAmB;AAAA,MAClE,aAAa,qBAAqB,aAAa,mBAAmB;AAAA,IACpE,GAAG,IAAI;AAAA,EACT,GAAG,CAAC,UAAU,QAAQ,iBAAiB,UAAU,CAAC;AAClD,MAAI,2BAAuB,0BAAY,SAAU,QAAQ;AACvD,QAAI;AACJ,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,UAAU,MAAM;AAChB,UAAM;AACN,QAAI,eAAe,MAAM,QACzB,SAAS,iBAAiB,SAAS,QAAQ,cAC3C,MAAM,MAAM,KACZ,OAAO,8BAA8B,OAAO,UAAU;AACxD,QAAI,cAAc,OAAO,QAAQ;AACjC,QAAI,0BAA0B,SAASP,2BAA0B;AAC/D,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,UAAU,YAAY,CAAC,GAAG,UAAU,MAAM,IAAI,WAAW,KAAK,SAAU,kBAAkB;AAC/F,sBAAgB,UAAU;AAAA,IAC5B,CAAC,GAAG,UAAU,eAAe,IAAI,WAAW,QAAQ,UAAU,eAAe,IAAI,YAAY,QAAQ,UAAU,KAAK,WAAW,gBAAgB,UAAU,WAAW,IAAI,YAAY,CAAC,KAAK,YAAY,SAAS,CAAC,GAAG;AAAA,MACjN,SAAS,qBAAqB,SAAS,uBAAuB;AAAA,IAChE,CAAC,GAAG,IAAI;AAAA,EACV,GAAG,CAAC,UAAU,QAAQ,UAAU,CAAC;AACjC,MAAI,oBAAgB,0BAAY,SAAU,QAAQ,QAAQ;AACxD,QAAI;AACJ,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,YAAY,MAAM,WAClB,WAAW,MAAM,UACjB,UAAU,MAAM,SAChB,UAAU,MAAM,SAChB,SAAS,MAAM;AACf,UAAM;AACN,QAAI,eAAe,MAAM,QACzB,SAAS,iBAAiB,SAAS,QAAQ,cAC3C,MAAM,MAAM,KACZ,OAAO,8BAA8B,OAAO,UAAU;AACxD,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,QAAQ;AAChE,0BAAsB,iBAAiB,kBAAkB,QAAQ,QAAQ;AACzE,QAAI,cAAc,OAAO,QAAQ;AACjC,QAAI,qBAAqB,SAASQ,oBAAmB,OAAO;AAC1D,UAAI,MAAM,kBAAkB,KAAK;AACjC,UAAI,OAAO,qBAAqB,GAAG,GAAG;AACpC,6BAAqB,GAAG,EAAE,KAAK;AAAA,MACjC;AAAA,IACF;AACA,QAAI,oBAAoB,SAASC,mBAAkB,OAAO;AACxD,eAAS;AAAA,QACP,MAAM;AAAA,QACN,YAAY,MAAM,OAAO;AAAA,MAC3B,CAAC;AAAA,IACH;AACA,QAAI,kBAAkB,SAASC,iBAAgB,OAAO;AAEpD,UAAI,YAAY,UAAU,CAAC,yBAAyB,QAAQ,aAAa;AACvE,iBAAS;AAAA,UACP,MAAM;AAAA,UACN,YAAY,MAAM,kBAAkB;AAAA,QACtC,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,mBAAmB,SAASC,oBAAmB;AACjD,UAAI,CAAC,YAAY,QAAQ;AACvB,iBAAS;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AAGA,QAAI,cAAc;AAClB,QAAI,gBAAgB,CAAC;AACrB,QAAI,CAAC,KAAK,UAAU;AAClB,UAAI;AACJ,uBAAiB,iBAAiB,CAAC,GAAG,eAAe,WAAW,IAAI,qBAAqB,UAAU,SAAS,iBAAiB,GAAG,eAAe,YAAY,qBAAqB,WAAW,kBAAkB,GAAG,eAAe,SAAS,qBAAqB,QAAQ,eAAe,GAAG,eAAe,UAAU,qBAAqB,SAAS,gBAAgB,GAAG;AAAA,IACnW;AACA,WAAO,UAAU,YAAY,CAAC,GAAG,UAAU,MAAM,IAAI,WAAW,KAAK,SAAU,WAAW;AACxF,eAAS,UAAU;AAAA,IACrB,CAAC,GAAG,UAAU,uBAAuB,IAAI,YAAY,UAAU,YAAY,mBAAmB,KAAK,WAAW,UAAU,YAAY,gBAAgB,IAAI,IAAI,UAAU,mBAAmB,IAAI,QAAQ,UAAU,eAAe,IAAI,WAAW,QAAQ,UAAU,eAAe,IAAI,YAAY,QAAQ,UAAU,iBAAiB,IAAI,QAAQ,KAAK,YAAY,IAAI,SAAY,KAAK,WAAW,SAAS,UAAU,eAAe,OAAO,UAAU,KAAK,WAAW,SAAS,UAAU,OAAO,YAAY,UAAU,QAAQ,YAAY,YAAY,YAAY,eAAe,IAAI;AAAA,EACljB,GAAG,CAAC,UAAU,sBAAsB,QAAQ,0BAA0B,uBAAuB,UAAU,CAAC;AAGxG,MAAI,iBAAa,0BAAY,WAAY;AACvC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,gBAAY,0BAAY,WAAY;AACtC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,eAAW,0BAAY,WAAY;AACrC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,0BAAsB,0BAAY,SAAU,qBAAqB;AACnE,aAAS;AAAA,MACP,MAAM;AAAA,MACN,kBAAkB;AAAA,IACpB,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,iBAAa,0BAAY,SAAU,iBAAiB;AACtD,aAAS;AAAA,MACP,MAAM;AAAA,MACN,cAAc;AAAA,IAChB,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,oBAAgB,0BAAY,SAAU,eAAe;AACvD,aAAS;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,YAAQ,0BAAY,WAAY;AAClC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,qBAAqB;AAAA,EACvB,aAAa;AAAA,EACb,eAAe,CAAC;AAClB;AAWA,SAAS,gBAAgB,OAAO,SAAS;AACvC,SAAO,kBAAkB,OAAO,SAAS,kBAAkB;AAC7D;AAUA,SAAS,gBAAgB,OAAO,SAAS;AACvC,SAAO,kBAAkB,OAAO,SAAS,kBAAkB;AAC7D;AASA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,cAAc,gBAAgB,OAAO,aAAa;AACtD,MAAI,gBAAgB,gBAAgB,OAAO,eAAe;AAC1D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAWA,SAAS,4BAA4B,OAAO;AAC1C,MAAI,MAAM,YAAY,MAAM,WAAW,MAAM,WAAW,MAAM,QAAQ;AACpE,WAAO;AAAA,EACT;AACA,MAAI,UAAU,MAAM;AACpB,MAAI,mBAAmB;AAAA,EAEvB,QAAQ,UAAU;AAAA;AAAA,GAGlB,QAAQ,mBAAmB,KAAK,QAAQ,iBAAiB,IAAI;AAC3D,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAQA,SAAS,sBAAsB,qBAAqB;AAClD,MAAI,sBAAsB,oBAAoB,qBAC5C,oBAAoB,oBAAoB;AAC1C,SAAO,kBAAkB,mBAAmB,IAAI;AAClD;AACA,IAAI,YAAY;AAAA,EACd,eAAe,kBAAAvB,QAAU;AAAA,EACzB,sBAAsB,kBAAAA,QAAU;AAAA,EAChC,sBAAsB,kBAAAA,QAAU;AAAA,EAChC,cAAc,kBAAAA,QAAU;AAAA,EACxB,uBAAuB,kBAAAA,QAAU;AAAA,EACjC,cAAc,kBAAAA,QAAU;AAAA,EACxB,aAAa,kBAAAA,QAAU;AAAA,EACvB,oBAAoB,kBAAAA,QAAU;AAAA,EAC9B,oBAAoB,kBAAAA,QAAU;AAAA,EAC9B,qBAAqB,kBAAAA,QAAU;AAAA,EAC/B,uBAAuB,kBAAAA,QAAU;AAAA,EACjC,mBAAmB,kBAAAA,QAAU;AAAA,EAC7B,uBAAuB,kBAAAA,QAAU;AAAA,EACjC,aAAa,kBAAAA,QAAU,MAAM;AAAA,IAC3B,kBAAkB,kBAAAA,QAAU;AAAA,IAC5B,qBAAqB,kBAAAA,QAAU;AAAA,IAC/B,UAAU,kBAAAA,QAAU,MAAM;AAAA,MACxB,gBAAgB,kBAAAA,QAAU;AAAA,MAC1B,eAAe,kBAAAA,QAAU;AAAA,MACzB,MAAM,kBAAAA,QAAU;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAI,eAAe;AAAA,EACjB,cAAc,eAAe;AAAA,EAC7B,cAAc,eAAe;AAAA,EAC7B,aAAa,eAAe;AAAA,EAC5B;AAAA,EACA,mBAAmB;AAAA,EACnB,uBAAuB;AACzB;AAGA,IAAI,oBAAoB;AAExB,IAAI,MAAuC;AACzC,sBAAoB,SAASiB,mBAAkB,SAAS,QAAQ;AAC9D,sBAAAjB,QAAU,eAAe,WAAW,SAAS,QAAQ,OAAO,IAAI;AAAA,EAClE;AACF;AAEA,IAAI,oBAAoB,OAAwC,4BAA4B;AAC5F,IAAI,4BAA4B,OAAwC,qCAAqC;AAC7G,IAAI,+BAA+B,OAAwC,wCAAwC;AACnH,IAAI,oCAAoC,OAAwC,8CAA8C;AAC9H,IAAI,wCAAwC,OAAwC,kDAAkD;AACtI,IAAI,oCAAoC,OAAwC,6CAA6C;AAC7H,IAAI,2BAA2B,OAAwC,mCAAmC;AAC1G,IAAI,gBAAgB,OAAwC,uBAAuB;AACnF,IAAI,0BAA0B,OAAwC,mCAAmC;AACzG,IAAI,6BAA6B,OAAwC,sCAAsC;AAC/G,IAAI,2BAA2B,OAAwC,oCAAoC;AAC3G,IAAI,yBAAyB,OAAwC,kCAAkC;AACvG,IAAI,gBAAgB,OAAwC,uBAAuB;AAEnF,IAAI,mBAAgC,OAAO,OAAO;AAAA,EAChD,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAGD,SAAS,kCAAkC,OAAO,QAAQ;AACxD,MAAI,OAAO,OAAO,MAChB,QAAQ,OAAO,OACf,QAAQ,OAAO,OACf,eAAe,OAAO;AACxB,MAAI,cAAc,MAAM,aACtB,gBAAgB,MAAM;AACxB,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,gBAAU;AAAA,QACR,aAAa;AAAA,MACf;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,aAAa,cAAc,IAAI,IAAI,IAAI,cAAc;AAAA,MACvD;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,aAAa,cAAc,KAAK,cAAc,SAAS,KAAK,cAAc;AAAA,MAC5E;AACA;AAAA,IACF,KAAK;AAAA,IACL,KAAK,2BACH;AACE,UAAI,cAAc,GAAG;AACnB;AAAA,MACF;AACA,UAAI,iBAAiB;AACrB,UAAI,cAAc,WAAW,GAAG;AAC9B,yBAAiB;AAAA,MACnB,WAAW,gBAAgB,cAAc,SAAS,GAAG;AACnD,yBAAiB,cAAc,SAAS;AAAA,MAC1C;AACA,gBAAU,SAAS;AAAA,QACjB,eAAe,CAAC,EAAE,OAAO,cAAc,MAAM,GAAG,WAAW,GAAG,cAAc,MAAM,cAAc,CAAC,CAAC;AAAA,MACpG,GAAG;AAAA,QACD,aAAa;AAAA,MACf,CAAC;AACD;AAAA,IACF;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,aAAa,cAAc,SAAS;AAAA,MACtC;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,eAAe,cAAc,MAAM,GAAG,cAAc,SAAS,CAAC;AAAA,MAChE;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,eAAe,CAAC,EAAE,OAAO,eAAe,CAAC,YAAY,CAAC;AAAA,MACxD;AACA;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,aAAa;AAAA,MACf;AACA;AAAA,IACF,KAAK,4BACH;AACE,UAAI,kBAAkB;AACtB,UAAI,oBAAoB,cAAc,QAAQ,YAAY;AAC1D,UAAI,oBAAoB,GAAG;AACzB;AAAA,MACF;AACA,UAAI,cAAc,WAAW,GAAG;AAC9B,0BAAkB;AAAA,MACpB,WAAW,sBAAsB,cAAc,SAAS,GAAG;AACzD,0BAAkB,cAAc,SAAS;AAAA,MAC3C;AACA,gBAAU;AAAA,QACR,eAAe,CAAC,EAAE,OAAO,cAAc,MAAM,GAAG,iBAAiB,GAAG,cAAc,MAAM,oBAAoB,CAAC,CAAC;AAAA,QAC9G,aAAa;AAAA,MACf;AACA;AAAA,IACF;AAAA,IACF,KAAK,0BACH;AACE,UAAI,mBAAmB,OAAO;AAC9B,gBAAU;AAAA,QACR,eAAe;AAAA,MACjB;AACA;AAAA,IACF;AAAA,IACF,KAAK,wBACH;AACE,UAAI,mBAAmB,OAAO;AAC9B,gBAAU;AAAA,QACR,aAAa;AAAA,MACf;AACA;AAAA,IACF;AAAA,IACF,KAAK;AACH,gBAAU;AAAA,QACR,aAAa,gBAAgB,OAAO,aAAa;AAAA,QACjD,eAAe,gBAAgB,OAAO,eAAe;AAAA,MACvD;AACA;AAAA,IACF;AACE,YAAM,IAAI,MAAM,4CAA4C;AAAA,EAChE;AACA,SAAO,SAAS,CAAC,GAAG,OAAO,OAAO;AACpC;AAEA,IAAI,YAAY,CAAC,UAAU,OAAO,WAAW,aAAa,gBAAgB,OAAO;AAAjF,IACE,aAAa,CAAC,UAAU,OAAO,aAAa,WAAW,kBAAkB;AAC3E,qBAAqB,mBAAmB;AACxC,SAAS,qBAAqB,WAAW;AACvC,MAAI,cAAc,QAAQ;AACxB,gBAAY,CAAC;AAAA,EACf;AACA,oBAAkB,WAAW,oBAAoB;AAEjD,MAAI,QAAQ,SAAS,CAAC,GAAG,cAAc,SAAS;AAChD,MAAIwB,yBAAwB,MAAM,uBAChChC,gBAAe,MAAM,cACrB,cAAc,MAAM,aACpB,oBAAoB,MAAM,mBAC1B,wBAAwB,MAAM;AAGhC,MAAI,wBAAwB,uBAAuB,mCAAmC,gBAAgB,KAAK,GAAG,KAAK,GACjH,QAAQ,sBAAsB,CAAC,GAC/B,WAAW,sBAAsB,CAAC;AACpC,MAAI,cAAc,MAAM,aACtB,gBAAgB,MAAM;AAGxB,MAAI,wBAAoB,qBAAO,IAAI;AACnC,MAAI,kBAAc,qBAAO,IAAI;AAC7B,MAAI,+BAA2B,qBAAO,aAAa;AACnD,MAAI,uBAAmB,qBAAO;AAC9B,mBAAiB,UAAU,CAAC;AAC5B,MAAI,SAAS,aAAa;AAAA,IACxB;AAAA,IACA;AAAA,EACF,CAAC;AAID,8BAAU,WAAY;AACpB,QAAI,kBAAkB,WAAW,OAAO;AACtC;AAAA,IACF;AACA,QAAI,cAAc,SAAS,yBAAyB,QAAQ,QAAQ;AAClE,UAAI,sBAAsB,yBAAyB,QAAQ,KAAK,SAAU,MAAM;AAC9E,eAAO,cAAc,QAAQ,IAAI,IAAI;AAAA,MACvC,CAAC;AACD,gBAAUgC,uBAAsB;AAAA,QAC9B,cAAchC;AAAA,QACd,aAAa,cAAc;AAAA,QAC3B;AAAA,QACA;AAAA,QACA,oBAAoB,cAAc,WAAW;AAAA,MAC/C,CAAC,GAAG,YAAY,QAAQ;AAAA,IAC1B;AACA,6BAAyB,UAAU;AAAA,EAGrC,GAAG,CAAC,cAAc,MAAM,CAAC;AAEzB,8BAAU,WAAY;AACpB,QAAI,kBAAkB,SAAS;AAC7B;AAAA,IACF;AACA,QAAI,gBAAgB,MAAM,YAAY,SAAS;AAC7C,kBAAY,QAAQ,MAAM;AAAA,IAC5B,WAAW,iBAAiB,QAAQ,WAAW,GAAG;AAChD,uBAAiB,QAAQ,WAAW,EAAE,MAAM;AAAA,IAC9C;AAAA,EACF,GAAG,CAAC,WAAW,CAAC;AAChB,2BAAyB;AAAA,IACvB,gBAAgB,kBAAkB;AAAA,IAClC;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,wBAAwB,4BAA4B,kBAAkB;AAE1E,8BAAU,WAAY;AACpB,sBAAkB,UAAU;AAC5B,WAAO,WAAY;AACjB,wBAAkB,UAAU;AAAA,IAC9B;AAAA,EACF,GAAG,CAAC,CAAC;AAGL,MAAI,kCAA8B,sBAAQ,WAAY;AACpD,QAAI;AACJ,WAAO,OAAO,CAAC,GAAG,KAAK,qBAAqB,IAAI,WAAY;AAC1D,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH,GAAG,KAAK,iBAAiB,IAAI,WAAY;AACvC,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH,GAAG,KAAK,SAAS,SAAS,SAAS;AACjC,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH,GAAG,KAAK,YAAY,SAAS,YAAY;AACvC,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH,GAAG;AAAA,EACL,GAAG,CAAC,UAAU,mBAAmB,qBAAqB,CAAC;AACvD,MAAI,8BAA0B,sBAAQ,WAAY;AAChD,QAAI;AACJ,WAAO,QAAQ,CAAC,GAAG,MAAM,qBAAqB,IAAI,SAAU,OAAO;AACjE,UAAI,4BAA4B,KAAK,GAAG;AACtC,iBAAS;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF,GAAG,MAAM,YAAY,SAAS,UAAU,OAAO;AAC7C,UAAI,4BAA4B,KAAK,GAAG;AACtC,iBAAS;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF,GAAG;AAAA,EACL,GAAG,CAAC,UAAU,qBAAqB,CAAC;AAGpC,MAAI,2BAAuB,0BAAY,SAAU,OAAO;AACtD,QAAID;AACJ,QAAI,QAAQ,UAAU,SAAS,CAAC,IAAI,OAClC,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,QAAQ,cAC3C,MAAM,MAAM,KACZ,UAAU,MAAM,SAChB,YAAY,MAAM,WAClB,mBAAmB,MAAM,cACzB,YAAY,MAAM,OAClB,OAAO,8BAA8B,OAAO,SAAS;AACvD,QAAI,cAAc,OAAO,QAAQ;AACjC,QAAI,mBAAmB,gBAAgB,kBAAkB,WAAW,YAAY,eAAe,oDAAoD,GACjJ,QAAQ,iBAAiB,CAAC;AAC5B,QAAI,cAAc,QAAQ,MAAM,UAAU,YAAY;AACtD,QAAI,0BAA0B,SAASkC,2BAA0B;AAC/D,eAAS;AAAA,QACP,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,4BAA4B,SAASC,2BAA0B,OAAO;AACxE,UAAI,MAAM,kBAAkB,KAAK;AACjC,UAAI,OAAO,4BAA4B,GAAG,GAAG;AAC3C,oCAA4B,GAAG,EAAE,KAAK;AAAA,MACxC;AAAA,IACF;AACA,WAAO,UAAUnC,aAAY,CAAC,GAAGA,WAAU,MAAM,IAAI,WAAW,KAAK,SAAU,kBAAkB;AAC/F,UAAI,kBAAkB;AACpB,yBAAiB,QAAQ,KAAK,gBAAgB;AAAA,MAChD;AAAA,IACF,CAAC,GAAGA,WAAU,WAAW,cAAc,IAAI,IAAIA,WAAU,UAAU,qBAAqB,SAAS,uBAAuB,GAAGA,WAAU,YAAY,qBAAqB,WAAW,yBAAyB,GAAGA,aAAY,IAAI;AAAA,EAC/N,GAAG,CAAC,UAAU,QAAQ,2BAA2B,CAAC;AAClD,MAAI,uBAAmB,0BAAY,SAAU,QAAQ,QAAQ;AAC3D,QAAI;AACJ,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,QAAQ,cAC3C,MAAM,MAAM,KACZ,YAAY,MAAM,WAClB,UAAU,MAAM,SAChB,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,QAAQ,uBAC9D,OAAO,8BAA8B,OAAO,UAAU;AACxD,QAAI,QAAQ,WAAW,SAAS,CAAC,IAAI,QACnC,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,QAAQ;AAChE,0BAAsB,oBAAoB,kBAAkB,QAAQ,WAAW;AAC/E,QAAI,wBAAwB,SAASoC,uBAAsB,OAAO;AAChE,UAAI,MAAM,kBAAkB,KAAK;AACjC,UAAI,OAAO,wBAAwB,GAAG,GAAG;AACvC,gCAAwB,GAAG,EAAE,KAAK;AAAA,MACpC;AAAA,IACF;AACA,QAAI,sBAAsB,SAASC,uBAAsB;AACvD,eAAS;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,UAAU,YAAY,CAAC,GAAG,UAAU,MAAM,IAAI,WAAW,KAAK,SAAU,cAAc;AAC3F,UAAI,cAAc;AAChB,oBAAY,UAAU;AAAA,MACxB;AAAA,IACF,CAAC,GAAG,YAAY,CAAC,oBAAoB;AAAA,MACnC,WAAW,qBAAqB,WAAW,qBAAqB;AAAA,MAChE,SAAS,qBAAqB,SAAS,mBAAmB;AAAA,IAC5D,GAAG,IAAI;AAAA,EACT,GAAG,CAAC,UAAU,yBAAyB,qBAAqB,CAAC;AAG7D,MAAI,sBAAkB,0BAAY,SAAU,cAAc;AACxD,aAAS;AAAA,MACP,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,yBAAqB,0BAAY,SAAU,cAAc;AAC3D,aAAS;AAAA,MACP,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,uBAAmB,0BAAY,SAAU,kBAAkB;AAC7D,aAAS;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,IACjB,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,qBAAiB,0BAAY,SAAU,gBAAgB;AACzD,aAAS;AAAA,MACP,MAAM;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,YAAQ,0BAAY,WAAY;AAClC,aAAS;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AG5wHA,wBAAsB;AACtB,IAAAC,SAAuB;;;ACHvB,YAAuB;AACvB,kCAA0B;AACnB,IAAI,kCAA8B,4BAAAC,SAAc;AAChD,IAAI,wCAAoC,4BAAAA,SAAc;AAE7D,IAAI,UAEJ,SAAU,kBAAkB;AAC1B,iBAAeC,UAAS,gBAAgB;AAExC,WAASA,WAAU;AACjB,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAE9E,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,iBAAiB,MAAM;AAE9F,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,oBAAoB,SAAU,kBAAkB;AACrH,UAAI,oBAAoB,MAAM,kBAAkB,kBAAkB;AAChE,cAAM,gBAAgB;AAEtB,cAAM,YAAY;AAAA,MACpB;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,SAAQ;AAErB,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,SAAK,gBAAgB;AAAA,EACvB;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,WAAa,oBAAc,4BAA4B,UAAU;AAAA,MAC/D,OAAO,KAAK;AAAA,IACd,GAAS,oBAAc,kCAAkC,UAAU;AAAA,MACjE,OAAO,KAAK;AAAA,IACd,GAAG,KAAK,MAAM,QAAQ,CAAC;AAAA,EACzB;AAEA,SAAOA;AACT,EAAQ,eAAS;;;AC9CV,IAAIC,eAAc,SAASA,aAAY,KAAK;AACjD,SAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI;AACvC;AAMO,IAAI,aAAa,SAASC,YAAW,IAAI;AAC9C,MAAI,OAAO,OAAO,YAAY;AAC5B,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,WAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,IACjC;AAEA,WAAO,GAAG,MAAM,QAAQ,IAAI;AAAA,EAC9B;AACF;AAMO,IAAI,eAAe,SAASC,cAAa,MAAM,MAAM;AAC1D,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,QAAQ,OAAO,KAAK,IAAI;AAE5B,MAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,WAAO;AAAA,EACT;AAEA,WAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,QAAI,MAAM,MAAMA,EAAC;AAEjB,QAAI,KAAK,GAAG,MAAM,KAAK,GAAG,GAAG;AAC3B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAKO,IAAI,SAAS,SAASC,QAAO,KAAK,MAAM;AAE7C,MAAI,OAAO,QAAQ,YAAY;AAC7B,WAAO,WAAW,KAAK,IAAI;AAAA,EAC7B,WACS,OAAO,MAAM;AAClB,QAAI,UAAU;AAAA,EAChB;AACJ;;;AF9CA,IAAI,eAAe;AAAA,EACjB,UAAU;AAAA,EACV,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,eAAe;AACjB;AACA,IAAI,oBAAoB,CAAC;AAClB,IAAI,cAEX,SAAU,kBAAkB;AAC1B,iBAAeC,cAAa,gBAAgB;AAE5C,WAASA,eAAc;AACrB,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAE9E,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,SAAS;AAAA,MAC9E,MAAM;AAAA,MACN,WAAW;AAAA,IACb,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,kBAAkB,MAAM;AAE/F,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,cAAc,IAAI;AAEzF,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,aAAa,IAAI;AAExF,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,iBAAiB,SAAU,YAAY;AAC5G,UAAI,CAAC,cAAc,MAAM,eAAe;AAAY;AACpD,aAAO,MAAM,MAAM,UAAU,UAAU;AACvC,YAAM,aAAa;AAEnB,YAAM,qBAAqB;AAAA,IAC7B,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,gBAAgB,SAAU,WAAW;AAC1G,YAAM,YAAY;AAAA,IACpB,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,uBAAuB;AAAA,MAC5F,SAAS;AAAA,MACT,OAAO;AAAA,MACP,IAAI,SAAS,GAAG,MAAM;AACpB,YAAI,YAAY,KAAK;AAErB,cAAM,SAAS;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,cAAc,WAAY;AAC/F,aAAO;AAAA,QACL,WAAW,MAAM,MAAM;AAAA,QACvB,eAAe,MAAM,MAAM;AAAA,QAC3B,eAAe,MAAM,MAAM;AAAA,QAC3B,WAAW,SAAS,CAAC,GAAG,MAAM,MAAM,WAAW;AAAA,UAC7C,OAAO,SAAS,CAAC,GAAG,MAAM,MAAM,aAAa,MAAM,MAAM,UAAU,OAAO;AAAA,YACxE,SAAS,CAAC,CAAC,MAAM;AAAA,YACjB,SAAS,MAAM;AAAA,UACjB,CAAC;AAAA,UACD,YAAY;AAAA,YACV,SAAS;AAAA,UACX;AAAA,UACA,qBAAqB,MAAM;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,kBAAkB,WAAY;AACnG,aAAO,CAAC,MAAM,cAAc,CAAC,MAAM,MAAM,OAAO,eAAe,SAAS;AAAA,QACtE,UAAU,MAAM,MAAM,KAAK,QAAQ,OAAO;AAAA,MAC5C,GAAG,MAAM,MAAM,KAAK,MAAM;AAAA,IAC5B,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,sBAAsB,WAAY;AACvG,aAAO,CAAC,MAAM,MAAM,OAAO,SAAY,MAAM,MAAM;AAAA,IACrD,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,iBAAiB,WAAY;AAClG,aAAO,CAAC,MAAM,aAAa,CAAC,MAAM,MAAM,OAAO,oBAAoB,MAAM,MAAM,KAAK;AAAA,IACtF,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,2BAA2B,WAAY;AAC5G,aAAO,MAAM,MAAM,OAAO,MAAM,MAAM,KAAK,OAAO;AAAA,IACpD,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,yBAAyB,WAAY;AAC1G,UAAI,CAAC,MAAM;AAAgB;AAE3B,YAAM,eAAe,QAAQ;AAE7B,YAAM,iBAAiB;AAAA,IACzB,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,wBAAwB,WAAY;AACzG,YAAM,sBAAsB;AAE5B,UAAI,wBAAwB,uBAAuB,uBAAuB,KAAK,CAAC,GAC5E,aAAa,sBAAsB;AAEvC,UAAI,mBAAmB,MAAM,MAAM;AACnC,UAAI,CAAC,oBAAoB,CAAC;AAAY;AACtC,YAAM,iBAAiB,IAAI,eAAS,kBAAkB,YAAY,MAAM,WAAW,CAAC;AAAA,IACtF,CAAC;AAED,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,kBAAkB,WAAY;AACnG,UAAI,MAAM,gBAAgB;AACxB,cAAM,eAAe,eAAe;AAAA,MACtC;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,aAAY;AAEzB,SAAO,qBAAqB,SAAS,mBAAmB,WAAW,WAAW;AAE5E,QAAI,KAAK,MAAM,cAAc,UAAU,aAAa,KAAK,MAAM,qBAAqB,UAAU,oBAAoB,KAAK,MAAM,kBAAkB,UAAU,iBAAiB,KAAC,kBAAAC,SAAU,KAAK,MAAM,WAAW,UAAU,WAAW;AAAA,MAC9N,QAAQ;AAAA,IACV,CAAC,GAAG;AAEF,UAAI,MAAwC;AAC1C,YAAI,KAAK,MAAM,cAAc,UAAU,aAAa,KAAK,MAAM,aAAa,QAAQ,UAAU,aAAa,QAAQ,aAAa,KAAK,MAAM,WAAW,UAAU,SAAS,GAAG;AAC1K,kBAAQ,KAAK,oJAAoJ;AAAA,QACnK;AAAA,MACF;AAEA,WAAK,qBAAqB;AAAA,IAC5B,WAAW,KAAK,MAAM,kBAAkB,UAAU,iBAAiB,KAAK,gBAAgB;AACtF,WAAK,MAAM,gBAAgB,KAAK,eAAe,qBAAqB,IAAI,KAAK,eAAe,sBAAsB;AAAA,IACpH;AAMA,QAAI,UAAU,cAAc,KAAK,MAAM,WAAW;AAChD,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAEA,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,WAAO,KAAK,MAAM,UAAU,IAAI;AAChC,SAAK,sBAAsB;AAAA,EAC7B;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,WAAOC,aAAY,KAAK,MAAM,QAAQ,EAAE;AAAA,MACtC,KAAK,KAAK;AAAA,MACV,OAAO,KAAK,eAAe;AAAA,MAC3B,WAAW,KAAK,mBAAmB;AAAA,MACnC,iBAAiB,KAAK,wBAAwB;AAAA,MAC9C,gBAAgB,KAAK;AAAA,MACrB,YAAY;AAAA,QACV,KAAK,KAAK;AAAA,QACV,OAAO,KAAK,cAAc;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAOF;AACT,EAAQ,gBAAS;AAEjB,gBAAgB,aAAa,gBAAgB;AAAA,EAC3C,WAAW;AAAA,EACX,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,eAAe;AACjB,CAAC;AAED,IAAI,aAAa,eAAS;AAEX,SAAR,OAAwB,MAAM;AACnC,MAAI,mBAAmB,KAAK,kBACxB,QAAQ,8BAA8B,MAAM,CAAC,kBAAkB,CAAC;AAEpE,SAAa,qBAAc,4BAA4B,UAAU,MAAM,SAAU,eAAe;AAC9F,WAAa,qBAAc,aAAa,SAAS;AAAA,MAC/C,kBAAkB,qBAAqB,SAAY,mBAAmB;AAAA,IACxE,GAAG,KAAK,CAAC;AAAA,EACX,CAAC;AACH;;;AGtMA,IAAAG,SAAuB;AACvB,qBAAoB;AAIpB,IAAI,iBAEJ,SAAU,kBAAkB;AAC1B,iBAAeC,iBAAgB,gBAAgB;AAE/C,WAASA,kBAAiB;AACxB,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAE9E,oBAAgB,uBAAuB,uBAAuB,KAAK,CAAC,GAAG,cAAc,SAAU,MAAM;AACnG,aAAO,MAAM,MAAM,UAAU,IAAI;AACjC,iBAAW,MAAM,MAAM,kBAAkB,IAAI;AAAA,IAC/C,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,gBAAe;AAE5B,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,WAAO,KAAK,MAAM,UAAU,IAAI;AAAA,EAClC;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,uBAAAC,SAAQ,QAAQ,KAAK,MAAM,gBAAgB,GAAG,kEAAkE;AAChH,WAAOC,aAAY,KAAK,MAAM,QAAQ,EAAE;AAAA,MACtC,KAAK,KAAK;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,SAAOF;AACT,EAAQ,gBAAS;AAEF,SAAR,UAA2B,OAAO;AACvC,SAAa,qBAAc,kCAAkC,UAAU,MAAM,SAAU,kBAAkB;AACvG,WAAa,qBAAc,gBAAgB,SAAS;AAAA,MAClD;AAAA,IACF,GAAG,KAAK,CAAC;AAAA,EACX,CAAC;AACH;;;APjCA,uBAA6B;AAE7B,SAAS,aAAa;AACpB,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AACpE,aAASG,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,UAAI,SAAS,UAAUA,EAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,WAAW,MAAM,MAAM,SAAS;AACzC;AAEA,IAAM,kBAAkB,cAAAC,QAAe,cAAc,MAAS;AAC9D,IAAM,qBAAqB,MAAM;AAC/B,QAAM,sBAAkB,0BAAW,eAAe;AAClD,MAAI,CAAC,iBAAiB;AACpB,UAAM,IAAI,MAAM,gEAAgE;AAAA,EAClF;AACA,SAAO;AACT;AAEA,IAAM,yBAAyB;AAC/B,IAAM,WAAW,WAAS;AACxB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,mBAAe,sBAAO,CAAC;AAC7B,QAAM,sBAAkB,sBAAO,MAAS;AACxC,QAAM,uBAAmB,sBAAO,MAAS;AACzC,QAAM,uBAAmB,sBAAO,CAAC,CAAC;AAClC,QAAM,6BAAyB,sBAAO,KAAK;AAC3C,QAAM,yBAAqB,sBAAO,CAAC,CAAC;AACpC,QAAM,CAAC,cAAc,eAAe,QAAI,wBAAS,EAAE;AACnD,QAAM;AAAA,IACJ;AAAA,EACF,QAAI,0BAAW,EAAY;AAC3B,QAAM,iBAAa,sBAAO,KAAK;AAC/B,QAAM,gCAA4B,sBAAO,IAAI;AAC7C,QAAM,sBAAsB,CAAC,MAAM,cAAc;AAC/C,QAAI;AAAA,MACF;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,WAAO;AAAA,MACL,WAAW,qBAAqB,WAAW,CAAAC,OAAK;AAC9C,cAAM,eAAe,MAAM,UAAU,QAAQ,UAAU;AACvD,cAAM,WAAW,MAAM,UAAU,OAAO,UAAU;AAClD,YAAI,UAAU,QAAQ;AACpB,cAAIA,GAAE,YAAY,gBAAgB,iBAAiB,YAAY,QAAQ,iBAAiB,YAAY,UAAa,CAAC,UAAU,YAAY;AACtI,YAAAA,GAAE,eAAe;AACjB,YAAAA,GAAE,gBAAgB;AAClB,sBAAU,kBAAkB,iBAAiB,OAAO;AAAA,UACtD;AACA,cAAIA,GAAE,YAAY,UAAU;AAC1B,kBAAM,kBAAkB,OAAO,OAAO,iBAAiB,OAAO;AAC9D,gBAAI,gBAAgB,SAAS,UAAU,gBAAgB,GAAG;AACxD,cAAAA,GAAE,eAAe;AACjB,cAAAA,GAAE,gBAAgB;AAClB,wBAAU,kBAAkB,UAAU,gBAAgB;AAAA,YACxD;AAAA,UACF;AAAA,QACF,YAAYA,GAAE,YAAY,UAAU,SAASA,GAAE,YAAY,UAAU,UAAU,CAAC,UAAU,UAAU,iBAAiB,YAAY;AAC/H,UAAAA,GAAE,eAAe;AACjB,UAAAA,GAAE,gBAAgB;AAClB,oBAAU,SAAS;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,qBAAqB,WAAS;AAClC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,WAAO;AAAA,MACL,eAAe,OAAK,cAAc,oBAAoB,GAAG,SAAS,CAAC;AAAA,MACnE,sBAAsB,OAAK,qBAAqB;AAAA,QAC9C,cAAc;AAAA,QACd,GAAG;AAAA,MACL,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,EACF;AACA,SAAO,cAAAD,QAAe,cAAc,SAAS,MAAM,cAAAA,QAAe,cAAc,aAAW,WAAW;AAAA,IACpG,kBAAkB;AAAA,IAElB;AAAA,IACA;AAAA,IACA,cAAc,gBAAgB;AAAA,IAE9B;AAAA,IACA,oBAAoB,CAAC,UAAU,oBAAoB;AACjD,UAAI,oBAAoB;AACtB,YAAI,gBAAgB,QAAQ;AAC1B,6BAAmB,UAAU,eAAe;AAAA,QAC9C,WAAW,iBAAiB,eAAe;AACzC,6BAAmB,IAAI,eAAe;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAAA,IACA,eAAe,CAAC,SAAS,oBAAoB;AAC3C,UAAI,iBAAiB,kBAAkB,QAAQ,WAAW,SAAS,CAAC,QAAQ,cAAc;AACxF,oBAAY,SAAS,cAAc,eAAe;AAAA,MACpD;AACA,UAAI,OAAO,UAAU,eAAe,KAAK,SAAS,cAAc,KAAK,QAAQ,iBAAiB,MAAM;AAClG,YAAI,eAAe;AACjB,gBAAM;AAAA,YACJ,cAAAE;AAAA,UACF,IAAI;AACJ,gBAAM,oBAAoB,cAAc,UAAU,UAAQ;AACxD,mBAAOA,cAAa,IAAI,MAAMA,cAAa,QAAQ,YAAY;AAAA,UACjE,CAAC;AACD,gBAAM,uBAAuB,MAAM,KAAK,aAAa;AACrD,cAAI,sBAAsB,IAAI;AAC5B,iCAAqB,OAAO,qBAAqB,QAAQ,GAAG,QAAQ,YAAY;AAAA,UAClF,OAAO;AACL,iCAAqB,OAAO,mBAAmB,CAAC;AAAA,UAClD;AACA,kBAAQ,gBAAgB;AACxB,iBAAO,QAAQ;AACf,sBAAY,SAAS,sBAAsB,eAAe;AAAA,QAC5D,OAAO;AACL,sBAAY,SAAS,QAAQ,cAAc,eAAe;AAAA,QAC5D;AACA,YAAI,iBAAiB,eAAe;AAClC,0BAAgB,SAAS;AAAA,YACvB,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF;AACA,uBAAiB,cAAc,SAAS,eAAe;AAAA,IACzD;AAAA,IACA,cAAc,CAAC,QAAQ,YAAY;AACjC,cAAQ,QAAQ,MAAM;AAAA,QACpB,KAAK,YAAU,iBAAiB;AAC9B,cAAI,QAAQ,eAAe,MAAM,iBAAiB,YAAY;AAC5D,mBAAO;AAAA,cACL,GAAG;AAAA,cACH,QAAQ;AAAA,YACV;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAAA,EACF,GAAG,cAAc,GAAG,eAAa,cAAAF,QAAe,cAAc,gBAAgB,UAAU;AAAA,IACtF,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,mBAAmB,SAAS;AAAA,MACvC;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,QAAQ,CAAC,CAAC;AACf;AACA,SAAS,YAAY;AAAA,EACnB,QAAQ,mBAAAG,QAAU;AAAA,EAClB,cAAc,mBAAAA,QAAU;AAAA,EACxB,eAAe,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,GAAG;AAAA,EAC9C,kBAAkB,mBAAAA,QAAU;AAAA,EAC5B,YAAY,mBAAAA,QAAU;AAAA,EACtB,UAAU,mBAAAA,QAAU;AAAA,EACpB,eAAe,mBAAAA,QAAU;AAAA,EACzB,gBAAgB,mBAAAA,QAAU;AAC5B;AAEA,SAAS,mBAAmB,iBAAiB;AAC3C,UAAQ,iBAAiB;AAAA,IACvB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACA,SAAS,sBAAsB,iBAAiB;AAC9C,QAAM,kBAAkB,mBAAmB,eAAe;AAC1D,UAAQ,iBAAiB;AAAA,IACvB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACA,SAAS,iBAAiB,iBAAiB;AACzC,QAAM,wBAAwB;AAAA,IAC5B,MAAM;AAAA,IACN,KAAK;AAAA,IACL,aAAa;AAAA,IACb,WAAW;AAAA,IACX,OAAO;AAAA,IACP,eAAe;AAAA,IACf,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,cAAc;AAAA,IACd,YAAY;AAAA,EACd;AACA,SAAO,kBAAkB,sBAAsB,eAAe,IAAI;AACpE;AACA,SAAS,gBAAgB,iBAAiB;AACxC,MAAI,oBAAoB,QAAQ;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,kBAAkB,gBAAgB,MAAM,GAAG,EAAE,CAAC,IAAI;AAC3D;AAEA,IAAM,iBAAiB;AACvB,IAAM,aAAa,sCAAO,GAAG,MAAM,YAAU;AAAA,EAC3C,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,WAAW,MAAM,cAAc;AACjC,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,0CAA0C,qBAAqB,KAAK,GAAG,GAAG,WAAS,MAAM,WAAW,WAAS,MAAM,YAAY,YAAY,iBAAiB,MAAM,SAAS,GAAG;AAAA,EAChL,MAAM,GAAG,MAAM,MAAM,MAAM,OAAO;AAAA,EAClC,OAAO;AAAA,EACP,mBAAmB,MAAM,aAAa,iBAAiB;AACzD,CAAC,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC3D,WAAW,eAAe;AAAA,EACxB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,oBAAoB,sCAAO,IAAI,MAAM,YAAU;AAAA,EACnD,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,WAAW,MAAM,cAAc;AACjC,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,GAAG,WAAS,WAAW,gBAAgB,MAAM,SAAS,GAAG;AAAA,EACvE,OAAO,MAAM;AAAA,EACb,QAAQ,MAAM;AAAA,EACd,QAAQ,GAAG,MAAM,MAAM,MAAM,QAAQ,MAAM,WAAW,IAAI;AAAA,EAC1D,QAAQ,MAAM;AAAA,EACd,mBAAmB,MAAM,aAAa,iBAAiB;AACzD,CAAC,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC3D,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,sCAAO,GAAG,MAAM;AAAA,EACtC,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,MAAM;AACR,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,yBAAyB,uBAAuB,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,MAAM,WAAS,GAAG,MAAM,MAAM,QAAQ,MAAM,SAAS,cAAc,KAAK,MAAM,KAAK,KAAK,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACzO,gBAAgB,eAAe;AAAA,EAC7B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,yBAAyB,WAAS;AACtC,MAAI,MAAM,WAAW;AACnB,WAAO,GAAG,MAAM,MAAM,MAAM;AAAA,EAC9B;AACA,SAAO,GAAG,MAAM,MAAM,MAAM,OAAO;AACrC;AACA,IAAM,iBAAiB,WAAS;AAC9B,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM,UAAU;AAClB,sBAAkB,SAAS,cAAc,KAAK,MAAM,KAAK;AAAA,EAC3D,WAAW,MAAM,UAAU;AACzB,sBAAkB,SAAS,aAAa,KAAK,MAAM,KAAK;AACxD,sBAAkB,MAAM,YAAY,KAAK,iBAAiB,IAAI,IAAI;AAAA,EACpE,OAAO;AACL,sBAAkB,MAAM,MAAM,OAAO;AACrC,sBAAkB,MAAM,YAAY,SAAS,cAAc,KAAK,MAAM,OAAO,IAAI,IAAI;AAAA,EACvF;AACA,SAAO,GAAI,CAAC,qBAAqB,WAAW,qDAAqD,GAAG,iBAAiB,eAAe;AACtI;AACA,IAAM,aAAa,sCAAO,GAAG,MAAM,YAAU;AAAA,EAC3C,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB,MAAM;AACzB,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,qDAAqD,aAAa,KAAK,wCAAwC,sEAAsE,mCAAmC,uFAAuF,KAAK,GAAG,GAAG,WAAS,MAAM,WAAW,YAAY,WAAW,WAAS,uBAAuB,KAAK,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,MAAM,MAAM,MAAM,WAAS,MAAM,MAAM,MAAM,MAAM,WAAS,eAAe,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC1nB,WAAW,eAAe;AAAA,EACxB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,sCAAO,UAAU,EAAE,MAAM;AAAA,EAC7C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,UAAU,KAAK,GAAG,GAAG,WAAS,CAAC,MAAM,YAAY,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACtJ,cAAc,eAAe;AAAA,EAC3B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,sCAAO,KAAK,MAAM;AAAA,EACvC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,8BAA8B,aAAa,eAAe,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,QAAQ,MAAM,YAAY,IAAI,IAAI,WAAS,SAAS,cAAc,MAAM,aAAa,MAAM,KAAK,MAAM,KAAK,GAAG,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC5S,eAAe,eAAe;AAAA,EAC5B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,WAAS;AAC7B,SAAO,GAAI,CAAC,UAAU,iBAAiB,SAAS,IAAI,GAAG,MAAM,MAAM,UAAU,IAAI,MAAM,MAAM,MAAM,OAAO,GAAG,KAAK,GAAG,uBAAuB,KAAK,OAAO,CAAC;AAC3J;AACA,IAAM,iBAAiB,sCAAO,IAAI,MAAM;AAAA,EACtC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,yCAAyC,KAAK,6FAA6F,WAAW,KAAK,iBAAiB,YAAY,IAAI,GAAG,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,YAAY,MAAM,KAAK,WAAS,MAAM,aAAa,YAAY,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,cAAc,KAAK,GAAG,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,MAAM,UAAU,EAAE;AAChf,eAAe,eAAe;AAAA,EAC5B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,sCAAO,UAAU,EAAE,MAAM;AAAA,EAC9C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,WAAW,UAAU,MAAM,GAAG,GAAG,gBAAgB,WAAS,MAAM,MAAM,MAAM,SAAS,GAAG,MAAM,MAAM,MAAM,OAAO,OAAO,WAAS,MAAM,MAAM,MAAM,GAAG,MAAM,MAAM,MAAM,OAAO,QAAQ,QAAQ,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC9P,eAAe,eAAe;AAAA,EAC5B,OAAO;AACT;AAEA,IAAI;AACJ,SAAS,aAAa;AAAE,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAASJ,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAAE,UAAI,SAAS,UAAUA,EAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,WAAW,MAAM,MAAM,SAAS;AAAG;AACxV,IAAI,wBAAwB,SAASK,uBAAsB,OAAO;AAChE,SAA0B,qBAAc,OAAO,WAAW;AAAA,IACxD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,YAAY,UAA6B,qBAAc,QAAQ;AAAA,IACxE,MAAM;AAAA,IACN,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAM,iBAAiB;AACvB,IAAM,oBAAoB,UAAQ;AAChC,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,SAAO,cAAAJ,QAAe,cAAc,uBAAuB;AAAA,IACzD,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB;AAAA,EACF,CAAC;AACH;AACA,IAAM,iBAAiB,sCAAO,iBAAiB,EAAE,WAAW;AAAA,EAC1D,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,cAAc,WAAW,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,OAAO,kBAAkB,WAAS,MAAM,aAAa,YAAY,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC/N,eAAe,eAAe;AAAA,EAC5B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,qBAAqB,sCAAO,UAAU,EAAE,MAAM;AAAA,EAClD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,gBAAgB,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,YAAY,UAAU,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACjI,mBAAmB,eAAe;AAAA,EAChC,OAAO;AACT;AAEA,IAAI;AACJ,SAAS,aAAa;AAAE,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAASD,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAAE,UAAI,SAAS,UAAUA,EAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,WAAW,MAAM,MAAM,SAAS;AAAG;AACxV,IAAI,uBAAuB,SAASM,sBAAqB,OAAO;AAC9D,SAA0B,qBAAc,OAAO,WAAW;AAAA,IACxD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,YAAY,UAA6B,qBAAc,QAAQ;AAAA,IACxE,MAAM;AAAA,IACN,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAM,iBAAiB;AACvB,IAAM,wBAAwB,UAAQ;AACpC,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,SAAO,cAAAL,QAAe,cAAc,sBAAsB;AAAA,IACxD,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB;AAAA,EACF,CAAC;AACH;AACA,IAAM,qBAAqB,sCAAO,qBAAqB,EAAE,WAAW;AAAA,EAClE,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,cAAc,WAAW,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,OAAO,kBAAkB,WAAS,MAAM,aAAa,YAAY,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC/N,mBAAmB,eAAe;AAAA,EAChC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB,sCAAO,IAAI,MAAM;AAAA,EACxC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,4FAA4F,KAAK,aAAa,iBAAiB,YAAY,MAAM,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC7Z,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,uBAAuB,WAAS;AACpC,MAAI,MAAM,SAAS;AACjB,WAAO;AAAA,EACT;AACA,SAAO,GAAG,MAAM,MAAM,MAAM,OAAO;AACrC;AACA,IAAM,mBAAmB,sCAAO,UAAU,EAAE,MAAM;AAAA,EAChD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,iCAAiC,kBAAkB,iBAAiB,KAAK,GAAG,GAAG,WAAS,qBAAqB,KAAK,GAAG,WAAS,qBAAqB,KAAK,GAAG,WAAS,MAAM,MAAM,YAAY,UAAU,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACjQ,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,sCAAO,IAAI,MAAM;AAAA,EACvC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,0CAA0C,KAAK,OAAO,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACzM,gBAAgB,eAAe;AAAA,EAC7B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,oBAAoB;AAAA,EAC1B,UAAQ;AACN,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,WACE,cAAAA,QAAe,aAAa,uBAAS,KAAK,QAAQ,GAAG,KAAK;AAAA,EAE9D;AAAC,EAAE,MAAM;AAAA,EACP,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,UAAU,gBAAgB,wBAAwB,YAAY,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,WAAS,MAAM,MAAM,MAAM,OAAO,KAAK,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,MAAM,MAAM,UAAU,IAAI,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC9R,kBAAkB,eAAe;AAAA,EAC/B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,sCAAO,UAAU,EAAE,MAAM;AAAA,EAC/C,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACrE,gBAAgB,eAAe;AAAA,EAC7B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,sCAAO,SAAS,EAAE,MAAM,YAAU;AAAA,EACxD,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,OAAO,MAAM;AACf,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,WAAW,eAAe,OAAO,GAAG,GAAG,WAAS,CAAC,MAAM,YAAY,WAAW,WAAS,MAAM,MAAM,MAAM,QAAQ,MAAM,YAAY,KAAK,KAAK,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACxM,gBAAgB,eAAe;AAAA,EAC7B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,GAAI,CAAC,uHAAuH,CAAC;AACnJ,IAAM,cAAc,sCAAO,KAAK,EAAE,MAAM;AAAA,EACtC,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,QAAQ;AACV,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,GAAG,WAAS,MAAM,YAAY,eAAe,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACpH,YAAY,eAAe;AAAA,EACzB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,eAAe,sCAAO,IAAI,MAAM;AAAA,EACpC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,0EAA0E,GAAG,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC3I,aAAa,eAAe;AAAA,EAC1B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,WAAS;AAC9B,QAAM,SAAS,MAAM,YAAY,GAAG,MAAM,MAAM,MAAM,OAAO,QAAQ;AACrE,QAAM,WAAW,MAAM,YAAY,GAAG,MAAM,MAAM,MAAM,OAAO,SAAS;AACxE,MAAI,SAAS;AACb,MAAI,MAAM,WAAW;AACnB,aAAS,GAAG,MAAM,MAAM,MAAM,QAAQ,MAAM,YAAY,IAAI;AAAA,EAC9D;AACA,SAAO,GAAI,CAAC,YAAY,YAAY,WAAW,eAAe,YAAY,GAAG,GAAG,CAAC,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,aAAa,GAAG,UAAU,MAAM;AACxJ;AACA,IAAM,yBAAyB,sCAAO,WAAW,EAAE,MAAM;AAAA,EACvD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,eAAe,kDAAkD,KAAK,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,IAAI,WAAS,eAAe,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC7M,uBAAuB,eAAe;AAAA,EACpC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,aAAa,WAAS;AAC1B,MAAI;AACJ,MAAI;AACJ,MAAI,CAAC,MAAM,QAAQ;AACjB,UAAM,iBAAiB,MAAM,YAAY,IAAI,MAAM,MAAM,MAAM,OAAO,UAAU,IAAI,MAAM,MAAM,MAAM,OAAO;AAC7G,aAAS,GAAG;AACZ,UAAM,kBAAkB,MAAM,YAAY,QAAQ;AAClD,UAAM,aAAa,GAAG,MAAM,MAAM,MAAM;AACxC,cAAU,GAAG,mBAAmB,MAAM,MAAM,MAAM,IAAI,cAAc,mBAAmB,MAAM,MAAM,MAAM,aAAa;AAAA,EACxH;AACA,SAAO,GAAI,CAAC,WAAW,aAAa,GAAG,GAAG,QAAQ,OAAO;AAC3D;AACA,IAAM,kCAAkC,sCAAO,IAAI,MAAM;AAAA,EACvD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,+DAA+D,KAAK,GAAG,GAAG,WAAS,WAAW,KAAK,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACjK,gCAAgC,eAAe;AAAA,EAC7C,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,+BAA+B,sCAAO,IAAI,MAAM;AAAA,EACpD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,kDAAkD,sBAAsB,GAAG,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC9K,6BAA6B,eAAe;AAAA,EAC1C,OAAO;AACT;AAEA,IAAM,eAAe;AACrB,IAAM,8BAA8B,sCAAO,IAAI,MAAM;AAAA,EACnD,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,gCAAgC,aAAa,6EAA6E,8BAA8B,4BAA4B,MAAM,GAAG,GAAG,WAAS,MAAM,aAAa,YAAY,WAAW,WAAS,MAAM,MAAM,MAAM,QAAQ,MAAM,YAAY,OAAO,MAAM,WAAS,MAAM,YAAY,QAAQ,cAAc,MAAM,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,UAAU,EAAE,GAAG,WAAS,MAAM,aAAa,SAAS,cAAc,KAAK,MAAM,KAAK,IAAI,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,CAAC,MAAM,cAAc,aAAa,WAAS,wBAAwB,cAAc,KAAK,CAAC;AAClnB,4BAA4B,eAAe;AAAA,EACzC,OAAO;AACT;AAEA,IAAM,UAAU,UAAQ;AACtB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM,qBAAiB,sBAAO,IAAI;AAClC,QAAM,iBAAa,sBAAO,IAAI;AAC9B,QAAM,wBAAoB,sBAAO,MAAS;AAC1C,QAAM,CAAC,cAAc,eAAe,QAAI,wBAAS,EAAE;AACnD,QAAM,uBAAmB,sBAAO;AAChC,QAAM,4BAAwB,sBAAO,CAAC;AACtC,+BAAU,MAAM;AACd,QAAI,eAAe,WAAW,UAAU,CAAC,kBAAkB,SAAS;AAClE,qBAAe,QAAQ,MAAM;AAAA,IAC/B;AACA,QAAI,WAAW,WAAW,CAAC,UAAU,kBAAkB,SAAS;AAC9D,iBAAW,QAAQ,MAAM;AAAA,IAC3B;AACA,sBAAkB,UAAU;AAAA,EAC9B,GAAG,CAAC,QAAQ,UAAU,CAAC;AACvB,+BAAU,MAAM;AACd,QAAI,WAAW,YAAY,OAAO;AAChC,iBAAW,UAAU;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,UAAU,CAAC;AACf,+BAAU,MAAM;AACd,QAAI,iBAAiB,SAAS;AAC5B,mBAAa,iBAAiB,OAAO;AAAA,IACvC;AACA,qBAAiB,UAAU,OAAO,WAAW,MAAM;AACjD,sBAAgB,EAAE;AAAA,IACpB,GAAG,GAAG;AACN,WAAO,MAAM;AACX,mBAAa,iBAAiB,OAAO;AAAA,IACvC;AAAA,EACF,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,kBAAc,2BAAY,CAAC,aAAa,YAAY,aAAa;AACrE,aAAS,QAAQ,YAAY,QAAQ,UAAU,SAAS;AACtD,YAAM,gBAAgB,mBAAmB,QAAQ,KAAK;AACtD,UAAI,iBAAiB,cAAc,YAAY,EAAE,QAAQ,YAAY,YAAY,CAAC,MAAM,GAAG;AACzF,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,kBAAkB,CAAC;AACvB,QAAM,qBAAiB,2BAAY,CAAAC,OAAK;AACtC,QAAIA,GAAE,YAAY,UAAU,OAAO;AACjC,UAAI,cAAc;AAChB,QAAAA,GAAE,eAAe;AACjB,QAAAA,GAAE,gBAAgB;AAAA,MACpB,WAAW,qBAAqB,QAAQ,qBAAqB,QAAW;AACtE,QAAAA,GAAE,eAAe;AACjB,QAAAA,GAAE,gBAAgB;AAClB,0BAAkB,gBAAgB;AAAA,MACpC;AAAA,IACF;AACA,SAAKA,GAAE,UAAU,MAAMA,GAAE,UAAU,QAAQA,GAAE,UAAU,MAAMA,GAAE,UAAU,OAAOA,GAAE,YAAY,UAAU,OAAO;AAC7G;AAAA,IACF;AACA,UAAM,YAAY,OAAO,aAAaA,GAAE,SAASA,GAAE,OAAO;AAC1D,QAAI,CAAC,aAAa,UAAU,WAAW,GAAG;AACxC;AAAA,IACF;AACA,QAAI,CAAC,cAAc;AACjB,UAAI,qBAAqB,QAAQ,qBAAqB,QAAW;AAC/D,8BAAsB,UAAU;AAAA,MAClC,OAAO;AACL,8BAAsB,UAAU;AAAA,MAClC;AAAA,IACF;AACA,UAAM,kBAAkB,eAAe;AACvC,oBAAgB,eAAe;AAC/B,QAAI,gBAAgB,YAAY,iBAAiB,sBAAsB,UAAU,GAAG,mBAAmB,QAAQ,MAAM;AACrH,QAAI,kBAAkB,QAAW;AAC/B,sBAAgB,YAAY,iBAAiB,GAAG,sBAAsB,OAAO;AAAA,IAC/E;AACA,QAAI,kBAAkB,QAAW;AAC/B,0BAAoB,aAAa;AAAA,IACnC;AAAA,EACF,GAAG,CAAC,cAAc,aAAa,oBAAoB,kBAAkB,mBAAmB,mBAAmB,CAAC;AAC5G,QAAM,iBAAiB,eAAa;AAClC,UAAM;AAAA,MACJ,KAAK;AAAA,MACL,GAAG;AAAA,IACL,IAAI,aAAa;AACjB,UAAM,qBAAqB,qBAAqB;AAAA,MAC9C,GAAG;AAAA,MACH,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,GAAG;AAAA,MACH,GAAG,SAAS;AAAA,IACd,CAAC;AACD,UAAM,kBAAkB;AAAA,MACtB,GAAG;AAAA,MACH,iBAAiB;AAAA,MACjB,iBAAiB,mBAAmB,WAAW;AAAA,MAC/C,aAAa;AAAA,IACf;AACA,UAAM,oBAAoB,WAAW,UAAU,kBAAkB;AACjE,WAAO,cAAAD,QAAe,aAAa,cAAAA,QAAe,SAAS,KAAK,QAAQ,GAAG;AAAA,MACzE,GAAG;AAAA,MACH,CAAC,MAAM,GAAG,cAAY;AACpB,kBAAU,QAAQ;AAClB,mBAAW,UAAU;AACrB,6BAAqB,QAAQ;AAAA,MAC/B;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,cAAAA,QAAe,cAAc,WAAW,MAAM,WAAS;AAC5D,QAAI;AAAA,MACF,KAAK;AAAA,IACP,IAAI;AACJ,WAAO,cAAAA,QAAe,cAAc,cAAAA,QAAe,UAAU,MAAM,eAAe,eAAe,GAAG,cAAAA,QAAe,cAAc,aAAa,cAAc;AAAA,MAC1J,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS,CAAAC,OAAK;AACZ,YAAI,QAAQ;AACV,UAAAA,GAAE,YAAY,0BAA0B;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,WAAW;AAAA,IACb,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AACA,QAAQ,YAAY;AAAA,EAClB,UAAU,mBAAAE,QAAU;AAAA,EACpB,QAAQ,mBAAAA,QAAU;AACpB;AACA,QAAQ,eAAe;AAAA,EACrB,QAAQ;AACV;AAEA,IAAI;AACJ,SAAS,aAAa;AAAE,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAASJ,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAAE,UAAI,SAAS,UAAUA,EAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,WAAW,MAAM,MAAM,SAAS;AAAG;AACxV,IAAI,uBAAuB,SAASO,sBAAqB,OAAO;AAC9D,SAA0B,qBAAc,OAAO,WAAW;AAAA,IACxD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,YAAY,UAA6B,qBAAc,QAAQ;AAAA,IACxE,MAAM;AAAA,IACN,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAM,eAAe,cAAAN,QAAe,cAAc,MAAS;AAC3D,IAAM,kBAAkB,MAAM;AAC5B,QAAM,mBAAe,0BAAW,YAAY;AAC5C,MAAI,CAAC,cAAc;AACjB,UAAM,IAAI,MAAM,6DAA6D;AAAA,EAC/E;AACA,SAAO;AACT;AAEA,IAAM,mBAAe,0BAAW,CAAC,MAAM,QAAQ;AAC7C,MAAI;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,QAAM,eAAW,sBAAO;AACxB,QAAM,iBAAa,sBAAO;AAC1B,QAAM,wBAAoB,sBAAO,MAAM;AACvC,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,+BAAU,MAAM;AACd,QAAI,SAAS,WAAW,WAAW,kBAAkB,SAAS;AAC5D,eAAS,QAAQ,MAAM;AAAA,IACzB;AACA,sBAAkB,UAAU;AAAA,EAC9B,GAAG,CAAC,UAAU,MAAM,CAAC;AACrB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI,qBAAqB,aAAa;AAAA,IACpC,MAAM;AAAA,IACN,GAAG;AAAA,IACH,WAAW,CAAAC,OAAK;AACd,UAAI,QAAQ;AACV,QAAAA,GAAE,YAAY,0BAA0B;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,cAAc,qBAAqB,MAAM,cAAc,MAAM,aAAa,IAAI,CAAC;AAAA,IAC/E,cAAc,qBAAqB,MAAM,cAAc,MAAM,aAAa,KAAK,CAAC;AAAA,EAClF,CAAC,CAAC;AACF,QAAM,kBAAkB,qBAAqB,MAAM,WAAW,SAAS;AACvE,QAAM,qBAAqB,kBAAkB,CAAC;AAC9C,QAAM,qBAAqB,UAAU;AACrC,+BAAU,MAAM;AACd,oBAAgB,cAAc;AAAA,EAChC,GAAG,CAAC,eAAe,CAAC;AACpB,SAAO,cAAAD,QAAe,cAAc,WAAW,MAAM,WAAS;AAC5D,QAAI;AAAA,MACF,KAAK;AAAA,IACP,IAAI;AACJ,WAAO,cAAAA,QAAe,cAAc,iBAAiB,WAAW;AAAA,MAC9D,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW;AAAA,IACb,GAAG,aAAa;AAAA,MACd,KAAK,eAAa;AAChB,wBAAgB,SAAS;AACzB,qCAAU,CAAC,YAAY,GAAG,CAAC,EAAE,SAAS;AACtC,kCAA0B,UAAU;AAAA,MACtC;AAAA,IACF,CAAC,GAAG,SAAS,cAAAA,QAAe,cAAc,gBAAgB,WAAW;AAAA,MACnE,WAAW,aAAa,kBAAkB,CAAC;AAAA,MAC3C,WAAW;AAAA,MACX,YAAY,MAAM;AAAA,IACpB,GAAG,KAAK,GAAG,CAAC,UAAU,cAAAA,QAAe,cAAc,cAAc,MAAM,QAAQ,GAAG,cAAAA,QAAe,cAAc,aAAa,cAAc;AAAA,MACxI,UAAU,CAAC;AAAA,MACX,UAAU,MAAM;AAAA,MAChB,SAAS,MAAM;AACb,qBAAa,IAAI;AAAA,MACnB;AAAA,MACA,QAAQ,MAAM;AACZ,qBAAa,KAAK;AAAA,MACpB;AAAA,MACA,SAAS,CAAAC,OAAK;AACZ,YAAI,QAAQ;AACV,UAAAA,GAAE,YAAY,0BAA0B;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,MAAM;AAAA,MACN,KAAK,6BAAU,CAAC,UAAU,sBAAsB,IAAI,CAAC;AAAA,IACvD,CAAC,CAAC,GAAG,CAAC,MAAM,UAAU,cAAAD,QAAe,cAAc,gBAAgB,SAAS;AAAA,MAC1E,WAAW,aAAa,kBAAkB,CAAC;AAAA,MAC3C,WAAW;AAAA,MACX,YAAY,MAAM;AAAA,MAClB,WAAW;AAAA,IACb,GAAG,cAAAA,QAAe,cAAc,sBAAsB,IAAI,CAAC,CAAC;AAAA,EAC9D,CAAC;AACH,CAAC;AACD,aAAa,cAAc;AAC3B,aAAa,YAAY;AAAA,EACvB,WAAW,mBAAAG,QAAU;AAAA,EACrB,QAAQ,mBAAAA,QAAU;AAAA,EAClB,UAAU,mBAAAA,QAAU;AAAA,EACpB,YAAY,mBAAAA,QAAU;AAAA,EACtB,YAAY,mBAAAA,QAAU,MAAM,UAAU;AACxC;AAEA,IAAM,eAAW,0BAAW,CAAC,MAAM,QAAQ;AACzC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,eAAe;AAAA,IACzB;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM,iBAAa,sBAAO;AAC1B,QAAM,eAAW,sBAAO;AACxB,QAAM,gBAAY,sBAAO,MAAM;AAC/B,QAAM,eAAe,qBAAqB,aAAa;AAAA,IACrD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,WAAS;AAChB,YAAM,YAAY,0BAA0B;AAAA,IAC9C;AAAA,IACA,GAAG;AAAA,IACH,WAAW,WAAS;AAClB,YAAM,YAAY,0BAA0B;AAAA,IAC9C;AAAA,EACF,CAAC,CAAC;AACF,QAAM,aAAa,cAAc;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,WAAW,WAAS;AAClB,UAAI,MAAM,YAAY,UAAU,SAAS,CAAC,UAAU,CAAC,UAAU,MAAM,UAAU,EAAE,EAAE,SAAS,MAAM,OAAO,GAAG;AAC1G,cAAM,YAAY,0BAA0B;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,SAAS,WAAS;AAChB,YAAM,YAAY,0BAA0B;AAAA,IAC9C;AAAA,EACF,CAAC;AACD,+BAAU,MAAM;AACd,QAAI,SAAS,WAAW,WAAW,UAAU,SAAS;AACpD,eAAS,QAAQ,MAAM;AAAA,IACzB;AACA,cAAU,UAAU;AAAA,EACtB,GAAG,CAAC,UAAU,MAAM,CAAC;AACrB,+BAAU,MAAM;AACd,oBAAgB,UAAU;AAAA,EAC5B,GAAG,CAAC,eAAe,CAAC;AACpB,SAAO,cAAAH,QAAe,cAAc,WAAW,MAAM,WAAS;AAC5D,QAAI;AAAA,MACF,KAAK;AAAA,IACP,IAAI;AACJ,UAAM,iBAAiB,aAAW;AAChC,sBAAgB,OAAO;AACvB,mCAAU,CAAC,YAAY,GAAG,CAAC,EAAE,OAAO;AACpC,gCAA0B,UAAU;AAAA,IACtC;AACA,WAAO,cAAAA,QAAe,cAAc,YAAY,WAAW,CAAC,GAAG,YAAY;AAAA,MACzE;AAAA,MACA,YAAY;AAAA,MACZ,KAAK,6BAAU,CAAC,UAAU,YAAY,CAAC;AAAA,IACzC,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,SAAS,cAAc;AACvB,SAAS,YAAY;AAAA,EACnB,WAAW,mBAAAG,QAAU;AAAA,EACrB,QAAQ,mBAAAA,QAAU;AAAA,EAClB,UAAU,mBAAAA,QAAU;AAAA,EACpB,YAAY,mBAAAA,QAAU;AAAA,EACtB,aAAa,mBAAAA,QAAU;AAAA,EACvB,YAAY,mBAAAA,QAAU,MAAM,UAAU;AACxC;AAEA,IAAM,cAAc,cAAAH,QAAe,WAAW,CAAC,MAAM,QAAQ;AAC3D,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,mBAAmB;AAAA,IAC7B;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA,gBAAgB,CAAC;AAAA,IACjB;AAAA,IACA;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,cAAAE;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,QAAM,eAAW,sBAAO;AACxB,QAAM,iBAAa,sBAAO;AAC1B,QAAM,qBAAiB,sBAAO;AAC9B,QAAM,wBAAoB,sBAAO,MAAS;AAC1C,QAAM,2BAAuB,sBAAO,MAAS;AAC7C,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,QAAM,CAAC,aAAa,cAAc,QAAI,wBAAS,MAAS;AACxD,QAAM,mBAAe,0BAAW,EAAY;AAC5C,QAAM,cAAc,YAAY,YAAY;AAC5C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,aAAa;AAAA,IACf,KAAK,aAAa;AAAA,IAClB;AAAA,IACA,cAAc;AAAA,IACd,SAAS,UAAQ;AACf,qBAAe,IAAI;AAAA,IACrB;AAAA,EACF,CAAC;AACD,+BAAU,MAAM;AACd,2BAAuB,UAAU;AACjC,UAAM,UAAU;AAChB,WAAO,MAAM;AACX,mBAAa,QAAQ,OAAO;AAAA,IAC9B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,+BAAU,MAAM;AACd,QAAI,SAAS,SAAS;AACpB,UAAI,UAAU,CAAC,kBAAkB,SAAS;AACxC,iBAAS,QAAQ,MAAM;AAAA,MACzB,WAAW,aAAa,CAAC,qBAAqB,WAAW,gBAAgB,QAAW;AAClF,iBAAS,QAAQ,MAAM;AAAA,MACzB;AAAA,IACF;AACA,sBAAkB,UAAU;AAC5B,yBAAqB,UAAU;AAAA,EACjC,GAAG,CAAC,QAAQ,UAAU,WAAW,WAAW,CAAC;AAC7C,+BAAU,MAAM;AACd,QAAI,gBAAgB,UAAa,QAAQ;AACvC,gBAAU;AAAA,IACZ;AAAA,EACF,GAAG,CAAC,aAAa,QAAQ,SAAS,CAAC;AACnC,QAAM;AAAA,IACJ;AAAA,IACA,GAAG;AAAA,EACL,IAAI,qBAAqB,aAAa;AAAA,IACpC,UAAU,MAAM,WAAW,SAAY;AAAA,IACvC,WAAW,qBAAqB,WAAW,CAAAD,OAAK;AAC9C,UAAI,QAAQ;AACV,QAAAA,GAAE,YAAY,0BAA0B;AAAA,MAC1C,WAAW,CAAC,cAAcA,GAAE,YAAY,UAAU,MAAM;AACtD,uBAAe,cAAc,CAAC,CAAC;AAC/B,QAAAA,GAAE,eAAe;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,IACD,SAAS,MAAM;AACb,mBAAa,IAAI;AAAA,IACnB;AAAA,IACA,QAAQ,CAAAA,OAAK;AACX,YAAM,gBAAgBA,GAAE;AACxB,qBAAe,UAAU,WAAW,MAAM;AACxC,YAAI,eAAe,CAAC,cAAc,SAAS,YAAY,aAAa,GAAG;AACrE,uBAAa,KAAK;AAAA,QACpB;AAAA,MACF,GAAG,CAAC;AAAA,IACN;AAAA,IACA,cAAc,qBAAqB,MAAM,cAAc,MAAM,aAAa,IAAI,CAAC;AAAA,IAC/E,cAAc,qBAAqB,MAAM,cAAc,MAAM,aAAa,KAAK,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,GAAG;AAAA,EACL,CAAC,CAAC;AACF,QAAM,2BAAuB,2BAAY,CAAC,MAAM,UAAU;AACxD,UAAM,cAAc,MAAM;AACxB,wBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,cAAc;AAAA,MAChB,CAAC;AACD,eAAS,WAAW,SAAS,QAAQ,MAAM;AAAA,IAC7C;AACA,UAAM,eAAe,WAAW;AAAA,MAC9B,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AACD,UAAM,WAAW,cAAAD,QAAe,UAAU;AAC1C,UAAM,cAAc,cAAAA,QAAe,aAAa,cAAc;AAAA,MAC5D,GAAG,aAAa;AAAA,QACd;AAAA,QACA;AAAA,QACA,WAAW,CAAAC,OAAK;AACd,cAAIA,GAAE,YAAY,UAAU,UAAUA,GAAE,YAAY,UAAU,WAAW;AACvE,YAAAA,GAAE,eAAe;AACjB,wBAAY;AAAA,UACd;AACA,cAAIA,GAAE,YAAY,UAAU,OAAO,CAAC,YAAY;AAC9C,qBAAS,WAAW,SAAS,QAAQ,MAAM;AAC3C,YAAAA,GAAE,eAAe;AAAA,UACnB;AACA,cAAI,aAAa,KAAK;AACpB,gBAAIA,GAAE,YAAY,UAAU,SAAS,UAAU,GAAG;AAChD,cAAAA,GAAE,eAAe;AAAA,YACnB;AACA,gBAAIA,GAAE,YAAY,UAAU,QAAQ,UAAU,cAAc,SAAS,GAAG;AACtE,cAAAA,GAAE,eAAe;AACjB,uBAAS,WAAW,SAAS,QAAQ,MAAM;AAAA,YAC7C;AAAA,UACF,OAAO;AACL,gBAAIA,GAAE,YAAY,UAAU,QAAQ,UAAU,GAAG;AAC/C,cAAAA,GAAE,eAAe;AAAA,YACnB;AACA,gBAAIA,GAAE,YAAY,UAAU,SAAS,UAAU,cAAc,SAAS,GAAG;AACvE,cAAAA,GAAE,eAAe;AACjB,uBAAS,WAAW,SAAS,QAAQ,MAAM;AAAA,YAC7C;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS,CAAAA,OAAK;AACZ,UAAAA,GAAE,YAAY,0BAA0B;AAAA,QAC1C;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AAAA,MACD,MAAM,MAAM,YAAY,WAAW;AAAA,IACrC,CAAC;AACD,UAAM,MAAM,GAAGC,cAAa,IAAI,KAAK;AACrC,WAAO,cAAAF,QAAe,cAAc,8BAA8B;AAAA,MAChE;AAAA,IACF,GAAG,WAAW;AAAA,EAChB,GAAG,CAAC,cAAc,YAAY,YAAY,mBAAmBE,eAAc,eAAe,OAAO,UAAU,aAAa,GAAG,CAAC;AAC5H,QAAM,YAAQ,uBAAQ,MAAM;AAC1B,UAAM,aAAa,iBAAiB,CAAC;AACrC,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM,OAAO,WAAW,CAAC;AACzB,UAAI,IAAI,UAAU;AAChB,YAAI,MAAM,UAAU;AAClB,gBAAM,eAAe,cAAAF,QAAe,aAAa,WAAW;AAAA,YAC1D,OAAO;AAAA,YACP,aAAa,MAAM;AACjB,qBAAO;AAAA,YACT;AAAA,UACF,CAAC,GAAG;AAAA,YACF,MAAM,MAAM,YAAY,WAAW;AAAA,UACrC,CAAC;AACD,iBAAO,KAAM,cAAAA,QAAe,cAAc,8BAA8B;AAAA,YACtE,KAAK;AAAA,UACP,GAAG,YAAY,CAAC;AAAA,QAClB,OAAO;AACL,iBAAO,KAAK,qBAAqB,MAAM,CAAC,CAAC;AAAA,QAC3C;AAAA,MACF,WAAW,CAAC,aAAa,CAAC,cAAc,MAAM,UAAU;AACtD,eAAO,KAAM,cAAAA,QAAe,cAAc,8BAA8B;AAAA,UACtE,KAAK;AAAA,QACP,GAAG,cAAAA,QAAe,cAAc,6BAA6B;AAAA,UAC3D,WAAW,MAAM;AAAA,UACjB,YAAY,MAAM;AAAA,QACpB,GAAG,iBAAiB,eAAe,WAAW,SAAS,CAAC,IAAI,KAAK,WAAW,SAAS,QAAQ,CAAC,CAAC;AAC/F;AAAA,MACF,OAAO;AACL,eAAO,KAAK,qBAAqB,MAAM,CAAC,CAAC;AAAA,MAC3C;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,MAAM,UAAU,sBAAsB,eAAe,YAAY,YAAY,UAAU,gBAAgB,MAAM,SAAS,CAAC;AACtI,QAAM,qBAAqB,kBAAkB,CAAC;AAC9C,QAAM,qBAAqB,UAAU;AACrC,+BAAU,MAAM;AACd,oBAAgB,aAAa;AAAA,EAC/B,GAAG,CAAC,eAAe,CAAC;AACpB,SAAO,cAAAA,QAAe,cAAc,WAAW,MAAM,WAAS;AAC5D,QAAI;AAAA,MACF,KAAK;AAAA,IACP,IAAI;AACJ,WAAO,cAAAA,QAAe,cAAc,iBAAiB,kBAAkB;AAAA,MACrE,GAAG;AAAA,MACH,WAAW;AAAA,MACX,WAAW;AAAA,MACX,KAAK,eAAa;AAChB,wBAAgB,SAAS;AACzB,qCAAU,CAAC,YAAY,2BAA2B,GAAG,CAAC,EAAE,SAAS;AAAA,MACnE;AAAA,IACF,CAAC,GAAG,SAAS,cAAAA,QAAe,cAAc,gBAAgB,WAAW;AAAA,MACnE,WAAW,aAAa,kBAAkB,CAAC;AAAA,MAC3C,WAAW;AAAA,MACX,YAAY,MAAM;AAAA,IACpB,GAAG,KAAK,GAAG,cAAAA,QAAe,cAAc,iCAAiC;AAAA,MACvE,QAAQ,MAAM;AAAA,MACd,WAAW,MAAM;AAAA,IACnB,GAAG,OAAO,cAAAA,QAAe,cAAc,wBAAwB,cAAc;AAAA,MAC3E,UAAU,MAAM;AAAA,MAChB,SAAS,MAAM;AACb,uBAAe,MAAS;AAAA,MAC1B;AAAA,MACA,SAAS,CAAAC,OAAK;AACZ,YAAI,cAAc,WAAW,SAAS,KAAK,QAAQ;AACjD,UAAAA,GAAE,YAAY,0BAA0B;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,WAAW,CAAAA,OAAK;AACd,YAAI,CAAC,YAAY;AACf,cAAI,aAAa,OAAOA,GAAE,YAAY,UAAU,SAAS,cAAc,SAAS,KAAK,iBAAiB,YAAY,QAAW;AAC3H,2BAAe,cAAc,cAAc,SAAS,CAAC,CAAC;AAAA,UACxD,WAAW,CAAC,aAAa,OAAOA,GAAE,YAAY,UAAU,QAAQ,cAAc,SAAS,KAAK,iBAAiB,YAAY,QAAW;AAClI,2BAAe,cAAc,cAAc,SAAS,CAAC,CAAC;AAAA,UACxD,WAAWA,GAAE,YAAY,UAAU,aAAa,cAAc,SAAS,GAAG;AACxE,8BAAkB;AAAA,cAChB,MAAM;AAAA,cACN,cAAc,cAAc,cAAc,SAAS,CAAC;AAAA,YACtD,CAAC;AACD,YAAAA,GAAE,YAAY,0BAA0B;AACxC,YAAAA,GAAE,eAAe;AACjB,YAAAA,GAAE,gBAAgB;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,MACA,WAAW,aAAa,cAAc,cAAc,WAAW;AAAA,MAC/D,WAAW,MAAM;AAAA,MACjB,MAAM;AAAA,MACN,KAAK,6BAAU,CAAC,UAAU,gBAAgB,CAAC;AAAA,MAC3C,aAAa,cAAc,WAAW,IAAI,cAAc;AAAA,IAC1D,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,UAAU,cAAAD,QAAe,cAAc,gBAAgB,SAAS;AAAA,MAC3E,WAAW,aAAa,kBAAkB,CAAC;AAAA,MAC3C,WAAW;AAAA,MACX,YAAY,MAAM;AAAA,MAClB,WAAW;AAAA,IACb,GAAG,cAAAA,QAAe,cAAc,sBAAsB,IAAI,CAAC,CAAC;AAAA,EAC9D,CAAC;AACH,CAAC;AACD,YAAY,YAAY;AAAA,EACtB,WAAW,mBAAAG,QAAU;AAAA,EACrB,QAAQ,mBAAAA,QAAU;AAAA,EAClB,UAAU,mBAAAA,QAAU;AAAA,EACpB,YAAY,mBAAAA,QAAU;AAAA,EACtB,YAAY,mBAAAA,QAAU,KAAK;AAAA,EAC3B,UAAU,mBAAAA,QAAU;AAAA,EACpB,YAAY,mBAAAA,QAAU,MAAM,CAAC,WAAW,WAAW,OAAO,CAAC;AAC7D;AACA,YAAY,eAAe;AAAA,EACzB,UAAU;AACZ;AACA,YAAY,cAAc;AAE1B,IAAM,SAAS,cAAAH,QAAe,WAAW,CAAC,MAAM,QAAQ;AACtD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,QAAM,qBAAiB,sBAAO;AAC9B,QAAM,iBAAa,sBAAO;AAC1B,QAAM,wBAAoB,sBAAO,MAAS;AAC1C,QAAM,CAAC,cAAc,eAAe,QAAI,wBAAS,EAAE;AACnD,QAAM,uBAAmB,sBAAO;AAChC,QAAM,4BAAwB,sBAAO,CAAC;AACtC,+BAAU,MAAM;AACd,QAAI,eAAe,WAAW,UAAU,CAAC,kBAAkB,SAAS;AAClE,qBAAe,QAAQ,MAAM;AAAA,IAC/B;AACA,QAAI,WAAW,WAAW,CAAC,UAAU,kBAAkB,SAAS;AAC9D,iBAAW,QAAQ,MAAM;AAAA,IAC3B;AACA,sBAAkB,UAAU;AAAA,EAC9B,GAAG,CAAC,QAAQ,UAAU,CAAC;AACvB,+BAAU,MAAM;AACd,QAAI,iBAAiB,SAAS;AAC5B,mBAAa,iBAAiB,OAAO;AAAA,IACvC;AACA,qBAAiB,UAAU,OAAO,WAAW,MAAM;AACjD,sBAAgB,EAAE;AAAA,IACpB,GAAG,GAAG;AACN,WAAO,MAAM;AACX,mBAAa,iBAAiB,OAAO;AAAA,IACvC;AAAA,EACF,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,kBAAc,2BAAY,CAAC,aAAa,YAAY,aAAa;AACrE,aAAS,QAAQ,YAAY,QAAQ,UAAU,SAAS;AACtD,YAAM,gBAAgB,mBAAmB,QAAQ,KAAK;AACtD,UAAI,iBAAiB,cAAc,YAAY,EAAE,QAAQ,YAAY,YAAY,CAAC,MAAM,GAAG;AACzF,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,kBAAkB,CAAC;AACvB,QAAM,qBAAiB,2BAAY,CAAAC,OAAK;AACtC,QAAIA,GAAE,YAAY,UAAU,OAAO;AACjC,UAAI,cAAc;AAChB,QAAAA,GAAE,eAAe;AACjB,QAAAA,GAAE,gBAAgB;AAAA,MACpB,WAAW,qBAAqB,QAAQ,qBAAqB,QAAW;AACtE,QAAAA,GAAE,eAAe;AACjB,QAAAA,GAAE,gBAAgB;AAClB,0BAAkB,gBAAgB;AAClC,kBAAU;AAAA,MACZ;AAAA,IACF;AACA,SAAKA,GAAE,UAAU,MAAMA,GAAE,UAAU,QAAQA,GAAE,UAAU,MAAMA,GAAE,UAAU,OAAOA,GAAE,YAAY,UAAU,OAAO;AAC7G;AAAA,IACF;AACA,UAAM,YAAY,OAAO,aAAaA,GAAE,SAASA,GAAE,OAAO;AAC1D,QAAI,CAAC,aAAa,UAAU,WAAW,GAAG;AACxC;AAAA,IACF;AACA,QAAI,CAAC,cAAc;AACjB,UAAI,qBAAqB,QAAQ,qBAAqB,QAAW;AAC/D,8BAAsB,UAAU;AAAA,MAClC,OAAO;AACL,8BAAsB,UAAU;AAAA,MAClC;AAAA,IACF;AACA,UAAM,kBAAkB,eAAe;AACvC,oBAAgB,eAAe;AAC/B,QAAI,gBAAgB,YAAY,iBAAiB,sBAAsB,UAAU,GAAG,mBAAmB,QAAQ,MAAM;AACrH,QAAI,kBAAkB,QAAW;AAC/B,sBAAgB,YAAY,iBAAiB,GAAG,sBAAsB,OAAO;AAAA,IAC/E;AACA,QAAI,kBAAkB,QAAW;AAC/B,0BAAoB,aAAa;AAAA,IACnC;AAAA,EACF,GAAG,CAAC,cAAc,aAAa,oBAAoB,kBAAkB,mBAAmB,WAAW,mBAAmB,CAAC;AACvH,QAAM;AAAA,IACJ;AAAA,IACA,GAAG;AAAA,EACL,IAAI,qBAAqB;AAAA,IACvB,UAAU,MAAM,WAAW,SAAY;AAAA,IACvC,cAAc,qBAAqB,MAAM,cAAc,MAAM,aAAa,IAAI,CAAC;AAAA,IAC/E,cAAc,qBAAqB,MAAM,cAAc,MAAM,aAAa,KAAK,CAAC;AAAA,IAChF,SAAS,qBAAqB,MAAM,SAAS,MAAM,aAAa,IAAI,CAAC;AAAA,IACrE,QAAQ,qBAAqB,MAAM,QAAQ,MAAM,aAAa,KAAK,CAAC;AAAA,IACpE,GAAG;AAAA,EACL,CAAC;AACD,QAAM,qBAAqB,kBAAkB,CAAC;AAC9C,QAAM,qBAAqB,aAAa;AACxC,SAAO,cAAAD,QAAe,cAAc,WAAW,MAAM,WAAS;AAC5D,QAAI;AAAA,MACF,KAAK;AAAA,IACP,IAAI;AACJ,WAAO,cAAAA,QAAe,cAAc,iBAAiB,WAAW;AAAA,MAC9D,WAAW;AAAA,MACX,WAAW;AAAA,IACb,GAAG,aAAa;AAAA,MACd,MAAM;AAAA,MACN,KAAK,eAAa;AAChB,wBAAgB,SAAS;AACzB,qCAAU,CAAC,YAAY,KAAK,yBAAyB,CAAC,EAAE,SAAS;AAAA,MACnE;AAAA,IACF,CAAC,GAAG,SAAS,cAAAA,QAAe,cAAc,gBAAgB,WAAW;AAAA,MACnE,WAAW,aAAa,kBAAkB,CAAC;AAAA,MAC3C,WAAW;AAAA,MACX,YAAY,MAAM;AAAA,IACpB,GAAG,KAAK,GAAG,cAAAA,QAAe,cAAc,cAAc,MAAM,QAAQ,GAAG,cAAAA,QAAe,cAAc,aAAa,cAAc;AAAA,MAC7H,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS,CAAAC,OAAK;AACZ,YAAI,QAAQ;AACV,UAAAA,GAAE,YAAY,0BAA0B;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,WAAW;AAAA,IACb,CAAC,CAAC,GAAG,CAAC,MAAM,UAAU,cAAAD,QAAe,cAAc,gBAAgB,SAAS;AAAA,MAC1E,WAAW,aAAa,kBAAkB,CAAC;AAAA,MAC3C,WAAW;AAAA,MACX,YAAY,MAAM;AAAA,MAClB,WAAW;AAAA,IACb,GAAG,cAAAA,QAAe,cAAc,sBAAsB,IAAI,CAAC,CAAC;AAAA,EAC9D,CAAC;AACH,CAAC;AACD,OAAO,cAAc;AACrB,OAAO,YAAY;AAAA,EACjB,WAAW,mBAAAG,QAAU;AAAA,EACrB,QAAQ,mBAAAA,QAAU;AAAA,EAClB,UAAU,mBAAAA,QAAU;AAAA,EACpB,YAAY,mBAAAA,QAAU;AAAA,EACtB,YAAY,mBAAAA,QAAU,MAAM,UAAU;AAAA,EACtC,OAAO,mBAAAA,QAAU;AACnB;AAEA,IAAMI,aAAQ,0BAAW,CAAC,OAAO,aAAa;AAC5C,QAAM;AAAA,IACJ,WAAW;AAAA,MACT;AAAA,IACF;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM,CAAC,gBAAgB,iBAAiB,QAAI,wBAAS,KAAK;AAC1D,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,aAAa;AACjB,QAAM,YAAQ,uBAAQ,OAAO;AAAA,IAC3B;AAAA,IACA;AAAA,EACF,IAAI,CAAC,gBAAgB,iBAAiB,CAAC;AACvC,SAAO,cAAAP,QAAe,cAAc,aAAa,UAAU;AAAA,IACzD;AAAA,EACF,GAAG,cAAAA,QAAe,cAAc,OAAS,WAAW;AAAA,IAClD,KAAK,6BAAU,CAAC,KAAK,QAAQ,CAAC;AAAA,EAChC,GAAG,KAAK,CAAC,CAAC;AACZ,CAAC;AACDO,OAAM,cAAc;AAEpB,IAAMC,QAAO,cAAAR,QAAe,WAAW,CAAC,OAAO,QAAQ,cAAAA,QAAe,cAAc,MAAQ,WAAW;AAAA,EACrG;AACF,GAAG,KAAK,CAAC,CAAC;AACVQ,MAAK,cAAc;AAEnB,IAAM,QAAQ,cAAAR,QAAe,WAAW,CAAC,MAAM,QAAQ;AACrD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,MACT;AAAA,IACF;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,QAAM,aAAa,cAAc;AAAA,IAC/B,cAAc,qBAAqB,cAAc,MAAM;AACrD,wBAAkB,IAAI;AAAA,IACxB,CAAC;AAAA,IACD,cAAc,qBAAqB,cAAc,MAAM;AACrD,wBAAkB,KAAK;AAAA,IACzB,CAAC;AAAA,IACD,GAAG;AAAA,EACL,CAAC;AACD,SAAO,cAAAA,QAAe,cAAc,SAAS,WAAW;AAAA,IACtD;AAAA,EACF,GAAG,UAAU,CAAC;AAChB,CAAC;AACD,MAAM,cAAc;AACpB,MAAM,YAAY;AAAA,EAChB,WAAW,mBAAAG,QAAU;AACvB;AAEA,IAAMM,WAAU,cAAAT,QAAe,WAAW,CAAC,OAAO,QAAQ,cAAAA,QAAe,cAAc,SAAW,WAAW;AAAA,EAC3G;AACF,GAAG,KAAK,CAAC,CAAC;AACVS,SAAQ,cAAc;AACtBA,SAAQ,YAAY;AAAA,EAClB,YAAY,mBAAAN,QAAU,MAAM,UAAU;AACxC;AAEA,IAAM,mBAAmB,CAAC,QAAQ,OAAO,aAAa,WAAW,UAAU,gBAAgB,YAAY;AACvG,IAAM,YAAY,CAAC,GAAG,kBAAkB,OAAO,WAAW,cAAc,SAAS,aAAa,cAAc;AAE5G,IAAM,cAAc,cAAAH,QAAe,cAAc,MAAS;AAC1D,IAAM,iBAAiB,MAAM;AAC3B,QAAM,kBAAc,0BAAW,WAAW;AAC1C,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,4DAA4D;AAAA,EAC9E;AACA,SAAO;AACT;AAEA,IAAM,WAAO,0BAAW,CAAC,OAAO,YAAY;AAC1C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM,wBAAoB,sBAAO,MAAS;AAC1C,+BAAU,MAAM;AACd,QAAI,kBAAkB,WAAW,QAAQ;AACvC,wBAAkB,QAAQ;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,MAAM;AACjD,+BAAU,MAAM;AACd,QAAI;AACJ,QAAI,QAAQ;AACV,mBAAa,IAAI;AAAA,IACnB,WAAW,YAAY;AACrB,gBAAU,WAAW,MAAM,aAAa,KAAK,GAAG,GAAG;AAAA,IACrD,OAAO;AACL,mBAAa,KAAK;AAAA,IACpB;AACA,WAAO,MAAM,aAAa,OAAO;AAAA,EACnC,GAAG,CAAC,QAAQ,UAAU,CAAC;AACvB,QAAM,mBAAe,0BAAW,EAAY;AAC5C,eAAa,UAAU;AACvB,mBAAiB,UAAU,CAAC;AAC5B,mBAAiB,UAAU;AAC3B,qBAAmB,UAAU,CAAC;AAC9B,QAAM,kBAAkB,aAAa,MAAM,sBAAsB,SAAS,IAAI,mBAAmB,SAAS;AAC1G,SACE,cAAAA,QAAe,cAAc,YAAY,UAAU;AAAA,IACjD,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,cAAAA,QAAe,cAAc,QAAQ;AAAA,IACtC,WAAW;AAAA,IACX,WAAW;AAAA,IAEX,eAAe,UAAU;AAAA,EAC3B,GAAG,UAAQ;AACT,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb,IAAI;AACJ,QAAI,gBAAgB;AACpB,sBAAkB,UAAU;AAC5B,SAAK,UAAU,cAAc,0BAA0B,WAAW,0BAA0B,QAAQ,uBAAuB;AACzH,sBAAgB;AAAA,QACd,OAAO,0BAA0B,QAAQ,sBAAsB,EAAE;AAAA,QACjE,GAAG;AAAA,MACL;AAAA,IACF;AACA,UAAM,YAAY,aAAa;AAAA,MAC7B,MAAM,WAAW,UAAU,SAAS;AAAA,MACpC,WAAW;AAAA,MACX,YAAY,eAAe,UAAU;AAAA,MACrC,GAAG;AAAA,IACL,CAAC;AACD,UAAM,OAAO,cAAAA,QAAe,cAAc,mBAAmB;AAAA,MAC3D,KAAK,SAAS,MAAM;AAAA,MACpB,UAAU,UAAU;AAAA,MACpB,WAAW,UAAU;AAAA,MACrB;AAAA,MACA,UAAU,CAAC;AAAA,MACX,YAAY,UAAU;AAAA,MACtB;AAAA,IACF,GAAG,cAAAA,QAAe,cAAc,YAAY,WAAW;AAAA,MACrD,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,OAAO;AAAA,IACT,GAAG,SAAS,IAAI,UAAU,cAAc,QAAQ,CAAC;AACjD,WAAO,mBAAe,+BAAa,MAAM,YAAY,IAAI;AAAA,EAC3D,CAAC,CAAC;AAEN,CAAC;AACD,KAAK,cAAc;AACnB,KAAK,YAAY;AAAA,EACf,iBAAiB,mBAAAG,QAAU;AAAA,EAC3B,eAAe,mBAAAA,QAAU;AAAA,EACzB,QAAQ,mBAAAA,QAAU;AAAA,EAClB,OAAO,mBAAAA,QAAU;AAAA,EACjB,WAAW,mBAAAA,QAAU,MAAM,SAAS;AAAA,EACpC,YAAY,mBAAAA,QAAU;AAAA,EACtB,WAAW,mBAAAA,QAAU;AAAA,EACrB,UAAU,mBAAAA,QAAU;AAAA,EACpB,WAAW,mBAAAA,QAAU;AACvB;AACA,KAAK,eAAe;AAAA,EAClB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,WAAW;AAAA,EACX,QAAQ;AACV;AAEA,IAAM,YAAY,cAAAH,QAAe,WAAW,CAAC,OAAO,QAAQ,cAAAA,QAAe,cAAc,iBAAiB,WAAW;AAAA,EACnH;AACF,GAAG,KAAK,CAAC,CAAC;AACV,UAAU,cAAc;AAExB,IAAI;AACJ,SAAS,aAAa;AAAE,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAASD,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAAE,UAAI,SAAS,UAAUA,EAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,WAAW,MAAM,MAAM,SAAS;AAAG;AACxV,IAAI,gBAAgB,SAASW,eAAc,OAAO;AAChD,SAA0B,qBAAc,OAAO,WAAW;AAAA,IACxD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,YAAY,UAA6B,qBAAc,QAAQ;AAAA,IACxE,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAI;AACJ,SAASC,YAAW;AAAE,EAAAA,YAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAASZ,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAAE,UAAI,SAAS,UAAUA,EAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAOY,UAAS,MAAM,MAAM,SAAS;AAAG;AAClV,IAAI,mBAAmB,SAASC,kBAAiB,OAAO;AACtD,SAA0B,qBAAc,OAAOD,UAAS;AAAA,IACtD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,UAAU,QAA2B,qBAAc,QAAQ;AAAA,IACpE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAM,cAAc,cAAAX,QAAe,cAAc,MAAS;AAC1D,IAAM,iBAAiB,MAAM;AAC3B,QAAM,cAAU,0BAAW,WAAW;AACtC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,6DAA6D;AAAA,EAC/E;AACA,SAAO;AACT;AAEA,IAAM,OAAO,cAAAA,QAAe,WAAW,CAAC,MAAMa,gBAAe;AAC3D,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAAX;AAAA,IACF;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AACnB,QAAM,cAAU,sBAAO;AACvB,QAAMY,aAAY;AAClB,OAAK,UAAU,UAAa,UAAU,SAAS,CAAC,UAAU;AACxD,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC9D;AACA,QAAM,eAAe,aAAa;AAClC,QAAM,YAAY,qBAAqB;AACvC,MAAI;AACJ,+BAAU,MAAM;AACd,QAAI,CAAC,YAAY,QAAQ,SAAS;AAChC,YAAM,gBAAgB,QAAQ,QAAQ;AACtC,UAAI,eAAe;AACjB,2BAAmB,QAAQ,YAAY,IAAI;AAAA,MAC7C;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,OAAO;AACT,QAAI,eAAe;AACjB,mBAAa,cAAc,KAAK,UAAQ;AACtC,eAAOZ,cAAa,IAAI,MAAMA,cAAa,KAAK;AAAA,MAClD,CAAC;AAAA,IACH,OAAO;AACL,mBAAaA,cAAa,YAAY,MAAMA,cAAa,KAAK;AAAA,IAChE;AAAA,EACF,OAAO;AACL,iBAAa;AAAA,EACf;AACA,+BAAU,MAAM;AACd,QAAI,UAAU,CAAC,YAAY,CAAC,iBAAiB,YAAY;AACvD,0BAAoB,YAAY;AAAA,IAClC;AAAA,EACF,GAAG,CAAC,cAAc,UAAU,QAAQ,YAAY,eAAe,mBAAmB,CAAC;AACnF,QAAM,mBAAe,uBAAQ,OAAO;AAAA,IAClC,YAAY;AAAA,EACd,IAAI,CAAC,QAAQ,CAAC;AACd,QAAM,MAAM,6BAAU,CAAC,SAASW,WAAU,CAAC;AAC3C,MAAI,UAAU;AACZ,WAAO,cAAAb,QAAe,cAAc,YAAY,UAAU;AAAA,MACxD,OAAO;AAAA,IACT,GAAG,cAAAA,QAAe,cAAcc,YAAW,WAAW;AAAA,MACpD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,KAAK,GAAG,cAAc,CAAC,WAAW,cAAAd,QAAe,cAAc,gBAAgB;AAAA,MAChF;AAAA,MACA,WAAW;AAAA,MACX,YAAY;AAAA,IACd,GAAG,cAAAA,QAAe,cAAc,kBAAkB,IAAI,CAAC,GAAG,QAAQ,CAAC;AAAA,EACrE;AACA,eAAa;AACb,SAAO,cAAAA,QAAe,cAAc,YAAY,UAAU;AAAA,IACxD,OAAO;AAAA,EACT,GAAG,cAAAA,QAAe,cAAcc,YAAW,aAAa;AAAA,IACtD,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAI,WAAW,WAAW;AAAA,MACxB,MAAM;AAAA,MACN,iBAAiB;AAAA,IACnB;AAAA,IACA,GAAG;AAAA,EACL,CAAC,GAAG,cAAc,CAAC,WAAW,cAAAd,QAAe,cAAc,gBAAgB;AAAA,IACzE;AAAA,IACA,WAAW;AAAA,EACb,GAAG,cAAAA,QAAe,cAAc,kBAAkB,IAAI,CAAC,GAAG,QAAQ,CAAC;AACrE,CAAC;AACD,KAAK,cAAc;AACnB,KAAK,YAAY;AAAA,EACf,OAAO,mBAAAG,QAAU;AAAA,EACjB,UAAU,mBAAAA,QAAU;AACtB;AAEA,IAAM,mBAAmB,cAAAH,QAAe,WAAW,CAAC,MAAM,QAAQ;AAChE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAA,QAAe,cAAc,eAAe,WAAW;AAAA,IAC5D;AAAA,IACA;AAAA,EACF,GAAG,KAAK,GAAG,cAAAA,QAAe,cAAc,gBAAgB;AAAA,IACtD;AAAA,IACA,WAAW;AAAA,IACX,YAAY;AAAA,EACd,GAAG,cAAAA,QAAe,cAAc,eAAe,IAAI,CAAC,GAAG,QAAQ;AACjE,CAAC;AACD,IAAM,UAAU,cAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ,cAAAA,QAAe,cAAc,MAAM,WAAW;AAAA,EACtG,WAAW;AAAA,EACX;AACF,GAAG,OAAO;AAAA,EACR,SAAS;AACX,CAAC,CAAC,CAAC;AACH,QAAQ,cAAc;AACtB,QAAQ,YAAY;AAAA,EAClB,OAAO,mBAAAG,QAAU;AAAA,EACjB,UAAU,mBAAAA,QAAU;AACtB;AAEA,IAAM,aAAa,cAAAH,QAAe,WAAW,CAAC,OAAO,QAAQ;AAC3D,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAA,QAAe,cAAc,kBAAkB,WAAW;AAAA,IAC/D;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,WAAW,cAAc;AAEzB,IAAM,aAAa,cAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ;AAC3D,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAA,QAAe,cAAc,kBAAkB,WAAW;AAAA,IAC/D;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,WAAW,cAAc;AAEzB,IAAM,WAAW,cAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ;AACzD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAA,QAAe,cAAc,gBAAgB,WAAW;AAAA,IAC7D;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,SAAS,cAAc;AAEvB,IAAM,YAAY,cAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ;AAC1D,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAA,QAAe,cAAc,iBAAiB,WAAW;AAAA,IAC9D;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,UAAU,cAAc;AAExB,IAAM,cAAc,WAAS;AAC3B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAA,QAAe,cAAc,mBAAmB,WAAW;AAAA,IAChE;AAAA,EACF,GAAG,KAAK,CAAC;AACX;AAEA,IAAM,YAAY,cAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ,cAAAA,QAAe,cAAc,MAAM,WAAW;AAAA,EACxG,WAAW;AAAA,EACX;AACF,GAAG,KAAK,CAAC,CAAC;AACV,UAAU,cAAc;AACxB,UAAU,YAAY;AAAA,EACpB,OAAO,mBAAAG,QAAU;AAAA,EACjB,UAAU,mBAAAA,QAAU;AACtB;AAEA,IAAM,oBAAoB,cAAAH,QAAe,WAAW,CAAC,MAAM,QAAQ;AACjE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAA,QAAe,cAAc,gBAAgB,WAAW;AAAA,IAC7D;AAAA,IACA;AAAA,EACF,GAAG,KAAK,GAAG,cAAAA,QAAe,cAAc,gBAAgB;AAAA,IACtD;AAAA,IACA,YAAY;AAAA,IACZ,WAAW;AAAA,EACb,GAAG,cAAAA,QAAe,cAAc,gBAAgB;AAAA,IAC9C,YAAY;AAAA,EACd,CAAC,CAAC,GAAG,QAAQ;AACf,CAAC;AACD,IAAM,WAAW,cAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ;AACzD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,MACT,cAAAE;AAAA,IACF;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,MAAI,CAAC,UAAU;AACb,qBAAiB,QAAQA,cAAa,KAAK,CAAC,IAAI,aAAa;AAAA,EAC/D;AACA,SAAO,cAAAF,QAAe,cAAc,MAAM,WAAW;AAAA,IACnD,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,OAAO;AAAA,IACR,SAAS;AAAA,EACX,CAAC,CAAC;AACJ,CAAC;AACD,SAAS,cAAc;AACvB,SAAS,YAAY;AAAA,EACnB,OAAO,mBAAAG,QAAU;AAAA,EACjB,UAAU,mBAAAA,QAAU;AACtB;AAEA,IAAM,wBAAwB,cAAAH,QAAe,WAAW,CAAC,MAAM,QAAQ;AACrE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,SAAO,cAAAA,QAAe,cAAc,oBAAoB,WAAW;AAAA,IACjE;AAAA,IACA;AAAA,EACF,GAAG,KAAK,GAAG,cAAAA,QAAe,cAAc,gBAAgB;AAAA,IACtD;AAAA,IACA,YAAY;AAAA,IACZ,WAAW;AAAA,EACb,GAAG,cAAAA,QAAe,cAAc,oBAAoB;AAAA,IAClD,YAAY;AAAA,EACd,CAAC,CAAC,GAAG,QAAQ;AACf,CAAC;AACD,IAAM,eAAe,cAAAA,QAAe,WAAW,CAAC,OAAO,QAAQ;AAC7D,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe;AACnB,MAAI,CAAC,UAAU;AACb,qBAAiB,UAAU,aAAa;AAAA,EAC1C;AACA,SAAO,cAAAA,QAAe,cAAc,MAAM,WAAW;AAAA,IACnD,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB;AAAA,IACA;AAAA,EACF,GAAG,OAAO;AAAA,IACR;AAAA,IACA,SAAS;AAAA,EACX,CAAC,CAAC;AACJ,CAAC;AACD,aAAa,cAAc;AAC3B,aAAa,YAAY;AAAA,EACvB,OAAO,mBAAAG,QAAU;AAAA,EACjB,UAAU,mBAAAA,QAAU;AACtB;", "names": ["isForwardRef", "React", "import_react", "import_prop_types", "e", "t", "n", "l", "i", "o", "r", "d", "h", "f", "u", "s", "m", "p", "w", "b", "__assign", "t", "i", "n", "validateControlledUnchanged", "Downshift", "i", "_extends2", "itemToString", "onMouseDown", "onMouseUp", "onTouchStart", "onTouchMove", "onTouchEnd", "selectedItemChanged", "stateReducer", "PropTypes", "document", "state", "action", "defaultStateValues", "useGetterPropsCalledChecker", "useControlPropsValidator", "stateChangeTypes", "scrollIntoView", "getA11ySelectionMessage", "getA11yStatusMessage", "menuHandleMouseLeave", "toggleButtonHandleClick", "toggleButtonHandleBlur", "toggleButtonHandleKeyDown", "itemHandleMouseMove", "itemHandleClick", "validatePropTypes", "itemHandleMouseDown", "e", "inputHandleKeyDown", "inputHandleChange", "inputHandleBlur", "inputHandleFocus", "getA11yRemovalMessage", "selectedItemHandleClick", "selectedItemHandleKeyDown", "dropdownHandleKeyDown", "dropdownHandleClick", "React", "createContext", "Manager", "unwrapArray", "safeInvoke", "shallowEqual", "i", "setRef", "InnerPopper", "deepEqual", "unwrapArray", "React", "InnerReference", "warning", "unwrapArray", "i", "React__default", "e", "itemToString", "PropTypes", "SvgChevronRightStroke", "SvgChevronLeftStroke", "SvgChevronDownStroke", "Field", "Hint", "Message", "SvgPlusStroke", "_extends", "SvgCheckLgStroke", "forwardRef", "Component"]}