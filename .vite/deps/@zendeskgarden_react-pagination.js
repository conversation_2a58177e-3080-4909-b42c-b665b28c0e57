import {
  useSelection
} from "./chunk-YKXCMXPN.js";
import {
  DEFAULT_THEME,
  getColor,
  getLineHeight,
  math,
  retrieveComponentStyles,
  useText
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import {
  getControlledValue
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  Ae,
  Me,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-pagination/dist/index.esm.js
var React2 = __toESM(require_react());
var import_react2 = __toESM(require_react());
var import_prop_types2 = __toESM(require_prop_types());

// node_modules/@zendeskgarden/container-pagination/dist/index.esm.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var usePagination = (options) => {
  const {
    selectedItem,
    focusedItem,
    getContainerProps: getSelectionContainerProps,
    getItemProps
  } = useSelection(options);
  const getContainerProps = function(_temp) {
    let {
      role = "list",
      ...other
    } = _temp === void 0 ? {} : _temp;
    return {
      ...getSelectionContainerProps(other),
      role: role === null ? void 0 : role,
      "data-garden-container-id": "containers.pagination",
      "data-garden-container-version": "1.0.5"
    };
  };
  const getPreviousPageProps = (_ref) => {
    let {
      role = "listitem",
      ...other
    } = _ref;
    return {
      ...getItemProps({
        selectedAriaKey: null,
        ...other
      }),
      role: role === null ? void 0 : role
    };
  };
  const getNextPageProps = (_ref2) => {
    let {
      role = "listitem",
      ...other
    } = _ref2;
    return {
      ...getItemProps({
        selectedAriaKey: null,
        ...other
      }),
      role: role === null ? void 0 : role
    };
  };
  const getPageProps = (_ref3) => {
    let {
      role = "listitem",
      ...other
    } = _ref3;
    return {
      ...getItemProps({
        selectedAriaKey: "aria-current",
        ...other
      }),
      role: role === null ? void 0 : role
    };
  };
  return {
    selectedItem,
    focusedItem,
    getContainerProps,
    getPageProps,
    getPreviousPageProps,
    getNextPageProps
  };
};
var PaginationContainer = (_ref) => {
  let {
    children,
    render = children,
    ...options
  } = _ref;
  return import_react.default.createElement(import_react.default.Fragment, null, render(usePagination(options)));
};
PaginationContainer.propTypes = {
  children: import_prop_types.default.func,
  render: import_prop_types.default.func,
  focusedItem: import_prop_types.default.any,
  selectedItem: import_prop_types.default.any,
  onSelect: import_prop_types.default.func,
  onFocus: import_prop_types.default.func
};

// node_modules/@zendeskgarden/react-pagination/dist/index.esm.js
function _extends$4() {
  _extends$4 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$4.apply(this, arguments);
}
var COMPONENT_ID$6 = "pagination.pagination_view";
var StyledPagination = styled_components_browser_esm_default.ul.attrs({
  "data-garden-id": COMPONENT_ID$6,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledPagination",
  componentId: "sc-1b7nye9-0"
})(["direction:", ";display:flex;justify-content:center;margin:0;padding:0;white-space:nowrap;color:", ";:focus{outline:none;}", ";"], (props) => props.theme.rtl && "rtl", (props) => getColor("neutralHue", 600, props.theme), (props) => retrieveComponentStyles(COMPONENT_ID$6, props));
StyledPagination.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$5 = "pagination.page";
var colorStyles = (props) => {
  const defaultColor = getColor("neutralHue", 600, props.theme);
  const hoverForegroundColor = getColor("neutralHue", 700, props.theme);
  const hoverBackgroundColor = getColor("primaryHue", 600, props.theme, 0.08);
  const boxShadowColor = getColor("primaryHue", 600, props.theme, 0.35);
  const activeForegroundColor = getColor("neutralHue", 800, props.theme);
  const activeBackgroundColor = getColor("primaryHue", 600, props.theme, 0.2);
  const currentForegroundColor = activeForegroundColor;
  const currentBackgroundColor = hoverBackgroundColor;
  const currentHoverBackgroundColor = getColor("primaryHue", 600, props.theme, 0.16);
  const currentActiveBackgroundColor = getColor("primaryHue", 600, props.theme, 0.28);
  return Ae(["color:", ";&:hover{background-color:", ";color:", ";}&[data-garden-focus-visible]{box-shadow:inset ", ";}&:active,&[data-garden-focus-visible]:active{background-color:", ";color:", ";}&[aria-current='true']{background-color:", ";color:", ";}&[aria-current='true']:hover{background-color:", ";}&[aria-current='true']:active{background-color:", ";}:disabled,[aria-disabled='true']{background-color:transparent;color:", ";}"], defaultColor, hoverBackgroundColor, hoverForegroundColor, props.theme.shadows.md(boxShadowColor), activeBackgroundColor, activeForegroundColor, currentBackgroundColor, currentForegroundColor, currentHoverBackgroundColor, currentActiveBackgroundColor, getColor("grey", 300, props.theme));
};
var sizeStyles$2 = (props) => {
  const fontSize = props.theme.fontSizes.md;
  const height = `${props.theme.space.base * 8}px`;
  const lineHeight = getLineHeight(height, fontSize);
  const padding = `${props.theme.space.base * 1.5}px`;
  return Ae(["padding:0 ", ";height:", ";line-height:", ";font-size:", ";"], padding, height, lineHeight, fontSize);
};
var StyledPageBase = styled_components_browser_esm_default.li.attrs({
  "data-garden-id": COMPONENT_ID$5,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledPageBase",
  componentId: "sc-lw1w9j-0"
})(["box-sizing:border-box;display:inline-block;transition:box-shadow 0.1s ease-in-out,background-color 0.25s ease-in-out,color 0.25s ease-in-out;visibility:", ";border-radius:", ";cursor:pointer;overflow:hidden;text-align:center;text-overflow:ellipsis;user-select:none;", ";&:focus{outline:none;}&[aria-current='true']{font-weight:", ";}:disabled,[aria-disabled='true']{cursor:default;}", ";", ";"], (props) => props.hidden && "hidden", (props) => props.theme.borderRadii.md, (props) => sizeStyles$2(props), (props) => props.theme.fontWeights.semibold, (props) => colorStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledPageBase.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$4 = "pagination.page";
var sizeStyles$1 = (props) => {
  const height = `${props.theme.space.base * 8}px`;
  return Ae(["min-width:", ";max-width:", ";&[aria-current='true']{max-width:none;}"], height, math(`${height} * 2`));
};
var StyledPage = styled_components_browser_esm_default(StyledPageBase).attrs({
  "data-garden-id": COMPONENT_ID$4,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledPage",
  componentId: "sc-1k0een3-0"
})(["margin-left:", ";", ';&[aria-current="true"]{font-weight:', ";}&", "{margin-left:0;}", ";"], (props) => `${props.theme.space.base}px`, (props) => sizeStyles$1(props), (props) => props.theme.fontWeights.semibold, (props) => props.theme.rtl ? ":last-of-type" : ":first-of-type", (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledPage.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "cursor_pagination";
var StyledCursorPagination = styled_components_browser_esm_default.nav.attrs({
  "data-garden-id": COMPONENT_ID$3,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledCursorPagination",
  componentId: "sc-qmfecg-0"
})(["display:flex;justify-content:center;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledCursorPagination.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "cursor_pagination.cursor";
var StyledCursor = styled_components_browser_esm_default(StyledPageBase).attrs({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.67.0",
  as: "button"
}).withConfig({
  displayName: "StyledCursor",
  componentId: "sc-507ee-0"
})(["display:flex;align-items:center;border:none;background:transparent;padding:", ";overflow:visible;&:not(", "-of-type){margin-right:", "px;}", ";"], (props) => `0px ${props.theme.space.base * 2}px`, (props) => props.theme.rtl ? ":first" : ":last", (props) => props.theme.space.base, (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledCursor.defaultProps = {
  theme: DEFAULT_THEME
};
var marginStyles = (props) => {
  const {
    type,
    theme
  } = props;
  const margin = theme.space.base;
  if (theme.rtl) {
    return Ae(["margin-", ":", "px;"], type === "last" || type === "next" ? "right" : "left", margin);
  }
  return Ae(["margin-", ":", "px;"], type === "first" || type === "previous" ? "right" : "left", margin);
};
var StyledIcon = styled_components_browser_esm_default((_ref) => {
  let {
    children,
    ...props
  } = _ref;
  return (0, import_react2.cloneElement)(import_react2.Children.only(children), props);
}).withConfig({
  displayName: "StyledIcon",
  componentId: "sc-2vzk6e-0"
})(["", " transform:", ";"], marginStyles, (props) => props.theme.rtl && "rotate(180deg)");
StyledIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "pagination.gap";
var sizeStyles = (props) => {
  const shift = 2;
  const marginTop = `-${shift}px`;
  const fontSize = math(`${props.theme.fontSizes.md} + ${shift}`);
  return Ae(["margin-top:", ";font-size:", ";"], marginTop, fontSize);
};
var StyledGap = styled_components_browser_esm_default(StyledPage).attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledGap",
  componentId: "sc-1hqjltf-0"
})(["cursor:default;", ";&:hover{background-color:transparent;color:inherit;}", ";"], (props) => sizeStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledGap.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "pagination.navigation";
var StyledNavigation = styled_components_browser_esm_default(StyledPage).attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledNavigation",
  componentId: "sc-184uuwe-0"
})(["display:flex;align-items:center;justify-content:center;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledNavigation.defaultProps = {
  theme: DEFAULT_THEME
};
var _path$3;
function _extends$3() {
  _extends$3 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$3.apply(this, arguments);
}
var SvgChevronLeftStroke = function SvgChevronLeftStroke2(props) {
  return React2.createElement("svg", _extends$3({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$3 || (_path$3 = React2.createElement("path", {
    fill: "currentColor",
    d: "M10.39 12.688a.5.5 0 01-.718.69l-.062-.066-4-5a.5.5 0 01-.054-.542l.054-.082 4-5a.5.5 0 01.83.55l-.05.074L6.641 8l3.75 4.688z"
  })));
};
var _path$2;
function _extends$2() {
  _extends$2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$2.apply(this, arguments);
}
var SvgChevronRightStroke = function SvgChevronRightStroke2(props) {
  return React2.createElement("svg", _extends$2({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$2 || (_path$2 = React2.createElement("path", {
    fill: "currentColor",
    d: "M5.61 3.312a.5.5 0 01.718-.69l.062.066 4 5a.5.5 0 01.054.542l-.054.082-4 5a.5.5 0 01-.83-.55l.05-.074L9.359 8l-3.75-4.688z"
  })));
};
var PreviousComponent$1 = (0, import_react2.forwardRef)((props, ref) => {
  const ariaLabel = useText(PreviousComponent$1, props, "aria-label", "Previous page");
  const theme = (0, import_react2.useContext)(Me);
  return import_react2.default.createElement(StyledNavigation, _extends$4({}, props, {
    "aria-label": ariaLabel,
    ref
  }), theme.rtl ? import_react2.default.createElement(SvgChevronRightStroke, null) : import_react2.default.createElement(SvgChevronLeftStroke, null));
});
PreviousComponent$1.displayName = "Pagination.Previous";
var Previous$1 = PreviousComponent$1;
var NextComponent$1 = (0, import_react2.forwardRef)((props, ref) => {
  const ariaLabel = useText(NextComponent$1, props, "aria-label", "Next page");
  const theme = (0, import_react2.useContext)(Me);
  return import_react2.default.createElement(StyledNavigation, _extends$4({}, props, {
    "aria-label": ariaLabel,
    ref
  }), theme.rtl ? import_react2.default.createElement(SvgChevronLeftStroke, null) : import_react2.default.createElement(SvgChevronRightStroke, null));
});
NextComponent$1.displayName = "Pagination.Next";
var Next$1 = NextComponent$1;
var PageComponent = (0, import_react2.forwardRef)((props, ref) => {
  const text = props["aria-current"] ? `Current page, page ${props.children}` : `Page ${props.children}`;
  const ariaLabel = useText(PageComponent, props, "aria-label", text);
  return import_react2.default.createElement(StyledPage, _extends$4({}, props, {
    "aria-label": ariaLabel,
    ref
  }));
});
PageComponent.displayName = "Pagination.Page";
var Page = PageComponent;
var GapComponent = (0, import_react2.forwardRef)((props, ref) => import_react2.default.createElement(StyledGap, _extends$4({}, props, {
  ref
}), "…"));
GapComponent.displayName = "Pagination.Gap";
var Gap = GapComponent;
var PREVIOUS_KEY = "previous";
var NEXT_KEY = "next";
var Pagination = (0, import_react2.forwardRef)((_ref, ref) => {
  let {
    currentPage: controlledCurrentPage,
    transformPageProps,
    totalPages,
    pagePadding,
    pageGap,
    onChange,
    ...otherProps
  } = _ref;
  const [focusedItem, setFocusedItem] = (0, import_react2.useState)();
  const [internalCurrentPage, setInternalCurrentPage] = (0, import_react2.useState)(1);
  const currentPage = getControlledValue(controlledCurrentPage, internalCurrentPage);
  const theme = (0, import_react2.useContext)(Me);
  const {
    getContainerProps,
    getPageProps,
    getPreviousPageProps,
    getNextPageProps
  } = usePagination({
    rtl: theme.rtl,
    focusedItem,
    selectedItem: currentPage,
    onFocus: (item) => {
      setFocusedItem(item);
    },
    onSelect: (item) => {
      let updatedCurrentPage = item;
      let updatedFocusedKey = focusedItem;
      if (updatedCurrentPage === PREVIOUS_KEY && currentPage > 1) {
        updatedCurrentPage = currentPage - 1;
        if (updatedCurrentPage === 1 && focusedItem === PREVIOUS_KEY) {
          updatedFocusedKey = 1;
        }
      } else if (updatedCurrentPage === NEXT_KEY && currentPage < totalPages) {
        updatedCurrentPage = currentPage + 1;
        if (updatedCurrentPage === totalPages && updatedFocusedKey === NEXT_KEY) {
          updatedFocusedKey = totalPages;
        }
      }
      if (onChange && updatedCurrentPage !== void 0) {
        onChange(updatedCurrentPage);
      }
      setFocusedItem(updatedFocusedKey);
      setInternalCurrentPage(updatedCurrentPage);
    }
  });
  const getTransformedProps = function(pageType) {
    let props = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    let pageNumber = arguments.length > 2 ? arguments[2] : void 0;
    if (transformPageProps) {
      return transformPageProps(pageType, props, pageNumber);
    }
    return props;
  };
  const renderPreviousPage = () => {
    const isFirstPageSelected = totalPages > 0 && currentPage === 1;
    const focusRef = import_react2.default.createRef();
    const props = isFirstPageSelected ? {
      hidden: true
    } : {
      ...getPreviousPageProps({
        "aria-label": "",
        role: null,
        item: PREVIOUS_KEY,
        focusRef
      }),
      "aria-label": void 0
    };
    return import_react2.default.createElement(Previous$1, _extends$4({
      isFocused: focusedItem === PREVIOUS_KEY
    }, getTransformedProps("previous", props), {
      ref: focusRef
    }));
  };
  const renderNextPage = () => {
    const isLastPageSelected = currentPage === totalPages;
    const focusRef = import_react2.default.createRef();
    const props = isLastPageSelected ? {
      hidden: true
    } : {
      ...getNextPageProps({
        "aria-label": "",
        role: null,
        item: NEXT_KEY,
        focusRef
      }),
      "aria-label": void 0
    };
    return import_react2.default.createElement(Next$1, _extends$4({
      isFocused: focusedItem === NEXT_KEY
    }, getTransformedProps("next", props), {
      ref: focusRef
    }));
  };
  const createGap = (pageIndex) => import_react2.default.createElement(Gap, _extends$4({
    key: `gap-${pageIndex}`
  }, getTransformedProps("gap")));
  const createPage = (pageIndex) => {
    const focusRef = import_react2.default.createRef();
    const props = {
      ...getPageProps({
        "aria-label": "",
        role: null,
        item: pageIndex,
        focusRef
      }),
      "aria-label": void 0,
      title: pageIndex
    };
    return import_react2.default.createElement(Page, _extends$4({
      key: pageIndex
    }, getTransformedProps("page", props, pageIndex), {
      ref: focusRef
    }), pageIndex);
  };
  const renderPages = () => {
    const pages = [];
    const PADDING = pagePadding;
    const GAP = pageGap;
    for (let pageIndex = 1; pageIndex <= totalPages; pageIndex++) {
      if (pageIndex === currentPage || pageIndex < GAP || pageIndex > totalPages - GAP + 1) {
        pages.push(createPage(pageIndex));
        continue;
      }
      let minimum;
      let maximum;
      if (currentPage <= GAP + PADDING) {
        minimum = GAP + 1;
        maximum = minimum + PADDING * 2;
      } else if (currentPage >= totalPages - GAP - PADDING) {
        maximum = totalPages - GAP;
        minimum = maximum - PADDING * 2;
      } else {
        minimum = currentPage - PADDING;
        maximum = currentPage + PADDING;
      }
      if (pageIndex >= minimum && pageIndex <= currentPage || pageIndex >= currentPage && pageIndex <= maximum) {
        pages.push(createPage(pageIndex));
        continue;
      }
      if (pageIndex === GAP) {
        if (minimum > GAP + 1 && currentPage > GAP + PADDING + 1) {
          pages.push(createGap(pageIndex));
        } else {
          pages.push(createPage(pageIndex));
        }
        continue;
      }
      if (pageIndex === totalPages - GAP + 1) {
        if (maximum < totalPages - GAP && currentPage < totalPages - GAP - PADDING) {
          pages.push(createGap(pageIndex));
        } else {
          pages.push(createPage(pageIndex));
        }
        continue;
      }
    }
    return pages;
  };
  return import_react2.default.createElement(StyledPagination, _extends$4({}, getContainerProps({
    role: null
  }), otherProps, {
    ref
  }), renderPreviousPage(), totalPages > 0 && renderPages(), renderNextPage());
});
Pagination.propTypes = {
  currentPage: import_prop_types2.default.number.isRequired,
  totalPages: import_prop_types2.default.number.isRequired,
  pagePadding: import_prop_types2.default.number,
  pageGap: import_prop_types2.default.number,
  onChange: import_prop_types2.default.func,
  transformPageProps: import_prop_types2.default.func
};
Pagination.defaultProps = {
  pagePadding: 2,
  pageGap: 2
};
Pagination.displayName = "Pagination";
var _path$1;
function _extends$1() {
  _extends$1 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$1.apply(this, arguments);
}
var SvgChevronDoubleLeftStroke = function SvgChevronDoubleLeftStroke2(props) {
  return React2.createElement("svg", _extends$1({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$1 || (_path$1 = React2.createElement("path", {
    fill: "currentColor",
    d: "M7.812 13.39a.5.5 0 01-.64-.012l-.062-.066-4-5a.5.5 0 01-.054-.542l.054-.082 4-5a.5.5 0 01.83.55l-.05.074L4.141 8l3.75 4.688a.5.5 0 01-.079.702zm5 0a.5.5 0 01-.64-.012l-.062-.066-4-5a.5.5 0 01-.054-.542l.054-.082 4-5a.5.5 0 01.83.55l-.05.074L9.141 8l3.75 4.688a.5.5 0 01-.079.702z"
  })));
};
var FirstComponent = (0, import_react2.forwardRef)((_ref, ref) => {
  let {
    children,
    ...other
  } = _ref;
  return import_react2.default.createElement(StyledCursor, _extends$4({
    ref
  }, other), import_react2.default.createElement(StyledIcon, {
    type: "first"
  }, import_react2.default.createElement(SvgChevronDoubleLeftStroke, null)), import_react2.default.createElement("span", null, children));
});
FirstComponent.displayName = "CursorPagination.First";
var First = FirstComponent;
var NextComponent = (0, import_react2.forwardRef)((_ref, ref) => {
  let {
    children,
    ...other
  } = _ref;
  return import_react2.default.createElement(StyledCursor, _extends$4({
    ref,
    as: "button"
  }, other), import_react2.default.createElement("span", null, children), import_react2.default.createElement(StyledIcon, {
    type: "next"
  }, import_react2.default.createElement(SvgChevronRightStroke, null)));
});
NextComponent.displayName = "CursorPagination.Next";
var Next = NextComponent;
var PreviousComponent = (0, import_react2.forwardRef)((_ref, ref) => {
  let {
    children,
    ...other
  } = _ref;
  return import_react2.default.createElement(StyledCursor, _extends$4({
    ref,
    as: "button"
  }, other), import_react2.default.createElement(StyledIcon, {
    type: "previous"
  }, import_react2.default.createElement(SvgChevronLeftStroke, null)), import_react2.default.createElement("span", null, children));
});
PreviousComponent.displayName = "CursorPagination.Previous";
var Previous = PreviousComponent;
var _path;
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var SvgChevronDoubleRightStroke = function SvgChevronDoubleRightStroke2(props) {
  return React2.createElement("svg", _extends({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path || (_path = React2.createElement("path", {
    fill: "currentColor",
    d: "M8.188 2.61a.5.5 0 01.64.013l.062.065 4 5a.5.5 0 01.054.542l-.054.082-4 5a.5.5 0 01-.83-.55l.05-.074L11.859 8l-3.75-4.688a.5.5 0 01.079-.702zm-5 0a.5.5 0 01.64.013l.062.065 4 5a.5.5 0 01.054.542l-.054.082-4 5a.5.5 0 01-.83-.55l.05-.074L6.859 8l-3.75-4.688a.5.5 0 01.079-.702z"
  })));
};
var LastComponent = (0, import_react2.forwardRef)((_ref, ref) => {
  let {
    children,
    ...other
  } = _ref;
  return import_react2.default.createElement(StyledCursor, _extends$4({
    ref,
    as: "button"
  }, other), import_react2.default.createElement("span", null, children), import_react2.default.createElement(StyledIcon, {
    type: "last"
  }, import_react2.default.createElement(SvgChevronDoubleRightStroke, null)));
});
LastComponent.displayName = "CursorPagination.Last";
var Last = LastComponent;
var CursorPaginationComponent = (0, import_react2.forwardRef)((props, ref) => import_react2.default.createElement(StyledCursorPagination, _extends$4({
  ref
}, props)));
CursorPaginationComponent.displayName = "CursorPagination";
var CursorPagination = CursorPaginationComponent;
CursorPagination.First = First;
CursorPagination.Next = Next;
CursorPagination.Previous = Previous;
CursorPagination.Last = Last;
export {
  CursorPagination,
  Pagination
};
//# sourceMappingURL=@zendeskgarden_react-pagination.js.map
