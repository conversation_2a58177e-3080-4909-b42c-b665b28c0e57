import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/react-uid/dist/es2015/uid.js
var generateUID = function() {
  var counter2 = 1;
  var map = /* @__PURE__ */ new WeakMap();
  var uid2 = function(item, index) {
    if (typeof item === "number" || typeof item === "string") {
      return index ? "idx-" + index : "val-" + item;
    }
    if (!map.has(item)) {
      map.set(item, counter2++);
      return uid2(item);
    }
    return "uid" + map.get(item);
  };
  return uid2;
};
var uid = generateUID();

// node_modules/react-uid/dist/es2015/hooks.js
var React2 = __toESM(require_react());

// node_modules/react-uid/dist/es2015/context.js
var React = __toESM(require_react());
var createSource = function(prefix) {
  if (prefix === void 0) {
    prefix = "";
  }
  return {
    value: 1,
    prefix,
    uid: generateUID()
  };
};
var counter = createSource();
var source = React.createContext(createSource());
var getId = function(source2) {
  return source2.value++;
};
var getPrefix = function(source2) {
  return source2 ? source2.prefix : "";
};

// node_modules/react-uid/dist/es2015/hooks.js
var generateUID2 = function(context) {
  var quartz = context || counter;
  var prefix = getPrefix(quartz);
  var id = getId(quartz);
  var uid2 = prefix + id;
  var gen = function(item) {
    return uid2 + quartz.uid(item);
  };
  return { uid: uid2, gen };
};
var useUIDState = function() {
  if (true) {
    if (!("useContext" in React2)) {
      throw new Error("Hooks API requires React 16.8+");
    }
  }
  return React2.useState(generateUID2(React2.useContext(source)));
};
var useUIDSeed = function() {
  var gen = useUIDState()[0].gen;
  return gen;
};

// node_modules/react-uid/dist/es2015/Control.js
var React4 = __toESM(require_react());

// node_modules/react-uid/node_modules/tslib/tslib.es6.js
var extendStatics = function(d, b) {
  extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
    d2.__proto__ = b2;
  } || function(d2, b2) {
    for (var p in b2)
      if (Object.prototype.hasOwnProperty.call(b2, p))
        d2[p] = b2[p];
  };
  return extendStatics(d, b);
};
function __extends(d, b) {
  if (typeof b !== "function" && b !== null)
    throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
  extendStatics(d, b);
  function __() {
    this.constructor = d;
  }
  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}

// node_modules/react-uid/dist/es2015/UIDComponent.js
var React3 = __toESM(require_react());
var prefixId = function(id, prefix, name) {
  var uid2 = prefix + id;
  return String(name ? name(uid2) : uid2);
};
var UID = (
  /** @class */
  function(_super) {
    __extends(UID2, _super);
    function UID2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.state = {
        quartz: _this.props.idSource || counter,
        prefix: getPrefix(_this.props.idSource),
        id: getId(_this.props.idSource || counter)
      };
      _this.uid = function(item) {
        return prefixId(_this.state.id + "-" + _this.state.quartz.uid(item), _this.state.prefix, _this.props.name);
      };
      return _this;
    }
    UID2.prototype.render = function() {
      var _a = this.props, children = _a.children, name = _a.name;
      var _b = this.state, id = _b.id, prefix = _b.prefix;
      return children(prefixId(id, prefix, name), this.uid);
    };
    return UID2;
  }(React3.Component)
);

export {
  uid,
  useUIDSeed
};
//# sourceMappingURL=chunk-JQPVIOLG.js.map
