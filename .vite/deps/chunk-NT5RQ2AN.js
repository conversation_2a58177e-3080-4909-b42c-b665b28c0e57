import {
  useUIDSeed
} from "./chunk-JQPVIOLG.js";
import {
  KEY_CODES,
  composeEventHandlers,
  getControlledValue
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/container-accordion/dist/index.esm.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
function useAccordion(_temp) {
  let {
    idPrefix,
    expandedSections,
    onChange,
    expandable = true,
    collapsible = true,
    defaultExpandedSections
  } = _temp === void 0 ? {} : _temp;
  const isControlled = expandedSections !== null && expandedSections !== void 0;
  const seed = useUIDSeed();
  const prefix = (0, import_react.useMemo)(() => idPrefix || seed(`accordion_${"2.0.5"}`), [idPrefix, seed]);
  const TRIGGER_ID = `${prefix}--trigger`;
  const PANEL_ID = `${prefix}--panel`;
  const [expandedState, setExpandedState] = (0, import_react.useState)(defaultExpandedSections || [0]);
  const controlledExpandedState = getControlledValue(expandedSections, expandedState);
  const [disabledState, setDisabledState] = (0, import_react.useState)(collapsible ? [] : expandedState);
  const sectionIndices = [];
  const toggle = (index) => {
    const expanded = [];
    const disabled = [];
    sectionIndices.forEach((sectionIndex) => {
      let isExpanded = false;
      if (sectionIndex === index) {
        isExpanded = collapsible ? expandedState.indexOf(sectionIndex) === -1 : true;
      } else if (expandable) {
        isExpanded = expandedState.indexOf(sectionIndex) !== -1;
      }
      if (isExpanded) {
        expanded.push(sectionIndex);
        if (!collapsible) {
          disabled.push(sectionIndex);
        }
      }
    });
    if (onChange) {
      onChange(index);
    }
    if (isControlled === false) {
      setExpandedState(expanded);
    }
    setDisabledState(disabled);
  };
  const getHeaderProps = function(_temp2) {
    let {
      role = "heading",
      ariaLevel,
      ...props
    } = _temp2 === void 0 ? {} : _temp2;
    if (ariaLevel === void 0) {
      throw new Error("Accessibility Error: You must apply the `ariaLevel` prop to the element that contains your heading.");
    }
    return {
      role,
      "aria-level": ariaLevel,
      "data-garden-container-id": "containers.accordion",
      "data-garden-container-version": "2.0.5",
      ...props
    };
  };
  const getTriggerProps = function(_temp3) {
    let {
      index,
      role = "button",
      tabIndex = 0,
      ...props
    } = _temp3 === void 0 ? {} : _temp3;
    if (index === void 0) {
      throw new Error("Accessibility Error: You must provide an `index` option to `getTriggerProps()`");
    }
    sectionIndices.push(index);
    return {
      id: `${TRIGGER_ID}:${index}`,
      role,
      tabIndex,
      "aria-controls": `${PANEL_ID}:${index}`,
      "aria-disabled": disabledState.indexOf(index) !== -1,
      "aria-expanded": isControlled ? controlledExpandedState.includes(index) : expandedState.includes(index),
      onClick: composeEventHandlers(props.onClick, () => toggle(index)),
      onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {
        if (event.keyCode === KEY_CODES.SPACE || event.keyCode === KEY_CODES.ENTER) {
          toggle(index);
          event.preventDefault();
        }
      }),
      ...props
    };
  };
  const getPanelProps = function(_temp4) {
    let {
      index,
      role = "region",
      ...props
    } = _temp4 === void 0 ? {} : _temp4;
    if (index === void 0) {
      throw new Error("Accessibility Error: You must provide an `index` option to `getSectionProps()`");
    }
    return {
      id: `${PANEL_ID}:${index}`,
      role,
      "aria-hidden": isControlled ? !controlledExpandedState.includes(index) : !expandedState.includes(index),
      "aria-labelledby": `${TRIGGER_ID}:${index}`,
      ...props
    };
  };
  return {
    getHeaderProps,
    getTriggerProps,
    getPanelProps,
    expandedSections: controlledExpandedState,
    disabledSections: disabledState
  };
}
var AccordionContainer = (props) => {
  const {
    children,
    render = children,
    ...options
  } = props;
  return import_react.default.createElement(import_react.default.Fragment, null, render(useAccordion(options)));
};
AccordionContainer.defaultProps = {
  expandable: true,
  collapsible: true
};
AccordionContainer.propTypes = {
  children: import_prop_types.default.func,
  render: import_prop_types.default.func,
  expandedSections: import_prop_types.default.array,
  defaultExpandedSections: import_prop_types.default.array,
  expandable: import_prop_types.default.bool,
  collapsible: import_prop_types.default.bool,
  idPrefix: import_prop_types.default.string,
  onChange: import_prop_types.default.func
};

export {
  useAccordion
};
//# sourceMappingURL=chunk-NT5RQ2AN.js.map
