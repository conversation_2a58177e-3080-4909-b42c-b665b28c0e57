import {
  require_warning
} from "./chunk-NWUESYL6.js";
import {
  react_merge_refs_esm_default
} from "./chunk-JLWORYXM.js";
import {
  CSSTransition_default
} from "./chunk-X5YXPSWX.js";
import "./chunk-PSGUSLG5.js";
import {
  DEFAULT_THEME,
  arrowStyles,
  getColor,
  getLineHeight,
  mediaQuery,
  menuStyles,
  retrieveComponentStyles,
  useDocument,
  useFocusVisible,
  useText
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import {
  KEYS,
  composeEventHandlers,
  useId
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  Ae,
  Me,
  We,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react_dom
} from "./chunk-JHQZW6XF.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __commonJS,
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/react-fast-compare/index.js
var require_react_fast_compare = __commonJS({
  "node_modules/react-fast-compare/index.js"(exports, module) {
    var hasElementType = typeof Element !== "undefined";
    var hasMap = typeof Map === "function";
    var hasSet = typeof Set === "function";
    var hasArrayBuffer = typeof ArrayBuffer === "function" && !!ArrayBuffer.isView;
    function equal(a, b) {
      if (a === b)
        return true;
      if (a && b && typeof a == "object" && typeof b == "object") {
        if (a.constructor !== b.constructor)
          return false;
        var length, i, keys;
        if (Array.isArray(a)) {
          length = a.length;
          if (length != b.length)
            return false;
          for (i = length; i-- !== 0; )
            if (!equal(a[i], b[i]))
              return false;
          return true;
        }
        var it;
        if (hasMap && a instanceof Map && b instanceof Map) {
          if (a.size !== b.size)
            return false;
          it = a.entries();
          while (!(i = it.next()).done)
            if (!b.has(i.value[0]))
              return false;
          it = a.entries();
          while (!(i = it.next()).done)
            if (!equal(i.value[1], b.get(i.value[0])))
              return false;
          return true;
        }
        if (hasSet && a instanceof Set && b instanceof Set) {
          if (a.size !== b.size)
            return false;
          it = a.entries();
          while (!(i = it.next()).done)
            if (!b.has(i.value[0]))
              return false;
          return true;
        }
        if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {
          length = a.length;
          if (length != b.length)
            return false;
          for (i = length; i-- !== 0; )
            if (a[i] !== b[i])
              return false;
          return true;
        }
        if (a.constructor === RegExp)
          return a.source === b.source && a.flags === b.flags;
        if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === "function" && typeof b.valueOf === "function")
          return a.valueOf() === b.valueOf();
        if (a.toString !== Object.prototype.toString && typeof a.toString === "function" && typeof b.toString === "function")
          return a.toString() === b.toString();
        keys = Object.keys(a);
        length = keys.length;
        if (length !== Object.keys(b).length)
          return false;
        for (i = length; i-- !== 0; )
          if (!Object.prototype.hasOwnProperty.call(b, keys[i]))
            return false;
        if (hasElementType && a instanceof Element)
          return false;
        for (i = length; i-- !== 0; ) {
          if ((keys[i] === "_owner" || keys[i] === "__v" || keys[i] === "__o") && a.$$typeof) {
            continue;
          }
          if (!equal(a[keys[i]], b[keys[i]]))
            return false;
        }
        return true;
      }
      return a !== a && b !== b;
    }
    module.exports = function isEqual2(a, b) {
      try {
        return equal(a, b);
      } catch (error) {
        if ((error.message || "").match(/stack|recursion/i)) {
          console.warn("react-fast-compare cannot handle circular refs");
          return false;
        }
        throw error;
      }
    };
  }
});

// node_modules/@zendeskgarden/react-modals/dist/index.esm.js
var React8 = __toESM(require_react());
var import_react3 = __toESM(require_react());
var import_react_dom = __toESM(require_react_dom());
var import_prop_types3 = __toESM(require_prop_types());

// node_modules/@zendeskgarden/container-modal/dist/index.esm.js
var import_react2 = __toESM(require_react());

// node_modules/@zendeskgarden/container-focusjail/dist/index.esm.js
var import_react = __toESM(require_react());

// node_modules/tabbable/dist/index.esm.js
var candidateSelectors = ["input:not([inert])", "select:not([inert])", "textarea:not([inert])", "a[href]:not([inert])", "button:not([inert])", "[tabindex]:not(slot):not([inert])", "audio[controls]:not([inert])", "video[controls]:not([inert])", '[contenteditable]:not([contenteditable="false"]):not([inert])', "details>summary:first-of-type:not([inert])", "details:not([inert])"];
var candidateSelector = candidateSelectors.join(",");
var NoElement = typeof Element === "undefined";
var matches = NoElement ? function() {
} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
var getRootNode = !NoElement && Element.prototype.getRootNode ? function(element) {
  var _element$getRootNode;
  return element === null || element === void 0 ? void 0 : (_element$getRootNode = element.getRootNode) === null || _element$getRootNode === void 0 ? void 0 : _element$getRootNode.call(element);
} : function(element) {
  return element === null || element === void 0 ? void 0 : element.ownerDocument;
};
var isInert = function isInert2(node, lookUp) {
  var _node$getAttribute;
  if (lookUp === void 0) {
    lookUp = true;
  }
  var inertAtt = node === null || node === void 0 ? void 0 : (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, "inert");
  var inert = inertAtt === "" || inertAtt === "true";
  var result = inert || lookUp && node && isInert2(node.parentNode);
  return result;
};
var isContentEditable = function isContentEditable2(node) {
  var _node$getAttribute2;
  var attValue = node === null || node === void 0 ? void 0 : (_node$getAttribute2 = node.getAttribute) === null || _node$getAttribute2 === void 0 ? void 0 : _node$getAttribute2.call(node, "contenteditable");
  return attValue === "" || attValue === "true";
};
var getCandidates = function getCandidates2(el, includeContainer, filter) {
  if (isInert(el)) {
    return [];
  }
  var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));
  if (includeContainer && matches.call(el, candidateSelector)) {
    candidates.unshift(el);
  }
  candidates = candidates.filter(filter);
  return candidates;
};
var getCandidatesIteratively = function getCandidatesIteratively2(elements, includeContainer, options) {
  var candidates = [];
  var elementsToCheck = Array.from(elements);
  while (elementsToCheck.length) {
    var element = elementsToCheck.shift();
    if (isInert(element, false)) {
      continue;
    }
    if (element.tagName === "SLOT") {
      var assigned = element.assignedElements();
      var content = assigned.length ? assigned : element.children;
      var nestedCandidates = getCandidatesIteratively2(content, true, options);
      if (options.flatten) {
        candidates.push.apply(candidates, nestedCandidates);
      } else {
        candidates.push({
          scopeParent: element,
          candidates: nestedCandidates
        });
      }
    } else {
      var validCandidate = matches.call(element, candidateSelector);
      if (validCandidate && options.filter(element) && (includeContainer || !elements.includes(element))) {
        candidates.push(element);
      }
      var shadowRoot = element.shadowRoot || // check for an undisclosed shadow
      typeof options.getShadowRoot === "function" && options.getShadowRoot(element);
      var validShadowRoot = !isInert(shadowRoot, false) && (!options.shadowRootFilter || options.shadowRootFilter(element));
      if (shadowRoot && validShadowRoot) {
        var _nestedCandidates = getCandidatesIteratively2(shadowRoot === true ? element.children : shadowRoot.children, true, options);
        if (options.flatten) {
          candidates.push.apply(candidates, _nestedCandidates);
        } else {
          candidates.push({
            scopeParent: element,
            candidates: _nestedCandidates
          });
        }
      } else {
        elementsToCheck.unshift.apply(elementsToCheck, element.children);
      }
    }
  }
  return candidates;
};
var getTabindex = function getTabindex2(node, isScope) {
  if (node.tabIndex < 0) {
    if ((isScope || /^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) || isContentEditable(node)) && isNaN(parseInt(node.getAttribute("tabindex"), 10))) {
      return 0;
    }
  }
  return node.tabIndex;
};
var sortOrderedTabbables = function sortOrderedTabbables2(a, b) {
  return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;
};
var isInput = function isInput2(node) {
  return node.tagName === "INPUT";
};
var isHiddenInput = function isHiddenInput2(node) {
  return isInput(node) && node.type === "hidden";
};
var isDetailsWithSummary = function isDetailsWithSummary2(node) {
  var r = node.tagName === "DETAILS" && Array.prototype.slice.apply(node.children).some(function(child) {
    return child.tagName === "SUMMARY";
  });
  return r;
};
var getCheckedRadio = function getCheckedRadio2(nodes, form) {
  for (var i = 0; i < nodes.length; i++) {
    if (nodes[i].checked && nodes[i].form === form) {
      return nodes[i];
    }
  }
};
var isTabbableRadio = function isTabbableRadio2(node) {
  if (!node.name) {
    return true;
  }
  var radioScope = node.form || getRootNode(node);
  var queryRadios = function queryRadios2(name) {
    return radioScope.querySelectorAll('input[type="radio"][name="' + name + '"]');
  };
  var radioSet;
  if (typeof window !== "undefined" && typeof window.CSS !== "undefined" && typeof window.CSS.escape === "function") {
    radioSet = queryRadios(window.CSS.escape(node.name));
  } else {
    try {
      radioSet = queryRadios(node.name);
    } catch (err) {
      console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s", err.message);
      return false;
    }
  }
  var checked = getCheckedRadio(radioSet, node.form);
  return !checked || checked === node;
};
var isRadio = function isRadio2(node) {
  return isInput(node) && node.type === "radio";
};
var isNonTabbableRadio = function isNonTabbableRadio2(node) {
  return isRadio(node) && !isTabbableRadio(node);
};
var isNodeAttached = function isNodeAttached2(node) {
  var _nodeRoot;
  var nodeRoot = node && getRootNode(node);
  var nodeRootHost = (_nodeRoot = nodeRoot) === null || _nodeRoot === void 0 ? void 0 : _nodeRoot.host;
  var attached = false;
  if (nodeRoot && nodeRoot !== node) {
    var _nodeRootHost, _nodeRootHost$ownerDo, _node$ownerDocument;
    attached = !!((_nodeRootHost = nodeRootHost) !== null && _nodeRootHost !== void 0 && (_nodeRootHost$ownerDo = _nodeRootHost.ownerDocument) !== null && _nodeRootHost$ownerDo !== void 0 && _nodeRootHost$ownerDo.contains(nodeRootHost) || node !== null && node !== void 0 && (_node$ownerDocument = node.ownerDocument) !== null && _node$ownerDocument !== void 0 && _node$ownerDocument.contains(node));
    while (!attached && nodeRootHost) {
      var _nodeRoot2, _nodeRootHost2, _nodeRootHost2$ownerD;
      nodeRoot = getRootNode(nodeRootHost);
      nodeRootHost = (_nodeRoot2 = nodeRoot) === null || _nodeRoot2 === void 0 ? void 0 : _nodeRoot2.host;
      attached = !!((_nodeRootHost2 = nodeRootHost) !== null && _nodeRootHost2 !== void 0 && (_nodeRootHost2$ownerD = _nodeRootHost2.ownerDocument) !== null && _nodeRootHost2$ownerD !== void 0 && _nodeRootHost2$ownerD.contains(nodeRootHost));
    }
  }
  return attached;
};
var isZeroArea = function isZeroArea2(node) {
  var _node$getBoundingClie = node.getBoundingClientRect(), width = _node$getBoundingClie.width, height = _node$getBoundingClie.height;
  return width === 0 && height === 0;
};
var isHidden = function isHidden2(node, _ref) {
  var displayCheck = _ref.displayCheck, getShadowRoot = _ref.getShadowRoot;
  if (getComputedStyle(node).visibility === "hidden") {
    return true;
  }
  var isDirectSummary = matches.call(node, "details>summary:first-of-type");
  var nodeUnderDetails = isDirectSummary ? node.parentElement : node;
  if (matches.call(nodeUnderDetails, "details:not([open]) *")) {
    return true;
  }
  if (!displayCheck || displayCheck === "full" || displayCheck === "legacy-full") {
    if (typeof getShadowRoot === "function") {
      var originalNode = node;
      while (node) {
        var parentElement = node.parentElement;
        var rootNode = getRootNode(node);
        if (parentElement && !parentElement.shadowRoot && getShadowRoot(parentElement) === true) {
          return isZeroArea(node);
        } else if (node.assignedSlot) {
          node = node.assignedSlot;
        } else if (!parentElement && rootNode !== node.ownerDocument) {
          node = rootNode.host;
        } else {
          node = parentElement;
        }
      }
      node = originalNode;
    }
    if (isNodeAttached(node)) {
      return !node.getClientRects().length;
    }
    if (displayCheck !== "legacy-full") {
      return true;
    }
  } else if (displayCheck === "non-zero-area") {
    return isZeroArea(node);
  }
  return false;
};
var isDisabledFromFieldset = function isDisabledFromFieldset2(node) {
  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {
    var parentNode = node.parentElement;
    while (parentNode) {
      if (parentNode.tagName === "FIELDSET" && parentNode.disabled) {
        for (var i = 0; i < parentNode.children.length; i++) {
          var child = parentNode.children.item(i);
          if (child.tagName === "LEGEND") {
            return matches.call(parentNode, "fieldset[disabled] *") ? true : !child.contains(node);
          }
        }
        return true;
      }
      parentNode = parentNode.parentElement;
    }
  }
  return false;
};
var isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable2(options, node) {
  if (node.disabled || // we must do an inert look up to filter out any elements inside an inert ancestor
  //  because we're limited in the type of selectors we can use in JSDom (see related
  //  note related to `candidateSelectors`)
  isInert(node) || isHiddenInput(node) || isHidden(node, options) || // For a details element with a summary, the summary element gets the focus
  isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {
    return false;
  }
  return true;
};
var isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable2(options, node) {
  if (isNonTabbableRadio(node) || getTabindex(node) < 0 || !isNodeMatchingSelectorFocusable(options, node)) {
    return false;
  }
  return true;
};
var isValidShadowRootTabbable = function isValidShadowRootTabbable2(shadowHostNode) {
  var tabIndex = parseInt(shadowHostNode.getAttribute("tabindex"), 10);
  if (isNaN(tabIndex) || tabIndex >= 0) {
    return true;
  }
  return false;
};
var sortByOrder = function sortByOrder2(candidates) {
  var regularTabbables = [];
  var orderedTabbables = [];
  candidates.forEach(function(item, i) {
    var isScope = !!item.scopeParent;
    var element = isScope ? item.scopeParent : item;
    var candidateTabindex = getTabindex(element, isScope);
    var elements = isScope ? sortByOrder2(item.candidates) : element;
    if (candidateTabindex === 0) {
      isScope ? regularTabbables.push.apply(regularTabbables, elements) : regularTabbables.push(element);
    } else {
      orderedTabbables.push({
        documentOrder: i,
        tabIndex: candidateTabindex,
        item,
        isScope,
        content: elements
      });
    }
  });
  return orderedTabbables.sort(sortOrderedTabbables).reduce(function(acc, sortable) {
    sortable.isScope ? acc.push.apply(acc, sortable.content) : acc.push(sortable.content);
    return acc;
  }, []).concat(regularTabbables);
};
var tabbable = function tabbable2(el, options) {
  options = options || {};
  var candidates;
  if (options.getShadowRoot) {
    candidates = getCandidatesIteratively([el], options.includeContainer, {
      filter: isNodeMatchingSelectorTabbable.bind(null, options),
      flatten: false,
      getShadowRoot: options.getShadowRoot,
      shadowRootFilter: isValidShadowRootTabbable
    });
  } else {
    candidates = getCandidates(el, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));
  }
  return sortByOrder(candidates);
};
var focusableCandidateSelector = candidateSelectors.concat("iframe").join(",");

// node_modules/@zendeskgarden/container-focusjail/dist/index.esm.js
var import_prop_types = __toESM(require_prop_types());
function ownerDocument(node) {
  return node && node.ownerDocument || document;
}
function activeElement(doc) {
  if (doc === void 0) {
    doc = ownerDocument();
  }
  try {
    var active = doc.activeElement;
    if (!active || !active.nodeName)
      return null;
    return active;
  } catch (e) {
    return doc.body;
  }
}
var useFocusJail = function(_temp) {
  let {
    focusOnMount = true,
    restoreFocus = true,
    environment,
    focusElem,
    containerRef
  } = _temp === void 0 ? {
    containerRef: (0, import_react.createRef)()
  } : _temp;
  const restoreFocusElement = (0, import_react.useRef)(null);
  const [currentRef, setCurrentRef] = (0, import_react.useState)(containerRef.current);
  (0, import_react.useEffect)(() => {
    if (containerRef.current !== currentRef) {
      setCurrentRef(containerRef.current);
    }
  });
  const focusElement = (0, import_react.useCallback)((element) => {
    if (focusElem) {
      focusElem(element);
    } else {
      element && element.focus();
    }
  }, [focusElem]);
  const validateContainerRef = () => {
    if (!currentRef) {
      throw new Error("Accessibility Error: You must apply the ref prop to your containing element.");
    }
  };
  const getInitialFocusNode = () => {
    const doc = environment ? environment : document;
    const activeElem = activeElement(doc);
    const containerElem = currentRef;
    return containerElem.contains(activeElem) ? activeElem : containerElem;
  };
  const getTabbableNodes = () => {
    const elements = tabbable(currentRef);
    return {
      firstItem: elements[0] || getInitialFocusNode(),
      lastItem: elements[elements.length - 1] || getInitialFocusNode()
    };
  };
  const getContainerProps = function(_temp2) {
    let {
      onKeyDown,
      ...other
    } = _temp2 === void 0 ? {} : _temp2;
    return {
      onKeyDown: composeEventHandlers(onKeyDown, (event) => {
        if (event.key !== KEYS.TAB) {
          return;
        }
        validateContainerRef();
        const tabbableNodes = getTabbableNodes();
        if (event.shiftKey && (event.target === tabbableNodes.firstItem || event.target === currentRef)) {
          focusElement(tabbableNodes.lastItem);
          event.preventDefault();
        }
        if (!event.shiftKey && event.target === tabbableNodes.lastItem) {
          focusElement(tabbableNodes.firstItem);
          event.preventDefault();
        }
      }),
      "data-garden-container-id": "containers.focusjail",
      "data-garden-container-version": "2.0.5",
      ...other
    };
  };
  (0, import_react.useEffect)(() => {
    const doc = environment || document;
    restoreFocusElement.current = activeElement(doc);
    if (focusOnMount) {
      focusElement(currentRef);
    }
    return () => {
      const isBodyInactive = restoreFocusElement.current !== doc.body;
      const hasActiveElement = restoreFocusElement.current !== null;
      if (isBodyInactive && hasActiveElement && restoreFocus) {
        focusElement(restoreFocusElement.current);
      }
    };
  }, [focusOnMount, restoreFocus, environment, focusElement, currentRef]);
  return {
    getContainerProps,
    focusElement
  };
};
var FocusJailContainer = (_ref) => {
  let {
    children,
    render = children,
    ...options
  } = _ref;
  return import_react.default.createElement(import_react.default.Fragment, null, render(useFocusJail(options)));
};
FocusJailContainer.propTypes = {
  children: import_prop_types.default.func,
  render: import_prop_types.default.func,
  focusOnMount: import_prop_types.default.bool,
  restoreFocus: import_prop_types.default.bool,
  environment: import_prop_types.default.any,
  containerRef: import_prop_types.default.any.isRequired,
  focusElem: import_prop_types.default.func
};
FocusJailContainer.defaultProps = {
  focusOnMount: true,
  restoreFocus: true
};

// node_modules/@zendeskgarden/container-modal/dist/index.esm.js
var import_prop_types2 = __toESM(require_prop_types());
var useModal = (_ref) => {
  let {
    onClose,
    modalRef,
    idPrefix,
    focusOnMount,
    restoreFocus,
    environment
  } = _ref;
  const prefix = useId(idPrefix);
  const titleId = `${prefix}__title`;
  const contentId = `${prefix}__content`;
  const isModalMousedDownRef = (0, import_react2.useRef)(false);
  const closeModal = (event) => {
    onClose && onClose(event);
  };
  const getBackdropProps = function(_temp) {
    let {
      onMouseUp,
      ...other
    } = _temp === void 0 ? {} : _temp;
    const containerId = "containers.modal";
    const handleMouseUp = (event) => {
      const target = event.target;
      const isModalContainer = containerId === target.getAttribute("data-garden-container-id");
      if (!isModalMousedDownRef.current && isModalContainer) {
        closeModal(event);
      }
      isModalMousedDownRef.current = false;
    };
    return {
      onMouseUp: composeEventHandlers(onMouseUp, handleMouseUp),
      "data-garden-container-id": containerId,
      "data-garden-container-version": "1.0.5",
      ...other
    };
  };
  const getModalProps = function(_temp2) {
    let {
      role = "dialog",
      onKeyDown,
      onMouseDown,
      ...other
    } = _temp2 === void 0 ? {} : _temp2;
    return {
      role: role === null ? void 0 : role,
      tabIndex: -1,
      "aria-modal": true,
      "aria-labelledby": titleId,
      "aria-describedby": contentId,
      onMouseDown: composeEventHandlers(onMouseDown, () => {
        isModalMousedDownRef.current = true;
      }),
      onKeyDown: composeEventHandlers(onKeyDown, (event) => {
        if (event.key === KEYS.ESCAPE) {
          closeModal(event);
        }
      }),
      ...other
    };
  };
  const getTitleProps = function(_temp3) {
    let {
      id = titleId,
      ...other
    } = _temp3 === void 0 ? {} : _temp3;
    return {
      id,
      ...other
    };
  };
  const getContentProps = function(_temp4) {
    let {
      id = contentId,
      ...other
    } = _temp4 === void 0 ? {} : _temp4;
    return {
      id,
      ...other
    };
  };
  const getCloseProps = (_ref2) => {
    let {
      onClick,
      ...other
    } = _ref2;
    return {
      onClick: composeEventHandlers(onClick, (event) => {
        closeModal(event);
      }),
      ...other
    };
  };
  const {
    getContainerProps
  } = useFocusJail({
    containerRef: modalRef,
    focusOnMount,
    restoreFocus,
    environment
  });
  return {
    getBackdropProps,
    getModalProps: (props) => getContainerProps(getModalProps(props)),
    getTitleProps,
    getContentProps,
    getCloseProps,
    closeModal
  };
};
var ModalContainer = (_ref) => {
  let {
    children,
    render = children,
    ...options
  } = _ref;
  return import_react2.default.createElement(import_react2.default.Fragment, null, render(useModal(options)));
};
ModalContainer.propTypes = {
  children: import_prop_types2.default.func,
  render: import_prop_types2.default.func,
  onClose: import_prop_types2.default.func,
  modalRef: import_prop_types2.default.any.isRequired,
  idPrefix: import_prop_types2.default.string,
  focusOnMount: import_prop_types2.default.bool,
  restoreFocus: import_prop_types2.default.bool,
  environment: import_prop_types2.default.any
};
ModalContainer.defaultProps = {
  focusOnMount: true,
  restoreFocus: true
};

// node_modules/react-popper/lib/esm/Popper.js
var React6 = __toESM(require_react());

// node_modules/react-popper/lib/esm/Manager.js
var React3 = __toESM(require_react());
var ManagerReferenceNodeContext = React3.createContext();
var ManagerReferenceNodeSetterContext = React3.createContext();

// node_modules/react-popper/lib/esm/utils.js
var React4 = __toESM(require_react());
var fromEntries = function fromEntries2(entries) {
  return entries.reduce(function(acc, _ref) {
    var key = _ref[0], value = _ref[1];
    acc[key] = value;
    return acc;
  }, {});
};
var useIsomorphicLayoutEffect = typeof window !== "undefined" && window.document && window.document.createElement ? React4.useLayoutEffect : React4.useEffect;

// node_modules/react-popper/lib/esm/usePopper.js
var React5 = __toESM(require_react());
var ReactDOM = __toESM(require_react_dom());

// node_modules/@popperjs/core/lib/enums.js
var top = "top";
var bottom = "bottom";
var right = "right";
var left = "left";
var auto = "auto";
var basePlacements = [top, bottom, right, left];
var start = "start";
var end = "end";
var clippingParents = "clippingParents";
var viewport = "viewport";
var popper = "popper";
var reference = "reference";
var variationPlacements = basePlacements.reduce(function(acc, placement) {
  return acc.concat([placement + "-" + start, placement + "-" + end]);
}, []);
var placements = [].concat(basePlacements, [auto]).reduce(function(acc, placement) {
  return acc.concat([placement, placement + "-" + start, placement + "-" + end]);
}, []);
var beforeRead = "beforeRead";
var read = "read";
var afterRead = "afterRead";
var beforeMain = "beforeMain";
var main = "main";
var afterMain = "afterMain";
var beforeWrite = "beforeWrite";
var write = "write";
var afterWrite = "afterWrite";
var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];

// node_modules/@popperjs/core/lib/dom-utils/getNodeName.js
function getNodeName(element) {
  return element ? (element.nodeName || "").toLowerCase() : null;
}

// node_modules/@popperjs/core/lib/dom-utils/getWindow.js
function getWindow(node) {
  if (node == null) {
    return window;
  }
  if (node.toString() !== "[object Window]") {
    var ownerDocument3 = node.ownerDocument;
    return ownerDocument3 ? ownerDocument3.defaultView || window : window;
  }
  return node;
}

// node_modules/@popperjs/core/lib/dom-utils/instanceOf.js
function isElement(node) {
  var OwnElement = getWindow(node).Element;
  return node instanceof OwnElement || node instanceof Element;
}
function isHTMLElement(node) {
  var OwnElement = getWindow(node).HTMLElement;
  return node instanceof OwnElement || node instanceof HTMLElement;
}
function isShadowRoot(node) {
  if (typeof ShadowRoot === "undefined") {
    return false;
  }
  var OwnElement = getWindow(node).ShadowRoot;
  return node instanceof OwnElement || node instanceof ShadowRoot;
}

// node_modules/@popperjs/core/lib/modifiers/applyStyles.js
function applyStyles(_ref) {
  var state = _ref.state;
  Object.keys(state.elements).forEach(function(name) {
    var style2 = state.styles[name] || {};
    var attributes = state.attributes[name] || {};
    var element = state.elements[name];
    if (!isHTMLElement(element) || !getNodeName(element)) {
      return;
    }
    Object.assign(element.style, style2);
    Object.keys(attributes).forEach(function(name2) {
      var value = attributes[name2];
      if (value === false) {
        element.removeAttribute(name2);
      } else {
        element.setAttribute(name2, value === true ? "" : value);
      }
    });
  });
}
function effect(_ref2) {
  var state = _ref2.state;
  var initialStyles = {
    popper: {
      position: state.options.strategy,
      left: "0",
      top: "0",
      margin: "0"
    },
    arrow: {
      position: "absolute"
    },
    reference: {}
  };
  Object.assign(state.elements.popper.style, initialStyles.popper);
  state.styles = initialStyles;
  if (state.elements.arrow) {
    Object.assign(state.elements.arrow.style, initialStyles.arrow);
  }
  return function() {
    Object.keys(state.elements).forEach(function(name) {
      var element = state.elements[name];
      var attributes = state.attributes[name] || {};
      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]);
      var style2 = styleProperties.reduce(function(style3, property) {
        style3[property] = "";
        return style3;
      }, {});
      if (!isHTMLElement(element) || !getNodeName(element)) {
        return;
      }
      Object.assign(element.style, style2);
      Object.keys(attributes).forEach(function(attribute) {
        element.removeAttribute(attribute);
      });
    });
  };
}
var applyStyles_default = {
  name: "applyStyles",
  enabled: true,
  phase: "write",
  fn: applyStyles,
  effect,
  requires: ["computeStyles"]
};

// node_modules/@popperjs/core/lib/utils/getBasePlacement.js
function getBasePlacement(placement) {
  return placement.split("-")[0];
}

// node_modules/@popperjs/core/lib/utils/math.js
var max = Math.max;
var min = Math.min;
var round = Math.round;

// node_modules/@popperjs/core/lib/utils/userAgent.js
function getUAString() {
  var uaData = navigator.userAgentData;
  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {
    return uaData.brands.map(function(item) {
      return item.brand + "/" + item.version;
    }).join(" ");
  }
  return navigator.userAgent;
}

// node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js
function isLayoutViewport() {
  return !/^((?!chrome|android).)*safari/i.test(getUAString());
}

// node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js
function getBoundingClientRect(element, includeScale, isFixedStrategy) {
  if (includeScale === void 0) {
    includeScale = false;
  }
  if (isFixedStrategy === void 0) {
    isFixedStrategy = false;
  }
  var clientRect = element.getBoundingClientRect();
  var scaleX = 1;
  var scaleY = 1;
  if (includeScale && isHTMLElement(element)) {
    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;
    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;
  }
  var _ref = isElement(element) ? getWindow(element) : window, visualViewport = _ref.visualViewport;
  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;
  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;
  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;
  var width = clientRect.width / scaleX;
  var height = clientRect.height / scaleY;
  return {
    width,
    height,
    top: y,
    right: x + width,
    bottom: y + height,
    left: x,
    x,
    y
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js
function getLayoutRect(element) {
  var clientRect = getBoundingClientRect(element);
  var width = element.offsetWidth;
  var height = element.offsetHeight;
  if (Math.abs(clientRect.width - width) <= 1) {
    width = clientRect.width;
  }
  if (Math.abs(clientRect.height - height) <= 1) {
    height = clientRect.height;
  }
  return {
    x: element.offsetLeft,
    y: element.offsetTop,
    width,
    height
  };
}

// node_modules/@popperjs/core/lib/dom-utils/contains.js
function contains(parent, child) {
  var rootNode = child.getRootNode && child.getRootNode();
  if (parent.contains(child)) {
    return true;
  } else if (rootNode && isShadowRoot(rootNode)) {
    var next = child;
    do {
      if (next && parent.isSameNode(next)) {
        return true;
      }
      next = next.parentNode || next.host;
    } while (next);
  }
  return false;
}

// node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js
function getComputedStyle2(element) {
  return getWindow(element).getComputedStyle(element);
}

// node_modules/@popperjs/core/lib/dom-utils/isTableElement.js
function isTableElement(element) {
  return ["table", "td", "th"].indexOf(getNodeName(element)) >= 0;
}

// node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js
function getDocumentElement(element) {
  return ((isElement(element) ? element.ownerDocument : (
    // $FlowFixMe[prop-missing]
    element.document
  )) || window.document).documentElement;
}

// node_modules/@popperjs/core/lib/dom-utils/getParentNode.js
function getParentNode(element) {
  if (getNodeName(element) === "html") {
    return element;
  }
  return (
    // this is a quicker (but less type safe) way to save quite some bytes from the bundle
    // $FlowFixMe[incompatible-return]
    // $FlowFixMe[prop-missing]
    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node
    element.parentNode || // DOM Element detected
    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected
    // $FlowFixMe[incompatible-call]: HTMLElement is a Node
    getDocumentElement(element)
  );
}

// node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js
function getTrueOffsetParent(element) {
  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837
  getComputedStyle2(element).position === "fixed") {
    return null;
  }
  return element.offsetParent;
}
function getContainingBlock(element) {
  var isFirefox = /firefox/i.test(getUAString());
  var isIE = /Trident/i.test(getUAString());
  if (isIE && isHTMLElement(element)) {
    var elementCss = getComputedStyle2(element);
    if (elementCss.position === "fixed") {
      return null;
    }
  }
  var currentNode = getParentNode(element);
  if (isShadowRoot(currentNode)) {
    currentNode = currentNode.host;
  }
  while (isHTMLElement(currentNode) && ["html", "body"].indexOf(getNodeName(currentNode)) < 0) {
    var css = getComputedStyle2(currentNode);
    if (css.transform !== "none" || css.perspective !== "none" || css.contain === "paint" || ["transform", "perspective"].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === "filter" || isFirefox && css.filter && css.filter !== "none") {
      return currentNode;
    } else {
      currentNode = currentNode.parentNode;
    }
  }
  return null;
}
function getOffsetParent(element) {
  var window2 = getWindow(element);
  var offsetParent = getTrueOffsetParent(element);
  while (offsetParent && isTableElement(offsetParent) && getComputedStyle2(offsetParent).position === "static") {
    offsetParent = getTrueOffsetParent(offsetParent);
  }
  if (offsetParent && (getNodeName(offsetParent) === "html" || getNodeName(offsetParent) === "body" && getComputedStyle2(offsetParent).position === "static")) {
    return window2;
  }
  return offsetParent || getContainingBlock(element) || window2;
}

// node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js
function getMainAxisFromPlacement(placement) {
  return ["top", "bottom"].indexOf(placement) >= 0 ? "x" : "y";
}

// node_modules/@popperjs/core/lib/utils/within.js
function within(min2, value, max2) {
  return max(min2, min(value, max2));
}
function withinMaxClamp(min2, value, max2) {
  var v = within(min2, value, max2);
  return v > max2 ? max2 : v;
}

// node_modules/@popperjs/core/lib/utils/getFreshSideObject.js
function getFreshSideObject() {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  };
}

// node_modules/@popperjs/core/lib/utils/mergePaddingObject.js
function mergePaddingObject(paddingObject) {
  return Object.assign({}, getFreshSideObject(), paddingObject);
}

// node_modules/@popperjs/core/lib/utils/expandToHashMap.js
function expandToHashMap(value, keys) {
  return keys.reduce(function(hashMap, key) {
    hashMap[key] = value;
    return hashMap;
  }, {});
}

// node_modules/@popperjs/core/lib/modifiers/arrow.js
var toPaddingObject = function toPaddingObject2(padding, state) {
  padding = typeof padding === "function" ? padding(Object.assign({}, state.rects, {
    placement: state.placement
  })) : padding;
  return mergePaddingObject(typeof padding !== "number" ? padding : expandToHashMap(padding, basePlacements));
};
function arrow(_ref) {
  var _state$modifiersData$;
  var state = _ref.state, name = _ref.name, options = _ref.options;
  var arrowElement = state.elements.arrow;
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var basePlacement = getBasePlacement(state.placement);
  var axis = getMainAxisFromPlacement(basePlacement);
  var isVertical = [left, right].indexOf(basePlacement) >= 0;
  var len = isVertical ? "height" : "width";
  if (!arrowElement || !popperOffsets2) {
    return;
  }
  var paddingObject = toPaddingObject(options.padding, state);
  var arrowRect = getLayoutRect(arrowElement);
  var minProp = axis === "y" ? top : left;
  var maxProp = axis === "y" ? bottom : right;
  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets2[axis] - state.rects.popper[len];
  var startDiff = popperOffsets2[axis] - state.rects.reference[axis];
  var arrowOffsetParent = getOffsetParent(arrowElement);
  var clientSize = arrowOffsetParent ? axis === "y" ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;
  var centerToReference = endDiff / 2 - startDiff / 2;
  var min2 = paddingObject[minProp];
  var max2 = clientSize - arrowRect[len] - paddingObject[maxProp];
  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;
  var offset2 = within(min2, center, max2);
  var axisProp = axis;
  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset2, _state$modifiersData$.centerOffset = offset2 - center, _state$modifiersData$);
}
function effect2(_ref2) {
  var state = _ref2.state, options = _ref2.options;
  var _options$element = options.element, arrowElement = _options$element === void 0 ? "[data-popper-arrow]" : _options$element;
  if (arrowElement == null) {
    return;
  }
  if (typeof arrowElement === "string") {
    arrowElement = state.elements.popper.querySelector(arrowElement);
    if (!arrowElement) {
      return;
    }
  }
  if (true) {
    if (!isHTMLElement(arrowElement)) {
      console.error(['Popper: "arrow" element must be an HTMLElement (not an SVGElement).', "To use an SVG arrow, wrap it in an HTMLElement that will be used as", "the arrow."].join(" "));
    }
  }
  if (!contains(state.elements.popper, arrowElement)) {
    if (true) {
      console.error(['Popper: "arrow" modifier\'s `element` must be a child of the popper', "element."].join(" "));
    }
    return;
  }
  state.elements.arrow = arrowElement;
}
var arrow_default = {
  name: "arrow",
  enabled: true,
  phase: "main",
  fn: arrow,
  effect: effect2,
  requires: ["popperOffsets"],
  requiresIfExists: ["preventOverflow"]
};

// node_modules/@popperjs/core/lib/utils/getVariation.js
function getVariation(placement) {
  return placement.split("-")[1];
}

// node_modules/@popperjs/core/lib/modifiers/computeStyles.js
var unsetSides = {
  top: "auto",
  right: "auto",
  bottom: "auto",
  left: "auto"
};
function roundOffsetsByDPR(_ref, win) {
  var x = _ref.x, y = _ref.y;
  var dpr = win.devicePixelRatio || 1;
  return {
    x: round(x * dpr) / dpr || 0,
    y: round(y * dpr) / dpr || 0
  };
}
function mapToStyles(_ref2) {
  var _Object$assign2;
  var popper2 = _ref2.popper, popperRect = _ref2.popperRect, placement = _ref2.placement, variation = _ref2.variation, offsets = _ref2.offsets, position = _ref2.position, gpuAcceleration = _ref2.gpuAcceleration, adaptive = _ref2.adaptive, roundOffsets = _ref2.roundOffsets, isFixed = _ref2.isFixed;
  var _offsets$x = offsets.x, x = _offsets$x === void 0 ? 0 : _offsets$x, _offsets$y = offsets.y, y = _offsets$y === void 0 ? 0 : _offsets$y;
  var _ref3 = typeof roundOffsets === "function" ? roundOffsets({
    x,
    y
  }) : {
    x,
    y
  };
  x = _ref3.x;
  y = _ref3.y;
  var hasX = offsets.hasOwnProperty("x");
  var hasY = offsets.hasOwnProperty("y");
  var sideX = left;
  var sideY = top;
  var win = window;
  if (adaptive) {
    var offsetParent = getOffsetParent(popper2);
    var heightProp = "clientHeight";
    var widthProp = "clientWidth";
    if (offsetParent === getWindow(popper2)) {
      offsetParent = getDocumentElement(popper2);
      if (getComputedStyle2(offsetParent).position !== "static" && position === "absolute") {
        heightProp = "scrollHeight";
        widthProp = "scrollWidth";
      }
    }
    offsetParent = offsetParent;
    if (placement === top || (placement === left || placement === right) && variation === end) {
      sideY = bottom;
      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : (
        // $FlowFixMe[prop-missing]
        offsetParent[heightProp]
      );
      y -= offsetY - popperRect.height;
      y *= gpuAcceleration ? 1 : -1;
    }
    if (placement === left || (placement === top || placement === bottom) && variation === end) {
      sideX = right;
      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : (
        // $FlowFixMe[prop-missing]
        offsetParent[widthProp]
      );
      x -= offsetX - popperRect.width;
      x *= gpuAcceleration ? 1 : -1;
    }
  }
  var commonStyles = Object.assign({
    position
  }, adaptive && unsetSides);
  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({
    x,
    y
  }, getWindow(popper2)) : {
    x,
    y
  };
  x = _ref4.x;
  y = _ref4.y;
  if (gpuAcceleration) {
    var _Object$assign;
    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? "0" : "", _Object$assign[sideX] = hasX ? "0" : "", _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? "translate(" + x + "px, " + y + "px)" : "translate3d(" + x + "px, " + y + "px, 0)", _Object$assign));
  }
  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + "px" : "", _Object$assign2[sideX] = hasX ? x + "px" : "", _Object$assign2.transform = "", _Object$assign2));
}
function computeStyles(_ref5) {
  var state = _ref5.state, options = _ref5.options;
  var _options$gpuAccelerat = options.gpuAcceleration, gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat, _options$adaptive = options.adaptive, adaptive = _options$adaptive === void 0 ? true : _options$adaptive, _options$roundOffsets = options.roundOffsets, roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;
  if (true) {
    var transitionProperty = getComputedStyle2(state.elements.popper).transitionProperty || "";
    if (adaptive && ["transform", "top", "right", "bottom", "left"].some(function(property) {
      return transitionProperty.indexOf(property) >= 0;
    })) {
      console.warn(["Popper: Detected CSS transitions on at least one of the following", 'CSS properties: "transform", "top", "right", "bottom", "left".', "\n\n", 'Disable the "computeStyles" modifier\'s `adaptive` option to allow', "for smooth transitions, or remove these properties from the CSS", "transition declaration on the popper element if only transitioning", "opacity or background-color for example.", "\n\n", "We recommend using the popper element as a wrapper around an inner", "element that can have any CSS property transitioned for animations."].join(" "));
    }
  }
  var commonStyles = {
    placement: getBasePlacement(state.placement),
    variation: getVariation(state.placement),
    popper: state.elements.popper,
    popperRect: state.rects.popper,
    gpuAcceleration,
    isFixed: state.options.strategy === "fixed"
  };
  if (state.modifiersData.popperOffsets != null) {
    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.popperOffsets,
      position: state.options.strategy,
      adaptive,
      roundOffsets
    })));
  }
  if (state.modifiersData.arrow != null) {
    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.arrow,
      position: "absolute",
      adaptive: false,
      roundOffsets
    })));
  }
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    "data-popper-placement": state.placement
  });
}
var computeStyles_default = {
  name: "computeStyles",
  enabled: true,
  phase: "beforeWrite",
  fn: computeStyles,
  data: {}
};

// node_modules/@popperjs/core/lib/modifiers/eventListeners.js
var passive = {
  passive: true
};
function effect3(_ref) {
  var state = _ref.state, instance = _ref.instance, options = _ref.options;
  var _options$scroll = options.scroll, scroll = _options$scroll === void 0 ? true : _options$scroll, _options$resize = options.resize, resize = _options$resize === void 0 ? true : _options$resize;
  var window2 = getWindow(state.elements.popper);
  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);
  if (scroll) {
    scrollParents.forEach(function(scrollParent) {
      scrollParent.addEventListener("scroll", instance.update, passive);
    });
  }
  if (resize) {
    window2.addEventListener("resize", instance.update, passive);
  }
  return function() {
    if (scroll) {
      scrollParents.forEach(function(scrollParent) {
        scrollParent.removeEventListener("scroll", instance.update, passive);
      });
    }
    if (resize) {
      window2.removeEventListener("resize", instance.update, passive);
    }
  };
}
var eventListeners_default = {
  name: "eventListeners",
  enabled: true,
  phase: "write",
  fn: function fn() {
  },
  effect: effect3,
  data: {}
};

// node_modules/@popperjs/core/lib/utils/getOppositePlacement.js
var hash = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, function(matched) {
    return hash[matched];
  });
}

// node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js
var hash2 = {
  start: "end",
  end: "start"
};
function getOppositeVariationPlacement(placement) {
  return placement.replace(/start|end/g, function(matched) {
    return hash2[matched];
  });
}

// node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js
function getWindowScroll(node) {
  var win = getWindow(node);
  var scrollLeft = win.pageXOffset;
  var scrollTop = win.pageYOffset;
  return {
    scrollLeft,
    scrollTop
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js
function getWindowScrollBarX(element) {
  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;
}

// node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js
function getViewportRect(element, strategy) {
  var win = getWindow(element);
  var html = getDocumentElement(element);
  var visualViewport = win.visualViewport;
  var width = html.clientWidth;
  var height = html.clientHeight;
  var x = 0;
  var y = 0;
  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    var layoutViewport = isLayoutViewport();
    if (layoutViewport || !layoutViewport && strategy === "fixed") {
      x = visualViewport.offsetLeft;
      y = visualViewport.offsetTop;
    }
  }
  return {
    width,
    height,
    x: x + getWindowScrollBarX(element),
    y
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js
function getDocumentRect(element) {
  var _element$ownerDocumen;
  var html = getDocumentElement(element);
  var winScroll = getWindowScroll(element);
  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;
  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);
  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);
  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);
  var y = -winScroll.scrollTop;
  if (getComputedStyle2(body || html).direction === "rtl") {
    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;
  }
  return {
    width,
    height,
    x,
    y
  };
}

// node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js
function isScrollParent(element) {
  var _getComputedStyle = getComputedStyle2(element), overflow = _getComputedStyle.overflow, overflowX = _getComputedStyle.overflowX, overflowY = _getComputedStyle.overflowY;
  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);
}

// node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js
function getScrollParent(node) {
  if (["html", "body", "#document"].indexOf(getNodeName(node)) >= 0) {
    return node.ownerDocument.body;
  }
  if (isHTMLElement(node) && isScrollParent(node)) {
    return node;
  }
  return getScrollParent(getParentNode(node));
}

// node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js
function listScrollParents(element, list) {
  var _element$ownerDocumen;
  if (list === void 0) {
    list = [];
  }
  var scrollParent = getScrollParent(element);
  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);
  var win = getWindow(scrollParent);
  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;
  var updatedList = list.concat(target);
  return isBody ? updatedList : (
    // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here
    updatedList.concat(listScrollParents(getParentNode(target)))
  );
}

// node_modules/@popperjs/core/lib/utils/rectToClientRect.js
function rectToClientRect(rect) {
  return Object.assign({}, rect, {
    left: rect.x,
    top: rect.y,
    right: rect.x + rect.width,
    bottom: rect.y + rect.height
  });
}

// node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js
function getInnerBoundingClientRect(element, strategy) {
  var rect = getBoundingClientRect(element, false, strategy === "fixed");
  rect.top = rect.top + element.clientTop;
  rect.left = rect.left + element.clientLeft;
  rect.bottom = rect.top + element.clientHeight;
  rect.right = rect.left + element.clientWidth;
  rect.width = element.clientWidth;
  rect.height = element.clientHeight;
  rect.x = rect.left;
  rect.y = rect.top;
  return rect;
}
function getClientRectFromMixedType(element, clippingParent, strategy) {
  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));
}
function getClippingParents(element) {
  var clippingParents2 = listScrollParents(getParentNode(element));
  var canEscapeClipping = ["absolute", "fixed"].indexOf(getComputedStyle2(element).position) >= 0;
  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;
  if (!isElement(clipperElement)) {
    return [];
  }
  return clippingParents2.filter(function(clippingParent) {
    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== "body";
  });
}
function getClippingRect(element, boundary, rootBoundary, strategy) {
  var mainClippingParents = boundary === "clippingParents" ? getClippingParents(element) : [].concat(boundary);
  var clippingParents2 = [].concat(mainClippingParents, [rootBoundary]);
  var firstClippingParent = clippingParents2[0];
  var clippingRect = clippingParents2.reduce(function(accRect, clippingParent) {
    var rect = getClientRectFromMixedType(element, clippingParent, strategy);
    accRect.top = max(rect.top, accRect.top);
    accRect.right = min(rect.right, accRect.right);
    accRect.bottom = min(rect.bottom, accRect.bottom);
    accRect.left = max(rect.left, accRect.left);
    return accRect;
  }, getClientRectFromMixedType(element, firstClippingParent, strategy));
  clippingRect.width = clippingRect.right - clippingRect.left;
  clippingRect.height = clippingRect.bottom - clippingRect.top;
  clippingRect.x = clippingRect.left;
  clippingRect.y = clippingRect.top;
  return clippingRect;
}

// node_modules/@popperjs/core/lib/utils/computeOffsets.js
function computeOffsets(_ref) {
  var reference2 = _ref.reference, element = _ref.element, placement = _ref.placement;
  var basePlacement = placement ? getBasePlacement(placement) : null;
  var variation = placement ? getVariation(placement) : null;
  var commonX = reference2.x + reference2.width / 2 - element.width / 2;
  var commonY = reference2.y + reference2.height / 2 - element.height / 2;
  var offsets;
  switch (basePlacement) {
    case top:
      offsets = {
        x: commonX,
        y: reference2.y - element.height
      };
      break;
    case bottom:
      offsets = {
        x: commonX,
        y: reference2.y + reference2.height
      };
      break;
    case right:
      offsets = {
        x: reference2.x + reference2.width,
        y: commonY
      };
      break;
    case left:
      offsets = {
        x: reference2.x - element.width,
        y: commonY
      };
      break;
    default:
      offsets = {
        x: reference2.x,
        y: reference2.y
      };
  }
  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;
  if (mainAxis != null) {
    var len = mainAxis === "y" ? "height" : "width";
    switch (variation) {
      case start:
        offsets[mainAxis] = offsets[mainAxis] - (reference2[len] / 2 - element[len] / 2);
        break;
      case end:
        offsets[mainAxis] = offsets[mainAxis] + (reference2[len] / 2 - element[len] / 2);
        break;
      default:
    }
  }
  return offsets;
}

// node_modules/@popperjs/core/lib/utils/detectOverflow.js
function detectOverflow(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options, _options$placement = _options.placement, placement = _options$placement === void 0 ? state.placement : _options$placement, _options$strategy = _options.strategy, strategy = _options$strategy === void 0 ? state.strategy : _options$strategy, _options$boundary = _options.boundary, boundary = _options$boundary === void 0 ? clippingParents : _options$boundary, _options$rootBoundary = _options.rootBoundary, rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary, _options$elementConte = _options.elementContext, elementContext = _options$elementConte === void 0 ? popper : _options$elementConte, _options$altBoundary = _options.altBoundary, altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary, _options$padding = _options.padding, padding = _options$padding === void 0 ? 0 : _options$padding;
  var paddingObject = mergePaddingObject(typeof padding !== "number" ? padding : expandToHashMap(padding, basePlacements));
  var altContext = elementContext === popper ? reference : popper;
  var popperRect = state.rects.popper;
  var element = state.elements[altBoundary ? altContext : elementContext];
  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);
  var referenceClientRect = getBoundingClientRect(state.elements.reference);
  var popperOffsets2 = computeOffsets({
    reference: referenceClientRect,
    element: popperRect,
    strategy: "absolute",
    placement
  });
  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets2));
  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect;
  var overflowOffsets = {
    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,
    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,
    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,
    right: elementClientRect.right - clippingClientRect.right + paddingObject.right
  };
  var offsetData = state.modifiersData.offset;
  if (elementContext === popper && offsetData) {
    var offset2 = offsetData[placement];
    Object.keys(overflowOffsets).forEach(function(key) {
      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;
      var axis = [top, bottom].indexOf(key) >= 0 ? "y" : "x";
      overflowOffsets[key] += offset2[axis] * multiply;
    });
  }
  return overflowOffsets;
}

// node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js
function computeAutoPlacement(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options, placement = _options.placement, boundary = _options.boundary, rootBoundary = _options.rootBoundary, padding = _options.padding, flipVariations = _options.flipVariations, _options$allowedAutoP = _options.allowedAutoPlacements, allowedAutoPlacements = _options$allowedAutoP === void 0 ? placements : _options$allowedAutoP;
  var variation = getVariation(placement);
  var placements2 = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function(placement2) {
    return getVariation(placement2) === variation;
  }) : basePlacements;
  var allowedPlacements = placements2.filter(function(placement2) {
    return allowedAutoPlacements.indexOf(placement2) >= 0;
  });
  if (allowedPlacements.length === 0) {
    allowedPlacements = placements2;
    if (true) {
      console.error(["Popper: The `allowedAutoPlacements` option did not allow any", "placements. Ensure the `placement` option matches the variation", "of the allowed placements.", 'For example, "auto" cannot be used to allow "bottom-start".', 'Use "auto-start" instead.'].join(" "));
    }
  }
  var overflows = allowedPlacements.reduce(function(acc, placement2) {
    acc[placement2] = detectOverflow(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding
    })[getBasePlacement(placement2)];
    return acc;
  }, {});
  return Object.keys(overflows).sort(function(a, b) {
    return overflows[a] - overflows[b];
  });
}

// node_modules/@popperjs/core/lib/modifiers/flip.js
function getExpandedFallbackPlacements(placement) {
  if (getBasePlacement(placement) === auto) {
    return [];
  }
  var oppositePlacement = getOppositePlacement(placement);
  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];
}
function flip(_ref) {
  var state = _ref.state, options = _ref.options, name = _ref.name;
  if (state.modifiersData[name]._skip) {
    return;
  }
  var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis, specifiedFallbackPlacements = options.fallbackPlacements, padding = options.padding, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, _options$flipVariatio = options.flipVariations, flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio, allowedAutoPlacements = options.allowedAutoPlacements;
  var preferredPlacement = state.options.placement;
  var basePlacement = getBasePlacement(preferredPlacement);
  var isBasePlacement = basePlacement === preferredPlacement;
  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));
  var placements2 = [preferredPlacement].concat(fallbackPlacements).reduce(function(acc, placement2) {
    return acc.concat(getBasePlacement(placement2) === auto ? computeAutoPlacement(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding,
      flipVariations,
      allowedAutoPlacements
    }) : placement2);
  }, []);
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var checksMap = /* @__PURE__ */ new Map();
  var makeFallbackChecks = true;
  var firstFittingPlacement = placements2[0];
  for (var i = 0; i < placements2.length; i++) {
    var placement = placements2[i];
    var _basePlacement = getBasePlacement(placement);
    var isStartVariation = getVariation(placement) === start;
    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;
    var len = isVertical ? "width" : "height";
    var overflow = detectOverflow(state, {
      placement,
      boundary,
      rootBoundary,
      altBoundary,
      padding
    });
    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;
    if (referenceRect[len] > popperRect[len]) {
      mainVariationSide = getOppositePlacement(mainVariationSide);
    }
    var altVariationSide = getOppositePlacement(mainVariationSide);
    var checks = [];
    if (checkMainAxis) {
      checks.push(overflow[_basePlacement] <= 0);
    }
    if (checkAltAxis) {
      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);
    }
    if (checks.every(function(check) {
      return check;
    })) {
      firstFittingPlacement = placement;
      makeFallbackChecks = false;
      break;
    }
    checksMap.set(placement, checks);
  }
  if (makeFallbackChecks) {
    var numberOfChecks = flipVariations ? 3 : 1;
    var _loop = function _loop2(_i2) {
      var fittingPlacement = placements2.find(function(placement2) {
        var checks2 = checksMap.get(placement2);
        if (checks2) {
          return checks2.slice(0, _i2).every(function(check) {
            return check;
          });
        }
      });
      if (fittingPlacement) {
        firstFittingPlacement = fittingPlacement;
        return "break";
      }
    };
    for (var _i = numberOfChecks; _i > 0; _i--) {
      var _ret = _loop(_i);
      if (_ret === "break")
        break;
    }
  }
  if (state.placement !== firstFittingPlacement) {
    state.modifiersData[name]._skip = true;
    state.placement = firstFittingPlacement;
    state.reset = true;
  }
}
var flip_default = {
  name: "flip",
  enabled: true,
  phase: "main",
  fn: flip,
  requiresIfExists: ["offset"],
  data: {
    _skip: false
  }
};

// node_modules/@popperjs/core/lib/modifiers/hide.js
function getSideOffsets(overflow, rect, preventedOffsets) {
  if (preventedOffsets === void 0) {
    preventedOffsets = {
      x: 0,
      y: 0
    };
  }
  return {
    top: overflow.top - rect.height - preventedOffsets.y,
    right: overflow.right - rect.width + preventedOffsets.x,
    bottom: overflow.bottom - rect.height + preventedOffsets.y,
    left: overflow.left - rect.width - preventedOffsets.x
  };
}
function isAnySideFullyClipped(overflow) {
  return [top, right, bottom, left].some(function(side) {
    return overflow[side] >= 0;
  });
}
function hide(_ref) {
  var state = _ref.state, name = _ref.name;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var preventedOffsets = state.modifiersData.preventOverflow;
  var referenceOverflow = detectOverflow(state, {
    elementContext: "reference"
  });
  var popperAltOverflow = detectOverflow(state, {
    altBoundary: true
  });
  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);
  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);
  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);
  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);
  state.modifiersData[name] = {
    referenceClippingOffsets,
    popperEscapeOffsets,
    isReferenceHidden,
    hasPopperEscaped
  };
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    "data-popper-reference-hidden": isReferenceHidden,
    "data-popper-escaped": hasPopperEscaped
  });
}
var hide_default = {
  name: "hide",
  enabled: true,
  phase: "main",
  requiresIfExists: ["preventOverflow"],
  fn: hide
};

// node_modules/@popperjs/core/lib/modifiers/offset.js
function distanceAndSkiddingToXY(placement, rects, offset2) {
  var basePlacement = getBasePlacement(placement);
  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;
  var _ref = typeof offset2 === "function" ? offset2(Object.assign({}, rects, {
    placement
  })) : offset2, skidding = _ref[0], distance = _ref[1];
  skidding = skidding || 0;
  distance = (distance || 0) * invertDistance;
  return [left, right].indexOf(basePlacement) >= 0 ? {
    x: distance,
    y: skidding
  } : {
    x: skidding,
    y: distance
  };
}
function offset(_ref2) {
  var state = _ref2.state, options = _ref2.options, name = _ref2.name;
  var _options$offset = options.offset, offset2 = _options$offset === void 0 ? [0, 0] : _options$offset;
  var data = placements.reduce(function(acc, placement) {
    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset2);
    return acc;
  }, {});
  var _data$state$placement = data[state.placement], x = _data$state$placement.x, y = _data$state$placement.y;
  if (state.modifiersData.popperOffsets != null) {
    state.modifiersData.popperOffsets.x += x;
    state.modifiersData.popperOffsets.y += y;
  }
  state.modifiersData[name] = data;
}
var offset_default = {
  name: "offset",
  enabled: true,
  phase: "main",
  requires: ["popperOffsets"],
  fn: offset
};

// node_modules/@popperjs/core/lib/modifiers/popperOffsets.js
function popperOffsets(_ref) {
  var state = _ref.state, name = _ref.name;
  state.modifiersData[name] = computeOffsets({
    reference: state.rects.reference,
    element: state.rects.popper,
    strategy: "absolute",
    placement: state.placement
  });
}
var popperOffsets_default = {
  name: "popperOffsets",
  enabled: true,
  phase: "read",
  fn: popperOffsets,
  data: {}
};

// node_modules/@popperjs/core/lib/utils/getAltAxis.js
function getAltAxis(axis) {
  return axis === "x" ? "y" : "x";
}

// node_modules/@popperjs/core/lib/modifiers/preventOverflow.js
function preventOverflow(_ref) {
  var state = _ref.state, options = _ref.options, name = _ref.name;
  var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, padding = options.padding, _options$tether = options.tether, tether = _options$tether === void 0 ? true : _options$tether, _options$tetherOffset = options.tetherOffset, tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;
  var overflow = detectOverflow(state, {
    boundary,
    rootBoundary,
    padding,
    altBoundary
  });
  var basePlacement = getBasePlacement(state.placement);
  var variation = getVariation(state.placement);
  var isBasePlacement = !variation;
  var mainAxis = getMainAxisFromPlacement(basePlacement);
  var altAxis = getAltAxis(mainAxis);
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var tetherOffsetValue = typeof tetherOffset === "function" ? tetherOffset(Object.assign({}, state.rects, {
    placement: state.placement
  })) : tetherOffset;
  var normalizedTetherOffsetValue = typeof tetherOffsetValue === "number" ? {
    mainAxis: tetherOffsetValue,
    altAxis: tetherOffsetValue
  } : Object.assign({
    mainAxis: 0,
    altAxis: 0
  }, tetherOffsetValue);
  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;
  var data = {
    x: 0,
    y: 0
  };
  if (!popperOffsets2) {
    return;
  }
  if (checkMainAxis) {
    var _offsetModifierState$;
    var mainSide = mainAxis === "y" ? top : left;
    var altSide = mainAxis === "y" ? bottom : right;
    var len = mainAxis === "y" ? "height" : "width";
    var offset2 = popperOffsets2[mainAxis];
    var min2 = offset2 + overflow[mainSide];
    var max2 = offset2 - overflow[altSide];
    var additive = tether ? -popperRect[len] / 2 : 0;
    var minLen = variation === start ? referenceRect[len] : popperRect[len];
    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len];
    var arrowElement = state.elements.arrow;
    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {
      width: 0,
      height: 0
    };
    var arrowPaddingObject = state.modifiersData["arrow#persistent"] ? state.modifiersData["arrow#persistent"].padding : getFreshSideObject();
    var arrowPaddingMin = arrowPaddingObject[mainSide];
    var arrowPaddingMax = arrowPaddingObject[altSide];
    var arrowLen = within(0, referenceRect[len], arrowRect[len]);
    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;
    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;
    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);
    var clientOffset = arrowOffsetParent ? mainAxis === "y" ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;
    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;
    var tetherMin = offset2 + minOffset - offsetModifierValue - clientOffset;
    var tetherMax = offset2 + maxOffset - offsetModifierValue;
    var preventedOffset = within(tether ? min(min2, tetherMin) : min2, offset2, tether ? max(max2, tetherMax) : max2);
    popperOffsets2[mainAxis] = preventedOffset;
    data[mainAxis] = preventedOffset - offset2;
  }
  if (checkAltAxis) {
    var _offsetModifierState$2;
    var _mainSide = mainAxis === "x" ? top : left;
    var _altSide = mainAxis === "x" ? bottom : right;
    var _offset = popperOffsets2[altAxis];
    var _len = altAxis === "y" ? "height" : "width";
    var _min = _offset + overflow[_mainSide];
    var _max = _offset - overflow[_altSide];
    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;
    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;
    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;
    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;
    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);
    popperOffsets2[altAxis] = _preventedOffset;
    data[altAxis] = _preventedOffset - _offset;
  }
  state.modifiersData[name] = data;
}
var preventOverflow_default = {
  name: "preventOverflow",
  enabled: true,
  phase: "main",
  fn: preventOverflow,
  requiresIfExists: ["offset"]
};

// node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js
function getHTMLElementScroll(element) {
  return {
    scrollLeft: element.scrollLeft,
    scrollTop: element.scrollTop
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js
function getNodeScroll(node) {
  if (node === getWindow(node) || !isHTMLElement(node)) {
    return getWindowScroll(node);
  } else {
    return getHTMLElementScroll(node);
  }
}

// node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js
function isElementScaled(element) {
  var rect = element.getBoundingClientRect();
  var scaleX = round(rect.width) / element.offsetWidth || 1;
  var scaleY = round(rect.height) / element.offsetHeight || 1;
  return scaleX !== 1 || scaleY !== 1;
}
function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {
  if (isFixed === void 0) {
    isFixed = false;
  }
  var isOffsetParentAnElement = isHTMLElement(offsetParent);
  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);
  var documentElement = getDocumentElement(offsetParent);
  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);
  var scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  var offsets = {
    x: 0,
    y: 0
  };
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || // https://github.com/popperjs/popper-core/issues/1078
    isScrollParent(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isHTMLElement(offsetParent)) {
      offsets = getBoundingClientRect(offsetParent, true);
      offsets.x += offsetParent.clientLeft;
      offsets.y += offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }
  return {
    x: rect.left + scroll.scrollLeft - offsets.x,
    y: rect.top + scroll.scrollTop - offsets.y,
    width: rect.width,
    height: rect.height
  };
}

// node_modules/@popperjs/core/lib/utils/orderModifiers.js
function order(modifiers) {
  var map = /* @__PURE__ */ new Map();
  var visited = /* @__PURE__ */ new Set();
  var result = [];
  modifiers.forEach(function(modifier) {
    map.set(modifier.name, modifier);
  });
  function sort(modifier) {
    visited.add(modifier.name);
    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);
    requires.forEach(function(dep) {
      if (!visited.has(dep)) {
        var depModifier = map.get(dep);
        if (depModifier) {
          sort(depModifier);
        }
      }
    });
    result.push(modifier);
  }
  modifiers.forEach(function(modifier) {
    if (!visited.has(modifier.name)) {
      sort(modifier);
    }
  });
  return result;
}
function orderModifiers(modifiers) {
  var orderedModifiers = order(modifiers);
  return modifierPhases.reduce(function(acc, phase) {
    return acc.concat(orderedModifiers.filter(function(modifier) {
      return modifier.phase === phase;
    }));
  }, []);
}

// node_modules/@popperjs/core/lib/utils/debounce.js
function debounce(fn2) {
  var pending;
  return function() {
    if (!pending) {
      pending = new Promise(function(resolve) {
        Promise.resolve().then(function() {
          pending = void 0;
          resolve(fn2());
        });
      });
    }
    return pending;
  };
}

// node_modules/@popperjs/core/lib/utils/format.js
function format(str) {
  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    args[_key - 1] = arguments[_key];
  }
  return [].concat(args).reduce(function(p, c) {
    return p.replace(/%s/, c);
  }, str);
}

// node_modules/@popperjs/core/lib/utils/validateModifiers.js
var INVALID_MODIFIER_ERROR = 'Popper: modifier "%s" provided an invalid %s property, expected %s but got %s';
var MISSING_DEPENDENCY_ERROR = 'Popper: modifier "%s" requires "%s", but "%s" modifier is not available';
var VALID_PROPERTIES = ["name", "enabled", "phase", "fn", "effect", "requires", "options"];
function validateModifiers(modifiers) {
  modifiers.forEach(function(modifier) {
    [].concat(Object.keys(modifier), VALID_PROPERTIES).filter(function(value, index, self) {
      return self.indexOf(value) === index;
    }).forEach(function(key) {
      switch (key) {
        case "name":
          if (typeof modifier.name !== "string") {
            console.error(format(INVALID_MODIFIER_ERROR, String(modifier.name), '"name"', '"string"', '"' + String(modifier.name) + '"'));
          }
          break;
        case "enabled":
          if (typeof modifier.enabled !== "boolean") {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"enabled"', '"boolean"', '"' + String(modifier.enabled) + '"'));
          }
          break;
        case "phase":
          if (modifierPhases.indexOf(modifier.phase) < 0) {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"phase"', "either " + modifierPhases.join(", "), '"' + String(modifier.phase) + '"'));
          }
          break;
        case "fn":
          if (typeof modifier.fn !== "function") {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"fn"', '"function"', '"' + String(modifier.fn) + '"'));
          }
          break;
        case "effect":
          if (modifier.effect != null && typeof modifier.effect !== "function") {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"effect"', '"function"', '"' + String(modifier.fn) + '"'));
          }
          break;
        case "requires":
          if (modifier.requires != null && !Array.isArray(modifier.requires)) {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"requires"', '"array"', '"' + String(modifier.requires) + '"'));
          }
          break;
        case "requiresIfExists":
          if (!Array.isArray(modifier.requiresIfExists)) {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"requiresIfExists"', '"array"', '"' + String(modifier.requiresIfExists) + '"'));
          }
          break;
        case "options":
        case "data":
          break;
        default:
          console.error('PopperJS: an invalid property has been provided to the "' + modifier.name + '" modifier, valid properties are ' + VALID_PROPERTIES.map(function(s) {
            return '"' + s + '"';
          }).join(", ") + '; but "' + key + '" was provided.');
      }
      modifier.requires && modifier.requires.forEach(function(requirement) {
        if (modifiers.find(function(mod) {
          return mod.name === requirement;
        }) == null) {
          console.error(format(MISSING_DEPENDENCY_ERROR, String(modifier.name), requirement, requirement));
        }
      });
    });
  });
}

// node_modules/@popperjs/core/lib/utils/uniqueBy.js
function uniqueBy(arr, fn2) {
  var identifiers = /* @__PURE__ */ new Set();
  return arr.filter(function(item) {
    var identifier = fn2(item);
    if (!identifiers.has(identifier)) {
      identifiers.add(identifier);
      return true;
    }
  });
}

// node_modules/@popperjs/core/lib/utils/mergeByName.js
function mergeByName(modifiers) {
  var merged = modifiers.reduce(function(merged2, current) {
    var existing = merged2[current.name];
    merged2[current.name] = existing ? Object.assign({}, existing, current, {
      options: Object.assign({}, existing.options, current.options),
      data: Object.assign({}, existing.data, current.data)
    }) : current;
    return merged2;
  }, {});
  return Object.keys(merged).map(function(key) {
    return merged[key];
  });
}

// node_modules/@popperjs/core/lib/createPopper.js
var INVALID_ELEMENT_ERROR = "Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.";
var INFINITE_LOOP_ERROR = "Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.";
var DEFAULT_OPTIONS = {
  placement: "bottom",
  modifiers: [],
  strategy: "absolute"
};
function areValidElements() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  return !args.some(function(element) {
    return !(element && typeof element.getBoundingClientRect === "function");
  });
}
function popperGenerator(generatorOptions) {
  if (generatorOptions === void 0) {
    generatorOptions = {};
  }
  var _generatorOptions = generatorOptions, _generatorOptions$def = _generatorOptions.defaultModifiers, defaultModifiers3 = _generatorOptions$def === void 0 ? [] : _generatorOptions$def, _generatorOptions$def2 = _generatorOptions.defaultOptions, defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;
  return function createPopper4(reference2, popper2, options) {
    if (options === void 0) {
      options = defaultOptions;
    }
    var state = {
      placement: "bottom",
      orderedModifiers: [],
      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),
      modifiersData: {},
      elements: {
        reference: reference2,
        popper: popper2
      },
      attributes: {},
      styles: {}
    };
    var effectCleanupFns = [];
    var isDestroyed = false;
    var instance = {
      state,
      setOptions: function setOptions(setOptionsAction) {
        var options2 = typeof setOptionsAction === "function" ? setOptionsAction(state.options) : setOptionsAction;
        cleanupModifierEffects();
        state.options = Object.assign({}, defaultOptions, state.options, options2);
        state.scrollParents = {
          reference: isElement(reference2) ? listScrollParents(reference2) : reference2.contextElement ? listScrollParents(reference2.contextElement) : [],
          popper: listScrollParents(popper2)
        };
        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers3, state.options.modifiers)));
        state.orderedModifiers = orderedModifiers.filter(function(m) {
          return m.enabled;
        });
        if (true) {
          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function(_ref) {
            var name = _ref.name;
            return name;
          });
          validateModifiers(modifiers);
          if (getBasePlacement(state.options.placement) === auto) {
            var flipModifier = state.orderedModifiers.find(function(_ref2) {
              var name = _ref2.name;
              return name === "flip";
            });
            if (!flipModifier) {
              console.error(['Popper: "auto" placements require the "flip" modifier be', "present and enabled to work."].join(" "));
            }
          }
          var _getComputedStyle = getComputedStyle2(popper2), marginTop = _getComputedStyle.marginTop, marginRight = _getComputedStyle.marginRight, marginBottom = _getComputedStyle.marginBottom, marginLeft = _getComputedStyle.marginLeft;
          if ([marginTop, marginRight, marginBottom, marginLeft].some(function(margin) {
            return parseFloat(margin);
          })) {
            console.warn(['Popper: CSS "margin" styles cannot be used to apply padding', "between the popper and its reference element or boundary.", "To replicate margin, use the `offset` modifier, as well as", "the `padding` option in the `preventOverflow` and `flip`", "modifiers."].join(" "));
          }
        }
        runModifierEffects();
        return instance.update();
      },
      // Sync update – it will always be executed, even if not necessary. This
      // is useful for low frequency updates where sync behavior simplifies the
      // logic.
      // For high frequency updates (e.g. `resize` and `scroll` events), always
      // prefer the async Popper#update method
      forceUpdate: function forceUpdate() {
        if (isDestroyed) {
          return;
        }
        var _state$elements = state.elements, reference3 = _state$elements.reference, popper3 = _state$elements.popper;
        if (!areValidElements(reference3, popper3)) {
          if (true) {
            console.error(INVALID_ELEMENT_ERROR);
          }
          return;
        }
        state.rects = {
          reference: getCompositeRect(reference3, getOffsetParent(popper3), state.options.strategy === "fixed"),
          popper: getLayoutRect(popper3)
        };
        state.reset = false;
        state.placement = state.options.placement;
        state.orderedModifiers.forEach(function(modifier) {
          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);
        });
        var __debug_loops__ = 0;
        for (var index = 0; index < state.orderedModifiers.length; index++) {
          if (true) {
            __debug_loops__ += 1;
            if (__debug_loops__ > 100) {
              console.error(INFINITE_LOOP_ERROR);
              break;
            }
          }
          if (state.reset === true) {
            state.reset = false;
            index = -1;
            continue;
          }
          var _state$orderedModifie = state.orderedModifiers[index], fn2 = _state$orderedModifie.fn, _state$orderedModifie2 = _state$orderedModifie.options, _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2, name = _state$orderedModifie.name;
          if (typeof fn2 === "function") {
            state = fn2({
              state,
              options: _options,
              name,
              instance
            }) || state;
          }
        }
      },
      // Async and optimistically optimized update – it will not be executed if
      // not necessary (debounced to run at most once-per-tick)
      update: debounce(function() {
        return new Promise(function(resolve) {
          instance.forceUpdate();
          resolve(state);
        });
      }),
      destroy: function destroy() {
        cleanupModifierEffects();
        isDestroyed = true;
      }
    };
    if (!areValidElements(reference2, popper2)) {
      if (true) {
        console.error(INVALID_ELEMENT_ERROR);
      }
      return instance;
    }
    instance.setOptions(options).then(function(state2) {
      if (!isDestroyed && options.onFirstUpdate) {
        options.onFirstUpdate(state2);
      }
    });
    function runModifierEffects() {
      state.orderedModifiers.forEach(function(_ref3) {
        var name = _ref3.name, _ref3$options = _ref3.options, options2 = _ref3$options === void 0 ? {} : _ref3$options, effect4 = _ref3.effect;
        if (typeof effect4 === "function") {
          var cleanupFn = effect4({
            state,
            name,
            instance,
            options: options2
          });
          var noopFn = function noopFn2() {
          };
          effectCleanupFns.push(cleanupFn || noopFn);
        }
      });
    }
    function cleanupModifierEffects() {
      effectCleanupFns.forEach(function(fn2) {
        return fn2();
      });
      effectCleanupFns = [];
    }
    return instance;
  };
}
var createPopper = popperGenerator();

// node_modules/@popperjs/core/lib/popper-lite.js
var defaultModifiers = [eventListeners_default, popperOffsets_default, computeStyles_default, applyStyles_default];
var createPopper2 = popperGenerator({
  defaultModifiers
});

// node_modules/@popperjs/core/lib/popper.js
var defaultModifiers2 = [eventListeners_default, popperOffsets_default, computeStyles_default, applyStyles_default, offset_default, flip_default, preventOverflow_default, arrow_default, hide_default];
var createPopper3 = popperGenerator({
  defaultModifiers: defaultModifiers2
});

// node_modules/react-popper/lib/esm/usePopper.js
var import_react_fast_compare = __toESM(require_react_fast_compare());
var EMPTY_MODIFIERS = [];
var usePopper = function usePopper2(referenceElement, popperElement, options) {
  if (options === void 0) {
    options = {};
  }
  var prevOptions = React5.useRef(null);
  var optionsWithDefaults = {
    onFirstUpdate: options.onFirstUpdate,
    placement: options.placement || "bottom",
    strategy: options.strategy || "absolute",
    modifiers: options.modifiers || EMPTY_MODIFIERS
  };
  var _React$useState = React5.useState({
    styles: {
      popper: {
        position: optionsWithDefaults.strategy,
        left: "0",
        top: "0"
      },
      arrow: {
        position: "absolute"
      }
    },
    attributes: {}
  }), state = _React$useState[0], setState = _React$useState[1];
  var updateStateModifier = React5.useMemo(function() {
    return {
      name: "updateState",
      enabled: true,
      phase: "write",
      fn: function fn2(_ref) {
        var state2 = _ref.state;
        var elements = Object.keys(state2.elements);
        ReactDOM.flushSync(function() {
          setState({
            styles: fromEntries(elements.map(function(element) {
              return [element, state2.styles[element] || {}];
            })),
            attributes: fromEntries(elements.map(function(element) {
              return [element, state2.attributes[element]];
            }))
          });
        });
      },
      requires: ["computeStyles"]
    };
  }, []);
  var popperOptions = React5.useMemo(function() {
    var newOptions = {
      onFirstUpdate: optionsWithDefaults.onFirstUpdate,
      placement: optionsWithDefaults.placement,
      strategy: optionsWithDefaults.strategy,
      modifiers: [].concat(optionsWithDefaults.modifiers, [updateStateModifier, {
        name: "applyStyles",
        enabled: false
      }])
    };
    if ((0, import_react_fast_compare.default)(prevOptions.current, newOptions)) {
      return prevOptions.current || newOptions;
    } else {
      prevOptions.current = newOptions;
      return newOptions;
    }
  }, [optionsWithDefaults.onFirstUpdate, optionsWithDefaults.placement, optionsWithDefaults.strategy, optionsWithDefaults.modifiers, updateStateModifier]);
  var popperInstanceRef = React5.useRef();
  useIsomorphicLayoutEffect(function() {
    if (popperInstanceRef.current) {
      popperInstanceRef.current.setOptions(popperOptions);
    }
  }, [popperOptions]);
  useIsomorphicLayoutEffect(function() {
    if (referenceElement == null || popperElement == null) {
      return;
    }
    var createPopper4 = options.createPopper || createPopper3;
    var popperInstance = createPopper4(referenceElement, popperElement, popperOptions);
    popperInstanceRef.current = popperInstance;
    return function() {
      popperInstance.destroy();
      popperInstanceRef.current = null;
    };
  }, [referenceElement, popperElement, options.createPopper]);
  return {
    state: popperInstanceRef.current ? popperInstanceRef.current.state : null,
    styles: state.styles,
    attributes: state.attributes,
    update: popperInstanceRef.current ? popperInstanceRef.current.update : null,
    forceUpdate: popperInstanceRef.current ? popperInstanceRef.current.forceUpdate : null
  };
};

// node_modules/react-popper/lib/esm/Reference.js
var React7 = __toESM(require_react());
var import_warning = __toESM(require_warning());

// node_modules/@zendeskgarden/react-modals/dist/index.esm.js
function _extends$2() {
  _extends$2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$2.apply(this, arguments);
}
function isDocument(element) {
  return "nodeType" in element && element.nodeType === document.DOCUMENT_NODE;
}
function isWindow(node) {
  if ("window" in node && node.window === node)
    return node;
  if (isDocument(node))
    return node.defaultView || false;
  return false;
}
function ownerDocument2(node) {
  return node && node.ownerDocument || document;
}
function ownerWindow(node) {
  var doc = ownerDocument2(node);
  return doc && doc.defaultView || window;
}
function getComputedStyle3(node, psuedoElement) {
  return ownerWindow(node).getComputedStyle(node, psuedoElement);
}
var rUpper = /([A-Z])/g;
function hyphenate(string) {
  return string.replace(rUpper, "-$1").toLowerCase();
}
var msPattern = /^ms-/;
function hyphenateStyleName(string) {
  return hyphenate(string).replace(msPattern, "-ms-");
}
var supportedTransforms = /^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;
function isTransform(value) {
  return !!(value && supportedTransforms.test(value));
}
function style(node, property) {
  var css = "";
  var transforms = "";
  if (typeof property === "string") {
    return node.style.getPropertyValue(hyphenateStyleName(property)) || getComputedStyle3(node).getPropertyValue(hyphenateStyleName(property));
  }
  Object.keys(property).forEach(function(key) {
    var value = property[key];
    if (!value && value !== 0) {
      node.style.removeProperty(hyphenateStyleName(key));
    } else if (isTransform(key)) {
      transforms += key + "(" + value + ") ";
    } else {
      css += hyphenateStyleName(key) + ": " + value + ";";
    }
  });
  if (transforms) {
    css += "transform: " + transforms + ";";
  }
  node.style.cssText += ";" + css;
}
var canUseDOM = !!(typeof window !== "undefined" && window.document && window.document.createElement);
var size;
function scrollbarSize(recalc) {
  if (!size && size !== 0 || recalc) {
    if (canUseDOM) {
      var scrollDiv = document.createElement("div");
      scrollDiv.style.position = "absolute";
      scrollDiv.style.top = "-9999px";
      scrollDiv.style.width = "50px";
      scrollDiv.style.height = "50px";
      scrollDiv.style.overflow = "scroll";
      document.body.appendChild(scrollDiv);
      size = scrollDiv.offsetWidth - scrollDiv.clientWidth;
      document.body.removeChild(scrollDiv);
    }
  }
  return size;
}
var COMPONENT_ID$j = "modals.backdrop";
var animationName$1 = We(["0%{opacity:0;}100%{opacity:1;}"]);
var StyledBackdrop = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$j,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledBackdrop",
  componentId: "sc-mzdjpo-0"
})(["display:flex;position:fixed;top:0;right:0;bottom:0;left:0;align-items:", ";justify-content:", ";z-index:400;background-color:", ";overflow:auto;-webkit-overflow-scrolling:touch;font-family:", ";direction:", ";animation:", ";", ";"], (props) => props.isCentered && "center", (props) => props.isCentered && "center", (props) => getColor("neutralHue", 800, props.theme, 0.85), (props) => props.theme.fonts.system, (props) => props.theme.rtl && "rtl", (props) => props.isAnimated && Ae(["", " 0.15s ease-in"], animationName$1), (props) => retrieveComponentStyles(COMPONENT_ID$j, props));
StyledBackdrop.defaultProps = {
  theme: DEFAULT_THEME
};
StyledBackdrop.propTypes = {
  isCentered: import_prop_types3.default.bool,
  isAnimated: import_prop_types3.default.bool
};
var COMPONENT_ID$i = "modals.body";
var StyledBody = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$i,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledBody",
  componentId: "sc-14rzecg-0"
})(["display:block;margin:0;padding:", ";height:100%;overflow:auto;line-height:", ";color:", ";font-size:", ";", ";"], (props) => `${props.theme.space.base * 5}px ${props.theme.space.base * 10}px`, (props) => getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md), (props) => props.theme.colors.foreground, (props) => props.theme.fontSizes.md, (props) => retrieveComponentStyles(COMPONENT_ID$i, props));
StyledBody.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$h = "modals.close";
var colorStyles = (props) => {
  const backgroundColor = "primaryHue";
  const foregroundColor = "neutralHue";
  return Ae(["background-color:transparent;color:", ";&:hover{background-color:", ";color:", ";}&[data-garden-focus-visible]{box-shadow:", ";}&:active{transition:background-color 0.1s ease-in-out,color 0.1s ease-in-out;background-color:", ";color:", ";}"], getColor(foregroundColor, 600, props.theme), getColor(backgroundColor, 600, props.theme, 0.08), getColor(foregroundColor, 700, props.theme), props.theme.shadows.md(getColor(backgroundColor, 600, props.theme, 0.35)), getColor(backgroundColor, 600, props.theme, 0.2), getColor(foregroundColor, 800, props.theme));
};
var BASE_MULTIPLIERS$1 = {
  top: 2.5,
  side: 6.5,
  size: 10
};
var StyledClose = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$h,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledClose",
  componentId: "sc-iseudj-0"
})(["display:block;position:absolute;top:", "px;", ":", ";transition:box-shadow 0.1s ease-in-out,background-color 0.25s ease-in-out,color 0.25s ease-in-out;border:none;border-radius:50%;background-color:transparent;cursor:pointer;padding:0;width:", "px;height:", "px;overflow:hidden;text-decoration:none;font-size:0;user-select:none;&::-moz-focus-inner{border:0;}&:focus{outline:none;}", " & > svg{vertical-align:middle;}", ";"], (props) => props.theme.space.base * BASE_MULTIPLIERS$1.top, (props) => props.theme.rtl ? "left" : "right", (props) => `${props.theme.space.base * BASE_MULTIPLIERS$1.side}px`, (props) => props.theme.space.base * BASE_MULTIPLIERS$1.size, (props) => props.theme.space.base * BASE_MULTIPLIERS$1.size, (props) => colorStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID$h, props));
StyledClose.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$g = "modals.footer";
var StyledFooter = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$g,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledFooter",
  componentId: "sc-d8pfdu-0"
})(["display:flex;flex-shrink:0;align-items:center;justify-content:flex-end;border-top:", ";padding:", ";", ";"], (props) => props.isLarge && `${props.theme.borders.sm} ${getColor("neutralHue", 200, props.theme)}`, (props) => props.isLarge ? `${props.theme.space.base * 8}px ${props.theme.space.base * 10}px` : `${props.theme.space.base * 5}px ${props.theme.space.base * 10}px ${props.theme.space.base * 8}px`, (props) => retrieveComponentStyles(COMPONENT_ID$g, props));
StyledFooter.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$f = "modals.footer_item";
var StyledFooterItem = styled_components_browser_esm_default.span.attrs({
  "data-garden-id": COMPONENT_ID$f,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledFooterItem",
  componentId: "sc-1mb76hl-0"
})(["display:flex;margin-", ":", "px;min-width:0;&:first-child{margin-", ":0;}", ";"], (props) => props.theme.rtl ? "right" : "left", (props) => props.theme.space.base * 5, (props) => props.theme.rtl ? "right" : "left", (props) => retrieveComponentStyles(COMPONENT_ID$f, props));
StyledFooterItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$e = "modals.header";
var StyledHeader = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$e,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledHeader",
  componentId: "sc-1787r9v-0"
})(["display:block;position:", ";margin:0;border-bottom:", " ", ";padding:", ";", "  line-height:", ";color:", ";font-size:", ";font-weight:", ";", ";"], (props) => props.isDanger && "relative", (props) => props.theme.borders.sm, getColor("neutralHue", 200), (props) => `${props.theme.space.base * 5}px ${props.theme.space.base * 10}px`, (props) => props.isCloseButtonPresent && `padding-${props.theme.rtl ? "left" : "right"}: ${props.theme.space.base * (BASE_MULTIPLIERS$1.size + BASE_MULTIPLIERS$1.side + 2)}px;`, (props) => getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md), (props) => props.isDanger ? getColor("dangerHue", 600, props.theme) : props.theme.colors.foreground, (props) => props.theme.fontSizes.md, (props) => props.theme.fontWeights.semibold, (props) => retrieveComponentStyles(COMPONENT_ID$e, props));
StyledHeader.defaultProps = {
  theme: DEFAULT_THEME
};
var _g;
var _circle;
function _extends$1() {
  _extends$1 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$1.apply(this, arguments);
}
var SvgAlertErrorStroke = function SvgAlertErrorStroke2(props) {
  return React8.createElement("svg", _extends$1({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _g || (_g = React8.createElement("g", {
    fill: "none",
    stroke: "currentColor"
  }, React8.createElement("circle", {
    cx: 7.5,
    cy: 8.5,
    r: 7
  }), React8.createElement("path", {
    strokeLinecap: "round",
    d: "M7.5 4.5V9"
  }))), _circle || (_circle = React8.createElement("circle", {
    cx: 7.5,
    cy: 12,
    r: 1,
    fill: "currentColor"
  })));
};
var StyledDangerIcon = styled_components_browser_esm_default(SvgAlertErrorStroke).withConfig({
  displayName: "StyledDangerIcon",
  componentId: "sc-1kwbb39-0"
})(["position:absolute;top:", "px;", ":", ";"], (props) => props.theme.space.base * 5.5, (props) => props.theme.rtl ? "right" : "left", (props) => `${props.theme.space.base * 4}px`);
StyledDangerIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$d = "modals.modal";
var animationName = We(["0%{transform:scale(0);opacity:0;}50%{transform:scale(1.05);}100%{opacity:1;}"]);
var boxShadow$1 = (props) => {
  const {
    theme
  } = props;
  const {
    space,
    shadows
  } = theme;
  const offsetY = `${space.base * 5}px`;
  const blurRadius = `${space.base * 7}px`;
  const color = getColor("neutralHue", 800, theme, 0.35);
  return shadows.lg(offsetY, blurRadius, color);
};
var sizeStyles$1 = (props) => {
  return Ae(["", "{width:", ";}"], mediaQuery("up", props.isLarge ? "md" : "sm", props.theme), props.isLarge ? props.theme.breakpoints.md : props.theme.breakpoints.sm);
};
var StyledModal = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$d,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledModal",
  componentId: "sc-1pe1axu-0"
})(["display:flex;position:fixed;flex-direction:column;margin:", ";border-radius:", ";box-shadow:", ";background-color:", ";min-height:60px;max-height:calc(100vh - ", "px);animation:", ";animation-delay:0.01s;overflow:auto;direction:", ";", " &:focus{outline:none;}@media (max-height:399px){top:", "px;bottom:auto;margin-bottom:", "px;max-height:none;}@media screen and (-ms-high-contrast:active),screen and (-ms-high-contrast:none){right:", ";bottom:", ";transform:", ";}", ";"], (props) => props.isCentered ? "0" : `${props.theme.space.base * 12}px`, (props) => props.theme.borderRadii.md, boxShadow$1, (props) => props.theme.colors.background, (props) => props.theme.space.base * 24, (props) => props.isAnimated && Ae(["", " 0.3s ease-in-out"], animationName), (props) => props.theme.rtl && "rtl", sizeStyles$1, (props) => props.theme.space.base * 6, (props) => props.theme.space.base * 6, (props) => props.isCentered && "50%", (props) => props.isCentered && "50%", (props) => props.isCentered && "translate(50%, 50%)", (props) => retrieveComponentStyles(COMPONENT_ID$d, props));
StyledModal.propTypes = {
  isLarge: import_prop_types3.default.bool,
  isAnimated: import_prop_types3.default.bool
};
StyledModal.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$c = "modals.tooltip_modal.backdrop";
var StyledTooltipModalBackdrop = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$c,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledTooltipModalBackdrop",
  componentId: "sc-1yaomgq-0"
})(["position:fixed;top:0;right:0;bottom:0;left:0;z-index:400;overflow:hidden;-webkit-overflow-scrolling:touch;font-family:", ";direction:", ";&.garden-tooltip-modal-transition-exit-active{pointer-events:none;}&.garden-tooltip-modal-transition-exit-active div{transition:opacity 200ms;opacity:0;}", ";"], (props) => props.theme.fonts.system, (props) => props.theme.rtl && "rtl", (props) => retrieveComponentStyles(COMPONENT_ID$c, props));
StyledTooltipModalBackdrop.defaultProps = {
  theme: DEFAULT_THEME
};
function getPopperPlacement(gardenPlacement) {
  const gardenToPopperMapping = {
    auto: "auto",
    top: "top",
    "top-start": "top-start",
    "top-end": "top-end",
    bottom: "bottom",
    "bottom-start": "bottom-start",
    "bottom-end": "bottom-end",
    end: "right",
    "end-top": "right-start",
    "end-bottom": "right-end",
    start: "left",
    "start-top": "left-start",
    "start-bottom": "left-end"
  };
  return gardenToPopperMapping[gardenPlacement];
}
function getRtlPopperPlacement(gardenPlacement) {
  const rtlPlacementMappings = {
    left: "right",
    "left-start": "right-start",
    "left-end": "right-end",
    "top-start": "top-end",
    "top-end": "top-start",
    right: "left",
    "right-start": "left-start",
    "right-end": "left-end",
    "bottom-start": "bottom-end",
    "bottom-end": "bottom-start"
  };
  const popperPlacement = getPopperPlacement(gardenPlacement);
  return rtlPlacementMappings[popperPlacement] || popperPlacement;
}
function getArrowPosition(popperPlacement) {
  const arrowPositionMappings = {
    top: "bottom",
    "top-start": "bottom-left",
    "top-end": "bottom-right",
    right: "left",
    "right-start": "left-top",
    "right-end": "left-bottom",
    bottom: "top",
    "bottom-start": "top-left",
    "bottom-end": "top-right",
    left: "right",
    "left-start": "right-top",
    "left-end": "right-bottom"
  };
  return arrowPositionMappings[popperPlacement] || "top";
}
function getMenuPosition(popperPlacement) {
  return popperPlacement ? popperPlacement.split("-")[0] : "bottom";
}
var StyledTooltipWrapper = styled_components_browser_esm_default.div.attrs((props) => ({
  className: props.isAnimated && "is-animated"
})).withConfig({
  displayName: "StyledTooltipWrapper",
  componentId: "sc-1xk05kf-0"
})(["", ";"], (props) => menuStyles(getMenuPosition(props.placement), {
  theme: props.theme,
  hidden: false,
  margin: "0",
  zIndex: props.zIndex,
  animationModifier: ".is-animated"
}));
StyledTooltipWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$b = "modals.tooltip_modal";
var StyledTooltipModal = styled_components_browser_esm_default.div.attrs((props) => ({
  "data-garden-id": COMPONENT_ID$b,
  "data-garden-version": "8.67.0",
  className: props.isAnimated && "is-animated"
})).withConfig({
  displayName: "StyledTooltipModal",
  componentId: "sc-42ubfr-0"
})(["padding:", "px;width:400px;", ";", ";"], (props) => props.theme.space.base * 5, (props) => {
  const computedArrowStyles = arrowStyles(getArrowPosition(props.placement), {
    size: `${props.theme.space.base * 2}px`,
    inset: "1px",
    animationModifier: ".is-animated"
  });
  if (props.isAnimated) {
    return props.hasArrow && props.transitionState === "entered" && computedArrowStyles;
  }
  return props.hasArrow && computedArrowStyles;
}, (props) => retrieveComponentStyles(COMPONENT_ID$b, props));
StyledTooltipModal.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$a = "modals.tooltip_modal.title";
var sizeStyles = (props) => `
  /* stylelint-disable-next-line property-no-unknown */
  padding-${props.theme.rtl ? "left" : "right"}: ${props.theme.space.base * 8}px;
  line-height: ${getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md)};
  font-size: ${props.theme.fontSizes.md};
`;
var StyledTooltipModalTitle = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$a,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledTooltipModalTitle",
  componentId: "sc-11xjgjs-0"
})(["margin:0;color:", ";font-weight:", ";", ";", ";"], (props) => props.theme.colors.foreground, (props) => props.theme.fontWeights.semibold, (props) => sizeStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID$a, props));
StyledTooltipModalTitle.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$9 = "modals.tooltip_modal.body";
var StyledTooltipModalBody = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$9,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledTooltipModalBody",
  componentId: "sc-195dkzj-0"
})(["display:block;margin:0;padding-top:", "px;line-height:", ";color:", ";font-size:", ";", ";"], (props) => props.theme.space.base * 1.5, (props) => getLineHeight(props.theme.lineHeights.md, props.theme.fontSizes.md), (props) => props.theme.colors.foreground, (props) => props.theme.fontSizes.md, (props) => retrieveComponentStyles(COMPONENT_ID$9, props));
StyledTooltipModalBody.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$8 = "modals.tooltip_modal.footer";
var StyledTooltipModalFooter = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$8,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledTooltipModalFooter",
  componentId: "sc-fm36a9-0"
})(["display:flex;flex-shrink:0;align-items:center;justify-content:flex-end;padding-top:", "px;", ";"], (p) => p.theme.space.base * 5, (props) => retrieveComponentStyles(COMPONENT_ID$8, props));
StyledTooltipModalFooter.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$7 = "modals.tooltip_modal.footer_item";
var StyledTooltipModalFooterItem = styled_components_browser_esm_default(StyledFooterItem).attrs({
  "data-garden-id": COMPONENT_ID$7,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledTooltipModalFooterItem",
  componentId: "sc-1nahj6p-0"
})(["", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$7, props));
StyledTooltipModalFooterItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$6 = "modals.tooltip_modal.close";
var StyledTooltipModalClose = styled_components_browser_esm_default(StyledClose).attrs({
  "data-garden-id": COMPONENT_ID$6,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledTooltipModalClose",
  componentId: "sc-1h2ke3q-0"
})(["top:", "px;", ":", ";width:", "px;height:", "px;", ";"], (props) => props.theme.space.base * 3.5, (props) => props.theme.rtl ? "left" : "right", (props) => `${props.theme.space.base * 3}px`, (props) => props.theme.space.base * 8, (props) => props.theme.space.base * 8, (props) => retrieveComponentStyles(COMPONENT_ID$6, props));
StyledTooltipModalClose.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$5 = "modals.drawer_modal";
var DRAWER_WIDTH = 380;
var boxShadow = (props) => {
  const {
    theme
  } = props;
  const {
    space,
    shadows
  } = theme;
  const offsetY = `${space.base * 5}px`;
  const blurRadius = `${space.base * 7}px`;
  const color = getColor("neutralHue", 800, theme, 0.35);
  return shadows.lg(offsetY, blurRadius, color);
};
var StyledDrawerModal = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$5,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledDrawerModal",
  componentId: "sc-i1sake-0"
})(["display:flex;position:fixed;top:0;", ":0;flex-direction:column;z-index:500;box-shadow:", ";background:", ";width:", "px;height:100%;overflow:auto;-webkit-overflow-scrolling:touch;font-family:", ";direction:", ";&.garden-drawer-transition-enter{transform:translateX(", "px);}&.garden-drawer-transition-enter-active{transform:translateX(0);transition:transform 0.25s ease-in-out;}&.garden-drawer-transition-exit-active{transform:translateX(", "px);transition:transform 0.25s ease-in-out;}&:focus{outline:none;}", ";"], (props) => props.theme.rtl ? "left" : "right", boxShadow, (props) => props.theme.colors.background, DRAWER_WIDTH, (props) => props.theme.fonts.system, (props) => props.theme.rtl && "rtl", (props) => props.theme.rtl ? -DRAWER_WIDTH : DRAWER_WIDTH, (props) => props.theme.rtl ? -DRAWER_WIDTH : DRAWER_WIDTH, (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledDrawerModal.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$4 = "modals.drawer_modal.close";
var BASE_MULTIPLIERS = {
  top: BASE_MULTIPLIERS$1.top,
  side: 2,
  size: BASE_MULTIPLIERS$1.size
};
var StyledDrawerModalClose = styled_components_browser_esm_default(StyledClose).attrs({
  "data-garden-id": COMPONENT_ID$4,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledDrawerModalClose",
  componentId: "sc-hrnaom-0"
})(["", ":", ";", ";"], (props) => props.theme.rtl ? "left" : "right", (props) => `${props.theme.space.base * BASE_MULTIPLIERS.side}px`, (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledDrawerModalClose.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "modals.drawer_modal.header";
var StyledDrawerModalHeader = styled_components_browser_esm_default(StyledHeader).attrs({
  "data-garden-id": COMPONENT_ID$3,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledDrawerModalHeader",
  componentId: "sc-1u04rqw-0"
})(["padding:", "px;", "  ", ";"], (props) => props.theme.space.base * 5, (props) => props.isCloseButtonPresent && `padding-${props.theme.rtl ? "left" : "right"}: ${props.theme.space.base * (BASE_MULTIPLIERS.size + BASE_MULTIPLIERS.side + 2)}px;`, (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledDrawerModalHeader.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "modals.drawer_modal.body";
var StyledDrawerModalBody = styled_components_browser_esm_default(StyledBody).attrs({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledDrawerModalBody",
  componentId: "sc-yafe2y-0"
})(["padding:", "px;", ";"], (props) => props.theme.space.base * 5, (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledDrawerModalBody.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "modals.drawer_modal.footer";
var StyledDrawerModalFooter = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledDrawerModalFooter",
  componentId: "sc-17if4ka-0"
})(["display:flex;flex-shrink:0;justify-content:flex-end;border-top:", ";padding:", "px;", ";"], (props) => `${props.theme.borders.sm} ${getColor("neutralHue", 200, props.theme)}`, (props) => props.theme.space.base * 5, (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledDrawerModalFooter.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "modals.drawer_modal.footer_item";
var StyledDrawerModalFooterItem = styled_components_browser_esm_default(StyledFooterItem).attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledDrawerModalFooterItem",
  componentId: "sc-1vbl885-0"
})(["", ";"], (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledDrawerModalFooterItem.defaultProps = {
  theme: DEFAULT_THEME
};
var ModalsContext = (0, import_react3.createContext)(void 0);
var useModalContext = () => {
  const context = (0, import_react3.useContext)(ModalsContext);
  if (context === void 0) {
    throw new Error("useModalContext must be used within a ModalsContext.Provider");
  }
  return context;
};
var isOverflowing = (element) => {
  const doc = ownerDocument2(element);
  const win = ownerWindow(doc);
  const isBody = element && element.tagName.toLowerCase() === "body";
  if (!isWindow(doc) && !isBody) {
    return element.scrollHeight > element.clientHeight;
  }
  const style2 = win.getComputedStyle(doc.body);
  const marginLeft = parseInt(style2.getPropertyValue("margin-left"), 10);
  const marginRight = parseInt(style2.getPropertyValue("margin-right"), 10);
  return marginLeft + doc.body.clientWidth + marginRight < win.innerWidth;
};
var Modal = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    backdropProps,
    children,
    onClose,
    isLarge,
    isCentered,
    isAnimated,
    id,
    appendToNode,
    focusOnMount,
    restoreFocus,
    ...modalProps
  } = _ref;
  const theme = (0, import_react3.useContext)(Me);
  const modalRef = (0, import_react3.useRef)(null);
  const environment = useDocument(theme);
  const [isCloseButtonPresent, setIsCloseButtonPresent] = (0, import_react3.useState)(false);
  const [hasHeader, setHasHeader] = (0, import_react3.useState)(false);
  const {
    getBackdropProps,
    getModalProps,
    getTitleProps,
    getContentProps,
    getCloseProps
  } = useModal({
    idPrefix: id,
    onClose,
    modalRef,
    focusOnMount,
    restoreFocus
  });
  useFocusVisible({
    scope: modalRef,
    relativeDocument: environment
  });
  (0, import_react3.useEffect)(() => {
    if (!environment) {
      return void 0;
    }
    const htmlElement = environment.querySelector("html");
    const bodyElement = environment.querySelector("body");
    let previousHtmlOverflow;
    let previousBodyPaddingRight;
    if (bodyElement) {
      if (isOverflowing(bodyElement)) {
        const scrollbarSize$1 = scrollbarSize();
        const bodyPaddingRight = parseInt(style(bodyElement, "paddingRight") || "0", 10);
        previousBodyPaddingRight = bodyElement.style.paddingRight;
        bodyElement.style.paddingRight = `${bodyPaddingRight + scrollbarSize$1}px`;
      }
      if (htmlElement) {
        previousHtmlOverflow = htmlElement.style.overflow;
        htmlElement.style.overflow = "hidden";
      }
      return () => {
        if (htmlElement) {
          htmlElement.style.overflow = previousHtmlOverflow;
        }
        bodyElement.style.paddingRight = previousBodyPaddingRight;
      };
    }
    return void 0;
  }, [environment]);
  const rootNode = (0, import_react3.useMemo)(() => {
    if (appendToNode) {
      return appendToNode;
    }
    if (environment) {
      return environment.body;
    }
    return void 0;
  }, [appendToNode, environment]);
  const value = (0, import_react3.useMemo)(() => ({
    isLarge,
    isCloseButtonPresent,
    hasHeader,
    setHasHeader,
    getTitleProps,
    getContentProps,
    getCloseProps,
    setIsCloseButtonPresent
  }), [isLarge, hasHeader, isCloseButtonPresent, getTitleProps, getContentProps, getCloseProps]);
  const modalContainerProps = getModalProps({
    "aria-describedby": void 0,
    ...hasHeader ? {} : {
      "aria-labelledby": void 0
    }
  });
  const attribute = hasHeader ? "aria-labelledby" : "aria-label";
  const defaultValue = hasHeader ? modalContainerProps["aria-labelledby"] : "Modal dialog";
  const labelValue = hasHeader ? modalContainerProps["aria-labelledby"] : modalProps["aria-label"];
  const ariaProps = {
    [attribute]: useText(Modal, {
      [attribute]: labelValue
    }, attribute, defaultValue)
  };
  if (!rootNode) {
    return null;
  }
  return (0, import_react_dom.createPortal)(import_react3.default.createElement(ModalsContext.Provider, {
    value
  }, import_react3.default.createElement(StyledBackdrop, _extends$2({
    isCentered,
    isAnimated
  }, getBackdropProps(backdropProps)), import_react3.default.createElement(StyledModal, _extends$2({
    isCentered,
    isAnimated,
    isLarge
  }, modalContainerProps, ariaProps, modalProps, {
    ref: react_merge_refs_esm_default([ref, modalRef])
  }), children))), rootNode);
});
Modal.displayName = "Modal";
Modal.propTypes = {
  backdropProps: import_prop_types3.default.object,
  isLarge: import_prop_types3.default.bool,
  isAnimated: import_prop_types3.default.bool,
  isCentered: import_prop_types3.default.bool,
  focusOnMount: import_prop_types3.default.bool,
  restoreFocus: import_prop_types3.default.bool,
  onClose: import_prop_types3.default.func,
  appendToNode: import_prop_types3.default.any
};
Modal.defaultProps = {
  isAnimated: true,
  isCentered: true
};
var Body$2 = (0, import_react3.forwardRef)((props, ref) => {
  const {
    getContentProps
  } = useModalContext();
  return import_react3.default.createElement(StyledBody, _extends$2({}, getContentProps(props), {
    ref
  }));
});
Body$2.displayName = "Body";
var _path;
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var SvgXStroke = function SvgXStroke2(props) {
  return React8.createElement("svg", _extends({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path || (_path = React8.createElement("path", {
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M3 13L13 3m0 10L3 3"
  })));
};
var Close$2 = (0, import_react3.forwardRef)((props, ref) => {
  const {
    getCloseProps,
    setIsCloseButtonPresent
  } = useModalContext();
  (0, import_react3.useEffect)(() => {
    setIsCloseButtonPresent(true);
    return () => setIsCloseButtonPresent(false);
  });
  const ariaLabel = useText(Close$2, props, "aria-label", "Close modal");
  return import_react3.default.createElement(StyledClose, _extends$2({}, getCloseProps({
    ...props,
    "aria-label": ariaLabel
  }), {
    ref
  }), import_react3.default.createElement(SvgXStroke, null));
});
Close$2.displayName = "Close";
var Footer$2 = import_react3.default.forwardRef((props, ref) => {
  const {
    isLarge
  } = useModalContext();
  return import_react3.default.createElement(StyledFooter, _extends$2({
    ref,
    isLarge
  }, props));
});
Footer$2.displayName = "Footer";
var FooterItem$2 = import_react3.default.forwardRef((props, ref) => import_react3.default.createElement(StyledFooterItem, _extends$2({
  ref
}, props)));
FooterItem$2.displayName = "FooterItem";
var Header$1 = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    children,
    tag,
    ...other
  } = _ref;
  const {
    isCloseButtonPresent,
    hasHeader,
    setHasHeader,
    getTitleProps
  } = useModalContext();
  (0, import_react3.useEffect)(() => {
    if (!hasHeader && setHasHeader) {
      setHasHeader(true);
    }
    return () => {
      if (hasHeader && setHasHeader) {
        setHasHeader(false);
      }
    };
  }, [hasHeader, setHasHeader]);
  return import_react3.default.createElement(StyledHeader, _extends$2({}, getTitleProps(other), {
    as: tag,
    isCloseButtonPresent,
    ref
  }), other.isDanger && import_react3.default.createElement(StyledDangerIcon, null), children);
});
Header$1.displayName = "Header";
Header$1.propTypes = {
  isDanger: import_prop_types3.default.bool,
  tag: import_prop_types3.default.any
};
Header$1.defaultProps = {
  tag: "div"
};
var TooltipModalContext = (0, import_react3.createContext)(void 0);
var useTooltipModalContext = () => {
  const context = (0, import_react3.useContext)(TooltipModalContext);
  if (context === void 0) {
    throw new Error("Element must be used within a TooltipModal component.");
  }
  return context;
};
var TitleComponent = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    children,
    tag,
    ...other
  } = _ref;
  const {
    getTitleProps,
    hasTitle,
    setHasTitle
  } = useTooltipModalContext();
  (0, import_react3.useEffect)(() => {
    if (!hasTitle && setHasTitle) {
      setHasTitle(true);
    }
    return () => {
      if (hasTitle && setHasTitle) {
        setHasTitle(false);
      }
    };
  }, [hasTitle, setHasTitle]);
  return import_react3.default.createElement(StyledTooltipModalTitle, _extends$2({}, getTitleProps(other), {
    as: tag,
    ref
  }), children);
});
TitleComponent.displayName = "TooltipModal.Title";
TitleComponent.propTypes = {
  tag: import_prop_types3.default.any
};
TitleComponent.defaultProps = {
  tag: "div"
};
var Title = TitleComponent;
var BodyComponent$1 = (0, import_react3.forwardRef)((props, ref) => {
  const {
    getContentProps
  } = useTooltipModalContext();
  return import_react3.default.createElement(StyledTooltipModalBody, _extends$2({}, getContentProps(props), {
    ref
  }));
});
BodyComponent$1.displayName = "TooltipModal.Body";
var Body$1 = BodyComponent$1;
var CloseComponent$1 = (0, import_react3.forwardRef)((props, ref) => {
  const {
    getCloseProps
  } = useTooltipModalContext();
  const ariaLabel = useText(CloseComponent$1, props, "aria-label", "Close tooltip");
  return import_react3.default.createElement(StyledTooltipModalClose, _extends$2({}, getCloseProps({
    ...props,
    "aria-label": ariaLabel
  }), {
    ref
  }), import_react3.default.createElement(SvgXStroke, null));
});
CloseComponent$1.displayName = "TooltipModal.Close";
var Close$1 = CloseComponent$1;
var FooterComponent$1 = (0, import_react3.forwardRef)((props, ref) => import_react3.default.createElement(StyledTooltipModalFooter, _extends$2({
  ref
}, props)));
FooterComponent$1.displayName = "TooltipModal.Footer";
var Footer$1 = FooterComponent$1;
var FooterItemComponent$1 = (0, import_react3.forwardRef)((props, ref) => import_react3.default.createElement(StyledTooltipModalFooterItem, _extends$2({
  ref
}, props)));
FooterItemComponent$1.displayName = "TooltipModal.FooterItem";
var FooterItem$1 = FooterItemComponent$1;
var TooltipModalComponent = import_react3.default.forwardRef((_ref, ref) => {
  let {
    referenceElement,
    popperModifiers,
    placement,
    onClose,
    hasArrow,
    isAnimated,
    zIndex,
    backdropProps,
    focusOnMount,
    restoreFocus,
    id,
    ...props
  } = _ref;
  const theme = (0, import_react3.useContext)(Me);
  const previousReferenceElementRef = (0, import_react3.useRef)();
  const modalRef = (0, import_react3.useRef)(null);
  const transitionRef = (0, import_react3.useRef)(null);
  const [popperElement, setPopperElement] = (0, import_react3.useState)();
  const [hasTitle, setHasTitle] = (0, import_react3.useState)(false);
  const {
    getTitleProps,
    getCloseProps,
    getContentProps,
    getBackdropProps,
    getModalProps
  } = useModal({
    idPrefix: id,
    onClose,
    modalRef,
    focusOnMount,
    restoreFocus: false
  });
  (0, import_react3.useEffect)(() => {
    if (!referenceElement && previousReferenceElementRef.current && restoreFocus) {
      previousReferenceElementRef.current.focus();
    }
    previousReferenceElementRef.current = referenceElement;
  }, [referenceElement, restoreFocus]);
  const popperPlacement = (0, import_react3.useMemo)(() => theme.rtl ? getRtlPopperPlacement(placement) : getPopperPlacement(placement), [placement, theme.rtl]);
  const {
    styles,
    attributes,
    state
  } = usePopper(referenceElement, popperElement, {
    placement: popperPlacement,
    modifiers: [{
      name: "offset",
      options: {
        offset: [0, theme.space.base * 3]
      }
    }, ...popperModifiers || []]
  });
  const modalProps = getModalProps({
    "aria-describedby": void 0,
    ...hasTitle ? {} : {
      "aria-labelledby": void 0
    }
  });
  const attribute = hasTitle ? "aria-labelledby" : "aria-label";
  const defaultValue = hasTitle ? modalProps["aria-labelledby"] : "Modal dialog";
  const labelValue = hasTitle ? modalProps["aria-labelledby"] : props["aria-label"];
  const ariaProps = {
    [attribute]: useText(TooltipModalComponent, {
      [attribute]: labelValue
    }, attribute, defaultValue)
  };
  const value = {
    hasTitle,
    setHasTitle,
    getTitleProps,
    getContentProps,
    getCloseProps
  };
  return import_react3.default.createElement(CSSTransition_default, {
    unmountOnExit: true,
    timeout: isAnimated ? 200 : 0,
    in: Boolean(referenceElement),
    classNames: isAnimated ? "garden-tooltip-modal-transition" : "",
    nodeRef: transitionRef
  }, (transitionState) => {
    return import_react3.default.createElement(TooltipModalContext.Provider, {
      value
    }, import_react3.default.createElement(StyledTooltipModalBackdrop, _extends$2({}, getBackdropProps(), backdropProps, {
      ref: transitionRef
    }), import_react3.default.createElement(StyledTooltipWrapper, _extends$2({
      ref: setPopperElement,
      style: styles.popper,
      placement: state ? state.placement : void 0,
      zIndex,
      isAnimated
    }, attributes.popper), import_react3.default.createElement(StyledTooltipModal, _extends$2({
      transitionState,
      placement: state ? state.placement : "top",
      hasArrow,
      isAnimated
    }, modalProps, ariaProps, props, {
      ref: react_merge_refs_esm_default([modalRef, ref])
    })))));
  });
});
TooltipModalComponent.displayName = "TooltipModal";
TooltipModalComponent.defaultProps = {
  placement: "auto",
  hasArrow: true,
  focusOnMount: true,
  restoreFocus: true
};
TooltipModalComponent.propTypes = {
  referenceElement: import_prop_types3.default.any,
  popperModifiers: import_prop_types3.default.any,
  placement: import_prop_types3.default.any,
  isAnimated: import_prop_types3.default.bool,
  hasArrow: import_prop_types3.default.bool,
  zIndex: import_prop_types3.default.number,
  onClose: import_prop_types3.default.func,
  backdropProps: import_prop_types3.default.any,
  focusOnMount: import_prop_types3.default.bool,
  restoreFocus: import_prop_types3.default.bool
};
var TooltipModal = TooltipModalComponent;
TooltipModal.Body = Body$1;
TooltipModal.Close = Close$1;
TooltipModal.Footer = Footer$1;
TooltipModal.FooterItem = FooterItem$1;
TooltipModal.Title = Title;
function activeElement2(doc) {
  if (doc === void 0) {
    doc = ownerDocument2();
  }
  try {
    var active = doc.activeElement;
    if (!active || !active.nodeName)
      return null;
    return active;
  } catch (e) {
    return doc.body;
  }
}
var HeaderComponent = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    tag,
    ...other
  } = _ref;
  const {
    isCloseButtonPresent,
    hasHeader,
    setHasHeader,
    getTitleProps
  } = useModalContext();
  (0, import_react3.useEffect)(() => {
    if (!hasHeader && setHasHeader) {
      setHasHeader(true);
    }
    return () => {
      if (hasHeader && setHasHeader) {
        setHasHeader(false);
      }
    };
  }, [hasHeader, setHasHeader]);
  return import_react3.default.createElement(StyledDrawerModalHeader, _extends$2({}, getTitleProps(other), {
    as: tag,
    isCloseButtonPresent,
    ref
  }));
});
HeaderComponent.displayName = "DrawerModal.Header";
HeaderComponent.propTypes = {
  tag: import_prop_types3.default.any
};
HeaderComponent.defaultProps = {
  tag: "div"
};
var Header = HeaderComponent;
var BodyComponent = (0, import_react3.forwardRef)((props, ref) => {
  const {
    getContentProps
  } = useModalContext();
  return import_react3.default.createElement(StyledDrawerModalBody, _extends$2({}, getContentProps(props), {
    ref
  }), props.children);
});
BodyComponent.displayName = "DrawerModal.Body";
var Body = BodyComponent;
var CloseComponent = (0, import_react3.forwardRef)((props, ref) => {
  const {
    getCloseProps,
    setIsCloseButtonPresent
  } = useModalContext();
  (0, import_react3.useEffect)(() => {
    setIsCloseButtonPresent(true);
    return () => setIsCloseButtonPresent(false);
  });
  const ariaLabel = useText(CloseComponent, props, "aria-label", "Close drawer");
  return import_react3.default.createElement(StyledDrawerModalClose, _extends$2({}, getCloseProps({
    ...props,
    "aria-label": ariaLabel
  }), {
    ref
  }), import_react3.default.createElement(SvgXStroke, null));
});
CloseComponent.displayName = "DrawerModal.Close";
var Close = CloseComponent;
var FooterComponent = (0, import_react3.forwardRef)((props, ref) => import_react3.default.createElement(StyledDrawerModalFooter, _extends$2({
  ref
}, props)));
FooterComponent.displayName = "DrawerModal.Footer";
var Footer = FooterComponent;
var FooterItemComponent = (0, import_react3.forwardRef)((props, ref) => import_react3.default.createElement(StyledDrawerModalFooterItem, _extends$2({
  ref
}, props)));
FooterItemComponent.displayName = "DrawerModal.FooterItem";
var FooterItem = FooterItemComponent;
var DrawerModalComponent = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    id,
    isOpen,
    onClose,
    backdropProps,
    appendToNode,
    focusOnMount,
    restoreFocus,
    ...props
  } = _ref;
  const modalRef = (0, import_react3.useRef)(null);
  const transitionRef = (0, import_react3.useRef)(null);
  const triggerRef = (0, import_react3.useRef)(null);
  const theme = (0, import_react3.useContext)(Me);
  const environment = useDocument(theme);
  const [isCloseButtonPresent, setIsCloseButtonPresent] = (0, import_react3.useState)(false);
  const [hasHeader, setHasHeader] = (0, import_react3.useState)(false);
  useFocusVisible({
    scope: modalRef,
    relativeDocument: modalRef.current
  });
  const {
    getTitleProps,
    getCloseProps,
    getContentProps,
    getBackdropProps,
    getModalProps
  } = useModal({
    idPrefix: id,
    modalRef,
    focusOnMount: false,
    restoreFocus: false,
    environment,
    onClose
  });
  (0, import_react3.useEffect)(() => {
    if (environment) {
      if (isOpen && modalRef.current) {
        if (restoreFocus) {
          triggerRef.current = activeElement2(environment);
        }
        if (focusOnMount) {
          modalRef.current.focus();
        }
      }
      if (!isOpen && triggerRef.current) {
        triggerRef.current.focus();
      }
    }
    return () => {
      if (!(restoreFocus && isOpen)) {
        triggerRef.current = null;
      }
    };
  }, [environment, restoreFocus, focusOnMount, isOpen]);
  (0, import_react3.useEffect)(() => {
    if (!environment) {
      return void 0;
    }
    const htmlElement = environment.querySelector("html");
    let previousHtmlOverflow;
    if (htmlElement && isOpen) {
      previousHtmlOverflow = htmlElement.style.overflow;
      htmlElement.style.overflow = "hidden";
    }
    return () => {
      if (htmlElement && isOpen) {
        htmlElement.style.overflow = previousHtmlOverflow;
      }
    };
  }, [environment, isOpen]);
  const rootNode = (0, import_react3.useMemo)(() => {
    if (appendToNode) {
      return appendToNode;
    }
    if (environment) {
      return environment.body;
    }
    return void 0;
  }, [appendToNode, environment]);
  const value = (0, import_react3.useMemo)(() => ({
    isCloseButtonPresent,
    hasHeader,
    setHasHeader,
    getTitleProps,
    getContentProps,
    getCloseProps,
    setIsCloseButtonPresent
  }), [isCloseButtonPresent, hasHeader, getTitleProps, getContentProps, getCloseProps]);
  const modalProps = getModalProps({
    "aria-describedby": void 0,
    ...hasHeader ? {} : {
      "aria-labelledby": void 0
    }
  });
  const attribute = hasHeader ? "aria-labelledby" : "aria-label";
  const defaultValue = hasHeader ? modalProps["aria-labelledby"] : "Modal dialog";
  const labelValue = hasHeader ? modalProps["aria-labelledby"] : props["aria-label"];
  const ariaProps = {
    [attribute]: useText(DrawerModalComponent, {
      [attribute]: labelValue
    }, attribute, defaultValue)
  };
  if (!rootNode) {
    return null;
  }
  return import_react_dom.default.createPortal(import_react3.default.createElement(ModalsContext.Provider, {
    value
  }, import_react3.default.createElement(CSSTransition_default, {
    in: isOpen,
    timeout: 250,
    unmountOnExit: true,
    classNames: "garden-drawer-transition",
    nodeRef: transitionRef
  }, import_react3.default.createElement(StyledBackdrop, _extends$2({
    isAnimated: true
  }, getBackdropProps(backdropProps)), import_react3.default.createElement(StyledDrawerModal, _extends$2({}, modalProps, ariaProps, props, {
    ref: react_merge_refs_esm_default([ref, modalRef, transitionRef])
  }))))), rootNode);
});
DrawerModalComponent.displayName = "DrawerModal";
DrawerModalComponent.propTypes = {
  backdropProps: import_prop_types3.default.object,
  focusOnMount: import_prop_types3.default.bool,
  restoreFocus: import_prop_types3.default.bool,
  onClose: import_prop_types3.default.func,
  appendToNode: import_prop_types3.default.any,
  isOpen: import_prop_types3.default.bool
};
DrawerModalComponent.defaultProps = {
  focusOnMount: true,
  restoreFocus: true
};
var DrawerModal = DrawerModalComponent;
DrawerModal.Body = Body;
DrawerModal.Close = Close;
DrawerModal.Footer = Footer;
DrawerModal.FooterItem = FooterItem;
DrawerModal.Header = Header;
var PLACEMENT = ["auto", "top", "top-start", "top-end", "bottom", "bottom-start", "bottom-end", "end", "end-top", "end-bottom", "start", "start-top", "start-bottom"];
export {
  Body$2 as Body,
  Close$2 as Close,
  DrawerModal,
  Footer$2 as Footer,
  FooterItem$2 as FooterItem,
  Header$1 as Header,
  Modal,
  PLACEMENT,
  TooltipModal
};
/*! Bundled license information:

tabbable/dist/index.esm.js:
  (*!
  * tabbable 6.1.2
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  *)
*/
//# sourceMappingURL=@zendeskgarden_react-modals.js.map
