{"version": 3, "sources": ["../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../node_modules/use-sync-external-store/shim/index.js", "../../node_modules/@tanstack/query-core/src/subscribable.ts", "../../node_modules/@tanstack/query-core/src/utils.ts", "../../node_modules/@tanstack/query-core/src/focusManager.ts", "../../node_modules/@tanstack/query-core/src/onlineManager.ts", "../../node_modules/@tanstack/query-core/src/retryer.ts", "../../node_modules/@tanstack/query-core/src/logger.ts", "../../node_modules/@tanstack/query-core/src/notifyManager.ts", "../../node_modules/@tanstack/query-core/src/removable.ts", "../../node_modules/@tanstack/query-core/src/query.ts", "../../node_modules/@tanstack/query-core/src/queryCache.ts", "../../node_modules/@tanstack/query-core/src/mutation.ts", "../../node_modules/@tanstack/query-core/src/mutationCache.ts", "../../node_modules/@tanstack/query-core/src/infiniteQueryBehavior.ts", "../../node_modules/@tanstack/query-core/src/queryClient.ts", "../../node_modules/@tanstack/query-core/src/queryObserver.ts", "../../node_modules/@tanstack/query-core/src/queriesObserver.ts", "../../node_modules/@tanstack/query-core/src/infiniteQueryObserver.ts", "../../node_modules/@tanstack/query-core/src/mutationObserver.ts", "../../node_modules/@tanstack/query-core/src/hydration.ts", "../../node_modules/@tanstack/react-query/src/reactBatchedUpdates.ts", "../../node_modules/@tanstack/react-query/src/setBatchUpdatesFn.ts", "../../node_modules/@tanstack/react-query/src/useSyncExternalStore.ts", "../../node_modules/@tanstack/react-query/src/QueryClientProvider.tsx", "../../node_modules/@tanstack/react-query/src/isRestoring.tsx", "../../node_modules/@tanstack/react-query/src/QueryErrorResetBoundary.tsx", "../../node_modules/@tanstack/react-query/src/utils.ts", "../../node_modules/@tanstack/react-query/src/errorBoundaryUtils.ts", "../../node_modules/@tanstack/react-query/src/suspense.ts", "../../node_modules/@tanstack/react-query/src/useQueries.ts", "../../node_modules/@tanstack/react-query/src/useBaseQuery.ts", "../../node_modules/@tanstack/react-query/src/useQuery.ts", "../../node_modules/@tanstack/react-query/src/Hydrate.tsx", "../../node_modules/@tanstack/react-query/src/useIsFetching.ts", "../../node_modules/@tanstack/react-query/src/useIsMutating.ts", "../../node_modules/@tanstack/react-query/src/useMutation.ts", "../../node_modules/@tanstack/react-query/src/useInfiniteQuery.ts"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n// dispatch for CommonJS interop named imports.\n\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\nvar didWarnOld18Alpha = false;\nvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  {\n    if (!didWarnOld18Alpha) {\n      if (React.startTransition !== undefined) {\n        didWarnOld18Alpha = true;\n\n        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  var value = getSnapshot();\n\n  {\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n\n      if (!objectIs(value, cachedValue)) {\n        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  var _useState = useState({\n    inst: {\n      value: value,\n      getSnapshot: getSnapshot\n    }\n  }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n\n    var handleStoreChange = function () {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst: inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange);\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  var prevValue = inst.value;\n\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\nvar isServerEnvironment = !canUseDOM;\n\nvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\nvar useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n\nexports.useSyncExternalStore = useSyncExternalStore$2;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "type Listener = () => void\n\nexport class Subscribable<TListener extends Function = Listener> {\n  protected listeners: Set<{ listener: TListener }>\n\n  constructor() {\n    this.listeners = new Set()\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    const identity = { listener }\n    this.listeners.add(identity)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(identity)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n", "import type { Mutation } from './mutation'\nimport type { Query } from './query'\nimport type {\n  FetchStatus,\n  MutationFunction,\n  MutationKey,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n} from './types'\n\n// TYPES\n\nexport interface QueryFilters {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: QueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (mutation: Mutation<any, any, any>) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: Mutation<PERSON>ey\n  /**\n   * Include or exclude fetching mutations\n   */\n  fetching?: boolean\n}\n\nexport type DataUpdateFunction<TInput, TOutput> = (input: TInput) => TOutput\n\nexport type Updater<TInput, TOutput> =\n  | TOutput\n  | DataUpdateFunction<TInput, TOutput>\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in window\n\nexport function noop(): undefined {\n  return undefined\n}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as DataUpdateFunction<TInput, TOutput>)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function difference<T>(array1: T[], array2: T[]): T[] {\n  return array1.filter((x) => !array2.includes(x))\n}\n\nexport function replaceAt<T>(array: T[], index: number, value: T): T[] {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function parseQueryArgs<\n  TOptions extends QueryOptions<any, any, any, TQueryKey>,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | TOptions,\n  arg2?: QueryFunction<any, TQueryKey> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (!isQueryKey(arg1)) {\n    return arg1 as TOptions\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions\n  }\n\n  return { ...arg2, queryKey: arg1 } as TOptions\n}\n\nexport function parseMutationArgs<\n  TOptions extends MutationOptions<any, any, any, any>,\n>(\n  arg1: MutationKey | MutationFunction<any, any> | TOptions,\n  arg2?: MutationFunction<any, any> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3, mutationKey: arg1, mutationFn: arg2 } as TOptions\n    }\n    return { ...arg2, mutationKey: arg1 } as TOptions\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2, mutationFn: arg1 } as TOptions\n  }\n\n  return { ...arg1 } as TOptions\n}\n\nexport function parseFilterArgs<\n  TFilters extends QueryFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1) ? [{ ...arg2, queryKey: arg1 }, arg3] : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function parseMutationFilterArgs<\n  TFilters extends MutationFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1)\n      ? [{ ...arg2, mutationKey: arg1 }, arg3]\n      : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (\n    typeof fetchStatus !== 'undefined' &&\n    fetchStatus !== query.state.fetchStatus\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, fetching, predicate, mutationKey } = filters\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (\n        hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)\n      ) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (\n    typeof fetching === 'boolean' &&\n    (mutation.state.status === 'loading') !== fetching\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: QueryOptions<any, any, any, TQueryKey>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashQueryKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashQueryKey(queryKey: QueryKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean {\n  return partialDeepEqual(a, b)\n}\n\n/**\n * Checks if `b` partially matches with `a`.\n */\nexport function partialDeepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some((key) => !partialDeepEqual(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aSize = array ? a.length : Object.keys(a).length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      copy[key] = replaceEqualDeep(a[key], b[key])\n      if (copy[key] === a[key]) {\n        equalItems++\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\nexport function shallowEqualObjects<T>(a: T, b: T): boolean {\n  if ((a && !b) || (b && !a)) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isQueryKey(value: unknown): value is QueryKey {\n  return Array.isArray(value)\n}\n\nexport function isError(value: any): value is Error {\n  return value instanceof Error\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nexport function scheduleMicrotask(callback: () => void) {\n  sleep(0).then(callback)\n}\n\nexport function getAbortController(): AbortController | undefined {\n  if (typeof AbortController === 'function') {\n    return new AbortController()\n  }\n  return\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual?.(prevData, data)) {\n    return prevData as TData\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data)\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable {\n  private focused?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibillitychange and focus\n        window.addEventListener('visibilitychange', listener, false)\n        window.addEventListener('focus', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n          window.removeEventListener('focus', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    this.focused = focused\n\n    if (focused) {\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.focused === 'boolean') {\n      return this.focused\n    }\n\n    // document global can be unavailable in react native\n    if (typeof document === 'undefined') {\n      return true\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(\n      document.visibilityState,\n    )\n  }\n}\n\nexport const focusManager = new FocusManager()\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setOnline: (online?: boolean) => void,\n) => (() => void) | undefined\n\nconst onlineEvents = ['online', 'offline'] as const\n\nexport class OnlineManager extends Subscribable {\n  private online?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onOnline()\n        // Listen to online\n        onlineEvents.forEach((event) => {\n          window.addEventListener(event, listener, false)\n        })\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach((event) => {\n            window.removeEventListener(event, listener)\n          })\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((online?: boolean) => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online)\n      } else {\n        this.onOnline()\n      }\n    })\n  }\n\n  setOnline(online?: boolean): void {\n    this.online = online\n\n    if (online) {\n      this.onOnline()\n    }\n  }\n\n  onOnline(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isOnline(): boolean {\n    if (typeof this.online === 'boolean') {\n      return this.online\n    }\n\n    if (\n      typeof navigator === 'undefined' ||\n      typeof navigator.onLine === 'undefined'\n    ) {\n      return true\n    }\n\n    return navigator.onLine\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n", "import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { sleep } from './utils'\nimport type { CancelOptions, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = unknown> {\n  fn: () => TData | Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = unknown>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => boolean) | undefined\n  let promiseResolve: (data: TData) => void\n  let promiseReject: (error: TError) => void\n\n  const promise = new Promise<TData>((outerResolve, outerReject) => {\n    promiseResolve = outerResolve\n    promiseReject = outerReject\n  })\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const shouldPause = () =>\n    !focusManager.isFocused() ||\n    (config.networkMode !== 'always' && !onlineManager.isOnline())\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      promiseResolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      promiseReject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        const canContinue = isResolved || !shouldPause()\n        if (canContinue) {\n          continueResolve(value)\n        }\n        return canContinue\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // Execute query\n    try {\n      promiseOrValue = config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? 3\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            if (shouldPause()) {\n              return pause()\n            }\n            return\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  // Start loop\n  if (canFetch(config.networkMode)) {\n    run()\n  } else {\n    pause().then(run)\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn?.()\n      return didContinue ? promise : Promise.resolve()\n    },\n    cancelRetry,\n    continueRetry,\n  }\n}\n", "export interface Logger {\n  log: LogFunction\n  warn: LogFunction\n  error: LogFunction\n}\n\ntype LogFunction = (...args: any[]) => void\n\nexport const defaultLogger: Logger = console\n", "import { scheduleMicrotask } from './utils'\n\n// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\nexport function createNotifyManager() {\n  let queue: NotifyCallback[] = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n\n  const batch = <T>(callback: () => T): T => {\n    let result\n    transactions++\n    try {\n      result = callback()\n    } finally {\n      transactions--\n      if (!transactions) {\n        flush()\n      }\n    }\n    return result\n  }\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleMicrotask(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  const batchCalls = <T extends Function>(callback: T): T => {\n    return ((...args: any[]) => {\n      schedule(() => {\n        callback(...args)\n      })\n    }) as any\n  }\n\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleMicrotask(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  const setNotifyFunction = (fn: NotifyFunction) => {\n    notifyFn = fn\n  }\n\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  const setBatchNotifyFunction = (fn: BatchNotifyFunction) => {\n    batchNotifyFn = fn\n  }\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction,\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n", "import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  cacheTime!: number\n  private gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.cacheTime)\n    }\n  }\n\n  protected updateCacheTime(newCacheTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(\n      this.cacheTime || 0,\n      newCacheTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout)\n      this.gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n", "import { getAbortController, noop, replaceData, timeUntilStale } from './utils'\nimport type {\n  InitialDataFunction,\n  QueryKey,\n  QueryOptions,\n  QueryStatus,\n  QueryFunctionContext,\n  QueryMeta,\n  CancelOptions,\n  SetDataOptions,\n  FetchStatus,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport type { Logger } from './logger'\nimport { defaultLogger } from './logger'\nimport { notifyManager } from './notifyManager'\nimport type { Retryer } from './retryer'\nimport { isCancelledError, canFetch, createRetryer } from './retryer'\nimport { Removable } from './removable'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  cache: QueryCache\n  queryKey: TQueryKey\n  queryHash: string\n  logger?: Logger\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = unknown> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: any\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal?: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n  ) => void\n}\n\nexport interface FetchOptions {\n  cancelRefetch?: boolean\n  meta?: any\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: any\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  initialState: QueryState<TData, TError>\n  revertState?: QueryState<TData, TError>\n  state: QueryState<TData, TError>\n  isFetchingOptimistic?: boolean\n\n  private cache: QueryCache\n  private logger: Logger\n  private promise?: Promise<TData>\n  private retryer?: Retryer<TData>\n  private observers: QueryObserver<any, any, any, any, any>[]\n  private defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  private abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.abortSignalConsumed = false\n    this.defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.cache = config.cache\n    this.logger = config.logger || defaultLogger\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.initialState = config.state || getDefaultState(this.options)\n    this.state = this.initialState\n    this.scheduleGc()\n  }\n\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  private setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.promise\n    this.retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some((observer) => observer.options.enabled !== false)\n  }\n\n  isDisabled(): boolean {\n    return this.getObserversCount() > 0 && !this.isActive()\n  }\n\n  isStale(): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      this.observers.some((observer) => observer.getCurrentResult().isStale)\n    )\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({ revert: true })\n          } else {\n            this.retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({ silent: true })\n      } else if (this.promise) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.retryer?.continueRetry()\n        // Return current promise if we are already fetching\n        return this.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (!Array.isArray(this.options.queryKey)) {\n      if (process.env.NODE_ENV !== 'production') {\n        this.logger.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = getAbortController()\n\n    // Create query function context\n    const queryFnContext: QueryFunctionContext<TQueryKey> = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta,\n    }\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true\n            return abortController.signal\n          }\n          return undefined\n        },\n      })\n    }\n\n    addSignalProperty(queryFnContext)\n\n    // Create fetch function\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject('Missing queryFn')\n      }\n      this.abortSignalConsumed = false\n      return this.options.queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const context: FetchContext<TQueryFnData, TError, TData, TQueryKey> = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(context)\n\n    // Store state in case the current fetch needs to be reverted\n    this.revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.cache.config.onError?.(error, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      }\n      this.isFetchingOptimistic = false\n    }\n\n    // Try to fetch the data\n    this.retryer = createRetryer({\n      fn: context.fetchFn as () => TData,\n      abort: abortController?.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        this.setData(data as TData)\n\n        // Notify cache callback\n        this.cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc()\n        }\n        this.isFetchingOptimistic = false\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n    })\n\n    this.promise = this.retryer.promise\n\n    return this.promise\n  }\n\n  private dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: action.meta ?? null,\n            fetchStatus: canFetch(this.options.networkMode)\n              ? 'fetching'\n              : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading',\n            }),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error as unknown\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState }\n          }\n\n          return {\n            ...state,\n            error: error as TError,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error as TError,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate(action)\n      })\n\n      this.cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = typeof data !== 'undefined'\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle',\n  }\n}\n", "import type { QueryFilters } from './utils'\nimport { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils'\nimport type { Action, QueryState } from './query'\nimport { Query } from './query'\nimport type { NotifyEvent, QueryKey, QueryOptions } from './types'\nimport { notifyManager } from './notifyManager'\nimport type { QueryClient } from './queryClient'\nimport { Subscribable } from './subscribable'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (error: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface QueryHashMap {\n  [hash: string]: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ntype QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  config: QueryCacheConfig\n\n  private queries: Query<any, any, any, any>[]\n  private queriesMap: QueryHashMap\n\n  constructor(config?: QueryCacheConfig) {\n    super()\n    this.config = config || {}\n    this.queries = []\n    this.queriesMap = {}\n  }\n\n  build<TQueryFnData, TError, TData, TQueryKey extends QueryKey>(\n    client: QueryClient,\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey!\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query\n      this.queries.push(query)\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.queriesMap[query.queryHash]\n\n    if (queryInMap) {\n      query.destroy()\n\n      this.queries = this.queries.filter((x) => x !== query)\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash]\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.queriesMap[queryHash]\n  }\n\n  getAll(): Query[] {\n    return this.queries\n  }\n\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    arg1: QueryKey,\n    arg2?: QueryFilters,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const [filters] = parseFilterArgs(arg1, arg2)\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.queries.find((query) => matchQuery(filters, query))\n  }\n\n  findAll(queryKey?: QueryKey, filters?: QueryFilters): Query[]\n  findAll(filters?: QueryFilters): Query[]\n  findAll(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): Query[]\n  findAll(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): Query[] {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    return Object.keys(filters).length > 0\n      ? this.queries.filter((query) => matchQuery(filters, query))\n      : this.queries\n  }\n\n  notify(event: QueryCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n", "import type { MutationOptions, MutationStatus, MutationMeta } from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { Logger } from './logger'\nimport { defaultLogger } from './logger'\nimport { notify<PERSON><PERSON>ger } from './notifyManager'\nimport { Removable } from './removable'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\nimport { canFetch, createRetryer } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  logger?: Logger\n  defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n  meta?: MutationMeta\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface LoadingAction<TVariables, TContext> {\n  type: 'loading'\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError, TVariables, TContext> {\n  type: 'setState'\n  state: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | LoadingAction<TVariables, TContext>\n  | PauseAction\n  | SetStateAction<TData, TError, TVariables, TContext>\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  mutationId: number\n\n  private observers: MutationObserver<TData, TError, TVariables, TContext>[]\n  private defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  private mutationCache: MutationCache\n  private logger: Logger\n  private retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.defaultOptions = config.defaultOptions\n    this.mutationId = config.mutationId\n    this.mutationCache = config.mutationCache\n    this.logger = config.logger || defaultLogger\n    this.observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options?: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  setState(state: MutationState<TData, TError, TVariables, TContext>): void {\n    this.dispatch({ type: 'setState', state })\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.observers = this.observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc()\n      } else {\n        this.mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return this.retryer?.continue() ?? this.execute()\n  }\n\n  async execute(): Promise<TData> {\n    const executeMutation = () => {\n      this.retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found')\n          }\n          return this.options.mutationFn(this.state.variables!)\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({ type: 'failed', failureCount, error })\n        },\n        onPause: () => {\n          this.dispatch({ type: 'pause' })\n        },\n        onContinue: () => {\n          this.dispatch({ type: 'continue' })\n        },\n        retry: this.options.retry ?? 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode,\n      })\n\n      return this.retryer.promise\n    }\n\n    const restored = this.state.status === 'loading'\n    try {\n      if (!restored) {\n        this.dispatch({ type: 'loading', variables: this.options.variables! })\n        // Notify cache callback\n        await this.mutationCache.config.onMutate?.(\n          this.state.variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(this.state.variables!)\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables,\n          })\n        }\n      }\n      const data = await executeMutation()\n\n      // Notify cache callback\n      await this.mutationCache.config.onSuccess?.(\n        data,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(\n        data,\n        this.state.variables!,\n        this.state.context!,\n      )\n\n      // Notify cache callback\n      await this.mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(\n        data,\n        null,\n        this.state.variables!,\n        this.state.context,\n      )\n\n      this.dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.mutationCache.config.onError?.(\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n\n        await this.options.onError?.(\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.mutationCache.config.onSettled?.(\n          undefined,\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.dispatch({ type: 'error', error: error as TError })\n      }\n    }\n  }\n\n  private dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'loading':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables,\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n  }\n}\n", "import type { MutationObserver } from './mutationObserver'\nimport type { MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport { notifyManager } from './notifyManager'\nimport type { Action, MutationState } from './mutation'\nimport { Mutation } from './mutation'\nimport type { MutationFilters } from './utils'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\ntype MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  config: MutationCacheConfig\n\n  private mutations: Mutation<any, any, any, any>[]\n  private mutationId: number\n  private resuming: Promise<unknown> | undefined\n\n  constructor(config?: MutationCacheConfig) {\n    super()\n    this.config = config || {}\n    this.mutations = []\n    this.mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey\n        ? client.getMutationDefaults(options.mutationKey)\n        : undefined,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.mutations.push(mutation)\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    this.mutations = this.mutations.filter((x) => x !== mutation)\n    this.notify({ type: 'removed', mutation })\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.mutations.forEach((mutation) => {\n        this.remove(mutation)\n      })\n    })\n  }\n\n  getAll(): Mutation[] {\n    return this.mutations\n  }\n\n  find<TData = unknown, TError = unknown, TVariables = any, TContext = unknown>(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.mutations.find((mutation) => matchMutation(filters, mutation))\n  }\n\n  findAll(filters: MutationFilters): Mutation[] {\n    return this.mutations.filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    this.resuming = (this.resuming ?? Promise.resolve())\n      .then(() => {\n        const pausedMutations = this.mutations.filter((x) => x.state.isPaused)\n        return notifyManager.batch(() =>\n          pausedMutations.reduce(\n            (promise, mutation) =>\n              promise.then(() => mutation.continue().catch(noop)),\n            Promise.resolve() as Promise<unknown>,\n          ),\n        )\n      })\n      .then(() => {\n        this.resuming = undefined\n      })\n\n    return this.resuming\n  }\n}\n", "import type { QueryBehavior } from './query'\n\nimport type {\n  InfiniteData,\n  QueryFunctionContext,\n  QueryOptions,\n  RefetchQueryFilters,\n} from './types'\n\nexport function infiniteQueryBehavior<\n  TQueryFnData,\n  TError,\n  TData,\n>(): QueryBehavior<TQueryFnData, TError, InfiniteData<TData>> {\n  return {\n    onFetch: (context) => {\n      context.fetchFn = () => {\n        const refetchPage: RefetchQueryFilters['refetchPage'] | undefined =\n          context.fetchOptions?.meta?.refetchPage\n        const fetchMore = context.fetchOptions?.meta?.fetchMore\n        const pageParam = fetchMore?.pageParam\n        const isFetchingNextPage = fetchMore?.direction === 'forward'\n        const isFetchingPreviousPage = fetchMore?.direction === 'backward'\n        const oldPages = context.state.data?.pages || []\n        const oldPageParams = context.state.data?.pageParams || []\n        let newPageParams = oldPageParams\n        let cancelled = false\n\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal?.aborted) {\n                cancelled = true\n              } else {\n                context.signal?.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        // Get query function\n        const queryFn =\n          context.options.queryFn || (() => Promise.reject('Missing queryFn'))\n\n        const buildNewPages = (\n          pages: unknown[],\n          param: unknown,\n          page: unknown,\n          previous?: boolean,\n        ) => {\n          newPageParams = previous\n            ? [param, ...newPageParams]\n            : [...newPageParams, param]\n          return previous ? [page, ...pages] : [...pages, page]\n        }\n\n        // Create function to fetch a page\n        const fetchPage = (\n          pages: unknown[],\n          manual?: boolean,\n          param?: unknown,\n          previous?: boolean,\n        ): Promise<unknown[]> => {\n          if (cancelled) {\n            return Promise.reject('Cancelled')\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages)\n          }\n\n          const queryFnContext: QueryFunctionContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta,\n          }\n\n          addSignalProperty(queryFnContext)\n\n          const queryFnResult = queryFn(queryFnContext)\n\n          const promise = Promise.resolve(queryFnResult).then((page) =>\n            buildNewPages(pages, param, page, previous),\n          )\n\n          return promise\n        }\n\n        let promise: Promise<unknown[]>\n\n        // Fetch first page?\n        if (!oldPages.length) {\n          promise = fetchPage([])\n        }\n\n        // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getNextPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param)\n        }\n\n        // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getPreviousPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param, true)\n        }\n\n        // Refetch pages\n        else {\n          newPageParams = []\n\n          const manual = typeof context.options.getNextPageParam === 'undefined'\n\n          const shouldFetchFirstPage =\n            refetchPage && oldPages[0]\n              ? refetchPage(oldPages[0], 0, oldPages)\n              : true\n\n          // Fetch first page\n          promise = shouldFetchFirstPage\n            ? fetchPage([], manual, oldPageParams[0])\n            : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]))\n\n          // Fetch remaining pages\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then((pages) => {\n              const shouldFetchNextPage =\n                refetchPage && oldPages[i]\n                  ? refetchPage(oldPages[i], i, oldPages)\n                  : true\n\n              if (shouldFetchNextPage) {\n                const param = manual\n                  ? oldPageParams[i]\n                  : getNextPageParam(context.options, pages)\n                return fetchPage(pages, manual, param)\n              }\n              return Promise.resolve(\n                buildNewPages(pages, oldPageParams[i], oldPages[i]),\n              )\n            })\n          }\n        }\n\n        const finalPromise = promise.then((pages) => ({\n          pages,\n          pageParams: newPageParams,\n        }))\n\n        return finalPromise\n      }\n    },\n  }\n}\n\nexport function getNextPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getNextPageParam?.(pages[pages.length - 1], pages)\n}\n\nexport function getPreviousPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getPreviousPageParam?.(pages[0], pages)\n}\n\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasNextPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages)\n    return (\n      typeof nextPageParam !== 'undefined' &&\n      nextPageParam !== null &&\n      nextPageParam !== false\n    )\n  }\n  return\n}\n\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasPreviousPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages)\n    return (\n      typeof previousPageParam !== 'undefined' &&\n      previousPageParam !== null &&\n      previousPageParam !== false\n    )\n  }\n  return\n}\n", "import type { QueryFilters, Updater, MutationFilters } from './utils'\nimport {\n  hashQueryKey,\n  noop,\n  parseFilterArgs,\n  parseQueryArgs,\n  partialMatchKey,\n  hashQueryKeyByOptions,\n  functionalUpdate,\n} from './utils'\nimport type {\n  QueryClientConfig,\n  DefaultOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  ResetQueryFilters,\n  SetDataOptions,\n  WithRequired,\n} from './types'\nimport type { QueryState } from './query'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport type { CancelOptions, DefaultedQueryObserverOptions } from './types'\nimport type { Logger } from './logger'\nimport { defaultLogger } from './logger'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: QueryOptions<any, any, any>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  private queryCache: QueryCache\n  private mutationCache: MutationCache\n  private logger: Logger\n  private defaultOptions: DefaultOptions\n  private queryDefaults: QueryDefaults[]\n  private mutationDefaults: MutationDefaults[]\n  private mountCount: number\n  private unsubscribeFocus?: () => void\n  private unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.queryCache = config.queryCache || new QueryCache()\n    this.mutationCache = config.mutationCache || new MutationCache()\n    this.logger = config.logger || defaultLogger\n    this.defaultOptions = config.defaultOptions || {}\n    this.queryDefaults = []\n    this.mutationDefaults = []\n    this.mountCount = 0\n\n    if (process.env.NODE_ENV !== 'production' && config.logger) {\n      this.logger.error(\n        `Passing a custom logger has been deprecated and will be removed in the next major version.`,\n      )\n    }\n  }\n\n  mount(): void {\n    this.mountCount++\n    if (this.mountCount !== 1) return\n\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused()) {\n        this.resumePausedMutations()\n        this.queryCache.onFocus()\n      }\n    })\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (onlineManager.isOnline()) {\n        this.resumePausedMutations()\n        this.queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.mountCount--\n    if (this.mountCount !== 0) return\n\n    this.unsubscribeFocus?.()\n    this.unsubscribeFocus = undefined\n\n    this.unsubscribeOnline?.()\n    this.unsubscribeOnline = undefined\n  }\n\n  isFetching(filters?: QueryFilters): number\n  isFetching(queryKey?: QueryKey, filters?: QueryFilters): number\n  isFetching(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): number {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    filters.fetchStatus = 'fetching'\n    return this.queryCache.findAll(filters).length\n  }\n\n  isMutating(filters?: MutationFilters): number {\n    return this.mutationCache.findAll({ ...filters, fetching: true }).length\n  }\n\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters?: QueryFilters,\n  ): TQueryFnData | undefined {\n    return this.queryCache.find<TQueryFnData>(queryKey, filters)?.state.data\n  }\n\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: WithRequired<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: Omit<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: Omit<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  ensureQueryData<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | WithRequired<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const cachedData = this.getQueryData<TData>(parsedOptions.queryKey!)\n\n    return cachedData\n      ? Promise.resolve(cachedData)\n      : this.fetchQuery(parsedOptions)\n  }\n\n  getQueriesData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): [QueryKey, TQueryFnData | undefined][]\n  getQueriesData<TQueryFnData = unknown>(\n    filters: QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][]\n  getQueriesData<TQueryFnData = unknown>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return this.getQueryCache()\n      .findAll(queryKeyOrFilters)\n      .map(({ queryKey, state }) => {\n        const data = state.data as TQueryFnData | undefined\n        return [queryKey, data]\n      })\n  }\n\n  setQueryData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): TQueryFnData | undefined {\n    const query = this.queryCache.find<TQueryFnData>(queryKey)\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (typeof data === 'undefined') {\n      return undefined\n    }\n\n    const parsedOptions = parseQueryArgs(queryKey)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n    return this.queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n\n  setQueriesData<TQueryFnData>(\n    filters: QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n\n  setQueriesData<TQueryFnData>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return notifyManager.batch(() =>\n      this.getQueryCache()\n        .findAll(queryKeyOrFilters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<TQueryFnData = unknown, TError = undefined>(\n    queryKey: QueryKey,\n    filters?: QueryFilters,\n  ): QueryState<TQueryFnData, TError> | undefined {\n    return this.queryCache.find<TQueryFnData, TError>(queryKey, filters)?.state\n  }\n\n  removeQueries(filters?: QueryFilters): void\n  removeQueries(queryKey?: QueryKey, filters?: QueryFilters): void\n  removeQueries(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): void {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    const queryCache = this.queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TPageData = unknown>(\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions,\n  ): Promise<void>\n  resetQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions,\n  ): Promise<void>\n  resetQueries(\n    arg1?: QueryKey | ResetQueryFilters,\n    arg2?: ResetQueryFilters | ResetOptions,\n    arg3?: ResetOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n    const queryCache = this.queryCache\n\n    const refetchFilters: RefetchQueryFilters = {\n      type: 'active',\n      ...filters,\n    }\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  cancelQueries(filters?: QueryFilters, options?: CancelOptions): Promise<void>\n  cancelQueries(\n    queryKey?: QueryKey,\n    filters?: QueryFilters,\n    options?: CancelOptions,\n  ): Promise<void>\n  cancelQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: QueryFilters | CancelOptions,\n    arg3?: CancelOptions,\n  ): Promise<void> {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3)\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true\n    }\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(cancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TPageData = unknown>(\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  invalidateQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  invalidateQueries(\n    arg1?: QueryKey | InvalidateQueryFilters,\n    arg2?: InvalidateQueryFilters | InvalidateOptions,\n    arg3?: InvalidateOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    return notifyManager.batch(() => {\n      this.queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      const refetchFilters: RefetchQueryFilters = {\n        ...filters,\n        type: filters.refetchType ?? filters.type ?? 'active',\n      }\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  refetchQueries<TPageData = unknown>(\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  refetchQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  refetchQueries(\n    arg1?: QueryKey | RefetchQueryFilters,\n    arg2?: RefetchQueryFilters | RefetchOptions,\n    arg3?: RefetchOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled())\n        .map((query) =>\n          query.fetch(undefined, {\n            ...options,\n            cancelRefetch: options?.cancelRefetch ?? true,\n            meta: { refetchPage: filters.refetchPage },\n          }),\n        ),\n    )\n\n    let promise = Promise.all(promises).then(noop)\n\n    if (!options?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData>\n  fetchQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(defaultedOptions.staleTime)\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void> {\n    return this.fetchQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<InfiniteData<TData>>\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<InfiniteData<TData>> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    parsedOptions.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData\n    >()\n    return this.fetchQuery(parsedOptions)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    return this.mutationCache.resumePausedMutations()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.mutationCache\n  }\n\n  getLogger(): Logger {\n    return this.logger\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.defaultOptions = options\n  }\n\n  setQueryDefaults(\n    queryKey: QueryKey,\n    options: QueryObserverOptions<unknown, any, any, any>,\n  ): void {\n    const result = this.queryDefaults.find(\n      (x) => hashQueryKey(queryKey) === hashQueryKey(x.queryKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.queryDefaults.push({ queryKey, defaultOptions: options })\n    }\n  }\n\n  getQueryDefaults(\n    queryKey?: QueryKey,\n  ): QueryObserverOptions<any, any, any, any, any> | undefined {\n    if (!queryKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.queryDefaults.find((x) =>\n      partialMatchKey(queryKey, x.queryKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter((x) =>\n        partialMatchKey(queryKey, x.queryKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several query defaults match with key '${JSON.stringify(\n            queryKey,\n          )}'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  setMutationDefaults(\n    mutationKey: MutationKey,\n    options: MutationObserverOptions<any, any, any, any>,\n  ): void {\n    const result = this.mutationDefaults.find(\n      (x) => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.mutationDefaults.push({ mutationKey, defaultOptions: options })\n    }\n  }\n\n  getMutationDefaults(\n    mutationKey?: MutationKey,\n  ): MutationObserverOptions<any, any, any, any> | undefined {\n    if (!mutationKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.mutationDefaults.find((x) =>\n      partialMatchKey(mutationKey, x.mutationKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter((x) =>\n        partialMatchKey(mutationKey, x.mutationKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several mutation defaults match with key '${JSON.stringify(\n            mutationKey,\n          )}'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  defaultQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey,\n  >(\n    options?:\n      | QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options?._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options?.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options?.mutationKey),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.queryCache.clear()\n    this.mutationCache.clear()\n  }\n}\n", "import type { DefaultedQueryObserverOptions, RefetchPageFilters } from './types'\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport type {\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\nimport type { Query, QueryState, Action, FetchOptions } from './query'\nimport type { QueryClient } from './queryClient'\nimport { focusManager } from './focusManager'\nimport { Subscribable } from './subscribable'\nimport { canFetch, isCancelledError } from './retryer'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\nexport interface NotifyOptions {\n  cache?: boolean\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\nexport interface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  options: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n\n  private client: QueryClient\n  private currentQuery!: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  private currentQueryInitialState!: QueryState<TQueryData, TError>\n  private currentResult!: QueryObserverResult<TData, TError>\n  private currentResultState?: QueryState<TQueryData, TError>\n  private currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  private previousQueryResult?: QueryObserverResult<TData, TError>\n  private selectError: TError | null\n  private selectFn?: (data: TQueryData) => TData\n  private selectResult?: TData\n  private staleTimeoutId?: ReturnType<typeof setTimeout>\n  private refetchIntervalId?: ReturnType<typeof setInterval>\n  private currentRefetchInterval?: number | false\n  private trackedProps!: Set<keyof QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.client = client\n    this.options = options\n    this.trackedProps = new Set()\n    this.selectError = null\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.remove = this.remove.bind(this)\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch()\n      }\n\n      this.updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.clearStaleTimeout()\n    this.clearRefetchInterval()\n    this.currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.currentQuery\n\n    this.options = this.client.defaultQueryOptions(options)\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      typeof options?.isDataEqual !== 'undefined'\n    ) {\n      this.client\n        .getLogger()\n        .error(\n          `The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option`,\n        )\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this,\n      })\n    }\n\n    if (\n      typeof this.options.enabled !== 'undefined' &&\n      typeof this.options.enabled !== 'boolean'\n    ) {\n      throw new Error('Expected enabled to be a boolean')\n    }\n\n    // Keep previous query key if the user does not supply one\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey\n    }\n\n    this.updateQuery()\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        this.options.staleTime !== prevOptions.staleTime)\n    ) {\n      this.updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        nextRefetchInterval !== this.currentRefetchInterval)\n    ) {\n      this.updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.client.getQueryCache().build(this.client, options)\n\n    return this.createResult(query, options)\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    return trackedResult\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.currentQuery\n  }\n\n  remove(): void {\n    this.client.getQueryCache().remove(this.currentQuery)\n  }\n\n  refetch<TPageData>({\n    refetchPage,\n    ...options\n  }: RefetchOptions & RefetchPageFilters<TPageData> = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: { refetchPage },\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.client.defaultQueryOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(this.client, defaultedOptions)\n    query.isFetchingOptimistic = true\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.currentResult\n    })\n  }\n\n  private executeFetch(\n    fetchOptions?: ObserverFetchOptions,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  private updateStaleTimeout(): void {\n    this.clearStaleTimeout()\n\n    if (\n      isServer ||\n      this.currentResult.isStale ||\n      !isValidTimeout(this.options.staleTime)\n    ) {\n      return\n    }\n\n    const time = timeUntilStale(\n      this.currentResult.dataUpdatedAt,\n      this.options.staleTime,\n    )\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  private computeRefetchInterval() {\n    return typeof this.options.refetchInterval === 'function'\n      ? this.options.refetchInterval(this.currentResult.data, this.currentQuery)\n      : this.options.refetchInterval ?? false\n  }\n\n  private updateRefetchInterval(nextInterval: number | false): void {\n    this.clearRefetchInterval()\n\n    this.currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      this.options.enabled === false ||\n      !isValidTimeout(this.currentRefetchInterval) ||\n      this.currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.executeFetch()\n      }\n    }, this.currentRefetchInterval)\n  }\n\n  private updateTimers(): void {\n    this.updateStaleTimeout()\n    this.updateRefetchInterval(this.computeRefetchInterval())\n  }\n\n  private clearStaleTimeout(): void {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId)\n      this.staleTimeoutId = undefined\n    }\n  }\n\n  private clearRefetchInterval(): void {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId)\n      this.refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.currentQuery\n    const prevOptions = this.options\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.currentResultState\n    const prevResultOptions = this.currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.currentQueryInitialState\n    const prevQueryResult = queryChange\n      ? this.currentResult\n      : this.previousQueryResult\n\n    const { state } = query\n    let { dataUpdatedAt, error, errorUpdatedAt, fetchStatus, status } = state\n    let isPreviousData = false\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode)\n          ? 'fetching'\n          : 'paused'\n        if (!dataUpdatedAt) {\n          status = 'loading'\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle'\n      }\n    }\n\n    // Keep previous data if needed\n    if (\n      options.keepPreviousData &&\n      !state.dataUpdatedAt &&\n      prevQueryResult?.isSuccess &&\n      status !== 'error'\n    ) {\n      data = prevQueryResult.data\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt\n      status = prevQueryResult.status\n      isPreviousData = true\n    }\n    // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (\n        prevResult &&\n        state.data === prevResultState?.data &&\n        options.select === this.selectFn\n      ) {\n        data = this.selectResult\n      } else {\n        try {\n          this.selectFn = options.select\n          data = options.select(state.data)\n          data = replaceData(prevResult?.data, data, options)\n          this.selectResult = data\n          this.selectError = null\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError)\n          }\n          this.selectError = selectError as TError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = state.data as unknown as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      typeof options.placeholderData !== 'undefined' &&\n      typeof data === 'undefined' &&\n      status === 'loading'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (options.placeholderData as PlaceholderDataFunction<TQueryData>)()\n            : options.placeholderData\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData)\n            this.selectError = null\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError)\n            }\n            this.selectError = selectError as TError\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success'\n        data = replaceData(prevResult?.data, placeholderData, options) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError as any\n      data = this.selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = fetchStatus === 'fetching'\n    const isLoading = status === 'loading'\n    const isError = status === 'error'\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        state.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove,\n    }\n\n    return result as QueryObserverResult<TData, TError>\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.currentQuery, this.options)\n    this.currentResultState = this.currentQuery.state\n    this.currentResultOptions = this.options\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.currentResult = nextResult\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = { cache: true }\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n\n      if (\n        notifyOnChangeProps === 'all' ||\n        (!notifyOnChangeProps && !this.trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(notifyOnChangeProps ?? this.trackedProps)\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey]\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  private updateQuery(): void {\n    const query = this.client.getQueryCache().build(this.client, this.options)\n\n    if (query === this.currentQuery) {\n      return\n    }\n\n    const prevQuery = this.currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.currentQuery = query\n    this.currentQueryInitialState = query.state\n    this.previousQueryResult = this.currentResult\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(action: Action<TData, TError>): void {\n    const notifyOptions: NotifyOptions = {}\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true\n    }\n\n    this.updateResult(notifyOptions)\n\n    if (this.hasListeners()) {\n      this.updateTimers()\n    }\n  }\n\n  private notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        this.options.onSuccess?.(this.currentResult.data!)\n        this.options.onSettled?.(this.currentResult.data!, null)\n      } else if (notifyOptions.onError) {\n        this.options.onError?.(this.currentResult.error!)\n        this.options.onSettled?.(undefined, this.currentResult.error!)\n      }\n\n      // Then trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated',\n        })\n      }\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    !query.state.dataUpdatedAt &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.dataUpdatedAt > 0 &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: typeof options['refetchOnMount'] &\n    typeof options['refetchOnWindowFocus'] &\n    typeof options['refetchOnReconnect'],\n) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    (query !== prevQuery || prevOptions.enabled === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return query.isStaleByTime(options.staleTime)\n}\n", "import { difference, replaceAt } from './utils'\nimport { notifyManager } from './notifyManager'\nimport type {\n  QueryObserverOptions,\n  QueryObserverResult,\n  DefaultedQueryObserverOptions,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\n\ntype QueriesObserverListener = (result: QueryObserverResult[]) => void\n\nexport class QueriesObserver extends Subscribable<QueriesObserverListener> {\n  private client: QueryClient\n  private result: QueryObserverResult[]\n  private queries: QueryObserverOptions[]\n  private observers: QueryObserver[]\n  private observersMap: Record<string, QueryObserver>\n\n  constructor(client: QueryClient, queries?: QueryObserverOptions[]) {\n    super()\n\n    this.client = client\n    this.queries = []\n    this.result = []\n    this.observers = []\n    this.observersMap = {}\n\n    if (queries) {\n      this.setQueries(queries)\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: QueryObserverOptions[],\n    notifyOptions?: NotifyOptions,\n  ): void {\n    this.queries = queries\n\n    notifyManager.batch(() => {\n      const prevObservers = this.observers\n\n      const newObserverMatches = this.findMatchingObservers(this.queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newObserversMap = Object.fromEntries(\n        newObservers.map((observer) => [observer.options.queryHash, observer]),\n      )\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.observers = newObservers\n      this.observersMap = newObserversMap\n      this.result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n\n      this.notify()\n    })\n  }\n\n  getCurrentResult(): QueryObserverResult[] {\n    return this.result\n  }\n\n  getQueries() {\n    return this.observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.observers\n  }\n\n  getOptimisticResult(queries: QueryObserverOptions[]): QueryObserverResult[] {\n    return this.findMatchingObservers(queries).map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n  }\n\n  private findMatchingObservers(\n    queries: QueryObserverOptions[],\n  ): QueryObserverMatch[] {\n    const prevObservers = this.observers\n    const prevObserversMap = new Map(\n      prevObservers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const defaultedQueryOptions = queries.map((options) =>\n      this.client.defaultQueryOptions(options),\n    )\n\n    const matchingObservers: QueryObserverMatch[] =\n      defaultedQueryOptions.flatMap((defaultedOptions) => {\n        const match = prevObserversMap.get(defaultedOptions.queryHash)\n        if (match != null) {\n          return [{ defaultedQueryOptions: defaultedOptions, observer: match }]\n        }\n        return []\n      })\n\n    const matchedQueryHashes = new Set(\n      matchingObservers.map((match) => match.defaultedQueryOptions.queryHash),\n    )\n    const unmatchedQueries = defaultedQueryOptions.filter(\n      (defaultedOptions) => !matchedQueryHashes.has(defaultedOptions.queryHash),\n    )\n\n    const matchingObserversSet = new Set(\n      matchingObservers.map((match) => match.observer),\n    )\n    const unmatchedObservers = prevObservers.filter(\n      (prevObserver) => !matchingObserversSet.has(prevObserver),\n    )\n\n    const getObserver = (options: QueryObserverOptions): QueryObserver => {\n      const defaultedOptions = this.client.defaultQueryOptions(options)\n      const currentObserver = this.observersMap[defaultedOptions.queryHash!]\n      return currentObserver ?? new QueryObserver(this.client, defaultedOptions)\n    }\n\n    const newOrReusedObservers: QueryObserverMatch[] = unmatchedQueries.map(\n      (options, index) => {\n        if (options.keepPreviousData) {\n          // return previous data from one of the observers that no longer match\n          const previouslyUsedObserver = unmatchedObservers[index]\n          if (previouslyUsedObserver !== undefined) {\n            return {\n              defaultedQueryOptions: options,\n              observer: previouslyUsedObserver,\n            }\n          }\n        }\n        return {\n          defaultedQueryOptions: options,\n          observer: getObserver(options),\n        }\n      },\n    )\n\n    const sortMatchesByOrderOfQueries = (\n      a: QueryObserverMatch,\n      b: QueryObserverMatch,\n    ): number =>\n      defaultedQueryOptions.indexOf(a.defaultedQueryOptions) -\n      defaultedQueryOptions.indexOf(b.defaultedQueryOptions)\n\n    return matchingObservers\n      .concat(newOrReusedObservers)\n      .sort(sortMatchesByOrderOfQueries)\n  }\n\n  private onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result)\n      this.notify()\n    }\n  }\n\n  private notify(): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(this.result)\n      })\n    })\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n", "import type {\n  DefaultedInfiniteQueryObserverOptions,\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  Query<PERSON><PERSON>,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions, ObserverFetchOptions } from './queryObserver'\nimport { QueryObserver } from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport type { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>,\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryKey,\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  InfiniteData<TData>,\n  InfiniteData<TQueryData>,\n  TQueryKey\n> {\n  // Type override\n  subscribe!: (\n    listener?: InfiniteQueryObserverListener<TData, TError>,\n  ) => () => void\n\n  // Type override\n  getCurrentResult!: () => InfiniteQueryObserverResult<TData, TError>\n\n  // Type override\n  protected fetch!: (\n    fetchOptions: ObserverFetchOptions,\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options?: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    super.setOptions(\n      {\n        ...options,\n        behavior: infiniteQueryBehavior(),\n      },\n      notifyOptions,\n    )\n  }\n\n  getOptimisticResult(\n    options: DefaultedInfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage({ pageParam, ...options }: FetchNextPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'forward', pageParam },\n      },\n    })\n  }\n\n  fetchPreviousPage({\n    pageParam,\n    ...options\n  }: FetchPreviousPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'backward', pageParam },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, InfiniteData<TQueryData>, TQueryKey>,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const result = super.createResult(query, options)\n\n    const { isFetching, isRefetching } = result\n\n    const isFetchingNextPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'forward'\n\n    const isFetchingPreviousPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'backward'\n\n    return {\n      ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data?.pages),\n      hasPreviousPage: hasPreviousPage(options, state.data?.pages),\n      isFetchingNextPage,\n      isFetchingPreviousPage,\n      isRefetching:\n        isRefetching && !isFetchingNextPage && !isFetchingPreviousPage,\n    }\n  }\n}\n", "import type { Action, Mutation } from './mutation'\nimport { getDefaultState } from './mutation'\nimport { notifyManager } from './notifyManager'\nimport type { QueryClient } from './queryClient'\nimport { Subscribable } from './subscribable'\nimport type {\n  MutateOptions,\n  MutationObserverBaseResult,\n  MutationObserverResult,\n  MutationObserverOptions,\n} from './types'\nimport { shallowEqualObjects } from './utils'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\ninterface NotifyOptions {\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  private client: QueryClient\n  private currentResult!: MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n  private currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  private mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options\n    this.options = this.client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this,\n      })\n    }\n    this.currentMutation?.setOptions(this.options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.updateResult()\n\n    // Determine which callbacks to trigger\n    const notifyOptions: NotifyOptions = {\n      listeners: true,\n    }\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true\n    }\n\n    this.notify(notifyOptions)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.currentResult\n  }\n\n  reset(): void {\n    this.currentMutation = undefined\n    this.updateResult()\n    this.notify({ listeners: true })\n  }\n\n  mutate(\n    variables?: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.mutateOptions = options\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this)\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, {\n      ...this.options,\n      variables:\n        typeof variables !== 'undefined' ? variables : this.options.variables,\n    })\n\n    this.currentMutation.addObserver(this)\n\n    return this.currentMutation.execute()\n  }\n\n  private updateResult(): void {\n    const state = this.currentMutation\n      ? this.currentMutation.state\n      : getDefaultState<TData, TError, TVariables, TContext>()\n\n    const result: MutationObserverBaseResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    > = {\n      ...state,\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    }\n\n    this.currentResult = result as MutationObserverResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    >\n  }\n\n  private notify(options: NotifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          this.mutateOptions.onSuccess?.(\n            this.currentResult.data!,\n            this.currentResult.variables!,\n            this.currentResult.context!,\n          )\n          this.mutateOptions.onSettled?.(\n            this.currentResult.data!,\n            null,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        } else if (options.onError) {\n          this.mutateOptions.onError?.(\n            this.currentResult.error!,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n          this.mutateOptions.onSettled?.(\n            undefined,\n            this.currentResult.error,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      if (options.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n    })\n  }\n}\n", "import type { QueryClient } from './queryClient'\nimport type { Query, QueryState } from './query'\nimport type {\n  MutationKey,\n  MutationOptions,\n  QueryKey,\n  QueryOptions,\n} from './types'\nimport type { Mutation, MutationState } from './mutation'\n\n// TYPES\n\nexport interface DehydrateOptions {\n  dehydrateMutations?: boolean\n  dehydrateQueries?: boolean\n  shouldDehydrateMutation?: ShouldDehydrateMutationFunction\n  shouldDehydrateQuery?: ShouldDehydrateQueryFunction\n}\n\nexport interface HydrateOptions {\n  defaultOptions?: {\n    queries?: QueryOptions\n    mutations?: MutationOptions\n  }\n}\n\ninterface DehydratedMutation {\n  mutationKey?: MutationKey\n  state: MutationState\n}\n\ninterface DehydratedQuery {\n  queryHash: string\n  queryKey: QueryKey\n  state: QueryState\n}\n\nexport interface DehydratedState {\n  mutations: DehydratedMutation[]\n  queries: DehydratedQuery[]\n}\n\nexport type ShouldDehydrateQueryFunction = (query: Query) => boolean\n\nexport type ShouldDehydrateMutationFunction = (mutation: Mutation) => boolean\n\n// FUNCTIONS\n\nfunction dehydrateMutation(mutation: Mutation): DehydratedMutation {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n  }\n}\n\n// Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\nfunction dehydrateQuery(query: Query): DehydratedQuery {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n  }\n}\n\nexport function defaultShouldDehydrateMutation(mutation: Mutation) {\n  return mutation.state.isPaused\n}\n\nexport function defaultShouldDehydrateQuery(query: Query) {\n  return query.state.status === 'success'\n}\n\nexport function dehydrate(\n  client: QueryClient,\n  options: DehydrateOptions = {},\n): DehydratedState {\n  const mutations: DehydratedMutation[] = []\n  const queries: DehydratedQuery[] = []\n\n  if (options.dehydrateMutations !== false) {\n    const shouldDehydrateMutation =\n      options.shouldDehydrateMutation || defaultShouldDehydrateMutation\n\n    client\n      .getMutationCache()\n      .getAll()\n      .forEach((mutation) => {\n        if (shouldDehydrateMutation(mutation)) {\n          mutations.push(dehydrateMutation(mutation))\n        }\n      })\n  }\n\n  if (options.dehydrateQueries !== false) {\n    const shouldDehydrateQuery =\n      options.shouldDehydrateQuery || defaultShouldDehydrateQuery\n\n    client\n      .getQueryCache()\n      .getAll()\n      .forEach((query) => {\n        if (shouldDehydrateQuery(query)) {\n          queries.push(dehydrateQuery(query))\n        }\n      })\n  }\n\n  return { mutations, queries }\n}\n\nexport function hydrate(\n  client: QueryClient,\n  dehydratedState: unknown,\n  options?: HydrateOptions,\n): void {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return\n  }\n\n  const mutationCache = client.getMutationCache()\n  const queryCache = client.getQueryCache()\n\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const mutations = (dehydratedState as DehydratedState).mutations || []\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const queries = (dehydratedState as DehydratedState).queries || []\n\n  mutations.forEach((dehydratedMutation) => {\n    mutationCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.mutations,\n        mutationKey: dehydratedMutation.mutationKey,\n      },\n      dehydratedMutation.state,\n    )\n  })\n\n  queries.forEach((dehydratedQuery) => {\n    const query = queryCache.get(dehydratedQuery.queryHash)\n\n    // Reset fetch status to idle in the dehydrated state to avoid\n    // query being stuck in fetching state upon hydration\n    const dehydratedQueryState = {\n      ...dehydratedQuery.state,\n      fetchStatus: 'idle' as const,\n    }\n\n    // Do not hydrate if an existing query exists with newer data\n    if (query) {\n      if (query.state.dataUpdatedAt < dehydratedQueryState.dataUpdatedAt) {\n        query.setState(dehydratedQueryState)\n      }\n      return\n    }\n\n    // Restore query\n    queryCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.queries,\n        queryKey: dehydratedQuery.queryKey,\n        queryHash: dehydratedQuery.queryHash,\n      },\n      dehydratedQueryState,\n    )\n  })\n}\n", "'use client'\nimport * as ReactDOM from 'react-dom'\nexport const unstable_batchedUpdates = ReactDOM.unstable_batchedUpdates\n", "import { notifyManager } from '@tanstack/query-core'\nimport { unstable_batchedUpdates } from './reactBatchedUpdates'\n\nnotifyManager.setBatchNotifyFunction(unstable_batchedUpdates)\n", "'use client'\n// Temporary workaround due to an issue with react-native uSES - https://github.com/TanStack/query/pull/3601\nimport { useSyncExternalStore as uSES } from 'use-sync-external-store/shim/index.js'\n\nexport const useSyncExternalStore = uSES\n", "'use client'\nimport * as React from 'react'\n\nimport type { QueryClient } from '@tanstack/query-core'\nimport type { ContextOptions } from './types'\n\ndeclare global {\n  interface Window {\n    ReactQueryClientContext?: React.Context<QueryClient | undefined>\n  }\n}\n\nexport const defaultContext = React.createContext<QueryClient | undefined>(\n  undefined,\n)\nconst QueryClientSharingContext = React.createContext<boolean>(false)\n\n// If we are given a context, we will use it.\n// Otherwise, if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\nfunction getQueryClientContext(\n  context: React.Context<QueryClient | undefined> | undefined,\n  contextSharing: boolean,\n) {\n  if (context) {\n    return context\n  }\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext\n    }\n\n    return window.ReactQueryClientContext\n  }\n\n  return defaultContext\n}\n\nexport const useQueryClient = ({ context }: ContextOptions = {}) => {\n  const queryClient = React.useContext(\n    getQueryClientContext(context, React.useContext(QueryClientSharingContext)),\n  )\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return queryClient\n}\n\ntype QueryClientProviderPropsBase = {\n  client: QueryClient\n  children?: React.ReactNode\n}\ntype QueryClientProviderPropsWithContext = ContextOptions & {\n  contextSharing?: never\n} & QueryClientProviderPropsBase\ntype QueryClientProviderPropsWithContextSharing = {\n  context?: never\n  contextSharing?: boolean\n} & QueryClientProviderPropsBase\n\nexport type QueryClientProviderProps =\n  | QueryClientProviderPropsWithContext\n  | QueryClientProviderPropsWithContextSharing\n\nexport const QueryClientProvider = ({\n  client,\n  children,\n  context,\n  contextSharing = false,\n}: QueryClientProviderProps): JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  if (process.env.NODE_ENV !== 'production' && contextSharing) {\n    client\n      .getLogger()\n      .error(\n        `The contextSharing option has been deprecated and will be removed in the next major version`,\n      )\n  }\n\n  const Context = getQueryClientContext(context, contextSharing)\n\n  return (\n    <QueryClientSharingContext.Provider value={!context && contextSharing}>\n      <Context.Provider value={client}>{children}</Context.Provider>\n    </QueryClientSharingContext.Provider>\n  )\n}\n", "'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n", "'use client'\nimport * as React from 'react'\n\n// CONTEXT\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: () => void\n  isReset: () => boolean\n  reset: () => void\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport interface QueryErrorResetBoundaryProps {\n  children:\n    | ((value: QueryErrorResetBoundaryValue) => React.ReactNode)\n    | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function'\n        ? (children as Function)(value)\n        : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n", "export function shouldThrowError<T extends (...args: any[]) => boolean>(\n  _useErrorBoundary: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params)\n  }\n\n  return !!_useErrorBoundary\n}\n", "'use client'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  UseErrorBoundary,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\nimport * as React from 'react'\nimport { shouldThrowError } from './utils'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (options.suspense || options.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  useErrorBoundary,\n  query,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  useErrorBoundary: UseErrorBoundary<\n    TQueryFnData,\n    TError,\n    TQueryData,\n    TQueryKey\n  >\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    shouldThrowError(useErrorBoundary, [result.error, query])\n  )\n}\n", "import type { DefaultedQueryObserverOptions } from '@tanstack/query-core'\nimport type { QueryObserver } from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\nimport type { QueryObserverResult } from '@tanstack/query-core'\nimport type { QueryKey } from '@tanstack/query-core'\n\nexport const ensureStaleTime = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => defaultedOptions?.suspense && willFetch(result, isRestoring)\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer\n    .fetchOptimistic(defaultedOptions)\n    .then(({ data }) => {\n      defaultedOptions.onSuccess?.(data as TData)\n      defaultedOptions.onSettled?.(data, null)\n    })\n    .catch((error) => {\n      errorResetBoundary.clearReset()\n      defaultedOptions.onError?.(error)\n      defaultedOptions.onSettled?.(undefined, error)\n    })\n", "'use client'\nimport * as React from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport type { QueryKey, QueryFunction } from '@tanstack/query-core'\nimport { notifyManager, QueriesObserver } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { UseQueryOptions, UseQueryResult } from './types'\nimport { useIsRestoring } from './isRestoring'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport {\n  ensureStaleTime,\n  shouldSuspend,\n  fetchOptimistic,\n  willFetch,\n} from './suspense'\n\n// This defines the `UseQueryOptions` that are accepted in `QueriesOptions` & `GetOptions`.\n// - `context` is omitted as it is passed as a root-level option to `useQueries` instead.\ntype UseQueryOptionsForUseQueries<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'context'>\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryOptionsForUseQueries<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryOptionsForUseQueries<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey> }\n    ? UseQueryOptionsForUseQueries<\n        TQueryFnData,\n        unknown,\n        TQueryFnData,\n        TQueryKey\n      >\n    : // Fallback\n      UseQueryOptionsForUseQueries\n\ntype GetResults<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryResult<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryResult<TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? UseQueryResult<TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryResult<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryResult<TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<unknown, any>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryResult<TData>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, any> }\n    ? UseQueryResult<TQueryFnData>\n    : // Fallback\n      UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = [],\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryOptionsForUseQueries[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesOptions<[...Tail], [...Result, GetOptions<Head>], [...Depth, 1]>\n  : unknown[] extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends UseQueryOptionsForUseQueries<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      infer TQueryKey\n    >[]\n  ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData, TQueryKey>[]\n  : // Fallback\n    UseQueryOptionsForUseQueries[]\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = [],\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryResult[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetResults<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesResults<[...Tail], [...Result, GetResults<Head>], [...Depth, 1]>\n  : T extends UseQueryOptionsForUseQueries<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      any\n    >[]\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    UseQueryResult<unknown extends TData ? TQueryFnData : TData, TError>[]\n  : // Fallback\n    UseQueryResult[]\n\nexport function useQueries<T extends any[]>({\n  queries,\n  context,\n}: {\n  queries: readonly [...QueriesOptions<T>]\n  context?: UseQueryOptions['context']\n}): QueriesResults<T> {\n  const queryClient = useQueryClient({ context })\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n\n  const defaultedQueries = React.useMemo(\n    () =>\n      queries.map((options) => {\n        const defaultedOptions = queryClient.defaultQueryOptions(options)\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions._optimisticResults = isRestoring\n          ? 'isRestoring'\n          : 'optimistic'\n\n        return defaultedOptions\n      }),\n    [queries, queryClient, isRestoring],\n  )\n\n  defaultedQueries.forEach((query) => {\n    ensureStaleTime(query)\n    ensurePreventErrorBoundaryRetry(query, errorResetBoundary)\n  })\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () => new QueriesObserver(queryClient, defaultedQueries),\n  )\n\n  const optimisticResult = observer.getOptimisticResult(defaultedQueries)\n\n  useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        isRestoring\n          ? () => undefined\n          : observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer, isRestoring],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, { listeners: false })\n  }, [defaultedQueries, observer])\n\n  const shouldAtLeastOneSuspend = optimisticResult.some((result, index) =>\n    shouldSuspend(defaultedQueries[index], result, isRestoring),\n  )\n\n  const suspensePromises = shouldAtLeastOneSuspend\n    ? optimisticResult.flatMap((result, index) => {\n        const options = defaultedQueries[index]\n        const queryObserver = observer.getObservers()[index]\n\n        if (options && queryObserver) {\n          if (shouldSuspend(options, result, isRestoring)) {\n            return fetchOptimistic(options, queryObserver, errorResetBoundary)\n          } else if (willFetch(result, isRestoring)) {\n            void fetchOptimistic(options, queryObserver, errorResetBoundary)\n          }\n        }\n        return []\n      })\n    : []\n\n  if (suspensePromises.length > 0) {\n    throw Promise.all(suspensePromises)\n  }\n  const observerQueries = observer.getQueries()\n  const firstSingleResultWhichShouldThrow = optimisticResult.find(\n    (result, index) =>\n      getHasError({\n        result,\n        errorResetBoundary,\n        useErrorBoundary: defaultedQueries[index]?.useErrorBoundary ?? false,\n        query: observerQueries[index]!,\n      }),\n  )\n\n  if (firstSingleResultWhichShouldThrow?.error) {\n    throw firstSingleResultWhichShouldThrow.error\n  }\n\n  return optimisticResult as QueriesResults<T>\n}\n", "'use client'\nimport * as React from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport type { Query<PERSON>ey, QueryObserver } from '@tanstack/query-core'\nimport { notifyManager } from '@tanstack/query-core'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { UseBaseQueryOptions } from './types'\nimport { useIsRestoring } from './isRestoring'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { ensureStaleTime, shouldSuspend, fetchOptimistic } from './suspense'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    T<PERSON>rror,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  Observer: typeof QueryObserver,\n) {\n  const queryClient = useQueryClient({ context: options.context })\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const defaultedOptions = queryClient.defaultQueryOptions(options)\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  // Include callbacks in batch renders\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(\n      defaultedOptions.onError,\n    )\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(\n      defaultedOptions.onSuccess,\n    )\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(\n      defaultedOptions.onSettled,\n    )\n  }\n\n  ensureStaleTime(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        queryClient,\n        defaultedOptions,\n      ),\n  )\n\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = isRestoring\n          ? () => undefined\n          : observer.subscribe(notifyManager.batchCalls(onStoreChange))\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, isRestoring],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, { listeners: false })\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result, isRestoring)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      useErrorBoundary: defaultedOptions.useErrorBoundary,\n      query: observer.getCurrentQuery(),\n    })\n  ) {\n    throw result.error\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n", "'use client'\nimport type { QueryFunction, QueryKey } from '@tanstack/query-core'\nimport { parseQueryArgs, QueryObserver } from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport { useBaseQuery } from './useBaseQuery'\n\n// HOOK\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & { initialData?: () => undefined },\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  > & { initialData?: () => undefined },\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey'\n  >,\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData?: () => undefined },\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn'\n  >,\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg3?: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError> {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(parsedOptions, QueryObserver)\n}\n", "'use client'\nimport * as React from 'react'\n\nimport type { HydrateOptions } from '@tanstack/query-core'\nimport { hydrate } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { ContextOptions } from './types'\n\nexport function useHydrate(\n  state: unknown,\n  options: HydrateOptions & ContextOptions = {},\n) {\n  const queryClient = useQueryClient({ context: options.context })\n\n  const optionsRef = React.useRef(options)\n  optionsRef.current = options\n\n  // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n  React.useMemo(() => {\n    if (state) {\n      hydrate(queryClient, state, optionsRef.current)\n    }\n  }, [queryClient, state])\n}\n\nexport interface HydrateProps {\n  state?: unknown\n  options?: HydrateOptions\n  children?: React.ReactNode\n}\n\nexport const Hydrate = ({ children, options, state }: HydrateProps) => {\n  useHydrate(state, options)\n  return children as React.ReactElement\n}\n", "'use client'\nimport * as React from 'react'\nimport type { QueryKey, QueryFilters } from '@tanstack/query-core'\nimport { notifyManager, parseFilterArgs } from '@tanstack/query-core'\n\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport type { ContextOptions } from './types'\nimport { useQueryClient } from './QueryClientProvider'\n\ninterface Options extends ContextOptions {}\n\nexport function useIsFetching(filters?: QueryFilters, options?: Options): number\nexport function useIsFetching(\n  queryKey?: QueryKey,\n  filters?: QueryFilters,\n  options?: Options,\n): number\nexport function useIsFetching(\n  arg1?: QueryKey | QueryFilters,\n  arg2?: QueryFilters | Options,\n  arg3?: Options,\n): number {\n  const [filters, options = {}] = parseFilterArgs(arg1, arg2, arg3)\n  const queryClient = useQueryClient({ context: options.context })\n  const queryCache = queryClient.getQueryCache()\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        queryCache.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [queryCache],\n    ),\n    () => queryClient.isFetching(filters),\n    () => queryClient.isFetching(filters),\n  )\n}\n", "'use client'\nimport * as React from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport type { MutationKey, MutationFilters } from '@tanstack/query-core'\nimport { notifyManager, parseMutationFilterArgs } from '@tanstack/query-core'\nimport type { ContextOptions } from './types'\nimport { useQueryClient } from './QueryClientProvider'\n\ninterface Options extends ContextOptions {}\n\nexport function useIsMutating(\n  filters?: MutationFilters,\n  options?: Options,\n): number\nexport function useIsMutating(\n  mutationKey?: MutationKey,\n  filters?: Omit<MutationFilters, 'mutationKey'>,\n  options?: Options,\n): number\nexport function useIsMutating(\n  arg1?: MutationKey | MutationFilters,\n  arg2?: Omit<MutationFilters, 'mutationKey'> | Options,\n  arg3?: Options,\n): number {\n  const [filters, options = {}] = parseMutationFilterArgs(arg1, arg2, arg3)\n\n  const queryClient = useQueryClient({ context: options.context })\n  const mutationCache = queryClient.getMutationCache()\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        mutationCache.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [mutationCache],\n    ),\n    () => queryClient.isMutating(filters),\n    () => queryClient.isMutating(filters),\n  )\n}\n", "'use client'\nimport * as React from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport type { MutationFunction, MutationKey } from '@tanstack/query-core'\nimport {\n  notifyManager,\n  parseMutationArgs,\n  MutationObserver,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\nimport { shouldThrowError } from './utils'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationFn: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationFn'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MutationKey,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MutationKey,\n  mutationFn?: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey' | 'mutationFn'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  arg1:\n    | MutationKey\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg2?:\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg3?: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const options = parseMutationArgs(arg1, arg2, arg3)\n  const queryClient = useQueryClient({ context: options.context })\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        queryClient,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.useErrorBoundary, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n", "'use client'\nimport type {\n  QueryObserver,\n  QueryFunction,\n  QueryKey,\n} from '@tanstack/query-core'\nimport { InfiniteQueryObserver, parseQueryArgs } from '@tanstack/query-core'\nimport type { UseInfiniteQueryOptions, UseInfiniteQueryResult } from './types'\nimport { useBaseQuery } from './useBaseQuery'\n\n// HOOK\n\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  T<PERSON><PERSON>y<PERSON><PERSON> extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey'\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey' | 'queryFn'\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1:\n    | TQueryKey\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg3?: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >,\n): UseInfiniteQueryResult<TData, TError> {\n  const options = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(\n    options,\n    InfiniteQueryObserver as typeof QueryObserver,\n  ) as UseInfiniteQueryResult<TData, TError>\n}\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAYA,QAAI,MAAuC;AACzC,OAAC,WAAW;AAEJ;AAGV,YACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,gCACpC,YACF;AACA,yCAA+B,4BAA4B,IAAI,MAAM,CAAC;AAAA,QACxE;AACU,YAAIA,UAAQ;AAEtB,YAAI,uBAAuBA,QAAM;AAEjC,iBAAS,MAAM,QAAQ;AACrB;AACE;AACE,uBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,qBAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,cACnC;AAEA,2BAAa,SAAS,QAAQ,IAAI;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,aAAa,OAAO,QAAQ,MAAM;AAGzC;AACE,gBAAI,yBAAyB,qBAAqB;AAClD,gBAAI,QAAQ,uBAAuB,iBAAiB;AAEpD,gBAAI,UAAU,IAAI;AAChB,wBAAU;AACV,qBAAO,KAAK,OAAO,CAAC,KAAK,CAAC;AAAA,YAC5B;AAGA,gBAAI,iBAAiB,KAAK,IAAI,SAAU,MAAM;AAC5C,qBAAO,OAAO,IAAI;AAAA,YACpB,CAAC;AAED,2BAAe,QAAQ,cAAc,MAAM;AAI3C,qBAAS,UAAU,MAAM,KAAK,QAAQ,KAAK,GAAG,SAAS,cAAc;AAAA,UACvE;AAAA,QACF;AAMA,iBAAS,GAAG,GAAG,GAAG;AAChB,iBAAO,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM;AAAA,QAErE;AAEA,YAAI,WAAW,OAAO,OAAO,OAAO,aAAa,OAAO,KAAK;AAI7D,YAAIC,YAAWD,QAAM,UACjBE,aAAYF,QAAM,WAClB,kBAAkBA,QAAM,iBACxB,gBAAgBA,QAAM;AAC1B,YAAI,oBAAoB;AACxB,YAAI,6BAA6B;AAWjC,iBAASG,sBAAqB,WAAW,aAIzC,mBAAmB;AACjB;AACE,gBAAI,CAAC,mBAAmB;AACtB,kBAAIH,QAAM,oBAAoB,QAAW;AACvC,oCAAoB;AAEpB,sBAAM,gMAA+M;AAAA,cACvN;AAAA,YACF;AAAA,UACF;AAMA,cAAI,QAAQ,YAAY;AAExB;AACE,gBAAI,CAAC,4BAA4B;AAC/B,kBAAI,cAAc,YAAY;AAE9B,kBAAI,CAAC,SAAS,OAAO,WAAW,GAAG;AACjC,sBAAM,sEAAsE;AAE5E,6CAA6B;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAgBA,cAAI,YAAYC,UAAS;AAAA,YACvB,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF;AAAA,UACF,CAAC,GACG,OAAO,UAAU,CAAC,EAAE,MACpB,cAAc,UAAU,CAAC;AAK7B,0BAAgB,WAAY;AAC1B,iBAAK,QAAQ;AACb,iBAAK,cAAc;AAKnB,gBAAI,uBAAuB,IAAI,GAAG;AAEhC,0BAAY;AAAA,gBACV;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,GAAG,CAAC,WAAW,OAAO,WAAW,CAAC;AAClC,UAAAC,WAAU,WAAY;AAGpB,gBAAI,uBAAuB,IAAI,GAAG;AAEhC,0BAAY;AAAA,gBACV;AAAA,cACF,CAAC;AAAA,YACH;AAEA,gBAAI,oBAAoB,WAAY;AAOlC,kBAAI,uBAAuB,IAAI,GAAG;AAEhC,4BAAY;AAAA,kBACV;AAAA,gBACF,CAAC;AAAA,cACH;AAAA,YACF;AAGA,mBAAO,UAAU,iBAAiB;AAAA,UACpC,GAAG,CAAC,SAAS,CAAC;AACd,wBAAc,KAAK;AACnB,iBAAO;AAAA,QACT;AAEA,iBAAS,uBAAuB,MAAM;AACpC,cAAI,oBAAoB,KAAK;AAC7B,cAAI,YAAY,KAAK;AAErB,cAAI;AACF,gBAAI,YAAY,kBAAkB;AAClC,mBAAO,CAAC,SAAS,WAAW,SAAS;AAAA,UACvC,SAASE,QAAP;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAASC,wBAAuB,WAAW,aAAa,mBAAmB;AAKzE,iBAAO,YAAY;AAAA,QACrB;AAEA,YAAI,YAAY,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,eAAe,OAAO,OAAO,SAAS,kBAAkB;AAEvI,YAAI,sBAAsB,CAAC;AAE3B,YAAI,OAAO,sBAAsBA,0BAAyBF;AAC1D,YAAI,yBAAyBH,QAAM,yBAAyB,SAAYA,QAAM,uBAAuB;AAErG,gBAAQ,uBAAuB;AAE/B,YACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,+BACpC,YACF;AACA,yCAA+B,2BAA2B,IAAI,MAAM,CAAC;AAAA,QACvE;AAAA,MAEE,GAAG;AAAA,IACL;AAAA;AAAA;;;AC9OA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACJO,IAAMM,eAAN,MAA0D;EAG/DC,cAAc;AACZ,SAAKC,YAAY,oBAAIC,IAAJ;AACjB,SAAKC,YAAY,KAAKA,UAAUC,KAAK,IAApB;EAClB;EAEDD,UAAUE,UAAiC;AACzC,UAAMC,WAAW;MAAED;;AACnB,SAAKJ,UAAUM,IAAID,QAAnB;AAEA,SAAKE,YAAL;AAEA,WAAO,MAAM;AACX,WAAKP,UAAUQ,OAAOH,QAAtB;AACA,WAAKI,cAAL;;EAEH;EAEDC,eAAwB;AACtB,WAAO,KAAKV,UAAUW,OAAO;EAC9B;EAESJ,cAAoB;EAE7B;EAESE,gBAAsB;EAE/B;AA9B8D;;;ACoE1D,IAAMG,WAAW,OAAOC,WAAW,eAAe,UAAUA;AAE5D,SAASC,OAAkB;AAChC,SAAOC;AACR;AAEM,SAASC,iBACdC,SACAC,OACS;AACT,SAAO,OAAOD,YAAY,aACrBA,QAAgDC,KAAjD,IACAD;AACL;AAEM,SAASE,eAAeC,OAAiC;AAC9D,SAAO,OAAOA,UAAU,YAAYA,SAAS,KAAKA,UAAUC;AAC7D;AAEM,SAASC,WAAcC,QAAaC,QAAkB;AAC3D,SAAOD,OAAOE,OAAQC,OAAM,CAACF,OAAOG,SAASD,CAAhB,CAAtB;AACR;AAEM,SAASE,UAAaC,OAAYC,OAAeV,OAAe;AACrE,QAAMW,OAAOF,MAAMG,MAAM,CAAZ;AACbD,OAAKD,KAAD,IAAUV;AACd,SAAOW;AACR;AAEM,SAASE,eAAeC,WAAmBC,WAA4B;AAC5E,SAAOC,KAAKC,IAAIH,aAAaC,aAAa,KAAKG,KAAKC,IAAL,GAAY,CAApD;AACR;AAEM,SAASC,eAIdC,MACAC,MACAC,MACU;AACV,MAAI,CAACC,WAAWH,IAAD,GAAQ;AACrB,WAAOA;EACR;AAED,MAAI,OAAOC,SAAS,YAAY;AAC9B,WAAO;MAAE,GAAGC;MAAME,UAAUJ;MAAMK,SAASJ;;EAC5C;AAED,SAAO;IAAE,GAAGA;IAAMG,UAAUJ;;AAC7B;AAEM,SAASM,kBAGdN,MACAC,MACAC,MACU;AACV,MAAIC,WAAWH,IAAD,GAAQ;AACpB,QAAI,OAAOC,SAAS,YAAY;AAC9B,aAAO;QAAE,GAAGC;QAAMK,aAAaP;QAAMQ,YAAYP;;IAClD;AACD,WAAO;MAAE,GAAGA;MAAMM,aAAaP;;EAChC;AAED,MAAI,OAAOA,SAAS,YAAY;AAC9B,WAAO;MAAE,GAAGC;MAAMO,YAAYR;;EAC/B;AAED,SAAO;IAAE,GAAGA;;AACb;AAEM,SAASS,gBAIdT,MACAC,MACAC,MACkC;AAClC,SACEC,WAAWH,IAAD,IAAS,CAAC;IAAE,GAAGC;IAAMG,UAAUJ;KAAQE,IAA9B,IAAsC,CAACF,QAAQ,CAAA,GAAIC,IAAb;AAE5D;AAEM,SAASS,wBAIdV,MACAC,MACAC,MACkC;AAClC,SACEC,WAAWH,IAAD,IACN,CAAC;IAAE,GAAGC;IAAMM,aAAaP;KAAQE,IAAjC,IACA,CAACF,QAAQ,CAAA,GAAIC,IAAb;AAEP;AAEM,SAASU,WACdC,SACAC,OACS;AACT,QAAM;IACJC,OAAO;IACPC;IACAC;IACAC;IACAb;IACAc;EANI,IAOFN;AAEJ,MAAIT,WAAWC,QAAD,GAAY;AACxB,QAAIW,OAAO;AACT,UAAIF,MAAMM,cAAcC,sBAAsBhB,UAAUS,MAAMQ,OAAjB,GAA2B;AACtE,eAAO;MACR;eACQ,CAACC,gBAAgBT,MAAMT,UAAUA,QAAjB,GAA4B;AACrD,aAAO;IACR;EACF;AAED,MAAIU,SAAS,OAAO;AAClB,UAAMS,WAAWV,MAAMU,SAAN;AACjB,QAAIT,SAAS,YAAY,CAACS,UAAU;AAClC,aAAO;IACR;AACD,QAAIT,SAAS,cAAcS,UAAU;AACnC,aAAO;IACR;EACF;AAED,MAAI,OAAOL,UAAU,aAAaL,MAAMW,QAAN,MAAoBN,OAAO;AAC3D,WAAO;EACR;AAED,MACE,OAAOF,gBAAgB,eACvBA,gBAAgBH,MAAMY,MAAMT,aAC5B;AACA,WAAO;EACR;AAED,MAAIC,aAAa,CAACA,UAAUJ,KAAD,GAAS;AAClC,WAAO;EACR;AAED,SAAO;AACR;AAEM,SAASa,cACdd,SACAe,UACS;AACT,QAAM;IAAEZ;IAAOa;IAAUX;IAAWV;EAA9B,IAA8CK;AACpD,MAAIT,WAAWI,WAAD,GAAe;AAC3B,QAAI,CAACoB,SAASN,QAAQd,aAAa;AACjC,aAAO;IACR;AACD,QAAIQ,OAAO;AACT,UACEc,aAAaF,SAASN,QAAQd,WAAlB,MAAmCsB,aAAatB,WAAD,GAC3D;AACA,eAAO;MACR;IACF,WAAU,CAACe,gBAAgBK,SAASN,QAAQd,aAAaA,WAA/B,GAA6C;AACtE,aAAO;IACR;EACF;AAED,MACE,OAAOqB,aAAa,aACnBD,SAASF,MAAMK,WAAW,cAAeF,UAC1C;AACA,WAAO;EACR;AAED,MAAIX,aAAa,CAACA,UAAUU,QAAD,GAAY;AACrC,WAAO;EACR;AAED,SAAO;AACR;AAEM,SAASP,sBACdhB,UACAiB,SACQ;AACR,QAAMU,UAASV,WAAO,OAAP,SAAAA,QAASW,mBAAkBH;AAC1C,SAAOE,OAAO3B,QAAD;AACd;AAMM,SAASyB,aAAazB,UAA4B;AACvD,SAAO6B,KAAKC,UAAU9B,UAAU,CAAC+B,GAAGC,QAClCC,cAAcD,GAAD,IACTE,OAAOC,KAAKH,GAAZ,EACGI,KADH,EAEGC,OAAO,CAACC,QAAQC,QAAQ;AACvBD,WAAOC,GAAD,IAAQP,IAAIO,GAAD;AACjB,WAAOD;EACR,GAAE,CAAA,CALL,IAMAN,GARC;AAUR;AAKM,SAASd,gBAAgBsB,GAAaC,GAAsB;AACjE,SAAOC,iBAAiBF,GAAGC,CAAJ;AACxB;AAKM,SAASC,iBAAiBF,GAAQC,GAAiB;AACxD,MAAID,MAAMC,GAAG;AACX,WAAO;EACR;AAED,MAAI,OAAOD,MAAM,OAAOC,GAAG;AACzB,WAAO;EACR;AAED,MAAID,KAAKC,KAAK,OAAOD,MAAM,YAAY,OAAOC,MAAM,UAAU;AAC5D,WAAO,CAACP,OAAOC,KAAKM,CAAZ,EAAeE,KAAMJ,SAAQ,CAACG,iBAAiBF,EAAED,GAAD,GAAOE,EAAEF,GAAD,CAAV,CAA9C;EACT;AAED,SAAO;AACR;AAQM,SAASK,iBAAiBJ,GAAQC,GAAa;AACpD,MAAID,MAAMC,GAAG;AACX,WAAOD;EACR;AAED,QAAMxD,QAAQ6D,aAAaL,CAAD,KAAOK,aAAaJ,CAAD;AAE7C,MAAIzD,SAAUiD,cAAcO,CAAD,KAAOP,cAAcQ,CAAD,GAAM;AACnD,UAAMK,QAAQ9D,QAAQwD,EAAEO,SAASb,OAAOC,KAAKK,CAAZ,EAAeO;AAChD,UAAMC,SAAShE,QAAQyD,IAAIP,OAAOC,KAAKM,CAAZ;AAC3B,UAAMQ,QAAQD,OAAOD;AACrB,UAAM7D,OAAYF,QAAQ,CAAA,IAAK,CAAA;AAE/B,QAAIkE,aAAa;AAEjB,aAASC,IAAI,GAAGA,IAAIF,OAAOE,KAAK;AAC9B,YAAMZ,MAAMvD,QAAQmE,IAAIH,OAAOG,CAAD;AAC9BjE,WAAKqD,GAAD,IAAQK,iBAAiBJ,EAAED,GAAD,GAAOE,EAAEF,GAAD,CAAV;AAC5B,UAAIrD,KAAKqD,GAAD,MAAUC,EAAED,GAAD,GAAO;AACxBW;MACD;IACF;AAED,WAAOJ,UAAUG,SAASC,eAAeJ,QAAQN,IAAItD;EACtD;AAED,SAAOuD;AACR;AAKM,SAASW,oBAAuBZ,GAAMC,GAAe;AAC1D,MAAKD,KAAK,CAACC,KAAOA,KAAK,CAACD,GAAI;AAC1B,WAAO;EACR;AAED,aAAWD,OAAOC,GAAG;AACnB,QAAIA,EAAED,GAAD,MAAUE,EAAEF,GAAD,GAAO;AACrB,aAAO;IACR;EACF;AAED,SAAO;AACR;AAEM,SAASM,aAAatE,OAAgB;AAC3C,SAAO8E,MAAMC,QAAQ/E,KAAd,KAAwBA,MAAMwE,WAAWb,OAAOC,KAAK5D,KAAZ,EAAmBwE;AACpE;AAGM,SAASd,cAAcsB,GAAqB;AACjD,MAAI,CAACC,mBAAmBD,CAAD,GAAK;AAC1B,WAAO;EACR;AAGD,QAAME,OAAOF,EAAEG;AACf,MAAI,OAAOD,SAAS,aAAa;AAC/B,WAAO;EACR;AAGD,QAAME,OAAOF,KAAKG;AAClB,MAAI,CAACJ,mBAAmBG,IAAD,GAAQ;AAC7B,WAAO;EACR;AAGD,MAAI,CAACA,KAAKE,eAAe,eAApB,GAAsC;AACzC,WAAO;EACR;AAGD,SAAO;AACR;AAED,SAASL,mBAAmBD,GAAiB;AAC3C,SAAOrB,OAAO0B,UAAUE,SAASC,KAAKR,CAA/B,MAAsC;AAC9C;AAEM,SAASxD,WAAWxB,OAAmC;AAC5D,SAAO8E,MAAMC,QAAQ/E,KAAd;AACR;AAEM,SAASyF,QAAQzF,OAA4B;AAClD,SAAOA,iBAAiB0F;AACzB;AAEM,SAASC,MAAMC,SAAgC;AACpD,SAAO,IAAIC,QAASC,aAAY;AAC9BC,eAAWD,SAASF,OAAV;EACX,CAFM;AAGR;AAMM,SAASI,kBAAkBC,UAAsB;AACtDN,QAAM,CAAD,EAAIO,KAAKD,QAAd;AACD;AAEM,SAASE,qBAAkD;AAChE,MAAI,OAAOC,oBAAoB,YAAY;AACzC,WAAO,IAAIA,gBAAJ;EACR;AACD;AACD;AAEM,SAASC,YAGdC,UAA6BC,MAAa7D,SAA0B;AAEpE,MAAIA,QAAQ8D,eAAZ,QAAI9D,QAAQ8D,YAAcF,UAAUC,IAAhC,GAAuC;AACzC,WAAOD;aACE,OAAO5D,QAAQ+D,sBAAsB,YAAY;AAC1D,WAAO/D,QAAQ+D,kBAAkBH,UAAUC,IAApC;EACR,WAAU7D,QAAQ+D,sBAAsB,OAAO;AAE9C,WAAOpC,iBAAiBiC,UAAUC,IAAX;EACxB;AACD,SAAOA;AACR;;;AC9aM,IAAMG,eAAN,cAA2BC,aAAa;EAM7CC,cAAc;AACZ,UAAA;AACA,SAAKC,QAASC,aAAY;AAGxB,UAAI,CAACC,YAAYC,OAAOC,kBAAkB;AACxC,cAAMC,WAAW,MAAMJ,QAAO;AAE9BE,eAAOC,iBAAiB,oBAAoBC,UAAU,KAAtD;AACAF,eAAOC,iBAAiB,SAASC,UAAU,KAA3C;AAEA,eAAO,MAAM;AAEXF,iBAAOG,oBAAoB,oBAAoBD,QAA/C;AACAF,iBAAOG,oBAAoB,SAASD,QAApC;;MAEH;AACD;;EAEH;EAESE,cAAoB;AAC5B,QAAI,CAAC,KAAKC,SAAS;AACjB,WAAKC,iBAAiB,KAAKT,KAA3B;IACD;EACF;EAESU,gBAAgB;AACxB,QAAI,CAAC,KAAKC,aAAL,GAAqB;AAAA,UAAA;AACxB,OAAA,gBAAA,KAAKH,YAAL,OAAA,SAAA,cAAA,KAAA,IAAA;AACA,WAAKA,UAAUI;IAChB;EACF;EAEDH,iBAAiBT,OAAsB;AAAA,QAAA;AACrC,SAAKA,QAAQA;AACb,KAAA,iBAAA,KAAKQ,YAAL,OAAA,SAAA,eAAA,KAAA,IAAA;AACA,SAAKA,UAAUR,MAAOa,aAAY;AAChC,UAAI,OAAOA,YAAY,WAAW;AAChC,aAAKC,WAAWD,OAAhB;MACD,OAAM;AACL,aAAKZ,QAAL;MACD;IACF,CANmB;EAOrB;EAEDa,WAAWD,SAAyB;AAClC,SAAKA,UAAUA;AAEf,QAAIA,SAAS;AACX,WAAKZ,QAAL;IACD;EACF;EAEDA,UAAgB;AACd,SAAKc,UAAUC,QAAQ,CAAC;MAAEX;IAAF,MAAiB;AACvCA,eAAQ;KADV;EAGD;EAEDY,YAAqB;AACnB,QAAI,OAAO,KAAKJ,YAAY,WAAW;AACrC,aAAO,KAAKA;IACb;AAGD,QAAI,OAAOK,aAAa,aAAa;AACnC,aAAO;IACR;AAED,WAAO,CAACN,QAAW,WAAW,WAAvB,EAAoCO,SACzCD,SAASE,eADJ;EAGR;AA/E4C;AAkFlCC,IAAAA,eAAe,IAAIxB,aAAJ;;;AClF5B,IAAMyB,eAAe,CAAC,UAAU,SAAX;AAEd,IAAMC,gBAAN,cAA4BC,aAAa;EAM9CC,cAAc;AACZ,UAAA;AACA,SAAKC,QAASC,cAAa;AAGzB,UAAI,CAACC,YAAYC,OAAOC,kBAAkB;AACxC,cAAMC,WAAW,MAAMJ,SAAQ;AAE/BL,qBAAaU,QAASC,WAAU;AAC9BJ,iBAAOC,iBAAiBG,OAAOF,UAAU,KAAzC;SADF;AAIA,eAAO,MAAM;AAEXT,uBAAaU,QAASC,WAAU;AAC9BJ,mBAAOK,oBAAoBD,OAAOF,QAAlC;WADF;;MAIH;AAED;;EAEH;EAESI,cAAoB;AAC5B,QAAI,CAAC,KAAKC,SAAS;AACjB,WAAKC,iBAAiB,KAAKX,KAA3B;IACD;EACF;EAESY,gBAAgB;AACxB,QAAI,CAAC,KAAKC,aAAL,GAAqB;AAAA,UAAA;AACxB,OAAA,gBAAA,KAAKH,YAAL,OAAA,SAAA,cAAA,KAAA,IAAA;AACA,WAAKA,UAAUI;IAChB;EACF;EAEDH,iBAAiBX,OAAsB;AAAA,QAAA;AACrC,SAAKA,QAAQA;AACb,KAAA,iBAAA,KAAKU,YAAL,OAAA,SAAA,eAAA,KAAA,IAAA;AACA,SAAKA,UAAUV,MAAOe,YAAqB;AACzC,UAAI,OAAOA,WAAW,WAAW;AAC/B,aAAKC,UAAUD,MAAf;MACD,OAAM;AACL,aAAKd,SAAL;MACD;IACF,CANmB;EAOrB;EAEDe,UAAUD,QAAwB;AAChC,SAAKA,SAASA;AAEd,QAAIA,QAAQ;AACV,WAAKd,SAAL;IACD;EACF;EAEDA,WAAiB;AACf,SAAKgB,UAAUX,QAAQ,CAAC;MAAED;IAAF,MAAiB;AACvCA,eAAQ;KADV;EAGD;EAEDa,WAAoB;AAClB,QAAI,OAAO,KAAKH,WAAW,WAAW;AACpC,aAAO,KAAKA;IACb;AAED,QACE,OAAOI,cAAc,eACrB,OAAOA,UAAUC,WAAW,aAC5B;AACA,aAAO;IACR;AAED,WAAOD,UAAUC;EAClB;AAlF6C;AAqFnCC,IAAAA,gBAAgB,IAAIxB,cAAJ;;;ACpD7B,SAASyB,kBAAkBC,cAAsB;AAC/C,SAAOC,KAAKC,IAAI,MAAO,KAAKF,cAAc,GAAnC;AACR;AAEM,SAASG,SAASC,aAA+C;AACtE,UAAQA,eAAAA,OAAAA,cAAe,cAAc,WACjCC,cAAcC,SAAd,IACA;AACL;AAEM,IAAMC,iBAAN,MAAqB;EAG1BC,YAAYC,SAAyB;AACnC,SAAKC,SAASD,WAAAA,OAAAA,SAAAA,QAASC;AACvB,SAAKC,SAASF,WAAAA,OAAAA,SAAAA,QAASE;EACxB;AANyB;AASrB,SAASC,iBAAiBC,OAAqC;AACpE,SAAOA,iBAAiBN;AACzB;AAEM,SAASO,cACdC,QACgB;AAChB,MAAIC,mBAAmB;AACvB,MAAIhB,eAAe;AACnB,MAAIiB,aAAa;AACjB,MAAIC;AACJ,MAAIC;AACJ,MAAIC;AAEJ,QAAMC,UAAU,IAAIC,QAAe,CAACC,cAAcC,gBAAgB;AAChEL,qBAAiBI;AACjBH,oBAAgBI;EACjB,CAHe;AAKhB,QAAMC,SAAUC,mBAAwC;AACtD,QAAI,CAACT,YAAY;AACfU,aAAO,IAAIpB,eAAemB,aAAnB,CAAD;AAENX,aAAOa,SAAPb,OAAAA,SAAAA,OAAOa,MAAP;IACD;;AAEH,QAAMC,cAAc,MAAM;AACxBb,uBAAmB;;AAGrB,QAAMc,gBAAgB,MAAM;AAC1Bd,uBAAmB;;AAGrB,QAAMe,cAAc,MAClB,CAACC,aAAaC,UAAb,KACAlB,OAAOX,gBAAgB,YAAY,CAACC,cAAcC,SAAd;AAEvC,QAAM4B,UAAWrB,WAAe;AAC9B,QAAI,CAACI,YAAY;AACfA,mBAAa;AACbF,aAAOoB,aAAP,OAAA,SAAApB,OAAOoB,UAAYtB,KAAnB;AACAK,oBAAU,OAAV,SAAAA,WAAU;AACVC,qBAAeN,KAAD;IACf;;AAGH,QAAMc,SAAUd,WAAe;AAC7B,QAAI,CAACI,YAAY;AACfA,mBAAa;AACbF,aAAOqB,WAAP,OAAA,SAAArB,OAAOqB,QAAUvB,KAAjB;AACAK,oBAAU,OAAV,SAAAA,WAAU;AACVE,oBAAcP,KAAD;IACd;;AAGH,QAAMwB,QAAQ,MAAM;AAClB,WAAO,IAAIf,QAASgB,qBAAoB;AACtCpB,mBAAcL,WAAU;AACtB,cAAM0B,cAActB,cAAc,CAACc,YAAW;AAC9C,YAAIQ,aAAa;AACfD,0BAAgBzB,KAAD;QAChB;AACD,eAAO0B;;AAETxB,aAAOyB,WAAPzB,OAAAA,SAAAA,OAAOyB,QAAP;KARK,EASJC,KAAK,MAAM;AACZvB,mBAAawB;AACb,UAAI,CAACzB,YAAY;AACfF,eAAO4B,cAAP5B,OAAAA,SAAAA,OAAO4B,WAAP;MACD;IACF,CAdM;EAeR;AAGD,QAAMC,MAAM,MAAM;AAEhB,QAAI3B,YAAY;AACd;IACD;AAED,QAAI4B;AAGJ,QAAI;AACFA,uBAAiB9B,OAAO+B,GAAP;aACVC,OAAP;AACAF,uBAAiBvB,QAAQK,OAAOoB,KAAf;IAClB;AAEDzB,YAAQY,QAAQW,cAAhB,EACGJ,KAAKP,OADR,EAEGc,MAAOD,WAAU;AAAA,UAAA,eAAA;AAEhB,UAAI9B,YAAY;AACd;MACD;AAGD,YAAMgC,SAAQlC,gBAAAA,OAAOkC,UAAV,OAAA,gBAAmB;AAC9B,YAAMC,cAAanC,qBAAAA,OAAOmC,eAAV,OAAA,qBAAwBnD;AACxC,YAAMoD,QACJ,OAAOD,eAAe,aAClBA,WAAWlD,cAAc+C,KAAf,IACVG;AACN,YAAME,cACJH,UAAU,QACT,OAAOA,UAAU,YAAYjD,eAAeiD,SAC5C,OAAOA,UAAU,cAAcA,MAAMjD,cAAc+C,KAAf;AAEvC,UAAI/B,oBAAoB,CAACoC,aAAa;AAEpCzB,eAAOoB,KAAD;AACN;MACD;AAED/C;AAGAe,aAAOsC,UAAP,OAAA,SAAAtC,OAAOsC,OAASrD,cAAc+C,KAA9B;AAGAO,YAAMH,KAAD,EAEFV,KAAK,MAAM;AACV,YAAIV,YAAW,GAAI;AACjB,iBAAOM,MAAK;QACb;AACD;OANJ,EAQGI,KAAK,MAAM;AACV,YAAIzB,kBAAkB;AACpBW,iBAAOoB,KAAD;QACP,OAAM;AACLH,cAAG;QACJ;OAbL;KAhCJ;EAgDD;AAGD,MAAIzC,SAASY,OAAOX,WAAR,GAAsB;AAChCwC,QAAG;EACJ,OAAM;AACLP,UAAK,EAAGI,KAAKG,GAAb;EACD;AAED,SAAO;IACLvB;IACAI;IACA8B,UAAU,MAAM;AACd,YAAMC,cAActC,cAAH,OAAA,SAAGA,WAAU;AAC9B,aAAOsC,cAAcnC,UAAUC,QAAQY,QAAR;;IAEjCL;IACAC;;AAEH;;;AClNM,IAAM2B,gBAAwBC;;;ACE9B,SAASC,sBAAsB;AACpC,MAAIC,QAA0B,CAAA;AAC9B,MAAIC,eAAe;AACnB,MAAIC,WAA4BC,cAAa;AAC3CA,aAAQ;;AAEV,MAAIC,gBAAsCD,cAAyB;AACjEA,aAAQ;;AAGV,QAAME,QAAYF,cAAyB;AACzC,QAAIG;AACJL;AACA,QAAI;AACFK,eAASH,SAAQ;IAClB,UAFD;AAGEF;AACA,UAAI,CAACA,cAAc;AACjBM,cAAK;MACN;IACF;AACD,WAAOD;;AAGT,QAAME,WAAYL,cAAmC;AACnD,QAAIF,cAAc;AAChBD,YAAMS,KAAKN,QAAX;IACD,OAAM;AACLO,wBAAkB,MAAM;AACtBR,iBAASC,QAAD;MACT,CAFgB;IAGlB;;AAMH,QAAMQ,aAAkCR,cAAmB;AACzD,WAAQ,IAAIS,SAAgB;AAC1BJ,eAAS,MAAM;AACbL,iBAAS,GAAGS,IAAJ;MACT,CAFO;;;AAMZ,QAAML,QAAQ,MAAY;AACxB,UAAMM,gBAAgBb;AACtBA,YAAQ,CAAA;AACR,QAAIa,cAAcC,QAAQ;AACxBJ,wBAAkB,MAAM;AACtBN,sBAAc,MAAM;AAClBS,wBAAcE,QAASZ,cAAa;AAClCD,qBAASC,QAAD;WADV;QAGD,CAJY;MAKd,CANgB;IAOlB;;AAOH,QAAMa,oBAAqBC,QAAuB;AAChDf,eAAWe;;AAOb,QAAMC,yBAA0BD,QAA4B;AAC1Db,oBAAgBa;;AAGlB,SAAO;IACLZ;IACAM;IACAH;IACAQ;IACAE;;AAEH;AAGYC,IAAAA,gBAAgBpB,oBAAmB;;;AC7FzC,IAAeqB,YAAf,MAAyB;EAI9BC,UAAgB;AACd,SAAKC,eAAL;EACD;EAESC,aAAmB;AAC3B,SAAKD,eAAL;AAEA,QAAIE,eAAe,KAAKC,SAAN,GAAkB;AAClC,WAAKC,YAAYC,WAAW,MAAM;AAChC,aAAKC,eAAL;SACC,KAAKH,SAFmB;IAG5B;EACF;EAESI,gBAAgBC,cAAwC;AAEhE,SAAKL,YAAYM,KAAKC,IACpB,KAAKP,aAAa,GAClBK,gBAAAA,OAAAA,eAAiBG,WAAWC,WAAW,IAAI,KAAK,GAFjC;EAIlB;EAESZ,iBAAiB;AACzB,QAAI,KAAKI,WAAW;AAClBS,mBAAa,KAAKT,SAAN;AACZ,WAAKA,YAAYU;IAClB;EACF;AA/B6B;;;AC0IzB,IAAMC,QAAN,cAKGC,UAAU;EAiBlBC,YAAYC,QAA6D;AACvE,UAAA;AAEA,SAAKC,sBAAsB;AAC3B,SAAKC,iBAAiBF,OAAOE;AAC7B,SAAKC,WAAWH,OAAOI,OAAvB;AACA,SAAKC,YAAY,CAAA;AACjB,SAAKC,QAAQN,OAAOM;AACpB,SAAKC,SAASP,OAAOO,UAAUC;AAC/B,SAAKC,WAAWT,OAAOS;AACvB,SAAKC,YAAYV,OAAOU;AACxB,SAAKC,eAAeX,OAAOY,SAASC,gBAAgB,KAAKT,OAAN;AACnD,SAAKQ,QAAQ,KAAKD;AAClB,SAAKG,WAAL;EACD;EAEO,IAAJC,OAA8B;AAChC,WAAO,KAAKX,QAAQW;EACrB;EAEOZ,WACNC,SACM;AACN,SAAKA,UAAU;MAAE,GAAG,KAAKF;MAAgB,GAAGE;;AAE5C,SAAKY,gBAAgB,KAAKZ,QAAQa,SAAlC;EACD;EAESC,iBAAiB;AACzB,QAAI,CAAC,KAAKb,UAAUc,UAAU,KAAKP,MAAMQ,gBAAgB,QAAQ;AAC/D,WAAKd,MAAMe,OAAO,IAAlB;IACD;EACF;EAEDC,QACEC,SACAnB,SACO;AACP,UAAMoB,OAAOC,YAAY,KAAKb,MAAMY,MAAMD,SAAS,KAAKnB,OAAhC;AAGxB,SAAKsB,SAAS;MACZF;MACAG,MAAM;MACNC,eAAexB,WAAAA,OAAAA,SAAAA,QAASyB;MACxBC,QAAQ1B,WAAAA,OAAAA,SAAAA,QAAS0B;KAJnB;AAOA,WAAON;EACR;EAEDO,SACEnB,OACAoB,iBACM;AACN,SAAKN,SAAS;MAAEC,MAAM;MAAYf;MAAOoB;KAAzC;EACD;EAEDC,OAAO7B,SAAwC;AAAA,QAAA;AAC7C,UAAM8B,UAAU,KAAKA;AACrB,KAAA,gBAAA,KAAKC,YAAL,OAAA,SAAA,cAAcF,OAAO7B,OAArB;AACA,WAAO8B,UAAUA,QAAQE,KAAKC,IAAb,EAAmBC,MAAMD,IAAzB,IAAiCE,QAAQC,QAAR;EACnD;EAEDC,UAAgB;AACd,UAAMA,QAAN;AAEA,SAAKR,OAAO;MAAES,QAAQ;KAAtB;EACD;EAEDC,QAAc;AACZ,SAAKF,QAAL;AACA,SAAKV,SAAS,KAAKpB,YAAnB;EACD;EAEDiC,WAAoB;AAClB,WAAO,KAAKvC,UAAUwC,KAAMC,cAAaA,SAAS1C,QAAQ2C,YAAY,KAA/D;EACR;EAEDC,aAAsB;AACpB,WAAO,KAAKC,kBAAL,IAA2B,KAAK,CAAC,KAAKL,SAAL;EACzC;EAEDM,UAAmB;AACjB,WACE,KAAKtC,MAAMuC,iBACX,CAAC,KAAKvC,MAAMgB,iBACZ,KAAKvB,UAAUwC,KAAMC,cAAaA,SAASM,iBAAT,EAA4BF,OAA9D;EAEH;EAEDG,cAAcC,YAAY,GAAY;AACpC,WACE,KAAK1C,MAAMuC,iBACX,CAAC,KAAKvC,MAAMgB,iBACZ,CAAC2B,eAAe,KAAK3C,MAAMgB,eAAe0B,SAA3B;EAElB;EAEDE,UAAgB;AAAA,QAAA;AACd,UAAMV,WAAW,KAAKzC,UAAUoD,KAAMC,OAAMA,EAAEC,yBAAF,CAA3B;AAEjB,QAAIb,UAAU;AACZA,eAASc,QAAQ;QAAEC,eAAe;OAAlC;IACD;AAGD,KAAK1B,iBAAAA,KAAAA,YAAL,OAAA,SAAA,eAAc2B,SAAd;EACD;EAEDC,WAAiB;AAAA,QAAA;AACf,UAAMjB,WAAW,KAAKzC,UAAUoD,KAAMC,OAAMA,EAAEM,uBAAF,CAA3B;AAEjB,QAAIlB,UAAU;AACZA,eAASc,QAAQ;QAAEC,eAAe;OAAlC;IACD;AAGD,KAAK1B,iBAAAA,KAAAA,YAAL,OAAA,SAAA,eAAc2B,SAAd;EACD;EAEDG,YAAYnB,UAAwD;AAClE,QAAI,CAAC,KAAKzC,UAAU6D,SAASpB,QAAxB,GAAmC;AACtC,WAAKzC,UAAU8D,KAAKrB,QAApB;AAGA,WAAKsB,eAAL;AAEA,WAAK9D,MAAM+D,OAAO;QAAE1C,MAAM;QAAiB2C,OAAO;QAAMxB;OAAxD;IACD;EACF;EAEDyB,eAAezB,UAAwD;AACrE,QAAI,KAAKzC,UAAU6D,SAASpB,QAAxB,GAAmC;AACrC,WAAKzC,YAAY,KAAKA,UAAUmE,OAAQd,OAAMA,MAAMZ,QAAnC;AAEjB,UAAI,CAAC,KAAKzC,UAAUc,QAAQ;AAG1B,YAAI,KAAKgB,SAAS;AAChB,cAAI,KAAKlC,qBAAqB;AAC5B,iBAAKkC,QAAQF,OAAO;cAAEwC,QAAQ;aAA9B;UACD,OAAM;AACL,iBAAKtC,QAAQuC,YAAb;UACD;QACF;AAED,aAAK5D,WAAL;MACD;AAED,WAAKR,MAAM+D,OAAO;QAAE1C,MAAM;QAAmB2C,OAAO;QAAMxB;OAA1D;IACD;EACF;EAEDG,oBAA4B;AAC1B,WAAO,KAAK5C,UAAUc;EACvB;EAEDwD,aAAmB;AACjB,QAAI,CAAC,KAAK/D,MAAMuC,eAAe;AAC7B,WAAKzB,SAAS;QAAEC,MAAM;OAAtB;IACD;EACF;EAEDiD,MACExE,SACAyE,cACgB;AAAA,QAAA,uBAAA;AAChB,QAAI,KAAKjE,MAAMQ,gBAAgB,QAAQ;AACrC,UAAI,KAAKR,MAAMgB,iBAAiBiD,gBAAAA,QAAAA,aAAchB,eAAe;AAE3D,aAAK5B,OAAO;UAAES,QAAQ;SAAtB;MACD,WAAU,KAAKR,SAAS;AAAA,YAAA;AAEvB,SAAA,iBAAA,KAAKC,YAAL,OAAA,SAAA,eAAc2C,cAAd;AAEA,eAAO,KAAK5C;MACb;IACF;AAGD,QAAI9B,SAAS;AACX,WAAKD,WAAWC,OAAhB;IACD;AAID,QAAI,CAAC,KAAKA,QAAQ2E,SAAS;AACzB,YAAMjC,WAAW,KAAKzC,UAAUoD,KAAMC,OAAMA,EAAEtD,QAAQ2E,OAArC;AACjB,UAAIjC,UAAU;AACZ,aAAK3C,WAAW2C,SAAS1C,OAAzB;MACD;IACF;AAED,QAAI,CAAC4E,MAAMC,QAAQ,KAAK7E,QAAQK,QAA3B,GAAsC;AACzC,UAAIyE,MAAuC;AACzC,aAAK3E,OAAO4E,MAAZ,qIAAA;MAGD;IACF;AAED,UAAMC,kBAAkBC,mBAAkB;AAG1C,UAAMC,iBAAkD;MACtD7E,UAAU,KAAKA;MACf8E,WAAWC;MACXzE,MAAM,KAAKA;IAH2C;AASxD,UAAM0E,oBAAqBC,YAAoB;AAC7CC,aAAOC,eAAeF,QAAQ,UAAU;QACtCG,YAAY;QACZC,KAAK,MAAM;AACT,cAAIV,iBAAiB;AACnB,iBAAKnF,sBAAsB;AAC3B,mBAAOmF,gBAAgBW;UACxB;AACD,iBAAOP;QACR;OARH;;AAYFC,sBAAkBH,cAAD;AAGjB,UAAMU,UAAU,MAAM;AACpB,UAAI,CAAC,KAAK5F,QAAQ2E,SAAS;AACzB,eAAOxC,QAAQ0D,OAAO,iBAAf;MACR;AACD,WAAKhG,sBAAsB;AAC3B,aAAO,KAAKG,QAAQ2E,QAAQO,cAArB;IACR;AAGD,UAAMY,UAAgE;MACpErB;MACAzE,SAAS,KAAKA;MACdK,UAAU,KAAKA;MACfG,OAAO,KAAKA;MACZoF;;AAGFP,sBAAkBS,OAAD;AAEjB,KAAK9F,wBAAAA,KAAAA,QAAQ+F,aAAb,OAAA,SAAA,sBAAuBC,QAAQF,OAA/B;AAGA,SAAKG,cAAc,KAAKzF;AAGxB,QACE,KAAKA,MAAMQ,gBAAgB,UAC3B,KAAKR,MAAM0F,gBAAX,wBAAyBJ,QAAQrB,iBAAjC,OAAA,SAAyB,sBAAsB9D,OAC/C;AAAA,UAAA;AACA,WAAKW,SAAS;QAAEC,MAAM;QAASZ,OAAMmF,yBAAAA,QAAQrB,iBAAV,OAAA,SAAE,uBAAsB9D;OAA3D;IACD;AAED,UAAMwF,UAAWpB,WAAyC;AAExD,UAAI,EAAEqB,iBAAiBrB,KAAD,KAAWA,MAAMzC,SAAS;AAC9C,aAAKhB,SAAS;UACZC,MAAM;UACNwD;SAFF;MAID;AAED,UAAI,CAACqB,iBAAiBrB,KAAD,GAAS;AAAA,YAAA,uBAAA,oBAAA,wBAAA;AAE5B,SAAK7E,yBAAAA,qBAAAA,KAAAA,MAAMN,QAAOuG,YAAUpB,OAAAA,SAAAA,sBAAAA,KAAAA,oBAAAA,OAAO,IAAnC;AACA,SAAA,0BAAA,sBAAA,KAAK7E,MAAMN,QAAOyG,cAAlB,OAAA,SAAA,uBAAA,KAAA,qBACE,KAAK7F,MAAMY,MACX2D,OACA,IAHF;AAMA,YAAID,MAAuC;AACzC,eAAK3E,OAAO4E,MAAMA,KAAlB;QACD;MACF;AAED,UAAI,CAAC,KAAKuB,sBAAsB;AAE9B,aAAK5F,WAAL;MACD;AACD,WAAK4F,uBAAuB;IAC7B;AAGD,SAAKvE,UAAUwE,cAAc;MAC3BC,IAAIV,QAAQF;MACZa,OAAOzB,mBAAF,OAAA,SAAEA,gBAAiByB,MAAMC,KAAK1B,eAA5B;MACP2B,WAAYvF,UAAS;AAAA,YAAA,wBAAA,qBAAA,wBAAA;AACnB,YAAI,OAAOA,SAAS,aAAa;AAC/B,cAAI0D,MAAuC;AACzC,iBAAK3E,OAAO4E,MAAZ,2IAC2I,KAAKzE,SADhJ;UAGD;AACD6F,kBAAQ,IAAIS,MAAS,KAAKtG,YAAlB,oBAAA,CAAD;AACP;QACD;AAED,aAAKY,QAAQE,IAAb;AAGA,SAAKlB,0BAAAA,sBAAAA,KAAAA,MAAMN,QAAO+G,cAAYvF,OAAAA,SAAAA,uBAAAA,KAAAA,qBAAAA,MAAM,IAApC;AACA,SAAA,0BAAA,sBAAA,KAAKlB,MAAMN,QAAOyG,cAAlB,OAAA,SAAA,uBAAA,KAAA,qBACEjF,MACA,KAAKZ,MAAMuE,OACX,IAHF;AAMA,YAAI,CAAC,KAAKuB,sBAAsB;AAE9B,eAAK5F,WAAL;QACD;AACD,aAAK4F,uBAAuB;;MAE9BH;MACAU,QAAQ,CAACC,cAAc/B,UAAU;AAC/B,aAAKzD,SAAS;UAAEC,MAAM;UAAUuF;UAAc/B;SAA9C;;MAEFgC,SAAS,MAAM;AACb,aAAKzF,SAAS;UAAEC,MAAM;SAAtB;;MAEFyF,YAAY,MAAM;AAChB,aAAK1F,SAAS;UAAEC,MAAM;SAAtB;;MAEF0F,OAAOnB,QAAQ9F,QAAQiH;MACvBC,YAAYpB,QAAQ9F,QAAQkH;MAC5BC,aAAarB,QAAQ9F,QAAQmH;IA1CF,CAAD;AA6C5B,SAAKrF,UAAU,KAAKC,QAAQD;AAE5B,WAAO,KAAKA;EACb;EAEOR,SAAS8F,QAAqC;AACpD,UAAMC,UACJ7G,WAC8B;AAAA,UAAA,cAAA;AAC9B,cAAQ4G,OAAO7F,MAAf;QACE,KAAK;AACH,iBAAO;YACL,GAAGf;YACH8G,mBAAmBF,OAAON;YAC1BS,oBAAoBH,OAAOrC;;QAE/B,KAAK;AACH,iBAAO;YACL,GAAGvE;YACHQ,aAAa;;QAEjB,KAAK;AACH,iBAAO;YACL,GAAGR;YACHQ,aAAa;;QAEjB,KAAK;AACH,iBAAO;YACL,GAAGR;YACH8G,mBAAmB;YACnBC,oBAAoB;YACpBrB,YAAWkB,eAAAA,OAAOzG,SAAT,OAAA,eAAiB;YAC1BK,aAAawG,SAAS,KAAKxH,QAAQmH,WAAd,IACjB,aACA;YACJ,GAAI,CAAC3G,MAAMgB,iBAAiB;cAC1BuD,OAAO;cACP0C,QAAQ;;;QAGd,KAAK;AACH,iBAAO;YACL,GAAGjH;YACHY,MAAMgG,OAAOhG;YACbsG,iBAAiBlH,MAAMkH,kBAAkB;YACzClG,gBAAa,wBAAE4F,OAAO5F,kBAAT,OAAA,wBAA0BmG,KAAKC,IAAL;YACvC7C,OAAO;YACPhC,eAAe;YACf0E,QAAQ;YACR,GAAI,CAACL,OAAO1F,UAAU;cACpBV,aAAa;cACbsG,mBAAmB;cACnBC,oBAAoB;;;QAG1B,KAAK;AACH,gBAAMxC,QAAQqC,OAAOrC;AAErB,cAAIqB,iBAAiBrB,KAAD,KAAWA,MAAMV,UAAU,KAAK4B,aAAa;AAC/D,mBAAO;cAAE,GAAG,KAAKA;;UAClB;AAED,iBAAO;YACL,GAAGzF;YACHuE;YACA8C,kBAAkBrH,MAAMqH,mBAAmB;YAC3CC,gBAAgBH,KAAKC,IAAL;YAChBN,mBAAmB9G,MAAM8G,oBAAoB;YAC7CC,oBAAoBxC;YACpB/D,aAAa;YACbyG,QAAQ;;QAEZ,KAAK;AACH,iBAAO;YACL,GAAGjH;YACHuC,eAAe;;QAEnB,KAAK;AACH,iBAAO;YACL,GAAGvC;YACH,GAAG4G,OAAO5G;;MAvEhB;;AA4EF,SAAKA,QAAQ6G,QAAQ,KAAK7G,KAAN;AAEpBuH,kBAAcC,MAAM,MAAM;AACxB,WAAK/H,UAAUgI,QAASvF,cAAa;AACnCA,iBAASwF,cAAcd,MAAvB;OADF;AAIA,WAAKlH,MAAM+D,OAAO;QAAEC,OAAO;QAAM3C,MAAM;QAAW6F;OAAlD;KALF;EAOD;AAjciB;AAocpB,SAAS3G,gBAMPT,SAC2B;AAC3B,QAAMoB,OACJ,OAAOpB,QAAQmI,gBAAgB,aAC1BnI,QAAQmI,YAAT,IACAnI,QAAQmI;AAEd,QAAMC,UAAU,OAAOhH,SAAS;AAEhC,QAAMiH,uBAAuBD,UACzB,OAAOpI,QAAQqI,yBAAyB,aACrCrI,QAAQqI,qBAAT,IACArI,QAAQqI,uBACV;AAEJ,SAAO;IACLjH;IACAsG,iBAAiB;IACjBlG,eAAe4G,UAAUC,wBAAAA,OAAAA,uBAAwBV,KAAKC,IAAL,IAAa;IAC9D7C,OAAO;IACP8C,kBAAkB;IAClBC,gBAAgB;IAChBR,mBAAmB;IACnBC,oBAAoB;IACpBrB,WAAW;IACXnD,eAAe;IACf0E,QAAQW,UAAU,YAAY;IAC9BpH,aAAa;;AAEhB;;;AC1iBM,IAAMsH,aAAN,cAAyBC,aAAiC;EAM/DC,YAAYC,QAA2B;AACrC,UAAA;AACA,SAAKA,SAASA,UAAU,CAAA;AACxB,SAAKC,UAAU,CAAA;AACf,SAAKC,aAAa,CAAA;EACnB;EAEDC,MACEC,QACAC,SACAC,OAC+C;AAAA,QAAA;AAC/C,UAAMC,WAAWF,QAAQE;AACzB,UAAMC,aACJH,qBAAAA,QAAQG,cAAaC,OAAAA,qBAAAA,sBAAsBF,UAAUF,OAAX;AAC5C,QAAIK,QAAQ,KAAKC,IAA4CH,SAAjD;AAEZ,QAAI,CAACE,OAAO;AACVA,cAAQ,IAAIE,MAAM;QAChBC,OAAO;QACPC,QAAQV,OAAOW,UAAP;QACRR;QACAC;QACAH,SAASD,OAAOY,oBAAoBX,OAA3B;QACTC;QACAW,gBAAgBb,OAAOc,iBAAiBX,QAAxB;MAPA,CAAV;AASR,WAAKY,IAAIT,KAAT;IACD;AAED,WAAOA;EACR;EAEDS,IAAIT,OAAwC;AAC1C,QAAI,CAAC,KAAKR,WAAWQ,MAAMF,SAAtB,GAAkC;AACrC,WAAKN,WAAWQ,MAAMF,SAAtB,IAAmCE;AACnC,WAAKT,QAAQmB,KAAKV,KAAlB;AACA,WAAKW,OAAO;QACVC,MAAM;QACNZ;OAFF;IAID;EACF;EAEDa,OAAOb,OAAwC;AAC7C,UAAMc,aAAa,KAAKtB,WAAWQ,MAAMF,SAAtB;AAEnB,QAAIgB,YAAY;AACdd,YAAMe,QAAN;AAEA,WAAKxB,UAAU,KAAKA,QAAQyB,OAAQC,OAAMA,MAAMjB,KAAjC;AAEf,UAAIc,eAAed,OAAO;AACxB,eAAO,KAAKR,WAAWQ,MAAMF,SAAtB;MACR;AAED,WAAKa,OAAO;QAAEC,MAAM;QAAWZ;OAA/B;IACD;EACF;EAEDkB,QAAc;AACZC,kBAAcC,MAAM,MAAM;AACxB,WAAK7B,QAAQ8B,QAASrB,WAAU;AAC9B,aAAKa,OAAOb,KAAZ;OADF;KADF;EAKD;EAEDC,IAMEH,WAC2D;AAC3D,WAAO,KAAKN,WAAWM,SAAhB;EACR;EAEDwB,SAAkB;AAChB,WAAO,KAAK/B;EACb;EAEDgC,KACEC,MACAC,MACgD;AAChD,UAAM,CAACC,OAAD,IAAYC,gBAAgBH,MAAMC,IAAP;AAEjC,QAAI,OAAOC,QAAQE,UAAU,aAAa;AACxCF,cAAQE,QAAQ;IACjB;AAED,WAAO,KAAKrC,QAAQgC,KAAMvB,WAAU6B,WAAWH,SAAS1B,KAAV,CAAvC;EACR;EAKD8B,QAAQN,MAAgCC,MAA8B;AACpE,UAAM,CAACC,OAAD,IAAYC,gBAAgBH,MAAMC,IAAP;AACjC,WAAOM,OAAOC,KAAKN,OAAZ,EAAqBO,SAAS,IACjC,KAAK1C,QAAQyB,OAAQhB,WAAU6B,WAAWH,SAAS1B,KAAV,CAAzC,IACA,KAAKT;EACV;EAEDoB,OAAOuB,OAA8B;AACnCf,kBAAcC,MAAM,MAAM;AACxB,WAAKe,UAAUd,QAAQ,CAAC;QAAEe;MAAF,MAAiB;AACvCA,iBAASF,KAAD;OADV;KADF;EAKD;EAEDG,UAAgB;AACdlB,kBAAcC,MAAM,MAAM;AACxB,WAAK7B,QAAQ8B,QAASrB,WAAU;AAC9BA,cAAMqC,QAAN;OADF;KADF;EAKD;EAEDC,WAAiB;AACfnB,kBAAcC,MAAM,MAAM;AACxB,WAAK7B,QAAQ8B,QAASrB,WAAU;AAC9BA,cAAMsC,SAAN;OADF;KADF;EAKD;AAtI8D;;;ACM1D,IAAMC,WAAN,cAKGC,UAAU;EAWlBC,YAAYC,QAA6D;AACvE,UAAA;AAEA,SAAKC,iBAAiBD,OAAOC;AAC7B,SAAKC,aAAaF,OAAOE;AACzB,SAAKC,gBAAgBH,OAAOG;AAC5B,SAAKC,SAASJ,OAAOI,UAAUC;AAC/B,SAAKC,YAAY,CAAA;AACjB,SAAKC,QAAQP,OAAOO,SAASC,iBAAe;AAE5C,SAAKC,WAAWT,OAAOU,OAAvB;AACA,SAAKC,WAAL;EACD;EAEDF,WACEC,SACM;AACN,SAAKA,UAAU;MAAE,GAAG,KAAKT;MAAgB,GAAGS;;AAE5C,SAAKE,gBAAgB,KAAKF,QAAQG,SAAlC;EACD;EAEO,IAAJC,OAAiC;AACnC,WAAO,KAAKJ,QAAQI;EACrB;EAEDC,SAASR,OAAiE;AACxE,SAAKS,SAAS;MAAEC,MAAM;MAAYV;KAAlC;EACD;EAEDW,YAAYC,UAAsD;AAChE,QAAI,CAAC,KAAKb,UAAUc,SAASD,QAAxB,GAAmC;AACtC,WAAKb,UAAUe,KAAKF,QAApB;AAGA,WAAKG,eAAL;AAEA,WAAKnB,cAAcoB,OAAO;QACxBN,MAAM;QACNO,UAAU;QACVL;OAHF;IAKD;EACF;EAEDM,eAAeN,UAAsD;AACnE,SAAKb,YAAY,KAAKA,UAAUoB,OAAQC,OAAMA,MAAMR,QAAnC;AAEjB,SAAKR,WAAL;AAEA,SAAKR,cAAcoB,OAAO;MACxBN,MAAM;MACNO,UAAU;MACVL;KAHF;EAKD;EAESS,iBAAiB;AACzB,QAAI,CAAC,KAAKtB,UAAUuB,QAAQ;AAC1B,UAAI,KAAKtB,MAAMuB,WAAW,WAAW;AACnC,aAAKnB,WAAL;MACD,OAAM;AACL,aAAKR,cAAc4B,OAAO,IAA1B;MACD;IACF;EACF;EAEDC,WAA6B;AAAA,QAAA,uBAAA;AAC3B,YAAO,yBAAA,gBAAA,KAAKC,YAAL,OAAA,SAAA,cAAcD,SAAd,MAAP,OAAA,wBAAmC,KAAKE,QAAL;EACpC;EAEY,MAAPA,UAA0B;AAC9B,UAAMC,kBAAkB,MAAM;AAAA,UAAA;AAC5B,WAAKF,UAAUG,cAAc;QAC3BC,IAAI,MAAM;AACR,cAAI,CAAC,KAAK3B,QAAQ4B,YAAY;AAC5B,mBAAOC,QAAQC,OAAO,qBAAf;UACR;AACD,iBAAO,KAAK9B,QAAQ4B,WAAW,KAAK/B,MAAMkC,SAAnC;;QAETC,QAAQ,CAACC,cAAcC,UAAU;AAC/B,eAAK5B,SAAS;YAAEC,MAAM;YAAU0B;YAAcC;WAA9C;;QAEFC,SAAS,MAAM;AACb,eAAK7B,SAAS;YAAEC,MAAM;WAAtB;;QAEF6B,YAAY,MAAM;AAChB,eAAK9B,SAAS;YAAEC,MAAM;WAAtB;;QAEF8B,QAAK,sBAAE,KAAKrC,QAAQqC,UAAf,OAAA,sBAAwB;QAC7BC,YAAY,KAAKtC,QAAQsC;QACzBC,aAAa,KAAKvC,QAAQuC;MAlBC,CAAD;AAqB5B,aAAO,KAAKhB,QAAQiB;;AAGtB,UAAMC,WAAW,KAAK5C,MAAMuB,WAAW;AACvC,QAAI;AAAA,UAAA,wBAAA,wBAAA,uBAAA,gBAAA,wBAAA,wBAAA,uBAAA;AACF,UAAI,CAACqB,UAAU;AAAA,YAAA,uBAAA,wBAAA,uBAAA;AACb,aAAKnC,SAAS;UAAEC,MAAM;UAAWwB,WAAW,KAAK/B,QAAQ+B;QAA3C,CAAd;AAEA,gBAAA,yBAAWtC,yBAAAA,KAAAA,cAAcH,QAAOoD,aAAhC,OAAA,SAAM,sBACJ,KAAA,wBAAA,KAAK7C,MAAMkC,WACX,IAFI;AAIN,cAAMY,UAAU,QAAM,yBAAA,gBAAA,KAAK3C,SAAQ0C,aAAb,OAAA,SAAA,sBAAA,KAAA,eAAwB,KAAK7C,MAAMkC,SAAnC;AACtB,YAAIY,YAAY,KAAK9C,MAAM8C,SAAS;AAClC,eAAKrC,SAAS;YACZC,MAAM;YACNoC;YACAZ,WAAW,KAAKlC,MAAMkC;WAHxB;QAKD;MACF;AACD,YAAMa,OAAO,MAAMnB,gBAAe;AAGlC,cAAM,0BAAA,yBAAA,KAAKhC,cAAcH,QAAOuD,cAAhC,OAAA,SAAM,uBAAA,KAAA,wBACJD,MACA,KAAK/C,MAAMkC,WACX,KAAKlC,MAAM8C,SACX,IAJI;AAON,cAAA,yBAAM,iBAAA,KAAK3C,SAAQ6C,cAAb,OAAA,SAAA,sBAAA,KAAA,gBACJD,MACA,KAAK/C,MAAMkC,WACX,KAAKlC,MAAM8C,OAHP;AAON,cAAM,0BAAA,yBAAA,KAAKlD,cAAcH,QAAOwD,cAAhC,OAAA,SAAM,uBACJF,KAAAA,wBAAAA,MACA,MACA,KAAK/C,MAAMkC,WACX,KAAKlC,MAAM8C,SACX,IALI;AAQN,cAAA,yBAAM,iBAAA,KAAK3C,SAAQ8C,cAAb,OAAA,SAAA,sBAAA,KAAA,gBACJF,MACA,MACA,KAAK/C,MAAMkC,WACX,KAAKlC,MAAM8C,OAJP;AAON,WAAKrC,SAAS;QAAEC,MAAM;QAAWqC;OAAjC;AACA,aAAOA;aACAV,OAAP;AACA,UAAI;AAAA,YAAA,wBAAA,wBAAA,uBAAA,gBAAA,wBAAA,yBAAA,wBAAA;AAEF,gBAAM,0BAAA,yBAAA,KAAKzC,cAAcH,QAAOyD,YAAhC,OAAA,SAAM,uBAAA,KAAA,wBACJb,OACA,KAAKrC,MAAMkC,WACX,KAAKlC,MAAM8C,SACX,IAJI;AAON,YAAIK,MAAuC;AACzC,eAAKtD,OAAOwC,MAAMA,KAAlB;QACD;AAED,gBAAA,yBAAM,iBAAA,KAAKlC,SAAQ+C,YAAb,OAAA,SAAA,sBAAA,KAAA,gBACJb,OACA,KAAKrC,MAAMkC,WACX,KAAKlC,MAAM8C,OAHP;AAON,gBAAM,0BAAA,0BAAA,KAAKlD,cAAcH,QAAOwD,cAAhC,OAAA,SAAM,uBACJG,KAAAA,yBAAAA,QACAf,OACA,KAAKrC,MAAMkC,WACX,KAAKlC,MAAM8C,SACX,IALI;AAQN,gBAAA,0BAAM,iBAAA,KAAK3C,SAAQ8C,cAAb,OAAA,SAAA,uBAAA,KAAA,gBACJG,QACAf,OACA,KAAKrC,MAAMkC,WACX,KAAKlC,MAAM8C,OAJP;AAMN,cAAMT;MACP,UAnCD;AAoCE,aAAK5B,SAAS;UAAEC,MAAM;UAAS2B;SAA/B;MACD;IACF;EACF;EAEO5B,SAAS4C,QAA2D;AAC1E,UAAMC,UACJtD,WACuD;AACvD,cAAQqD,OAAO3C,MAAf;QACE,KAAK;AACH,iBAAO;YACL,GAAGV;YACHoC,cAAciB,OAAOjB;YACrBmB,eAAeF,OAAOhB;;QAE1B,KAAK;AACH,iBAAO;YACL,GAAGrC;YACHwD,UAAU;;QAEd,KAAK;AACH,iBAAO;YACL,GAAGxD;YACHwD,UAAU;;QAEd,KAAK;AACH,iBAAO;YACL,GAAGxD;YACH8C,SAASO,OAAOP;YAChBC,MAAMK;YACNhB,cAAc;YACdmB,eAAe;YACflB,OAAO;YACPmB,UAAU,CAACC,SAAS,KAAKtD,QAAQuC,WAAd;YACnBnB,QAAQ;YACRW,WAAWmB,OAAOnB;;QAEtB,KAAK;AACH,iBAAO;YACL,GAAGlC;YACH+C,MAAMM,OAAON;YACbX,cAAc;YACdmB,eAAe;YACflB,OAAO;YACPd,QAAQ;YACRiC,UAAU;;QAEd,KAAK;AACH,iBAAO;YACL,GAAGxD;YACH+C,MAAMK;YACNf,OAAOgB,OAAOhB;YACdD,cAAcpC,MAAMoC,eAAe;YACnCmB,eAAeF,OAAOhB;YACtBmB,UAAU;YACVjC,QAAQ;;QAEZ,KAAK;AACH,iBAAO;YACL,GAAGvB;YACH,GAAGqD,OAAOrD;;MApDhB;;AAwDF,SAAKA,QAAQsD,QAAQ,KAAKtD,KAAN;AAEpB0D,kBAAcC,MAAM,MAAM;AACxB,WAAK5D,UAAU6D,QAAShD,cAAa;AACnCA,iBAASiD,iBAAiBR,MAA1B;OADF;AAGA,WAAKzD,cAAcoB,OAAO;QACxBC,UAAU;QACVP,MAAM;QACN2C;OAHF;KAJF;EAUD;AAlRiB;AAqRb,SAASpD,mBAKwC;AACtD,SAAO;IACL6C,SAASM;IACTL,MAAMK;IACNf,OAAO;IACPD,cAAc;IACdmB,eAAe;IACfC,UAAU;IACVjC,QAAQ;IACRW,WAAWkB;;AAEd;;;AC3SM,IAAMU,gBAAN,cAA4BC,aAAoC;EAOrEC,YAAYC,QAA8B;AACxC,UAAA;AACA,SAAKA,SAASA,UAAU,CAAA;AACxB,SAAKC,YAAY,CAAA;AACjB,SAAKC,aAAa;EACnB;EAEDC,MACEC,QACAC,SACAC,OAC+C;AAC/C,UAAMC,WAAW,IAAIC,SAAS;MAC5BC,eAAe;MACfC,QAAQN,OAAOO,UAAP;MACRT,YAAY,EAAE,KAAKA;MACnBG,SAASD,OAAOQ,uBAAuBP,OAA9B;MACTC;MACAO,gBAAgBR,QAAQS,cACpBV,OAAOW,oBAAoBV,QAAQS,WAAnC,IACAE;IARwB,CAAb;AAWjB,SAAKC,IAAIV,QAAT;AAEA,WAAOA;EACR;EAEDU,IAAIV,UAA8C;AAChD,SAAKN,UAAUiB,KAAKX,QAApB;AACA,SAAKY,OAAO;MAAEC,MAAM;MAASb;KAA7B;EACD;EAEDc,OAAOd,UAA8C;AACnD,SAAKN,YAAY,KAAKA,UAAUqB,OAAQC,OAAMA,MAAMhB,QAAnC;AACjB,SAAKY,OAAO;MAAEC,MAAM;MAAWb;KAA/B;EACD;EAEDiB,QAAc;AACZC,kBAAcC,MAAM,MAAM;AACxB,WAAKzB,UAAU0B,QAASpB,cAAa;AACnC,aAAKc,OAAOd,QAAZ;OADF;KADF;EAKD;EAEDqB,SAAqB;AACnB,WAAO,KAAK3B;EACb;EAED4B,KACEC,SAC2D;AAC3D,QAAI,OAAOA,QAAQC,UAAU,aAAa;AACxCD,cAAQC,QAAQ;IACjB;AAED,WAAO,KAAK9B,UAAU4B,KAAMtB,cAAayB,cAAcF,SAASvB,QAAV,CAA/C;EACR;EAED0B,QAAQH,SAAsC;AAC5C,WAAO,KAAK7B,UAAUqB,OAAQf,cAAayB,cAAcF,SAASvB,QAAV,CAAjD;EACR;EAEDY,OAAOe,OAAiC;AACtCT,kBAAcC,MAAM,MAAM;AACxB,WAAKS,UAAUR,QAAQ,CAAC;QAAES;MAAF,MAAiB;AACvCA,iBAASF,KAAD;OADV;KADF;EAKD;EAEDG,wBAA0C;AAAA,QAAA;AACxC,SAAKC,aAAY,iBAAA,KAAKA,aAAN,OAAA,iBAAkBC,QAAQC,QAAR,GAC/BC,KAAK,MAAM;AACV,YAAMC,kBAAkB,KAAKzC,UAAUqB,OAAQC,OAAMA,EAAEjB,MAAMqC,QAArC;AACxB,aAAOlB,cAAcC,MAAM,MACzBgB,gBAAgBE,OACd,CAACC,SAAStC,aACRsC,QAAQJ,KAAK,MAAMlC,SAASuC,SAAT,EAAoBC,MAAMC,IAA1B,CAAnB,GACFT,QAAQC,QAAR,CAHF,CADK;KAHK,EAWbC,KAAK,MAAM;AACV,WAAKH,WAAWtB;IACjB,CAba;AAehB,WAAO,KAAKsB;EACb;AAhGoE;;;AC1EhE,SAASW,wBAI8C;AAC5D,SAAO;IACLC,SAAUC,aAAY;AACpBA,cAAQC,UAAU,MAAM;AAAA,YAAA,uBAAA,wBAAA,wBAAA,wBAAA,qBAAA;AACtB,cAAMC,eACJF,wBAAAA,QAAQG,iBADuD,OAAA,UAAA,yBAC/D,sBAAsBC,SAAtB,OAAA,SAAA,uBAA4BF;AAC9B,cAAMG,aAAYL,yBAAAA,QAAQG,iBAAX,OAAA,UAAA,yBAAG,uBAAsBC,SAAtB,OAAA,SAAA,uBAA4BC;AAC9C,cAAMC,YAAYD,aAAAA,OAAAA,SAAAA,UAAWC;AAC7B,cAAMC,sBAAqBF,aAAS,OAAT,SAAAA,UAAWG,eAAc;AACpD,cAAMC,0BAAyBJ,aAAS,OAAT,SAAAA,UAAWG,eAAc;AACxD,cAAME,aAAW,sBAAAV,QAAQW,MAAMC,SAAd,OAAA,SAAA,oBAAoBC,UAAS,CAAA;AAC9C,cAAMC,kBAAgB,uBAAAd,QAAQW,MAAMC,SAAd,OAAA,SAAA,qBAAoBG,eAAc,CAAA;AACxD,YAAIC,gBAAgBF;AACpB,YAAIG,YAAY;AAEhB,cAAMC,oBAAqBC,YAAoB;AAC7CC,iBAAOC,eAAeF,QAAQ,UAAU;YACtCG,YAAY;YACZC,KAAK,MAAM;AAAA,kBAAA;AACT,mBAAA,kBAAIvB,QAAQwB,WAAR,QAAA,gBAAgBC,SAAS;AAC3BR,4BAAY;cACb,OAAM;AAAA,oBAAA;AACL,iBAAAjB,mBAAAA,QAAQwB,WAAR,OAAA,SAAA,iBAAgBE,iBAAiB,SAAS,MAAM;AAC9CT,8BAAY;iBADd;cAGD;AACD,qBAAOjB,QAAQwB;YAChB;WAXH;QAaD;AAGD,cAAMG,UACJ3B,QAAQ4B,QAAQD,YAAY,MAAME,QAAQC,OAAO,iBAAf;AAEpC,cAAMC,gBAAgB,CACpBlB,OACAmB,OACAC,MACAC,aACG;AACHlB,0BAAgBkB,WACZ,CAACF,OAAO,GAAGhB,aAAX,IACA,CAAC,GAAGA,eAAegB,KAAnB;AACJ,iBAAOE,WAAW,CAACD,MAAM,GAAGpB,KAAV,IAAmB,CAAC,GAAGA,OAAOoB,IAAX;QACtC;AAGD,cAAME,YAAY,CAChBtB,OACAuB,QACAJ,OACAE,aACuB;AACvB,cAAIjB,WAAW;AACb,mBAAOY,QAAQC,OAAO,WAAf;UACR;AAED,cAAI,OAAOE,UAAU,eAAe,CAACI,UAAUvB,MAAMwB,QAAQ;AAC3D,mBAAOR,QAAQS,QAAQzB,KAAhB;UACR;AAED,gBAAM0B,iBAAuC;YAC3CC,UAAUxC,QAAQwC;YAClBlC,WAAW0B;YACX5B,MAAMJ,QAAQ4B,QAAQxB;;AAGxBc,4BAAkBqB,cAAD;AAEjB,gBAAME,gBAAgBd,QAAQY,cAAD;AAE7B,gBAAMG,WAAUb,QAAQS,QAAQG,aAAhB,EAA+BE,KAAMV,UACnDF,cAAclB,OAAOmB,OAAOC,MAAMC,QAArB,CADC;AAIhB,iBAAOQ;;AAGT,YAAIA;AAGJ,YAAI,CAAChC,SAAS2B,QAAQ;AACpBK,oBAAUP,UAAU,CAAA,CAAD;QACpB,WAGQ5B,oBAAoB;AAC3B,gBAAM6B,SAAS,OAAO9B,cAAc;AACpC,gBAAM0B,QAAQI,SACV9B,YACAsC,iBAAiB5C,QAAQ4B,SAASlB,QAAlB;AACpBgC,oBAAUP,UAAUzB,UAAU0B,QAAQJ,KAAnB;QACpB,WAGQvB,wBAAwB;AAC/B,gBAAM2B,SAAS,OAAO9B,cAAc;AACpC,gBAAM0B,QAAQI,SACV9B,YACAuC,qBAAqB7C,QAAQ4B,SAASlB,QAAlB;AACxBgC,oBAAUP,UAAUzB,UAAU0B,QAAQJ,OAAO,IAA1B;QACpB,OAGI;AACHhB,0BAAgB,CAAA;AAEhB,gBAAMoB,SAAS,OAAOpC,QAAQ4B,QAAQgB,qBAAqB;AAE3D,gBAAME,uBACJ5C,eAAeQ,SAAS,CAAD,IACnBR,YAAYQ,SAAS,CAAD,GAAK,GAAGA,QAAjB,IACX;AAGNgC,oBAAUI,uBACNX,UAAU,CAAA,GAAIC,QAAQtB,cAAc,CAAD,CAA1B,IACTe,QAAQS,QAAQP,cAAc,CAAA,GAAIjB,cAAc,CAAD,GAAKJ,SAAS,CAAD,CAA/B,CAA7B;AAGJ,mBAASqC,IAAI,GAAGA,IAAIrC,SAAS2B,QAAQU,KAAK;AACxCL,sBAAUA,QAAQC,KAAM9B,WAAU;AAChC,oBAAMmC,sBACJ9C,eAAeQ,SAASqC,CAAD,IACnB7C,YAAYQ,SAASqC,CAAD,GAAKA,GAAGrC,QAAjB,IACX;AAEN,kBAAIsC,qBAAqB;AACvB,sBAAMhB,QAAQI,SACVtB,cAAciC,CAAD,IACbH,iBAAiB5C,QAAQ4B,SAASf,KAAlB;AACpB,uBAAOsB,UAAUtB,OAAOuB,QAAQJ,KAAhB;cACjB;AACD,qBAAOH,QAAQS,QACbP,cAAclB,OAAOC,cAAciC,CAAD,GAAKrC,SAASqC,CAAD,CAAlC,CADR;YAGR,CAfS;UAgBX;QACF;AAED,cAAME,eAAeP,QAAQC,KAAM9B,YAAW;UAC5CA;UACAE,YAAYC;QAFgC,EAAzB;AAKrB,eAAOiC;;IAEV;;AAEJ;AAEM,SAASL,iBACdhB,SACAf,OACqB;AACrB,SAAOe,QAAQgB,oBAAf,OAAA,SAAOhB,QAAQgB,iBAAmB/B,MAAMA,MAAMwB,SAAS,CAAhB,GAAoBxB,KAApD;AACR;AAEM,SAASgC,qBACdjB,SACAf,OACqB;AACrB,SAAOe,QAAQiB,wBAAf,OAAA,SAAOjB,QAAQiB,qBAAuBhC,MAAM,CAAD,GAAKA,KAAzC;AACR;AAMM,SAASqC,YACdtB,SACAf,OACqB;AACrB,MAAIe,QAAQgB,oBAAoBO,MAAMC,QAAQvC,KAAd,GAAsB;AACpD,UAAMwC,gBAAgBT,iBAAiBhB,SAASf,KAAV;AACtC,WACE,OAAOwC,kBAAkB,eACzBA,kBAAkB,QAClBA,kBAAkB;EAErB;AACD;AACD;AAMM,SAASC,gBACd1B,SACAf,OACqB;AACrB,MAAIe,QAAQiB,wBAAwBM,MAAMC,QAAQvC,KAAd,GAAsB;AACxD,UAAM0C,oBAAoBV,qBAAqBjB,SAASf,KAAV;AAC9C,WACE,OAAO0C,sBAAsB,eAC7BA,sBAAsB,QACtBA,sBAAsB;EAEzB;AACD;AACD;;;AC9JM,IAAMC,cAAN,MAAkB;EAWvBC,YAAYC,SAA4B,CAAA,GAAI;AAC1C,SAAKC,aAAaD,OAAOC,cAAc,IAAIC,WAAJ;AACvC,SAAKC,gBAAgBH,OAAOG,iBAAiB,IAAIC,cAAJ;AAC7C,SAAKC,SAASL,OAAOK,UAAUC;AAC/B,SAAKC,iBAAiBP,OAAOO,kBAAkB,CAAA;AAC/C,SAAKC,gBAAgB,CAAA;AACrB,SAAKC,mBAAmB,CAAA;AACxB,SAAKC,aAAa;AAElB,QAA6CV,OAAOK,QAAQ;AAC1D,WAAKA,OAAOM,MAAZ,4FAAA;IAGD;EACF;EAEDC,QAAc;AACZ,SAAKF;AACL,QAAI,KAAKA,eAAe;AAAG;AAE3B,SAAKG,mBAAmBC,aAAaC,UAAU,MAAM;AACnD,UAAID,aAAaE,UAAb,GAA0B;AAC5B,aAAKC,sBAAL;AACA,aAAKhB,WAAWiB,QAAhB;MACD;IACF,CALuB;AAMxB,SAAKC,oBAAoBC,cAAcL,UAAU,MAAM;AACrD,UAAIK,cAAcC,SAAd,GAA0B;AAC5B,aAAKJ,sBAAL;AACA,aAAKhB,WAAWqB,SAAhB;MACD;IACF,CALwB;EAM1B;EAEDC,UAAgB;AAAA,QAAA,uBAAA;AACd,SAAKb;AACL,QAAI,KAAKA,eAAe;AAAG;AAE3B,KAAA,wBAAA,KAAKG,qBAAL,OAAA,SAAA,sBAAA,KAAA,IAAA;AACA,SAAKA,mBAAmBW;AAExB,KAAA,wBAAA,KAAKL,sBAAL,OAAA,SAAA,sBAAA,KAAA,IAAA;AACA,SAAKA,oBAAoBK;EAC1B;EAIDC,WAAWC,MAAgCC,MAA6B;AACtE,UAAM,CAACC,OAAD,IAAYC,gBAAgBH,MAAMC,IAAP;AACjCC,YAAQE,cAAc;AACtB,WAAO,KAAK7B,WAAW8B,QAAQH,OAAxB,EAAiCI;EACzC;EAEDC,WAAWL,SAAmC;AAC5C,WAAO,KAAKzB,cAAc4B,QAAQ;MAAE,GAAGH;MAASM,UAAU;IAAxB,CAA3B,EAA2DF;EACnE;EAEDG,aACEC,UACAR,SAC0B;AAAA,QAAA;AAC1B,YAAA,wBAAO,KAAK3B,WAAWoC,KAAmBD,UAAUR,OAA7C,MAAP,OAAA,SAAO,sBAAuDU,MAAMC;EACrE;EAsCDC,gBAMEd,MAMAC,MAGAc,MACgB;AAChB,UAAMC,gBAAgBC,eAAejB,MAAMC,MAAMc,IAAb;AACpC,UAAMG,aAAa,KAAKT,aAAoBO,cAAcN,QAAvC;AAEnB,WAAOQ,aACHC,QAAQC,QAAQF,UAAhB,IACA,KAAKG,WAAWL,aAAhB;EACL;EAQDM,eACEC,mBACwC;AACxC,WAAO,KAAKC,cAAL,EACJnB,QAAQkB,iBADJ,EAEJE,IAAI,CAAC;MAAEf;MAAUE;IAAZ,MAAwB;AAC5B,YAAMC,OAAOD,MAAMC;AACnB,aAAO,CAACH,UAAUG,IAAX;IACR,CALI;EAMR;EAEDa,aACEhB,UACAiB,SACAC,SAC0B;AAC1B,UAAMC,QAAQ,KAAKtD,WAAWoC,KAAmBD,QAAnC;AACd,UAAMoB,WAAWD,SAAH,OAAA,SAAGA,MAAOjB,MAAMC;AAC9B,UAAMA,OAAOkB,iBAAiBJ,SAASG,QAAV;AAE7B,QAAI,OAAOjB,SAAS,aAAa;AAC/B,aAAOf;IACR;AAED,UAAMkB,gBAAgBC,eAAeP,QAAD;AACpC,UAAMsB,mBAAmB,KAAKC,oBAAoBjB,aAAzB;AACzB,WAAO,KAAKzC,WACT2D,MAAM,MAAMF,gBADR,EAEJG,QAAQtB,MAAM;MAAE,GAAGe;MAASQ,QAAQ;IAAtB,CAFV;EAGR;EAcDC,eACEd,mBACAI,SACAC,SACwC;AACxC,WAAOU,cAAcC,MAAM,MACzB,KAAKf,cAAL,EACGnB,QAAQkB,iBADX,EAEGE,IAAI,CAAC;MAAEf;IAAF,MAAiB,CACrBA,UACA,KAAKgB,aAA2BhB,UAAUiB,SAASC,OAAnD,CAFqB,CAFzB,CADK;EAQR;EAEDY,cACE9B,UACAR,SAC8C;AAAA,QAAA;AAC9C,YAAO,yBAAA,KAAK3B,WAAWoC,KAA2BD,UAAUR,OAArD,MAAA,OAAA,SAAA,uBAA+DU;EACvE;EAID6B,cAAczC,MAAgCC,MAA2B;AACvE,UAAM,CAACC,OAAD,IAAYC,gBAAgBH,MAAMC,IAAP;AACjC,UAAM1B,aAAa,KAAKA;AACxB+D,kBAAcC,MAAM,MAAM;AACxBhE,iBAAW8B,QAAQH,OAAnB,EAA4BwC,QAASb,WAAU;AAC7CtD,mBAAWoE,OAAOd,KAAlB;OADF;KADF;EAKD;EAWDe,aACE5C,MACAC,MACAc,MACe;AACf,UAAM,CAACb,SAAS0B,OAAV,IAAqBzB,gBAAgBH,MAAMC,MAAMc,IAAb;AAC1C,UAAMxC,aAAa,KAAKA;AAExB,UAAMsE,iBAAsC;MAC1CC,MAAM;MACN,GAAG5C;;AAGL,WAAOoC,cAAcC,MAAM,MAAM;AAC/BhE,iBAAW8B,QAAQH,OAAnB,EAA4BwC,QAASb,WAAU;AAC7CA,cAAMkB,MAAN;OADF;AAGA,aAAO,KAAKC,eAAeH,gBAAgBjB,OAApC;IACR,CALM;EAMR;EAQDqB,cACEjD,MACAC,MACAc,MACe;AACf,UAAM,CAACb,SAASgD,gBAAgB,CAAA,CAA1B,IAAgC/C,gBAAgBH,MAAMC,MAAMc,IAAb;AAErD,QAAI,OAAOmC,cAAcC,WAAW,aAAa;AAC/CD,oBAAcC,SAAS;IACxB;AAED,UAAMC,WAAWd,cAAcC,MAAM,MACnC,KAAKhE,WACF8B,QAAQH,OADX,EAEGuB,IAAKI,WAAUA,MAAMwB,OAAOH,aAAb,CAFlB,CADe;AAMjB,WAAO/B,QAAQmC,IAAIF,QAAZ,EAAsBG,KAAKC,IAA3B,EAAiCC,MAAMD,IAAvC;EACR;EAWDE,kBACE1D,MACAC,MACAc,MACe;AACf,UAAM,CAACb,SAAS0B,OAAV,IAAqBzB,gBAAgBH,MAAMC,MAAMc,IAAb;AAE1C,WAAOuB,cAAcC,MAAM,MAAM;AAAA,UAAA,MAAA;AAC/B,WAAKhE,WAAW8B,QAAQH,OAAxB,EAAiCwC,QAASb,WAAU;AAClDA,cAAM8B,WAAN;OADF;AAIA,UAAIzD,QAAQ0D,gBAAgB,QAAQ;AAClC,eAAOzC,QAAQC,QAAR;MACR;AACD,YAAMyB,iBAAsC;QAC1C,GAAG3C;QACH4C,OAAI,QAAA,uBAAE5C,QAAQ0D,gBAAV,OAAA,uBAAyB1D,QAAQ4C,SAAQ,OAAA,OAAA;;AAE/C,aAAO,KAAKE,eAAeH,gBAAgBjB,OAApC;IACR,CAbM;EAcR;EAWDoB,eACEhD,MACAC,MACAc,MACe;AACf,UAAM,CAACb,SAAS0B,OAAV,IAAqBzB,gBAAgBH,MAAMC,MAAMc,IAAb;AAE1C,UAAMqC,WAAWd,cAAcC,MAAM,MACnC,KAAKhE,WACF8B,QAAQH,OADX,EAEG2D,OAAQhC,WAAU,CAACA,MAAMiC,WAAN,CAFtB,EAGGrC,IAAKI,WAAD;AAAA,UAAA;AAAA,aACHA,MAAMkC,MAAMjE,QAAW;QACrB,GAAG8B;QACHoC,gBAAa,wBAAEpC,WAAF,OAAA,SAAEA,QAASoC,kBAAX,OAAA,wBAA4B;QACzCC,MAAM;UAAEC,aAAahE,QAAQgE;QAAvB;MAHe,CAAvB;IADG,CAHP,CADe;AAajB,QAAIC,UAAUhD,QAAQmC,IAAIF,QAAZ,EAAsBG,KAAKC,IAA3B;AAEd,QAAI,EAAC5B,WAAD,QAACA,QAASwC,eAAc;AAC1BD,gBAAUA,QAAQV,MAAMD,IAAd;IACX;AAED,WAAOW;EACR;EA6BD9C,WAMErB,MACAC,MAGAc,MACgB;AAChB,UAAMC,gBAAgBC,eAAejB,MAAMC,MAAMc,IAAb;AACpC,UAAMiB,mBAAmB,KAAKC,oBAAoBjB,aAAzB;AAGzB,QAAI,OAAOgB,iBAAiBqC,UAAU,aAAa;AACjDrC,uBAAiBqC,QAAQ;IAC1B;AAED,UAAMxC,QAAQ,KAAKtD,WAAW2D,MAAM,MAAMF,gBAA5B;AAEd,WAAOH,MAAMyC,cAActC,iBAAiBuC,SAArC,IACH1C,MAAMkC,MAAM/B,gBAAZ,IACAb,QAAQC,QAAQS,MAAMjB,MAAMC,IAA5B;EACL;EA6BD2D,cAMExE,MACAC,MAGAc,MACe;AACf,WAAO,KAAKM,WAAWrB,MAAaC,MAAac,IAA1C,EACJwC,KAAKC,IADD,EAEJC,MAAMD,IAFF;EAGR;EA6BDiB,mBAMEzE,MAGAC,MAGAc,MAC8B;AAC9B,UAAMC,gBAAgBC,eAAejB,MAAMC,MAAMc,IAAb;AACpCC,kBAAc0D,WAAWC,sBAAqB;AAK9C,WAAO,KAAKtD,WAAWL,aAAhB;EACR;EA6BD4D,sBAME5E,MAGAC,MAGAc,MACe;AACf,WAAO,KAAK0D,mBAAmBzE,MAAaC,MAAac,IAAlD,EACJwC,KAAKC,IADD,EAEJC,MAAMD,IAFF;EAGR;EAEDjE,wBAA0C;AACxC,WAAO,KAAKd,cAAcc,sBAAnB;EACR;EAEDiC,gBAA4B;AAC1B,WAAO,KAAKjD;EACb;EAEDsG,mBAAkC;AAChC,WAAO,KAAKpG;EACb;EAEDqG,YAAoB;AAClB,WAAO,KAAKnG;EACb;EAEDoG,oBAAoC;AAClC,WAAO,KAAKlG;EACb;EAEDmG,kBAAkBpD,SAA+B;AAC/C,SAAK/C,iBAAiB+C;EACvB;EAEDqD,iBACEvE,UACAkB,SACM;AACN,UAAMsD,SAAS,KAAKpG,cAAc6B,KAC/BwE,OAAMC,aAAa1E,QAAD,MAAe0E,aAAaD,EAAEzE,QAAH,CADjC;AAGf,QAAIwE,QAAQ;AACVA,aAAOrG,iBAAiB+C;IACzB,OAAM;AACL,WAAK9C,cAAcuG,KAAK;QAAE3E;QAAU7B,gBAAgB+C;OAApD;IACD;EACF;EAED0D,iBACE5E,UAC2D;AAC3D,QAAI,CAACA,UAAU;AACb,aAAOZ;IACR;AAGD,UAAMyF,wBAAwB,KAAKzG,cAAc6B,KAAMwE,OACrDK,gBAAgB9E,UAAUyE,EAAEzE,QAAb,CADa;AAK9B,QAAI+E,MAAuC;AAEzC,YAAMC,mBAAmB,KAAK5G,cAAc+E,OAAQsB,OAClDK,gBAAgB9E,UAAUyE,EAAEzE,QAAb,CADQ;AAIzB,UAAIgF,iBAAiBpF,SAAS,GAAG;AAC/B,aAAK3B,OAAOM,MAAZ,0DAC0D0G,KAAKC,UAC3DlF,QADsD,IAD1D,gNAAA;MAKD;IACF;AAED,WAAO6E,yBAAP,OAAA,SAAOA,sBAAuB1G;EAC/B;EAEDgH,oBACEC,aACAlE,SACM;AACN,UAAMsD,SAAS,KAAKnG,iBAAiB4B,KAClCwE,OAAMC,aAAaU,WAAD,MAAkBV,aAAaD,EAAEW,WAAH,CADpC;AAGf,QAAIZ,QAAQ;AACVA,aAAOrG,iBAAiB+C;IACzB,OAAM;AACL,WAAK7C,iBAAiBsG,KAAK;QAAES;QAAajH,gBAAgB+C;OAA1D;IACD;EACF;EAEDmE,oBACED,aACyD;AACzD,QAAI,CAACA,aAAa;AAChB,aAAOhG;IACR;AAGD,UAAMyF,wBAAwB,KAAKxG,iBAAiB4B,KAAMwE,OACxDK,gBAAgBM,aAAaX,EAAEW,WAAhB,CADa;AAK9B,QAAIL,MAAuC;AAEzC,YAAMC,mBAAmB,KAAK3G,iBAAiB8E,OAAQsB,OACrDK,gBAAgBM,aAAaX,EAAEW,WAAhB,CADQ;AAIzB,UAAIJ,iBAAiBpF,SAAS,GAAG;AAC/B,aAAK3B,OAAOM,MAAZ,6DAC6D0G,KAAKC,UAC9DE,WADyD,IAD7D,yNAAA;MAKD;IACF;AAED,WAAOP,yBAAP,OAAA,SAAOA,sBAAuB1G;EAC/B;EAEDoD,oBAOEL,SAeA;AACA,QAAIA,WAAJ,QAAIA,QAASoE,YAAY;AACvB,aAAOpE;IAOR;AAED,UAAMI,mBAAmB;MACvB,GAAG,KAAKnD,eAAeoH;MACvB,GAAG,KAAKX,iBAAiB1D,WAAtB,OAAA,SAAsBA,QAASlB,QAA/B;MACH,GAAGkB;MACHoE,YAAY;;AAGd,QAAI,CAAChE,iBAAiBkE,aAAalE,iBAAiBtB,UAAU;AAC5DsB,uBAAiBkE,YAAYC,sBAC3BnE,iBAAiBtB,UACjBsB,gBAFgD;IAInD;AAGD,QAAI,OAAOA,iBAAiBoE,uBAAuB,aAAa;AAC9DpE,uBAAiBoE,qBACfpE,iBAAiBqE,gBAAgB;IACpC;AACD,QAAI,OAAOrE,iBAAiBsE,qBAAqB,aAAa;AAC5DtE,uBAAiBsE,mBAAmB,CAAC,CAACtE,iBAAiBuE;IACxD;AAED,WAAOvE;EAOR;EAEDwE,uBACE5E,SACG;AACH,QAAIA,WAAJ,QAAIA,QAASoE,YAAY;AACvB,aAAOpE;IACR;AACD,WAAO;MACL,GAAG,KAAK/C,eAAe4H;MACvB,GAAG,KAAKV,oBAAoBnE,WAAzB,OAAA,SAAyBA,QAASkE,WAAlC;MACH,GAAGlE;MACHoE,YAAY;;EAEf;EAEDU,QAAc;AACZ,SAAKnI,WAAWmI,MAAhB;AACA,SAAKjI,cAAciI,MAAnB;EACD;AA/tBsB;;;ACjBlB,IAAMC,gBAAN,cAMGC,aAAmD;EA8B3DC,YACEC,QACAC,SAOA;AACA,UAAA;AAEA,SAAKD,SAASA;AACd,SAAKC,UAAUA;AACf,SAAKC,eAAe,oBAAIC,IAAJ;AACpB,SAAKC,cAAc;AACnB,SAAKC,YAAL;AACA,SAAKC,WAAWL,OAAhB;EACD;EAESI,cAAoB;AAC5B,SAAKE,SAAS,KAAKA,OAAOC,KAAK,IAAjB;AACd,SAAKC,UAAU,KAAKA,QAAQD,KAAK,IAAlB;EAChB;EAESE,cAAoB;AAC5B,QAAI,KAAKC,UAAUC,SAAS,GAAG;AAC7B,WAAKC,aAAaC,YAAY,IAA9B;AAEA,UAAIC,mBAAmB,KAAKF,cAAc,KAAKZ,OAAzB,GAAmC;AACvD,aAAKe,aAAL;MACD;AAED,WAAKC,aAAL;IACD;EACF;EAESC,gBAAsB;AAC9B,QAAI,CAAC,KAAKC,aAAL,GAAqB;AACxB,WAAKC,QAAL;IACD;EACF;EAEDC,yBAAkC;AAChC,WAAOC,cACL,KAAKT,cACL,KAAKZ,SACL,KAAKA,QAAQsB,kBAHK;EAKrB;EAEDC,2BAAoC;AAClC,WAAOF,cACL,KAAKT,cACL,KAAKZ,SACL,KAAKA,QAAQwB,oBAHK;EAKrB;EAEDL,UAAgB;AACd,SAAKT,YAAY,oBAAIR,IAAJ;AACjB,SAAKuB,kBAAL;AACA,SAAKC,qBAAL;AACA,SAAKd,aAAae,eAAe,IAAjC;EACD;EAEDtB,WACEL,SAOA4B,eACM;AACN,UAAMC,cAAc,KAAK7B;AACzB,UAAM8B,YAAY,KAAKlB;AAEvB,SAAKZ,UAAU,KAAKD,OAAOgC,oBAAoB/B,OAAhC;AAEf,QAEE,QAAOA,WAAP,OAAA,SAAOA,QAASgC,iBAAgB,aAChC;AACA,WAAKjC,OACFkC,UADH,EAEGC,MAFH,wLAAA;IAKD;AAED,QAAI,CAACC,oBAAoBN,aAAa,KAAK7B,OAAnB,GAA6B;AACnD,WAAKD,OAAOqC,cAAZ,EAA4BC,OAAO;QACjCC,MAAM;QACNC,OAAO,KAAK3B;QACZ4B,UAAU;OAHZ;IAKD;AAED,QACE,OAAO,KAAKxC,QAAQyC,YAAY,eAChC,OAAO,KAAKzC,QAAQyC,YAAY,WAChC;AACA,YAAM,IAAIC,MAAM,kCAAV;IACP;AAGD,QAAI,CAAC,KAAK1C,QAAQ2C,UAAU;AAC1B,WAAK3C,QAAQ2C,WAAWd,YAAYc;IACrC;AAED,SAAKC,YAAL;AAEA,UAAMC,UAAU,KAAK3B,aAAL;AAGhB,QACE2B,WACAC,sBACE,KAAKlC,cACLkB,WACA,KAAK9B,SACL6B,WAJmB,GAMrB;AACA,WAAKd,aAAL;IACD;AAGD,SAAKgC,aAAanB,aAAlB;AAGA,QACEiB,YACC,KAAKjC,iBAAiBkB,aACrB,KAAK9B,QAAQyC,YAAYZ,YAAYY,WACrC,KAAKzC,QAAQgD,cAAcnB,YAAYmB,YACzC;AACA,WAAKC,mBAAL;IACD;AAED,UAAMC,sBAAsB,KAAKC,uBAAL;AAG5B,QACEN,YACC,KAAKjC,iBAAiBkB,aACrB,KAAK9B,QAAQyC,YAAYZ,YAAYY,WACrCS,wBAAwB,KAAKE,yBAC/B;AACA,WAAKC,sBAAsBH,mBAA3B;IACD;EACF;EAEDI,oBACEtD,SAOoC;AACpC,UAAMuC,QAAQ,KAAKxC,OAAOqC,cAAZ,EAA4BmB,MAAM,KAAKxD,QAAQC,OAA/C;AAEd,WAAO,KAAKwD,aAAajB,OAAOvC,OAAzB;EACR;EAEDyD,mBAAuD;AACrD,WAAO,KAAKC;EACb;EAEDC,YACEC,QACoC;AACpC,UAAMC,gBAAgB,CAAA;AAEtBC,WAAOC,KAAKH,MAAZ,EAAoBI,QAASC,SAAQ;AACnCH,aAAOI,eAAeL,eAAeI,KAAK;QACxCE,cAAc;QACdC,YAAY;QACZC,KAAK,MAAM;AACT,eAAKpE,aAAaqE,IAAIL,GAAtB;AACA,iBAAOL,OAAOK,GAAD;QACd;OANH;KADF;AAWA,WAAOJ;EACR;EAEDU,kBAAsE;AACpE,WAAO,KAAK3D;EACb;EAEDN,SAAe;AACb,SAAKP,OAAOqC,cAAZ,EAA4B9B,OAAO,KAAKM,YAAxC;EACD;EAEDJ,QAAmB;IACjBgE;IACA,GAAGxE;EAFc,IAGiC,CAAA,GAElD;AACA,WAAO,KAAKyE,MAAM;MAChB,GAAGzE;MACH0E,MAAM;QAAEF;MAAF;IAFU,CAAX;EAIR;EAEDG,gBACE3E,SAO6C;AAC7C,UAAM4E,mBAAmB,KAAK7E,OAAOgC,oBAAoB/B,OAAhC;AAEzB,UAAMuC,QAAQ,KAAKxC,OAChBqC,cADW,EAEXmB,MAAM,KAAKxD,QAAQ6E,gBAFR;AAGdrC,UAAMsC,uBAAuB;AAE7B,WAAOtC,MAAMkC,MAAN,EAAcK,KAAK,MAAM,KAAKtB,aAAajB,OAAOqC,gBAAzB,CAAzB;EACR;EAESH,MACRM,cAC6C;AAAA,QAAA;AAC7C,WAAO,KAAKhE,aAAa;MACvB,GAAGgE;MACHC,gBAAeD,wBAAAA,aAAaC,kBAAiB,OAAA,wBAAA;KAFxC,EAGJF,KAAK,MAAM;AACZ,WAAK/B,aAAL;AACA,aAAO,KAAKW;IACb,CANM;EAOR;EAEO3C,aACNgE,cACiC;AAEjC,SAAKnC,YAAL;AAGA,QAAIqC,UAA2C,KAAKrE,aAAa6D,MAC/D,KAAKzE,SACL+E,YAF6C;AAK/C,QAAI,EAACA,gBAAD,QAACA,aAAcG,eAAc;AAC/BD,gBAAUA,QAAQE,MAAMC,IAAd;IACX;AAED,WAAOH;EACR;EAEOhC,qBAA2B;AACjC,SAAKxB,kBAAL;AAEA,QACE4D,YACA,KAAK3B,cAAc4B,WACnB,CAACC,eAAe,KAAKvF,QAAQgD,SAAd,GACf;AACA;IACD;AAED,UAAMwC,OAAOC,eACX,KAAK/B,cAAcgC,eACnB,KAAK1F,QAAQgD,SAFY;AAO3B,UAAM2C,UAAUH,OAAO;AAEvB,SAAKI,iBAAiBC,WAAW,MAAM;AACrC,UAAI,CAAC,KAAKnC,cAAc4B,SAAS;AAC/B,aAAKvC,aAAL;MACD;OACA4C,OAJ6B;EAKjC;EAEOxC,yBAAyB;AAAA,QAAA;AAC/B,WAAO,OAAO,KAAKnD,QAAQ8F,oBAAoB,aAC3C,KAAK9F,QAAQ8F,gBAAgB,KAAKpC,cAAcqC,MAAM,KAAKnF,YAA3D,KACA,wBAAA,KAAKZ,QAAQ8F,oBAFV,OAAA,wBAE6B;EACrC;EAEOzC,sBAAsB2C,cAAoC;AAChE,SAAKtE,qBAAL;AAEA,SAAK0B,yBAAyB4C;AAE9B,QACEX,YACA,KAAKrF,QAAQyC,YAAY,SACzB,CAAC8C,eAAe,KAAKnC,sBAAN,KACf,KAAKA,2BAA2B,GAChC;AACA;IACD;AAED,SAAK6C,oBAAoBC,YAAY,MAAM;AACzC,UACE,KAAKlG,QAAQmG,+BACbC,aAAaC,UAAb,GACA;AACA,aAAKtF,aAAL;MACD;OACA,KAAKqC,sBAP4B;EAQrC;EAEOpC,eAAqB;AAC3B,SAAKiC,mBAAL;AACA,SAAKI,sBAAsB,KAAKF,uBAAL,CAA3B;EACD;EAEO1B,oBAA0B;AAChC,QAAI,KAAKmE,gBAAgB;AACvBU,mBAAa,KAAKV,cAAN;AACZ,WAAKA,iBAAiBW;IACvB;EACF;EAEO7E,uBAA6B;AACnC,QAAI,KAAKuE,mBAAmB;AAC1BO,oBAAc,KAAKP,iBAAN;AACb,WAAKA,oBAAoBM;IAC1B;EACF;EAES/C,aACRjB,OACAvC,SAOoC;AACpC,UAAM8B,YAAY,KAAKlB;AACvB,UAAMiB,cAAc,KAAK7B;AACzB,UAAMyG,aAAa,KAAK/C;AAGxB,UAAMgD,kBAAkB,KAAKC;AAC7B,UAAMC,oBAAoB,KAAKC;AAC/B,UAAMC,cAAcvE,UAAUT;AAC9B,UAAMiF,oBAAoBD,cACtBvE,MAAMyE,QACN,KAAKC;AACT,UAAMC,kBAAkBJ,cACpB,KAAKpD,gBACL,KAAKyD;AAET,UAAM;MAAEH;IAAF,IAAYzE;AAClB,QAAI;MAAEmD;MAAexD;MAAOkF;MAAgBC;MAAaC;IAArD,IAAgEN;AACpE,QAAIO,iBAAiB;AACrB,QAAIC,oBAAoB;AACxB,QAAIzB;AAGJ,QAAI/F,QAAQyH,oBAAoB;AAC9B,YAAM5E,UAAU,KAAK3B,aAAL;AAEhB,YAAMwG,eAAe,CAAC7E,WAAW/B,mBAAmByB,OAAOvC,OAAR;AAEnD,YAAM2H,kBACJ9E,WAAWC,sBAAsBP,OAAOT,WAAW9B,SAAS6B,WAA5B;AAElC,UAAI6F,gBAAgBC,iBAAiB;AACnCN,sBAAcO,SAASrF,MAAMvC,QAAQ6H,WAAf,IAClB,aACA;AACJ,YAAI,CAACnC,eAAe;AAClB4B,mBAAS;QACV;MACF;AACD,UAAItH,QAAQyH,uBAAuB,eAAe;AAChDJ,sBAAc;MACf;IACF;AAGD,QACErH,QAAQ8H,oBACR,CAACd,MAAMtB,iBACPwB,mBAFA,QAEAA,gBAAiBa,aACjBT,WAAW,SACX;AACAvB,aAAOmB,gBAAgBnB;AACvBL,sBAAgBwB,gBAAgBxB;AAChC4B,eAASJ,gBAAgBI;AACzBC,uBAAiB;IAClB,WAEQvH,QAAQgI,UAAU,OAAOhB,MAAMjB,SAAS,aAAa;AAE5D,UACEU,cACAO,MAAMjB,UAASW,mBAAAA,OAAAA,SAAAA,gBAAiBX,SAChC/F,QAAQgI,WAAW,KAAKC,UACxB;AACAlC,eAAO,KAAKmC;MACb,OAAM;AACL,YAAI;AACF,eAAKD,WAAWjI,QAAQgI;AACxBjC,iBAAO/F,QAAQgI,OAAOhB,MAAMjB,IAArB;AACPA,iBAAOoC,YAAY1B,cAAAA,OAAAA,SAAAA,WAAYV,MAAMA,MAAM/F,OAAzB;AAClB,eAAKkI,eAAenC;AACpB,eAAK5F,cAAc;iBACZA,aAAP;AACA,cAAIiI,MAAuC;AACzC,iBAAKrI,OAAOkC,UAAZ,EAAwBC,MAAM/B,WAA9B;UACD;AACD,eAAKA,cAAcA;QACpB;MACF;IACF,OAEI;AACH4F,aAAOiB,MAAMjB;IACd;AAGD,QACE,OAAO/F,QAAQqI,oBAAoB,eACnC,OAAOtC,SAAS,eAChBuB,WAAW,WACX;AACA,UAAIe;AAGJ,UACE5B,cAAA,QAAAA,WAAYe,qBACZxH,QAAQqI,qBAAoBzB,qBAA5B,OAAA,SAA4BA,kBAAmByB,kBAC/C;AACAA,0BAAkB5B,WAAWV;MAC9B,OAAM;AACLsC,0BACE,OAAOrI,QAAQqI,oBAAoB,aAC9BrI,QAAQqI,gBAAT,IACArI,QAAQqI;AACd,YAAIrI,QAAQgI,UAAU,OAAOK,oBAAoB,aAAa;AAC5D,cAAI;AACFA,8BAAkBrI,QAAQgI,OAAOK,eAAf;AAClB,iBAAKlI,cAAc;mBACZA,aAAP;AACA,gBAAIiI,MAAuC;AACzC,mBAAKrI,OAAOkC,UAAZ,EAAwBC,MAAM/B,WAA9B;YACD;AACD,iBAAKA,cAAcA;UACpB;QACF;MACF;AAED,UAAI,OAAOkI,oBAAoB,aAAa;AAC1Cf,iBAAS;AACTvB,eAAOoC,YAAY1B,cAAAA,OAAAA,SAAAA,WAAYV,MAAMsC,iBAAiBrI,OAApC;AAClBwH,4BAAoB;MACrB;IACF;AAED,QAAI,KAAKrH,aAAa;AACpB+B,cAAQ,KAAK/B;AACb4F,aAAO,KAAKmC;AACZd,uBAAiBkB,KAAKC,IAAL;AACjBjB,eAAS;IACV;AAED,UAAMkB,aAAanB,gBAAgB;AACnC,UAAMoB,YAAYnB,WAAW;AAC7B,UAAMoB,WAAUpB,WAAW;AAE3B,UAAM1D,SAAiD;MACrD0D;MACAD;MACAoB;MACAV,WAAWT,WAAW;MACtBoB,SAAAA;MACAC,kBAAkBF,aAAaD;MAC/BzC;MACAL;MACAxD;MACAkF;MACAwB,cAAc5B,MAAM6B;MACpBC,eAAe9B,MAAM+B;MACrBC,kBAAkBhC,MAAMgC;MACxBC,WAAWjC,MAAMkC,kBAAkB,KAAKlC,MAAMgC,mBAAmB;MACjEG,qBACEnC,MAAMkC,kBAAkBnC,kBAAkBmC,mBAC1ClC,MAAMgC,mBAAmBjC,kBAAkBiC;MAC7CR;MACAY,cAAcZ,cAAc,CAACC;MAC7BY,gBAAgBX,YAAW1B,MAAMtB,kBAAkB;MACnD4D,UAAUjC,gBAAgB;MAC1BG;MACAD;MACAgC,gBAAgBb,YAAW1B,MAAMtB,kBAAkB;MACnDJ,SAASA,QAAQ/C,OAAOvC,OAAR;MAChBQ,SAAS,KAAKA;MACdF,QAAQ,KAAKA;;AAGf,WAAOsD;EACR;EAEDb,aAAanB,eAAqC;AAChD,UAAM6E,aAAa,KAAK/C;AAIxB,UAAM8F,aAAa,KAAKhG,aAAa,KAAK5C,cAAc,KAAKZ,OAA1C;AACnB,SAAK2G,qBAAqB,KAAK/F,aAAaoG;AAC5C,SAAKH,uBAAuB,KAAK7G;AAGjC,QAAImC,oBAAoBqH,YAAY/C,UAAb,GAA0B;AAC/C;IACD;AAED,SAAK/C,gBAAgB8F;AAGrB,UAAMC,uBAAsC;MAAEC,OAAO;;AAErD,UAAMC,wBAAwB,MAAe;AAC3C,UAAI,CAAClD,YAAY;AACf,eAAO;MACR;AAED,YAAM;QAAEmD;MAAF,IAA0B,KAAK5J;AAErC,UACE4J,wBAAwB,SACvB,CAACA,uBAAuB,CAAC,KAAK3J,aAAaU,MAC5C;AACA,eAAO;MACR;AAED,YAAMkJ,gBAAgB,IAAI3J,IAAI0J,uBAAR,OAAQA,sBAAuB,KAAK3J,YAApC;AAEtB,UAAI,KAAKD,QAAQ8J,kBAAkB;AACjCD,sBAAcvF,IAAI,OAAlB;MACD;AAED,aAAOR,OAAOC,KAAK,KAAKL,aAAjB,EAAgCqG,KAAM9F,SAAQ;AACnD,cAAM+F,WAAW/F;AACjB,cAAMgG,UAAU,KAAKvG,cAAcsG,QAAnB,MAAiCvD,WAAWuD,QAAD;AAC3D,eAAOC,WAAWJ,cAAcK,IAAIF,QAAlB;MACnB,CAJM;;AAOT,SAAIpI,iBAAA,OAAA,SAAAA,cAAelB,eAAc,SAASiJ,sBAAqB,GAAI;AACjEF,2BAAqB/I,YAAY;IAClC;AAED,SAAK2B,OAAO;MAAE,GAAGoH;MAAsB,GAAG7H;KAA1C;EACD;EAEOgB,cAAoB;AAC1B,UAAML,QAAQ,KAAKxC,OAAOqC,cAAZ,EAA4BmB,MAAM,KAAKxD,QAAQ,KAAKC,OAApD;AAEd,QAAIuC,UAAU,KAAK3B,cAAc;AAC/B;IACD;AAED,UAAMkB,YAAY,KAAKlB;AAGvB,SAAKA,eAAe2B;AACpB,SAAK0E,2BAA2B1E,MAAMyE;AACtC,SAAKG,sBAAsB,KAAKzD;AAEhC,QAAI,KAAKxC,aAAL,GAAqB;AACvBY,mBAAS,OAATA,SAAAA,UAAWH,eAAe,IAA1B;AACAY,YAAM1B,YAAY,IAAlB;IACD;EACF;EAEDsJ,cAAcC,QAAqC;AACjD,UAAMxI,gBAA+B,CAAA;AAErC,QAAIwI,OAAO9H,SAAS,WAAW;AAC7BV,oBAAcyI,YAAY,CAACD,OAAOE;IACnC,WAAUF,OAAO9H,SAAS,WAAW,CAACiI,iBAAiBH,OAAOlI,KAAR,GAAgB;AACrEN,oBAAc4I,UAAU;IACzB;AAED,SAAKzH,aAAanB,aAAlB;AAEA,QAAI,KAAKV,aAAL,GAAqB;AACvB,WAAKF,aAAL;IACD;EACF;EAEOqB,OAAOT,eAAoC;AACjD6I,kBAAcC,MAAM,MAAM;AAExB,UAAI9I,cAAcyI,WAAW;AAAA,YAAA,uBAAA,eAAA,uBAAA;AAC3B,SAAA,yBAAA,gBAAA,KAAKrK,SAAQqK,cAAb,OAAA,SAAA,sBAAA,KAAA,eAAyB,KAAK3G,cAAcqC,IAA5C;AACA,SAAK/F,yBAAAA,iBAAAA,KAAAA,SAAQ2K,cAAb,OAAA,SAAA,sBAAA,KAAA,gBAAyB,KAAKjH,cAAcqC,MAAO,IAAnD;MACD,WAAUnE,cAAc4I,SAAS;AAAA,YAAA,uBAAA,gBAAA,wBAAA;AAChC,SAAA,yBAAA,iBAAA,KAAKxK,SAAQwK,YAAb,OAAA,SAAA,sBAAA,KAAA,gBAAuB,KAAK9G,cAAcxB,KAA1C;AACA,SAAKlC,0BAAAA,iBAAAA,KAAAA,SAAQ2K,cAAb,OAAA,SAAA,uBAAA,KAAA,gBAAyBpE,QAAW,KAAK7C,cAAcxB,KAAvD;MACD;AAGD,UAAIN,cAAclB,WAAW;AAC3B,aAAKA,UAAUsD,QAAQ,CAAC;UAAE4G;QAAF,MAAiB;AACvCA,mBAAS,KAAKlH,aAAN;SADV;MAGD;AAGD,UAAI9B,cAAc8H,OAAO;AACvB,aAAK3J,OAAOqC,cAAZ,EAA4BC,OAAO;UACjCE,OAAO,KAAK3B;UACZ0B,MAAM;SAFR;MAID;KAvBH;EAyBD;AAppB0D;AAupB7D,SAASuI,kBACPtI,OACAvC,SACS;AACT,SACEA,QAAQyC,YAAY,SACpB,CAACF,MAAMyE,MAAMtB,iBACb,EAAEnD,MAAMyE,MAAMM,WAAW,WAAWtH,QAAQ8K,iBAAiB;AAEhE;AAED,SAAShK,mBACPyB,OACAvC,SACS;AACT,SACE6K,kBAAkBtI,OAAOvC,OAAR,KAChBuC,MAAMyE,MAAMtB,gBAAgB,KAC3BrE,cAAckB,OAAOvC,SAASA,QAAQ+K,cAAzB;AAElB;AAED,SAAS1J,cACPkB,OACAvC,SACAgL,OAGA;AACA,MAAIhL,QAAQyC,YAAY,OAAO;AAC7B,UAAMwI,QAAQ,OAAOD,UAAU,aAAaA,MAAMzI,KAAD,IAAUyI;AAE3D,WAAOC,UAAU,YAAaA,UAAU,SAAS3F,QAAQ/C,OAAOvC,OAAR;EACzD;AACD,SAAO;AACR;AAED,SAAS8C,sBACPP,OACAT,WACA9B,SACA6B,aACS;AACT,SACE7B,QAAQyC,YAAY,UACnBF,UAAUT,aAAaD,YAAYY,YAAY,WAC/C,CAACzC,QAAQkL,YAAY3I,MAAMyE,MAAMM,WAAW,YAC7ChC,QAAQ/C,OAAOvC,OAAR;AAEV;AAED,SAASsF,QACP/C,OACAvC,SACS;AACT,SAAOuC,MAAM4I,cAAcnL,QAAQgD,SAA5B;AACR;;;AC/uBM,IAAMoI,kBAAN,cAA8BC,aAAsC;EAOzEC,YAAYC,QAAqBC,SAAkC;AACjE,UAAA;AAEA,SAAKD,SAASA;AACd,SAAKC,UAAU,CAAA;AACf,SAAKC,SAAS,CAAA;AACd,SAAKC,YAAY,CAAA;AACjB,SAAKC,eAAe,CAAA;AAEpB,QAAIH,SAAS;AACX,WAAKI,WAAWJ,OAAhB;IACD;EACF;EAESK,cAAoB;AAC5B,QAAI,KAAKC,UAAUC,SAAS,GAAG;AAC7B,WAAKL,UAAUM,QAASC,cAAa;AACnCA,iBAASC,UAAWT,YAAW;AAC7B,eAAKU,SAASF,UAAUR,MAAxB;SADF;OADF;IAKD;EACF;EAESW,gBAAsB;AAC9B,QAAI,CAAC,KAAKN,UAAUC,MAAM;AACxB,WAAKM,QAAL;IACD;EACF;EAEDA,UAAgB;AACd,SAAKP,YAAY,oBAAIQ,IAAJ;AACjB,SAAKZ,UAAUM,QAASC,cAAa;AACnCA,eAASI,QAAT;KADF;EAGD;EAEDT,WACEJ,SACAe,eACM;AACN,SAAKf,UAAUA;AAEfgB,kBAAcC,MAAM,MAAM;AACxB,YAAMC,gBAAgB,KAAKhB;AAE3B,YAAMiB,qBAAqB,KAAKC,sBAAsB,KAAKpB,OAAhC;AAG3BmB,yBAAmBX,QAASa,WAC1BA,MAAMZ,SAASa,WAAWD,MAAME,uBAAuBR,aAAvD,CADF;AAIA,YAAMS,eAAeL,mBAAmBM,IAAKJ,WAAUA,MAAMZ,QAAxC;AACrB,YAAMiB,kBAAkBC,OAAOC,YAC7BJ,aAAaC,IAAKhB,cAAa,CAACA,SAASoB,QAAQC,WAAWrB,QAA7B,CAA/B,CADsB;AAGxB,YAAMsB,YAAYP,aAAaC,IAAKhB,cAClCA,SAASuB,iBAAT,CADgB;AAIlB,YAAMC,iBAAiBT,aAAaU,KAClC,CAACzB,UAAU0B,UAAU1B,aAAaS,cAAciB,KAAD,CAD1B;AAGvB,UAAIjB,cAAckB,WAAWZ,aAAaY,UAAU,CAACH,gBAAgB;AACnE;MACD;AAED,WAAK/B,YAAYsB;AACjB,WAAKrB,eAAeuB;AACpB,WAAKzB,SAAS8B;AAEd,UAAI,CAAC,KAAKM,aAAL,GAAqB;AACxB;MACD;AAEDC,iBAAWpB,eAAeM,YAAhB,EAA8BhB,QAASC,cAAa;AAC5DA,iBAASI,QAAT;OADF;AAIAyB,iBAAWd,cAAcN,aAAf,EAA8BV,QAASC,cAAa;AAC5DA,iBAASC,UAAWT,YAAW;AAC7B,eAAKU,SAASF,UAAUR,MAAxB;SADF;OADF;AAMA,WAAKsC,OAAL;KA3CF;EA6CD;EAEDP,mBAA0C;AACxC,WAAO,KAAK/B;EACb;EAEDuC,aAAa;AACX,WAAO,KAAKtC,UAAUuB,IAAKhB,cAAaA,SAASgC,gBAAT,CAAjC;EACR;EAEDC,eAAe;AACb,WAAO,KAAKxC;EACb;EAEDyC,oBAAoB3C,SAAwD;AAC1E,WAAO,KAAKoB,sBAAsBpB,OAA3B,EAAoCyB,IAAKJ,WAC9CA,MAAMZ,SAASkC,oBAAoBtB,MAAME,qBAAzC,CADK;EAGR;EAEOH,sBACNpB,SACsB;AACtB,UAAMkB,gBAAgB,KAAKhB;AAC3B,UAAM0C,mBAAmB,IAAIC,IAC3B3B,cAAcO,IAAKhB,cAAa,CAACA,SAASoB,QAAQC,WAAWrB,QAA7B,CAAhC,CADuB;AAIzB,UAAMc,wBAAwBvB,QAAQyB,IAAKI,aACzC,KAAK9B,OAAO+C,oBAAoBjB,OAAhC,CAD4B;AAI9B,UAAMkB,oBACJxB,sBAAsByB,QAASC,sBAAqB;AAClD,YAAM5B,QAAQuB,iBAAiBM,IAAID,iBAAiBnB,SAAtC;AACd,UAAIT,SAAS,MAAM;AACjB,eAAO,CAAC;UAAEE,uBAAuB0B;UAAkBxC,UAAUY;QAArD,CAAD;MACR;AACD,aAAO,CAAA;IACR,CAND;AAQF,UAAM8B,qBAAqB,IAAIrC,IAC7BiC,kBAAkBtB,IAAKJ,WAAUA,MAAME,sBAAsBO,SAA7D,CADyB;AAG3B,UAAMsB,mBAAmB7B,sBAAsB8B,OAC5CJ,sBAAqB,CAACE,mBAAmBG,IAAIL,iBAAiBnB,SAAxC,CADA;AAIzB,UAAMyB,uBAAuB,IAAIzC,IAC/BiC,kBAAkBtB,IAAKJ,WAAUA,MAAMZ,QAAvC,CAD2B;AAG7B,UAAM+C,qBAAqBtC,cAAcmC,OACtCI,kBAAiB,CAACF,qBAAqBD,IAAIG,YAAzB,CADM;AAI3B,UAAMC,cAAe7B,aAAiD;AACpE,YAAMoB,mBAAmB,KAAKlD,OAAO+C,oBAAoBjB,OAAhC;AACzB,YAAM8B,kBAAkB,KAAKxD,aAAa8C,iBAAiBnB,SAAnC;AACxB,aAAO6B,mBAAAA,OAAAA,kBAAmB,IAAIC,cAAc,KAAK7D,QAAQkD,gBAA/B;;AAG5B,UAAMY,uBAA6CT,iBAAiB3B,IAClE,CAACI,SAASM,UAAU;AAClB,UAAIN,QAAQiC,kBAAkB;AAE5B,cAAMC,yBAAyBP,mBAAmBrB,KAAD;AACjD,YAAI4B,2BAA2BC,QAAW;AACxC,iBAAO;YACLzC,uBAAuBM;YACvBpB,UAAUsD;;QAEb;MACF;AACD,aAAO;QACLxC,uBAAuBM;QACvBpB,UAAUiD,YAAY7B,OAAD;;IAExB,CAhBgD;AAmBnD,UAAMoC,8BAA8B,CAClCC,GACAC,MAEA5C,sBAAsB6C,QAAQF,EAAE3C,qBAAhC,IACAA,sBAAsB6C,QAAQD,EAAE5C,qBAAhC;AAEF,WAAOwB,kBACJsB,OAAOR,oBADH,EAEJS,KAAKL,2BAFD;EAGR;EAEOtD,SAASF,UAAyBR,QAAmC;AAC3E,UAAMkC,QAAQ,KAAKjC,UAAUkE,QAAQ3D,QAAvB;AACd,QAAI0B,UAAU,IAAI;AAChB,WAAKlC,SAASsE,UAAU,KAAKtE,QAAQkC,OAAOlC,MAArB;AACvB,WAAKsC,OAAL;IACD;EACF;EAEOA,SAAe;AACrBvB,kBAAcC,MAAM,MAAM;AACxB,WAAKX,UAAUE,QAAQ,CAAC;QAAEgE;MAAF,MAAiB;AACvCA,iBAAS,KAAKvE,MAAN;OADV;KADF;EAKD;AAzMwE;;;ACSpE,IAAMwE,wBAAN,cAMGC,cAMR;;;;;EAeAC,YACEC,QACAC,SAOA;AACA,UAAMD,QAAQC,OAAd;EACD;EAESC,cAAoB;AAC5B,UAAMA,YAAN;AACA,SAAKC,gBAAgB,KAAKA,cAAcC,KAAK,IAAxB;AACrB,SAAKC,oBAAoB,KAAKA,kBAAkBD,KAAK,IAA5B;EAC1B;EAEDE,WACEL,SAOAM,eACM;AACN,UAAMD,WACJ;MACE,GAAGL;MACHO,UAAUC,sBAAqB;IAFjC,GAIAF,aALF;EAOD;EAEDG,oBACET,SAO4C;AAC5CA,YAAQO,WAAWC,sBAAqB;AACxC,WAAO,MAAMC,oBAAoBT,OAA1B;EAIR;EAEDE,cAAc;IAAEQ;IAAW,GAAGV;EAAhB,IAAkD,CAAA,GAE9D;AACA,WAAO,KAAKW,MAAM;MAChB,GAAGX;MACHY,MAAM;QACJC,WAAW;UAAEC,WAAW;UAAWJ;QAAxB;MADP;IAFU,CAAX;EAMR;EAEDN,kBAAkB;IAChBM;IACA,GAAGV;EAFa,IAGY,CAAA,GAE5B;AACA,WAAO,KAAKW,MAAM;MAChB,GAAGX;MACHY,MAAM;QACJC,WAAW;UAAEC,WAAW;UAAYJ;QAAzB;MADP;IAFU,CAAX;EAMR;EAESK,aACRC,OACAhB,SAO4C;AAAA,QAAA,kBAAA,uBAAA,mBAAA,uBAAA,aAAA;AAC5C,UAAM;MAAEiB;IAAF,IAAYD;AAClB,UAAME,SAAS,MAAMH,aAAaC,OAAOhB,OAA1B;AAEf,UAAM;MAAEmB;MAAYC;IAAd,IAA+BF;AAErC,UAAMG,qBACJF,gBAAcF,mBAAAA,MAAMK,cAAWT,OAAAA,UAAAA,wBAAAA,iBAAAA,cAAWC,OAAAA,SAAAA,sBAAAA,eAAc;AAE1D,UAAMS,yBACJJ,gBAAcF,oBAAAA,MAAMK,cAAWT,OAAAA,UAAAA,wBAAAA,kBAAAA,cAAWC,OAAAA,SAAAA,sBAAAA,eAAc;AAE1D,WAAO;MACL,GAAGI;MACHhB,eAAe,KAAKA;MACpBE,mBAAmB,KAAKA;MACxBoB,aAAaA,YAAYxB,UAAD,cAAUiB,MAAMQ,SAAhB,OAAA,SAAU,YAAYC,KAAtB;MACxBC,iBAAiBA,gBAAgB3B,UAAD,eAAUiB,MAAMQ,SAAhB,OAAA,SAAU,aAAYC,KAAtB;MAChCL;MACAE;MACAH,cACEA,gBAAgB,CAACC,sBAAsB,CAACE;;EAE7C;AA9HD;;;ACRK,IAAMK,mBAAN,cAKGC,aAER;EAaAC,YACEC,QACAC,SACA;AACA,UAAA;AAEA,SAAKD,SAASA;AACd,SAAKE,WAAWD,OAAhB;AACA,SAAKE,YAAL;AACA,SAAKC,aAAL;EACD;EAESD,cAAoB;AAC5B,SAAKE,SAAS,KAAKA,OAAOC,KAAK,IAAjB;AACd,SAAKC,QAAQ,KAAKA,MAAMD,KAAK,IAAhB;EACd;EAEDJ,WACED,SACA;AAAA,QAAA;AACA,UAAMO,cAAc,KAAKP;AACzB,SAAKA,UAAU,KAAKD,OAAOS,uBAAuBR,OAAnC;AACf,QAAI,CAACS,oBAAoBF,aAAa,KAAKP,OAAnB,GAA6B;AACnD,WAAKD,OAAOW,iBAAZ,EAA+BC,OAAO;QACpCC,MAAM;QACNC,UAAU,KAAKC;QACfC,UAAU;OAHZ;IAKD;AACD,KAAA,wBAAA,KAAKD,oBAAL,OAAA,SAAA,sBAAsBb,WAAW,KAAKD,OAAtC;EACD;EAESgB,gBAAsB;AAC9B,QAAI,CAAC,KAAKC,aAAL,GAAqB;AAAA,UAAA;AACxB,OAAA,yBAAA,KAAKH,oBAAL,OAAA,SAAA,uBAAsBI,eAAe,IAArC;IACD;EACF;EAEDC,iBAAiBC,QAA2D;AAC1E,SAAKjB,aAAL;AAGA,UAAMkB,gBAA+B;MACnCC,WAAW;;AAGb,QAAIF,OAAOR,SAAS,WAAW;AAC7BS,oBAAcE,YAAY;IAC3B,WAAUH,OAAOR,SAAS,SAAS;AAClCS,oBAAcG,UAAU;IACzB;AAED,SAAKb,OAAOU,aAAZ;EACD;EAEDI,mBAKE;AACA,WAAO,KAAKC;EACb;EAEDpB,QAAc;AACZ,SAAKQ,kBAAkBa;AACvB,SAAKxB,aAAL;AACA,SAAKQ,OAAO;MAAEW,WAAW;KAAzB;EACD;EAEDlB,OACEwB,WACA5B,SACgB;AAChB,SAAK6B,gBAAgB7B;AAErB,QAAI,KAAKc,iBAAiB;AACxB,WAAKA,gBAAgBI,eAAe,IAApC;IACD;AAED,SAAKJ,kBAAkB,KAAKf,OAAOW,iBAAZ,EAA+BoB,MAAM,KAAK/B,QAAQ;MACvE,GAAG,KAAKC;MACR4B,WACE,OAAOA,cAAc,cAAcA,YAAY,KAAK5B,QAAQ4B;IAHS,CAAlD;AAMvB,SAAKd,gBAAgBiB,YAAY,IAAjC;AAEA,WAAO,KAAKjB,gBAAgBkB,QAArB;EACR;EAEO7B,eAAqB;AAC3B,UAAM8B,QAAQ,KAAKnB,kBACf,KAAKA,gBAAgBmB,QACrBC,iBAAe;AAEnB,UAAMC,SAKF;MACF,GAAGF;MACHG,WAAWH,MAAMI,WAAW;MAC5BC,WAAWL,MAAMI,WAAW;MAC5BE,SAASN,MAAMI,WAAW;MAC1BG,QAAQP,MAAMI,WAAW;MACzBjC,QAAQ,KAAKA;MACbE,OAAO,KAAKA;;AAGd,SAAKoB,gBAAgBS;EAMtB;EAEOxB,OAAOX,SAAwB;AACrCyC,kBAAcC,MAAM,MAAM;AAExB,UAAI,KAAKb,iBAAiB,KAAKZ,aAAL,GAAqB;AAC7C,YAAIjB,QAAQuB,WAAW;AAAA,cAAA,uBAAA,qBAAA,wBAAA;AACrB,WAAA,yBAAA,sBAAA,KAAKM,eAAcN,cACjB,OAAA,SAAA,sBAAA,KAAA,qBAAA,KAAKG,cAAciB,MACnB,KAAKjB,cAAcE,WACnB,KAAKF,cAAckB,OAHrB;AAKA,WAAA,0BAAA,uBAAA,KAAKf,eAAcgB,cAAnB,OAAA,SAAA,uBAAA,KAAA,sBACE,KAAKnB,cAAciB,MACnB,MACA,KAAKjB,cAAcE,WACnB,KAAKF,cAAckB,OAJrB;QAMD,WAAU5C,QAAQwB,SAAS;AAAA,cAAA,wBAAA,sBAAA,wBAAA;AAC1B,WAAA,0BAAA,uBAAA,KAAKK,eAAcL,YACjB,OAAA,SAAA,uBAAA,KAAA,sBAAA,KAAKE,cAAcoB,OACnB,KAAKpB,cAAcE,WACnB,KAAKF,cAAckB,OAHrB;AAKA,WAAA,0BAAA,uBAAA,KAAKf,eAAcgB,cAAnB,OAAA,SAAA,uBAAA,KAAA,sBACElB,QACA,KAAKD,cAAcoB,OACnB,KAAKpB,cAAcE,WACnB,KAAKF,cAAckB,OAJrB;QAMD;MACF;AAGD,UAAI5C,QAAQsB,WAAW;AACrB,aAAKA,UAAUyB,QAAQ,CAAC;UAAEC;QAAF,MAAiB;AACvCA,mBAAS,KAAKtB,aAAN;SADV;MAGD;KAnCH;EAqCD;AA1KD;;;ACcF,SAASuB,kBAAkBC,UAAwC;AACjE,SAAO;IACLC,aAAaD,SAASE,QAAQD;IAC9BE,OAAOH,SAASG;;AAEnB;AAMD,SAASC,eAAeC,OAA+B;AACrD,SAAO;IACLF,OAAOE,MAAMF;IACbG,UAAUD,MAAMC;IAChBC,WAAWF,MAAME;;AAEpB;AAEM,SAASC,+BAA+BR,UAAoB;AACjE,SAAOA,SAASG,MAAMM;AACvB;AAEM,SAASC,4BAA4BL,OAAc;AACxD,SAAOA,MAAMF,MAAMQ,WAAW;AAC/B;AAEM,SAASC,UACdC,QACAX,UAA4B,CAAA,GACX;AACjB,QAAMY,YAAkC,CAAA;AACxC,QAAMC,UAA6B,CAAA;AAEnC,MAAIb,QAAQc,uBAAuB,OAAO;AACxC,UAAMC,0BACJf,QAAQe,2BAA2BT;AAErCK,WACGK,iBADH,EAEGC,OAFH,EAGGC,QAASpB,cAAa;AACrB,UAAIiB,wBAAwBjB,QAAD,GAAY;AACrCc,kBAAUO,KAAKtB,kBAAkBC,QAAD,CAAhC;MACD;KANL;EAQD;AAED,MAAIE,QAAQoB,qBAAqB,OAAO;AACtC,UAAMC,uBACJrB,QAAQqB,wBAAwBb;AAElCG,WACGW,cADH,EAEGL,OAFH,EAGGC,QAASf,WAAU;AAClB,UAAIkB,qBAAqBlB,KAAD,GAAS;AAC/BU,gBAAQM,KAAKjB,eAAeC,KAAD,CAA3B;MACD;KANL;EAQD;AAED,SAAO;IAAES;IAAWC;;AACrB;AAEM,SAASU,QACdZ,QACAa,iBACAxB,SACM;AACN,MAAI,OAAOwB,oBAAoB,YAAYA,oBAAoB,MAAM;AACnE;EACD;AAED,QAAMC,gBAAgBd,OAAOK,iBAAP;AACtB,QAAMU,aAAaf,OAAOW,cAAP;AAGnB,QAAMV,YAAaY,gBAAoCZ,aAAa,CAAA;AAEpE,QAAMC,UAAWW,gBAAoCX,WAAW,CAAA;AAEhED,YAAUM,QAASS,wBAAuB;AAAA,QAAA;AACxCF,kBAAcG,MACZjB,QACA;MACE,GAAGX,WAAH,OAAA,UAAA,wBAAGA,QAAS6B,mBAAT,OAAA,SAAA,sBAAyBjB;MAC5Bb,aAAa4B,mBAAmB5B;OAElC4B,mBAAmB1B,KANrB;GADF;AAWAY,UAAQK,QAASY,qBAAoB;AAAA,QAAA;AACnC,UAAM3B,QAAQuB,WAAWK,IAAID,gBAAgBzB,SAA/B;AAId,UAAM2B,uBAAuB;MAC3B,GAAGF,gBAAgB7B;MACnBgC,aAAa;IAFc;AAM7B,QAAI9B,OAAO;AACT,UAAIA,MAAMF,MAAMiC,gBAAgBF,qBAAqBE,eAAe;AAClE/B,cAAMgC,SAASH,oBAAf;MACD;AACD;IACD;AAGDN,eAAWE,MACTjB,QACA;MACE,GAAGX,WAAH,OAAA,UAAA,yBAAGA,QAAS6B,mBAAT,OAAA,SAAA,uBAAyBhB;MAC5BT,UAAU0B,gBAAgB1B;MAC1BC,WAAWyB,gBAAgBzB;IAH7B,GAKA2B,oBAPF;GAnBF;AA6BD;;;;ACxKYI,IAAAA,2BAAAA;;;ACCbC,cAAcC,uBAAuBC,wBAArC;;;;;;;ACCO,IAAA,uBAAA,YAAAC;;;;ACQA,IAAA,iBAAA,oBAAA,MAAA;AAGP,IAAA,4BAAA,oBAAA,KAAA;AASA,SAAA,sBAAA,SAAA,gBAAA;AAIE,MAAA,SAAA;AACE,WAAA;EACD;AACD,MAAA,kBAAA,OAAA,WAAA,aAAA;AACE,QAAA,CAAA,OAAA,yBAAA;;IAEC;;EAGF;AAED,SAAA;AACD;AAEM,IAAA,iBAAA,CAAA;EAA0BC;AAAF,IAAA,CAAA,MAAA;AAC7B,QAAA,cAAA,iBAAA,sBAAA,SAAA,iBAAA,yBAAA,CAAA,CAAA;;AAKE,UAAA,IAAA,MAAA,wDAAA;EACD;AAED,SAAA;AACD;AAkBM,IAAA,sBAAA,CAAA;;;;EAILC,iBAAAA;AAJkC,MAAA;;AAOhCC,WAAAA,MAAAA;AACA,WAAA,MAAA;AACEA,aAAAA,QAAAA;;;;;EAUH;AAED,QAAA,UAAA,sBAAA,SAAA,cAAA;;;;IAIsB,OAAA;;AAGvB;;;;AC/FD,IAAA,qBAAA,qBAAA,KAAA;AAEO,IAAA,iBAAA,MAAA,kBAAA,kBAAA;AACMC,IAAAA,sBAAAA,mBAAAA;;;;ACKb,SAAA,cAAA;;;IAGIC,YAAAA,MAAAA;AACEC,gBAAAA;;IAEFC,OAAAA,MAAAA;AACED,gBAAAA;;IAEFA,SAAAA,MAAAA;AACE,aAAA;IACD;;AAEJ;AAED,IAAA,iCAAA,qBAAA,YAAA,CAAA;AAIO,IAAA,6BAAA,MAAA,kBAAA,8BAAA;AAWA,IAAA,0BAAA,CAAA;EACLE;AADsC,MAAA;;;IAKK;;AAM5C;;;;;;ACpDM,SAASC,iBACdC,mBACAC,QACS;AAET,MAAI,OAAOD,sBAAsB,YAAY;AAC3C,WAAOA,kBAAkB,GAAGC,MAAJ;EACzB;AAED,SAAO,CAAC,CAACD;AACV;;;;ACkBC,MAAA,QAAA,YAAA,QAAA,kBAAA;AAEE,QAAA,CAAA,mBAAA,QAAA,GAAA;;IAEC;EACF;AACF;AAEYE,IAAAA,6BAAAA,wBAAAA;;AAITC,uBAAAA,WAAAA;;AAEH;AAEM,IAAA,cAAA,CAAA;;;;EAULC;AAJA,MAAA;;AAsBD;;;AClEYC,IAAAA,kBACXC,sBACG;AACH,MAAIA,iBAAiBC,UAAU;AAG7B,QAAI,OAAOD,iBAAiBE,cAAc,UAAU;AAClDF,uBAAiBE,YAAY;IAC9B;EACF;AACF;IAEYC,YAAY,CACvBC,QACAC,gBACGD,OAAOE,aAAaF,OAAOG,cAAc,CAACF;AAExC,IAAMG,gBAAgB,CAC3BR,kBAGAI,QACAC,iBACGL,oBAAA,OAAA,SAAAA,iBAAkBC,aAAYE,UAAUC,QAAQC,WAAT;IAE/BI,kBAAkB,CAO7BT,kBAOAU,UACAC,uBAEAD,SACGD,gBAAgBT,gBADnB,EAEGY,KAAK,CAAC;EAAEC;AAAF,MAAa;AAClBb,mBAAiBc,aAAjB,OAAA,SAAAd,iBAAiBc,UAAYD,IAA7B;AACAb,mBAAiBe,aAAjBf,OAAAA,SAAAA,iBAAiBe,UAAYF,MAAM,IAAnC;AACD,CALH,EAMGG,MAAOC,WAAU;AAChBN,qBAAmBO,WAAnB;AACAlB,mBAAiBmB,WAAjB,OAAA,SAAAnB,iBAAiBmB,QAAUF,KAA3B;AACAjB,mBAAiBe,aAAjBf,OAAAA,SAAAA,iBAAiBe,UAAYK,QAAWH,KAAxC;AACD,CAVH;;;ACsGK,SAAA,WAAA;;EAELI;AAF0C,GAAA;;IAOLA;EAAF,CAAA;;;AAInC,QAAA,mBAAA,eAAA,MAAA,QAAA,IAAA,aAAA;;AAMMC,qBAAAA,qBAAAA,cAAAA,gBAAAA;AAIA,WAAA;;AAKNC,mBAAAA,QAAAA,WAAAA;;AAEEC,oCAAAA,OAAAA,kBAAAA;;;AAKF,QAAA,CAAA,QAAA,IAAA,gBAAA,MAAA,IAAA,gBAAA,aAAA,gBAAA,CAAA;AAIA,QAAA,mBAAA,SAAA,oBAAA,gBAAA;AAEAC,uBAAAA,mBAAAA,mBAAAA,cAAAA,MAAAA,SAAAA,SAAAA,UAAAA,cAAAA,WAAAA,aAAAA,CAAAA,GAAAA,CAAAA,UAAAA,WAAAA,CAAAA,GAAAA,MAAAA,SAAAA,iBAAAA,GAAAA,MAAAA,SAAAA,iBAAAA,CAAAA;;AAeEC,aAAAA,WAAAA,kBAAAA;MAAwCC,WAAAA;;EACzC,GAAA,CAAA,kBAAA,QAAA,CAAA;;AAMD,QAAA,mBAAA,0BAAA,iBAAA,QAAA,CAAA,QAAA,UAAA;AAEM,UAAA,UAAA,iBAAA,KAAA;AACA,UAAA,gBAAA,SAAA,aAAA,EAAA,KAAA;;;AAII,eAAA,gBAAA,SAAA,eAAA,kBAAA;;AAEA,aAAA,gBAAA,SAAA,eAAA,kBAAA;MACD;IACF;AACD,WAAA,CAAA;;AAIN,MAAA,iBAAA,SAAA,GAAA;AACE,UAAA,QAAA,IAAA,gBAAA;EACD;AACD,QAAA,kBAAA,SAAA,WAAA;;AAEE,QAAA,uBAAA;AAAA,WAAA,YAAA;;;;;IACc,CAAA;EADd,CAAA;AASF,MAAA,qCAAA,QAAA,kCAAA,OAAA;;EAEC;AAED,SAAA;AACD;;;;ACrOM,SAAA,aAAA,SAAA,UAAA;;;EAgB8B,CAAA;;;;;;;EAelC;;;EAMA;;;EAMA;;AAGDC,kCAAAA,kBAAAA,kBAAAA;;AAIA,QAAA,CAAA,QAAA,IAAA,gBAAA,MAAA,IAAA,SAAA,aAAA,gBAAA,CAAA;AAQA,QAAA,SAAA,SAAA,oBAAA,gBAAA;AAEAC,uBAAAA,mBAAAA,mBAAAA;AAGM,UAAA,cAAA,cAAA,MAAA,SAAA,SAAA,UAAA,cAAA,WAAA,aAAA,CAAA;AAMAC,aAAAA,aAAAA;AAEA,WAAA;EACD,GAAA,CAAA,UAAA,WAAA,CAAA,GAAA,MAAA,SAAA,iBAAA,GAAA,MAAA,SAAA,iBAAA,CAAA;;AAUHA,aAAAA,WAAAA,kBAAAA;MAAwCC,WAAAA;;EACzC,GAAA,CAAA,kBAAA,QAAA,CAAA;;AAIC,UAAA,gBAAA,kBAAA,UAAA,kBAAA;EACD;AAGD,MAAA,YAAA;;;;;EACc,CAAA,GAAA;;EAQb;AAGD,SAAA,CAAA,iBAAA,sBAAA,SAAA,YAAA,MAAA,IAAA;AAGD;;;ACGM,SAAA,SAAA,MAAA,MAAA,MAAA;;AAaL,SAAA,aAAA,eAAA,aAAA;AACD;;;;ACpIM,SAAA,WAAA,OAAA,UAAA,CAAA,GAAA;;;EAI8B,CAAA;AAEnC,QAAA,aAAA,cAAA,OAAA;AACAC,aAAAA,UAAAA;;AAOE,QAAA,OAAA;;IAEC;EACF,GAAA,CAAA,aAAA,KAAA,CAAA;AACF;AAQM,IAAA,UAAA,CAAA;;;EAAsCC;AAArB,MAAA;AACtBC,aAAAA,OAAAA,OAAAA;AACA,SAAA;AACD;;;;ACpBM,SAAA,cAAA,MAAA,MAAA,MAAA;AAKL,QAAA,CAAA,SAAA,UAAA,CAAA,CAAA,IAAA,gBAAA,MAAA,MAAA,IAAA;;;EACmC,CAAA;AACnC,QAAA,aAAA,YAAA,cAAA;AAEA,SAAA,qBAAA,mBAAA,mBAAA,WAAA,UAAA,cAAA,WAAA,aAAA,CAAA,GAAA,CAAA,UAAA,CAAA,GAAA,MAAA,YAAA,WAAA,OAAA,GAAA,MAAA,YAAA,WAAA,OAAA,CAAA;AASD;;;;ACfM,SAAA,cAAA,MAAA,MAAA,MAAA;AAKL,QAAA,CAAA,SAAA,UAAA,CAAA,CAAA,IAAA,wBAAA,MAAA,MAAA,IAAA;;;EAEmC,CAAA;AACnC,QAAA,gBAAA,YAAA,iBAAA;AAEA,SAAA,qBAAA,mBAAA,mBAAA,cAAA,UAAA,cAAA,WAAA,aAAA,CAAA,GAAA,CAAA,aAAA,CAAA,GAAA,MAAA,YAAA,WAAA,OAAA,GAAA,MAAA,YAAA,WAAA,OAAA,CAAA;AASD;;;;AC0BM,SAAA,YAAA,MAAA,MAAA,MAAA;;;;EAgB8B,CAAA;AAEnC,QAAA,CAAA,QAAA,IAAA,iBAAA,MAAA,IAAA,iBAAA,aAAA,OAAA,CAAA;;;EAUC,GAAA,CAAA,UAAA,OAAA,CAAA;AAED,QAAA,SAAA,qBAAA,oBAAA,mBAAA,SAAA,UAAA,cAAA,WAAA,aAAA,CAAA,GAAA,CAAA,QAAA,CAAA,GAAA,MAAA,SAAA,iBAAA,GAAA,MAAA,SAAA,iBAAA,CAAA;;;EAeG,GAAA,CAAA,QAAA,CAAA;AAIH,MAAA,OAAA,SAAA,iBAAA,SAAA,QAAA,kBAAA,CAAA,OAAA,KAAA,CAAA,GAAA;;EAKC;;;;;;AAGF;AAGD,SAAAC,QAAA;AAAA;;;AC9DO,SAAA,iBAAA,MAAA,MAAA,MAAA;;AAiCL,SAAA,aAAA,SAAA,qBAAA;AAID;", "names": ["React", "useState", "useEffect", "useSyncExternalStore", "error", "useSyncExternalStore$1", "Subscribable", "constructor", "listeners", "Set", "subscribe", "bind", "listener", "identity", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size", "isServer", "window", "noop", "undefined", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "Infinity", "difference", "array1", "array2", "filter", "x", "includes", "replaceAt", "array", "index", "copy", "slice", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "queryFn", "parseMutationArgs", "<PERSON><PERSON><PERSON>", "mutationFn", "parseFilter<PERSON><PERSON>s", "parseMutationFilterArgs", "matchQuery", "filters", "query", "type", "exact", "fetchStatus", "predicate", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "isActive", "isStale", "state", "matchMutation", "mutation", "fetching", "hashQuery<PERSON>ey", "status", "hashFn", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "Object", "keys", "sort", "reduce", "result", "key", "a", "b", "partialDeepEqual", "some", "replaceEqualDeep", "is<PERSON><PERSON>A<PERSON>y", "aSize", "length", "bItems", "bSize", "equalItems", "i", "shallowEqualObjects", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "constructor", "prot", "prototype", "hasOwnProperty", "toString", "call", "isError", "Error", "sleep", "timeout", "Promise", "resolve", "setTimeout", "scheduleMicrotask", "callback", "then", "getAbortController", "AbortController", "replaceData", "prevData", "data", "isDataEqual", "structuralSharing", "FocusManager", "Subscribable", "constructor", "setup", "onFocus", "isServer", "window", "addEventListener", "listener", "removeEventListener", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "hasListeners", "undefined", "focused", "setFocused", "listeners", "for<PERSON>ach", "isFocused", "document", "includes", "visibilityState", "focusManager", "onlineEvents", "OnlineManager", "Subscribable", "constructor", "setup", "onOnline", "isServer", "window", "addEventListener", "listener", "for<PERSON>ach", "event", "removeEventListener", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "hasListeners", "undefined", "online", "setOnline", "listeners", "isOnline", "navigator", "onLine", "onlineManager", "defaultRetryDelay", "failureCount", "Math", "min", "canFetch", "networkMode", "onlineManager", "isOnline", "CancelledError", "constructor", "options", "revert", "silent", "isCancelledError", "value", "createRetryer", "config", "isRetryCancelled", "isResolved", "continueFn", "promiseResolve", "promiseReject", "promise", "Promise", "outerResolve", "outerReject", "cancel", "cancelOptions", "reject", "abort", "cancelRetry", "continueRetry", "shouldP<PERSON>e", "focusManager", "isFocused", "resolve", "onSuccess", "onError", "pause", "continueResolve", "canContinue", "onPause", "then", "undefined", "onContinue", "run", "promiseOrValue", "fn", "error", "catch", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "sleep", "continue", "didContinue", "defaultLogger", "console", "createNotifyManager", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "batch", "result", "flush", "schedule", "push", "scheduleMicrotask", "batchCalls", "args", "originalQueue", "length", "for<PERSON>ach", "setNotifyFunction", "fn", "setBatchNotifyFunction", "notify<PERSON><PERSON>ger", "Removable", "destroy", "clearGcTimeout", "scheduleGc", "isValidTimeout", "cacheTime", "gcTimeout", "setTimeout", "optionalRemove", "updateCacheTime", "newCacheTime", "Math", "max", "isServer", "Infinity", "clearTimeout", "undefined", "Query", "Removable", "constructor", "config", "abortSignalConsumed", "defaultOptions", "setOptions", "options", "observers", "cache", "logger", "defaultLogger", "query<PERSON><PERSON>", "queryHash", "initialState", "state", "getDefaultState", "scheduleGc", "meta", "updateCacheTime", "cacheTime", "optionalRemove", "length", "fetchStatus", "remove", "setData", "newData", "data", "replaceData", "dispatch", "type", "dataUpdatedAt", "updatedAt", "manual", "setState", "setStateOptions", "cancel", "promise", "retryer", "then", "noop", "catch", "Promise", "resolve", "destroy", "silent", "reset", "isActive", "some", "observer", "enabled", "isDisabled", "getObserversCount", "isStale", "isInvalidated", "getCurrentResult", "isStaleByTime", "staleTime", "timeUntilStale", "onFocus", "find", "x", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "continue", "onOnline", "shouldFetchOnReconnect", "addObserver", "includes", "push", "clearGcTimeout", "notify", "query", "removeObserver", "filter", "revert", "cancelRetry", "invalidate", "fetch", "fetchOptions", "continueRetry", "queryFn", "Array", "isArray", "process", "error", "abortController", "getAbortController", "queryFnContext", "pageParam", "undefined", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "fetchFn", "reject", "context", "behavior", "onFetch", "revertState", "fetchMeta", "onError", "isCancelledError", "onSettled", "isFetchingOptimistic", "createRetryer", "fn", "abort", "bind", "onSuccess", "Error", "onFail", "failureCount", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "action", "reducer", "fetchFailureCount", "fetchFailureReason", "canFetch", "status", "dataUpdateCount", "Date", "now", "errorUpdateCount", "errorUpdatedAt", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "onQueryUpdate", "initialData", "hasData", "initialDataUpdatedAt", "Query<PERSON>ache", "Subscribable", "constructor", "config", "queries", "queriesMap", "build", "client", "options", "state", "query<PERSON><PERSON>", "queryHash", "hashQueryKeyByOptions", "query", "get", "Query", "cache", "logger", "<PERSON><PERSON><PERSON><PERSON>", "defaultQueryOptions", "defaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "push", "notify", "type", "remove", "queryInMap", "destroy", "filter", "x", "clear", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "getAll", "find", "arg1", "arg2", "filters", "parseFilter<PERSON><PERSON>s", "exact", "matchQuery", "findAll", "Object", "keys", "length", "event", "listeners", "listener", "onFocus", "onOnline", "Mutation", "Removable", "constructor", "config", "defaultOptions", "mutationId", "mutationCache", "logger", "defaultLogger", "observers", "state", "getDefaultState", "setOptions", "options", "scheduleGc", "updateCacheTime", "cacheTime", "meta", "setState", "dispatch", "type", "addObserver", "observer", "includes", "push", "clearGcTimeout", "notify", "mutation", "removeObserver", "filter", "x", "optionalRemove", "length", "status", "remove", "continue", "retryer", "execute", "executeMutation", "createRetryer", "fn", "mutationFn", "Promise", "reject", "variables", "onFail", "failureCount", "error", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "promise", "restored", "onMutate", "context", "data", "onSuccess", "onSettled", "onError", "process", "undefined", "action", "reducer", "failureReason", "isPaused", "canFetch", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "onMutationUpdate", "MutationCache", "Subscribable", "constructor", "config", "mutations", "mutationId", "build", "client", "options", "state", "mutation", "Mutation", "mutationCache", "logger", "<PERSON><PERSON><PERSON><PERSON>", "defaultMutationOptions", "defaultOptions", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "undefined", "add", "push", "notify", "type", "remove", "filter", "x", "clear", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "getAll", "find", "filters", "exact", "matchMutation", "findAll", "event", "listeners", "listener", "resumePausedMutations", "resuming", "Promise", "resolve", "then", "pausedMutations", "isPaused", "reduce", "promise", "continue", "catch", "noop", "infiniteQueryBehavior", "onFetch", "context", "fetchFn", "refetchPage", "fetchOptions", "meta", "fetchMore", "pageParam", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "state", "data", "pages", "oldPageParams", "pageParams", "newPageParams", "cancelled", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "aborted", "addEventListener", "queryFn", "options", "Promise", "reject", "buildNewPages", "param", "page", "previous", "fetchPage", "manual", "length", "resolve", "queryFnContext", "query<PERSON><PERSON>", "queryFnResult", "promise", "then", "getNextPageParam", "getPreviousPageParam", "shouldFetchFirstPage", "i", "shouldFetchNextPage", "finalPromise", "hasNextPage", "Array", "isArray", "nextPageParam", "hasPreviousPage", "previousPageParam", "QueryClient", "constructor", "config", "queryCache", "Query<PERSON>ache", "mutationCache", "MutationCache", "logger", "defaultLogger", "defaultOptions", "queryDefaults", "mutationDefaults", "mountCount", "error", "mount", "unsubscribeFocus", "focusManager", "subscribe", "isFocused", "resumePausedMutations", "onFocus", "unsubscribeOnline", "onlineManager", "isOnline", "onOnline", "unmount", "undefined", "isFetching", "arg1", "arg2", "filters", "parseFilter<PERSON><PERSON>s", "fetchStatus", "findAll", "length", "isMutating", "fetching", "getQueryData", "query<PERSON><PERSON>", "find", "state", "data", "ensureQueryData", "arg3", "parsedOptions", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cachedData", "Promise", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "getQueriesData", "query<PERSON>eyOrFilters", "get<PERSON><PERSON><PERSON><PERSON>ache", "map", "setQueryData", "updater", "options", "query", "prevData", "functionalUpdate", "defaultedOptions", "defaultQueryOptions", "build", "setData", "manual", "setQueriesData", "notify<PERSON><PERSON>ger", "batch", "getQueryState", "removeQueries", "for<PERSON>ach", "remove", "resetQueries", "refetchFilters", "type", "reset", "refetchQueries", "cancelQueries", "cancelOptions", "revert", "promises", "cancel", "all", "then", "noop", "catch", "invalidateQueries", "invalidate", "refetchType", "filter", "isDisabled", "fetch", "cancelRefetch", "meta", "refetchPage", "promise", "throwOnError", "retry", "isStaleByTime", "staleTime", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "behavior", "infiniteQueryBehavior", "prefetchInfiniteQuery", "getMutationCache", "<PERSON><PERSON><PERSON><PERSON>", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "x", "hashQuery<PERSON>ey", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstMatchingDefaults", "partialMatchKey", "process", "matchingDefaults", "JSON", "stringify", "setMutationDefaults", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "_defaulted", "queries", "queryHash", "hashQueryKeyByOptions", "refetchOnReconnect", "networkMode", "useErrorBoundary", "suspense", "defaultMutationOptions", "mutations", "clear", "QueryObserver", "Subscribable", "constructor", "client", "options", "trackedProps", "Set", "selectError", "bindMethods", "setOptions", "remove", "bind", "refetch", "onSubscribe", "listeners", "size", "<PERSON><PERSON><PERSON><PERSON>", "addObserver", "shouldFetchOnMount", "executeFetch", "updateTimers", "onUnsubscribe", "hasListeners", "destroy", "shouldFetchOnReconnect", "shouldFetchOn", "refetchOnReconnect", "shouldFetchOnWindowFocus", "refetchOnWindowFocus", "clearStaleTimeout", "clearRefetchInterval", "removeObserver", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "defaultQueryOptions", "isDataEqual", "<PERSON><PERSON><PERSON><PERSON>", "error", "shallowEqualObjects", "get<PERSON><PERSON><PERSON><PERSON>ache", "notify", "type", "query", "observer", "enabled", "Error", "query<PERSON><PERSON>", "updateQuery", "mounted", "shouldFetchOptionally", "updateResult", "staleTime", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "build", "createResult", "getCurrentResult", "currentResult", "trackResult", "result", "trackedResult", "Object", "keys", "for<PERSON>ach", "key", "defineProperty", "configurable", "enumerable", "get", "add", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refetchPage", "fetch", "meta", "fetchOptimistic", "defaultedOptions", "isFetchingOptimistic", "then", "fetchOptions", "cancelRefetch", "promise", "throwOnError", "catch", "noop", "isServer", "isStale", "isValidTimeout", "time", "timeUntilStale", "dataUpdatedAt", "timeout", "staleTimeoutId", "setTimeout", "refetchInterval", "data", "nextInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "focusManager", "isFocused", "clearTimeout", "undefined", "clearInterval", "prevResult", "prevResultState", "currentResultState", "prevResultOptions", "currentResultOptions", "query<PERSON>hange", "queryInitialState", "state", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "errorUpdatedAt", "fetchStatus", "status", "isPreviousData", "isPlaceholderData", "_optimisticResults", "fetchOnMount", "fetchOptionally", "canFetch", "networkMode", "keepPreviousData", "isSuccess", "select", "selectFn", "selectResult", "replaceData", "process", "placeholderData", "Date", "now", "isFetching", "isLoading", "isError", "isInitialLoading", "failureCount", "fetchFailureCount", "failureReason", "fetchFailureReason", "errorUpdateCount", "isFetched", "dataUpdateCount", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isPaused", "isRefetchError", "nextResult", "defaultNotifyOptions", "cache", "shouldNotifyListeners", "notifyOnChangeProps", "includedProps", "useErrorBoundary", "some", "<PERSON><PERSON><PERSON>", "changed", "has", "onQueryUpdate", "action", "onSuccess", "manual", "isCancelledError", "onError", "notify<PERSON><PERSON>ger", "batch", "onSettled", "listener", "shouldLoadOnMount", "retryOnMount", "refetchOnMount", "field", "value", "suspense", "isStaleByTime", "QueriesObserver", "Subscribable", "constructor", "client", "queries", "result", "observers", "observersMap", "setQueries", "onSubscribe", "listeners", "size", "for<PERSON>ach", "observer", "subscribe", "onUpdate", "onUnsubscribe", "destroy", "Set", "notifyOptions", "notify<PERSON><PERSON>ger", "batch", "prevObservers", "newObserverMatches", "findMatchingObservers", "match", "setOptions", "defaultedQueryOptions", "newObservers", "map", "newObserversMap", "Object", "fromEntries", "options", "queryHash", "newResult", "getCurrentResult", "hasIndexChange", "some", "index", "length", "hasListeners", "difference", "notify", "getQueries", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getObservers", "getOptimisticResult", "prevObserversMap", "Map", "defaultQueryOptions", "matchingObservers", "flatMap", "defaultedOptions", "get", "matchedQueryHashes", "unmatchedQueries", "filter", "has", "matchingObserversSet", "unmatchedObservers", "prevObserver", "getObserver", "currentObserver", "QueryObserver", "newOrReusedObservers", "keepPreviousData", "previouslyUsedObserver", "undefined", "sortMatchesByOrderOfQueries", "a", "b", "indexOf", "concat", "sort", "replaceAt", "listener", "InfiniteQueryObserver", "QueryObserver", "constructor", "client", "options", "bindMethods", "fetchNextPage", "bind", "fetchPreviousPage", "setOptions", "notifyOptions", "behavior", "infiniteQueryBehavior", "getOptimisticResult", "pageParam", "fetch", "meta", "fetchMore", "direction", "createResult", "query", "state", "result", "isFetching", "isRefetching", "isFetchingNextPage", "fetchMeta", "isFetchingPreviousPage", "hasNextPage", "data", "pages", "hasPreviousPage", "MutationObserver", "Subscribable", "constructor", "client", "options", "setOptions", "bindMethods", "updateResult", "mutate", "bind", "reset", "prevOptions", "defaultMutationOptions", "shallowEqualObjects", "getMutationCache", "notify", "type", "mutation", "currentMutation", "observer", "onUnsubscribe", "hasListeners", "removeObserver", "onMutationUpdate", "action", "notifyOptions", "listeners", "onSuccess", "onError", "getCurrentResult", "currentResult", "undefined", "variables", "mutateOptions", "build", "addObserver", "execute", "state", "getDefaultState", "result", "isLoading", "status", "isSuccess", "isError", "isIdle", "notify<PERSON><PERSON>ger", "batch", "data", "context", "onSettled", "error", "for<PERSON>ach", "listener", "dehydrateMutation", "mutation", "<PERSON><PERSON><PERSON>", "options", "state", "dehydrate<PERSON><PERSON>y", "query", "query<PERSON><PERSON>", "queryHash", "defaultShouldDehydrateMutation", "isPaused", "defaultShouldDehydrateQuery", "status", "dehydrate", "client", "mutations", "queries", "dehydrateMutations", "shouldDehydrateMutation", "getMutationCache", "getAll", "for<PERSON>ach", "push", "dehydrateQueries", "shouldDehydrateQuery", "get<PERSON><PERSON><PERSON><PERSON>ache", "hydrate", "dehydratedState", "mutationCache", "queryCache", "dehydratedMutation", "build", "defaultOptions", "dehydrated<PERSON><PERSON>y", "get", "dehydratedQueryState", "fetchStatus", "dataUpdatedAt", "setState", "unstable_batchedUpdates", "notify<PERSON><PERSON>ger", "setBatchNotifyFunction", "unstable_batchedUpdates", "useSyncExternalStore$1", "context", "contextSharing", "client", "IsRestoringProvider", "clear<PERSON><PERSON>t", "isReset", "reset", "children", "shouldThrowError", "_useErrorBoundary", "params", "useClearResetErrorBoundary", "errorResetBoundary", "query", "ensureStaleTime", "defaultedOptions", "suspense", "staleTime", "<PERSON><PERSON><PERSON><PERSON>", "result", "isRestoring", "isLoading", "isFetching", "shouldSuspend", "fetchOptimistic", "observer", "errorResetBoundary", "then", "data", "onSuccess", "onSettled", "catch", "error", "clear<PERSON><PERSON>t", "onError", "undefined", "context", "defaultedOptions", "defaultedQueries", "ensurePreventErrorBoundaryRetry", "useSyncExternalStore", "observer", "listeners", "ensurePreventErrorBoundaryRetry", "useSyncExternalStore", "observer", "listeners", "optionsRef", "state", "useHydrate", "noop"]}