{"version": 3, "sources": ["../../node_modules/react-merge-refs/src/index.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nexport default function mergeRefs<T = any>(\n  refs: Array<React.MutableRefObject<T> | React.LegacyRef<T>>\n): React.RefCallback<T> {\n  return (value) => {\n    refs.forEach((ref) => {\n      if (typeof ref === \"function\") {\n        ref(value);\n      } else if (ref != null) {\n        (ref as React.MutableRefObject<T | null>).current = value;\n      }\n    });\n  };\n}\n"], "mappings": ";SAEwBA,UACtBC,MAAAA;AAEA,SAAO,SAACC,OAAD;AACLD,SAAKE,QAAQ,SAACC,KAAD;AACX,UAAI,OAAOA,QAAQ,YAAY;AAC7BA,YAAIF,KAAD;MACJ,WAAUE,OAAO,MAAM;AACrBA,YAAyCC,UAAUH;MACrD;IACF,CAND;EAOD;AACF;;", "names": ["mergeRefs", "refs", "value", "for<PERSON>ach", "ref", "current"]}