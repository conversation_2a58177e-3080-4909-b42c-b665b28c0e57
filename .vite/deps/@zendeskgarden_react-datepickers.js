import {
  popper_default,
  require_deep_equal,
  require_lib
} from "./chunk-B7B3EFFH.js";
import {
  _defineProperty
} from "./chunk-MLNIKOSA.js";
import {
  require_warning
} from "./chunk-NWUESYL6.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-PSGUSLG5.js";
import {
  DEFAULT_THEME,
  _extends,
  _inheritsLoose,
  getColor,
  menuStyles,
  retrieveComponentStyles
} from "./chunk-KGUWDO6Q.js";
import {
  _assertThisInitialized
} from "./chunk-LN6LZUGQ.js";
import {
  KEY_CODES,
  composeEventHandlers
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  Ae,
  Me,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-datepickers/dist/index.esm.js
var React4 = __toESM(require_react());
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());

// node_modules/@zendeskgarden/react-datepickers/node_modules/react-popper/lib/esm/Popper.js
var import_deep_equal = __toESM(require_deep_equal());
var React2 = __toESM(require_react());

// node_modules/@zendeskgarden/react-datepickers/node_modules/react-popper/lib/esm/Manager.js
var React = __toESM(require_react());
var import_create_react_context = __toESM(require_lib());
var ManagerReferenceNodeContext = (0, import_create_react_context.default)();
var ManagerReferenceNodeSetterContext = (0, import_create_react_context.default)();
var Manager = function(_React$Component) {
  _inheritsLoose(Manager2, _React$Component);
  function Manager2() {
    var _this;
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "referenceNode", void 0);
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "setReferenceNode", function(newReferenceNode) {
      if (newReferenceNode && _this.referenceNode !== newReferenceNode) {
        _this.referenceNode = newReferenceNode;
        _this.forceUpdate();
      }
    });
    return _this;
  }
  var _proto = Manager2.prototype;
  _proto.componentWillUnmount = function componentWillUnmount() {
    this.referenceNode = null;
  };
  _proto.render = function render() {
    return React.createElement(ManagerReferenceNodeContext.Provider, {
      value: this.referenceNode
    }, React.createElement(ManagerReferenceNodeSetterContext.Provider, {
      value: this.setReferenceNode
    }, this.props.children));
  };
  return Manager2;
}(React.Component);

// node_modules/@zendeskgarden/react-datepickers/node_modules/react-popper/lib/esm/utils.js
var unwrapArray = function unwrapArray2(arg) {
  return Array.isArray(arg) ? arg[0] : arg;
};
var safeInvoke = function safeInvoke2(fn) {
  if (typeof fn === "function") {
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    return fn.apply(void 0, args);
  }
};
var shallowEqual = function shallowEqual2(objA, objB) {
  var aKeys = Object.keys(objA);
  var bKeys = Object.keys(objB);
  if (bKeys.length !== aKeys.length) {
    return false;
  }
  for (var i = 0; i < bKeys.length; i++) {
    var key = aKeys[i];
    if (objA[key] !== objB[key]) {
      return false;
    }
  }
  return true;
};
var setRef = function setRef2(ref, node) {
  if (typeof ref === "function") {
    return safeInvoke(ref, node);
  } else if (ref != null) {
    ref.current = node;
  }
};

// node_modules/@zendeskgarden/react-datepickers/node_modules/react-popper/lib/esm/Popper.js
var initialStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  opacity: 0,
  pointerEvents: "none"
};
var initialArrowStyle = {};
var InnerPopper = function(_React$Component) {
  _inheritsLoose(InnerPopper2, _React$Component);
  function InnerPopper2() {
    var _this;
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "state", {
      data: void 0,
      placement: void 0
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "popperInstance", void 0);
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "popperNode", null);
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "arrowNode", null);
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "setPopperNode", function(popperNode) {
      if (!popperNode || _this.popperNode === popperNode)
        return;
      setRef(_this.props.innerRef, popperNode);
      _this.popperNode = popperNode;
      _this.updatePopperInstance();
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "setArrowNode", function(arrowNode) {
      _this.arrowNode = arrowNode;
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "updateStateModifier", {
      enabled: true,
      order: 900,
      fn: function fn(data) {
        var placement = data.placement;
        _this.setState({
          data,
          placement
        });
        return data;
      }
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "getOptions", function() {
      return {
        placement: _this.props.placement,
        eventsEnabled: _this.props.eventsEnabled,
        positionFixed: _this.props.positionFixed,
        modifiers: _extends({}, _this.props.modifiers, {
          arrow: _extends({}, _this.props.modifiers && _this.props.modifiers.arrow, {
            enabled: !!_this.arrowNode,
            element: _this.arrowNode
          }),
          applyStyle: {
            enabled: false
          },
          updateStateModifier: _this.updateStateModifier
        })
      };
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "getPopperStyle", function() {
      return !_this.popperNode || !_this.state.data ? initialStyle : _extends({
        position: _this.state.data.offsets.popper.position
      }, _this.state.data.styles);
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "getPopperPlacement", function() {
      return !_this.state.data ? void 0 : _this.state.placement;
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "getArrowStyle", function() {
      return !_this.arrowNode || !_this.state.data ? initialArrowStyle : _this.state.data.arrowStyles;
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "getOutOfBoundariesState", function() {
      return _this.state.data ? _this.state.data.hide : void 0;
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "destroyPopperInstance", function() {
      if (!_this.popperInstance)
        return;
      _this.popperInstance.destroy();
      _this.popperInstance = null;
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "updatePopperInstance", function() {
      _this.destroyPopperInstance();
      var _assertThisInitialize = _assertThisInitialized(_assertThisInitialized(_this)), popperNode = _assertThisInitialize.popperNode;
      var referenceElement = _this.props.referenceElement;
      if (!referenceElement || !popperNode)
        return;
      _this.popperInstance = new popper_default(referenceElement, popperNode, _this.getOptions());
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "scheduleUpdate", function() {
      if (_this.popperInstance) {
        _this.popperInstance.scheduleUpdate();
      }
    });
    return _this;
  }
  var _proto = InnerPopper2.prototype;
  _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {
    if (this.props.placement !== prevProps.placement || this.props.referenceElement !== prevProps.referenceElement || this.props.positionFixed !== prevProps.positionFixed || !(0, import_deep_equal.default)(this.props.modifiers, prevProps.modifiers, {
      strict: true
    })) {
      if (true) {
        if (this.props.modifiers !== prevProps.modifiers && this.props.modifiers != null && prevProps.modifiers != null && shallowEqual(this.props.modifiers, prevProps.modifiers)) {
          console.warn("'modifiers' prop reference updated even though all values appear the same.\nConsider memoizing the 'modifiers' object to avoid needless rendering.");
        }
      }
      this.updatePopperInstance();
    } else if (this.props.eventsEnabled !== prevProps.eventsEnabled && this.popperInstance) {
      this.props.eventsEnabled ? this.popperInstance.enableEventListeners() : this.popperInstance.disableEventListeners();
    }
    if (prevState.placement !== this.state.placement) {
      this.scheduleUpdate();
    }
  };
  _proto.componentWillUnmount = function componentWillUnmount() {
    setRef(this.props.innerRef, null);
    this.destroyPopperInstance();
  };
  _proto.render = function render() {
    return unwrapArray(this.props.children)({
      ref: this.setPopperNode,
      style: this.getPopperStyle(),
      placement: this.getPopperPlacement(),
      outOfBoundaries: this.getOutOfBoundariesState(),
      scheduleUpdate: this.scheduleUpdate,
      arrowProps: {
        ref: this.setArrowNode,
        style: this.getArrowStyle()
      }
    });
  };
  return InnerPopper2;
}(React2.Component);
_defineProperty(InnerPopper, "defaultProps", {
  placement: "bottom",
  eventsEnabled: true,
  referenceElement: void 0,
  positionFixed: false
});
var placements = popper_default.placements;
function Popper(_ref) {
  var referenceElement = _ref.referenceElement, props = _objectWithoutPropertiesLoose(_ref, ["referenceElement"]);
  return React2.createElement(ManagerReferenceNodeContext.Consumer, null, function(referenceNode) {
    return React2.createElement(InnerPopper, _extends({
      referenceElement: referenceElement !== void 0 ? referenceElement : referenceNode
    }, props));
  });
}

// node_modules/@zendeskgarden/react-datepickers/node_modules/react-popper/lib/esm/Reference.js
var React3 = __toESM(require_react());
var import_warning = __toESM(require_warning());
var InnerReference = function(_React$Component) {
  _inheritsLoose(InnerReference2, _React$Component);
  function InnerReference2() {
    var _this;
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "refHandler", function(node) {
      setRef(_this.props.innerRef, node);
      safeInvoke(_this.props.setReferenceNode, node);
    });
    return _this;
  }
  var _proto = InnerReference2.prototype;
  _proto.componentWillUnmount = function componentWillUnmount() {
    setRef(this.props.innerRef, null);
  };
  _proto.render = function render() {
    (0, import_warning.default)(Boolean(this.props.setReferenceNode), "`Reference` should not be used outside of a `Manager` component.");
    return unwrapArray(this.props.children)({
      ref: this.refHandler
    });
  };
  return InnerReference2;
}(React3.Component);
function Reference(props) {
  return React3.createElement(ManagerReferenceNodeSetterContext.Consumer, null, function(setReferenceNode) {
    return React3.createElement(InnerReference, _extends({
      setReferenceNode
    }, props));
  });
}

// node_modules/@zendeskgarden/react-datepickers/dist/index.esm.js
var WEEK_STARTS_ON = [0, 1, 2, 3, 4, 5, 6];
var SHARED_PLACEMENT = ["auto", "top", "top-start", "top-end", "bottom", "bottom-start", "bottom-end"];
var PLACEMENT = [...SHARED_PLACEMENT, "end", "end-top", "end-bottom", "start", "start-top", "start-bottom"];
function getPopperPlacement(gardenPlacement) {
  switch (gardenPlacement) {
    case "end":
      return "right";
    case "end-top":
      return "right-start";
    case "end-bottom":
      return "right-end";
    case "start":
      return "left";
    case "start-top":
      return "left-start";
    case "start-bottom":
      return "left-end";
    default:
      return gardenPlacement;
  }
}
function getRtlPopperPlacement(gardenPlacement) {
  const popperPlacement = getPopperPlacement(gardenPlacement);
  switch (popperPlacement) {
    case "left":
      return "right";
    case "left-start":
      return "right-start";
    case "left-end":
      return "right-end";
    case "top-start":
      return "top-end";
    case "top-end":
      return "top-start";
    case "right":
      return "left";
    case "right-start":
      return "left-start";
    case "right-end":
      return "left-end";
    case "bottom-start":
      return "bottom-end";
    case "bottom-end":
      return "bottom-start";
    default:
      return popperPlacement;
  }
}
function getMenuPosition(popperPlacement) {
  return popperPlacement ? popperPlacement.split("-")[0] : "bottom";
}
function requiredArgs(required, args) {
  if (args.length < required) {
    throw new TypeError(required + " argument" + (required > 1 ? "s" : "") + " required, but only " + args.length + " present");
  }
}
function toDate(argument) {
  requiredArgs(1, arguments);
  var argStr = Object.prototype.toString.call(argument);
  if (argument instanceof Date || typeof argument === "object" && argStr === "[object Date]") {
    return new Date(argument.getTime());
  } else if (typeof argument === "number" || argStr === "[object Number]") {
    return new Date(argument);
  } else {
    if ((typeof argument === "string" || argStr === "[object String]") && typeof console !== "undefined") {
      console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://git.io/fjule");
      console.warn(new Error().stack);
    }
    return /* @__PURE__ */ new Date(NaN);
  }
}
function startOfMonth(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  date.setDate(1);
  date.setHours(0, 0, 0, 0);
  return date;
}
function endOfMonth(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var month = date.getMonth();
  date.setFullYear(date.getFullYear(), month + 1, 0);
  date.setHours(23, 59, 59, 999);
  return date;
}
function toInteger(dirtyNumber) {
  if (dirtyNumber === null || dirtyNumber === true || dirtyNumber === false) {
    return NaN;
  }
  var number = Number(dirtyNumber);
  if (isNaN(number)) {
    return number;
  }
  return number < 0 ? Math.ceil(number) : Math.floor(number);
}
function startOfWeek(dirtyDate, dirtyOptions) {
  requiredArgs(1, arguments);
  var options = dirtyOptions || {};
  var locale2 = options.locale;
  var localeWeekStartsOn = locale2 && locale2.options && locale2.options.weekStartsOn;
  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  }
  var date = toDate(dirtyDate);
  var day = date.getDay();
  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;
  date.setDate(date.getDate() - diff);
  date.setHours(0, 0, 0, 0);
  return date;
}
function endOfWeek(dirtyDate, dirtyOptions) {
  requiredArgs(1, arguments);
  var options = dirtyOptions || {};
  var locale2 = options.locale;
  var localeWeekStartsOn = locale2 && locale2.options && locale2.options.weekStartsOn;
  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  }
  var date = toDate(dirtyDate);
  var day = date.getDay();
  var diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);
  date.setDate(date.getDate() + diff);
  date.setHours(23, 59, 59, 999);
  return date;
}
function eachDayOfInterval(dirtyInterval, options) {
  requiredArgs(1, arguments);
  var interval = dirtyInterval || {};
  var startDate = toDate(interval.start);
  var endDate = toDate(interval.end);
  var endTime = endDate.getTime();
  if (!(startDate.getTime() <= endTime)) {
    throw new RangeError("Invalid interval");
  }
  var dates = [];
  var currentDate = startDate;
  currentDate.setHours(0, 0, 0, 0);
  var step = options && "step" in options ? Number(options.step) : 1;
  if (step < 1 || isNaN(step))
    throw new RangeError("`options.step` must be a number greater than 1");
  while (currentDate.getTime() <= endTime) {
    dates.push(toDate(currentDate));
    currentDate.setDate(currentDate.getDate() + step);
    currentDate.setHours(0, 0, 0, 0);
  }
  return dates;
}
function addDays(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var amount = toInteger(dirtyAmount);
  if (isNaN(amount)) {
    return /* @__PURE__ */ new Date(NaN);
  }
  if (!amount) {
    return date;
  }
  date.setDate(date.getDate() + amount);
  return date;
}
function startOfDay(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  date.setHours(0, 0, 0, 0);
  return date;
}
function isSameDay(dirtyDateLeft, dirtyDateRight) {
  requiredArgs(2, arguments);
  var dateLeftStartOfDay = startOfDay(dirtyDateLeft);
  var dateRightStartOfDay = startOfDay(dirtyDateRight);
  return dateLeftStartOfDay.getTime() === dateRightStartOfDay.getTime();
}
function isToday(dirtyDate) {
  requiredArgs(1, arguments);
  return isSameDay(dirtyDate, Date.now());
}
function isSameMonth(dirtyDateLeft, dirtyDateRight) {
  requiredArgs(2, arguments);
  var dateLeft = toDate(dirtyDateLeft);
  var dateRight = toDate(dirtyDateRight);
  return dateLeft.getFullYear() === dateRight.getFullYear() && dateLeft.getMonth() === dateRight.getMonth();
}
function isBefore(dirtyDate, dirtyDateToCompare) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var dateToCompare = toDate(dirtyDateToCompare);
  return date.getTime() < dateToCompare.getTime();
}
function isAfter(dirtyDate, dirtyDateToCompare) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var dateToCompare = toDate(dirtyDateToCompare);
  return date.getTime() > dateToCompare.getTime();
}
function getDate(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var dayOfMonth = date.getDate();
  return dayOfMonth;
}
var COMPONENT_ID$b = "datepickers.menu";
var StyledMenu = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$b,
  "data-garden-version": "8.68.0"
}).withConfig({
  displayName: "StyledMenu",
  componentId: "sc-1npbkk0-0"
})(["", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$b, props));
StyledMenu.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$a = "datepickers.menu_wrapper";
var StyledMenuWrapper = styled_components_browser_esm_default.div.attrs((props) => ({
  className: props.isAnimated && "is-animated"
})).withConfig({
  displayName: "StyledMenuWrapper",
  componentId: "sc-6fowoz-0"
})(["", ";", ";"], (props) => menuStyles(getMenuPosition(props.placement), {
  theme: props.theme,
  hidden: props.isHidden,
  margin: `${props.theme.space.base}px`,
  zIndex: props.zIndex,
  animationModifier: props.isAnimated ? ".is-animated" : void 0
}), (props) => retrieveComponentStyles(COMPONENT_ID$a, props));
StyledMenuWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$9 = "datepickers.datepicker";
var retrievePadding = (_ref) => {
  let {
    isCompact,
    theme
  } = _ref;
  let value = theme.space.base * 5;
  if (isCompact) {
    value = theme.space.base * 4;
  }
  return `margin: ${value}px;`;
};
var StyledDatepicker = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$9
}).withConfig({
  displayName: "StyledDatepicker",
  componentId: "sc-w3zqsp-0"
})(["direction:", ";", " background-color:", ";color:", ";", ";"], (props) => props.theme.rtl && "rtl", retrievePadding, (props) => props.theme.colors.background, (props) => props.theme.colors.foreground, (props) => retrieveComponentStyles(COMPONENT_ID$9, props));
StyledDatepicker.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$8 = "datepickers.range_calendar";
var StyledRangeCalendar = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$8
}).withConfig({
  displayName: "StyledRangeCalendar",
  componentId: "sc-1og46sy-0"
})(["display:flex;overflow:auto;", "{margin:0;", "}", ";"], StyledDatepicker, (props) => props.theme.rtl ? `&:last-of-type {margin-right: ${props.theme.space.base * 5}px}` : `&:first-of-type {margin-right: ${props.theme.space.base * 5}px}`, (props) => retrieveComponentStyles(COMPONENT_ID$8, props));
StyledRangeCalendar.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$7 = "datepickers.header";
var StyledHeader = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$7
}).withConfig({
  displayName: "StyledHeader",
  componentId: "sc-upq318-0"
})(["display:flex;width:", "px;", ";"], (props) => props.isCompact ? props.theme.space.base * 56 : props.theme.space.base * 70, (props) => retrieveComponentStyles(COMPONENT_ID$7, props));
StyledHeader.defaultProps = {
  theme: DEFAULT_THEME
};
var retrieveSizing = (_ref) => {
  let {
    isCompact,
    theme
  } = _ref;
  let size = theme.space.base * 10;
  if (isCompact) {
    size = theme.space.base * 8;
  }
  return Ae(["width:", "px;height:", "px;"], size, size);
};
var retrieveColor$1 = (_ref2) => {
  let {
    theme
  } = _ref2;
  return Ae([":hover{background-color:", ";color:", ";}:active{background-color:", ";color:", ";}color:", ";"], getColor("primaryHue", 600, theme, 0.08), theme.colors.foreground, getColor("primaryHue", 600, theme, 0.2), theme.colors.foreground, getColor("neutralHue", 600, theme));
};
var COMPONENT_ID$6 = "datepickers.header_paddle";
var StyledHeaderPaddle = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$6
}).withConfig({
  displayName: "StyledHeaderPaddle",
  componentId: "sc-2oqh0g-0"
})(["display:flex;align-items:center;justify-content:center;transform:", ";visibility:", ";border-radius:50%;cursor:pointer;", " ", " svg{width:", ";height:", ";}", ";"], (props) => props.theme.rtl && "rotate(180deg)", (props) => props.isHidden && "hidden", retrieveSizing, retrieveColor$1, (props) => `${props.theme.iconSizes.md}`, (props) => `${props.theme.iconSizes.md}`, (props) => retrieveComponentStyles(COMPONENT_ID$6, props));
StyledHeaderPaddle.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$5 = "datepickers.header_label";
var StyledHeaderLabel = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$5
}).withConfig({
  displayName: "StyledHeaderLabel",
  componentId: "sc-1ryf5ub-0"
})(["display:flex;flex-grow:1;align-items:center;justify-content:center;font-size:", ";font-weight:", ";", ";"], (props) => props.isCompact ? props.theme.fontSizes.sm : props.theme.fontSizes.md, (props) => props.theme.fontWeights.semibold, (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledHeaderLabel.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$4 = "datepickers.calendar";
var StyledCalendar = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$4
}).withConfig({
  displayName: "StyledCalendar",
  componentId: "sc-g5hoe8-0"
})(["width:", "px;", ";"], (props) => props.isCompact ? props.theme.space.base * 56 : props.theme.space.base * 70, (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledCalendar.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "datepickers.calendar_item";
var retrieveSize = (_ref) => {
  let {
    isCompact,
    theme
  } = _ref;
  let size;
  if (isCompact) {
    size = `${theme.space.base * 8}px`;
  } else {
    size = `${theme.space.base * 10}px`;
  }
  return Ae(["width:", ";height:", ";"], size, size);
};
var StyledCalendarItem = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$3
}).withConfig({
  displayName: "StyledCalendarItem",
  componentId: "sc-143w8wb-0"
})(["display:inline-block;position:relative;vertical-align:top;", " ", ";"], retrieveSize, (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledCalendarItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "datepickers.day_label";
var StyledDayLabel = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$2
}).withConfig({
  displayName: "StyledDayLabel",
  componentId: "sc-9bh1p7-0"
})(["display:flex;align-items:center;justify-content:center;width:100%;height:100%;font-size:", ";font-weight:", ";", ";"], (props) => props.isCompact ? props.theme.fontSizes.sm : props.theme.fontSizes.md, (props) => props.theme.fontWeights.semibold, (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledDayLabel.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "datepickers.highlight";
var retrieveBorderRadius = (_ref) => {
  let {
    theme,
    isEnd,
    isStart
  } = _ref;
  const startValue = "border-radius: 0 50% 50% 0;";
  const endValue = "border-radius: 50% 0 0 50%;";
  if (theme.rtl) {
    if (isStart) {
      return startValue;
    } else if (isEnd) {
      return endValue;
    }
  }
  if (isStart) {
    return endValue;
  } else if (isEnd) {
    return startValue;
  }
  return "";
};
var retrieveColor = (_ref2) => {
  let {
    isHighlighted,
    theme
  } = _ref2;
  if (isHighlighted) {
    return Ae(["background-color:", ";"], getColor("primaryHue", 600, theme, 0.08));
  }
  return Ae([""]);
};
var StyledHighlight = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$1
}).withConfig({
  displayName: "StyledHighlight",
  componentId: "sc-16vr32x-0"
})(["position:absolute;top:0;left:0;width:100%;height:100%;", " ", " ", ";"], retrieveBorderRadius, retrieveColor, (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledHighlight.defaultProps = {
  theme: DEFAULT_THEME
};
var retrieveStyledDayColors = (_ref) => {
  let {
    isSelected,
    isDisabled,
    isToday: isToday2,
    isPreviousMonth,
    theme
  } = _ref;
  let backgroundColor = "inherit";
  let color = getColor("primaryHue", 600, theme);
  if (isSelected && !isDisabled) {
    backgroundColor = getColor("primaryHue", 600, theme);
    color = theme.colors.background;
  } else if (isDisabled) {
    color = getColor("neutralHue", 400, theme);
  } else if (isToday2) {
    color = "inherit";
  } else if (isPreviousMonth) {
    color = getColor("neutralHue", 600, theme);
  }
  return Ae(["background-color:", ";color:", ";", ""], backgroundColor, color, !isSelected && !isDisabled && `
      :hover {
        background-color: ${getColor("primaryHue", 600, theme, 0.08)};
        color: ${getColor("primaryHue", 800, theme)};
      }

      :active {
        background-color: ${getColor("primaryHue", 600, theme, 0.2)};
        color: ${getColor("primaryHue", 800, theme)};
      }
  `);
};
var COMPONENT_ID = "datepickers.day";
var StyledDay = styled_components_browser_esm_default.div.attrs((props) => ({
  "data-garden-id": COMPONENT_ID,
  "aria-disabled": props.isDisabled ? "true" : "false"
})).withConfig({
  displayName: "StyledDay",
  componentId: "sc-v42uk5-0"
})(["display:flex;position:absolute;align-items:center;justify-content:center;border-radius:50%;cursor:", ";width:100%;height:100%;font-size:", ";font-weight:", ";", " ", ";"], (props) => props.isDisabled ? "inherit" : "pointer", (props) => props.isCompact ? props.theme.fontSizes.sm : props.theme.fontSizes.md, (props) => props.isToday && !props.isDisabled ? props.theme.fontWeights.semibold : "inherit", retrieveStyledDayColors, (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledDay.defaultProps = {
  theme: DEFAULT_THEME
};
var DatepickerContext = (0, import_react.createContext)(void 0);
var useDatepickerContext$1 = () => {
  return (0, import_react.useContext)(DatepickerContext);
};
var REGION_MAPPINGS = {
  "ar-DZ": 0,
  "ar-SA": 0,
  "en-CA": 0,
  "en-GB": 1,
  "en-US": 0,
  "fa-IR": 0,
  "fr-CH": 1,
  "nl-BE": 1,
  "pt-BR": 0,
  "zh-CN": 1,
  "zh-TW": 1
};
var LANGUAGE_MAPPINGS = {
  af: 0,
  ar: 6,
  be: 1,
  bg: 1,
  bn: 0,
  ca: 1,
  cs: 1,
  da: 1,
  de: 1,
  el: 1,
  en: 0,
  eo: 1,
  es: 1,
  et: 1,
  fa: 0,
  fi: 1,
  fil: 0,
  fr: 1,
  gl: 1,
  he: 0,
  hr: 1,
  hu: 1,
  id: 1,
  is: 1,
  it: 1,
  ja: 1,
  ka: 1,
  ko: 0,
  lt: 1,
  lv: 1,
  mk: 1,
  ms: 1,
  nb: 1,
  nl: 1,
  nn: 1,
  pl: 1,
  pt: 0,
  ro: 1,
  ru: 1,
  sk: 1,
  sl: 1,
  sr: 1,
  sv: 1,
  th: 1,
  tr: 1,
  ug: 0,
  uk: 1,
  vi: 1,
  zh: 1
};
function getStartOfWeek(locale2) {
  if (!locale2) {
    return 0;
  }
  for (const region in REGION_MAPPINGS) {
    if (locale2.startsWith(region)) {
      return REGION_MAPPINGS[region];
    }
  }
  for (const language in LANGUAGE_MAPPINGS) {
    if (locale2.startsWith(language)) {
      return LANGUAGE_MAPPINGS[language];
    }
  }
  return 0;
}
var _path$1;
function _extends$2() {
  _extends$2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$2.apply(this, arguments);
}
var SvgChevronLeftStroke = function SvgChevronLeftStroke2(props) {
  return React4.createElement("svg", _extends$2({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$1 || (_path$1 = React4.createElement("path", {
    fill: "currentColor",
    d: "M10.39 12.688a.5.5 0 01-.718.69l-.062-.066-4-5a.5.5 0 01-.054-.542l.054-.082 4-5a.5.5 0 01.83.55l-.05.074L6.641 8l3.75 4.688z"
  })));
};
var _path;
function _extends$1() {
  _extends$1 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$1.apply(this, arguments);
}
var SvgChevronRightStroke = function SvgChevronRightStroke2(props) {
  return React4.createElement("svg", _extends$1({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path || (_path = React4.createElement("path", {
    fill: "currentColor",
    d: "M5.61 3.312a.5.5 0 01.718-.69l.062.066 4 5a.5.5 0 01.054.542l-.054.082-4 5a.5.5 0 01-.83-.55l.05-.074L9.359 8l-3.75-4.688z"
  })));
};
var MonthSelector = (_ref) => {
  let {
    locale: locale2,
    isCompact
  } = _ref;
  const {
    state,
    dispatch
  } = useDatepickerContext$1();
  const headerLabelFormatter = (0, import_react.useCallback)((date) => {
    const formatter = new Intl.DateTimeFormat(locale2, {
      month: "long",
      year: "numeric"
    });
    return formatter.format(date);
  }, [locale2]);
  return import_react.default.createElement(StyledHeader, {
    isCompact
  }, import_react.default.createElement(StyledHeaderPaddle, {
    isCompact,
    onClick: () => {
      dispatch({
        type: "PREVIEW_PREVIOUS_MONTH"
      });
    }
  }, import_react.default.createElement(SvgChevronLeftStroke, null)), import_react.default.createElement(StyledHeaderLabel, {
    isCompact
  }, headerLabelFormatter(state.previewDate)), import_react.default.createElement(StyledHeaderPaddle, {
    isCompact,
    onClick: () => {
      dispatch({
        type: "PREVIEW_NEXT_MONTH"
      });
    }
  }, import_react.default.createElement(SvgChevronRightStroke, null)));
};
var Calendar$1 = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    value,
    minValue,
    maxValue,
    isCompact,
    locale: locale2,
    weekStartsOn
  } = _ref;
  const {
    state,
    dispatch
  } = useDatepickerContext$1();
  const preferredWeekStartsOn = weekStartsOn || getStartOfWeek(locale2);
  const monthStartDate = startOfMonth(state.previewDate);
  const monthEndDate = endOfMonth(monthStartDate);
  const startDate = startOfWeek(monthStartDate, {
    weekStartsOn: preferredWeekStartsOn
  });
  const endDate = endOfWeek(monthEndDate, {
    weekStartsOn: preferredWeekStartsOn
  });
  const dayLabelFormatter = (0, import_react.useCallback)((date) => {
    const formatter = new Intl.DateTimeFormat(locale2, {
      weekday: "short"
    });
    return formatter.format(date);
  }, [locale2]);
  const dayLabels = eachDayOfInterval({
    start: startDate,
    end: addDays(startDate, 6)
  }).map((date) => {
    const formattedDayLabel = dayLabelFormatter(date);
    return import_react.default.createElement(StyledCalendarItem, {
      key: `day-label-${formattedDayLabel}`,
      isCompact
    }, import_react.default.createElement(StyledDayLabel, {
      isCompact
    }, formattedDayLabel));
  });
  const items = eachDayOfInterval({
    start: startDate,
    end: endDate
  }).map((date, itemsIndex) => {
    const formattedDayLabel = getDate(date);
    const isCurrentDate = isToday(date);
    const isPreviousMonth = !isSameMonth(date, state.previewDate);
    const isSelected = value && isSameDay(date, value);
    let isDisabled = false;
    if (minValue !== void 0) {
      isDisabled = isBefore(date, minValue) && !isSameDay(date, minValue);
    }
    if (maxValue !== void 0) {
      isDisabled = isDisabled || isAfter(date, maxValue) && !isSameDay(date, maxValue);
    }
    return import_react.default.createElement(StyledCalendarItem, {
      key: `day-${itemsIndex}`,
      isCompact
    }, import_react.default.createElement(StyledDay, {
      isToday: isCurrentDate,
      isPreviousMonth,
      isSelected,
      isDisabled,
      isCompact,
      onClick: () => {
        if (!isDisabled) {
          dispatch({
            type: "SELECT_DATE",
            value: date
          });
        }
      }
    }, formattedDayLabel));
  });
  return import_react.default.createElement(StyledDatepicker, {
    ref,
    isCompact,
    onMouseDown: (e) => {
      e.preventDefault();
    }
  }, import_react.default.createElement(MonthSelector, {
    locale: locale2,
    isCompact
  }), import_react.default.createElement(StyledCalendar, {
    isCompact
  }, dayLabels, items));
});
Calendar$1.displayName = "Calendar";
function addMonths(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var amount = toInteger(dirtyAmount);
  if (isNaN(amount)) {
    return /* @__PURE__ */ new Date(NaN);
  }
  if (!amount) {
    return date;
  }
  var dayOfMonth = date.getDate();
  var endOfDesiredMonth = new Date(date.getTime());
  endOfDesiredMonth.setMonth(date.getMonth() + amount + 1, 0);
  var daysInMonth = endOfDesiredMonth.getDate();
  if (dayOfMonth >= daysInMonth) {
    return endOfDesiredMonth;
  } else {
    date.setFullYear(endOfDesiredMonth.getFullYear(), endOfDesiredMonth.getMonth(), dayOfMonth);
    return date;
  }
}
function subMonths(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var amount = toInteger(dirtyAmount);
  return addMonths(dirtyDate, -amount);
}
function isValid(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  return !isNaN(date);
}
var formatDistanceLocale = {
  lessThanXSeconds: {
    one: "less than a second",
    other: "less than {{count}} seconds"
  },
  xSeconds: {
    one: "1 second",
    other: "{{count}} seconds"
  },
  halfAMinute: "half a minute",
  lessThanXMinutes: {
    one: "less than a minute",
    other: "less than {{count}} minutes"
  },
  xMinutes: {
    one: "1 minute",
    other: "{{count}} minutes"
  },
  aboutXHours: {
    one: "about 1 hour",
    other: "about {{count}} hours"
  },
  xHours: {
    one: "1 hour",
    other: "{{count}} hours"
  },
  xDays: {
    one: "1 day",
    other: "{{count}} days"
  },
  aboutXWeeks: {
    one: "about 1 week",
    other: "about {{count}} weeks"
  },
  xWeeks: {
    one: "1 week",
    other: "{{count}} weeks"
  },
  aboutXMonths: {
    one: "about 1 month",
    other: "about {{count}} months"
  },
  xMonths: {
    one: "1 month",
    other: "{{count}} months"
  },
  aboutXYears: {
    one: "about 1 year",
    other: "about {{count}} years"
  },
  xYears: {
    one: "1 year",
    other: "{{count}} years"
  },
  overXYears: {
    one: "over 1 year",
    other: "over {{count}} years"
  },
  almostXYears: {
    one: "almost 1 year",
    other: "almost {{count}} years"
  }
};
function formatDistance(token, count, options) {
  options = options || {};
  var result;
  if (typeof formatDistanceLocale[token] === "string") {
    result = formatDistanceLocale[token];
  } else if (count === 1) {
    result = formatDistanceLocale[token].one;
  } else {
    result = formatDistanceLocale[token].other.replace("{{count}}", count);
  }
  if (options.addSuffix) {
    if (options.comparison > 0) {
      return "in " + result;
    } else {
      return result + " ago";
    }
  }
  return result;
}
function buildFormatLongFn(args) {
  return function() {
    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    var width = options.width ? String(options.width) : args.defaultWidth;
    var format = args.formats[width] || args.formats[args.defaultWidth];
    return format;
  };
}
var dateFormats = {
  full: "EEEE, MMMM do, y",
  long: "MMMM do, y",
  medium: "MMM d, y",
  short: "MM/dd/yyyy"
};
var timeFormats = {
  full: "h:mm:ss a zzzz",
  long: "h:mm:ss a z",
  medium: "h:mm:ss a",
  short: "h:mm a"
};
var dateTimeFormats = {
  full: "{{date}} 'at' {{time}}",
  long: "{{date}} 'at' {{time}}",
  medium: "{{date}}, {{time}}",
  short: "{{date}}, {{time}}"
};
var formatLong = {
  date: buildFormatLongFn({
    formats: dateFormats,
    defaultWidth: "full"
  }),
  time: buildFormatLongFn({
    formats: timeFormats,
    defaultWidth: "full"
  }),
  dateTime: buildFormatLongFn({
    formats: dateTimeFormats,
    defaultWidth: "full"
  })
};
var formatLong$1 = formatLong;
var formatRelativeLocale = {
  lastWeek: "'last' eeee 'at' p",
  yesterday: "'yesterday at' p",
  today: "'today at' p",
  tomorrow: "'tomorrow at' p",
  nextWeek: "eeee 'at' p",
  other: "P"
};
function formatRelative(token, _date, _baseDate, _options) {
  return formatRelativeLocale[token];
}
function buildLocalizeFn(args) {
  return function(dirtyIndex, dirtyOptions) {
    var options = dirtyOptions || {};
    var context = options.context ? String(options.context) : "standalone";
    var valuesArray;
    if (context === "formatting" && args.formattingValues) {
      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
      var width = options.width ? String(options.width) : defaultWidth;
      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];
    } else {
      var _defaultWidth = args.defaultWidth;
      var _width = options.width ? String(options.width) : args.defaultWidth;
      valuesArray = args.values[_width] || args.values[_defaultWidth];
    }
    var index = args.argumentCallback ? args.argumentCallback(dirtyIndex) : dirtyIndex;
    return valuesArray[index];
  };
}
var eraValues = {
  narrow: ["B", "A"],
  abbreviated: ["BC", "AD"],
  wide: ["Before Christ", "Anno Domini"]
};
var quarterValues = {
  narrow: ["1", "2", "3", "4"],
  abbreviated: ["Q1", "Q2", "Q3", "Q4"],
  wide: ["1st quarter", "2nd quarter", "3rd quarter", "4th quarter"]
};
var monthValues = {
  narrow: ["J", "F", "M", "A", "M", "J", "J", "A", "S", "O", "N", "D"],
  abbreviated: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
  wide: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]
};
var dayValues = {
  narrow: ["S", "M", "T", "W", "T", "F", "S"],
  short: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
  abbreviated: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
  wide: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
};
var dayPeriodValues = {
  narrow: {
    am: "a",
    pm: "p",
    midnight: "mi",
    noon: "n",
    morning: "morning",
    afternoon: "afternoon",
    evening: "evening",
    night: "night"
  },
  abbreviated: {
    am: "AM",
    pm: "PM",
    midnight: "midnight",
    noon: "noon",
    morning: "morning",
    afternoon: "afternoon",
    evening: "evening",
    night: "night"
  },
  wide: {
    am: "a.m.",
    pm: "p.m.",
    midnight: "midnight",
    noon: "noon",
    morning: "morning",
    afternoon: "afternoon",
    evening: "evening",
    night: "night"
  }
};
var formattingDayPeriodValues = {
  narrow: {
    am: "a",
    pm: "p",
    midnight: "mi",
    noon: "n",
    morning: "in the morning",
    afternoon: "in the afternoon",
    evening: "in the evening",
    night: "at night"
  },
  abbreviated: {
    am: "AM",
    pm: "PM",
    midnight: "midnight",
    noon: "noon",
    morning: "in the morning",
    afternoon: "in the afternoon",
    evening: "in the evening",
    night: "at night"
  },
  wide: {
    am: "a.m.",
    pm: "p.m.",
    midnight: "midnight",
    noon: "noon",
    morning: "in the morning",
    afternoon: "in the afternoon",
    evening: "in the evening",
    night: "at night"
  }
};
function ordinalNumber(dirtyNumber, _dirtyOptions) {
  var number = Number(dirtyNumber);
  var rem100 = number % 100;
  if (rem100 > 20 || rem100 < 10) {
    switch (rem100 % 10) {
      case 1:
        return number + "st";
      case 2:
        return number + "nd";
      case 3:
        return number + "rd";
    }
  }
  return number + "th";
}
var localize = {
  ordinalNumber,
  era: buildLocalizeFn({
    values: eraValues,
    defaultWidth: "wide"
  }),
  quarter: buildLocalizeFn({
    values: quarterValues,
    defaultWidth: "wide",
    argumentCallback: function(quarter) {
      return Number(quarter) - 1;
    }
  }),
  month: buildLocalizeFn({
    values: monthValues,
    defaultWidth: "wide"
  }),
  day: buildLocalizeFn({
    values: dayValues,
    defaultWidth: "wide"
  }),
  dayPeriod: buildLocalizeFn({
    values: dayPeriodValues,
    defaultWidth: "wide",
    formattingValues: formattingDayPeriodValues,
    defaultFormattingWidth: "wide"
  })
};
var localize$1 = localize;
function buildMatchPatternFn(args) {
  return function(string) {
    var options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    var matchResult = string.match(args.matchPattern);
    if (!matchResult)
      return null;
    var matchedString = matchResult[0];
    var parseResult = string.match(args.parsePattern);
    if (!parseResult)
      return null;
    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return {
      value,
      rest
    };
  };
}
function buildMatchFn(args) {
  return function(string) {
    var options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    var width = options.width;
    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];
    var matchResult = string.match(matchPattern);
    if (!matchResult) {
      return null;
    }
    var matchedString = matchResult[0];
    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];
    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function(pattern) {
      return pattern.test(matchedString);
    }) : findKey(parsePatterns, function(pattern) {
      return pattern.test(matchedString);
    });
    var value;
    value = args.valueCallback ? args.valueCallback(key) : key;
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return {
      value,
      rest
    };
  };
}
function findKey(object, predicate) {
  for (var key in object) {
    if (object.hasOwnProperty(key) && predicate(object[key])) {
      return key;
    }
  }
  return void 0;
}
function findIndex(array, predicate) {
  for (var key = 0; key < array.length; key++) {
    if (predicate(array[key])) {
      return key;
    }
  }
  return void 0;
}
var matchOrdinalNumberPattern = /^(\d+)(th|st|nd|rd)?/i;
var parseOrdinalNumberPattern = /\d+/i;
var matchEraPatterns = {
  narrow: /^(b|a)/i,
  abbreviated: /^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,
  wide: /^(before christ|before common era|anno domini|common era)/i
};
var parseEraPatterns = {
  any: [/^b/i, /^(a|c)/i]
};
var matchQuarterPatterns = {
  narrow: /^[1234]/i,
  abbreviated: /^q[1234]/i,
  wide: /^[1234](th|st|nd|rd)? quarter/i
};
var parseQuarterPatterns = {
  any: [/1/i, /2/i, /3/i, /4/i]
};
var matchMonthPatterns = {
  narrow: /^[jfmasond]/i,
  abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,
  wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i
};
var parseMonthPatterns = {
  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],
  any: [/^ja/i, /^f/i, /^mar/i, /^ap/i, /^may/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]
};
var matchDayPatterns = {
  narrow: /^[smtwf]/i,
  short: /^(su|mo|tu|we|th|fr|sa)/i,
  abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i,
  wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i
};
var parseDayPatterns = {
  narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],
  any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i]
};
var matchDayPeriodPatterns = {
  narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,
  any: /^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i
};
var parseDayPeriodPatterns = {
  any: {
    am: /^a/i,
    pm: /^p/i,
    midnight: /^mi/i,
    noon: /^no/i,
    morning: /morning/i,
    afternoon: /afternoon/i,
    evening: /evening/i,
    night: /night/i
  }
};
var match = {
  ordinalNumber: buildMatchPatternFn({
    matchPattern: matchOrdinalNumberPattern,
    parsePattern: parseOrdinalNumberPattern,
    valueCallback: function(value) {
      return parseInt(value, 10);
    }
  }),
  era: buildMatchFn({
    matchPatterns: matchEraPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseEraPatterns,
    defaultParseWidth: "any"
  }),
  quarter: buildMatchFn({
    matchPatterns: matchQuarterPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseQuarterPatterns,
    defaultParseWidth: "any",
    valueCallback: function(index) {
      return index + 1;
    }
  }),
  month: buildMatchFn({
    matchPatterns: matchMonthPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseMonthPatterns,
    defaultParseWidth: "any"
  }),
  day: buildMatchFn({
    matchPatterns: matchDayPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseDayPatterns,
    defaultParseWidth: "any"
  }),
  dayPeriod: buildMatchFn({
    matchPatterns: matchDayPeriodPatterns,
    defaultMatchWidth: "any",
    parsePatterns: parseDayPeriodPatterns,
    defaultParseWidth: "any"
  })
};
var match$1 = match;
var locale = {
  code: "en-US",
  formatDistance,
  formatLong: formatLong$1,
  formatRelative,
  localize: localize$1,
  match: match$1,
  options: {
    weekStartsOn: 0,
    firstWeekContainsDate: 1
  }
};
var defaultLocale = locale;
function addMilliseconds(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var timestamp = toDate(dirtyDate).getTime();
  var amount = toInteger(dirtyAmount);
  return new Date(timestamp + amount);
}
function subMilliseconds(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var amount = toInteger(dirtyAmount);
  return addMilliseconds(dirtyDate, -amount);
}
function assign(target, dirtyObject) {
  if (target == null) {
    throw new TypeError("assign requires that input parameter not be null or undefined");
  }
  dirtyObject = dirtyObject || {};
  for (var property in dirtyObject) {
    if (Object.prototype.hasOwnProperty.call(dirtyObject, property)) {
      target[property] = dirtyObject[property];
    }
  }
  return target;
}
function dateLongFormatter(pattern, formatLong2) {
  switch (pattern) {
    case "P":
      return formatLong2.date({
        width: "short"
      });
    case "PP":
      return formatLong2.date({
        width: "medium"
      });
    case "PPP":
      return formatLong2.date({
        width: "long"
      });
    case "PPPP":
    default:
      return formatLong2.date({
        width: "full"
      });
  }
}
function timeLongFormatter(pattern, formatLong2) {
  switch (pattern) {
    case "p":
      return formatLong2.time({
        width: "short"
      });
    case "pp":
      return formatLong2.time({
        width: "medium"
      });
    case "ppp":
      return formatLong2.time({
        width: "long"
      });
    case "pppp":
    default:
      return formatLong2.time({
        width: "full"
      });
  }
}
function dateTimeLongFormatter(pattern, formatLong2) {
  var matchResult = pattern.match(/(P+)(p+)?/);
  var datePattern = matchResult[1];
  var timePattern = matchResult[2];
  if (!timePattern) {
    return dateLongFormatter(pattern, formatLong2);
  }
  var dateTimeFormat;
  switch (datePattern) {
    case "P":
      dateTimeFormat = formatLong2.dateTime({
        width: "short"
      });
      break;
    case "PP":
      dateTimeFormat = formatLong2.dateTime({
        width: "medium"
      });
      break;
    case "PPP":
      dateTimeFormat = formatLong2.dateTime({
        width: "long"
      });
      break;
    case "PPPP":
    default:
      dateTimeFormat = formatLong2.dateTime({
        width: "full"
      });
      break;
  }
  return dateTimeFormat.replace("{{date}}", dateLongFormatter(datePattern, formatLong2)).replace("{{time}}", timeLongFormatter(timePattern, formatLong2));
}
var longFormatters = {
  p: timeLongFormatter,
  P: dateTimeLongFormatter
};
var longFormatters$1 = longFormatters;
function getTimezoneOffsetInMilliseconds(date) {
  var utcDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()));
  utcDate.setUTCFullYear(date.getFullYear());
  return date.getTime() - utcDate.getTime();
}
var protectedDayOfYearTokens = ["D", "DD"];
var protectedWeekYearTokens = ["YY", "YYYY"];
function isProtectedDayOfYearToken(token) {
  return protectedDayOfYearTokens.indexOf(token) !== -1;
}
function isProtectedWeekYearToken(token) {
  return protectedWeekYearTokens.indexOf(token) !== -1;
}
function throwProtectedError(token, format, input) {
  if (token === "YYYY") {
    throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(format, "`) for formatting years to the input `").concat(input, "`; see: https://git.io/fxCyr"));
  } else if (token === "YY") {
    throw new RangeError("Use `yy` instead of `YY` (in `".concat(format, "`) for formatting years to the input `").concat(input, "`; see: https://git.io/fxCyr"));
  } else if (token === "D") {
    throw new RangeError("Use `d` instead of `D` (in `".concat(format, "`) for formatting days of the month to the input `").concat(input, "`; see: https://git.io/fxCyr"));
  } else if (token === "DD") {
    throw new RangeError("Use `dd` instead of `DD` (in `".concat(format, "`) for formatting days of the month to the input `").concat(input, "`; see: https://git.io/fxCyr"));
  }
}
function startOfUTCWeek(dirtyDate, dirtyOptions) {
  requiredArgs(1, arguments);
  var options = dirtyOptions || {};
  var locale2 = options.locale;
  var localeWeekStartsOn = locale2 && locale2.options && locale2.options.weekStartsOn;
  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  }
  var date = toDate(dirtyDate);
  var day = date.getUTCDay();
  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;
  date.setUTCDate(date.getUTCDate() - diff);
  date.setUTCHours(0, 0, 0, 0);
  return date;
}
function getUTCWeekYear(dirtyDate, dirtyOptions) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate, dirtyOptions);
  var year = date.getUTCFullYear();
  var options = dirtyOptions || {};
  var locale2 = options.locale;
  var localeFirstWeekContainsDate = locale2 && locale2.options && locale2.options.firstWeekContainsDate;
  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);
  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate);
  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {
    throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");
  }
  var firstWeekOfNextYear = /* @__PURE__ */ new Date(0);
  firstWeekOfNextYear.setUTCFullYear(year + 1, 0, firstWeekContainsDate);
  firstWeekOfNextYear.setUTCHours(0, 0, 0, 0);
  var startOfNextYear = startOfUTCWeek(firstWeekOfNextYear, dirtyOptions);
  var firstWeekOfThisYear = /* @__PURE__ */ new Date(0);
  firstWeekOfThisYear.setUTCFullYear(year, 0, firstWeekContainsDate);
  firstWeekOfThisYear.setUTCHours(0, 0, 0, 0);
  var startOfThisYear = startOfUTCWeek(firstWeekOfThisYear, dirtyOptions);
  if (date.getTime() >= startOfNextYear.getTime()) {
    return year + 1;
  } else if (date.getTime() >= startOfThisYear.getTime()) {
    return year;
  } else {
    return year - 1;
  }
}
function setUTCDay(dirtyDate, dirtyDay, dirtyOptions) {
  requiredArgs(2, arguments);
  var options = dirtyOptions || {};
  var locale2 = options.locale;
  var localeWeekStartsOn = locale2 && locale2.options && locale2.options.weekStartsOn;
  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  }
  var date = toDate(dirtyDate);
  var day = toInteger(dirtyDay);
  var currentDay = date.getUTCDay();
  var remainder = day % 7;
  var dayIndex = (remainder + 7) % 7;
  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;
  date.setUTCDate(date.getUTCDate() + diff);
  return date;
}
function setUTCISODay(dirtyDate, dirtyDay) {
  requiredArgs(2, arguments);
  var day = toInteger(dirtyDay);
  if (day % 7 === 0) {
    day = day - 7;
  }
  var weekStartsOn = 1;
  var date = toDate(dirtyDate);
  var currentDay = date.getUTCDay();
  var remainder = day % 7;
  var dayIndex = (remainder + 7) % 7;
  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;
  date.setUTCDate(date.getUTCDate() + diff);
  return date;
}
function startOfUTCISOWeek(dirtyDate) {
  requiredArgs(1, arguments);
  var weekStartsOn = 1;
  var date = toDate(dirtyDate);
  var day = date.getUTCDay();
  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;
  date.setUTCDate(date.getUTCDate() - diff);
  date.setUTCHours(0, 0, 0, 0);
  return date;
}
function getUTCISOWeekYear(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var year = date.getUTCFullYear();
  var fourthOfJanuaryOfNextYear = /* @__PURE__ */ new Date(0);
  fourthOfJanuaryOfNextYear.setUTCFullYear(year + 1, 0, 4);
  fourthOfJanuaryOfNextYear.setUTCHours(0, 0, 0, 0);
  var startOfNextYear = startOfUTCISOWeek(fourthOfJanuaryOfNextYear);
  var fourthOfJanuaryOfThisYear = /* @__PURE__ */ new Date(0);
  fourthOfJanuaryOfThisYear.setUTCFullYear(year, 0, 4);
  fourthOfJanuaryOfThisYear.setUTCHours(0, 0, 0, 0);
  var startOfThisYear = startOfUTCISOWeek(fourthOfJanuaryOfThisYear);
  if (date.getTime() >= startOfNextYear.getTime()) {
    return year + 1;
  } else if (date.getTime() >= startOfThisYear.getTime()) {
    return year;
  } else {
    return year - 1;
  }
}
function startOfUTCISOWeekYear(dirtyDate) {
  requiredArgs(1, arguments);
  var year = getUTCISOWeekYear(dirtyDate);
  var fourthOfJanuary = /* @__PURE__ */ new Date(0);
  fourthOfJanuary.setUTCFullYear(year, 0, 4);
  fourthOfJanuary.setUTCHours(0, 0, 0, 0);
  var date = startOfUTCISOWeek(fourthOfJanuary);
  return date;
}
var MILLISECONDS_IN_WEEK$1 = 6048e5;
function getUTCISOWeek(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var diff = startOfUTCISOWeek(date).getTime() - startOfUTCISOWeekYear(date).getTime();
  return Math.round(diff / MILLISECONDS_IN_WEEK$1) + 1;
}
function setUTCISOWeek(dirtyDate, dirtyISOWeek) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var isoWeek = toInteger(dirtyISOWeek);
  var diff = getUTCISOWeek(date) - isoWeek;
  date.setUTCDate(date.getUTCDate() - diff * 7);
  return date;
}
function startOfUTCWeekYear(dirtyDate, dirtyOptions) {
  requiredArgs(1, arguments);
  var options = dirtyOptions || {};
  var locale2 = options.locale;
  var localeFirstWeekContainsDate = locale2 && locale2.options && locale2.options.firstWeekContainsDate;
  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);
  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate);
  var year = getUTCWeekYear(dirtyDate, dirtyOptions);
  var firstWeek = /* @__PURE__ */ new Date(0);
  firstWeek.setUTCFullYear(year, 0, firstWeekContainsDate);
  firstWeek.setUTCHours(0, 0, 0, 0);
  var date = startOfUTCWeek(firstWeek, dirtyOptions);
  return date;
}
var MILLISECONDS_IN_WEEK = 6048e5;
function getUTCWeek(dirtyDate, options) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var diff = startOfUTCWeek(date, options).getTime() - startOfUTCWeekYear(date, options).getTime();
  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;
}
function setUTCWeek(dirtyDate, dirtyWeek, options) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var week = toInteger(dirtyWeek);
  var diff = getUTCWeek(date, options) - week;
  date.setUTCDate(date.getUTCDate() - diff * 7);
  return date;
}
var MILLISECONDS_IN_HOUR = 36e5;
var MILLISECONDS_IN_MINUTE = 6e4;
var MILLISECONDS_IN_SECOND = 1e3;
var numericPatterns = {
  month: /^(1[0-2]|0?\d)/,
  date: /^(3[0-1]|[0-2]?\d)/,
  dayOfYear: /^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,
  week: /^(5[0-3]|[0-4]?\d)/,
  hour23h: /^(2[0-3]|[0-1]?\d)/,
  hour24h: /^(2[0-4]|[0-1]?\d)/,
  hour11h: /^(1[0-1]|0?\d)/,
  hour12h: /^(1[0-2]|0?\d)/,
  minute: /^[0-5]?\d/,
  second: /^[0-5]?\d/,
  singleDigit: /^\d/,
  twoDigits: /^\d{1,2}/,
  threeDigits: /^\d{1,3}/,
  fourDigits: /^\d{1,4}/,
  anyDigitsSigned: /^-?\d+/,
  singleDigitSigned: /^-?\d/,
  twoDigitsSigned: /^-?\d{1,2}/,
  threeDigitsSigned: /^-?\d{1,3}/,
  fourDigitsSigned: /^-?\d{1,4}/
};
var timezonePatterns = {
  basicOptionalMinutes: /^([+-])(\d{2})(\d{2})?|Z/,
  basic: /^([+-])(\d{2})(\d{2})|Z/,
  basicOptionalSeconds: /^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,
  extended: /^([+-])(\d{2}):(\d{2})|Z/,
  extendedOptionalSeconds: /^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/
};
function parseNumericPattern(pattern, string, valueCallback) {
  var matchResult = string.match(pattern);
  if (!matchResult) {
    return null;
  }
  var value = parseInt(matchResult[0], 10);
  return {
    value: valueCallback ? valueCallback(value) : value,
    rest: string.slice(matchResult[0].length)
  };
}
function parseTimezonePattern(pattern, string) {
  var matchResult = string.match(pattern);
  if (!matchResult) {
    return null;
  }
  if (matchResult[0] === "Z") {
    return {
      value: 0,
      rest: string.slice(1)
    };
  }
  var sign = matchResult[1] === "+" ? 1 : -1;
  var hours = matchResult[2] ? parseInt(matchResult[2], 10) : 0;
  var minutes = matchResult[3] ? parseInt(matchResult[3], 10) : 0;
  var seconds = matchResult[5] ? parseInt(matchResult[5], 10) : 0;
  return {
    value: sign * (hours * MILLISECONDS_IN_HOUR + minutes * MILLISECONDS_IN_MINUTE + seconds * MILLISECONDS_IN_SECOND),
    rest: string.slice(matchResult[0].length)
  };
}
function parseAnyDigitsSigned(string, valueCallback) {
  return parseNumericPattern(numericPatterns.anyDigitsSigned, string, valueCallback);
}
function parseNDigits(n, string, valueCallback) {
  switch (n) {
    case 1:
      return parseNumericPattern(numericPatterns.singleDigit, string, valueCallback);
    case 2:
      return parseNumericPattern(numericPatterns.twoDigits, string, valueCallback);
    case 3:
      return parseNumericPattern(numericPatterns.threeDigits, string, valueCallback);
    case 4:
      return parseNumericPattern(numericPatterns.fourDigits, string, valueCallback);
    default:
      return parseNumericPattern(new RegExp("^\\d{1," + n + "}"), string, valueCallback);
  }
}
function parseNDigitsSigned(n, string, valueCallback) {
  switch (n) {
    case 1:
      return parseNumericPattern(numericPatterns.singleDigitSigned, string, valueCallback);
    case 2:
      return parseNumericPattern(numericPatterns.twoDigitsSigned, string, valueCallback);
    case 3:
      return parseNumericPattern(numericPatterns.threeDigitsSigned, string, valueCallback);
    case 4:
      return parseNumericPattern(numericPatterns.fourDigitsSigned, string, valueCallback);
    default:
      return parseNumericPattern(new RegExp("^-?\\d{1," + n + "}"), string, valueCallback);
  }
}
function dayPeriodEnumToHours(enumValue) {
  switch (enumValue) {
    case "morning":
      return 4;
    case "evening":
      return 17;
    case "pm":
    case "noon":
    case "afternoon":
      return 12;
    case "am":
    case "midnight":
    case "night":
    default:
      return 0;
  }
}
function normalizeTwoDigitYear(twoDigitYear, currentYear) {
  var isCommonEra = currentYear > 0;
  var absCurrentYear = isCommonEra ? currentYear : 1 - currentYear;
  var result;
  if (absCurrentYear <= 50) {
    result = twoDigitYear || 100;
  } else {
    var rangeEnd = absCurrentYear + 50;
    var rangeEndCentury = Math.floor(rangeEnd / 100) * 100;
    var isPreviousCentury = twoDigitYear >= rangeEnd % 100;
    result = twoDigitYear + rangeEndCentury - (isPreviousCentury ? 100 : 0);
  }
  return isCommonEra ? result : 1 - result;
}
var DAYS_IN_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
var DAYS_IN_MONTH_LEAP_YEAR = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
function isLeapYearIndex(year) {
  return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;
}
var parsers = {
  G: {
    priority: 140,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "G":
        case "GG":
        case "GGG":
          return match2.era(string, {
            width: "abbreviated"
          }) || match2.era(string, {
            width: "narrow"
          });
        case "GGGGG":
          return match2.era(string, {
            width: "narrow"
          });
        case "GGGG":
        default:
          return match2.era(string, {
            width: "wide"
          }) || match2.era(string, {
            width: "abbreviated"
          }) || match2.era(string, {
            width: "narrow"
          });
      }
    },
    set: function(date, flags, value, _options) {
      flags.era = value;
      date.setUTCFullYear(value, 0, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["R", "u", "t", "T"]
  },
  y: {
    priority: 130,
    parse: function(string, token, match2, _options) {
      var valueCallback = function(year) {
        return {
          year,
          isTwoDigitYear: token === "yy"
        };
      };
      switch (token) {
        case "y":
          return parseNDigits(4, string, valueCallback);
        case "yo":
          return match2.ordinalNumber(string, {
            unit: "year",
            valueCallback
          });
        default:
          return parseNDigits(token.length, string, valueCallback);
      }
    },
    validate: function(_date, value, _options) {
      return value.isTwoDigitYear || value.year > 0;
    },
    set: function(date, flags, value, _options) {
      var currentYear = date.getUTCFullYear();
      if (value.isTwoDigitYear) {
        var normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);
        date.setUTCFullYear(normalizedTwoDigitYear, 0, 1);
        date.setUTCHours(0, 0, 0, 0);
        return date;
      }
      var year = !("era" in flags) || flags.era === 1 ? value.year : 1 - value.year;
      date.setUTCFullYear(year, 0, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "u", "w", "I", "i", "e", "c", "t", "T"]
  },
  Y: {
    priority: 130,
    parse: function(string, token, match2, _options) {
      var valueCallback = function(year) {
        return {
          year,
          isTwoDigitYear: token === "YY"
        };
      };
      switch (token) {
        case "Y":
          return parseNDigits(4, string, valueCallback);
        case "Yo":
          return match2.ordinalNumber(string, {
            unit: "year",
            valueCallback
          });
        default:
          return parseNDigits(token.length, string, valueCallback);
      }
    },
    validate: function(_date, value, _options) {
      return value.isTwoDigitYear || value.year > 0;
    },
    set: function(date, flags, value, options) {
      var currentYear = getUTCWeekYear(date, options);
      if (value.isTwoDigitYear) {
        var normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);
        date.setUTCFullYear(normalizedTwoDigitYear, 0, options.firstWeekContainsDate);
        date.setUTCHours(0, 0, 0, 0);
        return startOfUTCWeek(date, options);
      }
      var year = !("era" in flags) || flags.era === 1 ? value.year : 1 - value.year;
      date.setUTCFullYear(year, 0, options.firstWeekContainsDate);
      date.setUTCHours(0, 0, 0, 0);
      return startOfUTCWeek(date, options);
    },
    incompatibleTokens: ["y", "R", "u", "Q", "q", "M", "L", "I", "d", "D", "i", "t", "T"]
  },
  R: {
    priority: 130,
    parse: function(string, token, _match, _options) {
      if (token === "R") {
        return parseNDigitsSigned(4, string);
      }
      return parseNDigitsSigned(token.length, string);
    },
    set: function(_date, _flags, value, _options) {
      var firstWeekOfYear = /* @__PURE__ */ new Date(0);
      firstWeekOfYear.setUTCFullYear(value, 0, 4);
      firstWeekOfYear.setUTCHours(0, 0, 0, 0);
      return startOfUTCISOWeek(firstWeekOfYear);
    },
    incompatibleTokens: ["G", "y", "Y", "u", "Q", "q", "M", "L", "w", "d", "D", "e", "c", "t", "T"]
  },
  u: {
    priority: 130,
    parse: function(string, token, _match, _options) {
      if (token === "u") {
        return parseNDigitsSigned(4, string);
      }
      return parseNDigitsSigned(token.length, string);
    },
    set: function(date, _flags, value, _options) {
      date.setUTCFullYear(value, 0, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["G", "y", "Y", "R", "w", "I", "i", "e", "c", "t", "T"]
  },
  Q: {
    priority: 120,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "Q":
        case "QQ":
          return parseNDigits(token.length, string);
        case "Qo":
          return match2.ordinalNumber(string, {
            unit: "quarter"
          });
        case "QQQ":
          return match2.quarter(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.quarter(string, {
            width: "narrow",
            context: "formatting"
          });
        case "QQQQQ":
          return match2.quarter(string, {
            width: "narrow",
            context: "formatting"
          });
        case "QQQQ":
        default:
          return match2.quarter(string, {
            width: "wide",
            context: "formatting"
          }) || match2.quarter(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.quarter(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 4;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMonth((value - 1) * 3, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "q", "M", "L", "w", "I", "d", "D", "i", "e", "c", "t", "T"]
  },
  q: {
    priority: 120,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "q":
        case "qq":
          return parseNDigits(token.length, string);
        case "qo":
          return match2.ordinalNumber(string, {
            unit: "quarter"
          });
        case "qqq":
          return match2.quarter(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match2.quarter(string, {
            width: "narrow",
            context: "standalone"
          });
        case "qqqqq":
          return match2.quarter(string, {
            width: "narrow",
            context: "standalone"
          });
        case "qqqq":
        default:
          return match2.quarter(string, {
            width: "wide",
            context: "standalone"
          }) || match2.quarter(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match2.quarter(string, {
            width: "narrow",
            context: "standalone"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 4;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMonth((value - 1) * 3, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "Q", "M", "L", "w", "I", "d", "D", "i", "e", "c", "t", "T"]
  },
  M: {
    priority: 110,
    parse: function(string, token, match2, _options) {
      var valueCallback = function(value) {
        return value - 1;
      };
      switch (token) {
        case "M":
          return parseNumericPattern(numericPatterns.month, string, valueCallback);
        case "MM":
          return parseNDigits(2, string, valueCallback);
        case "Mo":
          return match2.ordinalNumber(string, {
            unit: "month",
            valueCallback
          });
        case "MMM":
          return match2.month(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.month(string, {
            width: "narrow",
            context: "formatting"
          });
        case "MMMMM":
          return match2.month(string, {
            width: "narrow",
            context: "formatting"
          });
        case "MMMM":
        default:
          return match2.month(string, {
            width: "wide",
            context: "formatting"
          }) || match2.month(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.month(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 11;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMonth(value, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "q", "Q", "L", "w", "I", "D", "i", "e", "c", "t", "T"]
  },
  L: {
    priority: 110,
    parse: function(string, token, match2, _options) {
      var valueCallback = function(value) {
        return value - 1;
      };
      switch (token) {
        case "L":
          return parseNumericPattern(numericPatterns.month, string, valueCallback);
        case "LL":
          return parseNDigits(2, string, valueCallback);
        case "Lo":
          return match2.ordinalNumber(string, {
            unit: "month",
            valueCallback
          });
        case "LLL":
          return match2.month(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match2.month(string, {
            width: "narrow",
            context: "standalone"
          });
        case "LLLLL":
          return match2.month(string, {
            width: "narrow",
            context: "standalone"
          });
        case "LLLL":
        default:
          return match2.month(string, {
            width: "wide",
            context: "standalone"
          }) || match2.month(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match2.month(string, {
            width: "narrow",
            context: "standalone"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 11;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMonth(value, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "q", "Q", "M", "w", "I", "D", "i", "e", "c", "t", "T"]
  },
  w: {
    priority: 100,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "w":
          return parseNumericPattern(numericPatterns.week, string);
        case "wo":
          return match2.ordinalNumber(string, {
            unit: "week"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 53;
    },
    set: function(date, _flags, value, options) {
      return startOfUTCWeek(setUTCWeek(date, value, options), options);
    },
    incompatibleTokens: ["y", "R", "u", "q", "Q", "M", "L", "I", "d", "D", "i", "t", "T"]
  },
  I: {
    priority: 100,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "I":
          return parseNumericPattern(numericPatterns.week, string);
        case "Io":
          return match2.ordinalNumber(string, {
            unit: "week"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 53;
    },
    set: function(date, _flags, value, options) {
      return startOfUTCISOWeek(setUTCISOWeek(date, value, options), options);
    },
    incompatibleTokens: ["y", "Y", "u", "q", "Q", "M", "L", "w", "d", "D", "e", "c", "t", "T"]
  },
  d: {
    priority: 90,
    subPriority: 1,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "d":
          return parseNumericPattern(numericPatterns.date, string);
        case "do":
          return match2.ordinalNumber(string, {
            unit: "date"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(date, value, _options) {
      var year = date.getUTCFullYear();
      var isLeapYear = isLeapYearIndex(year);
      var month = date.getUTCMonth();
      if (isLeapYear) {
        return value >= 1 && value <= DAYS_IN_MONTH_LEAP_YEAR[month];
      } else {
        return value >= 1 && value <= DAYS_IN_MONTH[month];
      }
    },
    set: function(date, _flags, value, _options) {
      date.setUTCDate(value);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "q", "Q", "w", "I", "D", "i", "e", "c", "t", "T"]
  },
  D: {
    priority: 90,
    subPriority: 1,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "D":
        case "DD":
          return parseNumericPattern(numericPatterns.dayOfYear, string);
        case "Do":
          return match2.ordinalNumber(string, {
            unit: "date"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(date, value, _options) {
      var year = date.getUTCFullYear();
      var isLeapYear = isLeapYearIndex(year);
      if (isLeapYear) {
        return value >= 1 && value <= 366;
      } else {
        return value >= 1 && value <= 365;
      }
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMonth(0, value);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "q", "Q", "M", "L", "w", "I", "d", "E", "i", "e", "c", "t", "T"]
  },
  E: {
    priority: 90,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "E":
        case "EE":
        case "EEE":
          return match2.day(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.day(string, {
            width: "short",
            context: "formatting"
          }) || match2.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "EEEEE":
          return match2.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "EEEEEE":
          return match2.day(string, {
            width: "short",
            context: "formatting"
          }) || match2.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "EEEE":
        default:
          return match2.day(string, {
            width: "wide",
            context: "formatting"
          }) || match2.day(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.day(string, {
            width: "short",
            context: "formatting"
          }) || match2.day(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 6;
    },
    set: function(date, _flags, value, options) {
      date = setUTCDay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["D", "i", "e", "c", "t", "T"]
  },
  e: {
    priority: 90,
    parse: function(string, token, match2, options) {
      var valueCallback = function(value) {
        var wholeWeekDays = Math.floor((value - 1) / 7) * 7;
        return (value + options.weekStartsOn + 6) % 7 + wholeWeekDays;
      };
      switch (token) {
        case "e":
        case "ee":
          return parseNDigits(token.length, string, valueCallback);
        case "eo":
          return match2.ordinalNumber(string, {
            unit: "day",
            valueCallback
          });
        case "eee":
          return match2.day(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.day(string, {
            width: "short",
            context: "formatting"
          }) || match2.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "eeeee":
          return match2.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "eeeeee":
          return match2.day(string, {
            width: "short",
            context: "formatting"
          }) || match2.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "eeee":
        default:
          return match2.day(string, {
            width: "wide",
            context: "formatting"
          }) || match2.day(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.day(string, {
            width: "short",
            context: "formatting"
          }) || match2.day(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 6;
    },
    set: function(date, _flags, value, options) {
      date = setUTCDay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["y", "R", "u", "q", "Q", "M", "L", "I", "d", "D", "E", "i", "c", "t", "T"]
  },
  c: {
    priority: 90,
    parse: function(string, token, match2, options) {
      var valueCallback = function(value) {
        var wholeWeekDays = Math.floor((value - 1) / 7) * 7;
        return (value + options.weekStartsOn + 6) % 7 + wholeWeekDays;
      };
      switch (token) {
        case "c":
        case "cc":
          return parseNDigits(token.length, string, valueCallback);
        case "co":
          return match2.ordinalNumber(string, {
            unit: "day",
            valueCallback
          });
        case "ccc":
          return match2.day(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match2.day(string, {
            width: "short",
            context: "standalone"
          }) || match2.day(string, {
            width: "narrow",
            context: "standalone"
          });
        case "ccccc":
          return match2.day(string, {
            width: "narrow",
            context: "standalone"
          });
        case "cccccc":
          return match2.day(string, {
            width: "short",
            context: "standalone"
          }) || match2.day(string, {
            width: "narrow",
            context: "standalone"
          });
        case "cccc":
        default:
          return match2.day(string, {
            width: "wide",
            context: "standalone"
          }) || match2.day(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match2.day(string, {
            width: "short",
            context: "standalone"
          }) || match2.day(string, {
            width: "narrow",
            context: "standalone"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 6;
    },
    set: function(date, _flags, value, options) {
      date = setUTCDay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["y", "R", "u", "q", "Q", "M", "L", "I", "d", "D", "E", "i", "e", "t", "T"]
  },
  i: {
    priority: 90,
    parse: function(string, token, match2, _options) {
      var valueCallback = function(value) {
        if (value === 0) {
          return 7;
        }
        return value;
      };
      switch (token) {
        case "i":
        case "ii":
          return parseNDigits(token.length, string);
        case "io":
          return match2.ordinalNumber(string, {
            unit: "day"
          });
        case "iii":
          return match2.day(string, {
            width: "abbreviated",
            context: "formatting",
            valueCallback
          }) || match2.day(string, {
            width: "short",
            context: "formatting",
            valueCallback
          }) || match2.day(string, {
            width: "narrow",
            context: "formatting",
            valueCallback
          });
        case "iiiii":
          return match2.day(string, {
            width: "narrow",
            context: "formatting",
            valueCallback
          });
        case "iiiiii":
          return match2.day(string, {
            width: "short",
            context: "formatting",
            valueCallback
          }) || match2.day(string, {
            width: "narrow",
            context: "formatting",
            valueCallback
          });
        case "iiii":
        default:
          return match2.day(string, {
            width: "wide",
            context: "formatting",
            valueCallback
          }) || match2.day(string, {
            width: "abbreviated",
            context: "formatting",
            valueCallback
          }) || match2.day(string, {
            width: "short",
            context: "formatting",
            valueCallback
          }) || match2.day(string, {
            width: "narrow",
            context: "formatting",
            valueCallback
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 7;
    },
    set: function(date, _flags, value, options) {
      date = setUTCISODay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["y", "Y", "u", "q", "Q", "M", "L", "w", "d", "D", "E", "e", "c", "t", "T"]
  },
  a: {
    priority: 80,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "a":
        case "aa":
        case "aaa":
          return match2.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "aaaaa":
          return match2.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "aaaa":
        default:
          return match2.dayPeriod(string, {
            width: "wide",
            context: "formatting"
          }) || match2.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    set: function(date, _flags, value, _options) {
      date.setUTCHours(dayPeriodEnumToHours(value), 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["b", "B", "H", "K", "k", "t", "T"]
  },
  b: {
    priority: 80,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "b":
        case "bb":
        case "bbb":
          return match2.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "bbbbb":
          return match2.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "bbbb":
        default:
          return match2.dayPeriod(string, {
            width: "wide",
            context: "formatting"
          }) || match2.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    set: function(date, _flags, value, _options) {
      date.setUTCHours(dayPeriodEnumToHours(value), 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["a", "B", "H", "K", "k", "t", "T"]
  },
  B: {
    priority: 80,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "B":
        case "BB":
        case "BBB":
          return match2.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "BBBBB":
          return match2.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "BBBB":
        default:
          return match2.dayPeriod(string, {
            width: "wide",
            context: "formatting"
          }) || match2.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match2.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    set: function(date, _flags, value, _options) {
      date.setUTCHours(dayPeriodEnumToHours(value), 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["a", "b", "t", "T"]
  },
  h: {
    priority: 70,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "h":
          return parseNumericPattern(numericPatterns.hour12h, string);
        case "ho":
          return match2.ordinalNumber(string, {
            unit: "hour"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 12;
    },
    set: function(date, _flags, value, _options) {
      var isPM = date.getUTCHours() >= 12;
      if (isPM && value < 12) {
        date.setUTCHours(value + 12, 0, 0, 0);
      } else if (!isPM && value === 12) {
        date.setUTCHours(0, 0, 0, 0);
      } else {
        date.setUTCHours(value, 0, 0, 0);
      }
      return date;
    },
    incompatibleTokens: ["H", "K", "k", "t", "T"]
  },
  H: {
    priority: 70,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "H":
          return parseNumericPattern(numericPatterns.hour23h, string);
        case "Ho":
          return match2.ordinalNumber(string, {
            unit: "hour"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 23;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCHours(value, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["a", "b", "h", "K", "k", "t", "T"]
  },
  K: {
    priority: 70,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "K":
          return parseNumericPattern(numericPatterns.hour11h, string);
        case "Ko":
          return match2.ordinalNumber(string, {
            unit: "hour"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 11;
    },
    set: function(date, _flags, value, _options) {
      var isPM = date.getUTCHours() >= 12;
      if (isPM && value < 12) {
        date.setUTCHours(value + 12, 0, 0, 0);
      } else {
        date.setUTCHours(value, 0, 0, 0);
      }
      return date;
    },
    incompatibleTokens: ["a", "b", "h", "H", "k", "t", "T"]
  },
  k: {
    priority: 70,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "k":
          return parseNumericPattern(numericPatterns.hour24h, string);
        case "ko":
          return match2.ordinalNumber(string, {
            unit: "hour"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 24;
    },
    set: function(date, _flags, value, _options) {
      var hours = value <= 24 ? value % 24 : value;
      date.setUTCHours(hours, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["a", "b", "h", "H", "K", "t", "T"]
  },
  m: {
    priority: 60,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "m":
          return parseNumericPattern(numericPatterns.minute, string);
        case "mo":
          return match2.ordinalNumber(string, {
            unit: "minute"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 59;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMinutes(value, 0, 0);
      return date;
    },
    incompatibleTokens: ["t", "T"]
  },
  s: {
    priority: 50,
    parse: function(string, token, match2, _options) {
      switch (token) {
        case "s":
          return parseNumericPattern(numericPatterns.second, string);
        case "so":
          return match2.ordinalNumber(string, {
            unit: "second"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 59;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCSeconds(value, 0);
      return date;
    },
    incompatibleTokens: ["t", "T"]
  },
  S: {
    priority: 30,
    parse: function(string, token, _match, _options) {
      var valueCallback = function(value) {
        return Math.floor(value * Math.pow(10, -token.length + 3));
      };
      return parseNDigits(token.length, string, valueCallback);
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMilliseconds(value);
      return date;
    },
    incompatibleTokens: ["t", "T"]
  },
  X: {
    priority: 10,
    parse: function(string, token, _match, _options) {
      switch (token) {
        case "X":
          return parseTimezonePattern(timezonePatterns.basicOptionalMinutes, string);
        case "XX":
          return parseTimezonePattern(timezonePatterns.basic, string);
        case "XXXX":
          return parseTimezonePattern(timezonePatterns.basicOptionalSeconds, string);
        case "XXXXX":
          return parseTimezonePattern(timezonePatterns.extendedOptionalSeconds, string);
        case "XXX":
        default:
          return parseTimezonePattern(timezonePatterns.extended, string);
      }
    },
    set: function(date, flags, value, _options) {
      if (flags.timestampIsSet) {
        return date;
      }
      return new Date(date.getTime() - value);
    },
    incompatibleTokens: ["t", "T", "x"]
  },
  x: {
    priority: 10,
    parse: function(string, token, _match, _options) {
      switch (token) {
        case "x":
          return parseTimezonePattern(timezonePatterns.basicOptionalMinutes, string);
        case "xx":
          return parseTimezonePattern(timezonePatterns.basic, string);
        case "xxxx":
          return parseTimezonePattern(timezonePatterns.basicOptionalSeconds, string);
        case "xxxxx":
          return parseTimezonePattern(timezonePatterns.extendedOptionalSeconds, string);
        case "xxx":
        default:
          return parseTimezonePattern(timezonePatterns.extended, string);
      }
    },
    set: function(date, flags, value, _options) {
      if (flags.timestampIsSet) {
        return date;
      }
      return new Date(date.getTime() - value);
    },
    incompatibleTokens: ["t", "T", "X"]
  },
  t: {
    priority: 40,
    parse: function(string, _token, _match, _options) {
      return parseAnyDigitsSigned(string);
    },
    set: function(_date, _flags, value, _options) {
      return [new Date(value * 1e3), {
        timestampIsSet: true
      }];
    },
    incompatibleTokens: "*"
  },
  T: {
    priority: 20,
    parse: function(string, _token, _match, _options) {
      return parseAnyDigitsSigned(string);
    },
    set: function(_date, _flags, value, _options) {
      return [new Date(value), {
        timestampIsSet: true
      }];
    },
    incompatibleTokens: "*"
  }
};
var parsers$1 = parsers;
var TIMEZONE_UNIT_PRIORITY = 10;
var formattingTokensRegExp = /[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g;
var longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
var escapedStringRegExp = /^'([^]*?)'?$/;
var doubleQuoteRegExp = /''/g;
var notWhitespaceRegExp = /\S/;
var unescapedLatinCharacterRegExp = /[a-zA-Z]/;
function parse(dirtyDateString, dirtyFormatString, dirtyReferenceDate, dirtyOptions) {
  requiredArgs(3, arguments);
  var dateString = String(dirtyDateString);
  var formatString = String(dirtyFormatString);
  var options = dirtyOptions || {};
  var locale2 = options.locale || defaultLocale;
  if (!locale2.match) {
    throw new RangeError("locale must contain match property");
  }
  var localeFirstWeekContainsDate = locale2.options && locale2.options.firstWeekContainsDate;
  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);
  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate);
  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {
    throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");
  }
  var localeWeekStartsOn = locale2.options && locale2.options.weekStartsOn;
  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  }
  if (formatString === "") {
    if (dateString === "") {
      return toDate(dirtyReferenceDate);
    } else {
      return /* @__PURE__ */ new Date(NaN);
    }
  }
  var subFnOptions = {
    firstWeekContainsDate,
    weekStartsOn,
    locale: locale2
  };
  var setters = [{
    priority: TIMEZONE_UNIT_PRIORITY,
    subPriority: -1,
    set: dateToSystemTimezone,
    index: 0
  }];
  var i;
  var tokens = formatString.match(longFormattingTokensRegExp).map(function(substring) {
    var firstCharacter2 = substring[0];
    if (firstCharacter2 === "p" || firstCharacter2 === "P") {
      var longFormatter = longFormatters$1[firstCharacter2];
      return longFormatter(substring, locale2.formatLong, subFnOptions);
    }
    return substring;
  }).join("").match(formattingTokensRegExp);
  var usedTokens = [];
  for (i = 0; i < tokens.length; i++) {
    var token = tokens[i];
    if (!options.useAdditionalWeekYearTokens && isProtectedWeekYearToken(token)) {
      throwProtectedError(token, formatString, dirtyDateString);
    }
    if (!options.useAdditionalDayOfYearTokens && isProtectedDayOfYearToken(token)) {
      throwProtectedError(token, formatString, dirtyDateString);
    }
    var firstCharacter = token[0];
    var parser = parsers$1[firstCharacter];
    if (parser) {
      var incompatibleTokens = parser.incompatibleTokens;
      if (Array.isArray(incompatibleTokens)) {
        var incompatibleToken = void 0;
        for (var _i = 0; _i < usedTokens.length; _i++) {
          var usedToken = usedTokens[_i].token;
          if (incompatibleTokens.indexOf(usedToken) !== -1 || usedToken === firstCharacter) {
            incompatibleToken = usedTokens[_i];
            break;
          }
        }
        if (incompatibleToken) {
          throw new RangeError("The format string mustn't contain `".concat(incompatibleToken.fullToken, "` and `").concat(token, "` at the same time"));
        }
      } else if (parser.incompatibleTokens === "*" && usedTokens.length) {
        throw new RangeError("The format string mustn't contain `".concat(token, "` and any other token at the same time"));
      }
      usedTokens.push({
        token: firstCharacter,
        fullToken: token
      });
      var parseResult = parser.parse(dateString, token, locale2.match, subFnOptions);
      if (!parseResult) {
        return /* @__PURE__ */ new Date(NaN);
      }
      setters.push({
        priority: parser.priority,
        subPriority: parser.subPriority || 0,
        set: parser.set,
        validate: parser.validate,
        value: parseResult.value,
        index: setters.length
      });
      dateString = parseResult.rest;
    } else {
      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {
        throw new RangeError("Format string contains an unescaped latin alphabet character `" + firstCharacter + "`");
      }
      if (token === "''") {
        token = "'";
      } else if (firstCharacter === "'") {
        token = cleanEscapedString(token);
      }
      if (dateString.indexOf(token) === 0) {
        dateString = dateString.slice(token.length);
      } else {
        return /* @__PURE__ */ new Date(NaN);
      }
    }
  }
  if (dateString.length > 0 && notWhitespaceRegExp.test(dateString)) {
    return /* @__PURE__ */ new Date(NaN);
  }
  var uniquePrioritySetters = setters.map(function(setter2) {
    return setter2.priority;
  }).sort(function(a, b) {
    return b - a;
  }).filter(function(priority, index, array) {
    return array.indexOf(priority) === index;
  }).map(function(priority) {
    return setters.filter(function(setter2) {
      return setter2.priority === priority;
    }).sort(function(a, b) {
      return b.subPriority - a.subPriority;
    });
  }).map(function(setterArray) {
    return setterArray[0];
  });
  var date = toDate(dirtyReferenceDate);
  if (isNaN(date)) {
    return /* @__PURE__ */ new Date(NaN);
  }
  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));
  var flags = {};
  for (i = 0; i < uniquePrioritySetters.length; i++) {
    var setter = uniquePrioritySetters[i];
    if (setter.validate && !setter.validate(utcDate, setter.value, subFnOptions)) {
      return /* @__PURE__ */ new Date(NaN);
    }
    var result = setter.set(utcDate, flags, setter.value, subFnOptions);
    if (result[0]) {
      utcDate = result[0];
      assign(flags, result[1]);
    } else {
      utcDate = result;
    }
  }
  return utcDate;
}
function dateToSystemTimezone(date, flags) {
  if (flags.timestampIsSet) {
    return date;
  }
  var convertedDate = /* @__PURE__ */ new Date(0);
  convertedDate.setFullYear(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate());
  convertedDate.setHours(date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds(), date.getUTCMilliseconds());
  return convertedDate;
}
function cleanEscapedString(input) {
  return input.match(escapedStringRegExp)[1].replace(doubleQuoteRegExp, "'");
}
function parseInputValue$1(_ref) {
  let {
    inputValue,
    customParseDate
  } = _ref;
  if (customParseDate) {
    return customParseDate(inputValue);
  }
  const MINIMUM_DATE = new Date(1001, 0, 0);
  let tryParseDate = parse(inputValue, "P", /* @__PURE__ */ new Date());
  if (isValid(tryParseDate) && !isBefore(tryParseDate, MINIMUM_DATE)) {
    return tryParseDate;
  }
  tryParseDate = parse(inputValue, "PP", /* @__PURE__ */ new Date());
  if (isValid(tryParseDate) && !isBefore(tryParseDate, MINIMUM_DATE)) {
    return tryParseDate;
  }
  tryParseDate = parse(inputValue, "PPP", /* @__PURE__ */ new Date());
  if (isValid(tryParseDate) && !isBefore(tryParseDate, MINIMUM_DATE)) {
    return tryParseDate;
  }
  return /* @__PURE__ */ new Date(NaN);
}
function formatInputValue(_ref2) {
  let {
    date,
    locale: locale2,
    formatDate
  } = _ref2;
  if (!date) {
    return "";
  }
  if (formatDate) {
    return formatDate(date);
  }
  return new Intl.DateTimeFormat(locale2, {
    month: "long",
    day: "numeric",
    year: "numeric"
  }).format(date);
}
var datepickerReducer = (_ref3) => {
  let {
    value,
    formatDate,
    locale: locale2,
    customParseDate,
    onChange
  } = _ref3;
  return (state, action) => {
    switch (action.type) {
      case "OPEN":
        return {
          ...state,
          isOpen: true,
          previewDate: value || /* @__PURE__ */ new Date()
        };
      case "CLOSE": {
        const inputValue = formatInputValue({
          date: value,
          locale: locale2,
          formatDate
        });
        return {
          ...state,
          isOpen: false,
          inputValue
        };
      }
      case "PREVIEW_NEXT_MONTH": {
        const previewDate = addMonths(state.previewDate, 1);
        return {
          ...state,
          previewDate
        };
      }
      case "PREVIEW_PREVIOUS_MONTH": {
        const previewDate = subMonths(state.previewDate, 1);
        return {
          ...state,
          previewDate
        };
      }
      case "MANUALLY_UPDATE_INPUT": {
        const inputValue = action.value;
        const currentDate = parseInputValue$1({
          inputValue,
          customParseDate
        });
        if (onChange && currentDate && isValid(currentDate) && !isSameDay(value, currentDate)) {
          onChange(currentDate);
        }
        return {
          ...state,
          isOpen: true,
          inputValue
        };
      }
      case "CONTROLLED_VALUE_CHANGE": {
        const previewDate = action.value || /* @__PURE__ */ new Date();
        const inputValue = formatInputValue({
          date: action.value,
          locale: locale2,
          formatDate
        });
        return {
          ...state,
          previewDate,
          inputValue
        };
      }
      case "CONTROLLED_LOCALE_CHANGE": {
        const inputValue = formatInputValue({
          date: value,
          locale: locale2,
          formatDate
        });
        return {
          ...state,
          inputValue
        };
      }
      case "SELECT_DATE": {
        const inputValue = formatInputValue({
          date: action.value,
          locale: locale2,
          formatDate
        });
        if (onChange && action.value && isValid(action.value) && !isSameDay(value, action.value)) {
          onChange(action.value);
        }
        return {
          ...state,
          isOpen: false,
          inputValue
        };
      }
      default:
        throw new Error();
    }
  };
};
function retrieveInitialState$1(initialProps) {
  let previewDate = initialProps.value;
  if (previewDate === void 0 || !isValid(previewDate)) {
    previewDate = /* @__PURE__ */ new Date();
  }
  let inputValue = "";
  if (initialProps.value !== void 0) {
    if (initialProps.formatDate) {
      inputValue = initialProps.formatDate(initialProps.value);
    } else {
      inputValue = new Intl.DateTimeFormat(initialProps.locale, {
        month: "long",
        day: "numeric",
        year: "numeric"
      }).format(previewDate);
    }
  }
  return {
    isOpen: false,
    previewDate,
    inputValue
  };
}
var Datepicker = (0, import_react.forwardRef)((props, calendarRef) => {
  const {
    children,
    placement,
    popperModifiers,
    eventsEnabled,
    zIndex,
    isAnimated,
    refKey,
    value,
    isCompact,
    onChange,
    formatDate,
    minValue,
    maxValue,
    locale: locale2,
    weekStartsOn,
    customParseDate,
    ...menuProps
  } = props;
  const theme = (0, import_react.useContext)(Me);
  const memoizedReducer = (0, import_react.useCallback)(datepickerReducer({
    value,
    formatDate,
    locale: locale2,
    customParseDate,
    onChange
  }), [value, formatDate, locale2, onChange, customParseDate]);
  const [state, dispatch] = (0, import_react.useReducer)(memoizedReducer, retrieveInitialState$1(props));
  const scheduleUpdateRef = (0, import_react.useRef)(void 0);
  const inputRef = (0, import_react.useRef)(null);
  const isInputMouseDownRef = (0, import_react.useRef)(false);
  (0, import_react.useEffect)(() => {
    if (state.isOpen && scheduleUpdateRef.current) {
      scheduleUpdateRef.current();
    }
  });
  const [isVisible, setIsVisible] = (0, import_react.useState)(state.isOpen);
  (0, import_react.useEffect)(() => {
    let timeout;
    if (state.isOpen) {
      setIsVisible(true);
    } else if (isAnimated) {
      timeout = setTimeout(() => setIsVisible(false), 200);
    } else {
      setIsVisible(false);
    }
    return () => clearTimeout(timeout);
  }, [state.isOpen, isAnimated]);
  (0, import_react.useEffect)(() => {
    dispatch({
      type: "CONTROLLED_VALUE_CHANGE",
      value
    });
  }, [value]);
  (0, import_react.useEffect)(() => {
    dispatch({
      type: "CONTROLLED_LOCALE_CHANGE"
    });
  }, [locale2]);
  const popperPlacement = theme.rtl ? getRtlPopperPlacement(placement) : getPopperPlacement(placement);
  const contextValue = (0, import_react.useMemo)(() => ({
    state,
    dispatch
  }), [state, dispatch]);
  return import_react.default.createElement(DatepickerContext.Provider, {
    value: contextValue
  }, import_react.default.createElement(Manager, null, import_react.default.createElement(Reference, null, (_ref) => {
    let {
      ref
    } = _ref;
    const childElement = import_react.default.Children.only(children);
    return import_react.default.cloneElement(childElement, {
      [refKey]: (refValue) => {
        ref(refValue);
        inputRef.current = refValue;
      },
      onMouseDown: composeEventHandlers(childElement.props.onMouseDown, () => {
        isInputMouseDownRef.current = true;
      }),
      onMouseUp: composeEventHandlers(childElement.props.onMouseUp, () => {
        setTimeout(() => {
          isInputMouseDownRef.current = false;
        }, 0);
      }),
      onClick: composeEventHandlers(childElement.props.onClick, () => {
        if (isInputMouseDownRef.current && !state.isOpen) {
          dispatch({
            type: "OPEN"
          });
        }
      }),
      onBlur: composeEventHandlers(childElement.props.onBlur, () => {
        dispatch({
          type: "CLOSE"
        });
      }),
      onChange: composeEventHandlers(childElement.props.onChange, (e) => {
        dispatch({
          type: "MANUALLY_UPDATE_INPUT",
          value: e.target.value
        });
      }),
      onKeyDown: composeEventHandlers(childElement.props.onKeyDown, (e) => {
        switch (e.keyCode) {
          case KEY_CODES.ESCAPE:
          case KEY_CODES.ENTER:
            dispatch({
              type: "CLOSE"
            });
            break;
          case KEY_CODES.UP:
          case KEY_CODES.DOWN:
          case KEY_CODES.SPACE:
            dispatch({
              type: "OPEN"
            });
            break;
        }
      }),
      autoComplete: "off",
      value: state.inputValue
    });
  }), import_react.default.createElement(Popper, {
    placement: popperPlacement,
    modifiers: popperModifiers,
    eventsEnabled: state.isOpen && eventsEnabled
  }, (_ref2) => {
    let {
      ref,
      style,
      scheduleUpdate,
      placement: currentPlacement
    } = _ref2;
    scheduleUpdateRef.current = scheduleUpdate;
    return import_react.default.createElement(StyledMenuWrapper, {
      ref,
      style,
      isHidden: !state.isOpen,
      isAnimated: isAnimated && (state.isOpen || isVisible),
      placement: currentPlacement,
      zIndex
    }, (state.isOpen || isVisible) && import_react.default.createElement(StyledMenu, menuProps, import_react.default.createElement(Calendar$1, {
      ref: calendarRef,
      isCompact,
      value,
      minValue,
      maxValue,
      locale: locale2,
      weekStartsOn
    })));
  })));
});
Datepicker.displayName = "Datepicker";
Datepicker.propTypes = {
  value: import_prop_types.default.any,
  onChange: import_prop_types.default.any,
  formatDate: import_prop_types.default.func,
  locale: import_prop_types.default.any,
  weekStartsOn: import_prop_types.default.oneOf(WEEK_STARTS_ON),
  minValue: import_prop_types.default.any,
  maxValue: import_prop_types.default.any,
  isCompact: import_prop_types.default.bool,
  customParseDate: import_prop_types.default.any,
  refKey: import_prop_types.default.string,
  placement: import_prop_types.default.oneOf(PLACEMENT),
  popperModifiers: import_prop_types.default.any,
  isAnimated: import_prop_types.default.bool,
  eventsEnabled: import_prop_types.default.bool,
  zIndex: import_prop_types.default.number
};
Datepicker.defaultProps = {
  placement: "bottom-start",
  refKey: "ref",
  isAnimated: true,
  eventsEnabled: true,
  zIndex: 1e3,
  locale: "en-US"
};
function compareAsc(dirtyDateLeft, dirtyDateRight) {
  requiredArgs(2, arguments);
  var dateLeft = toDate(dirtyDateLeft);
  var dateRight = toDate(dirtyDateRight);
  var diff = dateLeft.getTime() - dateRight.getTime();
  if (diff < 0) {
    return -1;
  } else if (diff > 0) {
    return 1;
  } else {
    return diff;
  }
}
function formatValue(_ref) {
  let {
    value,
    locale: locale2,
    formatDate
  } = _ref;
  let stringValue = "";
  if (value !== void 0 && isValid(value)) {
    if (formatDate) {
      stringValue = formatDate(value);
    } else {
      stringValue = new Intl.DateTimeFormat(locale2, {
        month: "long",
        day: "numeric",
        year: "numeric"
      }).format(value);
    }
  }
  return stringValue;
}
function parseInputValue(_ref2) {
  let {
    inputValue
  } = _ref2;
  const MINIMUM_DATE = new Date(1001, 0, 0);
  let tryParseDate = parse(inputValue || "", "P", /* @__PURE__ */ new Date());
  if (isValid(tryParseDate) && !isBefore(tryParseDate, MINIMUM_DATE)) {
    return tryParseDate;
  }
  tryParseDate = parse(inputValue || "", "PP", /* @__PURE__ */ new Date());
  if (isValid(tryParseDate) && !isBefore(tryParseDate, MINIMUM_DATE)) {
    return tryParseDate;
  }
  tryParseDate = parse(inputValue || "", "PPP", /* @__PURE__ */ new Date());
  if (isValid(tryParseDate) && !isBefore(tryParseDate, MINIMUM_DATE)) {
    return tryParseDate;
  }
  return /* @__PURE__ */ new Date(NaN);
}
var datepickerRangeReducer = (_ref3) => {
  let {
    startValue,
    endValue,
    locale: locale2,
    formatDate,
    customParseDate
  } = _ref3;
  return (state, action) => {
    switch (action.type) {
      case "START_FOCUS": {
        let previewDate = state.previewDate;
        if (startValue) {
          if (compareAsc(startValue, startOfMonth(state.previewDate)) === 1 && compareAsc(startValue, addMonths(endOfMonth(state.previewDate), 1)) === -1) {
            previewDate = state.previewDate;
          } else {
            previewDate = startOfMonth(startValue);
          }
        }
        return {
          ...state,
          previewDate,
          isStartFocused: true,
          isEndFocused: false
        };
      }
      case "END_FOCUS": {
        let previewDate = state.previewDate;
        if (endValue) {
          if (compareAsc(endValue, startOfMonth(state.previewDate)) === 1 && compareAsc(endValue, addMonths(endOfMonth(state.previewDate), 1)) === -1) {
            previewDate = state.previewDate;
          } else {
            previewDate = startOfMonth(endValue);
          }
        }
        return {
          ...state,
          previewDate,
          isEndFocused: true,
          isStartFocused: false
        };
      }
      case "START_BLUR": {
        let parsedDate;
        if (customParseDate) {
          parsedDate = customParseDate(state.startInputValue);
        } else {
          parsedDate = parseInputValue({
            inputValue: state.startInputValue
          });
        }
        const startInputValue = formatValue({
          value: parsedDate,
          locale: locale2,
          formatDate
        });
        return {
          ...state,
          startInputValue: startInputValue || formatValue({
            value: startValue,
            locale: locale2,
            formatDate
          }),
          isStartFocused: false
        };
      }
      case "END_BLUR": {
        let parsedDate;
        if (customParseDate) {
          parsedDate = customParseDate(state.endInputValue);
        } else {
          parsedDate = parseInputValue({
            inputValue: state.endInputValue
          });
        }
        const endInputValue = formatValue({
          value: parsedDate,
          locale: locale2,
          formatDate
        }) || formatValue({
          value: endValue,
          locale: locale2,
          formatDate
        });
        return {
          ...state,
          endInputValue,
          isEndFocused: false
        };
      }
      case "CONTROLLED_START_VALUE_CHANGE": {
        const startInputValue = formatValue({
          value: action.value,
          locale: locale2,
          formatDate
        });
        let previewDate = state.previewDate;
        if (action.value) {
          if (compareAsc(action.value, startOfMonth(state.previewDate)) === 1 && compareAsc(action.value, addMonths(endOfMonth(state.previewDate), 1)) === -1) {
            previewDate = state.previewDate;
          } else {
            previewDate = startOfMonth(action.value);
          }
        }
        return {
          ...state,
          startInputValue,
          hoverDate: void 0,
          previewDate
        };
      }
      case "CONTROLLED_END_VALUE_CHANGE": {
        const endInputValue = formatValue({
          value: action.value,
          locale: locale2,
          formatDate
        });
        let previewDate = state.previewDate;
        if (action.value) {
          if (compareAsc(action.value, startOfMonth(state.previewDate)) === 1 && compareAsc(action.value, addMonths(endOfMonth(state.previewDate), 1)) === -1) {
            previewDate = state.previewDate;
          } else {
            previewDate = startOfMonth(action.value);
          }
        }
        return {
          ...state,
          endInputValue,
          hoverDate: void 0,
          previewDate
        };
      }
      case "CLICK_DATE":
        if (state.isStartFocused) {
          if (endValue !== void 0 && (isBefore(action.value, endValue) || isSameDay(action.value, endValue))) {
            return {
              ...state,
              startInputValue: formatValue({
                value: action.value
              })
            };
          }
          return {
            ...state,
            startInputValue: formatValue({
              value: action.value
            }),
            endInputValue: void 0
          };
        } else if (state.isEndFocused) {
          if (startValue !== void 0 && (isAfter(action.value, startValue) || isSameDay(action.value, startValue))) {
            return {
              ...state,
              endInputValue: formatValue({
                value: action.value
              })
            };
          }
          return {
            ...state,
            startInputValue: formatValue({
              value: action.value
            })
          };
        } else if (startValue === void 0) {
          return {
            ...state,
            startInputValue: formatValue({
              value: action.value
            }),
            endInputValue: void 0
          };
        } else if (endValue === void 0) {
          if (isBefore(action.value, startValue)) {
            return {
              ...state,
              startInputValue: formatValue({
                value: action.value
              }),
              endInputValue: void 0
            };
          }
          return {
            ...state,
            endInputValue: formatValue({
              value: action.value
            })
          };
        }
        return state;
      case "START_INPUT_ONCHANGE": {
        return {
          ...state,
          startInputValue: action.value
        };
      }
      case "END_INPUT_ONCHANGE": {
        return {
          ...state,
          endInputValue: action.value
        };
      }
      case "HOVER_DATE":
        return {
          ...state,
          hoverDate: action.value
        };
      case "PREVIEW_NEXT_MONTH": {
        const previewDate = addMonths(state.previewDate, 1);
        return {
          ...state,
          previewDate,
          hoverDate: void 0
        };
      }
      case "PREVIEW_PREVIOUS_MONTH": {
        const previewDate = subMonths(state.previewDate, 1);
        return {
          ...state,
          previewDate,
          hoverDate: void 0
        };
      }
      default:
        throw new Error();
    }
  };
};
function retrieveInitialState(initialProps) {
  let previewDate = initialProps.startValue;
  if (previewDate === void 0 || !isValid(previewDate)) {
    previewDate = /* @__PURE__ */ new Date();
  }
  const startInputValue = formatValue({
    value: initialProps.startValue,
    locale: initialProps.locale,
    formatDate: initialProps.formatDate
  });
  const endInputValue = formatValue({
    value: initialProps.endValue,
    locale: initialProps.locale,
    formatDate: initialProps.formatDate
  });
  return {
    previewDate,
    startInputValue,
    endInputValue,
    isStartFocused: false,
    isEndFocused: false
  };
}
var DatepickerRangeContext = (0, import_react.createContext)(void 0);
var useDatepickerContext = () => {
  return (0, import_react.useContext)(DatepickerRangeContext);
};
var Start = (props) => {
  const {
    state,
    dispatch,
    onChange,
    startValue,
    endValue,
    startInputRef,
    customParseDate
  } = useDatepickerContext();
  const onChangeCallback = (0, import_react.useCallback)((e) => {
    dispatch({
      type: "START_INPUT_ONCHANGE",
      value: e.target.value
    });
    props.children.props.onChange && props.children.props.onChange(e);
  }, [dispatch, props.children]);
  const onFocusCallback = (0, import_react.useCallback)((e) => {
    dispatch({
      type: "START_FOCUS"
    });
    props.children.props.onFocus && props.children.props.onFocus(e);
  }, [dispatch, props.children]);
  const handleBlur = (0, import_react.useCallback)(() => {
    let parsedDate;
    if (customParseDate) {
      parsedDate = customParseDate(state.startInputValue);
    } else {
      parsedDate = parseInputValue({
        inputValue: state.startInputValue
      });
    }
    dispatch({
      type: "START_BLUR"
    });
    if (parsedDate && isValid(parsedDate) && !isSameDay(parsedDate, startValue)) {
      onChange && onChange({
        startValue: parsedDate,
        endValue
      });
    }
  }, [dispatch, onChange, startValue, endValue, customParseDate, state.startInputValue]);
  const onKeyDownCallback = (0, import_react.useCallback)((e) => {
    if (e.keyCode === KEY_CODES.ENTER) {
      e.preventDefault();
      handleBlur();
    }
    props.children.props.onKeyDown && props.children.props.onKeyDown(e);
  }, [handleBlur, props.children]);
  const onBlurCallback = (0, import_react.useCallback)((e) => {
    handleBlur();
    props.children.props.onBlur && props.children.props.onBlur(e);
  }, [handleBlur, props.children]);
  const childElement = import_react.default.Children.only(props.children);
  return import_react.default.cloneElement(childElement, {
    value: state.startInputValue || "",
    ref: startInputRef,
    onChange: composeEventHandlers(childElement.props.onChange, onChangeCallback),
    onFocus: composeEventHandlers(childElement.props.onFocus, onFocusCallback),
    onKeyDown: composeEventHandlers(childElement.props.onKeyDown, onKeyDownCallback),
    onBlur: composeEventHandlers(childElement.props.onBlur, onBlurCallback)
  });
};
Start.displayName = "DatepickerRange.Start";
var End = (props) => {
  const {
    state,
    dispatch,
    onChange,
    startValue,
    endValue,
    endInputRef,
    customParseDate
  } = useDatepickerContext();
  const onChangeCallback = (0, import_react.useCallback)((e) => {
    dispatch({
      type: "END_INPUT_ONCHANGE",
      value: e.target.value
    });
    props.children.props.onChange && props.children.props.onChange(e);
  }, [dispatch, props.children]);
  const onFocusCallback = (0, import_react.useCallback)((e) => {
    dispatch({
      type: "END_FOCUS"
    });
    props.children.props.onFocus && props.children.props.onFocus(e);
  }, [dispatch, props.children]);
  const handleBlur = (0, import_react.useCallback)(() => {
    dispatch({
      type: "END_BLUR"
    });
    let parsedDate;
    if (customParseDate) {
      parsedDate = customParseDate(state.endInputValue);
    } else {
      parsedDate = parseInputValue({
        inputValue: state.endInputValue
      });
    }
    if (onChange && parsedDate && isValid(parsedDate) && !isSameDay(parsedDate, endValue)) {
      onChange && onChange({
        startValue,
        endValue: parsedDate
      });
    }
  }, [dispatch, onChange, startValue, endValue, customParseDate, state.endInputValue]);
  const onKeydownCallback = (0, import_react.useCallback)((e) => {
    if (e.keyCode === KEY_CODES.ENTER) {
      handleBlur();
      e.preventDefault();
    }
    props.children.props.onKeyDown && props.children.props.onKeyDown(e);
  }, [handleBlur, props.children]);
  const onBlurCallback = (0, import_react.useCallback)((e) => {
    handleBlur();
    props.children.props.onBlur && props.children.props.onBlur(e);
  }, [handleBlur, props.children]);
  const childElement = import_react.default.Children.only(props.children);
  return import_react.default.cloneElement(childElement, {
    value: state.endInputValue || "",
    ref: endInputRef,
    onChange: composeEventHandlers(childElement.props.onChange, onChangeCallback),
    onFocus: composeEventHandlers(childElement.props.onFocus, onFocusCallback),
    onKeyDown: composeEventHandlers(childElement.props.onKeyDown, onKeydownCallback),
    onBlur: composeEventHandlers(childElement.props.onBlur, onBlurCallback)
  });
};
End.displayName = "DatepickerRange.End";
function _extends2() {
  _extends2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends2.apply(this, arguments);
}
function subDays(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var amount = toInteger(dirtyAmount);
  return addDays(dirtyDate, -amount);
}
var Month = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    displayDate,
    isPreviousHidden,
    isNextHidden
  } = _ref;
  const {
    state,
    dispatch,
    locale: locale2,
    weekStartsOn,
    isCompact,
    minValue,
    maxValue,
    startValue,
    endValue,
    onChange
  } = useDatepickerContext();
  const headerLabelFormatter = (0, import_react.useCallback)((date) => {
    const formatter = new Intl.DateTimeFormat(locale2, {
      month: "long",
      year: "numeric"
    });
    return formatter.format(date);
  }, [locale2]);
  const dayLabelFormatter = (0, import_react.useCallback)((date) => {
    const formatter = new Intl.DateTimeFormat(locale2, {
      weekday: "short"
    });
    return formatter.format(date);
  }, [locale2]);
  const dayFormatter = (0, import_react.useCallback)((date) => {
    const formatter = new Intl.DateTimeFormat(locale2, {
      day: "numeric"
    });
    return formatter.format(date);
  }, [locale2]);
  const preferredWeekStartsOn = weekStartsOn || getStartOfWeek(locale2);
  const monthStartDate = startOfMonth(displayDate);
  const monthEndDate = endOfMonth(monthStartDate);
  const startDate = startOfWeek(monthStartDate, {
    weekStartsOn: preferredWeekStartsOn
  });
  const endDate = endOfWeek(monthEndDate, {
    weekStartsOn: preferredWeekStartsOn
  });
  const dayLabels = eachDayOfInterval({
    start: startDate,
    end: addDays(startDate, 6)
  }).map((date) => {
    const formattedDayLabel = dayLabelFormatter(date);
    return import_react.default.createElement(StyledCalendarItem, {
      key: `day-label-${formattedDayLabel}`,
      isCompact
    }, import_react.default.createElement(StyledDayLabel, {
      isCompact
    }, formattedDayLabel));
  });
  const items = eachDayOfInterval({
    start: startDate,
    end: endDate
  }).map((date, itemsIndex) => {
    const formattedDayLabel = dayFormatter(date);
    const isCurrentDate = isToday(date);
    const isPreviousMonth = !isSameMonth(date, displayDate);
    if (isPreviousMonth) {
      return import_react.default.createElement(StyledCalendarItem, {
        key: `day-${itemsIndex}`,
        isCompact
      }, import_react.default.createElement(StyledDay, {
        isCompact,
        isPreviousMonth: true,
        isDisabled: true
      }, " "));
    }
    let isSelected = false;
    if (startValue !== void 0) {
      isSelected = isSameDay(date, startValue);
    }
    if (endValue !== void 0) {
      isSelected = isSelected || isSameDay(date, endValue);
    }
    let isDisabled = false;
    if (minValue !== void 0) {
      isDisabled = isBefore(date, minValue) && !isSameDay(date, minValue);
    }
    if (maxValue !== void 0) {
      isDisabled = isDisabled || isAfter(date, maxValue) && !isSameDay(date, maxValue);
    }
    let isHighlighted = false;
    if (startValue !== void 0 && endValue !== void 0) {
      isHighlighted = (isAfter(date, startValue) || isSameDay(date, startValue)) && (isBefore(date, endValue) || isSameDay(date, endValue)) && !isSameDay(startValue, endValue);
    } else if (startValue !== void 0 && state.hoverDate !== void 0) {
      isHighlighted = (isAfter(date, startValue) || isSameDay(date, startValue)) && (isBefore(date, state.hoverDate) || isSameDay(date, state.hoverDate));
    }
    const isHighlightStart = isHighlighted && startValue && isSameDay(date, startValue) || false;
    const isHighlightEnd = isHighlighted && endValue && isSameDay(date, endValue) || state.hoverDate && isSameDay(date, state.hoverDate) && !isBefore(date, endValue) || false;
    let isInvalidDateRange = endValue && startValue && compareAsc(endValue, startValue) === -1 || false;
    if (minValue) {
      if (startValue) {
        isInvalidDateRange = isInvalidDateRange || compareAsc(startValue, subDays(minValue, 1)) === -1;
      }
      if (endValue) {
        isInvalidDateRange = isInvalidDateRange || compareAsc(endValue, subDays(minValue, 1)) === -1;
      }
    }
    if (maxValue) {
      if (startValue) {
        isInvalidDateRange = isInvalidDateRange || compareAsc(startValue, maxValue) === 1;
      }
      if (endValue) {
        isInvalidDateRange = isInvalidDateRange || compareAsc(endValue, maxValue) === 1;
      }
    }
    return import_react.default.createElement(StyledCalendarItem, {
      key: `day-${itemsIndex}`,
      isCompact
    }, import_react.default.createElement(StyledHighlight, {
      isHighlighted: !isInvalidDateRange && isHighlighted && !isDisabled,
      isStart: !isInvalidDateRange && isHighlightStart,
      isEnd: !isInvalidDateRange && isHighlightEnd
    }), import_react.default.createElement(StyledDay, {
      isToday: isCurrentDate,
      isPreviousMonth,
      isSelected: !isInvalidDateRange && isSelected,
      isDisabled,
      isCompact,
      onClick: () => {
        if (!isDisabled) {
          dispatch({
            type: "CLICK_DATE",
            value: date
          });
          if (onChange) {
            if (state.isStartFocused) {
              if (endValue !== void 0 && (isBefore(date, endValue) || isSameDay(date, endValue))) {
                onChange({
                  startValue: date,
                  endValue
                });
              } else {
                onChange({
                  startValue: date,
                  endValue: void 0
                });
              }
            } else if (state.isEndFocused) {
              if (startValue !== void 0 && (isAfter(date, startValue) || isSameDay(date, startValue))) {
                onChange({
                  startValue,
                  endValue: date
                });
              } else {
                onChange({
                  startValue: date,
                  endValue: void 0
                });
              }
            } else if (startValue === void 0) {
              onChange({
                startValue: date,
                endValue: void 0
              });
            } else if (endValue === void 0) {
              if (isBefore(date, startValue)) {
                onChange({
                  startValue: date,
                  endValue: void 0
                });
              } else {
                onChange({
                  startValue,
                  endValue: date
                });
              }
            } else {
              onChange({
                startValue: date,
                endValue: void 0
              });
            }
          }
        }
      },
      onMouseEnter: () => {
        if (!isSelected) {
          dispatch({
            type: "HOVER_DATE",
            value: date
          });
        }
      }
    }, formattedDayLabel));
  });
  return import_react.default.createElement(StyledDatepicker, {
    ref,
    isCompact,
    onMouseDown: (e) => {
      e.preventDefault();
    }
  }, import_react.default.createElement(StyledHeader, {
    isCompact
  }, import_react.default.createElement(StyledHeaderPaddle, {
    isCompact,
    onClick: () => {
      dispatch({
        type: "PREVIEW_PREVIOUS_MONTH"
      });
    },
    isHidden: isPreviousHidden
  }, import_react.default.createElement(SvgChevronLeftStroke, null)), import_react.default.createElement(StyledHeaderLabel, {
    isCompact
  }, headerLabelFormatter(displayDate)), import_react.default.createElement(StyledHeaderPaddle, {
    isCompact,
    isHidden: isNextHidden,
    onClick: () => {
      dispatch({
        type: "PREVIEW_NEXT_MONTH"
      });
    }
  }, import_react.default.createElement(SvgChevronRightStroke, null))), import_react.default.createElement(StyledCalendar, {
    isCompact,
    onMouseLeave: () => {
      dispatch({
        type: "HOVER_DATE",
        value: void 0
      });
    }
  }, dayLabels, items));
});
Month.displayName = "Month";
var Calendar = (0, import_react.forwardRef)((props, ref) => {
  const {
    state
  } = useDatepickerContext();
  return import_react.default.createElement(StyledRangeCalendar, _extends2({
    ref,
    "data-garden-id": "datepickers.range",
    "data-garden-version": "8.68.0"
  }, props), import_react.default.createElement(Month, {
    displayDate: state.previewDate,
    isNextHidden: true
  }), import_react.default.createElement(Month, {
    displayDate: addMonths(state.previewDate, 1),
    isPreviousHidden: true
  }));
});
Calendar.displayName = "DatepickerRange.Calendar";
var DatepickerRangeComponent = (props) => {
  const {
    startValue,
    locale: locale2,
    weekStartsOn,
    formatDate,
    endValue,
    onChange,
    customParseDate,
    isCompact,
    minValue,
    maxValue,
    children
  } = props;
  const reducer = (0, import_react.useCallback)(datepickerRangeReducer({
    startValue,
    locale: locale2,
    formatDate,
    endValue,
    customParseDate
  }), [startValue, endValue, locale2, formatDate, onChange, customParseDate]);
  const [state, dispatch] = (0, import_react.useReducer)(reducer, retrieveInitialState(props));
  const previousStartValue = (0, import_react.useRef)(startValue);
  const previousEndValue = (0, import_react.useRef)(endValue);
  const startInputRef = (0, import_react.useRef)();
  const endInputRef = (0, import_react.useRef)();
  (0, import_react.useEffect)(() => {
    dispatch({
      type: "CONTROLLED_START_VALUE_CHANGE",
      value: startValue
    });
    if (endInputRef.current && previousStartValue.current !== startValue && startValue !== void 0) {
      endInputRef.current.focus();
    }
    previousStartValue.current = startValue;
  }, [props, startValue]);
  (0, import_react.useEffect)(() => {
    dispatch({
      type: "CONTROLLED_END_VALUE_CHANGE",
      value: endValue
    });
    if (startInputRef.current && previousEndValue.current !== endValue && endValue !== void 0) {
      startInputRef.current.focus();
    }
    previousEndValue.current = endValue;
  }, [props, endValue]);
  const value = (0, import_react.useMemo)(() => ({
    state,
    dispatch,
    isCompact,
    locale: locale2,
    weekStartsOn,
    minValue,
    maxValue,
    startValue,
    endValue,
    onChange,
    startInputRef,
    endInputRef,
    customParseDate
  }), [state, dispatch, isCompact, locale2, weekStartsOn, minValue, maxValue, startValue, endValue, onChange, startInputRef, endInputRef, customParseDate]);
  return import_react.default.createElement(DatepickerRangeContext.Provider, {
    value
  }, children);
};
DatepickerRangeComponent.propTypes = {
  locale: import_prop_types.default.string,
  weekStartsOn: import_prop_types.default.number,
  startValue: import_prop_types.default.instanceOf(Date),
  endValue: import_prop_types.default.instanceOf(Date),
  minValue: import_prop_types.default.instanceOf(Date),
  maxValue: import_prop_types.default.instanceOf(Date),
  onChange: import_prop_types.default.func,
  formatDate: import_prop_types.default.func,
  customParseDate: import_prop_types.default.func,
  isCompact: import_prop_types.default.bool
};
DatepickerRangeComponent.defaultProps = {
  locale: "en-US",
  isCompact: false
};
var DatepickerRange = DatepickerRangeComponent;
DatepickerRange.Calendar = Calendar;
DatepickerRange.End = End;
DatepickerRange.Start = Start;
export {
  Datepicker,
  DatepickerRange
};
//# sourceMappingURL=@zendeskgarden_react-datepickers.js.map
