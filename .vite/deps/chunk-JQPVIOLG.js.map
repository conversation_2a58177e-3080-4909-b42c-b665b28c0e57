{"version": 3, "sources": ["../../node_modules/react-uid/dist/es2015/uid.js", "../../node_modules/react-uid/dist/es2015/hooks.js", "../../node_modules/react-uid/dist/es2015/context.js", "../../node_modules/react-uid/dist/es2015/Control.js", "../../node_modules/react-uid/node_modules/tslib/tslib.es6.js", "../../node_modules/react-uid/dist/es2015/UIDComponent.js"], "sourcesContent": ["/**\n * generates a UID factory\n * @internal\n * @example\n * const uid = generateUID();\n * uid(object) = 1;\n * uid(object) = 1;\n * uid(anotherObject) = 2;\n */\nexport var generateUID = function () {\n    var counter = 1;\n    var map = new WeakMap();\n    /**\n     * @borrows {uid}\n     */\n    var uid = function (item, index) {\n        if (typeof item === 'number' ||\n            typeof item === 'string') {\n            return index ? \"idx-\" + index : \"val-\" + item;\n        }\n        if (!map.has(item)) {\n            map.set(item, counter++);\n            return uid(item);\n        }\n        return 'uid' + map.get(item);\n    };\n    return uid;\n};\n/**\n * @name uid\n * returns an UID associated with {item}\n * @param {Object} item - object to generate UID for\n * @param {Number} index, a fallback index\n * @example\n * uid(object) == 1;\n * uid(object) == 1;\n * uid(anotherObject) == 2;\n * uid(\"not object\", 42) == 42\n *\n * @see {@link useUID}\n */\nexport var uid = generateUID();\n", "import * as React from \"react\";\nimport { counter, getId, getPrefix, source } from \"./context\";\nvar generateUID = function (context) {\n    var quartz = context || counter;\n    var prefix = getPrefix(quartz);\n    var id = getId(quartz);\n    var uid = prefix + id;\n    var gen = function (item) { return uid + quartz.uid(item); };\n    return { uid: uid, gen: gen };\n};\nvar useUIDState = function () {\n    if (process.env.NODE_ENV !== \"production\") {\n        if (!('useContext' in React)) {\n            throw new Error('Hooks API requires React 16.8+');\n        }\n    }\n    // @ts-ignore\n    return React.useState(generateUID(React.useContext(source)));\n};\n/**\n * returns and unique id. SSR friendly\n * returns {String}\n * @see {@link UIDConsumer}\n * @see https://github.com/thearnica/react-uid#hooks-168\n * @example\n * const id = useUID();\n * id == 1; // for example\n */\nexport var useUID = function () {\n    var uid = useUIDState()[0].uid;\n    return uid;\n};\n/**\n * returns an uid generator\n * @see {@link UIDConsumer}\n * @see https://github.com/thearnica/react-uid#hooks-168\n * @example\n * const uid = useUIDSeed();\n * return (\n *  <>\n *    <label for={seed('email')}>Email: </label>\n *    <input id={seed('email')} name=\"email\" />\n *    {data.map(item => <div key={seed(item)}>...</div>\n *  </>\n * )\n */\nexport var useUIDSeed = function () {\n    var gen = useUIDState()[0].gen;\n    return gen;\n};\n", "import * as React from 'react';\nimport { generateUID } from \"./uid\";\nexport var createSource = function (prefix) {\n    if (prefix === void 0) { prefix = ''; }\n    return ({\n        value: 1,\n        prefix: prefix,\n        uid: generateUID()\n    });\n};\nexport var counter = createSource();\nexport var source = React.createContext(createSource());\nexport var getId = function (source) { return source.value++; };\nexport var getPrefix = function (source) { return source ? source.prefix : ''; };\n", "import * as React from 'react';\nimport { createSource, source } from \"./context\";\nimport { UID } from \"./UIDComponent\";\n/**\n * UID isolation component, required for SSR and testing.\n * Wrap your application with it to guarantee UID consistency between SSR and CSR.\n * @param {String} [prefix] - prefix for all generated ids\n * @example\n * <UIDReset>\n *    <App />\n * </UIDReset/>\n * @see https://github.com/thearnica/react-uid#server-side-friendly-uid\n */\nexport var UIDReset = function (_a) {\n    var children = _a.children, _b = _a.prefix, prefix = _b === void 0 ? '' : _b;\n    return (React.createElement(source.Provider, { value: createSource(prefix) }, children));\n};\n/**\n * Creates a sub-ids for nested components, isolating from inside a branch.\n * Useful for self-contained elements or code splitting\n * @see https://github.com/thearnica/react-uid#code-splitting\n */\nexport var UIDFork = function (_a) {\n    var children = _a.children, _b = _a.prefix, prefix = _b === void 0 ? '' : _b;\n    return (React.createElement(UIDConsumer, null, function (id) { return (React.createElement(source.Provider, { value: createSource(id + '-' + prefix) }, children)); }));\n};\n/**\n * UID in form of renderProps. Supports nesting and SSR. Prefer {@link useUID} hook version if possible.\n * @see https://github.com/thearnica/react-uid#server-side-friendly-uid\n * @see https://github.com/thearnica/react-uid#react-components\n * @example\n * // get UID to connect label to input\n * <UIDConsumer>\n *   {(id)} => <label htmlFor={id}><input id={id}/>}\n * </UIDConsumer>\n *\n * // get uid to generate uid for a keys in a list\n * <UIDConsumer>\n *   {(, uid)} => items.map(item => <li key={uid(item) />)}\n * </UIDConsumer>\n *\n * @see {@link useUID} - a hook version of this component\n * @see {@link UID} - not SSR compatible version\n */\nexport var UIDConsumer = function (_a) {\n    var name = _a.name, children = _a.children;\n    return (React.createElement(source.Consumer, null, function (value) { return (React.createElement(UID, { name: name, idSource: value, children: children })); }));\n};\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport default {\r\n    __extends,\r\n    __assign,\r\n    __rest,\r\n    __decorate,\r\n    __param,\r\n    __metadata,\r\n    __awaiter,\r\n    __generator,\r\n    __createBinding,\r\n    __exportStar,\r\n    __values,\r\n    __read,\r\n    __spread,\r\n    __spreadArrays,\r\n    __spreadArray,\r\n    __await,\r\n    __asyncGenerator,\r\n    __asyncDelegator,\r\n    __asyncValues,\r\n    __makeTemplateObject,\r\n    __importStar,\r\n    __importDefault,\r\n    __classPrivateFieldGet,\r\n    __classPrivateFieldSet,\r\n    __classPrivateFieldIn,\r\n};\r\n", "import { __extends } from \"tslib\";\nimport * as React from 'react';\nimport { counter, getId, getPrefix } from \"./context\";\n// --------------------------------------------\nvar prefixId = function (id, prefix, name) {\n    var uid = (prefix + id);\n    return String(name ? name(uid) : uid);\n};\n/**\n * @deprecated\n * UID in form of renderProps (not SSR friendly)\n * @see https://github.com/thearnica/react-uid#react-components\n * @example\n * // get UID to connect label to input\n * <UID>\n *   {(id)} => <label htmlFor={id}><input id={id}/>}\n * </UID>\n *\n * // get uid to generate uid for a keys in a list\n * <UID>\n *   {(, uid)} => items.map(item => <li key={uid(item) />)}\n * </UID>\n */\nvar UID = /** @class */ (function (_super) {\n    __extends(UID, _super);\n    function UID() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.state = {\n            quartz: _this.props.idSource || counter,\n            prefix: getPrefix(_this.props.idSource),\n            id: getId(_this.props.idSource || counter)\n        };\n        _this.uid = function (item) { return prefixId(_this.state.id + '-' + _this.state.quartz.uid(item), _this.state.prefix, _this.props.name); };\n        return _this;\n    }\n    UID.prototype.render = function () {\n        var _a = this.props, children = _a.children, name = _a.name;\n        var _b = this.state, id = _b.id, prefix = _b.prefix;\n        return children(prefixId(id, prefix, name), this.uid);\n    };\n    return UID;\n}(React.Component));\nexport { UID };\n"], "mappings": ";;;;;;;;AASO,IAAI,cAAc,WAAY;AACjC,MAAIA,WAAU;AACd,MAAI,MAAM,oBAAI,QAAQ;AAItB,MAAIC,OAAM,SAAU,MAAM,OAAO;AAC7B,QAAI,OAAO,SAAS,YAChB,OAAO,SAAS,UAAU;AAC1B,aAAO,QAAQ,SAAS,QAAQ,SAAS;AAAA,IAC7C;AACA,QAAI,CAAC,IAAI,IAAI,IAAI,GAAG;AAChB,UAAI,IAAI,MAAMD,UAAS;AACvB,aAAOC,KAAI,IAAI;AAAA,IACnB;AACA,WAAO,QAAQ,IAAI,IAAI,IAAI;AAAA,EAC/B;AACA,SAAOA;AACX;AAcO,IAAI,MAAM,YAAY;;;ACzC7B,IAAAC,SAAuB;;;ACAvB,YAAuB;AAEhB,IAAI,eAAe,SAAU,QAAQ;AACxC,MAAI,WAAW,QAAQ;AAAE,aAAS;AAAA,EAAI;AACtC,SAAQ;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,IACA,KAAK,YAAY;AAAA,EACrB;AACJ;AACO,IAAI,UAAU,aAAa;AAC3B,IAAI,SAAe,oBAAc,aAAa,CAAC;AAC/C,IAAI,QAAQ,SAAUC,SAAQ;AAAE,SAAOA,QAAO;AAAS;AACvD,IAAI,YAAY,SAAUA,SAAQ;AAAE,SAAOA,UAASA,QAAO,SAAS;AAAI;;;ADX/E,IAAIC,eAAc,SAAU,SAAS;AACjC,MAAI,SAAS,WAAW;AACxB,MAAI,SAAS,UAAU,MAAM;AAC7B,MAAI,KAAK,MAAM,MAAM;AACrB,MAAIC,OAAM,SAAS;AACnB,MAAI,MAAM,SAAU,MAAM;AAAE,WAAOA,OAAM,OAAO,IAAI,IAAI;AAAA,EAAG;AAC3D,SAAO,EAAE,KAAKA,MAAK,IAAS;AAChC;AACA,IAAI,cAAc,WAAY;AAC1B,MAAI,MAAuC;AACvC,QAAI,EAAE,gBAAgBC,SAAQ;AAC1B,YAAM,IAAI,MAAM,gCAAgC;AAAA,IACpD;AAAA,EACJ;AAEA,SAAa,gBAASF,aAAkB,kBAAW,MAAM,CAAC,CAAC;AAC/D;AA4BO,IAAI,aAAa,WAAY;AAChC,MAAI,MAAM,YAAY,EAAE,CAAC,EAAE;AAC3B,SAAO;AACX;;;AEjDA,IAAAG,SAAuB;;;ACgBvB,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA;AAAG,UAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,QAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AACpG,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEO,SAAS,UAAU,GAAG,GAAG;AAC5B,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;;;AC5BA,IAAAC,SAAuB;AAGvB,IAAI,WAAW,SAAU,IAAI,QAAQ,MAAM;AACvC,MAAIC,OAAO,SAAS;AACpB,SAAO,OAAO,OAAO,KAAKA,IAAG,IAAIA,IAAG;AACxC;AAgBA,IAAI;AAAA;AAAA,EAAqB,SAAU,QAAQ;AACvC,cAAUC,MAAK,MAAM;AACrB,aAASA,OAAM;AACX,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,QAAQ;AAAA,QACV,QAAQ,MAAM,MAAM,YAAY;AAAA,QAChC,QAAQ,UAAU,MAAM,MAAM,QAAQ;AAAA,QACtC,IAAI,MAAM,MAAM,MAAM,YAAY,OAAO;AAAA,MAC7C;AACA,YAAM,MAAM,SAAU,MAAM;AAAE,eAAO,SAAS,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,OAAO,IAAI,IAAI,GAAG,MAAM,MAAM,QAAQ,MAAM,MAAM,IAAI;AAAA,MAAG;AAC1I,aAAO;AAAA,IACX;AACA,IAAAA,KAAI,UAAU,SAAS,WAAY;AAC/B,UAAI,KAAK,KAAK,OAAO,WAAW,GAAG,UAAU,OAAO,GAAG;AACvD,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,IAAI,SAAS,GAAG;AAC7C,aAAO,SAAS,SAAS,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG;AAAA,IACxD;AACA,WAAOA;AAAA,EACX,EAAQ,gBAAS;AAAA;", "names": ["counter", "uid", "React", "source", "generateUID", "uid", "React", "React", "d", "b", "React", "uid", "UID"]}