import {
  popper_default,
  require_deep_equal,
  require_lib
} from "./chunk-B7B3EFFH.js";
import {
  _defineProperty
} from "./chunk-MLNIKOSA.js";
import {
  require_warning
} from "./chunk-NWUESYL6.js";
import {
  react_merge_refs_esm_default
} from "./chunk-JLWORYXM.js";
import {
  useUIDSeed
} from "./chunk-JQPVIOLG.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-PSGUSLG5.js";
import {
  DEFAULT_THEME,
  _extends,
  _inheritsLoose,
  arrowStyles,
  getColor,
  getLineHeight,
  retrieveComponentStyles
} from "./chunk-KGUWDO6Q.js";
import {
  _assertThisInitialized
} from "./chunk-LN6LZUGQ.js";
import {
  KEY_CODES,
  composeEventHandlers,
  getControlledValue
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  A<PERSON>,
  Me,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react_dom
} from "./chunk-JHQZW6XF.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-tooltips/dist/index.esm.js
var import_react2 = __toESM(require_react());
var import_react_dom = __toESM(require_react_dom());
var import_prop_types2 = __toESM(require_prop_types());

// node_modules/@zendeskgarden/container-tooltip/dist/index.esm.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var useTooltip = function(_temp) {
  let {
    delayMilliseconds = 500,
    id,
    isVisible
  } = _temp === void 0 ? {} : _temp;
  const [visibility, setVisibility] = (0, import_react.useState)(isVisible);
  const seed = useUIDSeed();
  const _id = (0, import_react.useMemo)(() => id || seed(`tooltip_${"1.0.5"}`), [id, seed]);
  const isMounted = (0, import_react.useRef)(false);
  const openTooltipTimeoutId = (0, import_react.useRef)();
  const closeTooltipTimeoutId = (0, import_react.useRef)();
  const openTooltip = function(delayMs) {
    if (delayMs === void 0) {
      delayMs = delayMilliseconds;
    }
    clearTimeout(closeTooltipTimeoutId.current);
    const timerId = setTimeout(() => {
      if (isMounted.current) {
        setVisibility(true);
      }
    }, delayMs);
    openTooltipTimeoutId.current = Number(timerId);
  };
  const closeTooltip = function(delayMs) {
    if (delayMs === void 0) {
      delayMs = delayMilliseconds;
    }
    clearTimeout(openTooltipTimeoutId.current);
    const timerId = setTimeout(() => {
      if (isMounted.current) {
        setVisibility(false);
      }
    }, delayMs);
    closeTooltipTimeoutId.current = Number(timerId);
  };
  (0, import_react.useEffect)(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);
  (0, import_react.useEffect)(() => {
    return () => {
      clearTimeout(openTooltipTimeoutId.current);
      clearTimeout(closeTooltipTimeoutId.current);
    };
  }, [closeTooltipTimeoutId, openTooltipTimeoutId]);
  const getTriggerProps = function(_temp2) {
    let {
      tabIndex = 0,
      onMouseEnter,
      onMouseLeave,
      onFocus,
      onBlur,
      onKeyDown,
      ...other
    } = _temp2 === void 0 ? {} : _temp2;
    return {
      tabIndex,
      onMouseEnter: composeEventHandlers(onMouseEnter, () => openTooltip()),
      onMouseLeave: composeEventHandlers(onMouseLeave, () => closeTooltip()),
      onFocus: composeEventHandlers(onFocus, () => openTooltip()),
      onBlur: composeEventHandlers(onBlur, () => closeTooltip(0)),
      onKeyDown: composeEventHandlers(onKeyDown, (event) => {
        if (event.keyCode === KEY_CODES.ESCAPE && visibility) {
          closeTooltip(0);
        }
      }),
      "aria-describedby": _id,
      "data-garden-container-id": "containers.tooltip",
      "data-garden-container-version": "1.0.5",
      ...other
    };
  };
  const getTooltipProps = function(_temp3) {
    let {
      role = "tooltip",
      onMouseEnter,
      onMouseLeave,
      ...other
    } = _temp3 === void 0 ? {} : _temp3;
    return {
      role,
      onMouseEnter: composeEventHandlers(onMouseEnter, () => openTooltip()),
      onMouseLeave: composeEventHandlers(onMouseLeave, () => closeTooltip()),
      "aria-hidden": !visibility,
      id: _id,
      ...other
    };
  };
  return {
    isVisible: visibility,
    getTooltipProps,
    getTriggerProps,
    openTooltip,
    closeTooltip
  };
};
var TooltipContainer = (_ref) => {
  let {
    children,
    render = children,
    ...options
  } = _ref;
  return import_react.default.createElement(import_react.default.Fragment, null, render(useTooltip(options)));
};
TooltipContainer.defaultProps = {
  delayMilliseconds: 500
};
TooltipContainer.propTypes = {
  children: import_prop_types.default.func,
  render: import_prop_types.default.func,
  delayMilliseconds: import_prop_types.default.number,
  isVisible: import_prop_types.default.bool
};

// node_modules/@zendeskgarden/react-tooltips/node_modules/react-popper/lib/esm/Popper.js
var import_deep_equal = __toESM(require_deep_equal());
var React3 = __toESM(require_react());

// node_modules/@zendeskgarden/react-tooltips/node_modules/react-popper/lib/esm/Manager.js
var React2 = __toESM(require_react());
var import_create_react_context = __toESM(require_lib());
var ManagerReferenceNodeContext = (0, import_create_react_context.default)();
var ManagerReferenceNodeSetterContext = (0, import_create_react_context.default)();
var Manager = function(_React$Component) {
  _inheritsLoose(Manager2, _React$Component);
  function Manager2() {
    var _this;
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "referenceNode", void 0);
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "setReferenceNode", function(newReferenceNode) {
      if (newReferenceNode && _this.referenceNode !== newReferenceNode) {
        _this.referenceNode = newReferenceNode;
        _this.forceUpdate();
      }
    });
    return _this;
  }
  var _proto = Manager2.prototype;
  _proto.componentWillUnmount = function componentWillUnmount() {
    this.referenceNode = null;
  };
  _proto.render = function render() {
    return React2.createElement(ManagerReferenceNodeContext.Provider, {
      value: this.referenceNode
    }, React2.createElement(ManagerReferenceNodeSetterContext.Provider, {
      value: this.setReferenceNode
    }, this.props.children));
  };
  return Manager2;
}(React2.Component);

// node_modules/@zendeskgarden/react-tooltips/node_modules/react-popper/lib/esm/utils.js
var unwrapArray = function unwrapArray2(arg) {
  return Array.isArray(arg) ? arg[0] : arg;
};
var safeInvoke = function safeInvoke2(fn) {
  if (typeof fn === "function") {
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    return fn.apply(void 0, args);
  }
};
var shallowEqual = function shallowEqual2(objA, objB) {
  var aKeys = Object.keys(objA);
  var bKeys = Object.keys(objB);
  if (bKeys.length !== aKeys.length) {
    return false;
  }
  for (var i = 0; i < bKeys.length; i++) {
    var key = aKeys[i];
    if (objA[key] !== objB[key]) {
      return false;
    }
  }
  return true;
};
var setRef = function setRef2(ref, node) {
  if (typeof ref === "function") {
    return safeInvoke(ref, node);
  } else if (ref != null) {
    ref.current = node;
  }
};

// node_modules/@zendeskgarden/react-tooltips/node_modules/react-popper/lib/esm/Popper.js
var initialStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  opacity: 0,
  pointerEvents: "none"
};
var initialArrowStyle = {};
var InnerPopper = function(_React$Component) {
  _inheritsLoose(InnerPopper2, _React$Component);
  function InnerPopper2() {
    var _this;
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "state", {
      data: void 0,
      placement: void 0
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "popperInstance", void 0);
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "popperNode", null);
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "arrowNode", null);
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "setPopperNode", function(popperNode) {
      if (!popperNode || _this.popperNode === popperNode)
        return;
      setRef(_this.props.innerRef, popperNode);
      _this.popperNode = popperNode;
      _this.updatePopperInstance();
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "setArrowNode", function(arrowNode) {
      _this.arrowNode = arrowNode;
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "updateStateModifier", {
      enabled: true,
      order: 900,
      fn: function fn(data) {
        var placement = data.placement;
        _this.setState({
          data,
          placement
        });
        return data;
      }
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "getOptions", function() {
      return {
        placement: _this.props.placement,
        eventsEnabled: _this.props.eventsEnabled,
        positionFixed: _this.props.positionFixed,
        modifiers: _extends({}, _this.props.modifiers, {
          arrow: _extends({}, _this.props.modifiers && _this.props.modifiers.arrow, {
            enabled: !!_this.arrowNode,
            element: _this.arrowNode
          }),
          applyStyle: {
            enabled: false
          },
          updateStateModifier: _this.updateStateModifier
        })
      };
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "getPopperStyle", function() {
      return !_this.popperNode || !_this.state.data ? initialStyle : _extends({
        position: _this.state.data.offsets.popper.position
      }, _this.state.data.styles);
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "getPopperPlacement", function() {
      return !_this.state.data ? void 0 : _this.state.placement;
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "getArrowStyle", function() {
      return !_this.arrowNode || !_this.state.data ? initialArrowStyle : _this.state.data.arrowStyles;
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "getOutOfBoundariesState", function() {
      return _this.state.data ? _this.state.data.hide : void 0;
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "destroyPopperInstance", function() {
      if (!_this.popperInstance)
        return;
      _this.popperInstance.destroy();
      _this.popperInstance = null;
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "updatePopperInstance", function() {
      _this.destroyPopperInstance();
      var _assertThisInitialize = _assertThisInitialized(_assertThisInitialized(_this)), popperNode = _assertThisInitialize.popperNode;
      var referenceElement = _this.props.referenceElement;
      if (!referenceElement || !popperNode)
        return;
      _this.popperInstance = new popper_default(referenceElement, popperNode, _this.getOptions());
    });
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "scheduleUpdate", function() {
      if (_this.popperInstance) {
        _this.popperInstance.scheduleUpdate();
      }
    });
    return _this;
  }
  var _proto = InnerPopper2.prototype;
  _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {
    if (this.props.placement !== prevProps.placement || this.props.referenceElement !== prevProps.referenceElement || this.props.positionFixed !== prevProps.positionFixed || !(0, import_deep_equal.default)(this.props.modifiers, prevProps.modifiers, {
      strict: true
    })) {
      if (true) {
        if (this.props.modifiers !== prevProps.modifiers && this.props.modifiers != null && prevProps.modifiers != null && shallowEqual(this.props.modifiers, prevProps.modifiers)) {
          console.warn("'modifiers' prop reference updated even though all values appear the same.\nConsider memoizing the 'modifiers' object to avoid needless rendering.");
        }
      }
      this.updatePopperInstance();
    } else if (this.props.eventsEnabled !== prevProps.eventsEnabled && this.popperInstance) {
      this.props.eventsEnabled ? this.popperInstance.enableEventListeners() : this.popperInstance.disableEventListeners();
    }
    if (prevState.placement !== this.state.placement) {
      this.scheduleUpdate();
    }
  };
  _proto.componentWillUnmount = function componentWillUnmount() {
    setRef(this.props.innerRef, null);
    this.destroyPopperInstance();
  };
  _proto.render = function render() {
    return unwrapArray(this.props.children)({
      ref: this.setPopperNode,
      style: this.getPopperStyle(),
      placement: this.getPopperPlacement(),
      outOfBoundaries: this.getOutOfBoundariesState(),
      scheduleUpdate: this.scheduleUpdate,
      arrowProps: {
        ref: this.setArrowNode,
        style: this.getArrowStyle()
      }
    });
  };
  return InnerPopper2;
}(React3.Component);
_defineProperty(InnerPopper, "defaultProps", {
  placement: "bottom",
  eventsEnabled: true,
  referenceElement: void 0,
  positionFixed: false
});
var placements = popper_default.placements;
function Popper(_ref) {
  var referenceElement = _ref.referenceElement, props = _objectWithoutPropertiesLoose(_ref, ["referenceElement"]);
  return React3.createElement(ManagerReferenceNodeContext.Consumer, null, function(referenceNode) {
    return React3.createElement(InnerPopper, _extends({
      referenceElement: referenceElement !== void 0 ? referenceElement : referenceNode
    }, props));
  });
}

// node_modules/@zendeskgarden/react-tooltips/node_modules/react-popper/lib/esm/Reference.js
var React4 = __toESM(require_react());
var import_warning = __toESM(require_warning());
var InnerReference = function(_React$Component) {
  _inheritsLoose(InnerReference2, _React$Component);
  function InnerReference2() {
    var _this;
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;
    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), "refHandler", function(node) {
      setRef(_this.props.innerRef, node);
      safeInvoke(_this.props.setReferenceNode, node);
    });
    return _this;
  }
  var _proto = InnerReference2.prototype;
  _proto.componentWillUnmount = function componentWillUnmount() {
    setRef(this.props.innerRef, null);
  };
  _proto.render = function render() {
    (0, import_warning.default)(Boolean(this.props.setReferenceNode), "`Reference` should not be used outside of a `Manager` component.");
    return unwrapArray(this.props.children)({
      ref: this.refHandler
    });
  };
  return InnerReference2;
}(React4.Component);
function Reference(props) {
  return React4.createElement(ManagerReferenceNodeSetterContext.Consumer, null, function(setReferenceNode) {
    return React4.createElement(InnerReference, _extends({
      setReferenceNode
    }, props));
  });
}

// node_modules/@zendeskgarden/react-tooltips/dist/index.esm.js
function getPopperPlacement(gardenPlacement) {
  const gardenToPopperMapping = {
    auto: "auto",
    top: "top",
    "top-start": "top-start",
    "top-end": "top-end",
    bottom: "bottom",
    "bottom-start": "bottom-start",
    "bottom-end": "bottom-end",
    end: "right",
    "end-top": "right-start",
    "end-bottom": "right-end",
    start: "left",
    "start-top": "left-start",
    "start-bottom": "left-end"
  };
  return gardenToPopperMapping[gardenPlacement];
}
function getRtlPopperPlacement(gardenPlacement) {
  const rtlPlacementMappings = {
    left: "right",
    "left-start": "right-start",
    "left-end": "right-end",
    "top-start": "top-end",
    "top-end": "top-start",
    right: "left",
    "right-start": "left-start",
    "right-end": "left-end",
    "bottom-start": "bottom-end",
    "bottom-end": "bottom-start"
  };
  const popperPlacement = getPopperPlacement(gardenPlacement);
  return rtlPlacementMappings[popperPlacement] || popperPlacement;
}
function getArrowPosition(popperPlacement) {
  const arrowPositionMappings = {
    top: "bottom",
    "top-start": "bottom-left",
    "top-end": "bottom-right",
    right: "left",
    "right-start": "left-top",
    "right-end": "left-bottom",
    bottom: "top",
    "bottom-start": "top-left",
    "bottom-end": "top-right",
    left: "right",
    "left-start": "right-top",
    "left-end": "right-bottom"
  };
  return arrowPositionMappings[popperPlacement] || "top";
}
var COMPONENT_ID$2 = "tooltip.paragraph";
var StyledParagraph = styled_components_browser_esm_default.p.attrs({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledParagraph",
  componentId: "sc-wuqkfc-0"
})(["margin:0;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledParagraph.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "tooltip.title";
var StyledTitle = styled_components_browser_esm_default.strong.attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledTitle",
  componentId: "sc-vnjcvz-0"
})(["display:none;margin:0;font-weight:", ";", ";"], (props) => props.theme.fontWeights.semibold, (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledTitle.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "tooltip.tooltip";
var sizeStyles = (_ref) => {
  let {
    theme,
    size,
    type,
    placement,
    hasArrow
  } = _ref;
  let margin = `${theme.space.base * 1.5}px`;
  let borderRadius = theme.borderRadii.sm;
  let padding = "0 1em";
  let maxWidth;
  let overflowWrap;
  let whiteSpace = "nowrap";
  let lineHeight = getLineHeight(theme.space.base * 5, theme.fontSizes.sm);
  let fontSize = theme.fontSizes.sm;
  let titleDisplay;
  let paragraphMarginTop;
  let wordWrap;
  if (size !== "small") {
    borderRadius = theme.borderRadii.md;
    overflowWrap = "break-word";
    whiteSpace = "normal";
    wordWrap = "break-word";
  }
  if (size === "extra-large") {
    padding = `${theme.space.base * 10}px`;
    maxWidth = `460px`;
    lineHeight = getLineHeight(theme.space.base * 5, theme.fontSizes.md);
    paragraphMarginTop = `${theme.space.base * 2.5}px`;
  } else if (size === "large") {
    padding = `${theme.space.base * 5}px`;
    maxWidth = `270px`;
    lineHeight = getLineHeight(theme.space.base * 5, theme.fontSizes.md);
    paragraphMarginTop = `${theme.space.base * 2}px`;
  } else if (size === "medium") {
    padding = "1em";
    maxWidth = `140px`;
    lineHeight = getLineHeight(theme.space.base * 4, theme.fontSizes.sm);
  }
  if (size === "extra-large" || size === "large") {
    fontSize = theme.fontSizes.md;
    titleDisplay = "block";
  }
  let arrowSize;
  let arrowInset;
  if (hasArrow) {
    if (size === "small" || size === "medium") {
      arrowSize = margin;
      arrowInset = type === "dark" ? "1px" : "0";
    } else {
      arrowInset = type === "dark" ? "2px" : "1px";
      if (size === "large") {
        margin = `${theme.space.base * 2}px`;
        arrowSize = margin;
      } else if (size === "extra-large") {
        margin = `${theme.space.base * 3}px`;
        arrowSize = `${theme.space.base * 2.5}px`;
      }
    }
  }
  return Ae(["margin:", ";border-radius:", ";padding:", ";max-width:", ";line-height:", ";word-wrap:", ";white-space:", ";font-size:", ";overflow-wrap:", ";", ";", "{margin-top:", ";}", "{display:", ";}"], margin, borderRadius, padding, maxWidth, lineHeight, wordWrap, whiteSpace, fontSize, overflowWrap, hasArrow && arrowStyles(getArrowPosition(placement), {
    size: arrowSize,
    inset: arrowInset
  }), StyledParagraph, paragraphMarginTop, StyledTitle, titleDisplay);
};
var colorStyles = (_ref2) => {
  let {
    theme,
    type
  } = _ref2;
  let border;
  let boxShadow = theme.shadows.lg(`${theme.space.base}px`, `${theme.space.base * 2}px`, getColor("chromeHue", 600, theme, 0.15));
  let backgroundColor = getColor("chromeHue", 700, theme);
  let color = theme.colors.background;
  let titleColor;
  if (type === "light") {
    boxShadow = theme.shadows.lg(`${theme.space.base * 3}px`, `${theme.space.base * 5}px`, getColor("chromeHue", 600, theme, 0.15));
    border = `${theme.borders.sm} ${getColor("neutralHue", 300, theme)}`;
    backgroundColor = theme.colors.background;
    color = getColor("neutralHue", 700, theme);
    titleColor = theme.colors.foreground;
  }
  return Ae(["border:", ";box-shadow:", ";background-color:", ";color:", ";", "{color:", ";}"], border, boxShadow, backgroundColor, color, StyledTitle, titleColor);
};
var StyledTooltip = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledTooltip",
  componentId: "sc-gzzjq4-0"
})(["display:inline-block;box-sizing:border-box;direction:", ";text-align:", ";font-weight:", ";", ";&[aria-hidden='true']{display:none;}", ";", ";"], (props) => props.theme.rtl && "rtl", (props) => props.theme.rtl ? "right" : "left", (props) => props.theme.fontWeights.regular, (props) => sizeStyles(props), colorStyles, (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledTooltip.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledTooltipWrapper = styled_components_browser_esm_default.div.withConfig({
  displayName: "StyledTooltipWrapper",
  componentId: "sc-1b7q9q6-0"
})(["transition:opacity 10ms;opacity:1;z-index:", ";&[aria-hidden='true']{visibility:hidden;opacity:0;}"], (props) => props.zIndex);
StyledTooltipWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var SHARED_PLACEMENT = ["auto", "top", "top-start", "top-end", "bottom", "bottom-start", "bottom-end"];
var PLACEMENT = [...SHARED_PLACEMENT, "end", "end-top", "end-bottom", "start", "start-top", "start-bottom"];
var SIZE = ["small", "medium", "large", "extra-large"];
var TYPE = ["light", "dark"];
var Tooltip = (_ref) => {
  let {
    id,
    delayMS,
    isInitialVisible,
    content,
    refKey,
    placement,
    eventsEnabled,
    popperModifiers,
    children,
    hasArrow,
    size,
    type,
    appendToNode,
    zIndex,
    isVisible: externalIsVisible,
    ...otherProps
  } = _ref;
  const {
    rtl
  } = (0, import_react2.useContext)(Me);
  const scheduleUpdateRef = (0, import_react2.useRef)();
  const {
    isVisible,
    getTooltipProps,
    getTriggerProps,
    openTooltip,
    closeTooltip
  } = useTooltip({
    id,
    delayMilliseconds: delayMS,
    isVisible: isInitialVisible
  });
  const controlledIsVisible = getControlledValue(externalIsVisible, isVisible);
  (0, import_react2.useEffect)(() => {
    if (controlledIsVisible && scheduleUpdateRef.current) {
      scheduleUpdateRef.current();
    }
  }, [controlledIsVisible, content]);
  const popperPlacement = rtl ? getRtlPopperPlacement(placement) : getPopperPlacement(placement);
  const singleChild = import_react2.default.Children.only(children);
  const modifiers = {
    preventOverflow: {
      boundariesElement: "window"
    },
    ...popperModifiers
  };
  return import_react2.default.createElement(Manager, null, import_react2.default.createElement(Reference, null, (_ref2) => {
    let {
      ref
    } = _ref2;
    return (0, import_react2.cloneElement)(singleChild, getTriggerProps({
      ...singleChild.props,
      [refKey]: react_merge_refs_esm_default([ref, singleChild.ref ? singleChild.ref : null])
    }));
  }), import_react2.default.createElement(Popper, {
    placement: popperPlacement,
    eventsEnabled: controlledIsVisible && eventsEnabled,
    modifiers
  }, (_ref3) => {
    let {
      ref,
      style,
      scheduleUpdate,
      placement: currentPlacement
    } = _ref3;
    scheduleUpdateRef.current = scheduleUpdate;
    const {
      onFocus,
      onBlur,
      ...otherTooltipProps
    } = otherProps;
    let computedSize = size;
    if (computedSize === void 0) {
      if (type === "dark") {
        computedSize = "small";
      } else {
        computedSize = "large";
      }
    }
    const tooltipProps = {
      hasArrow,
      placement: currentPlacement,
      size: computedSize,
      onFocus: composeEventHandlers(onFocus, () => {
        openTooltip();
      }),
      onBlur: composeEventHandlers(onBlur, () => {
        closeTooltip(0);
      }),
      "aria-hidden": !controlledIsVisible,
      type,
      ...otherTooltipProps
    };
    const tooltip = import_react2.default.createElement(StyledTooltipWrapper, {
      ref: controlledIsVisible ? ref : null,
      style,
      zIndex,
      "aria-hidden": !controlledIsVisible
    }, import_react2.default.createElement(StyledTooltip, getTooltipProps(tooltipProps), content));
    if (appendToNode) {
      return (0, import_react_dom.createPortal)(tooltip, appendToNode);
    }
    return tooltip;
  }));
};
Tooltip.displayName = "Tooltip";
Tooltip.propTypes = {
  appendToNode: import_prop_types2.default.any,
  hasArrow: import_prop_types2.default.bool,
  delayMS: import_prop_types2.default.number,
  eventsEnabled: import_prop_types2.default.bool,
  id: import_prop_types2.default.string,
  content: import_prop_types2.default.node.isRequired,
  placement: import_prop_types2.default.oneOf(PLACEMENT),
  popperModifiers: import_prop_types2.default.any,
  size: import_prop_types2.default.oneOf(SIZE),
  type: import_prop_types2.default.oneOf(TYPE),
  zIndex: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  isInitialVisible: import_prop_types2.default.bool,
  refKey: import_prop_types2.default.string
};
Tooltip.defaultProps = {
  hasArrow: true,
  eventsEnabled: true,
  type: "dark",
  placement: "top",
  delayMS: 500,
  refKey: "ref"
};
function _extends2() {
  _extends2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends2.apply(this, arguments);
}
var Paragraph = (0, import_react2.forwardRef)((props, ref) => import_react2.default.createElement(StyledParagraph, _extends2({
  ref
}, props)));
Paragraph.displayName = "Paragraph";
var Title = (0, import_react2.forwardRef)((props, ref) => import_react2.default.createElement(StyledTitle, _extends2({
  ref
}, props)));
Title.displayName = "Title";

export {
  Tooltip,
  Paragraph,
  Title
};
//# sourceMappingURL=chunk-RFVHSKWS.js.map
