import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/container-utilities/dist/index.esm.js
var import_react15 = __toESM(require_react());

// node_modules/@zendeskgarden/container-utilities/node_modules/@reach/auto-id/dist/reach-auto-id.mjs
var React2 = __toESM(require_react(), 1);

// node_modules/@zendeskgarden/container-utilities/node_modules/@reach/auto-id/node_modules/@reach/utils/dist/reach-utils.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var React = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_react7 = __toESM(require_react(), 1);
var import_react8 = __toESM(require_react(), 1);
var import_react9 = __toESM(require_react(), 1);
var import_react10 = __toESM(require_react(), 1);
var import_react11 = __toESM(require_react(), 1);
var import_react12 = __toESM(require_react(), 1);
var import_react13 = __toESM(require_react(), 1);
var import_react14 = __toESM(require_react(), 1);
function canUseDOM() {
  return !!(typeof window !== "undefined" && window.document && window.document.createElement);
}
var useIsomorphicLayoutEffect = canUseDOM() ? import_react9.useLayoutEffect : import_react9.useEffect;

// node_modules/@zendeskgarden/container-utilities/node_modules/@reach/auto-id/dist/reach-auto-id.mjs
var serverHandoffComplete = false;
var id = 0;
function genId() {
  return ++id;
}
var maybeReactUseId = React2["useId".toString()];
function useId(providedId) {
  if (maybeReactUseId !== void 0) {
    let generatedId = maybeReactUseId();
    return providedId ?? generatedId;
  }
  let initialId = providedId ?? (serverHandoffComplete ? genId() : null);
  let [id2, setId] = React2.useState(initialId);
  useIsomorphicLayoutEffect(() => {
    if (id2 === null) {
      setId(genId());
    }
  }, []);
  React2.useEffect(() => {
    if (serverHandoffComplete === false) {
      serverHandoffComplete = true;
    }
  }, []);
  return providedId ?? id2 ?? void 0;
}

// node_modules/@zendeskgarden/container-utilities/dist/index.esm.js
function composeEventHandlers() {
  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {
    fns[_key] = arguments[_key];
  }
  return function(event) {
    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
      args[_key2 - 1] = arguments[_key2];
    }
    return fns.some((fn) => {
      fn && fn(event, ...args);
      return event && event.defaultPrevented;
    });
  };
}
var idCounter = 0;
function generateId(prefix) {
  if (prefix === void 0) {
    prefix = "garden";
  }
  return `${prefix}-${idCounter++}`;
}
function setIdCounter(num) {
  idCounter = num;
}
function getControlledValue() {
  for (var _len = arguments.length, values = new Array(_len), _key = 0; _key < _len; _key++) {
    values[_key] = arguments[_key];
  }
  for (const value of values) {
    if (value !== void 0) {
      return value;
    }
  }
  return void 0;
}
function useCombinedRefs() {
  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {
    refs[_key] = arguments[_key];
  }
  const targetRef = (0, import_react15.useRef)(null);
  (0, import_react15.useEffect)(() => {
    console.warn('"useCombinedRefs()" has been deprecated and will be removed in the next major version release of "@zendeskgarden/container-utilities"');
    refs.forEach((ref) => {
      if (!ref)
        return;
      if (typeof ref === "function") {
        ref(targetRef.current);
      } else {
        ref.current = targetRef.current;
      }
    });
  }, []);
  return targetRef;
}
var convertToMatrix = (array, columnCount) => {
  return array.reduce((acc, curr) => {
    if (acc.length === 0)
      return [[curr]];
    if (acc[acc.length - 1].length < columnCount) {
      acc[acc.length - 1].push(curr);
    } else {
      acc.push([curr]);
    }
    return acc;
  }, []);
};
var KEY_CODES = {
  ALT: 18,
  ASTERISK: 170,
  BACKSPACE: 8,
  COMMA: 188,
  DELETE: 46,
  DOWN: 40,
  END: 35,
  ENTER: 13,
  ESCAPE: 27,
  HOME: 36,
  LEFT: 37,
  NUMPAD_ADD: 107,
  NUMPAD_DECIMAL: 110,
  NUMPAD_DIVIDE: 111,
  NUMPAD_ENTER: 108,
  NUMPAD_MULTIPLY: 106,
  NUMPAD_SUBTRACT: 109,
  PAGE_DOWN: 34,
  PAGE_UP: 33,
  PERIOD: 190,
  RIGHT: 39,
  SHIFT: 16,
  SPACE: 32,
  TAB: 9,
  UP: 38
};
var KEYS = {
  ALT: "Alt",
  ASTERISK: "*",
  BACKSPACE: "Backspace",
  COMMA: ",",
  DELETE: "Delete",
  DOWN: "ArrowDown",
  END: "End",
  ENTER: "Enter",
  ESCAPE: "Escape",
  HOME: "Home",
  LEFT: "ArrowLeft",
  NUMPAD_ADD: "Add",
  NUMPAD_DECIMAL: "Decimal",
  NUMPAD_DIVIDE: "Divide",
  NUMPAD_ENTER: "Enter",
  NUMPAD_MULTIPLY: "Multiply",
  NUMPAD_SUBTRACT: "Subtract",
  PAGE_DOWN: "PageDown",
  PAGE_UP: "PageUp",
  PERIOD: ".",
  RIGHT: "ArrowRight",
  SHIFT: "Shift",
  SPACE: " ",
  TAB: "Tab",
  UNIDENTIFIED: "Unidentified",
  UP: "ArrowUp"
};
var DocumentPosition;
(function(DocumentPosition2) {
  DocumentPosition2[DocumentPosition2["DISCONNECTED"] = 1] = "DISCONNECTED";
  DocumentPosition2[DocumentPosition2["PRECEDING"] = 2] = "PRECEDING";
  DocumentPosition2[DocumentPosition2["FOLLOWING"] = 4] = "FOLLOWING";
  DocumentPosition2[DocumentPosition2["CONTAINS"] = 8] = "CONTAINS";
  DocumentPosition2[DocumentPosition2["CONTAINED_BY"] = 16] = "CONTAINED_BY";
  DocumentPosition2[DocumentPosition2["IMPLEMENTATION_SPECIFIC"] = 32] = "IMPLEMENTATION_SPECIFIC";
})(DocumentPosition || (DocumentPosition = {}));

export {
  useId,
  composeEventHandlers,
  generateId,
  setIdCounter,
  getControlledValue,
  useCombinedRefs,
  convertToMatrix,
  KEY_CODES,
  KEYS,
  DocumentPosition
};
/*! Bundled license information:

@reach/utils/dist/reach-utils.mjs:
  (**
    * @reach/utils v0.18.0
    *
    * Copyright (c) 2018-2022, React Training LLC
    *
    * This source code is licensed under the MIT license found in the
    * LICENSE.md file in the root directory of this source tree.
    *
    * @license MIT
    *)

@reach/auto-id/dist/reach-auto-id.mjs:
  (**
    * @reach/auto-id v0.18.0
    *
    * Copyright (c) 2018-2022, React Training LLC
    *
    * This source code is licensed under the MIT license found in the
    * LICENSE.md file in the root directory of this source tree.
    *
    * @license MIT
    *)
*/
//# sourceMappingURL=chunk-UWW6VX3E.js.map
