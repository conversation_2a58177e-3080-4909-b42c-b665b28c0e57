import {
  uid
} from "./chunk-JQPVIOLG.js";
import {
  CSSTransition_default,
  TransitionGroup_default
} from "./chunk-X5YXPSWX.js";
import "./chunk-PSGUSLG5.js";
import {
  <PERSON><PERSON>,
  IconButton
} from "./chunk-V3K7YCP3.js";
import "./chunk-YKXCMXPN.js";
import {
  DEFAULT_THEME,
  getColor,
  getLineHeight,
  hideVisually,
  math,
  retrieveComponentStyles,
  useDocument,
  useText
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  Ae,
  Me,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import "./chunk-JHQZW6XF.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-notifications/dist/index.esm.js
var React = __toESM(require_react());
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
function _extends$6() {
  _extends$6 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$6.apply(this, arguments);
}
var TYPE = ["success", "warning", "error", "info"];
var COMPONENT_ID$b = "notifications.close";
var StyledClose = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$b,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledClose",
  componentId: "sc-1mr9nx1-0"
})(["display:block;position:absolute;top:", "px;", ":", ";transition:background-color 0.1s ease-in-out,color 0.25s ease-in-out;border:none;border-radius:50%;background-color:transparent;cursor:pointer;padding:0;width:", "px;height:", "px;overflow:hidden;color:", ";font-size:0;user-select:none;&:hover{color:", ";}&:focus{outline:none;}&[data-garden-focus-visible]{background-color:", ";color:", ";&::-moz-focus-inner{border:0;}}", ";"], (props) => props.theme.space.base, (props) => props.theme.rtl ? "left" : "right", (props) => `${props.theme.space.base}px`, (props) => props.theme.space.base * 7, (props) => props.theme.space.base * 7, (props) => props.hue ? getColor(props.hue, props.hue === "warningHue" ? 700 : 600, props.theme) : getColor("neutralHue", 600, props.theme), (props) => props.hue ? getColor(props.hue, 800, props.theme) : getColor("neutralHue", 800, props.theme), (props) => props.hue ? getColor(props.hue, props.hue === "warningHue" ? 700 : 600, props.theme, 0.15) : getColor("neutralHue", 600, props.theme, 0.15), (props) => props.hue ? getColor(props.hue, 800, props.theme) : getColor("neutralHue", 800, props.theme), (props) => retrieveComponentStyles(COMPONENT_ID$b, props));
StyledClose.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$a = "notifications.paragraph";
var StyledParagraph = styled_components_browser_esm_default.p.attrs({
  "data-garden-id": COMPONENT_ID$a,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledParagraph",
  componentId: "sc-12tmd6p-0"
})(["margin:", "px 0 0;", ";"], (props) => props.theme.space.base * 2, (props) => retrieveComponentStyles(COMPONENT_ID$a, props));
StyledParagraph.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$9 = "notifications.title";
var StyledTitle = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$9,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledTitle",
  componentId: "sc-xx4jsv-0"
})(["margin:0;color:", ";font-weight:", ";", ";"], (props) => props.theme.colors.foreground, (props) => props.isRegular ? props.theme.fontWeights.regular : props.theme.fontWeights.semibold, (props) => retrieveComponentStyles(COMPONENT_ID$9, props));
StyledTitle.defaultProps = {
  theme: DEFAULT_THEME
};
var boxShadow = (props) => {
  const {
    theme
  } = props;
  const {
    space,
    shadows
  } = theme;
  const offsetY = `${space.base * 5}px`;
  const blurRadius = `${space.base * 7}px`;
  const color = getColor("chromeHue", 600, theme, 0.15);
  return shadows.lg(offsetY, blurRadius, color);
};
var colorStyles$6 = (props) => {
  let backgroundColor;
  let borderColor;
  let foregroundColor;
  if (props.hue) {
    backgroundColor = getColor(props.hue, 100, props.theme);
    borderColor = getColor(props.hue, 300, props.theme);
    foregroundColor = getColor(props.hue, props.type === "info" ? 600 : 700, props.theme);
  } else {
    backgroundColor = props.theme.colors.background;
    borderColor = getColor("neutralHue", 300, props.theme);
    foregroundColor = getColor("neutralHue", 800, props.theme);
  }
  return Ae(["border-color:", ";background-color:", ";color:", ";"], borderColor, backgroundColor, foregroundColor);
};
var padding = (props) => {
  const {
    space
  } = props.theme;
  const paddingVertical = `${space.base * 5}px`;
  const paddingHorizontal = `${space.base * 10}px`;
  return `${paddingVertical} ${paddingHorizontal}`;
};
var StyledBase = styled_components_browser_esm_default.div.withConfig({
  displayName: "StyledBase",
  componentId: "sc-14syaqw-0"
})(["position:relative;border:", ";border-radius:", ";box-shadow:", ";padding:", ";line-height:", ";font-size:", ";direction:", ";", ";"], (props) => props.theme.borders.sm, (props) => props.theme.borderRadii.md, (props) => props.isFloating && boxShadow, padding, (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.theme.fontSizes.md, (props) => props.theme.rtl && "rtl", colorStyles$6);
StyledBase.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$8 = "notifications.alert";
var colorStyles$5 = (props) => Ae(["", "{color:", ";}"], StyledTitle, props.hue && getColor(props.hue, 800, props.theme));
var StyledAlert = styled_components_browser_esm_default(StyledBase).attrs((props) => ({
  "data-garden-id": COMPONENT_ID$8,
  "data-garden-version": "8.67.0",
  role: props.role === void 0 ? "alert" : props.role
})).withConfig({
  displayName: "StyledAlert",
  componentId: "sc-fyn8jp-0"
})(["", " ", ";"], colorStyles$5, (props) => retrieveComponentStyles(COMPONENT_ID$8, props));
StyledAlert.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$7 = "notifications.notification";
var colorStyles$4 = (props) => {
  const {
    type,
    theme
  } = props;
  const {
    colors
  } = theme;
  const {
    successHue,
    dangerHue,
    warningHue,
    foreground
  } = colors;
  let color;
  switch (type) {
    case "success":
      color = getColor(successHue, 600, theme);
      break;
    case "error":
      color = getColor(dangerHue, 600, theme);
      break;
    case "warning":
      color = getColor(warningHue, 700, theme);
      break;
    case "info":
      color = foreground;
      break;
    default:
      color = "inherit";
      break;
  }
  return Ae(["", "{color:", ";}"], StyledTitle, color);
};
var StyledNotification = styled_components_browser_esm_default(StyledBase).attrs((props) => ({
  "data-garden-id": COMPONENT_ID$7,
  "data-garden-version": "8.67.0",
  role: props.role === void 0 ? "status" : props.role
})).withConfig({
  displayName: "StyledNotification",
  componentId: "sc-uf6jh-0"
})(["", " ", ";"], colorStyles$4, (props) => retrieveComponentStyles(COMPONENT_ID$7, props));
StyledNotification.propTypes = {
  type: import_prop_types.default.oneOf(TYPE)
};
StyledNotification.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$6 = "notifications.well";
var StyledWell = styled_components_browser_esm_default(StyledBase).attrs({
  "data-garden-id": COMPONENT_ID$6,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledWell",
  componentId: "sc-a5831c-0"
})(["background-color:", ";color:", " ", ";"], (props) => props.isRecessed && getColor("neutralHue", 100, props.theme), (props) => getColor("neutralHue", 600, props.theme), (props) => retrieveComponentStyles(COMPONENT_ID$6, props));
StyledWell.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledIcon = styled_components_browser_esm_default((_ref) => {
  let {
    children,
    ...props
  } = _ref;
  return import_react.default.cloneElement(import_react.Children.only(children), props);
}).withConfig({
  displayName: "StyledIcon",
  componentId: "sc-msklws-0"
})(["position:absolute;right:", ";left:", ";margin-top:", "px;color:", ";"], (props) => props.theme.rtl && `${props.theme.space.base * 4}px`, (props) => !props.theme.rtl && `${props.theme.space.base * 4}px`, (props) => props.theme.space.base / 2, (props) => props.hue && getColor(props.hue, props.hue === "warningHue" ? 700 : 600, props.theme));
StyledIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$5 = "notifications.global-alert";
var colorStyles$3 = (props) => {
  let borderColor;
  let backgroundColor;
  let foregroundColor;
  let anchorHoverColor;
  let anchorActiveColor;
  let anchorBoxShadowColor;
  switch (props.alertType) {
    case "success":
      borderColor = getColor("successHue", 700, props.theme);
      backgroundColor = getColor("successHue", 600, props.theme);
      foregroundColor = getColor("successHue", 100, props.theme);
      anchorHoverColor = props.theme.palette.white;
      anchorActiveColor = props.theme.palette.white;
      anchorBoxShadowColor = getColor("successHue", 200, props.theme, 0.35);
      break;
    case "error":
      borderColor = getColor("dangerHue", 700, props.theme);
      backgroundColor = getColor("dangerHue", 600, props.theme);
      foregroundColor = getColor("dangerHue", 100, props.theme);
      anchorHoverColor = props.theme.palette.white;
      anchorActiveColor = props.theme.palette.white;
      anchorBoxShadowColor = getColor("dangerHue", 100, props.theme, 0.35);
      break;
    case "warning":
      borderColor = getColor("warningHue", 400, props.theme);
      backgroundColor = getColor("warningHue", 300, props.theme);
      foregroundColor = getColor("warningHue", 800, props.theme);
      anchorHoverColor = getColor("warningHue", 900, props.theme);
      anchorActiveColor = getColor("warningHue", 1e3, props.theme);
      anchorBoxShadowColor = getColor("warningHue", 800, props.theme, 0.35);
      break;
    case "info":
      borderColor = getColor("primaryHue", 300, props.theme);
      backgroundColor = getColor("primaryHue", 200, props.theme);
      foregroundColor = getColor("primaryHue", 700, props.theme);
      anchorHoverColor = getColor("primaryHue", 800, props.theme);
      anchorActiveColor = getColor("primaryHue", 900, props.theme);
      anchorBoxShadowColor = getColor("primaryHue", 700, props.theme, 0.35);
      break;
  }
  const boxShadow2 = `0 ${props.theme.borderWidths.sm} ${props.theme.borderWidths.sm} ${borderColor}`;
  return Ae(["box-shadow:", ";background-color:", ";color:", ";& a{color:inherit;&:focus{color:inherit;}&:hover,[data-garden-focus-visible]{color:", ";}&[data-garden-focus-visible]{box-shadow:", ";}&:active{color:", ";}}"], boxShadow2, backgroundColor, foregroundColor, anchorHoverColor, props.theme.shadows.sm(anchorBoxShadowColor), anchorActiveColor);
};
var sizeStyles$3 = (props) => {
  const {
    fontSizes,
    space
  } = props.theme;
  const minHeight = space.base * 13;
  const padding2 = space.base * 4;
  const lineHeight = getLineHeight(space.base * 5, fontSizes.md);
  return Ae(["padding:", "px;min-height:", "px;line-height:", ";font-size:", ";"], padding2, minHeight, lineHeight, fontSizes.md);
};
var StyledGlobalAlert = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$5,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledGlobalAlert",
  componentId: "sc-k6rimt-0"
})(["display:flex;flex-wrap:nowrap;overflow:auto;overflow-x:hidden;box-sizing:border-box;direction:", ";&& a{border-radius:", ";text-decoration:underline;&:focus{text-decoration:underline;}}", " ", " ", ";"], (props) => props.theme.rtl ? "rtl" : "ltr", (props) => props.theme.borderRadii.sm, sizeStyles$3, colorStyles$3, (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledGlobalAlert.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$4 = "notifications.global-alert.close";
var colorStyles$2 = (props) => {
  let hoverBackgroundColor;
  let hoverForegroundColor;
  let activeBackgroundColor;
  let activeForegroundColor;
  let boxShadowColor;
  switch (props.alertType) {
    case "success":
      hoverBackgroundColor = getColor("successHue", 100, props.theme, 0.08);
      hoverForegroundColor = props.theme.palette.white;
      activeBackgroundColor = getColor("successHue", 100, props.theme, 0.2);
      activeForegroundColor = props.theme.palette.white;
      boxShadowColor = getColor("successHue", 100, props.theme, 0.35);
      break;
    case "error":
      hoverBackgroundColor = getColor("dangerHue", 100, props.theme, 0.08);
      hoverForegroundColor = props.theme.palette.white;
      activeBackgroundColor = getColor("dangerHue", 100, props.theme, 0.2);
      activeForegroundColor = props.theme.palette.white;
      boxShadowColor = getColor("dangerHue", 100, props.theme, 0.35);
      break;
    case "warning":
      hoverBackgroundColor = getColor("warningHue", 800, props.theme, 0.08);
      hoverForegroundColor = getColor("warningHue", 900, props.theme);
      activeBackgroundColor = getColor("warningHue", 800, props.theme, 0.2);
      activeForegroundColor = getColor("warningHue", 1e3, props.theme);
      boxShadowColor = getColor("warningHue", 800, props.theme, 0.35);
      break;
    case "info":
      hoverBackgroundColor = getColor("primaryHue", 700, props.theme, 0.08);
      hoverForegroundColor = getColor("primaryHue", 800, props.theme);
      activeBackgroundColor = getColor("primaryHue", 700, props.theme, 0.2);
      activeForegroundColor = getColor("primaryHue", 900, props.theme);
      boxShadowColor = getColor("primaryHue", 700, props.theme, 0.35);
      break;
  }
  return Ae(["color:inherit;&:hover{background-color:", ";color:", ";}&[data-garden-focus-visible]{box-shadow:", ";}&:active{background-color:", ";color:", ";}"], hoverBackgroundColor, hoverForegroundColor, props.theme.shadows.md(boxShadowColor), activeBackgroundColor, activeForegroundColor);
};
var sizeStyles$2 = (props) => {
  const marginVertical = `-${props.theme.space.base * 1.5}px`;
  const marginStart = `${props.theme.space.base * 2}px`;
  const marginEnd = `-${props.theme.space.base * 2}px`;
  return Ae(["margin:", " ", " ", " ", ";"], marginVertical, props.theme.rtl ? marginStart : marginEnd, marginVertical, props.theme.rtl ? marginEnd : marginStart);
};
var StyledGlobalAlertClose = styled_components_browser_esm_default(IconButton).attrs({
  "data-garden-id": COMPONENT_ID$4,
  "data-garden-version": "8.67.0",
  size: "small"
}).withConfig({
  displayName: "StyledGlobalAlertClose",
  componentId: "sc-1g5s93s-0"
})(["", ";", ";", ";"], sizeStyles$2, colorStyles$2, (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledGlobalAlertClose.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "notifications.global-alert.button";
function colorStyles$1(props) {
  if (props.isBasic) {
    return colorStyles$2(props);
  }
  let backgroundColor;
  let hoverBackgroundColor;
  let activeBackgroundColor;
  let boxShadowColor;
  switch (props.alertType) {
    case "success":
      backgroundColor = getColor("successHue", 800, props.theme);
      hoverBackgroundColor = getColor("successHue", 900, props.theme);
      activeBackgroundColor = getColor("successHue", 1e3, props.theme);
      boxShadowColor = getColor("successHue", 200, props.theme, 0.35);
      break;
    case "error":
      backgroundColor = getColor("dangerHue", 800, props.theme);
      hoverBackgroundColor = getColor("dangerHue", 900, props.theme);
      activeBackgroundColor = getColor("dangerHue", 1e3, props.theme);
      boxShadowColor = getColor("dangerHue", 100, props.theme, 0.35);
      break;
    case "warning":
      backgroundColor = getColor("warningHue", 800, props.theme);
      hoverBackgroundColor = getColor("warningHue", 900, props.theme);
      activeBackgroundColor = getColor("warningHue", 1e3, props.theme);
      boxShadowColor = getColor("warningHue", 800, props.theme, 0.35);
      break;
    case "info":
      boxShadowColor = getColor("primaryHue", 700, props.theme, 0.35);
      break;
  }
  return Ae(["background-color:", ";&:hover{background-color:", ";}&[data-garden-focus-visible]{box-shadow:", ";}&:active{background-color:", ";}"], backgroundColor, hoverBackgroundColor, boxShadowColor && props.theme.shadows.md(boxShadowColor), activeBackgroundColor);
}
function sizeStyles$1(props) {
  const marginVertical = `-${props.theme.space.base * 1.5}px`;
  const marginStart = `${props.theme.space.base * 2}px`;
  return Ae(["margin:", " ", " ", " ", ";"], marginVertical, props.theme.rtl ? marginStart : 0, marginVertical, props.theme.rtl ? 0 : marginStart);
}
var StyledGlobalAlertButton = styled_components_browser_esm_default(Button).attrs({
  "data-garden-id": COMPONENT_ID$3,
  "data-garden-version": "8.67.0",
  focusInset: false,
  isDanger: false,
  isLink: false,
  isNeutral: false,
  isPill: false,
  isStretched: false,
  size: "small"
}).withConfig({
  displayName: "StyledGlobalAlertButton",
  componentId: "sc-1txe91a-0"
})(["flex-shrink:0;", ";", ";", ";"], sizeStyles$1, colorStyles$1, (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledGlobalAlertButton.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "notifications.global-alert.content";
var StyledGlobalAlertContent = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledGlobalAlertContent",
  componentId: "sc-rept0u-0"
})(["flex-grow:1;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledGlobalAlertContent.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "notifications.global-alert.icon";
var sizeStyles = (props) => {
  const size = props.theme.iconSizes.md;
  const marginTop = math(`(${props.theme.space.base * 5} - ${size}) / 2`);
  const marginHorizontal = `${props.theme.space.base * 2}px`;
  return Ae(["margin-top:", ";margin-", ":", ";width:", ";height:", ";"], marginTop, props.theme.rtl ? "left" : "right", marginHorizontal, size, size);
};
var StyledGlobalAlertIcon = styled_components_browser_esm_default((_ref) => {
  let {
    children,
    ...props
  } = _ref;
  return import_react.default.cloneElement(import_react.Children.only(children), props);
}).attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledGlobalAlertIcon",
  componentId: "sc-84ne9k-0"
})(["flex-shrink:0;", ";", ";"], sizeStyles, (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledGlobalAlertIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "notifications.global-alert.title";
var colorStyles = (props) => {
  let color;
  switch (props.alertType) {
    case "success":
    case "error":
      color = props.theme.palette.white;
      break;
    case "warning":
      color = getColor("warningHue", 900, props.theme);
      break;
    case "info":
      color = getColor("primaryHue", 800, props.theme);
      break;
  }
  return Ae(["color:", ";"], color);
};
var StyledGlobalAlertTitle = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledGlobalAlertTitle",
  componentId: "sc-10clqbo-0"
})(["display:inline;margin-", ":", "px;font-weight:", ";", ";", ";"], (props) => props.theme.rtl ? "left" : "right", (props) => props.theme.space.base * 2, (props) => props.isRegular ? props.theme.fontWeights.regular : props.theme.fontWeights.semibold, colorStyles, (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledGlobalAlertTitle.defaultProps = {
  theme: DEFAULT_THEME
};
var _g$2;
var _circle$2;
function _extends$5() {
  _extends$5 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$5.apply(this, arguments);
}
var SvgAlertErrorStroke = function SvgAlertErrorStroke2(props) {
  return React.createElement("svg", _extends$5({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _g$2 || (_g$2 = React.createElement("g", {
    fill: "none",
    stroke: "currentColor"
  }, React.createElement("circle", {
    cx: 7.5,
    cy: 8.5,
    r: 7
  }), React.createElement("path", {
    strokeLinecap: "round",
    d: "M7.5 4.5V9"
  }))), _circle$2 || (_circle$2 = React.createElement("circle", {
    cx: 7.5,
    cy: 12,
    r: 1,
    fill: "currentColor"
  })));
};
var _g$1;
function _extends$4() {
  _extends$4 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$4.apply(this, arguments);
}
var SvgCheckCircleStroke = function SvgCheckCircleStroke2(props) {
  return React.createElement("svg", _extends$4({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _g$1 || (_g$1 = React.createElement("g", {
    fill: "none",
    stroke: "currentColor"
  }, React.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 9l2.5 2.5 5-5"
  }), React.createElement("circle", {
    cx: 7.5,
    cy: 8.5,
    r: 7
  }))));
};
var _path$2;
var _circle$1;
function _extends$3() {
  _extends$3 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$3.apply(this, arguments);
}
var SvgAlertWarningStroke = function SvgAlertWarningStroke2(props) {
  return React.createElement("svg", _extends$3({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$2 || (_path$2 = React.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M.88 13.77L7.06 1.86c.19-.36.7-.36.89 0l6.18 11.91c.17.33-.07.73-.44.73H1.32c-.37 0-.61-.4-.44-.73zM7.5 6v3.5"
  })), _circle$1 || (_circle$1 = React.createElement("circle", {
    cx: 7.5,
    cy: 12,
    r: 1,
    fill: "currentColor"
  })));
};
var _g;
var _circle;
function _extends$2() {
  _extends$2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$2.apply(this, arguments);
}
var SvgInfoStroke = function SvgInfoStroke2(props) {
  return React.createElement("svg", _extends$2({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _g || (_g = React.createElement("g", {
    stroke: "currentColor"
  }, React.createElement("circle", {
    cx: 7.5,
    cy: 8.5,
    r: 7,
    fill: "none"
  }), React.createElement("path", {
    strokeLinecap: "round",
    d: "M7.5 12.5V8"
  }))), _circle || (_circle = React.createElement("circle", {
    cx: 7.5,
    cy: 5,
    r: 1,
    fill: "currentColor"
  })));
};
var validationIcons = {
  success: SvgCheckCircleStroke,
  error: SvgAlertErrorStroke,
  warning: SvgAlertWarningStroke,
  info: SvgInfoStroke
};
var validationHues = {
  success: "successHue",
  error: "dangerHue",
  warning: "warningHue",
  info: "neutralHue"
};
var NotificationsContext = (0, import_react.createContext)(void 0);
var useNotificationsContext = () => {
  return (0, import_react.useContext)(NotificationsContext);
};
var Alert = import_react.default.forwardRef((props, ref) => {
  const hue = validationHues[props.type];
  const Icon = validationIcons[props.type];
  return import_react.default.createElement(NotificationsContext.Provider, {
    value: hue
  }, import_react.default.createElement(StyledAlert, _extends$6({
    ref,
    hue
  }, props), import_react.default.createElement(StyledIcon, {
    hue
  }, import_react.default.createElement(Icon, null)), props.children));
});
Alert.displayName = "Alert";
Alert.propTypes = {
  type: import_prop_types.default.oneOf(TYPE).isRequired
};
var Notification = import_react.default.forwardRef((props, ref) => {
  const Icon = props.type ? validationIcons[props.type] : SvgInfoStroke;
  const hue = props.type && validationHues[props.type];
  return import_react.default.createElement(StyledNotification, _extends$6({
    ref,
    type: props.type,
    isFloating: true
  }, props), props.type && import_react.default.createElement(StyledIcon, {
    hue
  }, import_react.default.createElement(Icon, null)), props.children);
});
Notification.displayName = "Notification";
Notification.propTypes = {
  type: import_prop_types.default.oneOf(TYPE)
};
var Well = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledWell, _extends$6({
  ref
}, props)));
Well.displayName = "Well";
Well.propTypes = {
  isRecessed: import_prop_types.default.bool,
  isFloating: import_prop_types.default.bool
};
var _path$1;
function _extends$1() {
  _extends$1 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$1.apply(this, arguments);
}
var SvgXStroke$1 = function SvgXStroke(props) {
  return React.createElement("svg", _extends$1({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$1 || (_path$1 = React.createElement("path", {
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M3 9l6-6m0 6L3 3"
  })));
};
var Close = import_react.default.forwardRef((props, ref) => {
  const ariaLabel = useText(Close, props, "aria-label", "Close");
  const hue = useNotificationsContext();
  return import_react.default.createElement(StyledClose, _extends$6({
    ref,
    hue,
    "aria-label": ariaLabel
  }, props), import_react.default.createElement(SvgXStroke$1, null));
});
Close.displayName = "Close";
var Paragraph = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledParagraph, _extends$6({
  ref
}, props)));
Paragraph.displayName = "Paragraph";
var Title = import_react.default.forwardRef((props, ref) => import_react.default.createElement(StyledTitle, _extends$6({
  ref
}, props)));
Title.displayName = "Title";
var getInitialState = () => {
  return {
    toasts: []
  };
};
var toasterReducer = (state, action) => {
  switch (action.type) {
    case "ADD_TOAST": {
      return {
        ...state,
        toasts: [...state.toasts, action.payload]
      };
    }
    case "REMOVE_TOAST": {
      const filteredToasts = state.toasts.filter((toast) => toast.id !== action.payload);
      return {
        ...state,
        toasts: filteredToasts
      };
    }
    case "UPDATE_TOAST": {
      const updatedToasts = state.toasts.map((toast) => {
        if (toast.id !== action.payload.id) {
          return toast;
        }
        const updatedToast = toast;
        const {
          content,
          ...newOptions
        } = action.payload.options;
        if (content) {
          updatedToast.content = content;
        }
        updatedToast.options = {
          ...updatedToast.options,
          ...newOptions
        };
        return updatedToast;
      });
      return {
        ...state,
        toasts: updatedToasts
      };
    }
    case "REMOVE_ALL_TOASTS": {
      return {
        ...state,
        toasts: []
      };
    }
    default:
      throw new Error("Invalid toaster reducer action");
  }
};
var ToastContext = (0, import_react.createContext)(void 0);
var DEFAULT_TOAST_OPTIONS = {
  autoDismiss: 5e3,
  placement: "top-end"
};
var useToast = () => {
  const context = (0, import_react.useContext)(ToastContext);
  if (context === void 0) {
    throw new Error('useToast() must be used within a "ToastProvider"');
  }
  const {
    dispatch,
    state
  } = context;
  const addToast = (0, import_react.useCallback)(function(content) {
    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    const mergedOptions = {
      ...DEFAULT_TOAST_OPTIONS,
      ...options
    };
    const newToast = {
      id: mergedOptions.id || uid(content),
      content,
      options: mergedOptions
    };
    dispatch({
      type: "ADD_TOAST",
      payload: newToast
    });
    return newToast.id;
  }, [dispatch]);
  const removeToast = (0, import_react.useCallback)((id) => {
    dispatch({
      type: "REMOVE_TOAST",
      payload: id
    });
  }, [dispatch]);
  const updateToast = (0, import_react.useCallback)((id, options) => {
    dispatch({
      type: "UPDATE_TOAST",
      payload: {
        id,
        options
      }
    });
  }, [dispatch]);
  const removeAllToasts = (0, import_react.useCallback)(() => {
    dispatch({
      type: "REMOVE_ALL_TOASTS"
    });
  }, [dispatch]);
  return {
    addToast,
    removeToast,
    updateToast,
    removeAllToasts,
    toasts: state.toasts
  };
};
var Toast = (_ref) => {
  let {
    toast,
    pauseTimers
  } = _ref;
  const {
    removeToast
  } = useToast();
  const {
    id
  } = toast;
  const {
    autoDismiss
  } = toast.options;
  const [remainingTime, setRemainingTime] = (0, import_react.useState)(NaN);
  const startTimeRef = (0, import_react.useRef)(Date.now());
  const previousRemainingTimeRef = (0, import_react.useRef)(remainingTime);
  (0, import_react.useEffect)(() => {
    if (typeof autoDismiss === "number") {
      setRemainingTime(autoDismiss);
    } else {
      setRemainingTime(NaN);
    }
  }, [autoDismiss]);
  (0, import_react.useEffect)(() => {
    if (pauseTimers && !isNaN(remainingTime)) {
      previousRemainingTimeRef.current = remainingTime - (Date.now() - startTimeRef.current);
      setRemainingTime(NaN);
    } else if (!pauseTimers && isNaN(remainingTime) && !isNaN(previousRemainingTimeRef.current)) {
      setRemainingTime(previousRemainingTimeRef.current);
    }
  }, [pauseTimers, remainingTime]);
  (0, import_react.useEffect)(() => {
    let timeout;
    if (!isNaN(remainingTime)) {
      startTimeRef.current = Date.now();
      timeout = setTimeout(() => {
        removeToast(id);
      }, remainingTime);
    }
    return () => {
      clearTimeout(timeout);
    };
  }, [id, pauseTimers, remainingTime, removeToast]);
  const close = (0, import_react.useCallback)(() => {
    removeToast(toast.id);
  }, [removeToast, toast.id]);
  return toast.content({
    close
  });
};
var TRANSITION_CLASS = "garden-toast-transition";
var DEFAULT_DURATION = "400ms";
var StyledFadeInTransition = styled_components_browser_esm_default.div.withConfig({
  displayName: "styled__StyledFadeInTransition",
  componentId: "sc-nq0usb-0"
})(["transition:opacity ", " ease-in 300ms;opacity:", ";margin-bottom:", "px;", " &.", "-enter{transform:translateY( ", " );opacity:0;max-height:0;}&.", "-enter-active{transform:translateY(0);transition:opacity ", " ease-in,transform ", " cubic-bezier(0.15,0.85,0.35,1.2),max-height ", ";opacity:1;max-height:500px;}&.", "-exit{opacity:1;max-height:500px;}&.", "-exit-active{transition:opacity 550ms ease-out,max-height ", " linear 150ms;opacity:0;max-height:0;}"], DEFAULT_DURATION, (p) => p.isHidden ? "0 !important" : 1, (p) => p.theme.space.base * 2, (p) => p.isHidden && hideVisually(), TRANSITION_CLASS, (props) => {
  if (props.placement === "bottom-start" || props.placement === "bottom" || props.placement === "bottom-end") {
    return "100px";
  }
  return "-100px";
}, TRANSITION_CLASS, DEFAULT_DURATION, DEFAULT_DURATION, DEFAULT_DURATION, TRANSITION_CLASS, TRANSITION_CLASS, DEFAULT_DURATION);
StyledFadeInTransition.defaultProps = {
  theme: DEFAULT_THEME
};
var placementStyles = (props) => {
  const verticalDistance = `${props.theme.space.base * 16}px`;
  const horizontalDistance = `${props.theme.space.base * 3}px`;
  const topLeftStyles = Ae(["top:", ";left:", ";"], verticalDistance, horizontalDistance);
  const topStyles = Ae(["top:", ";left:50%;transform:translate(-50%,0);"], verticalDistance);
  const topRightStyles = Ae(["top:", ";right:", ";"], verticalDistance, horizontalDistance);
  const bottomLeftStyles = Ae(["bottom:", ";left:", ";"], verticalDistance, horizontalDistance);
  const bottomStyles = Ae(["bottom:", ";left:50%;transform:translate(-50%,0);"], verticalDistance);
  const bottomRightStyles = Ae(["right:", ";bottom:", ";"], horizontalDistance, verticalDistance);
  switch (props.toastPlacement) {
    case "top-start":
      if (props.theme.rtl) {
        return topRightStyles;
      }
      return topLeftStyles;
    case "top":
      return topStyles;
    case "top-end":
      if (props.theme.rtl) {
        return topLeftStyles;
      }
      return topRightStyles;
    case "bottom-start":
      if (props.theme.rtl) {
        return bottomRightStyles;
      }
      return bottomLeftStyles;
    case "bottom":
      return bottomStyles;
    case "bottom-end":
      if (props.theme.rtl) {
        return bottomLeftStyles;
      }
      return bottomRightStyles;
    default:
      return "";
  }
};
var StyledTransitionContainer = styled_components_browser_esm_default.div.withConfig({
  displayName: "styled__StyledTransitionContainer",
  componentId: "sc-nq0usb-1"
})(["position:fixed;z-index:", ";", ";"], (props) => props.toastZIndex, placementStyles);
StyledTransitionContainer.defaultProps = {
  theme: DEFAULT_THEME
};
var ToastSlot = (_ref) => {
  let {
    toasts,
    placement,
    zIndex,
    limit,
    ...props
  } = _ref;
  const [pauseTimers, setPauseTimers] = (0, import_react.useState)(false);
  const theme = (0, import_react.useContext)(Me);
  const environment = useDocument(theme);
  const handleVisibilityChange = (0, import_react.useCallback)((e) => {
    if (e.target.visibilityState === "visible") {
      setPauseTimers(false);
    } else {
      setPauseTimers(true);
    }
  }, []);
  (0, import_react.useEffect)(() => {
    if (environment) {
      environment.addEventListener("visibilitychange", handleVisibilityChange);
    }
    return () => {
      if (environment) {
        environment.removeEventListener("visibilitychange", handleVisibilityChange);
      }
    };
  }, [environment, handleVisibilityChange]);
  const handleMouseEnter = (0, import_react.useCallback)(() => {
    setPauseTimers(true);
  }, []);
  const handleMouseLeave = (0, import_react.useCallback)(() => {
    setPauseTimers(false);
  }, []);
  const isHidden = (0, import_react.useCallback)((index) => {
    if (placement === "bottom" || placement === "bottom-start" || placement === "bottom-end") {
      return index < toasts.length - limit;
    }
    return index >= limit;
  }, [limit, placement, toasts.length]);
  return import_react.default.createElement(StyledTransitionContainer, _extends$6({
    key: placement,
    toastPlacement: placement,
    toastZIndex: zIndex,
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave
  }, props), import_react.default.createElement(TransitionGroup_default, null, toasts.map((toast, index) => {
    const transitionRef = import_react.default.createRef();
    return import_react.default.createElement(CSSTransition_default, {
      key: toast.id,
      timeout: {
        enter: 400,
        exit: 550
      },
      unmountOnExit: true,
      classNames: TRANSITION_CLASS,
      nodeRef: transitionRef
    }, import_react.default.createElement(StyledFadeInTransition, {
      ref: transitionRef,
      placement,
      isHidden: isHidden(index)
    }, import_react.default.createElement(Toast, {
      toast,
      pauseTimers: pauseTimers || isHidden(index)
    })));
  })));
};
var ToastProvider = (_ref) => {
  let {
    limit,
    zIndex,
    placementProps = {},
    children
  } = _ref;
  const [state, dispatch] = (0, import_react.useReducer)(toasterReducer, getInitialState());
  const contextValue = (0, import_react.useMemo)(() => ({
    state,
    dispatch
  }), [state, dispatch]);
  const toastsByPlacement = (0, import_react.useCallback)((placement) => {
    let matchingToasts = state.toasts.filter((toast) => toast.options.placement === placement);
    if (placement === "bottom" || placement === "bottom-start" || placement === "bottom-end") {
      matchingToasts = matchingToasts.reverse();
    }
    return import_react.default.createElement(ToastSlot, _extends$6({
      placement,
      toasts: matchingToasts,
      zIndex,
      limit
    }, placementProps[placement]));
  }, [limit, state.toasts, zIndex, placementProps]);
  return import_react.default.createElement(ToastContext.Provider, {
    value: contextValue
  }, toastsByPlacement("top-start"), toastsByPlacement("top"), toastsByPlacement("top-end"), children, toastsByPlacement("bottom-start"), toastsByPlacement("bottom"), toastsByPlacement("bottom-end"));
};
ToastProvider.displayName = "ToastProvider";
ToastProvider.defaultProps = {
  limit: 4
};
ToastProvider.propTypes = {
  limit: import_prop_types.default.number,
  zIndex: import_prop_types.default.number,
  placementProps: import_prop_types.default.object
};
var GlobalAlertContext = (0, import_react.createContext)({
  type: "info"
});
var useGlobalAlertContext = () => (0, import_react.useContext)(GlobalAlertContext);
var GlobalAlertButton = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    isBasic,
    ...props
  } = _ref;
  const {
    type
  } = useGlobalAlertContext();
  return import_react.default.createElement(StyledGlobalAlertButton, _extends$6({
    ref,
    alertType: type
  }, props, {
    isPrimary: !isBasic,
    isBasic
  }));
});
GlobalAlertButton.displayName = "GlobalAlert.Button";
GlobalAlertButton.propTypes = {
  isBasic: import_prop_types.default.bool
};
var _path;
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var SvgXStroke2 = function SvgXStroke3(props) {
  return React.createElement("svg", _extends({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path || (_path = React.createElement("path", {
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M3 13L13 3m0 10L3 3"
  })));
};
var GlobalAlertClose = (0, import_react.forwardRef)((props, ref) => {
  const {
    type
  } = useGlobalAlertContext();
  const label = useText(GlobalAlertClose, props, "aria-label", "Close");
  return import_react.default.createElement(StyledGlobalAlertClose, _extends$6({
    ref,
    alertType: type
  }, props), import_react.default.createElement(SvgXStroke2, {
    role: "img",
    "aria-label": label
  }));
});
GlobalAlertClose.displayName = "GlobalAlert.Close";
var GlobalAlertContent = (0, import_react.forwardRef)((props, ref) => {
  return import_react.default.createElement(StyledGlobalAlertContent, _extends$6({
    ref
  }, props));
});
GlobalAlertContent.displayName = "GlobalAlert.Content";
var GlobalAlertTitle = (0, import_react.forwardRef)((props, ref) => {
  const {
    type
  } = useGlobalAlertContext();
  return import_react.default.createElement(StyledGlobalAlertTitle, _extends$6({
    alertType: type,
    ref
  }, props));
});
GlobalAlertTitle.displayName = "GlobalAlert.Title";
GlobalAlertTitle.propTypes = {
  isRegular: import_prop_types.default.bool
};
var GlobalAlertComponent = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    type,
    ...props
  } = _ref;
  return import_react.default.createElement(GlobalAlertContext.Provider, {
    value: (0, import_react.useMemo)(() => ({
      type
    }), [type])
  }, import_react.default.createElement(StyledGlobalAlert, _extends$6({
    ref,
    role: "status",
    alertType: type
  }, props), {
    success: import_react.default.createElement(StyledGlobalAlertIcon, null, import_react.default.createElement(SvgCheckCircleStroke, null)),
    error: import_react.default.createElement(StyledGlobalAlertIcon, null, import_react.default.createElement(SvgAlertErrorStroke, null)),
    warning: import_react.default.createElement(StyledGlobalAlertIcon, null, import_react.default.createElement(SvgAlertWarningStroke, null)),
    info: import_react.default.createElement(StyledGlobalAlertIcon, null, import_react.default.createElement(SvgInfoStroke, null))
  }[type], props.children));
});
GlobalAlertComponent.displayName = "GlobalAlert";
var GlobalAlert = GlobalAlertComponent;
GlobalAlert.Button = GlobalAlertButton;
GlobalAlert.Close = GlobalAlertClose;
GlobalAlert.Content = GlobalAlertContent;
GlobalAlert.Title = GlobalAlertTitle;
GlobalAlert.propTypes = {
  type: import_prop_types.default.oneOf(TYPE).isRequired
};
export {
  Alert,
  Close,
  GlobalAlert,
  Notification,
  Paragraph,
  Title,
  ToastProvider,
  Well,
  useToast
};
//# sourceMappingURL=@zendeskgarden_react-notifications.js.map
