import {
  KEYS,
  composeEventHandlers,
  getControlledValue
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/container-selection/dist/index.esm.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var stateReducer = (state, action) => {
  switch (action.type) {
    case "END":
    case "HOME":
    case "FOCUS":
    case "INCREMENT":
    case "DECREMENT": {
      return {
        ...state,
        focusedItem: action.payload
      };
    }
    case "MOUSE_SELECT": {
      return {
        ...state,
        selectedItem: action.payload,
        focusedItem: void 0
      };
    }
    case "KEYBOARD_SELECT": {
      return {
        ...state,
        selectedItem: action.payload
      };
    }
    case "EXIT_WIDGET": {
      return {
        ...state,
        focusedItem: void 0
      };
    }
    default:
      return state;
  }
};
var useSelection = function(_temp) {
  let {
    direction = "horizontal",
    defaultFocusedIndex = 0,
    defaultSelectedIndex,
    rtl,
    selectedItem,
    focusedItem,
    onSelect,
    onFocus
  } = _temp === void 0 ? {} : _temp;
  const isSelectedItemControlled = selectedItem !== void 0;
  const isFocusedItemControlled = focusedItem !== void 0;
  const refs = [];
  const items = [];
  const [state, dispatch] = (0, import_react.useReducer)(stateReducer, {
    selectedItem,
    focusedItem
  });
  const controlledFocusedItem = getControlledValue(focusedItem, state.focusedItem);
  const controlledSelectedItem = getControlledValue(selectedItem, state.selectedItem);
  (0, import_react.useEffect)(() => {
    if (controlledFocusedItem !== void 0) {
      const focusedIndex = items.indexOf(controlledFocusedItem);
      refs[focusedIndex] && refs[focusedIndex].current.focus();
    }
  }, [controlledFocusedItem]);
  (0, import_react.useEffect)(() => {
    if (selectedItem === void 0 && defaultSelectedIndex !== void 0) {
      onSelect && onSelect(items[defaultSelectedIndex]);
      if (!isSelectedItemControlled) {
        dispatch({
          type: "KEYBOARD_SELECT",
          payload: items[defaultSelectedIndex]
        });
      }
    }
  }, []);
  const getContainerProps = function(_temp2) {
    let {
      role = "listbox",
      ...other
    } = _temp2 === void 0 ? {} : _temp2;
    return {
      role: role === null ? void 0 : role,
      "data-garden-container-id": "containers.selection",
      "data-garden-container-version": "2.0.5",
      ...other
    };
  };
  const getItemProps = (_ref) => {
    let {
      selectedAriaKey = "aria-selected",
      role = "option",
      onFocus: onFocusCallback,
      onKeyDown,
      onClick,
      item,
      focusRef,
      refKey = "ref",
      ...other
    } = _ref;
    refs.push(focusRef);
    items.push(item);
    const isSelected = controlledSelectedItem === item;
    const isFocused = controlledFocusedItem === void 0 ? isSelected : controlledFocusedItem === item;
    const tabIndex = isFocused || controlledSelectedItem === void 0 && controlledFocusedItem === void 0 && items.indexOf(item) === defaultFocusedIndex ? 0 : -1;
    const verticalDirection = direction === "vertical" || direction === "both";
    const horizontalDirection = direction === "horizontal" || direction === "both";
    const handleFocus = () => {
      onFocus && onFocus(item);
      if (!isFocusedItemControlled) {
        dispatch({
          type: "FOCUS",
          payload: item
        });
      }
    };
    const handleClick = () => {
      onSelect && onSelect(item);
      onFocus && onFocus();
      if (!isSelectedItemControlled) {
        dispatch({
          type: "MOUSE_SELECT",
          payload: item
        });
      }
    };
    const handleKeyDown = (event) => {
      let nextIndex;
      let currentIndex;
      if (isFocusedItemControlled) {
        currentIndex = items.indexOf(focusedItem);
      } else {
        currentIndex = items.indexOf(state.focusedItem || state.selectedItem);
      }
      const onIncrement = () => {
        nextIndex = currentIndex + 1;
        if (currentIndex === items.length - 1) {
          nextIndex = 0;
        }
        !isFocusedItemControlled && dispatch({
          type: "INCREMENT",
          payload: items[nextIndex]
        });
        onFocus && onFocus(items[nextIndex]);
      };
      const onDecrement = () => {
        nextIndex = currentIndex - 1;
        if (currentIndex === 0) {
          nextIndex = items.length - 1;
        }
        !isFocusedItemControlled && dispatch({
          type: "DECREMENT",
          payload: items[nextIndex]
        });
        onFocus && onFocus(items[nextIndex]);
      };
      const hasModifierKeyPressed = event.ctrlKey || event.metaKey || event.shiftKey || event.altKey;
      if (!hasModifierKeyPressed) {
        if (event.key === KEYS.UP && verticalDirection || event.key === KEYS.LEFT && horizontalDirection) {
          if (rtl && horizontalDirection) {
            onIncrement();
          } else {
            onDecrement();
          }
          event.preventDefault();
        } else if (event.key === KEYS.DOWN && verticalDirection || event.key === KEYS.RIGHT && horizontalDirection) {
          if (rtl && horizontalDirection) {
            onDecrement();
          } else {
            onIncrement();
          }
          event.preventDefault();
        } else if (event.key === KEYS.HOME) {
          if (!isFocusedItemControlled) {
            dispatch({
              type: "HOME",
              payload: items[0]
            });
          }
          onFocus && onFocus(items[0]);
          event.preventDefault();
        } else if (event.key === KEYS.END) {
          if (!isFocusedItemControlled) {
            dispatch({
              type: "END",
              payload: items[items.length - 1]
            });
          }
          onFocus && onFocus(items[items.length - 1]);
          event.preventDefault();
        } else if (event.key === KEYS.SPACE || event.key === KEYS.ENTER) {
          onSelect && onSelect(item);
          if (!isSelectedItemControlled) {
            dispatch({
              type: "KEYBOARD_SELECT",
              payload: item
            });
          }
          event.preventDefault();
        }
      }
    };
    const onBlur = (event) => {
      if (event.target.tabIndex === 0) {
        if (!isFocusedItemControlled) {
          dispatch({
            type: "EXIT_WIDGET"
          });
        }
        onFocus && onFocus();
      }
    };
    return {
      role: role === null ? void 0 : role,
      tabIndex,
      [selectedAriaKey]: selectedAriaKey ? isSelected : void 0,
      [refKey]: focusRef,
      onFocus: composeEventHandlers(onFocusCallback, handleFocus),
      onClick: composeEventHandlers(onClick, handleClick),
      onKeyDown: composeEventHandlers(onKeyDown, handleKeyDown),
      onBlur,
      ...other
    };
  };
  return {
    focusedItem: controlledFocusedItem,
    selectedItem: controlledSelectedItem,
    getItemProps,
    getContainerProps
  };
};
var SelectionContainer = (_ref) => {
  let {
    children,
    render = children,
    ...options
  } = _ref;
  return import_react.default.createElement(import_react.default.Fragment, null, render(useSelection(options)));
};
SelectionContainer.defaultProps = {
  direction: "horizontal",
  defaultFocusedIndex: 0
};
SelectionContainer.propTypes = {
  children: import_prop_types.default.func,
  render: import_prop_types.default.func,
  rtl: import_prop_types.default.bool,
  direction: import_prop_types.default.oneOf(["horizontal", "vertical", "both"]),
  defaultFocusedIndex: import_prop_types.default.number,
  defaultSelectedIndex: import_prop_types.default.number,
  focusedItem: import_prop_types.default.any,
  selectedItem: import_prop_types.default.any,
  onSelect: import_prop_types.default.func,
  onFocus: import_prop_types.default.func
};

export {
  useSelection
};
//# sourceMappingURL=chunk-YKXCMXPN.js.map
