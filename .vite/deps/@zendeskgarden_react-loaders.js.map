{"version": 3, "sources": ["../../node_modules/@zendeskgarden/react-loaders/dist/index.esm.js", "../../node_modules/@zendeskgarden/container-schedule/dist/index.esm.js"], "sourcesContent": ["/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { forwardRef, useContext, useRef, useEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport styled, { keyframes, css, ThemeContext } from 'styled-components';\nimport { useSchedule } from '@zendeskgarden/container-schedule';\nimport { DEFAULT_THEME, retrieveComponentStyles, getColor, getLineHeight, useDocument, useText } from '@zendeskgarden/react-theming';\nimport { rgba } from 'polished';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nconst dotOneKeyframes = keyframes([\"0%{transform:translate(0,5px);}3%{transform:translate(1px,-5px);}6%{transform:translate(3px,-15px);}8%{transform:translate(5px,-18px);}9%{transform:translate(7px,-21px);}11%{transform:translate(8px,-22px);}13%{transform:translate(9px,-23px);}16%{transform:translate(12px,-25px);}18%{transform:translate(13px,-26px);}23%{transform:translate(18px,-26px);}24%{transform:translate(19px,-25px);}28%{transform:translate(22px,-23px);}31%{transform:translate(24px,-21px);}33%{transform:translate(26px,-18px);}34%{transform:translate(28px,-14px);}36%{transform:translate(29px,-12px);}38%{transform:translate(30px,-5px);}39%{transform:translate(31px,5px);}54%{transform:translate(31px,3px);}59%{transform:translate(33px);}61%{transform:translate(43px);}63%{transform:translate(48px);}64%{transform:translate(51px);}66%{transform:translate(53px);}68%{transform:translate(55px);}69%{transform:translate(57px);}76%{transform:translate(60px);}81%{transform:translate(61px);}83%,100%{transform:translate(62px);}\"]);\nconst dotTwoKeyframes = keyframes([\"4%{transform:translate(0);}6%{transform:translate(-1px);}8%{transform:translate(-2px);}9%{transform:translate(-5px);}11%{transform:translate(-7px);}13%{transform:translate(-12px);}14%{transform:translate(-17px);}16%{transform:translate(-19px);}18%{transform:translate(-22px);}19%{transform:translate(-25px);}21%{transform:translate(-26px);}23%{transform:translate(-27px);}24%{transform:translate(-28px);}26%{transform:translate(-29px);}29%{transform:translate(-30px);}33%,89%{transform:translate(-31px);}91%{transform:translate(-31px,1px);}94%{transform:translate(-31px,2px);}98%{transform:translate(-31px,3px);}99%{transform:translate(-31px,4px);}100%{transform:translate(-31px,5px);}\"]);\nconst dotThreeKeyframes = keyframes([\"39%{transform:translate(0);}44%{transform:translate(0,1px);}46%{transform:translate(0,2px);}48%{transform:translate(0,3px);}49%{transform:translate(0,4px);}51%{transform:translate(0,5px);}53%{transform:translate(-1px,-6px);}54%{transform:translate(-2px,-13px);}56%{transform:translate(-3px,-15px);}58%{transform:translate(-5px,-19px);}59%{transform:translate(-7px,-21px);}61%{transform:translate(-8px,-22px);}63%{transform:translate(-9px,-24px);}64%{transform:translate(-11px,-25px);}66%{transform:translate(-12px,-26px);}74%{transform:translate(-19px,-26px);}76%{transform:translate(-20px,-25px);}78%{transform:translate(-22px,-24px);}81%{transform:translate(-24px,-21px);}83%{transform:translate(-26px,-19px);}84%{transform:translate(-28px,-15px);}86%{transform:translate(-29px,-13px);}88%{transform:translate(-30px,-6px);}89%{transform:translate(-31px,5px);}91%{transform:translate(-31px,4px);}93%{transform:translate(-31px,3px);}94%{transform:translate(-31px,2px);}98%{transform:translate(-31px,1px);}100%{transform:translate(-31px);}\"]);\n\nconst StyledDotsCircle = styled.circle.attrs({\n  cy: 36,\n  r: 9\n}).withConfig({\n  displayName: \"StyledDots__StyledDotsCircle\",\n  componentId: \"sc-1ltah7e-0\"\n})([\"\"]);\nStyledDotsCircle.defaultProps = {\n  theme: DEFAULT_THEME\n};\nconst StyledDotsCircleOne = styled(StyledDotsCircle).attrs({\n  cx: 9\n}).withConfig({\n  displayName: \"StyledDots__StyledDotsCircleOne\",\n  componentId: \"sc-1ltah7e-1\"\n})([\"animation:\", \";\"], _ref => {\n  let {\n    duration\n  } = _ref;\n  return css([\"\", \" \", \"ms linear infinite\"], dotOneKeyframes, duration);\n});\nStyledDotsCircleOne.defaultProps = {\n  theme: DEFAULT_THEME\n};\nconst StyledDotsCircleTwo = styled(StyledDotsCircle).attrs(() => ({\n  cx: 40\n})).withConfig({\n  displayName: \"StyledDots__StyledDotsCircleTwo\",\n  componentId: \"sc-1ltah7e-2\"\n})([\"animation:\", \";\"], _ref2 => {\n  let {\n    duration\n  } = _ref2;\n  return css([\"\", \" \", \"ms linear infinite\"], dotTwoKeyframes, duration);\n});\nStyledDotsCircleTwo.defaultProps = {\n  theme: DEFAULT_THEME\n};\nconst StyledDotsCircleThree = styled(StyledDotsCircle).attrs(() => ({\n  cx: 71\n})).withConfig({\n  displayName: \"StyledDots__StyledDotsCircleThree\",\n  componentId: \"sc-1ltah7e-3\"\n})([\"animation:\", \";\"], _ref3 => {\n  let {\n    duration\n  } = _ref3;\n  return css([\"\", \" \", \"ms linear infinite\"], dotThreeKeyframes, duration);\n});\nStyledDotsCircleThree.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$5 = 'loaders.loading_placeholder';\nconst StyledLoadingPlaceholder = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$5,\n  'data-garden-version': '8.69.1',\n  role: 'progressbar'\n}).withConfig({\n  displayName: \"StyledLoadingPlaceholder\",\n  componentId: \"sc-x3bwsx-0\"\n})([\"display:inline-block;width:\", \";height:\", \";font-size:\", \";\", \"\"], props => props.width || '1em', props => props.height || '0.9em', props => props.fontSize, props => retrieveComponentStyles(COMPONENT_ID$5, props));\nStyledLoadingPlaceholder.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst sizeToHeight = (size, theme) => {\n  switch (size) {\n    case 'small':\n      return theme.space.base / 2;\n    case 'medium':\n      return theme.space.base * 1.5;\n    default:\n      return theme.space.base * 3;\n  }\n};\nconst sizeToBorderRadius = (size, theme) => sizeToHeight(size, theme) / 2;\nconst PROGRESS_BACKGROUND_COMPONENT_ID = 'loaders.progress_background';\nconst StyledProgressBackground = styled.div.attrs(props => ({\n  'data-garden-id': PROGRESS_BACKGROUND_COMPONENT_ID,\n  'data-garden-version': '8.69.1',\n  borderRadius: props.borderRadius || sizeToBorderRadius(props.size, props.theme)\n})).withConfig({\n  displayName: \"StyledProgress__StyledProgressBackground\",\n  componentId: \"sc-2g8w4s-0\"\n})([\"margin:\", \"px 0;border-radius:\", \"px;background-color:\", \";color:\", \";\", \"\"], props => props.theme.space.base * 2, props => props.borderRadius, props => getColor('neutralHue', 200, props.theme), props => props.color || getColor('successHue', 600, props.theme), props => retrieveComponentStyles(PROGRESS_BACKGROUND_COMPONENT_ID, props));\nStyledProgressBackground.defaultProps = {\n  theme: DEFAULT_THEME\n};\nconst PROGESS_INDICATOR_COMPONENT_ID = 'loaders.progress_indicator';\nconst StyledProgressIndicator = styled.div.attrs(props => ({\n  'data-garden-id': PROGESS_INDICATOR_COMPONENT_ID,\n  'data-garden-version': '8.69.1',\n  height: props.height || sizeToHeight(props.size, props.theme),\n  borderRadius: props.borderRadius || sizeToBorderRadius(props.size, props.theme)\n})).withConfig({\n  displayName: \"StyledProgress__StyledProgressIndicator\",\n  componentId: \"sc-2g8w4s-1\"\n})([\"transition:width 0.1s ease-in-out;border-radius:\", \"px;background:currentColor;width:\", \"%;height:\", \"px;\", \"\"], props => props.borderRadius, props => props.value, props => props.height, props => retrieveComponentStyles(PROGESS_INDICATOR_COMPONENT_ID, props));\nStyledProgressIndicator.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$4 = 'loaders.skeleton';\nconst fadeInAnimation = keyframes([\"0%,60%{opacity:0;}100%{opacity:1;}\"]);\nconst skeletonAnimation = keyframes([\"100%{left:100%;}\"]);\nconst skeletonRtlAnimation = keyframes([\"100%{right:100%;}\"]);\nconst retrieveSkeletonBackgroundColor = _ref => {\n  let {\n    theme,\n    isLight\n  } = _ref;\n  if (isLight) {\n    return css([\"background-color:\", \";\"], rgba(theme.colors.background, 0.2));\n  }\n  return css([\"background-color:\", \";\"], getColor('neutralHue', 800, theme, 0.1));\n};\nconst retrieveSkeletonAnimation = _ref2 => {\n  let {\n    theme\n  } = _ref2;\n  if (theme.rtl) {\n    return css([\"right:-1800px;animation:\", \" 1.5s ease-in-out 300ms infinite;\"], skeletonRtlAnimation);\n  }\n  return css([\"left:-1800px;animation:\", \" 1.5s ease-in-out 300ms infinite;\"], skeletonAnimation);\n};\nconst retrieveSkeletonGradient = _ref3 => {\n  let {\n    theme,\n    isLight\n  } = _ref3;\n  return css([\"background-image:linear-gradient( \", \",transparent,\", \",transparent );\"], theme.rtl ? '-45deg' : '45deg', isLight ? getColor('chromeHue', 700, theme, 0.4) : rgba(theme.colors.background, 0.6));\n};\nconst StyledSkeleton = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID$4,\n  'data-garden-version': '8.69.1'\n}).withConfig({\n  displayName: \"StyledSkeleton\",\n  componentId: \"sc-1raozze-0\"\n})([\"display:inline-block;position:relative;animation:\", \" 750ms linear;border-radius:\", \";width:\", \";height:\", \";overflow:hidden;line-height:\", \";\", \" &::before{position:absolute;top:0;width:1000px;height:100%;content:'';\", \" \", \"}\", \";\"], fadeInAnimation, props => props.theme.borderRadii.md, props => props.customWidth, props => props.customHeight, props => getLineHeight(props.theme.fontSizes.sm, props.theme.space.base * 5), retrieveSkeletonBackgroundColor, retrieveSkeletonAnimation, retrieveSkeletonGradient, props => retrieveComponentStyles(COMPONENT_ID$4, props));\nStyledSkeleton.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst StyledSpinnerCircle = styled.circle.attrs(props => ({\n  cx: 40,\n  cy: 40,\n  r: 34,\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeLinecap: 'round',\n  strokeWidth: props.strokeWidthValue,\n  strokeDasharray: `${props.dasharrayValue} 250`,\n  transform: props.transform\n})).withConfig({\n  displayName: \"StyledSpinnerCircle\",\n  componentId: \"sc-o4kd70-0\"\n})([\"\"]);\nStyledSpinnerCircle.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst StyledSVG = styled.svg.attrs(props => ({\n  'data-garden-version': '8.69.1',\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: props.width,\n  height: props.height,\n  focusable: 'false',\n  viewBox: `0 0 ${props.width} ${props.height}`,\n  role: 'img'\n})).withConfig({\n  displayName: \"StyledSVG\",\n  componentId: \"sc-1xtc3kx-0\"\n})([\"width:\", \";height:\", \";color:\", \";font-size:\", \";\", \";\"], props => props.containerWidth || '1em', props => props.containerHeight || '0.9em', props => props.color || 'inherit', props => props.fontSize || 'inherit', props => retrieveComponentStyles(props.dataGardenId, props));\nStyledSVG.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$3 = 'loaders.inline';\nconst PULSE_ANIMATION = keyframes([\"0%,100%{opacity:.2;}50%{opacity:.8;}\"]);\nconst StyledCircle = styled.circle.attrs({\n  fill: 'currentColor',\n  cy: 2,\n  r: 2\n}).withConfig({\n  displayName: \"StyledInline__StyledCircle\",\n  componentId: \"sc-fxsb9l-0\"\n})([\"\"]);\nStyledCircle.defaultProps = {\n  theme: DEFAULT_THEME\n};\nconst StyledInline = styled.svg.attrs(props => ({\n  'data-garden-id': COMPONENT_ID$3,\n  'data-garden-version': '8.69.1',\n  viewBox: '0 0 16 4',\n  width: props.size,\n  height: props.size * 0.25\n})).withConfig({\n  displayName: \"StyledInline\",\n  componentId: \"sc-fxsb9l-1\"\n})([\"color:\", \";\", \"{opacity:0.2;&:nth-child(1){animation:\", \" 1s infinite;animation-delay:\", \";}&:nth-child(2){animation:\", \" 1s infinite;animation-delay:0.2s;}&:nth-child(3){animation:\", \" 1s infinite;animation-delay:\", \";}}\", \"\"], props => props.color, StyledCircle, PULSE_ANIMATION, props => props.theme.rtl ? 'unset' : '0.4s', PULSE_ANIMATION, PULSE_ANIMATION, props => props.theme.rtl ? '0.4s' : 'unset', props => retrieveComponentStyles(COMPONENT_ID$3, props));\nStyledInline.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$2 = 'loaders.dots';\nconst Dots = forwardRef((_ref, ref) => {\n  let {\n    size,\n    color,\n    duration,\n    delayMS,\n    ...other\n  } = _ref;\n  const theme = useContext(ThemeContext);\n  const environment = useDocument(theme);\n  const canTransformSVG = useRef(null);\n  if (environment && canTransformSVG.current === null) {\n    const svg = environment.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    canTransformSVG.current = 'transform' in svg;\n  }\n  const {\n    delayComplete\n  } = useSchedule({\n    duration,\n    delayMS,\n    loop: true\n  });\n  const dotOne = useRef(null);\n  const dotTwo = useRef(null);\n  const dotThree = useRef(null);\n  useEffect(() => {\n    if (!canTransformSVG.current && delayComplete) {\n      const transforms = [window.getComputedStyle(dotOne.current).getPropertyValue('transform'), window.getComputedStyle(dotTwo.current).getPropertyValue('transform'), window.getComputedStyle(dotThree.current).getPropertyValue('transform')];\n      dotOne.current.setAttribute('transform', transforms[0]);\n      dotTwo.current.setAttribute('transform', transforms[1]);\n      dotThree.current.setAttribute('transform', transforms[2]);\n    }\n  });\n  if (!delayComplete && delayMS !== 0) {\n    return React.createElement(StyledLoadingPlaceholder, {\n      fontSize: size\n    }, \"\\xA0\");\n  }\n  return React.createElement(StyledSVG, _extends({\n    ref: ref,\n    fontSize: size,\n    color: color,\n    width: \"80\",\n    height: \"72\",\n    dataGardenId: COMPONENT_ID$2\n  }, other), React.createElement(\"g\", {\n    fill: \"currentColor\"\n  }, React.createElement(StyledDotsCircleOne, {\n    duration: duration,\n    ref: dotOne\n  }), React.createElement(StyledDotsCircleTwo, {\n    duration: duration,\n    ref: dotTwo\n  }), React.createElement(StyledDotsCircleThree, {\n    duration: duration,\n    ref: dotThree\n  })));\n});\nDots.displayName = 'Dots';\nDots.propTypes = {\n  size: PropTypes.any,\n  duration: PropTypes.number,\n  color: PropTypes.string,\n  delayMS: PropTypes.number\n};\nDots.defaultProps = {\n  size: 'inherit',\n  color: 'inherit',\n  duration: 1250,\n  delayMS: 750\n};\n\nconst SIZE = ['small', 'medium', 'large'];\n\nconst COMPONENT_ID$1 = 'loaders.progress';\nconst Progress = React.forwardRef((_ref, ref) => {\n  let {\n    value,\n    size,\n    'aria-label': label,\n    ...other\n  } = _ref;\n  const percentage = Math.max(0, Math.min(100, value));\n  const ariaLabel = useText(Progress, {\n    'aria-label': label\n  }, 'aria-label', 'Progress');\n  return (\n    React.createElement(StyledProgressBackground, _extends({\n      \"data-garden-id\": COMPONENT_ID$1,\n      \"data-garden-version\": '8.69.1',\n      \"aria-valuemax\": 100,\n      \"aria-valuemin\": 0,\n      \"aria-valuenow\": percentage,\n      role: \"progressbar\",\n      size: size,\n      ref: ref,\n      \"aria-label\": ariaLabel\n    }, other), React.createElement(StyledProgressIndicator, {\n      value: percentage,\n      size: size\n    }))\n  );\n});\nProgress.displayName = 'Progress';\nProgress.propTypes = {\n  color: PropTypes.string,\n  value: PropTypes.number.isRequired,\n  size: PropTypes.oneOf(SIZE)\n};\nProgress.defaultProps = {\n  value: 0,\n  size: 'medium'\n};\n\nconst Skeleton = forwardRef((_ref, ref) => {\n  let {\n    width,\n    height,\n    isLight,\n    ...other\n  } = _ref;\n  return React.createElement(StyledSkeleton, _extends({\n    ref: ref,\n    isLight: isLight,\n    customWidth: width,\n    customHeight: height\n  }, other), \"\\xA0\");\n});\nSkeleton.displayName = 'Skeleton';\nSkeleton.propTypes = {\n  width: PropTypes.string,\n  height: PropTypes.string,\n  isLight: PropTypes.bool\n};\nSkeleton.defaultProps = {\n  width: '100%',\n  height: '100%'\n};\n\nconst STROKE_WIDTH_FRAMES = {\n  0: 6,\n  14: 5,\n  26: 4,\n  36: 3,\n  46: 2,\n  58: 3,\n  70: 4,\n  80: 5,\n  91: 6\n};\nconst ROTATION_FRAMES = {\n  0: -90,\n  8: -81,\n  36: -30,\n  41: -18,\n  44: -8,\n  48: 0,\n  55: 22,\n  63: 53,\n  64: 62,\n  66: 67,\n  68: 78,\n  69: 90,\n  71: 99,\n  73: 112,\n  74: 129,\n  76: 138,\n  78: 159,\n  79: 180,\n  81: 190,\n  83: 207,\n  84: 221,\n  86: 226,\n  88: 235,\n  90: 243,\n  99: 270\n};\nconst DASHARRAY_FRAMES = {\n  0: 0,\n  13: 2,\n  26: 13,\n  53: 86,\n  58: 90,\n  63: 89,\n  64: 88,\n  66: 86,\n  68: 83,\n  69: 81,\n  71: 76,\n  73: 70,\n  74: 62,\n  76: 58,\n  78: 47,\n  79: 37,\n  81: 31,\n  83: 23,\n  84: 16,\n  88: 10,\n  89: 7,\n  98: 1,\n  99: 0\n};\n\nconst COMPONENT_ID = 'loaders.spinner';\nconst TOTAL_FRAMES = 100;\nconst computeFrames = (frames, duration) => {\n  return Object.entries(frames).reduce((acc, item, index, arr) => {\n    const [frame, value] = item;\n    const [nextFrame, nextValue] = arr[index + 1] || [TOTAL_FRAMES, arr[0][1]];\n    const diff = parseInt(nextFrame, 10) - parseInt(frame, 10);\n    const frameHz = 1000 / 60;\n    let subDuration = duration / (TOTAL_FRAMES - 1) * diff;\n    let lastValue = value;\n    acc[frame] = value;\n    for (let idx = 0; idx < diff; idx++) {\n      lastValue = lastValue + (nextValue - lastValue) * (frameHz / subDuration);\n      subDuration = duration / (TOTAL_FRAMES - 1) * (diff - idx);\n      acc[parseInt(frame, 10) + idx + 1] = lastValue;\n    }\n    acc[nextFrame] = nextValue;\n    return acc;\n  }, {});\n};\nconst Spinner = forwardRef((_ref, ref) => {\n  let {\n    size,\n    duration,\n    color,\n    delayMS,\n    ...other\n  } = _ref;\n  const strokeWidthValues = computeFrames(STROKE_WIDTH_FRAMES, duration);\n  const rotationValues = computeFrames(ROTATION_FRAMES, duration);\n  const dasharrayValues = computeFrames(DASHARRAY_FRAMES, duration);\n  const {\n    elapsed,\n    delayComplete\n  } = useSchedule({\n    duration,\n    delayMS\n  });\n  const frame = (elapsed * 100).toFixed(0);\n  const strokeWidthValue = strokeWidthValues[frame];\n  const rotationValue = rotationValues[frame];\n  const dasharrayValue = dasharrayValues[frame];\n  const WIDTH = 80;\n  const HEIGHT = 80;\n  if (!delayComplete && delayMS !== 0) {\n    return React.createElement(StyledLoadingPlaceholder, {\n      width: \"1em\",\n      height: \"1em\",\n      fontSize: size\n    }, \"\\xA0\");\n  }\n  return React.createElement(StyledSVG, _extends({\n    ref: ref,\n    fontSize: size,\n    color: color,\n    width: WIDTH,\n    height: HEIGHT,\n    dataGardenId: COMPONENT_ID,\n    containerHeight: \"1em\",\n    containerWidth: \"1em\"\n  }, other), React.createElement(StyledSpinnerCircle, {\n    dasharrayValue: dasharrayValue,\n    strokeWidthValue: strokeWidthValue,\n    transform: `rotate(${rotationValue}, ${WIDTH / 2}, ${HEIGHT / 2})`\n  }));\n});\nSpinner.displayName = 'Spinner';\nSpinner.propTypes = {\n  size: PropTypes.any,\n  duration: PropTypes.number,\n  color: PropTypes.string,\n  delayMS: PropTypes.number\n};\nSpinner.defaultProps = {\n  size: 'inherit',\n  duration: 1250,\n  color: 'inherit',\n  delayMS: 750\n};\n\nconst Inline = forwardRef((_ref, ref) => {\n  let {\n    size,\n    color,\n    ...other\n  } = _ref;\n  const ariaLabel = useText(Inline, other, 'aria-label', 'loading');\n  return (\n    React.createElement(StyledInline, _extends({\n      ref: ref,\n      size: size,\n      color: color,\n      \"aria-label\": ariaLabel,\n      role: \"img\"\n    }, other), React.createElement(StyledCircle, {\n      cx: \"14\"\n    }), React.createElement(StyledCircle, {\n      cx: \"8\"\n    }), React.createElement(StyledCircle, {\n      cx: \"2\"\n    }))\n  );\n});\nInline.displayName = 'Inline';\nInline.propTypes = {\n  size: PropTypes.number,\n  color: PropTypes.string\n};\nInline.defaultProps = {\n  size: 16,\n  color: 'inherit'\n};\n\nexport { Dots, Inline, Progress, Skeleton, Spinner };\n", "/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { useState, useLayoutEffect } from 'react';\nimport PropTypes from 'prop-types';\n\nconst useSchedule = function (_temp) {\n  let {\n    duration = 1250,\n    delayMS = 750,\n    loop = true\n  } = _temp === void 0 ? {} : _temp;\n  const [elapsed, setElapsed] = useState(0);\n  const [delayComplete, setDelayComplete] = useState(false);\n  useLayoutEffect(() => {\n    let raf;\n    let start;\n    let loopTimeout;\n    let destroyed = false;\n    const tick = () => {\n      if (destroyed) {\n        return;\n      }\n      raf = requestAnimationFrame(performAnimationFrame);\n    };\n    const performAnimationFrame = () => {\n      setElapsed(Date.now() - start);\n      tick();\n    };\n    const onStart = () => {\n      if (destroyed) {\n        return;\n      }\n      loopTimeout = setTimeout(() => {\n        cancelAnimationFrame(raf);\n        setElapsed(Date.now() - start);\n        if (loop) onStart();\n      }, duration);\n      start = Date.now();\n      setDelayComplete(true);\n      tick();\n    };\n    const renderingDelayTimeout = setTimeout(onStart, delayMS);\n    return () => {\n      destroyed = true;\n      clearTimeout(renderingDelayTimeout);\n      clearTimeout(loopTimeout);\n      cancelAnimationFrame(raf);\n    };\n  }, [duration, delayMS, loop]);\n  return {\n    elapsed: Math.min(1, elapsed / duration),\n    delayMS,\n    delayComplete\n  };\n};\n\nconst ScheduleContainer = _ref => {\n  let {\n    children,\n    render = children,\n    ...props\n  } = _ref;\n  return React.createElement(React.Fragment, null, render(useSchedule(props)));\n};\nScheduleContainer.defaultProps = {\n  duration: 1250,\n  delayMS: 750,\n  loop: true\n};\nScheduleContainer.propTypes = {\n  children: PropTypes.func,\n  render: PropTypes.func,\n  duration: PropTypes.number,\n  loop: PropTypes.bool,\n  delayMS: PropTypes.number\n};\n\nexport { ScheduleContainer, useSchedule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAAA,gBAAiE;AACjE,IAAAC,qBAAsB;;;ACDtB,mBAAiD;AACjD,wBAAsB;AAEtB,IAAM,cAAc,SAAU,OAAO;AACnC,MAAI;AAAA,IACF,WAAW;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,EACT,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,CAAC;AACxC,QAAM,CAAC,eAAe,gBAAgB,QAAI,uBAAS,KAAK;AACxD,oCAAgB,MAAM;AACpB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,YAAY;AAChB,UAAM,OAAO,MAAM;AACjB,UAAI,WAAW;AACb;AAAA,MACF;AACA,YAAM,sBAAsB,qBAAqB;AAAA,IACnD;AACA,UAAM,wBAAwB,MAAM;AAClC,iBAAW,KAAK,IAAI,IAAI,KAAK;AAC7B,WAAK;AAAA,IACP;AACA,UAAM,UAAU,MAAM;AACpB,UAAI,WAAW;AACb;AAAA,MACF;AACA,oBAAc,WAAW,MAAM;AAC7B,6BAAqB,GAAG;AACxB,mBAAW,KAAK,IAAI,IAAI,KAAK;AAC7B,YAAI;AAAM,kBAAQ;AAAA,MACpB,GAAG,QAAQ;AACX,cAAQ,KAAK,IAAI;AACjB,uBAAiB,IAAI;AACrB,WAAK;AAAA,IACP;AACA,UAAM,wBAAwB,WAAW,SAAS,OAAO;AACzD,WAAO,MAAM;AACX,kBAAY;AACZ,mBAAa,qBAAqB;AAClC,mBAAa,WAAW;AACxB,2BAAqB,GAAG;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,UAAU,SAAS,IAAI,CAAC;AAC5B,SAAO;AAAA,IACL,SAAS,KAAK,IAAI,GAAG,UAAU,QAAQ;AAAA,IACvC;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,oBAAoB,UAAQ;AAChC,MAAI;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,aAAAC,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,OAAO,YAAY,KAAK,CAAC,CAAC;AAC7E;AACA,kBAAkB,eAAe;AAAA,EAC/B,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AACR;AACA,kBAAkB,YAAY;AAAA,EAC5B,UAAU,kBAAAC,QAAU;AAAA,EACpB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,UAAU,kBAAAA,QAAU;AAAA,EACpB,MAAM,kBAAAA,QAAU;AAAA,EAChB,SAAS,kBAAAA,QAAU;AACrB;;;ADlEA,SAAS,WAAW;AAClB,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEA,IAAM,kBAAkB,GAAU,CAAC,s+BAAs+B,CAAC;AAC1gC,IAAM,kBAAkB,GAAU,CAAC,+qBAA+qB,CAAC;AACntB,IAAM,oBAAoB,GAAU,CAAC,ghCAAghC,CAAC;AAEtjC,IAAM,mBAAmB,sCAAO,OAAO,MAAM;AAAA,EAC3C,IAAI;AAAA,EACJ,GAAG;AACL,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,EAAE,CAAC;AACP,iBAAiB,eAAe;AAAA,EAC9B,OAAO;AACT;AACA,IAAM,sBAAsB,sCAAO,gBAAgB,EAAE,MAAM;AAAA,EACzD,IAAI;AACN,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,cAAc,GAAG,GAAG,UAAQ;AAC9B,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,SAAO,GAAI,CAAC,IAAI,KAAK,oBAAoB,GAAG,iBAAiB,QAAQ;AACvE,CAAC;AACD,oBAAoB,eAAe;AAAA,EACjC,OAAO;AACT;AACA,IAAM,sBAAsB,sCAAO,gBAAgB,EAAE,MAAM,OAAO;AAAA,EAChE,IAAI;AACN,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,cAAc,GAAG,GAAG,WAAS;AAC/B,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,SAAO,GAAI,CAAC,IAAI,KAAK,oBAAoB,GAAG,iBAAiB,QAAQ;AACvE,CAAC;AACD,oBAAoB,eAAe;AAAA,EACjC,OAAO;AACT;AACA,IAAM,wBAAwB,sCAAO,gBAAgB,EAAE,MAAM,OAAO;AAAA,EAClE,IAAI;AACN,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,cAAc,GAAG,GAAG,WAAS;AAC/B,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,SAAO,GAAI,CAAC,IAAI,KAAK,oBAAoB,GAAG,mBAAmB,QAAQ;AACzE,CAAC;AACD,sBAAsB,eAAe;AAAA,EACnC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,2BAA2B,sCAAO,IAAI,MAAM;AAAA,EAChD,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,MAAM;AACR,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,+BAA+B,YAAY,eAAe,KAAK,EAAE,GAAG,WAAS,MAAM,SAAS,OAAO,WAAS,MAAM,UAAU,SAAS,WAAS,MAAM,UAAU,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACzN,yBAAyB,eAAe;AAAA,EACtC,OAAO;AACT;AAEA,IAAM,eAAe,CAAC,MAAM,UAAU;AACpC,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,MAAM,MAAM,OAAO;AAAA,IAC5B,KAAK;AACH,aAAO,MAAM,MAAM,OAAO;AAAA,IAC5B;AACE,aAAO,MAAM,MAAM,OAAO;AAAA,EAC9B;AACF;AACA,IAAM,qBAAqB,CAAC,MAAM,UAAU,aAAa,MAAM,KAAK,IAAI;AACxE,IAAM,mCAAmC;AACzC,IAAM,2BAA2B,sCAAO,IAAI,MAAM,YAAU;AAAA,EAC1D,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,cAAc,MAAM,gBAAgB,mBAAmB,MAAM,MAAM,MAAM,KAAK;AAChF,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,WAAW,uBAAuB,wBAAwB,WAAW,KAAK,EAAE,GAAG,WAAS,MAAM,MAAM,MAAM,OAAO,GAAG,WAAS,MAAM,cAAc,WAAS,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,MAAM,SAAS,SAAS,cAAc,KAAK,MAAM,KAAK,GAAG,WAAS,wBAAwB,kCAAkC,KAAK,CAAC;AACnV,yBAAyB,eAAe;AAAA,EACtC,OAAO;AACT;AACA,IAAM,iCAAiC;AACvC,IAAM,0BAA0B,sCAAO,IAAI,MAAM,YAAU;AAAA,EACzD,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,QAAQ,MAAM,UAAU,aAAa,MAAM,MAAM,MAAM,KAAK;AAAA,EAC5D,cAAc,MAAM,gBAAgB,mBAAmB,MAAM,MAAM,MAAM,KAAK;AAChF,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,oDAAoD,qCAAqC,aAAa,OAAO,EAAE,GAAG,WAAS,MAAM,cAAc,WAAS,MAAM,OAAO,WAAS,MAAM,QAAQ,WAAS,wBAAwB,gCAAgC,KAAK,CAAC;AACvQ,wBAAwB,eAAe;AAAA,EACrC,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,GAAU,CAAC,oCAAoC,CAAC;AACxE,IAAM,oBAAoB,GAAU,CAAC,kBAAkB,CAAC;AACxD,IAAM,uBAAuB,GAAU,CAAC,mBAAmB,CAAC;AAC5D,IAAM,kCAAkC,UAAQ;AAC9C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,SAAS;AACX,WAAO,GAAI,CAAC,qBAAqB,GAAG,GAAG,KAAK,MAAM,OAAO,YAAY,GAAG,CAAC;AAAA,EAC3E;AACA,SAAO,GAAI,CAAC,qBAAqB,GAAG,GAAG,SAAS,cAAc,KAAK,OAAO,GAAG,CAAC;AAChF;AACA,IAAM,4BAA4B,WAAS;AACzC,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,MAAI,MAAM,KAAK;AACb,WAAO,GAAI,CAAC,4BAA4B,mCAAmC,GAAG,oBAAoB;AAAA,EACpG;AACA,SAAO,GAAI,CAAC,2BAA2B,mCAAmC,GAAG,iBAAiB;AAChG;AACA,IAAM,2BAA2B,WAAS;AACxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,GAAI,CAAC,sCAAsC,iBAAiB,iBAAiB,GAAG,MAAM,MAAM,WAAW,SAAS,UAAU,SAAS,aAAa,KAAK,OAAO,GAAG,IAAI,KAAK,MAAM,OAAO,YAAY,GAAG,CAAC;AAC9M;AACA,IAAM,iBAAiB,sCAAO,IAAI,MAAM;AAAA,EACtC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,qDAAqD,gCAAgC,WAAW,YAAY,iCAAiC,KAAK,2EAA2E,KAAK,KAAK,GAAG,GAAG,iBAAiB,WAAS,MAAM,MAAM,YAAY,IAAI,WAAS,MAAM,aAAa,WAAS,MAAM,cAAc,WAAS,cAAc,MAAM,MAAM,UAAU,IAAI,MAAM,MAAM,MAAM,OAAO,CAAC,GAAG,iCAAiC,2BAA2B,0BAA0B,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC3jB,eAAe,eAAe;AAAA,EAC5B,OAAO;AACT;AAEA,IAAM,sBAAsB,sCAAO,OAAO,MAAM,YAAU;AAAA,EACxD,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,aAAa,MAAM;AAAA,EACnB,iBAAiB,GAAG,MAAM;AAAA,EAC1B,WAAW,MAAM;AACnB,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,EAAE,CAAC;AACP,oBAAoB,eAAe;AAAA,EACjC,OAAO;AACT;AAEA,IAAM,YAAY,sCAAO,IAAI,MAAM,YAAU;AAAA,EAC3C,uBAAuB;AAAA,EACvB,OAAO;AAAA,EACP,OAAO,MAAM;AAAA,EACb,QAAQ,MAAM;AAAA,EACd,WAAW;AAAA,EACX,SAAS,OAAO,MAAM,SAAS,MAAM;AAAA,EACrC,MAAM;AACR,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,UAAU,YAAY,WAAW,eAAe,KAAK,GAAG,GAAG,WAAS,MAAM,kBAAkB,OAAO,WAAS,MAAM,mBAAmB,SAAS,WAAS,MAAM,SAAS,WAAW,WAAS,MAAM,YAAY,WAAW,WAAS,wBAAwB,MAAM,cAAc,KAAK,CAAC;AACtR,UAAU,eAAe;AAAA,EACvB,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,GAAU,CAAC,sCAAsC,CAAC;AAC1E,IAAM,eAAe,sCAAO,OAAO,MAAM;AAAA,EACvC,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,GAAG;AACL,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,EAAE,CAAC;AACP,aAAa,eAAe;AAAA,EAC1B,OAAO;AACT;AACA,IAAM,eAAe,sCAAO,IAAI,MAAM,YAAU;AAAA,EAC9C,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,SAAS;AAAA,EACT,OAAO,MAAM;AAAA,EACb,QAAQ,MAAM,OAAO;AACvB,EAAE,EAAE,WAAW;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,UAAU,KAAK,0CAA0C,iCAAiC,+BAA+B,gEAAgE,iCAAiC,OAAO,EAAE,GAAG,WAAS,MAAM,OAAO,cAAc,iBAAiB,WAAS,MAAM,MAAM,MAAM,UAAU,QAAQ,iBAAiB,iBAAiB,WAAS,MAAM,MAAM,MAAM,SAAS,SAAS,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACld,aAAa,eAAe;AAAA,EAC1B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,WAAO,0BAAW,CAAC,MAAM,QAAQ;AACrC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAQ,0BAAW,EAAY;AACrC,QAAM,cAAc,YAAY,KAAK;AACrC,QAAM,sBAAkB,sBAAO,IAAI;AACnC,MAAI,eAAe,gBAAgB,YAAY,MAAM;AACnD,UAAM,MAAM,YAAY,gBAAgB,8BAA8B,KAAK;AAC3E,oBAAgB,UAAU,eAAe;AAAA,EAC3C;AACA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,YAAY;AAAA,IACd;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,CAAC;AACD,QAAM,aAAS,sBAAO,IAAI;AAC1B,QAAM,aAAS,sBAAO,IAAI;AAC1B,QAAM,eAAW,sBAAO,IAAI;AAC5B,+BAAU,MAAM;AACd,QAAI,CAAC,gBAAgB,WAAW,eAAe;AAC7C,YAAM,aAAa,CAAC,OAAO,iBAAiB,OAAO,OAAO,EAAE,iBAAiB,WAAW,GAAG,OAAO,iBAAiB,OAAO,OAAO,EAAE,iBAAiB,WAAW,GAAG,OAAO,iBAAiB,SAAS,OAAO,EAAE,iBAAiB,WAAW,CAAC;AACzO,aAAO,QAAQ,aAAa,aAAa,WAAW,CAAC,CAAC;AACtD,aAAO,QAAQ,aAAa,aAAa,WAAW,CAAC,CAAC;AACtD,eAAS,QAAQ,aAAa,aAAa,WAAW,CAAC,CAAC;AAAA,IAC1D;AAAA,EACF,CAAC;AACD,MAAI,CAAC,iBAAiB,YAAY,GAAG;AACnC,WAAO,cAAAC,QAAM,cAAc,0BAA0B;AAAA,MACnD,UAAU;AAAA,IACZ,GAAG,GAAM;AAAA,EACX;AACA,SAAO,cAAAA,QAAM,cAAc,WAAW,SAAS;AAAA,IAC7C;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,GAAG,KAAK,GAAG,cAAAA,QAAM,cAAc,KAAK;AAAA,IAClC,MAAM;AAAA,EACR,GAAG,cAAAA,QAAM,cAAc,qBAAqB;AAAA,IAC1C;AAAA,IACA,KAAK;AAAA,EACP,CAAC,GAAG,cAAAA,QAAM,cAAc,qBAAqB;AAAA,IAC3C;AAAA,IACA,KAAK;AAAA,EACP,CAAC,GAAG,cAAAA,QAAM,cAAc,uBAAuB;AAAA,IAC7C;AAAA,IACA,KAAK;AAAA,EACP,CAAC,CAAC,CAAC;AACL,CAAC;AACD,KAAK,cAAc;AACnB,KAAK,YAAY;AAAA,EACf,MAAM,mBAAAC,QAAU;AAAA,EAChB,UAAU,mBAAAA,QAAU;AAAA,EACpB,OAAO,mBAAAA,QAAU;AAAA,EACjB,SAAS,mBAAAA,QAAU;AACrB;AACA,KAAK,eAAe;AAAA,EAClB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AACX;AAEA,IAAM,OAAO,CAAC,SAAS,UAAU,OAAO;AAExC,IAAM,iBAAiB;AACvB,IAAM,WAAW,cAAAD,QAAM,WAAW,CAAC,MAAM,QAAQ;AAC/C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AACnD,QAAM,YAAY,QAAQ,UAAU;AAAA,IAClC,cAAc;AAAA,EAChB,GAAG,cAAc,UAAU;AAC3B,SACE,cAAAA,QAAM,cAAc,0BAA0B,SAAS;AAAA,IACrD,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,cAAc;AAAA,EAChB,GAAG,KAAK,GAAG,cAAAA,QAAM,cAAc,yBAAyB;AAAA,IACtD,OAAO;AAAA,IACP;AAAA,EACF,CAAC,CAAC;AAEN,CAAC;AACD,SAAS,cAAc;AACvB,SAAS,YAAY;AAAA,EACnB,OAAO,mBAAAC,QAAU;AAAA,EACjB,OAAO,mBAAAA,QAAU,OAAO;AAAA,EACxB,MAAM,mBAAAA,QAAU,MAAM,IAAI;AAC5B;AACA,SAAS,eAAe;AAAA,EACtB,OAAO;AAAA,EACP,MAAM;AACR;AAEA,IAAM,eAAW,0BAAW,CAAC,MAAM,QAAQ;AACzC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,cAAAD,QAAM,cAAc,gBAAgB,SAAS;AAAA,IAClD;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,cAAc;AAAA,EAChB,GAAG,KAAK,GAAG,GAAM;AACnB,CAAC;AACD,SAAS,cAAc;AACvB,SAAS,YAAY;AAAA,EACnB,OAAO,mBAAAC,QAAU;AAAA,EACjB,QAAQ,mBAAAA,QAAU;AAAA,EAClB,SAAS,mBAAAA,QAAU;AACrB;AACA,SAAS,eAAe;AAAA,EACtB,OAAO;AAAA,EACP,QAAQ;AACV;AAEA,IAAM,sBAAsB;AAAA,EAC1B,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAM,kBAAkB;AAAA,EACtB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAM,mBAAmB;AAAA,EACvB,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAEA,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,gBAAgB,CAAC,QAAQ,aAAa;AAC1C,SAAO,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,KAAK,MAAM,OAAO,QAAQ;AAC9D,UAAM,CAAC,OAAO,KAAK,IAAI;AACvB,UAAM,CAAC,WAAW,SAAS,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,EAAE,CAAC,CAAC;AACzE,UAAM,OAAO,SAAS,WAAW,EAAE,IAAI,SAAS,OAAO,EAAE;AACzD,UAAM,UAAU,MAAO;AACvB,QAAI,cAAc,YAAY,eAAe,KAAK;AAClD,QAAI,YAAY;AAChB,QAAI,KAAK,IAAI;AACb,aAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,kBAAY,aAAa,YAAY,cAAc,UAAU;AAC7D,oBAAc,YAAY,eAAe,MAAM,OAAO;AACtD,UAAI,SAAS,OAAO,EAAE,IAAI,MAAM,CAAC,IAAI;AAAA,IACvC;AACA,QAAI,SAAS,IAAI;AACjB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,IAAM,cAAU,0BAAW,CAAC,MAAM,QAAQ;AACxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,oBAAoB,cAAc,qBAAqB,QAAQ;AACrE,QAAM,iBAAiB,cAAc,iBAAiB,QAAQ;AAC9D,QAAM,kBAAkB,cAAc,kBAAkB,QAAQ;AAChE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,YAAY;AAAA,IACd;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,SAAS,UAAU,KAAK,QAAQ,CAAC;AACvC,QAAM,mBAAmB,kBAAkB,KAAK;AAChD,QAAM,gBAAgB,eAAe,KAAK;AAC1C,QAAM,iBAAiB,gBAAgB,KAAK;AAC5C,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,MAAI,CAAC,iBAAiB,YAAY,GAAG;AACnC,WAAO,cAAAD,QAAM,cAAc,0BAA0B;AAAA,MACnD,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ,GAAG,GAAM;AAAA,EACX;AACA,SAAO,cAAAA,QAAM,cAAc,WAAW,SAAS;AAAA,IAC7C;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,EAClB,GAAG,KAAK,GAAG,cAAAA,QAAM,cAAc,qBAAqB;AAAA,IAClD;AAAA,IACA;AAAA,IACA,WAAW,UAAU,kBAAkB,QAAQ,MAAM,SAAS;AAAA,EAChE,CAAC,CAAC;AACJ,CAAC;AACD,QAAQ,cAAc;AACtB,QAAQ,YAAY;AAAA,EAClB,MAAM,mBAAAC,QAAU;AAAA,EAChB,UAAU,mBAAAA,QAAU;AAAA,EACpB,OAAO,mBAAAA,QAAU;AAAA,EACjB,SAAS,mBAAAA,QAAU;AACrB;AACA,QAAQ,eAAe;AAAA,EACrB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AACX;AAEA,IAAM,aAAS,0BAAW,CAAC,MAAM,QAAQ;AACvC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAY,QAAQ,QAAQ,OAAO,cAAc,SAAS;AAChE,SACE,cAAAD,QAAM,cAAc,cAAc,SAAS;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,MAAM;AAAA,EACR,GAAG,KAAK,GAAG,cAAAA,QAAM,cAAc,cAAc;AAAA,IAC3C,IAAI;AAAA,EACN,CAAC,GAAG,cAAAA,QAAM,cAAc,cAAc;AAAA,IACpC,IAAI;AAAA,EACN,CAAC,GAAG,cAAAA,QAAM,cAAc,cAAc;AAAA,IACpC,IAAI;AAAA,EACN,CAAC,CAAC;AAEN,CAAC;AACD,OAAO,cAAc;AACrB,OAAO,YAAY;AAAA,EACjB,MAAM,mBAAAC,QAAU;AAAA,EAChB,OAAO,mBAAAA,QAAU;AACnB;AACA,OAAO,eAAe;AAAA,EACpB,MAAM;AAAA,EACN,OAAO;AACT;", "names": ["import_react", "import_prop_types", "React", "PropTypes", "React", "PropTypes"]}