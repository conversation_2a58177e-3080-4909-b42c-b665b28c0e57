{"version": 3, "sources": ["../../node_modules/@tanstack/table-core/src/utils.ts", "../../node_modules/@tanstack/table-core/src/core/column.ts", "../../node_modules/@tanstack/table-core/src/core/headers.ts", "../../node_modules/@tanstack/table-core/src/features/ColumnSizing.ts", "../../node_modules/@tanstack/table-core/src/features/Expanding.ts", "../../node_modules/@tanstack/table-core/src/filterFns.ts", "../../node_modules/@tanstack/table-core/src/features/Filters.ts", "../../node_modules/@tanstack/table-core/src/aggregationFns.ts", "../../node_modules/@tanstack/table-core/src/features/Grouping.ts", "../../node_modules/@tanstack/table-core/src/features/Ordering.ts", "../../node_modules/@tanstack/table-core/src/features/Pagination.ts", "../../node_modules/@tanstack/table-core/src/features/Pinning.ts", "../../node_modules/@tanstack/table-core/src/features/RowSelection.ts", "../../node_modules/@tanstack/table-core/src/sortingFns.ts", "../../node_modules/@tanstack/table-core/src/features/Sorting.ts", "../../node_modules/@tanstack/table-core/src/features/Visibility.ts", "../../node_modules/@tanstack/table-core/src/core/table.ts", "../../node_modules/@tanstack/table-core/src/core/cell.ts", "../../node_modules/@tanstack/table-core/src/core/row.ts", "../../node_modules/@tanstack/table-core/src/columnHelper.ts", "../../node_modules/@tanstack/table-core/src/utils/getCoreRowModel.ts", "../../node_modules/@tanstack/table-core/src/utils/filterRowsUtils.ts", "../../node_modules/@tanstack/table-core/src/utils/getFilteredRowModel.ts", "../../node_modules/@tanstack/table-core/src/utils/getFacetedRowModel.ts", "../../node_modules/@tanstack/table-core/src/utils/getFacetedUniqueValues.ts", "../../node_modules/@tanstack/table-core/src/utils/getFacetedMinMaxValues.ts", "../../node_modules/@tanstack/table-core/src/utils/getSortedRowModel.ts", "../../node_modules/@tanstack/table-core/src/utils/getGroupedRowModel.ts", "../../node_modules/@tanstack/table-core/src/utils/getExpandedRowModel.ts", "../../node_modules/@tanstack/table-core/src/utils/getPaginationRowModel.ts", "../../node_modules/@tanstack/react-table/src/index.tsx"], "sourcesContent": ["import { TableState, Updater } from './types'\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\nexport type RequiredKeys<T, K extends keyof T> = Omit<T, K> &\n  Required<Pick<T, K>>\nexport type Overwrite<T, U extends { [TKey in keyof T]?: any }> = Omit<\n  T,\n  keyof U\n> &\n  U\n\nexport type UnionToIntersection<T> = (\n  T extends any ? (x: T) => any : never\n) extends (x: infer R) => any\n  ? R\n  : never\n\nexport type IsAny<T, Y, N> = 1 extends 0 & T ? Y : N\nexport type IsKnown<T, Y, N> = unknown extends T ? N : Y\n\ntype ComputeRange<\n  N extends number,\n  Result extends Array<unknown> = []\n> = Result['length'] extends N\n  ? Result\n  : ComputeRange<N, [...Result, Result['length']]>\ntype Index40 = ComputeRange<40>[number]\n\n// Is this type a tuple?\ntype IsTuple<T> = T extends readonly any[] & { length: infer Length }\n  ? Length extends Index40\n    ? T\n    : never\n  : never\n\n// If this type is a tuple, what indices are allowed?\ntype AllowedIndexes<\n  Tuple extends ReadonlyArray<any>,\n  Keys extends number = never\n> = Tuple extends readonly []\n  ? Keys\n  : Tuple extends readonly [infer _, ...infer Tail]\n  ? AllowedIndexes<Tail, Keys | Tail['length']>\n  : Keys\n\nexport type DeepKeys<T> = unknown extends T\n  ? keyof T\n  : object extends T\n  ? string\n  : T extends readonly any[] & IsTuple<T>\n  ? AllowedIndexes<T> | DeepKeysPrefix<T, AllowedIndexes<T>>\n  : T extends any[]\n  ? never & 'Dynamic length array indexing is not supported'\n  : T extends Date\n  ? never\n  : T extends object\n  ? (keyof T & string) | DeepKeysPrefix<T, keyof T>\n  : never\n\ntype DeepKeysPrefix<T, TPrefix> = TPrefix extends keyof T & (number | string)\n  ? `${TPrefix}.${DeepKeys<T[TPrefix]> & string}`\n  : never\n\nexport type DeepValue<T, TProp> = T extends Record<string | number, any>\n  ? TProp extends `${infer TBranch}.${infer TDeepProp}`\n    ? DeepValue<T[TBranch], TDeepProp>\n    : T[TProp & string]\n  : never\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport type Getter<TValue> = <TTValue = TValue>() => NoInfer<TTValue>\n\n///\n\nexport function functionalUpdate<T>(updater: Updater<T>, input: T): T {\n  return typeof updater === 'function'\n    ? (updater as (input: T) => T)(input)\n    : updater\n}\n\nexport function noop() {\n  //\n}\n\nexport function makeStateUpdater<K extends keyof TableState>(\n  key: K,\n  instance: unknown\n) {\n  return (updater: Updater<TableState[K]>) => {\n    ;(instance as any).setState(<TTableState>(old: TTableState) => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, (old as any)[key]),\n      }\n    })\n  }\n}\n\ntype AnyFunction = (...args: any) => any\n\nexport function isFunction<T extends AnyFunction>(d: any): d is T {\n  return d instanceof Function\n}\n\nexport function isNumberArray(d: any): d is number[] {\n  return Array.isArray(d) && d.every(val => typeof val === 'number')\n}\n\nexport function flattenBy<TNode>(\n  arr: TNode[],\n  getChildren: (item: TNode) => TNode[]\n) {\n  const flat: TNode[] = []\n\n  const recurse = (subArr: TNode[]) => {\n    subArr.forEach(item => {\n      flat.push(item)\n      const children = getChildren(item)\n      if (children?.length) {\n        recurse(children)\n      }\n    })\n  }\n\n  recurse(arr)\n\n  return flat\n}\n\nexport function memo<TDeps extends readonly any[], TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: any\n    debug?: () => any\n    onChange?: (result: TResult) => void\n  }\n): () => TResult {\n  let deps: any[] = []\n  let result: TResult | undefined\n\n  return () => {\n    let depTime: number\n    if (opts.key && opts.debug) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug) resultTime = Date.now()\n\n    result = fn(...newDeps)\n    opts?.onChange?.(result)\n\n    if (opts.key && opts.debug) {\n      if (opts?.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n        const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n        const resultFpsPercentage = resultEndTime / 16\n\n        const pad = (str: number | string, num: number) => {\n          str = String(str)\n          while (str.length < num) {\n            str = ' ' + str\n          }\n          return str\n        }\n\n        console.info(\n          `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n          `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120)\n            )}deg 100% 31%);`,\n          opts?.key\n        )\n      }\n    }\n\n    return result!\n  }\n}\n", "import {\n  Column,\n  Table,\n  AccessorFn,\n  ColumnDef,\n  RowData,\n  ColumnDefResolved,\n} from '../types'\nimport { memo } from '../utils'\n\nexport interface CoreColumn<TData extends RowData, TValue> {\n  id: string\n  depth: number\n  accessorFn?: AccessorFn<TData, TValue>\n  columnDef: ColumnDef<TData, TValue>\n  columns: Column<TData, TValue>[]\n  parent?: Column<TData, TValue>\n  getFlatColumns: () => Column<TData, TValue>[]\n  getLeafColumns: () => Column<TData, TValue>[]\n}\n\nexport function createColumn<TData extends RowData, TValue>(\n  table: Table<TData>,\n  columnDef: ColumnDef<TData, TValue>,\n  depth: number,\n  parent?: Column<TData, TValue>\n): Column<TData, TValue> {\n  const defaultColumn = table._getDefaultColumnDef()\n\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef,\n  } as ColumnDefResolved<TData>\n\n  const accessorKey = resolvedColumnDef.accessorKey\n\n  let id =\n    resolvedColumnDef.id ??\n    (accessorKey ? accessorKey.replace('.', '_') : undefined) ??\n    (typeof resolvedColumnDef.header === 'string'\n      ? resolvedColumnDef.header\n      : undefined)\n\n  let accessorFn: AccessorFn<TData> | undefined\n\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = (originalRow: TData) => {\n        let result = originalRow as Record<string, any>\n\n        for (const key of accessorKey.split('.')) {\n          result = result?.[key]\n          if (process.env.NODE_ENV !== 'production' && result === undefined) {\n            console.warn(\n              `\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`\n            )\n          }\n        }\n\n        return result\n      }\n    } else {\n      accessorFn = (originalRow: TData) =>\n        (originalRow as any)[resolvedColumnDef.accessorKey]\n    }\n  }\n\n  if (!id) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        resolvedColumnDef.accessorFn\n          ? `Columns require an id when using an accessorFn`\n          : `Columns require an id when using a non-string header`\n      )\n    }\n    throw new Error()\n  }\n\n  let column: CoreColumn<TData, any> = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent as any,\n    depth,\n    columnDef: resolvedColumnDef as ColumnDef<TData, any>,\n    columns: [],\n    getFlatColumns: memo(\n      () => [true],\n      () => {\n        return [\n          column as Column<TData, TValue>,\n          ...column.columns?.flatMap(d => d.getFlatColumns()),\n        ]\n      },\n      {\n        key: process.env.NODE_ENV === 'production' && 'column.getFlatColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n    getLeafColumns: memo(\n      () => [table._getOrderColumnsFn()],\n      orderColumns => {\n        if (column.columns?.length) {\n          let leafColumns = column.columns.flatMap(column =>\n            column.getLeafColumns()\n          )\n\n          return orderColumns(leafColumns)\n        }\n\n        return [column as Column<TData, TValue>]\n      },\n      {\n        key: process.env.NODE_ENV === 'production' && 'column.getLeafColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n  }\n\n  column = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.createColumn?.(column, table))\n  }, column)\n\n  // Yes, we have to convert table to uknown, because we know more than the compiler here.\n  return column as Column<TData, TValue>\n}\n", "import { <PERSON><PERSON><PERSON>, Column, Header, HeaderGroup, Table } from '../types'\nimport { memo } from '../utils'\nimport { TableFeature } from './table'\n\nexport interface CoreHeaderGroup<TData extends RowData> {\n  id: string\n  depth: number\n  headers: Header<TData, unknown>[]\n}\n\nexport interface HeaderContext<TData, TValue> {\n  table: Table<TData>\n  header: Header<TData, TValue>\n  column: Column<TData, TValue>\n}\n\nexport interface CoreHeader<TData extends RowData, TValue> {\n  id: string\n  index: number\n  depth: number\n  column: Column<TData, TValue>\n  headerGroup: HeaderGroup<TData>\n  subHeaders: Header<TData, TValue>[]\n  colSpan: number\n  rowSpan: number\n  getLeafHeaders: () => Header<TData, unknown>[]\n  isPlaceholder: boolean\n  placeholderId?: string\n  getContext: () => HeaderContext<TData, TValue>\n}\n\nexport interface HeadersInstance<TData extends RowData> {\n  getHeaderGroups: () => HeaderGroup<TData>[]\n  getLeftHeaderGroups: () => HeaderGroup<TData>[]\n  getCenterHeaderGroups: () => HeaderGroup<TData>[]\n  getRightHeaderGroups: () => HeaderGroup<TData>[]\n\n  getFooterGroups: () => HeaderGroup<TData>[]\n  getLeftFooterGroups: () => HeaderGroup<TData>[]\n  getCenterFooterGroups: () => HeaderGroup<TData>[]\n  getRightFooterGroups: () => HeaderGroup<TData>[]\n\n  getFlatHeaders: () => Header<TData, unknown>[]\n  getLeftFlatHeaders: () => Header<TData, unknown>[]\n  getCenterFlatHeaders: () => Header<TData, unknown>[]\n  getRightFlatHeaders: () => Header<TData, unknown>[]\n\n  getLeafHeaders: () => Header<TData, unknown>[]\n  getLeftLeafHeaders: () => Header<TData, unknown>[]\n  getCenterLeafHeaders: () => Header<TData, unknown>[]\n  getRightLeafHeaders: () => Header<TData, unknown>[]\n}\n\n//\n\nfunction createHeader<TData extends RowData, TValue>(\n  table: Table<TData>,\n  column: Column<TData, TValue>,\n  options: {\n    id?: string\n    isPlaceholder?: boolean\n    placeholderId?: string\n    index: number\n    depth: number\n  }\n): Header<TData, TValue> {\n  const id = options.id ?? column.id\n\n  let header: CoreHeader<TData, TValue> = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null!,\n    getLeafHeaders: (): Header<TData, unknown>[] => {\n      const leafHeaders: Header<TData, unknown>[] = []\n\n      const recurseHeader = (h: CoreHeader<TData, any>) => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader)\n        }\n        leafHeaders.push(h as Header<TData, unknown>)\n      }\n\n      recurseHeader(header)\n\n      return leafHeaders\n    },\n    getContext: () => ({\n      table,\n      header: header as Header<TData, TValue>,\n      column,\n    }),\n  }\n\n  table._features.forEach(feature => {\n    Object.assign(header, feature.createHeader?.(header, table))\n  })\n\n  return header as Header<TData, TValue>\n}\n\nexport const Headers: TableFeature = {\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): HeadersInstance<TData> => {\n    return {\n      // Header Groups\n\n      getHeaderGroups: memo(\n        () => [\n          table.getAllColumns(),\n          table.getVisibleLeafColumns(),\n          table.getState().columnPinning.left,\n          table.getState().columnPinning.right,\n        ],\n        (allColumns, leafColumns, left, right) => {\n          const leftColumns =\n            left\n              ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n              .filter(Boolean) ?? []\n\n          const rightColumns =\n            right\n              ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n              .filter(Boolean) ?? []\n\n          const centerColumns = leafColumns.filter(\n            column => !left?.includes(column.id) && !right?.includes(column.id)\n          )\n\n          const headerGroups = buildHeaderGroups(\n            allColumns,\n            [...leftColumns, ...centerColumns, ...rightColumns],\n            table\n          )\n\n          return headerGroups\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getHeaderGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getCenterHeaderGroups: memo(\n        () => [\n          table.getAllColumns(),\n          table.getVisibleLeafColumns(),\n          table.getState().columnPinning.left,\n          table.getState().columnPinning.right,\n        ],\n        (allColumns, leafColumns, left, right) => {\n          leafColumns = leafColumns.filter(\n            column => !left?.includes(column.id) && !right?.includes(column.id)\n          )\n          return buildHeaderGroups(allColumns, leafColumns, table, 'center')\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'development' && 'getCenterHeaderGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getLeftHeaderGroups: memo(\n        () => [\n          table.getAllColumns(),\n          table.getVisibleLeafColumns(),\n          table.getState().columnPinning.left,\n        ],\n        (allColumns, leafColumns, left) => {\n          const orderedLeafColumns =\n            left\n              ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n              .filter(Boolean) ?? []\n\n          return buildHeaderGroups(\n            allColumns,\n            orderedLeafColumns,\n            table,\n            'left'\n          )\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeftHeaderGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getRightHeaderGroups: memo(\n        () => [\n          table.getAllColumns(),\n          table.getVisibleLeafColumns(),\n          table.getState().columnPinning.right,\n        ],\n        (allColumns, leafColumns, right) => {\n          const orderedLeafColumns =\n            right\n              ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n              .filter(Boolean) ?? []\n\n          return buildHeaderGroups(\n            allColumns,\n            orderedLeafColumns,\n            table,\n            'right'\n          )\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getRightHeaderGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      // Footer Groups\n\n      getFooterGroups: memo(\n        () => [table.getHeaderGroups()],\n        headerGroups => {\n          return [...headerGroups].reverse()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getFooterGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getLeftFooterGroups: memo(\n        () => [table.getLeftHeaderGroups()],\n        headerGroups => {\n          return [...headerGroups].reverse()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeftFooterGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getCenterFooterGroups: memo(\n        () => [table.getCenterHeaderGroups()],\n        headerGroups => {\n          return [...headerGroups].reverse()\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'development' && 'getCenterFooterGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getRightFooterGroups: memo(\n        () => [table.getRightHeaderGroups()],\n        headerGroups => {\n          return [...headerGroups].reverse()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getRightFooterGroups',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      // Flat Headers\n\n      getFlatHeaders: memo(\n        () => [table.getHeaderGroups()],\n        headerGroups => {\n          return headerGroups\n            .map(headerGroup => {\n              return headerGroup.headers\n            })\n            .flat()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getFlatHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getLeftFlatHeaders: memo(\n        () => [table.getLeftHeaderGroups()],\n        left => {\n          return left\n            .map(headerGroup => {\n              return headerGroup.headers\n            })\n            .flat()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeftFlatHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getCenterFlatHeaders: memo(\n        () => [table.getCenterHeaderGroups()],\n        left => {\n          return left\n            .map(headerGroup => {\n              return headerGroup.headers\n            })\n            .flat()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getCenterFlatHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getRightFlatHeaders: memo(\n        () => [table.getRightHeaderGroups()],\n        left => {\n          return left\n            .map(headerGroup => {\n              return headerGroup.headers\n            })\n            .flat()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getRightFlatHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      // Leaf Headers\n\n      getCenterLeafHeaders: memo(\n        () => [table.getCenterFlatHeaders()],\n        flatHeaders => {\n          return flatHeaders.filter(header => !header.subHeaders?.length)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getCenterLeafHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getLeftLeafHeaders: memo(\n        () => [table.getLeftFlatHeaders()],\n        flatHeaders => {\n          return flatHeaders.filter(header => !header.subHeaders?.length)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeftLeafHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getRightLeafHeaders: memo(\n        () => [table.getRightFlatHeaders()],\n        flatHeaders => {\n          return flatHeaders.filter(header => !header.subHeaders?.length)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getRightLeafHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n\n      getLeafHeaders: memo(\n        () => [\n          table.getLeftHeaderGroups(),\n          table.getCenterHeaderGroups(),\n          table.getRightHeaderGroups(),\n        ],\n        (left, center, right) => {\n          return [\n            ...(left[0]?.headers ?? []),\n            ...(center[0]?.headers ?? []),\n            ...(right[0]?.headers ?? []),\n          ]\n            .map(header => {\n              return header.getLeafHeaders()\n            })\n            .flat()\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeafHeaders',\n          debug: () => table.options.debugAll ?? table.options.debugHeaders,\n        }\n      ),\n    }\n  },\n}\n\nexport function buildHeaderGroups<TData extends RowData>(\n  allColumns: Column<TData, unknown>[],\n  columnsToGroup: Column<TData, unknown>[],\n  table: Table<TData>,\n  headerFamily?: 'center' | 'left' | 'right'\n) {\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0\n\n  const findMaxDepth = (columns: Column<TData, unknown>[], depth = 1) => {\n    maxDepth = Math.max(maxDepth, depth)\n\n    columns\n      .filter(column => column.getIsVisible())\n      .forEach(column => {\n        if (column.columns?.length) {\n          findMaxDepth(column.columns, depth + 1)\n        }\n      }, 0)\n  }\n\n  findMaxDepth(allColumns)\n\n  let headerGroups: HeaderGroup<TData>[] = []\n\n  const createHeaderGroup = (\n    headersToGroup: Header<TData, unknown>[],\n    depth: number\n  ) => {\n    // The header group we are creating\n    const headerGroup: HeaderGroup<TData> = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: [],\n    }\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders: Header<TData, unknown>[] = []\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0]\n\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth\n\n      let column: Column<TData, unknown>\n      let isPlaceholder = false\n\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column\n        isPlaceholder = true\n      }\n\n      if (\n        latestPendingParentHeader &&\n        latestPendingParentHeader?.column === column\n      ) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup)\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup?.id]\n            .filter(Boolean)\n            .join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder\n            ? `${pendingParentHeaders.filter(d => d.column === column).length}`\n            : undefined,\n          depth,\n          index: pendingParentHeaders.length,\n        })\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup)\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header)\n      }\n\n      headerGroup.headers.push(headerToGroup)\n      headerToGroup.headerGroup = headerGroup\n    })\n\n    headerGroups.push(headerGroup)\n\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1)\n    }\n  }\n\n  const bottomHeaders = columnsToGroup.map((column, index) =>\n    createHeader(table, column, {\n      depth: maxDepth,\n      index,\n    })\n  )\n\n  createHeaderGroup(bottomHeaders, maxDepth - 1)\n\n  headerGroups.reverse()\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = (\n    headers: Header<TData, unknown>[]\n  ): { colSpan: number; rowSpan: number }[] => {\n    const filteredHeaders = headers.filter(header =>\n      header.column.getIsVisible()\n    )\n\n    return filteredHeaders.map(header => {\n      let colSpan = 0\n      let rowSpan = 0\n      let childRowSpans = [0]\n\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = []\n\n        recurseHeadersForSpans(header.subHeaders).forEach(\n          ({ colSpan: childColSpan, rowSpan: childRowSpan }) => {\n            colSpan += childColSpan\n            childRowSpans.push(childRowSpan)\n          }\n        )\n      } else {\n        colSpan = 1\n      }\n\n      const minChildRowSpan = Math.min(...childRowSpans)\n      rowSpan = rowSpan + minChildRowSpan\n\n      header.colSpan = colSpan\n      header.rowSpan = rowSpan\n\n      return { colSpan, rowSpan }\n    })\n  }\n\n  recurseHeadersForSpans(headerGroups[0]?.headers ?? [])\n\n  return headerGroups\n}\n", "import { TableFeature } from '../core/table'\nimport { <PERSON><PERSON><PERSON>, Column, Header, OnChangeFn, Table, Updater } from '../types'\nimport { makeStateUpdater } from '../utils'\nimport { ColumnPinningPosition } from './Pinning'\n\n//\n\nexport interface ColumnSizingTableState {\n  columnSizing: ColumnSizingState\n  columnSizingInfo: ColumnSizingInfoState\n}\n\nexport type ColumnSizingState = Record<string, number>\n\nexport interface ColumnSizingInfoState {\n  startOffset: null | number\n  startSize: null | number\n  deltaOffset: null | number\n  deltaPercentage: null | number\n  isResizingColumn: false | string\n  columnSizingStart: [string, number][]\n}\n\nexport type ColumnResizeMode = 'onChange' | 'onEnd'\n\nexport interface ColumnSizingOptions {\n  enableColumnResizing?: boolean\n  columnResizeMode?: ColumnResizeMode\n  onColumnSizingChange?: OnChangeFn<ColumnSizingState>\n  onColumnSizingInfoChange?: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport interface ColumnSizingDefaultOptions {\n  columnResizeMode: ColumnResizeMode\n  onColumnSizingChange: OnChangeFn<ColumnSizingState>\n  onColumnSizingInfoChange: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport interface ColumnSizingInstance {\n  setColumnSizing: (updater: Updater<ColumnSizingState>) => void\n  setColumnSizingInfo: (updater: Updater<ColumnSizingInfoState>) => void\n  resetColumnSizing: (defaultState?: boolean) => void\n  resetHeaderSizeInfo: (defaultState?: boolean) => void\n  getTotalSize: () => number\n  getLeftTotalSize: () => number\n  getCenterTotalSize: () => number\n  getRightTotalSize: () => number\n}\n\nexport interface ColumnSizingColumnDef {\n  enableResizing?: boolean\n  size?: number\n  minSize?: number\n  maxSize?: number\n}\n\nexport interface ColumnSizingColumn {\n  getSize: () => number\n  getStart: (position?: ColumnPinningPosition) => number\n  getCanResize: () => boolean\n  getIsResizing: () => boolean\n  resetSize: () => void\n}\n\nexport interface ColumnSizingHeader {\n  getSize: () => number\n  getStart: (position?: ColumnPinningPosition) => number\n  getResizeHandler: () => (event: unknown) => void\n}\n\n//\n\nexport const defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER,\n}\n\nconst getDefaultColumnSizingInfoState = (): ColumnSizingInfoState => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: [],\n})\n\nexport const ColumnSizing: TableFeature = {\n  getDefaultColumnDef: (): ColumnSizingColumnDef => {\n    return defaultColumnSizing\n  },\n  getInitialState: (state): ColumnSizingTableState => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingDefaultOptions => {\n    return {\n      columnResizeMode: 'onEnd',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): ColumnSizingColumn => {\n    return {\n      getSize: () => {\n        const columnSize = table.getState().columnSizing[column.id]\n\n        return Math.min(\n          Math.max(\n            column.columnDef.minSize ?? defaultColumnSizing.minSize,\n            columnSize ?? column.columnDef.size ?? defaultColumnSizing.size\n          ),\n          column.columnDef.maxSize ?? defaultColumnSizing.maxSize\n        )\n      },\n      getStart: position => {\n        const columns = !position\n          ? table.getVisibleLeafColumns()\n          : position === 'left'\n          ? table.getLeftVisibleLeafColumns()\n          : table.getRightVisibleLeafColumns()\n\n        const index = columns.findIndex(d => d.id === column.id)\n\n        if (index > 0) {\n          const prevSiblingColumn = columns[index - 1]!\n\n          return (\n            prevSiblingColumn.getStart(position) + prevSiblingColumn.getSize()\n          )\n        }\n\n        return 0\n      },\n      resetSize: () => {\n        table.setColumnSizing(({ [column.id]: _, ...rest }) => {\n          return rest\n        })\n      },\n      getCanResize: () => {\n        return (\n          (column.columnDef.enableResizing ?? true) &&\n          (table.options.enableColumnResizing ?? true)\n        )\n      },\n      getIsResizing: () => {\n        return table.getState().columnSizingInfo.isResizingColumn === column.id\n      },\n    }\n  },\n\n  createHeader: <TData extends RowData, TValue>(\n    header: Header<TData, TValue>,\n    table: Table<TData>\n  ): ColumnSizingHeader => {\n    return {\n      getSize: () => {\n        let sum = 0\n\n        const recurse = (header: Header<TData, TValue>) => {\n          if (header.subHeaders.length) {\n            header.subHeaders.forEach(recurse)\n          } else {\n            sum += header.column.getSize() ?? 0\n          }\n        }\n\n        recurse(header)\n\n        return sum\n      },\n      getStart: () => {\n        if (header.index > 0) {\n          const prevSiblingHeader =\n            header.headerGroup.headers[header.index - 1]!\n          return prevSiblingHeader.getStart() + prevSiblingHeader.getSize()\n        }\n\n        return 0\n      },\n      getResizeHandler: () => {\n        const column = table.getColumn(header.column.id)\n        const canResize = column?.getCanResize()\n\n        return (e: unknown) => {\n          if (!column || !canResize) {\n            return\n          }\n\n          ;(e as any).persist?.()\n\n          if (isTouchStartEvent(e)) {\n            // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n            if (e.touches && e.touches.length > 1) {\n              return\n            }\n          }\n\n          const startSize = header.getSize()\n\n          const columnSizingStart: [string, number][] = header\n            ? header\n                .getLeafHeaders()\n                .map(d => [d.column.id, d.column.getSize()])\n            : [[column.id, column.getSize()]]\n\n          const clientX = isTouchStartEvent(e)\n            ? Math.round(e.touches[0]!.clientX)\n            : (e as MouseEvent).clientX\n\n          const newColumnSizing: ColumnSizingState = {}\n\n          const updateOffset = (\n            eventType: 'move' | 'end',\n            clientXPos?: number\n          ) => {\n            if (typeof clientXPos !== 'number') {\n              return\n            }\n\n            table.setColumnSizingInfo(old => {\n              const deltaOffset = clientXPos - (old?.startOffset ?? 0)\n              const deltaPercentage = Math.max(\n                deltaOffset / (old?.startSize ?? 0),\n                -0.999999\n              )\n\n              old.columnSizingStart.forEach(([columnId, headerSize]) => {\n                newColumnSizing[columnId] =\n                  Math.round(\n                    Math.max(headerSize + headerSize * deltaPercentage, 0) * 100\n                  ) / 100\n              })\n\n              return {\n                ...old,\n                deltaOffset,\n                deltaPercentage,\n              }\n            })\n\n            if (\n              table.options.columnResizeMode === 'onChange' ||\n              eventType === 'end'\n            ) {\n              table.setColumnSizing(old => ({\n                ...old,\n                ...newColumnSizing,\n              }))\n            }\n          }\n\n          const onMove = (clientXPos?: number) =>\n            updateOffset('move', clientXPos)\n\n          const onEnd = (clientXPos?: number) => {\n            updateOffset('end', clientXPos)\n\n            table.setColumnSizingInfo(old => ({\n              ...old,\n              isResizingColumn: false,\n              startOffset: null,\n              startSize: null,\n              deltaOffset: null,\n              deltaPercentage: null,\n              columnSizingStart: [],\n            }))\n          }\n\n          const mouseEvents = {\n            moveHandler: (e: MouseEvent) => onMove(e.clientX),\n            upHandler: (e: MouseEvent) => {\n              document.removeEventListener('mousemove', mouseEvents.moveHandler)\n              document.removeEventListener('mouseup', mouseEvents.upHandler)\n              onEnd(e.clientX)\n            },\n          }\n\n          const touchEvents = {\n            moveHandler: (e: TouchEvent) => {\n              if (e.cancelable) {\n                e.preventDefault()\n                e.stopPropagation()\n              }\n              onMove(e.touches[0]!.clientX)\n              return false\n            },\n            upHandler: (e: TouchEvent) => {\n              document.removeEventListener('touchmove', touchEvents.moveHandler)\n              document.removeEventListener('touchend', touchEvents.upHandler)\n              if (e.cancelable) {\n                e.preventDefault()\n                e.stopPropagation()\n              }\n              onEnd(e.touches[0]?.clientX)\n            },\n          }\n\n          const passiveIfSupported = passiveEventSupported()\n            ? { passive: false }\n            : false\n\n          if (isTouchStartEvent(e)) {\n            document.addEventListener(\n              'touchmove',\n              touchEvents.moveHandler,\n              passiveIfSupported\n            )\n            document.addEventListener(\n              'touchend',\n              touchEvents.upHandler,\n              passiveIfSupported\n            )\n          } else {\n            document.addEventListener(\n              'mousemove',\n              mouseEvents.moveHandler,\n              passiveIfSupported\n            )\n            document.addEventListener(\n              'mouseup',\n              mouseEvents.upHandler,\n              passiveIfSupported\n            )\n          }\n\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            startOffset: clientX,\n            startSize,\n            deltaOffset: 0,\n            deltaPercentage: 0,\n            columnSizingStart,\n            isResizingColumn: column.id,\n          }))\n        }\n      },\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingInstance => {\n    return {\n      setColumnSizing: updater => table.options.onColumnSizingChange?.(updater),\n      setColumnSizingInfo: updater =>\n        table.options.onColumnSizingInfoChange?.(updater),\n      resetColumnSizing: defaultState => {\n        table.setColumnSizing(\n          defaultState ? {} : table.initialState.columnSizing ?? {}\n        )\n      },\n      resetHeaderSizeInfo: defaultState => {\n        table.setColumnSizingInfo(\n          defaultState\n            ? getDefaultColumnSizingInfoState()\n            : table.initialState.columnSizingInfo ??\n                getDefaultColumnSizingInfoState()\n        )\n      },\n      getTotalSize: () =>\n        table.getHeaderGroups()[0]?.headers.reduce((sum, header) => {\n          return sum + header.getSize()\n        }, 0) ?? 0,\n      getLeftTotalSize: () =>\n        table.getLeftHeaderGroups()[0]?.headers.reduce((sum, header) => {\n          return sum + header.getSize()\n        }, 0) ?? 0,\n      getCenterTotalSize: () =>\n        table.getCenterHeaderGroups()[0]?.headers.reduce((sum, header) => {\n          return sum + header.getSize()\n        }, 0) ?? 0,\n      getRightTotalSize: () =>\n        table.getRightHeaderGroups()[0]?.headers.reduce((sum, header) => {\n          return sum + header.getSize()\n        }, 0) ?? 0,\n    }\n  },\n}\n\nlet passiveSupported: boolean | null = null\nexport function passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported\n\n  let supported = false\n  try {\n    const options = {\n      get passive() {\n        supported = true\n        return false\n      },\n    }\n\n    const noop = () => {}\n\n    window.addEventListener('test', noop, options)\n    window.removeEventListener('test', noop)\n  } catch (err) {\n    supported = false\n  }\n  passiveSupported = supported\n  return passiveSupported\n}\n\nfunction isTouchStartEvent(e: unknown): e is TouchEvent {\n  return (e as TouchEvent).type === 'touchstart'\n}\n", "import { RowModel } from '..'\nimport { TableFeature } from '../core/table'\nimport { OnChangeFn, Table, Row, Updater, RowData } from '../types'\nimport { makeStateUpdater } from '../utils'\n\nexport type ExpandedStateList = Record<string, boolean>\nexport type ExpandedState = true | Record<string, boolean>\nexport interface ExpandedTableState {\n  expanded: ExpandedState\n}\n\nexport interface ExpandedRow {\n  toggleExpanded: (expanded?: boolean) => void\n  getIsExpanded: () => boolean\n  getCanExpand: () => boolean\n  getToggleExpandedHandler: () => () => void\n}\n\nexport interface ExpandedOptions<TData extends RowData> {\n  manualExpanding?: boolean\n  onExpandedChange?: OnChangeFn<ExpandedState>\n  autoResetExpanded?: boolean\n  enableExpanding?: boolean\n  getExpandedRowModel?: (table: Table<any>) => () => RowModel<any>\n  getIsRowExpanded?: (row: Row<TData>) => boolean\n  getRowCanExpand?: (row: Row<TData>) => boolean\n  paginateExpandedRows?: boolean\n}\n\nexport interface ExpandedInstance<TData extends RowData> {\n  _autoResetExpanded: () => void\n  setExpanded: (updater: Updater<ExpandedState>) => void\n  toggleAllRowsExpanded: (expanded?: boolean) => void\n  resetExpanded: (defaultState?: boolean) => void\n  getCanSomeRowsExpand: () => boolean\n  getToggleAllRowsExpandedHandler: () => (event: unknown) => void\n  getIsSomeRowsExpanded: () => boolean\n  getIsAllRowsExpanded: () => boolean\n  getExpandedDepth: () => number\n  getExpandedRowModel: () => RowModel<TData>\n  _getExpandedRowModel?: () => RowModel<TData>\n  getPreExpandedRowModel: () => RowModel<TData>\n}\n\n//\n\nexport const Expanding: TableFeature = {\n  getInitialState: (state): ExpandedTableState => {\n    return {\n      expanded: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedOptions<TData> => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true,\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedInstance<TData> => {\n    let registered = false\n    let queued = false\n\n    return {\n      _autoResetExpanded: () => {\n        if (!registered) {\n          table._queue(() => {\n            registered = true\n          })\n          return\n        }\n\n        if (\n          table.options.autoResetAll ??\n          table.options.autoResetExpanded ??\n          !table.options.manualExpanding\n        ) {\n          if (queued) return\n          queued = true\n          table._queue(() => {\n            table.resetExpanded()\n            queued = false\n          })\n        }\n      },\n      setExpanded: updater => table.options.onExpandedChange?.(updater),\n      toggleAllRowsExpanded: expanded => {\n        if (expanded ?? !table.getIsAllRowsExpanded()) {\n          table.setExpanded(true)\n        } else {\n          table.setExpanded({})\n        }\n      },\n      resetExpanded: defaultState => {\n        table.setExpanded(\n          defaultState ? {} : table.initialState?.expanded ?? {}\n        )\n      },\n      getCanSomeRowsExpand: () => {\n        return table\n          .getPrePaginationRowModel()\n          .flatRows.some(row => row.getCanExpand())\n      },\n      getToggleAllRowsExpandedHandler: () => {\n        return (e: unknown) => {\n          ;(e as any).persist?.()\n          table.toggleAllRowsExpanded()\n        }\n      },\n      getIsSomeRowsExpanded: () => {\n        const expanded = table.getState().expanded\n        return expanded === true || Object.values(expanded).some(Boolean)\n      },\n      getIsAllRowsExpanded: () => {\n        const expanded = table.getState().expanded\n\n        // If expanded is true, save some cycles and return true\n        if (typeof expanded === 'boolean') {\n          return expanded === true\n        }\n\n        if (!Object.keys(expanded).length) {\n          return false\n        }\n\n        // If any row is not expanded, return false\n        if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n          return false\n        }\n\n        // They must all be expanded :shrug:\n        return true\n      },\n      getExpandedDepth: () => {\n        let maxDepth = 0\n\n        const rowIds =\n          table.getState().expanded === true\n            ? Object.keys(table.getRowModel().rowsById)\n            : Object.keys(table.getState().expanded)\n\n        rowIds.forEach(id => {\n          const splitId = id.split('.')\n          maxDepth = Math.max(maxDepth, splitId.length)\n        })\n\n        return maxDepth\n      },\n      getPreExpandedRowModel: () => table.getSortedRowModel(),\n      getExpandedRowModel: () => {\n        if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n          table._getExpandedRowModel = table.options.getExpandedRowModel(table)\n        }\n\n        if (table.options.manualExpanding || !table._getExpandedRowModel) {\n          return table.getPreExpandedRowModel()\n        }\n\n        return table._getExpandedRowModel()\n      },\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): ExpandedRow => {\n    return {\n      toggleExpanded: expanded => {\n        table.setExpanded(old => {\n          const exists = old === true ? true : !!old?.[row.id]\n\n          let oldExpanded: ExpandedStateList = {}\n\n          if (old === true) {\n            Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n              oldExpanded[rowId] = true\n            })\n          } else {\n            oldExpanded = old\n          }\n\n          expanded = expanded ?? !exists\n\n          if (!exists && expanded) {\n            return {\n              ...oldExpanded,\n              [row.id]: true,\n            }\n          }\n\n          if (exists && !expanded) {\n            const { [row.id]: _, ...rest } = oldExpanded\n            return rest\n          }\n\n          return old\n        })\n      },\n      getIsExpanded: () => {\n        const expanded = table.getState().expanded\n\n        return !!(\n          table.options.getIsRowExpanded?.(row) ??\n          (expanded === true || expanded?.[row.id])\n        )\n      },\n      getCanExpand: () => {\n        return (\n          table.options.getRowCanExpand?.(row) ??\n          ((table.options.enableExpanding ?? true) && !!row.subRows?.length)\n        )\n      },\n      getToggleExpandedHandler: () => {\n        const canExpand = row.getCanExpand()\n\n        return () => {\n          if (!canExpand) return\n          row.toggleExpanded()\n        }\n      },\n    }\n  },\n}\n", "import { FilterFn } from './features/Filters'\n\nconst includesString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  const search = filterValue.toLowerCase()\n  return Boolean(\n    row\n      .getValue<string | null>(columnId)\n      ?.toString()\n      ?.toLowerCase()\n      ?.includes(search)\n  )\n}\n\nincludesString.autoRemove = (val: any) => testFalsey(val)\n\nconst includesStringSensitive: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return Boolean(\n    row.getValue<string | null>(columnId)?.toString()?.includes(filterValue)\n  )\n}\n\nincludesStringSensitive.autoRemove = (val: any) => testFalsey(val)\n\nconst equalsString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return (\n    row.getValue<string | null>(columnId)?.toString()?.toLowerCase() ===\n    filterValue?.toLowerCase()\n  )\n}\n\nequalsString.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludes: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue<unknown[]>(columnId)?.includes(filterValue)\n}\n\narrIncludes.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesAll: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return !filterValue.some(\n    val => !row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesAll.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesSome: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return filterValue.some(val =>\n    row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesSome.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst equals: FilterFn<any> = (row, columnId: string, filterValue: unknown) => {\n  return row.getValue(columnId) === filterValue\n}\n\nequals.autoRemove = (val: any) => testFalsey(val)\n\nconst weakEquals: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue(columnId) == filterValue\n}\n\nweakEquals.autoRemove = (val: any) => testFalsey(val)\n\nconst inNumberRange: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: [number, number]\n) => {\n  let [min, max] = filterValue\n\n  const rowValue = row.getValue<number>(columnId)\n  return rowValue >= min && rowValue <= max\n}\n\ninNumberRange.resolveFilterValue = (val: [any, any]) => {\n  let [unsafeMin, unsafeMax] = val\n\n  let parsedMin =\n    typeof unsafeMin !== 'number' ? parseFloat(unsafeMin as string) : unsafeMin\n  let parsedMax =\n    typeof unsafeMax !== 'number' ? parseFloat(unsafeMax as string) : unsafeMax\n\n  let min =\n    unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax\n\n  if (min > max) {\n    const temp = min\n    min = max\n    max = temp\n  }\n\n  return [min, max] as const\n}\n\ninNumberRange.autoRemove = (val: any) =>\n  testFalsey(val) || (testFalsey(val[0]) && testFalsey(val[1]))\n\n// Export\n\nexport const filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange,\n}\n\nexport type BuiltInFilterFn = keyof typeof filterFns\n\n// Utils\n\nfunction testFalsey(val: any) {\n  return val === undefined || val === null || val === ''\n}\n", "import { RowModel } from '..'\nimport { TableFeature } from '../core/table'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  FilterMeta,\n  FilterFns,\n} from '../types'\nimport { functionalUpdate, isFunction, makeStateUpdater } from '../utils'\n\nexport interface FiltersTableState {\n  columnFilters: ColumnFiltersState\n  globalFilter: any\n}\n\nexport type ColumnFiltersState = ColumnFilter[]\n\nexport interface ColumnFilter {\n  id: string\n  value: unknown\n}\n\nexport interface ResolvedColumnFilter<TData extends RowData> {\n  id: string\n  resolvedValue: unknown\n  filterFn: FilterFn<TData>\n}\n\nexport interface FilterFn<TData extends RowData> {\n  (\n    row: Row<TData>,\n    columnId: string,\n    filterValue: any,\n    addMeta: (meta: FilterMeta) => void\n  ): boolean\n\n  resolveFilterValue?: TransformFilterValueFn<TData>\n  autoRemove?: ColumnFilterAutoRemoveTestFn<TData>\n}\n\nexport type TransformFilterValueFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => unknown\n\nexport type ColumnFilterAutoRemoveTestFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => boolean\n\nexport type CustomFilterFns<TData extends RowData> = Record<\n  string,\n  FilterFn<TData>\n>\n\nexport type FilterFnOption<TData extends RowData> =\n  | 'auto'\n  | BuiltInFilterFn\n  | keyof FilterFns\n  | FilterFn<TData>\n\nexport interface FiltersColumnDef<TData extends RowData> {\n  filterFn?: FilterFnOption<TData>\n  enableColumnFilter?: boolean\n  enableGlobalFilter?: boolean\n}\n\nexport interface FiltersColumn<TData extends RowData> {\n  getAutoFilterFn: () => FilterFn<TData> | undefined\n  getFilterFn: () => FilterFn<TData> | undefined\n  setFilterValue: (updater: Updater<any>) => void\n  getCanFilter: () => boolean\n  getCanGlobalFilter: () => boolean\n  getFacetedRowModel: () => RowModel<TData>\n  _getFacetedRowModel?: () => RowModel<TData>\n  getIsFiltered: () => boolean\n  getFilterValue: () => unknown\n  getFilterIndex: () => number\n  getFacetedUniqueValues: () => Map<any, number>\n  _getFacetedUniqueValues?: () => Map<any, number>\n  getFacetedMinMaxValues: () => undefined | [number, number]\n  _getFacetedMinMaxValues?: () => undefined | [number, number]\n}\n\nexport interface FiltersRow<TData extends RowData> {\n  columnFilters: Record<string, boolean>\n  columnFiltersMeta: Record<string, FilterMeta>\n}\n\ninterface FiltersOptionsBase<TData extends RowData> {\n  enableFilters?: boolean\n  manualFiltering?: boolean\n  filterFromLeafRows?: boolean\n  maxLeafRowFilterDepth?: number\n  getFilteredRowModel?: (table: Table<any>) => () => RowModel<any>\n\n  // Column\n  onColumnFiltersChange?: OnChangeFn<ColumnFiltersState>\n  enableColumnFilters?: boolean\n\n  // Global\n  globalFilterFn?: FilterFnOption<TData>\n  onGlobalFilterChange?: OnChangeFn<any>\n  enableGlobalFilter?: boolean\n  getColumnCanGlobalFilter?: (column: Column<TData, unknown>) => boolean\n\n  // Faceting\n  getFacetedRowModel?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => RowModel<TData>\n  getFacetedUniqueValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => Map<any, number>\n  getFacetedMinMaxValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => undefined | [number, number]\n}\n\ntype ResolvedFilterFns = keyof FilterFns extends never\n  ? {\n      filterFns?: Record<string, FilterFn<any>>\n    }\n  : {\n      filterFns: Record<keyof FilterFns, FilterFn<any>>\n    }\n\nexport interface FiltersOptions<TData extends RowData>\n  extends FiltersOptionsBase<TData>,\n    ResolvedFilterFns {}\n\nexport interface FiltersInstance<TData extends RowData> {\n  setColumnFilters: (updater: Updater<ColumnFiltersState>) => void\n\n  resetColumnFilters: (defaultState?: boolean) => void\n\n  // Column Filters\n  getPreFilteredRowModel: () => RowModel<TData>\n  getFilteredRowModel: () => RowModel<TData>\n  _getFilteredRowModel?: () => RowModel<TData>\n\n  // Global Filters\n  setGlobalFilter: (updater: Updater<any>) => void\n  resetGlobalFilter: (defaultState?: boolean) => void\n  getGlobalAutoFilterFn: () => FilterFn<TData> | undefined\n  getGlobalFilterFn: () => FilterFn<TData> | undefined\n  getGlobalFacetedRowModel: () => RowModel<TData>\n  _getGlobalFacetedRowModel?: () => RowModel<TData>\n  getGlobalFacetedUniqueValues: () => Map<any, number>\n  _getGlobalFacetedUniqueValues?: () => Map<any, number>\n  getGlobalFacetedMinMaxValues: () => undefined | [number, number]\n  _getGlobalFacetedMinMaxValues?: () => undefined | [number, number]\n}\n\n//\n\nexport const Filters: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): FiltersColumnDef<TData> => {\n    return {\n      filterFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): FiltersTableState => {\n    return {\n      columnFilters: [],\n      globalFilter: undefined,\n      // filtersProgress: 1,\n      // facetProgress: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): FiltersOptions<TData> => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100,\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        const value = table\n          .getCoreRowModel()\n          .flatRows[0]?._getAllCellsByColumnId()\n          [column.id]?.getValue()\n\n        return typeof value === 'string' || typeof value === 'number'\n      },\n    } as FiltersOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): FiltersColumn<TData> => {\n    return {\n      getAutoFilterFn: () => {\n        const firstRow = table.getCoreRowModel().flatRows[0]\n\n        const value = firstRow?.getValue(column.id)\n\n        if (typeof value === 'string') {\n          return filterFns.includesString\n        }\n\n        if (typeof value === 'number') {\n          return filterFns.inNumberRange\n        }\n\n        if (typeof value === 'boolean') {\n          return filterFns.equals\n        }\n\n        if (value !== null && typeof value === 'object') {\n          return filterFns.equals\n        }\n\n        if (Array.isArray(value)) {\n          return filterFns.arrIncludes\n        }\n\n        return filterFns.weakEquals\n      },\n      getFilterFn: () => {\n        return isFunction(column.columnDef.filterFn)\n          ? column.columnDef.filterFn\n          : column.columnDef.filterFn === 'auto'\n          ? column.getAutoFilterFn()\n          // @ts-ignore \n          : table.options.filterFns?.[column.columnDef.filterFn as string] ??\n            filterFns[column.columnDef.filterFn as BuiltInFilterFn]\n      },\n      getCanFilter: () => {\n        return (\n          (column.columnDef.enableColumnFilter ?? true) &&\n          (table.options.enableColumnFilters ?? true) &&\n          (table.options.enableFilters ?? true) &&\n          !!column.accessorFn\n        )\n      },\n\n      getCanGlobalFilter: () => {\n        return (\n          (column.columnDef.enableGlobalFilter ?? true) &&\n          (table.options.enableGlobalFilter ?? true) &&\n          (table.options.enableFilters ?? true) &&\n          (table.options.getColumnCanGlobalFilter?.(column) ?? true) &&\n          !!column.accessorFn\n        )\n      },\n\n      getIsFiltered: () => column.getFilterIndex() > -1,\n\n      getFilterValue: () =>\n        table.getState().columnFilters?.find(d => d.id === column.id)?.value,\n\n      getFilterIndex: () =>\n        table.getState().columnFilters?.findIndex(d => d.id === column.id) ??\n        -1,\n\n      setFilterValue: value => {\n        table.setColumnFilters(old => {\n          const filterFn = column.getFilterFn()\n          const previousfilter = old?.find(d => d.id === column.id)\n\n          const newFilter = functionalUpdate(\n            value,\n            previousfilter ? previousfilter.value : undefined\n          )\n\n          //\n          if (\n            shouldAutoRemoveFilter(\n              filterFn as FilterFn<TData>,\n              newFilter,\n              column\n            )\n          ) {\n            return old?.filter(d => d.id !== column.id) ?? []\n          }\n\n          const newFilterObj = { id: column.id, value: newFilter }\n\n          if (previousfilter) {\n            return (\n              old?.map(d => {\n                if (d.id === column.id) {\n                  return newFilterObj\n                }\n                return d\n              }) ?? []\n            )\n          }\n\n          if (old?.length) {\n            return [...old, newFilterObj]\n          }\n\n          return [newFilterObj]\n        })\n      },\n      _getFacetedRowModel:\n        table.options.getFacetedRowModel &&\n        table.options.getFacetedRowModel(table, column.id),\n      getFacetedRowModel: () => {\n        if (!column._getFacetedRowModel) {\n          return table.getPreFilteredRowModel()\n        }\n\n        return column._getFacetedRowModel()\n      },\n      _getFacetedUniqueValues:\n        table.options.getFacetedUniqueValues &&\n        table.options.getFacetedUniqueValues(table, column.id),\n      getFacetedUniqueValues: () => {\n        if (!column._getFacetedUniqueValues) {\n          return new Map()\n        }\n\n        return column._getFacetedUniqueValues()\n      },\n      _getFacetedMinMaxValues:\n        table.options.getFacetedMinMaxValues &&\n        table.options.getFacetedMinMaxValues(table, column.id),\n      getFacetedMinMaxValues: () => {\n        if (!column._getFacetedMinMaxValues) {\n          return undefined\n        }\n\n        return column._getFacetedMinMaxValues()\n      },\n      // () => [column.getFacetedRowModel()],\n      // facetedRowModel => getRowModelMinMaxValues(facetedRowModel, column.id),\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): FiltersRow<TData> => {\n    return {\n      columnFilters: {},\n      columnFiltersMeta: {},\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): FiltersInstance<TData> => {\n    return {\n      getGlobalAutoFilterFn: () => {\n        return filterFns.includesString\n      },\n\n      getGlobalFilterFn: () => {\n        const { globalFilterFn: globalFilterFn } = table.options\n\n        return isFunction(globalFilterFn)\n          ? globalFilterFn\n          : globalFilterFn === 'auto'\n          ? table.getGlobalAutoFilterFn()\n          // @ts-ignore\n          : table.options.filterFns?.[globalFilterFn as string] ??\n            filterFns[globalFilterFn as BuiltInFilterFn]\n      },\n\n      setColumnFilters: (updater: Updater<ColumnFiltersState>) => {\n        const leafColumns = table.getAllLeafColumns()\n\n        const updateFn = (old: ColumnFiltersState) => {\n          return functionalUpdate(updater, old)?.filter(filter => {\n            const column = leafColumns.find(d => d.id === filter.id)\n\n            if (column) {\n              const filterFn = column.getFilterFn()\n\n              if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n                return false\n              }\n            }\n\n            return true\n          })\n        }\n\n        table.options.onColumnFiltersChange?.(updateFn)\n      },\n\n      setGlobalFilter: updater => {\n        table.options.onGlobalFilterChange?.(updater)\n      },\n\n      resetGlobalFilter: defaultState => {\n        table.setGlobalFilter(\n          defaultState ? undefined : table.initialState.globalFilter\n        )\n      },\n\n      resetColumnFilters: defaultState => {\n        table.setColumnFilters(\n          defaultState ? [] : table.initialState?.columnFilters ?? []\n        )\n      },\n\n      getPreFilteredRowModel: () => table.getCoreRowModel(),\n      getFilteredRowModel: () => {\n        if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n          table._getFilteredRowModel = table.options.getFilteredRowModel(table)\n        }\n\n        if (table.options.manualFiltering || !table._getFilteredRowModel) {\n          return table.getPreFilteredRowModel()\n        }\n\n        return table._getFilteredRowModel()\n      },\n\n      _getGlobalFacetedRowModel:\n        table.options.getFacetedRowModel &&\n        table.options.getFacetedRowModel(table, '__global__'),\n\n      getGlobalFacetedRowModel: () => {\n        if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n          return table.getPreFilteredRowModel()\n        }\n\n        return table._getGlobalFacetedRowModel()\n      },\n\n      _getGlobalFacetedUniqueValues:\n        table.options.getFacetedUniqueValues &&\n        table.options.getFacetedUniqueValues(table, '__global__'),\n      getGlobalFacetedUniqueValues: () => {\n        if (!table._getGlobalFacetedUniqueValues) {\n          return new Map()\n        }\n\n        return table._getGlobalFacetedUniqueValues()\n      },\n\n      _getGlobalFacetedMinMaxValues:\n        table.options.getFacetedMinMaxValues &&\n        table.options.getFacetedMinMaxValues(table, '__global__'),\n      getGlobalFacetedMinMaxValues: () => {\n        if (!table._getGlobalFacetedMinMaxValues) {\n          return\n        }\n\n        return table._getGlobalFacetedMinMaxValues()\n      },\n    }\n  },\n}\n\nexport function shouldAutoRemoveFilter<TData extends RowData>(\n  filterFn?: FilterFn<TData>,\n  value?: any,\n  column?: Column<TData, unknown>\n) {\n  return (\n    (filterFn && filterFn.autoRemove\n      ? filterFn.autoRemove(value, column)\n      : false) ||\n    typeof value === 'undefined' ||\n    (typeof value === 'string' && !value)\n  )\n}\n", "import { AggregationFn } from './features/Grouping'\nimport { isNumberArray } from './utils'\n\nconst sum: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId)\n    return sum + (typeof nextValue === 'number' ? nextValue : 0)\n  }, 0)\n}\n\nconst min: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n\n    if (\n      value != null &&\n      (min! > value || (min === undefined && value >= value))\n    ) {\n      min = value\n    }\n  })\n\n  return min\n}\n\nconst max: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (\n      value != null &&\n      (max! < value || (max === undefined && value >= value))\n    ) {\n      max = value\n    }\n  })\n\n  return max\n}\n\nconst extent: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value\n      } else {\n        if (min > value) min = value\n        if (max! < value) max = value\n      }\n    }\n  })\n\n  return [min, max]\n}\n\nconst mean: AggregationFn<any> = (columnId, leafRows) => {\n  let count = 0\n  let sum = 0\n\n  leafRows.forEach(row => {\n    let value = row.getValue<number>(columnId)\n    if (value != null && (value = +value) >= value) {\n      ++count, (sum += value)\n    }\n  })\n\n  if (count) return sum / count\n\n  return\n}\n\nconst median: AggregationFn<any> = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return\n  }\n\n  const values = leafRows.map(row => row.getValue(columnId))\n  if (!isNumberArray(values)) {\n    return\n  }\n  if (values.length === 1) {\n    return values[0]\n  }\n\n  const mid = Math.floor(values.length / 2)\n  const nums = values.sort((a, b) => a - b)\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1]! + nums[mid]!) / 2\n}\n\nconst unique: AggregationFn<any> = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values())\n}\n\nconst uniqueCount: AggregationFn<any> = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size\n}\n\nconst count: AggregationFn<any> = (_columnId, leafRows) => {\n  return leafRows.length\n}\n\nexport const aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count,\n}\n\nexport type BuiltInAggregationFn = keyof typeof aggregationFns\n", "import { RowModel } from '..'\nimport { BuiltInAggregationFn, aggregationFns } from '../aggregationFns'\nimport { TableFeature } from '../core/table'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  ColumnDefTemplate,\n  RowData,\n  AggregationFns,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type GroupingState = string[]\n\nexport interface GroupingTableState {\n  grouping: GroupingState\n}\n\nexport type AggregationFn<TData extends RowData> = (\n  columnId: string,\n  leafRows: Row<TData>[],\n  childRows: Row<TData>[]\n) => any\n\nexport type CustomAggregationFns = Record<string, AggregationFn<any>>\n\nexport type AggregationFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof AggregationFns\n  | BuiltInAggregationFn\n  | AggregationFn<TData>\n\nexport interface GroupingColumnDef<TData extends RowData, TValue> {\n  aggregationFn?: AggregationFnOption<TData>\n  aggregatedCell?: ColumnDefTemplate<\n    ReturnType<Cell<TData, TValue>['getContext']>\n  >\n  enableGrouping?: boolean\n  getGroupingValue?: (row: TData) => any\n}\n\nexport interface GroupingColumn<TData extends RowData> {\n  getCanGroup: () => boolean\n  getIsGrouped: () => boolean\n  getGroupedIndex: () => number\n  toggleGrouping: () => void\n  getToggleGroupingHandler: () => () => void\n  getAutoAggregationFn: () => AggregationFn<TData> | undefined\n  getAggregationFn: () => AggregationFn<TData> | undefined\n}\n\nexport interface GroupingRow {\n  groupingColumnId?: string\n  groupingValue?: unknown\n  getIsGrouped: () => boolean\n  getGroupingValue: (columnId: string) => unknown\n  _groupingValuesCache: Record<string, any>\n}\n\nexport interface GroupingCell {\n  getIsGrouped: () => boolean\n  getIsPlaceholder: () => boolean\n  getIsAggregated: () => boolean\n}\n\nexport interface ColumnDefaultOptions {\n  // Column\n  onGroupingChange: OnChangeFn<GroupingState>\n  enableGrouping: boolean\n}\n\ninterface GroupingOptionsBase {\n  manualGrouping?: boolean\n  onGroupingChange?: OnChangeFn<GroupingState>\n  enableGrouping?: boolean\n  getGroupedRowModel?: (table: Table<any>) => () => RowModel<any>\n  groupedColumnMode?: false | 'reorder' | 'remove'\n}\n\ntype ResolvedAggregationFns = keyof AggregationFns extends never\n  ? {\n      aggregationFns?: Record<string, AggregationFn<any>>\n    }\n  : {\n      aggregationFns: Record<keyof AggregationFns, AggregationFn<any>>\n    }\n\nexport interface GroupingOptions\n  extends GroupingOptionsBase,\n    ResolvedAggregationFns {}\n\nexport type GroupingColumnMode = false | 'reorder' | 'remove'\n\nexport interface GroupingInstance<TData extends RowData> {\n  setGrouping: (updater: Updater<GroupingState>) => void\n  resetGrouping: (defaultState?: boolean) => void\n  getPreGroupedRowModel: () => RowModel<TData>\n  getGroupedRowModel: () => RowModel<TData>\n  _getGroupedRowModel?: () => RowModel<TData>\n}\n\n//\n\nexport const Grouping: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): GroupingColumnDef<\n    TData,\n    unknown\n  > => {\n    return {\n      aggregatedCell: props => (props.getValue() as any)?.toString?.() ?? null,\n      aggregationFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): GroupingTableState => {\n    return {\n      grouping: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingOptions => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder',\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): GroupingColumn<TData> => {\n    return {\n      toggleGrouping: () => {\n        table.setGrouping(old => {\n          // Find any existing grouping for this column\n          if (old?.includes(column.id)) {\n            return old.filter(d => d !== column.id)\n          }\n\n          return [...(old ?? []), column.id]\n        })\n      },\n\n      getCanGroup: () => {\n        return (\n          column.columnDef.enableGrouping ??\n          true ??\n          table.options.enableGrouping ??\n          true ??\n          !!column.accessorFn\n        )\n      },\n\n      getIsGrouped: () => {\n        return table.getState().grouping?.includes(column.id)\n      },\n\n      getGroupedIndex: () => table.getState().grouping?.indexOf(column.id),\n\n      getToggleGroupingHandler: () => {\n        const canGroup = column.getCanGroup()\n\n        return () => {\n          if (!canGroup) return\n          column.toggleGrouping()\n        }\n      },\n      getAutoAggregationFn: () => {\n        const firstRow = table.getCoreRowModel().flatRows[0]\n\n        const value = firstRow?.getValue(column.id)\n\n        if (typeof value === 'number') {\n          return aggregationFns.sum\n        }\n\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return aggregationFns.extent\n        }\n      },\n      getAggregationFn: () => {\n        if (!column) {\n          throw new Error()\n        }\n\n        return isFunction(column.columnDef.aggregationFn)\n          ? column.columnDef.aggregationFn\n          : column.columnDef.aggregationFn === 'auto'\n          ? column.getAutoAggregationFn()\n          : table.options.aggregationFns?.[\n              column.columnDef.aggregationFn as string\n            ] ??\n            aggregationFns[\n              column.columnDef.aggregationFn as BuiltInAggregationFn\n            ]\n      },\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingInstance<TData> => {\n    return {\n      setGrouping: updater => table.options.onGroupingChange?.(updater),\n\n      resetGrouping: defaultState => {\n        table.setGrouping(\n          defaultState ? [] : table.initialState?.grouping ?? []\n        )\n      },\n\n      getPreGroupedRowModel: () => table.getFilteredRowModel(),\n      getGroupedRowModel: () => {\n        if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n          table._getGroupedRowModel = table.options.getGroupedRowModel(table)\n        }\n\n        if (table.options.manualGrouping || !table._getGroupedRowModel) {\n          return table.getPreGroupedRowModel()\n        }\n\n        return table._getGroupedRowModel()\n      },\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): GroupingRow => {\n    return {\n      getIsGrouped: () => !!row.groupingColumnId,\n      getGroupingValue: columnId => {\n        if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n          return row._groupingValuesCache[columnId]\n        }\n\n        const column = table.getColumn(columnId)\n\n        if (!column?.columnDef.getGroupingValue) {\n          return row.getValue(columnId)\n        }\n\n        row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(\n          row.original\n        )\n\n        return row._groupingValuesCache[columnId]\n      },\n      _groupingValuesCache: {},\n    }\n  },\n\n  createCell: <TData extends RowData, TValue>(\n    cell: Cell<TData, TValue>,\n    column: Column<TData, TValue>,\n    row: Row<TData>,\n    table: Table<TData>\n  ): GroupingCell => {\n    const getRenderValue = () =>\n      cell.getValue() ?? table.options.renderFallbackValue\n\n    return {\n      getIsGrouped: () =>\n        column.getIsGrouped() && column.id === row.groupingColumnId,\n      getIsPlaceholder: () => !cell.getIsGrouped() && column.getIsGrouped(),\n      getIsAggregated: () =>\n        !cell.getIsGrouped() &&\n        !cell.getIsPlaceholder() &&\n        !!row.subRows?.length,\n    }\n  },\n}\n\nexport function orderColumns<TData extends RowData>(\n  leafColumns: Column<TData, unknown>[],\n  grouping: string[],\n  groupedColumnMode?: GroupingColumnMode\n) {\n  if (!grouping?.length || !groupedColumnMode) {\n    return leafColumns\n  }\n\n  const nonGroupingColumns = leafColumns.filter(\n    col => !grouping.includes(col.id)\n  )\n\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns\n  }\n\n  const groupingColumns = grouping\n    .map(g => leafColumns.find(col => col.id === g)!)\n    .filter(Boolean)\n\n  return [...groupingColumns, ...nonGroupingColumns]\n}\n", "import { makeStateUpdater, memo } from '../utils'\n\nimport { Table, OnChangeFn, Updater, Column, RowData } from '../types'\n\nimport { orderColumns } from './Grouping'\nimport { TableFeature } from '../core/table'\n\nexport interface ColumnOrderTableState {\n  columnOrder: ColumnOrderState\n}\n\nexport type ColumnOrderState = string[]\n\nexport interface ColumnOrderOptions {\n  onColumnOrderChange?: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderDefaultOptions {\n  onColumnOrderChange: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderInstance<TData extends RowData> {\n  setColumnOrder: (updater: Updater<ColumnOrderState>) => void\n  resetColumnOrder: (defaultState?: boolean) => void\n  _getOrderColumnsFn: () => (\n    columns: Column<TData, unknown>[]\n  ) => Column<TData, unknown>[]\n}\n\n//\n\nexport const Ordering: TableFeature = {\n  getInitialState: (state): ColumnOrderTableState => {\n    return {\n      columnOrder: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderDefaultOptions => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderInstance<TData> => {\n    return {\n      setColumnOrder: updater => table.options.onColumnOrderChange?.(updater),\n      resetColumnOrder: defaultState => {\n        table.setColumnOrder(\n          defaultState ? [] : table.initialState.columnOrder ?? []\n        )\n      },\n      _getOrderColumnsFn: memo(\n        () => [\n          table.getState().columnOrder,\n          table.getState().grouping,\n          table.options.groupedColumnMode,\n        ],\n        (columnOrder, grouping, groupedColumnMode) => columns => {\n          // Sort grouped columns to the start of the column list\n          // before the headers are built\n          let orderedColumns: Column<TData, unknown>[] = []\n\n          // If there is no order, return the normal columns\n          if (!columnOrder?.length) {\n            orderedColumns = columns\n          } else {\n            const columnOrderCopy = [...columnOrder]\n\n            // If there is an order, make a copy of the columns\n            const columnsCopy = [...columns]\n\n            // And make a new ordered array of the columns\n\n            // Loop over the columns and place them in order into the new array\n            while (columnsCopy.length && columnOrderCopy.length) {\n              const targetColumnId = columnOrderCopy.shift()\n              const foundIndex = columnsCopy.findIndex(\n                d => d.id === targetColumnId\n              )\n              if (foundIndex > -1) {\n                orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]!)\n              }\n            }\n\n            // If there are any columns left, add them to the end\n            orderedColumns = [...orderedColumns, ...columnsCopy]\n          }\n\n          return orderColumns(orderedColumns, grouping, groupedColumnMode)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getOrderColumnsFn',\n          // debug: () => table.options.debugAll ?? table.options.debugTable,\n        }\n      ),\n    }\n  },\n}\n", "import { TableFeature } from '../core/table'\nimport { OnChangeFn, Table, RowModel, Updater, RowData } from '../types'\nimport { functionalUpdate, makeStateUpdater, memo } from '../utils'\n\nexport interface PaginationState {\n  pageIndex: number\n  pageSize: number\n}\n\nexport interface PaginationTableState {\n  pagination: PaginationState\n}\n\nexport interface PaginationInitialTableState {\n  pagination?: Partial<PaginationState>\n}\n\nexport interface PaginationOptions {\n  pageCount?: number\n  manualPagination?: boolean\n  onPaginationChange?: OnChangeFn<PaginationState>\n  autoResetPageIndex?: boolean\n  getPaginationRowModel?: (table: Table<any>) => () => RowModel<any>\n}\n\nexport interface PaginationDefaultOptions {\n  onPaginationChange: OnChangeFn<PaginationState>\n}\n\nexport interface PaginationInstance<TData extends RowData> {\n  _autoResetPageIndex: () => void\n  setPagination: (updater: Updater<PaginationState>) => void\n  resetPagination: (defaultState?: boolean) => void\n  setPageIndex: (updater: Updater<number>) => void\n  resetPageIndex: (defaultState?: boolean) => void\n  setPageSize: (updater: Updater<number>) => void\n  resetPageSize: (defaultState?: boolean) => void\n  setPageCount: (updater: Updater<number>) => void\n  getPageOptions: () => number[]\n  getCanPreviousPage: () => boolean\n  getCanNextPage: () => boolean\n  previousPage: () => void\n  nextPage: () => void\n  getPrePaginationRowModel: () => RowModel<TData>\n  getPaginationRowModel: () => RowModel<TData>\n  _getPaginationRowModel?: () => RowModel<TData>\n  getPageCount: () => number\n}\n\n//\n\nconst defaultPageIndex = 0\nconst defaultPageSize = 10\n\nconst getDefaultPaginationState = (): PaginationState => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize,\n})\n\nexport const Pagination: TableFeature = {\n  getInitialState: (state): PaginationTableState => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...state?.pagination,\n      },\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationDefaultOptions => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationInstance<TData> => {\n    let registered = false\n    let queued = false\n\n    return {\n      _autoResetPageIndex: () => {\n        if (!registered) {\n          table._queue(() => {\n            registered = true\n          })\n          return\n        }\n\n        if (\n          table.options.autoResetAll ??\n          table.options.autoResetPageIndex ??\n          !table.options.manualPagination\n        ) {\n          if (queued) return\n          queued = true\n          table._queue(() => {\n            table.resetPageIndex()\n            queued = false\n          })\n        }\n      },\n      setPagination: updater => {\n        const safeUpdater: Updater<PaginationState> = old => {\n          let newState = functionalUpdate(updater, old)\n\n          return newState\n        }\n\n        return table.options.onPaginationChange?.(safeUpdater)\n      },\n      resetPagination: defaultState => {\n        table.setPagination(\n          defaultState\n            ? getDefaultPaginationState()\n            : table.initialState.pagination ?? getDefaultPaginationState()\n        )\n      },\n      setPageIndex: updater => {\n        table.setPagination(old => {\n          let pageIndex = functionalUpdate(updater, old.pageIndex)\n\n          const maxPageIndex =\n            typeof table.options.pageCount === 'undefined' ||\n            table.options.pageCount === -1\n              ? Number.MAX_SAFE_INTEGER\n              : table.options.pageCount - 1\n\n          pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex))\n\n          return {\n            ...old,\n            pageIndex,\n          }\n        })\n      },\n      resetPageIndex: defaultState => {\n        table.setPageIndex(\n          defaultState\n            ? defaultPageIndex\n            : table.initialState?.pagination?.pageIndex ?? defaultPageIndex\n        )\n      },\n      resetPageSize: defaultState => {\n        table.setPageSize(\n          defaultState\n            ? defaultPageSize\n            : table.initialState?.pagination?.pageSize ?? defaultPageSize\n        )\n      },\n      setPageSize: updater => {\n        table.setPagination(old => {\n          const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize))\n          const topRowIndex = old.pageSize * old.pageIndex!\n          const pageIndex = Math.floor(topRowIndex / pageSize)\n\n          return {\n            ...old,\n            pageIndex,\n            pageSize,\n          }\n        })\n      },\n      setPageCount: updater =>\n        table.setPagination(old => {\n          let newPageCount = functionalUpdate(\n            updater,\n            table.options.pageCount ?? -1\n          )\n\n          if (typeof newPageCount === 'number') {\n            newPageCount = Math.max(-1, newPageCount)\n          }\n\n          return {\n            ...old,\n            pageCount: newPageCount,\n          }\n        }),\n\n      getPageOptions: memo(\n        () => [table.getPageCount()],\n        pageCount => {\n          let pageOptions: number[] = []\n          if (pageCount && pageCount > 0) {\n            pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i)\n          }\n          return pageOptions\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getPageOptions',\n          debug: () => table.options.debugAll ?? table.options.debugTable,\n        }\n      ),\n\n      getCanPreviousPage: () => table.getState().pagination.pageIndex > 0,\n\n      getCanNextPage: () => {\n        const { pageIndex } = table.getState().pagination\n\n        const pageCount = table.getPageCount()\n\n        if (pageCount === -1) {\n          return true\n        }\n\n        if (pageCount === 0) {\n          return false\n        }\n\n        return pageIndex < pageCount - 1\n      },\n\n      previousPage: () => {\n        return table.setPageIndex(old => old - 1)\n      },\n\n      nextPage: () => {\n        return table.setPageIndex(old => {\n          return old + 1\n        })\n      },\n\n      getPrePaginationRowModel: () => table.getExpandedRowModel(),\n      getPaginationRowModel: () => {\n        if (\n          !table._getPaginationRowModel &&\n          table.options.getPaginationRowModel\n        ) {\n          table._getPaginationRowModel =\n            table.options.getPaginationRowModel(table)\n        }\n\n        if (table.options.manualPagination || !table._getPaginationRowModel) {\n          return table.getPrePaginationRowModel()\n        }\n\n        return table._getPaginationRowModel()\n      },\n\n      getPageCount: () => {\n        return (\n          table.options.pageCount ??\n          Math.ceil(\n            table.getPrePaginationRowModel().rows.length /\n              table.getState().pagination.pageSize\n          )\n        )\n      },\n    }\n  },\n}\n", "import { TableFeature } from '../core/table'\nimport {\n  OnChangeFn,\n  Updater,\n  Table,\n  Column,\n  Row,\n  Cell,\n  RowData,\n} from '../types'\nimport { makeStateUpdater, memo } from '../utils'\n\nexport type ColumnPinningPosition = false | 'left' | 'right'\n\nexport interface ColumnPinningState {\n  left?: string[]\n  right?: string[]\n}\n\nexport interface ColumnPinningTableState {\n  columnPinning: ColumnPinningState\n}\n\nexport interface ColumnPinningOptions {\n  onColumnPinningChange?: OnChangeFn<ColumnPinningState>\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningDefaultOptions {\n  onColumnPinningChange: OnChangeFn<ColumnPinningState>\n}\n\nexport interface ColumnPinningColumnDef {\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningColumn {\n  getCanPin: () => boolean\n  getPinnedIndex: () => number\n  getIsPinned: () => ColumnPinningPosition\n  pin: (position: ColumnPinningPosition) => void\n}\n\nexport interface ColumnPinningRow<TData extends RowData> {\n  getLeftVisibleCells: () => Cell<TData, unknown>[]\n  getCenterVisibleCells: () => Cell<TData, unknown>[]\n  getRightVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface ColumnPinningInstance<TData extends RowData> {\n  setColumnPinning: (updater: Updater<ColumnPinningState>) => void\n  resetColumnPinning: (defaultState?: boolean) => void\n  getIsSomeColumnsPinned: (position?: ColumnPinningPosition) => boolean\n  getLeftLeafColumns: () => Column<TData, unknown>[]\n  getRightLeafColumns: () => Column<TData, unknown>[]\n  getCenterLeafColumns: () => Column<TData, unknown>[]\n}\n\n//\n\nconst getDefaultPinningState = (): ColumnPinningState => ({\n  left: [],\n  right: [],\n})\n\nexport const Pinning: TableFeature = {\n  getInitialState: (state): ColumnPinningTableState => {\n    return {\n      columnPinning: getDefaultPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningDefaultOptions => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): ColumnPinningColumn => {\n    return {\n      pin: position => {\n        const columnIds = column\n          .getLeafColumns()\n          .map(d => d.id)\n          .filter(Boolean) as string[]\n\n        table.setColumnPinning(old => {\n          if (position === 'right') {\n            return {\n              left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n              right: [\n                ...(old?.right ?? []).filter(d => !columnIds?.includes(d)),\n                ...columnIds,\n              ],\n            }\n          }\n\n          if (position === 'left') {\n            return {\n              left: [\n                ...(old?.left ?? []).filter(d => !columnIds?.includes(d)),\n                ...columnIds,\n              ],\n              right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n            }\n          }\n\n          return {\n            left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n            right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n          }\n        })\n      },\n\n      getCanPin: () => {\n        const leafColumns = column.getLeafColumns()\n\n        return leafColumns.some(\n          d =>\n            (d.columnDef.enablePinning ?? true) &&\n            (table.options.enablePinning ?? true)\n        )\n      },\n\n      getIsPinned: () => {\n        const leafColumnIds = column.getLeafColumns().map(d => d.id)\n\n        const { left, right } = table.getState().columnPinning\n\n        const isLeft = leafColumnIds.some(d => left?.includes(d))\n        const isRight = leafColumnIds.some(d => right?.includes(d))\n\n        return isLeft ? 'left' : isRight ? 'right' : false\n      },\n\n      getPinnedIndex: () => {\n        const position = column.getIsPinned()\n\n        return position\n          ? table.getState().columnPinning?.[position]?.indexOf(column.id) ?? -1\n          : 0\n      },\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): ColumnPinningRow<TData> => {\n    return {\n      getCenterVisibleCells: memo(\n        () => [\n          row._getAllVisibleCells(),\n          table.getState().columnPinning.left,\n          table.getState().columnPinning.right,\n        ],\n        (allCells, left, right) => {\n          const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n          return allCells.filter(d => !leftAndRight.includes(d.column.id))\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' &&\n            'row.getCenterVisibleCells',\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      ),\n      getLeftVisibleCells: memo(\n        () => [\n          row._getAllVisibleCells(),\n          table.getState().columnPinning.left,\n          ,\n        ],\n        (allCells, left) => {\n          const cells = (left ?? [])\n            .map(\n              columnId => allCells.find(cell => cell.column.id === columnId)!\n            )\n            .filter(Boolean)\n            .map(d => ({ ...d, position: 'left' } as Cell<TData, unknown>))\n\n          return cells\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' && 'row.getLeftVisibleCells',\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      ),\n      getRightVisibleCells: memo(\n        () => [row._getAllVisibleCells(), table.getState().columnPinning.right],\n        (allCells, right) => {\n          const cells = (right ?? [])\n            .map(\n              columnId => allCells.find(cell => cell.column.id === columnId)!\n            )\n            .filter(Boolean)\n            .map(d => ({ ...d, position: 'right' } as Cell<TData, unknown>))\n\n          return cells\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' && 'row.getRightVisibleCells',\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      ),\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningInstance<TData> => {\n    return {\n      setColumnPinning: updater =>\n        table.options.onColumnPinningChange?.(updater),\n\n      resetColumnPinning: defaultState =>\n        table.setColumnPinning(\n          defaultState\n            ? getDefaultPinningState()\n            : table.initialState?.columnPinning ?? getDefaultPinningState()\n        ),\n\n      getIsSomeColumnsPinned: position => {\n        const pinningState = table.getState().columnPinning\n\n        if (!position) {\n          return Boolean(\n            pinningState.left?.length || pinningState.right?.length\n          )\n        }\n        return Boolean(pinningState[position]?.length)\n      },\n\n      getLeftLeafColumns: memo(\n        () => [table.getAllLeafColumns(), table.getState().columnPinning.left],\n        (allColumns, left) => {\n          return (left ?? [])\n            .map(columnId => allColumns.find(column => column.id === columnId)!)\n            .filter(Boolean)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getLeftLeafColumns',\n          debug: () => table.options.debugAll ?? table.options.debugColumns,\n        }\n      ),\n\n      getRightLeafColumns: memo(\n        () => [table.getAllLeafColumns(), table.getState().columnPinning.right],\n        (allColumns, right) => {\n          return (right ?? [])\n            .map(columnId => allColumns.find(column => column.id === columnId)!)\n            .filter(Boolean)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getRightLeafColumns',\n          debug: () => table.options.debugAll ?? table.options.debugColumns,\n        }\n      ),\n\n      getCenterLeafColumns: memo(\n        () => [\n          table.getAllLeafColumns(),\n          table.getState().columnPinning.left,\n          table.getState().columnPinning.right,\n        ],\n        (allColumns, left, right) => {\n          const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n          return allColumns.filter(d => !leftAndRight.includes(d.id))\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getCenterLeafColumns',\n          debug: () => table.options.debugAll ?? table.options.debugColumns,\n        }\n      ),\n    }\n  },\n}\n", "import { TableFeature } from '../core/table'\nimport { OnChangeFn, Table, Row, RowModel, Updater, RowData } from '../types'\nimport { makeStateUpdater, memo } from '../utils'\n\nexport type RowSelectionState = Record<string, boolean>\n\nexport interface RowSelectionTableState {\n  rowSelection: RowSelectionState\n}\n\nexport interface RowSelectionOptions<TData extends RowData> {\n  enableRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  enableMultiRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  enableSubRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  onRowSelectionChange?: OnChangeFn<RowSelectionState>\n  // enableGroupingRowSelection?:\n  //   | boolean\n  //   | ((\n  //       row: Row<TData>\n  //     ) => boolean)\n  // isAdditiveSelectEvent?: (e: unknown) => boolean\n  // isInclusiveSelectEvent?: (e: unknown) => boolean\n  // selectRowsFn?: (\n  //   table: Table<TData>,\n  //   rowModel: RowModel<TData>\n  // ) => RowModel<TData>\n}\n\nexport interface RowSelectionRow {\n  getIsSelected: () => boolean\n  getIsSomeSelected: () => boolean\n  getIsAllSubRowsSelected: () => boolean\n  getCanSelect: () => boolean\n  getCanMultiSelect: () => boolean\n  getCanSelectSubRows: () => boolean\n  toggleSelected: (value?: boolean) => void\n  getToggleSelectedHandler: () => (event: unknown) => void\n}\n\nexport interface RowSelectionInstance<TData extends RowData> {\n  getToggleAllRowsSelectedHandler: () => (event: unknown) => void\n  getToggleAllPageRowsSelectedHandler: () => (event: unknown) => void\n  setRowSelection: (updater: Updater<RowSelectionState>) => void\n  resetRowSelection: (defaultState?: boolean) => void\n  getIsAllRowsSelected: () => boolean\n  getIsAllPageRowsSelected: () => boolean\n  getIsSomeRowsSelected: () => boolean\n  getIsSomePageRowsSelected: () => boolean\n  toggleAllRowsSelected: (value?: boolean) => void\n  toggleAllPageRowsSelected: (value?: boolean) => void\n  getPreSelectedRowModel: () => RowModel<TData>\n  getSelectedRowModel: () => RowModel<TData>\n  getFilteredSelectedRowModel: () => RowModel<TData>\n  getGroupedSelectedRowModel: () => RowModel<TData>\n}\n\n//\n\nexport const RowSelection: TableFeature = {\n  getInitialState: (state): RowSelectionTableState => {\n    return {\n      rowSelection: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionOptions<TData> => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true,\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionInstance<TData> => {\n    return {\n      setRowSelection: updater => table.options.onRowSelectionChange?.(updater),\n      resetRowSelection: defaultState =>\n        table.setRowSelection(\n          defaultState ? {} : table.initialState.rowSelection ?? {}\n        ),\n      toggleAllRowsSelected: value => {\n        table.setRowSelection(old => {\n          value =\n            typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected()\n\n          const rowSelection = { ...old }\n\n          const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows\n\n          // We don't use `mutateRowIsSelected` here for performance reasons.\n          // All of the rows are flat already, so it wouldn't be worth it\n          if (value) {\n            preGroupedFlatRows.forEach(row => {\n              if (!row.getCanSelect()) {\n                return\n              }\n              rowSelection[row.id] = true\n            })\n          } else {\n            preGroupedFlatRows.forEach(row => {\n              delete rowSelection[row.id]\n            })\n          }\n\n          return rowSelection\n        })\n      },\n      toggleAllPageRowsSelected: value =>\n        table.setRowSelection(old => {\n          const resolvedValue =\n            typeof value !== 'undefined'\n              ? value\n              : !table.getIsAllPageRowsSelected()\n\n          const rowSelection: RowSelectionState = { ...old }\n\n          table.getRowModel().rows.forEach(row => {\n            mutateRowIsSelected(rowSelection, row.id, resolvedValue, table)\n          })\n\n          return rowSelection\n        }),\n\n      // addRowSelectionRange: rowId => {\n      //   const {\n      //     rows,\n      //     rowsById,\n      //     options: { selectGroupingRows, selectSubRows },\n      //   } = table\n\n      //   const findSelectedRow = (rows: Row[]) => {\n      //     let found\n      //     rows.find(d => {\n      //       if (d.getIsSelected()) {\n      //         found = d\n      //         return true\n      //       }\n      //       const subFound = findSelectedRow(d.subRows || [])\n      //       if (subFound) {\n      //         found = subFound\n      //         return true\n      //       }\n      //       return false\n      //     })\n      //     return found\n      //   }\n\n      //   const firstRow = findSelectedRow(rows) || rows[0]\n      //   const lastRow = rowsById[rowId]\n\n      //   let include = false\n      //   const selectedRowIds = {}\n\n      //   const addRow = (row: Row) => {\n      //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n      //       rowsById,\n      //       selectGroupingRows: selectGroupingRows!,\n      //       selectSubRows: selectSubRows!,\n      //     })\n      //   }\n\n      //   table.rows.forEach(row => {\n      //     const isFirstRow = row.id === firstRow.id\n      //     const isLastRow = row.id === lastRow.id\n\n      //     if (isFirstRow || isLastRow) {\n      //       if (!include) {\n      //         include = true\n      //       } else if (include) {\n      //         addRow(row)\n      //         include = false\n      //       }\n      //     }\n\n      //     if (include) {\n      //       addRow(row)\n      //     }\n      //   })\n\n      //   table.setRowSelection(selectedRowIds)\n      // },\n      getPreSelectedRowModel: () => table.getCoreRowModel(),\n      getSelectedRowModel: memo(\n        () => [table.getState().rowSelection, table.getCoreRowModel()],\n        (rowSelection, rowModel) => {\n          if (!Object.keys(rowSelection).length) {\n            return {\n              rows: [],\n              flatRows: [],\n              rowsById: {},\n            }\n          }\n\n          return selectRowsFn(table, rowModel)\n        },\n        {\n          key: process.env.NODE_ENV === 'development' && 'getSelectedRowModel',\n          debug: () => table.options.debugAll ?? table.options.debugTable,\n        }\n      ),\n\n      getFilteredSelectedRowModel: memo(\n        () => [table.getState().rowSelection, table.getFilteredRowModel()],\n        (rowSelection, rowModel) => {\n          if (!Object.keys(rowSelection).length) {\n            return {\n              rows: [],\n              flatRows: [],\n              rowsById: {},\n            }\n          }\n\n          return selectRowsFn(table, rowModel)\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' &&\n            'getFilteredSelectedRowModel',\n          debug: () => table.options.debugAll ?? table.options.debugTable,\n        }\n      ),\n\n      getGroupedSelectedRowModel: memo(\n        () => [table.getState().rowSelection, table.getSortedRowModel()],\n        (rowSelection, rowModel) => {\n          if (!Object.keys(rowSelection).length) {\n            return {\n              rows: [],\n              flatRows: [],\n              rowsById: {},\n            }\n          }\n\n          return selectRowsFn(table, rowModel)\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' &&\n            'getGroupedSelectedRowModel',\n          debug: () => table.options.debugAll ?? table.options.debugTable,\n        }\n      ),\n\n      ///\n\n      // getGroupingRowCanSelect: rowId => {\n      //   const row = table.getRow(rowId)\n\n      //   if (!row) {\n      //     throw new Error()\n      //   }\n\n      //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n      //     return table.options.enableGroupingRowSelection(row)\n      //   }\n\n      //   return table.options.enableGroupingRowSelection ?? false\n      // },\n\n      getIsAllRowsSelected: () => {\n        const preGroupedFlatRows = table.getFilteredRowModel().flatRows\n        const { rowSelection } = table.getState()\n\n        let isAllRowsSelected = Boolean(\n          preGroupedFlatRows.length && Object.keys(rowSelection).length\n        )\n\n        if (isAllRowsSelected) {\n          if (\n            preGroupedFlatRows.some(\n              row => row.getCanSelect() && !rowSelection[row.id]\n            )\n          ) {\n            isAllRowsSelected = false\n          }\n        }\n\n        return isAllRowsSelected\n      },\n\n      getIsAllPageRowsSelected: () => {\n        const paginationFlatRows = table\n          .getPaginationRowModel()\n          .flatRows.filter(row => row.getCanSelect())\n        const { rowSelection } = table.getState()\n\n        let isAllPageRowsSelected = !!paginationFlatRows.length\n\n        if (\n          isAllPageRowsSelected &&\n          paginationFlatRows.some(row => !rowSelection[row.id])\n        ) {\n          isAllPageRowsSelected = false\n        }\n\n        return isAllPageRowsSelected\n      },\n\n      getIsSomeRowsSelected: () => {\n        const totalSelected = Object.keys(\n          table.getState().rowSelection ?? {}\n        ).length\n        return (\n          totalSelected > 0 &&\n          totalSelected < table.getFilteredRowModel().flatRows.length\n        )\n      },\n\n      getIsSomePageRowsSelected: () => {\n        const paginationFlatRows = table.getPaginationRowModel().flatRows\n        return table.getIsAllPageRowsSelected()\n          ? false\n          : paginationFlatRows\n              .filter(row => row.getCanSelect())\n              .some(d => d.getIsSelected() || d.getIsSomeSelected())\n      },\n\n      getToggleAllRowsSelectedHandler: () => {\n        return (e: unknown) => {\n          table.toggleAllRowsSelected(\n            ((e as MouseEvent).target as HTMLInputElement).checked\n          )\n        }\n      },\n\n      getToggleAllPageRowsSelectedHandler: () => {\n        return (e: unknown) => {\n          table.toggleAllPageRowsSelected(\n            ((e as MouseEvent).target as HTMLInputElement).checked\n          )\n        }\n      },\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): RowSelectionRow => {\n    return {\n      toggleSelected: value => {\n        const isSelected = row.getIsSelected()\n\n        table.setRowSelection(old => {\n          value = typeof value !== 'undefined' ? value : !isSelected\n\n          if (isSelected === value) {\n            return old\n          }\n\n          const selectedRowIds = { ...old }\n\n          mutateRowIsSelected(selectedRowIds, row.id, value, table)\n\n          return selectedRowIds\n        })\n      },\n      getIsSelected: () => {\n        const { rowSelection } = table.getState()\n        return isRowSelected(row, rowSelection)\n      },\n\n      getIsSomeSelected: () => {\n        const { rowSelection } = table.getState()\n        return isSubRowSelected(row, rowSelection, table) === 'some'\n      },\n\n      getIsAllSubRowsSelected: () => {\n        const { rowSelection } = table.getState()\n        return isSubRowSelected(row, rowSelection, table) === 'all'\n      },\n\n      getCanSelect: () => {\n        if (typeof table.options.enableRowSelection === 'function') {\n          return table.options.enableRowSelection(row)\n        }\n\n        return table.options.enableRowSelection ?? true\n      },\n\n      getCanSelectSubRows: () => {\n        if (typeof table.options.enableSubRowSelection === 'function') {\n          return table.options.enableSubRowSelection(row)\n        }\n\n        return table.options.enableSubRowSelection ?? true\n      },\n\n      getCanMultiSelect: () => {\n        if (typeof table.options.enableMultiRowSelection === 'function') {\n          return table.options.enableMultiRowSelection(row)\n        }\n\n        return table.options.enableMultiRowSelection ?? true\n      },\n      getToggleSelectedHandler: () => {\n        const canSelect = row.getCanSelect()\n\n        return (e: unknown) => {\n          if (!canSelect) return\n          row.toggleSelected(\n            ((e as MouseEvent).target as HTMLInputElement)?.checked\n          )\n        }\n      },\n    }\n  },\n}\n\nconst mutateRowIsSelected = <TData extends RowData>(\n  selectedRowIds: Record<string, boolean>,\n  id: string,\n  value: boolean,\n  table: Table<TData>\n) => {\n  const row = table.getRow(id)\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key])\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true\n    }\n  } else {\n    delete selectedRowIds[id]\n  }\n  // }\n\n  if (row.subRows?.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row =>\n      mutateRowIsSelected(selectedRowIds, row.id, value, table)\n    )\n  }\n}\n\nexport function selectRowsFn<TData extends RowData>(\n  table: Table<TData>,\n  rowModel: RowModel<TData>\n): RowModel<TData> {\n  const rowSelection = table.getState().rowSelection\n\n  const newSelectedFlatRows: Row<TData>[] = []\n  const newSelectedRowsById: Record<string, Row<TData>> = {}\n\n  // Filters top level and nested rows\n  const recurseRows = (rows: Row<TData>[], depth = 0): Row<TData>[] => {\n    return rows\n      .map(row => {\n        const isSelected = isRowSelected(row, rowSelection)\n\n        if (isSelected) {\n          newSelectedFlatRows.push(row)\n          newSelectedRowsById[row.id] = row\n        }\n\n        if (row.subRows?.length) {\n          row = {\n            ...row,\n            subRows: recurseRows(row.subRows, depth + 1),\n          }\n        }\n\n        if (isSelected) {\n          return row\n        }\n      })\n      .filter(Boolean) as Row<TData>[]\n  }\n\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById,\n  }\n}\n\nexport function isRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>\n): boolean {\n  return selection[row.id] ?? false\n}\n\nexport function isSubRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>,\n  table: Table<TData>\n): boolean | 'some' | 'all' {\n  if (row.subRows && row.subRows.length) {\n    let allChildrenSelected = true\n    let someSelected = false\n\n    row.subRows.forEach(subRow => {\n      // Bail out early if we know both of these\n      if (someSelected && !allChildrenSelected) {\n        return\n      }\n\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true\n      } else {\n        allChildrenSelected = false\n      }\n    })\n\n    return allChildrenSelected ? 'all' : someSelected ? 'some' : false\n  }\n\n  return false\n}\n", "import { SortingFn } from './features/Sorting'\n\nexport const reSplitAlphaNumeric = /([0-9]+)/gm\n\nconst alphanumeric: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\nconst alphanumericCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\nconst datetime: SortingFn<any> = (rowA, rowB, columnId) => {\n  const a = rowA.getValue<Date>(columnId)\n  const b = rowB.getValue<Date>(columnId)\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nconst basic: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId))\n}\n\n// Utils\n\nfunction compareBasic(a: any, b: any) {\n  return a === b ? 0 : a > b ? 1 : -1\n}\n\nfunction toString(a: any) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return ''\n    }\n    return String(a)\n  }\n  if (typeof a === 'string') {\n    return a\n  }\n  return ''\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr: string, bStr: string) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean)\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean)\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift()!\n    const bb = b.shift()!\n\n    const an = parseInt(aa, 10)\n    const bn = parseInt(bb, 10)\n\n    const combo = [an, bn].sort()\n\n    // Both are string\n    if (isNaN(combo[0]!)) {\n      if (aa > bb) {\n        return 1\n      }\n      if (bb > aa) {\n        return -1\n      }\n      continue\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1]!)) {\n      return isNaN(an) ? -1 : 1\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1\n    }\n    if (bn > an) {\n      return -1\n    }\n  }\n\n  return a.length - b.length\n}\n\n// Exports\n\nexport const sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic,\n}\n\nexport type BuiltInSortingFn = keyof typeof sortingFns\n", "import { RowModel } from '..'\nimport { TableFeature } from '../core/table'\nimport {\n  BuiltInSortingFn,\n  reSplitAlphaNumeric,\n  sortingFns,\n} from '../sortingFns'\n\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  SortingFns,\n} from '../types'\n\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type SortDirection = 'asc' | 'desc'\n\nexport interface ColumnSort {\n  id: string\n  desc: boolean\n}\n\nexport type SortingState = ColumnSort[]\n\nexport interface SortingTableState {\n  sorting: SortingState\n}\n\nexport interface SortingFn<TData extends RowData> {\n  (rowA: Row<TData>, rowB: Row<TData>, columnId: string): number\n}\n\nexport type CustomSortingFns<TData extends RowData> = Record<\n  string,\n  SortingFn<TData>\n>\n\nexport type SortingFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof SortingFns\n  | BuiltInSortingFn\n  | SortingFn<TData>\n\nexport interface SortingColumnDef<TData extends RowData> {\n  sortingFn?: SortingFnOption<TData>\n  sortDescFirst?: boolean\n  enableSorting?: boolean\n  enableMultiSort?: boolean\n  invertSorting?: boolean\n  sortUndefined?: false | -1 | 1\n}\n\nexport interface SortingColumn<TData extends RowData> {\n  getAutoSortingFn: () => SortingFn<TData>\n  getAutoSortDir: () => SortDirection\n  getSortingFn: () => SortingFn<TData>\n  getFirstSortDir: () => SortDirection\n  getNextSortingOrder: () => SortDirection | false\n  getCanSort: () => boolean\n  getCanMultiSort: () => boolean\n  getSortIndex: () => number\n  getIsSorted: () => false | SortDirection\n  clearSorting: () => void\n  toggleSorting: (desc?: boolean, isMulti?: boolean) => void\n  getToggleSortingHandler: () => undefined | ((event: unknown) => void)\n}\n\ninterface SortingOptionsBase {\n  manualSorting?: boolean\n  onSortingChange?: OnChangeFn<SortingState>\n  enableSorting?: boolean\n  enableSortingRemoval?: boolean\n  enableMultiRemove?: boolean\n  enableMultiSort?: boolean\n  sortDescFirst?: boolean\n  getSortedRowModel?: (table: Table<any>) => () => RowModel<any>\n  maxMultiSortColCount?: number\n  isMultiSortEvent?: (e: unknown) => boolean\n}\n\ntype ResolvedSortingFns = keyof SortingFns extends never\n  ? {\n      sortingFns?: Record<string, SortingFn<any>>\n    }\n  : {\n      sortingFns: Record<keyof SortingFns, SortingFn<any>>\n    }\n\nexport interface SortingOptions<TData extends RowData>\n  extends SortingOptionsBase,\n    ResolvedSortingFns {}\n\nexport interface SortingInstance<TData extends RowData> {\n  setSorting: (updater: Updater<SortingState>) => void\n  resetSorting: (defaultState?: boolean) => void\n  getPreSortedRowModel: () => RowModel<TData>\n  getSortedRowModel: () => RowModel<TData>\n  _getSortedRowModel?: () => RowModel<TData>\n}\n\n//\n\nexport const Sorting: TableFeature = {\n  getInitialState: (state): SortingTableState => {\n    return {\n      sorting: [],\n      ...state,\n    }\n  },\n\n  getDefaultColumnDef: <TData extends RowData>(): SortingColumnDef<TData> => {\n    return {\n      sortingFn: 'auto',\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingOptions<TData> => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: (e: unknown) => {\n        return (e as MouseEvent).shiftKey\n      },\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): SortingColumn<TData> => {\n    return {\n      getAutoSortingFn: () => {\n        const firstRows = table.getFilteredRowModel().flatRows.slice(10)\n\n        let isString = false\n\n        for (const row of firstRows) {\n          const value = row?.getValue(column.id)\n\n          if (Object.prototype.toString.call(value) === '[object Date]') {\n            return sortingFns.datetime\n          }\n\n          if (typeof value === 'string') {\n            isString = true\n\n            if (value.split(reSplitAlphaNumeric).length > 1) {\n              return sortingFns.alphanumeric\n            }\n          }\n        }\n\n        if (isString) {\n          return sortingFns.text\n        }\n\n        return sortingFns.basic\n      },\n      getAutoSortDir: () => {\n        const firstRow = table.getFilteredRowModel().flatRows[0]\n\n        const value = firstRow?.getValue(column.id)\n\n        if (typeof value === 'string') {\n          return 'asc'\n        }\n\n        return 'desc'\n      },\n      getSortingFn: () => {\n        if (!column) {\n          throw new Error()\n        }\n\n        return isFunction(column.columnDef.sortingFn)\n          ? column.columnDef.sortingFn\n          : column.columnDef.sortingFn === 'auto'\n          ? column.getAutoSortingFn()\n          : table.options.sortingFns?.[column.columnDef.sortingFn as string] ??\n            sortingFns[column.columnDef.sortingFn as BuiltInSortingFn]\n      },\n      toggleSorting: (desc, multi) => {\n        // if (column.columns.length) {\n        //   column.columns.forEach((c, i) => {\n        //     if (c.id) {\n        //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n        //     }\n        //   })\n        //   return\n        // }\n\n        // this needs to be outside of table.setSorting to be in sync with rerender\n        const nextSortingOrder = column.getNextSortingOrder()\n        const hasManualValue = typeof desc !== 'undefined' && desc !== null\n\n        table.setSorting(old => {\n          // Find any existing sorting for this column\n          const existingSorting = old?.find(d => d.id === column.id)\n          const existingIndex = old?.findIndex(d => d.id === column.id)\n\n          let newSorting: SortingState = []\n\n          // What should we do with this sort action?\n          let sortAction: 'add' | 'remove' | 'toggle' | 'replace'\n          let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc'\n\n          // Multi-mode\n          if (old?.length && column.getCanMultiSort() && multi) {\n            if (existingSorting) {\n              sortAction = 'toggle'\n            } else {\n              sortAction = 'add'\n            }\n          } else {\n            // Normal mode\n            if (old?.length && existingIndex !== old.length - 1) {\n              sortAction = 'replace'\n            } else if (existingSorting) {\n              sortAction = 'toggle'\n            } else {\n              sortAction = 'replace'\n            }\n          }\n\n          // Handle toggle states that will remove the sorting\n          if (sortAction === 'toggle') {\n            // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n            if (!hasManualValue) {\n              // Is our intention to remove?\n              if (!nextSortingOrder) {\n                sortAction = 'remove'\n              }\n            }\n          }\n\n          if (sortAction === 'add') {\n            newSorting = [\n              ...old,\n              {\n                id: column.id,\n                desc: nextDesc,\n              },\n            ]\n            // Take latest n columns\n            newSorting.splice(\n              0,\n              newSorting.length -\n                (table.options.maxMultiSortColCount ?? Number.MAX_SAFE_INTEGER)\n            )\n          } else if (sortAction === 'toggle') {\n            // This flips (or sets) the\n            newSorting = old.map(d => {\n              if (d.id === column.id) {\n                return {\n                  ...d,\n                  desc: nextDesc,\n                }\n              }\n              return d\n            })\n          } else if (sortAction === 'remove') {\n            newSorting = old.filter(d => d.id !== column.id)\n          } else {\n            newSorting = [\n              {\n                id: column.id,\n                desc: nextDesc,\n              },\n            ]\n          }\n\n          return newSorting\n        })\n      },\n\n      getFirstSortDir: () => {\n        const sortDescFirst =\n          column.columnDef.sortDescFirst ??\n          table.options.sortDescFirst ??\n          column.getAutoSortDir() === 'desc'\n        return sortDescFirst ? 'desc' : 'asc'\n      },\n\n      getNextSortingOrder: (multi?: boolean) => {\n        const firstSortDirection = column.getFirstSortDir()\n        const isSorted = column.getIsSorted()\n\n        if (!isSorted) {\n          return firstSortDirection\n        }\n\n        if (\n          isSorted !== firstSortDirection &&\n          (table.options.enableSortingRemoval ?? true) && // If enableSortRemove, enable in general\n          (multi ? table.options.enableMultiRemove ?? true : true) // If multi, don't allow if enableMultiRemove))\n        ) {\n          return false\n        }\n        return isSorted === 'desc' ? 'asc' : 'desc'\n      },\n\n      getCanSort: () => {\n        return (\n          (column.columnDef.enableSorting ?? true) &&\n          (table.options.enableSorting ?? true) &&\n          !!column.accessorFn\n        )\n      },\n\n      getCanMultiSort: () => {\n        return (\n          column.columnDef.enableMultiSort ??\n          table.options.enableMultiSort ??\n          !!column.accessorFn\n        )\n      },\n\n      getIsSorted: () => {\n        const columnSort = table\n          .getState()\n          .sorting?.find(d => d.id === column.id)\n\n        return !columnSort ? false : columnSort.desc ? 'desc' : 'asc'\n      },\n\n      getSortIndex: () =>\n        table.getState().sorting?.findIndex(d => d.id === column.id) ?? -1,\n\n      clearSorting: () => {\n        //clear sorting for just 1 column\n        table.setSorting(old =>\n          old?.length ? old.filter(d => d.id !== column.id) : []\n        )\n      },\n\n      getToggleSortingHandler: () => {\n        const canSort = column.getCanSort()\n\n        return (e: unknown) => {\n          if (!canSort) return\n          ;(e as any).persist?.()\n          column.toggleSorting?.(\n            undefined,\n            column.getCanMultiSort()\n              ? table.options.isMultiSortEvent?.(e)\n              : false\n          )\n        }\n      },\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingInstance<TData> => {\n    return {\n      setSorting: updater => table.options.onSortingChange?.(updater),\n      resetSorting: defaultState => {\n        table.setSorting(defaultState ? [] : table.initialState?.sorting ?? [])\n      },\n      getPreSortedRowModel: () => table.getGroupedRowModel(),\n      getSortedRowModel: () => {\n        if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n          table._getSortedRowModel = table.options.getSortedRowModel(table)\n        }\n\n        if (table.options.manualSorting || !table._getSortedRowModel) {\n          return table.getPreSortedRowModel()\n        }\n\n        return table._getSortedRowModel()\n      },\n    }\n  },\n}\n", "import { TableFeature } from '../core/table'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  Row,\n  RowData,\n} from '../types'\nimport { makeStateUpdater, memo } from '../utils'\n\nexport type VisibilityState = Record<string, boolean>\n\nexport interface VisibilityTableState {\n  columnVisibility: VisibilityState\n}\n\nexport interface VisibilityOptions {\n  onColumnVisibilityChange?: OnChangeFn<VisibilityState>\n  enableHiding?: boolean\n}\n\nexport interface VisibilityDefaultOptions {\n  onColumnVisibilityChange: OnChangeFn<VisibilityState>\n}\n\nexport interface VisibilityInstance<TData extends RowData> {\n  getVisibleFlatColumns: () => Column<TData, unknown>[]\n  getVisibleLeafColumns: () => Column<TData, unknown>[]\n  getLeftVisibleLeafColumns: () => Column<TData, unknown>[]\n  getRightVisibleLeafColumns: () => Column<TData, unknown>[]\n  getCenterVisibleLeafColumns: () => Column<TData, unknown>[]\n  setColumnVisibility: (updater: Updater<VisibilityState>) => void\n  resetColumnVisibility: (defaultState?: boolean) => void\n  toggleAllColumnsVisible: (value?: boolean) => void\n  getIsAllColumnsVisible: () => boolean\n  getIsSomeColumnsVisible: () => boolean\n  getToggleAllColumnsVisibilityHandler: () => (event: unknown) => void\n}\n\nexport interface VisibilityColumnDef {\n  enableHiding?: boolean\n}\n\nexport interface VisibilityRow<TData extends RowData> {\n  _getAllVisibleCells: () => Cell<TData, unknown>[]\n  getVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface VisibilityColumn {\n  getCanHide: () => boolean\n  getIsVisible: () => boolean\n  toggleVisibility: (value?: boolean) => void\n  getToggleVisibilityHandler: () => (event: unknown) => void\n}\n\n//\n\nexport const Visibility: TableFeature = {\n  getInitialState: (state): VisibilityTableState => {\n    return {\n      columnVisibility: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityDefaultOptions => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): VisibilityColumn => {\n    return {\n      toggleVisibility: value => {\n        if (column.getCanHide()) {\n          table.setColumnVisibility(old => ({\n            ...old,\n            [column.id]: value ?? !column.getIsVisible(),\n          }))\n        }\n      },\n      getIsVisible: () => {\n        return table.getState().columnVisibility?.[column.id] ?? true\n      },\n\n      getCanHide: () => {\n        return (\n          (column.columnDef.enableHiding ?? true) &&\n          (table.options.enableHiding ?? true)\n        )\n      },\n      getToggleVisibilityHandler: () => {\n        return (e: unknown) => {\n          column.toggleVisibility?.(\n            ((e as MouseEvent).target as HTMLInputElement).checked\n          )\n        }\n      },\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): VisibilityRow<TData> => {\n    return {\n      _getAllVisibleCells: memo(\n        () => [row.getAllCells(), table.getState().columnVisibility],\n        cells => {\n          return cells.filter(cell => cell.column.getIsVisible())\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'production' && 'row._getAllVisibleCells',\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      ),\n      getVisibleCells: memo(\n        () => [\n          row.getLeftVisibleCells(),\n          row.getCenterVisibleCells(),\n          row.getRightVisibleCells(),\n        ],\n        (left, center, right) => [...left, ...center, ...right],\n        {\n          key: process.env.NODE_ENV === 'development' && 'row.getVisibleCells',\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      ),\n    }\n  },\n\n  createTable: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityInstance<TData> => {\n    const makeVisibleColumnsMethod = (\n      key: string,\n      getColumns: () => Column<TData, unknown>[]\n    ): (() => Column<TData, unknown>[]) => {\n      return memo(\n        () => [\n          getColumns(),\n          getColumns()\n            .filter(d => d.getIsVisible())\n            .map(d => d.id)\n            .join('_'),\n        ],\n        columns => {\n          return columns.filter(d => d.getIsVisible?.())\n        },\n        {\n          key,\n          debug: () => table.options.debugAll ?? table.options.debugColumns,\n        }\n      )\n    }\n\n    return {\n      getVisibleFlatColumns: makeVisibleColumnsMethod(\n        'getVisibleFlatColumns',\n        () => table.getAllFlatColumns()\n      ),\n      getVisibleLeafColumns: makeVisibleColumnsMethod(\n        'getVisibleLeafColumns',\n        () => table.getAllLeafColumns()\n      ),\n      getLeftVisibleLeafColumns: makeVisibleColumnsMethod(\n        'getLeftVisibleLeafColumns',\n        () => table.getLeftLeafColumns()\n      ),\n      getRightVisibleLeafColumns: makeVisibleColumnsMethod(\n        'getRightVisibleLeafColumns',\n        () => table.getRightLeafColumns()\n      ),\n      getCenterVisibleLeafColumns: makeVisibleColumnsMethod(\n        'getCenterVisibleLeafColumns',\n        () => table.getCenterLeafColumns()\n      ),\n\n      setColumnVisibility: updater =>\n        table.options.onColumnVisibilityChange?.(updater),\n\n      resetColumnVisibility: defaultState => {\n        table.setColumnVisibility(\n          defaultState ? {} : table.initialState.columnVisibility ?? {}\n        )\n      },\n\n      toggleAllColumnsVisible: value => {\n        value = value ?? !table.getIsAllColumnsVisible()\n\n        table.setColumnVisibility(\n          table.getAllLeafColumns().reduce(\n            (obj, column) => ({\n              ...obj,\n              [column.id]: !value ? !column.getCanHide?.() : value,\n            }),\n            {}\n          )\n        )\n      },\n\n      getIsAllColumnsVisible: () =>\n        !table.getAllLeafColumns().some(column => !column.getIsVisible?.()),\n\n      getIsSomeColumnsVisible: () =>\n        table.getAllLeafColumns().some(column => column.getIsVisible?.()),\n\n      getToggleAllColumnsVisibilityHandler: () => {\n        return (e: unknown) => {\n          table.toggleAllColumnsVisible(\n            ((e as MouseEvent).target as HTMLInputElement)?.checked\n          )\n        }\n      },\n    }\n  },\n}\n", "import { functionalUpdate, memo, RequiredKeys } from '../utils'\n\nimport {\n  Updater,\n  TableOptionsResolved,\n  TableState,\n  Table,\n  InitialTableState,\n  Row,\n  Column,\n  RowModel,\n  ColumnDef,\n  TableOptions,\n  RowData,\n  TableMeta,\n  ColumnDefResolved,\n  GroupColumnDef,\n} from '../types'\n\n//\nimport { createColumn } from './column'\nimport { Headers } from './headers'\n//\n\nimport { ColumnSizing } from '../features/ColumnSizing'\nimport { Expanding } from '../features/Expanding'\nimport { Filters } from '../features/Filters'\nimport { Grouping } from '../features/Grouping'\nimport { Ordering } from '../features/Ordering'\nimport { Pagination } from '../features/Pagination'\nimport { Pinning } from '../features/Pinning'\nimport { RowSelection } from '../features/RowSelection'\nimport { Sorting } from '../features/Sorting'\nimport { Visibility } from '../features/Visibility'\n\nexport interface TableFeature {\n  getDefaultOptions?: (table: any) => any\n  getInitialState?: (initialState?: InitialTableState) => any\n  createTable?: (table: any) => any\n  getDefaultColumnDef?: () => any\n  createColumn?: (column: any, table: any) => any\n  createHeader?: (column: any, table: any) => any\n  createCell?: (cell: any, column: any, row: any, table: any) => any\n  createRow?: (row: any, table: any) => any\n}\n\nconst features = [\n  Headers,\n  Visibility,\n  Ordering,\n  Pinning,\n  Filters,\n  Sorting,\n  Grouping,\n  Expanding,\n  Pagination,\n  RowSelection,\n  ColumnSizing,\n] as const\n\n//\n\nexport interface CoreTableState {}\n\nexport interface CoreOptions<TData extends RowData> {\n  data: TData[]\n  state: Partial<TableState>\n  onStateChange: (updater: Updater<TableState>) => void\n  debugAll?: boolean\n  debugTable?: boolean\n  debugHeaders?: boolean\n  debugColumns?: boolean\n  debugRows?: boolean\n  initialState?: InitialTableState\n  autoResetAll?: boolean\n  mergeOptions?: (\n    defaultOptions: TableOptions<TData>,\n    options: Partial<TableOptions<TData>>\n  ) => TableOptions<TData>\n  meta?: TableMeta<TData>\n  getCoreRowModel: (table: Table<any>) => () => RowModel<any>\n  getSubRows?: (originalRow: TData, index: number) => undefined | TData[]\n  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string\n  columns: ColumnDef<TData, any>[]\n  defaultColumn?: Partial<ColumnDef<TData, unknown>>\n  renderFallbackValue: any\n}\n\nexport interface CoreInstance<TData extends RowData> {\n  initialState: TableState\n  reset: () => void\n  options: RequiredKeys<TableOptionsResolved<TData>, 'state'>\n  setOptions: (newOptions: Updater<TableOptionsResolved<TData>>) => void\n  getState: () => TableState\n  setState: (updater: Updater<TableState>) => void\n  _features: readonly TableFeature[]\n  _queue: (cb: () => void) => void\n  _getRowId: (_: TData, index: number, parent?: Row<TData>) => string\n  getCoreRowModel: () => RowModel<TData>\n  _getCoreRowModel?: () => RowModel<TData>\n  getRowModel: () => RowModel<TData>\n  getRow: (id: string) => Row<TData>\n  _getDefaultColumnDef: () => Partial<ColumnDef<TData, unknown>>\n  _getColumnDefs: () => ColumnDef<TData, unknown>[]\n  _getAllFlatColumnsById: () => Record<string, Column<TData, unknown>>\n  getAllColumns: () => Column<TData, unknown>[]\n  getAllFlatColumns: () => Column<TData, unknown>[]\n  getAllLeafColumns: () => Column<TData, unknown>[]\n  getColumn: (columnId: string) => Column<TData, unknown> | undefined\n}\n\nexport function createTable<TData extends RowData>(\n  options: TableOptionsResolved<TData>\n): Table<TData> {\n  if (options.debugAll || options.debugTable) {\n    console.info('Creating Table Instance...')\n  }\n\n  let table = { _features: features } as unknown as Table<TData>\n\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions?.(table))\n  }, {}) as TableOptionsResolved<TData>\n\n  const mergeOptions = (options: TableOptionsResolved<TData>) => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options)\n    }\n\n    return {\n      ...defaultOptions,\n      ...options,\n    }\n  }\n\n  const coreInitialState: CoreTableState = {}\n\n  let initialState = {\n    ...coreInitialState,\n    ...(options.initialState ?? {}),\n  } as TableState\n\n  table._features.forEach(feature => {\n    initialState = feature.getInitialState?.(initialState) ?? initialState\n  })\n\n  const queued: (() => void)[] = []\n  let queuedTimeout = false\n\n  const coreInstance: CoreInstance<TData> = {\n    _features: features,\n    options: {\n      ...defaultOptions,\n      ...options,\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb)\n\n      if (!queuedTimeout) {\n        queuedTimeout = true\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve()\n          .then(() => {\n            while (queued.length) {\n              queued.shift()!()\n            }\n            queuedTimeout = false\n          })\n          .catch(error =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState)\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options)\n      table.options = mergeOptions(newOptions) as RequiredKeys<\n        TableOptionsResolved<TData>,\n        'state'\n      >\n    },\n\n    getState: () => {\n      return table.options.state as TableState\n    },\n\n    setState: (updater: Updater<TableState>) => {\n      table.options.onStateChange?.(updater)\n    },\n\n    _getRowId: (row: TData, index: number, parent?: Row<TData>) =>\n      table.options.getRowId?.(row, index, parent) ??\n      `${parent ? [parent.id, index].join('.') : index}`,\n\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table)\n      }\n\n      return table._getCoreRowModel!()\n    },\n\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel()\n    },\n    getRow: (id: string) => {\n      const row = table.getRowModel().rowsById[id]\n\n      if (!row) {\n        if (process.env.NODE_ENV !== 'production') {\n          throw new Error(`getRow expected an ID, but got ${id}`)\n        }\n        throw new Error()\n      }\n\n      return row\n    },\n    _getDefaultColumnDef: memo(\n      () => [table.options.defaultColumn],\n      defaultColumn => {\n        defaultColumn = (defaultColumn ?? {}) as Partial<\n          ColumnDef<TData, unknown>\n        >\n\n        return {\n          header: props => {\n            const resolvedColumnDef = props.header.column\n              .columnDef as ColumnDefResolved<TData>\n\n            if (resolvedColumnDef.accessorKey) {\n              return resolvedColumnDef.accessorKey\n            }\n\n            if (resolvedColumnDef.accessorFn) {\n              return resolvedColumnDef.id\n            }\n\n            return null\n          },\n          // footer: props => props.header.column.id,\n          cell: props => props.renderValue<any>()?.toString?.() ?? null,\n          ...table._features.reduce((obj, feature) => {\n            return Object.assign(obj, feature.getDefaultColumnDef?.())\n          }, {}),\n          ...defaultColumn,\n        } as Partial<ColumnDef<TData, unknown>>\n      },\n      {\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n        key: process.env.NODE_ENV === 'development' && 'getDefaultColumnDef',\n      }\n    ),\n\n    _getColumnDefs: () => table.options.columns,\n\n    getAllColumns: memo(\n      () => [table._getColumnDefs()],\n      columnDefs => {\n        const recurseColumns = (\n          columnDefs: ColumnDef<TData, unknown>[],\n          parent?: Column<TData, unknown>,\n          depth = 0\n        ): Column<TData, unknown>[] => {\n          return columnDefs.map(columnDef => {\n            const column = createColumn(table, columnDef, depth, parent)\n\n            const groupingColumnDef = columnDef as GroupColumnDef<\n              TData,\n              unknown\n            >\n\n            column.columns = groupingColumnDef.columns\n              ? recurseColumns(groupingColumnDef.columns, column, depth + 1)\n              : []\n\n            return column\n          })\n        }\n\n        return recurseColumns(columnDefs)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    getAllFlatColumns: memo(\n      () => [table.getAllColumns()],\n      allColumns => {\n        return allColumns.flatMap(column => {\n          return column.getFlatColumns()\n        })\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllFlatColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    _getAllFlatColumnsById: memo(\n      () => [table.getAllFlatColumns()],\n      flatColumns => {\n        return flatColumns.reduce((acc, column) => {\n          acc[column.id] = column\n          return acc\n        }, {} as Record<string, Column<TData, unknown>>)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllFlatColumnsById',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    getAllLeafColumns: memo(\n      () => [table.getAllColumns(), table._getOrderColumnsFn()],\n      (allColumns, orderColumns) => {\n        let leafColumns = allColumns.flatMap(column => column.getLeafColumns())\n        return orderColumns(leafColumns)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllLeafColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId]\n\n      if (process.env.NODE_ENV !== 'production' && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`)\n      }\n\n      return column\n    },\n  }\n\n  Object.assign(table, coreInstance)\n\n  table._features.forEach(feature => {\n    return Object.assign(table, feature.createTable?.(table))\n  })\n\n  return table\n}\n", "import { RowData, Cell, Column, Row, Table } from '../types'\nimport { Getter, memo } from '../utils'\n\nexport interface CellContext<TData extends RowData, TValue> {\n  table: Table<TData>\n  column: Column<TData, TValue>\n  row: Row<TData>\n  cell: Cell<TData, TValue>\n  getValue: Getter<TValue>\n  renderValue: Getter<TValue | null>\n}\n\nexport interface CoreCell<TData extends RowData, TValue> {\n  id: string\n  getValue: CellContext<TData, TValue>['getValue']\n  renderValue: CellContext<TData, TValue>['renderValue']\n  row: Row<TData>\n  column: Column<TData, TValue>\n  getContext: () => CellContext<TData, TValue>\n}\n\nexport function createCell<TData extends RowData, TValue>(\n  table: Table<TData>,\n  row: Row<TData>,\n  column: Column<TData, TValue>,\n  columnId: string\n): Cell<TData, TValue> {\n  const getRenderValue = () =>\n    cell.getValue() ?? table.options.renderFallbackValue\n\n  const cell: CoreCell<TData, TValue> = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(\n      () => [table, column, row, cell],\n      (table, column, row, cell) => ({\n        table,\n        column,\n        row,\n        cell: cell as Cell<TData, TValue>,\n        getValue: cell.getValue,\n        renderValue: cell.renderValue,\n      }),\n      {\n        key: process.env.NODE_ENV === 'development' && 'cell.getContext',\n        debug: () => table.options.debugAll,\n      }\n    ),\n  }\n\n  table._features.forEach(feature => {\n    Object.assign(\n      cell,\n      feature.createCell?.(\n        cell as Cell<TData, TValue>,\n        column,\n        row as Row<TData>,\n        table\n      )\n    )\n  }, {})\n\n  return cell as Cell<TData, TValue>\n}\n", "import { RowData, Cell, Row, Table } from '../types'\nimport { flattenBy, memo } from '../utils'\nimport { createCell } from './cell'\n\nexport interface CoreRow<TData extends RowData> {\n  id: string\n  index: number\n  original: TData\n  depth: number\n  parentId?: string\n  _valuesCache: Record<string, unknown>\n  _uniqueValuesCache: Record<string, unknown>\n  getValue: <TValue>(columnId: string) => TValue\n  getUniqueValues: <TValue>(columnId: string) => TValue[]\n  renderValue: <TValue>(columnId: string) => TValue\n  subRows: Row<TData>[]\n  getLeafRows: () => Row<TData>[]\n  originalSubRows?: TData[]\n  getAllCells: () => Cell<TData, unknown>[]\n  _getAllCellsByColumnId: () => Record<string, Cell<TData, unknown>>\n  getParentRow: () => Row<TData> | undefined\n  getParentRows: () => Row<TData>[]\n}\n\nexport const createRow = <TData extends RowData>(\n  table: Table<TData>,\n  id: string,\n  original: TData,\n  rowIndex: number,\n  depth: number,\n  subRows?: Row<TData>[],\n  parentId?: string\n): Row<TData> => {\n  let row: CoreRow<TData> = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      row._valuesCache[columnId] = column.accessorFn(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._valuesCache[columnId] as any\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)]\n        return row._uniqueValuesCache[columnId]\n      }\n\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._uniqueValuesCache[columnId] as any\n    },\n    renderValue: columnId =>\n      row.getValue(columnId) ?? table.options.renderFallbackValue,\n    subRows: subRows ?? [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () => (row.parentId ? table.getRow(row.parentId) : undefined),\n    getParentRows: () => {\n      let parentRows: Row<TData>[] = []\n      let currentRow = row\n      while (true) {\n        const parentRow = currentRow.getParentRow()\n        if (!parentRow) break\n        parentRows.push(parentRow)\n        currentRow = parentRow\n      }\n      return parentRows.reverse()\n    },\n    getAllCells: memo(\n      () => [table.getAllLeafColumns()],\n      leafColumns => {\n        return leafColumns.map(column => {\n          return createCell(table, row as Row<TData>, column, column.id)\n        })\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'row.getAllCells',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    ),\n\n    _getAllCellsByColumnId: memo(\n      () => [row.getAllCells()],\n      allCells => {\n        return allCells.reduce((acc, cell) => {\n          acc[cell.column.id] = cell\n          return acc\n        }, {} as Record<string, Cell<TData, unknown>>)\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'production' && 'row.getAllCellsByColumnId',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    ),\n  }\n\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i]\n    Object.assign(row, feature?.createRow?.(row, table))\n  }\n\n  return row as Row<TData>\n}\n", "import {\n  AccessorFn,\n  ColumnDef,\n  DisplayColumnDef,\n  GroupColumnDef,\n  IdentifiedColumnDef,\n  RowData,\n} from './types'\nimport { DeepKeys, DeepValue, RequiredKeys } from './utils'\n\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nexport type ColumnHelper<TData extends RowData> = {\n  accessor: <\n    TAccessor extends AccessorFn<TData> | DeepKeys<TData>,\n    TValue extends TAccessor extends AccessorFn<TData, infer TReturn>\n      ? TReturn\n      : TAccessor extends DeepKeys<TData>\n      ? DeepValue<TData, TAccessor>\n      : never\n  >(\n    accessor: TAccessor,\n    column: TAccessor extends AccessorFn<TData>\n      ? DisplayColumnDef<TData, TValue>\n      : IdentifiedColumnDef<TData, TValue>\n  ) => ColumnDef<TData, TValue>\n  display: (column: DisplayColumnDef<TData>) => ColumnDef<TData, unknown>\n  group: (column: GroupColumnDef<TData>) => ColumnDef<TData, unknown>\n}\n\nexport function createColumnHelper<\n  TData extends RowData\n>(): ColumnHelper<TData> {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function'\n        ? ({\n            ...column,\n            accessorFn: accessor,\n          } as any)\n        : {\n            ...column,\n            accessorKey: accessor,\n          }\n    },\n    display: column => column as ColumnDef<TData, unknown>,\n    group: column => column as ColumnDef<TData, unknown>,\n  }\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { memo } from '../utils'\n\nexport function getCoreRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.options.data],\n      (\n        data\n      ): {\n        rows: Row<TData>[]\n        flatRows: Row<TData>[]\n        rowsById: Record<string, Row<TData>>\n      } => {\n        const rowModel: RowModel<TData> = {\n          rows: [],\n          flatRows: [],\n          rowsById: {},\n        }\n\n        const accessRows = (\n          originalRows: TData[],\n          depth = 0,\n          parentRow?: Row<TData>\n        ): Row<TData>[] => {\n          const rows = [] as Row<TData>[]\n\n          for (let i = 0; i < originalRows.length; i++) {\n            // This could be an expensive check at scale, so we should move it somewhere else, but where?\n            // if (!id) {\n            //   if (process.env.NODE_ENV !== 'production') {\n            //     throw new Error(`getRowId expected an ID, but got ${id}`)\n            //   }\n            // }\n\n            // Make the row\n            const row = createRow(\n              table,\n              table._getRowId(originalRows[i]!, i, parentRow),\n              originalRows[i]!,\n              i,\n              depth,\n              undefined,\n              parentRow?.id\n            )\n\n            // Keep track of every row in a flat array\n            rowModel.flatRows.push(row)\n            // Also keep track of every row by its ID\n            rowModel.rowsById[row.id] = row\n            // Push table row into parent\n            rows.push(row)\n\n            // Get the original subrows\n            if (table.options.getSubRows) {\n              row.originalSubRows = table.options.getSubRows(\n                originalRows[i]!,\n                i\n              )\n\n              // Then recursively access them\n              if (row.originalSubRows?.length) {\n                row.subRows = accessRows(row.originalSubRows, depth + 1, row)\n              }\n            }\n          }\n\n          return rows\n        }\n\n        rowModel.rows = accessRows(data)\n\n        return rowModel\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {\n          table._autoResetPageIndex()\n        },\n      }\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowModel, Table, RowData } from '../types'\n\nexport function filterRows<TData extends RowData>(\n  rows: Row<TData>[],\n  filterRowImpl: (row: Row<TData>) => any,\n  table: Table<TData>\n) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table)\n  }\n\n  return filterRowModelFromRoot(rows, filterRowImpl, table)\n}\n\nexport function filterRowModelFromLeafs<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => Row<TData>[],\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    const rows: Row<TData>[] = []\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const newRow = createRow(\n        table,\n        row.id,\n        row.original,\n        row.index,\n        row.depth,\n        undefined,\n        row.parentId\n      )\n      newRow.columnFilters = row.columnFilters\n\n      if (row.subRows?.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n        row = newRow\n\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredRowsById[i] = row\n          continue\n        }\n\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredRowsById[i] = row\n          continue\n        }\n      } else {\n        row = newRow\n        if (filterRow(row)) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredRowsById[i] = row\n        }\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n\nexport function filterRowModelFromRoot<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => any,\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  // Filters top level and nested rows\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    // Filter from parents downward first\n\n    const rows: Row<TData>[] = []\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const pass = filterRow(row)\n\n      if (pass) {\n        if (row.subRows?.length && depth < maxDepth) {\n          const newRow = createRow(\n            table,\n            row.id,\n            row.original,\n            row.index,\n            row.depth,\n            undefined,\n            row.parentId\n          )\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n          row = newRow\n        }\n\n        rows.push(row)\n        newFilteredFlatRows.push(row)\n        newFilteredRowsById[row.id] = row\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n", "import { ResolvedColumnFilter } from '../features/Filters'\nimport { Table, RowModel, Row, RowData } from '../types'\nimport { memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFilteredRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n      ],\n      (rowModel, columnFilters, globalFilter) => {\n        if (\n          !rowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          for (let i = 0; i < rowModel.flatRows.length; i++) {\n            rowModel.flatRows[i]!.columnFilters = {}\n            rowModel.flatRows[i]!.columnFiltersMeta = {}\n          }\n          return rowModel\n        }\n\n        const resolvedColumnFilters: ResolvedColumnFilter<TData>[] = []\n        const resolvedGlobalFilters: ResolvedColumnFilter<TData>[] = []\n\n        ;(columnFilters ?? []).forEach(d => {\n          const column = table.getColumn(d.id)\n\n          if (!column) {\n            return\n          }\n\n          const filterFn = column.getFilterFn()\n\n          if (!filterFn) {\n            if (process.env.NODE_ENV !== 'production') {\n              console.warn(\n                `Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`\n              )\n            }\n            return\n          }\n\n          resolvedColumnFilters.push({\n            id: d.id,\n            filterFn,\n            resolvedValue: filterFn.resolveFilterValue?.(d.value) ?? d.value,\n          })\n        })\n\n        const filterableIds = columnFilters.map(d => d.id)\n\n        const globalFilterFn = table.getGlobalFilterFn()\n\n        const globallyFilterableColumns = table\n          .getAllLeafColumns()\n          .filter(column => column.getCanGlobalFilter())\n\n        if (\n          globalFilter &&\n          globalFilterFn &&\n          globallyFilterableColumns.length\n        ) {\n          filterableIds.push('__global__')\n\n          globallyFilterableColumns.forEach(column => {\n            resolvedGlobalFilters.push({\n              id: column.id,\n              filterFn: globalFilterFn,\n              resolvedValue:\n                globalFilterFn.resolveFilterValue?.(globalFilter) ??\n                globalFilter,\n            })\n          })\n        }\n\n        let currentColumnFilter\n        let currentGlobalFilter\n\n        // Flag the prefiltered row model with each filter state\n        for (let j = 0; j < rowModel.flatRows.length; j++) {\n          const row = rowModel.flatRows[j]!\n\n          row.columnFilters = {}\n\n          if (resolvedColumnFilters.length) {\n            for (let i = 0; i < resolvedColumnFilters.length; i++) {\n              currentColumnFilter = resolvedColumnFilters[i]!\n              const id = currentColumnFilter.id\n\n              // Tag the row with the column filter state\n              row.columnFilters[id] = currentColumnFilter.filterFn(\n                row,\n                id,\n                currentColumnFilter.resolvedValue,\n                filterMeta => {\n                  row.columnFiltersMeta[id] = filterMeta\n                }\n              )\n            }\n          }\n\n          if (resolvedGlobalFilters.length) {\n            for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n              currentGlobalFilter = resolvedGlobalFilters[i]!\n              const id = currentGlobalFilter.id\n              // Tag the row with the first truthy global filter state\n              if (\n                currentGlobalFilter.filterFn(\n                  row,\n                  id,\n                  currentGlobalFilter.resolvedValue,\n                  filterMeta => {\n                    row.columnFiltersMeta[id] = filterMeta\n                  }\n                )\n              ) {\n                row.columnFilters.__global__ = true\n                break\n              }\n            }\n\n            if (row.columnFilters.__global__ !== true) {\n              row.columnFilters.__global__ = false\n            }\n          }\n        }\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        // Filter final rows using all of the active filters\n        return filterRows(rowModel.rows, filterRowsImpl, table)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getFilteredRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {\n          table._autoResetPageIndex()\n        },\n      }\n    )\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFacetedRowModel<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => RowModel<TData> {\n  return (table, columnId) =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n        table.getFilteredRowModel(),\n      ],\n      (preRowModel, columnFilters, globalFilter) => {\n        if (\n          !preRowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          return preRowModel\n        }\n\n        const filterableIds = [\n          ...columnFilters.map(d => d.id).filter(d => d !== columnId),\n          globalFilter ? '__global__' : undefined,\n        ].filter(Boolean) as string[]\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        return filterRows(preRowModel.rows, filterRowsImpl, table)\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'development' &&\n          'getFacetedRowModel_' + columnId,\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {},\n      }\n    )\n}\n", "import { Table, RowData } from '../types'\nimport { memo } from '../utils'\n\nexport function getFacetedUniqueValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => Map<any, number> {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return new Map()\n\n        let facetedUniqueValues = new Map<any, number>()\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (facetedUniqueValues.has(value)) {\n              facetedUniqueValues.set(\n                value,\n                (facetedUniqueValues.get(value) ?? 0) + 1\n              )\n            } else {\n              facetedUniqueValues.set(value, 1)\n            }\n          }\n        }\n\n        return facetedUniqueValues\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'development' &&\n          'getFacetedUniqueValues_' + columnId,\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {},\n      }\n    )\n}\n", "import { Table, RowData } from '../types'\nimport { memo } from '../utils'\n\nexport function getFacetedMinMaxValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => undefined | [number, number] {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return undefined\n\n        const firstValue =\n          facetedRowModel.flatRows[0]?.getUniqueValues(columnId)\n\n        if (typeof firstValue === 'undefined') {\n          return undefined\n        }\n\n        let facetedMinMaxValues: [any, any] = [firstValue, firstValue]\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (value < facetedMinMaxValues[0]) {\n              facetedMinMaxValues[0] = value\n            } else if (value > facetedMinMaxValues[1]) {\n              facetedMinMaxValues[1] = value\n            }\n          }\n        }\n\n        return facetedMinMaxValues\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'development' &&\n          'getFacetedMinMaxValues_' + columnId,\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {},\n      }\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { SortingFn } from '../features/Sorting'\nimport { memo } from '../utils'\n\nexport function getSortedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().sorting, table.getPreSortedRowModel()],\n      (sorting, rowModel) => {\n        if (!rowModel.rows.length || !sorting?.length) {\n          return rowModel\n        }\n\n        const sortingState = table.getState().sorting\n\n        const sortedFlatRows: Row<TData>[] = []\n\n        // Filter out sortings that correspond to non existing columns\n        const availableSorting = sortingState.filter(sort =>\n          table.getColumn(sort.id)?.getCanSort()\n        )\n\n        const columnInfoById: Record<\n          string,\n          {\n            sortUndefined?: false | -1 | 1\n            invertSorting?: boolean\n            sortingFn: SortingFn<TData>\n          }\n        > = {}\n\n        availableSorting.forEach(sortEntry => {\n          const column = table.getColumn(sortEntry.id)\n          if (!column) return\n\n          columnInfoById[sortEntry.id] = {\n            sortUndefined: column.columnDef.sortUndefined,\n            invertSorting: column.columnDef.invertSorting,\n            sortingFn: column.getSortingFn(),\n          }\n        })\n\n        const sortData = (rows: Row<TData>[]) => {\n          // This will also perform a stable sorting using the row index\n          // if needed.\n          const sortedData = [...rows]\n\n          sortedData.sort((rowA, rowB) => {\n            for (let i = 0; i < availableSorting.length; i += 1) {\n              const sortEntry = availableSorting[i]!\n              const columnInfo = columnInfoById[sortEntry.id]!\n              const isDesc = sortEntry?.desc ?? false\n\n              if (columnInfo.sortUndefined) {\n                const aValue = rowA.getValue(sortEntry.id)\n                const bValue = rowB.getValue(sortEntry.id)\n\n                const aUndefined = typeof aValue === 'undefined'\n                const bUndefined = typeof bValue === 'undefined'\n\n                if (aUndefined || bUndefined) {\n                  return aUndefined && bUndefined\n                    ? 0\n                    : aUndefined\n                    ? columnInfo.sortUndefined\n                    : -columnInfo.sortUndefined\n                }\n              }\n\n              // This function should always return in ascending order\n              let sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id)\n\n              if (sortInt !== 0) {\n                if (isDesc) {\n                  sortInt *= -1\n                }\n\n                if (columnInfo.invertSorting) {\n                  sortInt *= -1\n                }\n\n                return sortInt\n              }\n            }\n\n            return rowA.index - rowB.index\n          })\n\n          // If there are sub-rows, sort them\n          sortedData.forEach(row => {\n            sortedFlatRows.push(row)\n            if (row.subRows?.length) {\n              row.subRows = sortData(row.subRows)\n            }\n          })\n\n          return sortedData\n        }\n\n        return {\n          rows: sortData(rowModel.rows),\n          flatRows: sortedFlatRows,\n          rowsById: rowModel.rowsById,\n        }\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getSortedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {\n          table._autoResetPageIndex()\n        },\n      }\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { flattenBy, memo } from '../utils'\n\nexport function getGroupedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().grouping, table.getPreGroupedRowModel()],\n      (grouping, rowModel) => {\n        if (!rowModel.rows.length || !grouping.length) {\n          return rowModel\n        }\n\n        // Filter the grouping list down to columns that exist\n        const existingGrouping = grouping.filter(columnId =>\n          table.getColumn(columnId)\n        )\n\n        const groupedFlatRows: Row<TData>[] = []\n        const groupedRowsById: Record<string, Row<TData>> = {}\n        // const onlyGroupedFlatRows: Row[] = [];\n        // const onlyGroupedRowsById: Record<RowId, Row> = {};\n        // const nonGroupedFlatRows: Row[] = [];\n        // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n        // Recursively group the data\n        const groupUpRecursively = (\n          rows: Row<TData>[],\n          depth = 0,\n          parentId?: string\n        ) => {\n          // Grouping depth has been been met\n          // Stop grouping and simply rewrite thd depth and row relationships\n          if (depth >= existingGrouping.length) {\n            return rows.map(row => {\n              row.depth = depth\n\n              groupedFlatRows.push(row)\n              groupedRowsById[row.id] = row\n\n              if (row.subRows) {\n                row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id)\n              }\n\n              return row\n            })\n          }\n\n          const columnId: string = existingGrouping[depth]!\n\n          // Group the rows together for this level\n          const rowGroupsMap = groupBy(rows, columnId)\n\n          // Peform aggregations for each group\n          const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map(\n            ([groupingValue, groupedRows], index) => {\n              let id = `${columnId}:${groupingValue}`\n              id = parentId ? `${parentId}>${id}` : id\n\n              // First, Recurse to group sub rows before aggregation\n              const subRows = groupUpRecursively(groupedRows, depth + 1, id)\n\n              // Flatten the leaf rows of the rows in this group\n              const leafRows = depth\n                ? flattenBy(groupedRows, row => row.subRows)\n                : groupedRows\n\n              const row = createRow(\n                table,\n                id,\n                leafRows[0]!.original,\n                index,\n                depth,\n                undefined,\n                parentId\n              )\n\n              Object.assign(row, {\n                groupingColumnId: columnId,\n                groupingValue,\n                subRows,\n                leafRows,\n                getValue: (columnId: string) => {\n                  // Don't aggregate columns that are in the grouping\n                  if (existingGrouping.includes(columnId)) {\n                    if (row._valuesCache.hasOwnProperty(columnId)) {\n                      return row._valuesCache[columnId]\n                    }\n\n                    if (groupedRows[0]) {\n                      row._valuesCache[columnId] =\n                        groupedRows[0].getValue(columnId) ?? undefined\n                    }\n\n                    return row._valuesCache[columnId]\n                  }\n\n                  if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n                    return row._groupingValuesCache[columnId]\n                  }\n\n                  // Aggregate the values\n                  const column = table.getColumn(columnId)\n                  const aggregateFn = column?.getAggregationFn()\n\n                  if (aggregateFn) {\n                    row._groupingValuesCache[columnId] = aggregateFn(\n                      columnId,\n                      leafRows,\n                      groupedRows\n                    )\n\n                    return row._groupingValuesCache[columnId]\n                  }\n                },\n              })\n\n              subRows.forEach(subRow => {\n                groupedFlatRows.push(subRow)\n                groupedRowsById[subRow.id] = subRow\n                // if (subRow.getIsGrouped?.()) {\n                //   onlyGroupedFlatRows.push(subRow);\n                //   onlyGroupedRowsById[subRow.id] = subRow;\n                // } else {\n                //   nonGroupedFlatRows.push(subRow);\n                //   nonGroupedRowsById[subRow.id] = subRow;\n                // }\n              })\n\n              return row\n            }\n          )\n\n          return aggregatedGroupedRows\n        }\n\n        const groupedRows = groupUpRecursively(rowModel.rows, 0)\n\n        groupedRows.forEach(subRow => {\n          groupedFlatRows.push(subRow)\n          groupedRowsById[subRow.id] = subRow\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        })\n\n        return {\n          rows: groupedRows,\n          flatRows: groupedFlatRows,\n          rowsById: groupedRowsById,\n        }\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getGroupedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {\n          table._queue(() => {\n            table._autoResetExpanded()\n            table._autoResetPageIndex()\n          })\n        },\n      }\n    )\n}\n\nfunction groupBy<TData extends RowData>(rows: Row<TData>[], columnId: string) {\n  const groupMap = new Map<any, Row<TData>[]>()\n\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`\n    const previous = map.get(resKey)\n    if (!previous) {\n      map.set(resKey, [row])\n    } else {\n      previous.push(row)\n    }\n    return map\n  }, groupMap)\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { memo } from '../utils'\n\nexport function getExpandedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().expanded,\n        table.getPreExpandedRowModel(),\n        table.options.paginateExpandedRows,\n      ],\n      (expanded, rowModel, paginateExpandedRows) => {\n        if (\n          !rowModel.rows.length ||\n          (expanded !== true && !Object.keys(expanded ?? {}).length)\n        ) {\n          return rowModel\n        }\n\n        if (!paginateExpandedRows) {\n          // Only expand rows at this point if they are being paginated\n          return rowModel\n        }\n\n        return expandRows(rowModel)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getExpandedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n}\n\nexport function expandRows<TData extends RowData>(rowModel: RowModel<TData>) {\n  const expandedRows: Row<TData>[] = []\n\n  const handleRow = (row: Row<TData>) => {\n    expandedRows.push(row)\n\n    if (row.subRows?.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow)\n    }\n  }\n\n  rowModel.rows.forEach(handleRow)\n\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById,\n  }\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { memo } from '../utils'\nimport { expandRows } from './getExpandedRowModel'\n\nexport function getPaginationRowModel<TData extends RowData>(opts?: {\n  initialSync: boolean\n}): (table: Table<TData>) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().pagination,\n        table.getPrePaginationRowModel(),\n        table.options.paginateExpandedRows\n          ? undefined\n          : table.getState().expanded,\n      ],\n      (pagination, rowModel) => {\n        if (!rowModel.rows.length) {\n          return rowModel\n        }\n\n        const { pageSize, pageIndex } = pagination\n        let { rows, flatRows, rowsById } = rowModel\n        const pageStart = pageSize * pageIndex\n        const pageEnd = pageStart + pageSize\n\n        rows = rows.slice(pageStart, pageEnd)\n\n        let paginatedRowModel: RowModel<TData>\n\n        if (!table.options.paginateExpandedRows) {\n          paginatedRowModel = expandRows({\n            rows,\n            flatRows,\n            rowsById,\n          })\n        } else {\n          paginatedRowModel = {\n            rows,\n            flatRows,\n            rowsById,\n          }\n        }\n\n        paginatedRowModel.flatRows = []\n\n        const handleRow = (row: Row<TData>) => {\n          paginatedRowModel.flatRows.push(row)\n          if (row.subRows.length) {\n            row.subRows.forEach(handleRow)\n          }\n        }\n\n        paginatedRowModel.rows.forEach(handleRow)\n\n        return paginatedRowModel\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getPaginationRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n}\n", "import * as React from 'react'\nexport * from '@tanstack/table-core'\n\nimport {\n  TableOptions,\n  TableOptionsResolved,\n  RowData,\n  createTable,\n} from '@tanstack/table-core'\n\nexport type Renderable<TProps> = React.ReactNode | React.ComponentType<TProps>\n\n//\n\nexport function flexRender<TProps extends object>(\n  Comp: Renderable<TProps>,\n  props: TProps\n): React.ReactNode | JSX.Element {\n  return !Comp ? null : isReactComponent<TProps>(Comp) ? (\n    <Comp {...props} />\n  ) : (\n    Comp\n  )\n}\n\nfunction isReactComponent<TProps>(\n  component: unknown\n): component is React.ComponentType<TProps> {\n  return (\n    isClassComponent(component) ||\n    typeof component === 'function' ||\n    isExoticComponent(component)\n  )\n}\n\nfunction isClassComponent(component: any) {\n  return (\n    typeof component === 'function' &&\n    (() => {\n      const proto = Object.getPrototypeOf(component)\n      return proto.prototype && proto.prototype.isReactComponent\n    })()\n  )\n}\n\nfunction isExoticComponent(component: any) {\n  return (\n    typeof component === 'object' &&\n    typeof component.$$typeof === 'symbol' &&\n    ['react.memo', 'react.forward_ref'].includes(component.$$typeof.description)\n  )\n}\n\nexport function useReactTable<TData extends RowData>(\n  options: TableOptions<TData>\n) {\n  // Compose in the generic options to the user options\n  const resolvedOptions: TableOptionsResolved<TData> = {\n    state: {}, // Dummy state\n    onStateChange: () => {}, // noop\n    renderFallbackValue: null,\n    ...options,\n  }\n\n  // Create a new table and store it in state\n  const [tableRef] = React.useState(() => ({\n    current: createTable<TData>(resolvedOptions),\n  }))\n\n  // By default, manage table state here using the table's initial state\n  const [state, setState] = React.useState(() => tableRef.current.initialState)\n\n  // Compose the default state above with any user state. This will allow the user\n  // to only control a subset of the state if desired.\n  tableRef.current.setOptions(prev => ({\n    ...prev,\n    ...options,\n    state: {\n      ...state,\n      ...options.state,\n    },\n    // Similarly, we'll maintain both our internal state and any user-provided\n    // state.\n    onStateChange: updater => {\n      setState(updater)\n      options.onStateChange?.(updater)\n    },\n  }))\n\n  return tableRef.current\n}\n"], "mappings": ";;;;;;;;;;;AA2EO,SAASA,iBAAoBC,SAAqBC,OAAa;AACpE,SAAO,OAAOD,YAAY,aACrBA,QAA4BC,KAAK,IAClCD;AACN;AAEO,SAASE,OAAO;AACrB;AAGK,SAASC,iBACdC,KACAC,UACA;AACA,SAAQL,aAAoC;AACxCK,aAAiBC,SAAuBC,SAAqB;AAC7D,aAAO;QACL,GAAGA;QACH,CAACH,GAAG,GAAGL,iBAAiBC,SAAUO,IAAYH,GAAG,CAAC;;IAEtD,CAAC;;AAEL;AAIO,SAASI,WAAkCC,GAAgB;AAChE,SAAOA,aAAaC;AACtB;AAEO,SAASC,cAAcF,GAAuB;AACnD,SAAOG,MAAMC,QAAQJ,CAAC,KAAKA,EAAEK,MAAMC,SAAO,OAAOA,QAAQ,QAAQ;AACnE;AAEO,SAASC,UACdC,KACAC,aACA;AACA,QAAMC,OAAgB,CAAA;AAEtB,QAAMC,UAAWC,YAAoB;AACnCA,WAAOC,QAAQC,UAAQ;AACrBJ,WAAKK,KAAKD,IAAI;AACd,YAAME,WAAWP,YAAYK,IAAI;AACjC,UAAIE,YAAQ,QAARA,SAAUC,QAAQ;AACpBN,gBAAQK,QAAQ;MAClB;IACF,CAAC;;AAGHL,UAAQH,GAAG;AAEX,SAAOE;AACT;AAEO,SAASQ,KACdC,SACAC,IACAC,MAKe;AACf,MAAIC,OAAc,CAAA;AAClB,MAAIC;AAEJ,SAAO,MAAM;AACX,QAAIC;AACJ,QAAIH,KAAK1B,OAAO0B,KAAKI;AAAOD,gBAAUE,KAAKC,IAAG;AAE9C,UAAMC,UAAUT,QAAO;AAEvB,UAAMU,cACJD,QAAQX,WAAWK,KAAKL,UACxBW,QAAQE,KAAK,CAACC,KAAUC,UAAkBV,KAAKU,KAAK,MAAMD,GAAG;AAE/D,QAAI,CAACF,aAAa;AAChB,aAAON;IACT;AAEAD,WAAOM;AAEP,QAAIK;AACJ,QAAIZ,KAAK1B,OAAO0B,KAAKI;AAAOQ,mBAAaP,KAAKC,IAAG;AAEjDJ,aAASH,GAAG,GAAGQ,OAAO;AACtBP,YAAI,OAAA,SAAJA,KAAMa,YAAQ,OAAA,SAAdb,KAAMa,SAAWX,MAAM;AAEvB,QAAIF,KAAK1B,OAAO0B,KAAKI,OAAO;AAC1B,UAAIJ,QAAAA,QAAAA,KAAMI,MAAK,GAAI;AACjB,cAAMU,aAAaC,KAAKC,OAAOX,KAAKC,IAAG,IAAKH,WAAY,GAAG,IAAI;AAC/D,cAAMc,gBAAgBF,KAAKC,OAAOX,KAAKC,IAAG,IAAKM,cAAe,GAAG,IAAI;AACrE,cAAMM,sBAAsBD,gBAAgB;AAE5C,cAAME,MAAMA,CAACC,KAAsBC,QAAgB;AACjDD,gBAAME,OAAOF,GAAG;AAChB,iBAAOA,IAAIxB,SAASyB,KAAK;AACvBD,kBAAM,MAAMA;UACd;AACA,iBAAOA;;AAGTG,gBAAQC,KACL,OAAML,IAAIF,eAAe,CAAC,MAAME,IAAIL,YAAY,CAAC,QACjD;;;yBAGcC,KAAKU,IAChB,GACAV,KAAKW,IAAI,MAAM,MAAMR,qBAAqB,GAAG,CAC/C,mBACFlB,QAAAA,OAAAA,SAAAA,KAAM1B,GACR;MACF;IACF;AAEA,WAAO4B;;AAEX;AC7KO,SAASyB,aACdC,OACAC,WACAC,OACAC,QACuB;AAAA,MAAAC,MAAAC;AACvB,QAAMC,gBAAgBN,MAAMO,qBAAoB;AAEhD,QAAMC,oBAAoB;IACxB,GAAGF;IACH,GAAGL;;AAGL,QAAMQ,cAAcD,kBAAkBC;AAEtC,MAAIC,MAAEN,QAAAC,wBACJG,kBAAkBE,OAAE,OAAAL,wBACnBI,cAAcA,YAAYE,QAAQ,KAAK,GAAG,IAAIC,WAASR,OAAAA,OACvD,OAAOI,kBAAkBK,WAAW,WACjCL,kBAAkBK,SAClBD;AAEN,MAAIE;AAEJ,MAAIN,kBAAkBM,YAAY;AAChCA,iBAAaN,kBAAkBM;aACtBL,aAAa;AAEtB,QAAIA,YAAYM,SAAS,GAAG,GAAG;AAC7BD,mBAAcE,iBAAuB;AACnC,YAAI1C,SAAS0C;AAEb,mBAAWtE,OAAO+D,YAAYQ,MAAM,GAAG,GAAG;AAAA,cAAAC;AACxC5C,oBAAM4C,UAAG5C,WAAM,OAAA,SAAN4C,QAASxE,GAAG;AACrB,cAA6C4B,WAAWsC,QAAW;AACjEjB,oBAAQwB,KACL,IAAGzE,8BAA8B+D,kCACpC;UACF;QACF;AAEA,eAAOnC;;IAEX,OAAO;AACLwC,mBAAcE,iBACXA,YAAoBR,kBAAkBC,WAAW;IACtD;EACF;AAEA,MAAI,CAACC,IAAI;AACP,QAAIU,MAAuC;AACzC,YAAM,IAAIC,MACRb,kBAAkBM,aACb,mDACA,sDACP;IACF;AACA,UAAM,IAAIO,MAAK;EACjB;AAEA,MAAIC,SAAiC;IACnCZ,IAAK,GAAEhB,OAAOgB,EAAE;IAChBI;IACAX;IACAD;IACAD,WAAWO;IACXe,SAAS,CAAA;IACTC,gBAAgBvD,KACd,MAAM,CAAC,IAAI,GACX,MAAM;AAAA,UAAAwD;AACJ,aAAO,CACLH,QACA,IAAAG,kBAAGH,OAAOC,YAAPE,OAAAA,SAAAA,gBAAgBC,QAAQ3E,OAAKA,EAAEyE,eAAc,CAAE,CACnD;IACH,GACA;MACE9E,KAAK0E;MACL5C,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQE;MAAY;IACnE,CACF;IACAC,gBAAgB9D,KACd,MAAM,CAAC+B,MAAMgC,mBAAkB,CAAE,GACjCC,CAAAA,kBAAgB;AAAA,UAAAC;AACd,WAAAA,mBAAIZ,OAAOC,YAAPW,QAAAA,iBAAgBlE,QAAQ;AAC1B,YAAImE,cAAcb,OAAOC,QAAQG,QAAQJ,CAAAA,YACvCA,QAAOS,eAAc,CACvB;AAEA,eAAOE,cAAaE,WAAW;MACjC;AAEA,aAAO,CAACb,MAAM;IAChB,GACA;MACE5E,KAAK0E;MACL5C,OAAOA,MAAA;AAAA,YAAA4D;AAAA,gBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQE;MAAY;KAErE;;AAGFR,WAAStB,MAAMqC,UAAUC,OAAO,CAACC,KAAKC,YAAY;AAChD,WAAOC,OAAOC,OAAOH,KAAKC,QAAQzC,gBAARyC,OAAAA,SAAAA,QAAQzC,aAAeuB,QAAQtB,KAAK,CAAC;KAC9DsB,MAAM;AAGT,SAAOA;AACT;ACxEA,SAASqB,aACP3C,OACAsB,QACAM,SAOuB;AAAA,MAAAgB;AACvB,QAAMlC,MAAEkC,cAAGhB,QAAQlB,OAAE,OAAAkC,cAAItB,OAAOZ;AAEhC,MAAIG,SAAoC;IACtCH;IACAY;IACAvC,OAAO6C,QAAQ7C;IACf8D,eAAe,CAAC,CAACjB,QAAQiB;IACzBC,eAAelB,QAAQkB;IACvB5C,OAAO0B,QAAQ1B;IACf6C,YAAY,CAAA;IACZC,SAAS;IACTC,SAAS;IACTC,aAAa;IACbC,gBAAgBA,MAAgC;AAC9C,YAAMC,cAAwC,CAAA;AAE9C,YAAMC,gBAAiBC,OAA8B;AACnD,YAAIA,EAAEP,cAAcO,EAAEP,WAAW/E,QAAQ;AACvCsF,YAAEP,WAAWQ,IAAIF,aAAa;QAChC;AACAD,oBAAYtF,KAAKwF,CAA2B;;AAG9CD,oBAAcxC,MAAM;AAEpB,aAAOuC;;IAETI,YAAYA,OAAO;MACjBxD;MACAa;MACAS;;;AAIJtB,QAAMqC,UAAUzE,QAAQ4E,aAAW;AACjCC,WAAOC,OAAO7B,QAAQ2B,QAAQG,gBAARH,OAAAA,SAAAA,QAAQG,aAAe9B,QAAQb,KAAK,CAAC;EAC7D,CAAC;AAED,SAAOa;AACT;AAEO,IAAM4C,UAAwB;EACnCC,aACE1D,WAC2B;AAC3B,WAAO;;MAGL2D,iBAAiB1F,KACf,MAAM,CACJ+B,MAAM4D,cAAa,GACnB5D,MAAM6D,sBAAqB,GAC3B7D,MAAM8D,SAAQ,EAAGC,cAAcC,MAC/BhE,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAY/B,aAAa6B,MAAMC,UAAU;AAAA,YAAAE,kBAAAC;AACxC,cAAMC,eAAWF,mBACfH,QAAAA,OAAAA,SAAAA,KACIT,IAAIe,cAAYnC,YAAYoC,KAAKxH,OAAKA,EAAE2D,OAAO4D,QAAQ,CAAE,EAC1DE,OAAOC,OAAO,MAACN,OAAAA,mBAAI,CAAA;AAExB,cAAMO,gBAAYN,oBAChBH,SAAAA,OAAAA,SAAAA,MACIV,IAAIe,cAAYnC,YAAYoC,KAAKxH,OAAKA,EAAE2D,OAAO4D,QAAQ,CAAE,EAC1DE,OAAOC,OAAO,MAACL,OAAAA,oBAAI,CAAA;AAExB,cAAMO,gBAAgBxC,YAAYqC,OAChClD,YAAU,EAAC0C,QAAI,QAAJA,KAAMjD,SAASO,OAAOZ,EAAE,MAAK,EAACuD,SAAK,QAALA,MAAOlD,SAASO,OAAOZ,EAAE,EACpE;AAEA,cAAMkE,eAAeC,kBACnBX,YACA,CAAC,GAAGG,aAAa,GAAGM,eAAe,GAAGD,YAAY,GAClD1E,KACF;AAEA,eAAO4E;MACT,GACA;QACElI,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAmD;AAAA,kBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEAC,uBAAuB9G,KACrB,MAAM,CACJ+B,MAAM4D,cAAa,GACnB5D,MAAM6D,sBAAqB,GAC3B7D,MAAM8D,SAAQ,EAAGC,cAAcC,MAC/BhE,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAY/B,aAAa6B,MAAMC,UAAU;AACxC9B,sBAAcA,YAAYqC,OACxBlD,YAAU,EAAC0C,QAAI,QAAJA,KAAMjD,SAASO,OAAOZ,EAAE,MAAK,EAACuD,SAAK,QAALA,MAAOlD,SAASO,OAAOZ,EAAE,EACpE;AACA,eAAOmE,kBAAkBX,YAAY/B,aAAanC,OAAO,QAAQ;MACnE,GACA;QACEtD,KAC4C;QAC5C8B,OAAOA,MAAA;AAAA,cAAA4D;AAAA,kBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEAE,qBAAqB/G,KACnB,MAAM,CACJ+B,MAAM4D,cAAa,GACnB5D,MAAM6D,sBAAqB,GAC3B7D,MAAM8D,SAAQ,EAAGC,cAAcC,IAAI,GAErC,CAACE,YAAY/B,aAAa6B,SAAS;AAAA,YAAAiB;AACjC,cAAMC,sBAAkBD,oBACtBjB,QAAAA,OAAAA,SAAAA,KACIT,IAAIe,cAAYnC,YAAYoC,KAAKxH,OAAKA,EAAE2D,OAAO4D,QAAQ,CAAE,EAC1DE,OAAOC,OAAO,MAACQ,OAAAA,oBAAI,CAAA;AAExB,eAAOJ,kBACLX,YACAgB,oBACAlF,OACA,MACF;MACF,GACA;QACEtD,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAA2G;AAAA,kBAAAA,yBAAMnF,MAAM4B,QAAQC,aAAQsD,OAAAA,yBAAInF,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEAM,sBAAsBnH,KACpB,MAAM,CACJ+B,MAAM4D,cAAa,GACnB5D,MAAM6D,sBAAqB,GAC3B7D,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAY/B,aAAa8B,UAAU;AAAA,YAAAoB;AAClC,cAAMH,sBAAkBG,qBACtBpB,SAAAA,OAAAA,SAAAA,MACIV,IAAIe,cAAYnC,YAAYoC,KAAKxH,OAAKA,EAAE2D,OAAO4D,QAAQ,CAAE,EAC1DE,OAAOC,OAAO,MAACY,OAAAA,qBAAI,CAAA;AAExB,eAAOR,kBACLX,YACAgB,oBACAlF,OACA,OACF;MACF,GACA;QACEtD,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAA8G;AAAA,kBAAAA,yBAAMtF,MAAM4B,QAAQC,aAAQyD,OAAAA,yBAAItF,MAAM4B,QAAQkD;QAAY;MACnE,CACF;;MAIAS,iBAAiBtH,KACf,MAAM,CAAC+B,MAAM2D,gBAAe,CAAE,GAC9BiB,kBAAgB;AACd,eAAO,CAAC,GAAGA,YAAY,EAAEY,QAAO;MAClC,GACA;QACE9I,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAiH;AAAA,kBAAAA,yBAAMzF,MAAM4B,QAAQC,aAAQ4D,OAAAA,yBAAIzF,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEAY,qBAAqBzH,KACnB,MAAM,CAAC+B,MAAMgF,oBAAmB,CAAE,GAClCJ,kBAAgB;AACd,eAAO,CAAC,GAAGA,YAAY,EAAEY,QAAO;MAClC,GACA;QACE9I,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAmH;AAAA,kBAAAA,yBAAM3F,MAAM4B,QAAQC,aAAQ8D,OAAAA,yBAAI3F,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEAc,uBAAuB3H,KACrB,MAAM,CAAC+B,MAAM+E,sBAAqB,CAAE,GACpCH,kBAAgB;AACd,eAAO,CAAC,GAAGA,YAAY,EAAEY,QAAO;MAClC,GACA;QACE9I,KAC4C;QAC5C8B,OAAOA,MAAA;AAAA,cAAAqH;AAAA,kBAAAA,yBAAM7F,MAAM4B,QAAQC,aAAQgE,OAAAA,yBAAI7F,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEAgB,sBAAsB7H,KACpB,MAAM,CAAC+B,MAAMoF,qBAAoB,CAAE,GACnCR,kBAAgB;AACd,eAAO,CAAC,GAAGA,YAAY,EAAEY,QAAO;MAClC,GACA;QACE9I,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAuH;AAAA,kBAAAA,yBAAM/F,MAAM4B,QAAQC,aAAQkE,OAAAA,yBAAI/F,MAAM4B,QAAQkD;QAAY;MACnE,CACF;;MAIAkB,gBAAgB/H,KACd,MAAM,CAAC+B,MAAM2D,gBAAe,CAAE,GAC9BiB,kBAAgB;AACd,eAAOA,aACJrB,IAAIL,iBAAe;AAClB,iBAAOA,YAAY+C;QACrB,CAAC,EACAxI,KAAI;MACT,GACA;QACEf,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAA0H;AAAA,kBAAAA,yBAAMlG,MAAM4B,QAAQC,aAAQqE,OAAAA,yBAAIlG,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEAqB,oBAAoBlI,KAClB,MAAM,CAAC+B,MAAMgF,oBAAmB,CAAE,GAClChB,UAAQ;AACN,eAAOA,KACJT,IAAIL,iBAAe;AAClB,iBAAOA,YAAY+C;QACrB,CAAC,EACAxI,KAAI;MACT,GACA;QACEf,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAA4H;AAAA,kBAAAA,0BAAMpG,MAAM4B,QAAQC,aAAQuE,OAAAA,0BAAIpG,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEAuB,sBAAsBpI,KACpB,MAAM,CAAC+B,MAAM+E,sBAAqB,CAAE,GACpCf,UAAQ;AACN,eAAOA,KACJT,IAAIL,iBAAe;AAClB,iBAAOA,YAAY+C;QACrB,CAAC,EACAxI,KAAI;MACT,GACA;QACEf,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAA8H;AAAA,kBAAAA,0BAAMtG,MAAM4B,QAAQC,aAAQyE,OAAAA,0BAAItG,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEAyB,qBAAqBtI,KACnB,MAAM,CAAC+B,MAAMoF,qBAAoB,CAAE,GACnCpB,UAAQ;AACN,eAAOA,KACJT,IAAIL,iBAAe;AAClB,iBAAOA,YAAY+C;QACrB,CAAC,EACAxI,KAAI;MACT,GACA;QACEf,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAgI;AAAA,kBAAAA,0BAAMxG,MAAM4B,QAAQC,aAAQ2E,OAAAA,0BAAIxG,MAAM4B,QAAQkD;QAAY;MACnE,CACF;;MAIA2B,sBAAsBxI,KACpB,MAAM,CAAC+B,MAAMqG,qBAAoB,CAAE,GACnCK,iBAAe;AACb,eAAOA,YAAYlC,OAAO3D,YAAM;AAAA,cAAA8F;AAAA,iBAAI,GAAAA,qBAAC9F,OAAOkC,eAAU,QAAjB4D,mBAAmB3I;SAAO;MACjE,GACA;QACEtB,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAoI;AAAA,kBAAAA,0BAAM5G,MAAM4B,QAAQC,aAAQ+E,OAAAA,0BAAI5G,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEA+B,oBAAoB5I,KAClB,MAAM,CAAC+B,MAAMmG,mBAAkB,CAAE,GACjCO,iBAAe;AACb,eAAOA,YAAYlC,OAAO3D,YAAM;AAAA,cAAAiG;AAAA,iBAAI,GAAAA,sBAACjG,OAAOkC,eAAU,QAAjB+D,oBAAmB9I;SAAO;MACjE,GACA;QACEtB,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAuI;AAAA,kBAAAA,0BAAM/G,MAAM4B,QAAQC,aAAQkF,OAAAA,0BAAI/G,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEAkC,qBAAqB/I,KACnB,MAAM,CAAC+B,MAAMuG,oBAAmB,CAAE,GAClCG,iBAAe;AACb,eAAOA,YAAYlC,OAAO3D,YAAM;AAAA,cAAAoG;AAAA,iBAAI,GAAAA,sBAACpG,OAAOkC,eAAU,QAAjBkE,oBAAmBjJ;SAAO;MACjE,GACA;QACEtB,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAA0I;AAAA,kBAAAA,0BAAMlH,MAAM4B,QAAQC,aAAQqF,OAAAA,0BAAIlH,MAAM4B,QAAQkD;QAAY;MACnE,CACF;MAEA3B,gBAAgBlF,KACd,MAAM,CACJ+B,MAAMgF,oBAAmB,GACzBhF,MAAM+E,sBAAqB,GAC3B/E,MAAMoF,qBAAoB,CAAE,GAE9B,CAACpB,MAAMmD,QAAQlD,UAAU;AAAA,YAAAmD,iBAAAC,QAAAC,mBAAAC,UAAAC,kBAAAC;AACvB,eAAO,CACL,IAAAL,mBAAAC,SAAIrD,KAAK,CAAC,MAANqD,OAAAA,SAAAA,OAASpB,YAAOmB,OAAAA,kBAAI,CAAA,GACxB,IAAAE,qBAAAC,WAAIJ,OAAO,CAAC,MAARI,OAAAA,SAAAA,SAAWtB,YAAOqB,OAAAA,oBAAI,CAAA,GAC1B,IAAAE,oBAAAC,UAAIxD,MAAM,CAAC,MAAC,OAAA,SAARwD,QAAUxB,YAAO,OAAAuB,mBAAI,CAAA,CAAG,EAE3BjE,IAAI1C,YAAU;AACb,iBAAOA,OAAOsC,eAAc;QAC9B,CAAC,EACA1F,KAAI;MACT,GACA;QACEf,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAkJ;AAAA,kBAAAA,0BAAM1H,MAAM4B,QAAQC,aAAQ6F,OAAAA,0BAAI1H,MAAM4B,QAAQkD;QAAY;OAErE;;EAEJ;AACF;AAEO,SAASD,kBACdX,YACAyD,gBACA3H,OACA4H,cACA;AAAA,MAAAC,uBAAAC;AAOA,MAAIC,WAAW;AAEf,QAAMC,eAAe,SAACzG,SAAmCrB,OAAc;AAAA,QAAdA,UAAK,QAAA;AAALA,cAAQ;IAAC;AAChE6H,eAAW5I,KAAKU,IAAIkI,UAAU7H,KAAK;AAEnCqB,YACGiD,OAAOlD,YAAUA,OAAO2G,aAAY,CAAE,EACtCrK,QAAQ0D,YAAU;AAAA,UAAAG;AACjB,WAAAA,kBAAIH,OAAOC,YAAPE,QAAAA,gBAAgBzD,QAAQ;AAC1BgK,qBAAa1G,OAAOC,SAASrB,QAAQ,CAAC;MACxC;OACC,CAAC;;AAGR8H,eAAa9D,UAAU;AAEvB,MAAIU,eAAqC,CAAA;AAEzC,QAAMsD,oBAAoBA,CACxBC,gBACAjI,UACG;AAEH,UAAMgD,cAAkC;MACtChD;MACAQ,IAAI,CAACkH,cAAe,GAAE1H,OAAO,EAAEsE,OAAOC,OAAO,EAAE2D,KAAK,GAAG;MACvDnC,SAAS,CAAA;;AAIX,UAAMoC,uBAAiD,CAAA;AAGvDF,mBAAevK,QAAQ0K,mBAAiB;AAGtC,YAAMC,4BAA4B,CAAC,GAAGF,oBAAoB,EAAE7C,QAAO,EAAG,CAAC;AAEvE,YAAMgD,eAAeF,cAAchH,OAAOpB,UAAUgD,YAAYhD;AAEhE,UAAIoB;AACJ,UAAIuB,gBAAgB;AAEpB,UAAI2F,gBAAgBF,cAAchH,OAAOnB,QAAQ;AAE/CmB,iBAASgH,cAAchH,OAAOnB;MAChC,OAAO;AAELmB,iBAASgH,cAAchH;AACvBuB,wBAAgB;MAClB;AAEA,UACE0F,8BACAA,6BAAyB,OAAA,SAAzBA,0BAA2BjH,YAAWA,QACtC;AAEAiH,kCAA0BxF,WAAWjF,KAAKwK,aAAa;MACzD,OAAO;AAEL,cAAMzH,SAAS8B,aAAa3C,OAAOsB,QAAQ;UACzCZ,IAAI,CAACkH,cAAc1H,OAAOoB,OAAOZ,IAAI4H,iBAAa,OAAA,SAAbA,cAAe5H,EAAE,EACnD8D,OAAOC,OAAO,EACd2D,KAAK,GAAG;UACXvF;UACAC,eAAeD,gBACV,GAAEwF,qBAAqB7D,OAAOzH,OAAKA,EAAEuE,WAAWA,MAAM,EAAEtD,WACzD4C;UACJV;UACAnB,OAAOsJ,qBAAqBrK;QAC9B,CAAC;AAGD6C,eAAOkC,WAAWjF,KAAKwK,aAAa;AAGpCD,6BAAqBvK,KAAK+C,MAAM;MAClC;AAEAqC,kBAAY+C,QAAQnI,KAAKwK,aAAa;AACtCA,oBAAcpF,cAAcA;IAC9B,CAAC;AAED0B,iBAAa9G,KAAKoF,WAAW;AAE7B,QAAIhD,QAAQ,GAAG;AACbgI,wBAAkBG,sBAAsBnI,QAAQ,CAAC;IACnD;;AAGF,QAAMuI,gBAAgBd,eAAepE,IAAI,CAACjC,QAAQvC,UAChD4D,aAAa3C,OAAOsB,QAAQ;IAC1BpB,OAAO6H;IACPhJ;EACF,CAAC,CACH;AAEAmJ,oBAAkBO,eAAeV,WAAW,CAAC;AAE7CnD,eAAaY,QAAO;AAMpB,QAAMkD,yBACJzC,aAC2C;AAC3C,UAAM0C,kBAAkB1C,QAAQzB,OAAO3D,YACrCA,OAAOS,OAAO2G,aAAY,CAC5B;AAEA,WAAOU,gBAAgBpF,IAAI1C,YAAU;AACnC,UAAImC,UAAU;AACd,UAAIC,UAAU;AACd,UAAI2F,gBAAgB,CAAC,CAAC;AAEtB,UAAI/H,OAAOkC,cAAclC,OAAOkC,WAAW/E,QAAQ;AACjD4K,wBAAgB,CAAA;AAEhBF,+BAAuB7H,OAAOkC,UAAU,EAAEnF,QACxCwC,UAAsD;AAAA,cAArD;YAAE4C,SAAS6F;YAAc5F,SAAS6F;UAAa,IAAC1I;AAC/C4C,qBAAW6F;AACXD,wBAAc9K,KAAKgL,YAAY;QACjC,CACF;MACF,OAAO;AACL9F,kBAAU;MACZ;AAEA,YAAM+F,kBAAkB5J,KAAKW,IAAI,GAAG8I,aAAa;AACjD3F,gBAAUA,UAAU8F;AAEpBlI,aAAOmC,UAAUA;AACjBnC,aAAOoC,UAAUA;AAEjB,aAAO;QAAED;QAASC;;IACpB,CAAC;;AAGHyF,0BAAsBb,yBAAAC,iBAAClD,aAAa,CAAC,MAAC,OAAA,SAAfkD,eAAiB7B,YAAO,OAAA4B,wBAAI,CAAA,CAAE;AAErD,SAAOjD;AACT;ACzdO,IAAMoE,sBAAsB;EACjCC,MAAM;EACNC,SAAS;EACTC,SAASC,OAAOC;AAClB;AAEA,IAAMC,kCAAkCA,OAA8B;EACpEC,aAAa;EACbC,WAAW;EACXC,aAAa;EACbC,iBAAiB;EACjBC,kBAAkB;EAClBC,mBAAmB,CAAA;AACrB;AAEO,IAAMC,eAA6B;EACxCC,qBAAqBA,MAA6B;AAChD,WAAOd;;EAETe,iBAAkBC,WAAkC;AAClD,WAAO;MACLC,cAAc,CAAA;MACdC,kBAAkBZ,gCAA+B;MACjD,GAAGU;;;EAIPG,mBACEnK,WAC+B;AAC/B,WAAO;MACLoK,kBAAkB;MAClBC,sBAAsB5N,iBAAiB,gBAAgBuD,KAAK;MAC5DsK,0BAA0B7N,iBAAiB,oBAAoBuD,KAAK;;;EAIxED,cAAcA,CACZuB,QACAtB,UACuB;AACvB,WAAO;MACLuK,SAASA,MAAM;AAAA,YAAAC,uBAAApK,MAAAqK;AACb,cAAMC,aAAa1K,MAAM8D,SAAQ,EAAGmG,aAAa3I,OAAOZ,EAAE;AAE1D,eAAOvB,KAAKW,IACVX,KAAKU,KAAG2K,wBACNlJ,OAAOrB,UAAUiJ,YAAOsB,OAAAA,wBAAIxB,oBAAoBE,UAAO9I,OACvDsK,cAAAA,OAAAA,aAAcpJ,OAAOrB,UAAUgJ,SAAI,OAAA7I,OAAI4I,oBAAoBC,IAC7D,IAACwB,wBACDnJ,OAAOrB,UAAUkJ,YAAOsB,OAAAA,wBAAIzB,oBAAoBG,OAClD;;MAEFwB,UAAUC,cAAY;AACpB,cAAMrJ,UAAU,CAACqJ,WACb5K,MAAM6D,sBAAqB,IAC3B+G,aAAa,SACb5K,MAAM6K,0BAAyB,IAC/B7K,MAAM8K,2BAA0B;AAEpC,cAAM/L,QAAQwC,QAAQwJ,UAAUhO,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;AAEvD,YAAI3B,QAAQ,GAAG;AACb,gBAAMiM,oBAAoBzJ,QAAQxC,QAAQ,CAAC;AAE3C,iBACEiM,kBAAkBL,SAASC,QAAQ,IAAII,kBAAkBT,QAAO;QAEpE;AAEA,eAAO;;MAETU,WAAWA,MAAM;AACfjL,cAAMkL,gBAAgBC,WAAiC;AAAA,cAAhC;YAAE,CAAC7J,OAAOZ,EAAE,GAAG0K;YAAG,GAAGC;UAAK,IAACF;AAChD,iBAAOE;QACT,CAAC;;MAEHC,cAAcA,MAAM;AAAA,YAAAC,uBAAAC;AAClB,iBACED,wBAACjK,OAAOrB,UAAUwL,mBAAcF,OAAAA,wBAAI,WAAIC,wBACvCxL,MAAM4B,QAAQ8J,yBAAoBF,OAAAA,wBAAI;;MAG3CG,eAAeA,MAAM;AACnB,eAAO3L,MAAM8D,SAAQ,EAAGoG,iBAAiBP,qBAAqBrI,OAAOZ;MACvE;;;EAIJiC,cAAcA,CACZ9B,QACAb,UACuB;AACvB,WAAO;MACLuK,SAASA,MAAM;AACb,YAAIqB,OAAM;AAEV,cAAMlO,UAAWmD,CAAAA,YAAkC;AACjD,cAAIA,QAAOkC,WAAW/E,QAAQ;AAC5B6C,YAAAA,QAAOkC,WAAWnF,QAAQF,OAAO;UACnC,OAAO;AAAA,gBAAAmO;AACLD,YAAAA,SAAGC,wBAAIhL,QAAOS,OAAOiJ,QAAO,MAAEsB,OAAAA,wBAAI;UACpC;;AAGFnO,gBAAQmD,MAAM;AAEd,eAAO+K;;MAETjB,UAAUA,MAAM;AACd,YAAI9J,OAAO9B,QAAQ,GAAG;AACpB,gBAAM+M,oBACJjL,OAAOqC,YAAY+C,QAAQpF,OAAO9B,QAAQ,CAAC;AAC7C,iBAAO+M,kBAAkBnB,SAAQ,IAAKmB,kBAAkBvB,QAAO;QACjE;AAEA,eAAO;;MAETwB,kBAAkBA,MAAM;AACtB,cAAMzK,SAAStB,MAAMgM,UAAUnL,OAAOS,OAAOZ,EAAE;AAC/C,cAAMuL,YAAY3K,UAAM,OAAA,SAANA,OAAQgK,aAAY;AAEtC,eAAQY,OAAe;AACrB,cAAI,CAAC5K,UAAU,CAAC2K,WAAW;AACzB;UACF;AAEEC,YAAUC,WAAO,OAAA,SAAjBD,EAAUC,QAAO;AAEnB,cAAIC,kBAAkBF,CAAC,GAAG;AAExB,gBAAIA,EAAEG,WAAWH,EAAEG,QAAQrO,SAAS,GAAG;AACrC;YACF;UACF;AAEA,gBAAMwL,YAAY3I,OAAO0J,QAAO;AAEhC,gBAAMX,oBAAwC/I,SAC1CA,OACGsC,eAAc,EACdI,IAAIxG,OAAK,CAACA,EAAEuE,OAAOZ,IAAI3D,EAAEuE,OAAOiJ,QAAO,CAAE,CAAC,IAC7C,CAAC,CAACjJ,OAAOZ,IAAIY,OAAOiJ,QAAO,CAAE,CAAC;AAElC,gBAAM+B,UAAUF,kBAAkBF,CAAC,IAC/B/M,KAAKC,MAAM8M,EAAEG,QAAQ,CAAC,EAAGC,OAAO,IAC/BJ,EAAiBI;AAEtB,gBAAMC,kBAAqC,CAAA;AAE3C,gBAAMC,eAAeA,CACnBC,WACAC,eACG;AACH,gBAAI,OAAOA,eAAe,UAAU;AAClC;YACF;AAEA1M,kBAAM2M,oBAAoB9P,SAAO;AAAA,kBAAA+P,kBAAAC;AAC/B,oBAAMpD,cAAciD,eAAUE,mBAAI/P,OAAAA,OAAAA,SAAAA,IAAK0M,gBAAW,OAAAqD,mBAAI;AACtD,oBAAMlD,kBAAkBvK,KAAKU,IAC3B4J,gBAAWoD,iBAAIhQ,OAAG,OAAA,SAAHA,IAAK2M,cAAS,OAAAqD,iBAAI,IACjC,SACF;AAEAhQ,kBAAI+M,kBAAkBhM,QAAQkP,WAA4B;AAAA,oBAA3B,CAACxI,UAAUyI,UAAU,IAACD;AACnDP,gCAAgBjI,QAAQ,IACtBnF,KAAKC,MACHD,KAAKU,IAAIkN,aAAaA,aAAarD,iBAAiB,CAAC,IAAI,GAC3D,IAAI;cACR,CAAC;AAED,qBAAO;gBACL,GAAG7M;gBACH4M;gBACAC;;YAEJ,CAAC;AAED,gBACE1J,MAAM4B,QAAQwI,qBAAqB,cACnCqC,cAAc,OACd;AACAzM,oBAAMkL,gBAAgBrO,UAAQ;gBAC5B,GAAGA;gBACH,GAAG0P;cACL,EAAE;YACJ;;AAGF,gBAAMS,SAAUN,gBACdF,aAAa,QAAQE,UAAU;AAEjC,gBAAMO,QAASP,gBAAwB;AACrCF,yBAAa,OAAOE,UAAU;AAE9B1M,kBAAM2M,oBAAoB9P,UAAQ;cAChC,GAAGA;cACH8M,kBAAkB;cAClBJ,aAAa;cACbC,WAAW;cACXC,aAAa;cACbC,iBAAiB;cACjBE,mBAAmB,CAAA;YACrB,EAAE;;AAGJ,gBAAMsD,cAAc;YAClBC,aAAcjB,CAAAA,OAAkBc,OAAOd,GAAEI,OAAO;YAChDc,WAAYlB,CAAAA,OAAkB;AAC5BmB,uBAASC,oBAAoB,aAAaJ,YAAYC,WAAW;AACjEE,uBAASC,oBAAoB,WAAWJ,YAAYE,SAAS;AAC7DH,oBAAMf,GAAEI,OAAO;YACjB;;AAGF,gBAAMiB,cAAc;YAClBJ,aAAcjB,CAAAA,OAAkB;AAC9B,kBAAIA,GAAEsB,YAAY;AAChBtB,gBAAAA,GAAEuB,eAAc;AAChBvB,gBAAAA,GAAEwB,gBAAe;cACnB;AACAV,qBAAOd,GAAEG,QAAQ,CAAC,EAAGC,OAAO;AAC5B,qBAAO;;YAETc,WAAYlB,CAAAA,OAAkB;AAAA,kBAAAyB;AAC5BN,uBAASC,oBAAoB,aAAaC,YAAYJ,WAAW;AACjEE,uBAASC,oBAAoB,YAAYC,YAAYH,SAAS;AAC9D,kBAAIlB,GAAEsB,YAAY;AAChBtB,gBAAAA,GAAEuB,eAAc;AAChBvB,gBAAAA,GAAEwB,gBAAe;cACnB;AACAT,qBAAKU,cAACzB,GAAEG,QAAQ,CAAC,MAAC,OAAA,SAAZsB,YAAcrB,OAAO;YAC7B;;AAGF,gBAAMsB,qBAAqBC,sBAAqB,IAC5C;YAAEC,SAAS;UAAM,IACjB;AAEJ,cAAI1B,kBAAkBF,CAAC,GAAG;AACxBmB,qBAASU,iBACP,aACAR,YAAYJ,aACZS,kBACF;AACAP,qBAASU,iBACP,YACAR,YAAYH,WACZQ,kBACF;UACF,OAAO;AACLP,qBAASU,iBACP,aACAb,YAAYC,aACZS,kBACF;AACAP,qBAASU,iBACP,WACAb,YAAYE,WACZQ,kBACF;UACF;AAEA5N,gBAAM2M,oBAAoB9P,UAAQ;YAChC,GAAGA;YACH0M,aAAa+C;YACb9C;YACAC,aAAa;YACbC,iBAAiB;YACjBE;YACAD,kBAAkBrI,OAAOZ;UAC3B,EAAE;;MAEN;;;EAIJgD,aACE1D,WACyB;AACzB,WAAO;MACLkL,iBAAiB5O,aAAW0D,MAAM4B,QAAQyI,wBAAdrK,OAAAA,SAAAA,MAAM4B,QAAQyI,qBAAuB/N,OAAO;MACxEqQ,qBAAqBrQ,aACnB0D,MAAM4B,QAAQ0I,4BAAdtK,OAAAA,SAAAA,MAAM4B,QAAQ0I,yBAA2BhO,OAAO;MAClD0R,mBAAmBC,kBAAgB;AAAA,YAAAC;AACjClO,cAAMkL,gBACJ+C,eAAe,CAAA,KAAEC,wBAAGlO,MAAMmO,aAAalE,iBAAY,OAAAiE,wBAAI,CAAA,CACzD;;MAEFE,qBAAqBH,kBAAgB;AAAA,YAAAI;AACnCrO,cAAM2M,oBACJsB,eACI3E,gCAA+B,KAAE+E,yBACjCrO,MAAMmO,aAAajE,qBAAgB,OAAAmE,yBACjC/E,gCAA+B,CACvC;;MAEFgF,cAAcA,MAAA;AAAA,YAAAC,uBAAAC;AAAA,gBAAAD,yBAAAC,yBACZxO,MAAM2D,gBAAe,EAAG,CAAC,MAAzB6K,OAAAA,SAAAA,uBAA4BvI,QAAQ3D,OAAO,CAACsJ,MAAK/K,WAAW;AAC1D,iBAAO+K,OAAM/K,OAAO0J,QAAO;QAC7B,GAAG,CAAC,MAACgE,OAAAA,wBAAI;MAAC;MACZE,kBAAkBA,MAAA;AAAA,YAAAC,uBAAAC;AAAA,gBAAAD,yBAAAC,yBAChB3O,MAAMgF,oBAAmB,EAAG,CAAC,MAA7B2J,OAAAA,SAAAA,uBAAgC1I,QAAQ3D,OAAO,CAACsJ,MAAK/K,WAAW;AAC9D,iBAAO+K,OAAM/K,OAAO0J,QAAO;QAC7B,GAAG,CAAC,MAACmE,OAAAA,wBAAI;MAAC;MACZE,oBAAoBA,MAAA;AAAA,YAAAC,uBAAAC;AAAA,gBAAAD,yBAAAC,yBAClB9O,MAAM+E,sBAAqB,EAAG,CAAC,MAA/B+J,OAAAA,SAAAA,uBAAkC7I,QAAQ3D,OAAO,CAACsJ,MAAK/K,WAAW;AAChE,iBAAO+K,OAAM/K,OAAO0J,QAAO;QAC7B,GAAG,CAAC,MAACsE,OAAAA,wBAAI;MAAC;MACZE,mBAAmBA,MAAA;AAAA,YAAAC,uBAAAC;AAAA,gBAAAD,yBAAAC,yBACjBjP,MAAMoF,qBAAoB,EAAG,CAAC,MAA9B6J,OAAAA,SAAAA,uBAAiChJ,QAAQ3D,OAAO,CAACsJ,MAAK/K,WAAW;AAC/D,iBAAO+K,OAAM/K,OAAO0J,QAAO;QAC7B,GAAG,CAAC,MAACyE,OAAAA,wBAAI;MAAC;;EAEhB;AACF;AAEA,IAAIE,mBAAmC;AAChC,SAASrB,wBAAwB;AACtC,MAAI,OAAOqB,qBAAqB;AAAW,WAAOA;AAElD,MAAIC,YAAY;AAChB,MAAI;AACF,UAAMvN,UAAU;MACd,IAAIkM,UAAU;AACZqB,oBAAY;AACZ,eAAO;MACT;;AAGF,UAAM3S,QAAOA,MAAM;IAAA;AAEnB4S,WAAOrB,iBAAiB,QAAQvR,OAAMoF,OAAO;AAC7CwN,WAAO9B,oBAAoB,QAAQ9Q,KAAI;WAChC6S,KAAP;AACAF,gBAAY;EACd;AACAD,qBAAmBC;AACnB,SAAOD;AACT;AAEA,SAAS9C,kBAAkBF,GAA6B;AACtD,SAAQA,EAAiBoD,SAAS;AACpC;AClXO,IAAMC,YAA0B;EACrCxF,iBAAkBC,WAA8B;AAC9C,WAAO;MACLwF,UAAU,CAAA;MACV,GAAGxF;;;EAIPG,mBACEnK,WAC2B;AAC3B,WAAO;MACLyP,kBAAkBhT,iBAAiB,YAAYuD,KAAK;MACpD0P,sBAAsB;;;EAI1BhM,aACE1D,WAC4B;AAC5B,QAAI2P,aAAa;AACjB,QAAIC,SAAS;AAEb,WAAO;MACLC,oBAAoBA,MAAM;AAAA,YAAAzP,MAAA0P;AACxB,YAAI,CAACH,YAAY;AACf3P,gBAAM+P,OAAO,MAAM;AACjBJ,yBAAa;UACf,CAAC;AACD;QACF;AAEA,aAAAvP,QAAA0P,wBACE9P,MAAM4B,QAAQoO,iBAAYF,OAAAA,wBAC1B9P,MAAM4B,QAAQqO,sBAAiB,OAAA7P,OAC/B,CAACJ,MAAM4B,QAAQsO,iBACf;AACA,cAAIN;AAAQ;AACZA,mBAAS;AACT5P,gBAAM+P,OAAO,MAAM;AACjB/P,kBAAMmQ,cAAa;AACnBP,qBAAS;UACX,CAAC;QACH;;MAEFQ,aAAa9T,aAAW0D,MAAM4B,QAAQ6N,oBAAdzP,OAAAA,SAAAA,MAAM4B,QAAQ6N,iBAAmBnT,OAAO;MAChE+T,uBAAuBb,cAAY;AACjC,YAAIA,YAAAA,OAAAA,WAAY,CAACxP,MAAMsQ,qBAAoB,GAAI;AAC7CtQ,gBAAMoQ,YAAY,IAAI;QACxB,OAAO;AACLpQ,gBAAMoQ,YAAY,CAAA,CAAE;QACtB;;MAEFD,eAAelC,kBAAgB;AAAA,YAAAsC,uBAAAC;AAC7BxQ,cAAMoQ,YACJnC,eAAe,CAAA,KAAEsC,yBAAAC,sBAAGxQ,MAAMmO,iBAANqC,OAAAA,SAAAA,oBAAoBhB,aAAQ,OAAAe,wBAAI,CAAA,CACtD;;MAEFE,sBAAsBA,MAAM;AAC1B,eAAOzQ,MACJ0Q,yBAAwB,EACxBC,SAAS9R,KAAK+R,SAAOA,IAAIC,aAAY,CAAE;;MAE5CC,iCAAiCA,MAAM;AACrC,eAAQ5E,OAAe;AACnBA,YAAUC,WAAO,OAAA,SAAjBD,EAAUC,QAAO;AACnBnM,gBAAMqQ,sBAAqB;;;MAG/BU,uBAAuBA,MAAM;AAC3B,cAAMvB,WAAWxP,MAAM8D,SAAQ,EAAG0L;AAClC,eAAOA,aAAa,QAAQ/M,OAAOuO,OAAOxB,QAAQ,EAAE3Q,KAAK4F,OAAO;;MAElE6L,sBAAsBA,MAAM;AAC1B,cAAMd,WAAWxP,MAAM8D,SAAQ,EAAG0L;AAGlC,YAAI,OAAOA,aAAa,WAAW;AACjC,iBAAOA,aAAa;QACtB;AAEA,YAAI,CAAC/M,OAAOwO,KAAKzB,QAAQ,EAAExR,QAAQ;AACjC,iBAAO;QACT;AAGA,YAAIgC,MAAMkR,YAAW,EAAGP,SAAS9R,KAAK+R,SAAO,CAACA,IAAIO,cAAa,CAAE,GAAG;AAClE,iBAAO;QACT;AAGA,eAAO;;MAETC,kBAAkBA,MAAM;AACtB,YAAIrJ,WAAW;AAEf,cAAMsJ,SACJrR,MAAM8D,SAAQ,EAAG0L,aAAa,OAC1B/M,OAAOwO,KAAKjR,MAAMkR,YAAW,EAAGI,QAAQ,IACxC7O,OAAOwO,KAAKjR,MAAM8D,SAAQ,EAAG0L,QAAQ;AAE3C6B,eAAOzT,QAAQ8C,QAAM;AACnB,gBAAM6Q,UAAU7Q,GAAGO,MAAM,GAAG;AAC5B8G,qBAAW5I,KAAKU,IAAIkI,UAAUwJ,QAAQvT,MAAM;QAC9C,CAAC;AAED,eAAO+J;;MAETyJ,wBAAwBA,MAAMxR,MAAMyR,kBAAiB;MACrDC,qBAAqBA,MAAM;AACzB,YAAI,CAAC1R,MAAM2R,wBAAwB3R,MAAM4B,QAAQ8P,qBAAqB;AACpE1R,gBAAM2R,uBAAuB3R,MAAM4B,QAAQ8P,oBAAoB1R,KAAK;QACtE;AAEA,YAAIA,MAAM4B,QAAQsO,mBAAmB,CAAClQ,MAAM2R,sBAAsB;AAChE,iBAAO3R,MAAMwR,uBAAsB;QACrC;AAEA,eAAOxR,MAAM2R,qBAAoB;MACnC;;;EAIJC,WAAWA,CACThB,KACA5Q,UACgB;AAChB,WAAO;MACL6R,gBAAgBrC,cAAY;AAC1BxP,cAAMoQ,YAAYvT,SAAO;AAAA,cAAAiV;AACvB,gBAAMC,SAASlV,QAAQ,OAAO,OAAO,CAAC,EAACA,OAAAA,QAAAA,IAAM+T,IAAIlQ,EAAE;AAEnD,cAAIsR,cAAiC,CAAA;AAErC,cAAInV,QAAQ,MAAM;AAChB4F,mBAAOwO,KAAKjR,MAAMkR,YAAW,EAAGI,QAAQ,EAAE1T,QAAQqU,WAAS;AACzDD,0BAAYC,KAAK,IAAI;YACvB,CAAC;UACH,OAAO;AACLD,0BAAcnV;UAChB;AAEA2S,sBAAQsC,YAAGtC,aAAQ,OAAAsC,YAAI,CAACC;AAExB,cAAI,CAACA,UAAUvC,UAAU;AACvB,mBAAO;cACL,GAAGwC;cACH,CAACpB,IAAIlQ,EAAE,GAAG;;UAEd;AAEA,cAAIqR,UAAU,CAACvC,UAAU;AACvB,kBAAM;cAAE,CAACoB,IAAIlQ,EAAE,GAAG0K;cAAG,GAAGC;YAAK,IAAI2G;AACjC,mBAAO3G;UACT;AAEA,iBAAOxO;QACT,CAAC;;MAEHsU,eAAeA,MAAM;AAAA,YAAAe;AACnB,cAAM1C,WAAWxP,MAAM8D,SAAQ,EAAG0L;AAElC,eAAO,CAAC,GAAA0C,wBACNlS,MAAM4B,QAAQuQ,oBAAdnS,OAAAA,SAAAA,MAAM4B,QAAQuQ,iBAAmBvB,GAAG,MAACsB,OAAAA,wBACpC1C,aAAa,SAAQA,YAAQ,OAAA,SAARA,SAAWoB,IAAIlQ,EAAE;;MAG3CmQ,cAAcA,MAAM;AAAA,YAAAuB,uBAAA5G,uBAAA6G;AAClB,gBAAAD,wBACEpS,MAAM4B,QAAQ0Q,mBAAdtS,OAAAA,SAAAA,MAAM4B,QAAQ0Q,gBAAkB1B,GAAG,MAACwB,OAAAA,0BACnC5G,wBAACxL,MAAM4B,QAAQ2Q,oBAAe/G,OAAAA,wBAAI,SAAS,CAAC,GAAA6G,eAACzB,IAAI4B,YAAJH,QAAAA,aAAarU;;MAG/DyU,0BAA0BA,MAAM;AAC9B,cAAMC,YAAY9B,IAAIC,aAAY;AAElC,eAAO,MAAM;AACX,cAAI,CAAC6B;AAAW;AAChB9B,cAAIiB,eAAc;;MAEtB;;EAEJ;AACF;ACnOA,IAAMc,iBAAgCA,CACpC/B,KACAtM,UACAsO,gBACG;AAAA,MAAAC,eAAAC,uBAAAC;AACH,QAAMC,SAASJ,YAAYK,YAAW;AACtC,SAAOxO,SAAOoO,gBACZjC,IACGsC,SAAwB5O,QAAQ,MAAC,OAAA,UAAAwO,wBADpCD,cAEIM,SAAQ,MAAEJ,OAAAA,UAAAA,yBAFdD,sBAGIG,YAAW,MAAE,OAAA,SAHjBF,uBAIIhS,SAASiS,MAAM,CACrB;AACF;AAEAL,eAAeS,aAAc/V,SAAagW,WAAWhW,GAAG;AAExD,IAAMiW,0BAAyCA,CAC7C1C,KACAtM,UACAsO,gBACG;AAAA,MAAAW,gBAAAC;AACH,SAAO/O,SAAO8O,iBACZ3C,IAAIsC,SAAwB5O,QAAQ,MAACkP,OAAAA,UAAAA,wBAArCD,eAAuCJ,SAAQ,MAAE,OAAA,SAAjDK,sBAAmDzS,SAAS6R,WAAW,CACzE;AACF;AAEAU,wBAAwBF,aAAc/V,SAAagW,WAAWhW,GAAG;AAEjE,IAAMoW,eAA8BA,CAClC7C,KACAtM,UACAsO,gBACG;AAAA,MAAAc,gBAAAC;AACH,WACED,iBAAA9C,IAAIsC,SAAwB5O,QAAQ,MAAC,OAAA,UAAAqP,wBAArCD,eAAuCP,SAAQ,MAA/CQ,OAAAA,SAAAA,sBAAmDV,YAAW,QAC9DL,eAAAA,OAAAA,SAAAA,YAAaK,YAAW;AAE5B;AAEAQ,aAAaL,aAAc/V,SAAagW,WAAWhW,GAAG;AAEtD,IAAMuW,cAA6BA,CACjChD,KACAtM,UACAsO,gBACG;AAAA,MAAAiB;AACH,UAAAA,iBAAOjD,IAAIsC,SAAoB5O,QAAQ,MAAC,OAAA,SAAjCuP,eAAmC9S,SAAS6R,WAAW;AAChE;AAEAgB,YAAYR,aAAc/V,SAAagW,WAAWhW,GAAG,KAAK,EAACA,OAAAA,QAAAA,IAAKW;AAEhE,IAAM8V,iBAAgCA,CACpClD,KACAtM,UACAsO,gBACG;AACH,SAAO,CAACA,YAAY/T,KAClBxB,SAAG;AAAA,QAAA0W;AAAA,WAAI,GAAAA,iBAACnD,IAAIsC,SAAoB5O,QAAQ,MAAC,QAAjCyP,eAAmChT,SAAS1D,GAAG;EAAC,CAC1D;AACF;AAEAyW,eAAeV,aAAc/V,SAAagW,WAAWhW,GAAG,KAAK,EAACA,OAAAA,QAAAA,IAAKW;AAEnE,IAAMgW,kBAAiCA,CACrCpD,KACAtM,UACAsO,gBACG;AACH,SAAOA,YAAY/T,KAAKxB,SAAG;AAAA,QAAA4W;AAAA,YAAAA,iBACzBrD,IAAIsC,SAAoB5O,QAAQ,MAAC,OAAA,SAAjC2P,eAAmClT,SAAS1D,GAAG;EAAC,CAClD;AACF;AAEA2W,gBAAgBZ,aAAc/V,SAAagW,WAAWhW,GAAG,KAAK,EAACA,OAAAA,QAAAA,IAAKW;AAEpE,IAAMkW,SAAwBA,CAACtD,KAAKtM,UAAkBsO,gBAAyB;AAC7E,SAAOhC,IAAIsC,SAAS5O,QAAQ,MAAMsO;AACpC;AAEAsB,OAAOd,aAAc/V,SAAagW,WAAWhW,GAAG;AAEhD,IAAM8W,aAA4BA,CAChCvD,KACAtM,UACAsO,gBACG;AACH,SAAOhC,IAAIsC,SAAS5O,QAAQ,KAAKsO;AACnC;AAEAuB,WAAWf,aAAc/V,SAAagW,WAAWhW,GAAG;AAEpD,IAAM+W,gBAA+BA,CACnCxD,KACAtM,UACAsO,gBACG;AACH,MAAI,CAAC9S,MAAKD,IAAG,IAAI+S;AAEjB,QAAMyB,WAAWzD,IAAIsC,SAAiB5O,QAAQ;AAC9C,SAAO+P,YAAYvU,QAAOuU,YAAYxU;AACxC;AAEAuU,cAAcE,qBAAsBjX,SAAoB;AACtD,MAAI,CAACkX,WAAWC,SAAS,IAAInX;AAE7B,MAAIoX,YACF,OAAOF,cAAc,WAAWG,WAAWH,SAAmB,IAAIA;AACpE,MAAII,YACF,OAAOH,cAAc,WAAWE,WAAWF,SAAmB,IAAIA;AAEpE,MAAI1U,OACFyU,cAAc,QAAQnL,OAAOwL,MAAMH,SAAS,IAAI,YAAYA;AAC9D,MAAI5U,OAAM2U,cAAc,QAAQpL,OAAOwL,MAAMD,SAAS,IAAIE,WAAWF;AAErE,MAAI7U,OAAMD,MAAK;AACb,UAAMiV,OAAOhV;AACbA,IAAAA,OAAMD;AACNA,IAAAA,OAAMiV;EACR;AAEA,SAAO,CAAChV,MAAKD,IAAG;AAClB;AAEAuU,cAAchB,aAAc/V,SAC1BgW,WAAWhW,GAAG,KAAMgW,WAAWhW,IAAI,CAAC,CAAC,KAAKgW,WAAWhW,IAAI,CAAC,CAAC;AAItD,IAAM0X,YAAY;EACvBpC;EACAW;EACAG;EACAG;EACAE;EACAE;EACAE;EACAC;EACAC;AACF;AAMA,SAASf,WAAWhW,KAAU;AAC5B,SAAOA,QAAQuD,UAAavD,QAAQ,QAAQA,QAAQ;AACtD;ACcO,IAAM2X,UAAwB;EACnClL,qBAAqBA,MAAsD;AACzE,WAAO;MACLmL,UAAU;;;EAIdlL,iBAAkBC,WAA6B;AAC7C,WAAO;MACLkL,eAAe,CAAA;MACfC,cAAcvU;;;MAGd,GAAGoJ;;;EAIPG,mBACEnK,WAC0B;AAC1B,WAAO;MACLoV,uBAAuB3Y,iBAAiB,iBAAiBuD,KAAK;MAC9DqV,sBAAsB5Y,iBAAiB,gBAAgBuD,KAAK;MAC5DsV,oBAAoB;MACpBC,uBAAuB;MACvBC,gBAAgB;MAChBC,0BAA0BnU,YAAU;AAAA,YAAAoU,uBAAAC;AAClC,cAAMC,SAAKF,wBAAG1V,MACX6V,gBAAe,EACflF,SAAS,CAAC,MAACgF,OAAAA,UAAAA,yBAFAD,sBAEEI,uBAAsB,EACnCxU,OAAOZ,EAAE,MAHEiV,OAAAA,SAAAA,uBAGCzC,SAAQ;AAEvB,eAAO,OAAO0C,UAAU,YAAY,OAAOA,UAAU;MACvD;;;EAIJ7V,cAAcA,CACZuB,QACAtB,UACyB;AACzB,WAAO;MACL+V,iBAAiBA,MAAM;AACrB,cAAMC,WAAWhW,MAAM6V,gBAAe,EAAGlF,SAAS,CAAC;AAEnD,cAAMiF,QAAQI,YAAAA,OAAAA,SAAAA,SAAU9C,SAAS5R,OAAOZ,EAAE;AAE1C,YAAI,OAAOkV,UAAU,UAAU;AAC7B,iBAAOb,UAAUpC;QACnB;AAEA,YAAI,OAAOiD,UAAU,UAAU;AAC7B,iBAAOb,UAAUX;QACnB;AAEA,YAAI,OAAOwB,UAAU,WAAW;AAC9B,iBAAOb,UAAUb;QACnB;AAEA,YAAI0B,UAAU,QAAQ,OAAOA,UAAU,UAAU;AAC/C,iBAAOb,UAAUb;QACnB;AAEA,YAAIhX,MAAMC,QAAQyY,KAAK,GAAG;AACxB,iBAAOb,UAAUnB;QACnB;AAEA,eAAOmB,UAAUZ;;MAEnB8B,aAAaA,MAAM;AAAA,YAAAC,uBAAAC;AACjB,eAAOrZ,WAAWwE,OAAOrB,UAAUgV,QAAQ,IACvC3T,OAAOrB,UAAUgV,WACjB3T,OAAOrB,UAAUgV,aAAa,SAC9B3T,OAAOyU,gBAAe,KACxBG,yBAAAC,yBACEnW,MAAM4B,QAAQmT,cAAS,OAAA,SAAvBoB,uBAA0B7U,OAAOrB,UAAUgV,QAAQ,MAAWiB,OAAAA,wBAC9DnB,UAAUzT,OAAOrB,UAAUgV,QAAQ;;MAEzCmB,cAAcA,MAAM;AAAA,YAAA7K,uBAAAC,uBAAA6K;AAClB,iBACE9K,wBAACjK,OAAOrB,UAAUqW,uBAAkB,OAAA/K,wBAAI,WAAIC,wBAC3CxL,MAAM4B,QAAQ2U,wBAAmB,OAAA/K,wBAAI,WAAK6K,yBAC1CrW,MAAM4B,QAAQ4U,kBAAa,OAAAH,yBAAI,SAChC,CAAC,CAAC/U,OAAOR;;MAIb2V,oBAAoBA,MAAM;AAAA,YAAAC,wBAAAC,wBAAAC,wBAAAC;AACxB,iBACEH,yBAACpV,OAAOrB,UAAU6W,uBAAkBJ,OAAAA,yBAAI,WAAIC,yBAC3C3W,MAAM4B,QAAQkV,uBAAkB,OAAAH,yBAAI,WAAKC,yBACzC5W,MAAM4B,QAAQ4U,kBAAa,OAAAI,yBAAI,WAAKC,wBACpC7W,MAAM4B,QAAQ6T,4BAAwB,OAAA,SAAtCzV,MAAM4B,QAAQ6T,yBAA2BnU,MAAM,MAAC,OAAAuV,wBAAI,SACrD,CAAC,CAACvV,OAAOR;;MAIbiW,eAAeA,MAAMzV,OAAO0V,eAAc,IAAK;MAE/CC,gBAAgBA,MAAA;AAAA,YAAAC,uBAAAC;AAAA,gBAAAD,wBACdlX,MAAM8D,SAAQ,EAAGoR,kBAAa,OAAA,UAAAiC,yBAA9BD,sBAAgC3S,KAAKxH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE,MAA5DyW,OAAAA,SAAAA,uBAA+DvB;MAAK;MAEtEoB,gBAAgBA,MAAA;AAAA,YAAAI,wBAAAC;AAAA,gBAAAD,0BAAAC,yBACdrX,MAAM8D,SAAQ,EAAGoR,kBAAa,OAAA,SAA9BmC,uBAAgCtM,UAAUhO,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE,MAAC,OAAA0W,yBAClE;MAAE;MAEJE,gBAAgB1B,WAAS;AACvB5V,cAAMuX,iBAAiB1a,SAAO;AAC5B,gBAAMoY,WAAW3T,OAAO2U,YAAW;AACnC,gBAAMuB,iBAAiB3a,OAAAA,OAAAA,SAAAA,IAAK0H,KAAKxH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;AAExD,gBAAM+W,YAAYpb,iBAChBuZ,OACA4B,iBAAiBA,eAAe5B,QAAQhV,MAC1C;AAGA,cACE8W,uBACEzC,UACAwC,WACAnW,MACF,GACA;AAAA,gBAAAqW;AACA,oBAAAA,cAAO9a,OAAG,OAAA,SAAHA,IAAK2H,OAAOzH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE,MAAC,OAAAiX,cAAI,CAAA;UACjD;AAEA,gBAAMC,eAAe;YAAElX,IAAIY,OAAOZ;YAAIkV,OAAO6B;;AAE7C,cAAID,gBAAgB;AAAA,gBAAAK;AAClB,oBAAAA,WACEhb,OAAG,OAAA,SAAHA,IAAK0G,IAAIxG,OAAK;AACZ,kBAAIA,EAAE2D,OAAOY,OAAOZ,IAAI;AACtB,uBAAOkX;cACT;AACA,qBAAO7a;YACT,CAAC,MAAC,OAAA8a,WAAI,CAAA;UAEV;AAEA,cAAIhb,OAAG,QAAHA,IAAKmB,QAAQ;AACf,mBAAO,CAAC,GAAGnB,KAAK+a,YAAY;UAC9B;AAEA,iBAAO,CAACA,YAAY;QACtB,CAAC;;MAEHE,qBACE9X,MAAM4B,QAAQmW,sBACd/X,MAAM4B,QAAQmW,mBAAmB/X,OAAOsB,OAAOZ,EAAE;MACnDqX,oBAAoBA,MAAM;AACxB,YAAI,CAACzW,OAAOwW,qBAAqB;AAC/B,iBAAO9X,MAAMgY,uBAAsB;QACrC;AAEA,eAAO1W,OAAOwW,oBAAmB;;MAEnCG,yBACEjY,MAAM4B,QAAQsW,0BACdlY,MAAM4B,QAAQsW,uBAAuBlY,OAAOsB,OAAOZ,EAAE;MACvDwX,wBAAwBA,MAAM;AAC5B,YAAI,CAAC5W,OAAO2W,yBAAyB;AACnC,iBAAO,oBAAIE,IAAG;QAChB;AAEA,eAAO7W,OAAO2W,wBAAuB;;MAEvCG,yBACEpY,MAAM4B,QAAQyW,0BACdrY,MAAM4B,QAAQyW,uBAAuBrY,OAAOsB,OAAOZ,EAAE;MACvD2X,wBAAwBA,MAAM;AAC5B,YAAI,CAAC/W,OAAO8W,yBAAyB;AACnC,iBAAOxX;QACT;AAEA,eAAOU,OAAO8W,wBAAuB;MACvC;;;;;EAMJxG,WAAWA,CACThB,KACA5Q,UACsB;AACtB,WAAO;MACLkV,eAAe,CAAA;MACfoD,mBAAmB,CAAA;;;EAIvB5U,aACE1D,WAC2B;AAC3B,WAAO;MACLuY,uBAAuBA,MAAM;AAC3B,eAAOxD,UAAUpC;;MAGnB6F,mBAAmBA,MAAM;AAAA,YAAAC,wBAAAC;AACvB,cAAM;UAAElD;YAAmCxV,MAAM4B;AAEjD,eAAO9E,WAAW0Y,cAAc,IAC5BA,iBACAA,mBAAmB,SACnBxV,MAAMuY,sBAAqB,KAC7BE,0BAAAC,yBACE1Y,MAAM4B,QAAQmT,cAAS,OAAA,SAAvB2D,uBAA0BlD,cAAc,MAAWiD,OAAAA,yBACnD1D,UAAUS,cAAc;;MAG9B+B,kBAAmBjb,aAAyC;AAC1D,cAAM6F,cAAcnC,MAAM2Y,kBAAiB;AAE3C,cAAMC,WAAY/b,SAA4B;AAAA,cAAAgc;AAC5C,kBAAAA,oBAAOxc,iBAAiBC,SAASO,GAAG,MAAC,OAAA,SAA9Bgc,kBAAgCrU,OAAOA,YAAU;AACtD,kBAAMlD,SAASa,YAAYoC,KAAKxH,OAAKA,EAAE2D,OAAO8D,OAAO9D,EAAE;AAEvD,gBAAIY,QAAQ;AACV,oBAAM2T,WAAW3T,OAAO2U,YAAW;AAEnC,kBAAIyB,uBAAuBzC,UAAUzQ,OAAOoR,OAAOtU,MAAM,GAAG;AAC1D,uBAAO;cACT;YACF;AAEA,mBAAO;UACT,CAAC;;AAGHtB,cAAM4B,QAAQwT,yBAAdpV,OAAAA,SAAAA,MAAM4B,QAAQwT,sBAAwBwD,QAAQ;;MAGhDE,iBAAiBxc,aAAW;AAC1B0D,cAAM4B,QAAQyT,wBAAdrV,OAAAA,SAAAA,MAAM4B,QAAQyT,qBAAuB/Y,OAAO;;MAG9Cyc,mBAAmB9K,kBAAgB;AACjCjO,cAAM8Y,gBACJ7K,eAAerN,SAAYZ,MAAMmO,aAAagH,YAChD;;MAGF6D,oBAAoB/K,kBAAgB;AAAA,YAAAC,uBAAAsC;AAClCxQ,cAAMuX,iBACJtJ,eAAe,CAAA,KAAEC,yBAAAsC,sBAAGxQ,MAAMmO,iBAAY,OAAA,SAAlBqC,oBAAoB0E,kBAAahH,OAAAA,wBAAI,CAAA,CAC3D;;MAGF8J,wBAAwBA,MAAMhY,MAAM6V,gBAAe;MACnDoD,qBAAqBA,MAAM;AACzB,YAAI,CAACjZ,MAAMkZ,wBAAwBlZ,MAAM4B,QAAQqX,qBAAqB;AACpEjZ,gBAAMkZ,uBAAuBlZ,MAAM4B,QAAQqX,oBAAoBjZ,KAAK;QACtE;AAEA,YAAIA,MAAM4B,QAAQuX,mBAAmB,CAACnZ,MAAMkZ,sBAAsB;AAChE,iBAAOlZ,MAAMgY,uBAAsB;QACrC;AAEA,eAAOhY,MAAMkZ,qBAAoB;;MAGnCE,2BACEpZ,MAAM4B,QAAQmW,sBACd/X,MAAM4B,QAAQmW,mBAAmB/X,OAAO,YAAY;MAEtDqZ,0BAA0BA,MAAM;AAC9B,YAAIrZ,MAAM4B,QAAQuX,mBAAmB,CAACnZ,MAAMoZ,2BAA2B;AACrE,iBAAOpZ,MAAMgY,uBAAsB;QACrC;AAEA,eAAOhY,MAAMoZ,0BAAyB;;MAGxCE,+BACEtZ,MAAM4B,QAAQsW,0BACdlY,MAAM4B,QAAQsW,uBAAuBlY,OAAO,YAAY;MAC1DuZ,8BAA8BA,MAAM;AAClC,YAAI,CAACvZ,MAAMsZ,+BAA+B;AACxC,iBAAO,oBAAInB,IAAG;QAChB;AAEA,eAAOnY,MAAMsZ,8BAA6B;;MAG5CE,+BACExZ,MAAM4B,QAAQyW,0BACdrY,MAAM4B,QAAQyW,uBAAuBrY,OAAO,YAAY;MAC1DyZ,8BAA8BA,MAAM;AAClC,YAAI,CAACzZ,MAAMwZ,+BAA+B;AACxC;QACF;AAEA,eAAOxZ,MAAMwZ,8BAA6B;MAC5C;;EAEJ;AACF;AAEO,SAAS9B,uBACdzC,UACAW,OACAtU,QACA;AACA,UACG2T,YAAYA,SAAS7B,aAClB6B,SAAS7B,WAAWwC,OAAOtU,MAAM,IACjC,UACJ,OAAOsU,UAAU,eAChB,OAAOA,UAAU,YAAY,CAACA;AAEnC;ACxdA,IAAMhK,MAA0BA,CAACtH,UAAUoV,WAAWC,cAAc;AAGlE,SAAOA,UAAUrX,OAAO,CAACsJ,MAAKgO,SAAS;AACrC,UAAMC,YAAYD,KAAK1G,SAAS5O,QAAQ;AACxC,WAAOsH,QAAO,OAAOiO,cAAc,WAAWA,YAAY;KACzD,CAAC;AACN;AAEA,IAAM/Z,MAA0BA,CAACwE,UAAUoV,WAAWC,cAAc;AAClE,MAAI7Z;AAEJ6Z,YAAU/b,QAAQgT,SAAO;AACvB,UAAMgF,QAAQhF,IAAIsC,SAAiB5O,QAAQ;AAE3C,QACEsR,SAAS,SACR9V,OAAO8V,SAAU9V,SAAQc,UAAagV,SAASA,QAChD;AACA9V,MAAAA,OAAM8V;IACR;EACF,CAAC;AAED,SAAO9V;AACT;AAEA,IAAMD,MAA0BA,CAACyE,UAAUoV,WAAWC,cAAc;AAClE,MAAI9Z;AAEJ8Z,YAAU/b,QAAQgT,SAAO;AACvB,UAAMgF,QAAQhF,IAAIsC,SAAiB5O,QAAQ;AAC3C,QACEsR,SAAS,SACR/V,OAAO+V,SAAU/V,SAAQe,UAAagV,SAASA,QAChD;AACA/V,MAAAA,OAAM+V;IACR;EACF,CAAC;AAED,SAAO/V;AACT;AAEA,IAAMia,SAA6BA,CAACxV,UAAUoV,WAAWC,cAAc;AACrE,MAAI7Z;AACJ,MAAID;AAEJ8Z,YAAU/b,QAAQgT,SAAO;AACvB,UAAMgF,QAAQhF,IAAIsC,SAAiB5O,QAAQ;AAC3C,QAAIsR,SAAS,MAAM;AACjB,UAAI9V,SAAQc,QAAW;AACrB,YAAIgV,SAASA;AAAO9V,UAAAA,OAAMD,OAAM+V;MAClC,OAAO;AACL,YAAI9V,OAAM8V;AAAO9V,UAAAA,OAAM8V;AACvB,YAAI/V,OAAO+V;AAAO/V,UAAAA,OAAM+V;MAC1B;IACF;EACF,CAAC;AAED,SAAO,CAAC9V,MAAKD,IAAG;AAClB;AAEA,IAAMka,OAA2BA,CAACzV,UAAU0V,aAAa;AACvD,MAAIC,SAAQ;AACZ,MAAIrO,OAAM;AAEVoO,WAASpc,QAAQgT,SAAO;AACtB,QAAIgF,QAAQhF,IAAIsC,SAAiB5O,QAAQ;AACzC,QAAIsR,SAAS,SAASA,QAAQ,CAACA,UAAUA,OAAO;AAC9C,QAAEqE,QAAQrO,QAAOgK;IACnB;EACF,CAAC;AAED,MAAIqE;AAAO,WAAOrO,OAAMqO;AAExB;AACF;AAEA,IAAMC,SAA6BA,CAAC5V,UAAU0V,aAAa;AACzD,MAAI,CAACA,SAAShc,QAAQ;AACpB;EACF;AAEA,QAAMgT,SAASgJ,SAASzW,IAAIqN,SAAOA,IAAIsC,SAAS5O,QAAQ,CAAC;AACzD,MAAI,CAACrH,cAAc+T,MAAM,GAAG;AAC1B;EACF;AACA,MAAIA,OAAOhT,WAAW,GAAG;AACvB,WAAOgT,OAAO,CAAC;EACjB;AAEA,QAAMmJ,MAAMhb,KAAKib,MAAMpJ,OAAOhT,SAAS,CAAC;AACxC,QAAMqc,OAAOrJ,OAAOsJ,KAAK,CAACC,GAAGC,MAAMD,IAAIC,CAAC;AACxC,SAAOxJ,OAAOhT,SAAS,MAAM,IAAIqc,KAAKF,GAAG,KAAKE,KAAKF,MAAM,CAAC,IAAKE,KAAKF,GAAG,KAAM;AAC/E;AAEA,IAAMM,SAA6BA,CAACnW,UAAU0V,aAAa;AACzD,SAAO9c,MAAMwd,KAAK,IAAIC,IAAIX,SAASzW,IAAIxG,OAAKA,EAAEmW,SAAS5O,QAAQ,CAAC,CAAC,EAAE0M,OAAM,CAAE;AAC7E;AAEA,IAAM4J,cAAkCA,CAACtW,UAAU0V,aAAa;AAC9D,SAAO,IAAIW,IAAIX,SAASzW,IAAIxG,OAAKA,EAAEmW,SAAS5O,QAAQ,CAAC,CAAC,EAAE2E;AAC1D;AAEA,IAAMgR,QAA4BA,CAACY,WAAWb,aAAa;AACzD,SAAOA,SAAShc;AAClB;AAEO,IAAM8c,iBAAiB;EAC5BlP;EACA9L;EACAD;EACAia;EACAC;EACAG;EACAO;EACAG;EACAX;AACF;ACbO,IAAMc,WAAyB;EACpCjR,qBAAqBA,MAGhB;AACH,WAAO;MACLkR,gBAAgBC,WAAK;AAAA,YAAAC,WAAAC;AAAA,gBAAAD,aAAAC,kBAAKF,MAAM/H,SAAQ,MAAfiI,OAAAA,SAAAA,gBAA2BhI,YAAQ,OAAA,SAAnCgI,gBAA2BhI,SAAQ,MAAI,OAAA+H,YAAI;MAAI;MACxEE,eAAe;;;EAInBrR,iBAAkBC,WAA8B;AAC9C,WAAO;MACLqR,UAAU,CAAA;MACV,GAAGrR;;;EAIPG,mBACEnK,WACoB;AACpB,WAAO;MACLsb,kBAAkB7e,iBAAiB,YAAYuD,KAAK;MACpDub,mBAAmB;;;EAIvBxb,cAAcA,CACZuB,QACAtB,UAC0B;AAC1B,WAAO;MACLwb,gBAAgBA,MAAM;AACpBxb,cAAMyb,YAAY5e,SAAO;AAEvB,cAAIA,OAAAA,QAAAA,IAAKkE,SAASO,OAAOZ,EAAE,GAAG;AAC5B,mBAAO7D,IAAI2H,OAAOzH,OAAKA,MAAMuE,OAAOZ,EAAE;UACxC;AAEA,iBAAO,CAAC,GAAI7D,OAAG,OAAHA,MAAO,CAAA,GAAKyE,OAAOZ,EAAE;QACnC,CAAC;;MAGHgb,aAAaA,MAAM;AAAA,YAAAtb,MAAA+K,OAAA2B,OAAAvB;AACjB,gBAAAnL,QAAA+K,SAAA2B,SAAAvB,wBACEjK,OAAOrB,UAAU0b,mBAAcpQ,OAAAA,wBAC/B,SAAI,OAAAuB,QACJ9M,MAAM4B,QAAQ+Z,mBAAc,OAAAxQ,QAC5B,SAAI,OAAA/K,OACJ,CAAC,CAACkB,OAAOR;;MAIb8a,cAAcA,MAAM;AAAA,YAAAC;AAClB,gBAAAA,wBAAO7b,MAAM8D,SAAQ,EAAGuX,aAAQ,OAAA,SAAzBQ,sBAA2B9a,SAASO,OAAOZ,EAAE;;MAGtDob,iBAAiBA,MAAA;AAAA,YAAAC;AAAA,gBAAAA,yBAAM/b,MAAM8D,SAAQ,EAAGuX,aAAQ,OAAA,SAAzBU,uBAA2BC,QAAQ1a,OAAOZ,EAAE;MAAC;MAEpEub,0BAA0BA,MAAM;AAC9B,cAAMC,WAAW5a,OAAOoa,YAAW;AAEnC,eAAO,MAAM;AACX,cAAI,CAACQ;AAAU;AACf5a,iBAAOka,eAAc;;;MAGzBW,sBAAsBA,MAAM;AAC1B,cAAMnG,WAAWhW,MAAM6V,gBAAe,EAAGlF,SAAS,CAAC;AAEnD,cAAMiF,QAAQI,YAAAA,OAAAA,SAAAA,SAAU9C,SAAS5R,OAAOZ,EAAE;AAE1C,YAAI,OAAOkV,UAAU,UAAU;AAC7B,iBAAOkF,eAAelP;QACxB;AAEA,YAAInJ,OAAO2Z,UAAUjJ,SAASkJ,KAAKzG,KAAK,MAAM,iBAAiB;AAC7D,iBAAOkF,eAAehB;QACxB;;MAEFwC,kBAAkBA,MAAM;AAAA,YAAAC,uBAAAC;AACtB,YAAI,CAAClb,QAAQ;AACX,gBAAM,IAAID,MAAK;QACjB;AAEA,eAAOvE,WAAWwE,OAAOrB,UAAUmb,aAAa,IAC5C9Z,OAAOrB,UAAUmb,gBACjB9Z,OAAOrB,UAAUmb,kBAAkB,SACnC9Z,OAAO6a,qBAAoB,KAAEI,yBAAAC,yBAC7Bxc,MAAM4B,QAAQkZ,mBAAc,OAAA,SAA5B0B,uBACElb,OAAOrB,UAAUmb,aAAa,MAC/BmB,OAAAA,wBACDzB,eACExZ,OAAOrB,UAAUmb,aAAa;MAEtC;;;EAIJ1X,aACE1D,WAC4B;AAC5B,WAAO;MACLyb,aAAanf,aAAW0D,MAAM4B,QAAQ0Z,oBAAdtb,OAAAA,SAAAA,MAAM4B,QAAQ0Z,iBAAmBhf,OAAO;MAEhEmgB,eAAexO,kBAAgB;AAAA,YAAAyO,uBAAAlM;AAC7BxQ,cAAMyb,YACJxN,eAAe,CAAA,KAAEyO,yBAAAlM,sBAAGxQ,MAAMmO,iBAAY,OAAA,SAAlBqC,oBAAoB6K,aAAQqB,OAAAA,wBAAI,CAAA,CACtD;;MAGFC,uBAAuBA,MAAM3c,MAAMiZ,oBAAmB;MACtD2D,oBAAoBA,MAAM;AACxB,YAAI,CAAC5c,MAAM6c,uBAAuB7c,MAAM4B,QAAQgb,oBAAoB;AAClE5c,gBAAM6c,sBAAsB7c,MAAM4B,QAAQgb,mBAAmB5c,KAAK;QACpE;AAEA,YAAIA,MAAM4B,QAAQkb,kBAAkB,CAAC9c,MAAM6c,qBAAqB;AAC9D,iBAAO7c,MAAM2c,sBAAqB;QACpC;AAEA,eAAO3c,MAAM6c,oBAAmB;MAClC;;;EAIJjL,WAAWA,CACThB,KACA5Q,UACgB;AAChB,WAAO;MACL4b,cAAcA,MAAM,CAAC,CAAChL,IAAImM;MAC1BC,kBAAkB1Y,cAAY;AAC5B,YAAIsM,IAAIqM,qBAAqBC,eAAe5Y,QAAQ,GAAG;AACrD,iBAAOsM,IAAIqM,qBAAqB3Y,QAAQ;QAC1C;AAEA,cAAMhD,SAAStB,MAAMgM,UAAU1H,QAAQ;AAEvC,YAAI,EAAChD,UAAAA,QAAAA,OAAQrB,UAAU+c,mBAAkB;AACvC,iBAAOpM,IAAIsC,SAAS5O,QAAQ;QAC9B;AAEAsM,YAAIqM,qBAAqB3Y,QAAQ,IAAIhD,OAAOrB,UAAU+c,iBACpDpM,IAAIuM,QACN;AAEA,eAAOvM,IAAIqM,qBAAqB3Y,QAAQ;;MAE1C2Y,sBAAsB,CAAA;;;EAI1BG,YAAYA,CACVC,MACA/b,QACAsP,KACA5Q,UACiB;AAIjB,WAAO;MACL4b,cAAcA,MACZta,OAAOsa,aAAY,KAAMta,OAAOZ,OAAOkQ,IAAImM;MAC7CO,kBAAkBA,MAAM,CAACD,KAAKzB,aAAY,KAAMta,OAAOsa,aAAY;MACnE2B,iBAAiBA,MAAA;AAAA,YAAAlL;AAAA,eACf,CAACgL,KAAKzB,aAAY,KAClB,CAACyB,KAAKC,iBAAgB,KACtB,CAAC,GAAAjL,eAACzB,IAAI4B,YAAO,QAAXH,aAAarU;MAAM;;EAE3B;AACF;AAEO,SAASiE,aACdE,aACAkZ,UACAE,mBACA;AACA,MAAI,EAACF,YAAAA,QAAAA,SAAUrd,WAAU,CAACud,mBAAmB;AAC3C,WAAOpZ;EACT;AAEA,QAAMqb,qBAAqBrb,YAAYqC,OACrCiZ,SAAO,CAACpC,SAASta,SAAS0c,IAAI/c,EAAE,CAClC;AAEA,MAAI6a,sBAAsB,UAAU;AAClC,WAAOiC;EACT;AAEA,QAAME,kBAAkBrC,SACrB9X,IAAIoa,OAAKxb,YAAYoC,KAAKkZ,SAAOA,IAAI/c,OAAOid,CAAC,CAAE,EAC/CnZ,OAAOC,OAAO;AAEjB,SAAO,CAAC,GAAGiZ,iBAAiB,GAAGF,kBAAkB;AACnD;AChRO,IAAMI,WAAyB;EACpC7T,iBAAkBC,WAAiC;AACjD,WAAO;MACL6T,aAAa,CAAA;MACb,GAAG7T;;;EAIPG,mBACEnK,WAC8B;AAC9B,WAAO;MACL8d,qBAAqBrhB,iBAAiB,eAAeuD,KAAK;;;EAI9D0D,aACE1D,WAC+B;AAC/B,WAAO;MACL+d,gBAAgBzhB,aAAW0D,MAAM4B,QAAQkc,uBAAd9d,OAAAA,SAAAA,MAAM4B,QAAQkc,oBAAsBxhB,OAAO;MACtE0hB,kBAAkB/P,kBAAgB;AAAA,YAAAC;AAChClO,cAAM+d,eACJ9P,eAAe,CAAA,KAAEC,wBAAGlO,MAAMmO,aAAa0P,gBAAW,OAAA3P,wBAAI,CAAA,CACxD;;MAEFlM,oBAAoB/D,KAClB,MAAM,CACJ+B,MAAM8D,SAAQ,EAAG+Z,aACjB7d,MAAM8D,SAAQ,EAAGuX,UACjBrb,MAAM4B,QAAQ2Z,iBAAiB,GAEjC,CAACsC,aAAaxC,UAAUE,sBAAsBha,aAAW;AAGvD,YAAI0c,iBAA2C,CAAA;AAG/C,YAAI,EAACJ,eAAW,QAAXA,YAAa7f,SAAQ;AACxBigB,2BAAiB1c;QACnB,OAAO;AACL,gBAAM2c,kBAAkB,CAAC,GAAGL,WAAW;AAGvC,gBAAMM,cAAc,CAAC,GAAG5c,OAAO;AAK/B,iBAAO4c,YAAYngB,UAAUkgB,gBAAgBlgB,QAAQ;AACnD,kBAAMogB,iBAAiBF,gBAAgBG,MAAK;AAC5C,kBAAMC,aAAaH,YAAYpT,UAC7BhO,OAAKA,EAAE2D,OAAO0d,cAChB;AACA,gBAAIE,aAAa,IAAI;AACnBL,6BAAengB,KAAKqgB,YAAYI,OAAOD,YAAY,CAAC,EAAE,CAAC,CAAE;YAC3D;UACF;AAGAL,2BAAiB,CAAC,GAAGA,gBAAgB,GAAGE,WAAW;QACrD;AAEA,eAAOlc,aAAagc,gBAAgB5C,UAAUE,iBAAiB;MACjE,GACA;QACE7e,KAA+C;;OAGnD;;EAEJ;AACF;ACpDA,IAAM8hB,mBAAmB;AACzB,IAAMC,kBAAkB;AAExB,IAAMC,4BAA4BA,OAAwB;EACxDC,WAAWH;EACXI,UAAUH;AACZ;AAEO,IAAMI,aAA2B;EACtC9U,iBAAkBC,WAAgC;AAChD,WAAO;MACL,GAAGA;MACH8U,YAAY;QACV,GAAGJ,0BAAyB;QAC5B,GAAG1U,SAAK,OAAA,SAALA,MAAO8U;MACZ;;;EAIJ3U,mBACEnK,WAC6B;AAC7B,WAAO;MACL+e,oBAAoBtiB,iBAAiB,cAAcuD,KAAK;;;EAI5D0D,aACE1D,WAC8B;AAC9B,QAAI2P,aAAa;AACjB,QAAIC,SAAS;AAEb,WAAO;MACLoP,qBAAqBA,MAAM;AAAA,YAAA5e,MAAA0P;AACzB,YAAI,CAACH,YAAY;AACf3P,gBAAM+P,OAAO,MAAM;AACjBJ,yBAAa;UACf,CAAC;AACD;QACF;AAEA,aAAAvP,QAAA0P,wBACE9P,MAAM4B,QAAQoO,iBAAYF,OAAAA,wBAC1B9P,MAAM4B,QAAQqd,uBAAkB,OAAA7e,OAChC,CAACJ,MAAM4B,QAAQsd,kBACf;AACA,cAAItP;AAAQ;AACZA,mBAAS;AACT5P,gBAAM+P,OAAO,MAAM;AACjB/P,kBAAMmf,eAAc;AACpBvP,qBAAS;UACX,CAAC;QACH;;MAEFwP,eAAe9iB,aAAW;AACxB,cAAM+iB,cAAwCxiB,SAAO;AACnD,cAAIyiB,WAAWjjB,iBAAiBC,SAASO,GAAG;AAE5C,iBAAOyiB;;AAGT,eAAOtf,MAAM4B,QAAQmd,sBAAkB,OAAA,SAAhC/e,MAAM4B,QAAQmd,mBAAqBM,WAAW;;MAEvDE,iBAAiBtR,kBAAgB;AAAA,YAAAuR;AAC/Bxf,cAAMof,cACJnR,eACIyQ,0BAAyB,KAAEc,wBAC3Bxf,MAAMmO,aAAa2Q,eAAU,OAAAU,wBAAId,0BAAyB,CAChE;;MAEFe,cAAcnjB,aAAW;AACvB0D,cAAMof,cAAcviB,SAAO;AACzB,cAAI8hB,YAAYtiB,iBAAiBC,SAASO,IAAI8hB,SAAS;AAEvD,gBAAMe,eACJ,OAAO1f,MAAM4B,QAAQ+d,cAAc,eACnC3f,MAAM4B,QAAQ+d,cAAc,KACxBvW,OAAOC,mBACPrJ,MAAM4B,QAAQ+d,YAAY;AAEhChB,sBAAYxf,KAAKU,IAAI,GAAGV,KAAKW,IAAI6e,WAAWe,YAAY,CAAC;AAEzD,iBAAO;YACL,GAAG7iB;YACH8hB;;QAEJ,CAAC;;MAEHQ,gBAAgBlR,kBAAgB;AAAA,YAAA2R,wBAAApP,qBAAAqP;AAC9B7f,cAAMyf,aACJxR,eACIuQ,oBAAgBoB,0BAAApP,sBAChBxQ,MAAMmO,iBAAY0R,OAAAA,UAAAA,yBAAlBrP,oBAAoBsO,eAApBe,OAAAA,SAAAA,uBAAgClB,cAASiB,OAAAA,yBAAIpB,gBACnD;;MAEFsB,eAAe7R,kBAAgB;AAAA,YAAA8R,wBAAAC,sBAAAC;AAC7BjgB,cAAMkgB,YACJjS,eACIwQ,mBAAesB,0BAAAC,uBACfhgB,MAAMmO,iBAAY8R,OAAAA,UAAAA,wBAAlBD,qBAAoBlB,eAApBmB,OAAAA,SAAAA,sBAAgCrB,aAAQmB,OAAAA,yBAAItB,eAClD;;MAEFyB,aAAa5jB,aAAW;AACtB0D,cAAMof,cAAcviB,SAAO;AACzB,gBAAM+hB,WAAWzf,KAAKU,IAAI,GAAGxD,iBAAiBC,SAASO,IAAI+hB,QAAQ,CAAC;AACpE,gBAAMuB,cAActjB,IAAI+hB,WAAW/hB,IAAI8hB;AACvC,gBAAMA,YAAYxf,KAAKib,MAAM+F,cAAcvB,QAAQ;AAEnD,iBAAO;YACL,GAAG/hB;YACH8hB;YACAC;;QAEJ,CAAC;;MAEHwB,cAAc9jB,aACZ0D,MAAMof,cAAcviB,SAAO;AAAA,YAAAwjB;AACzB,YAAIC,eAAejkB,iBACjBC,UAAO+jB,wBACPrgB,MAAM4B,QAAQ+d,cAAS,OAAAU,wBAAI,EAC7B;AAEA,YAAI,OAAOC,iBAAiB,UAAU;AACpCA,yBAAenhB,KAAKU,IAAI,IAAIygB,YAAY;QAC1C;AAEA,eAAO;UACL,GAAGzjB;UACH8iB,WAAWW;;MAEf,CAAC;MAEHC,gBAAgBtiB,KACd,MAAM,CAAC+B,MAAMwgB,aAAY,CAAE,GAC3Bb,eAAa;AACX,YAAIc,cAAwB,CAAA;AAC5B,YAAId,aAAaA,YAAY,GAAG;AAC9Bc,wBAAc,CAAC,GAAG,IAAIvjB,MAAMyiB,SAAS,CAAC,EAAEe,KAAK,IAAI,EAAEnd,IAAI,CAAC6H,GAAGuV,MAAMA,CAAC;QACpE;AACA,eAAOF;MACT,GACA;QACE/jB,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAmD;AAAA,kBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;QAAU;MACjE,CACF;MAEAC,oBAAoBA,MAAM7gB,MAAM8D,SAAQ,EAAGgb,WAAWH,YAAY;MAElEmC,gBAAgBA,MAAM;AACpB,cAAM;UAAEnC;QAAU,IAAI3e,MAAM8D,SAAQ,EAAGgb;AAEvC,cAAMa,YAAY3f,MAAMwgB,aAAY;AAEpC,YAAIb,cAAc,IAAI;AACpB,iBAAO;QACT;AAEA,YAAIA,cAAc,GAAG;AACnB,iBAAO;QACT;AAEA,eAAOhB,YAAYgB,YAAY;;MAGjCoB,cAAcA,MAAM;AAClB,eAAO/gB,MAAMyf,aAAa5iB,SAAOA,MAAM,CAAC;;MAG1CmkB,UAAUA,MAAM;AACd,eAAOhhB,MAAMyf,aAAa5iB,SAAO;AAC/B,iBAAOA,MAAM;QACf,CAAC;;MAGH6T,0BAA0BA,MAAM1Q,MAAM0R,oBAAmB;MACzDuP,uBAAuBA,MAAM;AAC3B,YACE,CAACjhB,MAAMkhB,0BACPlhB,MAAM4B,QAAQqf,uBACd;AACAjhB,gBAAMkhB,yBACJlhB,MAAM4B,QAAQqf,sBAAsBjhB,KAAK;QAC7C;AAEA,YAAIA,MAAM4B,QAAQsd,oBAAoB,CAAClf,MAAMkhB,wBAAwB;AACnE,iBAAOlhB,MAAM0Q,yBAAwB;QACvC;AAEA,eAAO1Q,MAAMkhB,uBAAsB;;MAGrCV,cAAcA,MAAM;AAAA,YAAAW;AAClB,gBAAAA,yBACEnhB,MAAM4B,QAAQ+d,cAASwB,OAAAA,yBACvBhiB,KAAKiiB,KACHphB,MAAM0Q,yBAAwB,EAAG2Q,KAAKrjB,SACpCgC,MAAM8D,SAAQ,EAAGgb,WAAWF,QAChC;MAEJ;;EAEJ;AACF;ACnMA,IAAM0C,yBAAyBA,OAA2B;EACxDtd,MAAM,CAAA;EACNC,OAAO,CAAA;AACT;AAEO,IAAMsd,UAAwB;EACnCxX,iBAAkBC,WAAmC;AACnD,WAAO;MACLjG,eAAeud,uBAAsB;MACrC,GAAGtX;;;EAIPG,mBACEnK,WACgC;AAChC,WAAO;MACLwhB,uBAAuB/kB,iBAAiB,iBAAiBuD,KAAK;;;EAIlED,cAAcA,CACZuB,QACAtB,UACwB;AACxB,WAAO;MACLyhB,KAAK7W,cAAY;AACf,cAAM8W,YAAYpgB,OACfS,eAAc,EACdwB,IAAIxG,OAAKA,EAAE2D,EAAE,EACb8D,OAAOC,OAAO;AAEjBzE,cAAM2hB,iBAAiB9kB,SAAO;AAAA,cAAA+kB,YAAAC;AAC5B,cAAIjX,aAAa,SAAS;AAAA,gBAAAkX,WAAAC;AACxB,mBAAO;cACL/d,QAAM8d,YAACjlB,OAAAA,OAAAA,SAAAA,IAAKmH,SAAI8d,OAAAA,YAAI,CAAA,GAAItd,OAAOzH,OAAK,EAAC2kB,aAAS,QAATA,UAAW3gB,SAAShE,CAAC,EAAE;cAC5DkH,OAAO,CACL,KAAG8d,aAACllB,OAAAA,OAAAA,SAAAA,IAAKoH,UAAK8d,OAAAA,aAAI,CAAA,GAAIvd,OAAOzH,OAAK,EAAC2kB,aAAS,QAATA,UAAW3gB,SAAShE,CAAC,EAAE,GAC1D,GAAG2kB,SAAS;;UAGlB;AAEA,cAAI9W,aAAa,QAAQ;AAAA,gBAAAoX,YAAAC;AACvB,mBAAO;cACLje,MAAM,CACJ,KAAGge,aAACnlB,OAAAA,OAAAA,SAAAA,IAAKmH,SAAIge,OAAAA,aAAI,CAAA,GAAIxd,OAAOzH,OAAK,EAAC2kB,aAAS,QAATA,UAAW3gB,SAAShE,CAAC,EAAC,GACxD,GAAG2kB,SAAS;cAEdzd,SAAOge,cAACplB,OAAAA,OAAAA,SAAAA,IAAKoH,UAAKge,OAAAA,cAAI,CAAA,GAAIzd,OAAOzH,OAAK,EAAC2kB,aAAAA,QAAAA,UAAW3gB,SAAShE,CAAC,EAAC;;UAEjE;AAEA,iBAAO;YACLiH,QAAM4d,aAAC/kB,OAAAA,OAAAA,SAAAA,IAAKmH,SAAI4d,OAAAA,aAAI,CAAA,GAAIpd,OAAOzH,OAAK,EAAC2kB,aAAS,QAATA,UAAW3gB,SAAShE,CAAC,EAAE;YAC5DkH,SAAO4d,cAAChlB,OAAAA,OAAAA,SAAAA,IAAKoH,UAAK4d,OAAAA,cAAI,CAAA,GAAIrd,OAAOzH,OAAK,EAAC2kB,aAAAA,QAAAA,UAAW3gB,SAAShE,CAAC,EAAC;;QAEjE,CAAC;;MAGHmlB,WAAWA,MAAM;AACf,cAAM/f,cAAcb,OAAOS,eAAc;AAEzC,eAAOI,YAAYtD,KACjB9B,OAAC;AAAA,cAAAolB,uBAAA3W;AAAA,mBACC2W,wBAACplB,EAAEkD,UAAUmiB,kBAAaD,OAAAA,wBAAI,WAAI3W,wBACjCxL,MAAM4B,QAAQwgB,kBAAa5W,OAAAA,wBAAI;QAAK,CACzC;;MAGF6W,aAAaA,MAAM;AACjB,cAAMC,gBAAgBhhB,OAAOS,eAAc,EAAGwB,IAAIxG,OAAKA,EAAE2D,EAAE;AAE3D,cAAM;UAAEsD;UAAMC;QAAM,IAAIjE,MAAM8D,SAAQ,EAAGC;AAEzC,cAAMwe,SAASD,cAAczjB,KAAK9B,OAAKiH,QAAI,OAAA,SAAJA,KAAMjD,SAAShE,CAAC,CAAC;AACxD,cAAMylB,UAAUF,cAAczjB,KAAK9B,OAAKkH,SAAK,OAAA,SAALA,MAAOlD,SAAShE,CAAC,CAAC;AAE1D,eAAOwlB,SAAS,SAASC,UAAU,UAAU;;MAG/CC,gBAAgBA,MAAM;AAAA,YAAAvL,uBAAAC,wBAAAC;AACpB,cAAMxM,WAAWtJ,OAAO+gB,YAAW;AAEnC,eAAOzX,YAAQsM,yBAAAC,yBACXnX,MAAM8D,SAAQ,EAAGC,kBAAa,OAAA,UAAAqT,yBAA9BD,uBAAiCvM,QAAQ,MAAzCwM,OAAAA,SAAAA,uBAA4C4E,QAAQ1a,OAAOZ,EAAE,MAAC,OAAAwW,wBAAI,KAClE;MACN;;;EAIJtF,WAAWA,CACThB,KACA5Q,UAC4B;AAC5B,WAAO;MACL0iB,uBAAuBzkB,KACrB,MAAM,CACJ2S,IAAI+R,oBAAmB,GACvB3iB,MAAM8D,SAAQ,EAAGC,cAAcC,MAC/BhE,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAAC2e,UAAU5e,MAAMC,UAAU;AACzB,cAAM4e,eAAyB,CAAC,GAAI7e,QAAI,OAAJA,OAAQ,CAAA,GAAK,GAAIC,SAAK,OAALA,QAAS,CAAA,CAAG;AAEjE,eAAO2e,SAASpe,OAAOzH,OAAK,CAAC8lB,aAAa9hB,SAAShE,EAAEuE,OAAOZ,EAAE,CAAC;MACjE,GACA;QACEhE,KACE0E;QAEF5C,OAAOA,MAAA;AAAA,cAAAmD;AAAA,kBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQkhB;QAAS;MAChE,CACF;MACAC,qBAAqB9kB,KACnB,MAAM,CACJ2S,IAAI+R,oBAAmB,GACvB3iB,MAAM8D,SAAQ,EAAGC,cAAcC,MAAI,CAAA,GAGrC,CAAC4e,UAAU5e,SAAS;AAClB,cAAMgf,SAAShf,QAAI,OAAJA,OAAQ,CAAA,GACpBT,IACCe,cAAYse,SAASre,KAAK8Y,UAAQA,KAAK/b,OAAOZ,OAAO4D,QAAQ,CAC/D,EACCE,OAAOC,OAAO,EACdlB,IAAIxG,QAAM;UAAE,GAAGA;UAAG6N,UAAU;QAAO,EAA0B;AAEhE,eAAOoY;MACT,GACA;QACEtmB,KACE0E;QACF5C,OAAOA,MAAA;AAAA,cAAA4D;AAAA,kBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQkhB;QAAS;MAChE,CACF;MACAG,sBAAsBhlB,KACpB,MAAM,CAAC2S,IAAI+R,oBAAmB,GAAI3iB,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GACtE,CAAC2e,UAAU3e,UAAU;AACnB,cAAM+e,SAAS/e,SAAK,OAALA,QAAS,CAAA,GACrBV,IACCe,cAAYse,SAASre,KAAK8Y,UAAQA,KAAK/b,OAAOZ,OAAO4D,QAAQ,CAC/D,EACCE,OAAOC,OAAO,EACdlB,IAAIxG,QAAM;UAAE,GAAGA;UAAG6N,UAAU;QAAQ,EAA0B;AAEjE,eAAOoY;MACT,GACA;QACEtmB,KACE0E;QACF5C,OAAOA,MAAA;AAAA,cAAA2G;AAAA,kBAAAA,yBAAMnF,MAAM4B,QAAQC,aAAQsD,OAAAA,yBAAInF,MAAM4B,QAAQkhB;QAAS;OAElE;;;EAIJpf,aACE1D,WACiC;AACjC,WAAO;MACL2hB,kBAAkBrlB,aAChB0D,MAAM4B,QAAQ4f,yBAAdxhB,OAAAA,SAAAA,MAAM4B,QAAQ4f,sBAAwBllB,OAAO;MAE/C4mB,oBAAoBjV,kBAAY;AAAA,YAAAC,uBAAAsC;AAAA,eAC9BxQ,MAAM2hB,iBACJ1T,eACIqT,uBAAsB,KAAEpT,yBAAAsC,sBACxBxQ,MAAMmO,iBAANqC,OAAAA,SAAAA,oBAAoBzM,kBAAamK,OAAAA,wBAAIoT,uBAAsB,CACjE;MAAC;MAEH6B,wBAAwBvY,cAAY;AAAA,YAAAwY;AAClC,cAAMC,eAAerjB,MAAM8D,SAAQ,EAAGC;AAEtC,YAAI,CAAC6G,UAAU;AAAA,cAAA0Y,oBAAAC;AACb,iBAAO9e,UACL6e,qBAAAD,aAAarf,SAAI,OAAA,SAAjBsf,mBAAmBtlB,aAAMulB,sBAAIF,aAAapf,UAAK,OAAA,SAAlBsf,oBAAoBvlB,OACnD;QACF;AACA,eAAOyG,SAAO2e,wBAACC,aAAazY,QAAQ,MAArBwY,OAAAA,SAAAA,sBAAwBplB,MAAM;;MAG/CwlB,oBAAoBvlB,KAClB,MAAM,CAAC+B,MAAM2Y,kBAAiB,GAAI3Y,MAAM8D,SAAQ,EAAGC,cAAcC,IAAI,GACrE,CAACE,YAAYF,SAAS;AACpB,gBAAQA,QAAAA,OAAAA,OAAQ,CAAA,GACbT,IAAIe,cAAYJ,WAAWK,KAAKjD,YAAUA,OAAOZ,OAAO4D,QAAQ,CAAE,EAClEE,OAAOC,OAAO;MACnB,GACA;QACE/H,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAA8G;AAAA,kBAAAA,yBAAMtF,MAAM4B,QAAQC,aAAQyD,OAAAA,yBAAItF,MAAM4B,QAAQE;QAAY;MACnE,CACF;MAEA2hB,qBAAqBxlB,KACnB,MAAM,CAAC+B,MAAM2Y,kBAAiB,GAAI3Y,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GACtE,CAACC,YAAYD,UAAU;AACrB,gBAAQA,SAAAA,OAAAA,QAAS,CAAA,GACdV,IAAIe,cAAYJ,WAAWK,KAAKjD,YAAUA,OAAOZ,OAAO4D,QAAQ,CAAE,EAClEE,OAAOC,OAAO;MACnB,GACA;QACE/H,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAiH;AAAA,kBAAAA,yBAAMzF,MAAM4B,QAAQC,aAAQ4D,OAAAA,yBAAIzF,MAAM4B,QAAQE;QAAY;MACnE,CACF;MAEA4hB,sBAAsBzlB,KACpB,MAAM,CACJ+B,MAAM2Y,kBAAiB,GACvB3Y,MAAM8D,SAAQ,EAAGC,cAAcC,MAC/BhE,MAAM8D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAYF,MAAMC,UAAU;AAC3B,cAAM4e,eAAyB,CAAC,GAAI7e,QAAI,OAAJA,OAAQ,CAAA,GAAK,GAAIC,SAAK,OAALA,QAAS,CAAA,CAAG;AAEjE,eAAOC,WAAWM,OAAOzH,OAAK,CAAC8lB,aAAa9hB,SAAShE,EAAE2D,EAAE,CAAC;MAC5D,GACA;QACEhE,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAmH;AAAA,kBAAAA,yBAAM3F,MAAM4B,QAAQC,aAAQ8D,OAAAA,yBAAI3F,MAAM4B,QAAQE;QAAY;OAErE;;EAEJ;AACF;ACpOO,IAAM6hB,eAA6B;EACxC5Z,iBAAkBC,WAAkC;AAClD,WAAO;MACL4Z,cAAc,CAAA;MACd,GAAG5Z;;;EAIPG,mBACEnK,WAC+B;AAC/B,WAAO;MACL6jB,sBAAsBpnB,iBAAiB,gBAAgBuD,KAAK;MAC5D8jB,oBAAoB;MACpBC,yBAAyB;MACzBC,uBAAuB;;;;;;EAO3BtgB,aACE1D,WACgC;AAChC,WAAO;MACLikB,iBAAiB3nB,aAAW0D,MAAM4B,QAAQiiB,wBAAd7jB,OAAAA,SAAAA,MAAM4B,QAAQiiB,qBAAuBvnB,OAAO;MACxE4nB,mBAAmBjW,kBAAY;AAAA,YAAAkW;AAAA,eAC7BnkB,MAAMikB,gBACJhW,eAAe,CAAA,KAAEkW,wBAAGnkB,MAAMmO,aAAayV,iBAAY,OAAAO,wBAAI,CAAA,CACzD;MAAC;MACHC,uBAAuBxO,WAAS;AAC9B5V,cAAMikB,gBAAgBpnB,SAAO;AAC3B+Y,kBACE,OAAOA,UAAU,cAAcA,QAAQ,CAAC5V,MAAMqkB,qBAAoB;AAEpE,gBAAMT,eAAe;YAAE,GAAG/mB;;AAE1B,gBAAMynB,qBAAqBtkB,MAAM2c,sBAAqB,EAAGhM;AAIzD,cAAIiF,OAAO;AACT0O,+BAAmB1mB,QAAQgT,SAAO;AAChC,kBAAI,CAACA,IAAI2T,aAAY,GAAI;AACvB;cACF;AACAX,2BAAahT,IAAIlQ,EAAE,IAAI;YACzB,CAAC;UACH,OAAO;AACL4jB,+BAAmB1mB,QAAQgT,SAAO;AAChC,qBAAOgT,aAAahT,IAAIlQ,EAAE;YAC5B,CAAC;UACH;AAEA,iBAAOkjB;QACT,CAAC;;MAEHY,2BAA2B5O,WACzB5V,MAAMikB,gBAAgBpnB,SAAO;AAC3B,cAAM4nB,gBACJ,OAAO7O,UAAU,cACbA,QACA,CAAC5V,MAAM0kB,yBAAwB;AAErC,cAAMd,eAAkC;UAAE,GAAG/mB;;AAE7CmD,cAAMkR,YAAW,EAAGmQ,KAAKzjB,QAAQgT,SAAO;AACtC+T,8BAAoBf,cAAchT,IAAIlQ,IAAI+jB,eAAezkB,KAAK;QAChE,CAAC;AAED,eAAO4jB;MACT,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA4DHgB,wBAAwBA,MAAM5kB,MAAM6V,gBAAe;MACnDgP,qBAAqB5mB,KACnB,MAAM,CAAC+B,MAAM8D,SAAQ,EAAG8f,cAAc5jB,MAAM6V,gBAAe,CAAE,GAC7D,CAAC+N,cAAckB,aAAa;AAC1B,YAAI,CAACriB,OAAOwO,KAAK2S,YAAY,EAAE5lB,QAAQ;AACrC,iBAAO;YACLqjB,MAAM,CAAA;YACN1Q,UAAU,CAAA;YACVW,UAAU,CAAA;;QAEd;AAEA,eAAOyT,aAAa/kB,OAAO8kB,QAAQ;MACrC,GACA;QACEpoB,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAAmD;AAAA,kBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;QAAU;MACjE,CACF;MAEAoE,6BAA6B/mB,KAC3B,MAAM,CAAC+B,MAAM8D,SAAQ,EAAG8f,cAAc5jB,MAAMiZ,oBAAmB,CAAE,GACjE,CAAC2K,cAAckB,aAAa;AAC1B,YAAI,CAACriB,OAAOwO,KAAK2S,YAAY,EAAE5lB,QAAQ;AACrC,iBAAO;YACLqjB,MAAM,CAAA;YACN1Q,UAAU,CAAA;YACVW,UAAU,CAAA;;QAEd;AAEA,eAAOyT,aAAa/kB,OAAO8kB,QAAQ;MACrC,GACA;QACEpoB,KACE0E;QAEF5C,OAAOA,MAAA;AAAA,cAAA4D;AAAA,kBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQgf;QAAU;MACjE,CACF;MAEAqE,4BAA4BhnB,KAC1B,MAAM,CAAC+B,MAAM8D,SAAQ,EAAG8f,cAAc5jB,MAAMyR,kBAAiB,CAAE,GAC/D,CAACmS,cAAckB,aAAa;AAC1B,YAAI,CAACriB,OAAOwO,KAAK2S,YAAY,EAAE5lB,QAAQ;AACrC,iBAAO;YACLqjB,MAAM,CAAA;YACN1Q,UAAU,CAAA;YACVW,UAAU,CAAA;;QAEd;AAEA,eAAOyT,aAAa/kB,OAAO8kB,QAAQ;MACrC,GACA;QACEpoB,KACE0E;QAEF5C,OAAOA,MAAA;AAAA,cAAA2G;AAAA,kBAAAA,yBAAMnF,MAAM4B,QAAQC,aAAQsD,OAAAA,yBAAInF,MAAM4B,QAAQgf;QAAU;MACjE,CACF;;;;;;;;;;;;MAkBAyD,sBAAsBA,MAAM;AAC1B,cAAMC,qBAAqBtkB,MAAMiZ,oBAAmB,EAAGtI;AACvD,cAAM;UAAEiT;QAAa,IAAI5jB,MAAM8D,SAAQ;AAEvC,YAAIohB,oBAAoBzgB,QACtB6f,mBAAmBtmB,UAAUyE,OAAOwO,KAAK2S,YAAY,EAAE5lB,MACzD;AAEA,YAAIknB,mBAAmB;AACrB,cACEZ,mBAAmBzlB,KACjB+R,SAAOA,IAAI2T,aAAY,KAAM,CAACX,aAAahT,IAAIlQ,EAAE,CACnD,GACA;AACAwkB,gCAAoB;UACtB;QACF;AAEA,eAAOA;;MAGTR,0BAA0BA,MAAM;AAC9B,cAAMS,qBAAqBnlB,MACxBihB,sBAAqB,EACrBtQ,SAASnM,OAAOoM,SAAOA,IAAI2T,aAAY,CAAE;AAC5C,cAAM;UAAEX;QAAa,IAAI5jB,MAAM8D,SAAQ;AAEvC,YAAIshB,wBAAwB,CAAC,CAACD,mBAAmBnnB;AAEjD,YACEonB,yBACAD,mBAAmBtmB,KAAK+R,SAAO,CAACgT,aAAahT,IAAIlQ,EAAE,CAAC,GACpD;AACA0kB,kCAAwB;QAC1B;AAEA,eAAOA;;MAGTC,uBAAuBA,MAAM;AAAA,YAAAC;AAC3B,cAAMC,gBAAgB9iB,OAAOwO,MAAIqU,wBAC/BtlB,MAAM8D,SAAQ,EAAG8f,iBAAY0B,OAAAA,wBAAI,CAAA,CACnC,EAAEtnB;AACF,eACEunB,gBAAgB,KAChBA,gBAAgBvlB,MAAMiZ,oBAAmB,EAAGtI,SAAS3S;;MAIzDwnB,2BAA2BA,MAAM;AAC/B,cAAML,qBAAqBnlB,MAAMihB,sBAAqB,EAAGtQ;AACzD,eAAO3Q,MAAM0kB,yBAAwB,IACjC,QACAS,mBACG3gB,OAAOoM,SAAOA,IAAI2T,aAAY,CAAE,EAChC1lB,KAAK9B,OAAKA,EAAE0oB,cAAa,KAAM1oB,EAAE2oB,kBAAiB,CAAE;;MAG7DC,iCAAiCA,MAAM;AACrC,eAAQzZ,OAAe;AACrBlM,gBAAMokB,sBACFlY,EAAiB0Z,OAA4BC,OACjD;;;MAIJC,qCAAqCA,MAAM;AACzC,eAAQ5Z,OAAe;AACrBlM,gBAAMwkB,0BACFtY,EAAiB0Z,OAA4BC,OACjD;;MAEJ;;;EAIJjU,WAAWA,CACThB,KACA5Q,UACoB;AACpB,WAAO;MACL+lB,gBAAgBnQ,WAAS;AACvB,cAAMoQ,aAAapV,IAAI6U,cAAa;AAEpCzlB,cAAMikB,gBAAgBpnB,SAAO;AAC3B+Y,kBAAQ,OAAOA,UAAU,cAAcA,QAAQ,CAACoQ;AAEhD,cAAIA,eAAepQ,OAAO;AACxB,mBAAO/Y;UACT;AAEA,gBAAMopB,iBAAiB;YAAE,GAAGppB;;AAE5B8nB,8BAAoBsB,gBAAgBrV,IAAIlQ,IAAIkV,OAAO5V,KAAK;AAExD,iBAAOimB;QACT,CAAC;;MAEHR,eAAeA,MAAM;AACnB,cAAM;UAAE7B;QAAa,IAAI5jB,MAAM8D,SAAQ;AACvC,eAAOoiB,cAActV,KAAKgT,YAAY;;MAGxC8B,mBAAmBA,MAAM;AACvB,cAAM;UAAE9B;QAAa,IAAI5jB,MAAM8D,SAAQ;AACvC,eAAOqiB,iBAAiBvV,KAAKgT,YAAmB,MAAM;;MAGxDwC,yBAAyBA,MAAM;AAC7B,cAAM;UAAExC;QAAa,IAAI5jB,MAAM8D,SAAQ;AACvC,eAAOqiB,iBAAiBvV,KAAKgT,YAAmB,MAAM;;MAGxDW,cAAcA,MAAM;AAAA,YAAA/Y;AAClB,YAAI,OAAOxL,MAAM4B,QAAQkiB,uBAAuB,YAAY;AAC1D,iBAAO9jB,MAAM4B,QAAQkiB,mBAAmBlT,GAAG;QAC7C;AAEA,gBAAApF,wBAAOxL,MAAM4B,QAAQkiB,uBAAkB,OAAAtY,wBAAI;;MAG7C6a,qBAAqBA,MAAM;AAAA,YAAAhQ;AACzB,YAAI,OAAOrW,MAAM4B,QAAQoiB,0BAA0B,YAAY;AAC7D,iBAAOhkB,MAAM4B,QAAQoiB,sBAAsBpT,GAAG;QAChD;AAEA,gBAAAyF,yBAAOrW,MAAM4B,QAAQoiB,0BAAqB,OAAA3N,yBAAI;;MAGhDiQ,mBAAmBA,MAAM;AAAA,YAAA3P;AACvB,YAAI,OAAO3W,MAAM4B,QAAQmiB,4BAA4B,YAAY;AAC/D,iBAAO/jB,MAAM4B,QAAQmiB,wBAAwBnT,GAAG;QAClD;AAEA,gBAAA+F,yBAAO3W,MAAM4B,QAAQmiB,4BAAuB,OAAApN,yBAAI;;MAElD4P,0BAA0BA,MAAM;AAC9B,cAAMC,YAAY5V,IAAI2T,aAAY;AAElC,eAAQrY,OAAe;AAAA,cAAAua;AACrB,cAAI,CAACD;AAAW;AAChB5V,cAAImV,gBAAcU,UACdva,EAAiB0Z,WAAnBa,OAAAA,SAAAA,QAAgDZ,OAClD;;MAEJ;;EAEJ;AACF;AAEA,IAAMlB,sBAAsBA,CAC1BsB,gBACAvlB,IACAkV,OACA5V,UACG;AAAA,MAAAqS;AACH,QAAMzB,MAAM5Q,MAAM0mB,OAAOhmB,EAAE;AAQ3B,MAAIkV,OAAO;AACT,QAAI,CAAChF,IAAI0V,kBAAiB,GAAI;AAC5B7jB,aAAOwO,KAAKgV,cAAc,EAAEroB,QAAQlB,SAAO,OAAOupB,eAAevpB,GAAG,CAAC;IACvE;AACA,QAAIkU,IAAI2T,aAAY,GAAI;AACtB0B,qBAAevlB,EAAE,IAAI;IACvB;EACF,OAAO;AACL,WAAOulB,eAAevlB,EAAE;EAC1B;AAGA,OAAI2R,eAAAzB,IAAI4B,YAAJH,QAAAA,aAAarU,UAAU4S,IAAIyV,oBAAmB,GAAI;AACpDzV,QAAI4B,QAAQ5U,QAAQgT,CAAAA,SAClB+T,oBAAoBsB,gBAAgBrV,KAAIlQ,IAAIkV,OAAO5V,KAAK,CAC1D;EACF;AACF;AAEO,SAAS+kB,aACd/kB,OACA8kB,UACiB;AACjB,QAAMlB,eAAe5jB,MAAM8D,SAAQ,EAAG8f;AAEtC,QAAM+C,sBAAoC,CAAA;AAC1C,QAAMC,sBAAkD,CAAA;AAGxD,QAAMC,cAAc,SAACxF,MAAoBnhB,OAA4B;AACnE,WAAOmhB,KACJ9d,IAAIqN,SAAO;AAAA,UAAAkW;AACV,YAAMd,aAAaE,cAActV,KAAKgT,YAAY;AAElD,UAAIoC,YAAY;AACdW,4BAAoB7oB,KAAK8S,GAAG;AAC5BgW,4BAAoBhW,IAAIlQ,EAAE,IAAIkQ;MAChC;AAEA,WAAAkW,gBAAIlW,IAAI4B,YAAJsU,QAAAA,cAAa9oB,QAAQ;AACvB4S,cAAM;UACJ,GAAGA;UACH4B,SAASqU,YAAYjW,IAAI4B,OAAkB;;MAE/C;AAEA,UAAIwT,YAAY;AACd,eAAOpV;MACT;IACF,CAAC,EACApM,OAAOC,OAAO;;AAGnB,SAAO;IACL4c,MAAMwF,YAAY/B,SAASzD,IAAI;IAC/B1Q,UAAUgW;IACVrV,UAAUsV;;AAEd;AAEO,SAASV,cACdtV,KACAmW,WACS;AAAA,MAAAC;AACT,UAAAA,oBAAOD,UAAUnW,IAAIlQ,EAAE,MAAC,OAAAsmB,oBAAI;AAC9B;AAEO,SAASb,iBACdvV,KACAmW,WACA/mB,OAC0B;AAC1B,MAAI4Q,IAAI4B,WAAW5B,IAAI4B,QAAQxU,QAAQ;AACrC,QAAIipB,sBAAsB;AAC1B,QAAIC,eAAe;AAEnBtW,QAAI4B,QAAQ5U,QAAQupB,YAAU;AAE5B,UAAID,gBAAgB,CAACD,qBAAqB;AACxC;MACF;AAEA,UAAIf,cAAciB,QAAQJ,SAAS,GAAG;AACpCG,uBAAe;MACjB,OAAO;AACLD,8BAAsB;MACxB;IACF,CAAC;AAED,WAAOA,sBAAsB,QAAQC,eAAe,SAAS;EAC/D;AAEA,SAAO;AACT;AC3gBO,IAAME,sBAAsB;AAEnC,IAAMC,eAA+BA,CAACC,MAAMC,MAAMjjB,aAAa;AAC7D,SAAOkjB,oBACLrU,SAASmU,KAAKpU,SAAS5O,QAAQ,CAAC,EAAE2O,YAAW,GAC7CE,SAASoU,KAAKrU,SAAS5O,QAAQ,CAAC,EAAE2O,YAAW,CAC/C;AACF;AAEA,IAAMwU,4BAA4CA,CAACH,MAAMC,MAAMjjB,aAAa;AAC1E,SAAOkjB,oBACLrU,SAASmU,KAAKpU,SAAS5O,QAAQ,CAAC,GAChC6O,SAASoU,KAAKrU,SAAS5O,QAAQ,CAAC,CAClC;AACF;AAIA,IAAMojB,OAAuBA,CAACJ,MAAMC,MAAMjjB,aAAa;AACrD,SAAOqjB,aACLxU,SAASmU,KAAKpU,SAAS5O,QAAQ,CAAC,EAAE2O,YAAW,GAC7CE,SAASoU,KAAKrU,SAAS5O,QAAQ,CAAC,EAAE2O,YAAW,CAC/C;AACF;AAIA,IAAM2U,oBAAoCA,CAACN,MAAMC,MAAMjjB,aAAa;AAClE,SAAOqjB,aACLxU,SAASmU,KAAKpU,SAAS5O,QAAQ,CAAC,GAChC6O,SAASoU,KAAKrU,SAAS5O,QAAQ,CAAC,CAClC;AACF;AAEA,IAAMujB,WAA2BA,CAACP,MAAMC,MAAMjjB,aAAa;AACzD,QAAMiW,IAAI+M,KAAKpU,SAAe5O,QAAQ;AACtC,QAAMkW,IAAI+M,KAAKrU,SAAe5O,QAAQ;AAKtC,SAAOiW,IAAIC,IAAI,IAAID,IAAIC,IAAI,KAAK;AAClC;AAEA,IAAMsN,QAAwBA,CAACR,MAAMC,MAAMjjB,aAAa;AACtD,SAAOqjB,aAAaL,KAAKpU,SAAS5O,QAAQ,GAAGijB,KAAKrU,SAAS5O,QAAQ,CAAC;AACtE;AAIA,SAASqjB,aAAapN,GAAQC,GAAQ;AACpC,SAAOD,MAAMC,IAAI,IAAID,IAAIC,IAAI,IAAI;AACnC;AAEA,SAASrH,SAASoH,GAAQ;AACxB,MAAI,OAAOA,MAAM,UAAU;AACzB,QAAI3F,MAAM2F,CAAC,KAAKA,MAAM1F,YAAY0F,MAAM,WAAW;AACjD,aAAO;IACT;AACA,WAAO7a,OAAO6a,CAAC;EACjB;AACA,MAAI,OAAOA,MAAM,UAAU;AACzB,WAAOA;EACT;AACA,SAAO;AACT;AAKA,SAASiN,oBAAoBO,MAAcC,MAAc;AAGvD,QAAMzN,IAAIwN,KAAK9mB,MAAMmmB,mBAAmB,EAAE5iB,OAAOC,OAAO;AACxD,QAAM+V,IAAIwN,KAAK/mB,MAAMmmB,mBAAmB,EAAE5iB,OAAOC,OAAO;AAGxD,SAAO8V,EAAEvc,UAAUwc,EAAExc,QAAQ;AAC3B,UAAMiqB,KAAK1N,EAAE8D,MAAK;AAClB,UAAM6J,KAAK1N,EAAE6D,MAAK;AAElB,UAAM8J,KAAKC,SAASH,IAAI,EAAE;AAC1B,UAAMI,KAAKD,SAASF,IAAI,EAAE;AAE1B,UAAMI,QAAQ,CAACH,IAAIE,EAAE,EAAE/N,KAAI;AAG3B,QAAI1F,MAAM0T,MAAM,CAAC,CAAE,GAAG;AACpB,UAAIL,KAAKC,IAAI;AACX,eAAO;MACT;AACA,UAAIA,KAAKD,IAAI;AACX,eAAO;MACT;AACA;IACF;AAGA,QAAIrT,MAAM0T,MAAM,CAAC,CAAE,GAAG;AACpB,aAAO1T,MAAMuT,EAAE,IAAI,KAAK;IAC1B;AAGA,QAAIA,KAAKE,IAAI;AACX,aAAO;IACT;AACA,QAAIA,KAAKF,IAAI;AACX,aAAO;IACT;EACF;AAEA,SAAO5N,EAAEvc,SAASwc,EAAExc;AACtB;AAIO,IAAMuqB,aAAa;EACxBlB;EACAI;EACAC;EACAE;EACAC;EACAC;AACF;AClBO,IAAMU,UAAwB;EACnCze,iBAAkBC,WAA6B;AAC7C,WAAO;MACLye,SAAS,CAAA;MACT,GAAGze;;;EAIPF,qBAAqBA,MAAsD;AACzE,WAAO;MACL4e,WAAW;;;EAIfve,mBACEnK,WAC0B;AAC1B,WAAO;MACL2oB,iBAAiBlsB,iBAAiB,WAAWuD,KAAK;MAClD4oB,kBAAmB1c,OAAe;AAChC,eAAQA,EAAiB2c;MAC3B;;;EAIJ9oB,cAAcA,CACZuB,QACAtB,UACyB;AACzB,WAAO;MACL8oB,kBAAkBA,MAAM;AACtB,cAAMC,YAAY/oB,MAAMiZ,oBAAmB,EAAGtI,SAASqY,MAAM,EAAE;AAE/D,YAAIC,WAAW;AAEf,mBAAWrY,OAAOmY,WAAW;AAC3B,gBAAMnT,QAAQhF,OAAAA,OAAAA,SAAAA,IAAKsC,SAAS5R,OAAOZ,EAAE;AAErC,cAAI+B,OAAO2Z,UAAUjJ,SAASkJ,KAAKzG,KAAK,MAAM,iBAAiB;AAC7D,mBAAO2S,WAAWV;UACpB;AAEA,cAAI,OAAOjS,UAAU,UAAU;AAC7BqT,uBAAW;AAEX,gBAAIrT,MAAM3U,MAAMmmB,mBAAmB,EAAEppB,SAAS,GAAG;AAC/C,qBAAOuqB,WAAWlB;YACpB;UACF;QACF;AAEA,YAAI4B,UAAU;AACZ,iBAAOV,WAAWb;QACpB;AAEA,eAAOa,WAAWT;;MAEpBoB,gBAAgBA,MAAM;AACpB,cAAMlT,WAAWhW,MAAMiZ,oBAAmB,EAAGtI,SAAS,CAAC;AAEvD,cAAMiF,QAAQI,YAAAA,OAAAA,SAAAA,SAAU9C,SAAS5R,OAAOZ,EAAE;AAE1C,YAAI,OAAOkV,UAAU,UAAU;AAC7B,iBAAO;QACT;AAEA,eAAO;;MAETuT,cAAcA,MAAM;AAAA,YAAAC,uBAAAC;AAClB,YAAI,CAAC/nB,QAAQ;AACX,gBAAM,IAAID,MAAK;QACjB;AAEA,eAAOvE,WAAWwE,OAAOrB,UAAUyoB,SAAS,IACxCpnB,OAAOrB,UAAUyoB,YACjBpnB,OAAOrB,UAAUyoB,cAAc,SAC/BpnB,OAAOwnB,iBAAgB,KAAEM,yBAAAC,yBACzBrpB,MAAM4B,QAAQ2mB,eAAU,OAAA,SAAxBc,uBAA2B/nB,OAAOrB,UAAUyoB,SAAS,MAAWU,OAAAA,wBAChEb,WAAWjnB,OAAOrB,UAAUyoB,SAAS;;MAE3CY,eAAeA,CAACC,MAAMC,UAAU;AAW9B,cAAMC,mBAAmBnoB,OAAOooB,oBAAmB;AACnD,cAAMC,iBAAiB,OAAOJ,SAAS,eAAeA,SAAS;AAE/DvpB,cAAM4pB,WAAW/sB,SAAO;AAEtB,gBAAMgtB,kBAAkBhtB,OAAAA,OAAAA,SAAAA,IAAK0H,KAAKxH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;AACzD,gBAAMopB,gBAAgBjtB,OAAAA,OAAAA,SAAAA,IAAKkO,UAAUhO,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;AAE5D,cAAIqpB,aAA2B,CAAA;AAG/B,cAAIC;AACJ,cAAIC,WAAWN,iBAAiBJ,OAAOE,qBAAqB;AAG5D,cAAI5sB,OAAG,QAAHA,IAAKmB,UAAUsD,OAAO4oB,gBAAe,KAAMV,OAAO;AACpD,gBAAIK,iBAAiB;AACnBG,2BAAa;YACf,OAAO;AACLA,2BAAa;YACf;UACF,OAAO;AAEL,gBAAIntB,OAAG,QAAHA,IAAKmB,UAAU8rB,kBAAkBjtB,IAAImB,SAAS,GAAG;AACnDgsB,2BAAa;uBACJH,iBAAiB;AAC1BG,2BAAa;YACf,OAAO;AACLA,2BAAa;YACf;UACF;AAGA,cAAIA,eAAe,UAAU;AAE3B,gBAAI,CAACL,gBAAgB;AAEnB,kBAAI,CAACF,kBAAkB;AACrBO,6BAAa;cACf;YACF;UACF;AAEA,cAAIA,eAAe,OAAO;AAAA,gBAAAG;AACxBJ,yBAAa,CACX,GAAGltB,KACH;cACE6D,IAAIY,OAAOZ;cACX6oB,MAAMU;YACR,CAAC;AAGHF,uBAAWxL,OACT,GACAwL,WAAW/rB,WAAMmsB,wBACdnqB,MAAM4B,QAAQwoB,yBAAoBD,OAAAA,wBAAI/gB,OAAOC,iBAClD;UACF,WAAW2gB,eAAe,UAAU;AAElCD,yBAAaltB,IAAI0G,IAAIxG,OAAK;AACxB,kBAAIA,EAAE2D,OAAOY,OAAOZ,IAAI;AACtB,uBAAO;kBACL,GAAG3D;kBACHwsB,MAAMU;;cAEV;AACA,qBAAOltB;YACT,CAAC;UACH,WAAWitB,eAAe,UAAU;AAClCD,yBAAaltB,IAAI2H,OAAOzH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;UACjD,OAAO;AACLqpB,yBAAa,CACX;cACErpB,IAAIY,OAAOZ;cACX6oB,MAAMU;YACR,CAAC;UAEL;AAEA,iBAAOF;QACT,CAAC;;MAGHM,iBAAiBA,MAAM;AAAA,YAAAjqB,MAAAkqB;AACrB,cAAMC,iBAAanqB,QAAAkqB,wBACjBhpB,OAAOrB,UAAUsqB,kBAAa,OAAAD,wBAC9BtqB,MAAM4B,QAAQ2oB,kBAAa,OAAAnqB,OAC3BkB,OAAO4nB,eAAc,MAAO;AAC9B,eAAOqB,gBAAgB,SAAS;;MAGlCb,qBAAsBF,WAAoB;AAAA,YAAAhe,uBAAA6K;AACxC,cAAMmU,qBAAqBlpB,OAAO+oB,gBAAe;AACjD,cAAMI,WAAWnpB,OAAOopB,YAAW;AAEnC,YAAI,CAACD,UAAU;AACb,iBAAOD;QACT;AAEA,YACEC,aAAaD,wBAAkBhf,wBAC9BxL,MAAM4B,QAAQ+oB,yBAAoB,OAAAnf,wBAAI;SACtCge,SAAKnT,yBAAGrW,MAAM4B,QAAQgpB,sBAAiB,OAAAvU,yBAAI,OAAO,OACnD;AACA,iBAAO;QACT;AACA,eAAOoU,aAAa,SAAS,QAAQ;;MAGvCI,YAAYA,MAAM;AAAA,YAAAtf,uBAAAoL;AAChB,iBACEpL,wBAACjK,OAAOrB,UAAU6qB,kBAAavf,OAAAA,wBAAI,WAAIoL,yBACtC3W,MAAM4B,QAAQkpB,kBAAa,OAAAnU,yBAAI,SAChC,CAAC,CAACrV,OAAOR;;MAIbopB,iBAAiBA,MAAM;AAAA,YAAA/e,OAAAuL;AACrB,gBAAAvL,SAAAuL,yBACEpV,OAAOrB,UAAU8qB,oBAAe,OAAArU,yBAChC1W,MAAM4B,QAAQmpB,oBAAe5f,OAAAA,QAC7B,CAAC,CAAC7J,OAAOR;;MAIb4pB,aAAaA,MAAM;AAAA,YAAAM;AACjB,cAAMC,cAAUD,wBAAGhrB,MAChB8D,SAAQ,EACR2kB,YAAO,OAAA,SAFSuC,sBAEPzmB,KAAKxH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;AAExC,eAAO,CAACuqB,aAAa,QAAQA,WAAW1B,OAAO,SAAS;;MAG1D2B,cAAcA,MAAA;AAAA,YAAAC,wBAAAC;AAAA,gBAAAD,0BAAAC,yBACZprB,MAAM8D,SAAQ,EAAG2kB,YAAO,OAAA,SAAxB2C,uBAA0BrgB,UAAUhO,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE,MAAC,OAAAyqB,yBAAI;MAAE;MAEpEE,cAAcA,MAAM;AAElBrrB,cAAM4pB,WAAW/sB,SACfA,OAAG,QAAHA,IAAKmB,SAASnB,IAAI2H,OAAOzH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE,IAAI,CAAA,CACtD;;MAGF4qB,yBAAyBA,MAAM;AAC7B,cAAMC,UAAUjqB,OAAOupB,WAAU;AAEjC,eAAQ3e,OAAe;AACrB,cAAI,CAACqf;AAAS;AACZrf,YAAUC,WAAO,OAAA,SAAjBD,EAAUC,QAAO;AACnB7K,iBAAOgoB,iBAAPhoB,OAAAA,SAAAA,OAAOgoB,cACL1oB,QACAU,OAAO4oB,gBAAe,IAClBlqB,MAAM4B,QAAQgnB,oBAAgB,OAAA,SAA9B5oB,MAAM4B,QAAQgnB,iBAAmB1c,CAAC,IAClC,KACN;;MAEJ;;;EAIJxI,aACE1D,WAC2B;AAC3B,WAAO;MACL4pB,YAAYttB,aAAW0D,MAAM4B,QAAQ+mB,mBAAd3oB,OAAAA,SAAAA,MAAM4B,QAAQ+mB,gBAAkBrsB,OAAO;MAC9DkvB,cAAcvd,kBAAgB;AAAA,YAAAwd,uBAAAjb;AAC5BxQ,cAAM4pB,WAAW3b,eAAe,CAAA,KAAEwd,yBAAAjb,sBAAGxQ,MAAMmO,iBAAY,OAAA,SAAlBqC,oBAAoBiY,YAAOgD,OAAAA,wBAAI,CAAA,CAAE;;MAExEC,sBAAsBA,MAAM1rB,MAAM4c,mBAAkB;MACpDnL,mBAAmBA,MAAM;AACvB,YAAI,CAACzR,MAAM2rB,sBAAsB3rB,MAAM4B,QAAQ6P,mBAAmB;AAChEzR,gBAAM2rB,qBAAqB3rB,MAAM4B,QAAQ6P,kBAAkBzR,KAAK;QAClE;AAEA,YAAIA,MAAM4B,QAAQgqB,iBAAiB,CAAC5rB,MAAM2rB,oBAAoB;AAC5D,iBAAO3rB,MAAM0rB,qBAAoB;QACnC;AAEA,eAAO1rB,MAAM2rB,mBAAkB;MACjC;;EAEJ;AACF;ACjUO,IAAME,aAA2B;EACtC9hB,iBAAkBC,WAAgC;AAChD,WAAO;MACL8hB,kBAAkB,CAAA;MAClB,GAAG9hB;;;EAIPG,mBACEnK,WAC6B;AAC7B,WAAO;MACL+rB,0BAA0BtvB,iBAAiB,oBAAoBuD,KAAK;;;EAIxED,cAAcA,CACZuB,QACAtB,UACqB;AACrB,WAAO;MACLgsB,kBAAkBpW,WAAS;AACzB,YAAItU,OAAO2qB,WAAU,GAAI;AACvBjsB,gBAAMksB,oBAAoBrvB,UAAQ;YAChC,GAAGA;YACH,CAACyE,OAAOZ,EAAE,GAAGkV,SAAK,OAALA,QAAS,CAACtU,OAAO2G,aAAY;UAC5C,EAAE;QACJ;;MAEFA,cAAcA,MAAM;AAAA,YAAAiP,uBAAAC;AAClB,gBAAAD,yBAAAC,yBAAOnX,MAAM8D,SAAQ,EAAGgoB,qBAAjB3U,OAAAA,SAAAA,uBAAoC7V,OAAOZ,EAAE,MAAC,OAAAwW,wBAAI;;MAG3D+U,YAAYA,MAAM;AAAA,YAAA1gB,uBAAAC;AAChB,iBACED,wBAACjK,OAAOrB,UAAUksB,iBAAY5gB,OAAAA,wBAAI,WAAIC,wBACrCxL,MAAM4B,QAAQuqB,iBAAY3gB,OAAAA,wBAAI;;MAGnC4gB,4BAA4BA,MAAM;AAChC,eAAQlgB,OAAe;AACrB5K,iBAAO0qB,oBAAP1qB,OAAAA,SAAAA,OAAO0qB,iBACH9f,EAAiB0Z,OAA4BC,OACjD;;MAEJ;;;EAIJjU,WAAWA,CACThB,KACA5Q,UACyB;AACzB,WAAO;MACL2iB,qBAAqB1kB,KACnB,MAAM,CAAC2S,IAAIyb,YAAW,GAAIrsB,MAAM8D,SAAQ,EAAGgoB,gBAAgB,GAC3D9I,WAAS;AACP,eAAOA,MAAMxe,OAAO6Y,UAAQA,KAAK/b,OAAO2G,aAAY,CAAE;MACxD,GACA;QACEvL,KACE0E;QACF5C,OAAOA,MAAA;AAAA,cAAAmD;AAAA,kBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQkhB;QAAS;MAChE,CACF;MACAwJ,iBAAiBruB,KACf,MAAM,CACJ2S,IAAImS,oBAAmB,GACvBnS,IAAI8R,sBAAqB,GACzB9R,IAAIqS,qBAAoB,CAAE,GAE5B,CAACjf,MAAMmD,QAAQlD,UAAU,CAAC,GAAGD,MAAM,GAAGmD,QAAQ,GAAGlD,KAAK,GACtD;QACEvH,KAA+C;QAC/C8B,OAAOA,MAAA;AAAA,cAAA4D;AAAA,kBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQkhB;QAAS;OAElE;;;EAIJpf,aACE1D,WAC8B;AAC9B,UAAMusB,2BAA2BA,CAC/B7vB,KACA8vB,eACqC;AACrC,aAAOvuB,KACL,MAAM,CACJuuB,WAAU,GACVA,WAAU,EACPhoB,OAAOzH,OAAKA,EAAEkL,aAAY,CAAE,EAC5B1E,IAAIxG,OAAKA,EAAE2D,EAAE,EACb0H,KAAK,GAAG,CAAC,GAEd7G,aAAW;AACT,eAAOA,QAAQiD,OAAOzH,OAAKA,EAAEkL,gBAAY,OAAA,SAAdlL,EAAEkL,aAAY,CAAI;MAC/C,GACA;QACEvL;QACA8B,OAAOA,MAAA;AAAA,cAAA2G;AAAA,kBAAAA,yBAAMnF,MAAM4B,QAAQC,aAAQsD,OAAAA,yBAAInF,MAAM4B,QAAQE;QAAY;MACnE,CACF;;AAGF,WAAO;MACL2qB,uBAAuBF,yBACrB,yBACA,MAAMvsB,MAAM0sB,kBAAiB,CAC/B;MACA7oB,uBAAuB0oB,yBACrB,yBACA,MAAMvsB,MAAM2Y,kBAAiB,CAC/B;MACA9N,2BAA2B0hB,yBACzB,6BACA,MAAMvsB,MAAMwjB,mBAAkB,CAChC;MACA1Y,4BAA4ByhB,yBAC1B,8BACA,MAAMvsB,MAAMyjB,oBAAmB,CACjC;MACAkJ,6BAA6BJ,yBAC3B,+BACA,MAAMvsB,MAAM0jB,qBAAoB,CAClC;MAEAwI,qBAAqB5vB,aACnB0D,MAAM4B,QAAQmqB,4BAAd/rB,OAAAA,SAAAA,MAAM4B,QAAQmqB,yBAA2BzvB,OAAO;MAElDswB,uBAAuB3e,kBAAgB;AAAA,YAAAC;AACrClO,cAAMksB,oBACJje,eAAe,CAAA,KAAEC,wBAAGlO,MAAMmO,aAAa2d,qBAAgB,OAAA5d,wBAAI,CAAA,CAC7D;;MAGF2e,yBAAyBjX,WAAS;AAAA,YAAAkX;AAChClX,iBAAKkX,SAAGlX,UAAKkX,OAAAA,SAAI,CAAC9sB,MAAM+sB,uBAAsB;AAE9C/sB,cAAMksB,oBACJlsB,MAAM2Y,kBAAiB,EAAGrW,OACxB,CAACC,KAAKjB,YAAY;UAChB,GAAGiB;UACH,CAACjB,OAAOZ,EAAE,GAAG,CAACkV,QAAQ,EAACtU,OAAO2qB,cAAP3qB,QAAAA,OAAO2qB,WAAU,KAAOrW;QACjD,IACA,CAAA,CACF,CACF;;MAGFmX,wBAAwBA,MACtB,CAAC/sB,MAAM2Y,kBAAiB,EAAG9Z,KAAKyC,YAAU,EAACA,OAAO2G,gBAAY,QAAnB3G,OAAO2G,aAAY,EAAK;MAErE+kB,yBAAyBA,MACvBhtB,MAAM2Y,kBAAiB,EAAG9Z,KAAKyC,YAAUA,OAAO2G,gBAAY,OAAA,SAAnB3G,OAAO2G,aAAY,CAAI;MAElEglB,sCAAsCA,MAAM;AAC1C,eAAQ/gB,OAAe;AAAA,cAAAua;AACrBzmB,gBAAM6sB,yBAAuBpG,UACzBva,EAAiB0Z,WAAnBa,OAAAA,SAAAA,QAAgDZ,OAClD;;MAEJ;;EAEJ;AACF;AClLA,IAAMqH,WAAW,CACfzpB,SACAooB,YACAjO,UACA2D,SACAvM,SACAwT,SACAzN,UACAxL,WACAsP,YACA8E,cACA9Z,YAAY;AAsDP,SAASnG,YACd9B,SACc;AAAA,MAAAurB;AACd,MAAIvrB,QAAQC,YAAYD,QAAQgf,YAAY;AAC1CjhB,YAAQC,KAAK,4BAA4B;EAC3C;AAEA,MAAII,QAAQ;IAAEqC,WAAW6qB;;AAEzB,QAAME,iBAAiBptB,MAAMqC,UAAUC,OAAO,CAACC,KAAKC,YAAY;AAC9D,WAAOC,OAAOC,OAAOH,KAAKC,QAAQ2H,qBAAiB,OAAA,SAAzB3H,QAAQ2H,kBAAoBnK,KAAK,CAAC;KAC3D,CAAA,CAAE;AAEL,QAAMqtB,eAAgBzrB,CAAAA,aAAyC;AAC7D,QAAI5B,MAAM4B,QAAQyrB,cAAc;AAC9B,aAAOrtB,MAAM4B,QAAQyrB,aAAaD,gBAAgBxrB,QAAO;IAC3D;AAEA,WAAO;MACL,GAAGwrB;MACH,GAAGxrB;;;AAIP,QAAM0rB,mBAAmC,CAAA;AAEzC,MAAInf,eAAe;IACjB,GAAGmf;IACH,IAAAH,wBAAIvrB,QAAQuM,iBAAYgf,OAAAA,wBAAI,CAAA;;AAG9BntB,QAAMqC,UAAUzE,QAAQ4E,aAAW;AAAA,QAAA+qB;AACjCpf,oBAAYof,wBAAG/qB,QAAQuH,mBAARvH,OAAAA,SAAAA,QAAQuH,gBAAkBoE,YAAY,MAACof,OAAAA,wBAAIpf;EAC5D,CAAC;AAED,QAAMyB,SAAyB,CAAA;AAC/B,MAAI4d,gBAAgB;AAEpB,QAAMC,eAAoC;IACxCprB,WAAW6qB;IACXtrB,SAAS;MACP,GAAGwrB;MACH,GAAGxrB;;IAELuM;IACA4B,QAAQ2d,QAAM;AACZ9d,aAAO9R,KAAK4vB,EAAE;AAEd,UAAI,CAACF,eAAe;AAClBA,wBAAgB;AAIhBG,gBAAQC,QAAO,EACZC,KAAK,MAAM;AACV,iBAAOje,OAAO5R,QAAQ;AACpB4R,mBAAOyO,MAAK,EAAE;UAChB;AACAmP,0BAAgB;SACjB,EACAM,MAAMC,WACLC,WAAW,MAAM;AACf,gBAAMD;QACR,CAAC,CACH;MACJ;;IAEFE,OAAOA,MAAM;AACXjuB,YAAMpD,SAASoD,MAAMmO,YAAY;;IAEnC+f,YAAY5xB,aAAW;AACrB,YAAM6xB,aAAa9xB,iBAAiBC,SAAS0D,MAAM4B,OAAO;AAC1D5B,YAAM4B,UAAUyrB,aAAac,UAAU;;IAMzCrqB,UAAUA,MAAM;AACd,aAAO9D,MAAM4B,QAAQoI;;IAGvBpN,UAAWN,aAAiC;AAC1C0D,YAAM4B,QAAQwsB,iBAAdpuB,OAAAA,SAAAA,MAAM4B,QAAQwsB,cAAgB9xB,OAAO;;IAGvC+xB,WAAWA,CAACzd,KAAY7R,OAAeoB,WAAmB;AAAA,UAAAiS;AAAA,cAAAA,wBACxDpS,MAAM4B,QAAQ0sB,YAAdtuB,OAAAA,SAAAA,MAAM4B,QAAQ0sB,SAAW1d,KAAK7R,OAAOoB,MAAM,MAACiS,OAAAA,wBAC3C,GAAEjS,SAAS,CAACA,OAAOO,IAAI3B,KAAK,EAAEqJ,KAAK,GAAG,IAAIrJ;IAAO;IAEpD8W,iBAAiBA,MAAM;AACrB,UAAI,CAAC7V,MAAMuuB,kBAAkB;AAC3BvuB,cAAMuuB,mBAAmBvuB,MAAM4B,QAAQiU,gBAAgB7V,KAAK;MAC9D;AAEA,aAAOA,MAAMuuB,iBAAgB;;;;IAM/Brd,aAAaA,MAAM;AACjB,aAAOlR,MAAMihB,sBAAqB;;IAEpCyF,QAAShmB,QAAe;AACtB,YAAMkQ,MAAM5Q,MAAMkR,YAAW,EAAGI,SAAS5Q,EAAE;AAE3C,UAAI,CAACkQ,KAAK;AACR,YAAIxP,MAAuC;AACzC,gBAAM,IAAIC,MAAO,kCAAiCX,IAAI;QACxD;AACA,cAAM,IAAIW,MAAK;MACjB;AAEA,aAAOuP;;IAETrQ,sBAAsBtC,KACpB,MAAM,CAAC+B,MAAM4B,QAAQtB,aAAa,GAClCA,mBAAiB;AAAA,UAAAkuB;AACfluB,uBAAakuB,iBAAIluB,kBAAa,OAAAkuB,iBAAI,CAAA;AAIlC,aAAO;QACL3tB,QAAQoa,WAAS;AACf,gBAAMza,oBAAoBya,MAAMpa,OAAOS,OACpCrB;AAEH,cAAIO,kBAAkBC,aAAa;AACjC,mBAAOD,kBAAkBC;UAC3B;AAEA,cAAID,kBAAkBM,YAAY;AAChC,mBAAON,kBAAkBE;UAC3B;AAEA,iBAAO;;;QAGT2c,MAAMpC,WAAK;AAAA,cAAAwT,uBAAAC;AAAA,kBAAAD,yBAAAC,qBAAIzT,MAAM0T,YAAW,MAAjBD,OAAAA,SAAAA,mBAA0Bvb,YAAQ,OAAA,SAAlCub,mBAA0Bvb,SAAQ,MAAI,OAAAsb,wBAAI;QAAI;QAC7D,GAAGzuB,MAAMqC,UAAUC,OAAO,CAACC,KAAKC,YAAY;AAC1C,iBAAOC,OAAOC,OAAOH,KAAKC,QAAQsH,uBAAmB,OAAA,SAA3BtH,QAAQsH,oBAAmB,CAAI;WACxD,CAAA,CAAE;QACL,GAAGxJ;;IAEP,GACA;MACE9B,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQE;MAAY;MACjEpF,KAA+C;IACjD,CACF;IAEAkyB,gBAAgBA,MAAM5uB,MAAM4B,QAAQL;IAEpCqC,eAAe3F,KACb,MAAM,CAAC+B,MAAM4uB,eAAc,CAAE,GAC7BC,gBAAc;AACZ,YAAMC,iBAAiB,SACrBD,aACA1uB,QACAD,OAC6B;AAAA,YAD7BA,UAAK,QAAA;AAALA,kBAAQ;QAAC;AAET,eAAO2uB,YAAWtrB,IAAItD,eAAa;AACjC,gBAAMqB,SAASvB,aAAaC,OAAOC,WAAWC,OAAOC,MAAM;AAE3D,gBAAM4uB,oBAAoB9uB;AAK1BqB,iBAAOC,UAAUwtB,kBAAkBxtB,UAC/ButB,eAAeC,kBAAkBxtB,SAASD,QAAQpB,QAAQ,CAAC,IAC3D,CAAA;AAEJ,iBAAOoB;QACT,CAAC;;AAGH,aAAOwtB,eAAeD,UAAU;IAClC,GACA;MACEnyB,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA4D;AAAA,gBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQE;MAAY;IACnE,CACF;IAEA4qB,mBAAmBzuB,KACjB,MAAM,CAAC+B,MAAM4D,cAAa,CAAE,GAC5BM,gBAAc;AACZ,aAAOA,WAAWxC,QAAQJ,YAAU;AAClC,eAAOA,OAAOE,eAAc;MAC9B,CAAC;IACH,GACA;MACE9E,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA2G;AAAA,gBAAAA,yBAAMnF,MAAM4B,QAAQC,aAAQsD,OAAAA,yBAAInF,MAAM4B,QAAQE;MAAY;IACnE,CACF;IAEAktB,wBAAwB/wB,KACtB,MAAM,CAAC+B,MAAM0sB,kBAAiB,CAAE,GAChCuC,iBAAe;AACb,aAAOA,YAAY3sB,OAAO,CAAC4sB,KAAK5tB,WAAW;AACzC4tB,YAAI5tB,OAAOZ,EAAE,IAAIY;AACjB,eAAO4tB;SACN,CAAA,CAA4C;IACjD,GACA;MACExyB,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA8G;AAAA,gBAAAA,yBAAMtF,MAAM4B,QAAQC,aAAQyD,OAAAA,yBAAItF,MAAM4B,QAAQE;MAAY;IACnE,CACF;IAEA6W,mBAAmB1a,KACjB,MAAM,CAAC+B,MAAM4D,cAAa,GAAI5D,MAAMgC,mBAAkB,CAAE,GACxD,CAACkC,YAAYjC,kBAAiB;AAC5B,UAAIE,cAAc+B,WAAWxC,QAAQJ,YAAUA,OAAOS,eAAc,CAAE;AACtE,aAAOE,cAAaE,WAAW;IACjC,GACA;MACEzF,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAiH;AAAA,gBAAAA,yBAAMzF,MAAM4B,QAAQC,aAAQ4D,OAAAA,yBAAIzF,MAAM4B,QAAQE;MAAY;IACnE,CACF;IAEAkK,WAAW1H,cAAY;AACrB,YAAMhD,SAAStB,MAAMgvB,uBAAsB,EAAG1qB,QAAQ;AAEtD,UAA6C,CAAChD,QAAQ;AACpD3B,gBAAQouB,MAAO,2BAA0BzpB,2BAA2B;MACtE;AAEA,aAAOhD;IACT;;AAGFmB,SAAOC,OAAO1C,OAAOytB,YAAY;AAEjCztB,QAAMqC,UAAUzE,QAAQ4E,aAAW;AACjC,WAAOC,OAAOC,OAAO1C,OAAOwC,QAAQkB,eAAW,OAAA,SAAnBlB,QAAQkB,YAAc1D,KAAK,CAAC;EAC1D,CAAC;AAED,SAAOA;AACT;AC7UO,SAASod,WACdpd,OACA4Q,KACAtP,QACAgD,UACqB;AACrB,QAAM6qB,iBAAiBA,MAAA;AAAA,QAAAC;AAAA,YAAAA,iBACrB/R,KAAKnK,SAAQ,MAAEkc,OAAAA,iBAAIpvB,MAAM4B,QAAQytB;EAAmB;AAEtD,QAAMhS,OAAgC;IACpC3c,IAAK,GAAEkQ,IAAIlQ,MAAMY,OAAOZ;IACxBkQ;IACAtP;IACA4R,UAAUA,MAAMtC,IAAIsC,SAAS5O,QAAQ;IACrCqqB,aAAaQ;IACb3rB,YAAYvF,KACV,MAAM,CAAC+B,OAAOsB,QAAQsP,KAAKyM,IAAI,GAC/B,CAACrd,QAAOsB,SAAQsP,MAAKyM,WAAU;MAC7Brd,OAAAA;MACAsB,QAAAA;MACAsP,KAAAA;MACAyM,MAAMA;MACNnK,UAAUmK,MAAKnK;MACfyb,aAAatR,MAAKsR;IACpB,IACA;MACEjyB,KAA+C;MAC/C8B,OAAOA,MAAMwB,MAAM4B,QAAQC;KAE/B;;AAGF7B,QAAMqC,UAAUzE,QAAQ4E,aAAW;AACjCC,WAAOC,OACL2a,MACA7a,QAAQ4a,cAAR5a,OAAAA,SAAAA,QAAQ4a,WACNC,MACA/b,QACAsP,KACA5Q,KACF,CACF;KACC,CAAA,CAAE;AAEL,SAAOqd;AACT;IC1CazL,YAAYA,CACvB5R,OACAU,IACAyc,UACAmS,UACApvB,OACAsS,SACA+c,aACe;AACf,MAAI3e,MAAsB;IACxBlQ;IACA3B,OAAOuwB;IACPnS;IACAjd;IACAqvB;IACAC,cAAc,CAAA;IACdC,oBAAoB,CAAA;IACpBvc,UAAU5O,cAAY;AACpB,UAAIsM,IAAI4e,aAAatS,eAAe5Y,QAAQ,GAAG;AAC7C,eAAOsM,IAAI4e,aAAalrB,QAAQ;MAClC;AAEA,YAAMhD,SAAStB,MAAMgM,UAAU1H,QAAQ;AAEvC,UAAI,EAAChD,UAAM,QAANA,OAAQR,aAAY;AACvB,eAAOF;MACT;AAEAgQ,UAAI4e,aAAalrB,QAAQ,IAAIhD,OAAOR,WAClC8P,IAAIuM,UACJmS,QACF;AAEA,aAAO1e,IAAI4e,aAAalrB,QAAQ;;IAElCorB,iBAAiBprB,cAAY;AAC3B,UAAIsM,IAAI6e,mBAAmBvS,eAAe5Y,QAAQ,GAAG;AACnD,eAAOsM,IAAI6e,mBAAmBnrB,QAAQ;MACxC;AAEA,YAAMhD,SAAStB,MAAMgM,UAAU1H,QAAQ;AAEvC,UAAI,EAAChD,UAAM,QAANA,OAAQR,aAAY;AACvB,eAAOF;MACT;AAEA,UAAI,CAACU,OAAOrB,UAAUyvB,iBAAiB;AACrC9e,YAAI6e,mBAAmBnrB,QAAQ,IAAI,CAACsM,IAAIsC,SAAS5O,QAAQ,CAAC;AAC1D,eAAOsM,IAAI6e,mBAAmBnrB,QAAQ;MACxC;AAEAsM,UAAI6e,mBAAmBnrB,QAAQ,IAAIhD,OAAOrB,UAAUyvB,gBAClD9e,IAAIuM,UACJmS,QACF;AAEA,aAAO1e,IAAI6e,mBAAmBnrB,QAAQ;;IAExCqqB,aAAarqB,cAAQ;AAAA,UAAAuO;AAAA,cAAAA,gBACnBjC,IAAIsC,SAAS5O,QAAQ,MAAC,OAAAuO,gBAAI7S,MAAM4B,QAAQytB;IAAmB;IAC7D7c,SAASA,WAAAA,OAAAA,UAAW,CAAA;IACpBmd,aAAaA,MAAMryB,UAAUsT,IAAI4B,SAASzV,OAAKA,EAAEyV,OAAO;IACxDod,cAAcA,MAAOhf,IAAI2e,WAAWvvB,MAAM0mB,OAAO9V,IAAI2e,QAAQ,IAAI3uB;IACjEivB,eAAeA,MAAM;AACnB,UAAIC,aAA2B,CAAA;AAC/B,UAAIC,aAAanf;AACjB,aAAO,MAAM;AACX,cAAMof,YAAYD,WAAWH,aAAY;AACzC,YAAI,CAACI;AAAW;AAChBF,mBAAWhyB,KAAKkyB,SAAS;AACzBD,qBAAaC;MACf;AACA,aAAOF,WAAWtqB,QAAO;;IAE3B6mB,aAAapuB,KACX,MAAM,CAAC+B,MAAM2Y,kBAAiB,CAAE,GAChCxW,iBAAe;AACb,aAAOA,YAAYoB,IAAIjC,YAAU;AAC/B,eAAO8b,WAAWpd,OAAO4Q,KAAmBtP,QAAQA,OAAOZ,EAAE;MAC/D,CAAC;IACH,GACA;MACEhE,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQkhB;MAAS;IAChE,CACF;IAEAhN,wBAAwB7X,KACtB,MAAM,CAAC2S,IAAIyb,YAAW,CAAE,GACxBzJ,cAAY;AACV,aAAOA,SAAStgB,OAAO,CAAC4sB,KAAK7R,SAAS;AACpC6R,YAAI7R,KAAK/b,OAAOZ,EAAE,IAAI2c;AACtB,eAAO6R;SACN,CAAA,CAA0C;IAC/C,GACA;MACExyB,KACE0E;MACF5C,OAAOA,MAAA;AAAA,YAAA4D;AAAA,gBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQkhB;MAAS;KAElE;;AAGF,WAASnC,IAAI,GAAGA,IAAI3gB,MAAMqC,UAAUrE,QAAQ2iB,KAAK;AAC/C,UAAMne,UAAUxC,MAAMqC,UAAUse,CAAC;AACjCle,WAAOC,OAAOkO,KAAKpO,WAAO,OAAA,SAAPA,QAASoP,aAATpP,OAAAA,SAAAA,QAASoP,UAAYhB,KAAK5Q,KAAK,CAAC;EACrD;AAEA,SAAO4Q;AACT;AChEO,SAASqf,qBAES;AACvB,SAAO;IACLC,UAAUA,CAACA,UAAU5uB,WAAW;AAC9B,aAAO,OAAO4uB,aAAa,aACtB;QACC,GAAG5uB;QACHR,YAAYovB;MACd,IACA;QACE,GAAG5uB;QACHb,aAAayvB;;;IAGrBC,SAAS7uB,YAAUA;IACnB8uB,OAAO9uB,YAAUA;;AAErB;ACnFO,SAASuU,kBAEW;AACzB,SAAO7V,WACL/B,KACE,MAAM,CAAC+B,MAAM4B,QAAQyuB,IAAI,GAEvBA,UAKG;AACH,UAAMvL,WAA4B;MAChCzD,MAAM,CAAA;MACN1Q,UAAU,CAAA;MACVW,UAAU,CAAA;;AAGZ,UAAMgf,aAAa,SACjBC,cACArwB,OACA8vB,WACiB;AAAA,UAFjB9vB,UAAK,QAAA;AAALA,gBAAQ;MAAC;AAGT,YAAMmhB,OAAO,CAAA;AAEb,eAASV,IAAI,GAAGA,IAAI4P,aAAavyB,QAAQ2iB,KAAK;AAS5C,cAAM/P,MAAMgB,UACV5R,OACAA,MAAMquB,UAAUkC,aAAa5P,CAAC,GAAIA,GAAGqP,SAAS,GAC9CO,aAAa5P,CAAC,GACdA,GACAzgB,OACAU,QACAovB,aAAS,OAAA,SAATA,UAAWtvB,EACb;AAGAokB,iBAASnU,SAAS7S,KAAK8S,GAAG;AAE1BkU,iBAASxT,SAASV,IAAIlQ,EAAE,IAAIkQ;AAE5ByQ,aAAKvjB,KAAK8S,GAAG;AAGb,YAAI5Q,MAAM4B,QAAQ4uB,YAAY;AAAA,cAAAC;AAC5B7f,cAAI8f,kBAAkB1wB,MAAM4B,QAAQ4uB,WAClCD,aAAa5P,CAAC,GACdA,CACF;AAGA,eAAA8P,uBAAI7f,IAAI8f,oBAAJD,QAAAA,qBAAqBzyB,QAAQ;AAC/B4S,gBAAI4B,UAAU8d,WAAW1f,IAAI8f,iBAAiBxwB,QAAQ,GAAG0Q,GAAG;UAC9D;QACF;MACF;AAEA,aAAOyQ;;AAGTyD,aAASzD,OAAOiP,WAAWD,IAAI;AAE/B,WAAOvL;EACT,GACA;IACEpoB,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;IAAU;IAC/D3hB,UAAUA,MAAM;AACde,YAAMgf,oBAAmB;IAC3B;EACF,CACF;AACJ;AClFO,SAAS2R,WACdtP,MACAuP,eACA5wB,OACA;AACA,MAAIA,MAAM4B,QAAQ0T,oBAAoB;AACpC,WAAOub,wBAAwBxP,MAAMuP,eAAe5wB,KAAK;EAC3D;AAEA,SAAO8wB,uBAAuBzP,MAAMuP,eAAe5wB,KAAK;AAC1D;AAEO,SAAS6wB,wBACdE,cACAC,WACAhxB,OACiB;AAAA,MAAAixB;AACjB,QAAMC,sBAAoC,CAAA;AAC1C,QAAMC,sBAAkD,CAAA;AACxD,QAAMppB,YAAQkpB,wBAAGjxB,MAAM4B,QAAQ2T,0BAAqB,OAAA0b,wBAAI;AAExD,QAAMG,oBAAoB,SAACL,eAA4B7wB,OAAc;AAAA,QAAdA,UAAK,QAAA;AAALA,cAAQ;IAAC;AAC9D,UAAMmhB,OAAqB,CAAA;AAG3B,aAASV,IAAI,GAAGA,IAAIoQ,cAAa/yB,QAAQ2iB,KAAK;AAAA,UAAAtO;AAC5C,UAAIzB,MAAMmgB,cAAapQ,CAAC;AAExB,YAAM0Q,SAASzf,UACb5R,OACA4Q,IAAIlQ,IACJkQ,IAAIuM,UACJvM,IAAI7R,OACJ6R,IAAI1Q,OACJU,QACAgQ,IAAI2e,QACN;AACA8B,aAAOnc,gBAAgBtE,IAAIsE;AAE3B,WAAI7C,eAAAzB,IAAI4B,YAAO,QAAXH,aAAarU,UAAUkC,QAAQ6H,UAAU;AAC3CspB,eAAO7e,UAAU4e,kBAAkBxgB,IAAI4B,SAAStS,QAAQ,CAAC;AACzD0Q,cAAMygB;AAEN,YAAIL,UAAUpgB,GAAG,KAAK,CAACygB,OAAO7e,QAAQxU,QAAQ;AAC5CqjB,eAAKvjB,KAAK8S,GAAG;AACbugB,8BAAoBvgB,IAAIlQ,EAAE,IAAIkQ;AAC9BugB,8BAAoBxQ,CAAC,IAAI/P;AACzB;QACF;AAEA,YAAIogB,UAAUpgB,GAAG,KAAKygB,OAAO7e,QAAQxU,QAAQ;AAC3CqjB,eAAKvjB,KAAK8S,GAAG;AACbugB,8BAAoBvgB,IAAIlQ,EAAE,IAAIkQ;AAC9BugB,8BAAoBxQ,CAAC,IAAI/P;AACzB;QACF;MACF,OAAO;AACLA,cAAMygB;AACN,YAAIL,UAAUpgB,GAAG,GAAG;AAClByQ,eAAKvjB,KAAK8S,GAAG;AACbugB,8BAAoBvgB,IAAIlQ,EAAE,IAAIkQ;AAC9BugB,8BAAoBxQ,CAAC,IAAI/P;QAC3B;MACF;IACF;AAEA,WAAOyQ;;AAGT,SAAO;IACLA,MAAM+P,kBAAkBL,YAAY;IACpCpgB,UAAUugB;IACV5f,UAAU6f;;AAEd;AAEO,SAASL,uBACdC,cACAC,WACAhxB,OACiB;AAAA,MAAAsxB;AACjB,QAAMJ,sBAAoC,CAAA;AAC1C,QAAMC,sBAAkD,CAAA;AACxD,QAAMppB,YAAQupB,yBAAGtxB,MAAM4B,QAAQ2T,0BAAqB,OAAA+b,yBAAI;AAGxD,QAAMF,oBAAoB,SAACL,eAA4B7wB,OAAc;AAAA,QAAdA,UAAK,QAAA;AAALA,cAAQ;IAAC;AAG9D,UAAMmhB,OAAqB,CAAA;AAG3B,aAASV,IAAI,GAAGA,IAAIoQ,cAAa/yB,QAAQ2iB,KAAK;AAC5C,UAAI/P,MAAMmgB,cAAapQ,CAAC;AAExB,YAAM4Q,OAAOP,UAAUpgB,GAAG;AAE1B,UAAI2gB,MAAM;AAAA,YAAAzK;AACR,aAAIA,gBAAAlW,IAAI4B,YAAO,QAAXsU,cAAa9oB,UAAUkC,QAAQ6H,UAAU;AAC3C,gBAAMspB,SAASzf,UACb5R,OACA4Q,IAAIlQ,IACJkQ,IAAIuM,UACJvM,IAAI7R,OACJ6R,IAAI1Q,OACJU,QACAgQ,IAAI2e,QACN;AACA8B,iBAAO7e,UAAU4e,kBAAkBxgB,IAAI4B,SAAStS,QAAQ,CAAC;AACzD0Q,gBAAMygB;QACR;AAEAhQ,aAAKvjB,KAAK8S,GAAG;AACbsgB,4BAAoBpzB,KAAK8S,GAAG;AAC5BugB,4BAAoBvgB,IAAIlQ,EAAE,IAAIkQ;MAChC;IACF;AAEA,WAAOyQ;;AAGT,SAAO;IACLA,MAAM+P,kBAAkBL,YAAY;IACpCpgB,UAAUugB;IACV5f,UAAU6f;;AAEd;AC5HO,SAASlY,sBAEW;AACzB,SAAOjZ,WACL/B,KACE,MAAM,CACJ+B,MAAMgY,uBAAsB,GAC5BhY,MAAM8D,SAAQ,EAAGoR,eACjBlV,MAAM8D,SAAQ,EAAGqR,YAAY,GAE/B,CAAC2P,UAAU5P,eAAeC,iBAAiB;AACzC,QACE,CAAC2P,SAASzD,KAAKrjB,UACd,EAACkX,iBAAa,QAAbA,cAAelX,WAAU,CAACmX,cAC5B;AACA,eAASwL,IAAI,GAAGA,IAAImE,SAASnU,SAAS3S,QAAQ2iB,KAAK;AACjDmE,iBAASnU,SAASgQ,CAAC,EAAGzL,gBAAgB,CAAA;AACtC4P,iBAASnU,SAASgQ,CAAC,EAAGrI,oBAAoB,CAAA;MAC5C;AACA,aAAOwM;IACT;AAEA,UAAM0M,wBAAuD,CAAA;AAC7D,UAAMC,wBAAuD,CAAA;AAE5D,KAACvc,iBAAa,OAAbA,gBAAiB,CAAA,GAAItX,QAAQb,OAAK;AAAA,UAAA20B;AAClC,YAAMpwB,SAAStB,MAAMgM,UAAUjP,EAAE2D,EAAE;AAEnC,UAAI,CAACY,QAAQ;AACX;MACF;AAEA,YAAM2T,WAAW3T,OAAO2U,YAAW;AAEnC,UAAI,CAAChB,UAAU;AACb,YAAI7T,MAAuC;AACzCzB,kBAAQwB,KACL,oEAAmEG,OAAOZ,KAC7E;QACF;AACA;MACF;AAEA8wB,4BAAsB1zB,KAAK;QACzB4C,IAAI3D,EAAE2D;QACNuU;QACAwP,gBAAaiN,wBAAEzc,SAASX,sBAAkB,OAAA,SAA3BW,SAASX,mBAAqBvX,EAAE6Y,KAAK,MAAC,OAAA8b,wBAAI30B,EAAE6Y;MAC7D,CAAC;IACH,CAAC;AAED,UAAM+b,gBAAgBzc,cAAc3R,IAAIxG,OAAKA,EAAE2D,EAAE;AAEjD,UAAM8U,iBAAiBxV,MAAMwY,kBAAiB;AAE9C,UAAMoZ,4BAA4B5xB,MAC/B2Y,kBAAiB,EACjBnU,OAAOlD,YAAUA,OAAOmV,mBAAkB,CAAE;AAE/C,QACEtB,gBACAK,kBACAoc,0BAA0B5zB,QAC1B;AACA2zB,oBAAc7zB,KAAK,YAAY;AAE/B8zB,gCAA0Bh0B,QAAQ0D,YAAU;AAAA,YAAAuwB;AAC1CJ,8BAAsB3zB,KAAK;UACzB4C,IAAIY,OAAOZ;UACXuU,UAAUO;UACViP,gBAAaoN,wBACXrc,eAAelB,sBAAkB,OAAA,SAAjCkB,eAAelB,mBAAqBa,YAAY,MAAC,OAAA0c,wBACjD1c;QACJ,CAAC;MACH,CAAC;IACH;AAEA,QAAI2c;AACJ,QAAIC;AAGJ,aAASC,IAAI,GAAGA,IAAIlN,SAASnU,SAAS3S,QAAQg0B,KAAK;AACjD,YAAMphB,MAAMkU,SAASnU,SAASqhB,CAAC;AAE/BphB,UAAIsE,gBAAgB,CAAA;AAEpB,UAAIsc,sBAAsBxzB,QAAQ;AAChC,iBAAS2iB,IAAI,GAAGA,IAAI6Q,sBAAsBxzB,QAAQ2iB,KAAK;AACrDmR,gCAAsBN,sBAAsB7Q,CAAC;AAC7C,gBAAMjgB,KAAKoxB,oBAAoBpxB;AAG/BkQ,cAAIsE,cAAcxU,EAAE,IAAIoxB,oBAAoB7c,SAC1CrE,KACAlQ,IACAoxB,oBAAoBrN,eACpBwN,gBAAc;AACZrhB,gBAAI0H,kBAAkB5X,EAAE,IAAIuxB;UAC9B,CACF;QACF;MACF;AAEA,UAAIR,sBAAsBzzB,QAAQ;AAChC,iBAAS2iB,IAAI,GAAGA,IAAI8Q,sBAAsBzzB,QAAQ2iB,KAAK;AACrDoR,gCAAsBN,sBAAsB9Q,CAAC;AAC7C,gBAAMjgB,KAAKqxB,oBAAoBrxB;AAE/B,cACEqxB,oBAAoB9c,SAClBrE,KACAlQ,IACAqxB,oBAAoBtN,eACpBwN,gBAAc;AACZrhB,gBAAI0H,kBAAkB5X,EAAE,IAAIuxB;UAC9B,CACF,GACA;AACArhB,gBAAIsE,cAAcgd,aAAa;AAC/B;UACF;QACF;AAEA,YAAIthB,IAAIsE,cAAcgd,eAAe,MAAM;AACzCthB,cAAIsE,cAAcgd,aAAa;QACjC;MACF;IACF;AAEA,UAAMC,iBAAkBvhB,SAAoB;AAE1C,eAAS+P,IAAI,GAAGA,IAAIgR,cAAc3zB,QAAQ2iB,KAAK;AAC7C,YAAI/P,IAAIsE,cAAcyc,cAAchR,CAAC,CAAC,MAAO,OAAO;AAClD,iBAAO;QACT;MACF;AACA,aAAO;;AAIT,WAAOgQ,WAAW7L,SAASzD,MAAM8Q,gBAAgBnyB,KAAK;EACxD,GACA;IACEtD,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;IAAU;IAC/D3hB,UAAUA,MAAM;AACde,YAAMgf,oBAAmB;IAC3B;EACF,CACF;AACJ;ACtJO,SAASjH,qBAGW;AACzB,SAAO,CAAC/X,OAAOsE,aACbrG,KACE,MAAM,CACJ+B,MAAMgY,uBAAsB,GAC5BhY,MAAM8D,SAAQ,EAAGoR,eACjBlV,MAAM8D,SAAQ,EAAGqR,cACjBnV,MAAMiZ,oBAAmB,CAAE,GAE7B,CAACmZ,aAAald,eAAeC,iBAAiB;AAC5C,QACE,CAACid,YAAY/Q,KAAKrjB,UACjB,EAACkX,iBAAa,QAAbA,cAAelX,WAAU,CAACmX,cAC5B;AACA,aAAOid;IACT;AAEA,UAAMT,gBAAgB,CACpB,GAAGzc,cAAc3R,IAAIxG,OAAKA,EAAE2D,EAAE,EAAE8D,OAAOzH,OAAKA,MAAMuH,QAAQ,GAC1D6Q,eAAe,eAAevU,MAAS,EACvC4D,OAAOC,OAAO;AAEhB,UAAM0tB,iBAAkBvhB,SAAoB;AAE1C,eAAS+P,IAAI,GAAGA,IAAIgR,cAAc3zB,QAAQ2iB,KAAK;AAC7C,YAAI/P,IAAIsE,cAAcyc,cAAchR,CAAC,CAAC,MAAO,OAAO;AAClD,iBAAO;QACT;MACF;AACA,aAAO;;AAGT,WAAOgQ,WAAWyB,YAAY/Q,MAAM8Q,gBAAgBnyB,KAAK;EAC3D,GACA;IACEtD,KAEE,wBAAwB4H;IAC1B9F,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;IAAU;IAC/D3hB,UAAUA,MAAM;IAAA;EAClB,CACF;AACJ;AC9CO,SAASiZ,yBAGY;AAC1B,SAAO,CAAClY,OAAOsE,aACbrG,KACE,MAAA;AAAA,QAAAo0B;AAAA,WAAM,EAAAA,mBAACryB,MAAMgM,UAAU1H,QAAQ,MAAC,OAAA,SAAzB+tB,iBAA2Bta,mBAAkB,CAAE;EAAC,GACvDua,qBAAmB;AACjB,QAAI,CAACA;AAAiB,aAAO,oBAAIna,IAAG;AAEpC,QAAIoa,sBAAsB,oBAAIpa,IAAG;AAEjC,aAASwI,IAAI,GAAGA,IAAI2R,gBAAgB3hB,SAAS3S,QAAQ2iB,KAAK;AACxD,YAAM3P,SACJshB,gBAAgB3hB,SAASgQ,CAAC,EAAG+O,gBAAwBprB,QAAQ;AAE/D,eAAS0tB,IAAI,GAAGA,IAAIhhB,OAAOhT,QAAQg0B,KAAK;AACtC,cAAMpc,QAAQ5E,OAAOghB,CAAC;AAEtB,YAAIO,oBAAoBC,IAAI5c,KAAK,GAAG;AAAA,cAAA6c;AAClCF,8BAAoBG,IAClB9c,SACA6c,wBAACF,oBAAoBI,IAAI/c,KAAK,MAAC6c,OAAAA,wBAAI,KAAK,CAC1C;QACF,OAAO;AACLF,8BAAoBG,IAAI9c,OAAO,CAAC;QAClC;MACF;IACF;AAEA,WAAO2c;EACT,GACA;IACE71B,KAEE,4BAA4B4H;IAC9B9F,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;IAAU;IAC/D3hB,UAAUA,MAAM;IAAA;EAClB,CACF;AACJ;ACxCO,SAASoZ,yBAGwB;AACtC,SAAO,CAACrY,OAAOsE,aACbrG,KACE,MAAA;AAAA,QAAAo0B;AAAA,WAAM,EAAAA,mBAACryB,MAAMgM,UAAU1H,QAAQ,MAAC,OAAA,SAAzB+tB,iBAA2Bta,mBAAkB,CAAE;EAAC,GACvDua,qBAAmB;AAAA,QAAAM;AACjB,QAAI,CAACN;AAAiB,aAAO1xB;AAE7B,UAAMiyB,cAAUD,wBACdN,gBAAgB3hB,SAAS,CAAC,MAAC,OAAA,SAA3BiiB,sBAA6BlD,gBAAgBprB,QAAQ;AAEvD,QAAI,OAAOuuB,eAAe,aAAa;AACrC,aAAOjyB;IACT;AAEA,QAAIkyB,sBAAkC,CAACD,YAAYA,UAAU;AAE7D,aAASlS,IAAI,GAAGA,IAAI2R,gBAAgB3hB,SAAS3S,QAAQ2iB,KAAK;AACxD,YAAM3P,SACJshB,gBAAgB3hB,SAASgQ,CAAC,EAAG+O,gBAAwBprB,QAAQ;AAE/D,eAAS0tB,IAAI,GAAGA,IAAIhhB,OAAOhT,QAAQg0B,KAAK;AACtC,cAAMpc,QAAQ5E,OAAOghB,CAAC;AAEtB,YAAIpc,QAAQkd,oBAAoB,CAAC,GAAG;AAClCA,8BAAoB,CAAC,IAAIld;mBAChBA,QAAQkd,oBAAoB,CAAC,GAAG;AACzCA,8BAAoB,CAAC,IAAIld;QAC3B;MACF;IACF;AAEA,WAAOkd;EACT,GACA;IACEp2B,KAEE,4BAA4B4H;IAC9B9F,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;IAAU;IAC/D3hB,UAAUA,MAAM;IAAA;EAClB,CACF;AACJ;AC3CO,SAASwS,oBAEW;AACzB,SAAOzR,WACL/B,KACE,MAAM,CAAC+B,MAAM8D,SAAQ,EAAG2kB,SAASzoB,MAAM0rB,qBAAoB,CAAE,GAC7D,CAACjD,SAAS3D,aAAa;AACrB,QAAI,CAACA,SAASzD,KAAKrjB,UAAU,EAACyqB,WAAO,QAAPA,QAASzqB,SAAQ;AAC7C,aAAO8mB;IACT;AAEA,UAAMiO,eAAe/yB,MAAM8D,SAAQ,EAAG2kB;AAEtC,UAAMuK,iBAA+B,CAAA;AAGrC,UAAMC,mBAAmBF,aAAavuB,OAAO8V,UAAI;AAAA,UAAA+X;AAAA,cAAAA,mBAC/CryB,MAAMgM,UAAUsO,KAAK5Z,EAAE,MAAvB2xB,OAAAA,SAAAA,iBAA0BxH,WAAU;IAAE,CACxC;AAEA,UAAMqI,iBAOF,CAAA;AAEJD,qBAAiBr1B,QAAQu1B,eAAa;AACpC,YAAM7xB,SAAStB,MAAMgM,UAAUmnB,UAAUzyB,EAAE;AAC3C,UAAI,CAACY;AAAQ;AAEb4xB,qBAAeC,UAAUzyB,EAAE,IAAI;QAC7B0yB,eAAe9xB,OAAOrB,UAAUmzB;QAChCC,eAAe/xB,OAAOrB,UAAUozB;QAChC3K,WAAWpnB,OAAO6nB,aAAY;;IAElC,CAAC;AAED,UAAMmK,WAAYjS,UAAuB;AAGvC,YAAMkS,aAAa,CAAC,GAAGlS,IAAI;AAE3BkS,iBAAWjZ,KAAK,CAACgN,MAAMC,SAAS;AAC9B,iBAAS5G,IAAI,GAAGA,IAAIsS,iBAAiBj1B,QAAQ2iB,KAAK,GAAG;AAAA,cAAA6S;AACnD,gBAAML,YAAYF,iBAAiBtS,CAAC;AACpC,gBAAM8S,aAAaP,eAAeC,UAAUzyB,EAAE;AAC9C,gBAAMgzB,UAAMF,kBAAGL,aAAS,OAAA,SAATA,UAAW5J,SAAI,OAAAiK,kBAAI;AAElC,cAAIC,WAAWL,eAAe;AAC5B,kBAAMO,SAASrM,KAAKpU,SAASigB,UAAUzyB,EAAE;AACzC,kBAAMkzB,SAASrM,KAAKrU,SAASigB,UAAUzyB,EAAE;AAEzC,kBAAMmzB,aAAa,OAAOF,WAAW;AACrC,kBAAMG,aAAa,OAAOF,WAAW;AAErC,gBAAIC,cAAcC,YAAY;AAC5B,qBAAOD,cAAcC,aACjB,IACAD,aACAJ,WAAWL,gBACX,CAACK,WAAWL;YAClB;UACF;AAGA,cAAIW,UAAUN,WAAW/K,UAAUpB,MAAMC,MAAM4L,UAAUzyB,EAAE;AAE3D,cAAIqzB,YAAY,GAAG;AACjB,gBAAIL,QAAQ;AACVK,yBAAW;YACb;AAEA,gBAAIN,WAAWJ,eAAe;AAC5BU,yBAAW;YACb;AAEA,mBAAOA;UACT;QACF;AAEA,eAAOzM,KAAKvoB,QAAQwoB,KAAKxoB;MAC3B,CAAC;AAGDw0B,iBAAW31B,QAAQgT,SAAO;AAAA,YAAAyB;AACxB2gB,uBAAel1B,KAAK8S,GAAG;AACvB,aAAAyB,eAAIzB,IAAI4B,YAAJH,QAAAA,aAAarU,QAAQ;AACvB4S,cAAI4B,UAAU8gB,SAAS1iB,IAAI4B,OAAO;QACpC;MACF,CAAC;AAED,aAAO+gB;;AAGT,WAAO;MACLlS,MAAMiS,SAASxO,SAASzD,IAAI;MAC5B1Q,UAAUqiB;MACV1hB,UAAUwT,SAASxT;;EAEvB,GACA;IACE5U,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;IAAU;IAC/D3hB,UAAUA,MAAM;AACde,YAAMgf,oBAAmB;IAC3B;EACF,CACF;AACJ;AC/GO,SAASpC,qBAEW;AACzB,SAAO5c,WACL/B,KACE,MAAM,CAAC+B,MAAM8D,SAAQ,EAAGuX,UAAUrb,MAAM2c,sBAAqB,CAAE,GAC/D,CAACtB,UAAUyJ,aAAa;AACtB,QAAI,CAACA,SAASzD,KAAKrjB,UAAU,CAACqd,SAASrd,QAAQ;AAC7C,aAAO8mB;IACT;AAGA,UAAMkP,mBAAmB3Y,SAAS7W,OAAOF,cACvCtE,MAAMgM,UAAU1H,QAAQ,CAC1B;AAEA,UAAM2vB,kBAAgC,CAAA;AACtC,UAAMC,kBAA8C,CAAA;AAOpD,UAAMC,qBAAqB,SACzB9S,MACAnhB,OACAqvB,UACG;AAAA,UAFHrvB,UAAK,QAAA;AAALA,gBAAQ;MAAC;AAKT,UAAIA,SAAS8zB,iBAAiBh2B,QAAQ;AACpC,eAAOqjB,KAAK9d,IAAIqN,SAAO;AACrBA,cAAI1Q,QAAQA;AAEZ+zB,0BAAgBn2B,KAAK8S,GAAG;AACxBsjB,0BAAgBtjB,IAAIlQ,EAAE,IAAIkQ;AAE1B,cAAIA,IAAI4B,SAAS;AACf5B,gBAAI4B,UAAU2hB,mBAAmBvjB,IAAI4B,SAAStS,QAAQ,GAAG0Q,IAAIlQ,EAAE;UACjE;AAEA,iBAAOkQ;QACT,CAAC;MACH;AAEA,YAAMtM,WAAmB0vB,iBAAiB9zB,KAAK;AAG/C,YAAMk0B,eAAeC,QAAQhT,MAAM/c,QAAQ;AAG3C,YAAMgwB,wBAAwBp3B,MAAMwd,KAAK0Z,aAAaG,QAAO,CAAE,EAAEhxB,IAC/D,CAAAnD,MAA+BrB,UAAU;AAAA,YAAxC,CAACy1B,eAAeC,YAAW,IAACr0B;AAC3B,YAAIM,KAAM,GAAE4D,YAAYkwB;AACxB9zB,aAAK6uB,WAAY,GAAEA,YAAY7uB,OAAOA;AAGtC,cAAM8R,UAAU2hB,mBAAmBM,cAAav0B,QAAQ,GAAGQ,EAAE;AAG7D,cAAMsZ,WAAW9Z,QACb5C,UAAUm3B,cAAa7jB,CAAAA,SAAOA,KAAI4B,OAAO,IACzCiiB;AAEJ,cAAM7jB,MAAMgB,UACV5R,OACAU,IACAsZ,SAAS,CAAC,EAAGmD,UACbpe,OACAmB,OACAU,QACA2uB,QACF;AAEA9sB,eAAOC,OAAOkO,KAAK;UACjBmM,kBAAkBzY;UAClBkwB;UACAhiB;UACAwH;UACA9G,UAAW5O,CAAAA,cAAqB;AAE9B,gBAAI0vB,iBAAiBjzB,SAASuD,SAAQ,GAAG;AACvC,kBAAIsM,IAAI4e,aAAatS,eAAe5Y,SAAQ,GAAG;AAC7C,uBAAOsM,IAAI4e,aAAalrB,SAAQ;cAClC;AAEA,kBAAImwB,aAAY,CAAC,GAAG;AAAA,oBAAAC;AAClB9jB,oBAAI4e,aAAalrB,SAAQ,KAACowB,wBACxBD,aAAY,CAAC,EAAEvhB,SAAS5O,SAAQ,MAACowB,OAAAA,wBAAI9zB;cACzC;AAEA,qBAAOgQ,IAAI4e,aAAalrB,SAAQ;YAClC;AAEA,gBAAIsM,IAAIqM,qBAAqBC,eAAe5Y,SAAQ,GAAG;AACrD,qBAAOsM,IAAIqM,qBAAqB3Y,SAAQ;YAC1C;AAGA,kBAAMhD,SAAStB,MAAMgM,UAAU1H,SAAQ;AACvC,kBAAMqwB,cAAcrzB,UAAM,OAAA,SAANA,OAAQgb,iBAAgB;AAE5C,gBAAIqY,aAAa;AACf/jB,kBAAIqM,qBAAqB3Y,SAAQ,IAAIqwB,YACnCrwB,WACA0V,UACAya,YACF;AAEA,qBAAO7jB,IAAIqM,qBAAqB3Y,SAAQ;YAC1C;UACF;QACF,CAAC;AAEDkO,gBAAQ5U,QAAQupB,YAAU;AACxB8M,0BAAgBn2B,KAAKqpB,MAAM;AAC3B+M,0BAAgB/M,OAAOzmB,EAAE,IAAIymB;QAQ/B,CAAC;AAED,eAAOvW;MACT,CACF;AAEA,aAAO0jB;;AAGT,UAAMG,cAAcN,mBAAmBrP,SAASzD,MAAM,CAAC;AAEvDoT,gBAAY72B,QAAQupB,YAAU;AAC5B8M,sBAAgBn2B,KAAKqpB,MAAM;AAC3B+M,sBAAgB/M,OAAOzmB,EAAE,IAAIymB;IAQ/B,CAAC;AAED,WAAO;MACL9F,MAAMoT;MACN9jB,UAAUsjB;MACV3iB,UAAU4iB;;EAEd,GACA;IACEx3B,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;IAAU;IAC/D3hB,UAAUA,MAAM;AACde,YAAM+P,OAAO,MAAM;AACjB/P,cAAM6P,mBAAkB;AACxB7P,cAAMgf,oBAAmB;MAC3B,CAAC;IACH;EACF,CACF;AACJ;AAEA,SAASqV,QAA+BhT,MAAoB/c,UAAkB;AAC5E,QAAMswB,WAAW,oBAAIzc,IAAG;AAExB,SAAOkJ,KAAK/e,OAAO,CAACiB,KAAKqN,QAAQ;AAC/B,UAAMikB,SAAU,GAAEjkB,IAAIoM,iBAAiB1Y,QAAQ;AAC/C,UAAMwwB,WAAWvxB,IAAIovB,IAAIkC,MAAM;AAC/B,QAAI,CAACC,UAAU;AACbvxB,UAAImvB,IAAImC,QAAQ,CAACjkB,GAAG,CAAC;IACvB,OAAO;AACLkkB,eAASh3B,KAAK8S,GAAG;IACnB;AACA,WAAOrN;KACNqxB,QAAQ;AACb;ACrLO,SAASljB,sBAEW;AACzB,SAAO1R,WACL/B,KACE,MAAM,CACJ+B,MAAM8D,SAAQ,EAAG0L,UACjBxP,MAAMwR,uBAAsB,GAC5BxR,MAAM4B,QAAQ8N,oBAAoB,GAEpC,CAACF,UAAUsV,UAAUpV,yBAAyB;AAC5C,QACE,CAACoV,SAASzD,KAAKrjB,UACdwR,aAAa,QAAQ,CAAC/M,OAAOwO,KAAKzB,YAAAA,OAAAA,WAAY,CAAA,CAAE,EAAExR,QACnD;AACA,aAAO8mB;IACT;AAEA,QAAI,CAACpV,sBAAsB;AAEzB,aAAOoV;IACT;AAEA,WAAOiQ,WAAWjQ,QAAQ;EAC5B,GACA;IACEpoB,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;IAAU;EACjE,CACF;AACJ;AAEO,SAASmU,WAAkCjQ,UAA2B;AAC3E,QAAMkQ,eAA6B,CAAA;AAEnC,QAAMC,YAAarkB,SAAoB;AAAA,QAAAyB;AACrC2iB,iBAAal3B,KAAK8S,GAAG;AAErB,SAAIyB,eAAAzB,IAAI4B,YAAJH,QAAAA,aAAarU,UAAU4S,IAAIO,cAAa,GAAI;AAC9CP,UAAI4B,QAAQ5U,QAAQq3B,SAAS;IAC/B;;AAGFnQ,WAASzD,KAAKzjB,QAAQq3B,SAAS;AAE/B,SAAO;IACL5T,MAAM2T;IACNrkB,UAAUmU,SAASnU;IACnBW,UAAUwT,SAASxT;;AAEvB;ACjDO,SAAS2P,sBAA6C7iB,MAEV;AACjD,SAAO4B,WACL/B,KACE,MAAM,CACJ+B,MAAM8D,SAAQ,EAAGgb,YACjB9e,MAAM0Q,yBAAwB,GAC9B1Q,MAAM4B,QAAQ8N,uBACV9O,SACAZ,MAAM8D,SAAQ,EAAG0L,QAAQ,GAE/B,CAACsP,YAAYgG,aAAa;AACxB,QAAI,CAACA,SAASzD,KAAKrjB,QAAQ;AACzB,aAAO8mB;IACT;AAEA,UAAM;MAAElG;MAAUD;IAAU,IAAIG;AAChC,QAAI;MAAEuC;MAAM1Q;MAAUW;IAAS,IAAIwT;AACnC,UAAMoQ,YAAYtW,WAAWD;AAC7B,UAAMwW,UAAUD,YAAYtW;AAE5ByC,WAAOA,KAAK2H,MAAMkM,WAAWC,OAAO;AAEpC,QAAIC;AAEJ,QAAI,CAACp1B,MAAM4B,QAAQ8N,sBAAsB;AACvC0lB,0BAAoBL,WAAW;QAC7B1T;QACA1Q;QACAW;MACF,CAAC;IACH,OAAO;AACL8jB,0BAAoB;QAClB/T;QACA1Q;QACAW;;IAEJ;AAEA8jB,sBAAkBzkB,WAAW,CAAA;AAE7B,UAAMskB,YAAarkB,SAAoB;AACrCwkB,wBAAkBzkB,SAAS7S,KAAK8S,GAAG;AACnC,UAAIA,IAAI4B,QAAQxU,QAAQ;AACtB4S,YAAI4B,QAAQ5U,QAAQq3B,SAAS;MAC/B;;AAGFG,sBAAkB/T,KAAKzjB,QAAQq3B,SAAS;AAExC,WAAOG;EACT,GACA;IACE14B,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQgf;IAAU;EACjE,CACF;AACJ;;;AChDO,SAASyU,WACdC,MACAC,OAC+B;AAC/B,SAAO,CAACD,OAAO,OAAOE,iBAAyBF,IAAI,IACjDG,oBAACH,MAASC,KAAQ,IAElBD;AAEJ;AAEA,SAASE,iBACPE,WAC0C;AAC1C,SACEC,iBAAiBD,SAAS,KAC1B,OAAOA,cAAc,cACrBE,kBAAkBF,SAAS;AAE/B;AAEA,SAASC,iBAAiBD,WAAgB;AACxC,SACE,OAAOA,cAAc,eACpB,MAAM;AACL,UAAMG,QAAQC,OAAOC,eAAeL,SAAS;AAC7C,WAAOG,MAAMG,aAAaH,MAAMG,UAAUR;EAC5C,GAAC;AAEL;AAEA,SAASI,kBAAkBF,WAAgB;AACzC,SACE,OAAOA,cAAc,YACrB,OAAOA,UAAUO,aAAa,YAC9B,CAAC,cAAc,mBAAmB,EAAEC,SAASR,UAAUO,SAASE,WAAW;AAE/E;AAEO,SAASC,cACdC,SACA;AAEA,QAAMC,kBAA+C;IACnDC,OAAO,CAAA;;IACPC,eAAeA,MAAM;IAAA;;IACrBC,qBAAqB;IACrB,GAAGJ;;AAIL,QAAM,CAACK,QAAQ,IAAUC,eAAS,OAAO;IACvCC,SAASC,YAAmBP,eAAe;EAC7C,EAAE;AAGF,QAAM,CAACC,OAAOO,QAAQ,IAAUH,eAAS,MAAMD,SAASE,QAAQG,YAAY;AAI5EL,WAASE,QAAQI,WAAWC,WAAS;IACnC,GAAGA;IACH,GAAGZ;IACHE,OAAO;MACL,GAAGA;MACH,GAAGF,QAAQE;;;;IAIbC,eAAeU,aAAW;AACxBJ,eAASI,OAAO;AAChBb,cAAQG,iBAARH,OAAAA,SAAAA,QAAQG,cAAgBU,OAAO;IACjC;EACF,EAAE;AAEF,SAAOR,SAASE;AAClB;", "names": ["functionalUpdate", "updater", "input", "noop", "makeStateUpdater", "key", "instance", "setState", "old", "isFunction", "d", "Function", "isNumberArray", "Array", "isArray", "every", "val", "flattenBy", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat", "recurse", "subArr", "for<PERSON>ach", "item", "push", "children", "length", "memo", "getDeps", "fn", "opts", "deps", "result", "depTime", "debug", "Date", "now", "newDeps", "depsChanged", "some", "dep", "index", "resultTime", "onChange", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "createColumn", "table", "columnDef", "depth", "parent", "_ref", "_resolvedColumnDef$id", "defaultColumn", "_getDefaultColumnDef", "resolvedColumnDef", "accessorKey", "id", "replace", "undefined", "header", "accessorFn", "includes", "originalRow", "split", "_result", "warn", "process", "Error", "column", "columns", "getFlatColumns", "_column$columns", "flatMap", "_table$options$debugA", "options", "debugAll", "debugColumns", "getLeafColumns", "_getOrderColumnsFn", "orderColumns", "_column$columns2", "leafColumns", "_table$options$debugA2", "_features", "reduce", "obj", "feature", "Object", "assign", "createHeader", "_options$id", "isPlaceholder", "placeholderId", "subHeaders", "colSpan", "rowSpan", "headerGroup", "getLeafHeaders", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "h", "map", "getContext", "Headers", "createTable", "getHeaderGroups", "getAllColumns", "getVisibleLeafColumns", "getState", "columnPinning", "left", "right", "allColumns", "_left$map$filter", "_right$map$filter", "leftColumns", "columnId", "find", "filter", "Boolean", "rightColumns", "centerColumns", "headerGroups", "buildHeaderGroups", "debugHeaders", "getCenterHeaderGroups", "getLeftHeaderGroups", "_left$map$filter2", "orderedLeafColumns", "_table$options$debugA3", "getRightHeaderGroups", "_right$map$filter2", "_table$options$debugA4", "getFooterGroups", "reverse", "_table$options$debugA5", "getLeftFooterGroups", "_table$options$debugA6", "getCenterFooterGroups", "_table$options$debugA7", "getRightFooterGroups", "_table$options$debugA8", "getFlatHeaders", "headers", "_table$options$debugA9", "getLeftFlatHeaders", "_table$options$debugA10", "getCenterFlatHeaders", "_table$options$debugA11", "getRightFlatHeaders", "_table$options$debugA12", "getCenterLeafHeaders", "flatHeaders", "_header$subHeaders", "_table$options$debugA13", "getLeftLeafHeaders", "_header$subHeaders2", "_table$options$debugA14", "getRightLeafHeaders", "_header$subHeaders3", "_table$options$debugA15", "center", "_left$0$headers", "_left$", "_center$0$headers", "_center$", "_right$0$headers", "_right$", "_table$options$debugA16", "columnsToGroup", "headerFamily", "_headerGroups$0$heade", "_headerGroups$", "max<PERSON><PERSON><PERSON>", "findMaxDepth", "getIsVisible", "createHeaderGroup", "headersToGroup", "join", "pendingParentHeaders", "headerToGroup", "latestPendingParentHeader", "isLeafHeader", "bottomHeaders", "recurseHeadersForSpans", "filteredHeaders", "childRowSpans", "childColSpan", "childRowSpan", "minChildRowSpan", "defaultColumnSizing", "size", "minSize", "maxSize", "Number", "MAX_SAFE_INTEGER", "getDefaultColumnSizingInfoState", "startOffset", "startSize", "deltaOffset", "deltaPercentage", "isResizingColumn", "columnSizingStart", "ColumnSizing", "getDefaultColumnDef", "getInitialState", "state", "columnSizing", "columnSizingInfo", "getDefaultOptions", "columnResizeMode", "onColumnSizingChange", "onColumnSizingInfoChange", "getSize", "_column$columnDef$min", "_column$columnDef$max", "columnSize", "getStart", "position", "getLeftVisibleLeafColumns", "getRightVisibleLeafColumns", "findIndex", "prevSiblingColumn", "resetSize", "setColumnSizing", "_ref2", "_", "rest", "getCanResize", "_column$columnDef$ena", "_table$options$enable", "enableResizing", "enableColumnResizing", "getIsResizing", "sum", "_header$column$getSiz", "prevSiblingHeader", "getResizeHandler", "getColumn", "canResize", "e", "persist", "isTouchStartEvent", "touches", "clientX", "newColumnSizing", "updateOffset", "eventType", "clientXPos", "setColumnSizingInfo", "_old$startOffset", "_old$startSize", "_ref3", "headerSize", "onMove", "onEnd", "mouseEvents", "<PERSON><PERSON><PERSON><PERSON>", "up<PERSON><PERSON><PERSON>", "document", "removeEventListener", "touchEvents", "cancelable", "preventDefault", "stopPropagation", "_e$touches$", "passiveIfSupported", "passiveEventSupported", "passive", "addEventListener", "resetColumnSizing", "defaultState", "_table$initialState$c", "initialState", "resetHeaderSizeInfo", "_table$initialState$c2", "getTotalSize", "_table$getHeaderGroup", "_table$getHeaderGroup2", "getLeftTotalSize", "_table$getLeftHeaderG", "_table$getLeftHeaderG2", "getCenterTotalSize", "_table$getCenterHeade", "_table$getCenterHeade2", "getRightTotalSize", "_table$getRightHeader", "_table$getRightHeader2", "passiveSupported", "supported", "window", "err", "type", "Expanding", "expanded", "onExpandedChange", "paginateExpandedRows", "registered", "queued", "_autoResetExpanded", "_table$options$autoRe", "_queue", "autoResetAll", "autoResetExpanded", "manualExpanding", "resetExpanded", "setExpanded", "toggleAllRowsExpanded", "getIsAllRowsExpanded", "_table$initialState$e", "_table$initialState", "getCanSomeRowsExpand", "getPrePaginationRowModel", "flatRows", "row", "getCanExpand", "getToggleAllRowsExpandedHandler", "getIsSomeRowsExpanded", "values", "keys", "getRowModel", "getIsExpanded", "getExpandedDepth", "rowIds", "rowsById", "splitId", "getPreExpandedRowModel", "getSortedRowModel", "getExpandedRowModel", "_getExpandedRowModel", "createRow", "toggleExpanded", "_expanded", "exists", "oldExpanded", "rowId", "_table$options$getIsR", "getIsRowExpanded", "_table$options$getRow", "_row$subRows", "getRowCanExpand", "enableExpanding", "subRows", "getToggleExpandedHandler", "canExpand", "includesString", "filterValue", "_row$getValue", "_row$getValue$toStrin", "_row$getValue$toStrin2", "search", "toLowerCase", "getValue", "toString", "autoRemove", "<PERSON><PERSON><PERSON><PERSON>", "includesStringSensitive", "_row$getValue2", "_row$getValue2$toStri", "equalsString", "_row$getValue3", "_row$getValue3$toStri", "arrIncludes", "_row$getValue4", "arrIncludesAll", "_row$getValue5", "arrIncludesSome", "_row$getValue6", "equals", "weakEquals", "inNumberRange", "rowValue", "resolveFilterValue", "unsafeMin", "unsafeMax", "parsedMin", "parseFloat", "parsedMax", "isNaN", "Infinity", "temp", "filterFns", "Filters", "filterFn", "columnFilters", "globalFilter", "onColumnFiltersChange", "onGlobalFilterChange", "filterFromLeafRows", "maxLeafRowFilterDepth", "globalFilterFn", "getColumnCanGlobalFilter", "_table$getCoreRowMode", "_table$getCoreRowMode2", "value", "getCoreRowModel", "_getAllCellsByColumnId", "getAutoFilterFn", "firstRow", "getFilterFn", "_table$options$filter", "_table$options$filter2", "getCanFilter", "_table$options$enable2", "enableColumnFilter", "enableColumnFilters", "enableFilters", "getCanGlobalFilter", "_column$columnDef$ena2", "_table$options$enable3", "_table$options$enable4", "_table$options$getCol", "enableGlobalFilter", "getIsFiltered", "getFilterIndex", "getFilterValue", "_table$getState$colum", "_table$getState$colum2", "_table$getState$colum3", "_table$getState$colum4", "setFilterValue", "setColumnFilters", "previousfilter", "newFilter", "shouldAutoRemoveFilter", "_old$filter", "newFilterObj", "_old$map", "_getFacetedRowModel", "getFacetedRowModel", "getPreFilteredRowModel", "_getFacetedUniqueValues", "getFacetedUniqueValues", "Map", "_getFacetedMinMaxValues", "getFacetedMinMaxValues", "columnFiltersMeta", "getGlobalAutoFilterFn", "getGlobalFilterFn", "_table$options$filter3", "_table$options$filter4", "getAllLeafColumns", "updateFn", "_functionalUpdate", "setGlobalFilter", "resetGlobalFilter", "resetColumnFilters", "getFilteredRowModel", "_getFilteredRowModel", "manualFiltering", "_getGlobalFacetedRowModel", "getGlobalFacetedRowModel", "_getGlobalFacetedUniqueValues", "getGlobalFacetedUniqueValues", "_getGlobalFacetedMinMaxValues", "getGlobalFacetedMinMaxValues", "_leafRows", "childRows", "next", "nextValue", "extent", "mean", "leafRows", "count", "median", "mid", "floor", "nums", "sort", "a", "b", "unique", "from", "Set", "uniqueCount", "_columnId", "aggregationFns", "Grouping", "aggregatedCell", "props", "_toString", "_props$getValue", "aggregationFn", "grouping", "onGroupingChange", "groupedColumnMode", "toggleGrouping", "setGrouping", "getCanGroup", "enableGrouping", "getIsGrouped", "_table$getState$group", "getGroupedIndex", "_table$getState$group2", "indexOf", "getToggleGroupingHandler", "canGroup", "getAutoAggregationFn", "prototype", "call", "getAggregationFn", "_table$options$aggreg", "_table$options$aggreg2", "resetGrouping", "_table$initialState$g", "getPreGroupedRowModel", "getGroupedRowModel", "_getGroupedRowModel", "manualGrouping", "groupingColumnId", "getGroupingValue", "_groupingValuesCache", "hasOwnProperty", "original", "createCell", "cell", "getIsPlaceholder", "getIsAggregated", "nonGroupingColumns", "col", "groupingColumns", "g", "Ordering", "columnOrder", "onColumnOrderChange", "setColumnOrder", "resetColumnOrder", "orderedColumns", "columnOrderCopy", "columnsCopy", "targetColumnId", "shift", "foundIndex", "splice", "defaultPageIndex", "defaultPageSize", "getDefaultPaginationState", "pageIndex", "pageSize", "Pagination", "pagination", "onPaginationChange", "_autoResetPageIndex", "autoResetPageIndex", "manualPagination", "resetPageIndex", "setPagination", "safeUpdater", "newState", "resetPagination", "_table$initialState$p", "setPageIndex", "maxPageIndex", "pageCount", "_table$initialState$p2", "_table$initialState$p3", "resetPageSize", "_table$initialState$p4", "_table$initialState2", "_table$initialState2$", "setPageSize", "topRowIndex", "setPageCount", "_table$options$pageCo", "newPageCount", "getPageOptions", "getPageCount", "pageOptions", "fill", "i", "debugTable", "getCanPreviousPage", "getCanNextPage", "previousPage", "nextPage", "getPaginationRowModel", "_getPaginationRowModel", "_table$options$pageCo2", "ceil", "rows", "getDefaultPinningState", "<PERSON>nning", "onColumnPinningChange", "pin", "columnIds", "setColumnPinning", "_old$left3", "_old$right3", "_old$left", "_old$right", "_old$left2", "_old$right2", "getCanPin", "_d$columnDef$enablePi", "enablePinning", "getIsPinned", "leafColumnIds", "isLeft", "isRight", "getPinnedIndex", "getCenterVisibleCells", "_getAllVisibleCells", "allCells", "leftAndRight", "debugRows", "getLeftVisibleCells", "cells", "getRightVisibleCells", "resetColumnPinning", "getIsSomeColumnsPinned", "_pinningState$positio", "pinningState", "_pinningState$left", "_pinningState$right", "getLeftLeafColumns", "getRightLeafColumns", "getCenterLeafColumns", "RowSelection", "rowSelection", "onRowSelectionChange", "enableRowSelection", "enableMultiRowSelection", "enableSubRowSelection", "setRowSelection", "resetRowSelection", "_table$initialState$r", "toggleAllRowsSelected", "getIsAllRowsSelected", "preGroupedFlatRows", "getCanSelect", "toggleAllPageRowsSelected", "resolvedValue", "getIsAllPageRowsSelected", "mutateRowIsSelected", "getPreSelectedRowModel", "getSelectedRowModel", "rowModel", "selectRowsFn", "getFilteredSelectedRowModel", "getGroupedSelectedRowModel", "isAllRowsSelected", "paginationFlatRows", "isAllPageRowsSelected", "getIsSomeRowsSelected", "_table$getState$rowSe", "totalSelected", "getIsSomePageRowsSelected", "getIsSelected", "getIsSomeSelected", "getToggleAllRowsSelectedHandler", "target", "checked", "getToggleAllPageRowsSelectedHandler", "toggleSelected", "isSelected", "selectedRowIds", "isRowSelected", "isSubRowSelected", "getIsAllSubRowsSelected", "getCanSelectSubRows", "getCanMultiSelect", "getToggleSelectedHandler", "canSelect", "_target", "getRow", "newSelectedFlatRows", "newSelectedRowsById", "recurseRows", "_row$subRows2", "selection", "_selection$row$id", "allChildrenSelected", "someSelected", "subRow", "reSplitAlphaNumeric", "alphanumeric", "rowA", "rowB", "compareAlphanumeric", "alphanumericCaseSensitive", "text", "compareBasic", "textCaseSensitive", "datetime", "basic", "aStr", "bStr", "aa", "bb", "an", "parseInt", "bn", "combo", "sortingFns", "Sorting", "sorting", "sortingFn", "onSortingChange", "isMultiSortEvent", "shift<PERSON>ey", "getAutoSortingFn", "firstRows", "slice", "isString", "getAutoSortDir", "getSortingFn", "_table$options$sortin", "_table$options$sortin2", "toggleSorting", "desc", "multi", "nextSortingOrder", "getNextSortingOrder", "hasManual<PERSON><PERSON>ue", "setSorting", "existingSorting", "existingIndex", "newSorting", "sortAction", "nextDesc", "getCanMultiSort", "_table$options$maxMul", "maxMultiSortColCount", "getFirstSortDir", "_column$columnDef$sor", "sortDescFirst", "firstSortDirection", "isSorted", "getIsSorted", "enableSortingRemoval", "enableMultiRemove", "getCanSort", "enableSorting", "enableMultiSort", "_table$getState$sorti", "columnSort", "getSortIndex", "_table$getState$sorti2", "_table$getState$sorti3", "clearSorting", "getToggleSortingHandler", "canSort", "resetSorting", "_table$initialState$s", "getPreSortedRowModel", "_getSortedRowModel", "manualSorting", "Visibility", "columnVisibility", "onColumnVisibilityChange", "toggleVisibility", "getCanHide", "setColumnVisibility", "enableHiding", "getToggleVisibilityHandler", "getAllCells", "getVisibleCells", "makeVisibleColumnsMethod", "getColumns", "getVisibleFlatColumns", "getAllFlatColumns", "getCenterVisibleLeafColumns", "resetColumnVisibility", "toggleAllColumnsVisible", "_value", "getIsAllColumnsVisible", "getIsSomeColumnsVisible", "getToggleAllColumnsVisibilityHandler", "features", "_options$initialState", "defaultOptions", "mergeOptions", "coreInitialState", "_feature$getInitialSt", "queuedTimeout", "coreInstance", "cb", "Promise", "resolve", "then", "catch", "error", "setTimeout", "reset", "setOptions", "newOptions", "onStateChange", "_getRowId", "getRowId", "_getCoreRowModel", "_defaultColumn", "_props$renderValue$to", "_props$renderValue", "renderValue", "_getColumnDefs", "columnDefs", "recurseColumns", "groupingColumnDef", "_getAllFlatColumnsById", "flatColumns", "acc", "getRenderValue", "_cell$getValue", "renderFallbackValue", "rowIndex", "parentId", "_valuesCache", "_uniqueValuesCache", "getUniqueValues", "getLeafRows", "getParentRow", "getParentRows", "parentRows", "currentRow", "parentRow", "createColumnHelper", "accessor", "display", "group", "data", "accessRows", "originalRows", "getSubRows", "_row$originalSubRows", "originalSubRows", "filterRows", "filterRowImpl", "filterRowModelFromLeafs", "filterRowModelFromRoot", "rowsToFilter", "filterRow", "_table$options$maxLea", "newFilteredFlatRows", "newFilteredRowsById", "recurseFilterRows", "newRow", "_table$options$maxLea2", "pass", "resolvedColumnFilters", "resolvedGlobalFilters", "_filterFn$resolveFilt", "filterableIds", "globallyFilterableColumns", "_globalFilterFn$resol", "currentColumnFilter", "currentGlobalFilter", "j", "filterMeta", "__global__", "filterRowsImpl", "preRowModel", "_table$getColumn", "facetedRowModel", "facetedUniqueValues", "has", "_facetedUniqueValues$", "set", "get", "_facetedRowModel$flat", "firstValue", "facetedMinMaxValues", "sortingState", "sortedFlatRows", "availableSorting", "columnInfoById", "sortEntry", "sortUndefined", "invertSorting", "sortData", "sortedData", "_sortEntry$desc", "columnInfo", "isDesc", "aValue", "bValue", "aUndefined", "bUndefined", "sortInt", "existingGrouping", "groupedFlatRows", "groupedRowsById", "groupUpRecursively", "rowGroupsMap", "groupBy", "aggregatedGroupedRows", "entries", "groupingValue", "groupedRows", "_groupedRows$0$getVal", "aggregateFn", "groupMap", "res<PERSON>ey", "previous", "expandRows", "expandedRows", "handleRow", "pageStart", "pageEnd", "paginatedRowModel", "flexRender", "Comp", "props", "isReactComponent", "createElement", "component", "isClassComponent", "isExoticComponent", "proto", "Object", "getPrototypeOf", "prototype", "$$typeof", "includes", "description", "useReactTable", "options", "resolvedOptions", "state", "onStateChange", "renderFallbackValue", "tableRef", "useState", "current", "createTable", "setState", "initialState", "setOptions", "prev", "updater"]}