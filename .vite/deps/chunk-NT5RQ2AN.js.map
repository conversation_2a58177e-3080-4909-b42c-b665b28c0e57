{"version": 3, "sources": ["../../node_modules/@zendeskgarden/container-accordion/dist/index.esm.js"], "sourcesContent": ["/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport React, { useMemo, useState } from 'react';\nimport { useUIDSeed } from 'react-uid';\nimport { getControlledValue, composeEventHandlers, KEY_CODES } from '@zendeskgarden/container-utilities';\nimport PropTypes from 'prop-types';\n\nfunction useAccordion(_temp) {\n  let {\n    idPrefix,\n    expandedSections,\n    onChange,\n    expandable = true,\n    collapsible = true,\n    defaultExpandedSections\n  } = _temp === void 0 ? {} : _temp;\n  const isControlled = expandedSections !== null && expandedSections !== undefined;\n  const seed = useUIDSeed();\n  const prefix = useMemo(() => idPrefix || seed(`accordion_${'2.0.5'}`), [idPrefix, seed]);\n  const TRIGGER_ID = `${prefix}--trigger`;\n  const PANEL_ID = `${prefix}--panel`;\n  const [expandedState, setExpandedState] = useState(defaultExpandedSections || [0]);\n  const controlledExpandedState = getControlledValue(expandedSections, expandedState);\n  const [disabledState, setDisabledState] = useState(collapsible ? [] : expandedState);\n  const sectionIndices = [];\n  const toggle = index => {\n    const expanded = [];\n    const disabled = [];\n    sectionIndices.forEach(sectionIndex => {\n      let isExpanded = false;\n      if (sectionIndex === index) {\n        isExpanded = collapsible ? expandedState.indexOf(sectionIndex) === -1 : true;\n      } else if (expandable) {\n        isExpanded = expandedState.indexOf(sectionIndex) !== -1;\n      }\n      if (isExpanded) {\n        expanded.push(sectionIndex);\n        if (!collapsible) {\n          disabled.push(sectionIndex);\n        }\n      }\n    });\n    if (onChange) {\n      onChange(index);\n    }\n    if (isControlled === false) {\n      setExpandedState(expanded);\n    }\n    setDisabledState(disabled);\n  };\n  const getHeaderProps = function (_temp2) {\n    let {\n      role = 'heading',\n      ariaLevel,\n      ...props\n    } = _temp2 === void 0 ? {} : _temp2;\n    if (ariaLevel === undefined) {\n      throw new Error('Accessibility Error: You must apply the `ariaLevel` prop to the element that contains your heading.');\n    }\n    return {\n      role,\n      'aria-level': ariaLevel,\n      'data-garden-container-id': 'containers.accordion',\n      'data-garden-container-version': '2.0.5',\n      ...props\n    };\n  };\n  const getTriggerProps = function (_temp3) {\n    let {\n      index,\n      role = 'button',\n      tabIndex = 0,\n      ...props\n    } = _temp3 === void 0 ? {} : _temp3;\n    if (index === undefined) {\n      throw new Error('Accessibility Error: You must provide an `index` option to `getTriggerProps()`');\n    }\n    sectionIndices.push(index);\n    return {\n      id: `${TRIGGER_ID}:${index}`,\n      role,\n      tabIndex,\n      'aria-controls': `${PANEL_ID}:${index}`,\n      'aria-disabled': disabledState.indexOf(index) !== -1,\n      'aria-expanded': isControlled ? controlledExpandedState.includes(index) : expandedState.includes(index),\n      onClick: composeEventHandlers(props.onClick, () => toggle(index)),\n      onKeyDown: composeEventHandlers(props.onKeyDown, event => {\n        if (event.keyCode === KEY_CODES.SPACE || event.keyCode === KEY_CODES.ENTER) {\n          toggle(index);\n          event.preventDefault();\n        }\n      }),\n      ...props\n    };\n  };\n  const getPanelProps = function (_temp4) {\n    let {\n      index,\n      role = 'region',\n      ...props\n    } = _temp4 === void 0 ? {} : _temp4;\n    if (index === undefined) {\n      throw new Error('Accessibility Error: You must provide an `index` option to `getSectionProps()`');\n    }\n    return {\n      id: `${PANEL_ID}:${index}`,\n      role,\n      'aria-hidden': isControlled ? !controlledExpandedState.includes(index) : !expandedState.includes(index),\n      'aria-labelledby': `${TRIGGER_ID}:${index}`,\n      ...props\n    };\n  };\n  return {\n    getHeaderProps,\n    getTriggerProps,\n    getPanelProps,\n    expandedSections: controlledExpandedState,\n    disabledSections: disabledState\n  };\n}\n\nconst AccordionContainer = props => {\n  const {\n    children,\n    render = children,\n    ...options\n  } = props;\n  return React.createElement(React.Fragment, null, render(useAccordion(options)));\n};\nAccordionContainer.defaultProps = {\n  expandable: true,\n  collapsible: true\n};\nAccordionContainer.propTypes = {\n  children: PropTypes.func,\n  render: PropTypes.func,\n  expandedSections: PropTypes.array,\n  defaultExpandedSections: PropTypes.array,\n  expandable: PropTypes.bool,\n  collapsible: PropTypes.bool,\n  idPrefix: PropTypes.string,\n  onChange: PropTypes.func\n};\n\nexport { AccordionContainer, useAccordion };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAOA,mBAAyC;AAGzC,wBAAsB;AAEtB,SAAS,aAAa,OAAO;AAC3B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,cAAc;AAAA,IACd;AAAA,EACF,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,QAAM,eAAe,qBAAqB,QAAQ,qBAAqB;AACvE,QAAM,OAAO,WAAW;AACxB,QAAM,aAAS,sBAAQ,MAAM,YAAY,KAAK,aAAa,SAAS,GAAG,CAAC,UAAU,IAAI,CAAC;AACvF,QAAM,aAAa,GAAG;AACtB,QAAM,WAAW,GAAG;AACpB,QAAM,CAAC,eAAe,gBAAgB,QAAI,uBAAS,2BAA2B,CAAC,CAAC,CAAC;AACjF,QAAM,0BAA0B,mBAAmB,kBAAkB,aAAa;AAClF,QAAM,CAAC,eAAe,gBAAgB,QAAI,uBAAS,cAAc,CAAC,IAAI,aAAa;AACnF,QAAM,iBAAiB,CAAC;AACxB,QAAM,SAAS,WAAS;AACtB,UAAM,WAAW,CAAC;AAClB,UAAM,WAAW,CAAC;AAClB,mBAAe,QAAQ,kBAAgB;AACrC,UAAI,aAAa;AACjB,UAAI,iBAAiB,OAAO;AAC1B,qBAAa,cAAc,cAAc,QAAQ,YAAY,MAAM,KAAK;AAAA,MAC1E,WAAW,YAAY;AACrB,qBAAa,cAAc,QAAQ,YAAY,MAAM;AAAA,MACvD;AACA,UAAI,YAAY;AACd,iBAAS,KAAK,YAAY;AAC1B,YAAI,CAAC,aAAa;AAChB,mBAAS,KAAK,YAAY;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,UAAU;AACZ,eAAS,KAAK;AAAA,IAChB;AACA,QAAI,iBAAiB,OAAO;AAC1B,uBAAiB,QAAQ;AAAA,IAC3B;AACA,qBAAiB,QAAQ;AAAA,EAC3B;AACA,QAAM,iBAAiB,SAAU,QAAQ;AACvC,QAAI;AAAA,MACF,OAAO;AAAA,MACP;AAAA,MACA,GAAG;AAAA,IACL,IAAI,WAAW,SAAS,CAAC,IAAI;AAC7B,QAAI,cAAc,QAAW;AAC3B,YAAM,IAAI,MAAM,qGAAqG;AAAA,IACvH;AACA,WAAO;AAAA,MACL;AAAA,MACA,cAAc;AAAA,MACd,4BAA4B;AAAA,MAC5B,iCAAiC;AAAA,MACjC,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,kBAAkB,SAAU,QAAQ;AACxC,QAAI;AAAA,MACF;AAAA,MACA,OAAO;AAAA,MACP,WAAW;AAAA,MACX,GAAG;AAAA,IACL,IAAI,WAAW,SAAS,CAAC,IAAI;AAC7B,QAAI,UAAU,QAAW;AACvB,YAAM,IAAI,MAAM,gFAAgF;AAAA,IAClG;AACA,mBAAe,KAAK,KAAK;AACzB,WAAO;AAAA,MACL,IAAI,GAAG,cAAc;AAAA,MACrB;AAAA,MACA;AAAA,MACA,iBAAiB,GAAG,YAAY;AAAA,MAChC,iBAAiB,cAAc,QAAQ,KAAK,MAAM;AAAA,MAClD,iBAAiB,eAAe,wBAAwB,SAAS,KAAK,IAAI,cAAc,SAAS,KAAK;AAAA,MACtG,SAAS,qBAAqB,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC;AAAA,MAChE,WAAW,qBAAqB,MAAM,WAAW,WAAS;AACxD,YAAI,MAAM,YAAY,UAAU,SAAS,MAAM,YAAY,UAAU,OAAO;AAC1E,iBAAO,KAAK;AACZ,gBAAM,eAAe;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,gBAAgB,SAAU,QAAQ;AACtC,QAAI;AAAA,MACF;AAAA,MACA,OAAO;AAAA,MACP,GAAG;AAAA,IACL,IAAI,WAAW,SAAS,CAAC,IAAI;AAC7B,QAAI,UAAU,QAAW;AACvB,YAAM,IAAI,MAAM,gFAAgF;AAAA,IAClG;AACA,WAAO;AAAA,MACL,IAAI,GAAG,YAAY;AAAA,MACnB;AAAA,MACA,eAAe,eAAe,CAAC,wBAAwB,SAAS,KAAK,IAAI,CAAC,cAAc,SAAS,KAAK;AAAA,MACtG,mBAAmB,GAAG,cAAc;AAAA,MACpC,GAAG;AAAA,IACL;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,EACpB;AACF;AAEA,IAAM,qBAAqB,WAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,IACT,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,aAAAA,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,OAAO,aAAa,OAAO,CAAC,CAAC;AAChF;AACA,mBAAmB,eAAe;AAAA,EAChC,YAAY;AAAA,EACZ,aAAa;AACf;AACA,mBAAmB,YAAY;AAAA,EAC7B,UAAU,kBAAAC,QAAU;AAAA,EACpB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,kBAAkB,kBAAAA,QAAU;AAAA,EAC5B,yBAAyB,kBAAAA,QAAU;AAAA,EACnC,YAAY,kBAAAA,QAAU;AAAA,EACtB,aAAa,kBAAAA,QAAU;AAAA,EACvB,UAAU,kBAAAA,QAAU;AAAA,EACpB,UAAU,kBAAAA,QAAU;AACtB;", "names": ["React", "PropTypes"]}