{"version": 3, "sources": ["../../node_modules/object-keys/isArguments.js", "../../node_modules/object-keys/implementation.js", "../../node_modules/object-keys/index.js", "../../node_modules/has-symbols/shams.js", "../../node_modules/has-tostringtag/shams.js", "../../node_modules/has-symbols/index.js", "../../node_modules/has-proto/index.js", "../../node_modules/function-bind/implementation.js", "../../node_modules/function-bind/index.js", "../../node_modules/has/src/index.js", "../../node_modules/get-intrinsic/index.js", "../../node_modules/call-bind/index.js", "../../node_modules/call-bind/callBound.js", "../../node_modules/is-arguments/index.js", "../../node_modules/has-property-descriptors/index.js", "../../node_modules/define-properties/index.js", "../../node_modules/object-is/implementation.js", "../../node_modules/object-is/polyfill.js", "../../node_modules/object-is/shim.js", "../../node_modules/object-is/index.js", "../../node_modules/is-regex/index.js", "../../node_modules/functions-have-names/index.js", "../../node_modules/regexp.prototype.flags/implementation.js", "../../node_modules/regexp.prototype.flags/polyfill.js", "../../node_modules/regexp.prototype.flags/shim.js", "../../node_modules/regexp.prototype.flags/index.js", "../../node_modules/is-date-object/index.js", "../../node_modules/deep-equal/index.js", "../../node_modules/gud/index.js", "../../node_modules/@hypnosphi/create-react-context/lib/implementation.js", "../../node_modules/@hypnosphi/create-react-context/lib/index.js", "../../node_modules/popper.js/src/utils/isBrowser.js", "../../node_modules/popper.js/src/utils/debounce.js", "../../node_modules/popper.js/src/utils/isFunction.js", "../../node_modules/popper.js/src/utils/getStyleComputedProperty.js", "../../node_modules/popper.js/src/utils/getParentNode.js", "../../node_modules/popper.js/src/utils/getScrollParent.js", "../../node_modules/popper.js/src/utils/getReferenceNode.js", "../../node_modules/popper.js/src/utils/isIE.js", "../../node_modules/popper.js/src/utils/getOffsetParent.js", "../../node_modules/popper.js/src/utils/isOffsetContainer.js", "../../node_modules/popper.js/src/utils/getRoot.js", "../../node_modules/popper.js/src/utils/findCommonOffsetParent.js", "../../node_modules/popper.js/src/utils/getScroll.js", "../../node_modules/popper.js/src/utils/includeScroll.js", "../../node_modules/popper.js/src/utils/getBordersSize.js", "../../node_modules/popper.js/src/utils/getWindowSizes.js", "../../node_modules/popper.js/src/utils/getClientRect.js", "../../node_modules/popper.js/src/utils/getBoundingClientRect.js", "../../node_modules/popper.js/src/utils/getOffsetRectRelativeToArbitraryNode.js", "../../node_modules/popper.js/src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../../node_modules/popper.js/src/utils/isFixed.js", "../../node_modules/popper.js/src/utils/getFixedPositionOffsetParent.js", "../../node_modules/popper.js/src/utils/getBoundaries.js", "../../node_modules/popper.js/src/utils/computeAutoPlacement.js", "../../node_modules/popper.js/src/utils/getReferenceOffsets.js", "../../node_modules/popper.js/src/utils/getOuterSizes.js", "../../node_modules/popper.js/src/utils/getOppositePlacement.js", "../../node_modules/popper.js/src/utils/getPopperOffsets.js", "../../node_modules/popper.js/src/utils/find.js", "../../node_modules/popper.js/src/utils/findIndex.js", "../../node_modules/popper.js/src/utils/runModifiers.js", "../../node_modules/popper.js/src/methods/update.js", "../../node_modules/popper.js/src/utils/isModifierEnabled.js", "../../node_modules/popper.js/src/utils/getSupportedPropertyName.js", "../../node_modules/popper.js/src/methods/destroy.js", "../../node_modules/popper.js/src/utils/getWindow.js", "../../node_modules/popper.js/src/utils/setupEventListeners.js", "../../node_modules/popper.js/src/methods/enableEventListeners.js", "../../node_modules/popper.js/src/utils/removeEventListeners.js", "../../node_modules/popper.js/src/methods/disableEventListeners.js", "../../node_modules/popper.js/src/utils/isNumeric.js", "../../node_modules/popper.js/src/utils/setStyles.js", "../../node_modules/popper.js/src/utils/setAttributes.js", "../../node_modules/popper.js/src/modifiers/applyStyle.js", "../../node_modules/popper.js/src/utils/getRoundedOffsets.js", "../../node_modules/popper.js/src/modifiers/computeStyle.js", "../../node_modules/popper.js/src/utils/isModifierRequired.js", "../../node_modules/popper.js/src/modifiers/arrow.js", "../../node_modules/popper.js/src/utils/getOppositeVariation.js", "../../node_modules/popper.js/src/methods/placements.js", "../../node_modules/popper.js/src/utils/clockwise.js", "../../node_modules/popper.js/src/modifiers/flip.js", "../../node_modules/popper.js/src/modifiers/keepTogether.js", "../../node_modules/popper.js/src/modifiers/offset.js", "../../node_modules/popper.js/src/modifiers/preventOverflow.js", "../../node_modules/popper.js/src/modifiers/shift.js", "../../node_modules/popper.js/src/modifiers/hide.js", "../../node_modules/popper.js/src/modifiers/inner.js", "../../node_modules/popper.js/src/modifiers/index.js", "../../node_modules/popper.js/src/methods/defaults.js", "../../node_modules/popper.js/src/index.js"], "sourcesContent": ["'use strict';\n\nvar toStr = Object.prototype.toString;\n\nmodule.exports = function isArguments(value) {\n\tvar str = toStr.call(value);\n\tvar isArgs = str === '[object Arguments]';\n\tif (!isArgs) {\n\t\tisArgs = str !== '[object Array]' &&\n\t\t\tvalue !== null &&\n\t\t\ttypeof value === 'object' &&\n\t\t\ttypeof value.length === 'number' &&\n\t\t\tvalue.length >= 0 &&\n\t\t\ttoStr.call(value.callee) === '[object Function]';\n\t}\n\treturn isArgs;\n};\n", "'use strict';\n\nvar keysShim;\nif (!Object.keys) {\n\t// modified from https://github.com/es-shims/es5-shim\n\tvar has = Object.prototype.hasOwnProperty;\n\tvar toStr = Object.prototype.toString;\n\tvar isArgs = require('./isArguments'); // eslint-disable-line global-require\n\tvar isEnumerable = Object.prototype.propertyIsEnumerable;\n\tvar hasDontEnumBug = !isEnumerable.call({ toString: null }, 'toString');\n\tvar hasProtoEnumBug = isEnumerable.call(function () {}, 'prototype');\n\tvar dontEnums = [\n\t\t'toString',\n\t\t'toLocaleString',\n\t\t'valueOf',\n\t\t'hasOwnProperty',\n\t\t'isPrototypeOf',\n\t\t'propertyIsEnumerable',\n\t\t'constructor'\n\t];\n\tvar equalsConstructorPrototype = function (o) {\n\t\tvar ctor = o.constructor;\n\t\treturn ctor && ctor.prototype === o;\n\t};\n\tvar excludedKeys = {\n\t\t$applicationCache: true,\n\t\t$console: true,\n\t\t$external: true,\n\t\t$frame: true,\n\t\t$frameElement: true,\n\t\t$frames: true,\n\t\t$innerHeight: true,\n\t\t$innerWidth: true,\n\t\t$onmozfullscreenchange: true,\n\t\t$onmozfullscreenerror: true,\n\t\t$outerHeight: true,\n\t\t$outerWidth: true,\n\t\t$pageXOffset: true,\n\t\t$pageYOffset: true,\n\t\t$parent: true,\n\t\t$scrollLeft: true,\n\t\t$scrollTop: true,\n\t\t$scrollX: true,\n\t\t$scrollY: true,\n\t\t$self: true,\n\t\t$webkitIndexedDB: true,\n\t\t$webkitStorageInfo: true,\n\t\t$window: true\n\t};\n\tvar hasAutomationEqualityBug = (function () {\n\t\t/* global window */\n\t\tif (typeof window === 'undefined') { return false; }\n\t\tfor (var k in window) {\n\t\t\ttry {\n\t\t\t\tif (!excludedKeys['$' + k] && has.call(window, k) && window[k] !== null && typeof window[k] === 'object') {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tequalsConstructorPrototype(window[k]);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}());\n\tvar equalsConstructorPrototypeIfNotBuggy = function (o) {\n\t\t/* global window */\n\t\tif (typeof window === 'undefined' || !hasAutomationEqualityBug) {\n\t\t\treturn equalsConstructorPrototype(o);\n\t\t}\n\t\ttry {\n\t\t\treturn equalsConstructorPrototype(o);\n\t\t} catch (e) {\n\t\t\treturn false;\n\t\t}\n\t};\n\n\tkeysShim = function keys(object) {\n\t\tvar isObject = object !== null && typeof object === 'object';\n\t\tvar isFunction = toStr.call(object) === '[object Function]';\n\t\tvar isArguments = isArgs(object);\n\t\tvar isString = isObject && toStr.call(object) === '[object String]';\n\t\tvar theKeys = [];\n\n\t\tif (!isObject && !isFunction && !isArguments) {\n\t\t\tthrow new TypeError('Object.keys called on a non-object');\n\t\t}\n\n\t\tvar skipProto = hasProtoEnumBug && isFunction;\n\t\tif (isString && object.length > 0 && !has.call(object, 0)) {\n\t\t\tfor (var i = 0; i < object.length; ++i) {\n\t\t\t\ttheKeys.push(String(i));\n\t\t\t}\n\t\t}\n\n\t\tif (isArguments && object.length > 0) {\n\t\t\tfor (var j = 0; j < object.length; ++j) {\n\t\t\t\ttheKeys.push(String(j));\n\t\t\t}\n\t\t} else {\n\t\t\tfor (var name in object) {\n\t\t\t\tif (!(skipProto && name === 'prototype') && has.call(object, name)) {\n\t\t\t\t\ttheKeys.push(String(name));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (hasDontEnumBug) {\n\t\t\tvar skipConstructor = equalsConstructorPrototypeIfNotBuggy(object);\n\n\t\t\tfor (var k = 0; k < dontEnums.length; ++k) {\n\t\t\t\tif (!(skipConstructor && dontEnums[k] === 'constructor') && has.call(object, dontEnums[k])) {\n\t\t\t\t\ttheKeys.push(dontEnums[k]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn theKeys;\n\t};\n}\nmodule.exports = keysShim;\n", "'use strict';\n\nvar slice = Array.prototype.slice;\nvar isArgs = require('./isArguments');\n\nvar origKeys = Object.keys;\nvar keysShim = origKeys ? function keys(o) { return origKeys(o); } : require('./implementation');\n\nvar originalKeys = Object.keys;\n\nkeysShim.shim = function shimObjectKeys() {\n\tif (Object.keys) {\n\t\tvar keysWorksWithArguments = (function () {\n\t\t\t// Safari 5.0 bug\n\t\t\tvar args = Object.keys(arguments);\n\t\t\treturn args && args.length === arguments.length;\n\t\t}(1, 2));\n\t\tif (!keysWorksWithArguments) {\n\t\t\tObject.keys = function keys(object) { // eslint-disable-line func-name-matching\n\t\t\t\tif (isArgs(object)) {\n\t\t\t\t\treturn originalKeys(slice.call(object));\n\t\t\t\t}\n\t\t\t\treturn originalKeys(object);\n\t\t\t};\n\t\t}\n\t} else {\n\t\tObject.keys = keysShim;\n\t}\n\treturn Object.keys || keysShim;\n};\n\nmodule.exports = keysShim;\n", "'use strict';\n\n/* eslint complexity: [2, 18], max-statements: [2, 33] */\nmodule.exports = function hasSymbols() {\n\tif (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') { return false; }\n\tif (typeof Symbol.iterator === 'symbol') { return true; }\n\n\tvar obj = {};\n\tvar sym = Symbol('test');\n\tvar symObj = Object(sym);\n\tif (typeof sym === 'string') { return false; }\n\n\tif (Object.prototype.toString.call(sym) !== '[object Symbol]') { return false; }\n\tif (Object.prototype.toString.call(symObj) !== '[object Symbol]') { return false; }\n\n\t// temp disabled per https://github.com/ljharb/object.assign/issues/17\n\t// if (sym instanceof Symbol) { return false; }\n\t// temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4\n\t// if (!(symObj instanceof Symbol)) { return false; }\n\n\t// if (typeof Symbol.prototype.toString !== 'function') { return false; }\n\t// if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }\n\n\tvar symVal = 42;\n\tobj[sym] = symVal;\n\tfor (sym in obj) { return false; } // eslint-disable-line no-restricted-syntax, no-unreachable-loop\n\tif (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) { return false; }\n\n\tif (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) { return false; }\n\n\tvar syms = Object.getOwnPropertySymbols(obj);\n\tif (syms.length !== 1 || syms[0] !== sym) { return false; }\n\n\tif (!Object.prototype.propertyIsEnumerable.call(obj, sym)) { return false; }\n\n\tif (typeof Object.getOwnPropertyDescriptor === 'function') {\n\t\tvar descriptor = Object.getOwnPropertyDescriptor(obj, sym);\n\t\tif (descriptor.value !== symVal || descriptor.enumerable !== true) { return false; }\n\t}\n\n\treturn true;\n};\n", "'use strict';\n\nvar hasSymbols = require('has-symbols/shams');\n\nmodule.exports = function hasToStringTagShams() {\n\treturn hasSymbols() && !!Symbol.toStringTag;\n};\n", "'use strict';\n\nvar origSymbol = typeof Symbol !== 'undefined' && Symbol;\nvar hasSymbolSham = require('./shams');\n\nmodule.exports = function hasNativeSymbols() {\n\tif (typeof origSymbol !== 'function') { return false; }\n\tif (typeof Symbol !== 'function') { return false; }\n\tif (typeof origSymbol('foo') !== 'symbol') { return false; }\n\tif (typeof Symbol('bar') !== 'symbol') { return false; }\n\n\treturn hasSymbolSham();\n};\n", "'use strict';\n\nvar test = {\n\tfoo: {}\n};\n\nvar $Object = Object;\n\nmodule.exports = function hasProto() {\n\treturn { __proto__: test }.foo === test.foo && !({ __proto__: null } instanceof $Object);\n};\n", "'use strict';\n\n/* eslint no-invalid-this: 1 */\n\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar slice = Array.prototype.slice;\nvar toStr = Object.prototype.toString;\nvar funcType = '[object Function]';\n\nmodule.exports = function bind(that) {\n    var target = this;\n    if (typeof target !== 'function' || toStr.call(target) !== funcType) {\n        throw new TypeError(ERROR_MESSAGE + target);\n    }\n    var args = slice.call(arguments, 1);\n\n    var bound;\n    var binder = function () {\n        if (this instanceof bound) {\n            var result = target.apply(\n                this,\n                args.concat(slice.call(arguments))\n            );\n            if (Object(result) === result) {\n                return result;\n            }\n            return this;\n        } else {\n            return target.apply(\n                that,\n                args.concat(slice.call(arguments))\n            );\n        }\n    };\n\n    var boundLength = Math.max(0, target.length - args.length);\n    var boundArgs = [];\n    for (var i = 0; i < boundLength; i++) {\n        boundArgs.push('$' + i);\n    }\n\n    bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this,arguments); }')(binder);\n\n    if (target.prototype) {\n        var Empty = function Empty() {};\n        Empty.prototype = target.prototype;\n        bound.prototype = new Empty();\n        Empty.prototype = null;\n    }\n\n    return bound;\n};\n", "'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = Function.prototype.bind || implementation;\n", "'use strict';\n\nvar bind = require('function-bind');\n\nmodule.exports = bind.call(Function.call, Object.prototype.hasOwnProperty);\n", "'use strict';\n\nvar undefined;\n\nvar $SyntaxError = SyntaxError;\nvar $Function = Function;\nvar $TypeError = TypeError;\n\n// eslint-disable-next-line consistent-return\nvar getEvalledConstructor = function (expressionSyntax) {\n\ttry {\n\t\treturn $Function('\"use strict\"; return (' + expressionSyntax + ').constructor;')();\n\t} catch (e) {}\n};\n\nvar $gOPD = Object.getOwnPropertyDescriptor;\nif ($gOPD) {\n\ttry {\n\t\t$gOPD({}, '');\n\t} catch (e) {\n\t\t$gOPD = null; // this is IE 8, which has a broken gOPD\n\t}\n}\n\nvar throwTypeError = function () {\n\tthrow new $TypeError();\n};\nvar ThrowTypeError = $gOPD\n\t? (function () {\n\t\ttry {\n\t\t\t// eslint-disable-next-line no-unused-expressions, no-caller, no-restricted-properties\n\t\t\targuments.callee; // IE 8 does not throw here\n\t\t\treturn throwTypeError;\n\t\t} catch (calleeThrows) {\n\t\t\ttry {\n\t\t\t\t// IE 8 throws on Object.getOwnPropertyDescriptor(arguments, '')\n\t\t\t\treturn $gOPD(arguments, 'callee').get;\n\t\t\t} catch (gOPDthrows) {\n\t\t\t\treturn throwTypeError;\n\t\t\t}\n\t\t}\n\t}())\n\t: throwTypeError;\n\nvar hasSymbols = require('has-symbols')();\nvar hasProto = require('has-proto')();\n\nvar getProto = Object.getPrototypeOf || (\n\thasProto\n\t\t? function (x) { return x.__proto__; } // eslint-disable-line no-proto\n\t\t: null\n);\n\nvar needsEval = {};\n\nvar TypedArray = typeof Uint8Array === 'undefined' || !getProto ? undefined : getProto(Uint8Array);\n\nvar INTRINSICS = {\n\t'%AggregateError%': typeof AggregateError === 'undefined' ? undefined : AggregateError,\n\t'%Array%': Array,\n\t'%ArrayBuffer%': typeof ArrayBuffer === 'undefined' ? undefined : ArrayBuffer,\n\t'%ArrayIteratorPrototype%': hasSymbols && getProto ? getProto([][Symbol.iterator]()) : undefined,\n\t'%AsyncFromSyncIteratorPrototype%': undefined,\n\t'%AsyncFunction%': needsEval,\n\t'%AsyncGenerator%': needsEval,\n\t'%AsyncGeneratorFunction%': needsEval,\n\t'%AsyncIteratorPrototype%': needsEval,\n\t'%Atomics%': typeof Atomics === 'undefined' ? undefined : Atomics,\n\t'%BigInt%': typeof BigInt === 'undefined' ? undefined : BigInt,\n\t'%BigInt64Array%': typeof BigInt64Array === 'undefined' ? undefined : BigInt64Array,\n\t'%BigUint64Array%': typeof BigUint64Array === 'undefined' ? undefined : BigUint64Array,\n\t'%Boolean%': Boolean,\n\t'%DataView%': typeof DataView === 'undefined' ? undefined : DataView,\n\t'%Date%': Date,\n\t'%decodeURI%': decodeURI,\n\t'%decodeURIComponent%': decodeURIComponent,\n\t'%encodeURI%': encodeURI,\n\t'%encodeURIComponent%': encodeURIComponent,\n\t'%Error%': Error,\n\t'%eval%': eval, // eslint-disable-line no-eval\n\t'%EvalError%': EvalError,\n\t'%Float32Array%': typeof Float32Array === 'undefined' ? undefined : Float32Array,\n\t'%Float64Array%': typeof Float64Array === 'undefined' ? undefined : Float64Array,\n\t'%FinalizationRegistry%': typeof FinalizationRegistry === 'undefined' ? undefined : FinalizationRegistry,\n\t'%Function%': $Function,\n\t'%GeneratorFunction%': needsEval,\n\t'%Int8Array%': typeof Int8Array === 'undefined' ? undefined : Int8Array,\n\t'%Int16Array%': typeof Int16Array === 'undefined' ? undefined : Int16Array,\n\t'%Int32Array%': typeof Int32Array === 'undefined' ? undefined : Int32Array,\n\t'%isFinite%': isFinite,\n\t'%isNaN%': isNaN,\n\t'%IteratorPrototype%': hasSymbols && getProto ? getProto(getProto([][Symbol.iterator]())) : undefined,\n\t'%JSON%': typeof JSON === 'object' ? JSON : undefined,\n\t'%Map%': typeof Map === 'undefined' ? undefined : Map,\n\t'%MapIteratorPrototype%': typeof Map === 'undefined' || !hasSymbols || !getProto ? undefined : getProto(new Map()[Symbol.iterator]()),\n\t'%Math%': Math,\n\t'%Number%': Number,\n\t'%Object%': Object,\n\t'%parseFloat%': parseFloat,\n\t'%parseInt%': parseInt,\n\t'%Promise%': typeof Promise === 'undefined' ? undefined : Promise,\n\t'%Proxy%': typeof Proxy === 'undefined' ? undefined : Proxy,\n\t'%RangeError%': RangeError,\n\t'%ReferenceError%': ReferenceError,\n\t'%Reflect%': typeof Reflect === 'undefined' ? undefined : Reflect,\n\t'%RegExp%': RegExp,\n\t'%Set%': typeof Set === 'undefined' ? undefined : Set,\n\t'%SetIteratorPrototype%': typeof Set === 'undefined' || !hasSymbols || !getProto ? undefined : getProto(new Set()[Symbol.iterator]()),\n\t'%SharedArrayBuffer%': typeof SharedArrayBuffer === 'undefined' ? undefined : SharedArrayBuffer,\n\t'%String%': String,\n\t'%StringIteratorPrototype%': hasSymbols && getProto ? getProto(''[Symbol.iterator]()) : undefined,\n\t'%Symbol%': hasSymbols ? Symbol : undefined,\n\t'%SyntaxError%': $SyntaxError,\n\t'%ThrowTypeError%': ThrowTypeError,\n\t'%TypedArray%': TypedArray,\n\t'%TypeError%': $TypeError,\n\t'%Uint8Array%': typeof Uint8Array === 'undefined' ? undefined : Uint8Array,\n\t'%Uint8ClampedArray%': typeof Uint8ClampedArray === 'undefined' ? undefined : Uint8ClampedArray,\n\t'%Uint16Array%': typeof Uint16Array === 'undefined' ? undefined : Uint16Array,\n\t'%Uint32Array%': typeof Uint32Array === 'undefined' ? undefined : Uint32Array,\n\t'%URIError%': URIError,\n\t'%WeakMap%': typeof WeakMap === 'undefined' ? undefined : WeakMap,\n\t'%WeakRef%': typeof WeakRef === 'undefined' ? undefined : WeakRef,\n\t'%WeakSet%': typeof WeakSet === 'undefined' ? undefined : WeakSet\n};\n\nif (getProto) {\n\ttry {\n\t\tnull.error; // eslint-disable-line no-unused-expressions\n\t} catch (e) {\n\t\t// https://github.com/tc39/proposal-shadowrealm/pull/384#issuecomment-1364264229\n\t\tvar errorProto = getProto(getProto(e));\n\t\tINTRINSICS['%Error.prototype%'] = errorProto;\n\t}\n}\n\nvar doEval = function doEval(name) {\n\tvar value;\n\tif (name === '%AsyncFunction%') {\n\t\tvalue = getEvalledConstructor('async function () {}');\n\t} else if (name === '%GeneratorFunction%') {\n\t\tvalue = getEvalledConstructor('function* () {}');\n\t} else if (name === '%AsyncGeneratorFunction%') {\n\t\tvalue = getEvalledConstructor('async function* () {}');\n\t} else if (name === '%AsyncGenerator%') {\n\t\tvar fn = doEval('%AsyncGeneratorFunction%');\n\t\tif (fn) {\n\t\t\tvalue = fn.prototype;\n\t\t}\n\t} else if (name === '%AsyncIteratorPrototype%') {\n\t\tvar gen = doEval('%AsyncGenerator%');\n\t\tif (gen && getProto) {\n\t\t\tvalue = getProto(gen.prototype);\n\t\t}\n\t}\n\n\tINTRINSICS[name] = value;\n\n\treturn value;\n};\n\nvar LEGACY_ALIASES = {\n\t'%ArrayBufferPrototype%': ['ArrayBuffer', 'prototype'],\n\t'%ArrayPrototype%': ['Array', 'prototype'],\n\t'%ArrayProto_entries%': ['Array', 'prototype', 'entries'],\n\t'%ArrayProto_forEach%': ['Array', 'prototype', 'forEach'],\n\t'%ArrayProto_keys%': ['Array', 'prototype', 'keys'],\n\t'%ArrayProto_values%': ['Array', 'prototype', 'values'],\n\t'%AsyncFunctionPrototype%': ['AsyncFunction', 'prototype'],\n\t'%AsyncGenerator%': ['AsyncGeneratorFunction', 'prototype'],\n\t'%AsyncGeneratorPrototype%': ['AsyncGeneratorFunction', 'prototype', 'prototype'],\n\t'%BooleanPrototype%': ['Boolean', 'prototype'],\n\t'%DataViewPrototype%': ['DataView', 'prototype'],\n\t'%DatePrototype%': ['Date', 'prototype'],\n\t'%ErrorPrototype%': ['Error', 'prototype'],\n\t'%EvalErrorPrototype%': ['EvalError', 'prototype'],\n\t'%Float32ArrayPrototype%': ['Float32Array', 'prototype'],\n\t'%Float64ArrayPrototype%': ['Float64Array', 'prototype'],\n\t'%FunctionPrototype%': ['Function', 'prototype'],\n\t'%Generator%': ['GeneratorFunction', 'prototype'],\n\t'%GeneratorPrototype%': ['GeneratorFunction', 'prototype', 'prototype'],\n\t'%Int8ArrayPrototype%': ['Int8Array', 'prototype'],\n\t'%Int16ArrayPrototype%': ['Int16Array', 'prototype'],\n\t'%Int32ArrayPrototype%': ['Int32Array', 'prototype'],\n\t'%JSONParse%': ['JSON', 'parse'],\n\t'%JSONStringify%': ['JSON', 'stringify'],\n\t'%MapPrototype%': ['Map', 'prototype'],\n\t'%NumberPrototype%': ['Number', 'prototype'],\n\t'%ObjectPrototype%': ['Object', 'prototype'],\n\t'%ObjProto_toString%': ['Object', 'prototype', 'toString'],\n\t'%ObjProto_valueOf%': ['Object', 'prototype', 'valueOf'],\n\t'%PromisePrototype%': ['Promise', 'prototype'],\n\t'%PromiseProto_then%': ['Promise', 'prototype', 'then'],\n\t'%Promise_all%': ['Promise', 'all'],\n\t'%Promise_reject%': ['Promise', 'reject'],\n\t'%Promise_resolve%': ['Promise', 'resolve'],\n\t'%RangeErrorPrototype%': ['RangeError', 'prototype'],\n\t'%ReferenceErrorPrototype%': ['ReferenceError', 'prototype'],\n\t'%RegExpPrototype%': ['RegExp', 'prototype'],\n\t'%SetPrototype%': ['Set', 'prototype'],\n\t'%SharedArrayBufferPrototype%': ['SharedArrayBuffer', 'prototype'],\n\t'%StringPrototype%': ['String', 'prototype'],\n\t'%SymbolPrototype%': ['Symbol', 'prototype'],\n\t'%SyntaxErrorPrototype%': ['SyntaxError', 'prototype'],\n\t'%TypedArrayPrototype%': ['TypedArray', 'prototype'],\n\t'%TypeErrorPrototype%': ['TypeError', 'prototype'],\n\t'%Uint8ArrayPrototype%': ['Uint8Array', 'prototype'],\n\t'%Uint8ClampedArrayPrototype%': ['Uint8ClampedArray', 'prototype'],\n\t'%Uint16ArrayPrototype%': ['Uint16Array', 'prototype'],\n\t'%Uint32ArrayPrototype%': ['Uint32Array', 'prototype'],\n\t'%URIErrorPrototype%': ['URIError', 'prototype'],\n\t'%WeakMapPrototype%': ['WeakMap', 'prototype'],\n\t'%WeakSetPrototype%': ['WeakSet', 'prototype']\n};\n\nvar bind = require('function-bind');\nvar hasOwn = require('has');\nvar $concat = bind.call(Function.call, Array.prototype.concat);\nvar $spliceApply = bind.call(Function.apply, Array.prototype.splice);\nvar $replace = bind.call(Function.call, String.prototype.replace);\nvar $strSlice = bind.call(Function.call, String.prototype.slice);\nvar $exec = bind.call(Function.call, RegExp.prototype.exec);\n\n/* adapted from https://github.com/lodash/lodash/blob/4.17.15/dist/lodash.js#L6735-L6744 */\nvar rePropName = /[^%.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|%$))/g;\nvar reEscapeChar = /\\\\(\\\\)?/g; /** Used to match backslashes in property paths. */\nvar stringToPath = function stringToPath(string) {\n\tvar first = $strSlice(string, 0, 1);\n\tvar last = $strSlice(string, -1);\n\tif (first === '%' && last !== '%') {\n\t\tthrow new $SyntaxError('invalid intrinsic syntax, expected closing `%`');\n\t} else if (last === '%' && first !== '%') {\n\t\tthrow new $SyntaxError('invalid intrinsic syntax, expected opening `%`');\n\t}\n\tvar result = [];\n\t$replace(string, rePropName, function (match, number, quote, subString) {\n\t\tresult[result.length] = quote ? $replace(subString, reEscapeChar, '$1') : number || match;\n\t});\n\treturn result;\n};\n/* end adaptation */\n\nvar getBaseIntrinsic = function getBaseIntrinsic(name, allowMissing) {\n\tvar intrinsicName = name;\n\tvar alias;\n\tif (hasOwn(LEGACY_ALIASES, intrinsicName)) {\n\t\talias = LEGACY_ALIASES[intrinsicName];\n\t\tintrinsicName = '%' + alias[0] + '%';\n\t}\n\n\tif (hasOwn(INTRINSICS, intrinsicName)) {\n\t\tvar value = INTRINSICS[intrinsicName];\n\t\tif (value === needsEval) {\n\t\t\tvalue = doEval(intrinsicName);\n\t\t}\n\t\tif (typeof value === 'undefined' && !allowMissing) {\n\t\t\tthrow new $TypeError('intrinsic ' + name + ' exists, but is not available. Please file an issue!');\n\t\t}\n\n\t\treturn {\n\t\t\talias: alias,\n\t\t\tname: intrinsicName,\n\t\t\tvalue: value\n\t\t};\n\t}\n\n\tthrow new $SyntaxError('intrinsic ' + name + ' does not exist!');\n};\n\nmodule.exports = function GetIntrinsic(name, allowMissing) {\n\tif (typeof name !== 'string' || name.length === 0) {\n\t\tthrow new $TypeError('intrinsic name must be a non-empty string');\n\t}\n\tif (arguments.length > 1 && typeof allowMissing !== 'boolean') {\n\t\tthrow new $TypeError('\"allowMissing\" argument must be a boolean');\n\t}\n\n\tif ($exec(/^%?[^%]*%?$/, name) === null) {\n\t\tthrow new $SyntaxError('`%` may not be present anywhere but at the beginning and end of the intrinsic name');\n\t}\n\tvar parts = stringToPath(name);\n\tvar intrinsicBaseName = parts.length > 0 ? parts[0] : '';\n\n\tvar intrinsic = getBaseIntrinsic('%' + intrinsicBaseName + '%', allowMissing);\n\tvar intrinsicRealName = intrinsic.name;\n\tvar value = intrinsic.value;\n\tvar skipFurtherCaching = false;\n\n\tvar alias = intrinsic.alias;\n\tif (alias) {\n\t\tintrinsicBaseName = alias[0];\n\t\t$spliceApply(parts, $concat([0, 1], alias));\n\t}\n\n\tfor (var i = 1, isOwn = true; i < parts.length; i += 1) {\n\t\tvar part = parts[i];\n\t\tvar first = $strSlice(part, 0, 1);\n\t\tvar last = $strSlice(part, -1);\n\t\tif (\n\t\t\t(\n\t\t\t\t(first === '\"' || first === \"'\" || first === '`')\n\t\t\t\t|| (last === '\"' || last === \"'\" || last === '`')\n\t\t\t)\n\t\t\t&& first !== last\n\t\t) {\n\t\t\tthrow new $SyntaxError('property names with quotes must have matching quotes');\n\t\t}\n\t\tif (part === 'constructor' || !isOwn) {\n\t\t\tskipFurtherCaching = true;\n\t\t}\n\n\t\tintrinsicBaseName += '.' + part;\n\t\tintrinsicRealName = '%' + intrinsicBaseName + '%';\n\n\t\tif (hasOwn(INTRINSICS, intrinsicRealName)) {\n\t\t\tvalue = INTRINSICS[intrinsicRealName];\n\t\t} else if (value != null) {\n\t\t\tif (!(part in value)) {\n\t\t\t\tif (!allowMissing) {\n\t\t\t\t\tthrow new $TypeError('base intrinsic for ' + name + ' exists, but the property is not available.');\n\t\t\t\t}\n\t\t\t\treturn void undefined;\n\t\t\t}\n\t\t\tif ($gOPD && (i + 1) >= parts.length) {\n\t\t\t\tvar desc = $gOPD(value, part);\n\t\t\t\tisOwn = !!desc;\n\n\t\t\t\t// By convention, when a data property is converted to an accessor\n\t\t\t\t// property to emulate a data property that does not suffer from\n\t\t\t\t// the override mistake, that accessor's getter is marked with\n\t\t\t\t// an `originalValue` property. Here, when we detect this, we\n\t\t\t\t// uphold the illusion by pretending to see that original data\n\t\t\t\t// property, i.e., returning the value rather than the getter\n\t\t\t\t// itself.\n\t\t\t\tif (isOwn && 'get' in desc && !('originalValue' in desc.get)) {\n\t\t\t\t\tvalue = desc.get;\n\t\t\t\t} else {\n\t\t\t\t\tvalue = value[part];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tisOwn = hasOwn(value, part);\n\t\t\t\tvalue = value[part];\n\t\t\t}\n\n\t\t\tif (isOwn && !skipFurtherCaching) {\n\t\t\t\tINTRINSICS[intrinsicRealName] = value;\n\t\t\t}\n\t\t}\n\t}\n\treturn value;\n};\n", "'use strict';\n\nvar bind = require('function-bind');\nvar GetIntrinsic = require('get-intrinsic');\n\nvar $apply = GetIntrinsic('%Function.prototype.apply%');\nvar $call = GetIntrinsic('%Function.prototype.call%');\nvar $reflectApply = GetIntrinsic('%Reflect.apply%', true) || bind.call($call, $apply);\n\nvar $gOPD = GetIntrinsic('%Object.getOwnPropertyDescriptor%', true);\nvar $defineProperty = GetIntrinsic('%Object.defineProperty%', true);\nvar $max = GetIntrinsic('%Math.max%');\n\nif ($defineProperty) {\n\ttry {\n\t\t$defineProperty({}, 'a', { value: 1 });\n\t} catch (e) {\n\t\t// IE 8 has a broken defineProperty\n\t\t$defineProperty = null;\n\t}\n}\n\nmodule.exports = function callBind(originalFunction) {\n\tvar func = $reflectApply(bind, $call, arguments);\n\tif ($gOPD && $defineProperty) {\n\t\tvar desc = $gOPD(func, 'length');\n\t\tif (desc.configurable) {\n\t\t\t// original length, plus the receiver, minus any additional arguments (after the receiver)\n\t\t\t$defineProperty(\n\t\t\t\tfunc,\n\t\t\t\t'length',\n\t\t\t\t{ value: 1 + $max(0, originalFunction.length - (arguments.length - 1)) }\n\t\t\t);\n\t\t}\n\t}\n\treturn func;\n};\n\nvar applyBind = function applyBind() {\n\treturn $reflectApply(bind, $apply, arguments);\n};\n\nif ($defineProperty) {\n\t$defineProperty(module.exports, 'apply', { value: applyBind });\n} else {\n\tmodule.exports.apply = applyBind;\n}\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar callBind = require('./');\n\nvar $indexOf = callBind(GetIntrinsic('String.prototype.indexOf'));\n\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n\tvar intrinsic = GetIntrinsic(name, !!allowMissing);\n\tif (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n\t\treturn callBind(intrinsic);\n\t}\n\treturn intrinsic;\n};\n", "'use strict';\n\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar callBound = require('call-bind/callBound');\n\nvar $toString = callBound('Object.prototype.toString');\n\nvar isStandardArguments = function isArguments(value) {\n\tif (hasToStringTag && value && typeof value === 'object' && Symbol.toStringTag in value) {\n\t\treturn false;\n\t}\n\treturn $toString(value) === '[object Arguments]';\n};\n\nvar isLegacyArguments = function isArguments(value) {\n\tif (isStandardArguments(value)) {\n\t\treturn true;\n\t}\n\treturn value !== null &&\n\t\ttypeof value === 'object' &&\n\t\ttypeof value.length === 'number' &&\n\t\tvalue.length >= 0 &&\n\t\t$toString(value) !== '[object Array]' &&\n\t\t$toString(value.callee) === '[object Function]';\n};\n\nvar supportsStandardArguments = (function () {\n\treturn isStandardArguments(arguments);\n}());\n\nisStandardArguments.isLegacyArguments = isLegacyArguments; // for tests\n\nmodule.exports = supportsStandardArguments ? isStandardArguments : isLegacyArguments;\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar $defineProperty = GetIntrinsic('%Object.defineProperty%', true);\n\nvar hasPropertyDescriptors = function hasPropertyDescriptors() {\n\tif ($defineProperty) {\n\t\ttry {\n\t\t\t$defineProperty({}, 'a', { value: 1 });\n\t\t\treturn true;\n\t\t} catch (e) {\n\t\t\t// IE 8 has a broken defineProperty\n\t\t\treturn false;\n\t\t}\n\t}\n\treturn false;\n};\n\nhasPropertyDescriptors.hasArrayLengthDefineBug = function hasArrayLengthDefineBug() {\n\t// node v0.6 has a bug where array lengths can be Set but not Defined\n\tif (!hasPropertyDescriptors()) {\n\t\treturn null;\n\t}\n\ttry {\n\t\treturn $defineProperty([], 'length', { value: 1 }).length !== 1;\n\t} catch (e) {\n\t\t// In Firefox 4-22, defining length on an array throws an exception.\n\t\treturn true;\n\t}\n};\n\nmodule.exports = hasPropertyDescriptors;\n", "'use strict';\n\nvar keys = require('object-keys');\nvar hasSymbols = typeof Symbol === 'function' && typeof Symbol('foo') === 'symbol';\n\nvar toStr = Object.prototype.toString;\nvar concat = Array.prototype.concat;\nvar origDefineProperty = Object.defineProperty;\n\nvar isFunction = function (fn) {\n\treturn typeof fn === 'function' && toStr.call(fn) === '[object Function]';\n};\n\nvar hasPropertyDescriptors = require('has-property-descriptors')();\n\nvar supportsDescriptors = origDefineProperty && hasPropertyDescriptors;\n\nvar defineProperty = function (object, name, value, predicate) {\n\tif (name in object) {\n\t\tif (predicate === true) {\n\t\t\tif (object[name] === value) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t} else if (!isFunction(predicate) || !predicate()) {\n\t\t\treturn;\n\t\t}\n\t}\n\tif (supportsDescriptors) {\n\t\torigDefineProperty(object, name, {\n\t\t\tconfigurable: true,\n\t\t\tenumerable: false,\n\t\t\tvalue: value,\n\t\t\twritable: true\n\t\t});\n\t} else {\n\t\tobject[name] = value; // eslint-disable-line no-param-reassign\n\t}\n};\n\nvar defineProperties = function (object, map) {\n\tvar predicates = arguments.length > 2 ? arguments[2] : {};\n\tvar props = keys(map);\n\tif (hasSymbols) {\n\t\tprops = concat.call(props, Object.getOwnPropertySymbols(map));\n\t}\n\tfor (var i = 0; i < props.length; i += 1) {\n\t\tdefineProperty(object, props[i], map[props[i]], predicates[props[i]]);\n\t}\n};\n\ndefineProperties.supportsDescriptors = !!supportsDescriptors;\n\nmodule.exports = defineProperties;\n", "'use strict';\n\nvar numberIsNaN = function (value) {\n\treturn value !== value;\n};\n\nmodule.exports = function is(a, b) {\n\tif (a === 0 && b === 0) {\n\t\treturn 1 / a === 1 / b;\n\t}\n\tif (a === b) {\n\t\treturn true;\n\t}\n\tif (numberIsNaN(a) && numberIsNaN(b)) {\n\t\treturn true;\n\t}\n\treturn false;\n};\n\n", "'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = function getPolyfill() {\n\treturn typeof Object.is === 'function' ? Object.is : implementation;\n};\n", "'use strict';\n\nvar getPolyfill = require('./polyfill');\nvar define = require('define-properties');\n\nmodule.exports = function shimObjectIs() {\n\tvar polyfill = getPolyfill();\n\tdefine(Object, { is: polyfill }, {\n\t\tis: function testObjectIs() {\n\t\t\treturn Object.is !== polyfill;\n\t\t}\n\t});\n\treturn polyfill;\n};\n", "'use strict';\n\nvar define = require('define-properties');\nvar callBind = require('call-bind');\n\nvar implementation = require('./implementation');\nvar getPolyfill = require('./polyfill');\nvar shim = require('./shim');\n\nvar polyfill = callBind(getPolyfill(), Object);\n\ndefine(polyfill, {\n\tgetPolyfill: getPolyfill,\n\timplementation: implementation,\n\tshim: shim\n});\n\nmodule.exports = polyfill;\n", "'use strict';\n\nvar callBound = require('call-bind/callBound');\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar has;\nvar $exec;\nvar isRegexMarker;\nvar badStringifier;\n\nif (hasToStringTag) {\n\thas = callBound('Object.prototype.hasOwnProperty');\n\t$exec = callBound('RegExp.prototype.exec');\n\tisRegexMarker = {};\n\n\tvar throwRegexMarker = function () {\n\t\tthrow isRegexMarker;\n\t};\n\tbadStringifier = {\n\t\ttoString: throwRegexMarker,\n\t\tvalueOf: throwRegexMarker\n\t};\n\n\tif (typeof Symbol.toPrimitive === 'symbol') {\n\t\tbadStringifier[Symbol.toPrimitive] = throwRegexMarker;\n\t}\n}\n\nvar $toString = callBound('Object.prototype.toString');\nvar gOPD = Object.getOwnPropertyDescriptor;\nvar regexClass = '[object RegExp]';\n\nmodule.exports = hasToStringTag\n\t// eslint-disable-next-line consistent-return\n\t? function isRegex(value) {\n\t\tif (!value || typeof value !== 'object') {\n\t\t\treturn false;\n\t\t}\n\n\t\tvar descriptor = gOPD(value, 'lastIndex');\n\t\tvar hasLastIndexDataProperty = descriptor && has(descriptor, 'value');\n\t\tif (!hasLastIndexDataProperty) {\n\t\t\treturn false;\n\t\t}\n\n\t\ttry {\n\t\t\t$exec(value, badStringifier);\n\t\t} catch (e) {\n\t\t\treturn e === isRegexMarker;\n\t\t}\n\t}\n\t: function isRegex(value) {\n\t\t// In older browsers, typeof regex incorrectly returns 'function'\n\t\tif (!value || (typeof value !== 'object' && typeof value !== 'function')) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn $toString(value) === regexClass;\n\t};\n", "'use strict';\n\nvar functionsHaveNames = function functionsHaveNames() {\n\treturn typeof function f() {}.name === 'string';\n};\n\nvar gOPD = Object.getOwnPropertyDescriptor;\nif (gOPD) {\n\ttry {\n\t\tgOPD([], 'length');\n\t} catch (e) {\n\t\t// IE 8 has a broken gOPD\n\t\tgOPD = null;\n\t}\n}\n\nfunctionsHaveNames.functionsHaveConfigurableNames = function functionsHaveConfigurableNames() {\n\tif (!functionsHaveNames() || !gOPD) {\n\t\treturn false;\n\t}\n\tvar desc = gOPD(function () {}, 'name');\n\treturn !!desc && !!desc.configurable;\n};\n\nvar $bind = Function.prototype.bind;\n\nfunctionsHaveNames.boundFunctionsHaveNames = function boundFunctionsHaveNames() {\n\treturn functionsHaveNames() && typeof $bind === 'function' && function f() {}.bind().name !== '';\n};\n\nmodule.exports = functionsHaveNames;\n", "'use strict';\n\nvar functionsHaveConfigurableNames = require('functions-have-names').functionsHaveConfigurableNames();\n\nvar $Object = Object;\nvar $TypeError = TypeError;\n\nmodule.exports = function flags() {\n\tif (this != null && this !== $Object(this)) {\n\t\tthrow new $TypeError('RegExp.prototype.flags getter called on non-object');\n\t}\n\tvar result = '';\n\tif (this.hasIndices) {\n\t\tresult += 'd';\n\t}\n\tif (this.global) {\n\t\tresult += 'g';\n\t}\n\tif (this.ignoreCase) {\n\t\tresult += 'i';\n\t}\n\tif (this.multiline) {\n\t\tresult += 'm';\n\t}\n\tif (this.dotAll) {\n\t\tresult += 's';\n\t}\n\tif (this.unicode) {\n\t\tresult += 'u';\n\t}\n\tif (this.unicodeSets) {\n\t\tresult += 'v';\n\t}\n\tif (this.sticky) {\n\t\tresult += 'y';\n\t}\n\treturn result;\n};\n\nif (functionsHaveConfigurableNames && Object.defineProperty) {\n\tObject.defineProperty(module.exports, 'name', { value: 'get flags' });\n}\n", "'use strict';\n\nvar implementation = require('./implementation');\n\nvar supportsDescriptors = require('define-properties').supportsDescriptors;\nvar $gOPD = Object.getOwnPropertyDescriptor;\n\nmodule.exports = function getPolyfill() {\n\tif (supportsDescriptors && (/a/mig).flags === 'gim') {\n\t\tvar descriptor = $gOPD(RegExp.prototype, 'flags');\n\t\tif (\n\t\t\tdescriptor\n\t\t\t&& typeof descriptor.get === 'function'\n\t\t\t&& typeof RegExp.prototype.dotAll === 'boolean'\n\t\t\t&& typeof RegExp.prototype.hasIndices === 'boolean'\n\t\t) {\n\t\t\t/* eslint getter-return: 0 */\n\t\t\tvar calls = '';\n\t\t\tvar o = {};\n\t\t\tObject.defineProperty(o, 'hasIndices', {\n\t\t\t\tget: function () {\n\t\t\t\t\tcalls += 'd';\n\t\t\t\t}\n\t\t\t});\n\t\t\tObject.defineProperty(o, 'sticky', {\n\t\t\t\tget: function () {\n\t\t\t\t\tcalls += 'y';\n\t\t\t\t}\n\t\t\t});\n\t\t\tif (calls === 'dy') {\n\t\t\t\treturn descriptor.get;\n\t\t\t}\n\t\t}\n\t}\n\treturn implementation;\n};\n", "'use strict';\n\nvar supportsDescriptors = require('define-properties').supportsDescriptors;\nvar getPolyfill = require('./polyfill');\nvar gOPD = Object.getOwnPropertyDescriptor;\nvar defineProperty = Object.defineProperty;\nvar TypeErr = TypeError;\nvar getProto = Object.getPrototypeOf;\nvar regex = /a/;\n\nmodule.exports = function shimFlags() {\n\tif (!supportsDescriptors || !getProto) {\n\t\tthrow new TypeErr('RegExp.prototype.flags requires a true ES5 environment that supports property descriptors');\n\t}\n\tvar polyfill = getPolyfill();\n\tvar proto = getProto(regex);\n\tvar descriptor = gOPD(proto, 'flags');\n\tif (!descriptor || descriptor.get !== polyfill) {\n\t\tdefineProperty(proto, 'flags', {\n\t\t\tconfigurable: true,\n\t\t\tenumerable: false,\n\t\t\tget: polyfill\n\t\t});\n\t}\n\treturn polyfill;\n};\n", "'use strict';\n\nvar define = require('define-properties');\nvar callBind = require('call-bind');\n\nvar implementation = require('./implementation');\nvar getPolyfill = require('./polyfill');\nvar shim = require('./shim');\n\nvar flagsBound = callBind(getPolyfill());\n\ndefine(flagsBound, {\n\tgetPolyfill: getPolyfill,\n\timplementation: implementation,\n\tshim: shim\n});\n\nmodule.exports = flagsBound;\n", "'use strict';\n\nvar getDay = Date.prototype.getDay;\nvar tryDateObject = function tryDateGetDayCall(value) {\n\ttry {\n\t\tgetDay.call(value);\n\t\treturn true;\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\n\nvar toStr = Object.prototype.toString;\nvar dateClass = '[object Date]';\nvar hasToStringTag = require('has-tostringtag/shams')();\n\nmodule.exports = function isDateObject(value) {\n\tif (typeof value !== 'object' || value === null) {\n\t\treturn false;\n\t}\n\treturn hasToStringTag ? tryDateObject(value) : toStr.call(value) === dateClass;\n};\n", "var objectKeys = require('object-keys');\nvar isArguments = require('is-arguments');\nvar is = require('object-is');\nvar isRegex = require('is-regex');\nvar flags = require('regexp.prototype.flags');\nvar isDate = require('is-date-object');\n\nvar getTime = Date.prototype.getTime;\n\nfunction deepEqual(actual, expected, options) {\n  var opts = options || {};\n\n  // 7.1. All identical values are equivalent, as determined by ===.\n  if (opts.strict ? is(actual, expected) : actual === expected) {\n    return true;\n  }\n\n  // 7.3. Other pairs that do not both pass typeof value == 'object', equivalence is determined by ==.\n  if (!actual || !expected || (typeof actual !== 'object' && typeof expected !== 'object')) {\n    return opts.strict ? is(actual, expected) : actual == expected;\n  }\n\n  /*\n   * 7.4. For all other Object pairs, including Array objects, equivalence is\n   * determined by having the same number of owned properties (as verified\n   * with Object.prototype.hasOwnProperty.call), the same set of keys\n   * (although not necessarily the same order), equivalent values for every\n   * corresponding key, and an identical 'prototype' property. Note: this\n   * accounts for both named and indexed properties on Arrays.\n   */\n  // eslint-disable-next-line no-use-before-define\n  return objEquiv(actual, expected, opts);\n}\n\nfunction isUndefinedOrNull(value) {\n  return value === null || value === undefined;\n}\n\nfunction isBuffer(x) {\n  if (!x || typeof x !== 'object' || typeof x.length !== 'number') {\n    return false;\n  }\n  if (typeof x.copy !== 'function' || typeof x.slice !== 'function') {\n    return false;\n  }\n  if (x.length > 0 && typeof x[0] !== 'number') {\n    return false;\n  }\n  return true;\n}\n\nfunction objEquiv(a, b, opts) {\n  /* eslint max-statements: [2, 50] */\n  var i, key;\n  if (typeof a !== typeof b) { return false; }\n  if (isUndefinedOrNull(a) || isUndefinedOrNull(b)) { return false; }\n\n  // an identical 'prototype' property.\n  if (a.prototype !== b.prototype) { return false; }\n\n  if (isArguments(a) !== isArguments(b)) { return false; }\n\n  var aIsRegex = isRegex(a);\n  var bIsRegex = isRegex(b);\n  if (aIsRegex !== bIsRegex) { return false; }\n  if (aIsRegex || bIsRegex) {\n    return a.source === b.source && flags(a) === flags(b);\n  }\n\n  if (isDate(a) && isDate(b)) {\n    return getTime.call(a) === getTime.call(b);\n  }\n\n  var aIsBuffer = isBuffer(a);\n  var bIsBuffer = isBuffer(b);\n  if (aIsBuffer !== bIsBuffer) { return false; }\n  if (aIsBuffer || bIsBuffer) { // && would work too, because both are true or both false here\n    if (a.length !== b.length) { return false; }\n    for (i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) { return false; }\n    }\n    return true;\n  }\n\n  if (typeof a !== typeof b) { return false; }\n\n  try {\n    var ka = objectKeys(a);\n    var kb = objectKeys(b);\n  } catch (e) { // happens when one is a string literal and the other isn't\n    return false;\n  }\n  // having the same number of owned properties (keys incorporates hasOwnProperty)\n  if (ka.length !== kb.length) { return false; }\n\n  // the same set of keys (although not necessarily the same order),\n  ka.sort();\n  kb.sort();\n  // ~~~cheap key test\n  for (i = ka.length - 1; i >= 0; i--) {\n    if (ka[i] != kb[i]) { return false; }\n  }\n  // equivalent values for every corresponding key, and ~~~possibly expensive deep test\n  for (i = ka.length - 1; i >= 0; i--) {\n    key = ka[i];\n    if (!deepEqual(a[key], b[key], opts)) { return false; }\n  }\n\n  return true;\n}\n\nmodule.exports = deepEqual;\n", "// @flow\n'use strict';\n\nvar key = '__global_unique_id__';\n\nmodule.exports = function() {\n  return global[key] = (global[key] || 0) + 1;\n};\n", "'use strict';\n\nexports.__esModule = true;\n\nvar _react = require('react');\n\nvar _react2 = _interopRequireDefault(_react);\n\nvar _propTypes = require('prop-types');\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _gud = require('gud');\n\nvar _gud2 = _interopRequireDefault(_gud);\n\nvar _warning = require('warning');\n\nvar _warning2 = _interopRequireDefault(_warning);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar MAX_SIGNED_31_BIT_INT = 1073741823;\n\n// Inlined Object.is polyfill.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\n\nfunction createEventEmitter(value) {\n  var handlers = [];\n  return {\n    on: function on(handler) {\n      handlers.push(handler);\n    },\n    off: function off(handler) {\n      handlers = handlers.filter(function (h) {\n        return h !== handler;\n      });\n    },\n    get: function get() {\n      return value;\n    },\n    set: function set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(function (handler) {\n        return handler(value, changedBits);\n      });\n    }\n  };\n}\n\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\n\nfunction createReactContext(defaultValue, calculateChangedBits) {\n  var _Provider$childContex, _Consumer$contextType;\n\n  var contextProp = '__create-react-context-' + (0, _gud2.default)() + '__';\n\n  var Provider = function (_Component) {\n    _inherits(Provider, _Component);\n\n    function Provider() {\n      var _temp, _this, _ret;\n\n      _classCallCheck(this, Provider);\n\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      return _ret = (_temp = (_this = _possibleConstructorReturn(this, _Component.call.apply(_Component, [this].concat(args))), _this), _this.emitter = createEventEmitter(_this.props.value), _temp), _possibleConstructorReturn(_this, _ret);\n    }\n\n    Provider.prototype.getChildContext = function getChildContext() {\n      var _ref;\n\n      return _ref = {}, _ref[contextProp] = this.emitter, _ref;\n    };\n\n    Provider.prototype.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        var oldValue = this.props.value;\n        var newValue = nextProps.value;\n        var changedBits = void 0;\n\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0; // No change\n        } else {\n          changedBits = typeof calculateChangedBits === 'function' ? calculateChangedBits(oldValue, newValue) : MAX_SIGNED_31_BIT_INT;\n          if (process.env.NODE_ENV !== 'production') {\n            (0, _warning2.default)((changedBits & MAX_SIGNED_31_BIT_INT) === changedBits, 'calculateChangedBits: Expected the return value to be a ' + '31-bit integer. Instead received: %s', changedBits);\n          }\n\n          changedBits |= 0;\n\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    };\n\n    Provider.prototype.render = function render() {\n      return this.props.children;\n    };\n\n    return Provider;\n  }(_react.Component);\n\n  Provider.childContextTypes = (_Provider$childContex = {}, _Provider$childContex[contextProp] = _propTypes2.default.object.isRequired, _Provider$childContex);\n\n  var Consumer = function (_Component2) {\n    _inherits(Consumer, _Component2);\n\n    function Consumer() {\n      var _temp2, _this2, _ret2;\n\n      _classCallCheck(this, Consumer);\n\n      for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      return _ret2 = (_temp2 = (_this2 = _possibleConstructorReturn(this, _Component2.call.apply(_Component2, [this].concat(args))), _this2), _this2.state = {\n        value: _this2.getValue()\n      }, _this2.onUpdate = function (newValue, changedBits) {\n        var observedBits = _this2.observedBits | 0;\n        if ((observedBits & changedBits) !== 0) {\n          _this2.setState({ value: _this2.getValue() });\n        }\n      }, _temp2), _possibleConstructorReturn(_this2, _ret2);\n    }\n\n    Consumer.prototype.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      var observedBits = nextProps.observedBits;\n\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n      : observedBits;\n    };\n\n    Consumer.prototype.componentDidMount = function componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n      var observedBits = this.props.observedBits;\n\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n      : observedBits;\n    };\n\n    Consumer.prototype.componentWillUnmount = function componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    };\n\n    Consumer.prototype.getValue = function getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    };\n\n    Consumer.prototype.render = function render() {\n      return onlyChild(this.props.children)(this.state.value);\n    };\n\n    return Consumer;\n  }(_react.Component);\n\n  Consumer.contextTypes = (_Consumer$contextType = {}, _Consumer$contextType[contextProp] = _propTypes2.default.object, _Consumer$contextType);\n\n\n  return {\n    Provider: Provider,\n    Consumer: Consumer\n  };\n}\n\nexports.default = createReactContext;\nmodule.exports = exports['default'];", "'use strict';\n\nexports.__esModule = true;\n\nvar _react = require('react');\n\nvar _react2 = _interopRequireDefault(_react);\n\nvar _implementation = require('./implementation');\n\nvar _implementation2 = _interopRequireDefault(_implementation);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = _react2.default.createContext || _implementation2.default;\nmodule.exports = exports['default'];", "export default typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n", "import isBrowser from './isBrowser';\n\nconst timeoutDuration = (function(){\n  const longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}());\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    window.Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const window = element.ownerDocument.defaultView;\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nexport default function getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n", "import isBrowser from './isBrowser';\n\nconst isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nconst isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nexport default function isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  const noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  let offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    parseFloat(styles[`border${sideA}Width`]) +\n    parseFloat(styles[`border${sideB}Width`])\n  );\n}\n", "import isIE from './isIE';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE(10)\n      ? (parseInt(html[`offset${axis}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]))\n    : 0 \n  );\n}\n\nexport default function getWindowSizes(document) {\n  const body = document.body;\n  const html = document.documentElement;\n  const computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE from './isIE';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    }\n    else {\n      rect = element.getBoundingClientRect();\n    }\n  }\n  catch(e){}\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  const width =\n    sizes.width || element.clientWidth || result.width;\n  const height =\n    sizes.height || element.clientHeight || result.height;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE from './isIE';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isIE10 = runIsIE(10);\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if(fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = parseFloat(styles.marginTop);\n    const marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10 && !fixedPosition\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  const parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nexport default function getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n   if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getReferenceNode from './getReferenceNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement,\n  fixedPosition = false\n) {\n  // NOTE: 1 DOM access here\n\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport' ) {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  }\n\n  else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent,\n      fixedPosition\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes(popper.ownerDocument);\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  const isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0; \n  boundaries.top += isPaddingNumber ? padding : padding.top || 0; \n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0; \n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0; \n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\nimport getReferenceNode from './getReferenceNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference, fixedPosition = null) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const window = element.ownerDocument.defaultView;\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  const y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "import computeAutoPlacement from '../utils/computeAutoPlacement';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nexport default function update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  let data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {},\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(\n    this.state,\n    this.popper,\n    this.reference,\n    this.options.positionFixed\n  );\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(\n    this.options.placement,\n    data.offsets.reference,\n    this.popper,\n    this.reference,\n    this.options.modifiers.flip.boundariesElement,\n    this.options.modifiers.flip.padding\n  );\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(\n    this.popper,\n    data.offsets.reference,\n    data.placement\n  );\n\n  data.offsets.popper.position = this.options.positionFixed\n    ? 'fixed'\n    : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "import isModifierEnabled from '../utils/isModifierEnabled';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nexport default function destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import setupEventListeners from '../utils/setupEventListeners';\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nexport default function enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(\n      this.reference,\n      this.options,\n      this.state,\n      this.scheduleUpdate\n    );\n  }\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import removeEventListeners from '../utils/removeEventListeners';\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nexport default function disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import setStyles from '../utils/setStyles';\nimport setAttributes from '../utils/setAttributes';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nexport default function applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nexport function applyStyleOnLoad(\n  reference,\n  popper,\n  options,\n  modifierOptions,\n  state\n) {\n  // compute reference element offsets\n  const referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  const placement = computeAutoPlacement(\n    options.placement,\n    referenceOffsets,\n    popper,\n    reference,\n    options.modifiers.flip.boundariesElement,\n    options.modifiers.flip.padding\n  );\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n", "/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nexport default function getRoundedOffsets(data, shouldRound) {\n  const { popper, reference } = data.offsets;\n  const { round, floor } = Math;\n  const noRound = v => v;\n  \n  const referenceWidth = round(reference.width);\n  const popperWidth = round(popper.width);\n  \n  const isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  const isVariation = data.placement.indexOf('-') !== -1;\n  const sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  const bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  const horizontalToInteger = !shouldRound\n    ? noRound\n    : isVertical || isVariation || sameWidthParity\n    ? round\n    : floor;\n  const verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(\n      bothOddWidth && !isVariation && shouldRound\n        ? popper.left - 1\n        : popper.left\n    ),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right),\n  };\n}\n", "import getSupportedPropertyName from '../utils/getSupportedPropertyName';\nimport find from '../utils/find';\nimport getOffsetParent from '../utils/getOffsetParent';\nimport getBoundingClientRect from '../utils/getBoundingClientRect';\nimport getRoundedOffsets from '../utils/getRoundedOffsets';\nimport isBrowser from '../utils/isBrowser';\n\nconst isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeStyle(data, options) {\n  const { x, y } = options;\n  const { popper } = data.offsets;\n\n  // Remove this legacy support in Popper.js v2\n  const legacyGpuAccelerationOption = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'applyStyle'\n  ).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn(\n      'WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!'\n    );\n  }\n  const gpuAcceleration =\n    legacyGpuAccelerationOption !== undefined\n      ? legacyGpuAccelerationOption\n      : options.gpuAcceleration;\n\n  const offsetParent = getOffsetParent(data.instance.popper);\n  const offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  const styles = {\n    position: popper.position,\n  };\n\n  const offsets = getRoundedOffsets(\n    data,\n    window.devicePixelRatio < 2 || !isFirefox\n  );\n\n  const sideA = x === 'bottom' ? 'top' : 'bottom';\n  const sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  const prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  let left, top;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = `translate3d(${left}px, ${top}px, 0)`;\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    const invertTop = sideA === 'bottom' ? -1 : 1;\n    const invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = `${sideA}, ${sideB}`;\n  }\n\n  // Attributes\n  const attributes = {\n    'x-placement': data.placement,\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = { ...attributes, ...data.attributes };\n  data.styles = { ...styles, ...data.styles };\n  data.arrowStyles = { ...data.offsets.arrow, ...data.arrowStyles };\n\n  return data;\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOuterSizes from '../utils/getOuterSizes';\nimport isModifierRequired from '../utils/isModifierRequired';\nimport getStyleComputedProperty from '../utils/getStyleComputedProperty';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  let arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn(\n        'WARNING: `arrow.element` must be child of its popper element!'\n      );\n      return data;\n    }\n  }\n\n  const placement = data.placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -=\n      popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] +=\n      reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  const center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  const css = getStyleComputedProperty(data.instance.popper);\n  const popperMarginSide = parseFloat(css[`margin${sideCapitalized}`]);\n  const popperBorderSide = parseFloat(css[`border${sideCapitalized}Width`]);\n  let sideValue =\n    center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {\n    [side]: Math.round(sideValue),\n    [altSide]: '', // make sure to unset any eventual altSide value from the DOM node\n  };\n\n  return data;\n}\n", "/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nexport default function getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n", "/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nexport default [\n  'auto-start',\n  'auto',\n  'auto-end',\n  'top-start',\n  'top',\n  'top-end',\n  'right-start',\n  'right',\n  'right-end',\n  'bottom-end',\n  'bottom',\n  'bottom-start',\n  'left-end',\n  'left',\n  'left-start',\n];\n", "import placements from '../methods/placements';\n\n// Get rid of `auto` `auto-start` and `auto-end`\nconst validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nexport default function clockwise(placement, counter = false) {\n  const index = validPlacements.indexOf(placement);\n  const arr = validPlacements\n    .slice(index + 1)\n    .concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n", "import getOppositePlacement from '../utils/getOppositePlacement';\nimport getOppositeVariation from '../utils/getOppositeVariation';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\nimport getBoundaries from '../utils/getBoundaries';\nimport isModifierEnabled from '../utils/isModifierEnabled';\nimport clockwise from '../utils/clockwise';\n\nconst BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise',\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    options.boundariesElement,\n    data.positionFixed\n  );\n\n  let placement = data.placement.split('-')[0];\n  let placementOpposite = getOppositePlacement(placement);\n  let variation = data.placement.split('-')[1] || '';\n\n  let flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    const popperOffsets = data.offsets.popper;\n    const refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    const floor = Math.floor;\n    const overlapsRef =\n      (placement === 'left' &&\n        floor(popperOffsets.right) > floor(refOffsets.left)) ||\n      (placement === 'right' &&\n        floor(popperOffsets.left) < floor(refOffsets.right)) ||\n      (placement === 'top' &&\n        floor(popperOffsets.bottom) > floor(refOffsets.top)) ||\n      (placement === 'bottom' &&\n        floor(popperOffsets.top) < floor(refOffsets.bottom));\n\n    const overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    const overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    const overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    const overflowsBottom =\n      floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    const overflowsBoundaries =\n      (placement === 'left' && overflowsLeft) ||\n      (placement === 'right' && overflowsRight) ||\n      (placement === 'top' && overflowsTop) ||\n      (placement === 'bottom' && overflowsBottom);\n\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    const flippedVariationByRef =\n      !!options.flipVariations &&\n      ((isVertical && variation === 'start' && overflowsLeft) ||\n        (isVertical && variation === 'end' && overflowsRight) ||\n        (!isVertical && variation === 'start' && overflowsTop) ||\n        (!isVertical && variation === 'end' && overflowsBottom));\n\n    // flips variation if popper content overflows boundaries\n    const flippedVariationByContent =\n      !!options.flipVariationsByContent &&\n      ((isVertical && variation === 'start' && overflowsRight) ||\n        (isVertical && variation === 'end' && overflowsLeft) ||\n        (!isVertical && variation === 'start' && overflowsBottom) ||\n        (!isVertical && variation === 'end' && overflowsTop));\n\n    const flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = {\n        ...data.offsets.popper,\n        ...getPopperOffsets(\n          data.instance.popper,\n          data.offsets.reference,\n          data.placement\n        ),\n      };\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function keepTogether(data) {\n  const { popper, reference } = data.offsets;\n  const placement = data.placement.split('-')[0];\n  const floor = Math.floor;\n  const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  const side = isVertical ? 'right' : 'bottom';\n  const opSide = isVertical ? 'left' : 'top';\n  const measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] =\n      floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n", "import isNumeric from '../utils/isNumeric';\nimport getClientRect from '../utils/getClientRect';\nimport find from '../utils/find';\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nexport function toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  const split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  const value = +split[1];\n  const unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    let element;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    const rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    let size;\n    if (unit === 'vh') {\n      size = Math.max(\n        document.documentElement.clientHeight,\n        window.innerHeight || 0\n      );\n    } else {\n      size = Math.max(\n        document.documentElement.clientWidth,\n        window.innerWidth || 0\n      );\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nexport function parseOffset(\n  offset,\n  popperOffsets,\n  referenceOffsets,\n  basePlacement\n) {\n  const offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  const useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  const fragments = offset.split(/(\\+|\\-)/).map(frag => frag.trim());\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  const divider = fragments.indexOf(\n    find(fragments, frag => frag.search(/,|\\s/) !== -1)\n  );\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn(\n      'Offsets separated by white space(s) are deprecated, use a comma (,) instead.'\n    );\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  const splitRegex = /\\s*,\\s*|\\s+/;\n  let ops = divider !== -1\n    ? [\n        fragments\n          .slice(0, divider)\n          .concat([fragments[divider].split(splitRegex)[0]]),\n        [fragments[divider].split(splitRegex)[1]].concat(\n          fragments.slice(divider + 1)\n        ),\n      ]\n    : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map((op, index) => {\n    // Most of the units rely on the orientation of the popper\n    const measurement = (index === 1 ? !useHeight : useHeight)\n      ? 'height'\n      : 'width';\n    let mergeWithPrevious = false;\n    return (\n      op\n        // This aggregates any `+` or `-` sign that aren't considered operators\n        // e.g.: 10 + +5 => [10, +, +5]\n        .reduce((a, b) => {\n          if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n            a[a.length - 1] = b;\n            mergeWithPrevious = true;\n            return a;\n          } else if (mergeWithPrevious) {\n            a[a.length - 1] += b;\n            mergeWithPrevious = false;\n            return a;\n          } else {\n            return a.concat(b);\n          }\n        }, [])\n        // Here we convert the string values into number values (in px)\n        .map(str => toValue(str, measurement, popperOffsets, referenceOffsets))\n    );\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach((op, index) => {\n    op.forEach((frag, index2) => {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nexport default function offset(data, { offset }) {\n  const { placement, offsets: { popper, reference } } = data;\n  const basePlacement = placement.split('-')[0];\n\n  let offsets;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n", "import getOffsetParent from '../utils/getOffsetParent';\nimport getBoundaries from '../utils/getBoundaries';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function preventOverflow(data, options) {\n  let boundariesElement =\n    options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  const transformProp = getSupportedPropertyName('transform');\n  const popperStyles = data.instance.popper.style; // assignment to help minification\n  const { top, left, [transformProp]: transform } = popperStyles;\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    boundariesElement,\n    data.positionFixed\n  );\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  const order = options.priority;\n  let popper = data.offsets.popper;\n\n  const check = {\n    primary(placement) {\n      let value = popper[placement];\n      if (\n        popper[placement] < boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return { [placement]: value };\n    },\n    secondary(placement) {\n      const mainSide = placement === 'right' ? 'left' : 'top';\n      let value = popper[mainSide];\n      if (\n        popper[placement] > boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.min(\n          popper[mainSide],\n          boundaries[placement] -\n            (placement === 'right' ? popper.width : popper.height)\n        );\n      }\n      return { [mainSide]: value };\n    },\n  };\n\n  order.forEach(placement => {\n    const side =\n      ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = { ...popper, ...check[side](placement) };\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    const { reference, popper } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n\n    const shiftOffsets = {\n      start: { [side]: reference[side] },\n      end: {\n        [side]: reference[side] + reference[measurement] - popper[measurement],\n      },\n    };\n\n    data.offsets.popper = { ...popper, ...shiftOffsets[shiftvariation] };\n  }\n\n  return data;\n}\n", "import isModifierRequired from '../utils/isModifierRequired';\nimport find from '../utils/find';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  const refRect = data.offsets.reference;\n  const bound = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'preventOverflow'\n  ).boundaries;\n\n  if (\n    refRect.bottom < bound.top ||\n    refRect.left > bound.right ||\n    refRect.top > bound.bottom ||\n    refRect.right < bound.left\n  ) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOppositePlacement from '../utils/getOppositePlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function inner(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  const subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] =\n    reference[basePlacement] -\n    (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n", "import applyStyle, { applyStyleOnLoad } from './applyStyle';\nimport computeStyle from './computeStyle';\nimport arrow from './arrow';\nimport flip from './flip';\nimport keepTogether from './keepTogether';\nimport offset from './offset';\nimport preventOverflow from './preventOverflow';\nimport shift from './shift';\nimport hide from './hide';\nimport inner from './inner';\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nexport default {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift,\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0,\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent',\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether,\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]',\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false,\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner,\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide,\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right',\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined,\n  },\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n", "import modifiers from '../modifiers/index';\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nexport default {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: () => {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: () => {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers,\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n", "// Utils\nimport debounce from './utils/debounce';\nimport isFunction from './utils/isFunction';\n\n// Methods\nimport update from './methods/update';\nimport destroy from './methods/destroy';\nimport enableEventListeners from './methods/enableEventListeners';\nimport disableEventListeners from './methods/disableEventListeners';\nimport Defaults from './methods/defaults';\nimport placements from './methods/placements';\n\nexport default class Popper {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  constructor(reference, popper, options = {}) {\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = { ...Popper.Defaults, ...options };\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: [],\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys({\n      ...Popper.Defaults.modifiers,\n      ...options.modifiers,\n    }).forEach(name => {\n      this.options.modifiers[name] = {\n        // If it's a built-in modifier, use it as base\n        ...(Popper.Defaults.modifiers[name] || {}),\n        // If there are custom options, override and merge with default ones\n        ...(options.modifiers ? options.modifiers[name] : {}),\n      };\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers)\n      .map(name => ({\n        name,\n        ...this.options.modifiers[name],\n      }))\n      // sort the modifiers by order\n      .sort((a, b) => a.order - b.order);\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(modifierOptions => {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(\n          this.reference,\n          this.popper,\n          this.options,\n          modifierOptions,\n          this.state\n        );\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    const eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n  update() {\n    return update.call(this);\n  }\n  destroy() {\n    return destroy.call(this);\n  }\n  enableEventListeners() {\n    return enableEventListeners.call(this);\n  }\n  disableEventListeners() {\n    return disableEventListeners.call(this);\n  }\n\n  /**\n   * Schedules an update. It will run on the next UI update available.\n   * @method scheduleUpdate\n   * @memberof Popper\n   */\n  scheduleUpdate = () => requestAnimationFrame(this.update);\n\n  /**\n   * Collection of utilities useful when writing custom modifiers.\n   * Starting from version 1.7, this method is available only if you\n   * include `popper-utils.js` before `popper.js`.\n   *\n   * **DEPRECATION**: This way to access PopperUtils is deprecated\n   * and will be removed in v2! Use the PopperUtils module directly instead.\n   * Due to the high instability of the methods contained in Utils, we can't\n   * guarantee them to follow semver. Use them at your own risk!\n   * @static\n   * @private\n   * @type {Object}\n   * @deprecated since version 1.8\n   * @member Utils\n   * @memberof Popper\n   */\n  static Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\n\n  static placements = placements;\n\n  static Defaults = Defaults;\n}\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,QAAQ,OAAO,UAAU;AAE7B,WAAO,UAAU,SAAS,YAAY,OAAO;AAC5C,UAAI,MAAM,MAAM,KAAK,KAAK;AAC1B,UAAI,SAAS,QAAQ;AACrB,UAAI,CAAC,QAAQ;AACZ,iBAAS,QAAQ,oBAChB,UAAU,QACV,OAAO,UAAU,YACjB,OAAO,MAAM,WAAW,YACxB,MAAM,UAAU,KAChB,MAAM,KAAK,MAAM,MAAM,MAAM;AAAA,MAC/B;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,QAAI;AACJ,QAAI,CAAC,OAAO,MAAM;AAEb,YAAM,OAAO,UAAU;AACvB,cAAQ,OAAO,UAAU;AACzB,eAAS;AACT,qBAAe,OAAO,UAAU;AAChC,uBAAiB,CAAC,aAAa,KAAK,EAAE,UAAU,KAAK,GAAG,UAAU;AAClE,wBAAkB,aAAa,KAAK,WAAY;AAAA,MAAC,GAAG,WAAW;AAC/D,kBAAY;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AACI,mCAA6B,SAAU,GAAG;AAC7C,YAAI,OAAO,EAAE;AACb,eAAO,QAAQ,KAAK,cAAc;AAAA,MACnC;AACI,qBAAe;AAAA,QAClB,mBAAmB;AAAA,QACnB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,SAAS;AAAA,QACT,cAAc;AAAA,QACd,aAAa;AAAA,QACb,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,cAAc;AAAA,QACd,cAAc;AAAA,QACd,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,SAAS;AAAA,MACV;AACI,iCAA4B,WAAY;AAE3C,YAAI,OAAO,WAAW,aAAa;AAAE,iBAAO;AAAA,QAAO;AACnD,iBAAS,KAAK,QAAQ;AACrB,cAAI;AACH,gBAAI,CAAC,aAAa,MAAM,CAAC,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,OAAO,CAAC,MAAM,QAAQ,OAAO,OAAO,CAAC,MAAM,UAAU;AACzG,kBAAI;AACH,2CAA2B,OAAO,CAAC,CAAC;AAAA,cACrC,SAAS,GAAP;AACD,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD,SAAS,GAAP;AACD,mBAAO;AAAA,UACR;AAAA,QACD;AACA,eAAO;AAAA,MACR,EAAE;AACE,6CAAuC,SAAU,GAAG;AAEvD,YAAI,OAAO,WAAW,eAAe,CAAC,0BAA0B;AAC/D,iBAAO,2BAA2B,CAAC;AAAA,QACpC;AACA,YAAI;AACH,iBAAO,2BAA2B,CAAC;AAAA,QACpC,SAAS,GAAP;AACD,iBAAO;AAAA,QACR;AAAA,MACD;AAEA,iBAAW,SAAS,KAAK,QAAQ;AAChC,YAAI,WAAW,WAAW,QAAQ,OAAO,WAAW;AACpD,YAAIA,cAAa,MAAM,KAAK,MAAM,MAAM;AACxC,YAAI,cAAc,OAAO,MAAM;AAC/B,YAAI,WAAW,YAAY,MAAM,KAAK,MAAM,MAAM;AAClD,YAAI,UAAU,CAAC;AAEf,YAAI,CAAC,YAAY,CAACA,eAAc,CAAC,aAAa;AAC7C,gBAAM,IAAI,UAAU,oCAAoC;AAAA,QACzD;AAEA,YAAI,YAAY,mBAAmBA;AACnC,YAAI,YAAY,OAAO,SAAS,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,GAAG;AAC1D,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACvC,oBAAQ,KAAK,OAAO,CAAC,CAAC;AAAA,UACvB;AAAA,QACD;AAEA,YAAI,eAAe,OAAO,SAAS,GAAG;AACrC,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACvC,oBAAQ,KAAK,OAAO,CAAC,CAAC;AAAA,UACvB;AAAA,QACD,OAAO;AACN,mBAAS,QAAQ,QAAQ;AACxB,gBAAI,EAAE,aAAa,SAAS,gBAAgB,IAAI,KAAK,QAAQ,IAAI,GAAG;AACnE,sBAAQ,KAAK,OAAO,IAAI,CAAC;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AAEA,YAAI,gBAAgB;AACnB,cAAI,kBAAkB,qCAAqC,MAAM;AAEjE,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AAC1C,gBAAI,EAAE,mBAAmB,UAAU,CAAC,MAAM,kBAAkB,IAAI,KAAK,QAAQ,UAAU,CAAC,CAAC,GAAG;AAC3F,sBAAQ,KAAK,UAAU,CAAC,CAAC;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AACA,eAAO;AAAA,MACR;AAAA,IACD;AAnHK;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AAIA;AAyBA;AAkBA;AAsDL,WAAO,UAAU;AAAA;AAAA;;;ACzHjB;AAAA;AAAA;AAEA,QAAI,QAAQ,MAAM,UAAU;AAC5B,QAAI,SAAS;AAEb,QAAI,WAAW,OAAO;AACtB,QAAI,WAAW,WAAW,SAAS,KAAK,GAAG;AAAE,aAAO,SAAS,CAAC;AAAA,IAAG,IAAI;AAErE,QAAI,eAAe,OAAO;AAE1B,aAAS,OAAO,SAAS,iBAAiB;AACzC,UAAI,OAAO,MAAM;AAChB,YAAI,yBAA0B,WAAY;AAEzC,cAAI,OAAO,OAAO,KAAK,SAAS;AAChC,iBAAO,QAAQ,KAAK,WAAW,UAAU;AAAA,QAC1C,EAAE,GAAG,CAAC;AACN,YAAI,CAAC,wBAAwB;AAC5B,iBAAO,OAAO,SAAS,KAAK,QAAQ;AACnC,gBAAI,OAAO,MAAM,GAAG;AACnB,qBAAO,aAAa,MAAM,KAAK,MAAM,CAAC;AAAA,YACvC;AACA,mBAAO,aAAa,MAAM;AAAA,UAC3B;AAAA,QACD;AAAA,MACD,OAAO;AACN,eAAO,OAAO;AAAA,MACf;AACA,aAAO,OAAO,QAAQ;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA;AAGA,WAAO,UAAU,SAAS,aAAa;AACtC,UAAI,OAAO,WAAW,cAAc,OAAO,OAAO,0BAA0B,YAAY;AAAE,eAAO;AAAA,MAAO;AACxG,UAAI,OAAO,OAAO,aAAa,UAAU;AAAE,eAAO;AAAA,MAAM;AAExD,UAAI,MAAM,CAAC;AACX,UAAI,MAAM,OAAO,MAAM;AACvB,UAAI,SAAS,OAAO,GAAG;AACvB,UAAI,OAAO,QAAQ,UAAU;AAAE,eAAO;AAAA,MAAO;AAE7C,UAAI,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,mBAAmB;AAAE,eAAO;AAAA,MAAO;AAC/E,UAAI,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM,mBAAmB;AAAE,eAAO;AAAA,MAAO;AAUlF,UAAI,SAAS;AACb,UAAI,GAAG,IAAI;AACX,WAAK,OAAO,KAAK;AAAE,eAAO;AAAA,MAAO;AACjC,UAAI,OAAO,OAAO,SAAS,cAAc,OAAO,KAAK,GAAG,EAAE,WAAW,GAAG;AAAE,eAAO;AAAA,MAAO;AAExF,UAAI,OAAO,OAAO,wBAAwB,cAAc,OAAO,oBAAoB,GAAG,EAAE,WAAW,GAAG;AAAE,eAAO;AAAA,MAAO;AAEtH,UAAI,OAAO,OAAO,sBAAsB,GAAG;AAC3C,UAAI,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,KAAK;AAAE,eAAO;AAAA,MAAO;AAE1D,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,KAAK,GAAG,GAAG;AAAE,eAAO;AAAA,MAAO;AAE3E,UAAI,OAAO,OAAO,6BAA6B,YAAY;AAC1D,YAAI,aAAa,OAAO,yBAAyB,KAAK,GAAG;AACzD,YAAI,WAAW,UAAU,UAAU,WAAW,eAAe,MAAM;AAAE,iBAAO;AAAA,QAAO;AAAA,MACpF;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzCA,IAAAC,iBAAA;AAAA;AAAA;AAEA,QAAI,aAAa;AAEjB,WAAO,UAAU,SAAS,sBAAsB;AAC/C,aAAO,WAAW,KAAK,CAAC,CAAC,OAAO;AAAA,IACjC;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAEA,QAAI,aAAa,OAAO,WAAW,eAAe;AAClD,QAAI,gBAAgB;AAEpB,WAAO,UAAU,SAAS,mBAAmB;AAC5C,UAAI,OAAO,eAAe,YAAY;AAAE,eAAO;AAAA,MAAO;AACtD,UAAI,OAAO,WAAW,YAAY;AAAE,eAAO;AAAA,MAAO;AAClD,UAAI,OAAO,WAAW,KAAK,MAAM,UAAU;AAAE,eAAO;AAAA,MAAO;AAC3D,UAAI,OAAO,OAAO,KAAK,MAAM,UAAU;AAAE,eAAO;AAAA,MAAO;AAEvD,aAAO,cAAc;AAAA,IACtB;AAAA;AAAA;;;ACZA;AAAA;AAAA;AAEA,QAAI,OAAO;AAAA,MACV,KAAK,CAAC;AAAA,IACP;AAEA,QAAI,UAAU;AAEd,WAAO,UAAU,SAAS,WAAW;AACpC,aAAO,EAAE,WAAW,KAAK,EAAE,QAAQ,KAAK,OAAO,EAAE,EAAE,WAAW,KAAK,aAAa;AAAA,IACjF;AAAA;AAAA;;;ACVA,IAAAC,0BAAA;AAAA;AAAA;AAIA,QAAI,gBAAgB;AACpB,QAAI,QAAQ,MAAM,UAAU;AAC5B,QAAI,QAAQ,OAAO,UAAU;AAC7B,QAAI,WAAW;AAEf,WAAO,UAAU,SAAS,KAAK,MAAM;AACjC,UAAI,SAAS;AACb,UAAI,OAAO,WAAW,cAAc,MAAM,KAAK,MAAM,MAAM,UAAU;AACjE,cAAM,IAAI,UAAU,gBAAgB,MAAM;AAAA,MAC9C;AACA,UAAI,OAAO,MAAM,KAAK,WAAW,CAAC;AAElC,UAAI;AACJ,UAAI,SAAS,WAAY;AACrB,YAAI,gBAAgB,OAAO;AACvB,cAAI,SAAS,OAAO;AAAA,YAChB;AAAA,YACA,KAAK,OAAO,MAAM,KAAK,SAAS,CAAC;AAAA,UACrC;AACA,cAAI,OAAO,MAAM,MAAM,QAAQ;AAC3B,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,OAAO;AAAA,YACV;AAAA,YACA,KAAK,OAAO,MAAM,KAAK,SAAS,CAAC;AAAA,UACrC;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,cAAc,KAAK,IAAI,GAAG,OAAO,SAAS,KAAK,MAAM;AACzD,UAAI,YAAY,CAAC;AACjB,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,kBAAU,KAAK,MAAM,CAAC;AAAA,MAC1B;AAEA,cAAQ,SAAS,UAAU,sBAAsB,UAAU,KAAK,GAAG,IAAI,2CAA2C,EAAE,MAAM;AAE1H,UAAI,OAAO,WAAW;AAClB,YAAI,QAAQ,SAASC,SAAQ;AAAA,QAAC;AAC9B,cAAM,YAAY,OAAO;AACzB,cAAM,YAAY,IAAI,MAAM;AAC5B,cAAM,YAAY;AAAA,MACtB;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACnDA;AAAA;AAAA;AAEA,QAAI,iBAAiB;AAErB,WAAO,UAAU,SAAS,UAAU,QAAQ;AAAA;AAAA;;;ACJ5C;AAAA;AAAA;AAEA,QAAI,OAAO;AAEX,WAAO,UAAU,KAAK,KAAK,SAAS,MAAM,OAAO,UAAU,cAAc;AAAA;AAAA;;;ACJzE;AAAA;AAAA;AAEA,QAAIC;AAEJ,QAAI,eAAe;AACnB,QAAI,YAAY;AAChB,QAAI,aAAa;AAGjB,QAAI,wBAAwB,SAAU,kBAAkB;AACvD,UAAI;AACH,eAAO,UAAU,2BAA2B,mBAAmB,gBAAgB,EAAE;AAAA,MAClF,SAAS,GAAP;AAAA,MAAW;AAAA,IACd;AAEA,QAAI,QAAQ,OAAO;AACnB,QAAI,OAAO;AACV,UAAI;AACH,cAAM,CAAC,GAAG,EAAE;AAAA,MACb,SAAS,GAAP;AACD,gBAAQ;AAAA,MACT;AAAA,IACD;AAEA,QAAI,iBAAiB,WAAY;AAChC,YAAM,IAAI,WAAW;AAAA,IACtB;AACA,QAAI,iBAAiB,QACjB,WAAY;AACd,UAAI;AAEH,kBAAU;AACV,eAAO;AAAA,MACR,SAAS,cAAP;AACD,YAAI;AAEH,iBAAO,MAAM,WAAW,QAAQ,EAAE;AAAA,QACnC,SAAS,YAAP;AACD,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD,EAAE,IACA;AAEH,QAAI,aAAa,sBAAuB;AACxC,QAAI,WAAW,oBAAqB;AAEpC,QAAI,WAAW,OAAO,mBACrB,WACG,SAAU,GAAG;AAAE,aAAO,EAAE;AAAA,IAAW,IACnC;AAGJ,QAAI,YAAY,CAAC;AAEjB,QAAI,aAAa,OAAO,eAAe,eAAe,CAAC,WAAWA,aAAY,SAAS,UAAU;AAEjG,QAAI,aAAa;AAAA,MAChB,oBAAoB,OAAO,mBAAmB,cAAcA,aAAY;AAAA,MACxE,WAAW;AAAA,MACX,iBAAiB,OAAO,gBAAgB,cAAcA,aAAY;AAAA,MAClE,4BAA4B,cAAc,WAAW,SAAS,CAAC,EAAE,OAAO,QAAQ,EAAE,CAAC,IAAIA;AAAA,MACvF,oCAAoCA;AAAA,MACpC,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,4BAA4B;AAAA,MAC5B,4BAA4B;AAAA,MAC5B,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,YAAY,OAAO,WAAW,cAAcA,aAAY;AAAA,MACxD,mBAAmB,OAAO,kBAAkB,cAAcA,aAAY;AAAA,MACtE,oBAAoB,OAAO,mBAAmB,cAAcA,aAAY;AAAA,MACxE,aAAa;AAAA,MACb,cAAc,OAAO,aAAa,cAAcA,aAAY;AAAA,MAC5D,UAAU;AAAA,MACV,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,WAAW;AAAA,MACX,UAAU;AAAA;AAAA,MACV,eAAe;AAAA,MACf,kBAAkB,OAAO,iBAAiB,cAAcA,aAAY;AAAA,MACpE,kBAAkB,OAAO,iBAAiB,cAAcA,aAAY;AAAA,MACpE,0BAA0B,OAAO,yBAAyB,cAAcA,aAAY;AAAA,MACpF,cAAc;AAAA,MACd,uBAAuB;AAAA,MACvB,eAAe,OAAO,cAAc,cAAcA,aAAY;AAAA,MAC9D,gBAAgB,OAAO,eAAe,cAAcA,aAAY;AAAA,MAChE,gBAAgB,OAAO,eAAe,cAAcA,aAAY;AAAA,MAChE,cAAc;AAAA,MACd,WAAW;AAAA,MACX,uBAAuB,cAAc,WAAW,SAAS,SAAS,CAAC,EAAE,OAAO,QAAQ,EAAE,CAAC,CAAC,IAAIA;AAAA,MAC5F,UAAU,OAAO,SAAS,WAAW,OAAOA;AAAA,MAC5C,SAAS,OAAO,QAAQ,cAAcA,aAAY;AAAA,MAClD,0BAA0B,OAAO,QAAQ,eAAe,CAAC,cAAc,CAAC,WAAWA,aAAY,UAAS,oBAAI,IAAI,GAAE,OAAO,QAAQ,EAAE,CAAC;AAAA,MACpI,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,WAAW,OAAO,UAAU,cAAcA,aAAY;AAAA,MACtD,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,YAAY;AAAA,MACZ,SAAS,OAAO,QAAQ,cAAcA,aAAY;AAAA,MAClD,0BAA0B,OAAO,QAAQ,eAAe,CAAC,cAAc,CAAC,WAAWA,aAAY,UAAS,oBAAI,IAAI,GAAE,OAAO,QAAQ,EAAE,CAAC;AAAA,MACpI,uBAAuB,OAAO,sBAAsB,cAAcA,aAAY;AAAA,MAC9E,YAAY;AAAA,MACZ,6BAA6B,cAAc,WAAW,SAAS,GAAG,OAAO,QAAQ,EAAE,CAAC,IAAIA;AAAA,MACxF,YAAY,aAAa,SAASA;AAAA,MAClC,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,gBAAgB,OAAO,eAAe,cAAcA,aAAY;AAAA,MAChE,uBAAuB,OAAO,sBAAsB,cAAcA,aAAY;AAAA,MAC9E,iBAAiB,OAAO,gBAAgB,cAAcA,aAAY;AAAA,MAClE,iBAAiB,OAAO,gBAAgB,cAAcA,aAAY;AAAA,MAClE,cAAc;AAAA,MACd,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,IAC3D;AAEA,QAAI,UAAU;AACb,UAAI;AACH,aAAK;AAAA,MACN,SAAS,GAAP;AAEG,qBAAa,SAAS,SAAS,CAAC,CAAC;AACrC,mBAAW,mBAAmB,IAAI;AAAA,MACnC;AAAA,IACD;AAHM;AAKN,QAAI,SAAS,SAASC,QAAO,MAAM;AAClC,UAAI;AACJ,UAAI,SAAS,mBAAmB;AAC/B,gBAAQ,sBAAsB,sBAAsB;AAAA,MACrD,WAAW,SAAS,uBAAuB;AAC1C,gBAAQ,sBAAsB,iBAAiB;AAAA,MAChD,WAAW,SAAS,4BAA4B;AAC/C,gBAAQ,sBAAsB,uBAAuB;AAAA,MACtD,WAAW,SAAS,oBAAoB;AACvC,YAAI,KAAKA,QAAO,0BAA0B;AAC1C,YAAI,IAAI;AACP,kBAAQ,GAAG;AAAA,QACZ;AAAA,MACD,WAAW,SAAS,4BAA4B;AAC/C,YAAI,MAAMA,QAAO,kBAAkB;AACnC,YAAI,OAAO,UAAU;AACpB,kBAAQ,SAAS,IAAI,SAAS;AAAA,QAC/B;AAAA,MACD;AAEA,iBAAW,IAAI,IAAI;AAEnB,aAAO;AAAA,IACR;AAEA,QAAI,iBAAiB;AAAA,MACpB,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,oBAAoB,CAAC,SAAS,WAAW;AAAA,MACzC,wBAAwB,CAAC,SAAS,aAAa,SAAS;AAAA,MACxD,wBAAwB,CAAC,SAAS,aAAa,SAAS;AAAA,MACxD,qBAAqB,CAAC,SAAS,aAAa,MAAM;AAAA,MAClD,uBAAuB,CAAC,SAAS,aAAa,QAAQ;AAAA,MACtD,4BAA4B,CAAC,iBAAiB,WAAW;AAAA,MACzD,oBAAoB,CAAC,0BAA0B,WAAW;AAAA,MAC1D,6BAA6B,CAAC,0BAA0B,aAAa,WAAW;AAAA,MAChF,sBAAsB,CAAC,WAAW,WAAW;AAAA,MAC7C,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,mBAAmB,CAAC,QAAQ,WAAW;AAAA,MACvC,oBAAoB,CAAC,SAAS,WAAW;AAAA,MACzC,wBAAwB,CAAC,aAAa,WAAW;AAAA,MACjD,2BAA2B,CAAC,gBAAgB,WAAW;AAAA,MACvD,2BAA2B,CAAC,gBAAgB,WAAW;AAAA,MACvD,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,eAAe,CAAC,qBAAqB,WAAW;AAAA,MAChD,wBAAwB,CAAC,qBAAqB,aAAa,WAAW;AAAA,MACtE,wBAAwB,CAAC,aAAa,WAAW;AAAA,MACjD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,eAAe,CAAC,QAAQ,OAAO;AAAA,MAC/B,mBAAmB,CAAC,QAAQ,WAAW;AAAA,MACvC,kBAAkB,CAAC,OAAO,WAAW;AAAA,MACrC,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,uBAAuB,CAAC,UAAU,aAAa,UAAU;AAAA,MACzD,sBAAsB,CAAC,UAAU,aAAa,SAAS;AAAA,MACvD,sBAAsB,CAAC,WAAW,WAAW;AAAA,MAC7C,uBAAuB,CAAC,WAAW,aAAa,MAAM;AAAA,MACtD,iBAAiB,CAAC,WAAW,KAAK;AAAA,MAClC,oBAAoB,CAAC,WAAW,QAAQ;AAAA,MACxC,qBAAqB,CAAC,WAAW,SAAS;AAAA,MAC1C,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,6BAA6B,CAAC,kBAAkB,WAAW;AAAA,MAC3D,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,kBAAkB,CAAC,OAAO,WAAW;AAAA,MACrC,gCAAgC,CAAC,qBAAqB,WAAW;AAAA,MACjE,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,wBAAwB,CAAC,aAAa,WAAW;AAAA,MACjD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,gCAAgC,CAAC,qBAAqB,WAAW;AAAA,MACjE,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,sBAAsB,CAAC,WAAW,WAAW;AAAA,MAC7C,sBAAsB,CAAC,WAAW,WAAW;AAAA,IAC9C;AAEA,QAAI,OAAO;AACX,QAAI,SAAS;AACb,QAAI,UAAU,KAAK,KAAK,SAAS,MAAM,MAAM,UAAU,MAAM;AAC7D,QAAI,eAAe,KAAK,KAAK,SAAS,OAAO,MAAM,UAAU,MAAM;AACnE,QAAI,WAAW,KAAK,KAAK,SAAS,MAAM,OAAO,UAAU,OAAO;AAChE,QAAI,YAAY,KAAK,KAAK,SAAS,MAAM,OAAO,UAAU,KAAK;AAC/D,QAAI,QAAQ,KAAK,KAAK,SAAS,MAAM,OAAO,UAAU,IAAI;AAG1D,QAAI,aAAa;AACjB,QAAI,eAAe;AACnB,QAAI,eAAe,SAASC,cAAa,QAAQ;AAChD,UAAI,QAAQ,UAAU,QAAQ,GAAG,CAAC;AAClC,UAAI,OAAO,UAAU,QAAQ,EAAE;AAC/B,UAAI,UAAU,OAAO,SAAS,KAAK;AAClC,cAAM,IAAI,aAAa,gDAAgD;AAAA,MACxE,WAAW,SAAS,OAAO,UAAU,KAAK;AACzC,cAAM,IAAI,aAAa,gDAAgD;AAAA,MACxE;AACA,UAAI,SAAS,CAAC;AACd,eAAS,QAAQ,YAAY,SAAU,OAAO,QAAQ,OAAO,WAAW;AACvE,eAAO,OAAO,MAAM,IAAI,QAAQ,SAAS,WAAW,cAAc,IAAI,IAAI,UAAU;AAAA,MACrF,CAAC;AACD,aAAO;AAAA,IACR;AAGA,QAAI,mBAAmB,SAASC,kBAAiB,MAAM,cAAc;AACpE,UAAI,gBAAgB;AACpB,UAAI;AACJ,UAAI,OAAO,gBAAgB,aAAa,GAAG;AAC1C,gBAAQ,eAAe,aAAa;AACpC,wBAAgB,MAAM,MAAM,CAAC,IAAI;AAAA,MAClC;AAEA,UAAI,OAAO,YAAY,aAAa,GAAG;AACtC,YAAI,QAAQ,WAAW,aAAa;AACpC,YAAI,UAAU,WAAW;AACxB,kBAAQ,OAAO,aAAa;AAAA,QAC7B;AACA,YAAI,OAAO,UAAU,eAAe,CAAC,cAAc;AAClD,gBAAM,IAAI,WAAW,eAAe,OAAO,sDAAsD;AAAA,QAClG;AAEA,eAAO;AAAA,UACN;AAAA,UACA,MAAM;AAAA,UACN;AAAA,QACD;AAAA,MACD;AAEA,YAAM,IAAI,aAAa,eAAe,OAAO,kBAAkB;AAAA,IAChE;AAEA,WAAO,UAAU,SAAS,aAAa,MAAM,cAAc;AAC1D,UAAI,OAAO,SAAS,YAAY,KAAK,WAAW,GAAG;AAClD,cAAM,IAAI,WAAW,2CAA2C;AAAA,MACjE;AACA,UAAI,UAAU,SAAS,KAAK,OAAO,iBAAiB,WAAW;AAC9D,cAAM,IAAI,WAAW,2CAA2C;AAAA,MACjE;AAEA,UAAI,MAAM,eAAe,IAAI,MAAM,MAAM;AACxC,cAAM,IAAI,aAAa,oFAAoF;AAAA,MAC5G;AACA,UAAI,QAAQ,aAAa,IAAI;AAC7B,UAAI,oBAAoB,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AAEtD,UAAI,YAAY,iBAAiB,MAAM,oBAAoB,KAAK,YAAY;AAC5E,UAAI,oBAAoB,UAAU;AAClC,UAAI,QAAQ,UAAU;AACtB,UAAI,qBAAqB;AAEzB,UAAI,QAAQ,UAAU;AACtB,UAAI,OAAO;AACV,4BAAoB,MAAM,CAAC;AAC3B,qBAAa,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAAA,MAC3C;AAEA,eAAS,IAAI,GAAG,QAAQ,MAAM,IAAI,MAAM,QAAQ,KAAK,GAAG;AACvD,YAAI,OAAO,MAAM,CAAC;AAClB,YAAI,QAAQ,UAAU,MAAM,GAAG,CAAC;AAChC,YAAI,OAAO,UAAU,MAAM,EAAE;AAC7B,aAEG,UAAU,OAAO,UAAU,OAAO,UAAU,QACzC,SAAS,OAAO,SAAS,OAAO,SAAS,SAE3C,UAAU,MACZ;AACD,gBAAM,IAAI,aAAa,sDAAsD;AAAA,QAC9E;AACA,YAAI,SAAS,iBAAiB,CAAC,OAAO;AACrC,+BAAqB;AAAA,QACtB;AAEA,6BAAqB,MAAM;AAC3B,4BAAoB,MAAM,oBAAoB;AAE9C,YAAI,OAAO,YAAY,iBAAiB,GAAG;AAC1C,kBAAQ,WAAW,iBAAiB;AAAA,QACrC,WAAW,SAAS,MAAM;AACzB,cAAI,EAAE,QAAQ,QAAQ;AACrB,gBAAI,CAAC,cAAc;AAClB,oBAAM,IAAI,WAAW,wBAAwB,OAAO,6CAA6C;AAAA,YAClG;AACA,mBAAO;AAAA,UACR;AACA,cAAI,SAAU,IAAI,KAAM,MAAM,QAAQ;AACrC,gBAAI,OAAO,MAAM,OAAO,IAAI;AAC5B,oBAAQ,CAAC,CAAC;AASV,gBAAI,SAAS,SAAS,QAAQ,EAAE,mBAAmB,KAAK,MAAM;AAC7D,sBAAQ,KAAK;AAAA,YACd,OAAO;AACN,sBAAQ,MAAM,IAAI;AAAA,YACnB;AAAA,UACD,OAAO;AACN,oBAAQ,OAAO,OAAO,IAAI;AAC1B,oBAAQ,MAAM,IAAI;AAAA,UACnB;AAEA,cAAI,SAAS,CAAC,oBAAoB;AACjC,uBAAW,iBAAiB,IAAI;AAAA,UACjC;AAAA,QACD;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC9VA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,eAAe;AAEnB,QAAI,SAAS,aAAa,4BAA4B;AACtD,QAAI,QAAQ,aAAa,2BAA2B;AACpD,QAAI,gBAAgB,aAAa,mBAAmB,IAAI,KAAK,KAAK,KAAK,OAAO,MAAM;AAEpF,QAAI,QAAQ,aAAa,qCAAqC,IAAI;AAClE,QAAI,kBAAkB,aAAa,2BAA2B,IAAI;AAClE,QAAI,OAAO,aAAa,YAAY;AAEpC,QAAI,iBAAiB;AACpB,UAAI;AACH,wBAAgB,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,CAAC;AAAA,MACtC,SAAS,GAAP;AAED,0BAAkB;AAAA,MACnB;AAAA,IACD;AAEA,WAAO,UAAU,SAAS,SAAS,kBAAkB;AACpD,UAAI,OAAO,cAAc,MAAM,OAAO,SAAS;AAC/C,UAAI,SAAS,iBAAiB;AAC7B,YAAI,OAAO,MAAM,MAAM,QAAQ;AAC/B,YAAI,KAAK,cAAc;AAEtB;AAAA,YACC;AAAA,YACA;AAAA,YACA,EAAE,OAAO,IAAI,KAAK,GAAG,iBAAiB,UAAU,UAAU,SAAS,EAAE,EAAE;AAAA,UACxE;AAAA,QACD;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,QAAI,YAAY,SAASC,aAAY;AACpC,aAAO,cAAc,MAAM,QAAQ,SAAS;AAAA,IAC7C;AAEA,QAAI,iBAAiB;AACpB,sBAAgB,OAAO,SAAS,SAAS,EAAE,OAAO,UAAU,CAAC;AAAA,IAC9D,OAAO;AACN,aAAO,QAAQ,QAAQ;AAAA,IACxB;AAAA;AAAA;;;AC9CA;AAAA;AAAA;AAEA,QAAI,eAAe;AAEnB,QAAI,WAAW;AAEf,QAAI,WAAW,SAAS,aAAa,0BAA0B,CAAC;AAEhE,WAAO,UAAU,SAAS,mBAAmB,MAAM,cAAc;AAChE,UAAI,YAAY,aAAa,MAAM,CAAC,CAAC,YAAY;AACjD,UAAI,OAAO,cAAc,cAAc,SAAS,MAAM,aAAa,IAAI,IAAI;AAC1E,eAAO,SAAS,SAAS;AAAA,MAC1B;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,iBAAiB,iBAAiC;AACtD,QAAI,YAAY;AAEhB,QAAI,YAAY,UAAU,2BAA2B;AAErD,QAAI,sBAAsB,SAAS,YAAY,OAAO;AACrD,UAAI,kBAAkB,SAAS,OAAO,UAAU,YAAY,OAAO,eAAe,OAAO;AACxF,eAAO;AAAA,MACR;AACA,aAAO,UAAU,KAAK,MAAM;AAAA,IAC7B;AAEA,QAAI,oBAAoB,SAAS,YAAY,OAAO;AACnD,UAAI,oBAAoB,KAAK,GAAG;AAC/B,eAAO;AAAA,MACR;AACA,aAAO,UAAU,QAChB,OAAO,UAAU,YACjB,OAAO,MAAM,WAAW,YACxB,MAAM,UAAU,KAChB,UAAU,KAAK,MAAM,oBACrB,UAAU,MAAM,MAAM,MAAM;AAAA,IAC9B;AAEA,QAAI,4BAA6B,WAAY;AAC5C,aAAO,oBAAoB,SAAS;AAAA,IACrC,EAAE;AAEF,wBAAoB,oBAAoB;AAExC,WAAO,UAAU,4BAA4B,sBAAsB;AAAA;AAAA;;;AChCnE;AAAA;AAAA;AAEA,QAAI,eAAe;AAEnB,QAAI,kBAAkB,aAAa,2BAA2B,IAAI;AAElE,QAAI,yBAAyB,SAASC,0BAAyB;AAC9D,UAAI,iBAAiB;AACpB,YAAI;AACH,0BAAgB,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,CAAC;AACrC,iBAAO;AAAA,QACR,SAAS,GAAP;AAED,iBAAO;AAAA,QACR;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,2BAAuB,0BAA0B,SAAS,0BAA0B;AAEnF,UAAI,CAAC,uBAAuB,GAAG;AAC9B,eAAO;AAAA,MACR;AACA,UAAI;AACH,eAAO,gBAAgB,CAAC,GAAG,UAAU,EAAE,OAAO,EAAE,CAAC,EAAE,WAAW;AAAA,MAC/D,SAAS,GAAP;AAED,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChCjB;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,aAAa,OAAO,WAAW,cAAc,OAAO,OAAO,KAAK,MAAM;AAE1E,QAAI,QAAQ,OAAO,UAAU;AAC7B,QAAI,SAAS,MAAM,UAAU;AAC7B,QAAI,qBAAqB,OAAO;AAEhC,QAAIC,cAAa,SAAU,IAAI;AAC9B,aAAO,OAAO,OAAO,cAAc,MAAM,KAAK,EAAE,MAAM;AAAA,IACvD;AAEA,QAAI,yBAAyB,mCAAoC;AAEjE,QAAI,sBAAsB,sBAAsB;AAEhD,QAAIC,kBAAiB,SAAU,QAAQ,MAAM,OAAO,WAAW;AAC9D,UAAI,QAAQ,QAAQ;AACnB,YAAI,cAAc,MAAM;AACvB,cAAI,OAAO,IAAI,MAAM,OAAO;AAC3B;AAAA,UACD;AAAA,QACD,WAAW,CAACD,YAAW,SAAS,KAAK,CAAC,UAAU,GAAG;AAClD;AAAA,QACD;AAAA,MACD;AACA,UAAI,qBAAqB;AACxB,2BAAmB,QAAQ,MAAM;AAAA,UAChC,cAAc;AAAA,UACd,YAAY;AAAA,UACZ;AAAA,UACA,UAAU;AAAA,QACX,CAAC;AAAA,MACF,OAAO;AACN,eAAO,IAAI,IAAI;AAAA,MAChB;AAAA,IACD;AAEA,QAAI,mBAAmB,SAAU,QAAQ,KAAK;AAC7C,UAAI,aAAa,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI,CAAC;AACxD,UAAI,QAAQ,KAAK,GAAG;AACpB,UAAI,YAAY;AACf,gBAAQ,OAAO,KAAK,OAAO,OAAO,sBAAsB,GAAG,CAAC;AAAA,MAC7D;AACA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,QAAAC,gBAAe,QAAQ,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,GAAG,WAAW,MAAM,CAAC,CAAC,CAAC;AAAA,MACrE;AAAA,IACD;AAEA,qBAAiB,sBAAsB,CAAC,CAAC;AAEzC,WAAO,UAAU;AAAA;AAAA;;;ACpDjB,IAAAC,0BAAA;AAAA;AAAA;AAEA,QAAI,cAAc,SAAU,OAAO;AAClC,aAAO,UAAU;AAAA,IAClB;AAEA,WAAO,UAAU,SAAS,GAAG,GAAG,GAAG;AAClC,UAAI,MAAM,KAAK,MAAM,GAAG;AACvB,eAAO,IAAI,MAAM,IAAI;AAAA,MACtB;AACA,UAAI,MAAM,GAAG;AACZ,eAAO;AAAA,MACR;AACA,UAAI,YAAY,CAAC,KAAK,YAAY,CAAC,GAAG;AACrC,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACjBA;AAAA;AAAA;AAEA,QAAI,iBAAiB;AAErB,WAAO,UAAU,SAAS,cAAc;AACvC,aAAO,OAAO,OAAO,OAAO,aAAa,OAAO,KAAK;AAAA,IACtD;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAEA,QAAI,cAAc;AAClB,QAAI,SAAS;AAEb,WAAO,UAAU,SAAS,eAAe;AACxC,UAAI,WAAW,YAAY;AAC3B,aAAO,QAAQ,EAAE,IAAI,SAAS,GAAG;AAAA,QAChC,IAAI,SAAS,eAAe;AAC3B,iBAAO,OAAO,OAAO;AAAA,QACtB;AAAA,MACD,CAAC;AACD,aAAO;AAAA,IACR;AAAA;AAAA;;;ACbA;AAAA;AAAA;AAEA,QAAI,SAAS;AACb,QAAI,WAAW;AAEf,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,QAAI,OAAO;AAEX,QAAI,WAAW,SAAS,YAAY,GAAG,MAAM;AAE7C,WAAO,UAAU;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA;AAEA,QAAI,YAAY;AAChB,QAAI,iBAAiB,iBAAiC;AACtD,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,QAAI,gBAAgB;AACnB,YAAM,UAAU,iCAAiC;AACjD,cAAQ,UAAU,uBAAuB;AACzC,sBAAgB,CAAC;AAEb,yBAAmB,WAAY;AAClC,cAAM;AAAA,MACP;AACA,uBAAiB;AAAA,QAChB,UAAU;AAAA,QACV,SAAS;AAAA,MACV;AAEA,UAAI,OAAO,OAAO,gBAAgB,UAAU;AAC3C,uBAAe,OAAO,WAAW,IAAI;AAAA,MACtC;AAAA,IACD;AAXK;AAaL,QAAI,YAAY,UAAU,2BAA2B;AACrD,QAAI,OAAO,OAAO;AAClB,QAAI,aAAa;AAEjB,WAAO,UAAU,iBAEd,SAAS,QAAQ,OAAO;AACzB,UAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACxC,eAAO;AAAA,MACR;AAEA,UAAI,aAAa,KAAK,OAAO,WAAW;AACxC,UAAI,2BAA2B,cAAc,IAAI,YAAY,OAAO;AACpE,UAAI,CAAC,0BAA0B;AAC9B,eAAO;AAAA,MACR;AAEA,UAAI;AACH,cAAM,OAAO,cAAc;AAAA,MAC5B,SAAS,GAAP;AACD,eAAO,MAAM;AAAA,MACd;AAAA,IACD,IACE,SAAS,QAAQ,OAAO;AAEzB,UAAI,CAAC,SAAU,OAAO,UAAU,YAAY,OAAO,UAAU,YAAa;AACzE,eAAO;AAAA,MACR;AAEA,aAAO,UAAU,KAAK,MAAM;AAAA,IAC7B;AAAA;AAAA;;;ACzDD;AAAA;AAAA;AAEA,QAAI,qBAAqB,SAASC,sBAAqB;AACtD,aAAO,OAAO,SAAS,IAAI;AAAA,MAAC,EAAE,SAAS;AAAA,IACxC;AAEA,QAAI,OAAO,OAAO;AAClB,QAAI,MAAM;AACT,UAAI;AACH,aAAK,CAAC,GAAG,QAAQ;AAAA,MAClB,SAAS,GAAP;AAED,eAAO;AAAA,MACR;AAAA,IACD;AAEA,uBAAmB,iCAAiC,SAAS,iCAAiC;AAC7F,UAAI,CAAC,mBAAmB,KAAK,CAAC,MAAM;AACnC,eAAO;AAAA,MACR;AACA,UAAI,OAAO,KAAK,WAAY;AAAA,MAAC,GAAG,MAAM;AACtC,aAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK;AAAA,IACzB;AAEA,QAAI,QAAQ,SAAS,UAAU;AAE/B,uBAAmB,0BAA0B,SAAS,0BAA0B;AAC/E,aAAO,mBAAmB,KAAK,OAAO,UAAU,cAAc,SAAS,IAAI;AAAA,MAAC,EAAE,KAAK,EAAE,SAAS;AAAA,IAC/F;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC9BjB,IAAAC,0BAAA;AAAA;AAAA;AAEA,QAAI,iCAAiC,+BAAgC,+BAA+B;AAEpG,QAAI,UAAU;AACd,QAAI,aAAa;AAEjB,WAAO,UAAU,SAAS,QAAQ;AACjC,UAAI,QAAQ,QAAQ,SAAS,QAAQ,IAAI,GAAG;AAC3C,cAAM,IAAI,WAAW,oDAAoD;AAAA,MAC1E;AACA,UAAI,SAAS;AACb,UAAI,KAAK,YAAY;AACpB,kBAAU;AAAA,MACX;AACA,UAAI,KAAK,QAAQ;AAChB,kBAAU;AAAA,MACX;AACA,UAAI,KAAK,YAAY;AACpB,kBAAU;AAAA,MACX;AACA,UAAI,KAAK,WAAW;AACnB,kBAAU;AAAA,MACX;AACA,UAAI,KAAK,QAAQ;AAChB,kBAAU;AAAA,MACX;AACA,UAAI,KAAK,SAAS;AACjB,kBAAU;AAAA,MACX;AACA,UAAI,KAAK,aAAa;AACrB,kBAAU;AAAA,MACX;AACA,UAAI,KAAK,QAAQ;AAChB,kBAAU;AAAA,MACX;AACA,aAAO;AAAA,IACR;AAEA,QAAI,kCAAkC,OAAO,gBAAgB;AAC5D,aAAO,eAAe,OAAO,SAAS,QAAQ,EAAE,OAAO,YAAY,CAAC;AAAA,IACrE;AAAA;AAAA;;;ACzCA,IAAAC,oBAAA;AAAA;AAAA;AAEA,QAAI,iBAAiB;AAErB,QAAI,sBAAsB,4BAA6B;AACvD,QAAI,QAAQ,OAAO;AAEnB,WAAO,UAAU,SAAS,cAAc;AACvC,UAAI,uBAAwB,OAAQ,UAAU,OAAO;AACpD,YAAI,aAAa,MAAM,OAAO,WAAW,OAAO;AAChD,YACC,cACG,OAAO,WAAW,QAAQ,cAC1B,OAAO,OAAO,UAAU,WAAW,aACnC,OAAO,OAAO,UAAU,eAAe,WACzC;AAED,cAAI,QAAQ;AACZ,cAAI,IAAI,CAAC;AACT,iBAAO,eAAe,GAAG,cAAc;AAAA,YACtC,KAAK,WAAY;AAChB,uBAAS;AAAA,YACV;AAAA,UACD,CAAC;AACD,iBAAO,eAAe,GAAG,UAAU;AAAA,YAClC,KAAK,WAAY;AAChB,uBAAS;AAAA,YACV;AAAA,UACD,CAAC;AACD,cAAI,UAAU,MAAM;AACnB,mBAAO,WAAW;AAAA,UACnB;AAAA,QACD;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACnCA,IAAAC,gBAAA;AAAA;AAAA;AAEA,QAAI,sBAAsB,4BAA6B;AACvD,QAAI,cAAc;AAClB,QAAI,OAAO,OAAO;AAClB,QAAIC,kBAAiB,OAAO;AAC5B,QAAI,UAAU;AACd,QAAI,WAAW,OAAO;AACtB,QAAI,QAAQ;AAEZ,WAAO,UAAU,SAAS,YAAY;AACrC,UAAI,CAAC,uBAAuB,CAAC,UAAU;AACtC,cAAM,IAAI,QAAQ,2FAA2F;AAAA,MAC9G;AACA,UAAI,WAAW,YAAY;AAC3B,UAAI,QAAQ,SAAS,KAAK;AAC1B,UAAI,aAAa,KAAK,OAAO,OAAO;AACpC,UAAI,CAAC,cAAc,WAAW,QAAQ,UAAU;AAC/C,QAAAA,gBAAe,OAAO,SAAS;AAAA,UAC9B,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,KAAK;AAAA,QACN,CAAC;AAAA,MACF;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzBA;AAAA;AAAA;AAEA,QAAI,SAAS;AACb,QAAI,WAAW;AAEf,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,QAAI,OAAO;AAEX,QAAI,aAAa,SAAS,YAAY,CAAC;AAEvC,WAAO,YAAY;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA;AAEA,QAAI,SAAS,KAAK,UAAU;AAC5B,QAAI,gBAAgB,SAAS,kBAAkB,OAAO;AACrD,UAAI;AACH,eAAO,KAAK,KAAK;AACjB,eAAO;AAAA,MACR,SAAS,GAAP;AACD,eAAO;AAAA,MACR;AAAA,IACD;AAEA,QAAI,QAAQ,OAAO,UAAU;AAC7B,QAAI,YAAY;AAChB,QAAI,iBAAiB,iBAAiC;AAEtD,WAAO,UAAU,SAAS,aAAa,OAAO;AAC7C,UAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,eAAO;AAAA,MACR;AACA,aAAO,iBAAiB,cAAc,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM;AAAA,IACtE;AAAA;AAAA;;;ACrBA;AAAA;AAAA,QAAI,aAAa;AACjB,QAAI,cAAc;AAClB,QAAI,KAAK;AACT,QAAI,UAAU;AACd,QAAI,QAAQ;AACZ,QAAI,SAAS;AAEb,QAAI,UAAU,KAAK,UAAU;AAE7B,aAAS,UAAU,QAAQ,UAAU,SAAS;AAC5C,UAAI,OAAO,WAAW,CAAC;AAGvB,UAAI,KAAK,SAAS,GAAG,QAAQ,QAAQ,IAAI,WAAW,UAAU;AAC5D,eAAO;AAAA,MACT;AAGA,UAAI,CAAC,UAAU,CAAC,YAAa,OAAO,WAAW,YAAY,OAAO,aAAa,UAAW;AACxF,eAAO,KAAK,SAAS,GAAG,QAAQ,QAAQ,IAAI,UAAU;AAAA,MACxD;AAWA,aAAO,SAAS,QAAQ,UAAU,IAAI;AAAA,IACxC;AAEA,aAAS,kBAAkB,OAAO;AAChC,aAAO,UAAU,QAAQ,UAAU;AAAA,IACrC;AAEA,aAAS,SAAS,GAAG;AACnB,UAAI,CAAC,KAAK,OAAO,MAAM,YAAY,OAAO,EAAE,WAAW,UAAU;AAC/D,eAAO;AAAA,MACT;AACA,UAAI,OAAO,EAAE,SAAS,cAAc,OAAO,EAAE,UAAU,YAAY;AACjE,eAAO;AAAA,MACT;AACA,UAAI,EAAE,SAAS,KAAK,OAAO,EAAE,CAAC,MAAM,UAAU;AAC5C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,aAAS,SAAS,GAAG,GAAG,MAAM;AAE5B,UAAI,GAAG;AACP,UAAI,OAAO,MAAM,OAAO,GAAG;AAAE,eAAO;AAAA,MAAO;AAC3C,UAAI,kBAAkB,CAAC,KAAK,kBAAkB,CAAC,GAAG;AAAE,eAAO;AAAA,MAAO;AAGlE,UAAI,EAAE,cAAc,EAAE,WAAW;AAAE,eAAO;AAAA,MAAO;AAEjD,UAAI,YAAY,CAAC,MAAM,YAAY,CAAC,GAAG;AAAE,eAAO;AAAA,MAAO;AAEvD,UAAI,WAAW,QAAQ,CAAC;AACxB,UAAI,WAAW,QAAQ,CAAC;AACxB,UAAI,aAAa,UAAU;AAAE,eAAO;AAAA,MAAO;AAC3C,UAAI,YAAY,UAAU;AACxB,eAAO,EAAE,WAAW,EAAE,UAAU,MAAM,CAAC,MAAM,MAAM,CAAC;AAAA,MACtD;AAEA,UAAI,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AAC1B,eAAO,QAAQ,KAAK,CAAC,MAAM,QAAQ,KAAK,CAAC;AAAA,MAC3C;AAEA,UAAI,YAAY,SAAS,CAAC;AAC1B,UAAI,YAAY,SAAS,CAAC;AAC1B,UAAI,cAAc,WAAW;AAAE,eAAO;AAAA,MAAO;AAC7C,UAAI,aAAa,WAAW;AAC1B,YAAI,EAAE,WAAW,EAAE,QAAQ;AAAE,iBAAO;AAAA,QAAO;AAC3C,aAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC7B,cAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AAAE,mBAAO;AAAA,UAAO;AAAA,QACrC;AACA,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,MAAM,OAAO,GAAG;AAAE,eAAO;AAAA,MAAO;AAE3C,UAAI;AACF,YAAI,KAAK,WAAW,CAAC;AACrB,YAAI,KAAK,WAAW,CAAC;AAAA,MACvB,SAAS,GAAP;AACA,eAAO;AAAA,MACT;AAEA,UAAI,GAAG,WAAW,GAAG,QAAQ;AAAE,eAAO;AAAA,MAAO;AAG7C,SAAG,KAAK;AACR,SAAG,KAAK;AAER,WAAK,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK;AACnC,YAAI,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG;AAAE,iBAAO;AAAA,QAAO;AAAA,MACtC;AAEA,WAAK,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK;AACnC,cAAM,GAAG,CAAC;AACV,YAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG;AAAE,iBAAO;AAAA,QAAO;AAAA,MACxD;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/GjB;AAAA;AAAA;AAGA,QAAI,MAAM;AAEV,WAAO,UAAU,WAAW;AAC1B,aAAO,OAAO,GAAG,KAAK,OAAO,GAAG,KAAK,KAAK;AAAA,IAC5C;AAAA;AAAA;;;ACPA,IAAAC,0BAAA;AAAA;AAAA;AAEA,YAAQ,aAAa;AAErB,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,aAAa;AAEjB,QAAI,cAAc,uBAAuB,UAAU;AAEnD,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI;AAAY,eAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,wBAAwB;AAI5B,aAAS,SAAS,GAAG,GAAG;AACtB,UAAI,MAAM,GAAG;AACX,eAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,MAClC,OAAO;AACL,eAAO,MAAM,KAAK,MAAM;AAAA,MAC1B;AAAA,IACF;AAEA,aAAS,mBAAmB,OAAO;AACjC,UAAI,WAAW,CAAC;AAChB,aAAO;AAAA,QACL,IAAI,SAAS,GAAG,SAAS;AACvB,mBAAS,KAAK,OAAO;AAAA,QACvB;AAAA,QACA,KAAK,SAAS,IAAI,SAAS;AACzB,qBAAW,SAAS,OAAO,SAAU,GAAG;AACtC,mBAAO,MAAM;AAAA,UACf,CAAC;AAAA,QACH;AAAA,QACA,KAAK,SAAS,MAAM;AAClB,iBAAO;AAAA,QACT;AAAA,QACA,KAAK,SAAS,IAAI,UAAU,aAAa;AACvC,kBAAQ;AACR,mBAAS,QAAQ,SAAU,SAAS;AAClC,mBAAO,QAAQ,OAAO,WAAW;AAAA,UACnC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,aAAS,UAAU,UAAU;AAC3B,aAAO,MAAM,QAAQ,QAAQ,IAAI,SAAS,CAAC,IAAI;AAAA,IACjD;AAEA,aAAS,mBAAmB,cAAc,sBAAsB;AAC9D,UAAI,uBAAuB;AAE3B,UAAI,cAAc,6BAA6B,GAAG,MAAM,SAAS,IAAI;AAErE,UAAI,WAAW,SAAU,YAAY;AACnC,kBAAUC,WAAU,UAAU;AAE9B,iBAASA,YAAW;AAClB,cAAI,OAAO,OAAO;AAElB,0BAAgB,MAAMA,SAAQ;AAE9B,mBAAS,OAAO,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACnF,iBAAK,IAAI,IAAI,UAAU,IAAI;AAAA,UAC7B;AAEA,iBAAO,QAAQ,SAAS,QAAQ,2BAA2B,MAAM,WAAW,KAAK,MAAM,YAAY,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC,GAAG,QAAQ,MAAM,UAAU,mBAAmB,MAAM,MAAM,KAAK,GAAG,QAAQ,2BAA2B,OAAO,IAAI;AAAA,QACzO;AAEA,QAAAA,UAAS,UAAU,kBAAkB,SAAS,kBAAkB;AAC9D,cAAI;AAEJ,iBAAO,OAAO,CAAC,GAAG,KAAK,WAAW,IAAI,KAAK,SAAS;AAAA,QACtD;AAEA,QAAAA,UAAS,UAAU,4BAA4B,SAAS,0BAA0B,WAAW;AAC3F,cAAI,KAAK,MAAM,UAAU,UAAU,OAAO;AACxC,gBAAI,WAAW,KAAK,MAAM;AAC1B,gBAAI,WAAW,UAAU;AACzB,gBAAI,cAAc;AAElB,gBAAI,SAAS,UAAU,QAAQ,GAAG;AAChC,4BAAc;AAAA,YAChB,OAAO;AACL,4BAAc,OAAO,yBAAyB,aAAa,qBAAqB,UAAU,QAAQ,IAAI;AACtG,kBAAI,MAAuC;AACzC,iBAAC,GAAG,UAAU,UAAU,cAAc,2BAA2B,aAAa,gGAAqG,WAAW;AAAA,cAChM;AAEA,6BAAe;AAEf,kBAAI,gBAAgB,GAAG;AACrB,qBAAK,QAAQ,IAAI,UAAU,OAAO,WAAW;AAAA,cAC/C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,QAAAA,UAAS,UAAU,SAAS,SAAS,SAAS;AAC5C,iBAAO,KAAK,MAAM;AAAA,QACpB;AAEA,eAAOA;AAAA,MACT,EAAE,OAAO,SAAS;AAElB,eAAS,qBAAqB,wBAAwB,CAAC,GAAG,sBAAsB,WAAW,IAAI,YAAY,QAAQ,OAAO,YAAY;AAEtI,UAAI,WAAW,SAAU,aAAa;AACpC,kBAAUC,WAAU,WAAW;AAE/B,iBAASA,YAAW;AAClB,cAAI,QAAQ,QAAQ;AAEpB,0BAAgB,MAAMA,SAAQ;AAE9B,mBAAS,QAAQ,UAAU,QAAQ,OAAO,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACzF,iBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,UAC/B;AAEA,iBAAO,SAAS,UAAU,SAAS,2BAA2B,MAAM,YAAY,KAAK,MAAM,aAAa,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC,GAAG,SAAS,OAAO,QAAQ;AAAA,YACrJ,OAAO,OAAO,SAAS;AAAA,UACzB,GAAG,OAAO,WAAW,SAAU,UAAU,aAAa;AACpD,gBAAI,eAAe,OAAO,eAAe;AACzC,iBAAK,eAAe,iBAAiB,GAAG;AACtC,qBAAO,SAAS,EAAE,OAAO,OAAO,SAAS,EAAE,CAAC;AAAA,YAC9C;AAAA,UACF,GAAG,SAAS,2BAA2B,QAAQ,KAAK;AAAA,QACtD;AAEA,QAAAA,UAAS,UAAU,4BAA4B,SAAS,0BAA0B,WAAW;AAC3F,cAAI,eAAe,UAAU;AAE7B,eAAK,eAAe,iBAAiB,UAAa,iBAAiB,OAAO,wBACxE;AAAA,QACJ;AAEA,QAAAA,UAAS,UAAU,oBAAoB,SAAS,oBAAoB;AAClE,cAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,iBAAK,QAAQ,WAAW,EAAE,GAAG,KAAK,QAAQ;AAAA,UAC5C;AACA,cAAI,eAAe,KAAK,MAAM;AAE9B,eAAK,eAAe,iBAAiB,UAAa,iBAAiB,OAAO,wBACxE;AAAA,QACJ;AAEA,QAAAA,UAAS,UAAU,uBAAuB,SAAS,uBAAuB;AACxE,cAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,iBAAK,QAAQ,WAAW,EAAE,IAAI,KAAK,QAAQ;AAAA,UAC7C;AAAA,QACF;AAEA,QAAAA,UAAS,UAAU,WAAW,SAAS,WAAW;AAChD,cAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,mBAAO,KAAK,QAAQ,WAAW,EAAE,IAAI;AAAA,UACvC,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,QAAAA,UAAS,UAAU,SAAS,SAAS,SAAS;AAC5C,iBAAO,UAAU,KAAK,MAAM,QAAQ,EAAE,KAAK,MAAM,KAAK;AAAA,QACxD;AAEA,eAAOA;AAAA,MACT,EAAE,OAAO,SAAS;AAElB,eAAS,gBAAgB,wBAAwB,CAAC,GAAG,sBAAsB,WAAW,IAAI,YAAY,QAAQ,QAAQ;AAGtH,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,YAAQ,UAAU;AAClB,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;ACnMlC;AAAA;AAAA;AAEA,YAAQ,aAAa;AAErB,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,kBAAkB;AAEtB,QAAI,mBAAmB,uBAAuB,eAAe;AAE7D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,UAAU,QAAQ,QAAQ,iBAAiB,iBAAiB;AACpE,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;ACflC,IAAA,YAAe,OAAOC,WAAW,eAAe,OAAOC,aAAa,eAAe,OAAOC,cAAc;ACExG,IAAMC,kBAAmB,WAAU;MAC3BC,wBAAwB,CAAC,QAAQ,WAAW,SAApB;WACrBC,IAAI,GAAGA,IAAID,sBAAsBE,QAAQD,KAAK,GAAG;QACpDE,aAAaL,UAAUM,UAAUC,QAAQL,sBAAsBC,CAAtB,CAA5B,KAAyD,GAAG;aACpE;;;SAGJ;EAPgB;AAUlB,SAASK,kBAAkBC,IAAI;MAChCC,SAAS;SACN,WAAM;QACPA,QAAQ;;;aAGH;WACFC,QAAQC,QAAf,EAAyBC,KAAK,WAAM;eACzB;;KADX;;;AAOG,SAASC,aAAaL,IAAI;MAC3BM,YAAY;SACT,WAAM;QACP,CAACA,WAAW;kBACF;iBACD,WAAM;oBACH;;SAEXd,eAHH;;;;AAQN,IAAMe,qBAAqBX,aAAaP,OAAOa;AAY/C,IAAA,WAAgBK,qBACZR,oBACAM;AC9CW,SAASG,WAAWC,iBAAiB;MAC5CC,UAAU,CAAA;SAEdD,mBACAC,QAAQC,SAASC,KAAKH,eAAtB,MAA2C;;ACJhC,SAASI,yBAAyBC,SAASC,UAAU;MAC9DD,QAAQE,aAAa,GAAG;WACnB,CAAA;;MAGH3B,UAASyB,QAAQG,cAAcC;MAC/BC,MAAM9B,QAAO+B,iBAAiBN,SAAS,IAAjC;SACLC,WAAWI,IAAIJ,QAAJ,IAAgBI;;ACPrB,SAASE,cAAcP,SAAS;MACzCA,QAAQQ,aAAa,QAAQ;WACxBR;;SAEFA,QAAQS,cAAcT,QAAQU;;ACDxB,SAASC,gBAAgBX,SAAS;MAE3C,CAACA,SAAS;WACLxB,SAASoC;;UAGVZ,QAAQQ,UAAhB;SACO;SACA;aACIR,QAAQG,cAAcS;SAC1B;aACIZ,QAAQY;;8BAIwBb,yBAAyBC,OAAzB,GAAnCa,WAfuC,sBAevCA,UAAUC,YAf6B,sBAe7BA,WAAWC,YAfkB,sBAelBA;MACzB,wBAAwBC,KAAKH,WAAWE,YAAYD,SAApD,GAAgE;WAC3Dd;;SAGFW,gBAAgBJ,cAAcP,OAAd,CAAhB;;ACvBM,SAASiB,iBAAiBC,WAAW;SAC3CA,aAAaA,UAAUC,gBAAgBD,UAAUC,gBAAgBD;;ACN1E,IAAME,SAAStC,aAAa,CAAC,EAAEP,OAAO8C,wBAAwB7C,SAAS8C;AACvE,IAAMC,SAASzC,aAAa,UAAUkC,KAAKvC,UAAUM,SAAzB;AASb,SAASyC,KAAKC,SAAS;MAChCA,YAAY,IAAI;WACXL;;MAELK,YAAY,IAAI;WACXF;;SAEFH,UAAUG;;ACVJ,SAASG,gBAAgB1B,SAAS;MAC3C,CAACA,SAAS;WACLxB,SAASmD;;MAGZC,iBAAiBJ,KAAK,EAAL,IAAWhD,SAASoC,OAAO;MAG9CiB,eAAe7B,QAAQ6B,gBAAgB;SAEpCA,iBAAiBD,kBAAkB5B,QAAQ8B,oBAAoB;oBACpD9B,UAAUA,QAAQ8B,oBAAoBD;;MAGlDrB,WAAWqB,gBAAgBA,aAAarB;MAE1C,CAACA,YAAYA,aAAa,UAAUA,aAAa,QAAQ;WACpDR,UAAUA,QAAQG,cAAcwB,kBAAkBnD,SAASmD;;MAMlE,CAAC,MAAM,MAAM,OAAb,EAAsB3C,QAAQ6C,aAAarB,QAA3C,MAAyD,MACzDT,yBAAyB8B,cAAc,UAAvC,MAAuD,UACvD;WACOH,gBAAgBG,YAAhB;;SAGFA;;ACpCM,SAASE,kBAAkB/B,SAAS;MACzCQ,WAAaR,QAAbQ;MACJA,aAAa,QAAQ;WAChB;;SAGPA,aAAa,UAAUkB,gBAAgB1B,QAAQgC,iBAAxB,MAA+ChC;;ACD3D,SAASiC,QAAQC,MAAM;MAChCA,KAAKzB,eAAe,MAAM;WACrBwB,QAAQC,KAAKzB,UAAb;;SAGFyB;;ACAM,SAASC,uBAAuBC,UAAUC,UAAU;MAE7D,CAACD,YAAY,CAACA,SAASlC,YAAY,CAACmC,YAAY,CAACA,SAASnC,UAAU;WAC/D1B,SAASmD;;MAIZW,QACJF,SAASG,wBAAwBF,QAAjC,IACAG,KAAKC;MACDC,QAAQJ,QAAQF,WAAWC;MAC3BM,MAAML,QAAQD,WAAWD;MAGzBQ,QAAQpE,SAASqE,YAAT;QACRC,SAASJ,OAAO,CAAtB;QACMK,OAAOJ,KAAK,CAAlB;MACQK,0BAA4BJ,MAA5BI;MAILZ,aAAaY,2BACZX,aAAaW,2BACfN,MAAMO,SAASN,GAAf,GACA;QACIZ,kBAAkBiB,uBAAlB,GAA4C;aACvCA;;WAGFtB,gBAAgBsB,uBAAhB;;MAIHE,eAAejB,QAAQG,QAAR;MACjBc,aAAaxC,MAAM;WACdyB,uBAAuBe,aAAaxC,MAAM2B,QAA1C;SACF;WACEF,uBAAuBC,UAAUH,QAAQI,QAAR,EAAkB3B,IAAnD;;;ACzCI,SAASyC,UAAUnD,SAAuB;MAAdoD,OAAc,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAP;MAC1CC,YAAYD,SAAS,QAAQ,cAAc;MAC3C5C,WAAWR,QAAQQ;MAErBA,aAAa,UAAUA,aAAa,QAAQ;QACxC8C,OAAOtD,QAAQG,cAAcwB;QAC7B4B,mBAAmBvD,QAAQG,cAAcoD,oBAAoBD;WAC5DC,iBAAiBF,SAAjB;;SAGFrD,QAAQqD,SAAR;;ACPM,SAASG,cAAcC,MAAMzD,SAA2B;MAAlB0D,WAAkB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAP;MACxDC,YAAYR,UAAUnD,SAAS,KAAnB;MACZ4D,aAAaT,UAAUnD,SAAS,MAAnB;MACb6D,WAAWH,WAAW,KAAK;OAC5BI,OAAOH,YAAYE;OACnBE,UAAUJ,YAAYE;OACtBG,QAAQJ,aAAaC;OACrBI,SAASL,aAAaC;SACpBJ;;ACTM,SAASS,eAAeC,QAAQC,MAAM;MAC7CC,QAAQD,SAAS,MAAM,SAAS;MAChCE,QAAQD,UAAU,SAAS,UAAU;SAGzCE,WAAWJ,OAAAA,WAAgBE,QAAhB,OAAA,CAAX,IACAE,WAAWJ,OAAAA,WAAgBG,QAAhB,OAAA,CAAX;;ACdJ,SAASE,QAAQJ,MAAMxD,MAAM0C,MAAMmB,eAAe;SACzCC,KAAKC,IACV/D,KAAAA,WAAcwD,IAAd,GACAxD,KAAAA,WAAcwD,IAAd,GACAd,KAAAA,WAAcc,IAAd,GACAd,KAAAA,WAAcc,IAAd,GACAd,KAAAA,WAAcc,IAAd,GACA5C,KAAK,EAAL,IACKoD,SAAStB,KAAAA,WAAcc,IAAd,CAAT,IACHQ,SAASH,cAAAA,YAAuBL,SAAS,WAAW,QAAQ,OAAnD,CAAT,IACAQ,SAASH,cAAAA,YAAuBL,SAAS,WAAW,WAAW,QAAtD,CAAT,IACA,CAVG;;AAcM,SAASS,eAAerG,WAAU;MACzCoC,OAAOpC,UAASoC;MAChB0C,OAAO9E,UAASmD;MAChB8C,gBAAgBjD,KAAK,EAAL,KAAYlB,iBAAiBgD,IAAjB;SAE3B;YACGkB,QAAQ,UAAU5D,MAAM0C,MAAMmB,aAA9B;WACDD,QAAQ,SAAS5D,MAAM0C,MAAMmB,aAA7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjBI,SAASK,cAAcC,SAAS;sBAExCA,SADL;WAESA,QAAQf,OAAOe,QAAQC;YACtBD,QAAQjB,MAAMiB,QAAQE;;;ACGnB,SAASC,sBAAsBlF,SAAS;MACjDyD,OAAO,CAAA;MAKP;QACEjC,KAAK,EAAL,GAAU;aACLxB,QAAQkF,sBAAR;UACDvB,YAAYR,UAAUnD,SAAS,KAAnB;UACZ4D,aAAaT,UAAUnD,SAAS,MAAnB;WACd8D,OAAOH;WACPK,QAAQJ;WACRG,UAAUJ;WACVM,SAASL;WAEX;aACI5D,QAAQkF,sBAAR;;WAGLC,GAAN;EAAQ;MAEFC,SAAS;UACP3B,KAAKO;SACNP,KAAKK;WACHL,KAAKQ,QAAQR,KAAKO;YACjBP,KAAKM,SAASN,KAAKK;;MAIvBuB,QAAQrF,QAAQQ,aAAa,SAASqE,eAAe7E,QAAQG,aAAvB,IAAwC,CAAA;MAC9E6E,QACJK,MAAML,SAAShF,QAAQsF,eAAeF,OAAOJ;MACzCC,SACJI,MAAMJ,UAAUjF,QAAQuF,gBAAgBH,OAAOH;MAE7CO,iBAAiBxF,QAAQyF,cAAcT;MACvCU,gBAAgB1F,QAAQ2F,eAAeV;MAIvCO,kBAAkBE,eAAe;QAC7BvB,SAASpE,yBAAyBC,OAAzB;sBACGkE,eAAeC,QAAQ,GAAvB;qBACDD,eAAeC,QAAQ,GAAvB;WAEVa,SAASQ;WACTP,UAAUS;;SAGZZ,cAAcM,MAAd;;ACzDM,SAASQ,qCAAqCC,UAAUC,QAA+B;MAAvBC,gBAAuB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAP;MACvFxE,UAASyE,KAAQ,EAAR;MACTC,SAASH,OAAOtF,aAAa;MAC7B0F,eAAehB,sBAAsBW,QAAtB;MACfM,aAAajB,sBAAsBY,MAAtB;MACbM,eAAezF,gBAAgBkF,QAAhB;MAEf1B,SAASpE,yBAAyB+F,MAAzB;MACTO,iBAAiB9B,WAAWJ,OAAOkC,cAAlB;MACjBC,kBAAkB/B,WAAWJ,OAAOmC,eAAlB;MAGrBP,iBAAiBE,QAAQ;eACfnC,MAAMY,KAAKC,IAAIwB,WAAWrC,KAAK,CAAzB;eACNE,OAAOU,KAAKC,IAAIwB,WAAWnC,MAAM,CAA1B;;MAEhBe,UAAUD,cAAc;SACrBoB,aAAapC,MAAMqC,WAAWrC,MAAMuC;UACnCH,aAAalC,OAAOmC,WAAWnC,OAAOsC;WACrCJ,aAAalB;YACZkB,aAAajB;GAJT;UAMNsB,YAAY;UACZC,aAAa;MAMjB,CAACjF,WAAU0E,QAAQ;QACfM,YAAYhC,WAAWJ,OAAOoC,SAAlB;QACZC,aAAajC,WAAWJ,OAAOqC,UAAlB;YAEX1C,OAAOuC,iBAAiBE;YACxBxC,UAAUsC,iBAAiBE;YAC3BvC,QAAQsC,kBAAkBE;YAC1BvC,SAASqC,kBAAkBE;YAG3BD,YAAYA;YACZC,aAAaA;;MAIrBjF,WAAU,CAACwE,gBACPD,OAAO7C,SAASmD,YAAhB,IACAN,WAAWM,gBAAgBA,aAAa5F,aAAa,QACzD;cACUgD,cAAcuB,SAASe,MAAvB;;SAGLf;;ACtDM,SAAS0B,8CAA8CzG,SAAgC;MAAvB0G,gBAAuB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAP;MACvFpD,OAAOtD,QAAQG,cAAcwB;MAC7BgF,iBAAiBf,qCAAqC5F,SAASsD,IAA9C;MACjB0B,QAAQN,KAAKC,IAAIrB,KAAKgC,aAAa/G,OAAOqI,cAAc,CAAhD;MACR3B,SAASP,KAAKC,IAAIrB,KAAKiC,cAAchH,OAAOsI,eAAe,CAAlD;MAETlD,YAAY,CAAC+C,gBAAgBvD,UAAUG,IAAV,IAAkB;MAC/CM,aAAa,CAAC8C,gBAAgBvD,UAAUG,MAAM,MAAhB,IAA0B;MAExDwD,UAAS;SACRnD,YAAYgD,eAAe7C,MAAM6C,eAAeJ;UAC/C3C,aAAa+C,eAAe3C,OAAO2C,eAAeH;;;;SAKnD1B,cAAcgC,OAAd;;ACTM,SAASC,QAAQ/G,SAAS;MACjCQ,WAAWR,QAAQQ;MACrBA,aAAa,UAAUA,aAAa,QAAQ;WACvC;;MAELT,yBAAyBC,SAAS,UAAlC,MAAkD,SAAS;WACtD;;MAEHS,aAAaF,cAAcP,OAAd;MACf,CAACS,YAAY;WACR;;SAEFsG,QAAQtG,UAAR;;ACbM,SAASuG,6BAA6BhH,SAAS;MAEvD,CAACA,WAAW,CAACA,QAAQiH,iBAAiBzF,KAAAA,GAAQ;WAC1ChD,SAASmD;;MAEduF,KAAKlH,QAAQiH;SACVC,MAAMnH,yBAAyBmH,IAAI,WAA7B,MAA8C,QAAQ;SAC5DA,GAAGD;;SAEHC,MAAM1I,SAASmD;;ACET,SAASwF,cACtBC,QACAlG,WACAmG,SACAC,mBAEA;MADAvB,gBACA,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IADgB;MAIZwB,aAAa,EAAEzD,KAAK,GAAGE,MAAM,EAAhB;MACXnC,eAAekE,gBAAgBiB,6BAA6BI,MAA7B,IAAuCjF,uBAAuBiF,QAAQnG,iBAAiBC,SAAjB,CAA/B;MAGxEoG,sBAAsB,YAAa;iBACxBb,8CAA8C5E,cAAckE,aAA5D;SAGV;QAECyB,iBAAAA;QACAF,sBAAsB,gBAAgB;uBACvB3G,gBAAgBJ,cAAcW,SAAd,CAAhB;UACbsG,eAAehH,aAAa,QAAQ;yBACrB4G,OAAOjH,cAAcwB;;eAE/B2F,sBAAsB,UAAU;uBACxBF,OAAOjH,cAAcwB;WACjC;uBACY2F;;QAGbvC,UAAUa,qCACd4B,gBACA3F,cACAkE,aAHc;QAOZyB,eAAehH,aAAa,UAAU,CAACuG,QAAQlF,YAAR,GAAuB;4BACtCgD,eAAeuC,OAAOjH,aAAtB,GAAlB8E,SADwD,gBACxDA,QAAQD,QADgD,gBAChDA;iBACLlB,OAAOiB,QAAQjB,MAAMiB,QAAQwB;iBAC7BxC,SAASkB,SAASF,QAAQjB;iBAC1BE,QAAQe,QAAQf,OAAOe,QAAQyB;iBAC/BvC,QAAQe,QAAQD,QAAQf;WAC9B;mBAEQe;;;YAKPsC,WAAW;MACfI,kBAAkB,OAAOJ,YAAY;aAChCrD,QAAQyD,kBAAkBJ,UAAUA,QAAQrD,QAAQ;aACpDF,OAAO2D,kBAAkBJ,UAAUA,QAAQvD,OAAO;aAClDG,SAASwD,kBAAkBJ,UAAUA,QAAQpD,SAAS;aACtDF,UAAU0D,kBAAkBJ,UAAUA,QAAQtD,UAAU;SAE5DwD;;AC7ET,SAASG,QAAT,MAAoC;MAAjB1C,QAAiB,KAAjBA,OAAOC,SAAU,KAAVA;SACjBD,QAAQC;;AAYF,SAAS0C,qBACtBC,WACAC,SACAT,QACAlG,WACAoG,mBAEA;MADAD,UACA,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IADU;MAENO,UAAU5I,QAAQ,MAAlB,MAA8B,IAAI;WAC7B4I;;MAGHL,aAAaJ,cACjBC,QACAlG,WACAmG,SACAC,iBAJiB;MAObQ,QAAQ;SACP;aACIP,WAAWvC;cACV6C,QAAQ/D,MAAMyD,WAAWzD;;WAE5B;aACEyD,WAAWtD,QAAQ4D,QAAQ5D;cAC1BsD,WAAWtC;;YAEb;aACCsC,WAAWvC;cACVuC,WAAWxD,SAAS8D,QAAQ9D;;UAEhC;aACG8D,QAAQ7D,OAAOuD,WAAWvD;cACzBuD,WAAWtC;;;MAIjB8C,cAAcC,OAAOC,KAAKH,KAAZ,EACjBI,IAAI,SAAA,KAAA;;;OAEAJ,MAAMK,GAAN,GAFA;YAGGT,QAAQI,MAAMK,GAAN,CAAR;;GAJU,EAMjBC,KAAK,SAACC,GAAGC,GAAJ;WAAUA,EAAEC,OAAOF,EAAEE;GANT;MAQdC,gBAAgBT,YAAYU,OAChC,SAAA,OAAA;QAAGzD,QAAH,MAAGA,OAAOC,SAAV,MAAUA;WACRD,SAASoC,OAAO9B,eAAeL,UAAUmC,OAAO7B;GAF9B;MAKhBmD,oBAAoBF,cAAc3J,SAAS,IAC7C2J,cAAc,CAAd,EAAiBL,MACjBJ,YAAY,CAAZ,EAAeI;MAEbQ,YAAYf,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;SAEXF,qBAAqBC,YAAAA,MAAgBA,YAAc;;ACzD7C,SAASE,oBAAoBC,OAAO1B,QAAQlG,WAAiC;MAAtB6E,gBAAsB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAN;MAC9EgD,qBAAqBhD,gBAAgBiB,6BAA6BI,MAA7B,IAAuCjF,uBAAuBiF,QAAQnG,iBAAiBC,SAAjB,CAA/B;SAC3E0E,qCAAqC1E,WAAW6H,oBAAoBhD,aAApE;;ACVM,SAASiD,cAAchJ,SAAS;MACvCzB,UAASyB,QAAQG,cAAcC;MAC/B+D,SAAS5F,QAAO+B,iBAAiBN,OAAxB;MACTiJ,IAAI1E,WAAWJ,OAAOoC,aAAa,CAA/B,IAAoChC,WAAWJ,OAAO+E,gBAAgB,CAAlC;MACxCC,IAAI5E,WAAWJ,OAAOqC,cAAc,CAAhC,IAAqCjC,WAAWJ,OAAOiF,eAAe,CAAjC;MACzChE,SAAS;WACNpF,QAAQyF,cAAc0D;YACrBnJ,QAAQ2F,eAAesD;;SAE1B7D;;ACTM,SAASiE,qBAAqBzB,WAAW;MAChD0B,OAAO,EAAEtF,MAAM,SAASC,OAAO,QAAQF,QAAQ,OAAOD,KAAK,SAApD;SACN8D,UAAU2B,QAAQ,0BAA0B,SAAA,SAAA;WAAWD,KAAKE,OAAL;GAAvD;;ACIM,SAASC,iBAAiBrC,QAAQsC,kBAAkB9B,WAAW;cAChEA,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;MAGNe,aAAaX,cAAc5B,MAAd;MAGbwC,gBAAgB;WACbD,WAAW3E;YACV2E,WAAW1E;;MAIf4E,UAAU,CAAC,SAAS,MAAV,EAAkB7K,QAAQ4I,SAA1B,MAAyC;MACnDkC,WAAWD,UAAU,QAAQ;MAC7BE,gBAAgBF,UAAU,SAAS;MACnCG,cAAcH,UAAU,WAAW;MACnCI,uBAAuB,CAACJ,UAAU,WAAW;gBAErCC,QAAd,IACEJ,iBAAiBI,QAAjB,IACAJ,iBAAiBM,WAAjB,IAAgC,IAChCL,WAAWK,WAAX,IAA0B;MACxBpC,cAAcmC,eAAe;kBACjBA,aAAd,IACEL,iBAAiBK,aAAjB,IAAkCJ,WAAWM,oBAAX;SAC/B;kBACSF,aAAd,IACEL,iBAAiBL,qBAAqBU,aAArB,CAAjB;;SAGGH;;ACnCM,SAASM,KAAKC,KAAKC,OAAO;MAEnCC,MAAMC,UAAUJ,MAAM;WACjBC,IAAID,KAAKE,KAAT;;SAIFD,IAAI1B,OAAO2B,KAAX,EAAkB,CAAlB;;ACLM,SAASG,UAAUJ,KAAKK,MAAMC,OAAO;MAE9CJ,MAAMC,UAAUC,WAAW;WACtBJ,IAAII,UAAU,SAAA,KAAA;aAAOG,IAAIF,IAAJ,MAAcC;KAAnC;;MAIHE,QAAQT,KAAKC,KAAK,SAAA,KAAA;WAAOS,IAAIJ,IAAJ,MAAcC;GAA/B;SACPN,IAAInL,QAAQ2L,KAAZ;;ACLM,SAASE,aAAaC,YAAWC,MAAMC,MAAM;MACpDC,iBAAiBD,SAASE,SAC5BJ,aACAA,WAAUK,MAAM,GAAGZ,UAAUO,YAAW,QAAQE,IAA7B,CAAnB;iBAEWI,QAAQ,SAAA,UAAY;QAC7BvH,SAAS,UAAT,GAAsB;cAChBwH,KAAK,uDAAb;;QAEInM,KAAK2E,SAAS,UAAT,KAAwBA,SAAS3E;QACxC2E,SAASyH,WAAW5L,WAAWR,EAAX,GAAgB;WAIjC6F,QAAQqC,SAAStC,cAAciG,KAAKhG,QAAQqC,MAA3B;WACjBrC,QAAQ7D,YAAY4D,cAAciG,KAAKhG,QAAQ7D,SAA3B;aAElBhC,GAAG6L,MAAMlH,QAAT;;GAZX;SAgBOkH;;ACvBM,SAASQ,SAAS;MAE3B,KAAKzC,MAAM0C,aAAa;;;MAIxBT,OAAO;cACC;YACF,CAAA;iBACK,CAAA;gBACD,CAAA;aACH;aACA,CAAA;;OAINhG,QAAQ7D,YAAY2H,oBACvB,KAAKC,OACL,KAAK1B,QACL,KAAKlG,WACL,KAAKuK,QAAQC,aAJU;OAUpB9D,YAAYD,qBACf,KAAK8D,QAAQ7D,WACbmD,KAAKhG,QAAQ7D,WACb,KAAKkG,QACL,KAAKlG,WACL,KAAKuK,QAAQX,UAAUa,KAAKrE,mBAC5B,KAAKmE,QAAQX,UAAUa,KAAKtE,OANb;OAUZuE,oBAAoBb,KAAKnD;OAEzB8D,gBAAgB,KAAKD,QAAQC;OAG7B3G,QAAQqC,SAASqC,iBACpB,KAAKrC,QACL2D,KAAKhG,QAAQ7D,WACb6J,KAAKnD,SAHe;OAMjB7C,QAAQqC,OAAOyE,WAAW,KAAKJ,QAAQC,gBACxC,UACA;SAGGb,aAAa,KAAKC,WAAWC,IAA7B;MAIH,CAAC,KAAKjC,MAAMgD,WAAW;SACpBhD,MAAMgD,YAAY;SAClBL,QAAQM,SAAShB,IAAtB;SACK;SACAU,QAAQO,SAASjB,IAAtB;;;AClEW,SAASkB,kBAAkBnB,YAAWoB,cAAc;SAC1DpB,WAAUqB,KACf,SAAA,MAAA;QAAGC,OAAH,KAAGA,MAAMd,UAAT,KAASA;WAAcA,WAAWc,SAASF;GADtC;;ACAM,SAASG,yBAAyBpM,UAAU;MACnDqM,WAAW,CAAC,OAAO,MAAM,UAAU,OAAO,GAA/B;MACXC,YAAYtM,SAASuM,OAAO,CAAhB,EAAmBC,YAAnB,IAAmCxM,SAASkL,MAAM,CAAf;WAE5CvM,IAAI,GAAGA,IAAI0N,SAASzN,QAAQD,KAAK;QAClC8N,SAASJ,SAAS1N,CAAT;QACT+N,UAAUD,SAAAA,KAAYA,SAASH,YAActM;QAC/C,OAAOzB,SAASoC,KAAKgM,MAAMD,OAApB,MAAiC,aAAa;aAChDA;;;SAGJ;;ACVM,SAASE,UAAU;OAC3B/D,MAAM0C,cAAc;MAGrBS,kBAAkB,KAAKnB,WAAW,YAAlC,GAAiD;SAC9C1D,OAAO0F,gBAAgB,aAA5B;SACK1F,OAAOwF,MAAMf,WAAW;SACxBzE,OAAOwF,MAAM9I,MAAM;SACnBsD,OAAOwF,MAAM5I,OAAO;SACpBoD,OAAOwF,MAAM3I,QAAQ;SACrBmD,OAAOwF,MAAM7I,SAAS;SACtBqD,OAAOwF,MAAMG,aAAa;SAC1B3F,OAAOwF,MAAMP,yBAAyB,WAAzB,CAAlB,IAA2D;;OAGxDW,sBAAL;MAII,KAAKvB,QAAQwB,iBAAiB;SAC3B7F,OAAO3G,WAAWyM,YAAY,KAAK9F,MAAxC;;SAEK;;ACzBM,SAAS+F,UAAUnN,SAAS;MACnCG,gBAAgBH,QAAQG;SACvBA,gBAAgBA,cAAcC,cAAc7B;;ACJrD,SAAS6O,sBAAsBhH,cAAciH,OAAOC,UAAUC,eAAe;MACrEC,SAASpH,aAAa5F,aAAa;MACnCiN,SAASD,SAASpH,aAAajG,cAAcC,cAAcgG;SAC1DsH,iBAAiBL,OAAOC,UAAU,EAAEK,SAAS,KAAX,CAAzC;MAEI,CAACH,QAAQ;0BAET7M,gBAAgB8M,OAAOhN,UAAvB,GACA4M,OACAC,UACAC,aAJF;;gBAOYK,KAAKH,MAAnB;;AASa,SAASI,oBACtB3M,WACAuK,SACA3C,OACAgF,aACA;QAEMA,cAAcA;YACV5M,SAAV,EAAqBwM,iBAAiB,UAAU5E,MAAMgF,aAAa,EAAEH,SAAS,KAAX,CAAnE;MAGMI,gBAAgBpN,gBAAgBO,SAAhB;wBAEpB6M,eACA,UACAjF,MAAMgF,aACNhF,MAAMyE,aAJR;QAMMQ,gBAAgBA;QAChBC,gBAAgB;SAEflF;;ACtCM,SAASmF,uBAAuB;MACzC,CAAC,KAAKnF,MAAMkF,eAAe;SACxBlF,QAAQ+E,oBACX,KAAK3M,WACL,KAAKuK,SACL,KAAK3C,OACL,KAAKoF,cAJM;;;ACFF,SAASC,qBAAqBjN,WAAW4H,OAAO;YAEnD5H,SAAV,EAAqBkN,oBAAoB,UAAUtF,MAAMgF,WAAzD;QAGMP,cAAcnC,QAAQ,SAAA,QAAU;WAC7BgD,oBAAoB,UAAUtF,MAAMgF,WAA3C;GADF;QAKMA,cAAc;QACdP,gBAAgB,CAAA;QAChBQ,gBAAgB;QAChBC,gBAAgB;SACflF;;ACbM,SAASkE,wBAAwB;MAC1C,KAAKlE,MAAMkF,eAAe;yBACP,KAAKE,cAA1B;SACKpF,QAAQqF,qBAAqB,KAAKjN,WAAW,KAAK4H,KAA1C;;;ACLF,SAASuF,UAAUC,GAAG;SAC5BA,MAAM,MAAM,CAACC,MAAMhK,WAAW+J,CAAX,CAAN,KAAwBE,SAASF,CAAT;;ACE/B,SAASG,UAAUzO,SAASmE,QAAQ;SAC1C8D,KAAK9D,MAAZ,EAAoBiH,QAAQ,SAAA,MAAQ;QAC9BsD,OAAO;QAGT,CAAC,SAAS,UAAU,OAAO,SAAS,UAAU,MAA9C,EAAsD1P,QAAQwL,IAA9D,MACE,MACF6D,UAAUlK,OAAOqG,IAAP,CAAV,GACA;aACO;;YAEDoC,MAAMpC,IAAd,IAAsBrG,OAAOqG,IAAP,IAAekE;GAVvC;;ACHa,SAASC,cAAc3O,SAAS4O,YAAY;SAClD3G,KAAK2G,UAAZ,EAAwBxD,QAAQ,SAASZ,MAAM;QACvCC,QAAQmE,WAAWpE,IAAX;QACVC,UAAU,OAAO;cACXoE,aAAarE,MAAMoE,WAAWpE,IAAX,CAA3B;WACK;cACGsC,gBAAgBtC,IAAxB;;GALJ;;ACKa,SAASsE,WAAW/D,MAAM;YAK7BA,KAAKgE,SAAS3H,QAAQ2D,KAAK5G,MAArC;gBAIc4G,KAAKgE,SAAS3H,QAAQ2D,KAAK6D,UAAzC;MAGI7D,KAAKiE,gBAAgBhH,OAAOC,KAAK8C,KAAKkE,WAAjB,EAA8BpQ,QAAQ;cACnDkM,KAAKiE,cAAcjE,KAAKkE,WAAlC;;SAGKlE;;AAaF,SAASmE,iBACdhO,WACAkG,QACAqE,SACA0D,iBACArG,OACA;MAEMY,mBAAmBb,oBAAoBC,OAAO1B,QAAQlG,WAAWuK,QAAQC,aAAtD;MAKnB9D,YAAYD,qBAChB8D,QAAQ7D,WACR8B,kBACAtC,QACAlG,WACAuK,QAAQX,UAAUa,KAAKrE,mBACvBmE,QAAQX,UAAUa,KAAKtE,OANP;SASXwH,aAAa,eAAejH,SAAnC;YAIUR,QAAQ,EAAEyE,UAAUJ,QAAQC,gBAAgB,UAAU,WAA9C,CAAlB;SAEOD;;ACpDM,SAAS2D,kBAAkBrE,MAAMsE,aAAa;sBAC7BtE,KAAKhG,SAA3BqC,SADmD,cACnDA,QAAQlG,YAD2C,cAC3CA;MACRoO,QAAiB5K,KAAjB4K,OAAOC,QAAU7K,KAAV6K;MACTC,UAAU,SAAVA,SAAU,GAAA;WAAKC;;MAEfC,iBAAiBJ,MAAMpO,UAAU8D,KAAhB;MACjB2K,cAAcL,MAAMlI,OAAOpC,KAAb;MAEd4K,aAAa,CAAC,QAAQ,OAAT,EAAkB5Q,QAAQ+L,KAAKnD,SAA/B,MAA8C;MAC3DiI,cAAc9E,KAAKnD,UAAU5I,QAAQ,GAAvB,MAAgC;MAC9C8Q,kBAAkBJ,iBAAiB,MAAMC,cAAc;MACvDI,eAAeL,iBAAiB,MAAM,KAAKC,cAAc,MAAM;MAE/DK,sBAAsB,CAACX,cACzBG,UACAI,cAAcC,eAAeC,kBAC7BR,QACAC;MACEU,oBAAoB,CAACZ,cAAcG,UAAUF;SAE5C;UACCU,oBACJD,gBAAgB,CAACF,eAAeR,cAC5BjI,OAAOpD,OAAO,IACdoD,OAAOpD,IAHP;SAKDiM,kBAAkB7I,OAAOtD,GAAzB;YACGmM,kBAAkB7I,OAAOrD,MAAzB;WACDiM,oBAAoB5I,OAAOnD,KAA3B;;;ACxCX,IAAMiM,YAAYpR,aAAa,WAAWkC,KAAKvC,UAAUM,SAA1B;AAShB,SAASoR,aAAapF,MAAMU,SAAS;MAC1CxC,IAASwC,QAATxC,GAAGE,IAAMsC,QAANtC;MACH/B,SAAW2D,KAAKhG,QAAhBqC;MAGFgJ,8BAA8BlG,KAClCa,KAAKgE,SAASjE,WACd,SAAA,UAAA;WAAYjH,SAASuI,SAAS;GAFI,EAGlCiE;MACED,gCAAgClF,QAAW;YACrCG,KACN,+HADF;;MAIIgF,kBACJD,gCAAgClF,SAC5BkF,8BACA3E,QAAQ4E;MAERxO,eAAeH,gBAAgBqJ,KAAKgE,SAAS3H,MAA9B;MACfkJ,mBAAmBpL,sBAAsBrD,YAAtB;MAGnBsC,SAAS;cACHiD,OAAOyE;;MAGb9G,UAAUqK,kBACdrE,MACAxM,OAAOgS,mBAAmB,KAAK,CAACL,SAFlB;MAKV7L,QAAQ4E,MAAM,WAAW,QAAQ;MACjC3E,QAAQ6E,MAAM,UAAU,SAAS;MAKjCqH,mBAAmBnE,yBAAyB,WAAzB;MAWrBrI,OAAAA,QAAMF,MAAAA;MACNO,UAAU,UAAU;QAGlBxC,aAAarB,aAAa,QAAQ;YAC9B,CAACqB,aAAa0D,eAAeR,QAAQhB;WACtC;YACC,CAACuM,iBAAiBrL,SAASF,QAAQhB;;SAEtC;UACCgB,QAAQjB;;MAEZQ,UAAU,SAAS;QACjBzC,aAAarB,aAAa,QAAQ;aAC7B,CAACqB,aAAayD,cAAcP,QAAQd;WACtC;aACE,CAACqM,iBAAiBtL,QAAQD,QAAQd;;SAEtC;WACEc,QAAQf;;MAEbqM,mBAAmBG,kBAAkB;WAChCA,gBAAP,IAAA,iBAA0CxM,OAA1C,SAAqDF,MAArD;WACOO,KAAP,IAAgB;WACTC,KAAP,IAAgB;WACTyI,aAAa;SACf;QAEC0D,YAAYpM,UAAU,WAAW,KAAK;QACtCqM,aAAapM,UAAU,UAAU,KAAK;WACrCD,KAAP,IAAgBP,MAAM2M;WACfnM,KAAP,IAAgBN,OAAO0M;WAChB3D,aAAgB1I,QAAvB,OAAiCC;;MAI7BsK,aAAa;mBACF7D,KAAKnD;;OAIjBgH,aAAL,SAAA,CAAA,GAAuBA,YAAe7D,KAAK6D,UAA3C;OACKzK,SAAL,SAAA,CAAA,GAAmBA,QAAW4G,KAAK5G,MAAnC;OACK8K,cAAL,SAAA,CAAA,GAAwBlE,KAAKhG,QAAQ4L,OAAU5F,KAAKkE,WAApD;SAEOlE;;AClGM,SAAS6F,mBACtB9F,YACA+F,gBACAC,eACA;MACMC,aAAa7G,KAAKY,YAAW,SAAA,MAAA;QAAGsB,OAAH,KAAGA;WAAWA,SAASyE;GAAvC;MAEbG,aACJ,CAAC,CAACD,cACFjG,WAAUqB,KAAK,SAAA,UAAY;WAEvBtI,SAASuI,SAAS0E,iBAClBjN,SAASyH,WACTzH,SAASvB,QAAQyO,WAAWzO;GAJhC;MAQE,CAAC0O,YAAY;QACTD,cAAAA,MAAkBF,iBAAlB;QACAI,YAAAA,MAAiBH,gBAAjB;YACEzF,KACH4F,YADL,8BAC0CF,cAD1C,8DACgHA,cADhH,GAAA;;SAIKC;;ACxBM,SAASL,MAAM5F,MAAMU,SAAS;;MAEvC,CAACmF,mBAAmB7F,KAAKgE,SAASjE,WAAW,SAAS,cAArD,GAAsE;WAClEC;;MAGLiE,eAAevD,QAAQzL;MAGvB,OAAOgP,iBAAiB,UAAU;mBACrBjE,KAAKgE,SAAS3H,OAAO8J,cAAclC,YAAnC;QAGX,CAACA,cAAc;aACVjE;;SAEJ;QAGD,CAACA,KAAKgE,SAAS3H,OAAOnE,SAAS+L,YAA9B,GAA6C;cACxC3D,KACN,+DADF;aAGON;;;MAILnD,YAAYmD,KAAKnD,UAAUgB,MAAM,GAArB,EAA0B,CAA1B;sBACYmC,KAAKhG,SAA3BqC,SA5BmC,cA4BnCA,QAAQlG,YA5B2B,cA4B3BA;MACV0O,aAAa,CAAC,QAAQ,OAAT,EAAkB5Q,QAAQ4I,SAA1B,MAAyC;MAEtDuJ,MAAMvB,aAAa,WAAW;MAC9BwB,kBAAkBxB,aAAa,QAAQ;MACvCxM,OAAOgO,gBAAgBC,YAAhB;MACPC,UAAU1B,aAAa,SAAS;MAChC2B,SAAS3B,aAAa,WAAW;MACjC4B,mBAAmBxI,cAAcgG,YAAd,EAA4BmC,GAA5B;MAQrBjQ,UAAUqQ,MAAV,IAAoBC,mBAAmBpK,OAAOhE,IAAP,GAAc;SAClD2B,QAAQqC,OAAOhE,IAApB,KACEgE,OAAOhE,IAAP,KAAgBlC,UAAUqQ,MAAV,IAAoBC;;MAGpCtQ,UAAUkC,IAAV,IAAkBoO,mBAAmBpK,OAAOmK,MAAP,GAAgB;SAClDxM,QAAQqC,OAAOhE,IAApB,KACElC,UAAUkC,IAAV,IAAkBoO,mBAAmBpK,OAAOmK,MAAP;;OAEpCxM,QAAQqC,SAAStC,cAAciG,KAAKhG,QAAQqC,MAA3B;MAGhBqK,SAASvQ,UAAUkC,IAAV,IAAkBlC,UAAUiQ,GAAV,IAAiB,IAAIK,mBAAmB;MAInEnR,MAAMN,yBAAyBgL,KAAKgE,SAAS3H,MAAvC;MACNsK,mBAAmBnN,WAAWlE,IAAAA,WAAa+Q,eAAb,CAAX;MACnBO,mBAAmBpN,WAAWlE,IAAAA,WAAa+Q,kBAAb,OAAA,CAAX;MACrBQ,YACFH,SAAS1G,KAAKhG,QAAQqC,OAAOhE,IAApB,IAA4BsO,mBAAmBC;cAG9CjN,KAAKC,IAAID,KAAKmN,IAAIzK,OAAO+J,GAAP,IAAcK,kBAAkBI,SAAzC,GAAqD,CAA9D;OAEP5C,eAAeA;OACfjK,QAAQ4L,SAAb,sBAAA,CAAA,GAAA,eAAA,qBACGvN,MAAOsB,KAAK4K,MAAMsC,SAAX,CADV,GAAA,eAAA,qBAEGN,SAAU,EAFb,GAAA;SAKOvG;;AChFM,SAAS+G,qBAAqBnJ,WAAW;MAClDA,cAAc,OAAO;WAChB;aACEA,cAAc,SAAS;WACzB;;SAEFA;;ACkBT,IAAA,aAAe,CACb,cACA,QACA,YACA,aACA,OACA,WACA,eACA,SACA,aACA,cACA,UACA,gBACA,YACA,QACA,YAfa;AC5Bf,IAAMoJ,kBAAkBC,WAAW7G,MAAM,CAAjB;AAYT,SAAS8G,UAAUrK,WAA4B;MAAjBsK,UAAiB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAP;MAC/CC,QAAQJ,gBAAgB/S,QAAQ4I,SAAxB;MACRuC,MAAM4H,gBACT5G,MAAMgH,QAAQ,CADL,EAETC,OAAOL,gBAAgB5G,MAAM,GAAGgH,KAAzB,CAFE;SAGLD,UAAU/H,IAAIkI,QAAJ,IAAgBlI;;ACZnC,IAAMmI,YAAY;QACV;aACK;oBACO;;AAUL,SAAS3G,KAAKZ,MAAMU,SAAS;MAEtCQ,kBAAkBlB,KAAKgE,SAASjE,WAAW,OAA3C,GAAqD;WAChDC;;MAGLA,KAAKwH,WAAWxH,KAAKnD,cAAcmD,KAAKa,mBAAmB;WAEtDb;;MAGHxD,aAAaJ,cACjB4D,KAAKgE,SAAS3H,QACd2D,KAAKgE,SAAS7N,WACduK,QAAQpE,SACRoE,QAAQnE,mBACRyD,KAAKW,aALY;MAQf9D,YAAYmD,KAAKnD,UAAUgB,MAAM,GAArB,EAA0B,CAA1B;MACZ4J,oBAAoBnJ,qBAAqBzB,SAArB;MACpBe,YAAYoC,KAAKnD,UAAUgB,MAAM,GAArB,EAA0B,CAA1B,KAAgC;MAE5C6J,YAAY,CAAA;UAERhH,QAAQiH,UAAhB;SACOJ,UAAUK;kBACD,CAAC/K,WAAW4K,iBAAZ;;SAETF,UAAUM;kBACDX,UAAUrK,SAAV;;SAET0K,UAAUO;kBACDZ,UAAUrK,WAAW,IAArB;;;kBAGA6D,QAAQiH;;YAGdtH,QAAQ,SAAC0H,MAAMX,OAAU;QAC7BvK,cAAckL,QAAQL,UAAU5T,WAAWsT,QAAQ,GAAG;aACjDpH;;gBAGGA,KAAKnD,UAAUgB,MAAM,GAArB,EAA0B,CAA1B;wBACQS,qBAAqBzB,SAArB;QAEdgC,gBAAgBmB,KAAKhG,QAAQqC;QAC7B2L,aAAahI,KAAKhG,QAAQ7D;QAG1BqO,QAAQ7K,KAAK6K;QACbyD,cACHpL,cAAc,UACb2H,MAAM3F,cAAc3F,KAApB,IAA6BsL,MAAMwD,WAAW/O,IAAjB,KAC9B4D,cAAc,WACb2H,MAAM3F,cAAc5F,IAApB,IAA4BuL,MAAMwD,WAAW9O,KAAjB,KAC7B2D,cAAc,SACb2H,MAAM3F,cAAc7F,MAApB,IAA8BwL,MAAMwD,WAAWjP,GAAjB,KAC/B8D,cAAc,YACb2H,MAAM3F,cAAc9F,GAApB,IAA2ByL,MAAMwD,WAAWhP,MAAjB;QAEzBkP,gBAAgB1D,MAAM3F,cAAc5F,IAApB,IAA4BuL,MAAMhI,WAAWvD,IAAjB;QAC5CkP,iBAAiB3D,MAAM3F,cAAc3F,KAApB,IAA6BsL,MAAMhI,WAAWtD,KAAjB;QAC9CkP,eAAe5D,MAAM3F,cAAc9F,GAApB,IAA2ByL,MAAMhI,WAAWzD,GAAjB;QAC1CsP,kBACJ7D,MAAM3F,cAAc7F,MAApB,IAA8BwL,MAAMhI,WAAWxD,MAAjB;QAE1BsP,sBACHzL,cAAc,UAAUqL,iBACxBrL,cAAc,WAAWsL,kBACzBtL,cAAc,SAASuL,gBACvBvL,cAAc,YAAYwL;QAGvBxD,aAAa,CAAC,OAAO,QAAR,EAAkB5Q,QAAQ4I,SAA1B,MAAyC;QAGtD0L,wBACJ,CAAC,CAAC7H,QAAQ8H,mBACR3D,cAAcjH,cAAc,WAAWsK,iBACtCrD,cAAcjH,cAAc,SAASuK,kBACrC,CAACtD,cAAcjH,cAAc,WAAWwK,gBACxC,CAACvD,cAAcjH,cAAc,SAASyK;QAGrCI,4BACJ,CAAC,CAAC/H,QAAQgI,4BACR7D,cAAcjH,cAAc,WAAWuK,kBACtCtD,cAAcjH,cAAc,SAASsK,iBACrC,CAACrD,cAAcjH,cAAc,WAAWyK,mBACxC,CAACxD,cAAcjH,cAAc,SAASwK;QAErCO,mBAAmBJ,yBAAyBE;QAE9CR,eAAeK,uBAAuBK,kBAAkB;WAErDnB,UAAU;UAEXS,eAAeK,qBAAqB;oBAC1BZ,UAAUN,QAAQ,CAAlB;;UAGVuB,kBAAkB;oBACR5B,qBAAqBnJ,SAArB;;WAGTf,YAAYA,aAAae,YAAY,MAAMA,YAAY;WAIvD5D,QAAQqC,SAAb,SAAA,CAAA,GACK2D,KAAKhG,QAAQqC,QACbqC,iBACDsB,KAAKgE,SAAS3H,QACd2D,KAAKhG,QAAQ7D,WACb6J,KAAKnD,SAHJ,CAFL;aASOiD,aAAaE,KAAKgE,SAASjE,WAAWC,MAAM,MAA5C;;GAjFX;SAoFOA;;ACzIM,SAAS4I,aAAa5I,MAAM;sBACXA,KAAKhG,SAA3BqC,SADiC,cACjCA,QAAQlG,YADyB,cACzBA;MACV0G,YAAYmD,KAAKnD,UAAUgB,MAAM,GAArB,EAA0B,CAA1B;MACZ2G,QAAQ7K,KAAK6K;MACbK,aAAa,CAAC,OAAO,QAAR,EAAkB5Q,QAAQ4I,SAA1B,MAAyC;MACtDxE,OAAOwM,aAAa,UAAU;MAC9B2B,SAAS3B,aAAa,SAAS;MAC/B5F,cAAc4F,aAAa,UAAU;MAEvCxI,OAAOhE,IAAP,IAAemM,MAAMrO,UAAUqQ,MAAV,CAAN,GAA0B;SACtCxM,QAAQqC,OAAOmK,MAApB,IACEhC,MAAMrO,UAAUqQ,MAAV,CAAN,IAA2BnK,OAAO4C,WAAP;;MAE3B5C,OAAOmK,MAAP,IAAiBhC,MAAMrO,UAAUkC,IAAV,CAAN,GAAwB;SACtC2B,QAAQqC,OAAOmK,MAApB,IAA8BhC,MAAMrO,UAAUkC,IAAV,CAAN;;SAGzB2H;;ACRF,SAAS6I,QAAQC,KAAK7J,aAAaJ,eAAeF,kBAAkB;MAEnEd,QAAQiL,IAAIlJ,MAAM,2BAAV;MACRF,QAAQ,CAAC7B,MAAM,CAAN;MACT8F,OAAO9F,MAAM,CAAN;MAGT,CAAC6B,OAAO;WACHoJ;;MAGLnF,KAAK1P,QAAQ,GAAb,MAAsB,GAAG;QACvBgB,UAAAA;YACI0O,MAAR;WACO;kBACO9E;;WAEP;WACA;;kBAEOF;;QAGRjG,OAAOqB,cAAc9E,OAAd;WACNyD,KAAKuG,WAAL,IAAoB,MAAMS;aACxBiE,SAAS,QAAQA,SAAS,MAAM;QAErCoF,OAAAA;QACApF,SAAS,MAAM;aACVhK,KAAKC,IACVnG,SAASmD,gBAAgB4D,cACzBhH,OAAOsI,eAAe,CAFjB;WAIF;aACEnC,KAAKC,IACVnG,SAASmD,gBAAgB2D,aACzB/G,OAAOqI,cAAc,CAFhB;;WAKFkN,OAAO,MAAMrJ;SACf;WAGEA;;;AAeJ,SAASsJ,YACdjN,SACA8C,eACAF,kBACAsK,eACA;MACMjP,UAAU,CAAC,GAAG,CAAJ;MAKVkP,YAAY,CAAC,SAAS,MAAV,EAAkBjV,QAAQgV,aAA1B,MAA6C;MAIzDE,YAAYpN,QAAO8B,MAAM,SAAb,EAAwBV,IAAI,SAAA,MAAA;WAAQiM,KAAKC,KAAL;GAApC;MAIZC,UAAUH,UAAUlV,QACxBkL,KAAKgK,WAAW,SAAA,MAAA;WAAQC,KAAKG,OAAO,MAAZ,MAAwB;GAAhD,CADc;MAIZJ,UAAUG,OAAV,KAAsBH,UAAUG,OAAV,EAAmBrV,QAAQ,GAA3B,MAAoC,IAAI;YACxDqM,KACN,8EADF;;MAOIkJ,aAAa;MACfC,MAAMH,YAAY,KAClB,CACEH,UACG/I,MAAM,GAAGkJ,OADZ,EAEGjC,OAAO,CAAC8B,UAAUG,OAAV,EAAmBzL,MAAM2L,UAAzB,EAAqC,CAArC,CAAD,CAFV,GAGA,CAACL,UAAUG,OAAV,EAAmBzL,MAAM2L,UAAzB,EAAqC,CAArC,CAAD,EAA0CnC,OACxC8B,UAAU/I,MAAMkJ,UAAU,CAA1B,CADF,CAJF,IAQA,CAACH,SAAD;QAGEM,IAAItM,IAAI,SAACuM,IAAItC,OAAU;QAErBnI,eAAemI,UAAU,IAAI,CAAC8B,YAAYA,aAC5C,WACA;QACAS,oBAAoB;WAEtBD,GAGGE,OAAO,SAACtM,GAAGC,GAAM;UACZD,EAAEA,EAAExJ,SAAS,CAAb,MAAoB,MAAM,CAAC,KAAK,GAAN,EAAWG,QAAQsJ,CAAnB,MAA0B,IAAI;UACxDD,EAAExJ,SAAS,CAAb,IAAkByJ;4BACE;eACbD;iBACEqM,mBAAmB;UAC1BrM,EAAExJ,SAAS,CAAb,KAAmByJ;4BACC;eACbD;aACF;eACEA,EAAE+J,OAAO9J,CAAT;;OAER,CAAA,CAfL,EAiBGJ,IAAI,SAAA,KAAA;aAAO0L,QAAQC,KAAK7J,aAAaJ,eAAeF,gBAAzC;KAjBd;GAPE;MA6BF0B,QAAQ,SAACqJ,IAAItC,OAAU;OACtB/G,QAAQ,SAAC+I,MAAMS,QAAW;UACvBvG,UAAU8F,IAAV,GAAiB;gBACXhC,KAAR,KAAkBgC,QAAQM,GAAGG,SAAS,CAAZ,MAAmB,MAAM,KAAK;;KAF5D;GADF;SAOO7P;;AAYM,SAAS+B,OAAOiE,MAAhB,MAAkC;MAAVjE,UAAU,KAAVA;MAC7Bc,YAA8CmD,KAA9CnD,2BAA8CmD,KAAnChG,SAAWqC,SADiB,cACjBA,QAAQlG,YADS,cACTA;MAChC8S,gBAAgBpM,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;MAElB7D,UAAAA;MACAsJ,UAAU,CAACvH,OAAX,GAAoB;cACZ,CAAC,CAACA,SAAQ,CAAV;SACL;cACKiN,YAAYjN,SAAQM,QAAQlG,WAAW8S,aAAvC;;MAGRA,kBAAkB,QAAQ;WACrBlQ,OAAOiB,QAAQ,CAAR;WACPf,QAAQe,QAAQ,CAAR;aACNiP,kBAAkB,SAAS;WAC7BlQ,OAAOiB,QAAQ,CAAR;WACPf,QAAQe,QAAQ,CAAR;aACNiP,kBAAkB,OAAO;WAC3BhQ,QAAQe,QAAQ,CAAR;WACRjB,OAAOiB,QAAQ,CAAR;aACLiP,kBAAkB,UAAU;WAC9BhQ,QAAQe,QAAQ,CAAR;WACRjB,OAAOiB,QAAQ,CAAR;;OAGXqC,SAASA;SACP2D;;ACrLM,SAAS8J,gBAAgB9J,MAAMU,SAAS;MACjDnE,oBACFmE,QAAQnE,qBAAqB5F,gBAAgBqJ,KAAKgE,SAAS3H,MAA9B;MAK3B2D,KAAKgE,SAAS7N,cAAcoG,mBAAmB;wBAC7B5F,gBAAgB4F,iBAAhB;;MAMhBwN,gBAAgBzI,yBAAyB,WAAzB;MAChB0I,eAAehK,KAAKgE,SAAS3H,OAAOwF;MAClC9I,MAA0CiR,aAA1CjR,KAAKE,OAAqC+Q,aAArC/Q,MAAuBgR,YAAcD,aAA9BD,aAhBiC;eAiBxChR,MAAM;eACNE,OAAO;eACP8Q,aAAb,IAA8B;MAExBvN,aAAaJ,cACjB4D,KAAKgE,SAAS3H,QACd2D,KAAKgE,SAAS7N,WACduK,QAAQpE,SACRC,mBACAyD,KAAKW,aALY;eAUN5H,MAAMA;eACNE,OAAOA;eACP8Q,aAAb,IAA8BE;UAEtBzN,aAAaA;MAEfjF,QAAQmJ,QAAQwJ;MAClB7N,SAAS2D,KAAKhG,QAAQqC;MAEpBgD,QAAQ;aAAA,SAAA,QACJxC,WAAW;UACb6C,QAAQrD,OAAOQ,SAAP;UAEVR,OAAOQ,SAAP,IAAoBL,WAAWK,SAAX,KACpB,CAAC6D,QAAQyJ,qBACT;gBACQxQ,KAAKC,IAAIyC,OAAOQ,SAAP,GAAmBL,WAAWK,SAAX,CAA5B;;gCAEAA,WAAY6C,KAAtB;;eATU,SAAA,UAWF7C,WAAW;UACbkC,WAAWlC,cAAc,UAAU,SAAS;UAC9C6C,QAAQrD,OAAO0C,QAAP;UAEV1C,OAAOQ,SAAP,IAAoBL,WAAWK,SAAX,KACpB,CAAC6D,QAAQyJ,qBACT;gBACQxQ,KAAKmN,IACXzK,OAAO0C,QAAP,GACAvC,WAAWK,SAAX,KACGA,cAAc,UAAUR,OAAOpC,QAAQoC,OAAOnC,OAH3C;;gCAMA6E,UAAWW,KAArB;;;QAIEW,QAAQ,SAAA,WAAa;QACnBhI,OACJ,CAAC,QAAQ,KAAT,EAAgBpE,QAAQ4I,SAAxB,MAAuC,KAAK,YAAY;0BAC5CR,QAAWgD,MAAMhH,IAAN,EAAYwE,SAAZ,CAAzB;GAHF;OAMK7C,QAAQqC,SAASA;SAEf2D;;AChFM,SAASoK,MAAMpK,MAAM;MAC5BnD,YAAYmD,KAAKnD;MACjBoM,gBAAgBpM,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;MAChBwM,iBAAiBxN,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;MAGnBwM,gBAAgB;wBACYrK,KAAKhG,SAA3B7D,YADU,cACVA,WAAWkG,SADD,cACCA;QACbwI,aAAa,CAAC,UAAU,KAAX,EAAkB5Q,QAAQgV,aAA1B,MAA6C;QAC1D5Q,OAAOwM,aAAa,SAAS;QAC7B5F,cAAc4F,aAAa,UAAU;QAErCyF,eAAe;gCACTjS,MAAOlC,UAAUkC,IAAV,CAAjB;8BAEGA,MAAOlC,UAAUkC,IAAV,IAAkBlC,UAAU8I,WAAV,IAAyB5C,OAAO4C,WAAP,CADrD;;SAKGjF,QAAQqC,SAAb,SAAA,CAAA,GAA2BA,QAAWiO,aAAaD,cAAb,CAAtC;;SAGKrK;;ACnBM,SAASuK,KAAKvK,MAAM;MAC7B,CAAC6F,mBAAmB7F,KAAKgE,SAASjE,WAAW,QAAQ,iBAApD,GAAwE;WACpEC;;MAGHlD,UAAUkD,KAAKhG,QAAQ7D;MACvBqU,QAAQrL,KACZa,KAAKgE,SAASjE,WACd,SAAA,UAAA;WAAYjH,SAASuI,SAAS;GAFlB,EAGZ7E;MAGAM,QAAQ9D,SAASwR,MAAMzR,OACvB+D,QAAQ7D,OAAOuR,MAAMtR,SACrB4D,QAAQ/D,MAAMyR,MAAMxR,UACpB8D,QAAQ5D,QAAQsR,MAAMvR,MACtB;QAEI+G,KAAKuK,SAAS,MAAM;aACfvK;;SAGJuK,OAAO;SACP1G,WAAW,qBAAhB,IAAyC;SACpC;QAED7D,KAAKuK,SAAS,OAAO;aAChBvK;;SAGJuK,OAAO;SACP1G,WAAW,qBAAhB,IAAyC;;SAGpC7D;;AClCM,SAASyK,MAAMzK,MAAM;MAC5BnD,YAAYmD,KAAKnD;MACjBoM,gBAAgBpM,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;sBACQmC,KAAKhG,SAA3BqC,SAH0B,cAG1BA,QAAQlG,YAHkB,cAGlBA;MACV2I,UAAU,CAAC,QAAQ,OAAT,EAAkB7K,QAAQgV,aAA1B,MAA6C;MAEvDyB,iBAAiB,CAAC,OAAO,MAAR,EAAgBzW,QAAQgV,aAAxB,MAA2C;SAE3DnK,UAAU,SAAS,KAA1B,IACE3I,UAAU8S,aAAV,KACCyB,iBAAiBrO,OAAOyC,UAAU,UAAU,QAA3B,IAAuC;OAEtDjC,YAAYyB,qBAAqBzB,SAArB;OACZ7C,QAAQqC,SAAStC,cAAcsC,MAAd;SAEf2D;;ACOT,IAAA,YAAe;;;;;;;;;SASN;;WAEE;;aAEE;;QAELoK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAyCE;;WAEC;;aAEE;;QAELrO;;;;YAII;;;;;;;;;;;;;;;;;;;mBAoBO;;WAER;;aAEE;;QAEL+N;;;;;;cAMM,CAAC,QAAQ,SAAS,OAAO,QAAzB;;;;;;;aAOD;;;;;;uBAMU;;;;;;;;;;;gBAYP;;WAEL;;aAEE;;QAELlB;;;;;;;;;;;;SAaC;;WAEE;;aAEE;;QAELhD;;aAEK;;;;;;;;;;;;;QAcL;;WAEG;;aAEE;;QAELhF;;;;;;;cAOM;;;;;aAKD;;;;;;;uBAOU;;;;;;;;oBAQH;;;;;;;;6BAQS;;;;;;;;;SAUpB;;WAEE;;aAEE;;QAEL6J;;;;;;;;;;;;QAaA;;WAEG;;aAEE;;QAELF;;;;;;;;;;;;;;;;;gBAkBQ;;WAEL;;aAEE;;QAELnF;;;;;;qBAMa;;;;;;OAMd;;;;;;OAMA;;;;;;;;;;;;;;;;;cAkBO;;WAEH;;aAEE;;QAELrB;;YAEII;;;;;;;qBAOShE;;;ACzUrB,IAAA,WAAe;;;;;aAKF;;;;;iBAMI;;;;;iBAMA;;;;;;mBAOE;;;;;;;YAQP,SAAA,WAAM;EAAA;;;;;;;;;YAUN,SAAA,WAAM;EAAA;;;;;;;;ACvDlB,IAOqBwK,SAAAA,WAAAA;mBASPxU,WAAWkG,QAAsB;;QAAdqE,UAAc,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAJ,CAAA;;SAyFzCyC,iBAAiB,WAAA;aAAMyH,sBAAsB,MAAKpK,MAA3B;;SAvFhBA,SAASqK,SAAS,KAAKrK,OAAOsK,KAAK,IAAjB,CAAT;SAGTpK,UAAL,SAAA,CAAA,GAAoBiK,QAAOI,UAAarK,OAAxC;SAGK3C,QAAQ;mBACE;iBACF;qBACI,CAAA;;SAIZ5H,YAAYA,aAAaA,UAAU6U,SAAS7U,UAAU,CAAV,IAAeA;SAC3DkG,SAASA,UAAUA,OAAO2O,SAAS3O,OAAO,CAAP,IAAYA;SAG/CqE,QAAQX,YAAY,CAAA;WAClB7C,KAAP,SAAA,CAAA,GACKyN,QAAOI,SAAShL,WAChBW,QAAQX,SAFb,CAAA,EAGGM,QAAQ,SAAA,MAAQ;YACZK,QAAQX,UAAUsB,IAAvB,IAAA,SAAA,CAAA,GAEMsJ,QAAOI,SAAShL,UAAUsB,IAA1B,KAAmC,CAAA,GAEnCX,QAAQX,YAAYW,QAAQX,UAAUsB,IAAlB,IAA0B,CAAA,CAJpD;KAJF;SAaKtB,YAAY9C,OAAOC,KAAK,KAAKwD,QAAQX,SAAzB,EACd5C,IAAI,SAAA,MAAA;;;SAEA,MAAKuD,QAAQX,UAAUsB,IAAvB,CAFA;KADU,EAMdhE,KAAK,SAACC,GAAGC,GAAJ;aAAUD,EAAE/F,QAAQgG,EAAEhG;KANb;SAYZwI,UAAUM,QAAQ,SAAA,iBAAmB;UACpC+D,gBAAgB7D,WAAW5L,WAAWyP,gBAAgB6G,MAA3B,GAAoC;wBACjDA,OACd,MAAK9U,WACL,MAAKkG,QACL,MAAKqE,SACL0D,iBACA,MAAKrG,KALP;;KAFJ;SAaKyC,OAAL;QAEMyC,gBAAgB,KAAKvC,QAAQuC;QAC/BA,eAAe;WAEZC,qBAAL;;SAGGnF,MAAMkF,gBAAgBA;;;;gCAKpB;aACAzC,OAAOzL,KAAK,IAAZ;;;;iCAEC;aACD+M,QAAQ/M,KAAK,IAAb;;;;8CAEc;aACdmO,qBAAqBnO,KAAK,IAA1B;;;;+CAEe;aACfkN,sBAAsBlN,KAAK,IAA3B;;;;;;;;;;;;;;;;;;;;;;;;;;AA1FU4V,OAoHZO,SAAS,OAAO1X,WAAW,cAAcA,SAAS2X,QAAQC;AApH9CT,OAsHZ1D,aAAaA;AAtHD0D,OAwHZI,WAAWA;;", "names": ["isFunction", "require_shams", "require_implementation", "Empty", "undefined", "<PERSON><PERSON><PERSON>", "stringToPath", "getBaseIntrinsic", "applyBind", "hasPropertyDescriptors", "isFunction", "defineProperty", "require_implementation", "functionsHaveNames", "require_implementation", "require_polyfill", "require_shim", "defineProperty", "require_implementation", "Provider", "Consumer", "window", "document", "navigator", "timeoutDuration", "longerTimeoutBrowsers", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "userAgent", "indexOf", "microtaskDebounce", "fn", "called", "Promise", "resolve", "then", "taskDebounce", "scheduled", "supportsMicroTasks", "isFunction", "functionToCheck", "getType", "toString", "call", "getStyleComputedProperty", "element", "property", "nodeType", "ownerDocument", "defaultView", "css", "getComputedStyle", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "body", "overflow", "overflowX", "overflowY", "test", "getReferenceNode", "reference", "referenceNode", "isIE11", "MSInputMethodContext", "documentMode", "isIE10", "isIE", "version", "getOffsetParent", "documentElement", "noOffsetParent", "offsetParent", "nextElement<PERSON><PERSON>ling", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "side", "upperSide", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "parseFloat", "getSize", "computedStyle", "Math", "max", "parseInt", "getWindowSizes", "getClientRect", "offsets", "width", "height", "getBoundingClientRect", "e", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "fixedPosition", "runIsIE", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "excludeScroll", "relativeOffset", "innerWidth", "innerHeight", "offset", "isFixed", "getFixedPositionOffsetParent", "parentElement", "el", "getBoundaries", "popper", "padding", "boundariesElement", "boundaries", "boundariesNode", "isPaddingNumber", "getArea", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "Object", "keys", "map", "key", "sort", "a", "b", "area", "filtered<PERSON><PERSON>s", "filter", "computedPlacement", "variation", "split", "getReferenceOffsets", "state", "commonOffsetParent", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "find", "arr", "check", "Array", "prototype", "findIndex", "prop", "value", "cur", "match", "obj", "runModifiers", "modifiers", "data", "ends", "modifiersToRun", "undefined", "slice", "for<PERSON>ach", "warn", "enabled", "update", "isDestroyed", "options", "positionFixed", "flip", "originalPlacement", "position", "isCreated", "onCreate", "onUpdate", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "prefix", "to<PERSON><PERSON><PERSON>", "style", "destroy", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "getWindow", "attachToScrollParents", "event", "callback", "scrollParents", "isBody", "target", "addEventListener", "passive", "push", "setupEventListeners", "updateBound", "scrollElement", "eventsEnabled", "enableEventListeners", "scheduleUpdate", "removeEventListeners", "removeEventListener", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "setAttributes", "attributes", "setAttribute", "applyStyle", "instance", "arrowElement", "arrowStyles", "applyStyleOnLoad", "modifierOptions", "getRoundedOffsets", "shouldRound", "round", "floor", "noRound", "v", "referenceWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVertical", "isVariation", "sameWidthParity", "bothOddWidth", "horizontalToInteger", "verticalToInteger", "isFirefox", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "devicePixelRatio", "prefixedProperty", "invertTop", "invertLeft", "arrow", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "requested", "querySelector", "len", "sideCapitalized", "toLowerCase", "altSide", "opSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "min", "getOppositeVariation", "validPlacements", "placements", "clockwise", "counter", "index", "concat", "reverse", "BEHAVIORS", "flipped", "placementOpposite", "flipOrder", "behavior", "FLIP", "CLOCKWISE", "COUNTERCLOCKWISE", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariationByRef", "flipVariations", "flippedVariationByContent", "flipVariationsByContent", "flippedVariation", "keepTogether", "toValue", "str", "size", "parseOffset", "basePlacement", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "index2", "preventOverflow", "transformProp", "popperStyles", "transform", "priority", "escapeWithReference", "shift", "shiftvariation", "shiftOffsets", "hide", "bound", "inner", "subtractLength", "<PERSON><PERSON>", "requestAnimationFrame", "debounce", "bind", "De<PERSON>ults", "j<PERSON>y", "onLoad", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}