import {
  useAccordion
} from "./chunk-NT5RQ2AN.js";
import {
  require_lodash
} from "./chunk-DM3DLLZI.js";
import {
  react_merge_refs_esm_default
} from "./chunk-JLWORYXM.js";
import "./chunk-JQPVIOLG.js";
import {
  DEFAULT_THEME,
  getColor,
  getLineHeight,
  math,
  retrieveComponentStyles,
  useDocument
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import {
  composeEventHandlers
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  A<PERSON>,
  Me,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-accordions/dist/index.esm.js
var React = __toESM(require_react());
var import_react = __toESM(require_react());
var import_lodash = __toESM(require_lodash());
var import_prop_types = __toESM(require_prop_types());
function _extends$3() {
  _extends$3 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$3.apply(this, arguments);
}
var COMPONENT_ID$k = "accordions.step_content";
var sizeStyles$1 = (props) => {
  const {
    rtl,
    space
  } = props.theme;
  const paddingBottom = props.isActive ? space.base * 8 : space.base * 6;
  const paddingRight = rtl ? space.base * 6 : space.base * 5;
  const paddingLeft = rtl ? space.base * 5 : space.base * 6;
  const marginRight = rtl ? space.base * 3 : "0";
  const marginLeft = rtl ? "0" : space.base * 3;
  const marginVertical = space.base * 3;
  return Ae(["margin:", "px ", "px ", "px ", "px;padding:0 ", "px ", "px ", "px;"], marginVertical, marginRight, marginVertical, marginLeft, paddingRight, paddingBottom, paddingLeft);
};
var StyledContent = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$k,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledContent",
  componentId: "sc-mazvvo-0"
})(["", " min-width:", "px;word-break:break-word;", ";"], sizeStyles$1, (props) => props.theme.space.base * 30, (props) => retrieveComponentStyles(COMPONENT_ID$k, props));
StyledContent.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$j = "accordions.step_line";
var StyledLine = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$j,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledLine",
  componentId: "sc-1gkpjbr-0"
})(["display:block;position:absolute;top:", "px;right:", ";left:", ";flex:1;border-top:", ";border-color:", ";"], (props) => props.theme.space.base * 3, (props) => `calc(50% + ${props.theme.space.base * 6}px)`, (props) => `calc(-50% + ${props.theme.space.base * 6}px)`, (props) => props.theme.borders.sm, (props) => getColor("neutralHue", 300, props.theme));
StyledLine.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$i = "accordions.step";
var StyledStep = styled_components_browser_esm_default.li.attrs({
  "data-garden-id": COMPONENT_ID$i,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledStep",
  componentId: "sc-12fiwtz-0"
})(["position:", ";flex:", ";min-width:", ";&:last-of-type ", "{display:", ";}&:first-of-type ", "{display:", ";}&:not(:last-of-type) ", "{border-", ":", ";border-color:", ";}", ";"], (props) => props.isHorizontal && "relative", (props) => props.isHorizontal && "1", (props) => props.isHorizontal && `${props.theme.space.base * 15}px`, StyledLine, (props) => props.theme.rtl && "none", StyledLine, (props) => !props.theme.rtl && "none", StyledContent, (props) => props.theme.rtl ? "right" : "left", (props) => props.theme.borders.sm, (props) => getColor("neutralHue", 300, props.theme), (props) => retrieveComponentStyles(COMPONENT_ID$i, props));
StyledStep.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$h = "accordions.step_inner_content";
var StyledInnerContent = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$h,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledInnerContent",
  componentId: "sc-1xs9fh7-0"
})(["transition:max-height 0.25s ease-in-out;overflow:hidden;max-height:", ";line-height:", ";color:", ";font-size:", ";", ";"], (props) => !props.isActive && "0 !important", (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.theme.colors.foreground, (props) => props.theme.fontSizes.md, (props) => retrieveComponentStyles(COMPONENT_ID$h, props));
StyledInnerContent.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$g = "accordions.stepper";
var StyledStepper = styled_components_browser_esm_default.ol.attrs({
  "data-garden-id": COMPONENT_ID$g,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledStepper",
  componentId: "sc-dsxw0f-0"
})(["display:", ";margin:0;padding:0;list-style:none;", ";"], (props) => props.isHorizontal && "flex", (props) => retrieveComponentStyles(COMPONENT_ID$g, props));
StyledStepper.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$f = "accordions.step_icon";
var StyledIconFlexContainer = styled_components_browser_esm_default.div.withConfig({
  displayName: "StyledIcon__StyledIconFlexContainer",
  componentId: "sc-v20nz9-0"
})(["display:flex;flex-basis:100%;justify-content:center;width:100%;"]);
var sizeStyles = (props) => {
  const size = `${props.theme.space.base * 6}px`;
  const fontSize = props.theme.fontSizes.sm;
  return Ae(["margin-bottom:", ";margin-", ":", ";width:", ";min-width:", ";height:", ";min-height:", ";line-height:", ";font-size:", ";"], props.isHorizontal && `${props.theme.space.base * 2}px`, props.theme.rtl ? "left" : "right", !props.isHorizontal && `${props.theme.space.base * 3}px`, size, size, size, size, getLineHeight(size, fontSize), fontSize);
};
var colorStyles$2 = (props) => {
  return Ae(["background:", ";color:", ";"], props.isActive ? getColor("neutralHue", 600, props.theme) : getColor("neutralHue", 200, props.theme), props.isActive ? props.theme.colors.background : props.theme.colors.foreground);
};
var StyledIcon = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$f,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledIcon",
  componentId: "sc-v20nz9-1"
})(["display:flex;align-items:center;justify-content:center;transition:background 0.25s ease-in-out,color 0.25s ease-in-out;border-radius:100%;", " ", " ", ";"], sizeStyles, colorStyles$2, (props) => retrieveComponentStyles(COMPONENT_ID$f, props));
StyledIconFlexContainer.defaultProps = {
  theme: DEFAULT_THEME
};
StyledIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$e = "accordions.step_label";
var StyledLabel = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$e,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledLabel",
  componentId: "sc-1o82llj-0"
})(["display:", ";align-items:", ";transition:color 0.25s ease-in-out,font-weight 0.25s ease-in-out;text-align:", ";line-height:", ";color:", ";font-size:", ";font-weight:", ";", ";"], (props) => !props.isHorizontal && "flex", (props) => !props.isHorizontal && "center", (props) => props.isHorizontal && "center", (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.isActive ? props.theme.colors.foreground : getColor("neutralHue", 600, props.theme), (props) => props.theme.fontSizes.md, (props) => props.isActive && 600, (props) => retrieveComponentStyles(COMPONENT_ID$e, props));
StyledLabel.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$d = "accordions.step_label_text";
var StyledLabelText = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$d,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledLabelText",
  componentId: "sc-111m5zo-0"
})(["display:", ";padding:", ";word-wrap:", ";"], (props) => props.isHidden && "none", (props) => props.isHorizontal && `0 ${props.theme.space.base * 3}px`, (props) => props.isHorizontal && "break-word");
StyledLabelText.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$c = "accordions.accordion";
var StyledAccordion = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$c,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledAccordion",
  componentId: "sc-niv9ic-0"
})(["", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$c, props));
StyledAccordion.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$b = "accordions.panel";
var paddingStyles = (props) => {
  const {
    base
  } = props.theme.space;
  let paddingTop = base * 2;
  let paddingHorizontal = base * 5;
  let paddingBottom = base * 8;
  if (props.isCompact) {
    paddingTop = base * 2;
    paddingHorizontal = base * 3;
    paddingBottom = base * 4;
  }
  if (props.isExpanded === false) {
    paddingTop = 0;
    paddingBottom = 0;
  }
  return Ae(["transition:", ";padding:", "px ", "px ", "px;"], props.isAnimated && "padding 0.25s ease-in-out", paddingTop, paddingHorizontal, paddingBottom);
};
var StyledPanel = styled_components_browser_esm_default.section.attrs({
  "data-garden-id": COMPONENT_ID$b,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledPanel",
  componentId: "sc-1piryze-0"
})(["", ";border-bottom:", ";", ";"], paddingStyles, (props) => `${props.theme.borders.sm} ${props.isBare ? "transparent" : getColor("neutralHue", 300, props.theme)}`, (props) => retrieveComponentStyles(COMPONENT_ID$b, props));
StyledPanel.defaultProps = {
  isAnimated: true,
  theme: DEFAULT_THEME
};
var COMPONENT_ID$a = "accordions.section";
var StyledSection = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$a,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSection",
  componentId: "sc-v2t9bd-0"
})(["&:last-child ", "{border:none;}", ";"], StyledPanel, (props) => retrieveComponentStyles(COMPONENT_ID$a, props));
StyledSection.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$9 = "accordions.header";
var StyledHeader = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$9,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledHeader",
  componentId: "sc-2c6rbr-0"
})(["display:flex;align-items:center;box-shadow:", ";font-size:", ";&:hover{cursor:", ";}", ";"], (props) => props.isFocused && `${props.theme.shadows.md(getColor("primaryHue", 600, props.theme, 0.35))} inset`, (props) => props.theme.fontSizes.md, (props) => (props.isCollapsible || !props.isExpanded) && "pointer", (props) => retrieveComponentStyles(COMPONENT_ID$9, props));
StyledHeader.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$8 = "accordions.button";
var colorStyles$1 = (props) => {
  const showColor = props.isCollapsible || !props.isExpanded;
  let color = props.theme.colors.foreground;
  if (showColor && props.isHovered) {
    color = getColor("primaryHue", 600, props.theme);
  }
  return Ae(["color:", ";&:hover{cursor:", ";color:", ";}"], color, showColor && "pointer", showColor && color);
};
var StyledButton = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$8,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledButton",
  componentId: "sc-xj3hy7-0"
})(["transition:color 0.1s ease-in-out;outline:none;border:none;background:transparent;padding:", ";width:100%;text-align:", ";line-height:", ";font-family:inherit;font-size:", ";font-weight:", ";", " &::-moz-focus-inner{border:0;}&:hover{cursor:", ";}", ";"], (props) => props.isCompact ? `${props.theme.space.base * 2}px ${props.theme.space.base * 3}px` : `${props.theme.space.base * 5}px`, (props) => props.theme.rtl ? "right" : "left", (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.theme.fontSizes.md, (props) => props.theme.fontWeights.semibold, colorStyles$1, (props) => (props.isCollapsible || !props.isExpanded) && "pointer", (props) => retrieveComponentStyles(COMPONENT_ID$8, props));
StyledButton.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$7 = "accordions.step_inner_panel";
var StyledInnerPanel = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$7,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledInnerPanel",
  componentId: "sc-8nbueg-0"
})(["transition:", ";max-height:", ";overflow:hidden;line-height:", ";font-size:", ";", ";"], (props) => props.isAnimated && "max-height 0.25s ease-in-out", (props) => !props.isExpanded && "0 !important", (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.theme.fontSizes.md, (props) => retrieveComponentStyles(COMPONENT_ID$7, props));
StyledInnerPanel.defaultProps = {
  isAnimated: true,
  theme: DEFAULT_THEME
};
var COMPONENT_ID$6 = "accordions.rotate_icon";
var colorStyles = (props) => {
  const showColor = props.isCollapsible || !props.isRotated;
  let color = getColor("neutralHue", 600, props.theme);
  if (showColor && props.isHovered) {
    color = getColor("primaryHue", 600, props.theme);
  }
  return Ae(["color:", ";&:hover{color:", ";}"], color, showColor && color);
};
var StyledRotateIcon = styled_components_browser_esm_default(
  (_ref) => {
    let {
      children,
      isRotated,
      isHovered,
      isCompact,
      isCollapsible,
      ...props
    } = _ref;
    return (0, import_react.cloneElement)(import_react.Children.only(children), props);
  }
).attrs({
  "data-garden-id": COMPONENT_ID$6,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledRotateIcon",
  componentId: "sc-hp435q-0"
})(["transform:", ";transition:transform 0.25s ease-in-out,color 0.1s ease-in-out;box-sizing:content-box;padding:", ";width:", ";height:", ";vertical-align:middle;", " ", ";"], (props) => props.isRotated && `rotate(${props.theme.rtl ? "-" : "+"}180deg)`, (props) => props.isCompact ? `${props.theme.space.base * 1.5}px ${props.theme.space.base * 3}px` : `${props.theme.space.base * 5}px`, (props) => props.theme.iconSizes.md, (props) => props.theme.iconSizes.md, colorStyles, (props) => retrieveComponentStyles(COMPONENT_ID$6, props));
StyledRotateIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$5 = "timeline";
var StyledTimeline = styled_components_browser_esm_default.ol.attrs({
  "data-garden-id": COMPONENT_ID$5,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledTimeline",
  componentId: "sc-pig5kv-0"
})(["margin:0;padding:0;list-style:none;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledTimeline.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$4 = "timeline.content.separator";
var StyledSeparator = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$4,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledSeparator",
  componentId: "sc-fki51e-0"
})(["display:flex;position:relative;justify-content:center;padding:", ";&::after{position:absolute;border-left:", ";height:100%;content:'';}", ";"], (props) => `${props.theme.space.base * 5}px ${props.theme.space.base}px`, (props) => `${props.theme.borders.sm} ${getColor("neutralHue", 600, props.theme)}`, (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledSeparator.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "timeline.content";
var StyledTimelineContent = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$3,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledContent__StyledTimelineContent",
  componentId: "sc-19phgu1-0"
})(["flex:1;padding:", ";", ";"], (props) => `${props.theme.space.base * 5}px ${props.theme.space.base * 4}px`, (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledTimelineContent.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "timeline.opposite.content";
var StyledOppositeContent = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledOppositeContent",
  componentId: "sc-jurh2k-0"
})(["flex:1;padding:", ";text-align:", ";", ";"], (props) => `${props.theme.space.base * 5}px ${props.theme.space.base * 4}px`, (props) => props.theme.rtl ? "left" : "right", (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledOppositeContent.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "timeline.item";
var StyledTimelineItem = styled_components_browser_esm_default.li.attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledItem__StyledTimelineItem",
  componentId: "sc-5mcnzm-0"
})(["display:flex;position:relative;line-height:", ";color:", ";font-size:", ";&:last-of-type ", "::after{display:none;}", " ", " ", ";"], (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.theme.colors.foreground, (props) => props.theme.fontSizes.md, StyledSeparator, (props) => !props.hasOppositeContent && props.isAlternate && Ae(["&:before{flex:1;content:'';padding:", "px;}"], props.theme.space.base * 4), (props) => props.isAlternate && Ae(["&:nth-child(even){flex-direction:row-reverse;", "{text-align:", ";}", "{text-align:", ";}}"], StyledOppositeContent, props.theme.rtl ? "right" : "left", StyledTimelineContent, props.theme.rtl ? "left" : "right"), (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledTimelineItem.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "timeline.icon";
var StyledItemIcon = styled_components_browser_esm_default((_ref) => {
  let {
    surfaceColor,
    children,
    ...props
  } = _ref;
  return (0, import_react.cloneElement)(import_react.Children.only(children), props);
}).attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledItemIcon",
  componentId: "sc-vz2l6e-0"
})(["z-index:1;box-sizing:content-box;background-color:", ";padding:", "px 0;width:", ";height:", ";color:", ";", ";"], (props) => props.surfaceColor || props.theme.colors.background, (props) => props.theme.space.base, (props) => math(`${props.theme.iconSizes.sm} + 1`), (props) => math(`${props.theme.iconSizes.sm} + 1`), (props) => getColor("neutralHue", 600, props.theme), (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledItemIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var StepperContext = (0, import_react.createContext)(void 0);
var useStepperContext = () => {
  const context = (0, import_react.useContext)(StepperContext);
  if (context === void 0) {
    throw new Error("This component must be rendered within a Stepper component");
  }
  return context;
};
var StepContext = (0, import_react.createContext)(void 0);
var useStepContext = () => {
  const context = (0, import_react.useContext)(StepContext);
  if (context === void 0) {
    throw new Error("This component must be rendered within a Stepper.Step component");
  }
  return context;
};
var AccordionContext = (0, import_react.createContext)(void 0);
var useAccordionContext = () => {
  const context = (0, import_react.useContext)(AccordionContext);
  if (context === void 0) {
    throw new Error("This component must be rendered within a Accordion component");
  }
  return context;
};
var SectionContext = (0, import_react.createContext)(void 0);
var useSectionContext = () => {
  const context = (0, import_react.useContext)(SectionContext);
  if (context === void 0) {
    throw new Error("This component must be rendered within a Accordion.Section component");
  }
  return context;
};
var HeaderContext = (0, import_react.createContext)(void 0);
var useHeaderContext = () => {
  const context = (0, import_react.useContext)(HeaderContext);
  if (context === void 0) {
    throw new Error("This component must be rendered within a Accordion.Header component");
  }
  return context;
};
var TimelineContext = (0, import_react.createContext)(void 0);
var useTimelineContext = () => {
  const context = (0, import_react.useContext)(TimelineContext);
  if (context === void 0) {
    throw new Error("This component must be rendered within a Timeline component");
  }
  return context;
};
var TimelineItemContext = (0, import_react.createContext)(void 0);
var useTimelineItemContext = () => {
  const context = (0, import_react.useContext)(TimelineItemContext);
  if (context === void 0) {
    throw new Error("This component must be rendered within a Timeline.Item component");
  }
  return context;
};
var SectionComponent = (0, import_react.forwardRef)((props, ref) => {
  const {
    currentIndexRef
  } = useAccordionContext();
  const sectionIndexRef = (0, import_react.useRef)(currentIndexRef.current++);
  const sectionIndex = sectionIndexRef.current;
  return import_react.default.createElement(SectionContext.Provider, {
    value: sectionIndex
  }, import_react.default.createElement(StyledSection, _extends$3({
    ref
  }, props)));
});
SectionComponent.displayName = "Accordion.Section";
var Section = SectionComponent;
var _path$1;
function _extends$2() {
  _extends$2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$2.apply(this, arguments);
}
var SvgChevronDownStroke = function SvgChevronDownStroke2(props) {
  return React.createElement("svg", _extends$2({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$1 || (_path$1 = React.createElement("path", {
    fill: "currentColor",
    d: "M12.688 5.61a.5.5 0 01.69.718l-.066.062-5 4a.5.5 0 01-.542.054l-.082-.054-5-4a.5.5 0 01.55-.83l.074.05L8 9.359l4.688-3.75z"
  })));
};
var HeaderComponent = (0, import_react.forwardRef)((props, ref) => {
  const {
    level: ariaLevel,
    isCompact,
    isCollapsible,
    getHeaderProps,
    getTriggerProps,
    expandedSections
  } = useAccordionContext();
  const {
    onClick,
    onFocus,
    onBlur,
    onMouseOver,
    onMouseOut,
    children,
    ...other
  } = props;
  const sectionIndex = useSectionContext();
  const [isFocused, setIsFocused] = (0, import_react.useState)(false);
  const [isHovered, setIsHovered] = (0, import_react.useState)(false);
  const isExpanded = expandedSections.includes(sectionIndex);
  const {
    onClick: onTriggerClick,
    onKeyDown,
    ...otherTriggerProps
  } = getTriggerProps({
    type: "button",
    index: sectionIndex
  });
  const onHeaderFocus = (e) => {
    e.persist();
    setTimeout(() => {
      const isAccordionButton = e.target.getAttribute("data-garden-id") === COMPONENT_ID$8;
      const isFocusVisible = e.target.getAttribute("data-garden-focus-visible");
      if (isAccordionButton && isFocusVisible) {
        setIsFocused(true);
      }
    }, 0);
  };
  const value = (0, import_react.useMemo)(() => ({
    isHovered,
    otherTriggerProps
  }), [isHovered, otherTriggerProps]);
  return import_react.default.createElement(HeaderContext.Provider, {
    value
  }, import_react.default.createElement(StyledHeader, getHeaderProps({
    ref,
    ariaLevel,
    isCompact,
    isFocused,
    isExpanded,
    isCollapsible,
    onClick: composeEventHandlers(onClick, onTriggerClick),
    onFocus: composeEventHandlers(onFocus, onHeaderFocus),
    onBlur: composeEventHandlers(onBlur, () => setIsFocused(false)),
    onMouseOver: composeEventHandlers(onMouseOver, () => setIsHovered(true)),
    onMouseOut: composeEventHandlers(onMouseOut, () => setIsHovered(false)),
    ...other
  }), children, import_react.default.createElement(StyledRotateIcon, {
    isCompact,
    isHovered,
    isRotated: isExpanded,
    isCollapsible,
    onMouseOver: composeEventHandlers(onMouseOver, () => setIsHovered(true)),
    onMouseOut: composeEventHandlers(onMouseOut, () => setIsHovered(false))
  }, import_react.default.createElement(SvgChevronDownStroke, null))));
});
HeaderComponent.displayName = "Accordion.Header";
var Header = HeaderComponent;
var LabelComponent$1 = (0, import_react.forwardRef)((props, ref) => {
  const sectionIndex = useSectionContext();
  const {
    isCompact,
    isCollapsible,
    expandedSections
  } = useAccordionContext();
  const isExpanded = expandedSections.includes(sectionIndex);
  const {
    isHovered,
    otherTriggerProps
  } = useHeaderContext();
  return import_react.default.createElement(StyledButton, _extends$3({
    ref,
    isCompact,
    isHovered,
    isExpanded,
    isCollapsible
  }, otherTriggerProps, props));
});
LabelComponent$1.displayName = "Accordion.Label";
var Label$1 = LabelComponent$1;
var PanelComponent = (0, import_react.forwardRef)((props, ref) => {
  const {
    isCompact,
    isBare,
    isAnimated,
    getPanelProps,
    expandedSections
  } = useAccordionContext();
  const panelRef = (0, import_react.useRef)();
  const index = useSectionContext();
  const isExpanded = expandedSections.includes(index);
  const theme = (0, import_react.useContext)(Me);
  const environment = useDocument(theme);
  (0, import_react.useEffect)(() => {
    if (!isAnimated) {
      return void 0;
    }
    if (environment) {
      const updateMaxHeight = (0, import_lodash.default)(() => {
        if (panelRef.current) {
          const child = panelRef.current.children[0];
          child.style.maxHeight = `${child.scrollHeight}px`;
        }
      }, 100);
      const win = environment.defaultView || window;
      win.addEventListener("resize", updateMaxHeight);
      updateMaxHeight();
      return () => {
        updateMaxHeight.cancel();
        win.removeEventListener("resize", updateMaxHeight);
      };
    }
    return void 0;
  }, [isAnimated, panelRef, environment, props.children]);
  return import_react.default.createElement(StyledPanel, getPanelProps({
    role: null,
    ref: react_merge_refs_esm_default([panelRef, ref]),
    index,
    isBare,
    isCompact,
    isExpanded,
    isAnimated,
    ...props
  }), import_react.default.createElement(StyledInnerPanel, {
    isExpanded,
    isAnimated
  }, props.children));
});
PanelComponent.displayName = "Accordion.Panel";
var Panel = PanelComponent;
var AccordionComponent = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    level,
    isBare,
    onChange,
    isCompact,
    isAnimated,
    isExpandable,
    isCollapsible,
    defaultExpandedSections,
    expandedSections: controlledExpandedSections,
    ...props
  } = _ref;
  const {
    expandedSections,
    getHeaderProps,
    getTriggerProps,
    getPanelProps
  } = useAccordion({
    collapsible: isCollapsible,
    expandable: isExpandable || false,
    onChange,
    defaultExpandedSections,
    expandedSections: controlledExpandedSections
  });
  const currentIndexRef = (0, import_react.useRef)(0);
  (0, import_react.useEffect)(() => {
    currentIndexRef.current = 0;
  });
  const value = (0, import_react.useMemo)(() => ({
    level,
    isBare,
    isCompact,
    isAnimated,
    isCollapsible,
    getPanelProps,
    getHeaderProps,
    getTriggerProps,
    currentIndexRef,
    expandedSections
  }), [level, isBare, isCompact, isAnimated, isCollapsible, getPanelProps, getHeaderProps, getTriggerProps, currentIndexRef, expandedSections]);
  return import_react.default.createElement(AccordionContext.Provider, {
    value
  }, import_react.default.createElement(StyledAccordion, _extends$3({
    ref
  }, props)));
});
AccordionComponent.displayName = "Accordion";
AccordionComponent.defaultProps = {
  isAnimated: true,
  isCollapsible: true
};
var Accordion = AccordionComponent;
Accordion.Header = Header;
Accordion.Label = Label$1;
Accordion.Panel = Panel;
Accordion.Section = Section;
var StepComponent = (0, import_react.forwardRef)((props, ref) => {
  const {
    currentIndexRef,
    isHorizontal
  } = useStepperContext();
  const [currentStepIndex, setCurrentStepIndex] = (0, import_react.useState)(currentIndexRef.current);
  (0, import_react.useLayoutEffect)(() => {
    setCurrentStepIndex(currentIndexRef.current);
    currentIndexRef.current++;
    const currentIndex = currentIndexRef;
    return () => {
      currentIndex.current--;
    };
  }, [currentIndexRef]);
  const value = (0, import_react.useMemo)(() => ({
    currentStepIndex
  }), [currentStepIndex]);
  return import_react.default.createElement(StepContext.Provider, {
    value
  }, import_react.default.createElement(StyledStep, _extends$3({
    ref,
    isHorizontal
  }, props), isHorizontal && import_react.default.createElement(StyledLine, null), props.children));
});
StepComponent.displayName = "Stepper.Step";
var Step = StepComponent;
var _path;
function _extends$1() {
  _extends$1 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$1.apply(this, arguments);
}
var SvgCheckSmStroke = function SvgCheckSmStroke2(props) {
  return React.createElement("svg", _extends$1({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path || (_path = React.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: 1.25,
    d: "M3 9l3 3 7-7"
  })));
};
var LabelComponent = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    icon,
    iconProps,
    isHidden,
    children,
    ...other
  } = _ref;
  const {
    currentStepIndex
  } = useStepContext();
  const {
    activeIndex,
    isHorizontal
  } = useStepperContext();
  const numericStep = currentStepIndex + 1;
  const stepIcon = icon || numericStep;
  const isActive = activeIndex === currentStepIndex;
  const isCompleted = activeIndex > currentStepIndex;
  const styledIcon = import_react.default.createElement(StyledIcon, {
    isActive,
    isHorizontal
  }, isCompleted ? import_react.default.createElement(SvgCheckSmStroke, iconProps) : stepIcon);
  return import_react.default.createElement(StyledLabel, _extends$3({
    ref,
    isActive,
    isHorizontal
  }, other), isHorizontal ? import_react.default.createElement(StyledIconFlexContainer, null, styledIcon) : styledIcon, import_react.default.createElement(StyledLabelText, {
    isHidden,
    isHorizontal
  }, children));
});
LabelComponent.displayName = "Stepper.Label";
LabelComponent.propTypes = {
  icon: import_prop_types.default.node,
  iconProps: import_prop_types.default.object,
  isHidden: import_prop_types.default.bool
};
var Label = LabelComponent;
var ContentComponent$1 = (0, import_react.forwardRef)((props, ref) => {
  const contentRef = (0, import_react.useRef)();
  const {
    activeIndex,
    isHorizontal
  } = useStepperContext();
  const {
    currentStepIndex
  } = useStepContext();
  const isActive = currentStepIndex === activeIndex;
  const theme = (0, import_react.useContext)(Me);
  const environment = useDocument(theme);
  (0, import_react.useEffect)(() => {
    if (environment && isActive && isHorizontal === false) {
      const win = environment.defaultView || window;
      const updateMaxHeight = (0, import_lodash.default)(() => {
        if (contentRef.current) {
          const child = contentRef.current.children[0];
          child.style.maxHeight = `${child.scrollHeight}px`;
        }
      }, 100);
      win.addEventListener("resize", updateMaxHeight);
      updateMaxHeight();
      return () => {
        updateMaxHeight.cancel();
        win.removeEventListener("resize", updateMaxHeight);
      };
    }
    return void 0;
  }, [isActive, isHorizontal, contentRef, environment]);
  return isHorizontal === false ? import_react.default.createElement(StyledContent, _extends$3({
    ref: react_merge_refs_esm_default([contentRef, ref]),
    isActive
  }, props), import_react.default.createElement(StyledInnerContent, {
    isActive,
    "aria-hidden": !isActive
  }, props.children)) : null;
});
ContentComponent$1.displayName = "Stepper.Content";
var Content$1 = ContentComponent$1;
var StepperComponent = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    isHorizontal,
    activeIndex,
    ...props
  } = _ref;
  const currentIndexRef = (0, import_react.useRef)(0);
  const stepperContext = (0, import_react.useMemo)(() => ({
    isHorizontal: isHorizontal || false,
    activeIndex,
    currentIndexRef
  }), [isHorizontal, activeIndex, currentIndexRef]);
  (0, import_react.useEffect)(() => {
    currentIndexRef.current = 0;
  });
  return import_react.default.createElement(StepperContext.Provider, {
    value: stepperContext
  }, import_react.default.createElement(StyledStepper, _extends$3({
    ref,
    isHorizontal
  }, props)));
});
StepperComponent.displayName = "Stepper";
StepperComponent.defaultProps = {
  activeIndex: 0
};
var Stepper = StepperComponent;
Stepper.Content = Content$1;
Stepper.Label = Label;
Stepper.Step = Step;
var OppositeContentComponent = (0, import_react.forwardRef)((props, ref) => import_react.default.createElement(StyledOppositeContent, _extends$3({
  ref
}, props)));
OppositeContentComponent.displayName = "Timeline.OppositeContent";
var OppositeContent = OppositeContentComponent;
var ItemComponent = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    icon,
    surfaceColor,
    ...props
  } = _ref;
  const value = (0, import_react.useMemo)(() => ({
    icon,
    surfaceColor
  }), [icon, surfaceColor]);
  const {
    isAlternate
  } = useTimelineContext();
  let hasOppositeContent = false;
  import_react.Children.forEach(props.children, (child) => {
    if (child) {
      if (child.type === OppositeContent) {
        hasOppositeContent = true;
      }
    }
  });
  return import_react.default.createElement(TimelineItemContext.Provider, {
    value
  }, import_react.default.createElement(StyledTimelineItem, _extends$3({
    ref,
    isAlternate,
    hasOppositeContent
  }, props)));
});
ItemComponent.displayName = "Timeline.Item";
var Item = ItemComponent;
var _circle;
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var SvgCircleFullStroke = function SvgCircleFullStroke2(props) {
  return React.createElement("svg", _extends({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _circle || (_circle = React.createElement("circle", {
    cx: 6,
    cy: 6,
    r: 4.5,
    fill: "none",
    stroke: "currentColor"
  })));
};
var ContentComponent = (0, import_react.forwardRef)((props, ref) => {
  const {
    icon,
    surfaceColor
  } = useTimelineItemContext();
  return import_react.default.createElement(import_react.default.Fragment, null, import_react.default.createElement(StyledSeparator, null, import_react.default.createElement(StyledItemIcon, {
    surfaceColor
  }, icon || import_react.default.createElement(SvgCircleFullStroke, null))), import_react.default.createElement(StyledTimelineContent, _extends$3({
    ref
  }, props)));
});
ContentComponent.displayName = "Timeline.Content";
var Content = ContentComponent;
var TimelineComponent = (0, import_react.forwardRef)((_ref, ref) => {
  let {
    isAlternate,
    ...props
  } = _ref;
  const value = (0, import_react.useMemo)(() => ({
    isAlternate
  }), [isAlternate]);
  return import_react.default.createElement(TimelineContext.Provider, {
    value
  }, import_react.default.createElement(StyledTimeline, _extends$3({
    ref
  }, props)));
});
TimelineComponent.displayName = "Timeline";
var Timeline = TimelineComponent;
Timeline.Content = Content;
Timeline.Item = Item;
Timeline.OppositeContent = OppositeContent;
export {
  Accordion,
  Stepper,
  Timeline
};
//# sourceMappingURL=@zendeskgarden_react-accordions.js.map
