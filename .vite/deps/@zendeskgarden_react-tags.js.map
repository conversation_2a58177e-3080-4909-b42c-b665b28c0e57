{"version": 3, "sources": ["../../node_modules/@zendeskgarden/react-tags/dist/index.esm.js"], "sourcesContent": ["/**\n * Copyright Zendesk, Inc.\n *\n * Use of this source code is governed under the Apache License, Version 2.0\n * found at http://www.apache.org/licenses/LICENSE-2.0.\n */\n\nimport * as React from 'react';\nimport React__default, { Children, forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nimport styled, { css } from 'styled-components';\nimport { readableColor, math } from 'polished';\nimport { retrieveComponentStyles, DEFAULT_THEME, SELECTOR_FOCUS_VISIBLE, getColor, focusStyles, getLineHeight, useText } from '@zendeskgarden/react-theming';\n\nfunction _extends$1() {\n  _extends$1 = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends$1.apply(this, arguments);\n}\n\nconst SIZE = ['small', 'medium', 'large'];\n\nconst COMPONENT_ID$2 = 'tags.avatar';\nconst StyledAvatar = styled(_ref => {\n  let {\n    children,\n    ...props\n  } = _ref;\n  return React__default.cloneElement(Children.only(children), props);\n}).attrs({\n  'data-garden-id': COMPONENT_ID$2,\n  'data-garden-version': '8.69.8'\n}).withConfig({\n  displayName: \"StyledAvatar\",\n  componentId: \"sc-3kdmgt-0\"\n})([\"flex-shrink:0;font-size:0;\", \";\"], props => retrieveComponentStyles(COMPONENT_ID$2, props));\nStyledAvatar.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID$1 = 'tags.close';\nconst StyledClose = styled.button.attrs({\n  'data-garden-id': COMPONENT_ID$1,\n  'data-garden-version': '8.69.8'\n}).withConfig({\n  displayName: \"StyledClose\",\n  componentId: \"sc-d6lrpn-0\"\n})([\"display:flex;flex-shrink:0;align-items:center;justify-content:center;transition:opacity 0.25s ease-in-out;opacity:0.8;border:0;background:transparent;cursor:pointer;padding:0;color:inherit;font-size:0;appearance:none;&:hover{opacity:0.9;}&:focus{outline:none;}\", \";\"], props => retrieveComponentStyles(COMPONENT_ID$1, props));\nStyledClose.defaultProps = {\n  theme: DEFAULT_THEME\n};\n\nconst COMPONENT_ID = 'tags.tag_view';\nconst colorStyles = props => {\n  let backgroundColor;\n  let foregroundColor;\n  let closeColor;\n  if (props.hue) {\n    const shade = props.hue === 'yellow' ? 400 : 600;\n    backgroundColor = getColor(props.hue, shade, props.theme);\n    if (props.hue === 'yellow' || props.hue === 'lemon') {\n      foregroundColor = getColor('yellow', 800, props.theme);\n    } else {\n      foregroundColor = readableColor(backgroundColor, props.theme.palette.grey[800], props.theme.palette.white);\n    }\n  } else {\n    backgroundColor = getColor('neutralHue', 200, props.theme);\n    foregroundColor = getColor('neutralHue', 700, props.theme);\n    closeColor = getColor('neutralHue', 600, props.theme);\n  }\n  return css([\"background-color:\", \";color:\", \";&:hover{color:\", \";}\", \" & \", \"{color:\", \";}\"], backgroundColor, foregroundColor, foregroundColor, focusStyles({\n    theme: props.theme,\n    shadowWidth: 'sm',\n    selector: '&:focus'\n  }), StyledClose, closeColor);\n};\nconst sizeStyles = props => {\n  let borderRadius;\n  let padding;\n  let height;\n  let fontSize;\n  let minWidth;\n  let avatarSize;\n  if (props.size === 'small') {\n    borderRadius = props.theme.borderRadii.sm;\n    padding = props.theme.space.base;\n    height = props.theme.space.base * 4;\n    fontSize = props.theme.fontSizes.xs;\n    avatarSize = 0;\n  } else if (props.size === 'large') {\n    borderRadius = props.theme.borderRadii.md;\n    padding = props.theme.space.base * 3;\n    height = props.theme.space.base * 8;\n    fontSize = props.theme.fontSizes.sm;\n    avatarSize = props.theme.space.base * 6;\n  } else {\n    borderRadius = props.theme.borderRadii.sm;\n    padding = props.theme.space.base * 2;\n    height = props.theme.space.base * 5;\n    fontSize = props.theme.fontSizes.sm;\n    avatarSize = props.theme.space.base * 4;\n  }\n  let avatarBorderRadius = props.size === 'large' ? math(`${borderRadius} - 1`) : borderRadius;\n  const avatarMargin = (height - avatarSize) / 2;\n  const avatarTextMargin = props.isRound ? avatarMargin : avatarMargin * 2;\n  if (props.isRound) {\n    borderRadius = '50%';\n    padding = 0;\n    minWidth = height;\n    avatarBorderRadius = '50%';\n  } else if (props.isPill) {\n    borderRadius = '100px';\n    avatarBorderRadius = '50%';\n    if (props.size === 'small') {\n      padding = props.theme.space.base * 1.5;\n      minWidth = props.theme.space.base * 6;\n    } else if (props.size === 'large') {\n      minWidth = props.theme.space.base * 12;\n    } else {\n      minWidth = props.theme.space.base * 7.5;\n    }\n  }\n  return css([\"border-radius:\", \";padding:0 \", \"px;min-width:\", \";height:\", \"px;line-height:\", \";font-size:\", \";& > *{width:100%;min-width:\", \";}& \", \"{margin-\", \":-\", \"px;margin-\", \":\", \"px;border-radius:\", \";width:\", \"px;min-width:\", \"px;height:\", \"px;}& \", \"{margin-\", \":-\", \"px;border-radius:\", \";width:\", \"px;height:\", \"px;}\"], borderRadius, padding, minWidth ? `${minWidth}px` : `calc(${padding * 2}px + 1ch)`, height, getLineHeight(height, fontSize), fontSize, minWidth ? `${minWidth - padding * 2}px` : '1ch', StyledAvatar, props.theme.rtl ? 'right' : 'left', padding - avatarMargin, props.theme.rtl ? 'left' : 'right', avatarTextMargin, avatarBorderRadius, avatarSize, avatarSize, avatarSize, StyledClose, props.theme.rtl ? 'left' : 'right', padding, borderRadius, height, height);\n};\nconst StyledTag = styled.div.attrs({\n  'data-garden-id': COMPONENT_ID,\n  'data-garden-version': '8.69.8'\n}).withConfig({\n  displayName: \"StyledTag\",\n  componentId: \"sc-1jvbe03-0\"\n})([\"display:inline-flex;flex-wrap:nowrap;align-items:center;justify-content:\", \";transition:box-shadow 0.1s ease-in-out;box-sizing:border-box;border:0;max-width:100%;overflow:hidden;vertical-align:middle;text-decoration:none;white-space:nowrap;font-weight:\", \";direction:\", \";\", \";&:hover{cursor:default;text-decoration:none;}&:link:hover,&:visited:hover{cursor:pointer;}&:any-link:hover{cursor:pointer;}\", \"{text-decoration:none;}\", \";& > *{overflow:hidden;text-align:center;text-overflow:ellipsis;white-space:nowrap;}& b{font-weight:\", \";}& \", \"{display:\", \";}& \", \"{display:\", \";}\", \";\"], props => props.isRound && 'center', props => !props.isRegular && props.theme.fontWeights.semibold, props => props.theme.rtl ? 'rtl' : 'ltr', props => sizeStyles(props), SELECTOR_FOCUS_VISIBLE, props => colorStyles(props), props => props.theme.fontWeights.semibold, StyledAvatar, props => (props.isRound || props.size === 'small') && 'none', StyledClose, props => props.isRound && 'none', props => retrieveComponentStyles(COMPONENT_ID, props));\nStyledTag.defaultProps = {\n  size: 'medium',\n  theme: DEFAULT_THEME\n};\n\nvar _path;\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nvar SvgXStroke = function SvgXStroke(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 12,\n    height: 12,\n    focusable: \"false\",\n    viewBox: \"0 0 12 12\",\n    \"aria-hidden\": \"true\"\n  }, props), _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    stroke: \"currentColor\",\n    strokeLinecap: \"round\",\n    d: \"M3 9l6-6m0 6L3 3\"\n  })));\n};\n\nconst CloseComponent = forwardRef((props, ref) => {\n  const ariaLabel = useText(CloseComponent, props, 'aria-label', 'Remove');\n  return React__default.createElement(StyledClose, _extends$1({\n    ref: ref,\n    \"aria-label\": ariaLabel\n  }, props, {\n    type: \"button\",\n    tabIndex: -1\n  }), React__default.createElement(SvgXStroke, null));\n});\nCloseComponent.displayName = 'Tag.Close';\nconst Close = CloseComponent;\n\nconst AvatarComponent = props => React__default.createElement(StyledAvatar, props);\nAvatarComponent.displayName = 'Tag.Avatar';\nconst Avatar = AvatarComponent;\n\nconst TagComponent = forwardRef((_ref, ref) => {\n  let {\n    size,\n    hue,\n    ...otherProps\n  } = _ref;\n  return React__default.createElement(StyledTag, _extends$1({\n    ref: ref,\n    size: size,\n    hue: hue\n  }, otherProps));\n});\nTagComponent.displayName = 'Tag';\nTagComponent.propTypes = {\n  size: PropTypes.oneOf(SIZE),\n  hue: PropTypes.string,\n  isPill: PropTypes.bool,\n  isRound: PropTypes.bool,\n  isRegular: PropTypes.bool\n};\nTagComponent.defaultProps = {\n  size: 'medium'\n};\nconst Tag = TagComponent;\nTag.Avatar = Avatar;\nTag.Close = Close;\n\nexport { Tag };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,YAAuB;AACvB,mBAAqD;AACrD,wBAAsB;AAKtB,SAAS,aAAa;AACpB,eAAa,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,WAAW,MAAM,MAAM,SAAS;AACzC;AAEA,IAAM,OAAO,CAAC,SAAS,UAAU,OAAO;AAExC,IAAM,iBAAiB;AACvB,IAAM,eAAe,sCAAO,UAAQ;AAClC,MAAI;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,aAAAA,QAAe,aAAa,sBAAS,KAAK,QAAQ,GAAG,KAAK;AACnE,CAAC,EAAE,MAAM;AAAA,EACP,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,8BAA8B,GAAG,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AAC/F,aAAa,eAAe;AAAA,EAC1B,OAAO;AACT;AAEA,IAAM,iBAAiB;AACvB,IAAM,cAAc,sCAAO,OAAO,MAAM;AAAA,EACtC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,wQAAwQ,GAAG,GAAG,WAAS,wBAAwB,gBAAgB,KAAK,CAAC;AACzU,YAAY,eAAe;AAAA,EACzB,OAAO;AACT;AAEA,IAAM,eAAe;AACrB,IAAM,cAAc,WAAS;AAC3B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM,KAAK;AACb,UAAM,QAAQ,MAAM,QAAQ,WAAW,MAAM;AAC7C,sBAAkB,SAAS,MAAM,KAAK,OAAO,MAAM,KAAK;AACxD,QAAI,MAAM,QAAQ,YAAY,MAAM,QAAQ,SAAS;AACnD,wBAAkB,SAAS,UAAU,KAAK,MAAM,KAAK;AAAA,IACvD,OAAO;AACL,wBAAkB,cAAc,iBAAiB,MAAM,MAAM,QAAQ,KAAK,GAAG,GAAG,MAAM,MAAM,QAAQ,KAAK;AAAA,IAC3G;AAAA,EACF,OAAO;AACL,sBAAkB,SAAS,cAAc,KAAK,MAAM,KAAK;AACzD,sBAAkB,SAAS,cAAc,KAAK,MAAM,KAAK;AACzD,iBAAa,SAAS,cAAc,KAAK,MAAM,KAAK;AAAA,EACtD;AACA,SAAO,GAAI,CAAC,qBAAqB,WAAW,mBAAmB,MAAM,OAAO,WAAW,IAAI,GAAG,iBAAiB,iBAAiB,iBAAiB,YAAY;AAAA,IAC3J,OAAO,MAAM;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,EACZ,CAAC,GAAG,aAAa,UAAU;AAC7B;AACA,IAAM,aAAa,WAAS;AAC1B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM,SAAS,SAAS;AAC1B,mBAAe,MAAM,MAAM,YAAY;AACvC,cAAU,MAAM,MAAM,MAAM;AAC5B,aAAS,MAAM,MAAM,MAAM,OAAO;AAClC,eAAW,MAAM,MAAM,UAAU;AACjC,iBAAa;AAAA,EACf,WAAW,MAAM,SAAS,SAAS;AACjC,mBAAe,MAAM,MAAM,YAAY;AACvC,cAAU,MAAM,MAAM,MAAM,OAAO;AACnC,aAAS,MAAM,MAAM,MAAM,OAAO;AAClC,eAAW,MAAM,MAAM,UAAU;AACjC,iBAAa,MAAM,MAAM,MAAM,OAAO;AAAA,EACxC,OAAO;AACL,mBAAe,MAAM,MAAM,YAAY;AACvC,cAAU,MAAM,MAAM,MAAM,OAAO;AACnC,aAAS,MAAM,MAAM,MAAM,OAAO;AAClC,eAAW,MAAM,MAAM,UAAU;AACjC,iBAAa,MAAM,MAAM,MAAM,OAAO;AAAA,EACxC;AACA,MAAI,qBAAqB,MAAM,SAAS,UAAU,KAAK,GAAG,kBAAkB,IAAI;AAChF,QAAM,gBAAgB,SAAS,cAAc;AAC7C,QAAM,mBAAmB,MAAM,UAAU,eAAe,eAAe;AACvE,MAAI,MAAM,SAAS;AACjB,mBAAe;AACf,cAAU;AACV,eAAW;AACX,yBAAqB;AAAA,EACvB,WAAW,MAAM,QAAQ;AACvB,mBAAe;AACf,yBAAqB;AACrB,QAAI,MAAM,SAAS,SAAS;AAC1B,gBAAU,MAAM,MAAM,MAAM,OAAO;AACnC,iBAAW,MAAM,MAAM,MAAM,OAAO;AAAA,IACtC,WAAW,MAAM,SAAS,SAAS;AACjC,iBAAW,MAAM,MAAM,MAAM,OAAO;AAAA,IACtC,OAAO;AACL,iBAAW,MAAM,MAAM,MAAM,OAAO;AAAA,IACtC;AAAA,EACF;AACA,SAAO,GAAI,CAAC,kBAAkB,eAAe,iBAAiB,YAAY,mBAAmB,eAAe,gCAAgC,QAAQ,YAAY,MAAM,cAAc,KAAK,qBAAqB,WAAW,iBAAiB,cAAc,UAAU,YAAY,MAAM,qBAAqB,WAAW,cAAc,MAAM,GAAG,cAAc,SAAS,WAAW,GAAG,eAAe,QAAQ,UAAU,cAAc,QAAQ,cAAc,QAAQ,QAAQ,GAAG,UAAU,WAAW,GAAG,WAAW,UAAU,QAAQ,OAAO,cAAc,MAAM,MAAM,MAAM,UAAU,QAAQ,UAAU,cAAc,MAAM,MAAM,MAAM,SAAS,SAAS,kBAAkB,oBAAoB,YAAY,YAAY,YAAY,aAAa,MAAM,MAAM,MAAM,SAAS,SAAS,SAAS,cAAc,QAAQ,MAAM;AACpxB;AACA,IAAM,YAAY,sCAAO,IAAI,MAAM;AAAA,EACjC,kBAAkB;AAAA,EAClB,uBAAuB;AACzB,CAAC,EAAE,WAAW;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACf,CAAC,EAAE,CAAC,4EAA4E,oLAAoL,eAAe,KAAK,gIAAgI,2BAA2B,wGAAwG,QAAQ,aAAa,QAAQ,aAAa,MAAM,GAAG,GAAG,WAAS,MAAM,WAAW,UAAU,WAAS,CAAC,MAAM,aAAa,MAAM,MAAM,YAAY,UAAU,WAAS,MAAM,MAAM,MAAM,QAAQ,OAAO,WAAS,WAAW,KAAK,GAAG,wBAAwB,WAAS,YAAY,KAAK,GAAG,WAAS,MAAM,MAAM,YAAY,UAAU,cAAc,YAAU,MAAM,WAAW,MAAM,SAAS,YAAY,QAAQ,aAAa,WAAS,MAAM,WAAW,QAAQ,WAAS,wBAAwB,cAAc,KAAK,CAAC;AAC1gC,UAAU,eAAe;AAAA,EACvB,MAAM;AAAA,EACN,OAAO;AACT;AAEA,IAAI;AACJ,SAAS,WAAW;AAAE,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,SAAS,MAAM,MAAM,SAAS;AAAG;AAClV,IAAI,aAAa,SAASC,YAAW,OAAO;AAC1C,SAA0B,oBAAc,OAAO,SAAS;AAAA,IACtD,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,GAAG,KAAK,GAAG,UAAU,QAA2B,oBAAc,QAAQ;AAAA,IACpE,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,GAAG;AAAA,EACL,CAAC,EAAE;AACL;AAEA,IAAM,qBAAiB,yBAAW,CAAC,OAAO,QAAQ;AAChD,QAAM,YAAY,QAAQ,gBAAgB,OAAO,cAAc,QAAQ;AACvE,SAAO,aAAAD,QAAe,cAAc,aAAa,WAAW;AAAA,IAC1D;AAAA,IACA,cAAc;AAAA,EAChB,GAAG,OAAO;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC,GAAG,aAAAA,QAAe,cAAc,YAAY,IAAI,CAAC;AACpD,CAAC;AACD,eAAe,cAAc;AAC7B,IAAM,QAAQ;AAEd,IAAM,kBAAkB,WAAS,aAAAA,QAAe,cAAc,cAAc,KAAK;AACjF,gBAAgB,cAAc;AAC9B,IAAM,SAAS;AAEf,IAAM,mBAAe,yBAAW,CAAC,MAAM,QAAQ;AAC7C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,SAAO,aAAAA,QAAe,cAAc,WAAW,WAAW;AAAA,IACxD;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,UAAU,CAAC;AAChB,CAAC;AACD,aAAa,cAAc;AAC3B,aAAa,YAAY;AAAA,EACvB,MAAM,kBAAAE,QAAU,MAAM,IAAI;AAAA,EAC1B,KAAK,kBAAAA,QAAU;AAAA,EACf,QAAQ,kBAAAA,QAAU;AAAA,EAClB,SAAS,kBAAAA,QAAU;AAAA,EACnB,WAAW,kBAAAA,QAAU;AACvB;AACA,aAAa,eAAe;AAAA,EAC1B,MAAM;AACR;AACA,IAAM,MAAM;AACZ,IAAI,SAAS;AACb,IAAI,QAAQ;", "names": ["React__default", "SvgXStroke", "PropTypes"]}