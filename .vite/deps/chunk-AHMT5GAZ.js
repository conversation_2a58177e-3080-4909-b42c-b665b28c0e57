import {
  require_lodash
} from "./chunk-DM3DLLZI.js";
import {
  react_merge_refs_esm_default
} from "./chunk-JLWORYXM.js";
import {
  useUIDSeed
} from "./chunk-JQPVIOLG.js";
import {
  DEFAULT_THEME,
  SELECTOR_FOCUS_VISIBLE,
  em$1,
  focusStyles,
  getColor,
  getFocusBoxShadow,
  getLineHeight,
  hideVisually,
  math,
  retrieveComponentStyles,
  rgba,
  useDocument,
  useText
} from "./chunk-KGUWDO6Q.js";
import {
  composeEventHandlers,
  getControlledValue
} from "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  Ae,
  Me,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-forms/dist/index.esm.js
var React3 = __toESM(require_react());
var import_react3 = __toESM(require_react());

// node_modules/@zendeskgarden/container-field/dist/index.esm.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
function useField(idPrefix) {
  const seed = useUIDSeed();
  const prefix = (0, import_react.useMemo)(() => idPrefix || seed(`field_${"2.1.2"}`), [idPrefix, seed]);
  const inputId = `${prefix}--input`;
  const labelId = `${prefix}--label`;
  const hintId = `${prefix}--hint`;
  const messageId = `${prefix}--message`;
  const getLabelProps = function(_temp) {
    let {
      id = labelId,
      htmlFor = inputId,
      ...other
    } = _temp === void 0 ? {} : _temp;
    return {
      id,
      htmlFor,
      "data-garden-container-id": "containers.field",
      "data-garden-container-version": "2.1.2",
      ...other
    };
  };
  const getInputProps = function(_temp2, _temp3) {
    let {
      id = inputId,
      ...other
    } = _temp2 === void 0 ? {} : _temp2;
    let {
      isDescribed = false,
      hasMessage = false
    } = _temp3 === void 0 ? {} : _temp3;
    return {
      id,
      "aria-labelledby": labelId,
      "aria-describedby": isDescribed || hasMessage ? [].concat(isDescribed ? hintId : [], hasMessage ? messageId : []).join(" ") : null,
      ...other
    };
  };
  const getHintProps = function(_temp4) {
    let {
      id = hintId,
      ...other
    } = _temp4 === void 0 ? {} : _temp4;
    return {
      id,
      ...other
    };
  };
  const getMessageProps = function(_temp5) {
    let {
      id = messageId,
      ...other
    } = _temp5 === void 0 ? {} : _temp5;
    return {
      id,
      ...other
    };
  };
  return {
    getLabelProps,
    getInputProps,
    getHintProps,
    getMessageProps
  };
}
var FieldContainer = (_ref) => {
  let {
    children,
    render = children,
    id
  } = _ref;
  return import_react.default.createElement(import_react.default.Fragment, null, render(useField(id)));
};
FieldContainer.propTypes = {
  children: import_prop_types.default.func,
  render: import_prop_types.default.func,
  id: import_prop_types.default.string
};

// node_modules/@zendeskgarden/react-forms/dist/index.esm.js
var import_prop_types3 = __toESM(require_prop_types());
var import_lodash2 = __toESM(require_lodash());

// node_modules/@zendeskgarden/container-slider/dist/index.esm.js
var import_react2 = __toESM(require_react());
var import_lodash = __toESM(require_lodash());
var import_prop_types2 = __toESM(require_prop_types());
function composeEventHandlers2() {
  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {
    fns[_key] = arguments[_key];
  }
  return function(event) {
    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
      args[_key2 - 1] = arguments[_key2];
    }
    return fns.some((fn) => {
      fn && fn(event, ...args);
      return event && event.defaultPrevented;
    });
  };
}
var KEYS = {
  ALT: "Alt",
  ASTERISK: "*",
  BACKSPACE: "Backspace",
  COMMA: ",",
  DELETE: "Delete",
  DOWN: "ArrowDown",
  END: "End",
  ENTER: "Enter",
  ESCAPE: "Escape",
  HOME: "Home",
  LEFT: "ArrowLeft",
  NUMPAD_ADD: "Add",
  NUMPAD_DECIMAL: "Decimal",
  NUMPAD_DIVIDE: "Divide",
  NUMPAD_ENTER: "Enter",
  NUMPAD_MULTIPLY: "Multiply",
  NUMPAD_SUBTRACT: "Subtract",
  PAGE_DOWN: "PageDown",
  PAGE_UP: "PageUp",
  PERIOD: ".",
  RIGHT: "ArrowRight",
  SHIFT: "Shift",
  SPACE: " ",
  TAB: "Tab",
  UNIDENTIFIED: "Unidentified",
  UP: "ArrowUp"
};
var DocumentPosition;
(function(DocumentPosition2) {
  DocumentPosition2[DocumentPosition2["DISCONNECTED"] = 1] = "DISCONNECTED";
  DocumentPosition2[DocumentPosition2["PRECEDING"] = 2] = "PRECEDING";
  DocumentPosition2[DocumentPosition2["FOLLOWING"] = 4] = "FOLLOWING";
  DocumentPosition2[DocumentPosition2["CONTAINS"] = 8] = "CONTAINS";
  DocumentPosition2[DocumentPosition2["CONTAINED_BY"] = 16] = "CONTAINED_BY";
  DocumentPosition2[DocumentPosition2["IMPLEMENTATION_SPECIFIC"] = 32] = "IMPLEMENTATION_SPECIFIC";
})(DocumentPosition || (DocumentPosition = {}));
var SLIDER_MIN = 0;
var SLIDER_MAX = 100;
var SLIDER_STEP = 1;
function useSlider(_ref) {
  let {
    trackRef,
    minThumbRef,
    maxThumbRef,
    min = SLIDER_MIN,
    max = SLIDER_MAX,
    defaultMinValue,
    defaultMaxValue,
    minValue,
    maxValue,
    onChange = () => void 0,
    step = SLIDER_STEP,
    jump = step,
    disabled,
    rtl,
    environment
  } = _ref;
  const doc = environment || document;
  const [trackRect, setTrackRect] = (0, import_react2.useState)({
    width: 0
  });
  const init = function(initMinValue, initMaxValue) {
    if (initMinValue === void 0) {
      initMinValue = min;
    }
    if (initMaxValue === void 0) {
      initMaxValue = max;
    }
    const retVal = {
      minValue: initMinValue < min ? min : initMinValue,
      maxValue: initMaxValue > max ? max : initMaxValue
    };
    if (retVal.maxValue < min) {
      retVal.maxValue = min;
    }
    if (retVal.minValue > retVal.maxValue) {
      retVal.minValue = retVal.maxValue;
    }
    return retVal;
  };
  const [state, setState] = (0, import_react2.useState)(init(defaultMinValue, defaultMaxValue));
  const isControlled = minValue !== void 0 && minValue !== null || maxValue !== void 0 && maxValue !== null;
  const position = isControlled ? init(minValue, maxValue) : state;
  const setPosition = isControlled ? onChange : setState;
  (0, import_react2.useEffect)(() => {
    const handleResize = (0, import_lodash.default)(() => {
      if (trackRef.current) {
        setTrackRect(trackRef.current.getBoundingClientRect());
      }
    }, 100);
    handleResize();
    const win = doc.defaultView || window;
    win.addEventListener("resize", handleResize);
    return () => {
      win.removeEventListener("resize", handleResize);
      handleResize.cancel();
    };
  }, [doc, trackRef]);
  const getTrackPosition = (0, import_react2.useCallback)((event) => {
    let retVal = void 0;
    if (trackRect) {
      const offset = rtl ? trackRect.left + trackRect.width : trackRect.left;
      const mouseX = (event.pageX - offset) * (rtl ? -1 : 1);
      const x = (max - min) * mouseX / trackRect.width;
      const value = min + parseInt(x, 10);
      retVal = Math.floor(value / step) * step;
    }
    return retVal;
  }, [max, min, trackRect, rtl, step]);
  const setThumbPosition = (0, import_react2.useCallback)((thumb) => (value) => {
    if (!disabled) {
      let newMinValue = position.minValue;
      let newMaxValue = position.maxValue;
      if (thumb === "min") {
        if (value < min) {
          newMinValue = min;
        } else if (value > position.maxValue) {
          newMinValue = position.maxValue;
        } else {
          newMinValue = value;
        }
      } else if (thumb === "max") {
        if (value > max) {
          newMaxValue = max;
        } else if (value < position.minValue) {
          newMaxValue = position.minValue;
        } else {
          newMaxValue = value;
        }
      }
      if (!isNaN(newMinValue) && !isNaN(newMaxValue)) {
        setPosition({
          minValue: newMinValue,
          maxValue: newMaxValue
        });
      }
    }
  }, [disabled, max, min, position.maxValue, position.minValue, setPosition]);
  const getTrackProps = (0, import_react2.useCallback)(function(_temp) {
    let {
      onMouseDown,
      ...other
    } = _temp === void 0 ? {} : _temp;
    const handleMouseDown = (event) => {
      if (!disabled && event.button === 0) {
        const value = getTrackPosition(event);
        if (value !== void 0) {
          const minOffset = Math.abs(position.minValue - value);
          const maxOffset = Math.abs(position.maxValue - value);
          if (minOffset <= maxOffset) {
            minThumbRef.current && minThumbRef.current.focus();
            setThumbPosition("min")(value);
          } else {
            maxThumbRef.current && maxThumbRef.current.focus();
            setThumbPosition("max")(value);
          }
        }
      }
    };
    return {
      "data-garden-container-id": "containers.slider.track",
      "data-garden-container-version": "0.1.5",
      "aria-disabled": disabled,
      onMouseDown: composeEventHandlers2(onMouseDown, handleMouseDown),
      ...other
    };
  }, [disabled, getTrackPosition, maxThumbRef, minThumbRef, position.maxValue, position.minValue, setThumbPosition]);
  const getThumbProps = (0, import_react2.useCallback)((thumb) => (_ref2) => {
    let {
      onKeyDown,
      onMouseDown,
      onTouchStart,
      ...other
    } = _ref2;
    const handleKeyDown = (event) => {
      if (!disabled) {
        let value;
        switch (event.key) {
          case KEYS.RIGHT:
            value = (thumb === "min" ? position.minValue : position.maxValue) + (rtl ? -step : step);
            break;
          case KEYS.UP:
            value = (thumb === "min" ? position.minValue : position.maxValue) + step;
            break;
          case KEYS.LEFT:
            value = (thumb === "min" ? position.minValue : position.maxValue) - (rtl ? -step : step);
            break;
          case KEYS.DOWN:
            value = (thumb === "min" ? position.minValue : position.maxValue) - step;
            break;
          case KEYS.PAGE_UP:
            value = (thumb === "min" ? position.minValue : position.maxValue) + jump;
            break;
          case KEYS.PAGE_DOWN:
            value = (thumb === "min" ? position.minValue : position.maxValue) - jump;
            break;
          case KEYS.HOME:
            value = min;
            break;
          case KEYS.END:
            value = max;
            break;
        }
        if (value !== void 0) {
          event.preventDefault();
          event.stopPropagation();
          setThumbPosition(thumb)(value);
        }
      }
    };
    const handleMouseMove = (event) => {
      const value = getTrackPosition(event);
      if (value !== void 0) {
        setThumbPosition(thumb)(value);
      }
    };
    const handleTouchMove = (event) => {
      const value = getTrackPosition(event.targetTouches[0]);
      if (value !== void 0) {
        setThumbPosition(thumb)(value);
      }
    };
    const handleMouseUp = () => {
      doc.removeEventListener("mousemove", handleMouseMove);
      doc.removeEventListener("mouseup", handleMouseUp);
    };
    const handleTouchEnd = () => {
      doc.removeEventListener("touchend", handleTouchEnd);
      doc.removeEventListener("touchmove", handleTouchMove);
    };
    const handleMouseDown = (event) => {
      if (!disabled && event.button === 0) {
        event.stopPropagation();
        doc.addEventListener("mousemove", handleMouseMove);
        doc.addEventListener("mouseup", handleMouseUp);
        event.target && event.target.focus();
      }
    };
    const handleTouchStart = (event) => {
      if (!disabled) {
        event.stopPropagation();
        doc.addEventListener("touchmove", handleTouchMove);
        doc.addEventListener("touchend", handleTouchEnd);
        event.target && event.target.focus();
      }
    };
    return {
      "data-garden-container-id": "containers.slider.thumb",
      "data-garden-container-version": "0.1.5",
      role: "slider",
      tabIndex: disabled ? -1 : 0,
      "aria-valuemin": thumb === "min" ? min : position.minValue,
      "aria-valuemax": thumb === "min" ? position.maxValue : max,
      "aria-valuenow": thumb === "min" ? position.minValue : position.maxValue,
      onKeyDown: composeEventHandlers2(onKeyDown, handleKeyDown),
      onMouseDown: composeEventHandlers2(onMouseDown, handleMouseDown),
      onTouchStart: composeEventHandlers2(onTouchStart, handleTouchStart),
      ...other
    };
  }, [doc, disabled, getTrackPosition, jump, max, min, position.maxValue, position.minValue, rtl, step, setThumbPosition]);
  return (0, import_react2.useMemo)(() => ({
    getTrackProps,
    getMinThumbProps: getThumbProps("min"),
    getMaxThumbProps: getThumbProps("max"),
    trackRect,
    minValue: position.minValue,
    maxValue: position.maxValue
  }), [getTrackProps, getThumbProps, position.minValue, position.maxValue, trackRect]);
}
var SliderContainer = (_ref) => {
  let {
    children,
    render = children,
    ...options
  } = _ref;
  return import_react2.default.createElement(import_react2.default.Fragment, null, render(useSlider(options)));
};
SliderContainer.propTypes = {
  children: import_prop_types2.default.func,
  render: import_prop_types2.default.func,
  trackRef: import_prop_types2.default.any.isRequired,
  minThumbRef: import_prop_types2.default.any.isRequired,
  maxThumbRef: import_prop_types2.default.any.isRequired,
  environment: import_prop_types2.default.any,
  defaultMinValue: import_prop_types2.default.number,
  defaultMaxValue: import_prop_types2.default.number,
  minValue: import_prop_types2.default.number,
  maxValue: import_prop_types2.default.number,
  onChange: import_prop_types2.default.func,
  min: import_prop_types2.default.number,
  max: import_prop_types2.default.number,
  step: import_prop_types2.default.number,
  jump: import_prop_types2.default.number,
  disabled: import_prop_types2.default.bool,
  rtl: import_prop_types2.default.bool
};
SliderContainer.defaultProps = {
  min: SLIDER_MIN,
  max: SLIDER_MAX,
  step: SLIDER_STEP
};

// node_modules/@zendeskgarden/react-forms/dist/index.esm.js
function _extends$t() {
  _extends$t = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$t.apply(this, arguments);
}
var FieldContext = (0, import_react3.createContext)(void 0);
var useFieldContext = () => {
  const fieldContext = (0, import_react3.useContext)(FieldContext);
  return fieldContext;
};
var COMPONENT_ID$K = "forms.field";
var StyledField = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$K,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledField",
  componentId: "sc-12gzfsu-0"
})(["position:relative;direction:", ";margin:0;border:0;padding:0;font-size:0;", ";"], (props) => props.theme.rtl ? "rtl" : "ltr", (props) => retrieveComponentStyles(COMPONENT_ID$K, props));
StyledField.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$J = "forms.fieldset";
var StyledFieldset = styled_components_browser_esm_default(StyledField).attrs({
  as: "fieldset",
  "data-garden-id": COMPONENT_ID$J,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledFieldset",
  componentId: "sc-1vr4mxv-0"
})(["", "{margin-top:", "px;}", ";"], StyledField, (props) => props.theme.space.base * (props.isCompact ? 1 : 2), (props) => retrieveComponentStyles(COMPONENT_ID$J, props));
StyledFieldset.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$I = "forms.input_label";
var StyledLabel = styled_components_browser_esm_default.label.attrs((props) => ({
  "data-garden-id": props["data-garden-id"] || COMPONENT_ID$I,
  "data-garden-version": props["data-garden-version"] || "8.69.8"
})).withConfig({
  displayName: "StyledLabel",
  componentId: "sc-2utmsz-0"
})(["direction:", ";vertical-align:middle;line-height:", ";color:", ";font-size:", ";font-weight:", ";&[hidden]{display:", ";vertical-align:", ";text-indent:", ";font-size:", ";", ";}", ";"], (props) => props.theme.rtl && "rtl", (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.theme.colors.foreground, (props) => props.theme.fontSizes.md, (props) => props.isRegular ? props.theme.fontWeights.regular : props.theme.fontWeights.semibold, (props) => props.isRadio ? "inline-block" : "inline", (props) => props.isRadio && "top", (props) => props.isRadio && "-100%", (props) => props.isRadio && "0", (props) => !props.isRadio && hideVisually(), (props) => retrieveComponentStyles(COMPONENT_ID$I, props));
StyledLabel.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$H = "forms.fieldset_legend";
var StyledLegend = styled_components_browser_esm_default(StyledLabel).attrs({
  as: "legend",
  "data-garden-id": COMPONENT_ID$H,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledLegend",
  componentId: "sc-6s0zwq-0"
})(["padding:0;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$H, props));
StyledLegend.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$G = "forms.input_hint";
var StyledHint = styled_components_browser_esm_default.div.attrs((props) => ({
  "data-garden-id": props["data-garden-id"] || COMPONENT_ID$G,
  "data-garden-version": props["data-garden-version"] || "8.69.8"
})).withConfig({
  displayName: "StyledHint",
  componentId: "sc-17c2wu8-0"
})(["direction:", ";display:block;vertical-align:middle;line-height:", ";color:", ";font-size:", ";", ";"], (props) => props.theme.rtl && "rtl", (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => getColor("neutralHue", 600, props.theme), (props) => props.theme.fontSizes.md, (props) => retrieveComponentStyles(COMPONENT_ID$G, props));
StyledHint.defaultProps = {
  theme: DEFAULT_THEME
};
var _g$2;
var _circle$5;
function _extends$s() {
  _extends$s = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$s.apply(this, arguments);
}
var SvgAlertErrorStroke = function SvgAlertErrorStroke2(props) {
  return React3.createElement("svg", _extends$s({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _g$2 || (_g$2 = React3.createElement("g", {
    fill: "none",
    stroke: "currentColor"
  }, React3.createElement("circle", {
    cx: 7.5,
    cy: 8.5,
    r: 7
  }), React3.createElement("path", {
    strokeLinecap: "round",
    d: "M7.5 4.5V9"
  }))), _circle$5 || (_circle$5 = React3.createElement("circle", {
    cx: 7.5,
    cy: 12,
    r: 1,
    fill: "currentColor"
  })));
};
var _path$n;
var _circle$4;
function _extends$r() {
  _extends$r = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$r.apply(this, arguments);
}
var SvgAlertWarningStroke = function SvgAlertWarningStroke2(props) {
  return React3.createElement("svg", _extends$r({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$n || (_path$n = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M.88 13.77L7.06 1.86c.19-.36.7-.36.89 0l6.18 11.91c.17.33-.07.73-.44.73H1.32c-.37 0-.61-.4-.44-.73zM7.5 6v3.5"
  })), _circle$4 || (_circle$4 = React3.createElement("circle", {
    cx: 7.5,
    cy: 12,
    r: 1,
    fill: "currentColor"
  })));
};
var _g$1;
function _extends$q() {
  _extends$q = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$q.apply(this, arguments);
}
var SvgCheckCircleStroke$1 = function SvgCheckCircleStroke(props) {
  return React3.createElement("svg", _extends$q({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _g$1 || (_g$1 = React3.createElement("g", {
    fill: "none",
    stroke: "currentColor"
  }, React3.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 9l2.5 2.5 5-5"
  }), React3.createElement("circle", {
    cx: 7.5,
    cy: 8.5,
    r: 7
  }))));
};
var MessageIcon = (_ref) => {
  let {
    children,
    validation,
    ...props
  } = _ref;
  let retVal;
  if (validation === "error") {
    retVal = import_react3.default.createElement(SvgAlertErrorStroke, props);
  } else if (validation === "success") {
    retVal = import_react3.default.createElement(SvgCheckCircleStroke$1, props);
  } else if (validation === "warning") {
    retVal = import_react3.default.createElement(SvgAlertWarningStroke, props);
  } else {
    retVal = import_react3.default.cloneElement(import_react3.Children.only(children));
  }
  return retVal;
};
var COMPONENT_ID$F = "forms.input_message_icon";
var StyledMessageIcon = styled_components_browser_esm_default(MessageIcon).attrs({
  "data-garden-id": COMPONENT_ID$F,
  "data-garden-version": "8.69.8",
  "aria-hidden": null
}).withConfig({
  displayName: "StyledMessageIcon",
  componentId: "sc-1ph2gba-0"
})(["width:", ";height:", ";", ";"], (props) => props.theme.iconSizes.md, (props) => props.theme.iconSizes.md, (props) => retrieveComponentStyles(COMPONENT_ID$F, props));
StyledMessageIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var validationStyles = (props) => {
  const rtl = props.theme.rtl;
  const padding = math(`${props.theme.space.base} * 2px + ${props.theme.iconSizes.md}`);
  let color;
  if (props.validation === "error") {
    color = getColor("dangerHue", 600, props.theme);
  } else if (props.validation === "success") {
    color = getColor("successHue", 600, props.theme);
  } else if (props.validation === "warning") {
    color = getColor("warningHue", 700, props.theme);
  } else {
    color = getColor("neutralHue", 700, props.theme);
  }
  return Ae(["padding-", ":", ";color:", ";"], rtl ? "right" : "left", props.validation && padding, color);
};
var COMPONENT_ID$E = "forms.input_message";
var StyledMessage = styled_components_browser_esm_default.div.attrs((props) => ({
  "data-garden-id": props["data-garden-id"] || COMPONENT_ID$E,
  "data-garden-version": props["data-garden-version"] || "8.69.8"
})).withConfig({
  displayName: "StyledMessage",
  componentId: "sc-30hgg7-0"
})(["direction:", ";display:inline-block;position:relative;vertical-align:middle;line-height:", ";font-size:", ";", ";& ", "{position:absolute;top:-1px;", ":0;}", ":not([hidden]) + &{display:block;margin-top:", ";}", ";"], (props) => props.theme.rtl && "rtl", (props) => getLineHeight(props.theme.iconSizes.md, props.theme.fontSizes.sm), (props) => props.theme.fontSizes.sm, (props) => validationStyles(props), StyledMessageIcon, (props) => props.theme.rtl ? "right" : "left", StyledLabel, (props) => math(`${props.theme.space.base} * 1px`), (props) => retrieveComponentStyles(COMPONENT_ID$E, props));
StyledMessage.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$D = "forms.input";
var isInvalid = (validation) => {
  return validation === "warning" || validation === "error";
};
var colorStyles$c = (props) => {
  const HUE = "primaryHue";
  const SHADE = 600;
  const placeholderColor = getColor("neutralHue", SHADE - 200, props.theme);
  let borderColor;
  let hoverBorderColor;
  let focusBorderColor;
  let focusRingHue = HUE;
  let focusRingShade = SHADE;
  if (props.validation) {
    let hue = HUE;
    if (props.validation === "success") {
      hue = "successHue";
    } else if (props.validation === "warning") {
      hue = "warningHue";
      focusRingShade = 700;
    } else if (props.validation === "error") {
      hue = "dangerHue";
    }
    borderColor = getColor(hue, SHADE, props.theme);
    hoverBorderColor = borderColor;
    focusBorderColor = borderColor;
    focusRingHue = hue;
  } else {
    borderColor = getColor("neutralHue", SHADE - 300, props.theme);
    hoverBorderColor = getColor("primaryHue", SHADE, props.theme);
    focusBorderColor = hoverBorderColor;
  }
  const readOnlyBackgroundColor = getColor("neutralHue", SHADE - 500, props.theme);
  const readOnlyBorderColor = getColor("neutralHue", SHADE - 300, props.theme);
  const disabledBackgroundColor = readOnlyBackgroundColor;
  const disabledBorderColor = getColor("neutralHue", SHADE - 400, props.theme);
  const disabledForegroundColor = getColor("neutralHue", SHADE - 200, props.theme);
  let controlledBorderColor = borderColor;
  if (props.isFocused) {
    controlledBorderColor = focusBorderColor;
  }
  if (props.isHovered) {
    controlledBorderColor = hoverBorderColor;
  }
  return Ae(["border-color:", ";background-color:", ";color:", ";&::placeholder{color:", ";}&[readonly],&[aria-readonly='true']{border-color:", ";background-color:", ";}&:hover{border-color:", ";}", " &:disabled,&[aria-disabled='true']{border-color:", ";background-color:", ";color:", ";}"], controlledBorderColor, props.isBare ? "transparent" : props.theme.colors.background, props.theme.colors.foreground, placeholderColor, readOnlyBorderColor, !props.isBare && readOnlyBackgroundColor, hoverBorderColor, focusStyles({
    theme: props.theme,
    inset: props.focusInset,
    condition: !props.isBare,
    hue: focusRingHue,
    shade: focusRingShade,
    styles: {
      borderColor: focusBorderColor
    }
  }), disabledBorderColor, !props.isBare && disabledBackgroundColor, disabledForegroundColor);
};
var sizeStyles$f = (props) => {
  const fontSize = props.theme.fontSizes.md;
  const paddingHorizontal = `${props.theme.space.base * 3}px`;
  let height;
  let paddingVertical;
  let browseFontSize;
  let swatchHeight;
  if (props.isCompact) {
    height = `${props.theme.space.base * 8}px`;
    paddingVertical = `${props.theme.space.base * 1.5}px`;
    browseFontSize = math(`${props.theme.fontSizes.sm} - 1`);
    swatchHeight = `${props.theme.space.base * 6}px`;
  } else {
    height = `${props.theme.space.base * 10}px`;
    paddingVertical = `${props.theme.space.base * 2.5}px`;
    browseFontSize = props.theme.fontSizes.sm;
    swatchHeight = `${props.theme.space.base * 7}px`;
  }
  const lineHeight = math(`${height} - (${paddingVertical} * 2) - (${props.theme.borderWidths.sm} * 2)`);
  const padding = props.isBare ? "0" : `${em$1(paddingVertical, fontSize)} ${em$1(paddingHorizontal, fontSize)}`;
  const swatchMarginVertical = math(`(${lineHeight} - ${swatchHeight}) / 2`);
  const swatchMarginHorizontal = math(`${paddingVertical} + ${swatchMarginVertical} - ${paddingHorizontal}`);
  return Ae(["padding:", ";min-height:", ";line-height:", ";font-size:", ";&::-ms-browse{font-size:", ";}&[type='date'],&[type='datetime-local'],&[type='file'],&[type='month'],&[type='time'],&[type='week']{max-height:", ";}&[type='file']{line-height:1;}@supports (-ms-ime-align:auto){&[type='color']{padding:", ";}}&::-moz-color-swatch{margin-top:", ";margin-left:", ";width:calc(100% + ", ");height:", ";}&::-webkit-color-swatch{margin:", " ", ";}", ":not([hidden]) + &&,", " + &&,", " + &&,&& + ", ",&& + ", "{margin-top:", "px;}"], padding, props.isBare ? "1em" : height, getLineHeight(lineHeight, fontSize), fontSize, browseFontSize, height, props.isCompact ? "0 2px" : "1px 3px", swatchMarginVertical, swatchMarginHorizontal, math(`${swatchMarginHorizontal} * -2`), swatchHeight, swatchMarginVertical, swatchMarginHorizontal, StyledLabel, StyledHint, StyledMessage, StyledHint, StyledMessage, props.theme.space.base * (props.isCompact ? 1 : 2));
};
var StyledTextInput = styled_components_browser_esm_default.input.attrs((props) => ({
  "data-garden-id": COMPONENT_ID$D,
  "data-garden-version": "8.69.8",
  "aria-invalid": isInvalid(props.validation)
})).withConfig({
  displayName: "StyledTextInput",
  componentId: "sc-k12n8x-0"
})(["appearance:none;transition:border-color 0.25s ease-in-out,box-shadow 0.1s ease-in-out,background-color 0.25s ease-in-out,color 0.25s ease-in-out,z-index 0.25s ease-in-out;direction:", ";border:", ";border-radius:", ";width:100%;box-sizing:border-box;vertical-align:middle;font-family:inherit;&::-ms-browse{border-radius:", ";}&::-ms-clear,&::-ms-reveal{display:none;}&::-moz-color-swatch{border:none;border-radius:", ";}&::-webkit-color-swatch{border:none;border-radius:", ";}&::-webkit-color-swatch-wrapper{padding:0;}&::-webkit-clear-button,&::-webkit-inner-spin-button,&::-webkit-search-cancel-button,&::-webkit-search-results-button{display:none;}&::-webkit-datetime-edit{direction:", ";line-height:1;}&::placeholder{opacity:1;}&:invalid{box-shadow:none;}&[type='file']::-ms-value{display:none;}@media screen and (min--moz-device-pixel-ratio:0){&[type='number']{appearance:textfield;}}", ";", ";&:disabled{cursor:default;}", ";"], (props) => props.theme.rtl && "rtl", (props) => props.isBare ? "none" : props.theme.borders.sm, (props) => props.isBare ? "0" : props.theme.borderRadii.md, (props) => props.theme.borderRadii.sm, (props) => props.theme.borderRadii.sm, (props) => props.theme.borderRadii.sm, (props) => props.theme.rtl && "rtl", (props) => sizeStyles$f(props), (props) => colorStyles$c(props), (props) => retrieveComponentStyles(COMPONENT_ID$D, props));
StyledTextInput.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$C = "forms.textarea";
var hiddenStyles = `
  visibility: hidden;
  position: absolute;
  overflow: hidden;
  height: 0;
  top: 0;
  left: 0;
  transform: translateZ(0);
`;
var StyledTextarea = styled_components_browser_esm_default(StyledTextInput).attrs({
  as: "textarea",
  "data-garden-id": COMPONENT_ID$C,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledTextarea",
  componentId: "sc-wxschl-0"
})(["resize:", ";overflow:auto;", ";", ";"], (props) => props.isResizable ? "vertical" : "none", (props) => props.isHidden && hiddenStyles, (props) => retrieveComponentStyles(COMPONENT_ID$C, props));
StyledTextarea.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$B = "forms.media_figure";
var colorStyles$b = (props) => {
  let shade = 600;
  if (props.isDisabled) {
    shade = 400;
  } else if (props.isHovered || props.isFocused) {
    shade = 700;
  }
  return Ae(["color:", ";"], getColor("neutralHue", shade, props.theme));
};
var sizeStyles$e = (props) => {
  const size = props.theme.iconSizes.md;
  const marginFirst = `1px ${props.theme.space.base * 2}px auto 0`;
  const marginLast = `1px 0 auto ${props.theme.space.base * 2}px`;
  let margin;
  if (props.position === "start") {
    margin = props.theme.rtl ? marginLast : marginFirst;
  } else {
    margin = props.theme.rtl ? marginFirst : marginLast;
  }
  return Ae(["margin:", ";width:", ";height:", ";"], margin, size, size);
};
var StyledTextMediaFigure = styled_components_browser_esm_default(
  (_ref) => {
    let {
      children,
      position,
      isHovered,
      isFocused,
      isDisabled,
      isRotated,
      theme,
      ...props
    } = _ref;
    return import_react3.default.cloneElement(import_react3.Children.only(children), props);
  }
).attrs({
  "data-garden-id": COMPONENT_ID$B,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledTextMediaFigure",
  componentId: "sc-1qepknj-0"
})(["transform:", ";transition:transform 0.25s ease-in-out,color 0.25s ease-in-out;", ";", " ", ";"], (props) => props.isRotated && `rotate(${props.theme.rtl ? "-" : "+"}180deg)`, (props) => colorStyles$b(props), (props) => sizeStyles$e(props), (props) => retrieveComponentStyles(COMPONENT_ID$B, props));
StyledTextMediaFigure.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$A = "forms.faux_input";
var VALIDATION_HUES = {
  success: "successHue",
  warning: "warningHue",
  error: "dangerHue"
};
function getValidationHue(validation) {
  let defaultHue = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "primaryHue";
  if (validation) {
    return VALIDATION_HUES[validation];
  }
  return defaultHue;
}
var colorStyles$a = (props) => {
  const {
    theme,
    validation,
    focusInset,
    isBare,
    isFocused
  } = props;
  return Ae(["", ""], focusStyles({
    theme,
    inset: focusInset,
    condition: !isBare,
    hue: getValidationHue(validation),
    shade: validation === "warning" ? 700 : 600,
    selector: isFocused ? "&" : "&:focus-within",
    styles: {
      borderColor: getColor(getValidationHue(validation), 600, theme)
    }
  }));
};
var StyledTextFauxInput = styled_components_browser_esm_default(StyledTextInput).attrs((props) => ({
  as: "div",
  "aria-readonly": props.isReadOnly,
  "aria-disabled": props.isDisabled,
  "data-garden-id": COMPONENT_ID$A,
  "data-garden-version": "8.69.8"
})).withConfig({
  displayName: "StyledTextFauxInput",
  componentId: "sc-yqw7j9-0"
})(["display:", ";align-items:", ";cursor:", ";overflow:hidden;", " & > ", "{vertical-align:", ";", "{box-shadow:unset;}}& > ", "{flex-shrink:", ";}", ";"], (props) => props.mediaLayout ? "inline-flex" : "inline-block", (props) => props.mediaLayout && "baseline", (props) => props.mediaLayout && !props.isDisabled ? "text" : "default", colorStyles$a, StyledTextInput, (props) => !props.mediaLayout && "baseline", SELECTOR_FOCUS_VISIBLE, StyledTextMediaFigure, (props) => props.mediaLayout && "0", (props) => retrieveComponentStyles(COMPONENT_ID$A, props));
StyledTextFauxInput.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$z = "forms.media_input";
var StyledTextMediaInput = styled_components_browser_esm_default(StyledTextInput).attrs({
  "data-garden-id": COMPONENT_ID$z,
  "data-garden-version": "8.69.8",
  isBare: true
}).withConfig({
  displayName: "StyledTextMediaInput",
  componentId: "sc-12i9xfi-0"
})(["flex-grow:1;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$z, props));
StyledTextMediaInput.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$y = "forms.input_group";
var positionStyles = (props) => {
  const topMargin = `${props.theme.space.base * (props.isCompact ? 1 : 2)}px`;
  return Ae(["", ":not([hidden]) + &&,", " + &&,", " + &&,&& + ", ",&& + ", "{margin-top:", ";}& > ", "{position:relative;flex:1 1 auto;margin-top:0;margin-bottom:0;width:auto;min-width:0;}"], StyledLabel, StyledHint, StyledMessage, StyledHint, StyledMessage, topMargin, StyledTextInput);
};
var itemStyles = (props) => {
  const startDirection = props.theme.rtl ? "right" : "left";
  const endDirection = props.theme.rtl ? "left" : "right";
  return Ae(["& > *{z-index:-1;}& > ", "{z-index:0;}& > ", ":disabled{z-index:-2;}& > ", ":hover,& > button:hover,& > ", ":focus-visible,& > button:focus-visible,& > ", "[data-garden-focus-visible],& > button[data-garden-focus-visible],& > ", ":active,& > button:active{z-index:1;}& > button:disabled{border-top-width:0;border-bottom-width:0;}& > *:not(:first-child){margin-", ":-", ";}& > *:first-child:not(:last-child){border-top-", "-radius:0;border-bottom-", "-radius:0;}& > *:last-child:not(:first-child){border-top-", "-radius:0;border-bottom-", "-radius:0;}& > *:not(:first-child):not(:last-child){border-radius:0;}"], StyledTextInput, StyledTextInput, StyledTextInput, StyledTextInput, StyledTextInput, StyledTextInput, startDirection, props.theme.borderWidths.sm, endDirection, endDirection, startDirection, startDirection);
};
var StyledInputGroup = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$y,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledInputGroup",
  componentId: "sc-kjh1f0-0"
})(["display:inline-flex;position:relative;flex-wrap:nowrap;align-items:stretch;z-index:0;width:100%;", ";", ";", ";"], (props) => positionStyles(props), (props) => itemStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID$y, props));
StyledInputGroup.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$x = "forms.radio_label";
var sizeStyles$d = (props) => {
  const size = props.theme.space.base * 4;
  const padding = size + props.theme.space.base * 2;
  const lineHeight = props.theme.space.base * 5;
  return Ae(["padding-", ":", "px;&[hidden]{padding-", ":", "px;line-height:", "px;}"], props.theme.rtl ? "right" : "left", padding, props.theme.rtl ? "right" : "left", size, lineHeight);
};
var StyledRadioLabel = styled_components_browser_esm_default(StyledLabel).attrs({
  "data-garden-id": COMPONENT_ID$x,
  "data-garden-version": "8.69.8",
  isRadio: true
}).withConfig({
  displayName: "StyledRadioLabel",
  componentId: "sc-1aq2e5t-0"
})(["display:inline-block;position:relative;cursor:pointer;", ";", ";"], (props) => sizeStyles$d(props), (props) => retrieveComponentStyles(COMPONENT_ID$x, props));
StyledRadioLabel.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$w = "forms.checkbox_label";
var StyledCheckLabel = styled_components_browser_esm_default(StyledRadioLabel).attrs({
  "data-garden-id": COMPONENT_ID$w,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledCheckLabel",
  componentId: "sc-x7nr1-0"
})(["", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$w, props));
StyledCheckLabel.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$v = "forms.radio_hint";
var StyledRadioHint = styled_components_browser_esm_default(StyledHint).attrs({
  "data-garden-id": COMPONENT_ID$v,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledRadioHint",
  componentId: "sc-eo8twg-0"
})(["padding-", ":", ";", ";"], (props) => props.theme.rtl ? "right" : "left", (props) => math(`${props.theme.space.base} * 6px`), (props) => retrieveComponentStyles(COMPONENT_ID$v, props));
StyledRadioHint.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$u = "forms.checkbox_hint";
var StyledCheckHint = styled_components_browser_esm_default(StyledRadioHint).attrs({
  "data-garden-id": COMPONENT_ID$u,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledCheckHint",
  componentId: "sc-1kl8e8c-0"
})(["", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$u, props));
StyledCheckHint.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$t = "forms.radio";
var colorStyles$9 = (props) => {
  const SHADE = 600;
  const borderColor = getColor("neutralHue", SHADE - 300, props.theme);
  const backgroundColor = props.theme.colors.background;
  const iconColor = backgroundColor;
  const hoverBackgroundColor = getColor("primaryHue", SHADE, props.theme, 0.08);
  const hoverBorderColor = getColor("primaryHue", SHADE, props.theme);
  const focusBorderColor = hoverBorderColor;
  const activeBackgroundColor = getColor("primaryHue", SHADE, props.theme, 0.2);
  const activeBorderColor = focusBorderColor;
  const checkedBorderColor = focusBorderColor;
  const checkedBackgroundColor = checkedBorderColor;
  const checkedHoverBorderColor = getColor("primaryHue", SHADE + 100, props.theme);
  const checkedHoverBackgroundColor = checkedHoverBorderColor;
  const checkedActiveBorderColor = getColor("primaryHue", SHADE + 200, props.theme);
  const checkedActiveBackgroundColor = checkedActiveBorderColor;
  const disabledBackgroundColor = getColor("neutralHue", SHADE - 400, props.theme);
  return Ae(["& ~ ", "::before{border-color:", ";background-color:", ";}& ~ ", " > svg{color:", ";}& ~ ", ":hover::before{border-color:", ";background-color:", ";}", " & ~ ", ":active::before{border-color:", ";background-color:", ";}&:checked ~ ", "::before{border-color:", ";background-color:", ";}&:enabled:checked ~ ", ":hover::before{border-color:", ";background-color:", ";}&:enabled:checked ~ ", ":active::before{border-color:", ";background-color:", ";}&:disabled ~ ", "::before{border-color:transparent;background-color:", ";}"], StyledRadioLabel, borderColor, backgroundColor, StyledRadioLabel, iconColor, StyledRadioLabel, hoverBorderColor, hoverBackgroundColor, focusStyles({
    theme: props.theme,
    styles: {
      borderColor: focusBorderColor
    },
    selector: `&:focus-visible ~ ${StyledRadioLabel}::before, &[data-garden-focus-visible='true'] ~ ${StyledRadioLabel}::before`
  }), StyledRadioLabel, activeBorderColor, activeBackgroundColor, StyledRadioLabel, checkedBorderColor, checkedBackgroundColor, StyledRadioLabel, checkedHoverBorderColor, checkedHoverBackgroundColor, StyledRadioLabel, checkedActiveBorderColor, checkedActiveBackgroundColor, StyledRadioLabel, disabledBackgroundColor);
};
var sizeStyles$c = (props) => {
  const lineHeight = `${props.theme.space.base * 5}px`;
  const size = `${props.theme.space.base * 4}px`;
  const top = math(`(${lineHeight} - ${size}) / 2`);
  const iconSize = props.theme.iconSizes.sm;
  const iconPosition = math(`(${size} - ${iconSize}) / 2`);
  const iconTop = math(`${iconPosition} + ${top}`);
  const marginTop = `${props.theme.space.base * (props.isCompact ? 1 : 2)}px`;
  return Ae(["top:", ";width:", ";height:", ";& ~ ", "::before{top:", ";background-size:", ";width:", ";height:", ";box-sizing:border-box;}& ~ ", " > svg{top:", ";", ":", ";width:", ";height:", ";}&& ~ ", " ~ ", "{margin-top:", ";}"], top, size, size, StyledRadioLabel, top, props.theme.iconSizes.sm, size, size, StyledRadioLabel, iconTop, props.theme.rtl ? "right" : "left", iconPosition, iconSize, iconSize, StyledRadioLabel, StyledMessage, marginTop);
};
var StyledRadioInput = styled_components_browser_esm_default.input.attrs({
  "data-garden-id": COMPONENT_ID$t,
  "data-garden-version": "8.69.8",
  type: "radio"
}).withConfig({
  displayName: "StyledRadioInput",
  componentId: "sc-qsavpv-0"
})(["position:absolute;opacity:0;margin:0;& ~ ", "::before{position:absolute;", ":0;transition:border-color .25s ease-in-out,box-shadow .1s ease-in-out,background-color .25s ease-in-out,color .25s ease-in-out;border:", ";border-radius:50%;background-repeat:no-repeat;background-position:center;content:'';}& ~ ", " > svg{position:absolute;}", ";&:focus ~ ", "::before{outline:none;}& ~ ", ":active::before{transition:border-color 0.1s ease-in-out,background-color 0.1s ease-in-out,color 0.1s ease-in-out;}", ";&:disabled ~ ", "{cursor:default;}", ";"], StyledRadioLabel, (props) => props.theme.rtl ? "right" : "left", (props) => props.theme.borders.sm, StyledRadioLabel, (props) => sizeStyles$c(props), StyledRadioLabel, StyledRadioLabel, (props) => colorStyles$9(props), StyledRadioLabel, (props) => retrieveComponentStyles(COMPONENT_ID$t, props));
StyledRadioInput.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$s = "forms.checkbox";
var colorStyles$8 = (props) => {
  const SHADE = 600;
  const indeterminateBorderColor = getColor("primaryHue", SHADE, props.theme);
  const indeterminateBackgroundColor = indeterminateBorderColor;
  const indeterminateActiveBorderColor = getColor("primaryHue", SHADE + 100, props.theme);
  const indeterminateActiveBackgroundColor = indeterminateActiveBorderColor;
  const indeterminateDisabledBackgroundColor = getColor("neutralHue", SHADE - 400, props.theme);
  return Ae(["&:indeterminate ~ ", "::before{border-color:", ";background-color:", ";}&:enabled:indeterminate ~ ", ":active::before{border-color:", ";background-color:", ";}&:disabled:indeterminate ~ ", "::before{border-color:transparent;background-color:", ";}"], StyledCheckLabel, indeterminateBorderColor, indeterminateBackgroundColor, StyledCheckLabel, indeterminateActiveBorderColor, indeterminateActiveBackgroundColor, StyledCheckLabel, indeterminateDisabledBackgroundColor);
};
var StyledCheckInput = styled_components_browser_esm_default(StyledRadioInput).attrs({
  "data-garden-id": COMPONENT_ID$s,
  "data-garden-version": "8.69.8",
  type: "checkbox"
}).withConfig({
  displayName: "StyledCheckInput",
  componentId: "sc-176jxxe-0"
})(["& ~ ", "::before{border-radius:", ";}", ";", ";"], StyledCheckLabel, (props) => props.theme.borderRadii.md, (props) => colorStyles$8(props), (props) => retrieveComponentStyles(COMPONENT_ID$s, props));
StyledCheckInput.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$r = "forms.radio_message";
var StyledRadioMessage = styled_components_browser_esm_default(StyledMessage).attrs({
  "data-garden-id": COMPONENT_ID$r,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledRadioMessage",
  componentId: "sc-1pmi0q8-0"
})(["padding-", ":", ";", ";"], (props) => props.theme.rtl ? "right" : "left", (props) => math(`${props.theme.space.base} * 6px`), (props) => retrieveComponentStyles(COMPONENT_ID$r, props));
StyledRadioMessage.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$q = "forms.checkbox_message";
var StyledCheckMessage = styled_components_browser_esm_default(StyledRadioMessage).attrs({
  "data-garden-id": COMPONENT_ID$q,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledCheckMessage",
  componentId: "sc-s4p6kd-0"
})(["", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$q, props));
StyledCheckMessage.defaultProps = {
  theme: DEFAULT_THEME
};
var _path$m;
function _extends$p() {
  _extends$p = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$p.apply(this, arguments);
}
var SvgCheckSmFill = function SvgCheckSmFill2(props) {
  return React3.createElement("svg", _extends$p({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$m || (_path$m = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: 2,
    d: "M3 6l2 2 4-4"
  })));
};
var COMPONENT_ID$p = "forms.check_svg";
var StyledCheckSvg = styled_components_browser_esm_default(SvgCheckSmFill).attrs({
  "data-garden-id": COMPONENT_ID$p,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledCheckSvg",
  componentId: "sc-fvxetk-0"
})(["transition:opacity 0.25s ease-in-out;opacity:0;pointer-events:none;", ":checked ~ ", " > &{opacity:1;}", ":indeterminate ~ ", " > &{opacity:0;}", ";"], StyledCheckInput, StyledCheckLabel, StyledCheckInput, StyledCheckLabel, (props) => retrieveComponentStyles(COMPONENT_ID$p, props));
StyledCheckSvg.defaultProps = {
  theme: DEFAULT_THEME
};
var _path$l;
function _extends$o() {
  _extends$o = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$o.apply(this, arguments);
}
var SvgDashFill = function SvgDashFill2(props) {
  return React3.createElement("svg", _extends$o({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$l || (_path$l = React3.createElement("path", {
    stroke: "currentColor",
    strokeLinecap: "round",
    strokeWidth: 2,
    d: "M3 6h6"
  })));
};
var COMPONENT_ID$o = "forms.dash_svg";
var StyledDashSvg = styled_components_browser_esm_default(SvgDashFill).attrs({
  "data-garden-id": COMPONENT_ID$o,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledDashSvg",
  componentId: "sc-z3vq71-0"
})(["transition:opacity 0.25s ease-in-out;opacity:0;pointer-events:none;", ":indeterminate ~ ", " > &{opacity:1;}", ";"], StyledCheckInput, StyledCheckLabel, (props) => retrieveComponentStyles(COMPONENT_ID$o, props));
StyledDashSvg.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$n = "forms.file_upload";
var colorStyles$7 = (props) => {
  const baseColor = getColor("primaryHue", 600, props.theme);
  const hoverColor = getColor("primaryHue", 700, props.theme);
  const activeColor = getColor("primaryHue", 800, props.theme);
  const disabledBackgroundColor = getColor("neutralHue", 200, props.theme);
  const disabledForegroundColor = getColor("neutralHue", 400, props.theme);
  return Ae(["border-color:", ";background-color:", ";color:", ";&:hover{border-color:", ";background-color:", ";color:", ";}", " &:active{border-color:", ";background-color:", ";color:", ";}&[aria-disabled='true']{border-color:", ";background-color:", ";color:", ";}"], props.isDragging ? activeColor : getColor("neutralHue", 600, props.theme), props.isDragging && rgba(baseColor, 0.2), props.isDragging ? activeColor : baseColor, hoverColor, rgba(baseColor, 0.08), hoverColor, focusStyles({
    theme: props.theme,
    hue: baseColor
  }), activeColor, rgba(baseColor, 0.2), activeColor, disabledForegroundColor, disabledBackgroundColor, disabledForegroundColor);
};
var sizeStyles$b = (props) => {
  const marginTop = `${props.theme.space.base * (props.isCompact ? 1 : 2)}px`;
  const paddingHorizontal = `${props.isCompact ? 2 : 4}em`;
  const paddingVertical = math(`${props.theme.space.base * (props.isCompact ? 2.5 : 5)} - ${props.theme.borderWidths.sm}`);
  const fontSize = props.theme.fontSizes.md;
  const lineHeight = getLineHeight(props.theme.space.base * 5, fontSize);
  return Ae(["padding:", " ", ";min-width:4em;line-height:", ";font-size:", ";", ":not([hidden]) + &&,", " + &&,", " + &&,&& + ", ",&& + ", "{margin-top:", ";}"], paddingVertical, paddingHorizontal, lineHeight, fontSize, StyledLabel, StyledHint, StyledMessage, StyledHint, StyledMessage, marginTop);
};
var StyledFileUpload = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$n,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledFileUpload",
  componentId: "sc-1rodjgn-0"
})(["display:flex;align-items:center;justify-content:center;box-sizing:border-box;direction:", ";transition:border-color 0.25s ease-in-out,box-shadow 0.1s ease-in-out,background-color 0.25s ease-in-out,color 0.25s ease-in-out;border:dashed ", ";border-radius:", ";cursor:pointer;text-align:center;user-select:none;", ";&[aria-disabled='true']{cursor:default;}", ";", ";"], (props) => props.theme.rtl ? "rtl" : "ltr", (props) => props.theme.borderWidths.sm, (props) => props.theme.borderRadii.md, sizeStyles$b, colorStyles$7, (props) => retrieveComponentStyles(COMPONENT_ID$n, props));
StyledFileUpload.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$m = "forms.file.close";
var StyledFileClose = styled_components_browser_esm_default.button.attrs({
  "data-garden-id": COMPONENT_ID$m,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledFileClose",
  componentId: "sc-1m31jbf-0"
})(["display:flex;flex-shrink:0;align-items:center;justify-content:center;transition:opacity 0.25s ease-in-out;opacity:0.8;border:none;background:transparent;cursor:pointer;color:", ";appearance:none;&:hover{opacity:0.9;}&:focus{outline:none;}", ";"], (props) => props.theme.colors.foreground, (props) => retrieveComponentStyles(COMPONENT_ID$m, props));
StyledFileClose.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$l = "forms.file";
var colorStyles$6 = (props) => {
  let borderColor;
  let focusBorderColor;
  let foregroundColor;
  if (props.validation === "success") {
    borderColor = getColor("successHue", 600, props.theme);
    focusBorderColor = borderColor;
    foregroundColor = borderColor;
  } else if (props.validation === "error") {
    borderColor = getColor("dangerHue", 600, props.theme);
    focusBorderColor = borderColor;
    foregroundColor = borderColor;
  } else {
    borderColor = getColor("neutralHue", 300, props.theme);
    focusBorderColor = getColor("primaryHue", 600, props.theme);
    foregroundColor = props.theme.colors.foreground;
  }
  return Ae(["border-color:", ";color:", ";", ""], borderColor, foregroundColor, focusStyles({
    theme: props.theme,
    inset: props.focusInset,
    hue: focusBorderColor,
    styles: {
      borderColor: focusBorderColor
    }
  }));
};
var sizeStyles$a = (props) => {
  const size = `${props.theme.space.base * (props.isCompact ? 7 : 10)}px`;
  const spacing = `${props.theme.space.base * (props.isCompact ? 2 : 3)}px`;
  const fontSize = props.theme.fontSizes.md;
  const lineHeight = getLineHeight(props.theme.space.base * 5, fontSize);
  return `
    box-sizing: border-box;
    border: ${props.theme.borders.sm};
    border-radius: ${props.theme.borderRadii.md};
    padding: 0 ${spacing};
    height: ${size};
    line-height: ${lineHeight};
    font-size: ${fontSize};

    & > span {
      width: 100%;
    }

    & > ${StyledFileClose} {
      width: ${size};
      height: ${size};
      margin-${props.theme.rtl ? "left" : "right"}: -${spacing};
    }
  `;
};
var StyledFile = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$l,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledFile",
  componentId: "sc-195lzp1-0"
})(["display:flex;position:relative;flex-wrap:nowrap;align-items:center;transition:box-shadow 0.1s ease-in-out;", ";", ";& > span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}& > [role='progressbar']{position:absolute;bottom:0;left:0;transition:opacity 0.2s ease-in-out;margin:0;border-top-left-radius:0;border-top-right-radius:0;width:100%;& > div{border-top-", "-radius:0;}}& > [role='progressbar'][aria-valuenow='0'],& > [role='progressbar'][aria-valuenow='100']{opacity:0;}", ";"], sizeStyles$a, colorStyles$6, (props) => props.theme.rtl ? "right" : "left", (props) => retrieveComponentStyles(COMPONENT_ID$l, props));
StyledFile.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$k = "forms.file.delete";
var StyledFileDelete = styled_components_browser_esm_default(StyledFileClose).attrs({
  "data-garden-id": COMPONENT_ID$k,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledFileDelete",
  componentId: "sc-a8nnhx-0"
})(["color:", ";", ";"], (props) => getColor("dangerHue", 600, props.theme), (props) => retrieveComponentStyles(COMPONENT_ID$k, props));
StyledFileDelete.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$j = "forms.file.icon";
var StyledFileIcon = styled_components_browser_esm_default((_ref) => {
  let {
    children,
    isCompact,
    theme,
    ...props
  } = _ref;
  return import_react3.default.cloneElement(import_react3.Children.only(children), props);
}).attrs({
  "data-garden-id": COMPONENT_ID$j,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledFileIcon",
  componentId: "sc-7b3q0c-0"
})(["flex-shrink:0;width:", ";margin-", ":", "px;", ";"], (props) => props.isCompact ? props.theme.iconSizes.sm : props.theme.iconSizes.md, (props) => props.theme.rtl ? "left" : "right", (props) => props.theme.space.base * 2, (props) => retrieveComponentStyles(COMPONENT_ID$j, props));
StyledFileIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$i = "forms.file_list";
var StyledFileList = styled_components_browser_esm_default.ul.attrs({
  "data-garden-id": COMPONENT_ID$i,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledFileList",
  componentId: "sc-gbahjg-0"
})(["margin:0;padding:0;list-style:none;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$i, props));
StyledFileList.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$h = "forms.file_list.item";
var StyledFileListItem = styled_components_browser_esm_default.li.attrs({
  "data-garden-id": COMPONENT_ID$h,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledFileListItem",
  componentId: "sc-1ova3lo-0"
})(["&:not(:first-child),", " ~ ", " > &:first-child{margin-top:", "px;}", ";"], StyledFileUpload, StyledFileList, (props) => props.theme.space.base * 2, (props) => retrieveComponentStyles(COMPONENT_ID$h, props));
StyledFileListItem.defaultProps = {
  theme: DEFAULT_THEME
};
var _circle$3;
function _extends$n() {
  _extends$n = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$n.apply(this, arguments);
}
var SvgCircleSmFill$1 = function SvgCircleSmFill(props) {
  return React3.createElement("svg", _extends$n({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _circle$3 || (_circle$3 = React3.createElement("circle", {
    cx: 6,
    cy: 6,
    r: 2,
    fill: "currentColor"
  })));
};
var COMPONENT_ID$g = "forms.radio_svg";
var StyledRadioSvg = styled_components_browser_esm_default(SvgCircleSmFill$1).attrs({
  "data-garden-id": COMPONENT_ID$g,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledRadioSvg",
  componentId: "sc-1r1qtr1-0"
})(["transition:opacity 0.25s ease-in-out;opacity:0;", ":checked ~ ", " > &{opacity:1;}", ";"], StyledRadioInput, StyledRadioLabel, (props) => retrieveComponentStyles(COMPONENT_ID$g, props));
StyledRadioSvg.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$f = "forms.toggle_label";
var sizeStyles$9 = (props) => {
  const size = props.theme.space.base * 10;
  const padding = size + props.theme.space.base * 2;
  return Ae(["padding-", ":", "px;&[hidden]{padding-", ":", "px;}"], props.theme.rtl ? "right" : "left", padding, props.theme.rtl ? "right" : "left", size);
};
var StyledToggleLabel = styled_components_browser_esm_default(StyledCheckLabel).attrs({
  "data-garden-id": COMPONENT_ID$f,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledToggleLabel",
  componentId: "sc-e0asdk-0"
})(["", ";", ";"], (props) => sizeStyles$9(props), (props) => retrieveComponentStyles(COMPONENT_ID$f, props));
StyledToggleLabel.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$e = "forms.toggle_hint";
var StyledToggleHint = styled_components_browser_esm_default(StyledHint).attrs({
  "data-garden-id": COMPONENT_ID$e,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledToggleHint",
  componentId: "sc-nziggu-0"
})(["padding-", ":", ";", ";"], (props) => props.theme.rtl ? "right" : "left", (props) => math(`${props.theme.space.base} * 12px`), (props) => retrieveComponentStyles(COMPONENT_ID$e, props));
StyledToggleHint.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$d = "forms.toggle";
var colorStyles$5 = (props) => {
  const SHADE = 600;
  const backgroundColor = getColor("neutralHue", SHADE - 100, props.theme);
  const hoverBackgroundColor = getColor("neutralHue", SHADE, props.theme);
  const activeBackgroundColor = getColor("neutralHue", SHADE + 100, props.theme);
  return Ae(["& ~ ", "::before{background-color:", ";}&:enabled ~ ", ":hover::before{background-color:", ";}&:enabled ~ ", ":active::before{background-color:", ";}"], StyledToggleLabel, backgroundColor, StyledToggleLabel, hoverBackgroundColor, StyledToggleLabel, activeBackgroundColor);
};
var sizeStyles$8 = (props) => {
  const height = `${props.theme.space.base * 5}px`;
  const width = `${props.theme.space.base * 10}px`;
  const iconSize = props.theme.iconSizes.md;
  const iconPosition = math(`(${height} - ${iconSize}) / 2`);
  const checkedIconPosition = math(`${width} - ${iconSize} - ${iconPosition}`);
  return Ae(["top:0;width:", ";height:", ";& ~ ", "::before{width:", ";height:", ";}& ~ ", " > svg{top:", ";", ":", ";width:", ";height:", ";}&:checked ~ ", " > svg{", ":", ";}"], width, height, StyledToggleLabel, width, height, StyledToggleLabel, iconPosition, props.theme.rtl ? "right" : "left", iconPosition, iconSize, iconSize, StyledToggleLabel, props.theme.rtl ? "right" : "left", checkedIconPosition);
};
var StyledToggleInput = styled_components_browser_esm_default(StyledCheckInput).attrs({
  "data-garden-id": COMPONENT_ID$d,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledToggleInput",
  componentId: "sc-sgp47s-0"
})(["& ~ ", "::before{top:0;transition:box-shadow .1s ease-in-out,background-color .15s ease-in-out,color .25s ease-in-out;border:none;border-radius:100px;}", ";", ";", ";"], StyledToggleLabel, (props) => sizeStyles$8(props), (props) => colorStyles$5(props), (props) => retrieveComponentStyles(COMPONENT_ID$d, props));
StyledToggleInput.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$c = "forms.toggle_message";
var StyledToggleMessage = styled_components_browser_esm_default(StyledMessage).attrs({
  "data-garden-id": COMPONENT_ID$c,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledToggleMessage",
  componentId: "sc-13vuvl1-0"
})(["padding-", ":", ";& ", "{", ":", ";}", ";"], (props) => props.theme.rtl ? "right" : "left", (props) => math(`${props.theme.space.base} * 12px`), StyledMessageIcon, (props) => props.theme.rtl ? "right" : "left", (props) => math(`${props.theme.space.base} * 10px - ${props.theme.iconSizes.md}`), (props) => retrieveComponentStyles(COMPONENT_ID$c, props));
StyledToggleMessage.defaultProps = {
  theme: DEFAULT_THEME
};
var _circle$2;
function _extends$m() {
  _extends$m = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$m.apply(this, arguments);
}
var SvgCircleSmFill2 = function SvgCircleSmFill3(props) {
  return React3.createElement("svg", _extends$m({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _circle$2 || (_circle$2 = React3.createElement("circle", {
    cx: 8,
    cy: 8,
    r: 6,
    fill: "currentColor"
  })));
};
var COMPONENT_ID$b = "forms.toggle_svg";
var StyledToggleSvg = styled_components_browser_esm_default(SvgCircleSmFill2).attrs({
  "data-garden-id": COMPONENT_ID$b,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledToggleSvg",
  componentId: "sc-162xbyx-0"
})(["transition:all 0.15s ease-in-out;", ";"], (props) => retrieveComponentStyles(COMPONENT_ID$b, props));
StyledToggleSvg.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$a = "forms.select";
var colorStyles$4 = (props) => {
  const color = getColor("neutralHue", 700, props.theme);
  return Ae(["&:hover + ", ",&:focus + ", ",&:focus-visible + ", ",&[data-garden-focus-visible='true'] + ", "{color:", ";}"], StyledTextMediaFigure, StyledTextMediaFigure, StyledTextMediaFigure, StyledTextMediaFigure, color);
};
var sizeStyles$7 = (props) => {
  const padding = math(`${props.theme.iconSizes.md} + ${props.theme.space.base * 5}`);
  const iconVerticalPosition = `${props.theme.space.base * (props.isCompact ? 1.5 : 2.5) + 1}px`;
  const iconHorizontalPosition = `${props.theme.space.base * 3}px`;
  return Ae(["padding-", ":", ";& + ", "{top:", ";", ":", ";}"], props.theme.rtl ? "left" : "right", !props.isBare && padding, StyledTextMediaFigure, iconVerticalPosition, props.theme.rtl ? "left" : "right", iconHorizontalPosition);
};
var StyledSelect = styled_components_browser_esm_default(StyledTextInput).attrs({
  "data-garden-id": COMPONENT_ID$a,
  "data-garden-version": "8.69.8",
  as: "select"
}).withConfig({
  displayName: "StyledSelect",
  componentId: "sc-8xdxpt-0"
})(["cursor:pointer;text-overflow:ellipsis;", ";", ";&::-ms-expand{display:none;}&::-ms-value{background-color:transparent;color:inherit;}&:-moz-focusring{transition:none;text-shadow:0 0 0 ", ";color:transparent;}& + ", "{position:absolute;pointer-events:none;}"], (props) => sizeStyles$7(props), (props) => colorStyles$4(props), (props) => props.theme.colors.foreground, StyledTextMediaFigure);
StyledSelect.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$9 = "forms.select_wrapper";
var StyledSelectWrapper = styled_components_browser_esm_default(StyledTextFauxInput).attrs({
  "data-garden-id": COMPONENT_ID$9,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledSelectWrapper",
  componentId: "sc-i7b6hw-0"
})(["position:relative;padding:0;overflow:visible;& > ", "{border-color:transparent;background-color:transparent;", "{box-shadow:unset;}}"], StyledSelect, SELECTOR_FOCUS_VISIBLE);
StyledSelectWrapper.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$8 = "forms.range";
var thumbStyles = function(styles) {
  let modifier = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "";
  return `
    &${modifier}::-moz-range-thumb {
      ${styles}
    }

    &${modifier}::-ms-thumb {
      ${styles}
    }

    &${modifier}::-webkit-slider-thumb {
      ${styles}
    }
  `;
};
var trackStyles = function(styles) {
  let modifier = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "";
  return `
    &${modifier}::-moz-range-track {
      ${styles}
    }

    &${modifier}::-ms-track {
      ${styles}
    }

    &${modifier}::-webkit-slider-runnable-track {
      ${styles}
    }
  `;
};
var trackLowerStyles = function(styles) {
  let modifier = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "";
  return `
    &${modifier}::-moz-range-progress {
      ${styles}
    }

    &${modifier}::-ms-fill-lower {
      ${styles}
    }
  `;
};
var colorStyles$3 = (props) => {
  const SHADE = 600;
  const thumbBackgroundColor = getColor("primaryHue", SHADE, props.theme);
  const thumbBorderColor = thumbBackgroundColor;
  const thumbBoxShadow = props.theme.shadows.lg(math(`${props.theme.space.base} * 1px`), math(`${props.theme.space.base} * 2px`), getColor("neutralHue", SHADE + 200, props.theme, 0.24));
  const thumbFocusBoxShadow = getFocusBoxShadow({
    theme: props.theme
  });
  const thumbActiveBackgroundColor = getColor("primaryHue", SHADE + 100, props.theme);
  const thumbActiveBorderColor = thumbBorderColor;
  const thumbDisabledBackgroundColor = getColor("neutralHue", SHADE - 300, props.theme);
  const thumbDisabledBorderColor = thumbDisabledBackgroundColor;
  const thumbHoverBackgroundColor = thumbActiveBackgroundColor;
  const thumbHoverBorderColor = thumbHoverBackgroundColor;
  const trackBackgroundColor = getColor("neutralHue", SHADE - 400, props.theme);
  const trackLowerBackgroundColor = props.hasLowerTrack ? thumbBackgroundColor : "";
  const trackBackgroundImage = props.hasLowerTrack ? `linear-gradient(${trackLowerBackgroundColor}, ${trackLowerBackgroundColor})` : "";
  const trackDisabledLowerBackgroundColor = props.hasLowerTrack ? thumbDisabledBackgroundColor : "";
  const trackDisabledBackgroundImage = props.hasLowerTrack ? `linear-gradient(${trackDisabledLowerBackgroundColor}, ${trackDisabledLowerBackgroundColor})` : "";
  return Ae(["", " ", " ", " ", " ", " ", " ", " ", " ", ""], trackStyles(`
      background-color: ${trackBackgroundColor};
      background-image: ${trackBackgroundImage}; /* provide means for styling lower range on WebKit */
    `), thumbStyles(`
      border-color: ${thumbBorderColor};
      box-shadow: ${thumbBoxShadow};
      background-color: ${thumbBackgroundColor};
    `), trackLowerStyles(`
      background-color: ${trackLowerBackgroundColor};
    `), thumbStyles(`
        transition:
          border-color .25s ease-in-out,
          background-color .25s ease-in-out;
        border-color: ${thumbHoverBorderColor};
        background-color: ${thumbHoverBackgroundColor};
      `, ":hover"), thumbStyles(`
        box-shadow: ${thumbFocusBoxShadow};
      `, '[data-garden-focus-visible="true"]'), thumbStyles(`
        border-color: ${thumbActiveBorderColor};
        background-color: ${thumbActiveBackgroundColor};
      `, ":active"), trackStyles(`
        background-image: ${trackDisabledBackgroundImage};
      `, ":disabled"), thumbStyles(`
        border-color: ${thumbDisabledBorderColor};
        box-shadow: none;
        background-color: ${thumbDisabledBackgroundColor};
      `, ":disabled"), trackLowerStyles(`
        background-color: ${trackDisabledLowerBackgroundColor};
      `, ":disabled"));
};
var sizeStyles$6 = (props) => {
  const thumbSize = math(`${props.theme.space.base} * 5px`);
  const trackHeight = math(`${props.theme.space.base} * 1.5px`);
  const trackBorderRadius = trackHeight;
  const trackMargin = math(`(${thumbSize} - ${trackHeight}) / 2 + ${props.theme.shadowWidths.md}`);
  const thumbMargin = math(`(${trackHeight} - ${thumbSize}) / 2`);
  return Ae(["", ":not([hidden]) + &,", " + &,", " + &,& + ", ",& + ", "{margin-top:", ";}", ";", " ", ""], StyledLabel, StyledHint, StyledMessage, StyledHint, StyledMessage, math(`${props.theme.space.base} * 2px`), trackStyles(`
      margin: ${trackMargin} 0;
      border-radius: ${trackBorderRadius};
      height: ${trackHeight};
    `), thumbStyles(`
      margin: ${thumbMargin} 0; /* reset for IE */
      width: ${thumbSize};
      height: ${thumbSize};
    `), trackLowerStyles(`
      border-top-${props.theme.rtl ? "right" : "left"}-radius: ${trackBorderRadius};
      border-bottom-${props.theme.rtl ? "right" : "left"}-radius: ${trackBorderRadius};
      height: ${trackHeight};
    `));
};
var StyledRangeInput = styled_components_browser_esm_default.input.attrs((props) => ({
  "data-garden-id": COMPONENT_ID$8,
  "data-garden-version": "8.69.8",
  type: "range",
  style: {
    backgroundSize: props.hasLowerTrack && props.backgroundSize
  }
})).withConfig({
  displayName: "StyledRangeInput",
  componentId: "sc-1iv2yqp-0"
})(["appearance:none;direction:", ";margin:0;background-color:inherit;cursor:pointer;padding:0;width:100%;vertical-align:middle;", " &::-webkit-slider-container,&::-webkit-slider-runnable-track{background-size:inherit;}", ";", " ", ";&::-moz-focus-outer{border:0;}&::-ms-tooltip{display:none;}&:focus{outline:none;}&:disabled{cursor:default;}", ";"], (props) => props.theme.rtl && "rtl", (props) => trackStyles(`
      appearance: none;
      border-color: transparent; /* reset for IE */
      background-repeat: repeat-y;
      background-size: 0;
      background-position: ${props.theme.rtl ? "100% 100%" : "0% 0%"};
      width: 99.8%; /* fix for IE which cuts off the upper track's border radius */
      color: transparent; /* reset for IE */
      box-sizing: border-box; /* reset for IE */
    `), (props) => sizeStyles$6(props), (props) => thumbStyles(`
      appearance: none;
      transition: box-shadow .1s ease-in-out;
      border: ${props.theme.borders.md};
      border-radius: 100%;
      box-sizing: border-box;
    `), (props) => colorStyles$3(props), (props) => retrieveComponentStyles(COMPONENT_ID$8, props));
StyledRangeInput.defaultProps = {
  backgroundSize: "0%",
  hasLowerTrack: true,
  theme: DEFAULT_THEME
};
var COMPONENT_ID$7 = "forms.slider";
var StyledSlider = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$7,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledSlider",
  componentId: "sc-1di437a-0"
})(["display:block;position:relative;z-index:0;cursor:pointer;height:", ";&[aria-disabled='true']{cursor:default;}", ":not([hidden]) + &,", " + &,", " + &,& + ", ",& + ", "{margin-top:", ";}", ";"], (props) => math(`(${props.theme.space.base} * 5px) + (${props.theme.shadowWidths.md} * 2)`), StyledLabel, StyledHint, StyledMessage, StyledHint, StyledMessage, (props) => math(`${props.theme.space.base} * 2px`), (props) => retrieveComponentStyles(COMPONENT_ID$7, props));
StyledSlider.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$6 = "forms.slider_thumb";
var colorStyles$2 = (props) => {
  const SHADE = 600;
  const backgroundColor = getColor("primaryHue", SHADE, props.theme);
  const borderColor = backgroundColor;
  const boxShadow = props.theme.shadows.lg(math(`${props.theme.space.base} * 1px`), math(`${props.theme.space.base} * 2px`), getColor("neutralHue", SHADE + 200, props.theme, 0.24));
  const activeBackgroundColor = getColor("primaryHue", SHADE + 100, props.theme);
  const activeBorderColor = borderColor;
  const hoverBackgroundColor = activeBackgroundColor;
  const hoverBorderColor = hoverBackgroundColor;
  const disabledBackgroundColor = getColor("neutralHue", SHADE - 300, props.theme);
  const disabledBorderColor = disabledBackgroundColor;
  return Ae(["border-color:", ";box-shadow:", ";background-color:", ";&:hover,&[data-garden-hover='true']{border-color:", ";background-color:", ";}", " &:active,&[data-garden-active='true']{border-color:", ";background-color:", ";}&[aria-disabled='true']{border-color:", ";box-shadow:none;background-color:", ";}"], borderColor, boxShadow, backgroundColor, hoverBorderColor, hoverBackgroundColor, focusStyles({
    theme: props.theme
  }), activeBorderColor, activeBackgroundColor, disabledBorderColor, disabledBackgroundColor);
};
var sizeStyles$5 = (props) => {
  const size = math(`${props.theme.space.base} * 5px`);
  const marginTop = math(`${size} / -2`);
  return Ae(["margin-top:", ";width:", ";height:", ";"], marginTop, size, size);
};
var StyledSliderThumb = styled_components_browser_esm_default.div.attrs((props) => ({
  "data-garden-id": COMPONENT_ID$6,
  "data-garden-version": "8.69.8",
  "aria-disabled": props.isDisabled
})).withConfig({
  displayName: "StyledSliderThumb",
  componentId: "sc-yspbwa-0"
})(["appearance:none;position:absolute;top:50%;", ":", ";transition:border-color 0.25s ease-in-out,box-shadow 0.1s ease-in-out,background-color 0.25s ease-in-out;z-index:1;border:", ";border-radius:100%;cursor:inherit;box-sizing:border-box;font-size:0;", ";", ";", ";"], (props) => props.theme.rtl ? "right" : "left", (props) => math(`${props.position} * 1px`), (props) => props.theme.borders.md, (props) => sizeStyles$5(props), (props) => colorStyles$2(props), (props) => retrieveComponentStyles(COMPONENT_ID$6, props));
StyledSliderThumb.defaultProps = {
  position: 0,
  theme: DEFAULT_THEME
};
var COMPONENT_ID$5 = "forms.slider_track";
var colorStyles$1 = (props) => {
  const SHADE = 600;
  const backgroundColor = getColor("neutralHue", SHADE - 400, props.theme);
  const backgroundImageColor = getColor("primaryHue", SHADE, props.theme);
  const disabledBackgroundColor = getColor("neutralHue", SHADE - 300, props.theme);
  return Ae(["background-color:", ";background-image:linear-gradient(", ",", ");&[aria-disabled='true']{background-image:linear-gradient(", ",", ");}"], backgroundColor, backgroundImageColor, backgroundImageColor, disabledBackgroundColor, disabledBackgroundColor);
};
var sizeStyles$4 = (props) => {
  const height = math(`${props.theme.space.base} * 1.5px`);
  const backgroundPosition = math(`${props.backgroundPosition} * 1px`);
  const backgroundSize = math(`${props.backgroundSize} * 1px`);
  const borderRadius = height;
  const marginTop = math(`${height} / -2`);
  const padding = math(`${props.theme.space.base} * 2.5px`);
  return Ae(["margin-top:", ";border-radius:", ";background-position:", ";background-size:", ";padding:0 ", ";"], marginTop, borderRadius, backgroundPosition, backgroundSize, padding);
};
var StyledSliderTrack = styled_components_browser_esm_default.div.attrs((props) => ({
  "data-garden-id": COMPONENT_ID$5,
  "data-garden-version": "8.69.8",
  "aria-disabled": props.isDisabled
})).withConfig({
  displayName: "StyledSliderTrack",
  componentId: "sc-aw5m6g-0"
})(["position:absolute;top:50%;box-sizing:border-box;background-origin:content-box;background-repeat:repeat-y;width:100%;", ";", ";", ";"], (props) => sizeStyles$4(props), (props) => colorStyles$1(props), (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledSliderTrack.defaultProps = {
  backgroundSize: 0,
  backgroundPosition: 0,
  theme: DEFAULT_THEME
};
var COMPONENT_ID$4 = "forms.slider_track_rail";
var sizeStyles$3 = (props) => {
  const height = math(`${props.theme.space.base} * 1.5px`);
  const margin = math(`${props.theme.space.base} * 2.5px`);
  return Ae(["margin:0 ", " 0 ", ";height:", ";"], props.theme.rtl ? `-${margin}` : margin, props.theme.rtl ? margin : `-${margin}`, height);
};
var StyledSliderTrackRail = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$4,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledSliderTrackRail",
  componentId: "sc-1o5owbd-0"
})(["position:relative;", ";", ";"], (props) => sizeStyles$3(props), (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledSliderTrackRail.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "forms.tile_icon";
var sizeStyles$2 = (props) => {
  const iconSize = math(`${props.theme.iconSizes.md} * 2`);
  let position;
  let top;
  let horizontalValue;
  if (!props.isCentered) {
    position = "absolute";
    top = `${props.theme.space.base * 6}px`;
    horizontalValue = `left: ${props.theme.space.base * 5}px`;
    if (props.theme.rtl) {
      horizontalValue = `right: ${props.theme.space.base * 5}px`;
    }
  }
  return Ae(["position:", ";top:", ";", ";& > *{width:", ";height:", ";}"], position, top, horizontalValue, iconSize, iconSize);
};
var StyledTileIcon = styled_components_browser_esm_default.span.attrs({
  "data-garden-id": COMPONENT_ID$3,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledTileIcon",
  componentId: "sc-1wazhg4-0"
})(["display:block;transition:color 0.25s ease-in-out;text-align:center;line-height:0;", ";", ";"], (props) => sizeStyles$2(props), (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledTileIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "forms.tile";
var colorStyles = (props) => {
  const SHADE = 600;
  const iconColor = getColor("neutralHue", SHADE, props.theme);
  const color = getColor("neutralHue", SHADE + 200, props.theme);
  const borderColor = getColor("neutralHue", SHADE - 300, props.theme);
  const hoverBackgroundColor = getColor("primaryHue", SHADE, props.theme, 0.08);
  const hoverBorderColor = getColor("primaryHue", SHADE, props.theme);
  const focusBorderColor = hoverBorderColor;
  const activeBackgroundColor = getColor("primaryHue", SHADE, props.theme, 0.2);
  const activeBorderColor = focusBorderColor;
  const disabledBackgroundColor = getColor("neutralHue", SHADE - 500, props.theme);
  const disabledBorderColor = getColor("neutralHue", SHADE - 400, props.theme);
  const disabledColor = getColor("neutralHue", SHADE - 200, props.theme);
  const selectedBorderColor = focusBorderColor;
  const selectedBackgroundColor = selectedBorderColor;
  const selectedHoverBorderColor = getColor("primaryHue", SHADE + 100, props.theme);
  const selectedHoverBackgroundColor = selectedHoverBorderColor;
  const selectedActiveBorderColor = getColor("primaryHue", SHADE + 200, props.theme);
  const selectedActiveBackgroundColor = selectedActiveBorderColor;
  const selectedDisabledBackgroundColor = disabledBorderColor;
  return Ae(["border:", " ", ";border-color:", ";background-color:", ";color:", ";", "{color:", ";}&:hover:not([aria-disabled='true']){border-color:", ";background-color:", ";", "{color:", ";}}", " &:active:not([aria-disabled='true']){border-color:", ";background-color:", ";", "{color:", ";}}&[data-garden-selected='true']{border-color:", ";background-color:", ";color:", ";", "{color:", ";}}&[data-garden-selected='true']:not([aria-disabled='true']):hover{border-color:", ";background-color:", ";color:", ";", "{color:", ";}}&[data-garden-selected='true']:not([aria-disabled='true']):active{border-color:", ";background-color:", ";color:", ";", "{color:", ";}}&[aria-disabled='true']{border-color:", ";background-color:", ";color:", ";", "{color:", ";}}&[data-garden-selected='true'][aria-disabled='true']{background-color:", ";color:", ";", "{color:", ";}}"], props.theme.borders.sm, getColor("neutralHue", SHADE - 300, props.theme), borderColor, props.theme.colors.background, color, StyledTileIcon, iconColor, hoverBorderColor, hoverBackgroundColor, StyledTileIcon, color, focusStyles({
    theme: props.theme,
    hue: focusBorderColor,
    styles: {
      borderColor: focusBorderColor
    },
    selector: `&:focus-within`
  }), activeBorderColor, activeBackgroundColor, StyledTileIcon, color, selectedBorderColor, selectedBackgroundColor, props.theme.colors.background, StyledTileIcon, props.theme.colors.background, selectedHoverBorderColor, selectedHoverBackgroundColor, props.theme.colors.background, StyledTileIcon, props.theme.colors.background, selectedActiveBorderColor, selectedActiveBackgroundColor, props.theme.colors.background, StyledTileIcon, props.theme.colors.background, disabledBorderColor, disabledBackgroundColor, disabledColor, StyledTileIcon, disabledColor, selectedDisabledBackgroundColor, disabledColor, StyledTileIcon, disabledColor);
};
var StyledTile = styled_components_browser_esm_default.label.attrs((props) => ({
  "data-garden-id": COMPONENT_ID$2,
  "data-garden-version": "8.69.8",
  "data-garden-selected": props.isSelected
})).withConfig({
  displayName: "StyledTile",
  componentId: "sc-1jjvmxs-0"
})(["display:block;position:relative;transition:border-color .25s ease-in-out,box-shadow .1s ease-in-out,background-color .25s ease-in-out,color .25s ease-in-out;border-radius:", ";cursor:", ";padding:", "px;direction:", ";min-width:132px;word-break:break-word;", ";", ";"], (props) => props.theme.borderRadii.md, (props) => !props.isDisabled && "pointer", (props) => props.theme.space.base * 5, (props) => props.theme.rtl && "rtl", (props) => colorStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID$2, props));
StyledTile.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$1 = "forms.tile_description";
var sizeStyles$1 = (props) => {
  let marginDirection = "left";
  let marginValue;
  if (props.theme.rtl) {
    marginDirection = "right";
  }
  if (!props.isCentered) {
    marginValue = math(`(${props.theme.iconSizes.md} * 2) + ${props.theme.space.base * 5}px`);
  }
  return Ae(["margin-top:", "px;margin-", ":", ";"], props.theme.space.base, marginDirection, marginValue);
};
var StyledTileDescription = styled_components_browser_esm_default.span.attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledTileDescription",
  componentId: "sc-xfuu7u-0"
})(["display:block;text-align:", ";line-height:", ";font-size:", ";", ";", ";"], (props) => props.isCentered && "center", (props) => getLineHeight(props.theme.space.base * 4, props.theme.fontSizes.sm), (props) => props.theme.fontSizes.sm, (props) => sizeStyles$1(props), (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledTileDescription.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledTileInput = styled_components_browser_esm_default.input.withConfig({
  displayName: "StyledTileInput",
  componentId: "sc-1nq2m6q-0"
})(["position:absolute;border:0;clip:rect(1px,1px,1px,1px);padding:0;width:1px;height:1px;overflow:hidden;white-space:nowrap;"]);
StyledTileInput.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "forms.tile_label";
var sizeStyles = (props) => {
  let marginDirection = "left";
  let marginTop = `${props.theme.space.base * 2}px`;
  let marginValue;
  if (props.theme.rtl) {
    marginDirection = "right";
  }
  if (!props.isCentered) {
    marginValue = math(`(${props.theme.iconSizes.md} * 2) + ${props.theme.space.base * 5}px`);
    marginTop = "0";
  }
  return Ae(["margin-top:", ";margin-", ":", ";"], marginTop, marginDirection, marginValue);
};
var StyledTileLabel = styled_components_browser_esm_default.span.attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.69.8"
}).withConfig({
  displayName: "StyledTileLabel",
  componentId: "sc-1mypv27-0"
})(["display:block;text-align:", ";line-height:", ";font-size:", ";font-weight:", ";", ";", ";"], (props) => props.isCentered && "center", (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.theme.fontSizes.md, (props) => props.theme.fontWeights.semibold, (props) => sizeStyles(props), (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledTileLabel.defaultProps = {
  theme: DEFAULT_THEME
};
var Field = import_react3.default.forwardRef((props, ref) => {
  const [hasHint, setHasHint] = (0, import_react3.useState)(false);
  const [hasMessage, setHasMessage] = (0, import_react3.useState)(false);
  const [isLabelActive, setIsLabelActive] = (0, import_react3.useState)(false);
  const [isLabelHovered, setIsLabelHovered] = (0, import_react3.useState)(false);
  const multiThumbRangeRef = (0, import_react3.useRef)(null);
  const {
    getInputProps,
    getMessageProps,
    ...propGetters
  } = useField(props.id);
  const fieldProps = (0, import_react3.useMemo)(() => ({
    ...propGetters,
    getInputProps: function(options) {
      let describeOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
      return getInputProps(options, {
        ...describeOptions,
        isDescribed: hasHint,
        hasMessage
      });
    },
    getMessageProps: (options) => getMessageProps({
      role: "alert",
      ...options
    }),
    isLabelActive,
    setIsLabelActive,
    isLabelHovered,
    setIsLabelHovered,
    hasHint,
    setHasHint,
    hasMessage,
    setHasMessage,
    multiThumbRangeRef
  }), [propGetters, getInputProps, getMessageProps, isLabelActive, isLabelHovered, hasHint, hasMessage]);
  return import_react3.default.createElement(FieldContext.Provider, {
    value: fieldProps
  }, import_react3.default.createElement(StyledField, _extends$t({}, props, {
    ref
  })));
});
Field.displayName = "Field";
var FieldsetContext = (0, import_react3.createContext)(void 0);
var useFieldsetContext = () => {
  const fieldsetContext = (0, import_react3.useContext)(FieldsetContext);
  return fieldsetContext;
};
var LegendComponent = (0, import_react3.forwardRef)((props, ref) => {
  const fieldsetContext = useFieldsetContext();
  return import_react3.default.createElement(StyledLegend, _extends$t({}, props, fieldsetContext, {
    ref
  }));
});
LegendComponent.displayName = "Fieldset.Legend";
var Legend = LegendComponent;
var FieldsetComponent = (0, import_react3.forwardRef)((props, ref) => {
  const fieldsetContext = (0, import_react3.useMemo)(() => ({
    isCompact: props.isCompact
  }), [props.isCompact]);
  return import_react3.default.createElement(FieldsetContext.Provider, {
    value: fieldsetContext
  }, import_react3.default.createElement(StyledFieldset, _extends$t({}, props, {
    ref
  })));
});
FieldsetComponent.displayName = "Fieldset";
FieldsetComponent.propTypes = {
  isCompact: import_prop_types3.default.bool
};
var Fieldset = FieldsetComponent;
Fieldset.Legend = Legend;
var InputContext = (0, import_react3.createContext)(void 0);
var useInputContext = () => {
  return (0, import_react3.useContext)(InputContext);
};
var Hint = import_react3.default.forwardRef((props, ref) => {
  const {
    hasHint,
    setHasHint,
    getHintProps
  } = useFieldContext() || {};
  const type = useInputContext();
  (0, import_react3.useEffect)(() => {
    if (!hasHint && setHasHint) {
      setHasHint(true);
    }
    return () => {
      if (hasHint && setHasHint) {
        setHasHint(false);
      }
    };
  }, [hasHint, setHasHint]);
  let HintComponent;
  if (type === "checkbox") {
    HintComponent = StyledCheckHint;
  } else if (type === "radio") {
    HintComponent = StyledRadioHint;
  } else if (type === "toggle") {
    HintComponent = StyledToggleHint;
  } else {
    HintComponent = StyledHint;
  }
  let combinedProps = props;
  if (getHintProps) {
    combinedProps = getHintProps(combinedProps);
  }
  return import_react3.default.createElement(HintComponent, _extends$t({
    ref
  }, combinedProps));
});
Hint.displayName = "Hint";
var Label$1 = import_react3.default.forwardRef((props, ref) => {
  const fieldContext = useFieldContext();
  const fieldsetContext = useFieldsetContext();
  const type = useInputContext();
  let combinedProps = props;
  if (fieldContext) {
    combinedProps = fieldContext.getLabelProps(combinedProps);
    if (type === void 0) {
      const {
        setIsLabelActive,
        setIsLabelHovered,
        multiThumbRangeRef
      } = fieldContext;
      combinedProps = {
        ...combinedProps,
        onMouseUp: composeEventHandlers(props.onMouseUp, () => {
          setIsLabelActive(false);
        }),
        onMouseDown: composeEventHandlers(props.onMouseDown, () => {
          setIsLabelActive(true);
        }),
        onMouseEnter: composeEventHandlers(props.onMouseEnter, () => {
          setIsLabelHovered(true);
        }),
        onMouseLeave: composeEventHandlers(props.onMouseLeave, () => {
          setIsLabelHovered(false);
        }),
        onClick: composeEventHandlers(props.onClick, () => {
          multiThumbRangeRef.current && multiThumbRangeRef.current.focus();
        })
      };
    }
  }
  if (fieldsetContext) {
    combinedProps = {
      ...combinedProps,
      isRegular: combinedProps.isRegular === void 0 ? true : combinedProps.isRegular
    };
  }
  if (type === "radio") {
    return import_react3.default.createElement(StyledRadioLabel, _extends$t({
      ref
    }, combinedProps), import_react3.default.createElement(StyledRadioSvg, null), props.children);
  } else if (type === "checkbox") {
    const onLabelSelect = (e) => {
      const isFirefox = navigator.userAgent.toLowerCase().indexOf("firefox") > -1;
      if (fieldContext && isFirefox && e.target instanceof Element) {
        const inputId = e.target.getAttribute("for");
        if (!inputId)
          return;
        const input = document.getElementById(inputId);
        if (input && input.type === "checkbox") {
          if (e.shiftKey) {
            input.click();
            input.checked = true;
          }
          input.focus();
        }
      }
    };
    combinedProps = {
      ...combinedProps,
      onClick: composeEventHandlers(combinedProps.onClick, onLabelSelect)
    };
    return import_react3.default.createElement(StyledCheckLabel, _extends$t({
      ref
    }, combinedProps), import_react3.default.createElement(StyledCheckSvg, null), import_react3.default.createElement(StyledDashSvg, null), props.children);
  } else if (type === "toggle") {
    return import_react3.default.createElement(StyledToggleLabel, _extends$t({
      ref
    }, combinedProps), import_react3.default.createElement(StyledToggleSvg, null), props.children);
  }
  return import_react3.default.createElement(StyledLabel, _extends$t({
    ref
  }, combinedProps));
});
Label$1.displayName = "Label";
Label$1.propTypes = {
  isRegular: import_prop_types3.default.bool
};
var VALIDATION = ["success", "warning", "error"];
var FILE_VALIDATION = ["success", "error"];
var FILE_TYPE = ["pdf", "zip", "image", "document", "spreadsheet", "presentation", "generic"];
var Message = import_react3.default.forwardRef((_ref, ref) => {
  let {
    validation,
    validationLabel,
    children,
    ...props
  } = _ref;
  const {
    hasMessage,
    setHasMessage,
    getMessageProps
  } = useFieldContext() || {};
  const type = useInputContext();
  (0, import_react3.useEffect)(() => {
    if (!hasMessage && setHasMessage) {
      setHasMessage(true);
    }
    return () => {
      if (hasMessage && setHasMessage) {
        setHasMessage(false);
      }
    };
  }, [hasMessage, setHasMessage]);
  let MessageComponent;
  if (type === "checkbox") {
    MessageComponent = StyledCheckMessage;
  } else if (type === "radio") {
    MessageComponent = StyledRadioMessage;
  } else if (type === "toggle") {
    MessageComponent = StyledToggleMessage;
  } else {
    MessageComponent = StyledMessage;
  }
  let combinedProps = {
    validation,
    validationLabel,
    ...props
  };
  if (getMessageProps) {
    combinedProps = getMessageProps(combinedProps);
  }
  const ariaLabel = useText(Message, combinedProps, "validationLabel", validation, validation !== void 0);
  return import_react3.default.createElement(MessageComponent, _extends$t({
    ref
  }, combinedProps), validation && import_react3.default.createElement(StyledMessageIcon, {
    validation,
    "aria-label": ariaLabel
  }), children);
});
Message.displayName = "Message";
Message.propTypes = {
  validation: import_prop_types3.default.oneOf(VALIDATION),
  validationLabel: import_prop_types3.default.string
};
var Checkbox = import_react3.default.forwardRef((_ref, ref) => {
  let {
    indeterminate,
    children,
    ...props
  } = _ref;
  const fieldsetContext = useFieldsetContext();
  const fieldContext = useFieldContext();
  const inputRef = (inputElement) => {
    inputElement && (inputElement.indeterminate = indeterminate);
  };
  const combinedRef = (inputElement) => {
    [inputRef, ref].forEach((targetRef) => {
      if (targetRef) {
        if (typeof targetRef === "function") {
          targetRef(inputElement);
        } else {
          targetRef.current = inputElement;
        }
      }
    });
  };
  let combinedProps = {
    ref: combinedRef,
    ...props,
    ...fieldsetContext
  };
  if (fieldContext) {
    combinedProps = fieldContext.getInputProps(combinedProps);
  }
  return import_react3.default.createElement(InputContext.Provider, {
    value: "checkbox"
  }, import_react3.default.createElement(StyledCheckInput, combinedProps), children);
});
Checkbox.displayName = "Checkbox";
Checkbox.propTypes = {
  isCompact: import_prop_types3.default.bool,
  indeterminate: import_prop_types3.default.bool
};
var InputGroupContext = (0, import_react3.createContext)(void 0);
var useInputGroupContext = () => {
  return (0, import_react3.useContext)(InputGroupContext);
};
var Input = import_react3.default.forwardRef((_ref, ref) => {
  let {
    onSelect,
    ...props
  } = _ref;
  const fieldContext = useFieldContext();
  const inputGroupContext = useInputGroupContext();
  const onSelectHandler = props.readOnly ? composeEventHandlers(onSelect, (event) => {
    event.currentTarget.select();
  }) : onSelect;
  let combinedProps = {
    ref,
    onSelect: onSelectHandler,
    ...props
  };
  if (inputGroupContext) {
    combinedProps = {
      ...combinedProps,
      isCompact: inputGroupContext.isCompact || combinedProps.isCompact,
      focusInset: props.focusInset === void 0 ? true : props.focusInset
    };
  }
  if (fieldContext) {
    combinedProps = fieldContext.getInputProps(combinedProps);
  }
  return import_react3.default.createElement(StyledTextInput, combinedProps);
});
Input.propTypes = {
  isCompact: import_prop_types3.default.bool,
  isBare: import_prop_types3.default.bool,
  focusInset: import_prop_types3.default.bool,
  validation: import_prop_types3.default.oneOf(VALIDATION)
};
Input.displayName = "Input";
var Radio = import_react3.default.forwardRef((_ref, ref) => {
  let {
    children,
    ...props
  } = _ref;
  const fieldsetContext = useFieldsetContext();
  const fieldContext = useFieldContext();
  let combinedProps = {
    ref,
    ...props,
    ...fieldsetContext
  };
  if (fieldContext) {
    combinedProps = fieldContext.getInputProps(combinedProps);
  }
  return import_react3.default.createElement(InputContext.Provider, {
    value: "radio"
  }, import_react3.default.createElement(StyledRadioInput, combinedProps), children);
});
Radio.displayName = "Radio";
Radio.propTypes = {
  isCompact: import_prop_types3.default.bool
};
var Range = import_react3.default.forwardRef((_ref, ref) => {
  let {
    hasLowerTrack,
    min,
    max,
    step,
    ...props
  } = _ref;
  const [backgroundSize, setBackgroundSize] = (0, import_react3.useState)("0");
  const rangeRef = (0, import_react3.useRef)();
  const fieldContext = useFieldContext();
  const updateBackgroundWidthFromInput = (0, import_react3.useCallback)(
    (rangeTarget) => {
      let relativeMax = max;
      const {
        value
      } = rangeTarget;
      if (parseFloat(relativeMax) < parseFloat(min)) {
        relativeMax = 100;
      }
      const percentage = 100 * (value - min) / (relativeMax - min);
      setBackgroundSize(`${percentage}%`);
    },
    [max, min, step]
  );
  (0, import_react3.useEffect)(() => {
    updateBackgroundWidthFromInput(rangeRef.current);
  }, [rangeRef, updateBackgroundWidthFromInput, props.value]);
  const onChange = hasLowerTrack ? composeEventHandlers(props.onChange, (event) => {
    updateBackgroundWidthFromInput(event.target);
  }) : props.onChange;
  let combinedProps = {
    ref: react_merge_refs_esm_default([rangeRef, ref]),
    hasLowerTrack,
    min,
    max,
    step,
    backgroundSize,
    ...props,
    onChange
  };
  if (fieldContext) {
    combinedProps = fieldContext.getInputProps(combinedProps, {
      isDescribed: true
    });
  }
  return import_react3.default.createElement(StyledRangeInput, combinedProps);
});
Range.defaultProps = {
  hasLowerTrack: true,
  min: 0,
  max: 100,
  step: 1
};
Range.displayName = "Range";
var parseStyleValue = (value) => {
  return parseInt(value, 10) || 0;
};
var Textarea = import_react3.default.forwardRef((_ref, ref) => {
  let {
    minRows,
    maxRows,
    style,
    onChange,
    onSelect,
    ...props
  } = _ref;
  const fieldContext = useFieldContext();
  const textAreaRef = (0, import_react3.useRef)();
  const shadowTextAreaRef = (0, import_react3.useRef)(null);
  const [state, setState] = (0, import_react3.useState)({
    overflow: false,
    height: 0
  });
  const isControlled = props.value !== null && props.value !== void 0;
  const isAutoResizable = (minRows !== void 0 || maxRows !== void 0) && !props.isResizable;
  const calculateHeight = (0, import_react3.useCallback)(() => {
    if (!isAutoResizable) {
      return;
    }
    const textarea = textAreaRef.current;
    const computedStyle2 = window.getComputedStyle(textarea);
    const shadowTextArea = shadowTextAreaRef.current;
    shadowTextArea.style.width = computedStyle2.width;
    shadowTextArea.value = textarea.value || textarea.placeholder || " ";
    const boxSizing = computedStyle2.boxSizing;
    const padding = parseStyleValue(computedStyle2.paddingBottom) + parseStyleValue(computedStyle2.paddingTop);
    const border = parseStyleValue(computedStyle2.borderTopWidth) + parseStyleValue(computedStyle2.borderBottomWidth);
    const innerHeight = shadowTextArea.scrollHeight - padding;
    shadowTextArea.value = "x";
    const singleRowHeight = shadowTextArea.scrollHeight - padding;
    let outerHeight = innerHeight;
    if (minRows) {
      outerHeight = Math.max(Number(minRows) * singleRowHeight, outerHeight);
    }
    if (maxRows) {
      outerHeight = Math.min(Number(maxRows) * singleRowHeight, outerHeight);
    }
    outerHeight = Math.max(outerHeight, singleRowHeight);
    const updatedHeight = outerHeight + (boxSizing === "border-box" ? padding + border : 0);
    const overflow = Math.abs(outerHeight - innerHeight) <= 1;
    setState((prevState) => {
      if (updatedHeight > 0 && Math.abs((prevState.height || 0) - updatedHeight) > 1 || prevState.overflow !== overflow) {
        return {
          overflow,
          height: updatedHeight
        };
      }
      return prevState;
    });
  }, [maxRows, minRows, textAreaRef, isAutoResizable]);
  const onChangeHandler = (0, import_react3.useCallback)((e) => {
    if (!isControlled) {
      calculateHeight();
    }
    if (onChange) {
      onChange(e);
    }
  }, [calculateHeight, isControlled, onChange]);
  const theme = (0, import_react3.useContext)(Me);
  const environment = useDocument(theme);
  (0, import_react3.useEffect)(() => {
    if (!isAutoResizable) {
      return void 0;
    }
    if (environment) {
      const win = environment.defaultView || window;
      const resizeHandler = (0, import_lodash2.default)(calculateHeight);
      win.addEventListener("resize", resizeHandler);
      return () => {
        resizeHandler.cancel();
        win.removeEventListener("resize", resizeHandler);
      };
    }
    return void 0;
  }, [calculateHeight, isAutoResizable, environment]);
  (0, import_react3.useLayoutEffect)(() => {
    calculateHeight();
  });
  const computedStyle = {};
  if (isAutoResizable) {
    computedStyle.height = state.height;
    computedStyle.overflow = state.overflow ? "hidden" : void 0;
  }
  const onSelectHandler = props.readOnly ? composeEventHandlers(onSelect, (event) => {
    event.currentTarget.select();
  }) : onSelect;
  let combinedProps = {
    ref: react_merge_refs_esm_default([textAreaRef, ref]),
    rows: minRows,
    onChange: onChangeHandler,
    onSelect: onSelectHandler,
    style: {
      ...computedStyle,
      ...style
    },
    ...props
  };
  if (fieldContext) {
    combinedProps = fieldContext.getInputProps(combinedProps, {
      isDescribed: true
    });
  }
  return import_react3.default.createElement(import_react3.default.Fragment, null, import_react3.default.createElement(StyledTextarea, combinedProps), isAutoResizable && import_react3.default.createElement(StyledTextarea, {
    "aria-hidden": true,
    readOnly: true,
    isHidden: true,
    className: props.className,
    ref: shadowTextAreaRef,
    tabIndex: -1,
    isBare: props.isBare,
    isCompact: props.isCompact,
    style
  }));
});
Textarea.propTypes = {
  isCompact: import_prop_types3.default.bool,
  isBare: import_prop_types3.default.bool,
  focusInset: import_prop_types3.default.bool,
  isResizable: import_prop_types3.default.bool,
  minRows: import_prop_types3.default.number,
  maxRows: import_prop_types3.default.number,
  validation: import_prop_types3.default.oneOf(VALIDATION)
};
Textarea.displayName = "Textarea";
var Toggle = import_react3.default.forwardRef((_ref, ref) => {
  let {
    children,
    ...props
  } = _ref;
  const fieldsetContext = useFieldsetContext();
  const fieldContext = useFieldContext();
  let combinedProps = {
    ref,
    ...props,
    ...fieldsetContext
  };
  if (fieldContext) {
    combinedProps = fieldContext.getInputProps(combinedProps);
  }
  return import_react3.default.createElement(InputContext.Provider, {
    value: "toggle"
  }, import_react3.default.createElement(StyledToggleInput, combinedProps), children);
});
Toggle.displayName = "Toggle";
Toggle.propTypes = {
  isCompact: import_prop_types3.default.bool
};
var _path$k;
function _extends$l() {
  _extends$l = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$l.apply(this, arguments);
}
var SvgChevronDownStroke = function SvgChevronDownStroke2(props) {
  return React3.createElement("svg", _extends$l({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$k || (_path$k = React3.createElement("path", {
    fill: "currentColor",
    d: "M12.688 5.61a.5.5 0 01.69.718l-.066.062-5 4a.5.5 0 01-.542.054l-.082-.054-5-4a.5.5 0 01.55-.83l.074.05L8 9.359l4.688-3.75z"
  })));
};
var StartIconComponent = (props) => import_react3.default.createElement(StyledTextMediaFigure, _extends$t({
  position: "start"
}, props));
StartIconComponent.displayName = "FauxInput.StartIcon";
var StartIcon = StartIconComponent;
var EndIconComponent = (props) => import_react3.default.createElement(StyledTextMediaFigure, _extends$t({
  position: "end"
}, props));
EndIconComponent.displayName = "FauxInput.EndIcon";
var EndIcon = EndIconComponent;
var FauxInputComponent = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    onFocus,
    onBlur,
    disabled,
    readOnly,
    isFocused: controlledIsFocused,
    ...props
  } = _ref;
  const [isFocused, setIsFocused] = (0, import_react3.useState)(false);
  const onFocusHandler = composeEventHandlers(onFocus, () => {
    setIsFocused(true);
  });
  const onBlurHandler = composeEventHandlers(onBlur, () => {
    setIsFocused(false);
  });
  return import_react3.default.createElement(StyledTextFauxInput, _extends$t({
    onFocus: onFocusHandler,
    onBlur: onBlurHandler,
    isFocused: controlledIsFocused === void 0 ? isFocused : controlledIsFocused,
    isReadOnly: readOnly,
    isDisabled: disabled,
    tabIndex: disabled ? void 0 : 0
  }, props, {
    ref
  }));
});
FauxInputComponent.displayName = "FauxInput";
FauxInputComponent.propTypes = {
  isCompact: import_prop_types3.default.bool,
  isBare: import_prop_types3.default.bool,
  focusInset: import_prop_types3.default.bool,
  disabled: import_prop_types3.default.bool,
  readOnly: import_prop_types3.default.bool,
  validation: import_prop_types3.default.oneOf(VALIDATION),
  isFocused: import_prop_types3.default.bool,
  isHovered: import_prop_types3.default.bool
};
var FauxInput = FauxInputComponent;
FauxInput.EndIcon = EndIcon;
FauxInput.StartIcon = StartIcon;
var Select = import_react3.default.forwardRef((_ref, ref) => {
  let {
    disabled,
    isCompact,
    validation,
    focusInset,
    isBare,
    ...props
  } = _ref;
  const fieldContext = useFieldContext();
  let combinedProps = {
    disabled,
    isBare,
    isCompact,
    validation,
    focusInset,
    ref,
    ...props
  };
  if (fieldContext) {
    combinedProps = fieldContext.getInputProps(combinedProps, {
      isDescribed: true
    });
  }
  return import_react3.default.createElement(StyledSelectWrapper, {
    isCompact,
    isBare,
    validation,
    focusInset
  }, import_react3.default.createElement(StyledSelect, combinedProps), !isBare && import_react3.default.createElement(FauxInput.EndIcon, {
    isDisabled: disabled
  }, import_react3.default.createElement(SvgChevronDownStroke, null)));
});
Select.propTypes = {
  isCompact: import_prop_types3.default.bool,
  isBare: import_prop_types3.default.bool,
  focusInset: import_prop_types3.default.bool,
  validation: import_prop_types3.default.oneOf(VALIDATION)
};
Select.displayName = "Select";
var MIN = 0;
var MAX = 100;
var MultiThumbRange = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    min = MIN,
    max = MAX,
    minValue,
    maxValue,
    disabled,
    step,
    jump,
    onChange,
    onMouseDown,
    ...props
  } = _ref;
  const theme = (0, import_react3.useContext)(Me);
  const environment = useDocument(theme);
  const trackRailRef = (0, import_react3.useRef)(null);
  const minThumbRef = (0, import_react3.useRef)(null);
  const maxThumbRef = (0, import_react3.useRef)(null);
  const {
    getTrackProps,
    getMinThumbProps,
    getMaxThumbProps,
    trackRect,
    minValue: updatedMinValue,
    maxValue: updatedMaxValue
  } = useSlider({
    trackRef: trackRailRef,
    minThumbRef,
    maxThumbRef,
    min,
    max,
    minValue,
    maxValue,
    onChange,
    step,
    jump,
    disabled,
    rtl: theme.rtl,
    environment
  });
  const {
    onMouseDown: onSliderMouseDown,
    ...trackProps
  } = getTrackProps({
    onMouseDown
  });
  const fieldContext = useFieldContext();
  const {
    isLabelHovered,
    isLabelActive,
    multiThumbRangeRef
  } = fieldContext || {};
  (0, import_react3.useEffect)(() => {
    if (multiThumbRangeRef) {
      multiThumbRangeRef.current = minThumbRef.current;
    }
  }, [multiThumbRangeRef]);
  const minPosition = (updatedMinValue - min) / (max - min) * trackRect.width;
  const maxPosition = (updatedMaxValue - min) / (max - min) * trackRect.width;
  const sliderBackgroundSize = Math.abs(maxPosition) - Math.abs(minPosition);
  return import_react3.default.createElement(StyledSlider, _extends$t({
    ref,
    onMouseDown: onSliderMouseDown
  }, props), import_react3.default.createElement(StyledSliderTrack, {
    backgroundSize: sliderBackgroundSize,
    backgroundPosition: theme.rtl ? trackRect.width - maxPosition : minPosition,
    isDisabled: disabled
  }, import_react3.default.createElement(StyledSliderTrackRail, _extends$t({}, trackProps, {
    ref: trackRailRef
  }), import_react3.default.createElement(StyledSliderThumb, _extends$t({}, getMinThumbProps({
    "aria-label": updatedMinValue
  }), {
    isDisabled: disabled,
    position: minPosition,
    ref: minThumbRef,
    "data-garden-active": isLabelActive,
    "data-garden-hover": isLabelHovered
  })), import_react3.default.createElement(StyledSliderThumb, _extends$t({}, getMaxThumbProps({
    "aria-label": updatedMaxValue
  }), {
    isDisabled: disabled,
    position: maxPosition,
    ref: maxThumbRef
  })))));
});
MultiThumbRange.displayName = "MultiThumbRange";
MultiThumbRange.propTypes = {
  min: import_prop_types3.default.number,
  max: import_prop_types3.default.number,
  minValue: import_prop_types3.default.number,
  maxValue: import_prop_types3.default.number,
  step: import_prop_types3.default.number,
  jump: import_prop_types3.default.number,
  disabled: import_prop_types3.default.bool,
  onChange: import_prop_types3.default.func
};
MultiThumbRange.defaultProps = {
  min: MIN,
  max: MAX,
  step: 1
};
var TilesContext = (0, import_react3.createContext)(void 0);
var useTilesContext = () => {
  return (0, import_react3.useContext)(TilesContext);
};
var TileComponent = import_react3.default.forwardRef((_ref, ref) => {
  let {
    children,
    value,
    disabled,
    ...props
  } = _ref;
  const tilesContext = useTilesContext();
  const inputRef = (0, import_react3.useRef)(null);
  let inputProps;
  if (tilesContext) {
    inputProps = {
      name: tilesContext.name,
      checked: tilesContext.value === value,
      onChange: tilesContext.onChange
    };
  }
  return import_react3.default.createElement(StyledTile, _extends$t({
    ref,
    "aria-disabled": disabled,
    isDisabled: disabled,
    isSelected: tilesContext && tilesContext.value === value
  }, props), children, import_react3.default.createElement(StyledTileInput, _extends$t({}, inputProps, {
    disabled,
    value,
    type: "radio",
    ref: inputRef
  })));
});
TileComponent.displayName = "Tiles.Tile";
TileComponent.propTypes = {
  value: import_prop_types3.default.string,
  disabled: import_prop_types3.default.bool
};
var Tile = TileComponent;
var DescriptionComponent = (0, import_react3.forwardRef)((props, ref) => {
  const tilesContext = useTilesContext();
  return import_react3.default.createElement(StyledTileDescription, _extends$t({
    ref,
    isCentered: tilesContext && tilesContext.isCentered
  }, props));
});
DescriptionComponent.displayName = "Tiles.Description";
var Description = DescriptionComponent;
var IconComponent = (0, import_react3.forwardRef)((props, ref) => {
  const tileContext = useTilesContext();
  return import_react3.default.createElement(StyledTileIcon, _extends$t({
    ref,
    isCentered: tileContext && tileContext.isCentered
  }, props));
});
IconComponent.displayName = "Tiles.Icon";
var Icon = IconComponent;
var LabelComponent = (0, import_react3.forwardRef)((props, forwardedRef) => {
  const [title, setTitle] = (0, import_react3.useState)("");
  const ref = (0, import_react3.useRef)();
  const tilesContext = useTilesContext();
  (0, import_react3.useEffect)(() => {
    if (ref.current) {
      setTitle(ref.current.textContent || void 0);
    }
  }, [ref]);
  return import_react3.default.createElement(StyledTileLabel, _extends$t({
    ref: react_merge_refs_esm_default([ref, forwardedRef]),
    title,
    isCentered: tilesContext && tilesContext.isCentered
  }, props));
});
LabelComponent.displayName = "Tiles.Label";
var Label = LabelComponent;
var TilesComponent = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    onChange,
    value: controlledValue,
    name,
    isCentered,
    ...props
  } = _ref;
  const [value, setValue] = (0, import_react3.useState)(controlledValue);
  const handleOnChange = (0, import_react3.useCallback)(function() {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    setValue(args[0].target.value);
    if (onChange) {
      onChange(...args);
    }
  }, [onChange]);
  const selectedValue = getControlledValue(controlledValue, value);
  const tileContext = (0, import_react3.useMemo)(() => ({
    onChange: handleOnChange,
    value: selectedValue,
    name,
    isCentered
  }), [handleOnChange, selectedValue, name, isCentered]);
  return import_react3.default.createElement(TilesContext.Provider, {
    value: tileContext
  }, import_react3.default.createElement("div", _extends$t({
    ref,
    role: "radiogroup"
  }, props)));
});
TilesComponent.displayName = "Tiles";
TilesComponent.propTypes = {
  value: import_prop_types3.default.string,
  onChange: import_prop_types3.default.func,
  name: import_prop_types3.default.string.isRequired,
  isCentered: import_prop_types3.default.bool
};
TilesComponent.defaultProps = {
  isCentered: true
};
var Tiles = TilesComponent;
Tiles.Description = Description;
Tiles.Icon = Icon;
Tiles.Label = Label;
Tiles.Tile = Tile;
var InputGroup = import_react3.default.forwardRef((_ref, ref) => {
  let {
    isCompact,
    ...props
  } = _ref;
  const contextValue = (0, import_react3.useMemo)(() => ({
    isCompact
  }), [isCompact]);
  return import_react3.default.createElement(InputGroupContext.Provider, {
    value: contextValue
  }, import_react3.default.createElement(StyledInputGroup, _extends$t({
    ref,
    isCompact
  }, props)));
});
InputGroup.displayName = "InputGroup";
InputGroup.propTypes = {
  isCompact: import_prop_types3.default.bool
};
var FileUpload = import_react3.default.forwardRef((_ref, ref) => {
  let {
    disabled,
    ...props
  } = _ref;
  return import_react3.default.createElement(StyledFileUpload, _extends$t({
    ref,
    "aria-disabled": disabled
  }, props, {
    role: "button"
  }));
});
FileUpload.propTypes = {
  isDragging: import_prop_types3.default.bool,
  isCompact: import_prop_types3.default.bool,
  disabled: import_prop_types3.default.bool
};
FileUpload.displayName = "FileUpload";
var ItemComponent = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    ...props
  } = _ref;
  return import_react3.default.createElement(StyledFileListItem, _extends$t({}, props, {
    ref
  }));
});
ItemComponent.displayName = "FileList.Item";
var Item = ItemComponent;
var FileListComponent = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    ...props
  } = _ref;
  return import_react3.default.createElement(StyledFileList, _extends$t({}, props, {
    ref
  }));
});
FileListComponent.displayName = "FileList";
var FileList = FileListComponent;
FileList.Item = Item;
var _path$j;
function _extends$k() {
  _extends$k = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$k.apply(this, arguments);
}
var SvgXStroke$1 = function SvgXStroke(props) {
  return React3.createElement("svg", _extends$k({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$j || (_path$j = React3.createElement("path", {
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M3 9l6-6m0 6L3 3"
  })));
};
var _path$i;
function _extends$j() {
  _extends$j = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$j.apply(this, arguments);
}
var SvgXStroke2 = function SvgXStroke3(props) {
  return React3.createElement("svg", _extends$j({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$i || (_path$i = React3.createElement("path", {
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M3 13L13 3m0 10L3 3"
  })));
};
var FileContext = (0, import_react3.createContext)(void 0);
var useFileContext = () => {
  return (0, import_react3.useContext)(FileContext);
};
var CloseComponent = import_react3.default.forwardRef((props, ref) => {
  const fileContext = useFileContext();
  const onMouseDown = composeEventHandlers(
    props.onMouseDown,
    (event) => event.preventDefault()
  );
  const ariaLabel = useText(CloseComponent, props, "aria-label", "Close");
  return import_react3.default.createElement(StyledFileClose, _extends$t({
    ref,
    "aria-label": ariaLabel
  }, props, {
    type: "button",
    tabIndex: -1,
    onMouseDown
  }), fileContext && fileContext.isCompact ? import_react3.default.createElement(SvgXStroke$1, null) : import_react3.default.createElement(SvgXStroke2, null));
});
CloseComponent.displayName = "File.Close";
var Close = CloseComponent;
var _path$h;
function _extends$i() {
  _extends$i = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$i.apply(this, arguments);
}
var SvgTrashStroke$1 = function SvgTrashStroke(props) {
  return React3.createElement("svg", _extends$i({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$h || (_path$h = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M4.5 2.5V1c0-.3.2-.5.5-.5h2c.3 0 .5.2.5.5v1.5M2 2.5h8m-5.5 7V5m3 4.5V5m-5-.5V11c0 .3.2.5.5.5h6c.3 0 .5-.2.5-.5V4.5"
  })));
};
var _path$g;
function _extends$h() {
  _extends$h = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$h.apply(this, arguments);
}
var SvgTrashStroke2 = function SvgTrashStroke3(props) {
  return React3.createElement("svg", _extends$h({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$g || (_path$g = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M6.5 2.5V1c0-.3.2-.5.5-.5h2c.3 0 .5.2.5.5v1.5M3 2.5h10m-6.5 11v-8m3 8v-8m-6-1V15c0 .3.2.5.5.5h8c.3 0 .5-.2.5-.5V4.5"
  })));
};
var DeleteComponent = import_react3.default.forwardRef((props, ref) => {
  const fileContext = useFileContext();
  const onMouseDown = composeEventHandlers(
    props.onMouseDown,
    (event) => event.preventDefault()
  );
  const ariaLabel = useText(DeleteComponent, props, "aria-label", "Delete");
  return import_react3.default.createElement(StyledFileDelete, _extends$t({
    ref,
    "aria-label": ariaLabel
  }, props, {
    type: "button",
    tabIndex: -1,
    onMouseDown
  }), fileContext && fileContext.isCompact ? import_react3.default.createElement(SvgTrashStroke$1, null) : import_react3.default.createElement(SvgTrashStroke2, null));
});
DeleteComponent.displayName = "File.Delete";
var Delete = DeleteComponent;
var _path$f;
var _rect$1;
function _extends$g() {
  _extends$g = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$g.apply(this, arguments);
}
var SvgFilePdfStroke$1 = function SvgFilePdfStroke(props) {
  return React3.createElement("svg", _extends$g({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$f || (_path$f = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M10.5 3.21V11a.5.5 0 01-.5.5H2a.5.5 0 01-.5-.5V1A.5.5 0 012 .5h5.79a.5.5 0 01.35.15l2.21 2.21a.5.5 0 01.15.35zM7.5.5V3a.5.5 0 00.5.5h2.5m-7 6h5"
  })), _rect$1 || (_rect$1 = React3.createElement("rect", {
    width: 6,
    height: 3,
    x: 3,
    y: 5,
    fill: "currentColor",
    rx: 0.5,
    ry: 0.5
  })));
};
var _path$e;
function _extends$f() {
  _extends$f = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$f.apply(this, arguments);
}
var SvgFileZipStroke$1 = function SvgFileZipStroke(props) {
  return React3.createElement("svg", _extends$f({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$e || (_path$e = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M4.5.5v8m0-6h1m-2 1h1m0 1h1m-2 1h1m0 1h1m-2 1h1m6-4.29V11c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h5.79c.13 0 .26.05.35.15l2.21 2.21c.**********.15.35zM7.5.5V3c0 .*********.5h2.5"
  })));
};
var _path$d;
var _circle$1;
function _extends$e() {
  _extends$e = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$e.apply(this, arguments);
}
var SvgFileImageStroke$1 = function SvgFileImageStroke(props) {
  return React3.createElement("svg", _extends$e({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$d || (_path$d = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M10.5 3.21V11c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h5.79c.13 0 .26.05.35.15l2.21 2.21c.**********.15.35zM7.5.5V3c0 .*********.5h2.5m-7 6L5 8l1.5 1.5 1-1 1 1"
  })), _circle$1 || (_circle$1 = React3.createElement("circle", {
    cx: 8,
    cy: 6,
    r: 1,
    fill: "currentColor"
  })));
};
var _path$c;
function _extends$d() {
  _extends$d = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$d.apply(this, arguments);
}
var SvgFileDocumentStroke$1 = function SvgFileDocumentStroke(props) {
  return React3.createElement("svg", _extends$d({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$c || (_path$c = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M3.5 5.5h5m-5 2h5m-5 2h5m2-6.29V11c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h5.79c.13 0 .26.05.35.15l2.21 2.21c.**********.15.35zM7.5.5V3c0 .*********.5h2.5"
  })));
};
var _path$b;
function _extends$c() {
  _extends$c = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$c.apply(this, arguments);
}
var SvgFileSpreadsheetStroke$1 = function SvgFileSpreadsheetStroke(props) {
  return React3.createElement("svg", _extends$c({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$b || (_path$b = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M3.5 5.5h1m-1 2h1m-1 2h1m2-4h2m-2 2h2m-2 2h2m2-6.29V11c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h5.79c.13 0 .26.05.35.15l2.21 2.21c.**********.15.35zM7.5.5V3c0 .*********.5h2.5"
  })));
};
var _path$a;
function _extends$b() {
  _extends$b = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$b.apply(this, arguments);
}
var SvgFilePresentationStroke$1 = function SvgFilePresentationStroke(props) {
  return React3.createElement("svg", _extends$b({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$a || (_path$a = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    d: "M10.5 3.21V11c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h5.79c.13 0 .26.05.35.15l2.21 2.21c.**********.15.35zM6 9.5h2c.28 0 .5-.22.5-.5V8c0-.28-.22-.5-.5-.5H6c-.28 0-.5.22-.5.5v1c0 .*********.5zm-2-2h2c.28 0 .5-.22.5-.5V6c0-.28-.22-.5-.5-.5H4c-.28 0-.5.22-.5.5v1c0 .*********.5zm3.5-7V3c0 .*********.5h2.5"
  })));
};
var _path$9;
function _extends$a() {
  _extends$a = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$a.apply(this, arguments);
}
var SvgFileGenericStroke$1 = function SvgFileGenericStroke(props) {
  return React3.createElement("svg", _extends$a({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$9 || (_path$9 = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    d: "M10.5 3.21V11c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h5.79c.13 0 .26.05.35.15l2.21 2.21c.**********.15.35zM7.5.5V3c0 .*********.5h2.5"
  })));
};
var _g;
function _extends$9() {
  _extends$9 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$9.apply(this, arguments);
}
var SvgCheckCircleStroke2 = function SvgCheckCircleStroke3(props) {
  return React3.createElement("svg", _extends$9({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _g || (_g = React3.createElement("g", {
    fill: "none",
    stroke: "currentColor"
  }, React3.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3.5 6l2 2L9 4.5"
  }), React3.createElement("circle", {
    cx: 6,
    cy: 6,
    r: 5.5
  }))));
};
var _path$8;
function _extends$8() {
  _extends$8 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$8.apply(this, arguments);
}
var SvgFileErrorStroke$1 = function SvgFileErrorStroke(props) {
  return React3.createElement("svg", _extends$8({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path$8 || (_path$8 = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M10.5 3.21V11c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h5.79c.13 0 .26.05.35.15l2.21 2.21c.**********.15.35zM7.5.5V3c0 .*********.5h2.5M4 9.5l4-4m0 4l-4-4"
  })));
};
var _path$7;
var _rect;
function _extends$7() {
  _extends$7 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$7.apply(this, arguments);
}
var SvgFilePdfStroke2 = function SvgFilePdfStroke3(props) {
  return React3.createElement("svg", _extends$7({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$7 || (_path$7 = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M14.5 4.2V15a.5.5 0 01-.5.5H2a.5.5 0 01-.5-.5V1A.5.5 0 012 .5h8.85a.5.5 0 01.36.15l3.15 3.2a.5.5 0 01.14.35zm-10 8.3h7m-7-2h7m-1-10V4a.5.5 0 00.5.5h3.5"
  })), _rect || (_rect = React3.createElement("rect", {
    width: 8,
    height: 2,
    x: 4,
    y: 7,
    fill: "currentColor",
    rx: 0.5,
    ry: 0.5
  })));
};
var _path$6;
function _extends$6() {
  _extends$6 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$6.apply(this, arguments);
}
var SvgFileZipStroke2 = function SvgFileZipStroke3(props) {
  return React3.createElement("svg", _extends$6({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$6 || (_path$6 = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M6.5.5v11M5 2.5h1.5m0 1H8m-3 1h1.5m0 1H8m-3 1h1.5m0 1H8m-3 1h1.5m0 1H8m-3 1h1.5m8-6.3V15c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h8.85c.13 0 .26.05.36.15l3.15 3.2c.**********.14.35zm-4-3.7V4c0 .*********.5h3.5"
  })));
};
var _path$5;
var _circle;
function _extends$5() {
  _extends$5 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$5.apply(this, arguments);
}
var SvgFileImageStroke2 = function SvgFileImageStroke3(props) {
  return React3.createElement("svg", _extends$5({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$5 || (_path$5 = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M14.5 4.2V15c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h8.85c.13 0 .26.05.36.15l3.15 3.2c.**********.14.35zm-4-3.7V4c0 .*********.5h3.5m-11 9l2.65-2.65c.2-.2.51-.2.71 0l1.79 1.79c.2.2.51.2.71 0l.79-.79c.2-.2.51-.2.71 0l1.65 1.65"
  })), _circle || (_circle = React3.createElement("circle", {
    cx: 10.5,
    cy: 8.5,
    r: 1.5,
    fill: "currentColor"
  })));
};
var _path$4;
function _extends$4() {
  _extends$4 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$4.apply(this, arguments);
}
var SvgFileDocumentStroke2 = function SvgFileDocumentStroke3(props) {
  return React3.createElement("svg", _extends$4({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$4 || (_path$4 = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M4.5 7.5h7m-7 1.97h7m-7 2h7m3-7.27V15c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h8.85c.13 0 .26.05.36.15l3.15 3.2c.**********.14.35zm-4-3.7V4c0 .*********.5h3.5"
  })));
};
var _path$3;
function _extends$3() {
  _extends$3 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$3.apply(this, arguments);
}
var SvgFileSpreadsheetStroke2 = function SvgFileSpreadsheetStroke3(props) {
  return React3.createElement("svg", _extends$3({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$3 || (_path$3 = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M4.5 7.5h2m-2 2h2m-2 2h2m2-4h3m-3 2h3m-3 2h3m3-7.3V15c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h8.85c.13 0 .26.05.36.15l3.15 3.2c.**********.14.35zm-4-3.7V4c0 .*********.5h3.5"
  })));
};
var _path$2;
function _extends$2() {
  _extends$2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$2.apply(this, arguments);
}
var SvgFilePresentationStroke2 = function SvgFilePresentationStroke3(props) {
  return React3.createElement("svg", _extends$2({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$2 || (_path$2 = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    d: "M14.5 4.2V15c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h8.85c.13 0 .26.05.36.15l3.15 3.2c.**********.14.35zm-4-3.7V4c0 .*********.5h3.5M7 9.5h4c.28 0 .5.22.5.5v3c0 .28-.22.5-.5.5H7c-.28 0-.5-.22-.5-.5v-3c0-.28.22-.5.5-.5zm-.5 2H5c-.28 0-.5-.22-.5-.5V8c0-.28.22-.5.5-.5h4c.28 0 .5.22.5.5v1.5"
  })));
};
var _path$1;
function _extends$1() {
  _extends$1 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends$1.apply(this, arguments);
}
var SvgFileGenericStroke2 = function SvgFileGenericStroke3(props) {
  return React3.createElement("svg", _extends$1({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path$1 || (_path$1 = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    d: "M14.5 4.2V15c0 .28-.22.5-.5.5H2c-.28 0-.5-.22-.5-.5V1c0-.28.22-.5.5-.5h8.85c.13 0 .26.05.36.15l3.15 3.2c.**********.14.35zm-4-3.7V4c0 .*********.5h3.5"
  })));
};
var _path;
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var SvgFileErrorStroke2 = function SvgFileErrorStroke3(props) {
  return React3.createElement("svg", _extends({
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 16,
    focusable: "false",
    viewBox: "0 0 16 16",
    "aria-hidden": "true"
  }, props), _path || (_path = React3.createElement("path", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    d: "M14.5 4.205V15a.5.5 0 01-.5.5H2a.5.5 0 01-.5-.5V1A.5.5 0 012 .5h8.853a.5.5 0 01.356.15l3.148 3.204a.5.5 0 01.143.35zM10.5.5V4a.5.5 0 00.5.5h3.5m-9 8l5-5m0 5l-5-5"
  })));
};
var fileIconsDefault = {
  pdf: import_react3.default.createElement(SvgFilePdfStroke2, null),
  zip: import_react3.default.createElement(SvgFileZipStroke2, null),
  image: import_react3.default.createElement(SvgFileImageStroke2, null),
  document: import_react3.default.createElement(SvgFileDocumentStroke2, null),
  spreadsheet: import_react3.default.createElement(SvgFileSpreadsheetStroke2, null),
  presentation: import_react3.default.createElement(SvgFilePresentationStroke2, null),
  generic: import_react3.default.createElement(SvgFileGenericStroke2, null),
  success: import_react3.default.createElement(SvgCheckCircleStroke$1, null),
  error: import_react3.default.createElement(SvgFileErrorStroke2, null)
};
var fileIconsCompact = {
  pdf: import_react3.default.createElement(SvgFilePdfStroke$1, null),
  zip: import_react3.default.createElement(SvgFileZipStroke$1, null),
  image: import_react3.default.createElement(SvgFileImageStroke$1, null),
  document: import_react3.default.createElement(SvgFileDocumentStroke$1, null),
  spreadsheet: import_react3.default.createElement(SvgFileSpreadsheetStroke$1, null),
  presentation: import_react3.default.createElement(SvgFilePresentationStroke$1, null),
  generic: import_react3.default.createElement(SvgFileGenericStroke$1, null),
  success: import_react3.default.createElement(SvgCheckCircleStroke2, null),
  error: import_react3.default.createElement(SvgFileErrorStroke$1, null)
};
var FileComponent = (0, import_react3.forwardRef)((_ref, ref) => {
  let {
    children,
    type,
    isCompact,
    focusInset,
    validation,
    ...props
  } = _ref;
  const fileContextValue = (0, import_react3.useMemo)(() => ({
    isCompact
  }), [isCompact]);
  const validationType = validation || type;
  return import_react3.default.createElement(FileContext.Provider, {
    value: fileContextValue
  }, import_react3.default.createElement(StyledFile, _extends$t({}, props, {
    isCompact,
    focusInset,
    validation,
    ref
  }), validationType && import_react3.default.createElement(StyledFileIcon, {
    isCompact
  }, isCompact ? fileIconsCompact[validationType] : fileIconsDefault[validationType]), import_react3.Children.map(children, (child) => typeof child === "string" ? import_react3.default.createElement("span", null, child) : child)));
});
FileComponent.displayName = "File";
FileComponent.propTypes = {
  focusInset: import_prop_types3.default.bool,
  isCompact: import_prop_types3.default.bool,
  type: import_prop_types3.default.oneOf(FILE_TYPE),
  validation: import_prop_types3.default.oneOf(FILE_VALIDATION)
};
var File = FileComponent;
File.Close = Close;
File.Delete = Delete;
var MediaInput = import_react3.default.forwardRef((_ref, ref) => {
  let {
    start,
    end,
    disabled,
    isCompact,
    isBare,
    focusInset,
    readOnly,
    validation,
    wrapperProps = {},
    wrapperRef,
    onSelect,
    ...props
  } = _ref;
  const fieldContext = useFieldContext();
  const inputRef = (0, import_react3.useRef)();
  const [isFocused, setIsFocused] = (0, import_react3.useState)(false);
  const [isHovered, setIsHovered] = (0, import_react3.useState)(false);
  const {
    onClick,
    onFocus,
    onBlur,
    onMouseOver,
    onMouseOut,
    ...otherWrapperProps
  } = wrapperProps;
  const onFauxInputClickHandler = composeEventHandlers(onClick, () => {
    inputRef.current && inputRef.current.focus();
  });
  const onFauxInputFocusHandler = composeEventHandlers(onFocus, () => {
    setIsFocused(true);
  });
  const onFauxInputBlurHandler = composeEventHandlers(onBlur, () => {
    setIsFocused(false);
  });
  const onFauxInputMouseOverHandler = composeEventHandlers(onMouseOver, () => {
    setIsHovered(true);
  });
  const onFauxInputMouseOutHandler = composeEventHandlers(onMouseOut, () => {
    setIsHovered(false);
  });
  const onSelectHandler = readOnly ? composeEventHandlers(onSelect, (event) => {
    event.currentTarget.select();
  }) : onSelect;
  let combinedProps = {
    disabled,
    readOnly,
    ref: react_merge_refs_esm_default([inputRef, ref]),
    onSelect: onSelectHandler,
    ...props
  };
  let isLabelHovered;
  if (fieldContext) {
    combinedProps = fieldContext.getInputProps(combinedProps, {
      isDescribed: true
    });
    isLabelHovered = fieldContext.isLabelHovered;
  }
  return import_react3.default.createElement(FauxInput, _extends$t({
    tabIndex: null,
    onClick: onFauxInputClickHandler,
    onFocus: onFauxInputFocusHandler,
    onBlur: onFauxInputBlurHandler,
    onMouseOver: onFauxInputMouseOverHandler,
    onMouseOut: onFauxInputMouseOutHandler,
    disabled,
    isFocused,
    isHovered: isHovered || isLabelHovered,
    isCompact,
    isBare,
    focusInset,
    readOnly,
    validation,
    mediaLayout: true
  }, otherWrapperProps, {
    ref: wrapperRef
  }), start && import_react3.default.createElement(FauxInput.StartIcon, {
    isDisabled: disabled,
    isFocused,
    isHovered: isHovered || isLabelHovered
  }, start), import_react3.default.createElement(StyledTextMediaInput, combinedProps), end && import_react3.default.createElement(FauxInput.EndIcon, {
    isDisabled: disabled,
    isFocused,
    isHovered: isHovered || isLabelHovered
  }, end));
});
MediaInput.propTypes = {
  isCompact: import_prop_types3.default.bool,
  isBare: import_prop_types3.default.bool,
  focusInset: import_prop_types3.default.bool,
  validation: import_prop_types3.default.oneOf(VALIDATION),
  start: import_prop_types3.default.node,
  end: import_prop_types3.default.node,
  wrapperProps: import_prop_types3.default.object,
  wrapperRef: import_prop_types3.default.any
};
MediaInput.displayName = "MediaInput";

export {
  Field,
  Fieldset,
  Hint,
  Label$1,
  VALIDATION,
  Message,
  Checkbox,
  Input,
  Radio,
  Range,
  Textarea,
  Toggle,
  FauxInput,
  Select,
  MultiThumbRange,
  Tiles,
  InputGroup,
  FileUpload,
  FileList,
  File,
  MediaInput
};
//# sourceMappingURL=chunk-AHMT5GAZ.js.map
