import {
  DEFAULT_THEME,
  getColor,
  getLineHeight,
  retrieveComponentStyles,
  rgba,
  useDocument,
  useText
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  Ae,
  Me,
  We,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-loaders/dist/index.esm.js
var import_react2 = __toESM(require_react());
var import_prop_types2 = __toESM(require_prop_types());

// node_modules/@zendeskgarden/container-schedule/dist/index.esm.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var useSchedule = function(_temp) {
  let {
    duration = 1250,
    delayMS = 750,
    loop = true
  } = _temp === void 0 ? {} : _temp;
  const [elapsed, setElapsed] = (0, import_react.useState)(0);
  const [delayComplete, setDelayComplete] = (0, import_react.useState)(false);
  (0, import_react.useLayoutEffect)(() => {
    let raf;
    let start;
    let loopTimeout;
    let destroyed = false;
    const tick = () => {
      if (destroyed) {
        return;
      }
      raf = requestAnimationFrame(performAnimationFrame);
    };
    const performAnimationFrame = () => {
      setElapsed(Date.now() - start);
      tick();
    };
    const onStart = () => {
      if (destroyed) {
        return;
      }
      loopTimeout = setTimeout(() => {
        cancelAnimationFrame(raf);
        setElapsed(Date.now() - start);
        if (loop)
          onStart();
      }, duration);
      start = Date.now();
      setDelayComplete(true);
      tick();
    };
    const renderingDelayTimeout = setTimeout(onStart, delayMS);
    return () => {
      destroyed = true;
      clearTimeout(renderingDelayTimeout);
      clearTimeout(loopTimeout);
      cancelAnimationFrame(raf);
    };
  }, [duration, delayMS, loop]);
  return {
    elapsed: Math.min(1, elapsed / duration),
    delayMS,
    delayComplete
  };
};
var ScheduleContainer = (_ref) => {
  let {
    children,
    render = children,
    ...props
  } = _ref;
  return import_react.default.createElement(import_react.default.Fragment, null, render(useSchedule(props)));
};
ScheduleContainer.defaultProps = {
  duration: 1250,
  delayMS: 750,
  loop: true
};
ScheduleContainer.propTypes = {
  children: import_prop_types.default.func,
  render: import_prop_types.default.func,
  duration: import_prop_types.default.number,
  loop: import_prop_types.default.bool,
  delayMS: import_prop_types.default.number
};

// node_modules/@zendeskgarden/react-loaders/dist/index.esm.js
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var dotOneKeyframes = We(["0%{transform:translate(0,5px);}3%{transform:translate(1px,-5px);}6%{transform:translate(3px,-15px);}8%{transform:translate(5px,-18px);}9%{transform:translate(7px,-21px);}11%{transform:translate(8px,-22px);}13%{transform:translate(9px,-23px);}16%{transform:translate(12px,-25px);}18%{transform:translate(13px,-26px);}23%{transform:translate(18px,-26px);}24%{transform:translate(19px,-25px);}28%{transform:translate(22px,-23px);}31%{transform:translate(24px,-21px);}33%{transform:translate(26px,-18px);}34%{transform:translate(28px,-14px);}36%{transform:translate(29px,-12px);}38%{transform:translate(30px,-5px);}39%{transform:translate(31px,5px);}54%{transform:translate(31px,3px);}59%{transform:translate(33px);}61%{transform:translate(43px);}63%{transform:translate(48px);}64%{transform:translate(51px);}66%{transform:translate(53px);}68%{transform:translate(55px);}69%{transform:translate(57px);}76%{transform:translate(60px);}81%{transform:translate(61px);}83%,100%{transform:translate(62px);}"]);
var dotTwoKeyframes = We(["4%{transform:translate(0);}6%{transform:translate(-1px);}8%{transform:translate(-2px);}9%{transform:translate(-5px);}11%{transform:translate(-7px);}13%{transform:translate(-12px);}14%{transform:translate(-17px);}16%{transform:translate(-19px);}18%{transform:translate(-22px);}19%{transform:translate(-25px);}21%{transform:translate(-26px);}23%{transform:translate(-27px);}24%{transform:translate(-28px);}26%{transform:translate(-29px);}29%{transform:translate(-30px);}33%,89%{transform:translate(-31px);}91%{transform:translate(-31px,1px);}94%{transform:translate(-31px,2px);}98%{transform:translate(-31px,3px);}99%{transform:translate(-31px,4px);}100%{transform:translate(-31px,5px);}"]);
var dotThreeKeyframes = We(["39%{transform:translate(0);}44%{transform:translate(0,1px);}46%{transform:translate(0,2px);}48%{transform:translate(0,3px);}49%{transform:translate(0,4px);}51%{transform:translate(0,5px);}53%{transform:translate(-1px,-6px);}54%{transform:translate(-2px,-13px);}56%{transform:translate(-3px,-15px);}58%{transform:translate(-5px,-19px);}59%{transform:translate(-7px,-21px);}61%{transform:translate(-8px,-22px);}63%{transform:translate(-9px,-24px);}64%{transform:translate(-11px,-25px);}66%{transform:translate(-12px,-26px);}74%{transform:translate(-19px,-26px);}76%{transform:translate(-20px,-25px);}78%{transform:translate(-22px,-24px);}81%{transform:translate(-24px,-21px);}83%{transform:translate(-26px,-19px);}84%{transform:translate(-28px,-15px);}86%{transform:translate(-29px,-13px);}88%{transform:translate(-30px,-6px);}89%{transform:translate(-31px,5px);}91%{transform:translate(-31px,4px);}93%{transform:translate(-31px,3px);}94%{transform:translate(-31px,2px);}98%{transform:translate(-31px,1px);}100%{transform:translate(-31px);}"]);
var StyledDotsCircle = styled_components_browser_esm_default.circle.attrs({
  cy: 36,
  r: 9
}).withConfig({
  displayName: "StyledDots__StyledDotsCircle",
  componentId: "sc-1ltah7e-0"
})([""]);
StyledDotsCircle.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledDotsCircleOne = styled_components_browser_esm_default(StyledDotsCircle).attrs({
  cx: 9
}).withConfig({
  displayName: "StyledDots__StyledDotsCircleOne",
  componentId: "sc-1ltah7e-1"
})(["animation:", ";"], (_ref) => {
  let {
    duration
  } = _ref;
  return Ae(["", " ", "ms linear infinite"], dotOneKeyframes, duration);
});
StyledDotsCircleOne.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledDotsCircleTwo = styled_components_browser_esm_default(StyledDotsCircle).attrs(() => ({
  cx: 40
})).withConfig({
  displayName: "StyledDots__StyledDotsCircleTwo",
  componentId: "sc-1ltah7e-2"
})(["animation:", ";"], (_ref2) => {
  let {
    duration
  } = _ref2;
  return Ae(["", " ", "ms linear infinite"], dotTwoKeyframes, duration);
});
StyledDotsCircleTwo.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledDotsCircleThree = styled_components_browser_esm_default(StyledDotsCircle).attrs(() => ({
  cx: 71
})).withConfig({
  displayName: "StyledDots__StyledDotsCircleThree",
  componentId: "sc-1ltah7e-3"
})(["animation:", ";"], (_ref3) => {
  let {
    duration
  } = _ref3;
  return Ae(["", " ", "ms linear infinite"], dotThreeKeyframes, duration);
});
StyledDotsCircleThree.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$5 = "loaders.loading_placeholder";
var StyledLoadingPlaceholder = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$5,
  "data-garden-version": "8.69.1",
  role: "progressbar"
}).withConfig({
  displayName: "StyledLoadingPlaceholder",
  componentId: "sc-x3bwsx-0"
})(["display:inline-block;width:", ";height:", ";font-size:", ";", ""], (props) => props.width || "1em", (props) => props.height || "0.9em", (props) => props.fontSize, (props) => retrieveComponentStyles(COMPONENT_ID$5, props));
StyledLoadingPlaceholder.defaultProps = {
  theme: DEFAULT_THEME
};
var sizeToHeight = (size, theme) => {
  switch (size) {
    case "small":
      return theme.space.base / 2;
    case "medium":
      return theme.space.base * 1.5;
    default:
      return theme.space.base * 3;
  }
};
var sizeToBorderRadius = (size, theme) => sizeToHeight(size, theme) / 2;
var PROGRESS_BACKGROUND_COMPONENT_ID = "loaders.progress_background";
var StyledProgressBackground = styled_components_browser_esm_default.div.attrs((props) => ({
  "data-garden-id": PROGRESS_BACKGROUND_COMPONENT_ID,
  "data-garden-version": "8.69.1",
  borderRadius: props.borderRadius || sizeToBorderRadius(props.size, props.theme)
})).withConfig({
  displayName: "StyledProgress__StyledProgressBackground",
  componentId: "sc-2g8w4s-0"
})(["margin:", "px 0;border-radius:", "px;background-color:", ";color:", ";", ""], (props) => props.theme.space.base * 2, (props) => props.borderRadius, (props) => getColor("neutralHue", 200, props.theme), (props) => props.color || getColor("successHue", 600, props.theme), (props) => retrieveComponentStyles(PROGRESS_BACKGROUND_COMPONENT_ID, props));
StyledProgressBackground.defaultProps = {
  theme: DEFAULT_THEME
};
var PROGESS_INDICATOR_COMPONENT_ID = "loaders.progress_indicator";
var StyledProgressIndicator = styled_components_browser_esm_default.div.attrs((props) => ({
  "data-garden-id": PROGESS_INDICATOR_COMPONENT_ID,
  "data-garden-version": "8.69.1",
  height: props.height || sizeToHeight(props.size, props.theme),
  borderRadius: props.borderRadius || sizeToBorderRadius(props.size, props.theme)
})).withConfig({
  displayName: "StyledProgress__StyledProgressIndicator",
  componentId: "sc-2g8w4s-1"
})(["transition:width 0.1s ease-in-out;border-radius:", "px;background:currentColor;width:", "%;height:", "px;", ""], (props) => props.borderRadius, (props) => props.value, (props) => props.height, (props) => retrieveComponentStyles(PROGESS_INDICATOR_COMPONENT_ID, props));
StyledProgressIndicator.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$4 = "loaders.skeleton";
var fadeInAnimation = We(["0%,60%{opacity:0;}100%{opacity:1;}"]);
var skeletonAnimation = We(["100%{left:100%;}"]);
var skeletonRtlAnimation = We(["100%{right:100%;}"]);
var retrieveSkeletonBackgroundColor = (_ref) => {
  let {
    theme,
    isLight
  } = _ref;
  if (isLight) {
    return Ae(["background-color:", ";"], rgba(theme.colors.background, 0.2));
  }
  return Ae(["background-color:", ";"], getColor("neutralHue", 800, theme, 0.1));
};
var retrieveSkeletonAnimation = (_ref2) => {
  let {
    theme
  } = _ref2;
  if (theme.rtl) {
    return Ae(["right:-1800px;animation:", " 1.5s ease-in-out 300ms infinite;"], skeletonRtlAnimation);
  }
  return Ae(["left:-1800px;animation:", " 1.5s ease-in-out 300ms infinite;"], skeletonAnimation);
};
var retrieveSkeletonGradient = (_ref3) => {
  let {
    theme,
    isLight
  } = _ref3;
  return Ae(["background-image:linear-gradient( ", ",transparent,", ",transparent );"], theme.rtl ? "-45deg" : "45deg", isLight ? getColor("chromeHue", 700, theme, 0.4) : rgba(theme.colors.background, 0.6));
};
var StyledSkeleton = styled_components_browser_esm_default.div.attrs({
  "data-garden-id": COMPONENT_ID$4,
  "data-garden-version": "8.69.1"
}).withConfig({
  displayName: "StyledSkeleton",
  componentId: "sc-1raozze-0"
})(["display:inline-block;position:relative;animation:", " 750ms linear;border-radius:", ";width:", ";height:", ";overflow:hidden;line-height:", ";", " &::before{position:absolute;top:0;width:1000px;height:100%;content:'';", " ", "}", ";"], fadeInAnimation, (props) => props.theme.borderRadii.md, (props) => props.customWidth, (props) => props.customHeight, (props) => getLineHeight(props.theme.fontSizes.sm, props.theme.space.base * 5), retrieveSkeletonBackgroundColor, retrieveSkeletonAnimation, retrieveSkeletonGradient, (props) => retrieveComponentStyles(COMPONENT_ID$4, props));
StyledSkeleton.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledSpinnerCircle = styled_components_browser_esm_default.circle.attrs((props) => ({
  cx: 40,
  cy: 40,
  r: 34,
  fill: "none",
  stroke: "currentColor",
  strokeLinecap: "round",
  strokeWidth: props.strokeWidthValue,
  strokeDasharray: `${props.dasharrayValue} 250`,
  transform: props.transform
})).withConfig({
  displayName: "StyledSpinnerCircle",
  componentId: "sc-o4kd70-0"
})([""]);
StyledSpinnerCircle.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledSVG = styled_components_browser_esm_default.svg.attrs((props) => ({
  "data-garden-version": "8.69.1",
  xmlns: "http://www.w3.org/2000/svg",
  width: props.width,
  height: props.height,
  focusable: "false",
  viewBox: `0 0 ${props.width} ${props.height}`,
  role: "img"
})).withConfig({
  displayName: "StyledSVG",
  componentId: "sc-1xtc3kx-0"
})(["width:", ";height:", ";color:", ";font-size:", ";", ";"], (props) => props.containerWidth || "1em", (props) => props.containerHeight || "0.9em", (props) => props.color || "inherit", (props) => props.fontSize || "inherit", (props) => retrieveComponentStyles(props.dataGardenId, props));
StyledSVG.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$3 = "loaders.inline";
var PULSE_ANIMATION = We(["0%,100%{opacity:.2;}50%{opacity:.8;}"]);
var StyledCircle = styled_components_browser_esm_default.circle.attrs({
  fill: "currentColor",
  cy: 2,
  r: 2
}).withConfig({
  displayName: "StyledInline__StyledCircle",
  componentId: "sc-fxsb9l-0"
})([""]);
StyledCircle.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledInline = styled_components_browser_esm_default.svg.attrs((props) => ({
  "data-garden-id": COMPONENT_ID$3,
  "data-garden-version": "8.69.1",
  viewBox: "0 0 16 4",
  width: props.size,
  height: props.size * 0.25
})).withConfig({
  displayName: "StyledInline",
  componentId: "sc-fxsb9l-1"
})(["color:", ";", "{opacity:0.2;&:nth-child(1){animation:", " 1s infinite;animation-delay:", ";}&:nth-child(2){animation:", " 1s infinite;animation-delay:0.2s;}&:nth-child(3){animation:", " 1s infinite;animation-delay:", ";}}", ""], (props) => props.color, StyledCircle, PULSE_ANIMATION, (props) => props.theme.rtl ? "unset" : "0.4s", PULSE_ANIMATION, PULSE_ANIMATION, (props) => props.theme.rtl ? "0.4s" : "unset", (props) => retrieveComponentStyles(COMPONENT_ID$3, props));
StyledInline.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID$2 = "loaders.dots";
var Dots = (0, import_react2.forwardRef)((_ref, ref) => {
  let {
    size,
    color,
    duration,
    delayMS,
    ...other
  } = _ref;
  const theme = (0, import_react2.useContext)(Me);
  const environment = useDocument(theme);
  const canTransformSVG = (0, import_react2.useRef)(null);
  if (environment && canTransformSVG.current === null) {
    const svg = environment.createElementNS("http://www.w3.org/2000/svg", "svg");
    canTransformSVG.current = "transform" in svg;
  }
  const {
    delayComplete
  } = useSchedule({
    duration,
    delayMS,
    loop: true
  });
  const dotOne = (0, import_react2.useRef)(null);
  const dotTwo = (0, import_react2.useRef)(null);
  const dotThree = (0, import_react2.useRef)(null);
  (0, import_react2.useEffect)(() => {
    if (!canTransformSVG.current && delayComplete) {
      const transforms = [window.getComputedStyle(dotOne.current).getPropertyValue("transform"), window.getComputedStyle(dotTwo.current).getPropertyValue("transform"), window.getComputedStyle(dotThree.current).getPropertyValue("transform")];
      dotOne.current.setAttribute("transform", transforms[0]);
      dotTwo.current.setAttribute("transform", transforms[1]);
      dotThree.current.setAttribute("transform", transforms[2]);
    }
  });
  if (!delayComplete && delayMS !== 0) {
    return import_react2.default.createElement(StyledLoadingPlaceholder, {
      fontSize: size
    }, " ");
  }
  return import_react2.default.createElement(StyledSVG, _extends({
    ref,
    fontSize: size,
    color,
    width: "80",
    height: "72",
    dataGardenId: COMPONENT_ID$2
  }, other), import_react2.default.createElement("g", {
    fill: "currentColor"
  }, import_react2.default.createElement(StyledDotsCircleOne, {
    duration,
    ref: dotOne
  }), import_react2.default.createElement(StyledDotsCircleTwo, {
    duration,
    ref: dotTwo
  }), import_react2.default.createElement(StyledDotsCircleThree, {
    duration,
    ref: dotThree
  })));
});
Dots.displayName = "Dots";
Dots.propTypes = {
  size: import_prop_types2.default.any,
  duration: import_prop_types2.default.number,
  color: import_prop_types2.default.string,
  delayMS: import_prop_types2.default.number
};
Dots.defaultProps = {
  size: "inherit",
  color: "inherit",
  duration: 1250,
  delayMS: 750
};
var SIZE = ["small", "medium", "large"];
var COMPONENT_ID$1 = "loaders.progress";
var Progress = import_react2.default.forwardRef((_ref, ref) => {
  let {
    value,
    size,
    "aria-label": label,
    ...other
  } = _ref;
  const percentage = Math.max(0, Math.min(100, value));
  const ariaLabel = useText(Progress, {
    "aria-label": label
  }, "aria-label", "Progress");
  return import_react2.default.createElement(StyledProgressBackground, _extends({
    "data-garden-id": COMPONENT_ID$1,
    "data-garden-version": "8.69.1",
    "aria-valuemax": 100,
    "aria-valuemin": 0,
    "aria-valuenow": percentage,
    role: "progressbar",
    size,
    ref,
    "aria-label": ariaLabel
  }, other), import_react2.default.createElement(StyledProgressIndicator, {
    value: percentage,
    size
  }));
});
Progress.displayName = "Progress";
Progress.propTypes = {
  color: import_prop_types2.default.string,
  value: import_prop_types2.default.number.isRequired,
  size: import_prop_types2.default.oneOf(SIZE)
};
Progress.defaultProps = {
  value: 0,
  size: "medium"
};
var Skeleton = (0, import_react2.forwardRef)((_ref, ref) => {
  let {
    width,
    height,
    isLight,
    ...other
  } = _ref;
  return import_react2.default.createElement(StyledSkeleton, _extends({
    ref,
    isLight,
    customWidth: width,
    customHeight: height
  }, other), " ");
});
Skeleton.displayName = "Skeleton";
Skeleton.propTypes = {
  width: import_prop_types2.default.string,
  height: import_prop_types2.default.string,
  isLight: import_prop_types2.default.bool
};
Skeleton.defaultProps = {
  width: "100%",
  height: "100%"
};
var STROKE_WIDTH_FRAMES = {
  0: 6,
  14: 5,
  26: 4,
  36: 3,
  46: 2,
  58: 3,
  70: 4,
  80: 5,
  91: 6
};
var ROTATION_FRAMES = {
  0: -90,
  8: -81,
  36: -30,
  41: -18,
  44: -8,
  48: 0,
  55: 22,
  63: 53,
  64: 62,
  66: 67,
  68: 78,
  69: 90,
  71: 99,
  73: 112,
  74: 129,
  76: 138,
  78: 159,
  79: 180,
  81: 190,
  83: 207,
  84: 221,
  86: 226,
  88: 235,
  90: 243,
  99: 270
};
var DASHARRAY_FRAMES = {
  0: 0,
  13: 2,
  26: 13,
  53: 86,
  58: 90,
  63: 89,
  64: 88,
  66: 86,
  68: 83,
  69: 81,
  71: 76,
  73: 70,
  74: 62,
  76: 58,
  78: 47,
  79: 37,
  81: 31,
  83: 23,
  84: 16,
  88: 10,
  89: 7,
  98: 1,
  99: 0
};
var COMPONENT_ID = "loaders.spinner";
var TOTAL_FRAMES = 100;
var computeFrames = (frames, duration) => {
  return Object.entries(frames).reduce((acc, item, index, arr) => {
    const [frame, value] = item;
    const [nextFrame, nextValue] = arr[index + 1] || [TOTAL_FRAMES, arr[0][1]];
    const diff = parseInt(nextFrame, 10) - parseInt(frame, 10);
    const frameHz = 1e3 / 60;
    let subDuration = duration / (TOTAL_FRAMES - 1) * diff;
    let lastValue = value;
    acc[frame] = value;
    for (let idx = 0; idx < diff; idx++) {
      lastValue = lastValue + (nextValue - lastValue) * (frameHz / subDuration);
      subDuration = duration / (TOTAL_FRAMES - 1) * (diff - idx);
      acc[parseInt(frame, 10) + idx + 1] = lastValue;
    }
    acc[nextFrame] = nextValue;
    return acc;
  }, {});
};
var Spinner = (0, import_react2.forwardRef)((_ref, ref) => {
  let {
    size,
    duration,
    color,
    delayMS,
    ...other
  } = _ref;
  const strokeWidthValues = computeFrames(STROKE_WIDTH_FRAMES, duration);
  const rotationValues = computeFrames(ROTATION_FRAMES, duration);
  const dasharrayValues = computeFrames(DASHARRAY_FRAMES, duration);
  const {
    elapsed,
    delayComplete
  } = useSchedule({
    duration,
    delayMS
  });
  const frame = (elapsed * 100).toFixed(0);
  const strokeWidthValue = strokeWidthValues[frame];
  const rotationValue = rotationValues[frame];
  const dasharrayValue = dasharrayValues[frame];
  const WIDTH = 80;
  const HEIGHT = 80;
  if (!delayComplete && delayMS !== 0) {
    return import_react2.default.createElement(StyledLoadingPlaceholder, {
      width: "1em",
      height: "1em",
      fontSize: size
    }, " ");
  }
  return import_react2.default.createElement(StyledSVG, _extends({
    ref,
    fontSize: size,
    color,
    width: WIDTH,
    height: HEIGHT,
    dataGardenId: COMPONENT_ID,
    containerHeight: "1em",
    containerWidth: "1em"
  }, other), import_react2.default.createElement(StyledSpinnerCircle, {
    dasharrayValue,
    strokeWidthValue,
    transform: `rotate(${rotationValue}, ${WIDTH / 2}, ${HEIGHT / 2})`
  }));
});
Spinner.displayName = "Spinner";
Spinner.propTypes = {
  size: import_prop_types2.default.any,
  duration: import_prop_types2.default.number,
  color: import_prop_types2.default.string,
  delayMS: import_prop_types2.default.number
};
Spinner.defaultProps = {
  size: "inherit",
  duration: 1250,
  color: "inherit",
  delayMS: 750
};
var Inline = (0, import_react2.forwardRef)((_ref, ref) => {
  let {
    size,
    color,
    ...other
  } = _ref;
  const ariaLabel = useText(Inline, other, "aria-label", "loading");
  return import_react2.default.createElement(StyledInline, _extends({
    ref,
    size,
    color,
    "aria-label": ariaLabel,
    role: "img"
  }, other), import_react2.default.createElement(StyledCircle, {
    cx: "14"
  }), import_react2.default.createElement(StyledCircle, {
    cx: "8"
  }), import_react2.default.createElement(StyledCircle, {
    cx: "2"
  }));
});
Inline.displayName = "Inline";
Inline.propTypes = {
  size: import_prop_types2.default.number,
  color: import_prop_types2.default.string
};
Inline.defaultProps = {
  size: 16,
  color: "inherit"
};
export {
  Dots,
  Inline,
  Progress,
  Skeleton,
  Spinner
};
//# sourceMappingURL=@zendeskgarden_react-loaders.js.map
