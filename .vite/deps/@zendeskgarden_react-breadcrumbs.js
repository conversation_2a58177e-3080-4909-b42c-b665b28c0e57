import {
  DEFAULT_THEME,
  em$1,
  getColor,
  getLineHeight,
  retrieveComponentStyles,
  useText
} from "./chunk-KGUWDO6Q.js";
import "./chunk-LN6LZUGQ.js";
import "./chunk-UWW6VX3E.js";
import {
  require_prop_types
} from "./chunk-TWZ6Z4JP.js";
import {
  Ae,
  styled_components_browser_esm_default
} from "./chunk-IBK2SI4Q.js";
import {
  require_react
} from "./chunk-M7CKY7FR.js";
import {
  __toESM
} from "./chunk-Y4AOG3KG.js";

// node_modules/@zendeskgarden/react-breadcrumbs/dist/index.esm.js
var React2 = __toESM(require_react());
var import_react2 = __toESM(require_react());

// node_modules/@zendeskgarden/container-breadcrumb/dist/index.esm.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var useBreadcrumb = () => ({
  getContainerProps: (_ref) => {
    let {
      role = "navigation",
      ...other
    } = _ref;
    return {
      role: role === null ? void 0 : role,
      "data-garden-container-id": "containers.breadcrumb",
      "data-garden-container-version": "1.0.3",
      ...other
    };
  },
  getCurrentPageProps: (props) => ({
    "aria-current": "page",
    ...props
  })
});
var BreadcrumbContainer = (_ref) => {
  let {
    children,
    render = children
  } = _ref;
  return import_react.default.createElement(import_react.default.Fragment, null, render(useBreadcrumb()));
};
BreadcrumbContainer.propTypes = {
  render: import_prop_types.default.func,
  children: import_prop_types.default.func
};

// node_modules/@zendeskgarden/react-breadcrumbs/dist/index.esm.js
var COMPONENT_ID$1 = "breadcrumbs.list";
var StyledBreadcrumb = styled_components_browser_esm_default.ol.attrs({
  "data-garden-id": COMPONENT_ID$1,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledBreadcrumb",
  componentId: "sc-11jrinn-0"
})(["display:flex;margin:0;padding:0;list-style:none;font-size:", ";direction:", ";", ";"], (props) => props.theme.fontSizes.md, (props) => props.theme.rtl && "rtl", (props) => retrieveComponentStyles(COMPONENT_ID$1, props));
StyledBreadcrumb.defaultProps = {
  theme: DEFAULT_THEME
};
var _path;
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var SvgChevronRightStroke = function SvgChevronRightStroke2(props) {
  return React2.createElement("svg", _extends({
    xmlns: "http://www.w3.org/2000/svg",
    width: 12,
    height: 12,
    focusable: "false",
    viewBox: "0 0 12 12",
    "aria-hidden": "true"
  }, props), _path || (_path = React2.createElement("path", {
    fill: "currentColor",
    d: "M3.646 10.354a.5.5 0 01-.057-.638l.057-.07L7.293 6 3.646 2.354a.5.5 0 01-.057-.638l.057-.07a.5.5 0 01.638-.057l.07.057 4 4a.5.5 0 01.057.638l-.057.07-4 4a.5.5 0 01-.708 0z"
  })));
};
var ValidChevronIcon = (props) => {
  const {
    theme,
    ...validProps
  } = props;
  return import_react2.default.createElement(SvgChevronRightStroke, validProps);
};
var StyledChevronIcon = styled_components_browser_esm_default(ValidChevronIcon).attrs({
  role: "presentation",
  "aria-hidden": "true"
}).withConfig({
  displayName: "StyledChevronIcon",
  componentId: "sc-9r9qrm-0"
})(["transform:", ";margin:0 ", ";color:", ";"], (props) => props.theme.rtl && `rotate(180deg);`, (props) => em$1(props.theme.space.base, props.theme.fontSizes.md), (props) => getColor("neutralHue", 600, props.theme));
StyledChevronIcon.defaultProps = {
  theme: DEFAULT_THEME
};
var COMPONENT_ID = "breadcrumbs.item";
var linkStyles = (_ref) => {
  let {
    isCurrent
  } = _ref;
  return Ae(["& > :link,& > :visited{white-space:inherit;}", ""], isCurrent && `
      & > :link,
      & > :visited,
      & > :link:hover,
      & > :visited:hover,
      & > :link:focus,
      & > :visited:focus {
        color: inherit;
      }
    `);
};
var StyledBreadcrumbItem = styled_components_browser_esm_default.li.attrs({
  "data-garden-id": COMPONENT_ID,
  "data-garden-version": "8.67.0"
}).withConfig({
  displayName: "StyledBreadcrumbItem",
  componentId: "sc-r0suq7-0"
})(["line-height:", ";white-space:nowrap;color:", ";font-size:inherit;", ";", ";"], (props) => getLineHeight(props.theme.space.base * 5, props.theme.fontSizes.md), (props) => props.isCurrent ? getColor(props.theme.colors.neutralHue, 600) : "inherit", linkStyles, (props) => retrieveComponentStyles(COMPONENT_ID, props));
StyledBreadcrumbItem.defaultProps = {
  theme: DEFAULT_THEME
};
var StyledCenteredBreadcrumbItem = styled_components_browser_esm_default(StyledBreadcrumbItem).attrs({
  "aria-hidden": true
}).withConfig({
  displayName: "StyledCenteredBreadcrumbItem",
  componentId: "sc-1ces9y2-0"
})(["display:flex;align-items:center;"]);
StyledCenteredBreadcrumbItem.defaultProps = {
  theme: DEFAULT_THEME
};
var Breadcrumb = (0, import_react2.forwardRef)((props, ref) => {
  const {
    getContainerProps,
    getCurrentPageProps
  } = useBreadcrumb();
  const totalChildren = import_react2.Children.count(props.children);
  const mappedChildren = import_react2.Children.map(props.children, (child, index) => {
    const isLastItem = index === totalChildren - 1;
    if (isLastItem) {
      return import_react2.default.createElement(StyledBreadcrumbItem, {
        isCurrent: true
      }, (0, import_react2.cloneElement)(child, getCurrentPageProps()));
    }
    return import_react2.default.createElement(import_react2.default.Fragment, null, import_react2.default.createElement(StyledBreadcrumbItem, null, child), import_react2.default.createElement(StyledCenteredBreadcrumbItem, null, import_react2.default.createElement(StyledChevronIcon, null)));
  });
  const ariaLabel = useText(Breadcrumb, props, "aria-label", "Breadcrumbs");
  return import_react2.default.createElement("nav", getContainerProps({
    ...props,
    ref,
    role: null,
    "aria-label": ariaLabel
  }), import_react2.default.createElement(StyledBreadcrumb, null, mappedChildren));
});
Breadcrumb.displayName = "Breadcrumb";
export {
  Breadcrumb
};
//# sourceMappingURL=@zendeskgarden_react-breadcrumbs.js.map
